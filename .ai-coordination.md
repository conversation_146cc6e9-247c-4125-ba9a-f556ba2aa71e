# AI Development Coordination

This file manages work coordination between Augment Agent and Claude <PERSON> (Cursor AI) to prevent conflicts and ensure efficient collaboration.

## Current Work Claims

### Augment Agent
*Currently working on:*
- No active claims

*Reserved for future work:*
- Architecture and system design tasks
- Cross-file refactoring projects
- Documentation creation and updates
- Complex debugging requiring codebase-wide context

### Claude Code (Cursor AI)
*Currently working on:*
- No active claims

*Reserved for future work:*
- Real-time coding and implementation
- UI/UX component development
- Interactive debugging sessions
- Single-file modifications and bug fixes

## Handoff Queue

*No pending handoffs*

## Work Area Assignments

### Augment Agent Primary Areas
- `docs/` - All documentation
- `scripts/` - Database and deployment scripts
- `src/lib/` - Core library functions and utilities
- `src/contexts/` - React contexts and providers
- `src/hooks/` - Custom React hooks
- `app/admin/` - Admin dashboard backend logic
- `functions/` - Firebase Cloud Functions
- Configuration files (`.config.js`, `.json`, etc.)

### Claude Code Primary Areas
- `src/components/` - React components and UI
- `app/` - Next.js pages and routes (except admin backend)
- `src/styles/` - CSS and styling
- `public/` - Static assets
- `tests/` - Test files and test implementation
- Component-specific styling and interactions

### Shared Areas (Coordination Required)
- `src/types/` - TypeScript type definitions
- `src/store/` - State management
- `middleware.ts` - Authentication middleware
- Root configuration files

## Coordination Protocols

### Before Starting Work
1. **Check this file** for current claims and conflicts
2. **Claim your work area** by updating the "Current Work Claims" section
3. **Estimate completion time** and add it to your claim
4. **Check for dependencies** on other AI's work

### During Work
1. **Commit frequently** with clear, descriptive messages
2. **Use conventional commit format**: `[AI] type: description`
3. **Update progress** in the work log if working on major features
4. **Communicate blockers** by updating this file

### After Completing Work
1. **Remove your claim** from the "Current Work Claims" section
2. **Add handoff notes** if another AI needs to continue the work
3. **Update documentation** if you made architectural changes
4. **Run tests** before marking work as complete

## Emergency Protocols

### Immediate Fixes (Claude Code Priority)
- Production bugs affecting user experience
- UI/UX issues preventing normal operation
- Build failures blocking development

### Architecture Decisions (Augment Agent Priority)
- Database schema changes
- API design modifications
- Security implementation changes
- Performance optimization strategies

## Branch Naming Conventions

### Augment Agent
- `augment/feature-name` - New features
- `augment/analysis-topic` - Analysis and documentation
- `augment/refactor-area` - Code refactoring
- `augment/fix-issue` - Bug fixes requiring deep analysis

### Claude Code
- `cursor/feature-name` - New features
- `cursor/ui-improvements` - UI/UX enhancements
- `cursor/fix-issue` - Bug fixes and quick improvements
- `cursor/component-name` - Component-specific work

### Collaborative
- `collab/feature-name` - Features requiring both AIs
- `collab/integration-name` - Integration work

## Conflict Resolution

### File Conflicts
1. **Backup current work**: `git stash` or create backup branch
2. **Identify conflict owner** based on file type and primary areas
3. **Priority AI resolves conflict** and communicates resolution
4. **Test thoroughly** before final merge
5. **Document resolution** in work log

### Priority Rules
1. **Emergency fixes**: Claude Code has priority
2. **Architecture decisions**: Augment Agent has priority
3. **Shared files**: Sequential work only, no simultaneous editing
4. **Feature development**: First to claim has priority

## Communication Guidelines

### Commit Messages
```bash
[AUGMENT] feat: implement user level system architecture
[CURSOR] fix: resolve button hover animation glitch
[COLLAB] merge: integrate auth system with UI components
```

### Handoff Notes Format
```markdown
## Handoff: [Feature/Area Name]
**From**: [AI Name]
**To**: [AI Name]
**Date**: [YYYY-MM-DD]

### Completed Work
- [List of completed tasks]

### Next Steps
- [List of tasks for receiving AI]

### Important Notes
- [Any critical information, gotchas, or dependencies]

### Files Modified
- [List of key files changed]
```

---

**Last Updated**: 2025-01-18
**Next Review**: Check daily for active claims and conflicts
