# Syndicaps Project Context for Augment

## 🎯 Project Overview
Syndicaps is a Next.js e-commerce platform with Firebase backend, featuring:
- User authentication and profiles
- Gamification system with points and tiers
- Admin dashboard for management
- Community features and moderation
- Raffle/lottery system
- Payment processing

## 📁 Key Project Structure
```
syndicaps/
├── src/
│   ├── components/          # React components
│   ├── pages/              # Next.js pages
│   ├── lib/                # Utility libraries
│   ├── hooks/              # Custom React hooks
│   ├── types/              # TypeScript type definitions
│   └── admin/              # Admin-specific components
├── docs/                   # Documentation (follows yy-mm-dd structure)
├── PRPs/                   # Project Requirement Patterns
└── examples/               # Code examples and patterns
```

## 🔧 Technology Stack
- **Frontend**: Next.js 14, React 18, TypeScript
- **Backend**: Firebase (Firestore, Auth, Functions)
- **Styling**: Tailwind CSS
- **UI Components**: Custom components with accessibility focus
- **State Management**: React hooks, Context API
- **Testing**: Jest, React Testing Library
- **Deployment**: Cloudflare Pages

## 📚 Documentation Standards
All documentation follows the comprehensive standards defined in:
`docs/25-07-15-technical-documentation/syndicaps-documentation-standards.md`

### Key Standards:
- Folder structure: `yy-mm-dd-category-name`
- File naming: `kebab-case-naming.md`
- JSDoc requirements: @param, @returns, @example
- TypeScript: No `any` types, complete type annotations
- Code snippets: Proper `<augment_code_snippet>` usage

## 🎯 Common Tasks
When working on Syndicaps:

### Documentation Tasks
- Always place files in correct category folders
- Use kebab-case naming for all files
- Include complete JSDoc for all functions
- Provide working code examples
- Update README.md files when adding content

### Code Tasks
- Add JSDoc comments for all new functions
- Use proper TypeScript types (no `any`)
- Document component props and usage
- Include accessibility considerations
- Follow established patterns from examples/

### API Tasks
- Document all endpoints with complete JSDoc
- Include request/response examples
- Document error conditions and status codes
- Specify authentication requirements

## 🚨 Critical Rules
1. **NEVER** use `any` types in TypeScript
2. **ALWAYS** use full file paths in code snippets
3. **ALWAYS** follow kebab-case naming convention
4. **ALWAYS** place files in correct category folders
5. **ALWAYS** include JSDoc with @param, @returns, @example
6. **ALWAYS** use proper `<augment_code_snippet>` tags

## 📖 Reference Files
- **Main Standards**: `docs/25-07-15-technical-documentation/syndicaps-documentation-standards.md`
- **Global Rules**: `CLAUDE.md`
- **Base Patterns**: `PRPs/templates/syndicaps_base_prp.md`
- **Examples**: `examples/README.md`

## 🔍 Quality Checklist
Before any response:
- [ ] File paths are complete and correct
- [ ] Code follows TypeScript standards
- [ ] Documentation includes required JSDoc
- [ ] Files use proper naming conventions
- [ ] Content is in correct category folders
- [ ] Examples are functional and tested