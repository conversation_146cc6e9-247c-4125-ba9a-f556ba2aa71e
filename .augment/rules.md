# Augment Rules for Syndicaps Project

## 🎯 Project-Specific Documentation Standards

### File Organization Rules
- **MANDATORY**: Use `yy-mm-dd-category-name` folder structure in docs/
- **MANDATORY**: All files must use kebab-case naming (lowercase with hyphens)
- **MANDATORY**: Every category folder must have a README.md with standardized structure
- **FORBIDDEN**: Spaces in file names, uppercase letters in file names
- **REQUIRED**: Place files in correct categorized folders:
  - `25-07-15-technical-documentation/` - Architecture, development guides
  - `25-07-15-api-documentation/` - API references, deployment guides  
  - `25-07-15-admin-documentation/` - Admin procedures, dashboard guides
  - `25-07-15-user-guides/` - User documentation, community rules
  - `25-07-15-business-strategy/` - PRD, business plans, market analysis
  - `25-07-15-security-compliance/` - Security guidelines, audit reports
  - `25-07-15-analysis-audits/` - Codebase audits, performance analysis
  - `25-07-15-implementation-reports/` - Feature reports, project status
  - `25-07-15-archived-legacy/` - Outdated or superseded documentation

### Code Snippet Standards for Augment
- **MANDATORY**: Always use full file paths in `<augment_code_snippet>` tags
- **MANDATORY**: Use `mode="EXCERPT"` for showing existing code (max 10 lines)
- **MANDATORY**: Use `mode="EDIT"` for proposing changes or new code
- **REQUIRED**: Keep excerpts concise with placeholders (// ... implementation)
- **REQUIRED**: Only show new/modified lines in EDIT mode
- **FORBIDDEN**: Verbose code blocks that exceed 10 lines for excerpts

### TypeScript Documentation Requirements
- **MANDATORY**: All functions must have complete JSDoc with @param, @returns, @example
- **MANDATORY**: All interfaces must have property descriptions
- **FORBIDDEN**: Use of `any` types - must use proper type definitions
- **REQUIRED**: Type annotations for all function parameters and return values

### Component Documentation Standards
- **MANDATORY**: All React components must document props with JSDoc
- **REQUIRED**: Include usage examples for complex components
- **REQUIRED**: Document accessibility features (ARIA labels, semantic HTML)
- **REQUIRED**: Document error states and loading states

### API Documentation Requirements
- **MANDATORY**: Complete endpoint documentation with @route, @auth, @returns
- **REQUIRED**: Request/response examples for all endpoints
- **REQUIRED**: Error handling documentation with status codes
- **REQUIRED**: Authentication and authorization requirements

## 🔧 Code Quality Rules

### Function Documentation Template
```typescript
/**
 * [Brief description of function purpose]
 * @param paramName - Description of parameter
 * @returns Description of return value
 * @example
 * ```typescript
 * const result = await functionName(param);
 * console.log(result);
 * ```
 */
```

### Component Documentation Template
```typescript
/**
 * [Component description and purpose]
 */
interface ComponentProps {
  /** Description of prop */
  propName: PropType;
  /** Optional prop description */
  optionalProp?: OptionalType;
}

/**
 * @example
 * ```tsx
 * <Component propName="value" />
 * ```
 */
```

### API Documentation Template
```typescript
/**
 * [HTTP_METHOD] /api/endpoint/path
 * [Brief description of endpoint purpose]
 * 
 * @route [METHOD] /api/endpoint/path
 * @auth Required/Optional - [Auth requirements]
 * @param paramName - Parameter description
 * @returns {ResponseType} Response description
 * @throws 400 - Error condition
 * @throws 401 - Unauthorized
 * @throws 500 - Server error
 * 
 * @example
 * ```typescript
 * const response = await fetch('/api/endpoint', {
 *   method: 'POST',
 *   headers: { 'Authorization': `Bearer ${token}` },
 *   body: JSON.stringify(data)
 * });
 * ```
 */
```

## 📝 Content Creation Rules

### When Creating New Documentation
1. **ALWAYS** check existing documentation structure first
2. **ALWAYS** place files in appropriate category folders
3. **ALWAYS** use kebab-case naming convention
4. **ALWAYS** include complete JSDoc for code examples
5. **ALWAYS** provide working code examples
6. **ALWAYS** update relevant README.md files

### When Updating Existing Code
1. **ALWAYS** update corresponding documentation
2. **ALWAYS** add JSDoc comments for new functions
3. **ALWAYS** include usage examples for complex features
4. **ALWAYS** document breaking changes
5. **ALWAYS** update cross-references when moving files

### Cross-Reference Standards
- **Internal links**: Use relative paths `../category/file.md`
- **File references**: Use backticks `src/components/Component.tsx`
- **Function references**: Use backticks with parentheses `functionName()`
- **Component references**: Use backticks `ComponentName`

## 🚨 Quality Assurance Rules

### Pre-Response Checklist
Before providing any code or documentation:
- [ ] File paths are complete and correct
- [ ] Code snippets use proper `<augment_code_snippet>` tags
- [ ] JSDoc comments are complete with required fields
- [ ] TypeScript types are properly defined (no `any` types)
- [ ] Examples are tested and functional
- [ ] Files follow naming conventions
- [ ] Content is placed in correct category folders

### Code Snippet Validation
- **EXCERPT mode**: Only for showing existing code, max 10 lines
- **EDIT mode**: Only for new/modified code, focus on changes only
- **Path attribute**: Always include full file path from project root
- **Language tags**: Always specify language for syntax highlighting

### Documentation Quality Standards
- **Minimum JSDoc coverage**: 90% for new functions
- **Required sections**: Overview, Contents, Quick Access in READMEs
- **Cross-reference integrity**: All internal links must be valid
- **Example validation**: All code examples must be functional

## 🔗 Integration Rules

### Augment Code Workflow
1. **Read context**: Always check existing documentation standards
2. **Follow patterns**: Use established patterns from examples
3. **Validate structure**: Ensure proper organization
4. **Test examples**: Verify code examples work
5. **Update cross-refs**: Maintain link integrity

### File Creation Priority
1. Check if file exists in correct location
2. Verify naming follows kebab-case convention
3. Ensure proper category folder placement
4. Include required documentation sections
5. Add to relevant README.md files

### Error Prevention
- **NEVER** create files with spaces in names
- **NEVER** use uppercase letters in file names
- **NEVER** place files in wrong category folders
- **NEVER** omit required JSDoc fields
- **NEVER** use `any` types in TypeScript
- **NEVER** create code examples without testing

## 📊 Compliance Levels

### Critical (Must Fix Immediately)
- File naming convention violations
- Missing JSDoc on functions
- Use of `any` types
- Incorrect folder placement
- Missing `<augment_code_snippet>` tags

### Important (Should Fix Soon)
- Incomplete JSDoc fields
- Missing code examples
- Broken internal links
- Missing README updates

### Recommended (Nice to Have)
- Enhanced accessibility documentation
- Additional cross-references
- Performance considerations
- Advanced usage examples

---

**These rules are automatically enforced for all Syndicaps project interactions. Always follow these standards when creating, updating, or referencing documentation and code.**