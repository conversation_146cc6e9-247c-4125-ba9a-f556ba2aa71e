# Function Documentation Template

## Standard Function JSDoc
```typescript
/**
 * [Brief description of what the function does]
 * @param paramName - Description of the parameter and its purpose
 * @param optionalParam - Optional parameter description
 * @returns Promise/Type description of what is returned
 * @throws ErrorType - When this error occurs
 * @example
 * ```typescript
 * const result = await functionName('param1', { option: true });
 * console.log(result.data);
 * ```
 */
async function functionName(
  paramName: string,
  optionalParam?: OptionsType
): Promise<ReturnType> {
  // Implementation
}
```

## Component Documentation Template
```typescript
/**
 * [Component description and main purpose]
 * 
 * @accessibility
 * - Uses semantic HTML elements
 * - Includes ARIA labels for screen readers
 * - Supports keyboard navigation
 */
interface ComponentProps {
  /** Primary data for the component */
  data: DataType;
  /** Optional styling className */
  className?: string;
  /** Callback when action is performed */
  onAction?: (data: DataType) => void;
}

/**
 * @example
 * ```tsx
 * <ComponentName 
 *   data={userData}
 *   className="custom-styles"
 *   onAction={(data) => handleAction(data)}
 * />
 * ```
 */
export function ComponentName({ data, className, onAction }: ComponentProps) {
  // Implementation
}
```

## API Endpoint Documentation Template
```typescript
/**
 * POST /api/endpoint/path
 * [Brief description of what this endpoint does]
 * 
 * @route POST /api/endpoint/path
 * @auth Required - Firebase Auth token in Authorization header
 * @param requestData - Request body data structure
 * @returns {ResponseType} Success response with data
 * @throws 400 - Bad Request - Invalid parameters
 * @throws 401 - Unauthorized - Invalid or missing auth token
 * @throws 403 - Forbidden - Insufficient permissions
 * @throws 500 - Internal Server Error - Server-side error
 * 
 * @example
 * ```typescript
 * const response = await fetch('/api/endpoint/path', {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json',
 *     'Authorization': `Bearer ${authToken}`
 *   },
 *   body: JSON.stringify({ data: 'value' })
 * });
 * 
 * if (response.ok) {
 *   const result = await response.json();
 *   console.log(result);
 * }
 * ```
 */