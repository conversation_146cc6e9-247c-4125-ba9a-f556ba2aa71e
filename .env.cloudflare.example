# Syndicaps Cloudflare Hybrid Configuration
# Copy this file to .env.local and fill in your actual values

# ===== CLOUDFLARE CORE CONFIGURATION =====
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id
CLOUDFLARE_EMAIL=your_cloudflare_email

# ===== R2 STORAGE CONFIGURATION =====
R2_ACCESS_KEY_ID=your_r2_access_key_id
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
R2_BUCKET_IMAGES=syndicaps-images
R2_BUCKET_BACKUPS=syndicaps-backups
R2_PUBLIC_URL=https://syndicaps-images.r2.dev

# R2 Bucket Names by Environment
R2_BUCKET_IMAGES_DEV=syndicaps-images-dev
R2_BUCKET_BACKUPS_DEV=syndicaps-backups-dev
R2_BUCKET_IMAGES_STAGING=syndicaps-images-staging
R2_BUCKET_BACKUPS_STAGING=syndicaps-backups-staging
R2_BUCKET_IMAGES_PROD=syndicaps-images
R2_BUCKET_BACKUPS_PROD=syndicaps-backups

# ===== CLOUDFLARE IMAGES CONFIGURATION =====
CLOUDFLARE_IMAGES_ACCOUNT_HASH=your_images_account_hash
CLOUDFLARE_IMAGES_API_TOKEN=your_images_api_token
CLOUDFLARE_IMAGES_DELIVERY_URL=https://imagedelivery.net/your_account_hash

# ===== WORKERS CONFIGURATION =====
WORKERS_DOMAIN=syndicaps.com
WORKERS_SUBDOMAIN=api.syndicaps.com

# ===== KV NAMESPACE CONFIGURATION =====
# Development Environment
CLOUDFLARE_KV_CACHE_DEV_ID=your_cache_dev_namespace_id
CLOUDFLARE_KV_CACHE_DEV_PREVIEW_ID=your_cache_dev_preview_namespace_id
CLOUDFLARE_KV_SESSION_DEV_ID=your_session_dev_namespace_id
CLOUDFLARE_KV_SESSION_DEV_PREVIEW_ID=your_session_dev_preview_namespace_id

# Staging Environment
CLOUDFLARE_KV_CACHE_STAGING_ID=your_cache_staging_namespace_id
CLOUDFLARE_KV_SESSION_STAGING_ID=your_session_staging_namespace_id

# Production Environment
CLOUDFLARE_KV_CACHE_PROD_ID=your_cache_prod_namespace_id
CLOUDFLARE_KV_SESSION_PROD_ID=your_session_prod_namespace_id

# ===== CDN CONFIGURATION =====
CDN_BASE_URL=https://cdn.syndicaps.com
CDN_IMAGES_URL=https://images.syndicaps.com
CACHE_TTL_STATIC=********
CACHE_TTL_IMAGES=604800
CACHE_TTL_API=300
CACHE_TTL_HTML=3600

# ===== FEATURE FLAGS FOR HYBRID ROLLOUT =====
# Core Cloudflare Features
FEATURE_R2_STORAGE=false
FEATURE_CF_WORKERS=false
FEATURE_CF_IMAGES=false
FEATURE_HYBRID_CDN=false
FEATURE_EDGE_CACHING=false

# Advanced Worker Features
FEATURE_WORKER_IMAGE_OPT=false
FEATURE_WORKER_API_CACHE=false

# Monitoring and Analytics
FEATURE_ADVANCED_ANALYTICS=false

# Rollout percentages (0-100)
R2_ROLLOUT_PERCENTAGE=0
CF_WORKERS_ROLLOUT_PERCENTAGE=0
CF_IMAGES_ROLLOUT_PERCENTAGE=0
HYBRID_CDN_ROLLOUT_PERCENTAGE=0
EDGE_CACHING_ROLLOUT_PERCENTAGE=0
WORKER_IMAGE_OPT_ROLLOUT_PERCENTAGE=0
WORKER_API_CACHE_ROLLOUT_PERCENTAGE=0
ADVANCED_ANALYTICS_ROLLOUT_PERCENTAGE=100

# Emergency Kill Switches
R2_EMERGENCY_DISABLE=false
CF_WORKERS_EMERGENCY_DISABLE=false
CF_IMAGES_EMERGENCY_DISABLE=false
HYBRID_CDN_EMERGENCY_DISABLE=false
EDGE_CACHING_EMERGENCY_DISABLE=false
WORKER_IMAGE_OPT_EMERGENCY_DISABLE=false
WORKER_API_CACHE_EMERGENCY_DISABLE=false

# ===== PERFORMANCE MONITORING =====
PERFORMANCE_MONITORING_ENABLED=true
CLOUDFLARE_ANALYTICS_TOKEN=your_analytics_token
REAL_USER_MONITORING=true

# ===== SECURITY CONFIGURATION =====
WAF_ENABLED=true
RATE_LIMITING_ENABLED=true
BOT_MANAGEMENT_ENABLED=true
DDoS_PROTECTION_ENABLED=true

# ===== BACKUP AND DISASTER RECOVERY =====
BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30
DISASTER_RECOVERY_ENABLED=true

# ===== COST MONITORING =====
COST_MONITORING_ENABLED=true
COST_ALERT_THRESHOLD=100
BILLING_EMAIL=<EMAIL>

# ===== DEVELOPMENT CONFIGURATION =====
DEVELOPMENT_MODE=false
DEBUG_WORKERS=false
VERBOSE_LOGGING=false

# ===== ENVIRONMENT SPECIFIC SETTINGS =====
# Current deployment environment (development, staging, production)
DEPLOYMENT_ENVIRONMENT=development
NODE_ENV=development

# ===== WRANGLER CONFIGURATION =====
# Wrangler CLI settings for deployment
WRANGLER_SEND_METRICS=false
WRANGLER_LOG_LEVEL=info

# ===== FIREBASE INTEGRATION =====
# Firebase project configuration for hybrid setup
FIREBASE_PROJECT_ID=syndicaps-fullpower
FIREBASE_FUNCTIONS_URL=https://us-central1-syndicaps-fullpower.cloudfunctions.net

# ===== MIGRATION SETTINGS =====
# Settings for gradual migration from Firebase to Cloudflare
MIGRATION_PHASE=1
MIGRATION_BATCH_SIZE=100
MIGRATION_DELAY_MS=1000

# ===== TESTING CONFIGURATION =====
# Test environment settings
TEST_R2_BUCKET=syndicaps-test-bucket
TEST_KV_NAMESPACE=your_test_kv_namespace_id
ENABLE_INTEGRATION_TESTS=false

# ===== LOGGING AND MONITORING =====
# Enhanced logging configuration
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# ===== CACHE CONFIGURATION =====
# Cache TTL settings for different content types
CACHE_TTL_STATIC_ASSETS=********  # 1 year
CACHE_TTL_IMAGES=604800           # 1 week
CACHE_TTL_API_RESPONSES=300       # 5 minutes
CACHE_TTL_HTML_PAGES=3600         # 1 hour
CACHE_TTL_USER_DATA=60            # 1 minute

# ===== RATE LIMITING =====
# Rate limiting configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20
RATE_LIMIT_WINDOW_SIZE=60

# ===== SECURITY HEADERS =====
# Security configuration
ENABLE_SECURITY_HEADERS=true
ENABLE_CORS=true
CORS_ALLOWED_ORIGINS=https://syndicaps.com,https://www.syndicaps.com
ENABLE_CSP=true

# ===== WORKER LIMITS =====
# Cloudflare Workers resource limits
WORKER_CPU_TIME_LIMIT=10          # Free plan: 10ms
WORKER_MEMORY_LIMIT=128           # Free plan: 128MB
WORKER_REQUEST_LIMIT=100000       # Free plan: 100k requests/day
