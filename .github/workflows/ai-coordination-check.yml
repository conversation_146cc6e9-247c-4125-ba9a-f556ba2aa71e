name: AI Coordination Check

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]

jobs:
  coordination-check:
    runs-on: ubuntu-latest
    name: Validate AI Coordination

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Check AI Coordination Files
      run: |
        echo "🤖 Checking AI Coordination System..."
        
        # Check if coordination files exist
        if [ ! -f ".ai-coordination.md" ]; then
          echo "❌ .ai-coordination.md is missing!"
          exit 1
        fi
        
        if [ ! -f "AI_WORK_LOG.md" ]; then
          echo "❌ AI_WORK_LOG.md is missing!"
          exit 1
        fi
        
        if [ ! -f "HANDOFF_NOTES.md" ]; then
          echo "❌ HANDOFF_NOTES.md is missing!"
          exit 1
        fi
        
        echo "✅ All coordination files present"

    - name: Validate Commit Messages
      run: |
        echo "📝 Checking commit message format..."
        
        # Get commits in this PR/push
        if [ "${{ github.event_name }}" = "pull_request" ]; then
          COMMITS=$(git log --oneline ${{ github.event.pull_request.base.sha }}..${{ github.event.pull_request.head.sha }})
        else
          COMMITS=$(git log --oneline -10)
        fi
        
        # Check for AI prefixes in recent commits
        AI_COMMITS=$(echo "$COMMITS" | grep -E "\[(AUGMENT|CURSOR|COLLAB|HANDOFF)\]" || true)
        
        if [ -n "$AI_COMMITS" ]; then
          echo "✅ Found AI coordination commits:"
          echo "$AI_COMMITS"
        else
          echo "ℹ️ No AI coordination commits found in recent history"
        fi

    - name: Check for Work Conflicts
      run: |
        echo "🔍 Checking for potential work conflicts..."
        
        # Check if multiple AIs are claiming the same areas
        AUGMENT_CLAIMS=$(grep -A 10 "### Augment Agent" .ai-coordination.md | grep -E "^\s*-\s*\`" || true)
        CURSOR_CLAIMS=$(grep -A 10 "### Claude Code" .ai-coordination.md | grep -E "^\s*-\s*\`" || true)
        
        if [ -n "$AUGMENT_CLAIMS" ] && [ -n "$CURSOR_CLAIMS" ]; then
          echo "📋 Current work claims found:"
          echo "Augment Agent:"
          echo "$AUGMENT_CLAIMS"
          echo "Claude Code:"
          echo "$CURSOR_CLAIMS"
          
          # Simple conflict detection (same file paths)
          CONFLICTS=$(comm -12 <(echo "$AUGMENT_CLAIMS" | sort) <(echo "$CURSOR_CLAIMS" | sort) || true)
          if [ -n "$CONFLICTS" ]; then
            echo "⚠️ Potential conflicts detected:"
            echo "$CONFLICTS"
            echo "Please resolve conflicts in .ai-coordination.md"
            exit 1
          fi
        fi
        
        echo "✅ No work conflicts detected"

    - name: Validate Branch Naming
      if: github.event_name == 'pull_request'
      run: |
        echo "🌿 Checking branch naming convention..."
        
        BRANCH_NAME="${{ github.event.pull_request.head.ref }}"
        
        if [[ "$BRANCH_NAME" =~ ^(augment|cursor|collab)/.+ ]]; then
          echo "✅ Branch follows AI coordination naming: $BRANCH_NAME"
        elif [[ "$BRANCH_NAME" =~ ^(feature|fix|hotfix|release)/.+ ]]; then
          echo "ℹ️ Standard branch naming detected: $BRANCH_NAME"
        else
          echo "⚠️ Branch name doesn't follow conventions: $BRANCH_NAME"
          echo "Consider using: augment/*, cursor/*, collab/*, feature/*, fix/*"
        fi

    - name: Check Documentation Updates
      run: |
        echo "📚 Checking if documentation needs updates..."
        
        # Check if significant changes were made that might need docs
        CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD || git diff --name-only HEAD~1 || echo "")
        
        # Check for changes in key areas that typically need documentation
        SIGNIFICANT_CHANGES=$(echo "$CHANGED_FILES" | grep -E "(src/lib|src/contexts|src/hooks|app/api|functions)" || true)
        
        if [ -n "$SIGNIFICANT_CHANGES" ]; then
          echo "📝 Significant changes detected in:"
          echo "$SIGNIFICANT_CHANGES"
          
          # Check if docs were updated
          DOC_CHANGES=$(echo "$CHANGED_FILES" | grep -E "(docs/|README|\.md$)" || true)
          
          if [ -z "$DOC_CHANGES" ]; then
            echo "⚠️ Consider updating documentation for these changes"
          else
            echo "✅ Documentation updates found"
          fi
        else
          echo "ℹ️ No significant changes requiring documentation updates"
        fi

    - name: Generate Coordination Report
      run: |
        echo "📊 Generating AI Coordination Report..."
        
        cat > coordination-report.md << 'EOF'
        # AI Coordination Report
        
        ## Workflow Summary
        - **Event**: ${{ github.event_name }}
        - **Branch**: ${{ github.ref_name }}
        - **Commit**: ${{ github.sha }}
        
        ## Coordination Status
        EOF
        
        # Add current claims to report
        echo "### Current Work Claims" >> coordination-report.md
        grep -A 20 "## Current Work Claims" .ai-coordination.md >> coordination-report.md || echo "No active claims" >> coordination-report.md
        
        # Add recent log entries
        echo "### Recent Work Log" >> coordination-report.md
        tail -20 AI_WORK_LOG.md >> coordination-report.md || echo "No recent log entries" >> coordination-report.md
        
        echo "✅ Coordination report generated"

    - name: Upload Coordination Report
      uses: actions/upload-artifact@v4
      with:
        name: ai-coordination-report
        path: coordination-report.md
        retention-days: 30

  notify-coordination:
    runs-on: ubuntu-latest
    needs: coordination-check
    if: failure()
    
    steps:
    - name: Coordination Issue Notification
      run: |
        echo "🚨 AI Coordination issues detected!"
        echo "Please check the coordination-check job for details."
        echo "Common issues:"
        echo "- Missing coordination files"
        echo "- Work conflicts between AIs"
        echo "- Improper commit message format"
        echo "- Missing documentation updates"
