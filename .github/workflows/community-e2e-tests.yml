name: Community E2E Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/components/community/**'
      - 'app/community/**'
      - 'src/lib/firebase/community.ts'
      - 'tests/e2e/community-*.spec.ts'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/components/community/**'
      - 'app/community/**'
      - 'src/lib/firebase/community.ts'
      - 'tests/e2e/community-*.spec.ts'
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Test suite to run'
        required: false
        default: 'all'
        type: choice
        options:
        - all
        - discover
        - search
        - upload
        - navigation
        - realtime

jobs:
  e2e-tests:
    name: E2E Tests - Community Features
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        include:
          - browser: chromium
            project: chromium
          - browser: firefox
            project: firefox
          - browser: webkit
            project: webkit

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps ${{ matrix.browser }}

    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY_TEST }}
        NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN_TEST }}
        NEXT_PUBLIC_FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID_TEST }}
        NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET_TEST }}
        NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID_TEST }}
        NEXT_PUBLIC_FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID_TEST }}

    - name: Start development server
      run: npm run start &
      env:
        PORT: 3000
        NODE_ENV: test

    - name: Wait for server to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000

    - name: Run community E2E tests
      run: |
        if [ "${{ github.event.inputs.test_suite }}" = "discover" ]; then
          npx playwright test tests/e2e/community-discover*.spec.ts --project=${{ matrix.project }}
        elif [ "${{ github.event.inputs.test_suite }}" = "search" ]; then
          npx playwright test tests/e2e/community-search*.spec.ts --project=${{ matrix.project }}
        elif [ "${{ github.event.inputs.test_suite }}" = "upload" ]; then
          npx playwright test tests/e2e/community-submission-upload*.spec.ts --project=${{ matrix.project }}
        elif [ "${{ github.event.inputs.test_suite }}" = "navigation" ]; then
          npx playwright test tests/e2e/community-navigation*.spec.ts --project=${{ matrix.project }}
        elif [ "${{ github.event.inputs.test_suite }}" = "realtime" ]; then
          npx playwright test tests/e2e/community-realtime*.spec.ts --project=${{ matrix.project }}
        else
          npx playwright test tests/e2e/community-*.spec.ts --project=${{ matrix.project }}
        fi
      env:
        PLAYWRIGHT_BASE_URL: http://localhost:3000
        CI: true

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report-${{ matrix.browser }}
        path: |
          playwright-report/
          test-results/
        retention-days: 30

    - name: Upload performance metrics
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-metrics-${{ matrix.browser }}
        path: test-results/performance-*.json
        retention-days: 7

  performance-analysis:
    name: Performance Analysis
    runs-on: ubuntu-latest
    needs: e2e-tests
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download performance metrics
      uses: actions/download-artifact@v3
      with:
        pattern: performance-metrics-*
        merge-multiple: true

    - name: Analyze performance trends
      run: |
        echo "## Performance Analysis" >> $GITHUB_STEP_SUMMARY
        echo "| Metric | Chrome | Firefox | Safari |" >> $GITHUB_STEP_SUMMARY
        echo "|--------|--------|---------|--------|" >> $GITHUB_STEP_SUMMARY
        
        # Parse performance data (simplified)
        if [ -f "performance-chromium.json" ]; then
          echo "| Page Load | $(cat performance-chromium.json | jq -r '.pageLoad // "N/A"')ms | $(cat performance-firefox.json | jq -r '.pageLoad // "N/A"')ms | $(cat performance-webkit.json | jq -r '.pageLoad // "N/A"')ms |" >> $GITHUB_STEP_SUMMARY
        fi

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [e2e-tests, performance-analysis]
    if: always()
    
    steps:
    - name: Generate test summary
      run: |
        echo "## Community E2E Test Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ needs.e2e-tests.result }}" = "success" ]; then
          echo "✅ All E2E tests passed across all browsers" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Some E2E tests failed. Check the detailed reports." >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Test Coverage" >> $GITHUB_STEP_SUMMARY
        echo "- 🔍 Community Discover functionality" >> $GITHUB_STEP_SUMMARY
        echo "- 🔎 Search and filtering features" >> $GITHUB_STEP_SUMMARY  
        echo "- 📤 Submission upload workflow" >> $GITHUB_STEP_SUMMARY
        echo "- 🧭 Navigation and routing" >> $GITHUB_STEP_SUMMARY
        echo "- ⚡ Real-time updates and WebSocket connections" >> $GITHUB_STEP_SUMMARY
        echo "- 📱 Responsive design and accessibility" >> $GITHUB_STEP_SUMMARY
        echo "- 🔄 Error handling and recovery" >> $GITHUB_STEP_SUMMARY

  notify-on-failure:
    name: Notify on Failure
    runs-on: ubuntu-latest
    needs: e2e-tests
    if: failure() && github.ref == 'refs/heads/main'
    
    steps:
    - name: Notify team of test failures
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: |
          🚨 Community E2E tests failed on main branch
          
          Failed tests may indicate:
          - Breaking changes to community features
          - Performance regressions
          - API compatibility issues
          
          Please check the test results and fix any issues.
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}