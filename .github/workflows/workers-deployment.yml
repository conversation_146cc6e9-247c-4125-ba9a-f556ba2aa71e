name: Workers Deployment Pipeline

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'workers/**'
      - '.github/workflows/workers-deployment.yml'
  pull_request:
    branches:
      - main
    paths:
      - 'workers/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: 'Force deployment (skip some checks)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

jobs:
  # Quality checks and testing
  quality-checks:
    name: Quality Checks
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./workers
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: workers/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Type checking
        run: npm run type-check

      - name: Linting
        run: npm run lint

      - name: Run tests
        run: npm run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          directory: ./workers/coverage
          flags: workers
          name: workers-coverage

      - name: Build workers
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: worker-builds
          path: workers/dist/
          retention-days: 7

  # Security scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./workers
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: workers/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=high

      - name: Run dependency check
        run: npx audit-ci --config .audit-ci.json
        continue-on-error: true

  # Performance testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [quality-checks]
    defaults:
      run:
        working-directory: ./workers
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: workers/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: worker-builds
          path: workers/dist/

      - name: Run performance tests
        run: npm run test:performance

      - name: Run comprehensive tests
        run: npm run test:comprehensive

  # Staging deployment
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quality-checks, security-scan]
    if: github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    defaults:
      run:
        working-directory: ./workers
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: workers/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: worker-builds
          path: workers/dist/

      - name: Deploy to staging
        run: npx tsx scripts/deploy.ts staging
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

      - name: Run staging health checks
        run: npm run health-check:staging

      - name: Comment deployment status
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🚀 Workers deployed to staging environment successfully!\n\n' +
                    '**Staging URLs:**\n' +
                    '- Image Optimizer: https://images-staging.syndicaps.com\n' +
                    '- API Cache: https://api-cache-staging.syndicaps.com\n\n' +
                    'Please test the deployment before merging.'
            })

  # Production deployment
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-checks, security-scan, performance-tests]
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    defaults:
      run:
        working-directory: ./workers
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: workers/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: worker-builds
          path: workers/dist/

      - name: Deploy to production
        run: npx tsx scripts/deploy.ts production
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          DEPLOYMENT_WEBHOOK_URL: ${{ secrets.DEPLOYMENT_WEBHOOK_URL }}

      - name: Run production health checks
        run: npm run health-check:production

      - name: Create deployment tag
        if: success()
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git tag -a "workers-v$(date +'%Y%m%d-%H%M%S')" -m "Workers deployment $(date +'%Y-%m-%d %H:%M:%S')"
          git push origin --tags

      - name: Notify deployment success
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.repos.createCommitComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha,
              body: '🎉 Workers successfully deployed to production!\n\n' +
                    '**Production URLs:**\n' +
                    '- Image Optimizer: https://images.syndicaps.com\n' +
                    '- API Cache: https://api-cache.syndicaps.com\n\n' +
                    'Deployment completed at ' + new Date().toISOString()
            })

  # Rollback job (manual trigger only)
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'rollback'
    environment: production
    defaults:
      run:
        working-directory: ./workers
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: workers/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Rollback production deployment
        run: npm run deploy:rollback
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

      - name: Verify rollback
        run: npm run health-check:production

      - name: Notify rollback completion
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.repos.createCommitComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha,
              body: '🔄 Production workers rolled back successfully!\n\n' +
                    'Rollback completed at ' + new Date().toISOString()
            })
