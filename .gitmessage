# AI Coordination Commit Template
# 
# Format: [AI] type: description
# 
# AI Options:
# [AUGMENT] - Augment Agent work
# [CURSOR]  - Claude Code work  
# [COLLAB]  - Collaborative work
# [HANDOFF] - Work ready for handoff
#
# Type Options:
# feat:     New feature
# fix:      Bug fix
# docs:     Documentation changes
# style:    Code style changes (formatting, etc.)
# refactor: Code refactoring
# test:     Adding or updating tests
# ui:       UI/UX improvements
# merge:    Merging branches
#
# Examples:
# [AUGMENT] feat: implement user level system architecture
# [CURSOR] fix: resolve button hover animation glitch
# [COLLAB] merge: integrate auth system with UI components
# [HANDOFF] feat: auth backend ready for UI integration
#
# Remember to:
# - Keep subject line under 50 characters
# - Use imperative mood ("add" not "added")
# - Include context for the other AI if relevant
# - Reference issue numbers if applicable
