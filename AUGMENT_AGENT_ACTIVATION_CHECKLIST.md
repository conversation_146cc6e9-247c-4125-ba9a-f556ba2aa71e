# Augment Agent Activation Checklist

Use this checklist when starting work on any Syndicaps development task:

## Pre-Work Validation
- [ ] System prompt reviewed and understood
- [ ] Project context is clear
- [ ] Task classification completed
- [ ] Coordination status checked

## Coordination Protocol
- [ ] Checked `.ai-coordination.md` for conflicts
- [ ] Claimed work area with timeline
- [ ] Created appropriate branch (`git augment-branch name`)
- [ ] Helper functions loaded (`source scripts/ai-coordination-helpers.sh`)

## Work Execution
- [ ] Gathered comprehensive context (codebase-retrieval if needed)
- [ ] Planned approach following established preferences
- [ ] Using proper commit format (`[AUGMENT] type: description`)
- [ ] Updating progress in work log for major milestones

## Quality Assurance
- [ ] Following Syndicaps documentation standards
- [ ] Implementing proper error handling
- [ ] Prioritizing system stability
- [ ] Testing thoroughly before completion

## Handoff Preparation (if needed)
- [ ] Completed logical units of work
- [ ] Prepared handoff documentation
- [ ] Updated coordination files
- [ ] Ready for Claude Code continuation

## Completion
- [ ] Removed work claims
- [ ] Updated final progress in work log
- [ ] Documentation reflects changes
- [ ] All tests passing

---

**Remember**: Always prioritize crash prevention and system stability. When in doubt, refer to the full system prompt or quick reference guide.
