# Augment Agent Quick Reference Card

## 🚀 Instant Activation Checklist

### Before Any Work
```bash
# 1. Check coordination status
source scripts/ai-coordination-helpers.sh
check-claims
ai-status

# 2. Gather context (if needed)
# Use codebase-retrieval for comprehensive understanding

# 3. Claim work area
# Update .ai-coordination.md manually

# 4. Create branch
git augment-branch feature-name
```

### During Work
```bash
# Commit frequently with proper format
git augment-feat "implement user level system"
git augment-fix "resolve authentication issue"
git augment-docs "create API documentation"
git augment-refactor "optimize database queries"

# Update progress for major milestones
update-log "Completed user level architecture design"
```

### After Work
```bash
# Remove work claim from .ai-coordination.md
# Update AI_WORK_LOG.md with completion
# Create handoff notes if needed (use HANDOFF_NOTES.md template)
```

## 🎯 Task Classification Quick Guide

| Task Type | Your Role | Action |
|-----------|-----------|---------|
| **Architecture/System Design** | ✅ Primary | Design, implement, document |
| **Documentation/Analysis** | ✅ Primary | Create comprehensive docs |
| **Backend Logic** | ✅ Primary | Implement business logic |
| **Database Operations** | ✅ Primary | Schema, queries, optimization |
| **Cross-file Refactoring** | ✅ Primary | Large-scale code organization |
| **UI Components** | 🤝 Coordinate | Design specs → Claude Code implements |
| **Frontend Styling** | 🤝 Coordinate | Requirements → Claude Code implements |
| **Testing Implementation** | 🤝 Coordinate | Strategy design → Claude Code implements |

## 📁 Work Area Quick Reference

### Your Primary Areas
- `docs/` - All documentation
- `scripts/` - Database/deployment scripts
- `src/lib/` - Core utilities
- `src/contexts/` - React contexts
- `src/hooks/` - Custom hooks
- `app/admin/` - Admin backend
- `functions/` - Firebase Functions
- Config files

### Coordinate Areas
- `src/components/` - UI components
- `app/` - Pages (you: logic, Claude: UI)
- `tests/` - Testing (you: strategy, Claude: implementation)

### Shared Areas (Sequential Only)
- `src/types/` - TypeScript definitions
- `src/store/` - State management
- `middleware.ts` - Auth middleware

## 🔄 Common Workflows

### New Feature Development
1. **Analysis** → Create feature analysis doc
2. **Architecture** → Design system integration
3. **Documentation** → Implementation roadmap
4. **Backend** → Implement core logic
5. **Handoff** → Specs for Claude Code UI
6. **Integration** → Connect backend + frontend
7. **Testing** → Quality assurance

### Bug Investigation
1. **Reproduce** → Document issue thoroughly
2. **Analyze** → Root cause with codebase context
3. **Plan** → Solution with minimal disruption
4. **Fix** → Implement with testing
5. **Document** → Update docs + prevention

### System Optimization
1. **Analyze** → Identify bottlenecks
2. **Plan** → Incremental improvement approach
3. **Implement** → Apply optimizations in phases
4. **Monitor** → Track performance improvements
5. **Document** → Update architecture docs

## 📝 Documentation Templates

### Feature Analysis
```markdown
# [Feature Name] Analysis

## Executive Summary
[Business-friendly overview]

## Technical Gap Analysis
[Current vs desired state]

## Implementation Roadmap
### Phase 1: [Foundation]
### Phase 2: [Core Features]
### Phase 3: [Enhancement]

## Priority Matrix
[Urgency vs Impact assessment]

## Architecture Specifications
[Technical details and diagrams]
```

### Handoff Template
```markdown
## Handoff: [Feature Name]

**From**: Augment Agent
**To**: Claude Code
**Date**: [YYYY-MM-DD]

### Completed Work
- [Backend implementation]
- [API endpoints]
- [Database schema]

### Next Steps for Claude Code
- [UI component requirements]
- [User interaction patterns]
- [Styling specifications]

### Technical Context
- [Key architectural decisions]
- [Data flow patterns]
- [Integration points]

### Files Modified
- `src/lib/[feature].ts` - Core logic
- `functions/src/[feature].js` - API endpoints
- `docs/[feature]-api.md` - Documentation
```

## 🚨 Emergency Protocols

### Production Issues
1. **Skip coordination checks** - proceed immediately
2. **Focus on stability** - minimal viable fix
3. **Update coordination** - as soon as stable
4. **Root cause analysis** - after stability restored
5. **Prevention measures** - implement safeguards

### Coordination Conflicts
1. **Backup work** - `git stash` or backup branch
2. **Communicate** - update coordination files
3. **Apply priority rules** - from coordination system
4. **Resolve quickly** - minimize development disruption
5. **Update system** - prevent similar conflicts

## 🎯 Quality Standards Checklist

### Code Quality
- [ ] TypeScript strict typing
- [ ] Comprehensive error handling
- [ ] Performance optimized
- [ ] Security validated
- [ ] Accessibility compliant

### Documentation
- [ ] Executive Summary included
- [ ] Technical analysis complete
- [ ] Implementation roadmap detailed
- [ ] Priority matrix provided
- [ ] Architecture specs documented

### Testing
- [ ] Unit tests for core logic
- [ ] Integration tests for system interactions
- [ ] E2E tests for critical user paths
- [ ] Performance tests for bottlenecks
- [ ] Security tests for sensitive operations

## 🔧 Git Aliases Quick Reference

```bash
# Feature development
git augment-feat "description"

# Bug fixes
git augment-fix "description"

# Documentation
git augment-docs "description"

# Refactoring
git augment-refactor "description"

# Testing
git augment-test "description"

# Branch creation
git augment-branch "feature-name"

# Handoff commits
git handoff-commit "feature ready for UI implementation"
```

## 📊 Success Metrics

- ✅ Zero production crashes
- ✅ Comprehensive documentation for all features
- ✅ Smooth handoffs with Claude Code
- ✅ Efficient task classification and routing
- ✅ Consistent Git workflow and commit formatting
- ✅ Regular coordination file updates
- ✅ Proactive communication and progress tracking

---

**Remember**: Always prioritize system stability, follow coordination protocols, and maintain comprehensive documentation. When in doubt, check the full system prompt in `SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md`.
