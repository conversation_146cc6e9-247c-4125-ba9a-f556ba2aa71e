# Claude Code (Cursor AI) - Exact Commands to Execute

## 🚀 COPY AND PASTE THESE COMMANDS

**Run these commands in your Cursor AI terminal in the exact order shown:**

### Step 1: Setup and Configuration
```bash
# Navigate to project root (adjust path as needed)
cd /path/to/syndicaps

# Run the setup script
chmod +x scripts/setup-ai-coordination.sh
./scripts/setup-ai-coordination.sh

# Load helper functions
source scripts/ai-coordination-helpers.sh
```

### Step 2: Validate Setup
```bash
# Run validation script
chmod +x scripts/validate-claude-code-setup.sh
./scripts/validate-claude-code-setup.sh
```

### Step 3: Test Your Configuration
```bash
# Test coordination status
ai-status

# Check current work claims
check-claims

# Test Git aliases
git cursor-feat "test setup complete"
```

## 📋 Files You MUST Review

### 1. Your Work Areas (CRITICAL)
```bash
cat .ai-coordination.md
```
**Look for the "### Claude Code (Cursor AI)" section to see your assigned areas**

### 2. Communication Log
```bash
cat AI_WORK_LOG.md
```
**This is where you'll update daily progress**

### 3. Handoff Procedures
```bash
cat HANDOFF_NOTES.md
```
**Templates for receiving work from Augment Agent**

## 🎯 Your Daily Workflow Commands

### Starting Work
```bash
# 1. Load functions and check status
source scripts/ai-coordination-helpers.sh
ai-status

# 2. Check for new work or handoffs
check-claims
tail -20 HANDOFF_NOTES.md

# 3. Claim your work area (edit .ai-coordination.md manually)
# Add under "### Claude Code (Cursor AI)" section:
# - `src/components/feature/` (until YYYY-MM-DD HH:MM) - Description

# 4. Create branch and start work
git cursor-branch feature-name
```

### During Development
```bash
# Make commits with proper format
git cursor-feat "implement user profile component"
git cursor-ui "add purple hover effects to buttons"
git cursor-fix "resolve mobile responsive layout issue"
git cursor-style "apply dark theme to navigation"

# Update progress for major milestones
update-log "Completed user level badge components with animations"
```

### Completing Work
```bash
# 1. Remove your claim from .ai-coordination.md
# 2. Update final progress
update-log "Completed feature X - ready for integration testing"

# 3. If handing back to Augment Agent, add to HANDOFF_NOTES.md:
# Use the handoff template from the file
```

## 🧪 Validation Checklist

**Run this command to verify everything is working:**
```bash
./scripts/validate-claude-code-setup.sh
```

**Expected result:** All tests should pass (✅)

**If any tests fail:**
1. Re-run the setup script: `./scripts/setup-ai-coordination.sh`
2. Check file permissions: `chmod +x scripts/*.sh`
3. Reload functions: `source scripts/ai-coordination-helpers.sh`

## 🎯 Your Primary Responsibilities

### What You Handle:
- **UI Components**: React components in `src/components/`
- **Page Layouts**: Frontend parts of `app/` routes
- **Styling**: CSS, Tailwind, animations in `src/styles/`
- **User Interactions**: Click handlers, form submissions, animations
- **Testing Implementation**: Writing actual test code
- **Responsive Design**: Mobile/desktop layouts
- **Accessibility**: ARIA labels, keyboard navigation

### What You Coordinate With Augment Agent:
- **Architecture**: Receive designs and specifications
- **Backend Integration**: Connect to APIs and data
- **Testing Strategy**: Implement test plans they design
- **Performance**: Apply optimization recommendations

## 🚨 Emergency Commands

**If something breaks:**
```bash
# Quick status check
source scripts/ai-coordination-helpers.sh && ai-status

# Check recent coordination activity
git log --oneline -10 --grep="\[CURSOR\]\|\[AUGMENT\]"

# Report issue in work log
update-log "ISSUE: [describe problem] - need Augment Agent assistance"
```

## ✅ Success Indicators

**You'll know the setup is working when:**
- [ ] `ai-status` command shows coordination information
- [ ] `check-claims` displays current work assignments
- [ ] `git cursor-feat "test"` creates commits with `[CURSOR]` prefix
- [ ] You can read all coordination files without errors
- [ ] Validation script shows all tests passing

## 🔄 Integration Test

**To test the full workflow:**

1. **Check coordination:**
   ```bash
   source scripts/ai-coordination-helpers.sh
   check-claims
   ```

2. **Claim test work:**
   Edit `.ai-coordination.md` and add under your section:
   ```
   - `src/components/test/` (until 2025-01-18 16:00) - Testing coordination workflow
   ```

3. **Create branch and commit:**
   ```bash
   git cursor-branch test-coordination
   git cursor-feat "test coordination system integration"
   ```

4. **Update progress:**
   ```bash
   update-log "Successfully tested coordination workflow"
   ```

5. **Clean up:**
   Remove the test claim from `.ai-coordination.md`

## 📞 Getting Help

**If you encounter issues:**

1. **Update the work log:**
   ```bash
   update-log "HELP NEEDED: [describe issue]"
   ```

2. **Check the setup guide:**
   ```bash
   cat CLAUDE_CODE_SETUP_GUIDE.md
   ```

3. **Re-run validation:**
   ```bash
   ./scripts/validate-claude-code-setup.sh
   ```

---

**🎯 Once these commands complete successfully, you're ready for coordinated development with Augment Agent!**

**Quick Reference:** Save this command for daily use:
```bash
source scripts/ai-coordination-helpers.sh && ai-status && check-claims
```
