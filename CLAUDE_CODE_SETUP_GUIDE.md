# Claude Code (Cursor AI) - AI Coordination Setup Guide

## 🚀 Quick Setup Commands

**Copy and paste these commands in your Cursor AI terminal:**

```bash
# 1. Navigate to project root (if not already there)
cd /path/to/syndicaps

# 2. Make setup script executable and run it
chmod +x scripts/setup-ai-coordination.sh
./scripts/setup-ai-coordination.sh

# 3. Load helper functions
source scripts/ai-coordination-helpers.sh

# 4. Test your setup
ai-status
check-claims

# 5. Test Git aliases
git cursor-feat "test coordination setup"
```

## 📋 Step-by-Step Setup Process

### Step 1: Verify Project Structure
```bash
# Ensure you're in the correct directory
pwd
# Should show: /path/to/syndicaps

# Check for coordination files
ls -la | grep -E "(\.ai-coordination|AI_WORK_LOG|HANDOFF_NOTES)"
# Should show all coordination files exist
```

### Step 2: Run Configuration Scripts
```bash
# Run the main setup script
./scripts/setup-ai-coordination.sh

# Expected output should show:
# ✅ Git aliases configured!
# ✅ Commit template created!
# ✅ Helper functions created!
# ✅ VS Code snippets created!
# ✅ System ready for coordinated AI development!
```

### Step 3: Load Helper Functions
```bash
# Load coordination helper functions
source scripts/ai-coordination-helpers.sh

# Test that functions are available
type ai-status
type check-claims
type update-log

# All should show "is a function"
```

### Step 4: Test Git Aliases
```bash
# Test your Git aliases (these should work without errors)
git config --get alias.cursor-feat
git config --get alias.cursor-fix
git config --get alias.cursor-ui
git config --get alias.cursor-branch

# Each should return the alias definition
```

### Step 5: Validate Coordination System
```bash
# Check current coordination status
ai-status

# View current work claims
check-claims

# Both commands should run without errors
```

## 📁 Essential Files to Review

### 1. **Work Area Assignments** (MUST READ)
```bash
cat .ai-coordination.md
```
**Your Primary Areas:**
- `src/components/` - React components and UI
- `app/` - Next.js pages and routes (UI parts)
- `src/styles/` - CSS and styling
- `public/` - Static assets
- `tests/` - Test implementation
- Component interactions and animations

### 2. **Daily Communication Log**
```bash
cat AI_WORK_LOG.md
```
**Usage:** Update this daily with your progress

### 3. **Handoff Procedures**
```bash
cat HANDOFF_NOTES.md
```
**Usage:** Use templates when receiving/giving work to Augment Agent

### 4. **Quick Reference Guide**
```bash
cat AUGMENT_AGENT_QUICK_REFERENCE.md
```
**Usage:** Understand the overall coordination system

## 🎯 Your Role in the Coordination System

### **Claude Code (Cursor AI) Responsibilities:**
- **UI/UX Implementation**: Convert Augment Agent's designs into working components
- **Interactive Development**: Real-time coding, debugging, rapid iteration
- **Component Development**: React components, styling, user interactions
- **Testing Implementation**: Write and run tests based on Augment Agent's strategies
- **Frontend Optimization**: Performance, accessibility, responsive design

### **Coordination with Augment Agent:**
- **Receive**: Architecture designs, API specifications, backend logic
- **Implement**: UI components, user interactions, styling
- **Handoff**: Completed features back for integration testing

## 🔄 Daily Workflow for Claude Code

### Before Starting Work:
```bash
# 1. Check coordination status
source scripts/ai-coordination-helpers.sh
check-claims

# 2. Review any new handoffs
cat HANDOFF_NOTES.md | tail -20

# 3. Claim your work area (manually edit .ai-coordination.md)
# Add your work under "### Claude Code (Cursor AI)" section
```

### During Development:
```bash
# 1. Create appropriate branch
git cursor-branch feature-name

# 2. Make commits with proper format
git cursor-feat "implement user profile component"
git cursor-ui "add hover animations to navigation"
git cursor-fix "resolve mobile responsive issues"
git cursor-style "apply purple theme to buttons"

# 3. Update progress for major milestones
update-log "Completed user level badge components"
```

### After Completing Work:
```bash
# 1. Remove your claim from .ai-coordination.md
# 2. Update AI_WORK_LOG.md with completion
# 3. Create handoff notes if passing work back to Augment Agent
```

## 🧪 Testing Your Setup

### Test 1: Helper Functions
```bash
source scripts/ai-coordination-helpers.sh

# Test each function
ai-status
check-claims
handoff-template
update-log "Testing coordination system setup"

# All should work without errors
```

### Test 2: Git Aliases
```bash
# Test creating a branch
git cursor-branch test-setup

# Test making commits
git cursor-feat "test coordination system"
git cursor-ui "test UI development workflow"

# Check commit history
git log --oneline -3

# Should show commits with [CURSOR] prefix
```

### Test 3: Coordination File Access
```bash
# Test reading coordination files
head -10 .ai-coordination.md
head -10 AI_WORK_LOG.md
head -10 HANDOFF_NOTES.md

# All should display content without errors
```

### Test 4: Work Claim Process
```bash
# 1. Check current claims
check-claims

# 2. Manually edit .ai-coordination.md to add a test claim:
# Under "### Claude Code (Cursor AI)" section, add:
# - `src/components/test/` (until 2025-01-18 16:00) - Testing coordination setup

# 3. Verify claim appears
check-claims

# 4. Remove test claim after verification
```

## 🚨 Troubleshooting

### If Git Aliases Don't Work:
```bash
# Re-run setup script
./scripts/setup-ai-coordination.sh

# Manually check Git config
git config --list | grep cursor
```

### If Helper Functions Don't Load:
```bash
# Check if file exists
ls -la scripts/ai-coordination-helpers.sh

# Try loading with full path
source ./scripts/ai-coordination-helpers.sh

# Check for syntax errors
bash -n scripts/ai-coordination-helpers.sh
```

### If Coordination Files Are Missing:
```bash
# Check if files exist
ls -la .ai-coordination.md AI_WORK_LOG.md HANDOFF_NOTES.md

# If missing, contact Augment Agent to regenerate
```

## ✅ Setup Validation Checklist

- [ ] Setup script ran successfully
- [ ] Helper functions load without errors
- [ ] Git aliases are configured (`git config --get alias.cursor-feat`)
- [ ] Can run `ai-status` and `check-claims`
- [ ] Can create branches with `git cursor-branch name`
- [ ] Can make commits with `git cursor-feat "description"`
- [ ] Can read all coordination files
- [ ] Understand work area assignments
- [ ] Know how to claim work areas
- [ ] Know how to update progress logs

## 🎯 Ready to Start Coordinated Development!

Once all items in the checklist are complete, you're ready to participate in the coordinated development workflow. 

### Your Next Steps:
1. **Review current work claims** in `.ai-coordination.md`
2. **Check for any handoffs** waiting for you in `HANDOFF_NOTES.md`
3. **Start with a small task** to test the workflow
4. **Communicate with Augment Agent** through the coordination files

### Quick Commands Reference:
```bash
# Daily startup
source scripts/ai-coordination-helpers.sh && ai-status

# Start new work
git cursor-branch feature-name

# Make commits
git cursor-feat "description"

# Update progress
update-log "progress description"

# Check for work
check-claims
```

---

**Support**: If you encounter any issues, update the `AI_WORK_LOG.md` with your problem, and Augment Agent will assist with troubleshooting.
