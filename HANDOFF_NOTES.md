# AI Handoff Documentation

This file contains templates and active handoff notes for coordinating work between Augment Agent and Claude Code.

## Active Handoffs

*No active handoffs currently*

---

## Handoff Templates

### Standard Handoff Template

```markdown
## Handoff: [Feature/Component/Area Name]

**From**: [Augment Agent / Claude Code]
**To**: [Augment Agent / Claude Code]
**Date**: [YYYY-MM-DD HH:MM]
**Priority**: [High / Medium / Low]

### Work Completed
- [ ] [Specific task 1]
- [ ] [Specific task 2]
- [ ] [Specific task 3]

### Next Steps Required
1. **Immediate**: [What needs to be done first]
2. **Follow-up**: [Subsequent tasks]
3. **Testing**: [What needs to be tested]

### Technical Context
**Architecture Decisions Made**:
- [Decision 1 and rationale]
- [Decision 2 and rationale]

**Dependencies**:
- [External dependencies]
- [Internal code dependencies]
- [Data dependencies]

**Known Issues/Gotchas**:
- [Issue 1 and workaround]
- [Issue 2 and workaround]

### Files Modified
**Primary Files**:
- `path/to/file1.ts` - [Brief description of changes]
- `path/to/file2.tsx` - [Brief description of changes]

**Supporting Files**:
- `path/to/config.js` - [Configuration changes]
- `path/to/types.ts` - [Type definitions added]

**Tests**:
- `tests/path/to/test.spec.ts` - [Test coverage added]

### Code Examples
**Key Functions/Components**:
```typescript
// Example of important code pattern or interface
interface ExampleInterface {
  // Implementation details
}
```

**Usage Patterns**:
```typescript
// How to use the implemented functionality
const example = useExample();
```

### Testing Instructions
**Unit Tests**:
```bash
npm test path/to/specific/test
```

**Integration Tests**:
```bash
npm run test:integration
```

**Manual Testing**:
1. [Step 1]
2. [Step 2]
3. [Expected result]

### Documentation Updates Needed
- [ ] Update README.md
- [ ] Update API documentation
- [ ] Update component documentation
- [ ] Update architecture diagrams

### Handoff Checklist
- [ ] All code committed and pushed
- [ ] Tests passing
- [ ] Documentation updated
- [ ] Receiving AI notified
- [ ] Work claim updated in `.ai-coordination.md`
```

### Quick Handoff Template

```markdown
## Quick Handoff: [Brief Description]

**From**: [AI] **To**: [AI] **Date**: [YYYY-MM-DD]

**Completed**: [What was finished]
**Next**: [What needs to be done]
**Files**: [Key files to focus on]
**Notes**: [Any important context]
```

### Emergency Handoff Template

```markdown
## EMERGENCY Handoff: [Critical Issue]

**From**: [AI] **To**: [AI] **Date**: [YYYY-MM-DD HH:MM]
**Urgency**: CRITICAL

**Issue**: [Description of critical problem]
**Impact**: [What's broken/affected]
**Attempted Solutions**: [What was already tried]
**Current State**: [Exact state of the code]
**Immediate Action Required**: [What needs to be done NOW]

**Files Involved**:
- [Critical files]

**Rollback Option**: [How to revert if needed]
```

---

## Handoff Best Practices

### Before Handoff
1. **Complete Current Work**: Finish logical units of work
2. **Test Thoroughly**: Ensure your changes work as expected
3. **Clean Up**: Remove debug code, console.logs, temporary files
4. **Document**: Add comments for complex logic
5. **Commit**: Make clean, descriptive commits

### During Handoff
1. **Use Template**: Fill out appropriate handoff template completely
2. **Be Specific**: Provide exact file paths, function names, line numbers
3. **Include Context**: Explain WHY decisions were made, not just WHAT
4. **Provide Examples**: Show how to use new functionality
5. **List Dependencies**: Mention any external factors

### After Handoff
1. **Update Claims**: Remove your claim from `.ai-coordination.md`
2. **Monitor**: Be available for questions during transition
3. **Follow Up**: Check that receiving AI successfully took over
4. **Archive**: Move completed handoff to archive section

---

## Handoff Archive

### Completed Handoffs
*Archive completed handoffs here for reference*

---

## Common Handoff Scenarios

### Architecture → Implementation
**Typical Flow**: Augment Agent designs system → Claude Code implements UI
**Key Information**: API contracts, data structures, component interfaces

### Implementation → Testing
**Typical Flow**: Claude Code builds feature → Augment Agent adds comprehensive tests
**Key Information**: Test scenarios, edge cases, performance requirements

### Bug Investigation → Fix
**Typical Flow**: Augment Agent analyzes issue → Claude Code implements fix
**Key Information**: Root cause, reproduction steps, solution approach

### Feature → Documentation
**Typical Flow**: Either AI builds feature → Augment Agent documents
**Key Information**: User workflows, API changes, configuration options

---

**Template Usage**: Copy appropriate template, fill in details, commit to repository
