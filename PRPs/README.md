# 📋 Product Requirements Prompts (PRPs) for Syndicaps

*Comprehensive templates for AI-assisted feature development*

## 🎯 Overview

Product Requirements Prompts (PRPs) are specialized templates designed to help AI assistants implement features correctly and consistently within the Syndicaps codebase. Each template provides comprehensive context, patterns, and validation steps to ensure high-quality implementations.

## 📚 Template Library

### Base Templates

#### 🔧 [syndicaps_base_prp.md](./templates/syndicaps_base_prp.md)
**The foundational PRP template for React/Next.js/Firebase features**
- Comprehensive project context and patterns
- Multi-phase implementation blueprint
- Extensive validation and quality assurance steps
- Anti-patterns to avoid
- Context Engineering principles

Use this template for:
- Complex features with multiple components
- Features requiring Firebase integration
- New pages or major functionality
- Features that need comprehensive testing

#### 🧩 [syndicaps_component_prp.md](./templates/syndicaps_component_prp.md)
**Specialized template for creating new React components**
- Design system integration patterns
- Component architecture and props design
- Accessibility and responsive design requirements
- Comprehensive testing strategies
- Storybook documentation (if applicable)

Use this template for:
- New UI components for the design system
- Complex interactive components
- Form components with validation
- Components with animations or state management

#### 📄 [syndicaps_page_prp.md](./templates/syndicaps_page_prp.md)
**Template for creating new Next.js 15 pages**
- App Router and routing patterns
- SEO optimization and metadata
- Loading and error state handling
- Performance optimization
- Mobile responsiveness

Use this template for:
- New application pages
- Landing pages with SEO requirements
- Admin dashboard pages
- User profile and settings pages

#### 🪝 [syndicaps_hook_prp.md](./templates/syndicaps_hook_prp.md)
**Template for custom React hooks**
- React hooks rules compliance
- Firebase integration patterns
- Performance optimization techniques
- Memory leak prevention
- Comprehensive testing strategies

Use this template for:
- Data fetching hooks
- Firebase collection hooks
- Local storage or state management hooks
- Custom business logic hooks

#### 🔗 [syndicaps_api_prp.md](./templates/syndicaps_api_prp.md)
**Template for Next.js 15 API routes**
- RESTful API design patterns
- Authentication and authorization
- Request/response validation
- Rate limiting and security
- Firebase backend integration

Use this template for:
- REST API endpoints
- Webhook handlers
- Data processing endpoints
- Integration with external services

## 🚀 How to Use PRPs

### 1. Choose the Right Template
Select the most specific template for your task:
- **Component creation** → Use `syndicaps_component_prp.md`
- **Page creation** → Use `syndicaps_page_prp.md`
- **Hook creation** → Use `syndicaps_hook_prp.md`
- **API endpoint** → Use `syndicaps_api_prp.md`
- **Complex feature** → Use `syndicaps_base_prp.md`

### 2. Customize the Template
1. Copy the appropriate template
2. Replace placeholders with your specific requirements:
   - `[ComponentName]` → Your component name
   - `[PageName]` → Your page name
   - `[HookName]` → Your hook name
   - `[route-path]` → Your API route path
   - `[specific functionality description]` → What you're building

### 3. Provide to AI Assistant
Give the customized PRP to your AI assistant along with:
- Clear description of what you want to build
- Any specific requirements or constraints
- References to existing code that should be followed

### 4. Follow the Implementation Plan
The AI assistant will follow the PRP's implementation plan:
- **Phase 1**: Core structure and setup
- **Phase 2**: Business logic implementation
- **Phase 3**: Testing and validation
- **Phase 4**: Documentation and integration

### 5. Validate Results
Use the checklist in each PRP to ensure:
- ✅ Code quality standards met
- ✅ Testing requirements satisfied
- ✅ Performance targets achieved
- ✅ Security considerations addressed

## 🎯 PRP Features

### Context Engineering
Each PRP includes comprehensive context to help AI assistants understand:
- **Project Architecture**: How Syndicaps is structured
- **Existing Patterns**: Proven patterns to follow
- **Anti-Patterns**: Common mistakes to avoid
- **Quality Standards**: Testing and performance requirements

### Defensive Programming
All templates emphasize defensive programming:
- **Firebase Availability**: Always check if services are available
- **Error Handling**: Comprehensive error catching and user feedback
- **Input Validation**: Validate all inputs with Zod schemas
- **Memory Management**: Proper cleanup of listeners and timers

### Quality Assurance
Multi-level validation ensures working code:
- **Level 1**: Code quality (linting, TypeScript, build)
- **Level 2**: Testing (unit tests, coverage, E2E)
- **Level 3**: Accessibility (WCAG compliance, keyboard nav)
- **Level 4**: Performance (Core Web Vitals, bundle size)
- **Level 5**: Integration (Firebase, security, real-world testing)

## 📖 Template Anatomy

### Standard PRP Structure
1. **🎯 Goal**: Clear objective and scope
2. **🔍 Why**: Business value and technical reasoning
3. **📋 What**: Detailed specifications and success criteria
4. **📚 Required Context**: Files and patterns to reference
5. **🏗️ Implementation Plan**: Step-by-step phases
6. **🔍 Validation Steps**: Quality assurance checkpoints
7. **✅ Final Checklist**: Comprehensive completion criteria

### Key Sections

#### Required Context
Every PRP specifies exactly which files the AI should read:
```yaml
- file: CLAUDE.md
  why: Global AI assistant rules and conventions
  
- file: examples/components/SimpleComponent.tsx
  why: Basic component pattern with TypeScript and accessibility
```

#### Implementation Plan
Structured phases prevent the AI from skipping important steps:
```typescript
// Phase 1: Core Structure
CREATE src/components/[feature-name]/[MainComponent].tsx:
  - FOLLOW pattern from: examples/components/SimpleComponent.tsx
  - INCLUDE TypeScript interface with JSDoc comments
  - ADD proper error boundary wrapper
```

#### Validation Steps
Multiple levels ensure quality:
```bash
# Level 1: Code Quality
npm run lint                     # ESLint + TypeScript checks
npm run build                    # Production build verification
npm run typecheck               # TypeScript compilation
```

## 🔧 Advanced Usage

### Custom PRP Creation
For specialized use cases, create custom PRPs by:
1. Starting with the base template
2. Adding domain-specific requirements
3. Including relevant existing code patterns
4. Defining custom validation steps

### PRP Chaining
For complex features, chain multiple PRPs:
1. Use base PRP for overall feature planning
2. Use component PRP for UI components
3. Use hook PRP for custom logic
4. Use API PRP for backend integration

### Quality Gates
Implement quality gates using PRP validation:
- **Pre-commit**: Run linting and type checking
- **Pre-deploy**: Run full test suite and build
- **Post-deploy**: Monitor performance and errors

## 📊 Best Practices

### For AI Assistants
1. **Read All Referenced Files**: Don't skip the "Required Context" section
2. **Follow Patterns**: Use existing code patterns instead of inventing new ones
3. **Implement Defensively**: Always check Firebase availability and handle errors
4. **Test Thoroughly**: Use the testing patterns from examples
5. **Validate Comprehensively**: Complete all validation steps before finishing

### For Developers
1. **Be Specific**: Customize PRPs with detailed requirements
2. **Provide Context**: Include relevant existing code references
3. **Review Results**: Use the checklists to verify implementation quality
4. **Iterate**: Refine PRPs based on results and feedback

### For Teams
1. **Standardize**: Use PRPs consistently across the team
2. **Evolve**: Update templates as patterns and requirements change
3. **Document**: Keep PRPs updated with new learnings and patterns
4. **Share**: Contribute successful PRP variations back to the library

## 🚨 Common Pitfalls

### Anti-Patterns to Avoid
- **Skipping Context**: Not reading the required files
- **Ignoring Patterns**: Creating new patterns instead of following existing ones
- **Missing Validation**: Skipping quality assurance steps
- **Incomplete Testing**: Not achieving the required test coverage
- **Poor Error Handling**: Not implementing defensive programming

### Quality Issues
- **Type Safety**: Skipping TypeScript strict mode compliance
- **Accessibility**: Not testing with keyboard navigation and screen readers
- **Performance**: Ignoring Core Web Vitals and bundle size impact
- **Security**: Not validating inputs or checking authentication

## 📈 Success Metrics

Track PRP effectiveness through:
- **Code Quality**: Reduced bugs and improved maintainability
- **Development Speed**: Faster feature implementation
- **Consistency**: More uniform code patterns across the codebase
- **Quality**: Higher test coverage and better error handling

## 🔮 Future Enhancements

Planned improvements to the PRP system:
- **Automated Validation**: Scripts to verify PRP compliance
- **Template Generator**: Tools to create custom PRPs
- **Integration Testing**: End-to-end validation workflows
- **Performance Monitoring**: Automated performance regression detection

---

## 🤝 Contributing

To improve the PRP system:

1. **Report Issues**: If a PRP doesn't work well for your use case
2. **Suggest Improvements**: Propose enhancements to existing templates
3. **Share Patterns**: Contribute new patterns you've discovered
4. **Document Learnings**: Update templates with new insights

## 📞 Support

For help with PRPs:
- Check the [examples directory](../examples/) for implementation patterns
- Review [CLAUDE.md](../CLAUDE.md) for general AI assistant guidelines
- Consult [PLANNING.md](../PLANNING.md) for architecture details
- Reference [TASK.md](../TASK.md) for current project status

---

*Product Requirements Prompts are a key part of Syndicaps' Context Engineering system, designed to ensure consistent, high-quality feature development with AI assistance.*