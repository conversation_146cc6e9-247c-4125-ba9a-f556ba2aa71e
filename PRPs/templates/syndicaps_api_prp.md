# Syndicaps API Route PRP Template v1.0

**Specialized template for creating Next.js 15 API routes in the Syndicaps application**

## 🎯 Goal
Create an API route at `/api/[route-path]` that [specific functionality description]

## 🔍 Why
- **Backend Logic**: [What server-side functionality this provides]
- **Data Processing**: [How this handles data transformation or validation]
- **Integration**: [How this connects with Firebase, external APIs, or services]

## 📋 What

### API Specifications
- **Route**: `/api/[route-path]`
- **HTTP Methods**: [GET, POST, PUT, DELETE, etc.]
- **Request Format**: [JSON body structure, query parameters, headers]
- **Response Format**: [Success/error response structures]
- **Authentication**: [Public, authenticated users, admin only, API key]
- **Rate Limiting**: [Requests per minute/hour limits]

### Success Criteria
- [ ] API responds with correct HTTP status codes
- [ ] Request/response validation with Zod schemas
- [ ] Comprehensive error handling with user-friendly messages
- [ ] Authentication and authorization work correctly
- [ ] Rate limiting prevents abuse
- [ ] Proper logging for debugging and monitoring
- [ ] CORS headers configured correctly

## 📚 Required Context

### 🔴 MUST READ
```yaml
# Base Patterns
- file: CLAUDE.md
  why: Global AI assistant rules and API conventions
  
- file: PLANNING.md
  section: "API Design & Security"
  why: API patterns and security requirements

# Firebase Patterns
- file: src/lib/firebase/monitoring.ts
  why: Firebase defensive programming patterns

- file: examples/hooks/useFirebaseQuery.ts
  why: Firebase integration patterns with error handling

# Testing Patterns
- file: examples/tests/component.test.tsx
  why: Testing patterns and mocking strategies

# Existing API Routes (for patterns)
- file: app/api/auth/route.ts
  why: Authentication patterns (if exists)

- file: app/api/webhooks/route.ts
  why: Webhook handling patterns (if exists)
```

### 🔥 Next.js 15 API Route Patterns
```typescript
// Route Handler Structure
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// Request/Response Types
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// HTTP Method Handlers
export async function GET(request: NextRequest) {
  try {
    // GET logic
    return NextResponse.json({ success: true, data: result })
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request: NextRequest) {
  try {
    // POST logic
    return NextResponse.json({ success: true, data: result })
  } catch (error) {
    return handleApiError(error)
  }
}

// Error Handler
function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error)
  
  if (error instanceof z.ZodError) {
    return NextResponse.json(
      { success: false, error: 'Validation failed', details: error.errors },
      { status: 400 }
    )
  }
  
  return NextResponse.json(
    { success: false, error: 'Internal server error' },
    { status: 500 }
  )
}
```

### 🔒 Security & Validation Patterns
```typescript
// Input Validation with Zod
const RequestSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  data: z.array(z.object({
    id: z.string(),
    value: z.number().positive()
  }))
})

// Authentication Check
async function requireAuth(request: NextRequest): Promise<User | null> {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) return null
    
    const decodedToken = await admin.auth().verifyIdToken(token)
    return { uid: decodedToken.uid, email: decodedToken.email }
  } catch (error) {
    console.error('Auth verification failed:', error)
    return null
  }
}

// Rate Limiting
const rateLimiter = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(ip: string, limit: number = 100): boolean {
  const now = Date.now()
  const windowMs = 60 * 1000 // 1 minute
  
  const current = rateLimiter.get(ip)
  if (!current || now > current.resetTime) {
    rateLimiter.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (current.count >= limit) {
    return false
  }
  
  current.count++
  return true
}
```

## 🏗️ Implementation Plan

### Phase 1: Route Structure
```typescript
// Task 1: Create API route file
CREATE app/api/[route-path]/route.ts:
  - DEFINE request/response TypeScript interfaces
  - IMPLEMENT HTTP method handlers (GET, POST, etc.)
  - ADD comprehensive error handling
  - INCLUDE request validation with Zod
  - ENSURE proper HTTP status codes

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { headers } from 'next/headers'

// Request validation schemas
const GetRequestSchema = z.object({
  // Query parameter validation
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  filter: z.string().optional()
})

const PostRequestSchema = z.object({
  // Request body validation
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(1000),
  metadata: z.object({
    category: z.string(),
    tags: z.array(z.string())
  }).optional()
})

// Response type definitions
interface [ApiName]Response {
  success: boolean
  data?: any
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    hasMore: boolean
  }
}
```

### Phase 2: Authentication & Authorization
```typescript
// Task 2: Implement authentication (if required)
import { auth } from '@/lib/firebase/admin'

async function verifyAuthentication(request: NextRequest): Promise<{
  user: { uid: string; email: string } | null
  error: string | null
}> {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { user: null, error: 'No authorization header' }
    }
    
    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      return { user: null, error: 'No token provided' }
    }
    
    const decodedToken = await auth.verifyIdToken(token)
    return {
      user: { uid: decodedToken.uid, email: decodedToken.email || '' },
      error: null
    }
  } catch (error) {
    console.error('Authentication failed:', error)
    return { user: null, error: 'Invalid token' }
  }
}

// Authorization helper
function checkPermission(user: { uid: string }, requiredRole: string): boolean {
  // Implement role-based access control
  return true // Placeholder
}
```

### Phase 3: Core API Logic
```typescript
// Task 3: Implement main API functionality
export async function GET(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip || 'unknown'
    if (!checkRateLimit(ip, 100)) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }
    
    // Parse and validate query parameters
    const url = new URL(request.url)
    const rawParams = Object.fromEntries(url.searchParams)
    const params = GetRequestSchema.parse(rawParams)
    
    // Authentication (if required)
    const { user, error: authError } = await verifyAuthentication(request)
    if (authError) {
      return NextResponse.json(
        { success: false, error: authError },
        { status: 401 }
      )
    }
    
    // Main business logic
    const result = await performGetOperation(params, user)
    
    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    })
    
  } catch (error) {
    console.error('GET /api/[route-path] error:', error)
    return handleApiError(error)
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip || 'unknown'
    if (!checkRateLimit(ip, 50)) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validatedData = PostRequestSchema.parse(body)
    
    // Authentication (if required)
    const { user, error: authError } = await verifyAuthentication(request)
    if (authError) {
      return NextResponse.json(
        { success: false, error: authError },
        { status: 401 }
      )
    }
    
    // Main business logic
    const result = await performPostOperation(validatedData, user)
    
    return NextResponse.json(
      { success: true, data: result },
      { status: 201 }
    )
    
  } catch (error) {
    console.error('POST /api/[route-path] error:', error)
    return handleApiError(error)
  }
}
```

### Phase 4: Firebase Integration
```typescript
// Task 4: Implement Firebase operations with defensive programming
import { db } from '@/lib/firebase/admin'
import { collection, doc, getDocs, addDoc, updateDoc, deleteDoc, query, where, orderBy, limit, startAfter } from 'firebase/firestore'

async function performGetOperation(
  params: z.infer<typeof GetRequestSchema>,
  user: { uid: string } | null
) {
  if (!db) {
    throw new Error('Database service unavailable')
  }
  
  try {
    const collectionRef = collection(db, 'your-collection')
    let q = query(collectionRef)
    
    // Add query constraints
    if (params.filter) {
      q = query(q, where('category', '==', params.filter))
    }
    
    // Add ordering and pagination
    q = query(q, orderBy('createdAt', 'desc'))
    
    if (params.limit) {
      q = query(q, limit(params.limit))
    }
    
    const snapshot = await getDocs(q)
    const documents = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))
    
    return {
      data: documents,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 20,
        total: documents.length,
        hasMore: documents.length === (params.limit || 20)
      }
    }
  } catch (error) {
    console.error('Firebase GET operation failed:', error)
    throw new Error('Failed to fetch data')
  }
}

async function performPostOperation(
  data: z.infer<typeof PostRequestSchema>,
  user: { uid: string }
) {
  if (!db) {
    throw new Error('Database service unavailable')
  }
  
  try {
    const collectionRef = collection(db, 'your-collection')
    const docData = {
      ...data,
      createdBy: user.uid,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    const docRef = await addDoc(collectionRef, docData)
    
    return {
      id: docRef.id,
      ...docData
    }
  } catch (error) {
    console.error('Firebase POST operation failed:', error)
    throw new Error('Failed to create document')
  }
}
```

### Phase 5: Error Handling & Logging
```typescript
// Task 5: Implement comprehensive error handling
function handleApiError(error: unknown): NextResponse {
  // Log error details for debugging
  console.error('API Error Details:', {
    error: error instanceof Error ? error.message : 'Unknown error',
    stack: error instanceof Error ? error.stack : undefined,
    timestamp: new Date().toISOString()
  })
  
  // Zod validation errors
  if (error instanceof z.ZodError) {
    return NextResponse.json(
      {
        success: false,
        error: 'Validation failed',
        details: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
      },
      { status: 400 }
    )
  }
  
  // Known application errors
  if (error instanceof Error) {
    // Don't expose internal error messages in production
    const isProduction = process.env.NODE_ENV === 'production'
    const message = isProduction ? 'Internal server error' : error.message
    
    return NextResponse.json(
      { success: false, error: message },
      { status: 500 }
    )
  }
  
  // Unknown errors
  return NextResponse.json(
    { success: false, error: 'An unexpected error occurred' },
    { status: 500 }
  )
}

// Request logging middleware
function logRequest(request: NextRequest, method: string) {
  console.log(`API ${method} ${request.url}`, {
    userAgent: request.headers.get('user-agent'),
    ip: request.ip,
    timestamp: new Date().toISOString()
  })
}
```

### Phase 6: Testing Implementation
```typescript
// Task 6: Create comprehensive API tests
CREATE app/api/[route-path]/__tests__/route.test.ts:
  - TEST all HTTP methods and status codes
  - VERIFY request/response validation
  - CHECK authentication and authorization
  - TEST error scenarios and edge cases
  - ENSURE rate limiting works correctly

import { GET, POST } from '../route'
import { NextRequest } from 'next/server'

// Mock Firebase
jest.mock('@/lib/firebase/admin', () => ({
  db: {
    collection: jest.fn(),
    doc: jest.fn()
  },
  auth: {
    verifyIdToken: jest.fn()
  }
}))

describe('/api/[route-path]', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  describe('GET', () => {
    it('returns data for valid request', async () => {
      const request = new NextRequest('http://localhost/api/test?page=1&limit=10')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toBeDefined()
    })
    
    it('returns 400 for invalid query parameters', async () => {
      const request = new NextRequest('http://localhost/api/test?page=invalid')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Validation failed')
    })
    
    it('returns 401 for invalid authentication', async () => {
      const request = new NextRequest('http://localhost/api/test', {
        headers: { authorization: 'Bearer invalid-token' }
      })
      
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
    })
  })
  
  describe('POST', () => {
    it('creates resource for valid request', async () => {
      const requestBody = {
        title: 'Test Title',
        description: 'Test Description'
      }
      
      const request = new NextRequest('http://localhost/api/test', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'content-type': 'application/json',
          authorization: 'Bearer valid-token'
        }
      })
      
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.id).toBeDefined()
    })
    
    it('returns 400 for invalid request body', async () => {
      const request = new NextRequest('http://localhost/api/test', {
        method: 'POST',
        body: JSON.stringify({ invalid: 'data' }),
        headers: { 'content-type': 'application/json' }
      })
      
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
    })
  })
  
  describe('Rate Limiting', () => {
    it('blocks requests after rate limit exceeded', async () => {
      // Make multiple requests to exceed rate limit
      const requests = Array(101).fill(null).map(() =>
        GET(new NextRequest('http://localhost/api/test'))
      )
      
      const responses = await Promise.all(requests)
      const lastResponse = responses[responses.length - 1]
      
      expect(lastResponse.status).toBe(429)
    })
  })
})
```

## 🔍 Validation Steps

### API Functionality
```bash
# Test all endpoints with curl or Postman
- [ ] GET requests return correct data and status codes
- [ ] POST requests create resources correctly
- [ ] PUT/PATCH requests update resources correctly
- [ ] DELETE requests remove resources correctly
- [ ] Error responses include helpful messages
```

### Security Testing
```bash
# Security verification
- [ ] Authentication works for protected endpoints
- [ ] Authorization prevents unauthorized access
- [ ] Input validation prevents injection attacks
- [ ] Rate limiting prevents abuse
- [ ] CORS headers configured correctly
```

### Performance Testing
```bash
# Performance verification
- [ ] API responds within acceptable time limits
- [ ] Database queries are optimized
- [ ] Memory usage remains stable
- [ ] Rate limiting doesn't affect normal usage
- [ ] Pagination works for large datasets
```

### Error Handling
```bash
# Error scenario testing
- [ ] Invalid JSON returns 400 with helpful message
- [ ] Missing authentication returns 401
- [ ] Insufficient permissions return 403
- [ ] Not found resources return 404
- [ ] Server errors return 500 with generic message
```

## 🎨 API Design Patterns

### RESTful API Structure
```typescript
// Resource-based URLs
GET    /api/users           // List users
GET    /api/users/[id]      // Get specific user
POST   /api/users           // Create user
PUT    /api/users/[id]      // Update user
DELETE /api/users/[id]      // Delete user

// Nested resources
GET    /api/users/[id]/posts      // User's posts
POST   /api/users/[id]/posts      // Create post for user

// Collections with filtering
GET    /api/posts?category=tech&limit=10&page=2
```

### Response Format Standards
```typescript
// Success Response
{
  "success": true,
  "data": { /* resource data */ },
  "pagination": { /* if applicable */ }
}

// Error Response
{
  "success": false,
  "error": "User-friendly error message",
  "details": [ /* validation errors */ ]
}

// List Response with Pagination
{
  "success": true,
  "data": [/* array of resources */],
  "pagination": {
    "page": 2,
    "limit": 10,
    "total": 150,
    "hasMore": true
  }
}
```

## ✅ Final Checklist

### API Structure
- [ ] Route handlers for all required HTTP methods
- [ ] Request/response validation with Zod schemas
- [ ] Proper HTTP status codes for all scenarios
- [ ] Comprehensive error handling and logging
- [ ] Authentication and authorization (if required)

### Security & Performance
- [ ] Input validation prevents injection attacks
- [ ] Rate limiting configured appropriately
- [ ] CORS headers set correctly
- [ ] Sensitive data not exposed in responses
- [ ] Database queries optimized for performance

### Firebase Integration
- [ ] Defensive programming for service availability
- [ ] Proper error handling for all Firebase operations
- [ ] Transactions used for data consistency
- [ ] Security rules enforce access control
- [ ] Real-time updates handled correctly (if applicable)

### Testing & Documentation
- [ ] Test coverage ≥80% for all endpoints
- [ ] Edge cases and error scenarios tested
- [ ] API documentation includes examples
- [ ] Request/response schemas documented
- [ ] Authentication requirements clearly stated

---

## 📚 API Integration

This API route should integrate seamlessly with the Syndicaps ecosystem:

- **Consistent Error Handling**: Use standard error formats and status codes
- **Authentication**: Integrate with Firebase Auth for user verification
- **Rate Limiting**: Protect against abuse while allowing normal usage
- **Logging**: Provide adequate information for debugging and monitoring
- **Performance**: Respond quickly and handle load efficiently

---

*This template ensures API routes maintain consistency with Syndicaps' architecture while providing secure, reliable, and performant backend functionality.*