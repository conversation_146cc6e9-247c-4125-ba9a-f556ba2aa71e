# Syndicaps Feature PRP Template v1.0

**Template optimized for React/Next.js/Firebase features with Context Engineering**

## 🎯 Goal
[What needs to be built - be specific about the end state and user-facing functionality]

## 🔍 Why
- **Business Value**: [How this impacts users and business goals]
- **Integration**: [How this fits with existing Syndicaps features]
- **Problems Solved**: [What user pain points this addresses]

## 📋 What
[Detailed user-visible behavior and technical requirements]

### Success Criteria
- [ ] [Specific, measurable outcome 1]
- [ ] [Specific, measurable outcome 2]
- [ ] [Performance requirement - e.g., loads in <3s]
- [ ] [Accessibility requirement - WCAG 2.1 AA compliant]
- [ ] [Mobile responsiveness requirement]
- [ ] [Error handling requirement]

## 📚 Required Context

### 🔴 MUST READ - Include these in your context window
```yaml
# Project Documentation
- file: CLAUDE.md
  why: Global AI assistant rules and conventions
  
- file: PLANNING.md
  why: Project architecture and patterns to follow

- file: examples/README.md
  why: Code patterns and examples to reference

# Component Examples
- file: examples/components/SimpleComponent.tsx
  why: Basic component pattern with TypeScript and accessibility

- file: examples/components/DataFetchComponent.tsx
  why: Firebase integration with defensive programming

- file: examples/components/ErrorBoundaryExample.tsx
  why: Error handling and crash prevention patterns

- file: examples/components/FormComponent.tsx
  why: Form handling with validation and error states

# Hook Examples
- file: examples/hooks/useFirebaseQuery.ts
  why: Firebase data fetching patterns with error handling

# Testing Examples
- file: examples/tests/component.test.tsx
  why: Testing patterns for React components with Firebase

# Existing Components (if extending)
- file: [path/to/existing/component.tsx]
  why: [Pattern to follow or extend]
```

### 🏗️ Current Codebase Overview
```bash
# Run this to understand current structure
find . -type f \( -name "*.tsx" -o -name "*.ts" \) -not -path "*/node_modules/*" -not -path "*/.next/*" | head -20
```

**Current Architecture**:
- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS with design system
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **State**: Zustand for global state, React state for local
- **Animation**: Framer Motion
- **Forms**: React Hook Form + Zod validation
- **Testing**: Jest + React Testing Library + Playwright

### 🎯 Target File Structure
```bash
# Files to be created/modified for this feature
src/components/[feature-name]/
├── [MainComponent].tsx           # Primary component
├── [SubComponent].tsx           # Supporting components
├── __tests__/                   # Component tests
│   └── [MainComponent].test.tsx
└── index.ts                     # Export barrel

app/[feature-route]/             # If new page required
├── page.tsx                     # Next.js page component
├── loading.tsx                  # Loading UI
└── error.tsx                    # Error UI

src/hooks/                       # If custom hooks needed
└── use[FeatureName].ts

src/lib/                         # If utilities needed
└── [feature-name].ts
```

### ⚠️ Critical Syndicaps Patterns & Gotchas

```typescript
// CRITICAL: Always check Firebase service availability
if (!db) {
  console.warn('Firebase database not available')
  return
}

// CRITICAL: Use defensive programming for all Firebase operations
try {
  const result = await getData()
  // Handle success
} catch (error) {
  console.error('Firebase operation failed:', error)
  // Provide user feedback and fallback
}

// CRITICAL: Always include proper error boundaries
<SimpleErrorBoundary fallback={<FallbackUI />}>
  <YourComponent />
</SimpleErrorBoundary>

// CRITICAL: Use established TypeScript patterns
interface ComponentProps {
  /** Required prop with JSDoc */
  title: string
  /** Optional prop with default */
  variant?: 'primary' | 'secondary'
  /** Event handler */
  onClick?: () => void
}

// CRITICAL: Follow Tailwind design system
const variantStyles = {
  primary: 'bg-accent-500 hover:bg-accent-600 text-white',
  secondary: 'bg-gray-800 hover:bg-gray-700 text-gray-100'
}

// CRITICAL: Include accessibility attributes
<button
  aria-label="Descriptive label"
  aria-describedby="help-text"
  role="button"
  tabIndex={0}
>

// CRITICAL: Handle loading and error states
{loading && <LoadingSpinner />}
{error && <ErrorMessage error={error} onRetry={handleRetry} />}
{data && <SuccessContent data={data} />}
```

## 🏗️ Implementation Blueprint

### Phase 1: Core Structure
```typescript
// Task 1: Create base component structure
CREATE src/components/[feature-name]/[MainComponent].tsx:
  - FOLLOW pattern from: examples/components/SimpleComponent.tsx
  - INCLUDE TypeScript interface with JSDoc comments
  - ADD proper error boundary wrapper
  - IMPLEMENT loading, error, and success states
  - ENSURE accessibility attributes (ARIA labels, keyboard nav)

// Task 2: Create supporting components (if needed)
CREATE src/components/[feature-name]/[SubComponent].tsx:
  - FOLLOW same patterns as main component
  - MAINTAIN consistency with design system
  - INCLUDE proper prop validation
```

### Phase 2: Data Integration
```typescript
// Task 3: Implement Firebase integration (if needed)
CREATE src/hooks/use[FeatureName].ts:
  - FOLLOW pattern from: examples/hooks/useFirebaseQuery.ts
  - INCLUDE defensive programming checks
  - ADD proper error handling and retry logic
  - IMPLEMENT loading states and cache if appropriate

// Task 4: Create data utilities (if needed)
CREATE src/lib/[feature-name].ts:
  - ADD data transformation functions
  - INCLUDE input validation with Zod schemas
  - IMPLEMENT error handling utilities
```

### Phase 3: User Interface
```typescript
// Task 5: Implement form handling (if forms involved)
REFERENCE examples/components/FormComponent.tsx:
  - USE React Hook Form + Zod validation
  - INCLUDE proper error states and user feedback
  - ADD accessibility labels and descriptions
  - IMPLEMENT loading states during submission

// Task 6: Add animations and interactions
USE Framer Motion following established patterns:
  - SUBTLE animations for better UX
  - CONSISTENT with existing component animations
  - AVOID excessive or distracting animations
```

### Phase 4: Pages and Routing (if new pages)
```typescript
// Task 7: Create Next.js pages (if needed)
CREATE app/[feature-route]/page.tsx:
  - FOLLOW app router conventions
  - INCLUDE proper metadata and SEO
  - ADD error boundaries and loading states
  - IMPLEMENT responsive design

// Task 8: Add navigation and linking
UPDATE existing navigation components:
  - ADD new routes to navigation menus
  - ENSURE proper active state handling
  - INCLUDE breadcrumb navigation if applicable
```

### Phase 5: Testing Implementation
```typescript
// Task 9: Create comprehensive tests
CREATE src/components/[feature-name]/__tests__/[MainComponent].test.tsx:
  - FOLLOW pattern from: examples/tests/component.test.tsx
  - INCLUDE rendering, interaction, and accessibility tests
  - MOCK Firebase services properly
  - TEST error states and recovery mechanisms
  - ENSURE 80%+ test coverage

// Task 10: Add integration tests (if applicable)
CREATE tests/integration/[feature-name].test.ts:
  - TEST complete user workflows
  - INCLUDE Firebase integration testing
  - VERIFY error handling and edge cases
```

## 🔍 Validation Loop

### Level 1: Code Quality
```bash
# Run these FIRST - fix any errors before proceeding
npm run lint                     # ESLint + TypeScript checks
npm run build                    # Production build verification
npm run typecheck               # TypeScript compilation

# Expected: No errors. If errors exist, READ and fix them.
```

### Level 2: Testing
```bash
# Run comprehensive test suite
npm run test                     # Unit tests
npm run test:coverage           # Coverage report (target: 80%+)
npm run test:e2e                # End-to-end tests (if applicable)

# Expected: All tests pass, coverage meets requirements
```

### Level 3: Accessibility
```bash
# Manual accessibility testing required
# - Test with keyboard navigation only
# - Test with screen reader (VoiceOver/NVDA)
# - Verify color contrast ratios
# - Check focus management and ARIA labels
```

### Level 4: Performance
```bash
# Performance verification
npm run build && npm run start  # Production build testing
# - Verify Core Web Vitals compliance
# - Check bundle size impact
# - Test loading performance
# - Verify mobile responsiveness
```

### Level 5: Firebase Integration
```bash
# Firebase testing checklist
# - Test with Firebase emulators
# - Verify offline functionality
# - Test error scenarios (network failures)
# - Confirm security rules work correctly
# - Test real-time updates (if applicable)
```

## ✅ Final Validation Checklist

### Functionality
- [ ] All user requirements implemented and working
- [ ] Error states handled gracefully with user feedback
- [ ] Loading states provide appropriate feedback
- [ ] Forms validate properly with helpful error messages
- [ ] Firebase integration works reliably with defensive programming

### Code Quality
- [ ] TypeScript strict mode compliance: `npm run typecheck`
- [ ] ESLint passes: `npm run lint`
- [ ] Production build succeeds: `npm run build`
- [ ] Test coverage ≥80%: `npm run test:coverage`
- [ ] No console errors in browser developer tools

### User Experience
- [ ] Responsive design works on mobile, tablet, desktop
- [ ] Accessibility compliance verified (keyboard nav, screen readers)
- [ ] Loading states provide clear feedback
- [ ] Error recovery mechanisms work intuitively
- [ ] Animations enhance rather than distract from UX

### Performance
- [ ] Page load time <3 seconds
- [ ] First Contentful Paint <1.5 seconds
- [ ] No memory leaks or infinite loops
- [ ] Bundle size impact acceptable
- [ ] Core Web Vitals requirements met

### Security & Best Practices
- [ ] Input validation implemented (client and server)
- [ ] Firebase security rules updated if needed
- [ ] No sensitive data exposed in client code
- [ ] Proper error handling without information leakage
- [ ] HTTPS-only in production

---

## ❌ Anti-Patterns to Avoid

### Code Patterns
- ❌ Don't create components >500 lines (split into smaller components)
- ❌ Don't skip TypeScript interfaces for props
- ❌ Don't hardcode colors/spacing (use Tailwind design tokens)
- ❌ Don't forget accessibility attributes (ARIA labels, keyboard nav)
- ❌ Don't skip error boundaries for complex components

### Firebase Patterns  
- ❌ Don't assume Firebase services are always available
- ❌ Don't skip error handling for Firebase operations
- ❌ Don't forget to cleanup Firebase listeners
- ❌ Don't expose Firebase configuration in client code
- ❌ Don't skip input validation before Firestore operations

### Performance Patterns
- ❌ Don't create infinite loops in useEffect dependencies
- ❌ Don't forget cleanup functions for timers/intervals
- ❌ Don't overuse real-time listeners (use strategically)
- ❌ Don't skip loading states for async operations
- ❌ Don't forget to optimize images and assets

### Testing Patterns
- ❌ Don't skip testing error scenarios
- ❌ Don't mock away the actual functionality being tested
- ❌ Don't forget accessibility testing
- ❌ Don't skip integration tests for complex features
- ❌ Don't commit code without running tests

---

## 🎯 Context Engineering Notes

This PRP is designed for AI assistance using Context Engineering principles:

1. **Comprehensive Context**: All necessary files and patterns referenced
2. **Defensive Programming**: Error handling and edge cases emphasized
3. **Quality Assurance**: Multi-level validation ensures working code
4. **Pattern Consistency**: Examples library ensures consistent implementation
5. **Performance Focus**: Core Web Vitals and optimization requirements included

When executing this PRP:
- **READ all referenced example files** to understand patterns
- **FOLLOW established conventions** rather than creating new patterns
- **IMPLEMENT comprehensive error handling** as shown in examples
- **TEST thoroughly** using provided testing patterns
- **VALIDATE at each level** before proceeding to next phase

---

*This PRP template ensures features are implemented following Syndicaps' established patterns while maintaining high quality, performance, and accessibility standards.*