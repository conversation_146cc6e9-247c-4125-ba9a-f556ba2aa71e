# Syndicaps Component PRP Template v1.0

**Specialized template for creating new React components in the Syndicaps design system**

## 🎯 Goal
Create a new [ComponentName] component that [specific functionality description]

## 🔍 Why
- **Design System**: Extends Syndicaps component library with reusable UI element
- **User Experience**: [How this improves user interaction]
- **Developer Experience**: [How this helps other developers]

## 📋 What

### Component Specifications
- **Component Name**: [ComponentName]
- **Props Interface**: [List expected props and their types]
- **Variants**: [Visual/behavioral variants needed]
- **States**: [Loading, error, disabled, etc.]
- **Responsive**: [Mobile, tablet, desktop behavior]

### Success Criteria
- [ ] Component renders correctly in all variants
- [ ] Props are properly typed with TypeScript
- [ ] Accessibility requirements met (WCAG 2.1 AA)
- [ ] Responsive design works on all screen sizes
- [ ] Component integrates with existing design system
- [ ] Comprehensive test coverage (≥80%)
- [ ] Storybook documentation (if using Storybook)

## 📚 Required Context

### 🔴 MUST READ
```yaml
# Base Patterns
- file: examples/components/SimpleComponent.tsx
  why: Base component pattern with TypeScript, accessibility, animations

- file: examples/components/FormComponent.tsx
  why: Form component patterns if this involves form elements

- file: examples/components/ErrorBoundaryExample.tsx
  why: Error boundary patterns for component isolation

# Design System
- file: PLANNING.md
  section: "Design System & Conventions"
  why: Color palette, spacing, typography standards

# Testing
- file: examples/tests/component.test.tsx
  why: Component testing patterns and utilities
```

### 🎨 Design System Tokens
```typescript
// Colors - Use design system tokens
const colors = {
  primary: 'bg-accent-500 hover:bg-accent-600',
  secondary: 'bg-gray-800 hover:bg-gray-700', 
  danger: 'bg-red-600 hover:bg-red-700',
  success: 'bg-green-600 hover:bg-green-700'
}

// Sizes - Consistent sizing scale
const sizes = {
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-3 text-base', 
  lg: 'px-6 py-4 text-lg'
}

// Spacing - Use Tailwind spacing scale
const spacing = {
  xs: 'space-x-1 space-y-1',
  sm: 'space-x-2 space-y-2',
  md: 'space-x-4 space-y-4',
  lg: 'space-x-6 space-y-6'
}
```

## 🏗️ Implementation Plan

### Phase 1: Component Structure
```typescript
// Task 1: Create component file structure
CREATE src/components/ui/[ComponentName].tsx:
  - START with examples/components/SimpleComponent.tsx pattern
  - DEFINE TypeScript interface with comprehensive JSDoc
  - IMPLEMENT variant system (primary, secondary, etc.)
  - ADD size system (sm, md, lg)
  - INCLUDE disabled and loading states

interface [ComponentName]Props {
  /** Primary content or label */
  children: React.ReactNode
  /** Visual variant */
  variant?: 'primary' | 'secondary' | 'danger' | 'success'
  /** Size variant */
  size?: 'sm' | 'md' | 'lg'
  /** Loading state */
  isLoading?: boolean
  /** Disabled state */
  disabled?: boolean
  /** Click handler */
  onClick?: () => void
  /** Additional CSS classes */
  className?: string
}
```

### Phase 2: Styling Implementation
```typescript
// Task 2: Implement styling system
const [ComponentName]: React.FC<[ComponentName]Props> = ({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  disabled = false,
  onClick,
  className = ''
}) => {
  // Variant styles using design tokens
  const variantStyles = {
    primary: 'bg-accent-500 hover:bg-accent-600 text-white',
    secondary: 'bg-gray-800 hover:bg-gray-700 text-gray-100',
    danger: 'bg-red-600 hover:bg-red-700 text-white',
    success: 'bg-green-600 hover:bg-green-700 text-white'
  }
  
  // Size styles
  const sizeStyles = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  }
  
  // Base styles with transitions and focus states
  const baseStyles = 'rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900'
  
  // Conditional styles
  const conditionalStyles = disabled 
    ? 'opacity-50 cursor-not-allowed' 
    : 'cursor-pointer'
}
```

### Phase 3: Accessibility Implementation
```typescript
// Task 3: Add accessibility features
return (
  <motion.div
    className={finalClassName}
    onClick={handleClick}
    onKeyDown={handleKeyDown}
    role="button"
    tabIndex={disabled ? -1 : 0}
    aria-disabled={disabled}
    aria-label={ariaLabel}
    aria-describedby={ariaDescribedBy}
    whileHover={!disabled ? { scale: 1.02 } : {}}
    whileTap={!disabled ? { scale: 0.98 } : {}}
  >
    {/* Loading state */}
    {isLoading && (
      <div className="flex items-center space-x-2">
        <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full" />
        <span>Loading...</span>
      </div>
    )}
    
    {/* Normal content */}
    {!isLoading && children}
  </motion.div>
)
```

### Phase 4: Testing Implementation
```typescript
// Task 4: Create comprehensive tests
CREATE src/components/ui/__tests__/[ComponentName].test.tsx:

describe('[ComponentName]', () => {
  it('renders with required props', () => {
    render(<[ComponentName]>Test Content</[ComponentName]>)
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })
  
  it('applies correct variant styles', () => {
    const { rerender } = render(<[ComponentName] variant="primary">Test</[ComponentName]>)
    expect(screen.getByRole('button')).toHaveClass('bg-accent-500')
    
    rerender(<[ComponentName] variant="danger">Test</[ComponentName]>)
    expect(screen.getByRole('button')).toHaveClass('bg-red-600')
  })
  
  it('handles click events', async () => {
    const handleClick = jest.fn()
    render(<[ComponentName] onClick={handleClick}>Clickable</[ComponentName]>)
    
    await userEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
  
  it('supports keyboard navigation', async () => {
    const handleClick = jest.fn()
    render(<[ComponentName] onClick={handleClick}>Keyboard</[ComponentName]>)
    
    const button = screen.getByRole('button')
    button.focus()
    
    await userEvent.keyboard('{Enter}')
    expect(handleClick).toHaveBeenCalledTimes(1)
    
    await userEvent.keyboard(' ')
    expect(handleClick).toHaveBeenCalledTimes(2)
  })
  
  it('shows loading state correctly', () => {
    render(<[ComponentName] isLoading>Loading Test</[ComponentName]>)
    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.queryByText('Loading Test')).not.toBeInTheDocument()
  })
  
  it('disables interaction when disabled', async () => {
    const handleClick = jest.fn()
    render(<[ComponentName] disabled onClick={handleClick}>Disabled</[ComponentName]>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-disabled', 'true')
    expect(button).toHaveAttribute('tabIndex', '-1')
    
    await userEvent.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })
  
  it('meets accessibility requirements', () => {
    render(<[ComponentName] aria-label="Test button">Accessible</[ComponentName]>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-label', 'Test button')
    expect(button).toHaveAttribute('tabIndex', '0')
  })
})
```

## 🔍 Validation Steps

### Design System Compliance
```bash
# Verify component follows design system
- [ ] Uses established color tokens (accent-500, gray-800, etc.)
- [ ] Follows spacing scale (px-3, py-2, etc.) 
- [ ] Implements consistent border radius (rounded-lg)
- [ ] Uses proper typography scale (text-sm, text-base, etc.)
- [ ] Includes hover and focus states
- [ ] Responsive behavior consistent with system
```

### Accessibility Validation
```bash
# Manual accessibility testing
- [ ] Component focusable with keyboard
- [ ] Proper ARIA labels and roles
- [ ] Screen reader announces correctly
- [ ] Focus indicators visible
- [ ] Color contrast ratios meet WCAG AA standards
- [ ] Works with keyboard navigation only
```

### Integration Testing
```typescript
// Test component in different contexts
- [ ] Component works in forms
- [ ] Component works in modals/dialogs
- [ ] Component works in mobile responsive layouts
- [ ] Component works with dark theme (if applicable)
- [ ] Component works with different content lengths
```

## 🎯 Component Variations

### Common Patterns to Support
```typescript
// Icon support
interface IconProps {
  icon?: LucideIcon
  iconPosition?: 'left' | 'right'
}

// Link variant (for navigation)
interface LinkProps {
  href?: string
  external?: boolean
}

// Form integration
interface FormProps {
  type?: 'button' | 'submit' | 'reset'
  form?: string
}

// Advanced interactions
interface AdvancedProps {
  tooltip?: string
  badge?: string | number
  confirmation?: boolean
}
```

## 📦 Export and Documentation

### Component Export
```typescript
// Task 5: Create barrel export
CREATE src/components/ui/index.ts:
export { default as [ComponentName] } from './[ComponentName]'

// Update main export
UPDATE src/components/index.ts:
export * from './ui'
```

### Usage Documentation
```typescript
// Task 6: Add usage examples in component file
/*
// Basic usage
<[ComponentName]>Click me</[ComponentName]>

// With variant and size
<[ComponentName] variant="danger" size="lg">
  Delete Item
</[ComponentName]>

// With loading state
<[ComponentName] isLoading disabled>
  Processing...
</[ComponentName]>

// With click handler
<[ComponentName] 
  variant="primary"
  onClick={() => console.log('clicked')}
>
  Submit
</[ComponentName]>
*/
```

## ✅ Final Checklist

### Code Quality
- [ ] TypeScript strict mode compliance
- [ ] ESLint passes without warnings
- [ ] Component props fully documented with JSDoc
- [ ] All variants and states implemented
- [ ] Proper error handling included

### Design & UX
- [ ] Visual design matches Syndicaps design system
- [ ] Animations are subtle and purposeful
- [ ] Loading states provide appropriate feedback
- [ ] Disabled states are clearly indicated
- [ ] Component scales appropriately across screen sizes

### Accessibility & Performance
- [ ] WCAG 2.1 AA compliance verified
- [ ] Keyboard navigation works correctly
- [ ] Screen reader compatibility confirmed
- [ ] No performance impact on page load
- [ ] No memory leaks or infinite re-renders

### Testing & Documentation
- [ ] Test coverage ≥80%
- [ ] All user interactions tested
- [ ] Error scenarios covered
- [ ] Usage examples provided
- [ ] Component documented for other developers

---

## 🎨 Design System Integration

This component should integrate seamlessly with the Syndicaps design system:

- **Consistent Visual Language**: Follow established color, typography, and spacing patterns
- **Reusable Architecture**: Component should be flexible enough for multiple use cases
- **Accessibility First**: Built-in accessibility features, not added as afterthought
- **Performance Optimized**: Minimal bundle impact, efficient re-renders
- **Developer Experience**: Clear props, helpful TypeScript types, comprehensive examples

---

*This template ensures new components maintain consistency with Syndicaps design standards while providing excellent user and developer experience.*