# Syndicaps Hook PRP Template v1.0

**Specialized template for creating custom React hooks in the Syndicaps application**

## 🎯 Goal
Create a custom React hook `use[HookName]` that [specific functionality description]

## 🔍 Why
- **Code Reuse**: [How this hook eliminates duplication across components]
- **Business Logic**: [What business functionality this encapsulates]
- **Developer Experience**: [How this improves component development]

## 📋 What

### Hook Specifications
- **Hook Name**: `use[HookName]`
- **Input Parameters**: [List expected parameters and their types]
- **Return Values**: [What the hook returns - data, functions, state]
- **Side Effects**: [Firebase operations, API calls, event listeners, etc.]
- **Dependencies**: [Other hooks, external libraries, Firebase services]

### Success Criteria
- [ ] Hook follows React hooks rules and conventions
- [ ] Comprehensive TypeScript interfaces with JSDoc
- [ ] Proper error handling and loading states
- [ ] Memory leak prevention (cleanup functions)
- [ ] Firebase integration uses defensive programming
- [ ] Test coverage ≥80%
- [ ] Performance optimized (memoization, debouncing)

## 📚 Required Context

### 🔴 MUST READ
```yaml
# Base Patterns
- file: examples/hooks/useFirebaseQuery.ts
  why: Firebase integration patterns with defensive programming

- file: CLAUDE.md
  why: Global AI assistant rules and React patterns

- file: PLANNING.md
  section: "State Management & Hooks"
  why: Hook patterns and conventions used in Syndicaps

# Testing Patterns
- file: examples/tests/component.test.tsx
  why: Testing patterns for hooks and Firebase integration

# Firebase Patterns
- file: src/lib/firebase/monitoring.ts
  why: Firebase service availability checks and error handling
```

### 🧠 React Hooks Best Practices
```typescript
// Hook Rules Compliance
- ALWAYS start with "use" prefix
- ONLY call hooks at top level (not in loops, conditions, nested functions)
- ALWAYS include dependencies in useEffect dependency arrays
- ALWAYS cleanup side effects (listeners, timers, subscriptions)
- USE defensive programming for external services

// Common Hook Patterns
function useCustomHook(params) {
  // 1. State declarations
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  
  // 2. Refs for cleanup
  const abortControllerRef = useRef(null)
  const timeoutRef = useRef(null)
  
  // 3. Side effects
  useEffect(() => {
    // Setup
    const setup = async () => { /* ... */ }
    setup()
    
    // Cleanup
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [/* dependencies */])
  
  // 4. Return values
  return { data, loading, error, refetch }
}
```

### 🔥 Firebase Integration Patterns
```typescript
// CRITICAL: Always check Firebase availability
if (!db) {
  console.warn('Firebase database not available in use[HookName]')
  return { data: null, loading: false, error: new Error('Database unavailable') }
}

// Defensive Firebase Operations
try {
  const result = await firebaseOperation()
  return result
} catch (error) {
  console.error('Firebase operation failed in use[HookName]:', error)
  throw error
}

// Real-time Listener Cleanup
useEffect(() => {
  if (!db) return
  
  const unsubscribe = onSnapshot(
    collection(db, 'collectionName'),
    (snapshot) => {
      // Handle updates
    },
    (error) => {
      console.error('Real-time listener error:', error)
      setError(error)
    }
  )
  
  return () => unsubscribe()
}, [])
```

## 🏗️ Implementation Plan

### Phase 1: Hook Structure
```typescript
// Task 1: Create hook file structure
CREATE src/hooks/use[HookName].ts:
  - START with examples/hooks/useFirebaseQuery.ts pattern
  - DEFINE TypeScript interfaces for parameters and return values
  - IMPLEMENT basic hook structure with state management
  - ADD proper JSDoc documentation

interface Use[HookName]Params {
  /** Primary parameter description */
  param1: string
  /** Optional parameter with default */
  param2?: number
  /** Configuration options */
  options?: {
    enabled?: boolean
    refetchOnMount?: boolean
    retryCount?: number
  }
}

interface Use[HookName]Return {
  /** Main data returned by the hook */
  data: DataType | null
  /** Loading state indicator */
  loading: boolean
  /** Error state if operation fails */
  error: Error | null
  /** Function to manually trigger refetch */
  refetch: () => void
  /** Function to reset hook state */
  reset: () => void
}

/**
 * Custom hook for [functionality description]
 * 
 * @param params - Hook parameters
 * @returns Hook state and control functions
 * 
 * @example
 * ```tsx
 * const { data, loading, error } = use[HookName]({
 *   param1: 'value',
 *   options: { enabled: true }
 * })
 * ```
 */
export function use[HookName](params: Use[HookName]Params): Use[HookName]Return {
  // Implementation will go here
}
```

### Phase 2: State Management
```typescript
// Task 2: Implement state management
export function use[HookName](params: Use[HookName]Params): Use[HookName]Return {
  // Core state
  const [data, setData] = useState<DataType | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  // Refs for cleanup and control
  const abortControllerRef = useRef<AbortController | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])
  
  // Control functions
  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])
  
  const refetch = useCallback(() => {
    if (!mountedRef.current) return
    // Trigger re-fetch logic
  }, [/* dependencies */])
}
```

### Phase 3: Core Logic Implementation
```typescript
// Task 3: Implement main functionality
const executeOperation = useCallback(async () => {
  if (!params.options?.enabled) return
  if (!db) {
    console.warn('Firebase not available in use[HookName]')
    setError(new Error('Database service unavailable'))
    return
  }
  
  try {
    setLoading(true)
    setError(null)
    
    // Create abort controller for this operation
    abortControllerRef.current = new AbortController()
    
    // Perform the main operation
    const result = await performMainOperation(params, {
      signal: abortControllerRef.current.signal
    })
    
    // Only update state if component is still mounted
    if (mountedRef.current) {
      setData(result)
    }
    
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('Operation aborted in use[HookName]')
      return
    }
    
    console.error('use[HookName] operation failed:', error)
    
    if (mountedRef.current) {
      setError(error instanceof Error ? error : new Error('Unknown error'))
    }
  } finally {
    if (mountedRef.current) {
      setLoading(false)
    }
  }
}, [params.param1, params.param2, params.options?.enabled])

// Execute operation when dependencies change
useEffect(() => {
  if (params.options?.enabled !== false) {
    executeOperation()
  }
}, [executeOperation])
```

### Phase 4: Firebase Integration (if applicable)
```typescript
// Task 4: Add Firebase real-time capabilities (if needed)
const setupRealtimeListener = useCallback(() => {
  if (!db || !params.options?.realtime) return
  
  try {
    const unsubscribe = onSnapshot(
      query(collection(db, 'collectionName'), /* query constraints */),
      (snapshot) => {
        if (!mountedRef.current) return
        
        const data = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        
        setData(data)
        setLoading(false)
      },
      (error) => {
        console.error('Real-time listener error in use[HookName]:', error)
        if (mountedRef.current) {
          setError(error)
          setLoading(false)
        }
      }
    )
    
    return unsubscribe
  } catch (error) {
    console.error('Failed to setup real-time listener:', error)
    if (mountedRef.current) {
      setError(error instanceof Error ? error : new Error('Listener setup failed'))
    }
  }
}, [params.param1, params.options?.realtime])

// Setup real-time listener
useEffect(() => {
  if (params.options?.realtime) {
    const unsubscribe = setupRealtimeListener()
    return unsubscribe
  }
}, [setupRealtimeListener])
```

### Phase 5: Performance Optimization
```typescript
// Task 5: Add performance optimizations

// Debounce frequent operations
const debouncedExecute = useMemo(
  () => debounce(executeOperation, 300),
  [executeOperation]
)

// Memoize expensive computations
const processedData = useMemo(() => {
  if (!data) return null
  
  // Expensive data processing
  return data.map(item => ({
    ...item,
    processed: expensiveProcessing(item)
  }))
}, [data])

// Cache results
const cache = useRef(new Map())

const getCachedResult = useCallback((key: string) => {
  return cache.current.get(key)
}, [])

const setCachedResult = useCallback((key: string, value: any) => {
  cache.current.set(key, value)
  
  // Cleanup old cache entries
  if (cache.current.size > 100) {
    const firstKey = cache.current.keys().next().value
    cache.current.delete(firstKey)
  }
}, [])
```

### Phase 6: Testing Implementation
```typescript
// Task 6: Create comprehensive tests
CREATE src/hooks/__tests__/use[HookName].test.ts:
  - FOLLOW examples/tests/component.test.tsx patterns
  - TEST hook behavior and state management
  - VERIFY Firebase integration works correctly
  - CHECK error handling and edge cases
  - ENSURE cleanup functions work properly

import { renderHook, act, waitFor } from '@testing-library/react'
import { use[HookName] } from '../use[HookName]'

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {
    collection: jest.fn(),
    doc: jest.fn()
  }
}))

describe('use[HookName]', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  it('initializes with correct default state', () => {
    const { result } = renderHook(() => use[HookName]({
      param1: 'test'
    }))
    
    expect(result.current.data).toBeNull()
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBeNull()
    expect(typeof result.current.refetch).toBe('function')
    expect(typeof result.current.reset).toBe('function')
  })
  
  it('handles successful data fetching', async () => {
    const mockData = [{ id: '1', name: 'Test' }]
    const mockOperation = jest.fn().mockResolvedValue(mockData)
    
    const { result } = renderHook(() => use[HookName]({
      param1: 'test',
      options: { enabled: true }
    }))
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })
    
    expect(result.current.data).toEqual(mockData)
    expect(result.current.error).toBeNull()
  })
  
  it('handles errors gracefully', async () => {
    const mockError = new Error('Operation failed')
    const mockOperation = jest.fn().mockRejectedValue(mockError)
    
    const { result } = renderHook(() => use[HookName]({
      param1: 'test'
    }))
    
    await waitFor(() => {
      expect(result.current.error).toEqual(mockError)
    })
    
    expect(result.current.data).toBeNull()
    expect(result.current.loading).toBe(false)
  })
  
  it('cleans up properly on unmount', () => {
    const mockCleanup = jest.fn()
    
    const { unmount } = renderHook(() => use[HookName]({
      param1: 'test'
    }))
    
    unmount()
    
    // Verify cleanup was called
    expect(mockCleanup).toHaveBeenCalled()
  })
  
  it('supports refetch functionality', async () => {
    const mockOperation = jest.fn().mockResolvedValue(['data'])
    
    const { result } = renderHook(() => use[HookName]({
      param1: 'test'
    }))
    
    await act(async () => {
      result.current.refetch()
    })
    
    expect(mockOperation).toHaveBeenCalledTimes(2) // Initial + refetch
  })
  
  it('respects enabled option', () => {
    const mockOperation = jest.fn()
    
    renderHook(() => use[HookName]({
      param1: 'test',
      options: { enabled: false }
    }))
    
    expect(mockOperation).not.toHaveBeenCalled()
  })
  
  it('handles Firebase unavailability', () => {
    // Mock Firebase as unavailable
    jest.doMock('@/lib/firebase', () => ({ db: null }))
    
    const { result } = renderHook(() => use[HookName]({
      param1: 'test'
    }))
    
    expect(result.current.error).toEqual(
      expect.objectContaining({
        message: expect.stringContaining('Database unavailable')
      })
    )
  })
})
```

## 🔍 Validation Steps

### Hook Rules Compliance
```bash
# React hooks rules verification
- [ ] Hook name starts with "use"
- [ ] Hooks called at top level only
- [ ] useEffect dependencies are complete
- [ ] Cleanup functions prevent memory leaks
- [ ] No conditional hook calls
```

### Firebase Integration
```bash
# Firebase integration testing
- [ ] Defensive programming for service availability
- [ ] Proper error handling for all Firebase operations
- [ ] Real-time listeners cleaned up correctly
- [ ] Abort controllers cancel pending operations
- [ ] Retry logic works for transient failures
```

### Performance Testing
```bash
# Performance verification
- [ ] No unnecessary re-renders
- [ ] Expensive computations are memoized
- [ ] Debouncing used for frequent operations
- [ ] Cache size is controlled
- [ ] Memory usage stays stable
```

### TypeScript Compliance
```bash
# Type safety verification
- [ ] All parameters and return values typed
- [ ] Generic types used appropriately
- [ ] JSDoc comments for all public APIs
- [ ] No TypeScript errors in strict mode
- [ ] Proper inference for hook return types
```

## 🎨 Common Hook Patterns

### Data Fetching Hook
```typescript
export function useApiData<T>(url: string, options?: RequestOptions) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  // Implementation...
  
  return { data, loading, error, refetch }
}
```

### Firebase Collection Hook
```typescript
export function useFirestoreCollection<T>(
  collectionName: string,
  queryConstraints?: QueryConstraint[]
) {
  const [documents, setDocuments] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  
  // Implementation with real-time updates...
  
  return { documents, loading, error, refetch }
}
```

### Local Storage Hook
```typescript
export function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch {
      return defaultValue
    }
  })
  
  const setStoredValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setValue(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))
    } catch (error) {
      console.error('Error saving to localStorage:', error)
    }
  }
  
  return [value, setStoredValue] as const
}
```

### Debounced Value Hook
```typescript
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  
  return debouncedValue
}
```

## ✅ Final Checklist

### Code Quality
- [ ] Hook follows React rules and conventions
- [ ] TypeScript strict mode compliance
- [ ] ESLint passes without warnings
- [ ] Comprehensive JSDoc documentation
- [ ] Error handling covers all scenarios

### Performance & Memory
- [ ] No memory leaks (cleanup functions work)
- [ ] Expensive operations are optimized
- [ ] Re-renders are minimized
- [ ] Cache management prevents memory bloat
- [ ] Debouncing used appropriately

### Firebase Integration
- [ ] Defensive programming for service availability
- [ ] Proper error handling and retry logic
- [ ] Real-time listeners cleaned up correctly
- [ ] Operations can be cancelled
- [ ] Works correctly when Firebase is unavailable

### Testing & Documentation
- [ ] Test coverage ≥80%
- [ ] All hook behaviors tested
- [ ] Error scenarios covered
- [ ] Cleanup functions verified
- [ ] Usage examples provided

---

## 📚 Hook Library Integration

This hook should integrate seamlessly with the Syndicaps hook ecosystem:

- **Consistent API**: Follow established patterns for parameters and return values
- **Error Handling**: Use standard error reporting and recovery mechanisms
- **Performance**: Maintain app responsiveness and minimize resource usage
- **Firebase Integration**: Use defensive programming patterns throughout
- **Developer Experience**: Provide clear TypeScript types and helpful error messages

---

*This template ensures custom hooks maintain consistency with Syndicaps' architecture while providing reliable, performant, and maintainable functionality.*