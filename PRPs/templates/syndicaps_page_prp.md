# Syndicaps Page PRP Template v1.0

**Specialized template for creating new Next.js 15 pages in the Syndicaps application**

## 🎯 Goal
Create a new [PageName] page at route /[route-path] that [specific functionality description]

## 🔍 Why
- **User Value**: [How this page serves user needs and business goals]
- **Navigation Flow**: [How this fits into the user journey and site architecture]
- **SEO Impact**: [Search engine optimization benefits]

## 📋 What

### Page Specifications
- **Route**: `/[route-path]`
- **Page Type**: [Static/Dynamic/Server/Client Component]
- **Data Sources**: [Firebase collections, APIs, or static data needed]
- **User Permissions**: [Public, authenticated, admin, etc.]
- **SEO Requirements**: [Title, description, structured data]

### Success Criteria
- [ ] Page renders correctly on all devices (mobile, tablet, desktop)
- [ ] Page loads in <3 seconds on 3G connection
- [ ] SEO metadata properly configured
- [ ] Accessibility requirements met (WCAG 2.1 AA)
- [ ] Error boundaries handle edge cases gracefully
- [ ] Loading states provide appropriate user feedback
- [ ] Navigation integration works correctly

## 📚 Required Context

### 🔴 MUST READ
```yaml
# Base Patterns
- file: CLAUDE.md
  why: Global AI assistant rules and conventions
  
- file: PLANNING.md
  section: "App Router & Pages"
  why: Next.js 15 patterns and page structure conventions

- file: examples/README.md
  why: Code patterns and examples to reference

# Component Examples
- file: examples/components/SimpleComponent.tsx
  why: Basic component patterns with TypeScript and accessibility

- file: examples/components/DataFetchComponent.tsx
  why: Firebase integration with defensive programming

- file: examples/components/ErrorBoundaryExample.tsx
  why: Error handling and crash prevention patterns

# Existing Pages (for patterns)
- file: app/page.tsx
  why: Homepage patterns and structure

- file: app/shop/page.tsx
  why: Shop page patterns (if exists)

- file: app/layout.tsx
  why: Root layout and global providers
```

### 🏗️ Next.js 15 App Router Patterns
```typescript
// Page Component Structure
export default function PageName() {
  return (
    <SimpleErrorBoundary fallback={<PageErrorFallback />}>
      <PageContent />
    </SimpleErrorBoundary>
  )
}

// Metadata API (for SEO)
export const metadata: Metadata = {
  title: 'Page Title | Syndicaps',
  description: 'Page description for SEO',
  openGraph: {
    title: 'Page Title',
    description: 'Page description',
    images: ['/og-image.jpg']
  }
}

// Loading UI (loading.tsx)
export default function Loading() {
  return <PageSkeleton />
}

// Error UI (error.tsx)
export default function Error({ error, reset }: {
  error: Error
  reset: () => void
}) {
  return <PageErrorUI error={error} onRetry={reset} />
}
```

### 🎨 Design System Integration
```typescript
// Page Layout Patterns
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
}

// Container Classes
const containerClasses = {
  default: 'container mx-auto px-4 py-8',
  wide: 'max-w-7xl mx-auto px-4 py-8',
  narrow: 'max-w-4xl mx-auto px-4 py-8',
  full: 'w-full px-4 py-8'
}

// Section Spacing
const sectionSpacing = {
  tight: 'space-y-6',
  normal: 'space-y-8', 
  loose: 'space-y-12'
}
```

## 🏗️ Implementation Plan

### Phase 1: Page Structure
```typescript
// Task 1: Create base page file
CREATE app/[route-path]/page.tsx:
  - FOLLOW pattern from existing pages
  - IMPLEMENT proper TypeScript interfaces
  - ADD error boundary wrapper
  - INCLUDE loading and error states
  - ENSURE responsive design classes

// Basic page structure
export default function [PageName]() {
  return (
    <SimpleErrorBoundary fallback={<[PageName]ErrorFallback />}>
      <motion.div
        variants={pageVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        className="min-h-screen"
      >
        <div className="container mx-auto px-4 py-8">
          <[PageName]Content />
        </div>
      </motion.div>
    </SimpleErrorBoundary>
  )
}
```

### Phase 2: SEO Implementation
```typescript
// Task 2: Add metadata and SEO
ADD metadata export to page.tsx:
  - INCLUDE proper title with Syndicaps branding
  - ADD descriptive meta description
  - CONFIGURE Open Graph tags
  - INCLUDE relevant keywords
  - ADD structured data if applicable

export const metadata: Metadata = {
  title: '[Page Title] | Syndicaps',
  description: '[SEO-optimized description under 160 characters]',
  keywords: ['[relevant]', '[keywords]', '[for]', '[this]', '[page]'],
  openGraph: {
    title: '[Page Title]',
    description: '[Social media description]',
    url: 'https://syndicaps.com/[route-path]',
    siteName: 'Syndicaps',
    images: [{
      url: '/og-image-[page].jpg',
      width: 1200,
      height: 630,
      alt: '[Alt text for social image]'
    }],
    locale: 'en_US',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: '[Page Title]',
    description: '[Twitter description]',
    images: ['/og-image-[page].jpg']
  }
}
```

### Phase 3: Data Integration
```typescript
// Task 3: Implement data fetching (if needed)
CREATE src/hooks/use[PageName]Data.ts:
  - FOLLOW pattern from examples/hooks/useFirebaseQuery.ts
  - INCLUDE defensive programming checks
  - ADD proper error handling and retry logic
  - IMPLEMENT loading states and caching

// Task 4: Create data utilities (if needed)
CREATE src/lib/[page-name].ts:
  - ADD data transformation functions
  - INCLUDE input validation with Zod schemas
  - IMPLEMENT error handling utilities

// Example data hook
export function use[PageName]Data() {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (!db) {
      console.warn('Firebase not available for [PageName] data')
      setLoading(false)
      return
    }

    const fetchData = async () => {
      try {
        setLoading(true)
        const result = await getData()
        setData(result)
      } catch (err) {
        setError(err)
        console.error('[PageName] data fetch failed:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return { data, loading, error, refetch: fetchData }
}
```

### Phase 4: Page Components
```typescript
// Task 5: Create page-specific components
CREATE src/components/[page-name]/[PageName]Content.tsx:
  - FOLLOW examples/components/SimpleComponent.tsx pattern
  - INCLUDE proper TypeScript interfaces
  - ADD accessibility attributes
  - IMPLEMENT responsive design
  - ENSURE error handling

// Task 6: Create supporting components
CREATE src/components/[page-name]/[PageName]Section.tsx:
  - BREAK down page into logical sections
  - FOLLOW component composition patterns
  - MAINTAIN consistency with design system
  - INCLUDE loading and error states
```

### Phase 5: Loading and Error States
```typescript
// Task 7: Create loading UI
CREATE app/[route-path]/loading.tsx:
  - DESIGN skeleton screens matching page layout
  - USE consistent loading patterns
  - ENSURE accessibility for screen readers
  - MATCH visual hierarchy of actual content

export default function Loading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="animate-pulse space-y-8">
        {/* Header skeleton */}
        <div className="space-y-4">
          <div className="h-8 bg-gray-700 rounded w-1/3"></div>
          <div className="h-4 bg-gray-700 rounded w-2/3"></div>
        </div>
        
        {/* Content skeleton */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="space-y-4">
              <div className="h-48 bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-700 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Task 8: Create error UI
CREATE app/[route-path]/error.tsx:
  - PROVIDE helpful error messages
  - INCLUDE retry functionality
  - OFFER navigation alternatives
  - LOG errors for debugging

'use client'

export default function Error({
  error,
  reset
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('[PageName] page error:', error)
  }, [error])

  return (
    <div className="container mx-auto px-4 py-16 text-center">
      <div className="max-w-md mx-auto space-y-6">
        <div className="text-6xl">😕</div>
        <h1 className="text-2xl font-bold text-white">
          Something went wrong
        </h1>
        <p className="text-gray-400">
          We encountered an error loading this page.
        </p>
        <div className="space-y-3">
          <button
            onClick={reset}
            className="w-full bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Try again
          </button>
          <Link
            href="/"
            className="block w-full bg-gray-800 hover:bg-gray-700 text-gray-100 px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Go to homepage
          </Link>
        </div>
      </div>
    </div>
  )
}
```

### Phase 6: Testing Implementation
```typescript
// Task 9: Create page tests
CREATE src/components/[page-name]/__tests__/[PageName].test.tsx:
  - FOLLOW examples/tests/component.test.tsx patterns
  - TEST page rendering and navigation
  - VERIFY data loading and error states
  - CHECK accessibility compliance
  - ENSURE responsive behavior

describe('[PageName] Page', () => {
  it('renders page content correctly', () => {
    render(<[PageName] />)
    
    expect(screen.getByRole('main')).toBeInTheDocument()
    expect(screen.getByText('[Expected heading]')).toBeInTheDocument()
  })

  it('handles loading state', () => {
    render(<Loading />)
    
    expect(screen.getByTestId('[page]-skeleton')).toBeInTheDocument()
  })

  it('handles error state', () => {
    const mockError = new Error('Test error')
    const mockReset = jest.fn()
    
    render(<Error error={mockError} reset={mockReset} />)
    
    expect(screen.getByText(/something went wrong/i)).toBeInTheDocument()
    expect(screen.getByText(/try again/i)).toBeInTheDocument()
  })

  it('has correct SEO metadata', () => {
    expect(metadata.title).toContain('Syndicaps')
    expect(metadata.description).toBeTruthy()
    expect(metadata.openGraph.title).toBeTruthy()
  })
})
```

## 🔍 Validation Steps

### SEO Validation
```bash
# SEO testing checklist
- [ ] Page title follows "[Page Name] | Syndicaps" format
- [ ] Meta description is 150-160 characters
- [ ] Open Graph tags are complete
- [ ] Twitter Card metadata is configured
- [ ] Structured data is valid (if applicable)
- [ ] URL structure is SEO-friendly
```

### Performance Validation
```bash
# Performance testing
- [ ] Page loads in <3 seconds on 3G
- [ ] First Contentful Paint <1.5 seconds
- [ ] Largest Contentful Paint <2.5 seconds
- [ ] Cumulative Layout Shift <0.1
- [ ] No performance regressions in Lighthouse
```

### Accessibility Validation
```bash
# Accessibility testing
- [ ] Page is navigable with keyboard only
- [ ] Screen reader announces page correctly
- [ ] Color contrast meets WCAG AA standards
- [ ] Focus management works properly
- [ ] ARIA landmarks are properly used
```

### Mobile Responsiveness
```bash
# Mobile testing
- [ ] Page works on 320px viewport
- [ ] Touch targets are at least 44px
- [ ] Text is readable without zooming
- [ ] Horizontal scrolling not required
- [ ] Loading states work on mobile
```

## 🎯 Next.js 15 Specific Features

### App Router Integration
```typescript
// Parallel routes (if needed)
// app/[route-path]/@modal/(.)[modal-route]/page.tsx

// Route groups (if needed)
// app/(dashboard)/[route-path]/page.tsx

// Dynamic routes (if needed)
// app/[route-path]/[slug]/page.tsx

export default function DynamicPage({ params }: {
  params: { slug: string }
}) {
  return <PageContent slug={params.slug} />
}

// generateStaticParams for SSG
export async function generateStaticParams() {
  const posts = await getPosts()
  return posts.map((post) => ({
    slug: post.slug
  }))
}
```

### Server Components (if applicable)
```typescript
// Server Component for data fetching
export default async function ServerPage() {
  try {
    const data = await fetchDataOnServer()
    return <PageContent data={data} />
  } catch (error) {
    console.error('Server data fetch failed:', error)
    return <PageErrorFallback />
  }
}

// Client Component wrapper (if needed)
'use client'

export default function ClientPage() {
  return (
    <Suspense fallback={<PageSkeleton />}>
      <ClientPageContent />
    </Suspense>
  )
}
```

## ✅ Final Checklist

### Page Structure
- [ ] Page file structure follows Next.js 15 conventions
- [ ] Error boundaries wrap all major sections
- [ ] Loading states provide clear feedback
- [ ] Error states offer recovery options
- [ ] Navigation integration works correctly

### SEO & Performance
- [ ] Metadata is complete and optimized
- [ ] Page loads meet performance targets
- [ ] Images are optimized and have alt text
- [ ] Core Web Vitals requirements met
- [ ] No accessibility violations

### Code Quality
- [ ] TypeScript strict mode compliance
- [ ] ESLint passes without warnings
- [ ] Components follow established patterns
- [ ] Defensive programming for Firebase
- [ ] Comprehensive test coverage

### User Experience
- [ ] Page serves clear user value
- [ ] Navigation flow is intuitive
- [ ] Content is scannable and well-organized
- [ ] Mobile experience is optimized
- [ ] Error states are user-friendly

---

## 🎨 Design System Integration

This page should integrate seamlessly with the Syndicaps design system:

- **Visual Consistency**: Follow established typography, colors, and spacing
- **Component Reuse**: Leverage existing UI components where possible
- **Responsive Design**: Work perfectly across all device sizes
- **Loading States**: Provide appropriate feedback during data loading
- **Error Handling**: Graceful degradation with helpful error messages

---

*This template ensures new pages maintain consistency with Syndicaps' design system and development standards while providing excellent user experience and SEO performance.*