'use client'

import React, { useState } from 'react'
import { 
  User<PERSON><PERSON>ck, 
  Shield, 
  Plus, 
  Edit, 
  Trash2, 
  Key, 
  Eye,
  UserPlus,
  Users,
  Settings
} from 'lucide-react'

interface AdminUser {
  id: string
  email: string
  name: string
  role: 'super_admin' | 'admin' | 'moderator' | 'analyst' | 'support'
  lastLogin: Date
  isActive: boolean
  permissions: string[]
}

export default function AdminManagementPage() {
  const [selectedTab, setSelectedTab] = useState<'users' | 'roles' | 'permissions'>('users')
  const [showAddModal, setShowAddModal] = useState(false)

  // Mock data - replace with actual data fetching
  const adminUsers: AdminUser[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Super Admin',
      role: 'super_admin',
      lastLogin: new Date(),
      isActive: true,
      permissions: ['all']
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Content Moderator',
      role: 'moderator',
      lastLogin: new Date(Date.now() - 86400000),
      isActive: true,
      permissions: ['content', 'users', 'support']
    }
  ]

  const roleColors = {
    super_admin: 'bg-red-100 text-red-800 border-red-200',
    admin: 'bg-blue-100 text-blue-800 border-blue-200',
    moderator: 'bg-purple-100 text-purple-800 border-purple-200',
    analyst: 'bg-green-100 text-green-800 border-green-200',
    support: 'bg-yellow-100 text-yellow-800 border-yellow-200'
  }

  const getRoleDisplay = (role: string) => {
    const roleMap = {
      super_admin: 'Super Admin',
      admin: 'Admin',
      moderator: 'Moderator',
      analyst: 'Analyst',
      support: 'Support'
    }
    return roleMap[role as keyof typeof roleMap] || role
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Admin Management</h1>
            <p className="text-gray-400">Manage administrator accounts, roles, and permissions</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <UserPlus size={18} />
            <span>Add Admin</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'users', label: 'Admin Users', icon: Users },
              { id: 'roles', label: 'Roles & Permissions', icon: Shield },
              { id: 'permissions', label: 'Permission Matrix', icon: Key }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Admin Users Tab */}
      {selectedTab === 'users' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Admin</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Role</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Last Login</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Status</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {adminUsers.map((admin) => (
                  <tr key={admin.id} className="border-b border-gray-700 hover:bg-gray-750">
                    <td className="py-4 px-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-medium">
                            {admin.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="ml-3">
                          <div className="text-white font-medium">{admin.name}</div>
                          <div className="text-gray-400 text-sm">{admin.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${roleColors[admin.role]}`}>
                        <Shield size={12} className="mr-1" />
                        {getRoleDisplay(admin.role)}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-gray-300">
                      {admin.lastLogin.toLocaleDateString()}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        admin.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {admin.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <button className="text-blue-400 hover:text-blue-300 p-1">
                          <Eye size={16} />
                        </button>
                        <button className="text-yellow-400 hover:text-yellow-300 p-1">
                          <Edit size={16} />
                        </button>
                        {admin.role !== 'super_admin' && (
                          <button className="text-red-400 hover:text-red-300 p-1">
                            <Trash2 size={16} />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Roles & Permissions Tab */}
      {selectedTab === 'roles' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries({
            super_admin: {
              name: 'Super Admin',
              description: 'Full system access with root privileges',
              color: 'red',
              permissions: ['All Permissions', 'Root Access', 'System Control']
            },
            admin: {
              name: 'Admin',
              description: 'Full business operations access',
              color: 'blue', 
              permissions: ['Dashboard', 'Users', 'Content', 'Commerce']
            },
            moderator: {
              name: 'Moderator',
              description: 'Content and user moderation',
              color: 'purple',
              permissions: ['Content', 'Users', 'Support', 'Reviews']
            },
            analyst: {
              name: 'Analyst',
              description: 'Read-only analytics and reporting',
              color: 'green',
              permissions: ['Analytics', 'Reports', 'Dashboard']
            },
            support: {
              name: 'Support',
              description: 'Customer support operations',
              color: 'yellow',
              permissions: ['Support', 'Tickets', 'User Assistance']
            }
          }).map(([key, role]) => (
            <div key={key} className="bg-gray-800 rounded-lg p-6 border-l-4 border-gray-600">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">{role.name}</h3>
                <Shield className="text-gray-400" size={20} />
              </div>
              <p className="text-gray-400 text-sm mb-4">{role.description}</p>
              <div className="space-y-2">
                <span className="text-sm font-medium text-gray-300">Permissions:</span>
                <div className="flex flex-wrap gap-2">
                  {role.permissions.map((permission, index) => (
                    <span 
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-700 text-gray-300"
                    >
                      {permission}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Permission Matrix Tab */}
      {selectedTab === 'permissions' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">Permission Matrix</h3>
            <p className="text-gray-400 text-sm">View and manage detailed permissions for each role</p>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-3 px-4 text-gray-300">Resource</th>
                  <th className="text-center py-3 px-4 text-red-400">Super Admin</th>
                  <th className="text-center py-3 px-4 text-blue-400">Admin</th>
                  <th className="text-center py-3 px-4 text-purple-400">Moderator</th>
                  <th className="text-center py-3 px-4 text-green-400">Analyst</th>
                  <th className="text-center py-3 px-4 text-yellow-400">Support</th>
                </tr>
              </thead>
              <tbody>
                {[
                  'Dashboard', 'Analytics', 'Users', 'Products', 'Orders', 
                  'Content', 'Support', 'Admin Management', 'Security', 'System Settings'
                ].map((resource) => (
                  <tr key={resource} className="border-b border-gray-700">
                    <td className="py-3 px-4 text-white font-medium">{resource}</td>
                    <td className="text-center py-3 px-4">
                      <span className="text-green-400">✓</span>
                    </td>
                    <td className="text-center py-3 px-4">
                      <span className={resource.includes('Admin') || resource.includes('Security') ? 'text-red-400' : 'text-green-400'}>
                        {resource.includes('Admin') || resource.includes('Security') ? '✗' : '✓'}
                      </span>
                    </td>
                    <td className="text-center py-3 px-4">
                      <span className={['Dashboard', 'Users', 'Content', 'Support'].includes(resource) ? 'text-green-400' : 'text-red-400'}>
                        {['Dashboard', 'Users', 'Content', 'Support'].includes(resource) ? '✓' : '✗'}
                      </span>
                    </td>
                    <td className="text-center py-3 px-4">
                      <span className={['Dashboard', 'Analytics'].includes(resource) ? 'text-green-400' : 'text-red-400'}>
                        {['Dashboard', 'Analytics'].includes(resource) ? '✓' : '✗'}
                      </span>
                    </td>
                    <td className="text-center py-3 px-4">
                      <span className={['Dashboard', 'Support'].includes(resource) ? 'text-green-400' : 'text-red-400'}>
                        {['Dashboard', 'Support'].includes(resource) ? '✓' : '✗'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Add Admin Modal Placeholder */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Add New Admin</h3>
            <p className="text-gray-400 mb-4">Admin creation functionality would be implemented here</p>
            <div className="flex justify-end space-x-3">
              <button 
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                Create Admin
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}