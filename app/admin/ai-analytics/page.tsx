'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Brain, 
  TrendingUp, 
  Target, 
  Zap,
  BarChart3,
  Users,
  ShoppingCart,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Plus,
  Download,
  Upload,
  Filter,
  Search,
  Cpu,
  Database,
  Activity,
  Lightbulb,
  Sparkles,
  TrendingDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface MLModel {
  id: string
  name: string
  description: string
  type: 'classification' | 'regression' | 'clustering' | 'recommendation' | 'forecasting' | 'anomaly_detection'
  status: 'training' | 'deployed' | 'testing' | 'failed' | 'archived'
  accuracy: number
  lastTrained: Date
  version: string
  features: string[]
  targetVariable: string
  trainingData: {
    samples: number
    features: number
    lastUpdated: Date
  }
  performance: {
    precision: number
    recall: number
    f1Score: number
    auc: number
  }
  predictions: {
    total: number
    today: number
    accuracy: number
  }
  deployment: {
    environment: 'development' | 'staging' | 'production'
    endpoint: string
    instances: number
    avgResponseTime: number
  }
  createdAt: Date
  createdBy: string
}

interface PredictiveInsight {
  id: string
  title: string
  description: string
  category: 'revenue' | 'customer' | 'inventory' | 'marketing' | 'operations'
  priority: 'low' | 'medium' | 'high' | 'critical'
  confidence: number
  impact: 'positive' | 'negative' | 'neutral'
  timeframe: 'immediate' | 'short_term' | 'medium_term' | 'long_term'
  prediction: {
    value: number
    unit: string
    change: number
    changeType: 'percentage' | 'absolute'
  }
  recommendations: string[]
  dataPoints: {
    historical: { date: Date; value: number }[]
    predicted: { date: Date; value: number; confidence: number }[]
  }
  modelId: string
  generatedAt: Date
  expiresAt: Date
}

interface AIRecommendation {
  id: string
  title: string
  description: string
  type: 'product' | 'pricing' | 'marketing' | 'inventory' | 'customer_service'
  priority: 'low' | 'medium' | 'high' | 'critical'
  confidence: number
  expectedImpact: {
    metric: string
    value: number
    unit: string
  }
  implementation: {
    effort: 'low' | 'medium' | 'high'
    timeline: string
    resources: string[]
  }
  reasoning: string[]
  supportingData: Record<string, any>
  status: 'pending' | 'in_progress' | 'implemented' | 'rejected'
  createdAt: Date
  implementedAt?: Date
}

interface AnalyticsExperiment {
  id: string
  name: string
  description: string
  hypothesis: string
  type: 'ab_test' | 'multivariate' | 'cohort_analysis' | 'feature_flag'
  status: 'draft' | 'running' | 'completed' | 'paused' | 'failed'
  startDate: Date
  endDate?: Date
  duration: number // days
  variants: {
    id: string
    name: string
    description: string
    allocation: number // percentage
    metrics: {
      participants: number
      conversions: number
      conversionRate: number
      revenue: number
    }
  }[]
  primaryMetric: string
  secondaryMetrics: string[]
  results: {
    winner?: string
    significance: number
    confidenceInterval: [number, number]
    pValue: number
    effect: number
  }
  createdBy: string
  createdAt: Date
}

interface DataInsight {
  id: string
  title: string
  description: string
  category: 'trend' | 'anomaly' | 'correlation' | 'pattern' | 'opportunity'
  severity: 'info' | 'warning' | 'critical'
  confidence: number
  data: {
    metric: string
    currentValue: number
    previousValue: number
    change: number
    changeType: 'percentage' | 'absolute'
    timeframe: string
  }
  visualization: {
    type: 'line' | 'bar' | 'pie' | 'scatter' | 'heatmap'
    data: any[]
  }
  actionable: boolean
  suggestedActions: string[]
  detectedAt: Date
  relevantModels: string[]
}

export default function AIAnalyticsPage() {
  const [mlModels, setMlModels] = useState<MLModel[]>([])
  const [insights, setInsights] = useState<PredictiveInsight[]>([])
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([])
  const [experiments, setExperiments] = useState<AnalyticsExperiment[]>([])
  const [dataInsights, setDataInsights] = useState<DataInsight[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'models' | 'insights' | 'recommendations' | 'experiments'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadAIAnalyticsData()
  }, [])

  const loadAIAnalyticsData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual AI/ML API integration
      const mockMLModels: MLModel[] = [
        {
          id: 'model_001',
          name: 'Customer Churn Prediction',
          description: 'Predicts likelihood of customer churn based on behavior patterns',
          type: 'classification',
          status: 'deployed',
          accuracy: 0.87,
          lastTrained: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          version: '2.1.0',
          features: ['order_frequency', 'avg_order_value', 'last_login', 'support_tickets', 'engagement_score'],
          targetVariable: 'will_churn',
          trainingData: {
            samples: 15420,
            features: 23,
            lastUpdated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          },
          performance: {
            precision: 0.85,
            recall: 0.82,
            f1Score: 0.83,
            auc: 0.91
          },
          predictions: {
            total: 45230,
            today: 234,
            accuracy: 0.87
          },
          deployment: {
            environment: 'production',
            endpoint: '/api/ml/churn-prediction',
            instances: 3,
            avgResponseTime: 45
          },
          createdAt: new Date('2024-12-01'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'model_002',
          name: 'Revenue Forecasting',
          description: 'Predicts monthly revenue based on historical data and market trends',
          type: 'forecasting',
          status: 'deployed',
          accuracy: 0.92,
          lastTrained: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          version: '1.5.2',
          features: ['historical_revenue', 'seasonality', 'marketing_spend', 'product_launches', 'market_trends'],
          targetVariable: 'monthly_revenue',
          trainingData: {
            samples: 36,
            features: 15,
            lastUpdated: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
          },
          performance: {
            precision: 0.91,
            recall: 0.89,
            f1Score: 0.90,
            auc: 0.94
          },
          predictions: {
            total: 156,
            today: 1,
            accuracy: 0.92
          },
          deployment: {
            environment: 'production',
            endpoint: '/api/ml/revenue-forecast',
            instances: 2,
            avgResponseTime: 120
          },
          createdAt: new Date('2024-11-15'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'model_003',
          name: 'Product Recommendation Engine',
          description: 'Recommends products to users based on collaborative filtering',
          type: 'recommendation',
          status: 'deployed',
          accuracy: 0.78,
          lastTrained: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          version: '3.0.1',
          features: ['user_preferences', 'purchase_history', 'browsing_behavior', 'similar_users', 'product_features'],
          targetVariable: 'recommended_products',
          trainingData: {
            samples: 125000,
            features: 45,
            lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
          },
          performance: {
            precision: 0.76,
            recall: 0.74,
            f1Score: 0.75,
            auc: 0.82
          },
          predictions: {
            total: 890000,
            today: 12340,
            accuracy: 0.78
          },
          deployment: {
            environment: 'production',
            endpoint: '/api/ml/product-recommendations',
            instances: 5,
            avgResponseTime: 25
          },
          createdAt: new Date('2024-10-20'),
          createdBy: '<EMAIL>'
        }
      ]

      const mockInsights: PredictiveInsight[] = [
        {
          id: 'insight_001',
          title: 'Revenue Growth Acceleration Expected',
          description: 'Based on current trends and seasonal patterns, revenue is expected to increase by 23% next month',
          category: 'revenue',
          priority: 'high',
          confidence: 0.89,
          impact: 'positive',
          timeframe: 'short_term',
          prediction: {
            value: 145000,
            unit: 'USD',
            change: 23,
            changeType: 'percentage'
          },
          recommendations: [
            'Increase inventory for high-demand products',
            'Scale marketing campaigns to capitalize on growth',
            'Prepare customer support for increased volume'
          ],
          dataPoints: {
            historical: Array.from({ length: 12 }, (_, i) => ({
              date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000),
              value: 100000 + Math.random() * 20000 + i * 2000
            })),
            predicted: Array.from({ length: 3 }, (_, i) => ({
              date: new Date(Date.now() + (i + 1) * 30 * 24 * 60 * 60 * 1000),
              value: 145000 + i * 5000,
              confidence: 0.89 - i * 0.05
            }))
          },
          modelId: 'model_002',
          generatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        },
        {
          id: 'insight_002',
          title: 'High Churn Risk Detected',
          description: '156 customers identified as high churn risk in the next 30 days',
          category: 'customer',
          priority: 'critical',
          confidence: 0.87,
          impact: 'negative',
          timeframe: 'immediate',
          prediction: {
            value: 156,
            unit: 'customers',
            change: -12000,
            changeType: 'absolute'
          },
          recommendations: [
            'Launch targeted retention campaign',
            'Offer personalized discounts to at-risk customers',
            'Increase customer support touchpoints'
          ],
          dataPoints: {
            historical: Array.from({ length: 30 }, (_, i) => ({
              date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
              value: Math.floor(Math.random() * 50 + 100)
            })),
            predicted: Array.from({ length: 7 }, (_, i) => ({
              date: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000),
              value: 156 - i * 5,
              confidence: 0.87
            }))
          },
          modelId: 'model_001',
          generatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        }
      ]

      const mockRecommendations: AIRecommendation[] = [
        {
          id: 'rec_001',
          title: 'Optimize Product Pricing Strategy',
          description: 'Adjust pricing for 12 products to maximize revenue based on demand elasticity analysis',
          type: 'pricing',
          priority: 'high',
          confidence: 0.84,
          expectedImpact: {
            metric: 'revenue',
            value: 15,
            unit: 'percentage_increase'
          },
          implementation: {
            effort: 'medium',
            timeline: '2-3 weeks',
            resources: ['pricing_team', 'product_managers']
          },
          reasoning: [
            'Price elasticity analysis shows opportunity for optimization',
            'Competitor pricing analysis reveals market gaps',
            'Customer willingness-to-pay data supports price increases'
          ],
          supportingData: {
            affected_products: 12,
            avg_price_change: 8.5,
            confidence_interval: [0.78, 0.91]
          },
          status: 'pending',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000)
        },
        {
          id: 'rec_002',
          title: 'Expand Product Recommendation Coverage',
          description: 'Implement recommendations on checkout page to increase average order value',
          type: 'product',
          priority: 'medium',
          confidence: 0.76,
          expectedImpact: {
            metric: 'average_order_value',
            value: 12,
            unit: 'percentage_increase'
          },
          implementation: {
            effort: 'low',
            timeline: '1 week',
            resources: ['frontend_team']
          },
          reasoning: [
            'A/B test shows 12% AOV increase with checkout recommendations',
            'Current recommendation coverage is only 45%',
            'High-performing recommendation model available'
          ],
          supportingData: {
            current_coverage: 0.45,
            test_results: { aov_increase: 0.12, significance: 0.95 }
          },
          status: 'in_progress',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          implementedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        }
      ]

      const mockExperiments: AnalyticsExperiment[] = [
        {
          id: 'exp_001',
          name: 'Checkout Flow Optimization',
          description: 'Testing simplified checkout process vs. current multi-step flow',
          hypothesis: 'Simplified checkout will increase conversion rate by 15%',
          type: 'ab_test',
          status: 'running',
          startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          duration: 30,
          variants: [
            {
              id: 'control',
              name: 'Current Checkout',
              description: 'Existing multi-step checkout process',
              allocation: 50,
              metrics: {
                participants: 2340,
                conversions: 187,
                conversionRate: 0.08,
                revenue: 23400
              }
            },
            {
              id: 'treatment',
              name: 'Simplified Checkout',
              description: 'Single-page checkout with smart defaults',
              allocation: 50,
              metrics: {
                participants: 2298,
                conversions: 207,
                conversionRate: 0.09,
                revenue: 25890
              }
            }
          ],
          primaryMetric: 'conversion_rate',
          secondaryMetrics: ['average_order_value', 'time_to_complete'],
          results: {
            significance: 0.85,
            confidenceInterval: [0.02, 0.18],
            pValue: 0.15,
            effect: 0.12
          },
          createdBy: '<EMAIL>',
          createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
        }
      ]

      const mockDataInsights: DataInsight[] = [
        {
          id: 'data_001',
          title: 'Unusual Spike in Mobile Traffic',
          description: 'Mobile traffic increased by 45% in the last 24 hours',
          category: 'anomaly',
          severity: 'warning',
          confidence: 0.92,
          data: {
            metric: 'mobile_traffic',
            currentValue: 8900,
            previousValue: 6140,
            change: 45,
            changeType: 'percentage',
            timeframe: '24_hours'
          },
          visualization: {
            type: 'line',
            data: Array.from({ length: 24 }, (_, i) => ({
              hour: i,
              value: Math.random() * 1000 + 300 + (i > 18 ? 200 : 0)
            }))
          },
          actionable: true,
          suggestedActions: [
            'Investigate traffic source',
            'Monitor conversion rates',
            'Check mobile site performance'
          ],
          detectedAt: new Date(Date.now() - 30 * 60 * 1000),
          relevantModels: ['model_003']
        }
      ]

      setMlModels(mockMLModels)
      setInsights(mockInsights)
      setRecommendations(mockRecommendations)
      setExperiments(mockExperiments)
      setDataInsights(mockDataInsights)
    } catch (error) {
      console.error('Error loading AI analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'deployed': case 'running': case 'completed': return 'text-green-400 bg-green-900/20'
      case 'training': case 'testing': case 'in_progress': return 'text-blue-400 bg-blue-900/20'
      case 'failed': return 'text-red-400 bg-red-900/20'
      case 'paused': case 'draft': return 'text-yellow-400 bg-yellow-900/20'
      case 'archived': case 'pending': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'revenue': return 'text-green-400 bg-green-900/20'
      case 'customer': return 'text-blue-400 bg-blue-900/20'
      case 'inventory': return 'text-purple-400 bg-purple-900/20'
      case 'marketing': return 'text-pink-400 bg-pink-900/20'
      case 'operations': return 'text-yellow-400 bg-yellow-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatAccuracy = (accuracy: number) => {
    return `${(accuracy * 100).toFixed(1)}%`
  }

  const formatConfidence = (confidence: number) => {
    return `${(confidence * 100).toFixed(0)}%`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="w-8 h-8 text-purple-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">AI/ML Analytics & Predictions</h1>
            <p className="text-gray-400">Machine learning models, predictive analytics, and AI-powered business insights</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadAIAnalyticsData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/ai-analytics/models/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Model
          </Link>
        </div>
      </div>

      {/* AI Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active ML Models</p>
              <p className="text-2xl font-bold text-white">
                {mlModels.filter(m => m.status === 'deployed').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {mlModels.filter(m => m.status === 'training').length} training
              </p>
            </div>
            <Brain className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Predictions Today</p>
              <p className="text-2xl font-bold text-white">
                {mlModels.reduce((sum, m) => sum + m.predictions.today, 0).toLocaleString()}
              </p>
              <p className="text-xs text-blue-400 mt-1">Across all models</p>
            </div>
            <Target className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Model Accuracy</p>
              <p className="text-2xl font-bold text-white">
                {mlModels.length > 0
                  ? formatAccuracy(mlModels.reduce((sum, m) => sum + m.accuracy, 0) / mlModels.length)
                  : '0%'}
              </p>
              <p className="text-xs text-green-400 mt-1">Production models</p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Insights</p>
              <p className="text-2xl font-bold text-white">
                {insights.filter(i => new Date(i.expiresAt) > new Date()).length}
              </p>
              <p className="text-xs text-yellow-400 mt-1">
                {insights.filter(i => i.priority === 'critical').length} critical
              </p>
            </div>
            <Lightbulb className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'models', label: 'ML Models', icon: Brain, count: mlModels.length },
            { id: 'insights', label: 'Predictive Insights', icon: Lightbulb, count: insights.length },
            { id: 'recommendations', label: 'AI Recommendations', icon: Sparkles, count: recommendations.length },
            { id: 'experiments', label: 'Experiments', icon: Activity, count: experiments.length }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Critical Insights */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Critical AI Insights</h3>
            <div className="space-y-4">
              {insights.filter(i => i.priority === 'critical' || i.priority === 'high').slice(0, 3).map((insight) => (
                <div key={insight.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-medium text-white">{insight.title}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(insight.priority)}`}>
                          {insight.priority}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${getCategoryColor(insight.category)}`}>
                          {insight.category}
                        </span>
                      </div>
                      <p className="text-gray-400 text-sm mb-3">{insight.description}</p>

                      <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                        <div>
                          <span className="text-gray-400">Prediction:</span>
                          <span className="text-white ml-1">
                            {insight.prediction.changeType === 'percentage' ?
                              `${insight.prediction.change > 0 ? '+' : ''}${insight.prediction.change}%` :
                              `${insight.prediction.change > 0 ? '+' : ''}${insight.prediction.change}`
                            }
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-400">Confidence:</span>
                          <span className="text-white ml-1">{formatConfidence(insight.confidence)}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Timeframe:</span>
                          <span className="text-white ml-1">{insight.timeframe.replace('_', ' ')}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {insight.recommendations.slice(0, 2).map((rec, index) => (
                          <span key={index} className="text-xs bg-purple-900/30 text-purple-300 px-2 py-1 rounded">
                            {rec}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {insight.impact === 'positive' && <ArrowUp className="text-green-400" size={20} />}
                      {insight.impact === 'negative' && <ArrowDown className="text-red-400" size={20} />}
                      <span className="text-2xl font-bold text-white">
                        {insight.prediction.unit === 'USD' ?
                          formatCurrency(insight.prediction.value) :
                          `${insight.prediction.value} ${insight.prediction.unit}`
                        }
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Model Performance */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Model Performance</h3>
              <div className="space-y-3">
                {mlModels.filter(m => m.status === 'deployed').map((model) => (
                  <div key={model.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${getStatusColor(model.status)}`}>
                        <Brain size={16} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{model.name}</p>
                        <p className="text-xs text-gray-400">{model.type}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-white">{formatAccuracy(model.accuracy)}</p>
                      <p className="text-xs text-gray-400">{model.predictions.today} predictions today</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">AI Recommendations</h3>
              <div className="space-y-3">
                {recommendations.slice(0, 4).map((rec) => (
                  <div key={rec.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${getPriorityColor(rec.priority)}`}>
                        <Sparkles size={16} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{rec.title}</p>
                        <p className="text-xs text-gray-400">
                          {rec.expectedImpact.value}{rec.expectedImpact.unit.includes('percentage') ? '%' : ''} {rec.expectedImpact.metric.replace('_', ' ')}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-400">{formatConfidence(rec.confidence)}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(rec.status)}`}>
                        {rec.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Data Insights */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent Data Insights</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dataInsights.map((insight) => (
                <div key={insight.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-white">{insight.title}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      insight.severity === 'critical' ? 'text-red-400 bg-red-900/20' :
                      insight.severity === 'warning' ? 'text-yellow-400 bg-yellow-900/20' :
                      'text-blue-400 bg-blue-900/20'
                    }`}>
                      {insight.severity}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400 mb-3">{insight.description}</p>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Change:</span>
                    <span className={`font-medium ${insight.data.change > 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {insight.data.change > 0 ? '+' : ''}{insight.data.change}%
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="text-gray-400">Confidence:</span>
                    <span className="text-white">{formatConfidence(insight.confidence)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'models' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search ML models..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="deployed">Deployed</option>
                  <option value="training">Training</option>
                  <option value="testing">Testing</option>
                  <option value="failed">Failed</option>
                </select>
              </div>
            </div>
          </div>

          {/* ML Models List */}
          <div className="space-y-4">
            {mlModels.map((model) => (
              <motion.div
                key={model.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{model.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(model.status)}`}>
                        {model.status}
                      </span>
                      <span className="text-xs text-gray-400">v{model.version}</span>
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{model.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Type:</span>
                        <span className="text-white ml-1">{model.type.replace('_', ' ')}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Accuracy:</span>
                        <span className="text-white ml-1">{formatAccuracy(model.accuracy)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Predictions Today:</span>
                        <span className="text-white ml-1">{model.predictions.today.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Response Time:</span>
                        <span className="text-white ml-1">{model.deployment.avgResponseTime}ms</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Last trained: {model.lastTrained.toLocaleDateString()}</span>
                      <span>Features: {model.features.length}</span>
                      <span>Training samples: {model.trainingData.samples.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Configure">
                      <Settings size={16} />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Retrain">
                      <RefreshCw size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'insights' || activeTab === 'recommendations' || activeTab === 'experiments') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'insights' && 'Predictive Insights & Forecasting'}
            {activeTab === 'recommendations' && 'AI-Powered Business Recommendations'}
            {activeTab === 'experiments' && 'A/B Testing & Analytics Experiments'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'insights' && <Lightbulb className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'recommendations' && <Sparkles className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'experiments' && <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'insights' && 'Advanced Predictive Analytics'}
              {activeTab === 'recommendations' && 'Intelligent Business Recommendations'}
              {activeTab === 'experiments' && 'Data-Driven Experimentation'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'insights' && 'Generate actionable insights from predictive models with confidence scoring and impact analysis.'}
              {activeTab === 'recommendations' && 'AI-powered recommendations for pricing, inventory, marketing, and business optimization.'}
              {activeTab === 'experiments' && 'Design and analyze A/B tests, multivariate experiments, and feature rollouts with statistical significance.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
