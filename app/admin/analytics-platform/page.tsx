'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BarChart3, 
  Database, 
  Cloud, 
  Zap,
  Settings,
  Plus,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Activity,
  TrendingUp,
  TrendingDown,
  Download,
  Upload,
  Filter,
  Search,
  Globe,
  Server,
  Cpu,
  HardDrive,
  Wifi,
  Shield,
  Key,
  Link as LinkIcon,
  FileText
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface AnalyticsConnection {
  id: string
  name: string
  provider: 'google_analytics' | 'mixpanel' | 'amplitude' | 'segment' | 'snowflake' | 'bigquery' | 'redshift' | 'databricks' | 'custom'
  type: 'analytics' | 'data_warehouse' | 'business_intelligence' | 'customer_data_platform'
  status: 'connected' | 'disconnected' | 'error' | 'syncing'
  description: string
  configuration: {
    endpoint?: string
    apiKey?: string
    projectId?: string
    dataset?: string
    credentials?: Record<string, any>
    syncFrequency: 'real_time' | 'hourly' | 'daily' | 'weekly'
    dataRetention: number // days
  }
  dataStreams: DataStream[]
  metrics: {
    totalEvents: number
    eventsToday: number
    lastSync: Date
    syncDuration: number // seconds
    errorCount: number
    dataVolume: number // MB
  }
  permissions: string[]
  createdAt: Date
  lastModified: Date
  createdBy: string
}

interface DataStream {
  id: string
  name: string
  type: 'events' | 'users' | 'products' | 'orders' | 'sessions' | 'custom'
  source: string
  destination: string
  isActive: boolean
  schema: {
    fields: { name: string; type: string; required: boolean }[]
    primaryKey: string
    partitionKey?: string
  }
  transformations: DataTransformation[]
  metrics: {
    recordsProcessed: number
    recordsToday: number
    lastProcessed: Date
    errorRate: number
    avgLatency: number
  }
}

interface DataTransformation {
  id: string
  name: string
  type: 'filter' | 'map' | 'aggregate' | 'join' | 'custom'
  config: Record<string, any>
  isActive: boolean
}

interface DataPipeline {
  id: string
  name: string
  description: string
  status: 'running' | 'stopped' | 'error' | 'scheduled'
  schedule: {
    type: 'continuous' | 'batch'
    frequency?: string // cron expression
    timezone: string
  }
  sources: string[]
  destinations: string[]
  transformations: DataTransformation[]
  metrics: {
    recordsProcessed: number
    successRate: number
    avgProcessingTime: number
    lastRun: Date
    nextRun?: Date
  }
  alerts: {
    onFailure: boolean
    onDelay: boolean
    recipients: string[]
  }
  createdAt: Date
  createdBy: string
}

interface AnalyticsReport {
  id: string
  name: string
  description: string
  type: 'dashboard' | 'report' | 'alert'
  dataSource: string
  visualization: {
    type: 'chart' | 'table' | 'metric' | 'map'
    config: Record<string, any>
  }
  filters: Record<string, any>
  schedule?: {
    frequency: string
    recipients: string[]
    format: 'pdf' | 'excel' | 'csv'
  }
  metrics: {
    views: number
    lastViewed: Date
    avgLoadTime: number
  }
  createdAt: Date
  createdBy: string
}

export default function AnalyticsPlatformPage() {
  const [connections, setConnections] = useState<AnalyticsConnection[]>([])
  const [dataStreams, setDataStreams] = useState<DataStream[]>([])
  const [pipelines, setPipelines] = useState<DataPipeline[]>([])
  const [reports, setReports] = useState<AnalyticsReport[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'connections' | 'streams' | 'pipelines' | 'reports'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadAnalyticsData()
  }, [])

  const loadAnalyticsData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual analytics platform API integration
      const mockConnections: AnalyticsConnection[] = [
        {
          id: 'conn_001',
          name: 'Google Analytics 4',
          provider: 'google_analytics',
          type: 'analytics',
          status: 'connected',
          description: 'Web analytics and user behavior tracking',
          configuration: {
            projectId: 'syndicaps-analytics',
            apiKey: 'ga4_api_key_***',
            syncFrequency: 'hourly',
            dataRetention: 365
          },
          dataStreams: [],
          metrics: {
            totalEvents: 1250000,
            eventsToday: 15420,
            lastSync: new Date(Date.now() - 30 * 60 * 1000),
            syncDuration: 45,
            errorCount: 3,
            dataVolume: 2.4
          },
          permissions: ['read', 'export'],
          createdAt: new Date('2025-01-01'),
          lastModified: new Date(),
          createdBy: '<EMAIL>'
        },
        {
          id: 'conn_002',
          name: 'Mixpanel Events',
          provider: 'mixpanel',
          type: 'analytics',
          status: 'connected',
          description: 'Product analytics and user journey tracking',
          configuration: {
            projectId: 'syndicaps-mixpanel',
            apiKey: 'mixpanel_api_key_***',
            syncFrequency: 'real_time',
            dataRetention: 730
          },
          dataStreams: [],
          metrics: {
            totalEvents: 890000,
            eventsToday: 12340,
            lastSync: new Date(Date.now() - 5 * 60 * 1000),
            syncDuration: 12,
            errorCount: 0,
            dataVolume: 1.8
          },
          permissions: ['read', 'write', 'export'],
          createdAt: new Date('2025-01-03'),
          lastModified: new Date(),
          createdBy: '<EMAIL>'
        },
        {
          id: 'conn_003',
          name: 'BigQuery Data Warehouse',
          provider: 'bigquery',
          type: 'data_warehouse',
          status: 'syncing',
          description: 'Cloud data warehouse for analytics and reporting',
          configuration: {
            projectId: 'syndicaps-bigquery',
            dataset: 'analytics_data',
            syncFrequency: 'daily',
            dataRetention: 2555 // 7 years
          },
          dataStreams: [],
          metrics: {
            totalEvents: 5600000,
            eventsToday: 45000,
            lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
            syncDuration: 180,
            errorCount: 1,
            dataVolume: 15.6
          },
          permissions: ['read', 'write', 'admin'],
          createdAt: new Date('2024-12-15'),
          lastModified: new Date(),
          createdBy: '<EMAIL>'
        },
        {
          id: 'conn_004',
          name: 'Segment CDP',
          provider: 'segment',
          type: 'customer_data_platform',
          status: 'error',
          description: 'Customer data platform for unified user profiles',
          configuration: {
            endpoint: 'https://api.segment.io/v1',
            apiKey: 'segment_write_key_***',
            syncFrequency: 'real_time',
            dataRetention: 365
          },
          dataStreams: [],
          metrics: {
            totalEvents: 2100000,
            eventsToday: 8900,
            lastSync: new Date(Date.now() - 4 * 60 * 60 * 1000),
            syncDuration: 0,
            errorCount: 25,
            dataVolume: 3.2
          },
          permissions: ['read', 'write'],
          createdAt: new Date('2025-01-08'),
          lastModified: new Date(),
          createdBy: '<EMAIL>'
        }
      ]

      const mockDataStreams: DataStream[] = [
        {
          id: 'stream_001',
          name: 'User Events Stream',
          type: 'events',
          source: 'web_app',
          destination: 'bigquery',
          isActive: true,
          schema: {
            fields: [
              { name: 'user_id', type: 'string', required: true },
              { name: 'event_name', type: 'string', required: true },
              { name: 'timestamp', type: 'timestamp', required: true },
              { name: 'properties', type: 'json', required: false }
            ],
            primaryKey: 'user_id',
            partitionKey: 'timestamp'
          },
          transformations: [
            {
              id: 'transform_001',
              name: 'PII Anonymization',
              type: 'map',
              config: { anonymize_fields: ['email', 'ip_address'] },
              isActive: true
            }
          ],
          metrics: {
            recordsProcessed: 1250000,
            recordsToday: 15420,
            lastProcessed: new Date(Date.now() - 5 * 60 * 1000),
            errorRate: 0.02,
            avgLatency: 150
          }
        },
        {
          id: 'stream_002',
          name: 'Order Data Stream',
          type: 'orders',
          source: 'ecommerce_api',
          destination: 'mixpanel',
          isActive: true,
          schema: {
            fields: [
              { name: 'order_id', type: 'string', required: true },
              { name: 'user_id', type: 'string', required: true },
              { name: 'total_amount', type: 'decimal', required: true },
              { name: 'items', type: 'json', required: true },
              { name: 'created_at', type: 'timestamp', required: true }
            ],
            primaryKey: 'order_id'
          },
          transformations: [],
          metrics: {
            recordsProcessed: 45000,
            recordsToday: 234,
            lastProcessed: new Date(Date.now() - 10 * 60 * 1000),
            errorRate: 0.01,
            avgLatency: 89
          }
        }
      ]

      const mockPipelines: DataPipeline[] = [
        {
          id: 'pipeline_001',
          name: 'Daily Analytics ETL',
          description: 'Extract, transform, and load daily analytics data',
          status: 'running',
          schedule: {
            type: 'batch',
            frequency: '0 2 * * *', // Daily at 2 AM
            timezone: 'UTC'
          },
          sources: ['google_analytics', 'mixpanel'],
          destinations: ['bigquery'],
          transformations: [
            {
              id: 'transform_001',
              name: 'Data Deduplication',
              type: 'filter',
              config: { remove_duplicates: true },
              isActive: true
            },
            {
              id: 'transform_002',
              name: 'Revenue Attribution',
              type: 'join',
              config: { join_table: 'orders', join_key: 'user_id' },
              isActive: true
            }
          ],
          metrics: {
            recordsProcessed: 2500000,
            successRate: 99.8,
            avgProcessingTime: 1800, // 30 minutes
            lastRun: new Date(Date.now() - 6 * 60 * 60 * 1000),
            nextRun: new Date(Date.now() + 18 * 60 * 60 * 1000)
          },
          alerts: {
            onFailure: true,
            onDelay: true,
            recipients: ['<EMAIL>', '<EMAIL>']
          },
          createdAt: new Date('2024-12-20'),
          createdBy: '<EMAIL>'
        }
      ]

      const mockReports: AnalyticsReport[] = [
        {
          id: 'report_001',
          name: 'Daily Revenue Dashboard',
          description: 'Real-time revenue and conversion metrics',
          type: 'dashboard',
          dataSource: 'bigquery',
          visualization: {
            type: 'chart',
            config: {
              chartType: 'line',
              metrics: ['revenue', 'orders', 'conversion_rate'],
              timeRange: '7d'
            }
          },
          filters: {
            date_range: 'last_7_days',
            product_category: 'all'
          },
          metrics: {
            views: 1250,
            lastViewed: new Date(Date.now() - 30 * 60 * 1000),
            avgLoadTime: 2.3
          },
          createdAt: new Date('2025-01-01'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'report_002',
          name: 'Weekly Business Report',
          description: 'Comprehensive weekly business performance report',
          type: 'report',
          dataSource: 'bigquery',
          visualization: {
            type: 'table',
            config: {
              columns: ['metric', 'current_week', 'previous_week', 'change'],
              sortBy: 'metric'
            }
          },
          filters: {},
          schedule: {
            frequency: '0 9 * * 1', // Monday at 9 AM
            recipients: ['<EMAIL>'],
            format: 'pdf'
          },
          metrics: {
            views: 45,
            lastViewed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            avgLoadTime: 5.7
          },
          createdAt: new Date('2024-12-15'),
          createdBy: '<EMAIL>'
        }
      ]

      setConnections(mockConnections)
      setDataStreams(mockDataStreams)
      setPipelines(mockPipelines)
      setReports(mockReports)
    } catch (error) {
      console.error('Error loading analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredConnections = connections.filter(connection => {
    const matchesSearch = connection.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         connection.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || connection.type === filterType
    const matchesStatus = filterStatus === 'all' || connection.status === filterStatus
    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-400 bg-green-900/20'
      case 'disconnected': return 'text-gray-400 bg-gray-900/20'
      case 'error': return 'text-red-400 bg-red-900/20'
      case 'syncing': return 'text-blue-400 bg-blue-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'analytics': return 'text-purple-400 bg-purple-900/20'
      case 'data_warehouse': return 'text-blue-400 bg-blue-900/20'
      case 'business_intelligence': return 'text-green-400 bg-green-900/20'
      case 'customer_data_platform': return 'text-yellow-400 bg-yellow-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'google_analytics': return <BarChart3 size={16} />
      case 'mixpanel': return <Activity size={16} />
      case 'bigquery': return <Database size={16} />
      case 'segment': return <Globe size={16} />
      case 'snowflake': return <Cloud size={16} />
      default: return <Server size={16} />
    }
  }

  const formatDataVolume = (volumeMB: number) => {
    if (volumeMB < 1024) return `${volumeMB.toFixed(1)} MB`
    const volumeGB = volumeMB / 1024
    if (volumeGB < 1024) return `${volumeGB.toFixed(1)} GB`
    const volumeTB = volumeGB / 1024
    return `${volumeTB.toFixed(1)} TB`
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const toggleConnection = async (connectionId: string) => {
    setConnections(prev => prev.map(conn => 
      conn.id === connectionId 
        ? { 
            ...conn, 
            status: conn.status === 'connected' ? 'disconnected' : 'connected' 
          }
        : conn
    ))
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BarChart3 className="w-8 h-8 text-purple-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Analytics Platform Connections</h1>
            <p className="text-gray-400">External analytics platforms, data warehousing, and business intelligence</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadAnalyticsData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/analytics-platform/connections/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Add Connection
          </Link>
        </div>
      </div>

      {/* Analytics Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Connections</p>
              <p className="text-2xl font-bold text-white">
                {connections.filter(c => c.status === 'connected').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {connections.filter(c => c.status === 'error').length} errors
              </p>
            </div>
            <LinkIcon className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Events Today</p>
              <p className="text-2xl font-bold text-white">
                {connections.reduce((sum, c) => sum + c.metrics.eventsToday, 0).toLocaleString()}
              </p>
              <p className="text-xs text-blue-400 mt-1">Across all platforms</p>
            </div>
            <Activity className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Data Volume</p>
              <p className="text-2xl font-bold text-white">
                {formatDataVolume(connections.reduce((sum, c) => sum + c.metrics.dataVolume, 0))}
              </p>
              <p className="text-xs text-purple-400 mt-1">Total processed</p>
            </div>
            <Database className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Data Streams</p>
              <p className="text-2xl font-bold text-white">
                {dataStreams.filter(s => s.isActive).length}
              </p>
              <p className="text-xs text-yellow-400 mt-1">Active streams</p>
            </div>
            <Zap className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'connections', label: 'Connections', icon: LinkIcon, count: connections.length },
            { id: 'streams', label: 'Data Streams', icon: Zap, count: dataStreams.length },
            { id: 'pipelines', label: 'Pipelines', icon: Settings, count: pipelines.length },
            { id: 'reports', label: 'Reports', icon: FileText, count: reports.length }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Connection Status Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {connections.map((connection) => (
              <motion.div
                key={connection.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded ${getTypeColor(connection.type)}`}>
                      {getProviderIcon(connection.provider)}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">{connection.name}</h3>
                      <p className="text-sm text-gray-400">{connection.provider}</p>
                    </div>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(connection.status)}`}>
                    {connection.status}
                  </span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Events Today:</span>
                    <span className="text-white">{connection.metrics.eventsToday.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Data Volume:</span>
                    <span className="text-white">{formatDataVolume(connection.metrics.dataVolume)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Last Sync:</span>
                    <span className="text-white">{connection.metrics.lastSync.toLocaleTimeString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Errors:</span>
                    <span className={`${connection.metrics.errorCount > 0 ? 'text-red-400' : 'text-green-400'}`}>
                      {connection.metrics.errorCount}
                    </span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-700">
                  <div className="flex space-x-2">
                    <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm">
                      View Details
                    </button>
                    <button
                      onClick={() => toggleConnection(connection.id)}
                      className={`px-3 py-2 rounded text-sm ${
                        connection.status === 'connected'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                    >
                      {connection.status === 'connected' ? 'Disconnect' : 'Connect'}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Recent Activity */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent Data Pipeline Activity</h3>
            <div className="space-y-3">
              {pipelines.slice(0, 5).map((pipeline) => (
                <div key={pipeline.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      pipeline.status === 'running' ? 'bg-green-400' :
                      pipeline.status === 'error' ? 'bg-red-400' :
                      pipeline.status === 'stopped' ? 'bg-gray-400' : 'bg-yellow-400'
                    }`}></div>
                    <div>
                      <p className="text-sm font-medium text-white">{pipeline.name}</p>
                      <p className="text-xs text-gray-400">
                        Last run: {pipeline.metrics.lastRun.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white">
                      {pipeline.metrics.recordsProcessed.toLocaleString()} records
                    </p>
                    <p className="text-xs text-gray-400">
                      {pipeline.metrics.successRate.toFixed(1)}% success
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'connections' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search connections..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Types</option>
                  <option value="analytics">Analytics</option>
                  <option value="data_warehouse">Data Warehouse</option>
                  <option value="business_intelligence">Business Intelligence</option>
                  <option value="customer_data_platform">Customer Data Platform</option>
                </select>

                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="connected">Connected</option>
                  <option value="disconnected">Disconnected</option>
                  <option value="error">Error</option>
                  <option value="syncing">Syncing</option>
                </select>
              </div>
            </div>
          </div>

          {/* Connections List */}
          <div className="space-y-4">
            {filteredConnections.map((connection) => (
              <motion.div
                key={connection.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg ${getTypeColor(connection.type)}`}>
                      {getProviderIcon(connection.provider)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-white">{connection.name}</h3>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(connection.status)}`}>
                          {connection.status}
                        </span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(connection.type)}`}>
                          {connection.type.replace('_', ' ')}
                        </span>
                      </div>

                      <p className="text-gray-400 text-sm mb-3">{connection.description}</p>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                        <div>
                          <span className="text-gray-400">Provider:</span>
                          <span className="text-white ml-1">{connection.provider}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Events Today:</span>
                          <span className="text-white ml-1">{connection.metrics.eventsToday.toLocaleString()}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Data Volume:</span>
                          <span className="text-white ml-1">{formatDataVolume(connection.metrics.dataVolume)}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Sync Frequency:</span>
                          <span className="text-white ml-1">{connection.configuration.syncFrequency.replace('_', ' ')}</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Created: {connection.createdAt.toLocaleDateString()}</span>
                        <span>Last Sync: {connection.metrics.lastSync.toLocaleString()}</span>
                        <span>Duration: {formatDuration(connection.metrics.syncDuration)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Configure">
                      <Settings size={16} />
                    </button>
                    <button
                      onClick={() => toggleConnection(connection.id)}
                      className={`p-2 rounded ${
                        connection.status === 'connected'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                      title={connection.status === 'connected' ? 'Disconnect' : 'Connect'}
                    >
                      {connection.status === 'connected' ? <XCircle size={16} /> : <CheckCircle size={16} />}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'streams' || activeTab === 'pipelines' || activeTab === 'reports') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'streams' && 'Data Streams Management'}
            {activeTab === 'pipelines' && 'Data Pipeline Management'}
            {activeTab === 'reports' && 'Analytics Reports & Dashboards'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'streams' && <Zap className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'pipelines' && <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'reports' && <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'streams' && 'Real-time Data Streaming'}
              {activeTab === 'pipelines' && 'ETL Pipeline Management'}
              {activeTab === 'reports' && 'Business Intelligence Reporting'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'streams' && 'Manage real-time data streams with schema validation, transformations, and monitoring.'}
              {activeTab === 'pipelines' && 'Create and manage ETL pipelines for data processing, transformation, and loading.'}
              {activeTab === 'reports' && 'Build custom dashboards and reports with automated scheduling and distribution.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
