'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Calendar, 
  Download, 
  Filter,
  Settings,
  RefreshCw,
  Eye,
  Plus,
  Layers,
  Target,
  Zap,
  Brain
} from 'lucide-react'
import BackButton from '../../../../src/admin/components/common/BackButton'
import { AnalyticsChart } from '../../../../src/admin/components/analytics/AnalyticsChart'
import { useAnalytics } from '../../../../src/admin/hooks/useAnalytics'

interface CustomReport {
  id: string
  name: string
  description: string
  metrics: string[]
  chartType: string
  timeRange: string
  filters: any[]
  createdAt: Date
  lastRun: Date
  isScheduled: boolean
}

interface BusinessIntelligence {
  insights: Array<{
    id: string
    title: string
    description: string
    impact: 'high' | 'medium' | 'low'
    category: string
    recommendation: string
    confidence: number
  }>
  trends: Array<{
    metric: string
    direction: 'up' | 'down' | 'stable'
    change: number
    significance: 'significant' | 'moderate' | 'minor'
  }>
  predictions: Array<{
    metric: string
    predicted_value: number
    confidence_interval: [number, number]
    timeframe: string
  }>
}

export default function AdvancedAnalyticsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'custom' | 'intelligence' | 'reports'>('overview')
  const [customReports, setCustomReports] = useState<CustomReport[]>([])
  const [businessIntelligence, setBusinessIntelligence] = useState<BusinessIntelligence | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateReport, setShowCreateReport] = useState(false)

  const {
    data: analyticsData,
    loading: analyticsLoading,
    refresh: refreshAnalytics
  } = useAnalytics({
    initialMetrics: ['total_revenue', 'total_orders', 'total_users', 'conversion_rate', 'avg_order_value'],
    enableRealTime: true
  })

  useEffect(() => {
    loadAdvancedAnalytics()
  }, [])

  const loadAdvancedAnalytics = async () => {
    setLoading(true)
    try {
      // Load custom reports
      const reports = await loadCustomReports()
      setCustomReports(reports)

      // Load business intelligence insights
      const intelligence = await loadBusinessIntelligence()
      setBusinessIntelligence(intelligence)

    } catch (error) {
      console.error('Error loading advanced analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadCustomReports = async (): Promise<CustomReport[]> => {
    // Simulate API call - would fetch from backend
    return [
      {
        id: '1',
        name: 'Weekly Revenue Analysis',
        description: 'Comprehensive weekly revenue breakdown with trends',
        metrics: ['total_revenue', 'avg_order_value', 'conversion_rate'],
        chartType: 'line',
        timeRange: '7d',
        filters: [],
        createdAt: new Date('2025-01-01'),
        lastRun: new Date(),
        isScheduled: true
      },
      {
        id: '2',
        name: 'Product Performance Dashboard',
        description: 'Top performing products and categories analysis',
        metrics: ['product_sales', 'category_performance', 'inventory_turnover'],
        chartType: 'bar',
        timeRange: '30d',
        filters: [{ field: 'category', operator: 'in', value: ['artisan', 'keyset'] }],
        createdAt: new Date('2025-01-05'),
        lastRun: new Date(),
        isScheduled: false
      }
    ]
  }

  const loadBusinessIntelligence = async (): Promise<BusinessIntelligence> => {
    // Simulate AI-powered business intelligence
    return {
      insights: [
        {
          id: '1',
          title: 'Revenue Growth Opportunity',
          description: 'Artisan keycaps show 45% higher conversion rates during weekend periods',
          impact: 'high',
          category: 'Revenue Optimization',
          recommendation: 'Increase weekend marketing campaigns for artisan products',
          confidence: 0.87
        },
        {
          id: '2',
          title: 'Customer Retention Pattern',
          description: 'Users who purchase within first 7 days have 3x higher lifetime value',
          impact: 'high',
          category: 'Customer Retention',
          recommendation: 'Implement welcome series with purchase incentives',
          confidence: 0.92
        },
        {
          id: '3',
          title: 'Inventory Optimization',
          description: 'Cherry MX compatible products have 23% faster turnover rates',
          impact: 'medium',
          category: 'Inventory Management',
          recommendation: 'Prioritize Cherry MX compatible inventory restocking',
          confidence: 0.78
        }
      ],
      trends: [
        { metric: 'Revenue', direction: 'up', change: 12.5, significance: 'significant' },
        { metric: 'User Acquisition', direction: 'up', change: 8.3, significance: 'moderate' },
        { metric: 'Cart Abandonment', direction: 'down', change: -5.2, significance: 'moderate' },
        { metric: 'Average Order Value', direction: 'stable', change: 1.1, significance: 'minor' }
      ],
      predictions: [
        { metric: 'Monthly Revenue', predicted_value: 45000, confidence_interval: [42000, 48000], timeframe: 'Next 30 days' },
        { metric: 'New Users', predicted_value: 1200, confidence_interval: [1100, 1300], timeframe: 'Next 30 days' },
        { metric: 'Order Volume', predicted_value: 850, confidence_interval: [800, 900], timeframe: 'Next 30 days' }
      ]
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-400 bg-red-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingUp className="text-green-400" size={16} />
      case 'down': return <TrendingUp className="text-red-400 rotate-180" size={16} />
      case 'stable': return <div className="w-4 h-0.5 bg-gray-400"></div>
      default: return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Advanced Analytics</h1>
          <p className="text-gray-400">Business intelligence, custom reporting, and predictive insights</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={refreshAnalytics}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateReport(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Report
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'custom', label: 'Custom Reports', icon: Layers },
            { id: 'intelligence', label: 'Business Intelligence', icon: Brain },
            { id: 'reports', label: 'Scheduled Reports', icon: Calendar }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {analyticsData?.metrics.map((metric, index) => (
              <motion.div
                key={metric.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">{metric.label}</p>
                    <p className="text-2xl font-bold text-white">{metric.formattedValue}</p>
                    {metric.change && (
                      <div className="flex items-center mt-1">
                        {getTrendIcon(metric.change > 0 ? 'up' : metric.change < 0 ? 'down' : 'stable')}
                        <span className={`text-sm ml-1 ${
                          metric.change > 0 ? 'text-green-400' : 
                          metric.change < 0 ? 'text-red-400' : 'text-gray-400'
                        }`}>
                          {metric.change > 0 ? '+' : ''}{metric.change}%
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="text-purple-400">
                    <BarChart3 size={24} />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Revenue Trend</h3>
              {analyticsData && (
                <AnalyticsChart
                  config={{
                    type: 'line',
                    series: [{ label: 'Revenue', color: '#8B5CF6' }],
                    options: { showGrid: true, showTooltips: true, animated: true }
                  }}
                  data={analyticsData.timeSeries}
                  height={300}
                  loading={analyticsLoading}
                />
              )}
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Order Volume</h3>
              {analyticsData && (
                <AnalyticsChart
                  config={{
                    type: 'bar',
                    series: [{ label: 'Orders', color: '#10B981' }],
                    options: { showGrid: true, showTooltips: true, animated: true }
                  }}
                  data={analyticsData.timeSeries}
                  height={300}
                  loading={analyticsLoading}
                />
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'custom' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {customReports.map((report) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white">{report.name}</h3>
                    <p className="text-gray-400 text-sm mt-1">{report.description}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-gray-400 hover:text-white">
                      <Eye size={16} />
                    </button>
                    <button className="text-gray-400 hover:text-white">
                      <Settings size={16} />
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Metrics:</span>
                    <span className="text-white">{report.metrics.length}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Time Range:</span>
                    <span className="text-white">{report.timeRange}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Last Run:</span>
                    <span className="text-white">{report.lastRun.toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Scheduled:</span>
                    <span className={`${report.isScheduled ? 'text-green-400' : 'text-gray-400'}`}>
                      {report.isScheduled ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-700">
                  <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-sm transition-colors">
                    Run Report
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'intelligence' && businessIntelligence && (
        <div className="space-y-6">
          {/* AI Insights */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Brain className="mr-2 text-purple-400" size={20} />
              AI-Powered Insights
            </h3>
            <div className="space-y-4">
              {businessIntelligence.insights.map((insight) => (
                <div key={insight.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-white">{insight.title}</h4>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getImpactColor(insight.impact)}`}>
                      {insight.impact} impact
                    </span>
                  </div>
                  <p className="text-gray-300 text-sm mb-3">{insight.description}</p>
                  <div className="bg-gray-800 p-3 rounded border-l-4 border-purple-500">
                    <p className="text-purple-300 text-sm font-medium">Recommendation:</p>
                    <p className="text-gray-300 text-sm">{insight.recommendation}</p>
                  </div>
                  <div className="mt-2 flex items-center justify-between text-xs">
                    <span className="text-gray-400">Category: {insight.category}</span>
                    <span className="text-gray-400">Confidence: {(insight.confidence * 100).toFixed(0)}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Trend Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Trend Analysis</h3>
              <div className="space-y-3">
                {businessIntelligence.trends.map((trend, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div className="flex items-center">
                      {getTrendIcon(trend.direction)}
                      <span className="text-white ml-3">{trend.metric}</span>
                    </div>
                    <div className="text-right">
                      <span className={`font-medium ${
                        trend.direction === 'up' ? 'text-green-400' : 
                        trend.direction === 'down' ? 'text-red-400' : 'text-gray-400'
                      }`}>
                        {trend.direction === 'up' ? '+' : trend.direction === 'down' ? '' : '±'}{trend.change}%
                      </span>
                      <p className="text-xs text-gray-400">{trend.significance}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Predictions</h3>
              <div className="space-y-3">
                {businessIntelligence.predictions.map((prediction, index) => (
                  <div key={index} className="p-3 bg-gray-700 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">{prediction.metric}</span>
                      <span className="text-purple-400 font-bold">{prediction.predicted_value.toLocaleString()}</span>
                    </div>
                    <div className="text-xs text-gray-400">
                      <p>Range: {prediction.confidence_interval[0].toLocaleString()} - {prediction.confidence_interval[1].toLocaleString()}</p>
                      <p>Timeframe: {prediction.timeframe}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'reports' && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Scheduled Reports</h3>
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-white mb-2">No Scheduled Reports</h4>
            <p className="text-gray-400 mb-4">Set up automated reports to be delivered to your inbox.</p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
              Schedule Report
            </button>
          </div>
        </div>
      )}

      {/* Create Report Modal */}
      {showCreateReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Create Custom Report</h3>
            <p className="text-gray-400 mb-4">
              Custom report builder will allow you to create personalized analytics reports with 
              custom metrics, visualizations, and scheduling options.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowCreateReport(false)}
                className="flex-1 py-2 px-4 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
