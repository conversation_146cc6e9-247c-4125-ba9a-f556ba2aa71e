/**
 * Admin Community Analytics Page
 *
 * Main page for comprehensive community analytics and insights.
 * Provides deep analytics into community health, engagement, and performance.
 *
 * Features:
 * - Real-time community health metrics and KPIs
 * - User engagement and activity pattern analysis
 * - Content performance tracking across all types
 * - Growth trends and predictive analytics
 * - Interactive data visualizations and charts
 * - Customizable time ranges and filtering
 * - Exportable reports and insights
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { CommunityAnalyticsDashboard } from '../../../../src/admin/components/analytics/CommunityAnalyticsDashboard';

export const metadata: Metadata = {
  title: 'Community Analytics | Syndicaps Admin',
  description: 'Comprehensive community analytics dashboard with engagement insights, performance metrics, and growth trends.',
  robots: 'noindex, nofollow'
};

/**
 * Admin Community Analytics Page Component
 * 
 * Protected admin route that requires community_analytics read permission.
 * Renders the comprehensive community analytics dashboard.
 */
export default function AdminCommunityAnalyticsPage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'community_analytics', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <CommunityAnalyticsDashboard />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
