'use client'

import React, { useState } from 'react'
import { 
  Layers, 
  Key, 
  Plus, 
  Edit, 
  Trash2, 
  Eye,
  EyeOff,
  Activity,
  Globe,
  Lock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  <PERSON>,
  <PERSON><PERSON>,
  RefreshCw
} from 'lucide-react'

interface Api<PERSON>ey {
  id: string
  name: string
  key: string
  permissions: string[]
  lastUsed: Date | null
  requests: number
  status: 'active' | 'inactive' | 'revoked'
  createdAt: Date
  expiresAt: Date | null
}

interface ApiEndpoint {
  id: string
  path: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  description: string
  status: 'active' | 'deprecated' | 'maintenance'
  requests24h: number
  averageResponseTime: number
  errorRate: number
}

export default function ApiManagementPage() {
  const [selectedTab, setSelectedTab] = useState<'keys' | 'endpoints' | 'analytics' | 'documentation'>('keys')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showKeys, setShowKeys] = useState(false)

  // Mock data - replace with actual data fetching
  const apiKeys: ApiKey[] = [
    {
      id: '1',
      name: 'Mobile App API Key',
      key: '********************************',
      permissions: ['read:users', 'write:orders', 'read:products'],
      lastUsed: new Date(Date.now() - 3600000),
      requests: 15420,
      status: 'active',
      createdAt: new Date(Date.now() - 2592000000),
      expiresAt: null
    },
    {
      id: '2',
      name: 'Analytics Dashboard',
      key: 'sk_test_xyz789uvw456rst123opq987',
      permissions: ['read:analytics', 'read:users'],
      lastUsed: new Date(Date.now() - 86400000),
      requests: 2341,
      status: 'active',
      createdAt: new Date(Date.now() - 1296000000),
      expiresAt: new Date(Date.now() + 7776000000)
    },
    {
      id: '3',
      name: 'Legacy Integration',
      key: 'sk_live_old123legacy456system789',
      permissions: ['read:products'],
      lastUsed: null,
      requests: 0,
      status: 'revoked',
      createdAt: new Date(Date.now() - 5184000000),
      expiresAt: null
    }
  ]

  const apiEndpoints: ApiEndpoint[] = [
    {
      id: '1',
      path: '/api/auth/login',
      method: 'POST',
      description: 'User authentication endpoint',
      status: 'active',
      requests24h: 1205,
      averageResponseTime: 245,
      errorRate: 0.8
    },
    {
      id: '2',
      path: '/api/users',
      method: 'GET',
      description: 'Retrieve user list with pagination',
      status: 'active',
      requests24h: 8934,
      averageResponseTime: 120,
      errorRate: 0.2
    },
    {
      id: '3',
      path: '/api/products/{id}',
      method: 'GET',
      description: 'Get product details by ID',
      status: 'active',
      requests24h: 15420,
      averageResponseTime: 95,
      errorRate: 1.2
    },
    {
      id: '4',
      path: '/api/orders',
      method: 'POST',
      description: 'Create new order',
      status: 'active',
      requests24h: 456,
      averageResponseTime: 380,
      errorRate: 2.1
    },
    {
      id: '5',
      path: '/api/v1/legacy/products',
      method: 'GET',
      description: 'Legacy product endpoint (deprecated)',
      status: 'deprecated',
      requests24h: 23,
      averageResponseTime: 890,
      errorRate: 12.5
    }
  ]

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800 border-green-200',
      inactive: 'bg-gray-100 text-gray-800 border-gray-200',
      revoked: 'bg-red-100 text-red-800 border-red-200',
      deprecated: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      maintenance: 'bg-orange-100 text-orange-800 border-orange-200'
    }
    return colors[status as keyof typeof colors] || colors.inactive
  }

  const getMethodColor = (method: string) => {
    const colors = {
      GET: 'bg-blue-900 text-blue-300',
      POST: 'bg-green-900 text-green-300',
      PUT: 'bg-yellow-900 text-yellow-300',
      DELETE: 'bg-red-900 text-red-300',
      PATCH: 'bg-purple-900 text-purple-300'
    }
    return colors[method as keyof typeof colors] || 'bg-gray-900 text-gray-300'
  }

  const maskApiKey = (key: string) => {
    if (!showKeys) {
      return `${key.substring(0, 8)}${'•'.repeat(20)}${key.substring(key.length - 4)}`
    }
    return key
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // Show success message
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">API Management</h1>
            <p className="text-gray-400">Manage API keys, endpoints, and monitor usage</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowKeys(!showKeys)}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              {showKeys ? <EyeOff size={18} /> : <Eye size={18} />}
              <span>{showKeys ? 'Hide Keys' : 'Show Keys'}</span>
            </button>
            <button 
              onClick={() => setShowCreateModal(true)}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              <Plus size={18} />
              <span>Create API Key</span>
            </button>
          </div>
        </div>
      </div>

      {/* API Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total API Keys</p>
              <p className="text-2xl font-bold text-blue-400">{apiKeys.length}</p>
            </div>
            <Key className="text-blue-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">{apiKeys.filter(k => k.status === 'active').length} active</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Requests Today</p>
              <p className="text-2xl font-bold text-green-400">26,355</p>
            </div>
            <Activity className="text-green-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">+12% from yesterday</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Response Time</p>
              <p className="text-2xl font-bold text-purple-400">195ms</p>
            </div>
            <Clock className="text-purple-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">-8ms from yesterday</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Error Rate</p>
              <p className="text-2xl font-bold text-yellow-400">1.4%</p>
            </div>
            <AlertTriangle className="text-yellow-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">+0.2% from yesterday</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'keys', label: 'API Keys', icon: Key },
              { id: 'endpoints', label: 'Endpoints', icon: Globe },
              { id: 'analytics', label: 'Analytics', icon: BarChart3 },
              { id: 'documentation', label: 'Documentation', icon: Layers }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* API Keys Tab */}
      {selectedTab === 'keys' && (
        <div className="bg-gray-800 rounded-lg">
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">API Keys</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-gray-700">
                <tr>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Name</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Key</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Status</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Requests</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Last Used</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {apiKeys.map((apiKey) => (
                  <tr key={apiKey.id} className="border-b border-gray-700 hover:bg-gray-750">
                    <td className="py-4 px-6">
                      <div>
                        <div className="text-white font-medium">{apiKey.name}</div>
                        <div className="text-gray-400 text-sm">
                          Created {apiKey.createdAt.toLocaleDateString()}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2">
                        <code className="text-gray-300 bg-gray-700 px-2 py-1 rounded text-sm font-mono">
                          {maskApiKey(apiKey.key)}
                        </code>
                        <button 
                          onClick={() => copyToClipboard(apiKey.key)}
                          className="text-gray-400 hover:text-white p-1"
                          title="Copy to clipboard"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(apiKey.status)}`}>
                        {apiKey.status === 'active' && <CheckCircle size={12} className="mr-1" />}
                        {apiKey.status === 'revoked' && <XCircle size={12} className="mr-1" />}
                        {apiKey.status.charAt(0).toUpperCase() + apiKey.status.slice(1)}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-gray-300">
                      {apiKey.requests.toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-gray-300">
                      {apiKey.lastUsed ? apiKey.lastUsed.toLocaleDateString() : 'Never'}
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2">
                        <button className="text-blue-400 hover:text-blue-300 p-1" title="View details">
                          <Eye size={16} />
                        </button>
                        <button className="text-yellow-400 hover:text-yellow-300 p-1" title="Edit">
                          <Edit size={16} />
                        </button>
                        <button className="text-green-400 hover:text-green-300 p-1" title="Regenerate">
                          <RefreshCw size={16} />
                        </button>
                        <button className="text-red-400 hover:text-red-300 p-1" title="Revoke">
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* API Endpoints Tab */}
      {selectedTab === 'endpoints' && (
        <div className="bg-gray-800 rounded-lg">
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">API Endpoints</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-gray-700">
                <tr>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Endpoint</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Method</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Status</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Requests (24h)</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Avg Response</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Error Rate</th>
                </tr>
              </thead>
              <tbody>
                {apiEndpoints.map((endpoint) => (
                  <tr key={endpoint.id} className="border-b border-gray-700 hover:bg-gray-750">
                    <td className="py-4 px-6">
                      <div>
                        <code className="text-white font-medium">{endpoint.path}</code>
                        <div className="text-gray-400 text-sm">{endpoint.description}</div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getMethodColor(endpoint.method)}`}>
                        {endpoint.method}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(endpoint.status)}`}>
                        {endpoint.status.charAt(0).toUpperCase() + endpoint.status.slice(1)}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-gray-300">
                      {endpoint.requests24h.toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-gray-300">
                      {endpoint.averageResponseTime}ms
                    </td>
                    <td className="py-4 px-6">
                      <span className={`text-sm font-medium ${
                        endpoint.errorRate > 5 ? 'text-red-400' :
                        endpoint.errorRate > 2 ? 'text-yellow-400' :
                        'text-green-400'
                      }`}>
                        {endpoint.errorRate}%
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {selectedTab === 'analytics' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <BarChart3 className="mr-2" size={20} />
              Request Volume (Last 7 Days)
            </h3>
            <div className="h-64 flex items-end justify-between space-x-2">
              {[18000, 22000, 19000, 25000, 23000, 26000, 24000].map((value, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div 
                    className="bg-red-600 w-8 rounded-t"
                    style={{ height: `${(value / 26000) * 200}px` }}
                  ></div>
                  <span className="text-xs text-gray-400 mt-2">
                    {new Date(Date.now() - (6 - index) * 86400000).toLocaleDateString('en', { weekday: 'short' })}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Top Endpoints by Usage</h3>
            <div className="space-y-3">
              {apiEndpoints
                .sort((a, b) => b.requests24h - a.requests24h)
                .slice(0, 5)
                .map((endpoint) => (
                  <div key={endpoint.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getMethodColor(endpoint.method)}`}>
                        {endpoint.method}
                      </span>
                      <code className="text-white text-sm">{endpoint.path}</code>
                    </div>
                    <span className="text-gray-300 font-medium">
                      {endpoint.requests24h.toLocaleString()}
                    </span>
                  </div>
                ))}
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 lg:col-span-2">
            <h3 className="text-lg font-semibold text-white mb-4">API Key Usage</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {apiKeys
                .filter(key => key.status === 'active')
                .map((key) => (
                  <div key={key.id} className="p-4 bg-gray-700 rounded-lg">
                    <h4 className="text-white font-medium mb-2">{key.name}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Requests:</span>
                        <span className="text-white">{key.requests.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Last used:</span>
                        <span className="text-white">
                          {key.lastUsed ? key.lastUsed.toLocaleDateString() : 'Never'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Permissions:</span>
                        <span className="text-white">{key.permissions.length}</span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}

      {/* Documentation Tab */}
      {selectedTab === 'documentation' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">API Documentation</h3>
            <div className="prose prose-invert max-w-none">
              <h4 className="text-white">Authentication</h4>
              <p className="text-gray-300">
                All API requests must include an API key in the Authorization header:
              </p>
              <pre className="bg-gray-900 p-4 rounded text-sm">
                <code className="text-green-400">Authorization: Bearer YOUR_API_KEY</code>
              </pre>
              
              <h4 className="text-white mt-6">Rate Limiting</h4>
              <p className="text-gray-300">
                API requests are limited to 1000 requests per minute per API key.
                Rate limit information is included in response headers.
              </p>
              
              <h4 className="text-white mt-6">Response Format</h4>
              <p className="text-gray-300">
                All responses are returned as JSON with the following structure:
              </p>
              <pre className="bg-gray-900 p-4 rounded text-sm">
                <code className="text-blue-400">{`{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}`}</code>
              </pre>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Links</h3>
              <div className="space-y-3">
                <a href="#" className="block text-blue-400 hover:text-blue-300">
                  Getting Started Guide
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300">
                  API Reference
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300">
                  Code Examples
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300">
                  SDKs & Libraries
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300">
                  Changelog
                </a>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Support</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-gray-400">Documentation:</span>
                  <a href="#" className="text-blue-400 hover:text-blue-300 ml-2">
                    docs.syndicaps.com
                  </a>
                </div>
                <div>
                  <span className="text-gray-400">Support Email:</span>
                  <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 ml-2">
                    <EMAIL>
                  </a>
                </div>
                <div>
                  <span className="text-gray-400">Status Page:</span>
                  <a href="#" className="text-blue-400 hover:text-blue-300 ml-2">
                    status.syndicaps.com
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create API Key Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Create New API Key</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Name</label>
                <input 
                  type="text" 
                  placeholder="e.g., Mobile App Integration"
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Permissions</label>
                <div className="space-y-2">
                  {['read:users', 'write:users', 'read:products', 'write:products', 'read:orders', 'write:orders'].map((permission) => (
                    <label key={permission} className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" />
                      <span className="text-gray-300 text-sm">{permission}</span>
                    </label>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Expires</label>
                <select className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none">
                  <option>Never</option>
                  <option>30 days</option>
                  <option>90 days</option>
                  <option>1 year</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                Create API Key
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}