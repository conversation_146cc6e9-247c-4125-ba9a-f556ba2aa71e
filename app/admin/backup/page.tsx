'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Shield, 
  Download, 
  Upload, 
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Play,
  Pause,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2,
  Database,
  HardDrive,
  Cloud,
  Server,
  Archive,
  FileText,
  Calendar,
  Zap,
  Activity,
  BarChart3,
  Filter,
  Search
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface BackupJob {
  id: string
  name: string
  description: string
  type: 'full' | 'incremental' | 'differential'
  status: 'scheduled' | 'running' | 'completed' | 'failed' | 'paused'
  schedule: {
    frequency: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly'
    time?: string
    days?: string[]
    timezone: string
  }
  sources: BackupSource[]
  destination: BackupDestination
  retention: {
    policy: 'time_based' | 'count_based' | 'size_based'
    value: number
    unit: 'days' | 'weeks' | 'months' | 'count' | 'gb'
  }
  encryption: {
    enabled: boolean
    algorithm?: string
    keyId?: string
  }
  compression: {
    enabled: boolean
    algorithm?: string
    level?: number
  }
  metrics: {
    totalBackups: number
    successfulBackups: number
    failedBackups: number
    lastBackupSize: number
    totalSize: number
    avgDuration: number
    lastRun?: Date
    nextRun?: Date
  }
  notifications: {
    onSuccess: boolean
    onFailure: boolean
    recipients: string[]
    channels: ('email' | 'slack' | 'webhook')[]
  }
  createdAt: Date
  createdBy: string
}

interface BackupSource {
  id: string
  type: 'database' | 'files' | 'application_data' | 'configuration'
  name: string
  path: string
  includePatterns: string[]
  excludePatterns: string[]
  isActive: boolean
}

interface BackupDestination {
  id: string
  type: 'local' | 's3' | 'gcs' | 'azure' | 'ftp' | 'sftp'
  name: string
  config: Record<string, any>
  isActive: boolean
}

interface BackupRecord {
  id: string
  jobId: string
  type: 'full' | 'incremental' | 'differential'
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  duration?: number
  size: number
  filesCount: number
  errorCount: number
  warnings: string[]
  checksum: string
  location: string
  metadata: Record<string, any>
}

interface RestoreJob {
  id: string
  name: string
  description: string
  backupRecordId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  restoreType: 'full' | 'selective'
  targetLocation: string
  selectedItems?: string[]
  overwriteExisting: boolean
  startTime: Date
  endTime?: Date
  duration?: number
  restoredFiles: number
  errorCount: number
  createdBy: string
}

interface DisasterRecoveryPlan {
  id: string
  name: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  rto: number // Recovery Time Objective in minutes
  rpo: number // Recovery Point Objective in minutes
  triggers: string[]
  steps: RecoveryStep[]
  contacts: {
    primary: string[]
    secondary: string[]
    escalation: string[]
  }
  lastTested?: Date
  testResults?: {
    success: boolean
    duration: number
    issues: string[]
  }
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

interface RecoveryStep {
  id: string
  order: number
  title: string
  description: string
  type: 'manual' | 'automated'
  estimatedDuration: number
  dependencies: string[]
  automation?: {
    script: string
    parameters: Record<string, any>
  }
}

export default function BackupRecoveryPage() {
  const [backupJobs, setBackupJobs] = useState<BackupJob[]>([])
  const [backupRecords, setBackupRecords] = useState<BackupRecord[]>([])
  const [restoreJobs, setRestoreJobs] = useState<RestoreJob[]>([])
  const [drPlans, setDrPlans] = useState<DisasterRecoveryPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'backups' | 'restores' | 'disaster-recovery' | 'monitoring'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')

  useEffect(() => {
    loadBackupData()
  }, [])

  const loadBackupData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual backup system API integration
      const mockBackupJobs: BackupJob[] = [
        {
          id: 'job_001',
          name: 'Daily Database Backup',
          description: 'Complete database backup including user data, orders, and configurations',
          type: 'full',
          status: 'completed',
          schedule: {
            frequency: 'daily',
            time: '02:00',
            timezone: 'UTC'
          },
          sources: [
            {
              id: 'src_001',
              type: 'database',
              name: 'Primary Database',
              path: 'postgresql://localhost:5432/syndicaps',
              includePatterns: ['*'],
              excludePatterns: ['temp_*', 'cache_*'],
              isActive: true
            }
          ],
          destination: {
            id: 'dest_001',
            type: 's3',
            name: 'AWS S3 Backup Bucket',
            config: {
              bucket: 'syndicaps-backups',
              region: 'us-east-1',
              encryption: 'AES256'
            },
            isActive: true
          },
          retention: {
            policy: 'time_based',
            value: 30,
            unit: 'days'
          },
          encryption: {
            enabled: true,
            algorithm: 'AES-256',
            keyId: 'backup-key-001'
          },
          compression: {
            enabled: true,
            algorithm: 'gzip',
            level: 6
          },
          metrics: {
            totalBackups: 45,
            successfulBackups: 44,
            failedBackups: 1,
            lastBackupSize: 2.4, // GB
            totalSize: 108.6, // GB
            avgDuration: 1800, // 30 minutes
            lastRun: new Date(Date.now() - 6 * 60 * 60 * 1000),
            nextRun: new Date(Date.now() + 18 * 60 * 60 * 1000)
          },
          notifications: {
            onSuccess: false,
            onFailure: true,
            recipients: ['<EMAIL>', '<EMAIL>'],
            channels: ['email', 'slack']
          },
          createdAt: new Date('2024-12-01'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'job_002',
          name: 'Application Files Backup',
          description: 'Backup of application files, configurations, and static assets',
          type: 'incremental',
          status: 'running',
          schedule: {
            frequency: 'hourly',
            timezone: 'UTC'
          },
          sources: [
            {
              id: 'src_002',
              type: 'files',
              name: 'Application Directory',
              path: '/var/www/syndicaps',
              includePatterns: ['**/*'],
              excludePatterns: ['node_modules/**', 'logs/**', '*.tmp'],
              isActive: true
            }
          ],
          destination: {
            id: 'dest_002',
            type: 'gcs',
            name: 'Google Cloud Storage',
            config: {
              bucket: 'syndicaps-app-backups',
              project: 'syndicaps-prod'
            },
            isActive: true
          },
          retention: {
            policy: 'count_based',
            value: 168, // 7 days * 24 hours
            unit: 'count'
          },
          encryption: {
            enabled: true,
            algorithm: 'AES-256'
          },
          compression: {
            enabled: true,
            algorithm: 'lz4',
            level: 3
          },
          metrics: {
            totalBackups: 1680,
            successfulBackups: 1675,
            failedBackups: 5,
            lastBackupSize: 0.15, // GB
            totalSize: 252.0, // GB
            avgDuration: 180, // 3 minutes
            lastRun: new Date(Date.now() - 30 * 60 * 1000),
            nextRun: new Date(Date.now() + 30 * 60 * 1000)
          },
          notifications: {
            onSuccess: false,
            onFailure: true,
            recipients: ['<EMAIL>'],
            channels: ['email']
          },
          createdAt: new Date('2024-12-01'),
          createdBy: '<EMAIL>'
        }
      ]

      const mockBackupRecords: BackupRecord[] = [
        {
          id: 'record_001',
          jobId: 'job_001',
          type: 'full',
          status: 'completed',
          startTime: new Date(Date.now() - 6 * 60 * 60 * 1000),
          endTime: new Date(Date.now() - 6 * 60 * 60 * 1000 + 30 * 60 * 1000),
          duration: 1800,
          size: 2.4,
          filesCount: 15420,
          errorCount: 0,
          warnings: [],
          checksum: 'sha256:a1b2c3d4e5f6...',
          location: 's3://syndicaps-backups/daily/2025-01-14/database-full.tar.gz',
          metadata: {
            compression_ratio: 0.65,
            encryption_verified: true
          }
        },
        {
          id: 'record_002',
          jobId: 'job_002',
          type: 'incremental',
          status: 'completed',
          startTime: new Date(Date.now() - 30 * 60 * 1000),
          endTime: new Date(Date.now() - 27 * 60 * 1000),
          duration: 180,
          size: 0.15,
          filesCount: 234,
          errorCount: 0,
          warnings: ['Skipped 3 locked files'],
          checksum: 'sha256:f6e5d4c3b2a1...',
          location: 'gcs://syndicaps-app-backups/hourly/2025-01-14-14/app-incremental.tar.lz4',
          metadata: {
            changed_files: 234,
            new_files: 12,
            deleted_files: 5
          }
        }
      ]

      const mockRestoreJobs: RestoreJob[] = [
        {
          id: 'restore_001',
          name: 'Emergency Database Restore',
          description: 'Restore database from backup after corruption incident',
          backupRecordId: 'record_001',
          status: 'completed',
          restoreType: 'full',
          targetLocation: '/var/lib/postgresql/restore',
          overwriteExisting: true,
          startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000),
          duration: 2700,
          restoredFiles: 15420,
          errorCount: 0,
          createdBy: '<EMAIL>'
        }
      ]

      const mockDrPlans: DisasterRecoveryPlan[] = [
        {
          id: 'dr_001',
          name: 'Database Failure Recovery',
          description: 'Complete recovery plan for primary database failure',
          priority: 'critical',
          rto: 60, // 1 hour
          rpo: 15, // 15 minutes
          triggers: ['database_unavailable', 'data_corruption', 'hardware_failure'],
          steps: [
            {
              id: 'step_001',
              order: 1,
              title: 'Assess Damage',
              description: 'Evaluate the extent of database damage and determine recovery approach',
              type: 'manual',
              estimatedDuration: 15,
              dependencies: []
            },
            {
              id: 'step_002',
              order: 2,
              title: 'Activate Backup Database',
              description: 'Switch traffic to backup database instance',
              type: 'automated',
              estimatedDuration: 5,
              dependencies: ['step_001'],
              automation: {
                script: 'activate_backup_db.sh',
                parameters: { instance: 'backup-db-01' }
              }
            },
            {
              id: 'step_003',
              order: 3,
              title: 'Restore from Latest Backup',
              description: 'Restore primary database from most recent backup',
              type: 'automated',
              estimatedDuration: 30,
              dependencies: ['step_002'],
              automation: {
                script: 'restore_database.sh',
                parameters: { backup_id: 'latest' }
              }
            }
          ],
          contacts: {
            primary: ['<EMAIL>', '<EMAIL>'],
            secondary: ['<EMAIL>'],
            escalation: ['<EMAIL>']
          },
          lastTested: new Date('2024-12-15'),
          testResults: {
            success: true,
            duration: 45,
            issues: ['Network latency higher than expected']
          },
          isActive: true,
          createdAt: new Date('2024-11-01'),
          updatedAt: new Date('2024-12-15')
        }
      ]

      setBackupJobs(mockBackupJobs)
      setBackupRecords(mockBackupRecords)
      setRestoreJobs(mockRestoreJobs)
      setDrPlans(mockDrPlans)
    } catch (error) {
      console.error('Error loading backup data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredBackupJobs = backupJobs.filter(job => {
    const matchesSearch = job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' || job.status === filterStatus
    const matchesType = filterType === 'all' || job.type === filterType
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-900/20'
      case 'running': return 'text-blue-400 bg-blue-900/20'
      case 'scheduled': return 'text-yellow-400 bg-yellow-900/20'
      case 'failed': return 'text-red-400 bg-red-900/20'
      case 'paused': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'full': return 'text-purple-400 bg-purple-900/20'
      case 'incremental': return 'text-blue-400 bg-blue-900/20'
      case 'differential': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatSize = (sizeGB: number) => {
    if (sizeGB < 1) return `${(sizeGB * 1024).toFixed(0)} MB`
    if (sizeGB < 1024) return `${sizeGB.toFixed(1)} GB`
    return `${(sizeGB / 1024).toFixed(1)} TB`
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) return `${hours}h ${minutes}m`
    if (minutes > 0) return `${minutes}m ${secs}s`
    return `${secs}s`
  }

  const calculateSuccessRate = (job: BackupJob) => {
    if (job.metrics.totalBackups === 0) return 0
    return (job.metrics.successfulBackups / job.metrics.totalBackups) * 100
  }

  const runBackupJob = async (jobId: string) => {
    setBackupJobs(prev => prev.map(job => 
      job.id === jobId 
        ? { ...job, status: 'running' }
        : job
    ))
    // Simulate backup execution
    setTimeout(() => {
      setBackupJobs(prev => prev.map(job => 
        job.id === jobId 
          ? { ...job, status: 'completed', metrics: { ...job.metrics, lastRun: new Date() } }
          : job
      ))
    }, 3000)
  }

  const pauseBackupJob = async (jobId: string) => {
    setBackupJobs(prev => prev.map(job => 
      job.id === jobId 
        ? { ...job, status: job.status === 'paused' ? 'scheduled' : 'paused' }
        : job
    ))
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Shield className="w-8 h-8 text-green-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Automated Backup & Recovery</h1>
            <p className="text-gray-400">Disaster recovery automation, backup management, and system restoration</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadBackupData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/backup/jobs/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Backup Job
          </Link>
        </div>
      </div>

      {/* Backup System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Backup Jobs</p>
              <p className="text-2xl font-bold text-white">
                {backupJobs.filter(j => j.status !== 'paused').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {backupJobs.filter(j => j.status === 'running').length} running
              </p>
            </div>
            <Archive className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Backup Size</p>
              <p className="text-2xl font-bold text-white">
                {formatSize(backupJobs.reduce((sum, j) => sum + j.metrics.totalSize, 0))}
              </p>
              <p className="text-xs text-blue-400 mt-1">Across all jobs</p>
            </div>
            <HardDrive className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-2xl font-bold text-white">
                {backupJobs.length > 0
                  ? (backupJobs.reduce((sum, j) => sum + calculateSuccessRate(j), 0) / backupJobs.length).toFixed(1)
                  : 0}%
              </p>
              <p className="text-xs text-green-400 mt-1">Last 30 days</p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">DR Plans</p>
              <p className="text-2xl font-bold text-white">
                {drPlans.filter(p => p.isActive).length}
              </p>
              <p className="text-xs text-yellow-400 mt-1">
                {drPlans.filter(p => p.priority === 'critical').length} critical
              </p>
            </div>
            <Shield className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'backups', label: 'Backup Jobs', icon: Archive, count: backupJobs.length },
            { id: 'restores', label: 'Restore Jobs', icon: RefreshCw, count: restoreJobs.length },
            { id: 'disaster-recovery', label: 'Disaster Recovery', icon: Shield, count: drPlans.length },
            { id: 'monitoring', label: 'Monitoring', icon: Activity }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Recent Backup Activity */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent Backup Activity</h3>
            <div className="space-y-3">
              {backupRecords.slice(0, 5).map((record) => {
                const job = backupJobs.find(j => j.id === record.jobId)
                return (
                  <div key={record.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        record.status === 'completed' ? 'bg-green-400' :
                        record.status === 'running' ? 'bg-blue-400' :
                        record.status === 'failed' ? 'bg-red-400' : 'bg-gray-400'
                      }`}></div>
                      <div>
                        <p className="text-sm font-medium text-white">{job?.name || 'Unknown Job'}</p>
                        <p className="text-xs text-gray-400">
                          {record.type} backup • {record.startTime.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-white">{formatSize(record.size)}</p>
                      <p className="text-xs text-gray-400">
                        {record.duration ? formatDuration(record.duration) : 'In progress'}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Backup Jobs Status */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Backup Jobs Status</h3>
              <div className="space-y-3">
                {backupJobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${getTypeColor(job.type)}`}>
                        <Archive size={16} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{job.name}</p>
                        <p className="text-xs text-gray-400">
                          {job.schedule.frequency} • {job.type}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(job.status)}`}>
                        {job.status}
                      </span>
                      <div className="flex space-x-1">
                        <button
                          onClick={() => runBackupJob(job.id)}
                          className="bg-green-600 hover:bg-green-700 text-white p-1 rounded"
                          title="Run Now"
                        >
                          <Play size={12} />
                        </button>
                        <button
                          onClick={() => pauseBackupJob(job.id)}
                          className="bg-yellow-600 hover:bg-yellow-700 text-white p-1 rounded"
                          title={job.status === 'paused' ? 'Resume' : 'Pause'}
                        >
                          {job.status === 'paused' ? <Play size={12} /> : <Pause size={12} />}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Disaster Recovery Plans</h3>
              <div className="space-y-3">
                {drPlans.map((plan) => (
                  <div key={plan.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${getPriorityColor(plan.priority)}`}>
                        <Shield size={16} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{plan.name}</p>
                        <p className="text-xs text-gray-400">
                          RTO: {plan.rto}m • RPO: {plan.rpo}m
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(plan.priority)}`}>
                        {plan.priority}
                      </span>
                      {plan.lastTested && (
                        <span className="text-xs text-gray-400">
                          Tested {plan.lastTested.toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'backups' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search backup jobs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="running">Running</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="paused">Paused</option>
                </select>

                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Types</option>
                  <option value="full">Full</option>
                  <option value="incremental">Incremental</option>
                  <option value="differential">Differential</option>
                </select>
              </div>
            </div>
          </div>

          {/* Backup Jobs List */}
          <div className="space-y-4">
            {filteredBackupJobs.map((job) => (
              <motion.div
                key={job.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{job.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(job.status)}`}>
                        {job.status}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(job.type)}`}>
                        {job.type}
                      </span>
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{job.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Schedule:</span>
                        <span className="text-white ml-1">{job.schedule.frequency}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Success Rate:</span>
                        <span className="text-white ml-1">{calculateSuccessRate(job).toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Total Size:</span>
                        <span className="text-white ml-1">{formatSize(job.metrics.totalSize)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Avg Duration:</span>
                        <span className="text-white ml-1">{formatDuration(job.metrics.avgDuration)}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Created: {job.createdAt.toLocaleDateString()}</span>
                      {job.metrics.lastRun && (
                        <span>Last run: {job.metrics.lastRun.toLocaleString()}</span>
                      )}
                      {job.metrics.nextRun && (
                        <span>Next run: {job.metrics.nextRun.toLocaleString()}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => runBackupJob(job.id)}
                      className="bg-green-600 hover:bg-green-700 text-white p-2 rounded"
                      title="Run Now"
                    >
                      <Play size={16} />
                    </button>
                    <button
                      onClick={() => pauseBackupJob(job.id)}
                      className="bg-yellow-600 hover:bg-yellow-700 text-white p-2 rounded"
                      title={job.status === 'paused' ? 'Resume' : 'Pause'}
                    >
                      {job.status === 'paused' ? <Play size={16} /> : <Pause size={16} />}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'restores' || activeTab === 'disaster-recovery' || activeTab === 'monitoring') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'restores' && 'Restore Job Management'}
            {activeTab === 'disaster-recovery' && 'Disaster Recovery Planning'}
            {activeTab === 'monitoring' && 'Backup System Monitoring'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'restores' && <RefreshCw className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'disaster-recovery' && <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'monitoring' && <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'restores' && 'Advanced Restore Management'}
              {activeTab === 'disaster-recovery' && 'Comprehensive DR Planning'}
              {activeTab === 'monitoring' && 'Real-time Backup Monitoring'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'restores' && 'Manage restore operations with selective recovery, point-in-time restoration, and validation testing.'}
              {activeTab === 'disaster-recovery' && 'Create and manage disaster recovery plans with automated failover, testing schedules, and compliance reporting.'}
              {activeTab === 'monitoring' && 'Monitor backup system health, performance metrics, and storage utilization with real-time alerts.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
