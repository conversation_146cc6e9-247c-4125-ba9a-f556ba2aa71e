'use client'

import React, { useState } from 'react'
import { 
  RefreshCw, 
  Download, 
  Upload, 
  Database, 
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Play,
  Pause,
  Settings,
  Archive,
  HardDrive,
  Shield
} from 'lucide-react'

interface BackupRecord {
  id: string
  name: string
  type: 'full' | 'incremental' | 'differential'
  status: 'completed' | 'running' | 'failed' | 'scheduled'
  size: string
  duration: number
  createdAt: Date
  location: string
  checksum: string
}

export default function BackupManagementPage() {
  const [selectedTab, setSelectedTab] = useState<'backups' | 'schedule' | 'restore' | 'settings'>('backups')
  const [isRunningBackup, setIsRunningBackup] = useState(false)

  // Mock backup data - replace with actual data fetching
  const backupRecords: BackupRecord[] = [
    {
      id: '1',
      name: 'Daily_Full_Backup_2025-01-14',
      type: 'full',
      status: 'completed',
      size: '2.4 GB',
      duration: 1847000,
      createdAt: new Date(Date.now() - 86400000),
      location: 's3://syndicaps-backups/daily/',
      checksum: 'sha256:abc123def456'
    },
    {
      id: '2', 
      name: 'Incremental_Backup_2025-01-14_12',
      type: 'incremental',
      status: 'completed',
      size: '156 MB',
      duration: 342000,
      createdAt: new Date(Date.now() - 43200000),
      location: 's3://syndicaps-backups/incremental/',
      checksum: 'sha256:def456ghi789'
    },
    {
      id: '3',
      name: 'Full_Backup_2025-01-13',
      type: 'full', 
      status: 'completed',
      size: '2.3 GB',
      duration: 1923000,
      createdAt: new Date(Date.now() - 172800000),
      location: 's3://syndicaps-backups/daily/',
      checksum: 'sha256:ghi789jkl012'
    },
    {
      id: '4',
      name: 'Weekly_Backup_2025-01-12',
      type: 'full',
      status: 'failed',
      size: '0 B',
      duration: 0,
      createdAt: new Date(Date.now() - 259200000),
      location: '',
      checksum: ''
    }
  ]

  const getStatusIcon = (status: string) => {
    const icons = {
      completed: CheckCircle,
      running: RefreshCw,
      failed: XCircle,
      scheduled: Clock
    }
    return icons[status as keyof typeof icons] || Clock
  }

  const getStatusColor = (status: string) => {
    const colors = {
      completed: 'text-green-400',
      running: 'text-blue-400', 
      failed: 'text-red-400',
      scheduled: 'text-yellow-400'
    }
    return colors[status as keyof typeof colors] || 'text-gray-400'
  }

  const getTypeColor = (type: string) => {
    const colors = {
      full: 'bg-blue-900 text-blue-300',
      incremental: 'bg-green-900 text-green-300',
      differential: 'bg-purple-900 text-purple-300'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-900 text-gray-300'
  }

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }

  const startBackup = (type: 'full' | 'incremental') => {
    setIsRunningBackup(true)
    // Simulate backup process
    setTimeout(() => {
      setIsRunningBackup(false)
    }, 5000)
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Backup Management</h1>
            <p className="text-gray-400">Manage database backups, schedules, and restoration</p>
          </div>
          <div className="flex space-x-3">
            <button 
              onClick={() => startBackup('incremental')}
              disabled={isRunningBackup}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <RefreshCw size={18} className={isRunningBackup ? 'animate-spin' : ''} />
              <span>Incremental Backup</span>
            </button>
            <button 
              onClick={() => startBackup('full')}
              disabled={isRunningBackup}
              className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <Database size={18} />
              <span>Full Backup</span>
            </button>
          </div>
        </div>
      </div>

      {/* Backup Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Last Backup</p>
              <p className="text-xl font-bold text-green-400">14 hours ago</p>
            </div>
            <CheckCircle className="text-green-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Daily full backup completed</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Size</p>
              <p className="text-xl font-bold text-blue-400">15.2 GB</p>
            </div>
            <HardDrive className="text-blue-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">All backup storage</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-xl font-bold text-purple-400">98.5%</p>
            </div>
            <Shield className="text-purple-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Last 30 days</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Next Backup</p>
              <p className="text-xl font-bold text-yellow-400">6 hours</p>
            </div>
            <Clock className="text-yellow-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Scheduled daily backup</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'backups', label: 'Backup History', icon: Archive },
              { id: 'schedule', label: 'Backup Schedule', icon: Calendar },
              { id: 'restore', label: 'Restore Database', icon: Upload },
              { id: 'settings', label: 'Backup Settings', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Backup History Tab */}
      {selectedTab === 'backups' && (
        <div className="bg-gray-800 rounded-lg">
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Backup History</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-gray-700">
                <tr>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Backup Name</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Type</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Status</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Size</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Duration</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Created</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {backupRecords.map((backup) => {
                  const StatusIcon = getStatusIcon(backup.status)
                  return (
                    <tr key={backup.id} className="border-b border-gray-700 hover:bg-gray-750">
                      <td className="py-4 px-6">
                        <div>
                          <div className="text-white font-medium">{backup.name}</div>
                          {backup.location && (
                            <div className="text-gray-400 text-sm">{backup.location}</div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(backup.type)}`}>
                          {backup.type.charAt(0).toUpperCase() + backup.type.slice(1)}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <StatusIcon className={`${getStatusColor(backup.status)} ${backup.status === 'running' ? 'animate-spin' : ''}`} size={16} />
                          <span className="text-gray-300 capitalize">{backup.status}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-gray-300">{backup.size}</td>
                      <td className="py-4 px-6 text-gray-300">
                        {backup.duration > 0 ? formatDuration(backup.duration) : '-'}
                      </td>
                      <td className="py-4 px-6 text-gray-300">
                        {backup.createdAt.toLocaleDateString()} {backup.createdAt.toLocaleTimeString()}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          {backup.status === 'completed' && (
                            <>
                              <button className="text-blue-400 hover:text-blue-300 p-1" title="Download">
                                <Download size={16} />
                              </button>
                              <button className="text-green-400 hover:text-green-300 p-1" title="Restore">
                                <Upload size={16} />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Backup Schedule Tab */}
      {selectedTab === 'schedule' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Calendar className="mr-2" size={20} />
              Automated Schedules
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                  <h4 className="text-white font-medium">Daily Full Backup</h4>
                  <p className="text-gray-400 text-sm">Every day at 2:00 AM</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 text-sm">Enabled</span>
                  <button className="text-gray-400 hover:text-white">
                    <Settings size={16} />
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                  <h4 className="text-white font-medium">Hourly Incremental</h4>
                  <p className="text-gray-400 text-sm">Every hour during business hours</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 text-sm">Enabled</span>
                  <button className="text-gray-400 hover:text-white">
                    <Settings size={16} />
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                  <h4 className="text-white font-medium">Weekly Archive</h4>
                  <p className="text-gray-400 text-sm">Every Sunday at 1:00 AM</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-yellow-400 text-sm">Disabled</span>
                  <button className="text-gray-400 hover:text-white">
                    <Settings size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Clock className="mr-2" size={20} />
              Upcoming Backups
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border border-gray-700 rounded">
                <div>
                  <span className="text-white font-medium">Incremental Backup</span>
                  <p className="text-gray-400 text-sm">Next run in 2 hours</p>
                </div>
                <span className="text-blue-400 text-sm">14:00</span>
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-700 rounded">
                <div>
                  <span className="text-white font-medium">Daily Full Backup</span>
                  <p className="text-gray-400 text-sm">Next run in 6 hours</p>
                </div>
                <span className="text-blue-400 text-sm">02:00</span>
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-700 rounded">
                <div>
                  <span className="text-white font-medium">Weekly Archive</span>
                  <p className="text-gray-400 text-sm">Next run in 3 days</p>
                </div>
                <span className="text-gray-400 text-sm">Disabled</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Restore Database Tab */}
      {selectedTab === 'restore' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-white mb-6 text-center flex items-center justify-center">
              <Upload className="mr-2" size={20} />
              Database Restoration
            </h3>
            
            <div className="bg-red-900 border border-red-700 rounded-lg p-4 mb-6">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="text-red-400 mt-0.5" size={20} />
                <div>
                  <h4 className="text-red-300 font-medium">Warning: Critical Operation</h4>
                  <p className="text-red-200 text-sm mt-1">
                    Database restoration will overwrite all current data. This action cannot be undone.
                    Ensure you have a recent backup before proceeding.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Select Backup to Restore
                </label>
                <select className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none">
                  <option value="">Choose a backup...</option>
                  {backupRecords
                    .filter(backup => backup.status === 'completed')
                    .map(backup => (
                      <option key={backup.id} value={backup.id}>
                        {backup.name} - {backup.size} ({backup.createdAt.toLocaleDateString()})
                      </option>
                    ))}
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="text-white font-medium">Restoration Options</h4>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" defaultChecked />
                    <span className="text-gray-300">Verify backup integrity</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" />
                    <span className="text-gray-300">Create backup before restore</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" />
                    <span className="text-gray-300">Restore user data only</span>
                  </label>
                </div>

                <div className="space-y-3">
                  <h4 className="text-white font-medium">Verification Required</h4>
                  <input 
                    type="text" 
                    placeholder="Type 'RESTORE' to confirm"
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                  <p className="text-gray-400 text-xs">
                    Type RESTORE in all caps to enable the restoration button
                  </p>
                </div>
              </div>

              <div className="flex justify-center space-x-4 pt-4">
                <button className="px-6 py-2 text-gray-400 hover:text-white transition-colors">
                  Cancel
                </button>
                <button 
                  disabled
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <Upload size={18} />
                  <span>Restore Database</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backup Settings Tab */}
      {selectedTab === 'settings' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Storage Configuration</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Storage Provider</label>
                <select className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none">
                  <option>Amazon S3</option>
                  <option>Google Cloud Storage</option>
                  <option>Azure Blob Storage</option>
                  <option>Local Storage</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Bucket/Container</label>
                <input 
                  type="text" 
                  defaultValue="syndicaps-backups"
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Retention Period</label>
                <select className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none">
                  <option>30 days</option>
                  <option>60 days</option>
                  <option>90 days</option>
                  <option>1 year</option>
                  <option>Forever</option>
                </select>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Backup Options</h3>
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" defaultChecked />
                <span className="text-gray-300">Compress backups</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" defaultChecked />
                <span className="text-gray-300">Encrypt backups</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" />
                <span className="text-gray-300">Verify backup integrity</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" defaultChecked />
                <span className="text-gray-300">Send email notifications</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="rounded text-red-600 focus:ring-red-500" />
                <span className="text-gray-300">Cleanup old backups automatically</span>
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}