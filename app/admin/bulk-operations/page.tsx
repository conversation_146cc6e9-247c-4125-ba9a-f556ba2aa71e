'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Layers, 
  Play, 
  Pause, 
  Square, 
  RefreshCw, 
  Download,
  Upload,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Users,
  Package,
  ShoppingCart,
  FileText,
  Settings,
  BarChart3,
  Zap
} from 'lucide-react'
import BackButton from '../../../src/admin/components/common/BackButton'

interface BulkOperation {
  id: string
  name: string
  type: 'user_update' | 'product_update' | 'order_update' | 'email_campaign' | 'data_export' | 'data_import'
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled'
  entityType: 'users' | 'products' | 'orders' | 'reviews' | 'tickets'
  totalItems: number
  processedItems: number
  successCount: number
  errorCount: number
  startedAt?: Date
  completedAt?: Date
  estimatedCompletion?: Date
  createdBy: string
  description: string
  parameters: Record<string, any>
  errors: BulkOperationError[]
}

interface BulkOperationError {
  itemId: string
  error: string
  details?: string
  timestamp: Date
}

interface BulkOperationTemplate {
  id: string
  name: string
  description: string
  type: string
  entityType: string
  parameters: Record<string, any>
  isSystem: boolean
}

export default function BulkOperationsPage() {
  const [operations, setOperations] = useState<BulkOperation[]>([])
  const [templates, setTemplates] = useState<BulkOperationTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedOperation, setSelectedOperation] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'active' | 'completed' | 'templates' | 'create'>('active')
  const [showCreateOperation, setShowCreateOperation] = useState(false)

  useEffect(() => {
    loadBulkOperations()
  }, [])

  const loadBulkOperations = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockOperations: BulkOperation[] = [
        {
          id: 'bulk_001',
          name: 'Update User Tier Status',
          type: 'user_update',
          status: 'running',
          entityType: 'users',
          totalItems: 1250,
          processedItems: 847,
          successCount: 832,
          errorCount: 15,
          startedAt: new Date(Date.now() - 45 * 60 * 1000),
          estimatedCompletion: new Date(Date.now() + 15 * 60 * 1000),
          createdBy: '<EMAIL>',
          description: 'Bulk update user tier status based on purchase history and engagement metrics',
          parameters: {
            criteria: 'purchase_amount > 500',
            newTier: 'premium',
            notifyUsers: true
          },
          errors: [
            {
              itemId: 'user_123',
              error: 'Invalid email address',
              details: 'Email format validation failed',
              timestamp: new Date(Date.now() - 30 * 60 * 1000)
            }
          ]
        },
        {
          id: 'bulk_002',
          name: 'Product Price Update',
          type: 'product_update',
          status: 'completed',
          entityType: 'products',
          totalItems: 456,
          processedItems: 456,
          successCount: 456,
          errorCount: 0,
          startedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 90 * 60 * 1000),
          createdBy: '<EMAIL>',
          description: 'Apply 15% discount to all artisan keycap products for holiday sale',
          parameters: {
            category: 'artisan',
            discountPercentage: 15,
            startDate: '2025-01-15',
            endDate: '2025-01-31'
          },
          errors: []
        },
        {
          id: 'bulk_003',
          name: 'Email Campaign - Welcome Series',
          type: 'email_campaign',
          status: 'pending',
          entityType: 'users',
          totalItems: 234,
          processedItems: 0,
          successCount: 0,
          errorCount: 0,
          createdBy: '<EMAIL>',
          description: 'Send welcome email series to new users registered in the last 7 days',
          parameters: {
            template: 'welcome_series_v2',
            segment: 'new_users_7d',
            scheduledTime: '2025-01-14T10:00:00Z'
          },
          errors: []
        }
      ]

      const mockTemplates: BulkOperationTemplate[] = [
        {
          id: 'template_001',
          name: 'User Tier Upgrade',
          description: 'Upgrade users to higher tier based on criteria',
          type: 'user_update',
          entityType: 'users',
          parameters: {
            field: 'tier',
            criteria: 'configurable',
            notifyUsers: true
          },
          isSystem: true
        },
        {
          id: 'template_002',
          name: 'Product Bulk Discount',
          description: 'Apply discount to multiple products',
          type: 'product_update',
          entityType: 'products',
          parameters: {
            field: 'price',
            operation: 'discount',
            notifyCustomers: false
          },
          isSystem: true
        },
        {
          id: 'template_003',
          name: 'Order Status Update',
          description: 'Update order status in bulk',
          type: 'order_update',
          entityType: 'orders',
          parameters: {
            field: 'status',
            sendNotifications: true
          },
          isSystem: true
        }
      ]

      setOperations(mockOperations)
      setTemplates(mockTemplates)
    } catch (error) {
      console.error('Error loading bulk operations:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-400 bg-blue-900/20'
      case 'completed': return 'text-green-400 bg-green-900/20'
      case 'failed': return 'text-red-400 bg-red-900/20'
      case 'pending': return 'text-yellow-400 bg-yellow-900/20'
      case 'paused': return 'text-orange-400 bg-orange-900/20'
      case 'cancelled': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="text-blue-400" size={16} />
      case 'completed': return <CheckCircle className="text-green-400" size={16} />
      case 'failed': return <XCircle className="text-red-400" size={16} />
      case 'pending': return <Clock className="text-yellow-400" size={16} />
      case 'paused': return <Pause className="text-orange-400" size={16} />
      case 'cancelled': return <Square className="text-gray-400" size={16} />
      default: return <Clock className="text-gray-400" size={16} />
    }
  }

  const getEntityIcon = (entityType: string) => {
    switch (entityType) {
      case 'users': return <Users className="text-purple-400" size={16} />
      case 'products': return <Package className="text-green-400" size={16} />
      case 'orders': return <ShoppingCart className="text-blue-400" size={16} />
      case 'reviews': return <FileText className="text-yellow-400" size={16} />
      case 'tickets': return <FileText className="text-orange-400" size={16} />
      default: return <FileText className="text-gray-400" size={16} />
    }
  }

  const calculateProgress = (operation: BulkOperation) => {
    if (operation.totalItems === 0) return 0
    return (operation.processedItems / operation.totalItems) * 100
  }

  const formatDuration = (startTime: Date, endTime?: Date) => {
    const end = endTime || new Date()
    const duration = end.getTime() - startTime.getTime()
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }

  const pauseOperation = async (operationId: string) => {
    // Simulate API call
    console.log('Pausing operation:', operationId)
  }

  const resumeOperation = async (operationId: string) => {
    // Simulate API call
    console.log('Resuming operation:', operationId)
  }

  const cancelOperation = async (operationId: string) => {
    // Simulate API call
    console.log('Cancelling operation:', operationId)
  }

  const activeOperations = operations.filter(op => ['pending', 'running', 'paused'].includes(op.status))
  const completedOperations = operations.filter(op => ['completed', 'failed', 'cancelled'].includes(op.status))

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Layers className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Bulk Operations</h1>
            <p className="text-gray-400">Manage large-scale data operations with advanced monitoring and control</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadBulkOperations}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateOperation(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Zap size={20} className="mr-2" />
            New Operation
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Operations</p>
              <p className="text-2xl font-bold text-white">{activeOperations.length}</p>
            </div>
            <Play className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Items Processing</p>
              <p className="text-2xl font-bold text-white">
                {activeOperations.reduce((sum, op) => sum + (op.totalItems - op.processedItems), 0)}
              </p>
            </div>
            <Clock className="text-yellow-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-2xl font-bold text-white">
                {operations.length > 0 
                  ? Math.round((operations.reduce((sum, op) => sum + op.successCount, 0) / 
                      operations.reduce((sum, op) => sum + op.processedItems, 1)) * 100)
                  : 0}%
              </p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Templates</p>
              <p className="text-2xl font-bold text-white">{templates.length}</p>
            </div>
            <FileText className="text-purple-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'active', label: 'Active Operations', count: activeOperations.length },
            { id: 'completed', label: 'Completed', count: completedOperations.length },
            { id: 'templates', label: 'Templates', count: templates.length },
            { id: 'create', label: 'Create New', count: null }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'active' && (
        <div className="space-y-4">
          {activeOperations.length === 0 ? (
            <div className="bg-gray-800 p-8 rounded-lg text-center">
              <Layers className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Active Operations</h3>
              <p className="text-gray-400">All bulk operations have completed or there are no operations running.</p>
            </div>
          ) : (
            activeOperations.map((operation) => (
              <motion.div
                key={operation.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getEntityIcon(operation.entityType)}
                      <h3 className="text-lg font-semibold text-white">{operation.name}</h3>
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(operation.status)}`}>
                        {getStatusIcon(operation.status)}
                        <span className="ml-1">{operation.status}</span>
                      </span>
                    </div>
                    <p className="text-gray-400 text-sm mb-3">{operation.description}</p>
                    
                    {/* Progress Bar */}
                    <div className="mb-3">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Progress</span>
                        <span className="text-white">
                          {operation.processedItems} / {operation.totalItems} ({calculateProgress(operation).toFixed(1)}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${calculateProgress(operation)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Success:</span>
                        <span className="text-green-400 ml-1 font-medium">{operation.successCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Errors:</span>
                        <span className="text-red-400 ml-1 font-medium">{operation.errorCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Duration:</span>
                        <span className="text-white ml-1 font-medium">
                          {operation.startedAt ? formatDuration(operation.startedAt) : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    {operation.status === 'running' && (
                      <button
                        onClick={() => pauseOperation(operation.id)}
                        className="bg-orange-600 hover:bg-orange-700 text-white p-2 rounded"
                      >
                        <Pause size={16} />
                      </button>
                    )}
                    {operation.status === 'paused' && (
                      <button
                        onClick={() => resumeOperation(operation.id)}
                        className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded"
                      >
                        <Play size={16} />
                      </button>
                    )}
                    <button
                      onClick={() => cancelOperation(operation.id)}
                      className="bg-red-600 hover:bg-red-700 text-white p-2 rounded"
                    >
                      <Square size={16} />
                    </button>
                  </div>
                </div>

                {/* Errors */}
                {operation.errors.length > 0 && (
                  <div className="mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded">
                    <div className="flex items-center mb-2">
                      <AlertTriangle className="text-red-400 mr-2" size={16} />
                      <span className="text-red-400 font-medium">Recent Errors ({operation.errors.length})</span>
                    </div>
                    <div className="space-y-1">
                      {operation.errors.slice(0, 3).map((error, index) => (
                        <div key={index} className="text-sm text-gray-300">
                          <span className="text-red-400">Item {error.itemId}:</span> {error.error}
                        </div>
                      ))}
                      {operation.errors.length > 3 && (
                        <p className="text-sm text-gray-400">+{operation.errors.length - 3} more errors</p>
                      )}
                    </div>
                  </div>
                )}
              </motion.div>
            ))
          )}
        </div>
      )}

      {activeTab === 'completed' && (
        <div className="space-y-4">
          {completedOperations.map((operation) => (
            <motion.div
              key={operation.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {getEntityIcon(operation.entityType)}
                    <h3 className="text-lg font-semibold text-white">{operation.name}</h3>
                    <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(operation.status)}`}>
                      {getStatusIcon(operation.status)}
                      <span className="ml-1">{operation.status}</span>
                    </span>
                  </div>
                  <p className="text-gray-400 text-sm mb-3">{operation.description}</p>
                  
                  <div className="grid grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Total:</span>
                      <span className="text-white ml-1 font-medium">{operation.totalItems}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Success:</span>
                      <span className="text-green-400 ml-1 font-medium">{operation.successCount}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Errors:</span>
                      <span className="text-red-400 ml-1 font-medium">{operation.errorCount}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Duration:</span>
                      <span className="text-white ml-1 font-medium">
                        {operation.startedAt && operation.completedAt 
                          ? formatDuration(operation.startedAt, operation.completedAt)
                          : 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded">
                    <Download size={16} />
                  </button>
                  <button className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded">
                    <BarChart3 size={16} />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {activeTab === 'templates' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white">{template.name}</h3>
                  <p className="text-gray-400 text-sm mt-1">{template.description}</p>
                </div>
                {template.isSystem && (
                  <span className="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-900/20 text-blue-400">
                    System
                  </span>
                )}
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Type:</span>
                  <span className="text-white">{template.type.replace('_', ' ')}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Entity:</span>
                  <span className="text-white">{template.entityType}</span>
                </div>
              </div>

              <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition-colors">
                Use Template
              </button>
            </motion.div>
          ))}
        </div>
      )}

      {activeTab === 'create' && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Create New Bulk Operation</h3>
          <div className="text-center py-8">
            <Zap className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-white mb-2">Bulk Operation Builder</h4>
            <p className="text-gray-400 mb-4">
              Create custom bulk operations with advanced filtering, validation, and monitoring.
            </p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
              Launch Builder
            </button>
          </div>
        </div>
      )}

      {/* Create Operation Modal */}
      {showCreateOperation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Create Bulk Operation</h3>
            <p className="text-gray-400 mb-4">
              Advanced bulk operation builder will allow you to create custom operations with 
              filtering, validation, progress monitoring, and error handling.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowCreateOperation(false)}
                className="flex-1 py-2 px-4 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
