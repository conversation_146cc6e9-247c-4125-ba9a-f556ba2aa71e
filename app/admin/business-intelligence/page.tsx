'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp,
  Calendar,
  Download,
  Upload,
  Settings,
  Plus,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  Grid,
  Layout,
  Database,
  Clock,
  Users,
  DollarSign,
  ShoppingCart,
  Target,
  Zap,
  Activity,
  Globe,
  Monitor,
  Layers,
  Share2,
  BookOpen,
  FileText,
  AlertCircle,
  CheckCircle,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface Dashboard {
  id: string
  name: string
  description: string
  category: 'executive' | 'sales' | 'marketing' | 'operations' | 'finance' | 'custom'
  isPublic: boolean
  isDefault: boolean
  layout: DashboardLayout
  widgets: DashboardWidget[]
  filters: DashboardFilter[]
  refreshInterval: number // minutes
  lastUpdated: Date
  createdBy: string
  sharedWith: string[]
  permissions: {
    view: string[]
    edit: string[]
    admin: string[]
  }
  metrics: {
    views: number
    lastViewed: Date
    avgLoadTime: number
  }
}

interface DashboardLayout {
  columns: number
  rows: number
  gridSize: 'small' | 'medium' | 'large'
  theme: 'light' | 'dark' | 'auto'
  spacing: number
}

interface DashboardWidget {
  id: string
  type: 'chart' | 'metric' | 'table' | 'map' | 'text' | 'image' | 'iframe'
  title: string
  description?: string
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  dataSource: DataSource
  visualization: VisualizationConfig
  filters: WidgetFilter[]
  refreshInterval: number
  isVisible: boolean
  permissions: string[]
}

interface DataSource {
  id: string
  name: string
  type: 'database' | 'api' | 'file' | 'realtime'
  connection: {
    endpoint?: string
    query?: string
    parameters?: Record<string, any>
    authentication?: {
      type: 'none' | 'basic' | 'bearer' | 'api_key'
      credentials?: Record<string, string>
    }
  }
  schema: {
    fields: { name: string; type: string; format?: string }[]
    primaryKey?: string
    relationships?: { field: string; references: string }[]
  }
  cache: {
    enabled: boolean
    ttl: number // seconds
    strategy: 'memory' | 'redis' | 'database'
  }
}

interface VisualizationConfig {
  chartType: 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'heatmap' | 'gauge' | 'funnel'
  xAxis?: {
    field: string
    label: string
    type: 'category' | 'datetime' | 'numeric'
    format?: string
  }
  yAxis?: {
    field: string
    label: string
    type: 'numeric'
    format?: string
    aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max'
  }
  series?: {
    field: string
    label: string
    color?: string
    type?: 'line' | 'bar' | 'area'
  }[]
  colors: string[]
  options: {
    showLegend: boolean
    showGrid: boolean
    showTooltip: boolean
    animation: boolean
    responsive: boolean
  }
}

interface WidgetFilter {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'between' | 'in'
  value: any
  isActive: boolean
}

interface DashboardFilter {
  id: string
  name: string
  type: 'date_range' | 'select' | 'multi_select' | 'text' | 'number'
  field: string
  options?: { label: string; value: any }[]
  defaultValue?: any
  isGlobal: boolean
  affectedWidgets: string[]
}

interface Report {
  id: string
  name: string
  description: string
  type: 'standard' | 'custom' | 'scheduled' | 'adhoc'
  category: 'financial' | 'sales' | 'marketing' | 'operations' | 'compliance'
  template: ReportTemplate
  parameters: ReportParameter[]
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
    time: string
    timezone: string
    recipients: string[]
    format: 'pdf' | 'excel' | 'csv' | 'html'
  }
  lastRun?: Date
  nextRun?: Date
  status: 'active' | 'paused' | 'error' | 'completed'
  metrics: {
    runs: number
    avgDuration: number
    successRate: number
    lastError?: string
  }
  createdBy: string
  createdAt: Date
}

interface ReportTemplate {
  id: string
  name: string
  layout: 'portrait' | 'landscape'
  sections: ReportSection[]
  styling: {
    headerLogo?: string
    footerText?: string
    colors: Record<string, string>
    fonts: Record<string, string>
  }
}

interface ReportSection {
  id: string
  type: 'header' | 'content' | 'chart' | 'table' | 'text' | 'page_break'
  title?: string
  content?: string
  dataSource?: string
  visualization?: VisualizationConfig
  position: {
    x: number
    y: number
    width: number
    height: number
  }
}

interface ReportParameter {
  name: string
  type: 'date' | 'string' | 'number' | 'boolean' | 'select'
  label: string
  required: boolean
  defaultValue?: any
  options?: { label: string; value: any }[]
}

interface DataConnector {
  id: string
  name: string
  type: 'database' | 'api' | 'file' | 'cloud_storage'
  status: 'connected' | 'disconnected' | 'error' | 'testing'
  configuration: {
    host?: string
    port?: number
    database?: string
    username?: string
    password?: string
    connectionString?: string
    apiEndpoint?: string
    apiKey?: string
    filePath?: string
    bucketName?: string
  }
  schema: {
    tables?: { name: string; columns: { name: string; type: string }[] }[]
    endpoints?: { path: string; method: string; parameters: string[] }[]
  }
  metrics: {
    lastConnection: Date
    uptime: number
    avgResponseTime: number
    errorCount: number
    dataVolume: number
  }
  createdAt: Date
  lastModified: Date
}

export default function BusinessIntelligencePage() {
  const [dashboards, setDashboards] = useState<Dashboard[]>([])
  const [reports, setReports] = useState<Report[]>([])
  const [dataConnectors, setDataConnectors] = useState<DataConnector[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'dashboards' | 'reports' | 'data-sources' | 'builder'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadBusinessIntelligenceData()
  }, [])

  const loadBusinessIntelligenceData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual BI API integration
      const mockDashboards: Dashboard[] = [
        {
          id: 'dash_001',
          name: 'Executive Overview',
          description: 'High-level business metrics and KPIs for executive team',
          category: 'executive',
          isPublic: false,
          isDefault: true,
          layout: {
            columns: 12,
            rows: 8,
            gridSize: 'medium',
            theme: 'dark',
            spacing: 16
          },
          widgets: [
            {
              id: 'widget_001',
              type: 'metric',
              title: 'Total Revenue',
              position: { x: 0, y: 0, width: 3, height: 2 },
              dataSource: {
                id: 'ds_revenue',
                name: 'Revenue Data',
                type: 'database',
                connection: {
                  query: 'SELECT SUM(total) as revenue FROM orders WHERE created_at >= ?',
                  parameters: { date_from: '2025-01-01' }
                },
                schema: {
                  fields: [{ name: 'revenue', type: 'number', format: 'currency' }]
                },
                cache: { enabled: true, ttl: 300, strategy: 'memory' }
              },
              visualization: {
                chartType: 'gauge',
                colors: ['#10B981'],
                options: {
                  showLegend: false,
                  showGrid: false,
                  showTooltip: true,
                  animation: true,
                  responsive: true
                }
              },
              filters: [],
              refreshInterval: 5,
              isVisible: true,
              permissions: ['executive', 'admin']
            },
            {
              id: 'widget_002',
              type: 'chart',
              title: 'Sales Trend',
              position: { x: 3, y: 0, width: 6, height: 4 },
              dataSource: {
                id: 'ds_sales_trend',
                name: 'Sales Trend Data',
                type: 'database',
                connection: {
                  query: 'SELECT DATE(created_at) as date, SUM(total) as sales FROM orders GROUP BY DATE(created_at) ORDER BY date',
                  parameters: {}
                },
                schema: {
                  fields: [
                    { name: 'date', type: 'datetime', format: 'YYYY-MM-DD' },
                    { name: 'sales', type: 'number', format: 'currency' }
                  ]
                },
                cache: { enabled: true, ttl: 600, strategy: 'memory' }
              },
              visualization: {
                chartType: 'line',
                xAxis: { field: 'date', label: 'Date', type: 'datetime' },
                yAxis: { field: 'sales', label: 'Sales', type: 'numeric', format: 'currency' },
                colors: ['#3B82F6'],
                options: {
                  showLegend: true,
                  showGrid: true,
                  showTooltip: true,
                  animation: true,
                  responsive: true
                }
              },
              filters: [],
              refreshInterval: 10,
              isVisible: true,
              permissions: ['executive', 'admin', 'sales']
            }
          ],
          filters: [
            {
              id: 'filter_001',
              name: 'Date Range',
              type: 'date_range',
              field: 'created_at',
              defaultValue: { start: '2025-01-01', end: '2025-12-31' },
              isGlobal: true,
              affectedWidgets: ['widget_001', 'widget_002']
            }
          ],
          refreshInterval: 5,
          lastUpdated: new Date(),
          createdBy: '<EMAIL>',
          sharedWith: ['<EMAIL>', '<EMAIL>'],
          permissions: {
            view: ['executive', 'admin'],
            edit: ['admin'],
            admin: ['admin']
          },
          metrics: {
            views: 1250,
            lastViewed: new Date(Date.now() - 30 * 60 * 1000),
            avgLoadTime: 2.3
          }
        },
        {
          id: 'dash_002',
          name: 'Sales Performance',
          description: 'Detailed sales metrics, conversion rates, and team performance',
          category: 'sales',
          isPublic: false,
          isDefault: false,
          layout: {
            columns: 12,
            rows: 6,
            gridSize: 'medium',
            theme: 'dark',
            spacing: 16
          },
          widgets: [
            {
              id: 'widget_003',
              type: 'chart',
              title: 'Sales by Product Category',
              position: { x: 0, y: 0, width: 6, height: 3 },
              dataSource: {
                id: 'ds_sales_by_category',
                name: 'Sales by Category',
                type: 'database',
                connection: {
                  query: 'SELECT p.category, SUM(oi.quantity * oi.price) as sales FROM order_items oi JOIN products p ON oi.product_id = p.id GROUP BY p.category',
                  parameters: {}
                },
                schema: {
                  fields: [
                    { name: 'category', type: 'string' },
                    { name: 'sales', type: 'number', format: 'currency' }
                  ]
                },
                cache: { enabled: true, ttl: 900, strategy: 'memory' }
              },
              visualization: {
                chartType: 'pie',
                colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'],
                options: {
                  showLegend: true,
                  showGrid: false,
                  showTooltip: true,
                  animation: true,
                  responsive: true
                }
              },
              filters: [],
              refreshInterval: 15,
              isVisible: true,
              permissions: ['sales', 'admin', 'executive']
            }
          ],
          filters: [],
          refreshInterval: 10,
          lastUpdated: new Date(),
          createdBy: '<EMAIL>',
          sharedWith: ['<EMAIL>'],
          permissions: {
            view: ['sales', 'admin', 'executive'],
            edit: ['sales', 'admin'],
            admin: ['admin']
          },
          metrics: {
            views: 890,
            lastViewed: new Date(Date.now() - 2 * 60 * 60 * 1000),
            avgLoadTime: 1.8
          }
        }
      ]

      const mockReports: Report[] = [
        {
          id: 'report_001',
          name: 'Monthly Financial Report',
          description: 'Comprehensive monthly financial performance report',
          type: 'scheduled',
          category: 'financial',
          template: {
            id: 'template_001',
            name: 'Financial Report Template',
            layout: 'portrait',
            sections: [
              {
                id: 'section_001',
                type: 'header',
                title: 'Monthly Financial Report',
                position: { x: 0, y: 0, width: 100, height: 10 }
              },
              {
                id: 'section_002',
                type: 'chart',
                title: 'Revenue Trend',
                dataSource: 'ds_revenue_trend',
                visualization: {
                  chartType: 'line',
                  colors: ['#3B82F6'],
                  options: {
                    showLegend: true,
                    showGrid: true,
                    showTooltip: false,
                    animation: false,
                    responsive: true
                  }
                },
                position: { x: 0, y: 10, width: 100, height: 30 }
              }
            ],
            styling: {
              headerLogo: '/logos/syndicaps-logo.png',
              footerText: '© 2025 Syndicaps. Confidential.',
              colors: { primary: '#3B82F6', secondary: '#10B981' },
              fonts: { heading: 'Inter', body: 'Inter' }
            }
          },
          parameters: [
            {
              name: 'month',
              type: 'date',
              label: 'Report Month',
              required: true,
              defaultValue: new Date().toISOString().slice(0, 7)
            }
          ],
          schedule: {
            frequency: 'monthly',
            time: '09:00',
            timezone: 'UTC',
            recipients: ['<EMAIL>', '<EMAIL>'],
            format: 'pdf'
          },
          lastRun: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          nextRun: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
          status: 'active',
          metrics: {
            runs: 12,
            avgDuration: 45,
            successRate: 100,
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2024-12-01')
        },
        {
          id: 'report_002',
          name: 'Weekly Sales Summary',
          description: 'Weekly sales performance and team metrics',
          type: 'scheduled',
          category: 'sales',
          template: {
            id: 'template_002',
            name: 'Sales Summary Template',
            layout: 'landscape',
            sections: [
              {
                id: 'section_003',
                type: 'header',
                title: 'Weekly Sales Summary',
                position: { x: 0, y: 0, width: 100, height: 8 }
              },
              {
                id: 'section_004',
                type: 'table',
                title: 'Top Performing Products',
                dataSource: 'ds_top_products',
                position: { x: 0, y: 8, width: 50, height: 40 }
              }
            ],
            styling: {
              colors: { primary: '#10B981', secondary: '#3B82F6' },
              fonts: { heading: 'Inter', body: 'Inter' }
            }
          },
          parameters: [
            {
              name: 'week_start',
              type: 'date',
              label: 'Week Start Date',
              required: true
            }
          ],
          schedule: {
            frequency: 'weekly',
            time: '08:00',
            timezone: 'UTC',
            recipients: ['<EMAIL>'],
            format: 'excel'
          },
          lastRun: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          nextRun: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          status: 'active',
          metrics: {
            runs: 52,
            avgDuration: 15,
            successRate: 98.1,
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2024-11-01')
        }
      ]

      const mockDataConnectors: DataConnector[] = [
        {
          id: 'connector_001',
          name: 'Primary Database',
          type: 'database',
          status: 'connected',
          configuration: {
            host: 'localhost',
            port: 5432,
            database: 'syndicaps_prod',
            username: 'analytics_user'
          },
          schema: {
            tables: [
              {
                name: 'orders',
                columns: [
                  { name: 'id', type: 'integer' },
                  { name: 'user_id', type: 'integer' },
                  { name: 'total', type: 'decimal' },
                  { name: 'created_at', type: 'timestamp' }
                ]
              },
              {
                name: 'products',
                columns: [
                  { name: 'id', type: 'integer' },
                  { name: 'name', type: 'varchar' },
                  { name: 'category', type: 'varchar' },
                  { name: 'price', type: 'decimal' }
                ]
              }
            ]
          },
          metrics: {
            lastConnection: new Date(Date.now() - 5 * 60 * 1000),
            uptime: 99.9,
            avgResponseTime: 45,
            errorCount: 2,
            dataVolume: 2.4
          },
          createdAt: new Date('2024-12-01'),
          lastModified: new Date()
        },
        {
          id: 'connector_002',
          name: 'Google Analytics API',
          type: 'api',
          status: 'connected',
          configuration: {
            apiEndpoint: 'https://analyticsreporting.googleapis.com/v4',
            apiKey: 'ga_api_key_***'
          },
          schema: {
            endpoints: [
              {
                path: '/reports:batchGet',
                method: 'POST',
                parameters: ['reportRequests', 'dateRanges', 'metrics', 'dimensions']
              }
            ]
          },
          metrics: {
            lastConnection: new Date(Date.now() - 15 * 60 * 1000),
            uptime: 98.5,
            avgResponseTime: 250,
            errorCount: 5,
            dataVolume: 0.8
          },
          createdAt: new Date('2024-12-15'),
          lastModified: new Date()
        }
      ]

      setDashboards(mockDashboards)
      setReports(mockReports)
      setDataConnectors(mockDataConnectors)
    } catch (error) {
      console.error('Error loading business intelligence data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': case 'active': case 'completed': return 'text-green-400 bg-green-900/20'
      case 'disconnected': case 'paused': return 'text-yellow-400 bg-yellow-900/20'
      case 'error': return 'text-red-400 bg-red-900/20'
      case 'testing': return 'text-blue-400 bg-blue-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'executive': return 'text-purple-400 bg-purple-900/20'
      case 'sales': return 'text-green-400 bg-green-900/20'
      case 'marketing': return 'text-pink-400 bg-pink-900/20'
      case 'operations': return 'text-blue-400 bg-blue-900/20'
      case 'finance': case 'financial': return 'text-yellow-400 bg-yellow-900/20'
      case 'custom': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m ${seconds % 60}s`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BarChart3 className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Business Intelligence</h1>
            <p className="text-gray-400">Custom dashboards, advanced reporting, and data visualization platform</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadBusinessIntelligenceData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/business-intelligence/builder"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Dashboard
          </Link>
        </div>
      </div>

      {/* Business Intelligence Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Dashboards</p>
              <p className="text-2xl font-bold text-white">
                {dashboards.length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {dashboards.filter(d => d.isDefault).length} default
              </p>
            </div>
            <Layout className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Scheduled Reports</p>
              <p className="text-2xl font-bold text-white">
                {reports.filter(r => r.type === 'scheduled').length}
              </p>
              <p className="text-xs text-blue-400 mt-1">
                {reports.filter(r => r.status === 'active').length} active
              </p>
            </div>
            <FileText className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Data Sources</p>
              <p className="text-2xl font-bold text-white">
                {dataConnectors.filter(c => c.status === 'connected').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {dataConnectors.filter(c => c.status === 'error').length} errors
              </p>
            </div>
            <Database className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Views</p>
              <p className="text-2xl font-bold text-white">
                {dashboards.reduce((sum, d) => sum + d.metrics.views, 0).toLocaleString()}
              </p>
              <p className="text-xs text-purple-400 mt-1">This month</p>
            </div>
            <Eye className="text-purple-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'dashboards', label: 'Dashboards', icon: Layout, count: dashboards.length },
            { id: 'reports', label: 'Reports', icon: FileText, count: reports.length },
            { id: 'data-sources', label: 'Data Sources', icon: Database, count: dataConnectors.length },
            { id: 'builder', label: 'Dashboard Builder', icon: Grid }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Popular Dashboards */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Popular Dashboards</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dashboards
                .sort((a, b) => b.metrics.views - a.metrics.views)
                .slice(0, 6)
                .map((dashboard) => (
                  <div key={dashboard.id} className="bg-gray-700 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-white">{dashboard.name}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${getCategoryColor(dashboard.category)}`}>
                        {dashboard.category}
                      </span>
                    </div>
                    <p className="text-xs text-gray-400 mb-3">{dashboard.description}</p>

                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-400">{dashboard.metrics.views} views</span>
                      <span className="text-gray-400">
                        {dashboard.widgets.length} widgets
                      </span>
                    </div>

                    <div className="mt-3 flex space-x-2">
                      <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-1 px-2 rounded text-xs">
                        View
                      </button>
                      <button className="bg-gray-600 hover:bg-gray-500 text-white py-1 px-2 rounded text-xs">
                        Edit
                      </button>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          {/* Recent Reports */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent Report Runs</h3>
            <div className="space-y-3">
              {reports.slice(0, 5).map((report) => (
                <div key={report.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded ${getStatusColor(report.status)}`}>
                      <FileText size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">{report.name}</p>
                      <p className="text-xs text-gray-400">
                        {report.category} • {report.type}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white">
                      {report.lastRun ? report.lastRun.toLocaleDateString() : 'Never'}
                    </p>
                    <p className="text-xs text-gray-400">
                      {formatPercentage(report.metrics.successRate)} success
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Data Source Health */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Data Source Health</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {dataConnectors.map((connector) => (
                <div key={connector.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-white">{connector.name}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(connector.status)}`}>
                      {connector.status}
                    </span>
                  </div>

                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Uptime:</span>
                      <span className="text-white">{formatPercentage(connector.metrics.uptime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Response Time:</span>
                      <span className="text-white">{connector.metrics.avgResponseTime}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Errors:</span>
                      <span className={`${connector.metrics.errorCount > 0 ? 'text-red-400' : 'text-green-400'}`}>
                        {connector.metrics.errorCount}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Dashboard Usage</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Dashboards</span>
                  <span className="text-white font-medium">{dashboards.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Public Dashboards</span>
                  <span className="text-white font-medium">{dashboards.filter(d => d.isPublic).length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Load Time</span>
                  <span className="text-white font-medium">
                    {(dashboards.reduce((sum, d) => sum + d.metrics.avgLoadTime, 0) / dashboards.length).toFixed(1)}s
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Widgets</span>
                  <span className="text-white font-medium">
                    {dashboards.reduce((sum, d) => sum + d.widgets.length, 0)}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Report Statistics</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Reports</span>
                  <span className="text-white font-medium">{reports.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Scheduled Reports</span>
                  <span className="text-white font-medium">{reports.filter(r => r.type === 'scheduled').length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Avg Success Rate</span>
                  <span className="text-white font-medium">
                    {formatPercentage(reports.reduce((sum, r) => sum + r.metrics.successRate, 0) / reports.length)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Runs</span>
                  <span className="text-white font-medium">
                    {reports.reduce((sum, r) => sum + r.metrics.runs, 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'dashboards' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search dashboards..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Categories</option>
                  <option value="executive">Executive</option>
                  <option value="sales">Sales</option>
                  <option value="marketing">Marketing</option>
                  <option value="operations">Operations</option>
                  <option value="finance">Finance</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
            </div>
          </div>

          {/* Dashboards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dashboards.map((dashboard) => (
              <motion.div
                key={dashboard.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-semibold text-white">{dashboard.name}</h3>
                      {dashboard.isDefault && (
                        <span className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded">
                          Default
                        </span>
                      )}
                      {dashboard.isPublic && (
                        <span className="text-xs bg-green-900/30 text-green-300 px-2 py-1 rounded">
                          Public
                        </span>
                      )}
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(dashboard.category)}`}>
                      {dashboard.category}
                    </span>
                  </div>

                  <div className="flex space-x-1">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Share">
                      <Share2 size={16} />
                    </button>
                  </div>
                </div>

                <p className="text-gray-400 text-sm mb-4">{dashboard.description}</p>

                <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                  <div>
                    <span className="text-gray-400">Widgets:</span>
                    <span className="text-white ml-1">{dashboard.widgets.length}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Views:</span>
                    <span className="text-white ml-1">{dashboard.metrics.views}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Load Time:</span>
                    <span className="text-white ml-1">{dashboard.metrics.avgLoadTime}s</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Refresh:</span>
                    <span className="text-white ml-1">{dashboard.refreshInterval}m</span>
                  </div>
                </div>

                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <span>Updated: {dashboard.lastUpdated.toLocaleDateString()}</span>
                  <span>By: {dashboard.createdBy.split('@')[0]}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'reports' || activeTab === 'data-sources' || activeTab === 'builder') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'reports' && 'Advanced Report Management'}
            {activeTab === 'data-sources' && 'Data Source Configuration'}
            {activeTab === 'builder' && 'Visual Dashboard Builder'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'reports' && <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'data-sources' && <Database className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'builder' && <Grid className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'reports' && 'Comprehensive Reporting Engine'}
              {activeTab === 'data-sources' && 'Enterprise Data Integration'}
              {activeTab === 'builder' && 'Drag-and-Drop Dashboard Builder'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'reports' && 'Create, schedule, and distribute custom reports with advanced formatting and automated delivery.'}
              {activeTab === 'data-sources' && 'Connect to databases, APIs, files, and cloud services with real-time synchronization and monitoring.'}
              {activeTab === 'builder' && 'Build custom dashboards with drag-and-drop widgets, real-time data, and interactive visualizations.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
