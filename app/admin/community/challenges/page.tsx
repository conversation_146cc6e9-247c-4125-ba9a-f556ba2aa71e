/**
 * Admin Challenges Management Page
 *
 * Main page for managing community challenges and contests in the admin dashboard.
 * Provides comprehensive challenge creation, judging tools, and team management.
 *
 * Features:
 * - Challenge lifecycle management (draft, active, judging, completed)
 * - Advanced challenge builder with templates and customization
 * - Judging interface with criteria-based scoring
 * - Team challenge management and collaboration tools
 * - Prize distribution and winner selection
 * - Real-time analytics and participation metrics
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { ChallengesManager } from '../../../../src/admin/components/community/challenges/ChallengesManager';

export const metadata: Metadata = {
  title: 'Challenges Management | Syndicaps Admin',
  description: 'Create and manage community challenges, contests, and competitions with comprehensive judging tools.',
  robots: 'noindex, nofollow'
};

/**
 * Admin Challenges Page Component
 * 
 * Protected admin route that requires community_challenges read permission.
 * Renders the comprehensive challenges management interface.
 */
export default function AdminChallengesPage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'community_challenges', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <ChallengesManager />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
