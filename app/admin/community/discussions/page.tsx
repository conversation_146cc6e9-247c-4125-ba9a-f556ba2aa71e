/**
 * Admin Discussions Management Page
 *
 * Main page for managing community discussions in the admin dashboard.
 * Provides comprehensive moderation tools and analytics for discussion threads.
 *
 * Features:
 * - Discussion thread management with filtering and search
 * - Moderation actions and bulk operations
 * - Real-time analytics and performance metrics
 * - Permission-based access control
 * - Responsive design optimized for admin workflows
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { DiscussionsManager } from '../../../../src/admin/components/community/discussions/DiscussionsManager';

export const metadata: Metadata = {
  title: 'Discussions Management | Syndicaps Admin',
  description: 'Manage community discussions, moderate content, and analyze engagement metrics.',
  robots: 'noindex, nofollow'
};

/**
 * Admin Discussions Page Component
 * 
 * Protected admin route that requires community_discussions read permission.
 * Renders the comprehensive discussions management interface.
 */
export default function AdminDiscussionsPage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'community_discussions', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <DiscussionsManager />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
