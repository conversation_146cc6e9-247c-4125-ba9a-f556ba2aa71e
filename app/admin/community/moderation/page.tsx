/**
 * Admin Moderation Dashboard Page
 *
 * Main page for the unified moderation dashboard in the admin interface.
 * Provides centralized queue management, automated moderation, and comprehensive analytics.
 *
 * Features:
 * - Unified moderation queue with priority-based sorting
 * - Multi-content type management (discussions, submissions, challenges)
 * - Automated moderation rules and AI-assisted decisions
 * - Real-time queue updates and assignment management
 * - Comprehensive moderation analytics and performance metrics
 * - Bulk operations and workflow automation
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { ModerationDashboard } from '../../../../src/admin/components/community/moderation/ModerationDashboard';

export const metadata: Metadata = {
  title: 'Moderation Dashboard | Syndicaps Admin',
  description: 'Unified community content moderation dashboard with automated rules and comprehensive analytics.',
  robots: 'noindex, nofollow'
};

/**
 * Admin Moderation Dashboard Page Component
 * 
 * Protected admin route that requires community_moderation read permission.
 * Renders the comprehensive moderation dashboard interface.
 */
export default function AdminModerationPage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'community_moderation', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <ModerationDashboard />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
