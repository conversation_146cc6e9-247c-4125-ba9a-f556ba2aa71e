/**
 * Admin Submissions Management Page
 *
 * Main page for managing community submissions in the admin dashboard.
 * Provides comprehensive review queue, moderation tools, and quality assessment.
 *
 * Features:
 * - Submission review queue with priority sorting
 * - Quality scoring and assessment tools
 * - Content moderation and approval workflow
 * - Featured content curation system
 * - Bulk operations for efficient management
 * - Real-time analytics and performance metrics
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { SubmissionsManager } from '../../../../src/admin/components/community/submissions/SubmissionsManager';

export const metadata: Metadata = {
  title: 'Submissions Management | Syndicaps Admin',
  description: 'Review and moderate community submissions, assess quality, and curate featured content.',
  robots: 'noindex, nofollow'
};

/**
 * Admin Submissions Page Component
 * 
 * Protected admin route that requires community_submissions read permission.
 * Renders the comprehensive submissions management interface.
 */
export default function AdminSubmissionsPage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'community_submissions', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <SubmissionsManager />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
