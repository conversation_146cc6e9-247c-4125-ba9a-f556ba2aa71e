'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  FileText, 
  Calendar, 
  Image, 
  Video, 
  Share2, 
  Clock,
  Eye,
  Edit,
  Trash2,
  Plus,
  RefreshCw,
  Filter,
  Search,
  Upload,
  Settings,
  Send,
  BarChart3,
  Users,
  Heart,
  MessageCircle,
  Globe
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface ContentItem {
  id: string
  title: string
  type: 'blog' | 'social' | 'email' | 'product' | 'announcement'
  status: 'draft' | 'scheduled' | 'published' | 'archived'
  platform?: 'instagram' | 'twitter' | 'facebook' | 'linkedin' | 'website'
  content: string
  excerpt?: string
  featuredImage?: string
  author: string
  scheduledDate?: Date
  publishedDate?: Date
  tags: string[]
  engagement: {
    views: number
    likes: number
    comments: number
    shares: number
  }
  seo: {
    metaTitle?: string
    metaDescription?: string
    keywords: string[]
  }
}

interface ContentCalendar {
  date: Date
  items: ContentItem[]
}

interface SocialMediaAccount {
  id: string
  platform: string
  username: string
  isConnected: boolean
  followers: number
  lastSync: Date
}

export default function ContentManagementPage() {
  const [contentItems, setContentItems] = useState<ContentItem[]>([])
  const [socialAccounts, setSocialAccounts] = useState<SocialMediaAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'content' | 'calendar' | 'social' | 'analytics'>('content')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [selectedDate, setSelectedDate] = useState(new Date())

  useEffect(() => {
    loadContentData()
  }, [])

  const loadContentData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockContent: ContentItem[] = [
        {
          id: 'cnt_001',
          title: 'New Artisan Keycap Collection Launch',
          type: 'blog',
          status: 'published',
          content: 'Introducing our latest artisan keycap collection featuring hand-crafted designs...',
          excerpt: 'Discover our newest artisan keycap collection with unique hand-crafted designs.',
          featuredImage: '/images/keycap-collection.jpg',
          author: '<EMAIL>',
          publishedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          tags: ['artisan', 'keycaps', 'collection', 'launch'],
          engagement: {
            views: 1250,
            likes: 89,
            comments: 23,
            shares: 15
          },
          seo: {
            metaTitle: 'New Artisan Keycap Collection - Syndicaps',
            metaDescription: 'Explore our latest artisan keycap collection featuring unique hand-crafted designs.',
            keywords: ['artisan keycaps', 'mechanical keyboards', 'custom keycaps']
          }
        },
        {
          id: 'cnt_002',
          title: 'Weekend Sale Announcement',
          type: 'social',
          status: 'scheduled',
          platform: 'instagram',
          content: '🔥 Weekend Flash Sale! 25% off all keycap sets. Limited time only! #SyndicapsSale #MechanicalKeyboards',
          author: '<EMAIL>',
          scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          tags: ['sale', 'promotion', 'weekend'],
          engagement: {
            views: 0,
            likes: 0,
            comments: 0,
            shares: 0
          },
          seo: {
            keywords: ['sale', 'keycaps', 'discount']
          }
        },
        {
          id: 'cnt_003',
          title: 'Monthly Newsletter - January 2025',
          type: 'email',
          status: 'draft',
          content: 'Welcome to our January newsletter featuring new products, community highlights...',
          author: '<EMAIL>',
          tags: ['newsletter', 'monthly', 'community'],
          engagement: {
            views: 0,
            likes: 0,
            comments: 0,
            shares: 0
          },
          seo: {
            keywords: ['newsletter', 'updates', 'community']
          }
        }
      ]

      const mockSocialAccounts: SocialMediaAccount[] = [
        {
          id: 'social_001',
          platform: 'Instagram',
          username: '@syndicaps',
          isConnected: true,
          followers: 15420,
          lastSync: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: 'social_002',
          platform: 'Twitter',
          username: '@syndicaps',
          isConnected: true,
          followers: 8750,
          lastSync: new Date(Date.now() - 45 * 60 * 1000)
        },
        {
          id: 'social_003',
          platform: 'Facebook',
          username: 'Syndicaps',
          isConnected: false,
          followers: 0,
          lastSync: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        },
        {
          id: 'social_004',
          platform: 'LinkedIn',
          username: 'syndicaps',
          isConnected: true,
          followers: 2340,
          lastSync: new Date(Date.now() - 60 * 60 * 1000)
        }
      ]

      setContentItems(mockContent)
      setSocialAccounts(mockSocialAccounts)
    } catch (error) {
      console.error('Error loading content data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredContent = contentItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || item.type === filterType
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus
    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'text-green-400 bg-green-900/20'
      case 'scheduled': return 'text-blue-400 bg-blue-900/20'
      case 'draft': return 'text-yellow-400 bg-yellow-900/20'
      case 'archived': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'blog': return 'text-purple-400 bg-purple-900/20'
      case 'social': return 'text-pink-400 bg-pink-900/20'
      case 'email': return 'text-blue-400 bg-blue-900/20'
      case 'product': return 'text-green-400 bg-green-900/20'
      case 'announcement': return 'text-orange-400 bg-orange-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'blog': return <FileText size={16} />
      case 'social': return <Share2 size={16} />
      case 'email': return <Send size={16} />
      case 'product': return <Image size={16} />
      case 'announcement': return <Globe size={16} />
      default: return <FileText size={16} />
    }
  }

  const formatEngagement = (engagement: ContentItem['engagement']) => {
    const total = engagement.views + engagement.likes + engagement.comments + engagement.shares
    return total.toLocaleString()
  }

  const generateCalendarDays = () => {
    const today = new Date()
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    const days = []

    for (let date = new Date(startOfMonth); date <= endOfMonth; date.setDate(date.getDate() + 1)) {
      const dayContent = contentItems.filter(item => {
        if (item.scheduledDate) {
          return item.scheduledDate.toDateString() === date.toDateString()
        }
        if (item.publishedDate) {
          return item.publishedDate.toDateString() === date.toDateString()
        }
        return false
      })

      days.push({
        date: new Date(date),
        items: dayContent
      })
    }

    return days
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText className="w-8 h-8 text-purple-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Content Management</h1>
            <p className="text-gray-400">Content scheduling, calendar management, and social media integration</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadContentData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/content/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Content
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Content</p>
              <p className="text-2xl font-bold text-white">{contentItems.length}</p>
              <p className="text-xs text-green-400 mt-1">+5 this week</p>
            </div>
            <FileText className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Scheduled</p>
              <p className="text-2xl font-bold text-white">
                {contentItems.filter(item => item.status === 'scheduled').length}
              </p>
              <p className="text-xs text-blue-400 mt-1">Next 7 days</p>
            </div>
            <Calendar className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Engagement</p>
              <p className="text-2xl font-bold text-white">
                {contentItems.reduce((sum, item) => sum + item.engagement.views + item.engagement.likes, 0).toLocaleString()}
              </p>
              <p className="text-xs text-green-400 mt-1">+12% this month</p>
            </div>
            <Heart className="text-pink-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Connected Accounts</p>
              <p className="text-2xl font-bold text-white">
                {socialAccounts.filter(account => account.isConnected).length}
              </p>
              <p className="text-xs text-gray-400 mt-1">Social platforms</p>
            </div>
            <Share2 className="text-green-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'content', label: 'Content Library', icon: FileText },
            { id: 'calendar', label: 'Content Calendar', icon: Calendar },
            { id: 'social', label: 'Social Media', icon: Share2 },
            { id: 'analytics', label: 'Analytics', icon: BarChart3 }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
              </button>
            )
          })}
        </div>
      </div>

      {/* Search and Filters */}
      {activeTab === 'content' && (
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search content..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>
            
            <div className="flex gap-3">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Types</option>
                <option value="blog">Blog</option>
                <option value="social">Social</option>
                <option value="email">Email</option>
                <option value="product">Product</option>
                <option value="announcement">Announcement</option>
              </select>
              
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Status</option>
                <option value="draft">Draft</option>
                <option value="scheduled">Scheduled</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'content' && (
        <div className="space-y-4">
          {filteredContent.length === 0 ? (
            <div className="bg-gray-800 p-8 rounded-lg text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Content Found</h3>
              <p className="text-gray-400">Create your first content item or adjust your search criteria.</p>
            </div>
          ) : (
            filteredContent.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`p-1 rounded ${getTypeColor(item.type)}`}>
                        {getTypeIcon(item.type)}
                      </div>
                      <h3 className="text-lg font-semibold text-white">{item.title}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                      {item.platform && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-pink-400 bg-pink-900/20">
                          {item.platform}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                      {item.excerpt || item.content.substring(0, 150) + '...'}
                    </p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div className="flex items-center">
                        <Eye size={14} className="text-gray-400 mr-1" />
                        <span className="text-white">{item.engagement.views.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center">
                        <Heart size={14} className="text-gray-400 mr-1" />
                        <span className="text-white">{item.engagement.likes.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center">
                        <MessageCircle size={14} className="text-gray-400 mr-1" />
                        <span className="text-white">{item.engagement.comments.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center">
                        <Share2 size={14} className="text-gray-400 mr-1" />
                        <span className="text-white">{item.engagement.shares.toLocaleString()}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>By {item.author}</span>
                      {item.publishedDate && (
                        <span>Published: {item.publishedDate.toLocaleDateString()}</span>
                      )}
                      {item.scheduledDate && (
                        <span>Scheduled: {item.scheduledDate.toLocaleDateString()}</span>
                      )}
                    </div>

                    {item.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3">
                        {item.tags.map((tag) => (
                          <span key={tag} className="inline-flex px-2 py-1 text-xs font-medium rounded bg-gray-700 text-gray-300">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded" title="Settings">
                      <Settings size={16} />
                    </button>
                    <button className="bg-red-600 hover:bg-red-700 text-white p-2 rounded" title="Delete">
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      )}

      {activeTab === 'calendar' && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Content Calendar</h3>
          <div className="grid grid-cols-7 gap-2">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="p-2 text-center text-gray-400 font-medium text-sm">
                {day}
              </div>
            ))}
            {generateCalendarDays().map((day, index) => (
              <div key={index} className="min-h-[100px] p-2 border border-gray-700 rounded">
                <div className="text-sm text-white mb-1">{day.date.getDate()}</div>
                <div className="space-y-1">
                  {day.items.slice(0, 2).map((item) => (
                    <div key={item.id} className={`text-xs p-1 rounded truncate ${getTypeColor(item.type)}`}>
                      {item.title}
                    </div>
                  ))}
                  {day.items.length > 2 && (
                    <div className="text-xs text-gray-400">+{day.items.length - 2} more</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'social' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {socialAccounts.map((account) => (
              <motion.div
                key={account.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">{account.platform}</h3>
                  <div className={`w-3 h-3 rounded-full ${account.isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Username:</span>
                    <span className="text-white">{account.username}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Followers:</span>
                    <span className="text-white">{account.followers.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Last Sync:</span>
                    <span className="text-white">{account.lastSync.toLocaleDateString()}</span>
                  </div>
                </div>

                <div className="mt-4">
                  {account.isConnected ? (
                    <button className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded transition-colors">
                      Disconnect
                    </button>
                  ) : (
                    <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors">
                      Connect
                    </button>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Content Analytics</h3>
          <div className="text-center py-8">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-white mb-2">Content Performance Analytics</h4>
            <p className="text-gray-400 mb-4">
              Detailed analytics for content performance, engagement metrics, and audience insights.
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
