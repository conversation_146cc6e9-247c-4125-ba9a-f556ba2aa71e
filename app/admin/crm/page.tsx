'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  UserPlus, 
  Phone, 
  Mail, 
  Calendar,
  DollarSign,
  TrendingUp,
  Activity,
  MessageSquare,
  FileText,
  Tag,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  Download,
  Upload,
  BarChart3,
  Target,
  Heart,
  Star,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface Customer {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  company?: string
  title?: string
  status: 'lead' | 'prospect' | 'customer' | 'inactive' | 'churned'
  source: 'website' | 'referral' | 'social' | 'email' | 'phone' | 'event' | 'other'
  tags: string[]
  customFields: Record<string, any>
  createdAt: Date
  lastContact?: Date
  lastActivity?: Date
  assignedTo?: string
  lifecycle: {
    stage: 'awareness' | 'consideration' | 'decision' | 'retention' | 'advocacy'
    score: number
    lastStageChange: Date
  }
  metrics: {
    totalSpent: number
    orderCount: number
    averageOrderValue: number
    lifetimeValue: number
    engagementScore: number
    churnRisk: number
  }
  preferences: {
    communicationChannel: 'email' | 'phone' | 'sms' | 'chat'
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
    topics: string[]
    optedOut: boolean
  }
}

interface Interaction {
  id: string
  customerId: string
  type: 'email' | 'phone' | 'meeting' | 'note' | 'task' | 'purchase' | 'support'
  direction: 'inbound' | 'outbound'
  subject: string
  content: string
  outcome?: 'positive' | 'neutral' | 'negative'
  nextAction?: string
  scheduledFollowUp?: Date
  attachments: string[]
  createdBy: string
  createdAt: Date
  duration?: number // minutes
  tags: string[]
}

interface Deal {
  id: string
  customerId: string
  title: string
  description: string
  value: number
  currency: string
  stage: 'qualification' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost'
  probability: number
  expectedCloseDate: Date
  actualCloseDate?: Date
  source: string
  assignedTo: string
  products: {
    id: string
    name: string
    quantity: number
    price: number
  }[]
  activities: Interaction[]
  competitors: string[]
  lossReason?: string
  createdAt: Date
  updatedAt: Date
}

interface Campaign {
  id: string
  name: string
  type: 'email' | 'social' | 'event' | 'webinar' | 'content'
  status: 'draft' | 'active' | 'paused' | 'completed'
  targetSegment: string
  startDate: Date
  endDate?: Date
  budget: number
  spent: number
  metrics: {
    reach: number
    impressions: number
    clicks: number
    conversions: number
    leads: number
    cost_per_lead: number
    roi: number
  }
  createdBy: string
  createdAt: Date
}

export default function CRMSystemPage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [interactions, setInteractions] = useState<Interaction[]>([])
  const [deals, setDeals] = useState<Deal[]>([])
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'dashboard' | 'customers' | 'deals' | 'interactions' | 'campaigns'>('dashboard')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterStage, setFilterStage] = useState<string>('all')

  useEffect(() => {
    loadCRMData()
  }, [])

  const loadCRMData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual CRM API integration
      const mockCustomers: Customer[] = [
        {
          id: 'cust_001',
          firstName: 'John',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '******-0123',
          company: 'TechCorp Inc.',
          title: 'IT Manager',
          status: 'customer',
          source: 'website',
          tags: ['enterprise', 'high-value', 'tech'],
          customFields: {
            industry: 'Technology',
            employees: '500-1000',
            budget: 'High'
          },
          createdAt: new Date('2024-12-01'),
          lastContact: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          lastActivity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          assignedTo: '<EMAIL>',
          lifecycle: {
            stage: 'retention',
            score: 85,
            lastStageChange: new Date('2024-12-15')
          },
          metrics: {
            totalSpent: 2450,
            orderCount: 5,
            averageOrderValue: 490,
            lifetimeValue: 3200,
            engagementScore: 78,
            churnRisk: 0.15
          },
          preferences: {
            communicationChannel: 'email',
            frequency: 'weekly',
            topics: ['product-updates', 'industry-news'],
            optedOut: false
          }
        },
        {
          id: 'cust_002',
          firstName: 'Sarah',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '******-0456',
          company: 'Creative Design Studio',
          title: 'Creative Director',
          status: 'prospect',
          source: 'referral',
          tags: ['creative', 'small-business'],
          customFields: {
            industry: 'Design',
            employees: '10-50',
            budget: 'Medium'
          },
          createdAt: new Date('2025-01-05'),
          lastContact: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          assignedTo: '<EMAIL>',
          lifecycle: {
            stage: 'consideration',
            score: 65,
            lastStageChange: new Date('2025-01-08')
          },
          metrics: {
            totalSpent: 0,
            orderCount: 0,
            averageOrderValue: 0,
            lifetimeValue: 0,
            engagementScore: 45,
            churnRisk: 0.35
          },
          preferences: {
            communicationChannel: 'email',
            frequency: 'monthly',
            topics: ['design-inspiration', 'tutorials'],
            optedOut: false
          }
        },
        {
          id: 'cust_003',
          firstName: 'Michael',
          lastName: 'Chen',
          email: '<EMAIL>',
          company: 'StartupXYZ',
          title: 'Founder',
          status: 'lead',
          source: 'social',
          tags: ['startup', 'early-stage'],
          customFields: {
            industry: 'SaaS',
            employees: '1-10',
            budget: 'Low'
          },
          createdAt: new Date('2025-01-12'),
          lastActivity: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          assignedTo: '<EMAIL>',
          lifecycle: {
            stage: 'awareness',
            score: 35,
            lastStageChange: new Date('2025-01-12')
          },
          metrics: {
            totalSpent: 0,
            orderCount: 0,
            averageOrderValue: 0,
            lifetimeValue: 0,
            engagementScore: 25,
            churnRisk: 0.60
          },
          preferences: {
            communicationChannel: 'email',
            frequency: 'weekly',
            topics: ['startup-tips', 'product-updates'],
            optedOut: false
          }
        }
      ]

      const mockDeals: Deal[] = [
        {
          id: 'deal_001',
          customerId: 'cust_001',
          title: 'Enterprise Keycap Set Order',
          description: 'Bulk order for office mechanical keyboards',
          value: 5000,
          currency: 'USD',
          stage: 'negotiation',
          probability: 75,
          expectedCloseDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          source: 'website',
          assignedTo: '<EMAIL>',
          products: [
            { id: 'prod_001', name: 'Premium Keycap Set', quantity: 50, price: 100 }
          ],
          activities: [],
          competitors: ['KeyboardCorp', 'CapMaster'],
          createdAt: new Date('2025-01-08'),
          updatedAt: new Date()
        },
        {
          id: 'deal_002',
          customerId: 'cust_002',
          title: 'Custom Design Collaboration',
          description: 'Custom keycap design for creative team',
          value: 1200,
          currency: 'USD',
          stage: 'proposal',
          probability: 50,
          expectedCloseDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
          source: 'referral',
          assignedTo: '<EMAIL>',
          products: [
            { id: 'prod_002', name: 'Custom Artisan Set', quantity: 12, price: 100 }
          ],
          activities: [],
          competitors: ['ArtisanKeys'],
          createdAt: new Date('2025-01-10'),
          updatedAt: new Date()
        }
      ]

      const mockInteractions: Interaction[] = [
        {
          id: 'int_001',
          customerId: 'cust_001',
          type: 'email',
          direction: 'outbound',
          subject: 'Follow-up on Enterprise Order',
          content: 'Hi John, following up on your interest in our enterprise keycap solutions...',
          outcome: 'positive',
          nextAction: 'Schedule demo call',
          scheduledFollowUp: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
          attachments: ['enterprise_catalog.pdf'],
          createdBy: '<EMAIL>',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          tags: ['follow-up', 'enterprise']
        },
        {
          id: 'int_002',
          customerId: 'cust_002',
          type: 'phone',
          direction: 'inbound',
          subject: 'Inquiry about Custom Design Services',
          content: 'Customer called asking about custom keycap design options for their creative team.',
          outcome: 'positive',
          nextAction: 'Send design portfolio',
          duration: 25,
          attachments: [],
          createdBy: '<EMAIL>',
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          tags: ['inquiry', 'custom-design']
        }
      ]

      const mockCampaigns: Campaign[] = [
        {
          id: 'camp_001',
          name: 'New Year Keycap Collection Launch',
          type: 'email',
          status: 'active',
          targetSegment: 'active_customers',
          startDate: new Date('2025-01-01'),
          endDate: new Date('2025-01-31'),
          budget: 5000,
          spent: 2300,
          metrics: {
            reach: 1250,
            impressions: 3400,
            clicks: 340,
            conversions: 45,
            leads: 23,
            cost_per_lead: 100,
            roi: 2.3
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2024-12-20')
        },
        {
          id: 'camp_002',
          name: 'Enterprise Solutions Webinar',
          type: 'webinar',
          status: 'completed',
          targetSegment: 'enterprise_prospects',
          startDate: new Date('2024-12-15'),
          endDate: new Date('2024-12-15'),
          budget: 2000,
          spent: 1800,
          metrics: {
            reach: 500,
            impressions: 1200,
            clicks: 120,
            conversions: 15,
            leads: 12,
            cost_per_lead: 150,
            roi: 1.8
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2024-12-01')
        }
      ]

      setCustomers(mockCustomers)
      setDeals(mockDeals)
      setInteractions(mockInteractions)
      setCampaigns(mockCampaigns)
    } catch (error) {
      console.error('Error loading CRM data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.company && customer.company.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = filterStatus === 'all' || customer.status === filterStatus
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'customer': return 'text-green-400 bg-green-900/20'
      case 'prospect': return 'text-blue-400 bg-blue-900/20'
      case 'lead': return 'text-yellow-400 bg-yellow-900/20'
      case 'inactive': return 'text-gray-400 bg-gray-900/20'
      case 'churned': return 'text-red-400 bg-red-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'awareness': return 'text-purple-400 bg-purple-900/20'
      case 'consideration': return 'text-blue-400 bg-blue-900/20'
      case 'decision': return 'text-yellow-400 bg-yellow-900/20'
      case 'retention': return 'text-green-400 bg-green-900/20'
      case 'advocacy': return 'text-pink-400 bg-pink-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getDealStageColor = (stage: string) => {
    switch (stage) {
      case 'qualification': return 'text-purple-400 bg-purple-900/20'
      case 'proposal': return 'text-blue-400 bg-blue-900/20'
      case 'negotiation': return 'text-yellow-400 bg-yellow-900/20'
      case 'closed_won': return 'text-green-400 bg-green-900/20'
      case 'closed_lost': return 'text-red-400 bg-red-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString()
  }

  const calculatePipelineValue = () => {
    return deals
      .filter(deal => !['closed_won', 'closed_lost'].includes(deal.stage))
      .reduce((sum, deal) => sum + (deal.value * deal.probability / 100), 0)
  }

  const calculateConversionRate = () => {
    const totalCustomers = customers.filter(c => c.status === 'customer').length
    const totalLeads = customers.length
    return totalLeads > 0 ? (totalCustomers / totalLeads) * 100 : 0
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Users className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">CRM System</h1>
            <p className="text-gray-400">Customer relationship management, sales pipeline, and interaction tracking</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadCRMData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/crm/customers/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <UserPlus size={20} className="mr-2" />
            Add Customer
          </Link>
        </div>
      </div>

      {/* CRM Dashboard Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Customers</p>
              <p className="text-2xl font-bold text-white">{customers.length}</p>
              <p className="text-xs text-green-400 mt-1">
                +{customers.filter(c => new Date(c.createdAt).getMonth() === new Date().getMonth()).length} this month
              </p>
            </div>
            <Users className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Pipeline Value</p>
              <p className="text-2xl font-bold text-white">{formatCurrency(calculatePipelineValue())}</p>
              <p className="text-xs text-blue-400 mt-1">Weighted by probability</p>
            </div>
            <DollarSign className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Conversion Rate</p>
              <p className="text-2xl font-bold text-white">{calculateConversionRate().toFixed(1)}%</p>
              <p className="text-xs text-green-400 mt-1">Lead to customer</p>
            </div>
            <Target className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Deals</p>
              <p className="text-2xl font-bold text-white">
                {deals.filter(d => !['closed_won', 'closed_lost'].includes(d.stage)).length}
              </p>
              <p className="text-xs text-yellow-400 mt-1">In pipeline</p>
            </div>
            <TrendingUp className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
            { id: 'customers', label: 'Customers', icon: Users, count: customers.length },
            { id: 'deals', label: 'Deals', icon: DollarSign, count: deals.filter(d => !['closed_won', 'closed_lost'].includes(d.stage)).length },
            { id: 'interactions', label: 'Interactions', icon: MessageSquare, count: interactions.length },
            { id: 'campaigns', label: 'Campaigns', icon: Target, count: campaigns.filter(c => c.status === 'active').length }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'dashboard' && (
        <div className="space-y-6">
          {/* Sales Pipeline */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Sales Pipeline</h3>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {['qualification', 'proposal', 'negotiation', 'closed_won', 'closed_lost'].map((stage) => {
                const stageDeals = deals.filter(deal => deal.stage === stage)
                const stageValue = stageDeals.reduce((sum, deal) => sum + deal.value, 0)

                return (
                  <div key={stage} className="bg-gray-700 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-white capitalize">
                        {stage.replace('_', ' ')}
                      </h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${getDealStageColor(stage)}`}>
                        {stageDeals.length}
                      </span>
                    </div>
                    <p className="text-lg font-bold text-white">{formatCurrency(stageValue)}</p>
                    <div className="mt-2 space-y-1">
                      {stageDeals.slice(0, 3).map((deal) => (
                        <div key={deal.id} className="text-xs text-gray-400 truncate">
                          {deal.title} - {formatCurrency(deal.value)}
                        </div>
                      ))}
                      {stageDeals.length > 3 && (
                        <div className="text-xs text-gray-500">+{stageDeals.length - 3} more</div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Recent Interactions</h3>
              <div className="space-y-3">
                {interactions.slice(0, 5).map((interaction) => {
                  const customer = customers.find(c => c.id === interaction.customerId)
                  return (
                    <div key={interaction.id} className="flex items-start space-x-3 p-3 bg-gray-700 rounded">
                      <div className="flex-shrink-0">
                        {interaction.type === 'email' && <Mail className="text-blue-400" size={16} />}
                        {interaction.type === 'phone' && <Phone className="text-green-400" size={16} />}
                        {interaction.type === 'meeting' && <Calendar className="text-purple-400" size={16} />}
                        {interaction.type === 'note' && <FileText className="text-yellow-400" size={16} />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-white truncate">
                          {interaction.subject}
                        </p>
                        <p className="text-xs text-gray-400">
                          {customer ? `${customer.firstName} ${customer.lastName}` : 'Unknown Customer'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(interaction.createdAt)}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Top Customers</h3>
              <div className="space-y-3">
                {customers
                  .sort((a, b) => b.metrics.lifetimeValue - a.metrics.lifetimeValue)
                  .slice(0, 5)
                  .map((customer) => (
                    <div key={customer.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-white">
                            {customer.firstName} {customer.lastName}
                          </p>
                          <p className="text-xs text-gray-400">{customer.company}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-white">
                          {formatCurrency(customer.metrics.lifetimeValue)}
                        </p>
                        <p className="text-xs text-gray-400">
                          {customer.metrics.orderCount} orders
                        </p>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'customers' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search customers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="lead">Lead</option>
                  <option value="prospect">Prospect</option>
                  <option value="customer">Customer</option>
                  <option value="inactive">Inactive</option>
                  <option value="churned">Churned</option>
                </select>
              </div>
            </div>
          </div>

          {/* Customers List */}
          <div className="space-y-4">
            {filteredCustomers.map((customer) => (
              <motion.div
                key={customer.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-lg font-medium">
                        {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-white">
                          {customer.firstName} {customer.lastName}
                        </h3>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(customer.status)}`}>
                          {customer.status}
                        </span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStageColor(customer.lifecycle.stage)}`}>
                          {customer.lifecycle.stage}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                        <div>
                          <span className="text-gray-400">Company:</span>
                          <span className="text-white ml-1">{customer.company || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Email:</span>
                          <span className="text-white ml-1">{customer.email}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">LTV:</span>
                          <span className="text-white ml-1">{formatCurrency(customer.metrics.lifetimeValue)}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Score:</span>
                          <span className="text-white ml-1">{customer.lifecycle.score}/100</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Created: {formatDate(customer.createdAt)}</span>
                        {customer.lastContact && (
                          <span>Last Contact: {formatDate(customer.lastContact)}</span>
                        )}
                        <span>Assigned to: {customer.assignedTo}</span>
                      </div>

                      {customer.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-3">
                          {customer.tags.map((tag) => (
                            <span key={tag} className="inline-flex px-2 py-1 text-xs font-medium rounded bg-gray-700 text-gray-300">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Profile">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Contact">
                      <Mail size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'deals' || activeTab === 'interactions' || activeTab === 'campaigns') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'deals' && 'Sales Pipeline Management'}
            {activeTab === 'interactions' && 'Customer Interaction History'}
            {activeTab === 'campaigns' && 'Marketing Campaign Management'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'deals' && <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'interactions' && <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'campaigns' && <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'deals' && 'Advanced Deal Management'}
              {activeTab === 'interactions' && 'Comprehensive Interaction Tracking'}
              {activeTab === 'campaigns' && 'Integrated Campaign Management'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'deals' && 'Manage your sales pipeline with deal tracking, probability scoring, and revenue forecasting.'}
              {activeTab === 'interactions' && 'Track all customer interactions across email, phone, meetings, and social channels.'}
              {activeTab === 'campaigns' && 'Create and manage marketing campaigns with ROI tracking and performance analytics.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
