'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Settings, 
  Puzzle, 
  Code, 
  Layers,
  Plus,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  Download,
  Upload,
  Search,
  Filter,
  Grid,
  List,
  Workflow,
  Database,
  Palette,
  Cpu,
  Zap,
  Target,
  Activity,
  BarChart3,
  Users,
  FileText,
  Package,
  Globe,
  Shield,
  Key,
  Lock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  ArrowRight,
  Play,
  Pause,
  Save
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface CustomField {
  id: string
  name: string
  label: string
  description: string
  type: 'text' | 'number' | 'boolean' | 'date' | 'datetime' | 'select' | 'multiselect' | 'textarea' | 'file' | 'url' | 'email' | 'phone'
  entityType: 'user' | 'product' | 'order' | 'customer' | 'category' | 'custom'
  isRequired: boolean
  isUnique: boolean
  isSearchable: boolean
  defaultValue?: any
  validation: {
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
    pattern?: string
    options?: { label: string; value: any; color?: string }[]
  }
  display: {
    showInList: boolean
    showInDetail: boolean
    showInForm: boolean
    order: number
    width?: string
    helpText?: string
  }
  permissions: {
    read: string[]
    write: string[]
    admin: string[]
  }
  status: 'active' | 'inactive' | 'deprecated'
  createdAt: Date
  createdBy: string
  lastModified: Date
  usage: {
    recordsWithValue: number
    totalRecords: number
    popularValues: { value: any; count: number }[]
  }
}

interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: 'approval' | 'automation' | 'notification' | 'integration' | 'custom'
  trigger: WorkflowTrigger
  steps: WorkflowStep[]
  conditions: WorkflowCondition[]
  variables: WorkflowVariable[]
  settings: {
    isActive: boolean
    priority: 'low' | 'medium' | 'high' | 'critical'
    timeout: number
    retryPolicy: {
      maxRetries: number
      backoffStrategy: 'linear' | 'exponential'
      initialDelay: number
    }
    notifications: {
      onSuccess: boolean
      onFailure: boolean
      onTimeout: boolean
      recipients: string[]
    }
  }
  metrics: {
    totalExecutions: number
    successfulExecutions: number
    failedExecutions: number
    avgExecutionTime: number
    lastExecution?: Date
  }
  version: string
  isTemplate: boolean
  createdAt: Date
  createdBy: string
  lastModified: Date
}

interface WorkflowTrigger {
  type: 'manual' | 'scheduled' | 'event' | 'webhook' | 'api'
  configuration: {
    event?: string
    schedule?: string
    webhook?: { url: string; secret: string }
    conditions?: { field: string; operator: string; value: any }[]
  }
}

interface WorkflowStep {
  id: string
  name: string
  type: 'action' | 'condition' | 'approval' | 'delay' | 'notification' | 'integration'
  configuration: {
    action?: string
    parameters?: Record<string, any>
    condition?: { field: string; operator: string; value: any }
    approvers?: string[]
    delay?: number
    message?: string
    integration?: { service: string; endpoint: string; method: string }
  }
  position: { x: number; y: number }
  connections: { from: string; to: string; condition?: string }[]
  isActive: boolean
}

interface WorkflowCondition {
  id: string
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'starts_with' | 'ends_with' | 'in' | 'not_in'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

interface WorkflowVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  defaultValue?: any
  description: string
  isRequired: boolean
}

interface Plugin {
  id: string
  name: string
  description: string
  version: string
  author: string
  category: 'ui' | 'integration' | 'analytics' | 'automation' | 'security' | 'utility'
  type: 'component' | 'service' | 'middleware' | 'theme' | 'widget'
  status: 'active' | 'inactive' | 'error' | 'updating'
  permissions: string[]
  dependencies: { name: string; version: string; required: boolean }[]
  configuration: {
    settings: { key: string; type: string; value: any; description: string }[]
    endpoints: { path: string; method: string; description: string }[]
    hooks: { event: string; handler: string; priority: number }[]
  }
  installation: {
    installedAt: Date
    installedBy: string
    source: 'marketplace' | 'upload' | 'git' | 'local'
    sourceUrl?: string
    checksum: string
  }
  metrics: {
    usage: number
    performance: { avgResponseTime: number; errorRate: number }
    lastUsed: Date
    resourceUsage: { cpu: number; memory: number; storage: number }
  }
  manifest: {
    entryPoint: string
    assets: string[]
    permissions: string[]
    apiVersion: string
    minSystemVersion: string
  }
}

interface Theme {
  id: string
  name: string
  description: string
  type: 'light' | 'dark' | 'auto' | 'custom'
  isDefault: boolean
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    success: string
    warning: string
    error: string
    info: string
  }
  typography: {
    fontFamily: string
    headingFont: string
    fontSize: {
      xs: string
      sm: string
      base: string
      lg: string
      xl: string
      '2xl': string
      '3xl': string
    }
    fontWeight: {
      normal: number
      medium: number
      semibold: number
      bold: number
    }
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
    '2xl': string
  }
  borderRadius: {
    sm: string
    md: string
    lg: string
    xl: string
  }
  shadows: {
    sm: string
    md: string
    lg: string
    xl: string
  }
  customCSS?: string
  createdAt: Date
  createdBy: string
  usage: {
    activeUsers: number
    totalUsers: number
    lastUsed: Date
  }
}

interface CustomIntegration {
  id: string
  name: string
  description: string
  type: 'api' | 'webhook' | 'database' | 'file' | 'email' | 'sms' | 'custom'
  status: 'active' | 'inactive' | 'error' | 'testing'
  configuration: {
    endpoint?: string
    method?: string
    headers?: Record<string, string>
    authentication?: {
      type: 'none' | 'basic' | 'bearer' | 'api_key' | 'oauth'
      credentials?: Record<string, string>
    }
    parameters?: { name: string; type: string; required: boolean; description: string }[]
    mapping?: { source: string; target: string; transformation?: string }[]
  }
  triggers: {
    events: string[]
    conditions: { field: string; operator: string; value: any }[]
    schedule?: string
  }
  testing: {
    lastTest?: Date
    testResults?: { success: boolean; responseTime: number; error?: string }
    testData?: any
  }
  metrics: {
    totalCalls: number
    successfulCalls: number
    failedCalls: number
    avgResponseTime: number
    lastCall?: Date
  }
  createdAt: Date
  createdBy: string
  lastModified: Date
}

export default function CustomizationPage() {
  const [customFields, setCustomFields] = useState<CustomField[]>([])
  const [workflowTemplates, setWorkflowTemplates] = useState<WorkflowTemplate[]>([])
  const [plugins, setPlugins] = useState<Plugin[]>([])
  const [themes, setThemes] = useState<Theme[]>([])
  const [customIntegrations, setCustomIntegrations] = useState<CustomIntegration[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'fields' | 'workflows' | 'plugins' | 'themes' | 'integrations'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadCustomizationData()
  }, [])

  const loadCustomizationData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual customization API integration
      const mockCustomFields: CustomField[] = [
        {
          id: 'field_001',
          name: 'customer_tier',
          label: 'Customer Tier',
          description: 'Customer loyalty tier level',
          type: 'select',
          entityType: 'customer',
          isRequired: false,
          isUnique: false,
          isSearchable: true,
          defaultValue: 'bronze',
          validation: {
            options: [
              { label: 'Bronze', value: 'bronze', color: '#CD7F32' },
              { label: 'Silver', value: 'silver', color: '#C0C0C0' },
              { label: 'Gold', value: 'gold', color: '#FFD700' },
              { label: 'Platinum', value: 'platinum', color: '#E5E4E2' }
            ]
          },
          display: {
            showInList: true,
            showInDetail: true,
            showInForm: true,
            order: 1,
            width: '120px',
            helpText: 'Customer tier based on purchase history and engagement'
          },
          permissions: {
            read: ['admin', 'sales', 'support'],
            write: ['admin', 'sales'],
            admin: ['admin']
          },
          status: 'active',
          createdAt: new Date('2024-12-01'),
          createdBy: '<EMAIL>',
          lastModified: new Date(),
          usage: {
            recordsWithValue: 1250,
            totalRecords: 1500,
            popularValues: [
              { value: 'bronze', count: 800 },
              { value: 'silver', count: 300 },
              { value: 'gold', count: 120 },
              { value: 'platinum', count: 30 }
            ]
          }
        },
        {
          id: 'field_002',
          name: 'product_certification',
          label: 'Product Certification',
          description: 'Quality certification status for products',
          type: 'multiselect',
          entityType: 'product',
          isRequired: false,
          isUnique: false,
          isSearchable: true,
          validation: {
            options: [
              { label: 'ISO 9001', value: 'iso_9001' },
              { label: 'CE Marking', value: 'ce_marking' },
              { label: 'FCC Approved', value: 'fcc_approved' },
              { label: 'RoHS Compliant', value: 'rohs_compliant' }
            ]
          },
          display: {
            showInList: true,
            showInDetail: true,
            showInForm: true,
            order: 2,
            helpText: 'Select all applicable certifications'
          },
          permissions: {
            read: ['admin', 'product_manager', 'sales'],
            write: ['admin', 'product_manager'],
            admin: ['admin']
          },
          status: 'active',
          createdAt: new Date('2024-11-15'),
          createdBy: '<EMAIL>',
          lastModified: new Date(),
          usage: {
            recordsWithValue: 450,
            totalRecords: 500,
            popularValues: [
              { value: 'ce_marking', count: 400 },
              { value: 'rohs_compliant', count: 380 },
              { value: 'fcc_approved', count: 200 },
              { value: 'iso_9001', count: 150 }
            ]
          }
        }
      ]

      const mockWorkflowTemplates: WorkflowTemplate[] = [
        {
          id: 'workflow_001',
          name: 'Order Approval Workflow',
          description: 'Multi-step approval process for high-value orders',
          category: 'approval',
          trigger: {
            type: 'event',
            configuration: {
              event: 'order.created',
              conditions: [
                { field: 'order.total', operator: 'greater_than', value: 1000 }
              ]
            }
          },
          steps: [
            {
              id: 'step_001',
              name: 'Manager Approval',
              type: 'approval',
              configuration: {
                approvers: ['<EMAIL>'],
                message: 'Please review and approve this high-value order'
              },
              position: { x: 100, y: 100 },
              connections: [{ from: 'trigger', to: 'step_001' }],
              isActive: true
            },
            {
              id: 'step_002',
              name: 'Finance Review',
              type: 'approval',
              configuration: {
                approvers: ['<EMAIL>'],
                message: 'Please review financial aspects of this order'
              },
              position: { x: 300, y: 100 },
              connections: [{ from: 'step_001', to: 'step_002', condition: 'approved' }],
              isActive: true
            },
            {
              id: 'step_003',
              name: 'Send Confirmation',
              type: 'notification',
              configuration: {
                message: 'Your order has been approved and is being processed'
              },
              position: { x: 500, y: 100 },
              connections: [{ from: 'step_002', to: 'step_003', condition: 'approved' }],
              isActive: true
            }
          ],
          conditions: [],
          variables: [
            {
              name: 'order_total',
              type: 'number',
              description: 'Total order amount',
              isRequired: true
            },
            {
              name: 'customer_tier',
              type: 'string',
              description: 'Customer tier level',
              isRequired: false,
              defaultValue: 'bronze'
            }
          ],
          settings: {
            isActive: true,
            priority: 'high',
            timeout: 86400, // 24 hours
            retryPolicy: {
              maxRetries: 3,
              backoffStrategy: 'exponential',
              initialDelay: 300
            },
            notifications: {
              onSuccess: true,
              onFailure: true,
              onTimeout: true,
              recipients: ['<EMAIL>']
            }
          },
          metrics: {
            totalExecutions: 156,
            successfulExecutions: 148,
            failedExecutions: 8,
            avgExecutionTime: 3600,
            lastExecution: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          version: '1.2.0',
          isTemplate: true,
          createdAt: new Date('2024-11-01'),
          createdBy: '<EMAIL>',
          lastModified: new Date()
        }
      ]

      const mockPlugins: Plugin[] = [
        {
          id: 'plugin_001',
          name: 'Advanced Analytics Widget',
          description: 'Enhanced analytics dashboard with custom charts and KPIs',
          version: '2.1.0',
          author: 'Syndicaps Team',
          category: 'analytics',
          type: 'widget',
          status: 'active',
          permissions: ['analytics:read', 'dashboard:write'],
          dependencies: [
            { name: 'chart.js', version: '^4.0.0', required: true },
            { name: 'lodash', version: '^4.17.0', required: true }
          ],
          configuration: {
            settings: [
              {
                key: 'refresh_interval',
                type: 'number',
                value: 30,
                description: 'Data refresh interval in seconds'
              },
              {
                key: 'chart_type',
                type: 'select',
                value: 'line',
                description: 'Default chart type'
              }
            ],
            endpoints: [
              {
                path: '/api/plugins/analytics/data',
                method: 'GET',
                description: 'Fetch analytics data'
              }
            ],
            hooks: [
              {
                event: 'dashboard.load',
                handler: 'initializeWidget',
                priority: 10
              }
            ]
          },
          installation: {
            installedAt: new Date('2024-12-01'),
            installedBy: '<EMAIL>',
            source: 'marketplace',
            sourceUrl: 'https://marketplace.syndicaps.com/plugins/analytics-widget',
            checksum: 'sha256:abc123...'
          },
          metrics: {
            usage: 1250,
            performance: {
              avgResponseTime: 150,
              errorRate: 0.02
            },
            lastUsed: new Date(Date.now() - 30 * 60 * 1000),
            resourceUsage: {
              cpu: 2.5,
              memory: 45,
              storage: 12
            }
          },
          manifest: {
            entryPoint: 'dist/index.js',
            assets: ['styles.css', 'icons.svg'],
            permissions: ['analytics:read', 'dashboard:write'],
            apiVersion: '1.0',
            minSystemVersion: '2.0.0'
          }
        }
      ]

      const mockThemes: Theme[] = [
        {
          id: 'theme_001',
          name: 'Syndicaps Dark Pro',
          description: 'Professional dark theme optimized for extended use',
          type: 'dark',
          isDefault: true,
          colors: {
            primary: '#3B82F6',
            secondary: '#10B981',
            accent: '#F59E0B',
            background: '#111827',
            surface: '#1F2937',
            text: '#F9FAFB',
            textSecondary: '#9CA3AF',
            border: '#374151',
            success: '#10B981',
            warning: '#F59E0B',
            error: '#EF4444',
            info: '#3B82F6'
          },
          typography: {
            fontFamily: 'Inter, system-ui, sans-serif',
            headingFont: 'Inter, system-ui, sans-serif',
            fontSize: {
              xs: '0.75rem',
              sm: '0.875rem',
              base: '1rem',
              lg: '1.125rem',
              xl: '1.25rem',
              '2xl': '1.5rem',
              '3xl': '1.875rem'
            },
            fontWeight: {
              normal: 400,
              medium: 500,
              semibold: 600,
              bold: 700
            }
          },
          spacing: {
            xs: '0.25rem',
            sm: '0.5rem',
            md: '1rem',
            lg: '1.5rem',
            xl: '2rem',
            '2xl': '3rem'
          },
          borderRadius: {
            sm: '0.25rem',
            md: '0.375rem',
            lg: '0.5rem',
            xl: '0.75rem'
          },
          shadows: {
            sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
          },
          createdAt: new Date('2024-12-01'),
          createdBy: '<EMAIL>',
          usage: {
            activeUsers: 45,
            totalUsers: 50,
            lastUsed: new Date()
          }
        }
      ]

      const mockCustomIntegrations: CustomIntegration[] = [
        {
          id: 'integration_001',
          name: 'Inventory Sync',
          description: 'Real-time inventory synchronization with external warehouse system',
          type: 'api',
          status: 'active',
          configuration: {
            endpoint: 'https://warehouse.example.com/api/inventory',
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${api_key}'
            },
            authentication: {
              type: 'bearer',
              credentials: { token: 'warehouse_api_token' }
            },
            parameters: [
              { name: 'product_id', type: 'string', required: true, description: 'Product identifier' },
              { name: 'quantity', type: 'number', required: true, description: 'Stock quantity' },
              { name: 'location', type: 'string', required: false, description: 'Warehouse location' }
            ],
            mapping: [
              { source: 'product.id', target: 'product_id' },
              { source: 'inventory.quantity', target: 'quantity' },
              { source: 'inventory.location', target: 'location' }
            ]
          },
          triggers: {
            events: ['inventory.updated', 'product.created'],
            conditions: [
              { field: 'inventory.quantity', operator: 'greater_than', value: 0 }
            ]
          },
          testing: {
            lastTest: new Date(Date.now() - 24 * 60 * 60 * 1000),
            testResults: {
              success: true,
              responseTime: 250,
            },
            testData: {
              product_id: 'test_product_123',
              quantity: 100,
              location: 'warehouse_a'
            }
          },
          metrics: {
            totalCalls: 2450,
            successfulCalls: 2398,
            failedCalls: 52,
            avgResponseTime: 280,
            lastCall: new Date(Date.now() - 5 * 60 * 1000)
          },
          createdAt: new Date('2024-11-20'),
          createdBy: '<EMAIL>',
          lastModified: new Date()
        }
      ]

      setCustomFields(mockCustomFields)
      setWorkflowTemplates(mockWorkflowTemplates)
      setPlugins(mockPlugins)
      setThemes(mockThemes)
      setCustomIntegrations(mockCustomIntegrations)
    } catch (error) {
      console.error('Error loading customization data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-900/20'
      case 'inactive': case 'paused': return 'text-gray-400 bg-gray-900/20'
      case 'error': case 'failed': return 'text-red-400 bg-red-900/20'
      case 'testing': case 'updating': return 'text-blue-400 bg-blue-900/20'
      case 'deprecated': return 'text-yellow-400 bg-yellow-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'approval': return 'text-blue-400 bg-blue-900/20'
      case 'automation': return 'text-green-400 bg-green-900/20'
      case 'notification': return 'text-yellow-400 bg-yellow-900/20'
      case 'integration': return 'text-purple-400 bg-purple-900/20'
      case 'analytics': return 'text-pink-400 bg-pink-900/20'
      case 'security': return 'text-red-400 bg-red-900/20'
      case 'ui': return 'text-cyan-400 bg-cyan-900/20'
      case 'utility': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'text': case 'textarea': return 'text-blue-400 bg-blue-900/20'
      case 'number': return 'text-green-400 bg-green-900/20'
      case 'boolean': return 'text-purple-400 bg-purple-900/20'
      case 'date': case 'datetime': return 'text-yellow-400 bg-yellow-900/20'
      case 'select': case 'multiselect': return 'text-pink-400 bg-pink-900/20'
      case 'file': return 'text-orange-400 bg-orange-900/20'
      case 'email': case 'url': case 'phone': return 'text-cyan-400 bg-cyan-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Settings className="w-8 h-8 text-purple-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Advanced Customization Engine</h1>
            <p className="text-gray-400">Custom fields, workflow builder, plugin architecture, and extensible platform</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadCustomizationData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/customization/builder"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Custom
          </Link>
        </div>
      </div>

      {/* Customization Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Custom Fields</p>
              <p className="text-2xl font-bold text-white">
                {customFields.filter(f => f.status === 'active').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {customFields.filter(f => f.isRequired).length} required
              </p>
            </div>
            <Database className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Workflows</p>
              <p className="text-2xl font-bold text-white">
                {workflowTemplates.filter(w => w.settings.isActive).length}
              </p>
              <p className="text-xs text-blue-400 mt-1">
                {workflowTemplates.reduce((sum, w) => sum + w.metrics.totalExecutions, 0)} executions
              </p>
            </div>
            <Workflow className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Installed Plugins</p>
              <p className="text-2xl font-bold text-white">
                {plugins.filter(p => p.status === 'active').length}
              </p>
              <p className="text-xs text-purple-400 mt-1">
                {plugins.filter(p => p.status === 'error').length} errors
              </p>
            </div>
            <Puzzle className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Custom Integrations</p>
              <p className="text-2xl font-bold text-white">
                {customIntegrations.filter(i => i.status === 'active').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {formatPercentage(
                  customIntegrations.reduce((sum, i) => sum + (i.metrics.successfulCalls / i.metrics.totalCalls), 0) / customIntegrations.length * 100
                )} success
              </p>
            </div>
            <Zap className="text-green-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'fields', label: 'Custom Fields', icon: Database, count: customFields.length },
            { id: 'workflows', label: 'Workflows', icon: Workflow, count: workflowTemplates.length },
            { id: 'plugins', label: 'Plugins', icon: Puzzle, count: plugins.length },
            { id: 'themes', label: 'Themes', icon: Palette, count: themes.length },
            { id: 'integrations', label: 'Integrations', icon: Zap, count: customIntegrations.length }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Customization Statistics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Custom Fields Usage</h3>
              <div className="space-y-4">
                {customFields.slice(0, 5).map((field) => (
                  <div key={field.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTypeColor(field.type)}`}>
                        {field.type}
                      </span>
                      <div>
                        <p className="text-sm font-medium text-white">{field.label}</p>
                        <p className="text-xs text-gray-400">{field.entityType}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-white">
                        {formatPercentage((field.usage.recordsWithValue / field.usage.totalRecords) * 100)}
                      </p>
                      <p className="text-xs text-gray-400">
                        {field.usage.recordsWithValue}/{field.usage.totalRecords}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Workflow Performance</h3>
              <div className="space-y-4">
                {workflowTemplates.map((workflow) => (
                  <div key={workflow.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${getCategoryColor(workflow.category)}`}>
                        <Workflow size={16} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{workflow.name}</p>
                        <p className="text-xs text-gray-400">{workflow.category}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-white">
                        {formatPercentage((workflow.metrics.successfulExecutions / workflow.metrics.totalExecutions) * 100)}
                      </p>
                      <p className="text-xs text-gray-400">
                        {workflow.metrics.totalExecutions} runs
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Plugin Ecosystem</h3>
              <div className="space-y-3">
                {plugins.map((plugin) => (
                  <div key={plugin.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${getCategoryColor(plugin.category)}`}>
                        <Puzzle size={16} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{plugin.name}</p>
                        <p className="text-xs text-gray-400">v{plugin.version} • {plugin.category}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(plugin.status)}`}>
                        {plugin.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Integration Health</h3>
              <div className="space-y-3">
                {customIntegrations.map((integration) => (
                  <div key={integration.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${getStatusColor(integration.status)}`}>
                        <Zap size={16} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{integration.name}</p>
                        <p className="text-xs text-gray-400">{integration.type}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-white">
                        {formatPercentage((integration.metrics.successfulCalls / integration.metrics.totalCalls) * 100)}
                      </p>
                      <p className="text-xs text-gray-400">
                        {integration.metrics.avgResponseTime}ms avg
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link
                href="/admin/customization/fields/create"
                className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Database size={20} />
                <span>Create Custom Field</span>
              </Link>

              <Link
                href="/admin/customization/workflows/builder"
                className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Workflow size={20} />
                <span>Build Workflow</span>
              </Link>

              <Link
                href="/admin/customization/plugins/marketplace"
                className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Puzzle size={20} />
                <span>Browse Plugins</span>
              </Link>

              <Link
                href="/admin/customization/integrations/create"
                className="bg-yellow-600 hover:bg-yellow-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Zap size={20} />
                <span>Add Integration</span>
              </Link>
            </div>
          </div>

          {/* System Health */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Customization System Health</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-2">
                  {formatPercentage(
                    (customFields.filter(f => f.status === 'active').length / customFields.length) * 100
                  )}
                </div>
                <p className="text-sm text-gray-400">Custom Fields Active</p>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">
                  {formatPercentage(
                    workflowTemplates.length > 0
                      ? (workflowTemplates.reduce((sum, w) => sum + w.metrics.successfulExecutions, 0) /
                         workflowTemplates.reduce((sum, w) => sum + w.metrics.totalExecutions, 0)) * 100
                      : 0
                  )}
                </div>
                <p className="text-sm text-gray-400">Workflow Success Rate</p>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  {plugins.filter(p => p.status === 'active').length}
                </div>
                <p className="text-sm text-gray-400">Active Plugins</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'fields' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search custom fields..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Entity Types</option>
                  <option value="user">User</option>
                  <option value="product">Product</option>
                  <option value="order">Order</option>
                  <option value="customer">Customer</option>
                  <option value="category">Category</option>
                  <option value="custom">Custom</option>
                </select>

                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="deprecated">Deprecated</option>
                </select>
              </div>
            </div>
          </div>

          {/* Custom Fields List */}
          <div className="space-y-4">
            {customFields.map((field) => (
              <motion.div
                key={field.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{field.label}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(field.type)}`}>
                        {field.type}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(field.status)}`}>
                        {field.status}
                      </span>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-blue-300 bg-blue-900/20">
                        {field.entityType}
                      </span>
                      {field.isRequired && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-red-300 bg-red-900/20">
                          Required
                        </span>
                      )}
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{field.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Usage:</span>
                        <span className="text-white ml-1">
                          {formatPercentage((field.usage.recordsWithValue / field.usage.totalRecords) * 100)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Records:</span>
                        <span className="text-white ml-1">{field.usage.recordsWithValue}/{field.usage.totalRecords}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Searchable:</span>
                        <span className={`ml-1 ${field.isSearchable ? 'text-green-400' : 'text-gray-400'}`}>
                          {field.isSearchable ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Unique:</span>
                        <span className={`ml-1 ${field.isUnique ? 'text-green-400' : 'text-gray-400'}`}>
                          {field.isUnique ? 'Yes' : 'No'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Created: {field.createdAt.toLocaleDateString()}</span>
                      <span>By: {field.createdBy.split('@')[0]}</span>
                      <span>Modified: {field.lastModified.toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Usage Analytics">
                      <BarChart3 size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'workflows' || activeTab === 'plugins' || activeTab === 'themes' || activeTab === 'integrations') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'workflows' && 'Visual Workflow Builder'}
            {activeTab === 'plugins' && 'Plugin Marketplace & Management'}
            {activeTab === 'themes' && 'Theme Customization Engine'}
            {activeTab === 'integrations' && 'Custom Integration Builder'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'workflows' && <Workflow className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'plugins' && <Puzzle className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'themes' && <Palette className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'integrations' && <Zap className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'workflows' && 'Advanced Workflow Automation'}
              {activeTab === 'plugins' && 'Extensible Plugin Architecture'}
              {activeTab === 'themes' && 'Complete Theme Customization'}
              {activeTab === 'integrations' && 'Custom Integration Platform'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'workflows' && 'Create complex business workflows with visual drag-and-drop builder, approval chains, and automation rules.'}
              {activeTab === 'plugins' && 'Extend platform functionality with custom plugins, marketplace integration, and sandboxed execution environment.'}
              {activeTab === 'themes' && 'Design custom themes with advanced color schemes, typography, spacing, and CSS customization.'}
              {activeTab === 'integrations' && 'Build custom integrations with external systems using APIs, webhooks, and data transformation tools.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
