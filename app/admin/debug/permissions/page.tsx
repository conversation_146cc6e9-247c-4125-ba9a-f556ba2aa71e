'use client'

import React from 'react'
import { useUser } from '../../../../src/lib/useUser'
import { usePermissionCheck } from '../../../../src/admin/hooks/usePermissionFilteredNavigation'
import { ROLE_PERMISSIONS } from '../../../../src/admin/types/permissions'

export default function AdminPermissionsDebugPage() {
  const { user, profile, loading } = useUser()
  const { adminPermissions, adminRole } = usePermissionCheck()

  if (loading) {
    return <div className="p-6">Loading user data...</div>
  }

  return (
    <div className="p-6 space-y-6">
      <div className="bg-gray-800 p-6 rounded-lg">
        <h1 className="text-2xl font-bold text-white mb-4">Admin Permissions Debug</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-lg font-semibold text-white mb-3">User Information</h2>
            <div className="space-y-2 text-sm">
              <div><span className="text-gray-400">Email:</span> <span className="text-white">{user?.email || 'Not logged in'}</span></div>
              <div><span className="text-gray-400">Profile Role:</span> <span className="text-white">{profile?.role || 'No profile role'}</span></div>
              <div><span className="text-gray-400">Mapped Admin Role:</span> <span className="text-white">{adminRole || 'No admin role'}</span></div>
              <div><span className="text-gray-400">Admin Permissions Count:</span> <span className="text-white">{adminPermissions.length}</span></div>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold text-white mb-3">Permission Check</h2>
            <div className="space-y-2 text-sm">
              <div><span className="text-gray-400">Has Inventory Read:</span> 
                <span className={adminPermissions.some(p => p.resource === 'inventory' && p.actions.includes('read')) ? 'text-green-400' : 'text-red-400'}>
                  {adminPermissions.some(p => p.resource === 'inventory' && p.actions.includes('read')) ? 'Yes' : 'No'}
                </span>
              </div>
              <div><span className="text-gray-400">Has Root Access:</span> 
                <span className={adminPermissions.some(p => p.actions.includes('root')) ? 'text-green-400' : 'text-red-400'}>
                  {adminPermissions.some(p => p.actions.includes('root')) ? 'Yes' : 'No'}
                </span>
              </div>
              <div><span className="text-gray-400">Has Admin Management:</span> 
                <span className={adminPermissions.some(p => p.resource === 'admin_management') ? 'text-green-400' : 'text-red-400'}>
                  {adminPermissions.some(p => p.resource === 'admin_management') ? 'Yes' : 'No'}
                </span>
              </div>
              <div><span className="text-gray-400">Has Security Access:</span> 
                <span className={adminPermissions.some(p => p.resource === 'security') ? 'text-green-400' : 'text-red-400'}>
                  {adminPermissions.some(p => p.resource === 'security') ? 'Yes' : 'No'}
                </span>
              </div>
              <div><span className="text-gray-400">Has Backup Control:</span> 
                <span className={adminPermissions.some(p => p.resource === 'backups' && (p.actions.includes('backup') || p.actions.includes('restore'))) ? 'text-green-400' : 'text-red-400'}>
                  {adminPermissions.some(p => p.resource === 'backups' && (p.actions.includes('backup') || p.actions.includes('restore'))) ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-800 p-6 rounded-lg">
        <h2 className="text-lg font-semibold text-white mb-3">Current Admin Permissions</h2>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-2 text-gray-400">Resource</th>
                <th className="text-left py-2 text-gray-400">Actions</th>
                <th className="text-left py-2 text-gray-400">Scope</th>
              </tr>
            </thead>
            <tbody>
              {adminPermissions.map((permission, index) => (
                <tr key={index} className="border-b border-gray-700">
                  <td className="py-2 text-white">{permission.resource}</td>
                  <td className="py-2 text-green-400">{permission.actions.join(', ')}</td>
                  <td className="py-2 text-gray-300">{permission.scope || 'all'}</td>
                </tr>
              ))}
            </tbody>
          </table>
          {adminPermissions.length === 0 && (
            <div className="text-center py-4 text-gray-400">No admin permissions found</div>
          )}
        </div>
      </div>

      <div className="bg-gray-800 p-6 rounded-lg">
        <h2 className="text-lg font-semibold text-white mb-3">Available Roles & Permissions</h2>
        <div className="space-y-4">
          {Object.entries(ROLE_PERMISSIONS).map(([role, permissions]) => (
            <div key={role} className="border border-gray-700 rounded p-4">
              <h3 className="font-medium text-white mb-2">{role.toUpperCase()}</h3>
              <div className="text-xs text-gray-400">
                {permissions.map(p => `${p.resource}:${p.actions.join(',')}`).join(' | ')}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}