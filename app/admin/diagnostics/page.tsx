/**
 * Admin Diagnostics Page
 * 
 * Comprehensive diagnostic page for troubleshooting admin panel issues,
 * including Firebase connectivity, authentication, and API endpoints.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React from 'react';
import AdminLayout from '../../../src/admin/components/layout/AdminLayout';
import FirebaseDiagnostics from '../../../src/admin/components/diagnostics/FirebaseDiagnostics';
import SecurityMonitoringDashboard from '../../../src/admin/components/security/SecurityMonitoringDashboard';
import AdminCard from '../../../src/admin/components/common/AdminCard';
import { 
  Database, 
  Shield, 
  Activity, 
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

const AdminDiagnosticsPage: React.FC = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">System Diagnostics</h1>
            <p className="text-gray-400 mt-2">
              Comprehensive system health checks and troubleshooting tools
            </p>
          </div>
          
          <div className="flex items-center space-x-2 px-3 py-2 bg-blue-600/20 border border-blue-600/30 rounded-lg">
            <Info className="w-4 h-4 text-blue-400" />
            <span className="text-sm text-blue-300">Development Mode</span>
          </div>
        </div>

        {/* Quick Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AdminCard className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-600/20 rounded-lg">
                <Database className="w-6 h-6 text-green-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Firebase</h3>
                <p className="text-sm text-gray-400">Database connectivity</p>
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-600/20 rounded-lg">
                <Shield className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Security</h3>
                <p className="text-sm text-gray-400">Authentication & permissions</p>
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-600/20 rounded-lg">
                <Activity className="w-6 h-6 text-purple-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Performance</h3>
                <p className="text-sm text-gray-400">System performance metrics</p>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Common Issues & Solutions */}
        <AdminCard className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Common Issues & Solutions</h2>
          
          <div className="space-y-4">
            <div className="p-4 bg-red-900/20 border border-red-600/30 rounded-lg">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-red-300">
                    SyntaxError: Unexpected token '&lt;', "&lt;!DOCTYPE "... is not valid JSON
                  </h3>
                  <p className="text-sm text-red-200 mt-1">
                    This error occurs when an API call receives HTML instead of JSON.
                  </p>
                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-red-300 mb-2">Possible Causes:</h4>
                    <ul className="text-sm text-red-200 space-y-1">
                      <li>• Authentication redirect intercepting API calls</li>
                      <li>• Firebase configuration issues</li>
                      <li>• Middleware redirecting to error pages</li>
                      <li>• Network connectivity problems</li>
                      <li>• API routes returning HTML error pages</li>
                    </ul>
                  </div>
                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-red-300 mb-2">Solutions:</h4>
                    <ul className="text-sm text-red-200 space-y-1">
                      <li>• Check browser Network tab for failed requests</li>
                      <li>• Verify Firebase configuration in .env.local</li>
                      <li>• Ensure user is properly authenticated</li>
                      <li>• Check API routes return JSON responses</li>
                      <li>• Review middleware for unexpected redirects</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-4 bg-green-900/20 border border-green-600/30 rounded-lg">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-green-300">
                    System Running Normally
                  </h3>
                  <p className="text-sm text-green-200 mt-1">
                    All systems are operational and functioning correctly.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </AdminCard>

        {/* Firebase Diagnostics */}
        <FirebaseDiagnostics />

        {/* Security Monitoring */}
        <SecurityMonitoringDashboard />

        {/* Environment Information */}
        <AdminCard className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Environment Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Application</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Environment:</span>
                  <span className="text-white">{process.env.NODE_ENV}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Next.js Version:</span>
                  <span className="text-white">14.x</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">React Version:</span>
                  <span className="text-white">18.x</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-white mb-3">Firebase</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Project ID:</span>
                  <span className="text-white font-mono text-xs">
                    {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'Not configured'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Auth Domain:</span>
                  <span className="text-white font-mono text-xs">
                    {process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'Not configured'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">API Key:</span>
                  <span className="text-white font-mono text-xs">
                    {process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '••••••••' : 'Not configured'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </AdminCard>

        {/* Browser Information */}
        <AdminCard className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Browser Information</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">User Agent:</span>
              <span className="text-white font-mono text-xs max-w-md truncate">
                {typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Online Status:</span>
              <span className="text-white">
                {typeof navigator !== 'undefined' ? (navigator.onLine ? 'Online' : 'Offline') : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Local Storage:</span>
              <span className="text-white">
                {typeof window !== 'undefined' && 'localStorage' in window ? 'Supported' : 'Not supported'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">IndexedDB:</span>
              <span className="text-white">
                {typeof window !== 'undefined' && 'indexedDB' in window ? 'Supported' : 'Not supported'}
              </span>
            </div>
          </div>
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default AdminDiagnosticsPage;
