'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Building2, 
  Network, 
  Handshake, 
  Globe,
  Database,
  Cloud,
  Zap,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  RefreshCw,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Users,
  Target,
  BarChart3,
  TrendingUp,
  TrendingDown,
  ArrowRight,
  Package,
  Shield,
  Key,
  Lock,
  Activity,
  Monitor,
  Server,
  Layers,
  Link as LinkIcon,
  ExternalLink,
  FileText,
  Calendar,
  DollarSign,
  Percent
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface ERPIntegration {
  id: string
  name: string
  provider: 'sap' | 'oracle' | 'microsoft_dynamics' | 'netsuite' | 'workday' | 'sage' | 'epicor' | 'infor' | 'custom'
  type: 'financial' | 'inventory' | 'hr' | 'crm' | 'supply_chain' | 'manufacturing' | 'full_suite'
  status: 'active' | 'inactive' | 'configuring' | 'error' | 'testing'
  version: string
  monitoring: {
    uptime: number
    avgResponseTime: number
    errorRate: number
    dataQuality: number
    lastHealthCheck: Date
  }
  costs: {
    setup: number
    monthly: number
    perTransaction: number
    support: number
  }
  createdAt: Date
  lastModified: Date
}

interface PartnershipProgram {
  id: string
  name: string
  type: 'technology' | 'channel' | 'strategic' | 'marketplace' | 'reseller' | 'integration'
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'strategic'
  status: 'active' | 'pending' | 'suspended' | 'terminated'
  partner: {
    company: string
    contact: {
      name: string
      email: string
      phone: string
      role: string
    }
    website: string
    industry: string
    size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
    headquarters: string
  }
  performance: {
    revenue: {
      total: number
      thisMonth: number
      lastMonth: number
      growth: number
    }
    customers: {
      referred: number
      converted: number
      conversionRate: number
      lifetime_value: number
    }
    engagement: {
      lastActivity: Date
      meetingsThisQuarter: number
      supportTickets: number
      satisfaction: number
    }
  }
  createdAt: Date
  lastReviewed: Date
}

interface EnterpriseConnector {
  id: string
  name: string
  category: 'erp' | 'crm' | 'accounting' | 'inventory' | 'hr' | 'marketing' | 'analytics' | 'communication'
  provider: string
  description: string
  status: 'available' | 'installed' | 'configuring' | 'error' | 'deprecated'
  version: string
  usage: {
    installations: number
    activeUsers: number
    dataTransferred: number
    apiCalls: number
    lastUsed: Date
  }
  marketplace: {
    rating: number
    reviews: number
    downloads: number
    featured: boolean
    verified: boolean
  }
}

interface IntegrationMetrics {
  timeRange: '24h' | '7d' | '30d' | '90d'
  overview: {
    totalIntegrations: number
    activeIntegrations: number
    dataTransferred: number
    apiCalls: number
    errorRate: number
    uptime: number
  }
  erp: {
    connections: number
    syncSuccess: number
    dataQuality: number
    avgResponseTime: number
  }
  partnerships: {
    activePartners: number
    revenue: number
    referrals: number
    satisfaction: number
  }
  performance: {
    topIntegrations: { name: string; usage: number; performance: number }[]
    errorsByType: Record<string, number>
    responseTimeByService: Record<string, number>
    dataQualityTrends: { date: Date; score: number }[]
  }
  costs: {
    totalCost: number
    costPerIntegration: number
    roi: number
    savings: number
  }
}

export default function EnterpriseIntegrationsPage() {
  const [erpIntegrations, setErpIntegrations] = useState<ERPIntegration[]>([])
  const [partnershipPrograms, setPartnershipPrograms] = useState<PartnershipProgram[]>([])
  const [enterpriseConnectors, setEnterpriseConnectors] = useState<EnterpriseConnector[]>([])
  const [integrationMetrics, setIntegrationMetrics] = useState<IntegrationMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'erp' | 'partnerships' | 'connectors' | 'marketplace'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadEnterpriseIntegrationsData()
  }, [])

  const loadEnterpriseIntegrationsData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual enterprise integrations API
      const mockERPIntegrations: ERPIntegration[] = [
        {
          id: 'erp_001',
          name: 'SAP S/4HANA Integration',
          provider: 'sap',
          type: 'full_suite',
          status: 'active',
          version: '2023.1',
          monitoring: {
            uptime: 99.8,
            avgResponseTime: 420,
            errorRate: 0.5,
            dataQuality: 98.2,
            lastHealthCheck: new Date(Date.now() - 15 * 60 * 1000)
          },
          costs: {
            setup: 45000,
            monthly: 8500,
            perTransaction: 0.15,
            support: 2500
          },
          createdAt: new Date('2024-10-01'),
          lastModified: new Date()
        },
        {
          id: 'erp_002',
          name: 'Microsoft Dynamics 365',
          provider: 'microsoft_dynamics',
          type: 'financial',
          status: 'active',
          version: '10.0.38',
          monitoring: {
            uptime: 99.5,
            avgResponseTime: 510,
            errorRate: 1.2,
            dataQuality: 94.8,
            lastHealthCheck: new Date(Date.now() - 30 * 60 * 1000)
          },
          costs: {
            setup: 25000,
            monthly: 4200,
            perTransaction: 0.08,
            support: 1200
          },
          createdAt: new Date('2024-11-01'),
          lastModified: new Date()
        }
      ]

      const mockPartnershipPrograms: PartnershipProgram[] = [
        {
          id: 'partner_001',
          name: 'TechFlow Solutions Partnership',
          type: 'technology',
          tier: 'gold',
          status: 'active',
          partner: {
            company: 'TechFlow Solutions Inc.',
            contact: {
              name: 'Sarah Chen',
              email: '<EMAIL>',
              phone: '******-0123',
              role: 'Partnership Manager'
            },
            website: 'https://techflow.com',
            industry: 'Software Development',
            size: 'medium',
            headquarters: 'San Francisco, CA'
          },
          performance: {
            revenue: {
              total: 125000,
              thisMonth: 18500,
              lastMonth: 16200,
              growth: 14.2
            },
            customers: {
              referred: 45,
              converted: 32,
              conversionRate: 71.1,
              lifetime_value: 3900
            },
            engagement: {
              lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
              meetingsThisQuarter: 6,
              supportTickets: 3,
              satisfaction: 4.7
            }
          },
          createdAt: new Date('2024-08-15'),
          lastReviewed: new Date('2024-12-01')
        }
      ]

      const mockEnterpriseConnectors: EnterpriseConnector[] = [
        {
          id: 'connector_001',
          name: 'Salesforce CRM Connector',
          category: 'crm',
          provider: 'Salesforce',
          description: 'Bi-directional sync with Salesforce CRM for customer data and sales pipeline management',
          status: 'installed',
          version: '3.2.1',
          usage: {
            installations: 1250,
            activeUsers: 890,
            dataTransferred: 45.6,
            apiCalls: 125000,
            lastUsed: new Date(Date.now() - 30 * 60 * 1000)
          },
          marketplace: {
            rating: 4.8,
            reviews: 234,
            downloads: 15600,
            featured: true,
            verified: true
          }
        },
        {
          id: 'connector_002',
          name: 'QuickBooks Accounting Sync',
          category: 'accounting',
          provider: 'Intuit',
          description: 'Real-time financial data synchronization with QuickBooks Online and Desktop',
          status: 'available',
          version: '2.1.5',
          usage: {
            installations: 890,
            activeUsers: 650,
            dataTransferred: 23.4,
            apiCalls: 89000,
            lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          marketplace: {
            rating: 4.6,
            reviews: 156,
            downloads: 12400,
            featured: false,
            verified: true
          }
        }
      ]

      const mockIntegrationMetrics: IntegrationMetrics = {
        timeRange: '30d',
        overview: {
          totalIntegrations: 24,
          activeIntegrations: 18,
          dataTransferred: 2.4,
          apiCalls: 1250000,
          errorRate: 0.8,
          uptime: 99.7
        },
        erp: {
          connections: 8,
          syncSuccess: 97.5,
          dataQuality: 94.2,
          avgResponseTime: 450
        },
        partnerships: {
          activePartners: 12,
          revenue: 485000,
          referrals: 156,
          satisfaction: 4.6
        },
        performance: {
          topIntegrations: [
            { name: 'SAP S/4HANA', usage: 95, performance: 98 },
            { name: 'Microsoft Dynamics 365', usage: 87, performance: 94 },
            { name: 'Oracle NetSuite', usage: 78, performance: 92 }
          ],
          errorsByType: {
            'Authentication': 45,
            'Rate Limit': 23,
            'Data Validation': 18,
            'Network': 12,
            'Configuration': 8
          },
          responseTimeByService: {
            'SAP': 420,
            'Oracle': 380,
            'Microsoft': 510,
            'Salesforce': 290
          },
          dataQualityTrends: [
            { date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), score: 92 },
            { date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), score: 94 },
            { date: new Date(), score: 96 }
          ]
        },
        costs: {
          totalCost: 125000,
          costPerIntegration: 5200,
          roi: 340,
          savings: 890000
        }
      }

      setErpIntegrations(mockERPIntegrations)
      setPartnershipPrograms(mockPartnershipPrograms)
      setEnterpriseConnectors(mockEnterpriseConnectors)
      setIntegrationMetrics(mockIntegrationMetrics)
    } catch (error) {
      console.error('Error loading enterprise integrations data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Building2 className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Enterprise Integrations & Partnerships</h1>
            <p className="text-gray-400">ERP systems, enterprise software connections, and strategic partnership management</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadEnterpriseIntegrationsData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/enterprise-integrations/marketplace"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Browse Marketplace
          </Link>
        </div>
      </div>
    </div>
  )
}
