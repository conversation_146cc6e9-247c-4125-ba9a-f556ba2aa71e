'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Zap, 
  Activity, 
  GitBranch, 
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Play,
  Pause,
  Settings,
  Plus,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  BarChart3,
  Database,
  Globe,
  MessageSquare,
  Mail,
  Webhook,
  Code,
  Target,
  Users,
  ShoppingCart,
  FileText
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface EventStream {
  id: string
  name: string
  description: string
  source: 'user_actions' | 'system' | 'api' | 'webhook' | 'scheduled' | 'external'
  status: 'active' | 'paused' | 'error' | 'stopped'
  eventTypes: string[]
  schema: {
    version: string
    fields: { name: string; type: string; required: boolean; description: string }[]
  }
  processors: EventProcessor[]
  metrics: {
    eventsToday: number
    totalEvents: number
    avgProcessingTime: number
    errorRate: number
    throughput: number // events per second
    lastEvent: Date
  }
  configuration: {
    batchSize: number
    maxRetries: number
    deadLetterQueue: boolean
    partitioning: {
      enabled: boolean
      key?: string
      partitions?: number
    }
  }
  createdAt: Date
  createdBy: string
}

interface EventProcessor {
  id: string
  name: string
  description: string
  type: 'filter' | 'transform' | 'enrich' | 'route' | 'aggregate' | 'custom'
  isActive: boolean
  order: number
  configuration: Record<string, any>
  conditions?: EventCondition[]
  actions: EventAction[]
  metrics: {
    processed: number
    filtered: number
    errors: number
    avgLatency: number
  }
}

interface EventCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'regex'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

interface EventAction {
  type: 'webhook' | 'email' | 'database' | 'queue' | 'api_call' | 'notification' | 'custom'
  configuration: Record<string, any>
  retryPolicy: {
    maxRetries: number
    backoffStrategy: 'linear' | 'exponential'
    initialDelay: number
  }
}

interface EventRule {
  id: string
  name: string
  description: string
  eventType: string
  isActive: boolean
  priority: 'low' | 'medium' | 'high' | 'critical'
  conditions: EventCondition[]
  actions: EventAction[]
  schedule?: {
    type: 'immediate' | 'delayed' | 'scheduled'
    delay?: number // minutes
    cron?: string
  }
  metrics: {
    triggered: number
    successful: number
    failed: number
    lastTriggered?: Date
  }
  createdAt: Date
  createdBy: string
}

interface EventLog {
  id: string
  streamId: string
  eventType: string
  eventData: Record<string, any>
  status: 'processing' | 'completed' | 'failed' | 'retrying'
  processingSteps: {
    processorId: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    startTime: Date
    endTime?: Date
    error?: string
    output?: Record<string, any>
  }[]
  metadata: {
    source: string
    timestamp: Date
    correlationId?: string
    userId?: string
    sessionId?: string
  }
  retryCount: number
  createdAt: Date
}

interface EventMetrics {
  timeRange: '1h' | '24h' | '7d' | '30d'
  totalEvents: number
  successfulEvents: number
  failedEvents: number
  avgProcessingTime: number
  peakThroughput: number
  errorsByType: Record<string, number>
  eventsBySource: Record<string, number>
  processingLatency: { timestamp: Date; latency: number }[]
}

export default function EventProcessingPage() {
  const [eventStreams, setEventStreams] = useState<EventStream[]>([])
  const [eventRules, setEventRules] = useState<EventRule[]>([])
  const [eventLogs, setEventLogs] = useState<EventLog[]>([])
  const [eventMetrics, setEventMetrics] = useState<EventMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'streams' | 'rules' | 'logs' | 'metrics'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterSource, setFilterSource] = useState<string>('all')

  useEffect(() => {
    loadEventData()
  }, [])

  const loadEventData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual event processing API integration
      const mockEventStreams: EventStream[] = [
        {
          id: 'stream_001',
          name: 'User Activity Stream',
          description: 'Real-time user actions and interactions',
          source: 'user_actions',
          status: 'active',
          eventTypes: ['user_login', 'user_logout', 'page_view', 'button_click', 'form_submit'],
          schema: {
            version: '1.0',
            fields: [
              { name: 'user_id', type: 'string', required: true, description: 'Unique user identifier' },
              { name: 'event_type', type: 'string', required: true, description: 'Type of user action' },
              { name: 'timestamp', type: 'datetime', required: true, description: 'Event timestamp' },
              { name: 'properties', type: 'object', required: false, description: 'Additional event properties' },
              { name: 'session_id', type: 'string', required: false, description: 'User session identifier' }
            ]
          },
          processors: [
            {
              id: 'proc_001',
              name: 'User Enrichment',
              description: 'Enrich events with user profile data',
              type: 'enrich',
              isActive: true,
              order: 1,
              configuration: {
                enrichment_source: 'user_database',
                fields_to_add: ['user_tier', 'registration_date', 'total_orders']
              },
              actions: [],
              metrics: {
                processed: 15420,
                filtered: 0,
                errors: 23,
                avgLatency: 45
              }
            },
            {
              id: 'proc_002',
              name: 'Analytics Router',
              description: 'Route events to analytics platforms',
              type: 'route',
              isActive: true,
              order: 2,
              configuration: {
                routes: [
                  { destination: 'google_analytics', events: ['page_view'] },
                  { destination: 'mixpanel', events: ['user_login', 'button_click'] }
                ]
              },
              actions: [],
              metrics: {
                processed: 15397,
                filtered: 0,
                errors: 5,
                avgLatency: 120
              }
            }
          ],
          metrics: {
            eventsToday: 15420,
            totalEvents: 2450000,
            avgProcessingTime: 165,
            errorRate: 0.18,
            throughput: 12.5,
            lastEvent: new Date(Date.now() - 30 * 1000)
          },
          configuration: {
            batchSize: 100,
            maxRetries: 3,
            deadLetterQueue: true,
            partitioning: {
              enabled: true,
              key: 'user_id',
              partitions: 8
            }
          },
          createdAt: new Date('2025-01-01'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'stream_002',
          name: 'E-commerce Events',
          description: 'Order processing and transaction events',
          source: 'api',
          status: 'active',
          eventTypes: ['order_created', 'payment_processed', 'order_shipped', 'order_delivered', 'refund_issued'],
          schema: {
            version: '1.0',
            fields: [
              { name: 'order_id', type: 'string', required: true, description: 'Unique order identifier' },
              { name: 'user_id', type: 'string', required: true, description: 'Customer identifier' },
              { name: 'event_type', type: 'string', required: true, description: 'Order event type' },
              { name: 'amount', type: 'decimal', required: false, description: 'Transaction amount' },
              { name: 'timestamp', type: 'datetime', required: true, description: 'Event timestamp' }
            ]
          },
          processors: [
            {
              id: 'proc_003',
              name: 'Order Validation',
              description: 'Validate order data and business rules',
              type: 'filter',
              isActive: true,
              order: 1,
              configuration: {
                validation_rules: ['amount_positive', 'user_exists', 'inventory_available']
              },
              actions: [],
              metrics: {
                processed: 2340,
                filtered: 45,
                errors: 12,
                avgLatency: 89
              }
            }
          ],
          metrics: {
            eventsToday: 2340,
            totalEvents: 450000,
            avgProcessingTime: 89,
            errorRate: 0.51,
            throughput: 2.1,
            lastEvent: new Date(Date.now() - 2 * 60 * 1000)
          },
          configuration: {
            batchSize: 50,
            maxRetries: 5,
            deadLetterQueue: true,
            partitioning: {
              enabled: false
            }
          },
          createdAt: new Date('2025-01-03'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'stream_003',
          name: 'System Monitoring Events',
          description: 'System health and performance monitoring',
          source: 'system',
          status: 'active',
          eventTypes: ['cpu_usage', 'memory_usage', 'disk_usage', 'error_occurred', 'service_restart'],
          schema: {
            version: '1.0',
            fields: [
              { name: 'service_name', type: 'string', required: true, description: 'Service identifier' },
              { name: 'metric_name', type: 'string', required: true, description: 'Metric being reported' },
              { name: 'value', type: 'number', required: true, description: 'Metric value' },
              { name: 'unit', type: 'string', required: false, description: 'Metric unit' },
              { name: 'timestamp', type: 'datetime', required: true, description: 'Measurement timestamp' }
            ]
          },
          processors: [
            {
              id: 'proc_004',
              name: 'Threshold Monitor',
              description: 'Monitor metrics against defined thresholds',
              type: 'filter',
              isActive: true,
              order: 1,
              configuration: {
                thresholds: {
                  cpu_usage: { warning: 70, critical: 85 },
                  memory_usage: { warning: 80, critical: 90 },
                  disk_usage: { warning: 85, critical: 95 }
                }
              },
              conditions: [
                { field: 'value', operator: 'greater_than', value: 70 }
              ],
              actions: [
                {
                  type: 'notification',
                  configuration: {
                    channels: ['email', 'slack'],
                    recipients: ['<EMAIL>']
                  },
                  retryPolicy: {
                    maxRetries: 3,
                    backoffStrategy: 'exponential',
                    initialDelay: 60
                  }
                }
              ],
              metrics: {
                processed: 8900,
                filtered: 234,
                errors: 5,
                avgLatency: 25
              }
            }
          ],
          metrics: {
            eventsToday: 8900,
            totalEvents: 1200000,
            avgProcessingTime: 25,
            errorRate: 0.06,
            throughput: 8.2,
            lastEvent: new Date(Date.now() - 10 * 1000)
          },
          configuration: {
            batchSize: 200,
            maxRetries: 2,
            deadLetterQueue: true,
            partitioning: {
              enabled: true,
              key: 'service_name',
              partitions: 4
            }
          },
          createdAt: new Date('2024-12-20'),
          createdBy: '<EMAIL>'
        }
      ]

      const mockEventRules: EventRule[] = [
        {
          id: 'rule_001',
          name: 'High-Value Order Alert',
          description: 'Alert when orders exceed $500',
          eventType: 'order_created',
          isActive: true,
          priority: 'high',
          conditions: [
            { field: 'amount', operator: 'greater_than', value: 500 }
          ],
          actions: [
            {
              type: 'email',
              configuration: {
                to: ['<EMAIL>'],
                subject: 'High-Value Order Alert',
                template: 'high_value_order'
              },
              retryPolicy: {
                maxRetries: 3,
                backoffStrategy: 'linear',
                initialDelay: 60
              }
            },
            {
              type: 'webhook',
              configuration: {
                url: 'https://api.syndicaps.com/webhooks/high-value-order',
                method: 'POST'
              },
              retryPolicy: {
                maxRetries: 5,
                backoffStrategy: 'exponential',
                initialDelay: 30
              }
            }
          ],
          schedule: {
            type: 'immediate'
          },
          metrics: {
            triggered: 45,
            successful: 44,
            failed: 1,
            lastTriggered: new Date(Date.now() - 3 * 60 * 60 * 1000)
          },
          createdAt: new Date('2025-01-05'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'rule_002',
          name: 'User Onboarding Sequence',
          description: 'Trigger onboarding emails for new users',
          eventType: 'user_login',
          isActive: true,
          priority: 'medium',
          conditions: [
            { field: 'properties.is_first_login', operator: 'equals', value: true }
          ],
          actions: [
            {
              type: 'email',
              configuration: {
                template: 'welcome_sequence',
                delay_minutes: 0
              },
              retryPolicy: {
                maxRetries: 3,
                backoffStrategy: 'linear',
                initialDelay: 300
              }
            }
          ],
          schedule: {
            type: 'delayed',
            delay: 5
          },
          metrics: {
            triggered: 234,
            successful: 230,
            failed: 4,
            lastTriggered: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          createdAt: new Date('2025-01-01'),
          createdBy: '<EMAIL>'
        }
      ]

      const mockEventLogs: EventLog[] = [
        {
          id: 'log_001',
          streamId: 'stream_001',
          eventType: 'user_login',
          eventData: {
            user_id: 'user_12345',
            timestamp: new Date(),
            properties: {
              ip_address: '*************',
              user_agent: 'Mozilla/5.0...',
              is_first_login: false
            }
          },
          status: 'completed',
          processingSteps: [
            {
              processorId: 'proc_001',
              status: 'completed',
              startTime: new Date(Date.now() - 5 * 60 * 1000),
              endTime: new Date(Date.now() - 5 * 60 * 1000 + 45 * 1000),
              output: {
                user_tier: 'premium',
                registration_date: '2024-06-15',
                total_orders: 12
              }
            },
            {
              processorId: 'proc_002',
              status: 'completed',
              startTime: new Date(Date.now() - 4 * 60 * 1000),
              endTime: new Date(Date.now() - 4 * 60 * 1000 + 120 * 1000)
            }
          ],
          metadata: {
            source: 'web_app',
            timestamp: new Date(Date.now() - 5 * 60 * 1000),
            correlationId: 'corr_12345',
            sessionId: 'sess_67890'
          },
          retryCount: 0,
          createdAt: new Date(Date.now() - 5 * 60 * 1000)
        }
      ]

      const mockEventMetrics: EventMetrics = {
        timeRange: '24h',
        totalEvents: 26660,
        successfulEvents: 26580,
        failedEvents: 80,
        avgProcessingTime: 126,
        peakThroughput: 45.2,
        errorsByType: {
          'validation_error': 35,
          'timeout_error': 25,
          'network_error': 15,
          'processing_error': 5
        },
        eventsBySource: {
          'user_actions': 15420,
          'api': 2340,
          'system': 8900
        },
        processingLatency: Array.from({ length: 24 }, (_, i) => ({
          timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000),
          latency: Math.random() * 200 + 50
        }))
      }

      setEventStreams(mockEventStreams)
      setEventRules(mockEventRules)
      setEventLogs(mockEventLogs)
      setEventMetrics(mockEventMetrics)
    } catch (error) {
      console.error('Error loading event data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredEventStreams = eventStreams.filter(stream => {
    const matchesSearch = stream.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         stream.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' || stream.status === filterStatus
    const matchesSource = filterSource === 'all' || stream.source === filterSource
    return matchesSearch && matchesStatus && matchesSource
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-900/20'
      case 'paused': return 'text-yellow-400 bg-yellow-900/20'
      case 'error': return 'text-red-400 bg-red-900/20'
      case 'stopped': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'user_actions': return 'text-blue-400 bg-blue-900/20'
      case 'system': return 'text-purple-400 bg-purple-900/20'
      case 'api': return 'text-green-400 bg-green-900/20'
      case 'webhook': return 'text-yellow-400 bg-yellow-900/20'
      case 'scheduled': return 'text-orange-400 bg-orange-900/20'
      case 'external': return 'text-pink-400 bg-pink-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'user_actions': return <Users size={16} />
      case 'system': return <Database size={16} />
      case 'api': return <Code size={16} />
      case 'webhook': return <Webhook size={16} />
      case 'scheduled': return <Clock size={16} />
      case 'external': return <Globe size={16} />
      default: return <Activity size={16} />
    }
  }

  const toggleStreamStatus = async (streamId: string) => {
    setEventStreams(prev => prev.map(stream => 
      stream.id === streamId 
        ? { 
            ...stream, 
            status: stream.status === 'active' ? 'paused' : 'active' 
          }
        : stream
    ))
  }

  const toggleRuleStatus = async (ruleId: string) => {
    setEventRules(prev => prev.map(rule => 
      rule.id === ruleId 
        ? { ...rule, isActive: !rule.isActive }
        : rule
    ))
  }

  const formatThroughput = (throughput: number) => {
    if (throughput < 1) return `${(throughput * 1000).toFixed(0)} events/min`
    return `${throughput.toFixed(1)} events/sec`
  }

  const formatLatency = (latency: number) => {
    return `${latency.toFixed(0)}ms`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Zap className="w-8 h-8 text-yellow-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Event Processing System</h1>
            <p className="text-gray-400">Advanced event-driven architecture, real-time processing, and automation</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadEventData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/events/streams/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Stream
          </Link>
        </div>
      </div>

      {/* Event Processing Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Streams</p>
              <p className="text-2xl font-bold text-white">
                {eventStreams.filter(s => s.status === 'active').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {eventStreams.filter(s => s.status === 'error').length} errors
              </p>
            </div>
            <Activity className="text-yellow-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Events Today</p>
              <p className="text-2xl font-bold text-white">
                {eventMetrics ? eventMetrics.totalEvents.toLocaleString() : '0'}
              </p>
              <p className="text-xs text-blue-400 mt-1">Across all streams</p>
            </div>
            <BarChart3 className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-2xl font-bold text-white">
                {eventMetrics
                  ? ((eventMetrics.successfulEvents / eventMetrics.totalEvents) * 100).toFixed(1)
                  : 0}%
              </p>
              <p className="text-xs text-green-400 mt-1">Last 24 hours</p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Peak Throughput</p>
              <p className="text-2xl font-bold text-white">
                {eventMetrics ? formatThroughput(eventMetrics.peakThroughput) : '0'}
              </p>
              <p className="text-xs text-purple-400 mt-1">Events per second</p>
            </div>
            <Zap className="text-purple-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'streams', label: 'Event Streams', icon: Activity, count: eventStreams.length },
            { id: 'rules', label: 'Processing Rules', icon: GitBranch, count: eventRules.length },
            { id: 'logs', label: 'Event Logs', icon: FileText, count: eventLogs.length },
            { id: 'metrics', label: 'Metrics', icon: Target }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Event Streams Status */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Event Streams Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {eventStreams.map((stream) => (
                <div key={stream.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className={`p-1 rounded ${getSourceColor(stream.source)}`}>
                        {getSourceIcon(stream.source)}
                      </div>
                      <h4 className="text-sm font-medium text-white">{stream.name}</h4>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(stream.status)}`}>
                      {stream.status}
                    </span>
                  </div>

                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Events Today:</span>
                      <span className="text-white">{stream.metrics.eventsToday.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Throughput:</span>
                      <span className="text-white">{formatThroughput(stream.metrics.throughput)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Avg Latency:</span>
                      <span className="text-white">{formatLatency(stream.metrics.avgProcessingTime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Error Rate:</span>
                      <span className={`${stream.metrics.errorRate > 1 ? 'text-red-400' : 'text-green-400'}`}>
                        {stream.metrics.errorRate.toFixed(2)}%
                      </span>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-600">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => toggleStreamStatus(stream.id)}
                        className={`flex-1 py-1 px-2 rounded text-xs ${
                          stream.status === 'active'
                            ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                            : 'bg-green-600 hover:bg-green-700 text-white'
                        }`}
                      >
                        {stream.status === 'active' ? 'Pause' : 'Start'}
                      </button>
                      <button className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-2 rounded text-xs">
                        View
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Processing Rules */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Active Processing Rules</h3>
            <div className="space-y-3">
              {eventRules.slice(0, 5).map((rule) => (
                <div key={rule.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded ${getPriorityColor(rule.priority)}`}>
                      <GitBranch size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">{rule.name}</p>
                      <p className="text-xs text-gray-400">
                        {rule.eventType} • {rule.priority} priority
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="text-right text-xs">
                      <p className="text-white">{rule.metrics.triggered} triggered</p>
                      <p className="text-gray-400">
                        {((rule.metrics.successful / rule.metrics.triggered) * 100).toFixed(1)}% success
                      </p>
                    </div>
                    <button
                      onClick={() => toggleRuleStatus(rule.id)}
                      className={`p-1 rounded ${
                        rule.isActive
                          ? 'bg-green-600 hover:bg-green-700'
                          : 'bg-gray-600 hover:bg-gray-500'
                      } text-white`}
                    >
                      {rule.isActive ? <CheckCircle size={12} /> : <XCircle size={12} />}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Event Sources Breakdown */}
          {eventMetrics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Events by Source</h3>
                <div className="space-y-3">
                  {Object.entries(eventMetrics.eventsBySource).map(([source, count]) => (
                    <div key={source} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`p-1 rounded ${getSourceColor(source)}`}>
                          {getSourceIcon(source)}
                        </div>
                        <span className="text-sm text-white capitalize">{source.replace('_', ' ')}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium text-white">{count.toLocaleString()}</span>
                        <span className="text-xs text-gray-400 ml-2">
                          ({((count / eventMetrics.totalEvents) * 100).toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Error Breakdown</h3>
                <div className="space-y-3">
                  {Object.entries(eventMetrics.errorsByType).map(([errorType, count]) => (
                    <div key={errorType} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="text-red-400" size={16} />
                        <span className="text-sm text-white capitalize">{errorType.replace('_', ' ')}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium text-white">{count}</span>
                        <span className="text-xs text-gray-400 ml-2">
                          ({((count / eventMetrics.failedEvents) * 100).toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'streams' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search event streams..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="paused">Paused</option>
                  <option value="error">Error</option>
                  <option value="stopped">Stopped</option>
                </select>

                <select
                  value={filterSource}
                  onChange={(e) => setFilterSource(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Sources</option>
                  <option value="user_actions">User Actions</option>
                  <option value="system">System</option>
                  <option value="api">API</option>
                  <option value="webhook">Webhook</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="external">External</option>
                </select>
              </div>
            </div>
          </div>

          {/* Event Streams List */}
          <div className="space-y-4">
            {filteredEventStreams.map((stream) => (
              <motion.div
                key={stream.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`p-2 rounded ${getSourceColor(stream.source)}`}>
                        {getSourceIcon(stream.source)}
                      </div>
                      <h3 className="text-lg font-semibold text-white">{stream.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(stream.status)}`}>
                        {stream.status}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSourceColor(stream.source)}`}>
                        {stream.source.replace('_', ' ')}
                      </span>
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{stream.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Events Today:</span>
                        <span className="text-white ml-1">{stream.metrics.eventsToday.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Throughput:</span>
                        <span className="text-white ml-1">{formatThroughput(stream.metrics.throughput)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Avg Latency:</span>
                        <span className="text-white ml-1">{formatLatency(stream.metrics.avgProcessingTime)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Error Rate:</span>
                        <span className={`ml-1 ${stream.metrics.errorRate > 1 ? 'text-red-400' : 'text-green-400'}`}>
                          {stream.metrics.errorRate.toFixed(2)}%
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Created: {stream.createdAt.toLocaleDateString()}</span>
                      <span>Processors: {stream.processors.length}</span>
                      <span>Event Types: {stream.eventTypes.length}</span>
                      <span>Last Event: {stream.metrics.lastEvent.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => toggleStreamStatus(stream.id)}
                      className={`p-2 rounded ${
                        stream.status === 'active'
                          ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                      title={stream.status === 'active' ? 'Pause' : 'Start'}
                    >
                      {stream.status === 'active' ? <Pause size={16} /> : <Play size={16} />}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'rules' || activeTab === 'logs' || activeTab === 'metrics') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'rules' && 'Event Processing Rules'}
            {activeTab === 'logs' && 'Event Processing Logs'}
            {activeTab === 'metrics' && 'Event Processing Metrics'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'rules' && <GitBranch className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'logs' && <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'metrics' && <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'rules' && 'Advanced Event Processing Rules'}
              {activeTab === 'logs' && 'Comprehensive Event Logging'}
              {activeTab === 'metrics' && 'Real-time Event Metrics'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'rules' && 'Create and manage complex event processing rules with conditions, actions, and automated responses.'}
              {activeTab === 'logs' && 'Monitor event processing with detailed logs, error tracking, and performance analysis.'}
              {activeTab === 'metrics' && 'Analyze event processing performance with real-time metrics, trends, and insights.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
