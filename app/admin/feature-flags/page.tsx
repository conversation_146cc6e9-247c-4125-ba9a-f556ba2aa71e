'use client'

import React, { useState } from 'react'
import { 
  Flag, 
  Plus, 
  Edit, 
  Trash2, 
  Toggle,
  Users,
  Percent,
  Calendar,
  Eye,
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  Filter,
  Download,
  Upload
} from 'lucide-react'

interface FeatureFlag {
  id: string
  name: string
  key: string
  description: string
  status: 'active' | 'inactive' | 'scheduled'
  type: 'boolean' | 'percentage' | 'user_targeting' | 'variant'
  value: boolean | number | string
  targetingRules: TargetingRule[]
  rolloutPercentage: number
  environment: 'development' | 'staging' | 'production'
  createdBy: string
  createdAt: Date
  updatedAt: Date
  scheduledAt?: Date
  analytics: {
    impressions: number
    conversions: number
    conversionRate: number
  }
}

interface TargetingRule {
  id: string
  type: 'user_id' | 'email' | 'user_group' | 'location' | 'device'
  operator: 'equals' | 'contains' | 'starts_with' | 'in_list'
  value: string | string[]
  enabled: boolean
}

export default function FeatureFlagsPage() {
  const [selectedTab, setSelectedTab] = useState<'flags' | 'environments' | 'analytics' | 'history'>('flags')
  const [selectedEnvironment, setSelectedEnvironment] = useState<'development' | 'staging' | 'production'>('production')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [filterStatus, setFilterStatus] = useState<string>('all')

  // Mock feature flags data
  const featureFlags: FeatureFlag[] = [
    {
      id: '1',
      name: 'New Dashboard UI',
      key: 'new_dashboard_ui',
      description: 'Enable the redesigned admin dashboard interface',
      status: 'active',
      type: 'percentage',
      value: 75,
      targetingRules: [],
      rolloutPercentage: 75,
      environment: 'production',
      createdBy: '<EMAIL>',
      createdAt: new Date(Date.now() - 2592000000),
      updatedAt: new Date(Date.now() - 86400000),
      analytics: {
        impressions: 15420,
        conversions: 12336,
        conversionRate: 80.0
      }
    },
    {
      id: '2',
      name: 'Enhanced Search',
      key: 'enhanced_search_v2',
      description: 'New search algorithm with AI-powered suggestions',
      status: 'inactive',
      type: 'boolean',
      value: false,
      targetingRules: [
        {
          id: '1',
          type: 'user_group',
          operator: 'in_list',
          value: ['beta_testers', 'premium_users'],
          enabled: true
        }
      ],
      rolloutPercentage: 0,
      environment: 'production',
      createdBy: '<EMAIL>',
      createdAt: new Date(Date.now() - 1296000000),
      updatedAt: new Date(Date.now() - 3600000),
      analytics: {
        impressions: 0,
        conversions: 0,
        conversionRate: 0
      }
    },
    {
      id: '3',
      name: 'Mobile App Redesign',
      key: 'mobile_app_v3',
      description: 'Complete mobile application interface overhaul',
      status: 'scheduled',
      type: 'boolean',
      value: true,
      targetingRules: [],
      rolloutPercentage: 100,
      environment: 'production',
      createdBy: '<EMAIL>',
      createdAt: new Date(Date.now() - 604800000),
      updatedAt: new Date(Date.now() - 86400000),
      scheduledAt: new Date(Date.now() + 172800000),
      analytics: {
        impressions: 0,
        conversions: 0,
        conversionRate: 0
      }
    },
    {
      id: '4',
      name: 'Real-time Notifications',
      key: 'realtime_notifications',
      description: 'Push notifications with WebSocket integration',
      status: 'active',
      type: 'user_targeting',
      value: true,
      targetingRules: [
        {
          id: '2',
          type: 'email',
          operator: 'contains',
          value: '@syndicaps.com',
          enabled: true
        }
      ],
      rolloutPercentage: 50,
      environment: 'production',
      createdBy: '<EMAIL>',
      createdAt: new Date(Date.now() - 1728000000),
      updatedAt: new Date(Date.now() - 7200000),
      analytics: {
        impressions: 8934,
        conversions: 7125,
        conversionRate: 79.8
      }
    }
  ]

  const getStatusIcon = (status: string) => {
    const icons = {
      active: CheckCircle,
      inactive: XCircle,
      scheduled: Clock
    }
    return icons[status as keyof typeof icons] || Clock
  }

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'text-green-400',
      inactive: 'text-red-400',
      scheduled: 'text-yellow-400'
    }
    return colors[status as keyof typeof colors] || 'text-gray-400'
  }

  const getTypeColor = (type: string) => {
    const colors = {
      boolean: 'bg-blue-900 text-blue-300',
      percentage: 'bg-green-900 text-green-300',
      user_targeting: 'bg-purple-900 text-purple-300',
      variant: 'bg-orange-900 text-orange-300'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-900 text-gray-300'
  }

  const toggleFlag = (flagId: string) => {
    // Toggle flag status
    console.log('Toggling flag:', flagId)
  }

  const filteredFlags = featureFlags.filter(flag => 
    (filterStatus === 'all' || flag.status === filterStatus) &&
    flag.environment === selectedEnvironment
  )

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Feature Flags</h1>
            <p className="text-gray-400">Manage feature rollouts, A/B tests, and configuration toggles</p>
          </div>
          <div className="flex space-x-3">
            <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
              <Download size={18} />
              <span>Export Config</span>
            </button>
            <button 
              onClick={() => setShowCreateModal(true)}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              <Plus size={18} />
              <span>Create Flag</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Flags</p>
              <p className="text-2xl font-bold text-blue-400">{featureFlags.length}</p>
            </div>
            <Flag className="text-blue-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Across all environments</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Flags</p>
              <p className="text-2xl font-bold text-green-400">
                {featureFlags.filter(f => f.status === 'active').length}
              </p>
            </div>
            <CheckCircle className="text-green-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Currently enabled</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Conversion</p>
              <p className="text-2xl font-bold text-purple-400">79.9%</p>
            </div>
            <Target className="text-purple-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Across active flags</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Scheduled</p>
              <p className="text-2xl font-bold text-yellow-400">
                {featureFlags.filter(f => f.status === 'scheduled').length}
              </p>
            </div>
            <Clock className="text-yellow-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Pending deployment</p>
        </div>
      </div>

      {/* Environment Selector & Filters */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-gray-300 text-sm">Environment:</span>
            <select
              value={selectedEnvironment}
              onChange={(e) => setSelectedEnvironment(e.target.value as any)}
              className="bg-gray-700 text-white rounded px-3 py-1 text-sm focus:ring-2 focus:ring-red-500 focus:outline-none"
            >
              <option value="production">Production</option>
              <option value="staging">Staging</option>
              <option value="development">Development</option>
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="bg-gray-700 text-white rounded px-3 py-1 text-sm focus:ring-2 focus:ring-red-500 focus:outline-none"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="scheduled">Scheduled</option>
            </select>
          </div>
        </div>
        <div className="text-sm text-gray-400">
          {filteredFlags.length} of {featureFlags.length} flags
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'flags', label: 'Feature Flags', icon: Flag },
              { id: 'environments', label: 'Environments', icon: Activity },
              { id: 'analytics', label: 'Analytics', icon: Target },
              { id: 'history', label: 'Change History', icon: Clock }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Feature Flags Tab */}
      {selectedTab === 'flags' && (
        <div className="space-y-4">
          {filteredFlags.map((flag) => {
            const StatusIcon = getStatusIcon(flag.status)
            return (
              <div key={flag.id} className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-3">
                      <h3 className="text-lg font-semibold text-white">{flag.name}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getTypeColor(flag.type)}`}>
                        {flag.type.replace('_', ' ').toUpperCase()}
                      </span>
                      <div className="flex items-center space-x-2">
                        <StatusIcon className={getStatusColor(flag.status)} size={16} />
                        <span className={`text-sm font-medium ${getStatusColor(flag.status)}`}>
                          {flag.status.charAt(0).toUpperCase() + flag.status.slice(1)}
                        </span>
                      </div>
                    </div>
                    <p className="text-gray-400 mb-4">{flag.description}</p>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Key:</span>
                        <code className="text-blue-400 ml-2 bg-gray-900 px-2 py-1 rounded">{flag.key}</code>
                      </div>
                      <div>
                        <span className="text-gray-500">Rollout:</span>
                        <span className="text-white ml-2">{flag.rolloutPercentage}%</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Impressions:</span>
                        <span className="text-white ml-2">{flag.analytics.impressions.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Conversion:</span>
                        <span className="text-white ml-2">{flag.analytics.conversionRate}%</span>
                      </div>
                    </div>
                    {flag.targetingRules.length > 0 && (
                      <div className="mt-4">
                        <span className="text-gray-500 text-sm">Targeting Rules:</span>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {flag.targetingRules.map((rule) => (
                            <span 
                              key={rule.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300"
                            >
                              {rule.type}: {rule.operator} {Array.isArray(rule.value) ? rule.value.join(', ') : rule.value}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-3 ml-6">
                    <button
                      onClick={() => toggleFlag(flag.id)}
                      className={`p-2 rounded-lg transition-colors ${
                        flag.status === 'active' 
                          ? 'bg-green-600 hover:bg-green-700 text-white' 
                          : 'bg-gray-600 hover:bg-gray-700 text-white'
                      }`}
                      title={flag.status === 'active' ? 'Disable flag' : 'Enable flag'}
                    >
                      <Toggle size={18} />
                    </button>
                    <button className="text-blue-400 hover:text-blue-300 p-2" title="View analytics">
                      <Eye size={18} />
                    </button>
                    <button className="text-yellow-400 hover:text-yellow-300 p-2" title="Edit flag">
                      <Edit size={18} />
                    </button>
                    <button className="text-red-400 hover:text-red-300 p-2" title="Delete flag">
                      <Trash2 size={18} />
                    </button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Environments Tab */}
      {selectedTab === 'environments' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {['development', 'staging', 'production'].map((env) => (
            <div key={env} className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4 capitalize">{env}</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Flags:</span>
                  <span className="text-white">
                    {featureFlags.filter(f => f.environment === env).length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Active:</span>
                  <span className="text-green-400">
                    {featureFlags.filter(f => f.environment === env && f.status === 'active').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Inactive:</span>
                  <span className="text-red-400">
                    {featureFlags.filter(f => f.environment === env && f.status === 'inactive').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Scheduled:</span>
                  <span className="text-yellow-400">
                    {featureFlags.filter(f => f.environment === env && f.status === 'scheduled').length}
                  </span>
                </div>
              </div>
              <button className="w-full mt-4 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                Manage {env}
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {selectedTab === 'analytics' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Flag Performance</h3>
            <div className="space-y-4">
              {featureFlags
                .filter(f => f.status === 'active' && f.analytics.impressions > 0)
                .map((flag) => (
                  <div key={flag.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div>
                      <h4 className="text-white font-medium">{flag.name}</h4>
                      <p className="text-gray-400 text-sm">{flag.analytics.impressions.toLocaleString()} impressions</p>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-bold">{flag.analytics.conversionRate}%</div>
                      <div className="text-gray-400 text-sm">conversion</div>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Rollout Status</h3>
            <div className="space-y-4">
              {featureFlags.map((flag) => (
                <div key={flag.id} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white font-medium">{flag.name}</span>
                    <span className="text-gray-400">{flag.rolloutPercentage}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        flag.status === 'active' ? 'bg-green-600' :
                        flag.status === 'scheduled' ? 'bg-yellow-600' :
                        'bg-gray-600'
                      }`}
                      style={{ width: `${flag.rolloutPercentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Change History Tab */}
      {selectedTab === 'history' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Changes</h3>
          <div className="space-y-4">
            {[
              {
                id: '1',
                action: 'Flag Updated',
                flag: 'New Dashboard UI',
                user: '<EMAIL>',
                details: 'Increased rollout percentage from 50% to 75%',
                timestamp: new Date(Date.now() - 3600000)
              },
              {
                id: '2',
                action: 'Flag Created',
                flag: 'Mobile App Redesign',
                user: '<EMAIL>',
                details: 'Scheduled deployment for next week',
                timestamp: new Date(Date.now() - 86400000)
              },
              {
                id: '3',
                action: 'Flag Disabled',
                flag: 'Enhanced Search',
                user: '<EMAIL>',
                details: 'Temporarily disabled due to performance issues',
                timestamp: new Date(Date.now() - 172800000)
              }
            ].map((change) => (
              <div key={change.id} className="flex items-start space-x-4 p-4 border border-gray-700 rounded-lg">
                <div className="w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-medium">{change.action}: {change.flag}</h4>
                    <span className="text-gray-400 text-sm">{change.timestamp.toLocaleString()}</span>
                  </div>
                  <p className="text-gray-300 text-sm">{change.details}</p>
                  <p className="text-gray-500 text-xs mt-1">by {change.user}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Create Flag Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Create Feature Flag</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Flag Name</label>
                <input 
                  type="text" 
                  placeholder="e.g., New User Onboarding"
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Flag Key</label>
                <input 
                  type="text" 
                  placeholder="e.g., new_user_onboarding"
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea 
                  rows={3}
                  placeholder="Describe what this flag controls..."
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Type</label>
                  <select className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none">
                    <option value="boolean">Boolean</option>
                    <option value="percentage">Percentage</option>
                    <option value="user_targeting">User Targeting</option>
                    <option value="variant">Variant</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Environment</label>
                  <select className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none">
                    <option value="development">Development</option>
                    <option value="staging">Staging</option>
                    <option value="production">Production</option>
                  </select>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                Create Flag
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}