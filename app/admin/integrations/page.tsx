'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Plug, 
  Globe, 
  Key, 
  Webhook,
  Settings,
  Plus,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Activity,
  BarChart3,
  Shield,
  Zap,
  Database,
  Mail,
  CreditCard,
  MessageSquare,
  Search,
  Filter,
  Download,
  Upload
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface Integration {
  id: string
  name: string
  description: string
  category: 'payment' | 'shipping' | 'analytics' | 'marketing' | 'communication' | 'storage' | 'custom'
  provider: string
  status: 'connected' | 'disconnected' | 'error' | 'pending'
  version: string
  apiEndpoint: string
  authentication: {
    type: 'api_key' | 'oauth' | 'basic' | 'bearer'
    configured: boolean
    lastVerified?: Date
  }
  webhooks: {
    enabled: boolean
    endpoint?: string
    events: string[]
    secret?: string
  }
  rateLimit: {
    requests: number
    period: 'minute' | 'hour' | 'day'
    remaining: number
    resetAt: Date
  }
  metrics: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    avgResponseTime: number
    uptime: number
  }
  configuration: Record<string, any>
  createdAt: Date
  lastSync?: Date
  lastError?: {
    message: string
    timestamp: Date
    code?: string
  }
}

interface WebhookEvent {
  id: string
  integrationId: string
  event: string
  payload: Record<string, any>
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying'
  attempts: number
  maxAttempts: number
  receivedAt: Date
  processedAt?: Date
  error?: string
  responseTime?: number
}

interface APIKey {
  id: string
  name: string
  key: string
  permissions: string[]
  isActive: boolean
  expiresAt?: Date
  lastUsed?: Date
  usageCount: number
  createdBy: string
  createdAt: Date
}

export default function IntegrationsPage() {
  const [integrations, setIntegrations] = useState<Integration[]>([])
  const [webhookEvents, setWebhookEvents] = useState<WebhookEvent[]>([])
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'integrations' | 'webhooks' | 'api-keys' | 'analytics'>('integrations')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadIntegrationsData()
  }, [])

  const loadIntegrationsData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockIntegrations: Integration[] = [
        {
          id: 'int_001',
          name: 'Stripe Payment Gateway',
          description: 'Process payments and manage subscriptions',
          category: 'payment',
          provider: 'Stripe',
          status: 'connected',
          version: 'v2023-10-16',
          apiEndpoint: 'https://api.stripe.com/v1',
          authentication: {
            type: 'api_key',
            configured: true,
            lastVerified: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          webhooks: {
            enabled: true,
            endpoint: 'https://api.syndicaps.com/webhooks/stripe',
            events: ['payment_intent.succeeded', 'payment_intent.payment_failed', 'customer.subscription.updated'],
            secret: 'whsec_***'
          },
          rateLimit: {
            requests: 100,
            period: 'minute',
            remaining: 87,
            resetAt: new Date(Date.now() + 45 * 60 * 1000)
          },
          metrics: {
            totalRequests: 15420,
            successfulRequests: 15234,
            failedRequests: 186,
            avgResponseTime: 245,
            uptime: 99.8
          },
          configuration: {
            publishableKey: 'pk_live_***',
            secretKey: 'sk_live_***',
            webhookSecret: 'whsec_***'
          },
          createdAt: new Date('2025-01-01'),
          lastSync: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: 'int_002',
          name: 'SendGrid Email Service',
          description: 'Transactional and marketing email delivery',
          category: 'communication',
          provider: 'SendGrid',
          status: 'connected',
          version: 'v3',
          apiEndpoint: 'https://api.sendgrid.com/v3',
          authentication: {
            type: 'api_key',
            configured: true,
            lastVerified: new Date(Date.now() - 1 * 60 * 60 * 1000)
          },
          webhooks: {
            enabled: true,
            endpoint: 'https://api.syndicaps.com/webhooks/sendgrid',
            events: ['delivered', 'bounce', 'open', 'click'],
            secret: 'sg_webhook_***'
          },
          rateLimit: {
            requests: 600,
            period: 'minute',
            remaining: 543,
            resetAt: new Date(Date.now() + 30 * 60 * 1000)
          },
          metrics: {
            totalRequests: 8750,
            successfulRequests: 8698,
            failedRequests: 52,
            avgResponseTime: 180,
            uptime: 99.9
          },
          configuration: {
            apiKey: 'SG.***',
            fromEmail: '<EMAIL>',
            fromName: 'Syndicaps'
          },
          createdAt: new Date('2025-01-03'),
          lastSync: new Date(Date.now() - 15 * 60 * 1000)
        },
        {
          id: 'int_003',
          name: 'Google Analytics',
          description: 'Website analytics and user behavior tracking',
          category: 'analytics',
          provider: 'Google',
          status: 'error',
          version: 'GA4',
          apiEndpoint: 'https://analyticsreporting.googleapis.com/v4',
          authentication: {
            type: 'oauth',
            configured: true,
            lastVerified: new Date(Date.now() - 24 * 60 * 60 * 1000)
          },
          webhooks: {
            enabled: false,
            events: []
          },
          rateLimit: {
            requests: 100,
            period: 'minute',
            remaining: 100,
            resetAt: new Date(Date.now() + 60 * 60 * 1000)
          },
          metrics: {
            totalRequests: 2340,
            successfulRequests: 2156,
            failedRequests: 184,
            avgResponseTime: 890,
            uptime: 92.1
          },
          configuration: {
            propertyId: 'GA_MEASUREMENT_ID',
            clientId: 'google_client_id',
            clientSecret: 'google_client_secret'
          },
          createdAt: new Date('2025-01-05'),
          lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
          lastError: {
            message: 'OAuth token expired',
            timestamp: new Date(Date.now() - 30 * 60 * 1000),
            code: 'OAUTH_TOKEN_EXPIRED'
          }
        }
      ]

      const mockWebhookEvents: WebhookEvent[] = [
        {
          id: 'wh_001',
          integrationId: 'int_001',
          event: 'payment_intent.succeeded',
          payload: {
            id: 'pi_3OqJ2K2eZvKYlo2C0123456789',
            amount: 2999,
            currency: 'usd',
            status: 'succeeded'
          },
          status: 'completed',
          attempts: 1,
          maxAttempts: 3,
          receivedAt: new Date(Date.now() - 30 * 60 * 1000),
          processedAt: new Date(Date.now() - 30 * 60 * 1000 + 500),
          responseTime: 245
        },
        {
          id: 'wh_002',
          integrationId: 'int_002',
          event: 'delivered',
          payload: {
            email: '<EMAIL>',
            timestamp: Date.now() - 15 * 60 * 1000,
            sg_event_id: 'sendgrid_event_123',
            sg_message_id: 'message_456'
          },
          status: 'completed',
          attempts: 1,
          maxAttempts: 3,
          receivedAt: new Date(Date.now() - 15 * 60 * 1000),
          processedAt: new Date(Date.now() - 15 * 60 * 1000 + 180),
          responseTime: 180
        },
        {
          id: 'wh_003',
          integrationId: 'int_001',
          event: 'payment_intent.payment_failed',
          payload: {
            id: 'pi_3OqJ2K2eZvKYlo2C0987654321',
            amount: 4999,
            currency: 'usd',
            status: 'requires_payment_method',
            last_payment_error: {
              code: 'card_declined',
              message: 'Your card was declined.'
            }
          },
          status: 'failed',
          attempts: 3,
          maxAttempts: 3,
          receivedAt: new Date(Date.now() - 45 * 60 * 1000),
          error: 'Processing timeout after 30 seconds'
        }
      ]

      const mockApiKeys: APIKey[] = [
        {
          id: 'key_001',
          name: 'Production API Key',
          key: 'sk_live_***************************',
          permissions: ['read', 'write', 'admin'],
          isActive: true,
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
          lastUsed: new Date(Date.now() - 5 * 60 * 1000),
          usageCount: 15420,
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-01')
        },
        {
          id: 'key_002',
          name: 'Mobile App API Key',
          key: 'sk_mobile_***************************',
          permissions: ['read'],
          isActive: true,
          lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
          usageCount: 8750,
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-10')
        },
        {
          id: 'key_003',
          name: 'Analytics Integration',
          key: 'sk_analytics_***************************',
          permissions: ['read'],
          isActive: false,
          expiresAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          lastUsed: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
          usageCount: 2340,
          createdBy: '<EMAIL>',
          createdAt: new Date('2024-12-01')
        }
      ]

      setIntegrations(mockIntegrations)
      setWebhookEvents(mockWebhookEvents)
      setApiKeys(mockApiKeys)
    } catch (error) {
      console.error('Error loading integrations data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredIntegrations = integrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = filterCategory === 'all' || integration.category === filterCategory
    const matchesStatus = filterStatus === 'all' || integration.status === filterStatus
    return matchesSearch && matchesCategory && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-400 bg-green-900/20'
      case 'disconnected': return 'text-gray-400 bg-gray-900/20'
      case 'error': return 'text-red-400 bg-red-900/20'
      case 'pending': return 'text-yellow-400 bg-yellow-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'payment': return 'text-green-400 bg-green-900/20'
      case 'shipping': return 'text-blue-400 bg-blue-900/20'
      case 'analytics': return 'text-purple-400 bg-purple-900/20'
      case 'marketing': return 'text-pink-400 bg-pink-900/20'
      case 'communication': return 'text-yellow-400 bg-yellow-900/20'
      case 'storage': return 'text-orange-400 bg-orange-900/20'
      case 'custom': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'payment': return <CreditCard size={16} />
      case 'shipping': return <Globe size={16} />
      case 'analytics': return <BarChart3 size={16} />
      case 'marketing': return <Mail size={16} />
      case 'communication': return <MessageSquare size={16} />
      case 'storage': return <Database size={16} />
      case 'custom': return <Settings size={16} />
      default: return <Plug size={16} />
    }
  }

  const getWebhookStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-900/20'
      case 'processing': return 'text-blue-400 bg-blue-900/20'
      case 'pending': return 'text-yellow-400 bg-yellow-900/20'
      case 'failed': return 'text-red-400 bg-red-900/20'
      case 'retrying': return 'text-orange-400 bg-orange-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const toggleIntegration = async (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { 
            ...integration, 
            status: integration.status === 'connected' ? 'disconnected' : 'connected' 
          }
        : integration
    ))
  }

  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(1)}%`
  }

  const formatResponseTime = (time: number) => {
    return `${time}ms`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Plug className="w-8 h-8 text-green-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Third-Party Integrations</h1>
            <p className="text-gray-400">API management, webhook handling, and service connection platform</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadIntegrationsData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/integrations/marketplace"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Browse Marketplace
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Integrations</p>
              <p className="text-2xl font-bold text-white">
                {integrations.filter(i => i.status === 'connected').length}
              </p>
              <p className="text-xs text-green-400 mt-1">+2 this month</p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">API Requests</p>
              <p className="text-2xl font-bold text-white">
                {integrations.reduce((sum, i) => sum + i.metrics.totalRequests, 0).toLocaleString()}
              </p>
              <p className="text-xs text-blue-400 mt-1">This month</p>
            </div>
            <Activity className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-2xl font-bold text-white">
                {integrations.length > 0
                  ? ((integrations.reduce((sum, i) => sum + i.metrics.successfulRequests, 0) /
                      integrations.reduce((sum, i) => sum + i.metrics.totalRequests, 1)) * 100).toFixed(1)
                  : 0}%
              </p>
              <p className="text-xs text-green-400 mt-1">+1.2% vs last month</p>
            </div>
            <Shield className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Webhook Events</p>
              <p className="text-2xl font-bold text-white">{webhookEvents.length}</p>
              <p className="text-xs text-purple-400 mt-1">Last 24 hours</p>
            </div>
            <Webhook className="text-purple-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'integrations', label: 'Integrations', count: integrations.length },
            { id: 'webhooks', label: 'Webhooks', count: webhookEvents.length },
            { id: 'api-keys', label: 'API Keys', count: apiKeys.length },
            { id: 'analytics', label: 'Analytics', count: null }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Search and Filters */}
      {activeTab === 'integrations' && (
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search integrations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex gap-3">
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Categories</option>
                <option value="payment">Payment</option>
                <option value="shipping">Shipping</option>
                <option value="analytics">Analytics</option>
                <option value="marketing">Marketing</option>
                <option value="communication">Communication</option>
                <option value="storage">Storage</option>
                <option value="custom">Custom</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Status</option>
                <option value="connected">Connected</option>
                <option value="disconnected">Disconnected</option>
                <option value="error">Error</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'integrations' && (
        <div className="space-y-4">
          {filteredIntegrations.length === 0 ? (
            <div className="bg-gray-800 p-8 rounded-lg text-center">
              <Plug className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Integrations Found</h3>
              <p className="text-gray-400">Browse the marketplace to add your first integration.</p>
            </div>
          ) : (
            filteredIntegrations.map((integration) => (
              <motion.div
                key={integration.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`p-1 rounded ${getCategoryColor(integration.category)}`}>
                        {getCategoryIcon(integration.category)}
                      </div>
                      <h3 className="text-lg font-semibold text-white">{integration.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(integration.status)}`}>
                        {integration.status}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(integration.category)}`}>
                        {integration.category}
                      </span>
                      {integration.webhooks.enabled && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-purple-400 bg-purple-900/20">
                          Webhooks
                        </span>
                      )}
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{integration.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Requests:</span>
                        <span className="text-white ml-1">{integration.metrics.totalRequests.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Success Rate:</span>
                        <span className="text-white ml-1">
                          {((integration.metrics.successfulRequests / integration.metrics.totalRequests) * 100).toFixed(1)}%
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Avg. Response:</span>
                        <span className="text-white ml-1">{formatResponseTime(integration.metrics.avgResponseTime)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Uptime:</span>
                        <span className="text-white ml-1">{formatUptime(integration.metrics.uptime)}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Provider: {integration.provider}</span>
                      <span>Version: {integration.version}</span>
                      {integration.lastSync && (
                        <span>Last sync: {integration.lastSync.toLocaleString()}</span>
                      )}
                    </div>

                    {integration.lastError && (
                      <div className="mt-3 p-3 bg-red-900/20 border border-red-500/30 rounded">
                        <div className="flex items-center mb-1">
                          <AlertTriangle className="text-red-400 mr-2" size={16} />
                          <span className="text-red-400 font-medium">Error</span>
                        </div>
                        <p className="text-sm text-gray-300">{integration.lastError.message}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {integration.lastError.timestamp.toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Configure">
                      <Settings size={16} />
                    </button>
                    <button
                      onClick={() => toggleIntegration(integration.id)}
                      className={`p-2 rounded ${
                        integration.status === 'connected'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                      title={integration.status === 'connected' ? 'Disconnect' : 'Connect'}
                    >
                      {integration.status === 'connected' ? <XCircle size={16} /> : <CheckCircle size={16} />}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      )}

      {activeTab === 'webhooks' && (
        <div className="space-y-4">
          {webhookEvents.map((event) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">{event.event}</h3>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getWebhookStatusColor(event.status)}`}>
                      {event.status}
                    </span>
                    <span className="text-sm text-gray-400">
                      {integrations.find(i => i.id === event.integrationId)?.name}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-gray-400">Received:</span>
                      <span className="text-white ml-1">{event.receivedAt.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Attempts:</span>
                      <span className="text-white ml-1">{event.attempts}/{event.maxAttempts}</span>
                    </div>
                    {event.responseTime && (
                      <div>
                        <span className="text-gray-400">Response Time:</span>
                        <span className="text-white ml-1">{event.responseTime}ms</span>
                      </div>
                    )}
                    {event.processedAt && (
                      <div>
                        <span className="text-gray-400">Processed:</span>
                        <span className="text-white ml-1">{event.processedAt.toLocaleString()}</span>
                      </div>
                    )}
                  </div>

                  {event.error && (
                    <div className="mt-3 p-3 bg-red-900/20 border border-red-500/30 rounded">
                      <p className="text-sm text-red-300">{event.error}</p>
                    </div>
                  )}
                </div>

                <div className="flex space-x-2 ml-4">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Payload">
                    <Eye size={16} />
                  </button>
                  {event.status === 'failed' && (
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Retry">
                      <RefreshCw size={16} />
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'api-keys' || activeTab === 'analytics') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'api-keys' && 'API Key Management'}
            {activeTab === 'analytics' && 'Integration Analytics'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'api-keys' && <Key className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'analytics' && <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'api-keys' && 'API Key Management System'}
              {activeTab === 'analytics' && 'Integration Performance Analytics'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'api-keys' && 'Manage API keys, permissions, and access controls for third-party integrations.'}
              {activeTab === 'analytics' && 'Comprehensive analytics for integration performance, usage patterns, and error tracking.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
