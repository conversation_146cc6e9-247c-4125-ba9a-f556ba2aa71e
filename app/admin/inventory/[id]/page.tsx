'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  TrendingUp, 
  TrendingDown, 
  MapPin, 
  Clock, 
  DollarSign,
  AlertTriangle,
  Plus,
  Minus,
  Edit,
  BarChart3,
  History
} from 'lucide-react'
import { useParams } from 'next/navigation'
import BackButton from '../../../../src/admin/components/common/BackButton'
import { InventoryAlert, InventoryItem, InventoryMovement, InventoryManager } from '../../../../src/lib/ecommerce/inventoryManagement'

export default function InventoryItemDetailPage() {
  const params = useParams()
  const itemId = params.id as string
  
  const [item, setItem] = useState<InventoryItem | null>(null)
  const [movements, setMovements] = useState<InventoryMovement[]>([])
  const [loading, setLoading] = useState(true)
  const [showAdjustStock, setShowAdjustStock] = useState(false)
  const [adjustmentQuantity, setAdjustmentQuantity] = useState('')
  const [adjustmentReason, setAdjustmentReason] = useState('')
  const [adjustmentType, setAdjustmentType] = useState<'increase' | 'decrease'>('increase')

  const inventoryManager = new InventoryManager()

  useEffect(() => {
    if (itemId) {
      loadItemData()
    }
  }, [itemId])

  const loadItemData = async () => {
    setLoading(true)
    try {
      const itemData = await inventoryManager.getInventoryItem(itemId)
      setItem(itemData)

      if (itemData) {
        const movementData = await inventoryManager.getInventoryMovements(itemData.productId)
        setMovements(movementData)
      }
    } catch (error) {
      console.error('Error loading item data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStockAdjustment = async () => {
    if (!item || !adjustmentQuantity || !adjustmentReason) return

    try {
      const quantity = parseInt(adjustmentQuantity)
      const finalQuantity = adjustmentType === 'decrease' ? -quantity : quantity

      await inventoryManager.updateStock(
        item.productId,
        finalQuantity,
        'adjustment',
        adjustmentReason,
        { adjustmentType, adminAction: true }
      )

      // Reload data
      await loadItemData()
      
      // Reset form
      setShowAdjustStock(false)
      setAdjustmentQuantity('')
      setAdjustmentReason('')
      
    } catch (error) {
      console.error('Error adjusting stock:', error)
      alert('Failed to adjust stock. Please try again.')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'text-green-400 bg-green-900/20'
      case 'low_stock':
        return 'text-yellow-400 bg-yellow-900/20'
      case 'out_of_stock':
        return 'text-red-400 bg-red-900/20'
      case 'discontinued':
        return 'text-gray-400 bg-gray-900/20'
      default:
        return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'purchase':
      case 'restock':
      case 'return':
        return <TrendingUp className="text-green-400" size={16} />
      case 'sale':
      case 'damage':
      case 'loss':
        return <TrendingDown className="text-red-400" size={16} />
      case 'adjustment':
        return <Edit className="text-blue-400" size={16} />
      case 'transfer':
        return <MapPin className="text-purple-400" size={16} />
      default:
        return <Package className="text-gray-400" size={16} />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading inventory item...</p>
        </div>
      </div>
    )
  }

  if (!item) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">Item not found</h3>
        <p className="text-gray-400">The inventory item you're looking for doesn't exist.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{item.name}</h1>
          <p className="text-gray-400">SKU: {item.sku} • Category: {item.category}</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowAdjustStock(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Edit size={20} className="mr-2" />
            Adjust Stock
          </button>
        </div>
      </div>

      {/* Status and Alerts */}
      <div className="flex items-center gap-4">
        <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(item.status)}`}>
          {item.status.replace('_', ' ')}
        </span>
        {item.alerts.length > 0 && (
          <div className="flex items-center text-yellow-400">
            <AlertTriangle size={16} className="mr-1" />
            <span className="text-sm">{item.alerts.length} alert(s)</span>
          </div>
        )}
      </div>

      {/* Stock Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Current Stock</p>
              <p className="text-2xl font-bold text-white">{item.currentStock}</p>
            </div>
            <Package className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Available</p>
              <p className="text-2xl font-bold text-green-400">{item.availableStock}</p>
            </div>
            <TrendingUp className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Reserved</p>
              <p className="text-2xl font-bold text-yellow-400">{item.reservedStock}</p>
            </div>
            <Clock className="text-yellow-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Value</p>
              <p className="text-2xl font-bold text-green-400">${item.totalValue.toLocaleString()}</p>
            </div>
            <DollarSign className="text-green-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Stock Level Indicator */}
      <div className="bg-gray-800 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-4">Stock Level</h3>
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Current: {item.currentStock}</span>
            <span className="text-gray-400">Reorder Point: {item.reorderPoint}</span>
            <span className="text-gray-400">Max: {item.maxStock}</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-4">
            <div
              className="h-4 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500"
              style={{ width: `${Math.min((item.currentStock / item.maxStock) * 100, 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500">
            <span>0</span>
            <span className="text-yellow-400">Reorder Point</span>
            <span>Max Stock</span>
          </div>
        </div>
      </div>

      {/* Locations */}
      {item.locations.length > 0 && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Stock Locations</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {item.locations.map((location) => (
              <div key={location.id} className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-white">{location.name}</h4>
                  <MapPin className="text-gray-400" size={16} />
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Stock:</span>
                    <span className="text-white">{location.stock}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Available:</span>
                    <span className="text-green-400">{location.available}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Reserved:</span>
                    <span className="text-yellow-400">{location.reserved}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Movements */}
      <div className="bg-gray-800 p-6 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Recent Movements</h3>
          <History className="text-gray-400" size={20} />
        </div>
        <div className="space-y-3">
          {movements.slice(0, 10).map((movement) => (
            <div key={movement.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div className="flex items-center">
                {getMovementIcon(movement.type)}
                <div className="ml-3">
                  <p className="text-sm font-medium text-white">{movement.reason}</p>
                  <p className="text-xs text-gray-400">
                    {new Date(movement.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`text-sm font-medium ${
                  ['purchase', 'restock', 'return'].includes(movement.type) 
                    ? 'text-green-400' 
                    : 'text-red-400'
                }`}>
                  {['purchase', 'restock', 'return'].includes(movement.type) ? '+' : '-'}{movement.quantity}
                </p>
                <p className="text-xs text-gray-400 capitalize">{movement.type}</p>
              </div>
            </div>
          ))}
          {movements.length === 0 && (
            <p className="text-gray-400 text-center py-4">No movements recorded</p>
          )}
        </div>
      </div>

      {/* Stock Adjustment Modal */}
      {showAdjustStock && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Adjust Stock</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Adjustment Type
                </label>
                <div className="flex gap-2">
                  <button
                    onClick={() => setAdjustmentType('increase')}
                    className={`flex-1 py-2 px-4 rounded-lg flex items-center justify-center ${
                      adjustmentType === 'increase'
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    <Plus size={16} className="mr-1" />
                    Increase
                  </button>
                  <button
                    onClick={() => setAdjustmentType('decrease')}
                    className={`flex-1 py-2 px-4 rounded-lg flex items-center justify-center ${
                      adjustmentType === 'decrease'
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    <Minus size={16} className="mr-1" />
                    Decrease
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Quantity
                </label>
                <input
                  type="number"
                  value={adjustmentQuantity}
                  onChange={(e) => setAdjustmentQuantity(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  placeholder="Enter quantity"
                  min="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Reason
                </label>
                <textarea
                  value={adjustmentReason}
                  onChange={(e) => setAdjustmentReason(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  placeholder="Reason for adjustment"
                  rows={3}
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowAdjustStock(false)}
                className="flex-1 py-2 px-4 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleStockAdjustment}
                disabled={!adjustmentQuantity || !adjustmentReason}
                className="flex-1 py-2 px-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Adjust Stock
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
