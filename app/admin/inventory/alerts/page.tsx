'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  AlertTriangle, 
  Bell, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Package,
  TrendingDown,
  RefreshCw,
  Filter,
  Search,
  Settings
} from 'lucide-react'
import BackButton from '../../../../src/admin/components/common/BackButton'
import { InventoryManager, InventoryAlert } from '../../../../src/lib/ecommerce/inventoryManagement'

interface AlertStats {
  total: number
  critical: number
  warning: number
  info: number
  resolved: number
}

export default function InventoryAlertsPage() {
  const [alerts, setAlerts] = useState<InventoryAlert[]>([])
  const [stats, setStats] = useState<AlertStats>({
    total: 0,
    critical: 0,
    warning: 0,
    info: 0,
    resolved: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterSeverity, setFilterSeverity] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')
  const [showResolved, setShowResolved] = useState(false)

  const inventoryManager = new InventoryManager()

  useEffect(() => {
    loadAlerts()
  }, [])

  const loadAlerts = async () => {
    setLoading(true)
    try {
      const alertsData = await inventoryManager.getInventoryAlerts()
      setAlerts(alertsData)
      
      const calculatedStats = calculateStats(alertsData)
      setStats(calculatedStats)
    } catch (error) {
      console.error('Error loading alerts:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (alertsData: InventoryAlert[]): AlertStats => {
    const total = alertsData.length
    const critical = alertsData.filter(alert => alert.severity === 'critical').length
    const warning = alertsData.filter(alert => alert.severity === 'warning').length
    const info = alertsData.filter(alert => alert.severity === 'info').length
    const resolved = alertsData.filter(alert => alert.resolved).length

    return { total, critical, warning, info, resolved }
  }

  const filteredAlerts = alerts
    .filter(alert => {
      const matchesSearch = alert.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           alert.productName?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesSeverity = filterSeverity === 'all' || alert.severity === filterSeverity
      const matchesType = filterType === 'all' || alert.type === filterType
      const matchesResolved = showResolved || !alert.resolved
      
      return matchesSearch && matchesSeverity && matchesType && matchesResolved
    })
    .sort((a, b) => {
      // Sort by severity (critical first), then by date
      const severityOrder = { critical: 0, warning: 1, info: 2 }
      const aSeverity = severityOrder[a.severity as keyof typeof severityOrder] ?? 3
      const bSeverity = severityOrder[b.severity as keyof typeof severityOrder] ?? 3
      
      if (aSeverity !== bSeverity) {
        return aSeverity - bSeverity
      }
      
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-400 bg-red-900/20 border-red-500/30'
      case 'warning':
        return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30'
      case 'info':
        return 'text-blue-400 bg-blue-900/20 border-blue-500/30'
      default:
        return 'text-gray-400 bg-gray-900/20 border-gray-500/30'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="text-red-400" size={20} />
      case 'warning':
        return <AlertTriangle className="text-yellow-400" size={20} />
      case 'info':
        return <Bell className="text-blue-400" size={20} />
      default:
        return <Bell className="text-gray-400" size={20} />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'low_stock':
      case 'out_of_stock':
        return <TrendingDown className="text-red-400" size={16} />
      case 'reorder_point':
        return <Package className="text-yellow-400" size={16} />
      case 'expiry':
        return <Clock className="text-orange-400" size={16} />
      default:
        return <Bell className="text-gray-400" size={16} />
    }
  }

  const markAsResolved = async (alertId: string) => {
    try {
      await inventoryManager.resolveAlert(alertId)
      await loadAlerts()
    } catch (error) {
      console.error('Error resolving alert:', error)
    }
  }

  const markAsUnresolved = async (alertId: string) => {
    try {
      await inventoryManager.unresolveAlert(alertId)
      await loadAlerts()
    } catch (error) {
      console.error('Error unresolving alert:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Inventory Alerts</h1>
          <p className="text-gray-400">Monitor and manage inventory alerts and notifications</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadAlerts}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
            <Settings size={20} className="mr-2" />
            Settings
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Alerts</p>
              <p className="text-xl font-bold text-white">{stats.total}</p>
            </div>
            <Bell className="text-gray-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Critical</p>
              <p className="text-xl font-bold text-red-400">{stats.critical}</p>
            </div>
            <XCircle className="text-red-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Warning</p>
              <p className="text-xl font-bold text-yellow-400">{stats.warning}</p>
            </div>
            <AlertTriangle className="text-yellow-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Info</p>
              <p className="text-xl font-bold text-blue-400">{stats.info}</p>
            </div>
            <Bell className="text-blue-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Resolved</p>
              <p className="text-xl font-bold text-green-400">{stats.resolved}</p>
            </div>
            <CheckCircle className="text-green-400" size={20} />
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              />
            </div>
          </div>
          
          <div className="flex gap-3">
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value)}
              className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
            </select>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
            >
              <option value="all">All Types</option>
              <option value="low_stock">Low Stock</option>
              <option value="out_of_stock">Out of Stock</option>
              <option value="reorder_point">Reorder Point</option>
              <option value="expiry">Expiry</option>
            </select>
            
            <label className="flex items-center gap-2 text-sm text-gray-300">
              <input
                type="checkbox"
                checked={showResolved}
                onChange={(e) => setShowResolved(e.target.checked)}
                className="rounded"
              />
              Show Resolved
            </label>
          </div>
        </div>
      </div>

      {/* Alerts List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading alerts...</p>
          </div>
        ) : filteredAlerts.length === 0 ? (
          <div className="text-center py-8">
            <Bell className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No alerts found</h3>
            <p className="text-gray-400">No alerts match your current filters.</p>
          </div>
        ) : (
          filteredAlerts.map((alert) => (
            <motion.div
              key={alert.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)} ${
                alert.resolved ? 'opacity-60' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getSeverityIcon(alert.severity)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      {getTypeIcon(alert.type)}
                      <span className="text-xs font-medium text-gray-400 uppercase tracking-wider">
                        {alert.type.replace('_', ' ')}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getSeverityColor(alert.severity)}`}>
                        {alert.severity}
                      </span>
                    </div>
                    <h3 className="text-sm font-medium text-white mb-1">{alert.message}</h3>
                    {alert.productName && (
                      <p className="text-sm text-gray-400">Product: {alert.productName}</p>
                    )}
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>Created: {new Date(alert.createdAt).toLocaleString()}</span>
                      {alert.resolved && alert.resolvedAt && (
                        <span>Resolved: {new Date(alert.resolvedAt).toLocaleString()}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {alert.resolved ? (
                    <button
                      onClick={() => markAsUnresolved(alert.id)}
                      className="text-gray-400 hover:text-white text-sm"
                    >
                      Unresolve
                    </button>
                  ) : (
                    <button
                      onClick={() => markAsResolved(alert.id)}
                      className="text-green-400 hover:text-green-300 text-sm"
                    >
                      Resolve
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>
    </div>
  )
}
