'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown, 
  Plus, 
  Search, 
  Filter,
  Download,
  RefreshCw,
  BarChart3,
  Warehouse,
  ShoppingCart
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'
import { InventoryItem, InventoryAlert } from '../../../src/lib/ecommerce/inventoryManagement'

interface InventoryStats {
  totalItems: number
  lowStockItems: number
  outOfStockItems: number
  totalValue: number
  reorderAlerts: number
  averageTurnover: number
}

export default function InventoryManagementPage() {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])
  const [alerts, setAlerts] = useState<InventoryAlert[]>([])
  const [stats, setStats] = useState<InventoryStats>({
    totalItems: 0,
    lowStockItems: 0,
    outOfStockItems: 0,
    totalValue: 0,
    reorderAlerts: 0,
    averageTurnover: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('name')

  useEffect(() => {
    loadInventoryData()
  }, [])

  const loadInventoryData = async () => {
    setLoading(true)
    try {
      // Load inventory items - replace with actual API calls or available functions
      const items: InventoryItem[] = [] // TODO: Replace with actual data fetching
      setInventoryItems(items)

      // Load alerts - replace with actual API calls or available functions
      const alertsData: InventoryAlert[] = [] // TODO: Replace with actual data fetching
      setAlerts(alertsData)

      // Calculate stats
      const calculatedStats = calculateStats(items, alertsData)
      setStats(calculatedStats)

    } catch (error) {
      console.error('Error loading inventory data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (items: InventoryItem[], alerts: InventoryAlert[]): InventoryStats => {
    const totalItems = items.length
    const lowStockItems = items.filter(item => item.status === 'low_stock').length
    const outOfStockItems = items.filter(item => item.status === 'out_of_stock').length
    const totalValue = items.reduce((sum, item) => sum + item.totalValue, 0)
    const reorderAlerts = alerts.filter(alert => alert.type === 'low_stock' || alert.type === 'out_of_stock').length

    return {
      totalItems,
      lowStockItems,
      outOfStockItems,
      totalValue,
      reorderAlerts,
      averageTurnover: 0 // Would calculate from movement data
    }
  }

  const filteredItems = inventoryItems
    .filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.sku.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesFilter = filterStatus === 'all' || item.status === filterStatus
      return matchesSearch && matchesFilter
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'stock':
          return b.currentStock - a.currentStock
        case 'value':
          return b.totalValue - a.totalValue
        case 'status':
          return a.status.localeCompare(b.status)
        default:
          return 0
      }
    })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'text-green-400 bg-green-900/20'
      case 'low_stock':
        return 'text-yellow-400 bg-yellow-900/20'
      case 'out_of_stock':
        return 'text-red-400 bg-red-900/20'
      case 'discontinued':
        return 'text-gray-400 bg-gray-900/20'
      default:
        return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getStockLevelIndicator = (item: InventoryItem) => {
    const percentage = (item.currentStock / item.maxStock) * 100
    if (percentage <= 10) return 'bg-red-500'
    if (percentage <= 25) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Inventory Management</h1>
          <p className="text-gray-400">Monitor stock levels, alerts, and inventory performance</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadInventoryData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/inventory/add"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Add Item
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Items</p>
              <p className="text-2xl font-bold text-white">{stats.totalItems}</p>
            </div>
            <Package className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Low Stock Alerts</p>
              <p className="text-2xl font-bold text-yellow-400">{stats.lowStockItems}</p>
            </div>
            <AlertTriangle className="text-yellow-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Out of Stock</p>
              <p className="text-2xl font-bold text-red-400">{stats.outOfStockItems}</p>
            </div>
            <TrendingDown className="text-red-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Value</p>
              <p className="text-2xl font-bold text-green-400">${stats.totalValue.toLocaleString()}</p>
            </div>
            <TrendingUp className="text-green-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-900/20 border border-red-500/30 rounded-lg p-4"
        >
          <div className="flex items-center mb-3">
            <AlertTriangle className="text-red-400 mr-2" size={20} />
            <h3 className="text-lg font-semibold text-red-400">Inventory Alerts</h3>
          </div>
          <div className="space-y-2">
            {alerts.slice(0, 5).map((alert, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <span className="text-gray-300">{alert.message}</span>
                <span className="text-red-400">{alert.severity}</span>
              </div>
            ))}
            {alerts.length > 5 && (
              <p className="text-gray-400 text-sm">+{alerts.length - 5} more alerts</p>
            )}
          </div>
        </motion.div>
      )}

      {/* Filters and Search */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search by name or SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              />
            </div>
          </div>
          
          <div className="flex gap-3">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
            >
              <option value="all">All Status</option>
              <option value="in_stock">In Stock</option>
              <option value="low_stock">Low Stock</option>
              <option value="out_of_stock">Out of Stock</option>
              <option value="discontinued">Discontinued</option>
            </select>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
            >
              <option value="name">Sort by Name</option>
              <option value="stock">Sort by Stock</option>
              <option value="value">Sort by Value</option>
              <option value="status">Sort by Status</option>
            </select>
            
            <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg flex items-center transition-colors">
              <Download size={20} className="mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Inventory Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">SKU</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Stock Level</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Value</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Last Movement</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-400">
                    Loading inventory data...
                  </td>
                </tr>
              ) : filteredItems.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-400">
                    No inventory items found
                  </td>
                </tr>
              ) : (
                filteredItems.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-750">
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-white">{item.name}</div>
                        <div className="text-sm text-gray-400">{item.category}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-300 font-mono">{item.sku}</td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <div className="flex justify-between text-sm">
                            <span className="text-white">{item.currentStock}</span>
                            <span className="text-gray-400">/{item.maxStock}</span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div
                              className={`h-2 rounded-full ${getStockLevelIndicator(item)}`}
                              style={{ width: `${Math.min((item.currentStock / item.maxStock) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`}>
                        {item.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-white">${item.totalValue.toLocaleString()}</td>
                    <td className="px-6 py-4 text-sm text-gray-400">
                      {item.lastMovement ? new Date(item.lastMovement).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex space-x-2">
                        <Link
                          href={`/admin/inventory/${item.id}`}
                          className="text-purple-400 hover:text-purple-300"
                        >
                          View
                        </Link>
                        <Link
                          href={`/admin/inventory/${item.id}/edit`}
                          className="text-blue-400 hover:text-blue-300"
                        >
                          Edit
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
