'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ShoppingCart, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle,
  Play,
  Pause,
  Settings,
  RefreshCw,
  Package,
  AlertTriangle,
  DollarSign
} from 'lucide-react'
import BackButton from '../../../../src/admin/components/common/BackButton'
import { InventoryManager, PurchaseOrder, AutoReorderRule } from '../../../../src/lib/ecommerce/inventoryManagement'

interface ReorderStats {
  totalRules: number
  activeRules: number
  pendingOrders: number
  completedOrders: number
  totalValue: number
  avgLeadTime: number
}

export default function AutoReorderPage() {
  const [reorderRules, setReorderRules] = useState<AutoReorderRule[]>([])
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([])
  const [stats, setStats] = useState<ReorderStats>({
    totalRules: 0,
    activeRules: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalValue: 0,
    avgLeadTime: 0
  })
  const [loading, setLoading] = useState(true)
  const [showCreateRule, setShowCreateRule] = useState(false)

  const inventoryManager = new InventoryManager()

  useEffect(() => {
    loadReorderData()
  }, [])

  const loadReorderData = async () => {
    setLoading(true)
    try {
      // Load reorder rules
      const rules = await inventoryManager.getAutoReorderRules()
      setReorderRules(rules)

      // Load recent purchase orders
      const orders = await inventoryManager.getRecentPurchaseOrders(30) // Last 30 days
      setPurchaseOrders(orders)

      // Calculate stats
      const calculatedStats = calculateStats(rules, orders)
      setStats(calculatedStats)

    } catch (error) {
      console.error('Error loading reorder data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (rules: AutoReorderRule[], orders: PurchaseOrder[]): ReorderStats => {
    const totalRules = rules.length
    const activeRules = rules.filter(rule => rule.enabled).length
    const pendingOrders = orders.filter(order => order.status === 'pending').length
    const completedOrders = orders.filter(order => order.status === 'completed').length
    const totalValue = orders.reduce((sum, order) => sum + order.totalCost, 0)
    const avgLeadTime = orders.length > 0 
      ? orders.reduce((sum, order) => sum + (order.expectedDelivery ? 7 : 0), 0) / orders.length 
      : 0

    return {
      totalRules,
      activeRules,
      pendingOrders,
      completedOrders,
      totalValue,
      avgLeadTime
    }
  }

  const toggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      await inventoryManager.updateAutoReorderRule(ruleId, { enabled })
      await loadReorderData()
    } catch (error) {
      console.error('Error toggling rule:', error)
    }
  }

  const triggerManualReorder = async (productId: string) => {
    try {
      await inventoryManager.triggerReorder(productId)
      await loadReorderData()
    } catch (error) {
      console.error('Error triggering reorder:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400 bg-yellow-900/20'
      case 'ordered':
        return 'text-blue-400 bg-blue-900/20'
      case 'completed':
        return 'text-green-400 bg-green-900/20'
      case 'cancelled':
        return 'text-red-400 bg-red-900/20'
      default:
        return 'text-gray-400 bg-gray-900/20'
    }
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Automated Reordering</h1>
          <p className="text-gray-400">Manage automatic reorder rules and purchase orders</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadReorderData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateRule(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Settings size={20} className="mr-2" />
            Create Rule
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Rules</p>
              <p className="text-xl font-bold text-white">{stats.totalRules}</p>
            </div>
            <Settings className="text-blue-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Rules</p>
              <p className="text-xl font-bold text-green-400">{stats.activeRules}</p>
            </div>
            <CheckCircle className="text-green-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Pending Orders</p>
              <p className="text-xl font-bold text-yellow-400">{stats.pendingOrders}</p>
            </div>
            <Clock className="text-yellow-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Completed</p>
              <p className="text-xl font-bold text-green-400">{stats.completedOrders}</p>
            </div>
            <Package className="text-green-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Value</p>
              <p className="text-xl font-bold text-green-400">${stats.totalValue.toLocaleString()}</p>
            </div>
            <DollarSign className="text-green-400" size={20} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gray-800 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Lead Time</p>
              <p className="text-xl font-bold text-blue-400">{stats.avgLeadTime.toFixed(1)} days</p>
            </div>
            <TrendingUp className="text-blue-400" size={20} />
          </div>
        </motion.div>
      </div>

      {/* Reorder Rules */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Reorder Rules</h3>
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500 mx-auto mb-2"></div>
              <p className="text-gray-400">Loading reorder rules...</p>
            </div>
          ) : reorderRules.length === 0 ? (
            <div className="text-center py-8">
              <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h4 className="text-lg font-medium text-white mb-2">No reorder rules configured</h4>
              <p className="text-gray-400 mb-4">Create automated reorder rules to maintain optimal stock levels.</p>
              <button
                onClick={() => setShowCreateRule(true)}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
              >
                Create First Rule
              </button>
            </div>
          ) : (
            reorderRules.map((rule) => (
              <div key={rule.id} className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium text-white">{rule.productName}</h4>
                      <span className="text-sm text-gray-400">SKU: {rule.productSku}</span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        rule.enabled ? 'text-green-400 bg-green-900/20' : 'text-gray-400 bg-gray-900/20'
                      }`}>
                        {rule.enabled ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Reorder Point:</span>
                        <span className="text-white ml-1">{rule.reorderPoint}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Order Quantity:</span>
                        <span className="text-white ml-1">{rule.orderQuantity}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Supplier:</span>
                        <span className="text-white ml-1">{rule.supplierId}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Lead Time:</span>
                        <span className="text-white ml-1">{rule.leadTimeDays} days</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => toggleRule(rule.id, !rule.enabled)}
                      className={`p-2 rounded-lg ${
                        rule.enabled 
                          ? 'bg-red-600 hover:bg-red-700 text-white' 
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                    >
                      {rule.enabled ? <Pause size={16} /> : <Play size={16} />}
                    </button>
                    <button
                      onClick={() => triggerManualReorder(rule.productId)}
                      className="p-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg"
                    >
                      <ShoppingCart size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Recent Purchase Orders */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Purchase Orders</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Order ID</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Product</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Supplier</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Quantity</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Cost</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Status</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Expected</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {purchaseOrders.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                    No recent purchase orders
                  </td>
                </tr>
              ) : (
                purchaseOrders.slice(0, 10).map((order) => (
                  <tr key={order.id} className="hover:bg-gray-750">
                    <td className="px-4 py-3 text-sm text-white font-mono">{order.id.slice(-8)}</td>
                    <td className="px-4 py-3 text-sm text-white">{order.productName}</td>
                    <td className="px-4 py-3 text-sm text-gray-300">{order.supplierId}</td>
                    <td className="px-4 py-3 text-sm text-white">{order.quantity}</td>
                    <td className="px-4 py-3 text-sm text-white">${order.totalCost.toLocaleString()}</td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      {order.expectedDelivery ? new Date(order.expectedDelivery).toLocaleDateString() : 'TBD'}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Rule Modal */}
      {showCreateRule && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Create Reorder Rule</h3>
            <p className="text-gray-400 mb-4">
              This feature will be implemented to allow creating automated reorder rules with product selection, 
              reorder points, quantities, and supplier configuration.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowCreateRule(false)}
                className="flex-1 py-2 px-4 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
