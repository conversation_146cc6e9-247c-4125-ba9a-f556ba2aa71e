'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Mail, 
  Send, 
  Users, 
  TrendingUp, 
  Calendar,
  Target,
  BarChart3,
  Settings,
  Play,
  Pause,
  Square,
  RefreshCw,
  Plus,
  Eye,
  Edit,
  Copy,
  Trash2,
  Filter,
  Search,
  Clock,
  CheckCircle,
  AlertTriangle,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface Campaign {
  id: string
  name: string
  type: 'email' | 'sms' | 'push' | 'automation'
  status: 'draft' | 'scheduled' | 'running' | 'paused' | 'completed' | 'failed'
  subject?: string
  template: string
  audience: {
    segmentId: string
    segmentName: string
    size: number
  }
  schedule: {
    type: 'immediate' | 'scheduled' | 'recurring'
    sendDate?: Date
    timezone: string
    frequency?: 'daily' | 'weekly' | 'monthly'
  }
  metrics: {
    sent: number
    delivered: number
    opened: number
    clicked: number
    converted: number
    unsubscribed: number
    bounced: number
  }
  abTest?: {
    enabled: boolean
    variants: string[]
    splitPercentage: number
    winnerCriteria: 'open_rate' | 'click_rate' | 'conversion_rate'
  }
  createdBy: string
  createdAt: Date
  lastModified: Date
}

interface AutomationWorkflow {
  id: string
  name: string
  description: string
  trigger: {
    type: 'user_action' | 'date_based' | 'behavior' | 'segment_entry'
    conditions: Record<string, any>
  }
  steps: WorkflowStep[]
  isActive: boolean
  metrics: {
    triggered: number
    completed: number
    conversionRate: number
  }
  createdAt: Date
}

interface WorkflowStep {
  id: string
  type: 'email' | 'sms' | 'wait' | 'condition' | 'action'
  config: Record<string, any>
  delay?: {
    amount: number
    unit: 'minutes' | 'hours' | 'days'
  }
}

interface ABTest {
  id: string
  name: string
  campaignId: string
  variants: {
    id: string
    name: string
    subject: string
    content: string
    percentage: number
    metrics: Campaign['metrics']
  }[]
  status: 'running' | 'completed' | 'paused'
  winnerDeclared?: string
  startDate: Date
  endDate?: Date
  testDuration: number // hours
}

export default function MarketingAutomationPage() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [workflows, setWorkflows] = useState<AutomationWorkflow[]>([])
  const [abTests, setAbTests] = useState<ABTest[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'campaigns' | 'automation' | 'ab-testing' | 'analytics'>('campaigns')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadMarketingData()
  }, [])

  const loadMarketingData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockCampaigns: Campaign[] = [
        {
          id: 'camp_001',
          name: 'Welcome Series - New Users',
          type: 'email',
          status: 'running',
          subject: 'Welcome to Syndicaps! 🎮',
          template: 'welcome_series_v2',
          audience: {
            segmentId: 'seg_new_users',
            segmentName: 'New Users (Last 7 Days)',
            size: 234
          },
          schedule: {
            type: 'recurring',
            timezone: 'UTC',
            frequency: 'daily'
          },
          metrics: {
            sent: 1250,
            delivered: 1198,
            opened: 456,
            clicked: 89,
            converted: 23,
            unsubscribed: 5,
            bounced: 52
          },
          abTest: {
            enabled: true,
            variants: ['variant_a', 'variant_b'],
            splitPercentage: 50,
            winnerCriteria: 'open_rate'
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-01'),
          lastModified: new Date()
        },
        {
          id: 'camp_002',
          name: 'Flash Sale - Weekend Promotion',
          type: 'email',
          status: 'scheduled',
          subject: '⚡ 48-Hour Flash Sale - 30% Off Everything!',
          template: 'promotional_flash_sale',
          audience: {
            segmentId: 'seg_active_users',
            segmentName: 'Active Users',
            size: 1567
          },
          schedule: {
            type: 'scheduled',
            sendDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
            timezone: 'UTC'
          },
          metrics: {
            sent: 0,
            delivered: 0,
            opened: 0,
            clicked: 0,
            converted: 0,
            unsubscribed: 0,
            bounced: 0
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-10'),
          lastModified: new Date()
        },
        {
          id: 'camp_003',
          name: 'Re-engagement Campaign',
          type: 'automation',
          status: 'running',
          template: 'reengagement_series',
          audience: {
            segmentId: 'seg_dormant_users',
            segmentName: 'Dormant Users (30+ Days)',
            size: 456
          },
          schedule: {
            type: 'immediate',
            timezone: 'UTC'
          },
          metrics: {
            sent: 456,
            delivered: 432,
            opened: 123,
            clicked: 34,
            converted: 8,
            unsubscribed: 12,
            bounced: 24
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-05'),
          lastModified: new Date()
        }
      ]

      const mockWorkflows: AutomationWorkflow[] = [
        {
          id: 'wf_001',
          name: 'Abandoned Cart Recovery',
          description: 'Automated sequence to recover abandoned shopping carts',
          trigger: {
            type: 'user_action',
            conditions: {
              event: 'cart_abandoned',
              timeDelay: 60 // minutes
            }
          },
          steps: [
            {
              id: 'step_1',
              type: 'wait',
              config: {},
              delay: { amount: 1, unit: 'hours' }
            },
            {
              id: 'step_2',
              type: 'email',
              config: {
                template: 'cart_reminder_1',
                subject: 'You left something in your cart!'
              }
            },
            {
              id: 'step_3',
              type: 'wait',
              config: {},
              delay: { amount: 24, unit: 'hours' }
            },
            {
              id: 'step_4',
              type: 'email',
              config: {
                template: 'cart_reminder_2',
                subject: 'Still thinking about those keycaps? 10% off inside!'
              }
            }
          ],
          isActive: true,
          metrics: {
            triggered: 234,
            completed: 156,
            conversionRate: 0.23
          },
          createdAt: new Date('2025-01-01')
        },
        {
          id: 'wf_002',
          name: 'Post-Purchase Follow-up',
          description: 'Automated follow-up sequence after purchase completion',
          trigger: {
            type: 'user_action',
            conditions: {
              event: 'purchase_completed'
            }
          },
          steps: [
            {
              id: 'step_1',
              type: 'email',
              config: {
                template: 'order_confirmation',
                subject: 'Order confirmed! Here\'s what happens next'
              }
            },
            {
              id: 'step_2',
              type: 'wait',
              config: {},
              delay: { amount: 7, unit: 'days' }
            },
            {
              id: 'step_3',
              type: 'email',
              config: {
                template: 'review_request',
                subject: 'How are you liking your new keycaps?'
              }
            }
          ],
          isActive: true,
          metrics: {
            triggered: 567,
            completed: 489,
            conversionRate: 0.34
          },
          createdAt: new Date('2025-01-03')
        }
      ]

      const mockABTests: ABTest[] = [
        {
          id: 'ab_001',
          name: 'Subject Line Test - Welcome Email',
          campaignId: 'camp_001',
          variants: [
            {
              id: 'var_a',
              name: 'Variant A - Emoji',
              subject: 'Welcome to Syndicaps! 🎮',
              content: 'welcome_template_a',
              percentage: 50,
              metrics: {
                sent: 625,
                delivered: 599,
                opened: 234,
                clicked: 45,
                converted: 12,
                unsubscribed: 3,
                bounced: 26
              }
            },
            {
              id: 'var_b',
              name: 'Variant B - Text Only',
              subject: 'Welcome to Syndicaps Community',
              content: 'welcome_template_b',
              percentage: 50,
              metrics: {
                sent: 625,
                delivered: 599,
                opened: 222,
                clicked: 44,
                converted: 11,
                unsubscribed: 2,
                bounced: 26
              }
            }
          ],
          status: 'running',
          startDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          testDuration: 168 // 7 days
        }
      ]

      setCampaigns(mockCampaigns)
      setWorkflows(mockWorkflows)
      setAbTests(mockABTests)
    } catch (error) {
      console.error('Error loading marketing data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (campaign.subject && campaign.subject.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = filterStatus === 'all' || campaign.status === filterStatus
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-400 bg-green-900/20'
      case 'scheduled': return 'text-blue-400 bg-blue-900/20'
      case 'completed': return 'text-purple-400 bg-purple-900/20'
      case 'paused': return 'text-yellow-400 bg-yellow-900/20'
      case 'draft': return 'text-gray-400 bg-gray-900/20'
      case 'failed': return 'text-red-400 bg-red-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'email': return 'text-blue-400 bg-blue-900/20'
      case 'sms': return 'text-green-400 bg-green-900/20'
      case 'push': return 'text-purple-400 bg-purple-900/20'
      case 'automation': return 'text-orange-400 bg-orange-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const calculateOpenRate = (metrics: Campaign['metrics']) => {
    if (metrics.delivered === 0) return 0
    return (metrics.opened / metrics.delivered) * 100
  }

  const calculateClickRate = (metrics: Campaign['metrics']) => {
    if (metrics.opened === 0) return 0
    return (metrics.clicked / metrics.opened) * 100
  }

  const calculateConversionRate = (metrics: Campaign['metrics']) => {
    if (metrics.clicked === 0) return 0
    return (metrics.converted / metrics.clicked) * 100
  }

  const pauseCampaign = async (campaignId: string) => {
    setCampaigns(prev => prev.map(campaign => 
      campaign.id === campaignId 
        ? { ...campaign, status: campaign.status === 'running' ? 'paused' : 'running' }
        : campaign
    ))
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Mail className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Marketing Automation</h1>
            <p className="text-gray-400">Email campaigns, promotional management, and A/B testing framework</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadMarketingData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/marketing/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Campaign
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Campaigns</p>
              <p className="text-2xl font-bold text-white">
                {campaigns.filter(c => c.status === 'running').length}
              </p>
              <p className="text-xs text-green-400 mt-1">+2 this week</p>
            </div>
            <Send className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Sent</p>
              <p className="text-2xl font-bold text-white">
                {campaigns.reduce((sum, c) => sum + c.metrics.sent, 0).toLocaleString()}
              </p>
              <p className="text-xs text-blue-400 mt-1">This month</p>
            </div>
            <Mail className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg. Open Rate</p>
              <p className="text-2xl font-bold text-white">
                {campaigns.length > 0 
                  ? (campaigns.reduce((sum, c) => sum + calculateOpenRate(c.metrics), 0) / campaigns.length).toFixed(1)
                  : 0}%
              </p>
              <p className="text-xs text-green-400 mt-1">+2.3% vs last month</p>
            </div>
            <Eye className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Automations</p>
              <p className="text-2xl font-bold text-white">
                {workflows.filter(w => w.isActive).length}
              </p>
              <p className="text-xs text-gray-400 mt-1">Active workflows</p>
            </div>
            <Zap className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'campaigns', label: 'Campaigns', count: campaigns.length },
            { id: 'automation', label: 'Automation', count: workflows.length },
            { id: 'ab-testing', label: 'A/B Testing', count: abTests.length },
            { id: 'analytics', label: 'Analytics', count: null }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Search and Filters */}
      {activeTab === 'campaigns' && (
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search campaigns..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>
            
            <div className="flex gap-3">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Status</option>
                <option value="draft">Draft</option>
                <option value="scheduled">Scheduled</option>
                <option value="running">Running</option>
                <option value="paused">Paused</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'campaigns' && (
        <div className="space-y-4">
          {filteredCampaigns.length === 0 ? (
            <div className="bg-gray-800 p-8 rounded-lg text-center">
              <Mail className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Campaigns Found</h3>
              <p className="text-gray-400">Create your first marketing campaign or adjust your search criteria.</p>
            </div>
          ) : (
            filteredCampaigns.map((campaign) => (
              <motion.div
                key={campaign.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{campaign.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(campaign.status)}`}>
                        {campaign.status}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(campaign.type)}`}>
                        {campaign.type}
                      </span>
                      {campaign.abTest?.enabled && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-yellow-400 bg-yellow-900/20">
                          A/B Test
                        </span>
                      )}
                    </div>
                    
                    {campaign.subject && (
                      <p className="text-gray-400 text-sm mb-3">Subject: {campaign.subject}</p>
                    )}
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Audience:</span>
                        <span className="text-white ml-1">{campaign.audience.size.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Sent:</span>
                        <span className="text-white ml-1">{campaign.metrics.sent.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Open Rate:</span>
                        <span className="text-white ml-1">{calculateOpenRate(campaign.metrics).toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Click Rate:</span>
                        <span className="text-white ml-1">{calculateClickRate(campaign.metrics).toFixed(1)}%</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>By {campaign.createdBy}</span>
                      <span>Created: {campaign.createdAt.toLocaleDateString()}</span>
                      {campaign.schedule.sendDate && (
                        <span>Scheduled: {campaign.schedule.sendDate.toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded" title="Duplicate">
                      <Copy size={16} />
                    </button>
                    {(campaign.status === 'running' || campaign.status === 'paused') && (
                      <button
                        onClick={() => pauseCampaign(campaign.id)}
                        className={`p-2 rounded ${
                          campaign.status === 'running' 
                            ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                            : 'bg-green-600 hover:bg-green-700 text-white'
                        }`}
                        title={campaign.status === 'running' ? 'Pause' : 'Resume'}
                      >
                        {campaign.status === 'running' ? <Pause size={16} /> : <Play size={16} />}
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'automation' || activeTab === 'ab-testing' || activeTab === 'analytics') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'automation' && 'Marketing Automation Workflows'}
            {activeTab === 'ab-testing' && 'A/B Testing Management'}
            {activeTab === 'analytics' && 'Marketing Analytics Dashboard'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'automation' && <Zap className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'ab-testing' && <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'analytics' && <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'automation' && 'Advanced Marketing Automation'}
              {activeTab === 'ab-testing' && 'A/B Testing Platform'}
              {activeTab === 'analytics' && 'Marketing Performance Analytics'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'automation' && 'Create sophisticated automation workflows with triggers, conditions, and multi-step sequences.'}
              {activeTab === 'ab-testing' && 'Design and manage A/B tests for email campaigns, subject lines, and content variations.'}
              {activeTab === 'analytics' && 'Comprehensive analytics for campaign performance, audience insights, and ROI tracking.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
