'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Smartphone, 
  Code, 
  Globe, 
  Zap,
  Key,
  Shield,
  Download,
  Upload,
  Settings,
  Plus,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  Activity,
  BarChart3,
  Clock,
  Users,
  Database,
  Cloud,
  Server,
  Monitor,
  Layers,
  BookOpen,
  FileText,
  Package,
  Webhook,
  Link as LinkIcon,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  Target
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface APIEndpoint {
  id: string
  path: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  version: string
  category: 'auth' | 'users' | 'products' | 'orders' | 'analytics' | 'admin' | 'webhooks'
  description: string
  isPublic: boolean
  requiresAuth: boolean
  rateLimit: {
    requests: number
    window: number // seconds
    burst: number
  }
  parameters: APIParameter[]
  responses: APIResponse[]
  documentation: {
    summary: string
    description: string
    examples: { request: any; response: any }[]
    changelog: { version: string; changes: string[]; date: Date }[]
  }
  metrics: {
    totalRequests: number
    requestsToday: number
    avgResponseTime: number
    errorRate: number
    popularityScore: number
  }
  status: 'active' | 'deprecated' | 'beta' | 'maintenance'
  createdAt: Date
  lastModified: Date
}

interface APIParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  location: 'path' | 'query' | 'header' | 'body'
  required: boolean
  description: string
  example?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    enum?: any[]
  }
}

interface APIResponse {
  statusCode: number
  description: string
  schema: any
  examples: any[]
  headers?: Record<string, string>
}

interface SDK {
  id: string
  name: string
  platform: 'ios' | 'android' | 'react_native' | 'flutter' | 'javascript' | 'python' | 'php' | 'java' | 'csharp'
  version: string
  status: 'stable' | 'beta' | 'alpha' | 'deprecated'
  description: string
  features: string[]
  installation: {
    packageManager: string
    command: string
    dependencies: string[]
  }
  documentation: {
    quickStart: string
    apiReference: string
    examples: string
    changelog: string
  }
  metrics: {
    downloads: number
    downloadsThisMonth: number
    githubStars: number
    issues: number
    lastRelease: Date
  }
  support: {
    minVersion: string
    maxVersion?: string
    platforms: string[]
  }
  maintainer: string
  repository: string
  license: string
  createdAt: Date
}

interface APIKey {
  id: string
  name: string
  description: string
  keyType: 'public' | 'private' | 'webhook' | 'server'
  permissions: string[]
  rateLimit: {
    requests: number
    window: number
  }
  restrictions: {
    ipWhitelist: string[]
    domainWhitelist: string[]
    environments: ('development' | 'staging' | 'production')[]
  }
  usage: {
    totalRequests: number
    requestsToday: number
    lastUsed: Date
    topEndpoints: { endpoint: string; requests: number }[]
  }
  status: 'active' | 'revoked' | 'expired' | 'suspended'
  expiresAt?: Date
  createdAt: Date
  createdBy: string
  lastRotated?: Date
}

interface WebhookEndpoint {
  id: string
  name: string
  url: string
  events: string[]
  isActive: boolean
  secret: string
  retryPolicy: {
    maxRetries: number
    backoffStrategy: 'linear' | 'exponential'
    initialDelay: number
  }
  filters: {
    field: string
    operator: string
    value: any
  }[]
  headers: Record<string, string>
  timeout: number
  metrics: {
    totalDeliveries: number
    successfulDeliveries: number
    failedDeliveries: number
    avgResponseTime: number
    lastDelivery?: Date
  }
  status: 'active' | 'paused' | 'error'
  createdAt: Date
  lastModified: Date
}

interface DeveloperApp {
  id: string
  name: string
  description: string
  type: 'mobile' | 'web' | 'server' | 'integration'
  status: 'active' | 'suspended' | 'pending_review'
  developer: {
    id: string
    name: string
    email: string
    company?: string
  }
  apiKeys: string[]
  webhooks: string[]
  permissions: string[]
  usage: {
    totalRequests: number
    requestsThisMonth: number
    dataTransfer: number
    costs: number
  }
  limits: {
    requestsPerMonth: number
    dataTransferGB: number
    webhookEndpoints: number
  }
  billing: {
    plan: 'free' | 'basic' | 'pro' | 'enterprise'
    monthlyFee: number
    usageCharges: number
    nextBilling: Date
  }
  createdAt: Date
  lastActivity: Date
}

interface APIMetrics {
  timeRange: '1h' | '24h' | '7d' | '30d'
  totalRequests: number
  successfulRequests: number
  errorRequests: number
  avgResponseTime: number
  peakRPS: number
  topEndpoints: { endpoint: string; requests: number; avgTime: number }[]
  errorsByCode: Record<string, number>
  requestsByPlatform: Record<string, number>
  geographicDistribution: { country: string; requests: number }[]
  bandwidthUsage: number
}

export default function MobileAPIPage() {
  const [apiEndpoints, setApiEndpoints] = useState<APIEndpoint[]>([])
  const [sdks, setSdks] = useState<SDK[]>([])
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([])
  const [developerApps, setDeveloperApps] = useState<DeveloperApp[]>([])
  const [apiMetrics, setApiMetrics] = useState<APIMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'endpoints' | 'sdks' | 'keys' | 'webhooks' | 'developers'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadMobileAPIData()
  }, [])

  const loadMobileAPIData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual mobile API management integration
      const mockAPIEndpoints: APIEndpoint[] = [
        {
          id: 'endpoint_001',
          path: '/api/v1/auth/login',
          method: 'POST',
          version: '1.0',
          category: 'auth',
          description: 'Authenticate user and return access token',
          isPublic: true,
          requiresAuth: false,
          rateLimit: {
            requests: 10,
            window: 60,
            burst: 20
          },
          parameters: [
            {
              name: 'email',
              type: 'string',
              location: 'body',
              required: true,
              description: 'User email address',
              example: '<EMAIL>',
              validation: { pattern: '^[^@]+@[^@]+\\.[^@]+$' }
            },
            {
              name: 'password',
              type: 'string',
              location: 'body',
              required: true,
              description: 'User password',
              validation: { min: 8 }
            }
          ],
          responses: [
            {
              statusCode: 200,
              description: 'Authentication successful',
              schema: {
                type: 'object',
                properties: {
                  token: { type: 'string' },
                  user: { type: 'object' },
                  expiresIn: { type: 'number' }
                }
              },
              examples: [
                {
                  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                  user: { id: 1, email: '<EMAIL>', name: 'John Doe' },
                  expiresIn: 3600
                }
              ]
            },
            {
              statusCode: 401,
              description: 'Invalid credentials',
              schema: {
                type: 'object',
                properties: {
                  error: { type: 'string' },
                  message: { type: 'string' }
                }
              },
              examples: [{ error: 'INVALID_CREDENTIALS', message: 'Email or password is incorrect' }]
            }
          ],
          documentation: {
            summary: 'User Authentication',
            description: 'Authenticates a user with email and password, returning a JWT token for subsequent API calls.',
            examples: [
              {
                request: { email: '<EMAIL>', password: 'password123' },
                response: { token: 'jwt_token_here', user: { id: 1, email: '<EMAIL>' } }
              }
            ],
            changelog: [
              {
                version: '1.0',
                changes: ['Initial implementation'],
                date: new Date('2024-12-01')
              }
            ]
          },
          metrics: {
            totalRequests: 125000,
            requestsToday: 1250,
            avgResponseTime: 120,
            errorRate: 2.1,
            popularityScore: 95
          },
          status: 'active',
          createdAt: new Date('2024-12-01'),
          lastModified: new Date()
        },
        {
          id: 'endpoint_002',
          path: '/api/v1/products',
          method: 'GET',
          version: '1.0',
          category: 'products',
          description: 'Retrieve paginated list of products',
          isPublic: true,
          requiresAuth: false,
          rateLimit: {
            requests: 100,
            window: 60,
            burst: 200
          },
          parameters: [
            {
              name: 'page',
              type: 'number',
              location: 'query',
              required: false,
              description: 'Page number for pagination',
              example: 1,
              validation: { min: 1 }
            },
            {
              name: 'limit',
              type: 'number',
              location: 'query',
              required: false,
              description: 'Number of items per page',
              example: 20,
              validation: { min: 1, max: 100 }
            },
            {
              name: 'category',
              type: 'string',
              location: 'query',
              required: false,
              description: 'Filter by product category',
              example: 'keycaps'
            }
          ],
          responses: [
            {
              statusCode: 200,
              description: 'Products retrieved successfully',
              schema: {
                type: 'object',
                properties: {
                  data: { type: 'array' },
                  pagination: { type: 'object' },
                  total: { type: 'number' }
                }
              },
              examples: [
                {
                  data: [
                    { id: 1, name: 'Artisan Keycap Set', price: 89.99, category: 'keycaps' }
                  ],
                  pagination: { page: 1, limit: 20, hasNext: true },
                  total: 156
                }
              ]
            }
          ],
          documentation: {
            summary: 'Product Listing',
            description: 'Retrieves a paginated list of products with optional filtering by category.',
            examples: [
              {
                request: { page: 1, limit: 20, category: 'keycaps' },
                response: { data: [], pagination: {}, total: 0 }
              }
            ],
            changelog: [
              {
                version: '1.0',
                changes: ['Initial implementation with pagination'],
                date: new Date('2024-12-01')
              }
            ]
          },
          metrics: {
            totalRequests: 89000,
            requestsToday: 890,
            avgResponseTime: 85,
            errorRate: 0.5,
            popularityScore: 88
          },
          status: 'active',
          createdAt: new Date('2024-12-01'),
          lastModified: new Date()
        }
      ]

      const mockSDKs: SDK[] = [
        {
          id: 'sdk_001',
          name: 'Syndicaps iOS SDK',
          platform: 'ios',
          version: '2.1.0',
          status: 'stable',
          description: 'Native iOS SDK for Syndicaps API integration with Swift support',
          features: [
            'Authentication & User Management',
            'Product Catalog Integration',
            'Order Management',
            'Real-time Notifications',
            'Offline Caching',
            'Analytics Tracking'
          ],
          installation: {
            packageManager: 'CocoaPods',
            command: 'pod "SyndicapsSDK", "~> 2.1"',
            dependencies: ['Alamofire', 'SwiftyJSON']
          },
          documentation: {
            quickStart: 'https://docs.syndicaps.com/sdk/ios/quickstart',
            apiReference: 'https://docs.syndicaps.com/sdk/ios/reference',
            examples: 'https://github.com/syndicaps/ios-sdk-examples',
            changelog: 'https://docs.syndicaps.com/sdk/ios/changelog'
          },
          metrics: {
            downloads: 15420,
            downloadsThisMonth: 1250,
            githubStars: 234,
            issues: 12,
            lastRelease: new Date('2025-01-10')
          },
          support: {
            minVersion: 'iOS 12.0',
            platforms: ['iPhone', 'iPad', 'Apple Watch']
          },
          maintainer: '<EMAIL>',
          repository: 'https://github.com/syndicaps/ios-sdk',
          license: 'MIT',
          createdAt: new Date('2024-10-01')
        },
        {
          id: 'sdk_002',
          name: 'Syndicaps Android SDK',
          platform: 'android',
          version: '2.0.3',
          status: 'stable',
          description: 'Native Android SDK with Kotlin and Java support',
          features: [
            'Authentication & User Management',
            'Product Catalog Integration',
            'Order Management',
            'Push Notifications',
            'Offline Support',
            'Analytics Integration'
          ],
          installation: {
            packageManager: 'Gradle',
            command: 'implementation "com.syndicaps:android-sdk:2.0.3"',
            dependencies: ['OkHttp', 'Gson', 'Room']
          },
          documentation: {
            quickStart: 'https://docs.syndicaps.com/sdk/android/quickstart',
            apiReference: 'https://docs.syndicaps.com/sdk/android/reference',
            examples: 'https://github.com/syndicaps/android-sdk-examples',
            changelog: 'https://docs.syndicaps.com/sdk/android/changelog'
          },
          metrics: {
            downloads: 18900,
            downloadsThisMonth: 1560,
            githubStars: 189,
            issues: 8,
            lastRelease: new Date('2025-01-05')
          },
          support: {
            minVersion: 'Android 5.0 (API 21)',
            platforms: ['Phone', 'Tablet', 'Wear OS']
          },
          maintainer: '<EMAIL>',
          repository: 'https://github.com/syndicaps/android-sdk',
          license: 'MIT',
          createdAt: new Date('2024-10-01')
        },
        {
          id: 'sdk_003',
          name: 'Syndicaps JavaScript SDK',
          platform: 'javascript',
          version: '3.2.1',
          status: 'stable',
          description: 'Universal JavaScript SDK for web and Node.js applications',
          features: [
            'Promise-based API',
            'TypeScript Support',
            'Automatic Retries',
            'Request Caching',
            'Error Handling',
            'Webhook Verification'
          ],
          installation: {
            packageManager: 'npm',
            command: 'npm install @syndicaps/sdk',
            dependencies: ['axios']
          },
          documentation: {
            quickStart: 'https://docs.syndicaps.com/sdk/javascript/quickstart',
            apiReference: 'https://docs.syndicaps.com/sdk/javascript/reference',
            examples: 'https://github.com/syndicaps/js-sdk-examples',
            changelog: 'https://docs.syndicaps.com/sdk/javascript/changelog'
          },
          metrics: {
            downloads: 45600,
            downloadsThisMonth: 3200,
            githubStars: 567,
            issues: 23,
            lastRelease: new Date('2025-01-12')
          },
          support: {
            minVersion: 'Node.js 14+',
            platforms: ['Browser', 'Node.js', 'React', 'Vue', 'Angular']
          },
          maintainer: '<EMAIL>',
          repository: 'https://github.com/syndicaps/js-sdk',
          license: 'MIT',
          createdAt: new Date('2024-09-01')
        }
      ]

      const mockAPIKeys: APIKey[] = [
        {
          id: 'key_001',
          name: 'Mobile App Production',
          description: 'Production API key for iOS and Android mobile applications',
          keyType: 'private',
          permissions: ['products:read', 'orders:write', 'users:read', 'analytics:write'],
          rateLimit: {
            requests: 10000,
            window: 3600
          },
          restrictions: {
            ipWhitelist: [],
            domainWhitelist: ['app.syndicaps.com'],
            environments: ['production']
          },
          usage: {
            totalRequests: 2450000,
            requestsToday: 12500,
            lastUsed: new Date(Date.now() - 5 * 60 * 1000),
            topEndpoints: [
              { endpoint: '/api/v1/products', requests: 890000 },
              { endpoint: '/api/v1/auth/login', requests: 125000 },
              { endpoint: '/api/v1/orders', requests: 89000 }
            ]
          },
          status: 'active',
          createdAt: new Date('2024-12-01'),
          createdBy: '<EMAIL>'
        },
        {
          id: 'key_002',
          name: 'Third-Party Integration',
          description: 'API key for external partner integrations',
          keyType: 'server',
          permissions: ['products:read', 'webhooks:write'],
          rateLimit: {
            requests: 1000,
            window: 3600
          },
          restrictions: {
            ipWhitelist: ['***********/24'],
            domainWhitelist: [],
            environments: ['production']
          },
          usage: {
            totalRequests: 156000,
            requestsToday: 450,
            lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
            topEndpoints: [
              { endpoint: '/api/v1/products', requests: 145000 },
              { endpoint: '/api/v1/webhooks', requests: 11000 }
            ]
          },
          status: 'active',
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
          createdAt: new Date('2024-11-15'),
          createdBy: '<EMAIL>'
        }
      ]

      const mockWebhooks: WebhookEndpoint[] = [
        {
          id: 'webhook_001',
          name: 'Order Status Updates',
          url: 'https://partner.example.com/webhooks/orders',
          events: ['order.created', 'order.updated', 'order.shipped', 'order.delivered'],
          isActive: true,
          secret: 'whsec_***',
          retryPolicy: {
            maxRetries: 3,
            backoffStrategy: 'exponential',
            initialDelay: 1000
          },
          filters: [
            { field: 'order.total', operator: 'greater_than', value: 100 }
          ],
          headers: {
            'User-Agent': 'Syndicaps-Webhooks/1.0',
            'Content-Type': 'application/json'
          },
          timeout: 30000,
          metrics: {
            totalDeliveries: 15420,
            successfulDeliveries: 15200,
            failedDeliveries: 220,
            avgResponseTime: 450,
            lastDelivery: new Date(Date.now() - 30 * 60 * 1000)
          },
          status: 'active',
          createdAt: new Date('2024-12-01'),
          lastModified: new Date()
        }
      ]

      const mockDeveloperApps: DeveloperApp[] = [
        {
          id: 'app_001',
          name: 'Syndicaps Mobile App',
          description: 'Official Syndicaps mobile application for iOS and Android',
          type: 'mobile',
          status: 'active',
          developer: {
            id: 'dev_001',
            name: 'Syndicaps Team',
            email: '<EMAIL>',
            company: 'Syndicaps Inc.'
          },
          apiKeys: ['key_001'],
          webhooks: [],
          permissions: ['full_access'],
          usage: {
            totalRequests: 2450000,
            requestsThisMonth: 125000,
            dataTransfer: 45.6,
            costs: 0
          },
          limits: {
            requestsPerMonth: 10000000,
            dataTransferGB: 1000,
            webhookEndpoints: 10
          },
          billing: {
            plan: 'enterprise',
            monthlyFee: 0,
            usageCharges: 0,
            nextBilling: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          },
          createdAt: new Date('2024-12-01'),
          lastActivity: new Date(Date.now() - 5 * 60 * 1000)
        }
      ]

      const mockAPIMetrics: APIMetrics = {
        timeRange: '24h',
        totalRequests: 45230,
        successfulRequests: 44100,
        errorRequests: 1130,
        avgResponseTime: 125,
        peakRPS: 45.2,
        topEndpoints: [
          { endpoint: '/api/v1/products', requests: 15420, avgTime: 85 },
          { endpoint: '/api/v1/auth/login', requests: 8900, avgTime: 120 },
          { endpoint: '/api/v1/orders', requests: 6700, avgTime: 150 }
        ],
        errorsByCode: {
          '400': 450,
          '401': 320,
          '404': 180,
          '500': 180
        },
        requestsByPlatform: {
          'iOS': 18500,
          'Android': 15200,
          'Web': 8900,
          'API': 2630
        },
        geographicDistribution: [
          { country: 'United States', requests: 22600 },
          { country: 'Canada', requests: 8900 },
          { country: 'United Kingdom', requests: 5400 },
          { country: 'Germany', requests: 3200 },
          { country: 'Australia', requests: 2800 }
        ],
        bandwidthUsage: 2.4
      }

      setApiEndpoints(mockAPIEndpoints)
      setSdks(mockSDKs)
      setApiKeys(mockAPIKeys)
      setWebhooks(mockWebhooks)
      setDeveloperApps(mockDeveloperApps)
      setApiMetrics(mockAPIMetrics)
    } catch (error) {
      console.error('Error loading mobile API data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'stable': return 'text-green-400 bg-green-900/20'
      case 'beta': case 'pending_review': return 'text-blue-400 bg-blue-900/20'
      case 'alpha': case 'maintenance': return 'text-yellow-400 bg-yellow-900/20'
      case 'deprecated': case 'revoked': case 'suspended': case 'error': return 'text-red-400 bg-red-900/20'
      case 'expired': case 'paused': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'text-green-400 bg-green-900/20'
      case 'POST': return 'text-blue-400 bg-blue-900/20'
      case 'PUT': return 'text-yellow-400 bg-yellow-900/20'
      case 'DELETE': return 'text-red-400 bg-red-900/20'
      case 'PATCH': return 'text-purple-400 bg-purple-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'auth': return 'text-red-400 bg-red-900/20'
      case 'users': return 'text-blue-400 bg-blue-900/20'
      case 'products': return 'text-green-400 bg-green-900/20'
      case 'orders': return 'text-yellow-400 bg-yellow-900/20'
      case 'analytics': return 'text-purple-400 bg-purple-900/20'
      case 'admin': return 'text-pink-400 bg-pink-900/20'
      case 'webhooks': return 'text-orange-400 bg-orange-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'ios': return '🍎'
      case 'android': return '🤖'
      case 'javascript': return '🟨'
      case 'python': return '🐍'
      case 'react_native': return '⚛️'
      case 'flutter': return '🦋'
      default: return '📱'
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes < 1024) return `${bytes} GB`
    return `${(bytes / 1024).toFixed(1)} TB`
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Smartphone className="w-8 h-8 text-green-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Mobile API & Ecosystem</h1>
            <p className="text-gray-400">Mobile-first APIs, SDK development, and third-party developer ecosystem</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadMobileAPIData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/mobile-api/endpoints/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Endpoint
          </Link>
        </div>
      </div>

      {/* Mobile API Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">API Endpoints</p>
              <p className="text-2xl font-bold text-white">
                {apiEndpoints.filter(e => e.status === 'active').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {apiEndpoints.filter(e => e.status === 'beta').length} beta
              </p>
            </div>
            <Code className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Requests Today</p>
              <p className="text-2xl font-bold text-white">
                {apiMetrics ? formatNumber(apiMetrics.totalRequests) : '0'}
              </p>
              <p className="text-xs text-blue-400 mt-1">
                {apiMetrics ? formatPercentage((apiMetrics.successfulRequests / apiMetrics.totalRequests) * 100) : '0%'} success
              </p>
            </div>
            <Activity className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active SDKs</p>
              <p className="text-2xl font-bold text-white">
                {sdks.filter(s => s.status === 'stable').length}
              </p>
              <p className="text-xs text-purple-400 mt-1">
                {formatNumber(sdks.reduce((sum, s) => sum + s.metrics.downloads, 0))} downloads
              </p>
            </div>
            <Package className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Developer Apps</p>
              <p className="text-2xl font-bold text-white">
                {developerApps.filter(a => a.status === 'active').length}
              </p>
              <p className="text-xs text-yellow-400 mt-1">
                {apiKeys.filter(k => k.status === 'active').length} API keys
              </p>
            </div>
            <Users className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'endpoints', label: 'API Endpoints', icon: Code, count: apiEndpoints.length },
            { id: 'sdks', label: 'SDKs', icon: Package, count: sdks.length },
            { id: 'keys', label: 'API Keys', icon: Key, count: apiKeys.length },
            { id: 'webhooks', label: 'Webhooks', icon: Webhook, count: webhooks.length },
            { id: 'developers', label: 'Developers', icon: Users, count: developerApps.length }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* API Performance Metrics */}
          {apiMetrics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">API Performance</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Requests</span>
                    <span className="text-white font-medium">{formatNumber(apiMetrics.totalRequests)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Success Rate</span>
                    <span className="text-green-400 font-medium">
                      {formatPercentage((apiMetrics.successfulRequests / apiMetrics.totalRequests) * 100)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Avg Response Time</span>
                    <span className="text-white font-medium">{apiMetrics.avgResponseTime}ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Peak RPS</span>
                    <span className="text-white font-medium">{apiMetrics.peakRPS}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Bandwidth Usage</span>
                    <span className="text-white font-medium">{formatBytes(apiMetrics.bandwidthUsage)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Top Endpoints</h3>
                <div className="space-y-3">
                  {apiMetrics.topEndpoints.map((endpoint, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                      <div>
                        <p className="text-sm font-medium text-white">{endpoint.endpoint}</p>
                        <p className="text-xs text-gray-400">{endpoint.avgTime}ms avg</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-white">{formatNumber(endpoint.requests)}</p>
                        <p className="text-xs text-gray-400">requests</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Requests by Platform</h3>
                <div className="space-y-3">
                  {Object.entries(apiMetrics.requestsByPlatform).map(([platform, requests]) => (
                    <div key={platform} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getPlatformIcon(platform.toLowerCase())}</span>
                        <span className="text-sm text-white">{platform}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium text-white">{formatNumber(requests)}</span>
                        <span className="text-xs text-gray-400 ml-2">
                          ({formatPercentage((requests / apiMetrics.totalRequests) * 100)})
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Error Distribution</h3>
                <div className="space-y-3">
                  {Object.entries(apiMetrics.errorsByCode).map(([code, count]) => (
                    <div key={code} className="flex items-center justify-between">
                      <span className="text-sm text-white">HTTP {code}</span>
                      <div className="text-right">
                        <span className="text-sm font-medium text-white">{count}</span>
                        <span className="text-xs text-gray-400 ml-2">
                          ({formatPercentage((count / apiMetrics.errorRequests) * 100)})
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* SDK Downloads */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">SDK Ecosystem</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sdks.map((sdk) => (
                <div key={sdk.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getPlatformIcon(sdk.platform)}</span>
                      <h4 className="text-sm font-medium text-white">{sdk.name}</h4>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(sdk.status)}`}>
                      {sdk.status}
                    </span>
                  </div>

                  <p className="text-xs text-gray-400 mb-3">v{sdk.version}</p>

                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Downloads:</span>
                      <span className="text-white">{formatNumber(sdk.metrics.downloads)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">This Month:</span>
                      <span className="text-white">{formatNumber(sdk.metrics.downloadsThisMonth)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">GitHub Stars:</span>
                      <span className="text-white">{sdk.metrics.githubStars}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Open Issues:</span>
                      <span className={`${sdk.metrics.issues > 20 ? 'text-red-400' : 'text-green-400'}`}>
                        {sdk.metrics.issues}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent API Activity */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent API Activity</h3>
            <div className="space-y-3">
              {apiEndpoints.slice(0, 5).map((endpoint) => (
                <div key={endpoint.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <div className="flex items-center space-x-3">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getMethodColor(endpoint.method)}`}>
                      {endpoint.method}
                    </span>
                    <div>
                      <p className="text-sm font-medium text-white">{endpoint.path}</p>
                      <p className="text-xs text-gray-400">{endpoint.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-white">{formatNumber(endpoint.metrics.requestsToday)} today</p>
                    <p className="text-xs text-gray-400">{endpoint.metrics.avgResponseTime}ms avg</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'endpoints' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search API endpoints..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Categories</option>
                  <option value="auth">Authentication</option>
                  <option value="users">Users</option>
                  <option value="products">Products</option>
                  <option value="orders">Orders</option>
                  <option value="analytics">Analytics</option>
                  <option value="admin">Admin</option>
                  <option value="webhooks">Webhooks</option>
                </select>

                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="beta">Beta</option>
                  <option value="deprecated">Deprecated</option>
                  <option value="maintenance">Maintenance</option>
                </select>
              </div>
            </div>
          </div>

          {/* API Endpoints List */}
          <div className="space-y-4">
            {apiEndpoints.map((endpoint) => (
              <motion.div
                key={endpoint.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getMethodColor(endpoint.method)}`}>
                        {endpoint.method}
                      </span>
                      <h3 className="text-lg font-semibold text-white">{endpoint.path}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(endpoint.status)}`}>
                        {endpoint.status}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(endpoint.category)}`}>
                        {endpoint.category}
                      </span>
                      {endpoint.requiresAuth && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-yellow-300 bg-yellow-900/20">
                          Auth Required
                        </span>
                      )}
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{endpoint.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Requests Today:</span>
                        <span className="text-white ml-1">{formatNumber(endpoint.metrics.requestsToday)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Avg Response:</span>
                        <span className="text-white ml-1">{endpoint.metrics.avgResponseTime}ms</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Error Rate:</span>
                        <span className={`ml-1 ${endpoint.metrics.errorRate > 5 ? 'text-red-400' : 'text-green-400'}`}>
                          {formatPercentage(endpoint.metrics.errorRate)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Rate Limit:</span>
                        <span className="text-white ml-1">{endpoint.rateLimit.requests}/{endpoint.rateLimit.window}s</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Version: {endpoint.version}</span>
                      <span>Parameters: {endpoint.parameters.length}</span>
                      <span>Updated: {endpoint.lastModified.toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Documentation">
                      <BookOpen size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Test">
                      <Zap size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'sdks' || activeTab === 'keys' || activeTab === 'webhooks' || activeTab === 'developers') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'sdks' && 'SDK Development & Distribution'}
            {activeTab === 'keys' && 'API Key Management'}
            {activeTab === 'webhooks' && 'Webhook Configuration'}
            {activeTab === 'developers' && 'Developer Portal & Apps'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'sdks' && <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'keys' && <Key className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'webhooks' && <Webhook className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'developers' && <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'sdks' && 'Multi-Platform SDK Ecosystem'}
              {activeTab === 'keys' && 'Secure API Key Management'}
              {activeTab === 'webhooks' && 'Real-time Webhook System'}
              {activeTab === 'developers' && 'Developer Community Platform'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'sdks' && 'Develop, maintain, and distribute SDKs for iOS, Android, JavaScript, Python, and more platforms.'}
              {activeTab === 'keys' && 'Generate, manage, and monitor API keys with granular permissions and usage tracking.'}
              {activeTab === 'webhooks' && 'Configure webhook endpoints for real-time event notifications with retry policies and monitoring.'}
              {activeTab === 'developers' && 'Manage developer applications, API access, billing, and community engagement.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
