'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Monitor, 
  Bell, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Activity,
  Server,
  Database,
  Globe,
  Zap,
  RefreshCw,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  BarChart3,
  Cpu,
  HardDrive,
  Wifi,
  Users,
  ShoppingCart,
  Mail
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface Alert {
  id: string
  title: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed'
  source: 'system' | 'application' | 'database' | 'network' | 'security' | 'business'
  category: string
  metric: string
  threshold: {
    operator: 'greater_than' | 'less_than' | 'equals' | 'not_equals'
    value: number
    unit: string
  }
  currentValue: number
  triggeredAt: Date
  acknowledgedAt?: Date
  acknowledgedBy?: string
  resolvedAt?: Date
  escalationLevel: number
  escalationRules: EscalationRule[]
  tags: string[]
  affectedServices: string[]
  runbook?: string
}

interface EscalationRule {
  level: number
  delayMinutes: number
  recipients: string[]
  channels: ('email' | 'sms' | 'slack' | 'webhook')[]
}

interface MonitoringRule {
  id: string
  name: string
  description: string
  isActive: boolean
  source: string
  metric: string
  condition: {
    operator: 'greater_than' | 'less_than' | 'equals' | 'not_equals' | 'change_percent'
    value: number
    timeWindow: number // minutes
    evaluationFrequency: number // minutes
  }
  severity: Alert['severity']
  escalationRules: EscalationRule[]
  suppressionRules?: {
    timeRanges: { start: string; end: string; days: string[] }[]
    conditions: string[]
  }
  createdBy: string
  createdAt: Date
  lastTriggered?: Date
  triggerCount: number
}

interface SystemMetric {
  id: string
  name: string
  category: 'system' | 'application' | 'business'
  value: number
  unit: string
  timestamp: Date
  trend: 'up' | 'down' | 'stable'
  status: 'healthy' | 'warning' | 'critical'
  history: { timestamp: Date; value: number }[]
}

interface IncidentReport {
  id: string
  title: string
  description: string
  severity: Alert['severity']
  status: 'investigating' | 'identified' | 'monitoring' | 'resolved'
  affectedServices: string[]
  startTime: Date
  endTime?: Date
  duration?: number
  rootCause?: string
  resolution?: string
  assignedTo: string[]
  updates: {
    id: string
    message: string
    timestamp: Date
    author: string
    type: 'update' | 'escalation' | 'resolution'
  }[]
  postMortem?: {
    summary: string
    timeline: { time: Date; event: string }[]
    rootCause: string
    actionItems: { task: string; assignee: string; dueDate: Date }[]
  }
}

export default function AdvancedMonitoringPage() {
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [monitoringRules, setMonitoringRules] = useState<MonitoringRule[]>([])
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([])
  const [incidents, setIncidents] = useState<IncidentReport[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'dashboard' | 'alerts' | 'rules' | 'incidents' | 'metrics'>('dashboard')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterSeverity, setFilterSeverity] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadMonitoringData()
    
    // Set up real-time updates
    const interval = setInterval(loadMonitoringData, 30000) // Update every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const loadMonitoringData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual monitoring API integration
      const mockAlerts: Alert[] = [
        {
          id: 'alert_001',
          title: 'High CPU Usage',
          description: 'CPU usage has exceeded 85% for the last 10 minutes',
          severity: 'high',
          status: 'active',
          source: 'system',
          category: 'Performance',
          metric: 'cpu_usage_percent',
          threshold: {
            operator: 'greater_than',
            value: 85,
            unit: '%'
          },
          currentValue: 92.3,
          triggeredAt: new Date(Date.now() - 15 * 60 * 1000),
          escalationLevel: 1,
          escalationRules: [
            {
              level: 1,
              delayMinutes: 5,
              recipients: ['<EMAIL>'],
              channels: ['email', 'slack']
            },
            {
              level: 2,
              delayMinutes: 15,
              recipients: ['<EMAIL>', '<EMAIL>'],
              channels: ['email', 'sms', 'slack']
            }
          ],
          tags: ['production', 'performance'],
          affectedServices: ['web-server', 'api-gateway'],
          runbook: 'https://docs.syndicaps.com/runbooks/high-cpu'
        },
        {
          id: 'alert_002',
          title: 'Database Connection Pool Exhausted',
          description: 'All database connections are in use, new requests are being queued',
          severity: 'critical',
          status: 'acknowledged',
          source: 'database',
          category: 'Database',
          metric: 'db_connections_active',
          threshold: {
            operator: 'greater_than',
            value: 95,
            unit: 'connections'
          },
          currentValue: 100,
          triggeredAt: new Date(Date.now() - 45 * 60 * 1000),
          acknowledgedAt: new Date(Date.now() - 30 * 60 * 1000),
          acknowledgedBy: '<EMAIL>',
          escalationLevel: 2,
          escalationRules: [
            {
              level: 1,
              delayMinutes: 0,
              recipients: ['<EMAIL>'],
              channels: ['email', 'sms', 'slack']
            }
          ],
          tags: ['production', 'database', 'critical'],
          affectedServices: ['database', 'api'],
          runbook: 'https://docs.syndicaps.com/runbooks/db-connections'
        },
        {
          id: 'alert_003',
          title: 'Low Disk Space',
          description: 'Available disk space is below 10% on primary storage',
          severity: 'medium',
          status: 'resolved',
          source: 'system',
          category: 'Storage',
          metric: 'disk_usage_percent',
          threshold: {
            operator: 'greater_than',
            value: 90,
            unit: '%'
          },
          currentValue: 78.5,
          triggeredAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          resolvedAt: new Date(Date.now() - 30 * 60 * 1000),
          escalationLevel: 1,
          escalationRules: [
            {
              level: 1,
              delayMinutes: 30,
              recipients: ['<EMAIL>'],
              channels: ['email']
            }
          ],
          tags: ['production', 'storage'],
          affectedServices: ['file-storage']
        }
      ]

      const mockRules: MonitoringRule[] = [
        {
          id: 'rule_001',
          name: 'High CPU Usage Alert',
          description: 'Alert when CPU usage exceeds 85% for 5 minutes',
          isActive: true,
          source: 'system',
          metric: 'cpu_usage_percent',
          condition: {
            operator: 'greater_than',
            value: 85,
            timeWindow: 5,
            evaluationFrequency: 1
          },
          severity: 'high',
          escalationRules: [
            {
              level: 1,
              delayMinutes: 5,
              recipients: ['<EMAIL>'],
              channels: ['email', 'slack']
            }
          ],
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-01'),
          lastTriggered: new Date(Date.now() - 15 * 60 * 1000),
          triggerCount: 23
        },
        {
          id: 'rule_002',
          name: 'Database Connection Pool Monitor',
          description: 'Alert when database connections exceed 95%',
          isActive: true,
          source: 'database',
          metric: 'db_connections_active',
          condition: {
            operator: 'greater_than',
            value: 95,
            timeWindow: 1,
            evaluationFrequency: 1
          },
          severity: 'critical',
          escalationRules: [
            {
              level: 1,
              delayMinutes: 0,
              recipients: ['<EMAIL>', '<EMAIL>'],
              channels: ['email', 'sms', 'slack']
            }
          ],
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-03'),
          lastTriggered: new Date(Date.now() - 45 * 60 * 1000),
          triggerCount: 5
        }
      ]

      const mockMetrics: SystemMetric[] = [
        {
          id: 'metric_001',
          name: 'CPU Usage',
          category: 'system',
          value: 92.3,
          unit: '%',
          timestamp: new Date(),
          trend: 'up',
          status: 'critical',
          history: Array.from({ length: 24 }, (_, i) => ({
            timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000),
            value: Math.random() * 40 + 50
          }))
        },
        {
          id: 'metric_002',
          name: 'Memory Usage',
          category: 'system',
          value: 68.7,
          unit: '%',
          timestamp: new Date(),
          trend: 'stable',
          status: 'healthy',
          history: Array.from({ length: 24 }, (_, i) => ({
            timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000),
            value: Math.random() * 20 + 60
          }))
        },
        {
          id: 'metric_003',
          name: 'Active Users',
          category: 'business',
          value: 1247,
          unit: 'users',
          timestamp: new Date(),
          trend: 'up',
          status: 'healthy',
          history: Array.from({ length: 24 }, (_, i) => ({
            timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000),
            value: Math.floor(Math.random() * 500 + 1000)
          }))
        },
        {
          id: 'metric_004',
          name: 'Response Time',
          category: 'application',
          value: 245,
          unit: 'ms',
          timestamp: new Date(),
          trend: 'down',
          status: 'healthy',
          history: Array.from({ length: 24 }, (_, i) => ({
            timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000),
            value: Math.random() * 100 + 200
          }))
        }
      ]

      const mockIncidents: IncidentReport[] = [
        {
          id: 'inc_001',
          title: 'Database Performance Degradation',
          description: 'Significant slowdown in database query performance affecting user experience',
          severity: 'high',
          status: 'monitoring',
          affectedServices: ['database', 'api', 'web-app'],
          startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
          assignedTo: ['<EMAIL>', '<EMAIL>'],
          updates: [
            {
              id: 'update_1',
              message: 'Incident detected, investigating root cause',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              author: '<EMAIL>',
              type: 'update'
            },
            {
              id: 'update_2',
              message: 'Identified slow query causing table locks, optimizing indexes',
              timestamp: new Date(Date.now() - 90 * 60 * 1000),
              author: '<EMAIL>',
              type: 'update'
            },
            {
              id: 'update_3',
              message: 'Index optimization complete, monitoring performance',
              timestamp: new Date(Date.now() - 30 * 60 * 1000),
              author: '<EMAIL>',
              type: 'update'
            }
          ]
        }
      ]

      setAlerts(mockAlerts)
      setMonitoringRules(mockRules)
      setSystemMetrics(mockMetrics)
      setIncidents(mockIncidents)
    } catch (error) {
      console.error('Error loading monitoring data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alert.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSeverity = filterSeverity === 'all' || alert.severity === filterSeverity
    const matchesStatus = filterStatus === 'all' || alert.status === filterStatus
    return matchesSearch && matchesSeverity && matchesStatus
  })

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-900/20 border-red-500/30'
      case 'high': return 'text-orange-400 bg-orange-900/20 border-orange-500/30'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30'
      case 'low': return 'text-blue-400 bg-blue-900/20 border-blue-500/30'
      case 'info': return 'text-gray-400 bg-gray-900/20 border-gray-500/30'
      default: return 'text-gray-400 bg-gray-900/20 border-gray-500/30'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-red-400 bg-red-900/20'
      case 'acknowledged': return 'text-yellow-400 bg-yellow-900/20'
      case 'resolved': return 'text-green-400 bg-green-900/20'
      case 'suppressed': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400'
      case 'warning': return 'text-yellow-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="text-green-400" size={16} />
      case 'down': return <TrendingDown className="text-red-400" size={16} />
      case 'stable': return <div className="w-4 h-0.5 bg-gray-400"></div>
      default: return null
    }
  }

  const acknowledgeAlert = async (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId 
        ? { 
            ...alert, 
            status: 'acknowledged',
            acknowledgedAt: new Date(),
            acknowledgedBy: '<EMAIL>'
          }
        : alert
    ))
  }

  const resolveAlert = async (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId 
        ? { 
            ...alert, 
            status: 'resolved',
            resolvedAt: new Date()
          }
        : alert
    ))
  }

  const formatDuration = (startTime: Date, endTime?: Date) => {
    const end = endTime || new Date()
    const duration = end.getTime() - startTime.getTime()
    const hours = Math.floor(duration / (1000 * 60 * 60))
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Monitor className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Advanced Monitoring & Alerting</h1>
            <p className="text-gray-400">Real-time monitoring, alert systems, and performance tracking</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadMonitoringData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/monitoring/rules/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Rule
          </Link>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Alerts</p>
              <p className="text-2xl font-bold text-white">
                {alerts.filter(a => a.status === 'active').length}
              </p>
              <p className="text-xs text-red-400 mt-1">
                {alerts.filter(a => a.severity === 'critical' && a.status === 'active').length} critical
              </p>
            </div>
            <AlertTriangle className="text-red-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">System Health</p>
              <p className="text-2xl font-bold text-white">
                {systemMetrics.filter(m => m.status === 'healthy').length}/{systemMetrics.length}
              </p>
              <p className="text-xs text-green-400 mt-1">Services healthy</p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Monitoring Rules</p>
              <p className="text-2xl font-bold text-white">
                {monitoringRules.filter(r => r.isActive).length}
              </p>
              <p className="text-xs text-blue-400 mt-1">Active rules</p>
            </div>
            <Settings className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Open Incidents</p>
              <p className="text-2xl font-bold text-white">
                {incidents.filter(i => i.status !== 'resolved').length}
              </p>
              <p className="text-xs text-yellow-400 mt-1">Require attention</p>
            </div>
            <XCircle className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: Monitor },
            { id: 'alerts', label: 'Alerts', icon: Bell, count: alerts.filter(a => a.status === 'active').length },
            { id: 'rules', label: 'Rules', icon: Settings, count: monitoringRules.length },
            { id: 'incidents', label: 'Incidents', icon: AlertTriangle, count: incidents.filter(i => i.status !== 'resolved').length },
            { id: 'metrics', label: 'Metrics', icon: BarChart3 }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && tab.count > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'dashboard' && (
        <div className="space-y-6">
          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {systemMetrics.map((metric) => (
              <motion.div
                key={metric.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-400">{metric.name}</h3>
                  <div className="flex items-center space-x-1">
                    {getTrendIcon(metric.trend)}
                    <span className={`text-xs ${getMetricStatusColor(metric.status)}`}>
                      {metric.status}
                    </span>
                  </div>
                </div>
                <div className="flex items-baseline space-x-2">
                  <span className="text-2xl font-bold text-white">{metric.value}</span>
                  <span className="text-sm text-gray-400">{metric.unit}</span>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Updated {metric.timestamp.toLocaleTimeString()}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Recent Alerts */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent Alerts</h3>
            <div className="space-y-3">
              {alerts.slice(0, 5).map((alert) => (
                <div key={alert.id} className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium text-white">{alert.title}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(alert.status)}`}>
                          {alert.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-300 mb-2">{alert.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Source: {alert.source}</span>
                        <span>Triggered: {formatDuration(alert.triggeredAt)} ago</span>
                        <span>Current: {alert.currentValue}{alert.threshold.unit}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      {alert.status === 'active' && (
                        <>
                          <button
                            onClick={() => acknowledgeAlert(alert.id)}
                            className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs"
                          >
                            Acknowledge
                          </button>
                          <button
                            onClick={() => resolveAlert(alert.id)}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs"
                          >
                            Resolve
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'alerts' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search alerts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterSeverity}
                  onChange={(e) => setFilterSeverity(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                  <option value="info">Info</option>
                </select>

                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="acknowledged">Acknowledged</option>
                  <option value="resolved">Resolved</option>
                  <option value="suppressed">Suppressed</option>
                </select>
              </div>
            </div>
          </div>

          {/* Alerts List */}
          <div className="space-y-4">
            {filteredAlerts.map((alert) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`border rounded-lg p-6 ${getSeverityColor(alert.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{alert.title}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(alert.status)}`}>
                        {alert.status}
                      </span>
                      <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">
                        {alert.severity}
                      </span>
                    </div>

                    <p className="text-gray-300 text-sm mb-3">{alert.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Source:</span>
                        <span className="text-white ml-1">{alert.source}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Current Value:</span>
                        <span className="text-white ml-1">{alert.currentValue}{alert.threshold.unit}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Threshold:</span>
                        <span className="text-white ml-1">{alert.threshold.operator} {alert.threshold.value}{alert.threshold.unit}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Duration:</span>
                        <span className="text-white ml-1">{formatDuration(alert.triggeredAt)}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Triggered: {alert.triggeredAt.toLocaleString()}</span>
                      {alert.acknowledgedAt && (
                        <span>Acknowledged: {alert.acknowledgedAt.toLocaleString()}</span>
                      )}
                      {alert.resolvedAt && (
                        <span>Resolved: {alert.resolvedAt.toLocaleString()}</span>
                      )}
                    </div>

                    {alert.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3">
                        {alert.tags.map((tag) => (
                          <span key={tag} className="inline-flex px-2 py-1 text-xs font-medium rounded bg-gray-700 text-gray-300">
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    {alert.status === 'active' && (
                      <>
                        <button
                          onClick={() => acknowledgeAlert(alert.id)}
                          className="bg-yellow-600 hover:bg-yellow-700 text-white p-2 rounded"
                          title="Acknowledge"
                        >
                          <CheckCircle size={16} />
                        </button>
                        <button
                          onClick={() => resolveAlert(alert.id)}
                          className="bg-green-600 hover:bg-green-700 text-white p-2 rounded"
                          title="Resolve"
                        >
                          <XCircle size={16} />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'rules' || activeTab === 'incidents' || activeTab === 'metrics') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'rules' && 'Monitoring Rules Management'}
            {activeTab === 'incidents' && 'Incident Management'}
            {activeTab === 'metrics' && 'System Metrics Dashboard'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'rules' && <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'incidents' && <AlertTriangle className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'metrics' && <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'rules' && 'Advanced Monitoring Rules'}
              {activeTab === 'incidents' && 'Incident Response System'}
              {activeTab === 'metrics' && 'Real-time Metrics Dashboard'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'rules' && 'Create and manage monitoring rules with custom thresholds, escalation policies, and notification channels.'}
              {activeTab === 'incidents' && 'Track and manage incidents with timeline, root cause analysis, and post-mortem documentation.'}
              {activeTab === 'metrics' && 'Real-time system metrics with historical data, trends, and performance analytics.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
