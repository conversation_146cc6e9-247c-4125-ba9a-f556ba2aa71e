'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Building, 
  Users, 
  Palette, 
  Globe,
  Settings,
  Shield,
  Database,
  Cloud,
  Layers,
  Eye,
  Edit,
  Plus,
  RefreshCw,
  Download,
  Upload,
  Filter,
  Search,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  BarChart3,
  Activity,
  Zap,
  Target,
  Key,
  Lock,
  Server,
  HardDrive,
  Cpu,
  Monitor
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface Tenant {
  id: string
  name: string
  displayName: string
  domain: string
  subdomain: string
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  plan: 'starter' | 'professional' | 'enterprise' | 'custom'
  createdAt: Date
  lastActivity: Date
  owner: {
    name: string
    email: string
    phone?: string
  }
  branding: TenantBranding
  configuration: TenantConfiguration
  resources: TenantResources
  metrics: TenantMetrics
  billing: {
    plan: string
    monthlyRevenue: number
    nextBillingDate: Date
    paymentStatus: 'current' | 'overdue' | 'failed'
  }
}

interface TenantBranding {
  logo: {
    primary: string
    secondary?: string
    favicon: string
  }
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    text: string
  }
  typography: {
    primaryFont: string
    secondaryFont: string
    headingFont: string
  }
  customCSS?: string
  emailTemplates: {
    header: string
    footer: string
    colors: Record<string, string>
  }
  whiteLabel: {
    enabled: boolean
    hideProviderBranding: boolean
    customFooter?: string
  }
}

interface TenantConfiguration {
  features: {
    enabled: string[]
    disabled: string[]
    limits: Record<string, number>
  }
  integrations: {
    enabled: string[]
    configurations: Record<string, any>
  }
  security: {
    mfaRequired: boolean
    passwordPolicy: {
      minLength: number
      requireSpecialChars: boolean
      requireNumbers: boolean
      requireUppercase: boolean
    }
    sessionTimeout: number
    ipWhitelist: string[]
  }
  notifications: {
    email: boolean
    sms: boolean
    webhook: boolean
    channels: string[]
  }
  customFields: {
    name: string
    type: 'text' | 'number' | 'boolean' | 'date' | 'select'
    required: boolean
    options?: string[]
  }[]
}

interface TenantResources {
  database: {
    type: 'shared' | 'dedicated'
    size: number // GB
    connections: number
    backupRetention: number // days
  }
  storage: {
    used: number // GB
    limit: number // GB
    type: 'shared' | 'dedicated'
  }
  compute: {
    cpu: number // cores
    memory: number // GB
    instances: number
  }
  bandwidth: {
    used: number // GB
    limit: number // GB
    monthlyReset: Date
  }
}

interface TenantMetrics {
  users: {
    total: number
    active: number
    newThisMonth: number
  }
  usage: {
    apiCalls: number
    storageUsed: number
    bandwidthUsed: number
    features: Record<string, number>
  }
  performance: {
    avgResponseTime: number
    uptime: number
    errorRate: number
  }
  business: {
    revenue: number
    orders: number
    conversionRate: number
  }
}

interface TenantTemplate {
  id: string
  name: string
  description: string
  category: 'ecommerce' | 'saas' | 'marketplace' | 'corporate' | 'custom'
  preview: string
  branding: Partial<TenantBranding>
  configuration: Partial<TenantConfiguration>
  features: string[]
  isDefault: boolean
  createdAt: Date
  usageCount: number
}

interface IsolationPolicy {
  id: string
  name: string
  description: string
  type: 'data' | 'compute' | 'network' | 'application'
  level: 'shared' | 'isolated' | 'dedicated'
  rules: {
    resource: string
    isolation: 'strict' | 'soft' | 'none'
    encryption: boolean
    accessControl: string[]
  }[]
  compliance: string[]
  isActive: boolean
  createdAt: Date
  lastUpdated: Date
}

export default function MultiTenantPage() {
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [tenantTemplates, setTenantTemplates] = useState<TenantTemplate[]>([])
  const [isolationPolicies, setIsolationPolicies] = useState<IsolationPolicy[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'tenants' | 'templates' | 'isolation' | 'analytics'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterPlan, setFilterPlan] = useState<string>('all')

  useEffect(() => {
    loadMultiTenantData()
  }, [])

  const loadMultiTenantData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual multi-tenant API integration
      const mockTenants: Tenant[] = [
        {
          id: 'tenant_001',
          name: 'acme-corp',
          displayName: 'Acme Corporation',
          domain: 'acme.syndicaps.com',
          subdomain: 'acme',
          status: 'active',
          plan: 'enterprise',
          createdAt: new Date('2024-10-15'),
          lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
          owner: {
            name: 'John Smith',
            email: '<EMAIL>',
            phone: '******-0123'
          },
          branding: {
            logo: {
              primary: '/logos/acme-primary.png',
              favicon: '/logos/acme-favicon.ico'
            },
            colors: {
              primary: '#1E40AF',
              secondary: '#3B82F6',
              accent: '#F59E0B',
              background: '#F8FAFC',
              text: '#1F2937'
            },
            typography: {
              primaryFont: 'Inter',
              secondaryFont: 'Inter',
              headingFont: 'Inter'
            },
            emailTemplates: {
              header: '<div style="background: #1E40AF; padding: 20px;">',
              footer: '<div style="background: #F3F4F6; padding: 10px;">',
              colors: { primary: '#1E40AF', secondary: '#3B82F6' }
            },
            whiteLabel: {
              enabled: true,
              hideProviderBranding: true,
              customFooter: '© 2025 Acme Corporation. All rights reserved.'
            }
          },
          configuration: {
            features: {
              enabled: ['advanced_analytics', 'custom_integrations', 'priority_support'],
              disabled: [],
              limits: { users: 1000, storage: 500, api_calls: 100000 }
            },
            integrations: {
              enabled: ['salesforce', 'hubspot', 'slack'],
              configurations: {}
            },
            security: {
              mfaRequired: true,
              passwordPolicy: {
                minLength: 12,
                requireSpecialChars: true,
                requireNumbers: true,
                requireUppercase: true
              },
              sessionTimeout: 480,
              ipWhitelist: ['***********/24', '10.0.0.0/8']
            },
            notifications: {
              email: true,
              sms: true,
              webhook: true,
              channels: ['email', 'slack', 'webhook']
            },
            customFields: [
              { name: 'Department', type: 'select', required: true, options: ['Sales', 'Marketing', 'Support'] },
              { name: 'Employee ID', type: 'text', required: false }
            ]
          },
          resources: {
            database: {
              type: 'dedicated',
              size: 100,
              connections: 50,
              backupRetention: 30
            },
            storage: {
              used: 245,
              limit: 500,
              type: 'dedicated'
            },
            compute: {
              cpu: 8,
              memory: 32,
              instances: 4
            },
            bandwidth: {
              used: 1250,
              limit: 5000,
              monthlyReset: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)
            }
          },
          metrics: {
            users: {
              total: 245,
              active: 189,
              newThisMonth: 23
            },
            usage: {
              apiCalls: 45230,
              storageUsed: 245,
              bandwidthUsed: 1250,
              features: { analytics: 1250, integrations: 890, reports: 456 }
            },
            performance: {
              avgResponseTime: 120,
              uptime: 99.9,
              errorRate: 0.02
            },
            business: {
              revenue: 15000,
              orders: 1250,
              conversionRate: 3.4
            }
          },
          billing: {
            plan: 'Enterprise',
            monthlyRevenue: 15000,
            nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
            paymentStatus: 'current'
          }
        },
        {
          id: 'tenant_002',
          name: 'startup-xyz',
          displayName: 'StartupXYZ',
          domain: 'startup.syndicaps.com',
          subdomain: 'startup',
          status: 'active',
          plan: 'professional',
          createdAt: new Date('2025-01-05'),
          lastActivity: new Date(Date.now() - 30 * 60 * 1000),
          owner: {
            name: 'Sarah Johnson',
            email: '<EMAIL>'
          },
          branding: {
            logo: {
              primary: '/logos/startup-primary.png',
              favicon: '/logos/startup-favicon.ico'
            },
            colors: {
              primary: '#7C3AED',
              secondary: '#A855F7',
              accent: '#EC4899',
              background: '#FFFFFF',
              text: '#111827'
            },
            typography: {
              primaryFont: 'Poppins',
              secondaryFont: 'Poppins',
              headingFont: 'Poppins'
            },
            emailTemplates: {
              header: '<div style="background: #7C3AED; padding: 20px;">',
              footer: '<div style="background: #F9FAFB; padding: 10px;">',
              colors: { primary: '#7C3AED', secondary: '#A855F7' }
            },
            whiteLabel: {
              enabled: false,
              hideProviderBranding: false
            }
          },
          configuration: {
            features: {
              enabled: ['basic_analytics', 'email_support'],
              disabled: ['custom_integrations', 'priority_support'],
              limits: { users: 50, storage: 50, api_calls: 10000 }
            },
            integrations: {
              enabled: ['google_analytics', 'mailchimp'],
              configurations: {}
            },
            security: {
              mfaRequired: false,
              passwordPolicy: {
                minLength: 8,
                requireSpecialChars: false,
                requireNumbers: true,
                requireUppercase: false
              },
              sessionTimeout: 240,
              ipWhitelist: []
            },
            notifications: {
              email: true,
              sms: false,
              webhook: false,
              channels: ['email']
            },
            customFields: []
          },
          resources: {
            database: {
              type: 'shared',
              size: 10,
              connections: 10,
              backupRetention: 7
            },
            storage: {
              used: 12,
              limit: 50,
              type: 'shared'
            },
            compute: {
              cpu: 2,
              memory: 4,
              instances: 1
            },
            bandwidth: {
              used: 125,
              limit: 500,
              monthlyReset: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000)
            }
          },
          metrics: {
            users: {
              total: 12,
              active: 8,
              newThisMonth: 5
            },
            usage: {
              apiCalls: 2340,
              storageUsed: 12,
              bandwidthUsed: 125,
              features: { analytics: 234, reports: 45 }
            },
            performance: {
              avgResponseTime: 95,
              uptime: 99.5,
              errorRate: 0.05
            },
            business: {
              revenue: 500,
              orders: 45,
              conversionRate: 2.1
            }
          },
          billing: {
            plan: 'Professional',
            monthlyRevenue: 500,
            nextBillingDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000),
            paymentStatus: 'current'
          }
        }
      ]

      const mockTenantTemplates: TenantTemplate[] = [
        {
          id: 'template_001',
          name: 'E-commerce Starter',
          description: 'Complete e-commerce setup with product catalog, shopping cart, and payment processing',
          category: 'ecommerce',
          preview: '/templates/ecommerce-preview.png',
          branding: {
            colors: {
              primary: '#059669',
              secondary: '#10B981',
              accent: '#F59E0B',
              background: '#F9FAFB',
              text: '#111827'
            },
            typography: {
              primaryFont: 'Inter',
              secondaryFont: 'Inter',
              headingFont: 'Inter'
            }
          },
          configuration: {
            features: {
              enabled: ['product_catalog', 'shopping_cart', 'payment_processing', 'inventory_management'],
              disabled: [],
              limits: { products: 1000, orders: 10000 }
            }
          },
          features: ['Product Management', 'Order Processing', 'Payment Gateway', 'Inventory Tracking'],
          isDefault: true,
          createdAt: new Date('2024-12-01'),
          usageCount: 45
        },
        {
          id: 'template_002',
          name: 'SaaS Platform',
          description: 'Software-as-a-Service platform with user management, subscriptions, and analytics',
          category: 'saas',
          preview: '/templates/saas-preview.png',
          branding: {
            colors: {
              primary: '#3B82F6',
              secondary: '#60A5FA',
              accent: '#8B5CF6',
              background: '#FFFFFF',
              text: '#1F2937'
            }
          },
          configuration: {
            features: {
              enabled: ['user_management', 'subscriptions', 'analytics', 'api_access'],
              disabled: [],
              limits: { users: 10000, api_calls: 1000000 }
            }
          },
          features: ['User Management', 'Subscription Billing', 'API Access', 'Advanced Analytics'],
          isDefault: false,
          createdAt: new Date('2024-12-01'),
          usageCount: 23
        }
      ]

      const mockIsolationPolicies: IsolationPolicy[] = [
        {
          id: 'policy_001',
          name: 'Enterprise Data Isolation',
          description: 'Strict data isolation for enterprise tenants with dedicated resources',
          type: 'data',
          level: 'dedicated',
          rules: [
            {
              resource: 'database',
              isolation: 'strict',
              encryption: true,
              accessControl: ['tenant_admin', 'system_admin']
            },
            {
              resource: 'storage',
              isolation: 'strict',
              encryption: true,
              accessControl: ['tenant_admin', 'system_admin']
            }
          ],
          compliance: ['SOC2', 'GDPR', 'HIPAA'],
          isActive: true,
          createdAt: new Date('2024-11-01'),
          lastUpdated: new Date('2024-12-15')
        },
        {
          id: 'policy_002',
          name: 'Standard Shared Resources',
          description: 'Shared resources with logical isolation for standard tenants',
          type: 'compute',
          level: 'shared',
          rules: [
            {
              resource: 'application',
              isolation: 'soft',
              encryption: false,
              accessControl: ['tenant_user', 'tenant_admin']
            }
          ],
          compliance: ['SOC2'],
          isActive: true,
          createdAt: new Date('2024-11-01'),
          lastUpdated: new Date('2024-12-01')
        }
      ]

      setTenants(mockTenants)
      setTenantTemplates(mockTenantTemplates)
      setIsolationPolicies(mockIsolationPolicies)
    } catch (error) {
      console.error('Error loading multi-tenant data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.domain.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' || tenant.status === filterStatus
    const matchesPlan = filterPlan === 'all' || tenant.plan === filterPlan
    return matchesSearch && matchesStatus && matchesPlan
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-900/20'
      case 'inactive': return 'text-gray-400 bg-gray-900/20'
      case 'suspended': return 'text-red-400 bg-red-900/20'
      case 'pending': return 'text-yellow-400 bg-yellow-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'enterprise': return 'text-purple-400 bg-purple-900/20'
      case 'professional': return 'text-blue-400 bg-blue-900/20'
      case 'starter': return 'text-green-400 bg-green-900/20'
      case 'custom': return 'text-yellow-400 bg-yellow-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getIsolationLevelColor = (level: string) => {
    switch (level) {
      case 'dedicated': return 'text-red-400 bg-red-900/20'
      case 'isolated': return 'text-yellow-400 bg-yellow-900/20'
      case 'shared': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  const formatBytes = (bytes: number) => {
    if (bytes < 1024) return `${bytes} GB`
    return `${(bytes / 1024).toFixed(1)} TB`
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const calculateResourceUsage = (used: number, limit: number) => {
    return (used / limit) * 100
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Building className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Multi-Tenant Architecture</h1>
            <p className="text-gray-400">Tenant management, white-labeling, resource isolation, and customization</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadMultiTenantData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/multi-tenant/tenants/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Tenant
          </Link>
        </div>
      </div>

      {/* Multi-Tenant Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Tenants</p>
              <p className="text-2xl font-bold text-white">
                {tenants.filter(t => t.status === 'active').length}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {tenants.filter(t => t.status === 'pending').length} pending
              </p>
            </div>
            <Building className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Users</p>
              <p className="text-2xl font-bold text-white">
                {tenants.reduce((sum, t) => sum + t.metrics.users.total, 0).toLocaleString()}
              </p>
              <p className="text-xs text-blue-400 mt-1">Across all tenants</p>
            </div>
            <Users className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Monthly Revenue</p>
              <p className="text-2xl font-bold text-white">
                {formatCurrency(tenants.reduce((sum, t) => sum + t.billing.monthlyRevenue, 0))}
              </p>
              <p className="text-xs text-green-400 mt-1">All tenants combined</p>
            </div>
            <BarChart3 className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Resource Usage</p>
              <p className="text-2xl font-bold text-white">
                {formatPercentage(
                  tenants.reduce((sum, t) => sum + calculateResourceUsage(t.resources.storage.used, t.resources.storage.limit), 0) / tenants.length
                )}
              </p>
              <p className="text-xs text-purple-400 mt-1">Average storage</p>
            </div>
            <Database className="text-purple-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'tenants', label: 'Tenants', icon: Building, count: tenants.length },
            { id: 'templates', label: 'Templates', icon: Layers, count: tenantTemplates.length },
            { id: 'isolation', label: 'Isolation Policies', icon: Shield, count: isolationPolicies.length },
            { id: 'analytics', label: 'Analytics', icon: Activity }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Tenant Status Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Tenant Distribution by Plan</h3>
              <div className="space-y-3">
                {['enterprise', 'professional', 'starter', 'custom'].map((plan) => {
                  const count = tenants.filter(t => t.plan === plan).length
                  const percentage = tenants.length > 0 ? (count / tenants.length) * 100 : 0
                  return (
                    <div key={plan} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className={`w-3 h-3 rounded-full ${getPlanColor(plan).split(' ')[1]}`}></span>
                        <span className="text-sm text-white capitalize">{plan}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium text-white">{count}</span>
                        <span className="text-xs text-gray-400 ml-2">({percentage.toFixed(1)}%)</span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Resource Utilization</h3>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-400">Storage</span>
                    <span className="text-white">
                      {formatBytes(tenants.reduce((sum, t) => sum + t.resources.storage.used, 0))} /
                      {formatBytes(tenants.reduce((sum, t) => sum + t.resources.storage.limit, 0))}
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{
                        width: `${Math.min(100, (tenants.reduce((sum, t) => sum + t.resources.storage.used, 0) / tenants.reduce((sum, t) => sum + t.resources.storage.limit, 0)) * 100)}%`
                      }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-400">Bandwidth</span>
                    <span className="text-white">
                      {formatBytes(tenants.reduce((sum, t) => sum + t.resources.bandwidth.used, 0))} /
                      {formatBytes(tenants.reduce((sum, t) => sum + t.resources.bandwidth.limit, 0))}
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{
                        width: `${Math.min(100, (tenants.reduce((sum, t) => sum + t.resources.bandwidth.used, 0) / tenants.reduce((sum, t) => sum + t.resources.bandwidth.limit, 0)) * 100)}%`
                      }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-400">CPU Cores</span>
                    <span className="text-white">
                      {tenants.reduce((sum, t) => sum + t.resources.compute.cpu, 0)} cores allocated
                    </span>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-400">Memory</span>
                    <span className="text-white">
                      {tenants.reduce((sum, t) => sum + t.resources.compute.memory, 0)} GB allocated
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Tenant Activity */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent Tenant Activity</h3>
            <div className="space-y-3">
              {tenants
                .sort((a, b) => new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime())
                .slice(0, 5)
                .map((tenant) => (
                  <div key={tenant.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(tenant.status).split(' ')[1]}`}></div>
                      <div>
                        <p className="text-sm font-medium text-white">{tenant.displayName}</p>
                        <p className="text-xs text-gray-400">{tenant.domain}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-white">{tenant.metrics.users.active} active users</p>
                      <p className="text-xs text-gray-400">
                        Last activity: {tenant.lastActivity.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          {/* Template Usage */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Popular Tenant Templates</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tenantTemplates
                .sort((a, b) => b.usageCount - a.usageCount)
                .slice(0, 6)
                .map((template) => (
                  <div key={template.id} className="bg-gray-700 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-white">{template.name}</h4>
                      <span className="text-xs text-gray-400">{template.usageCount} uses</span>
                    </div>
                    <p className="text-xs text-gray-400 mb-3">{template.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded">
                        {template.category}
                      </span>
                      {template.isDefault && (
                        <span className="text-xs bg-green-900/30 text-green-300 px-2 py-1 rounded">
                          Default
                        </span>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'tenants' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search tenants..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                  <option value="pending">Pending</option>
                </select>

                <select
                  value={filterPlan}
                  onChange={(e) => setFilterPlan(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Plans</option>
                  <option value="enterprise">Enterprise</option>
                  <option value="professional">Professional</option>
                  <option value="starter">Starter</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
            </div>
          </div>

          {/* Tenants List */}
          <div className="space-y-4">
            {filteredTenants.map((tenant) => (
              <motion.div
                key={tenant.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{tenant.displayName}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(tenant.status)}`}>
                        {tenant.status}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPlanColor(tenant.plan)}`}>
                        {tenant.plan}
                      </span>
                      {tenant.branding.whiteLabel.enabled && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-purple-300 bg-purple-900/20">
                          White Label
                        </span>
                      )}
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{tenant.domain}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Users:</span>
                        <span className="text-white ml-1">{tenant.metrics.users.total} ({tenant.metrics.users.active} active)</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Revenue:</span>
                        <span className="text-white ml-1">{formatCurrency(tenant.billing.monthlyRevenue)}/mo</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Storage:</span>
                        <span className="text-white ml-1">
                          {formatBytes(tenant.resources.storage.used)} / {formatBytes(tenant.resources.storage.limit)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Uptime:</span>
                        <span className="text-white ml-1">{formatPercentage(tenant.metrics.performance.uptime)}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Created: {tenant.createdAt.toLocaleDateString()}</span>
                      <span>Owner: {tenant.owner.name}</span>
                      <span>Last activity: {tenant.lastActivity.toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Configure">
                      <Settings size={16} />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Customize Branding">
                      <Palette size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'templates' || activeTab === 'isolation' || activeTab === 'analytics') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'templates' && 'Tenant Template Management'}
            {activeTab === 'isolation' && 'Resource Isolation Policies'}
            {activeTab === 'analytics' && 'Multi-Tenant Analytics'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'templates' && <Layers className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'isolation' && <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'analytics' && <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'templates' && 'Pre-configured Tenant Templates'}
              {activeTab === 'isolation' && 'Advanced Resource Isolation'}
              {activeTab === 'analytics' && 'Cross-Tenant Analytics & Insights'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'templates' && 'Create and manage reusable tenant templates with pre-configured branding, features, and settings.'}
              {activeTab === 'isolation' && 'Define and enforce resource isolation policies for data security, compliance, and performance.'}
              {activeTab === 'analytics' && 'Monitor tenant performance, resource usage, and business metrics across all tenants.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
