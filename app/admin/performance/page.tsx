'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Zap, 
  Server, 
  Database, 
  Globe,
  Cpu,
  HardDrive,
  Wifi,
  Monitor,
  Activity,
  BarChart3,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  Clock,
  Users,
  Target,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ArrowUp,
  ArrowDown,
  Layers,
  Cloud,
  Shield,
  Key,
  Lock,
  Package,
  Download,
  Upload
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface CacheLayer {
  id: string
  name: string
  type: 'redis' | 'memcached' | 'cdn' | 'browser' | 'application' | 'database'
  status: 'active' | 'inactive' | 'error' | 'warming'
  configuration: {
    ttl: number // seconds
    maxSize: number // MB
    evictionPolicy: 'lru' | 'lfu' | 'fifo' | 'random'
    compression: boolean
    encryption: boolean
    replication: {
      enabled: boolean
      nodes: number
      strategy: 'master_slave' | 'cluster' | 'sentinel'
    }
  }
  metrics: {
    hitRate: number
    missRate: number
    totalRequests: number
    avgResponseTime: number
    memoryUsage: number
    evictions: number
    connections: number
  }
  endpoints: string[]
  regions: string[]
  createdAt: Date
  lastOptimized: Date
}

interface CDNConfiguration {
  id: string
  name: string
  provider: 'cloudflare' | 'aws_cloudfront' | 'azure_cdn' | 'google_cdn' | 'custom'
  status: 'active' | 'inactive' | 'configuring' | 'error'
  domains: string[]
  origins: {
    domain: string
    protocol: 'http' | 'https'
    port: number
    path: string
    weight: number
    health: 'healthy' | 'unhealthy' | 'unknown'
  }[]
  caching: {
    rules: {
      pattern: string
      ttl: number
      cacheKey: string[]
      bypassConditions: string[]
    }[]
    compression: {
      enabled: boolean
      types: string[]
      level: number
    }
    optimization: {
      minify: boolean
      imageOptimization: boolean
      webpConversion: boolean
      brotliCompression: boolean
    }
  }
  security: {
    waf: boolean
    ddosProtection: boolean
    rateLimiting: {
      enabled: boolean
      requests: number
      window: number
    }
    ssl: {
      enabled: boolean
      certificate: string
      hsts: boolean
    }
  }
  analytics: {
    bandwidth: number
    requests: number
    cacheHitRatio: number
    avgResponseTime: number
    errorRate: number
    topCountries: { country: string; requests: number }[]
  }
  costs: {
    monthly: number
    bandwidth: number
    requests: number
    storage: number
  }
}

interface DatabaseOptimization {
  id: string
  database: string
  type: 'postgresql' | 'mysql' | 'mongodb' | 'redis' | 'elasticsearch'
  status: 'optimized' | 'needs_attention' | 'critical' | 'analyzing'
  performance: {
    queryTime: {
      avg: number
      p95: number
      p99: number
      slowQueries: number
    }
    connections: {
      active: number
      idle: number
      max: number
      poolSize: number
    }
    throughput: {
      reads: number
      writes: number
      transactions: number
    }
    resources: {
      cpuUsage: number
      memoryUsage: number
      diskUsage: number
      iops: number
    }
  }
  optimizations: {
    indexes: {
      missing: { table: string; columns: string[]; impact: 'high' | 'medium' | 'low' }[]
      unused: { table: string; index: string; size: number }[]
      duplicates: { table: string; indexes: string[] }[]
    }
    queries: {
      slow: { query: string; time: number; frequency: number; optimization: string }[]
      inefficient: { query: string; cost: number; suggestion: string }[]
    }
    schema: {
      recommendations: { type: string; description: string; impact: string }[]
      partitioning: { table: string; strategy: string; benefit: string }[]
    }
  }
  monitoring: {
    alerts: { type: string; threshold: number; current: number; status: 'ok' | 'warning' | 'critical' }[]
    trends: { metric: string; trend: 'improving' | 'stable' | 'degrading'; change: number }[]
  }
}

interface ScalingConfiguration {
  id: string
  service: string
  type: 'horizontal' | 'vertical' | 'auto'
  status: 'active' | 'inactive' | 'scaling' | 'error'
  current: {
    instances: number
    cpu: number
    memory: number
    storage: number
  }
  limits: {
    minInstances: number
    maxInstances: number
    maxCpu: number
    maxMemory: number
  }
  triggers: {
    cpu: { threshold: number; duration: number }
    memory: { threshold: number; duration: number }
    requests: { threshold: number; duration: number }
    responseTime: { threshold: number; duration: number }
  }
  policies: {
    scaleUp: {
      cooldown: number
      increment: number
      strategy: 'aggressive' | 'conservative' | 'balanced'
    }
    scaleDown: {
      cooldown: number
      decrement: number
      strategy: 'aggressive' | 'conservative' | 'balanced'
    }
  }
  history: {
    timestamp: Date
    action: 'scale_up' | 'scale_down'
    from: number
    to: number
    trigger: string
    duration: number
  }[]
  costs: {
    current: number
    projected: number
    savings: number
  }
}

interface PerformanceMetrics {
  timeRange: '1h' | '24h' | '7d' | '30d'
  overall: {
    score: number
    grade: 'A' | 'B' | 'C' | 'D' | 'F'
    trends: {
      responseTime: { current: number; change: number; trend: 'up' | 'down' | 'stable' }
      throughput: { current: number; change: number; trend: 'up' | 'down' | 'stable' }
      errorRate: { current: number; change: number; trend: 'up' | 'down' | 'stable' }
      availability: { current: number; change: number; trend: 'up' | 'down' | 'stable' }
    }
  }
  frontend: {
    pageLoadTime: number
    firstContentfulPaint: number
    largestContentfulPaint: number
    cumulativeLayoutShift: number
    firstInputDelay: number
    bundleSize: number
    cacheHitRate: number
  }
  backend: {
    apiResponseTime: number
    databaseQueryTime: number
    cacheHitRate: number
    queueProcessingTime: number
    memoryUsage: number
    cpuUsage: number
    diskUsage: number
  }
  infrastructure: {
    serverUptime: number
    networkLatency: number
    bandwidthUsage: number
    storageUsage: number
    backupStatus: 'success' | 'failed' | 'in_progress'
    securityScore: number
  }
}

interface OptimizationRecommendation {
  id: string
  category: 'caching' | 'database' | 'frontend' | 'backend' | 'infrastructure' | 'cdn'
  priority: 'critical' | 'high' | 'medium' | 'low'
  title: string
  description: string
  impact: {
    performance: number // percentage improvement
    cost: number // cost change (positive = savings)
    complexity: 'low' | 'medium' | 'high'
    timeToImplement: number // hours
  }
  implementation: {
    steps: string[]
    requirements: string[]
    risks: string[]
    rollbackPlan: string
  }
  metrics: {
    before: Record<string, number>
    expectedAfter: Record<string, number>
    actualAfter?: Record<string, number>
  }
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
  assignedTo?: string
  createdAt: Date
  implementedAt?: Date
}

export default function PerformancePage() {
  const [cacheLayers, setCacheLayers] = useState<CacheLayer[]>([])
  const [cdnConfigurations, setCdnConfigurations] = useState<CDNConfiguration[]>([])
  const [databaseOptimizations, setDatabaseOptimizations] = useState<DatabaseOptimization[]>([])
  const [scalingConfigurations, setScalingConfigurations] = useState<ScalingConfiguration[]>([])
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null)
  const [recommendations, setRecommendations] = useState<OptimizationRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'caching' | 'cdn' | 'database' | 'scaling' | 'recommendations'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadPerformanceData()
  }, [])

  const loadPerformanceData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual performance API integration
      const mockPerformanceMetrics: PerformanceMetrics = {
        timeRange: '24h',
        overall: {
          score: 92,
          grade: 'A',
          trends: {
            responseTime: { current: 120, change: -15, trend: 'down' },
            throughput: { current: 1250, change: 8, trend: 'up' },
            errorRate: { current: 0.02, change: -0.01, trend: 'down' },
            availability: { current: 99.9, change: 0.1, trend: 'up' }
          }
        },
        frontend: {
          pageLoadTime: 1.2,
          firstContentfulPaint: 0.8,
          largestContentfulPaint: 1.5,
          cumulativeLayoutShift: 0.05,
          firstInputDelay: 45,
          bundleSize: 245,
          cacheHitRate: 89.5
        },
        backend: {
          apiResponseTime: 120,
          databaseQueryTime: 45,
          cacheHitRate: 94.2,
          queueProcessingTime: 85,
          memoryUsage: 68,
          cpuUsage: 35,
          diskUsage: 45
        },
        infrastructure: {
          serverUptime: 99.9,
          networkLatency: 25,
          bandwidthUsage: 2.4,
          storageUsage: 45,
          backupStatus: 'success',
          securityScore: 95
        }
      }

      setPerformanceMetrics(mockPerformanceMetrics)
    } catch (error) {
      console.error('Error loading performance data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Zap className="w-8 h-8 text-yellow-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Performance Optimization & Scaling</h1>
            <p className="text-gray-400">Advanced caching, CDN integration, database optimization, and horizontal scaling</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadPerformanceData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/performance/optimizer"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Run Optimizer
          </Link>
        </div>
      </div>

      {/* Performance Overview */}
      {performanceMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800 p-6 rounded-lg"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Performance Score</p>
                <p className={`text-3xl font-bold ${performanceMetrics.overall.grade === 'A' ? 'text-green-400' :
                  performanceMetrics.overall.grade === 'B' ? 'text-blue-400' :
                  performanceMetrics.overall.grade === 'C' ? 'text-yellow-400' : 'text-red-400'}`}>
                  {performanceMetrics.overall.score}
                </p>
                <p className={`text-sm font-medium ${performanceMetrics.overall.grade === 'A' ? 'text-green-400' :
                  performanceMetrics.overall.grade === 'B' ? 'text-blue-400' :
                  performanceMetrics.overall.grade === 'C' ? 'text-yellow-400' : 'text-red-400'}`}>
                  Grade {performanceMetrics.overall.grade}
                </p>
              </div>
              <Target className="text-green-400" size={24} />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gray-800 p-6 rounded-lg"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Response Time</p>
                <p className="text-2xl font-bold text-white">
                  {performanceMetrics.overall.trends.responseTime.current}ms
                </p>
                <div className="flex items-center mt-1">
                  {performanceMetrics.overall.trends.responseTime.trend === 'down' ? (
                    <ArrowDown className="text-green-400" size={16} />
                  ) : performanceMetrics.overall.trends.responseTime.trend === 'up' ? (
                    <ArrowUp className="text-red-400" size={16} />
                  ) : (
                    <div className="w-4 h-4 bg-gray-400 rounded-full" />
                  )}
                  <span className={`text-xs ml-1 ${
                    performanceMetrics.overall.trends.responseTime.trend === 'down' ? 'text-green-400' :
                    performanceMetrics.overall.trends.responseTime.trend === 'up' ? 'text-red-400' : 'text-gray-400'
                  }`}>
                    {Math.abs(performanceMetrics.overall.trends.responseTime.change)}ms
                  </span>
                </div>
              </div>
              <Clock className="text-blue-400" size={24} />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gray-800 p-6 rounded-lg"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Throughput</p>
                <p className="text-2xl font-bold text-white">
                  {performanceMetrics.overall.trends.throughput.current}
                </p>
                <div className="flex items-center mt-1">
                  {performanceMetrics.overall.trends.throughput.trend === 'up' ? (
                    <ArrowUp className="text-green-400" size={16} />
                  ) : performanceMetrics.overall.trends.throughput.trend === 'down' ? (
                    <ArrowDown className="text-red-400" size={16} />
                  ) : (
                    <div className="w-4 h-4 bg-gray-400 rounded-full" />
                  )}
                  <span className={`text-xs ml-1 ${
                    performanceMetrics.overall.trends.throughput.trend === 'up' ? 'text-green-400' :
                    performanceMetrics.overall.trends.throughput.trend === 'down' ? 'text-red-400' : 'text-gray-400'
                  }`}>
                    +{performanceMetrics.overall.trends.throughput.change} req/s
                  </span>
                </div>
              </div>
              <Activity className="text-purple-400" size={24} />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gray-800 p-6 rounded-lg"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Availability</p>
                <p className="text-2xl font-bold text-white">
                  {performanceMetrics.infrastructure.serverUptime}%
                </p>
                <div className="flex items-center mt-1">
                  {performanceMetrics.overall.trends.availability.trend === 'up' ? (
                    <ArrowUp className="text-green-400" size={16} />
                  ) : performanceMetrics.overall.trends.availability.trend === 'down' ? (
                    <ArrowDown className="text-red-400" size={16} />
                  ) : (
                    <div className="w-4 h-4 bg-gray-400 rounded-full" />
                  )}
                  <span className={`text-xs ml-1 ${
                    performanceMetrics.overall.trends.availability.trend === 'up' ? 'text-green-400' :
                    performanceMetrics.overall.trends.availability.trend === 'down' ? 'text-red-400' : 'text-gray-400'
                  }`}>
                    +{performanceMetrics.overall.trends.availability.change}%
                  </span>
                </div>
              </div>
              <CheckCircle className="text-green-400" size={24} />
            </div>
          </motion.div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'caching', label: 'Caching', icon: Database },
            { id: 'cdn', label: 'CDN', icon: Globe },
            { id: 'database', label: 'Database', icon: Server },
            { id: 'scaling', label: 'Scaling', icon: Layers },
            { id: 'recommendations', label: 'Recommendations', icon: Target }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && performanceMetrics && (
        <div className="space-y-6">
          {/* Performance Metrics Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Frontend Performance */}
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Monitor className="mr-2 text-purple-400" size={20} />
                Frontend Performance
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Page Load Time:</span>
                  <span className="text-white font-medium">{performanceMetrics.frontend.pageLoadTime}s</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">First Contentful Paint:</span>
                  <span className="text-white font-medium">{performanceMetrics.frontend.firstContentfulPaint}s</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Largest Contentful Paint:</span>
                  <span className="text-white font-medium">{performanceMetrics.frontend.largestContentfulPaint}s</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">First Input Delay:</span>
                  <span className="text-white font-medium">{performanceMetrics.frontend.firstInputDelay}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Bundle Size:</span>
                  <span className="text-white font-medium">{performanceMetrics.frontend.bundleSize}KB</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Cache Hit Rate:</span>
                  <span className="text-green-400 font-medium">{performanceMetrics.frontend.cacheHitRate}%</span>
                </div>
              </div>
            </div>

            {/* Backend Performance */}
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Server className="mr-2 text-blue-400" size={20} />
                Backend Performance
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">API Response Time:</span>
                  <span className="text-white font-medium">{performanceMetrics.backend.apiResponseTime}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Database Query Time:</span>
                  <span className="text-white font-medium">{performanceMetrics.backend.databaseQueryTime}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Cache Hit Rate:</span>
                  <span className="text-green-400 font-medium">{performanceMetrics.backend.cacheHitRate}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Queue Processing:</span>
                  <span className="text-white font-medium">{performanceMetrics.backend.queueProcessingTime}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">CPU Usage:</span>
                  <span className={`font-medium ${performanceMetrics.backend.cpuUsage > 80 ? 'text-red-400' : 'text-green-400'}`}>
                    {performanceMetrics.backend.cpuUsage}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Memory Usage:</span>
                  <span className={`font-medium ${performanceMetrics.backend.memoryUsage > 85 ? 'text-red-400' : 'text-green-400'}`}>
                    {performanceMetrics.backend.memoryUsage}%
                  </span>
                </div>
              </div>
            </div>

            {/* Infrastructure Health */}
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Cloud className="mr-2 text-green-400" size={20} />
                Infrastructure Health
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Server Uptime:</span>
                  <span className="text-green-400 font-medium">{performanceMetrics.infrastructure.serverUptime}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Network Latency:</span>
                  <span className="text-white font-medium">{performanceMetrics.infrastructure.networkLatency}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Bandwidth Usage:</span>
                  <span className="text-white font-medium">{performanceMetrics.infrastructure.bandwidthUsage}TB</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Storage Usage:</span>
                  <span className={`font-medium ${performanceMetrics.infrastructure.storageUsage > 80 ? 'text-red-400' : 'text-green-400'}`}>
                    {performanceMetrics.infrastructure.storageUsage}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Backup Status:</span>
                  <span className={`font-medium ${
                    performanceMetrics.infrastructure.backupStatus === 'success' ? 'text-green-400' :
                    performanceMetrics.infrastructure.backupStatus === 'failed' ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {performanceMetrics.infrastructure.backupStatus}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Security Score:</span>
                  <span className="text-green-400 font-medium">{performanceMetrics.infrastructure.securityScore}/100</span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Performance Optimization Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link
                href="/admin/performance/cache-optimizer"
                className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Database size={20} />
                <span>Optimize Caching</span>
              </Link>

              <Link
                href="/admin/performance/cdn-config"
                className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Globe size={20} />
                <span>Configure CDN</span>
              </Link>

              <Link
                href="/admin/performance/db-optimizer"
                className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Server size={20} />
                <span>Database Tuning</span>
              </Link>

              <Link
                href="/admin/performance/auto-scaling"
                className="bg-yellow-600 hover:bg-yellow-700 text-white p-4 rounded-lg flex items-center space-x-3 transition-colors"
              >
                <Layers size={20} />
                <span>Auto Scaling</span>
              </Link>
            </div>
          </div>

          {/* Performance Trends */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Performance Trends (24h)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="text-blue-400 mr-2" size={20} />
                  <span className="text-gray-400">Response Time</span>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {performanceMetrics.overall.trends.responseTime.current}ms
                </div>
                <div className="flex items-center justify-center">
                  {performanceMetrics.overall.trends.responseTime.trend === 'down' ? (
                    <ArrowDown className="text-green-400 mr-1" size={16} />
                  ) : (
                    <ArrowUp className="text-red-400 mr-1" size={16} />
                  )}
                  <span className={`text-sm ${
                    performanceMetrics.overall.trends.responseTime.trend === 'down' ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {Math.abs(performanceMetrics.overall.trends.responseTime.change)}ms
                  </span>
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Activity className="text-purple-400 mr-2" size={20} />
                  <span className="text-gray-400">Throughput</span>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {performanceMetrics.overall.trends.throughput.current}
                </div>
                <div className="flex items-center justify-center">
                  <ArrowUp className="text-green-400 mr-1" size={16} />
                  <span className="text-sm text-green-400">
                    +{performanceMetrics.overall.trends.throughput.change} req/s
                  </span>
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <AlertTriangle className="text-yellow-400 mr-2" size={20} />
                  <span className="text-gray-400">Error Rate</span>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {performanceMetrics.overall.trends.errorRate.current}%
                </div>
                <div className="flex items-center justify-center">
                  <ArrowDown className="text-green-400 mr-1" size={16} />
                  <span className="text-sm text-green-400">
                    -{Math.abs(performanceMetrics.overall.trends.errorRate.change)}%
                  </span>
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="text-green-400 mr-2" size={20} />
                  <span className="text-gray-400">Availability</span>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {performanceMetrics.overall.trends.availability.current}%
                </div>
                <div className="flex items-center justify-center">
                  <ArrowUp className="text-green-400 mr-1" size={16} />
                  <span className="text-sm text-green-400">
                    +{performanceMetrics.overall.trends.availability.change}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'caching' || activeTab === 'cdn' || activeTab === 'database' || activeTab === 'scaling' || activeTab === 'recommendations') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'caching' && 'Advanced Caching System'}
            {activeTab === 'cdn' && 'CDN Configuration & Optimization'}
            {activeTab === 'database' && 'Database Performance Optimization'}
            {activeTab === 'scaling' && 'Horizontal & Vertical Scaling'}
            {activeTab === 'recommendations' && 'AI-Powered Optimization Recommendations'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'caching' && <Database className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'cdn' && <Globe className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'database' && <Server className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'scaling' && <Layers className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'recommendations' && <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'caching' && 'Multi-Layer Caching Strategy'}
              {activeTab === 'cdn' && 'Global Content Delivery Network'}
              {activeTab === 'database' && 'Database Performance Tuning'}
              {activeTab === 'scaling' && 'Auto-Scaling Infrastructure'}
              {activeTab === 'recommendations' && 'Intelligent Performance Optimization'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'caching' && 'Implement Redis, Memcached, CDN, and application-level caching with intelligent cache invalidation and optimization.'}
              {activeTab === 'cdn' && 'Configure global CDN with edge caching, image optimization, compression, and security features for maximum performance.'}
              {activeTab === 'database' && 'Optimize database queries, indexes, partitioning, and connection pooling for maximum throughput and minimal latency.'}
              {activeTab === 'scaling' && 'Configure automatic horizontal and vertical scaling based on CPU, memory, requests, and custom metrics.'}
              {activeTab === 'recommendations' && 'AI-powered analysis of performance bottlenecks with actionable optimization recommendations and impact predictions.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
