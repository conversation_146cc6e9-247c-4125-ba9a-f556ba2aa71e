'use client'

import React, { useState } from 'react'
import { 
  TrendingUp, 
  Play, 
  Pause, 
  RotateCcw, 
  Save, 
  Download,
  Users,
  Star,
  Gift,
  Target,
  BarChart3,
  Settings,
  Plus,
  Minus,
  Calculator
} from 'lucide-react'

interface SimulationScenario {
  id: string
  name: string
  description: string
  parameters: {
    totalUsers: number
    dailyActiveUsers: number
    averageEngagement: number
    pointsPerAction: {
      login: number
      purchase: number
      review: number
      referral: number
      social: number
    }
    rewardThresholds: {
      bronze: number
      silver: number
      gold: number
      platinum: number
    }
  }
  results?: SimulationResults
}

interface SimulationResults {
  totalPointsDistributed: number
  averagePointsPerUser: number
  userDistribution: {
    bronze: number
    silver: number
    gold: number
    platinum: number
  }
  projectedCosts: {
    monthly: number
    yearly: number
  }
  engagementIncrease: number
  retentionRate: number
}

export default function PointSimulationPage() {
  const [selectedScenario, setSelectedScenario] = useState<string>('1')
  const [isRunning, setIsRunning] = useState(false)
  const [simulationProgress, setSimulationProgress] = useState(0)
  const [showCreateModal, setShowCreateModal] = useState(false)

  // Mock simulation scenarios
  const scenarios: SimulationScenario[] = [
    {
      id: '1',
      name: 'Current System',
      description: 'Baseline simulation with current point distribution',
      parameters: {
        totalUsers: 10000,
        dailyActiveUsers: 3500,
        averageEngagement: 2.5,
        pointsPerAction: {
          login: 10,
          purchase: 100,
          review: 25,
          referral: 500,
          social: 15
        },
        rewardThresholds: {
          bronze: 1000,
          silver: 5000,
          gold: 15000,
          platinum: 50000
        }
      },
      results: {
        totalPointsDistributed: 8750000,
        averagePointsPerUser: 875,
        userDistribution: {
          bronze: 6500,
          silver: 2800,
          gold: 650,
          platinum: 50
        },
        projectedCosts: {
          monthly: 12500,
          yearly: 150000
        },
        engagementIncrease: 0,
        retentionRate: 72.5
      }
    },
    {
      id: '2',
      name: 'Aggressive Growth',
      description: 'Higher point rewards to drive engagement',
      parameters: {
        totalUsers: 10000,
        dailyActiveUsers: 3500,
        averageEngagement: 3.2,
        pointsPerAction: {
          login: 15,
          purchase: 150,
          review: 40,
          referral: 750,
          social: 25
        },
        rewardThresholds: {
          bronze: 1000,
          silver: 5000,
          gold: 15000,
          platinum: 50000
        }
      },
      results: {
        totalPointsDistributed: 14250000,
        averagePointsPerUser: 1425,
        userDistribution: {
          bronze: 5800,
          silver: 3200,
          gold: 900,
          platinum: 100
        },
        projectedCosts: {
          monthly: 20375,
          yearly: 244500
        },
        engagementIncrease: 28,
        retentionRate: 85.2
      }
    },
    {
      id: '3',
      name: 'Conservative Approach',
      description: 'Lower rewards with focus on retention',
      parameters: {
        totalUsers: 10000,
        dailyActiveUsers: 3500,
        averageEngagement: 2.1,
        pointsPerAction: {
          login: 5,
          purchase: 75,
          review: 15,
          referral: 300,
          social: 8
        },
        rewardThresholds: {
          bronze: 800,
          silver: 4000,
          gold: 12000,
          platinum: 40000
        }
      },
      results: {
        totalPointsDistributed: 5825000,
        averagePointsPerUser: 583,
        userDistribution: {
          bronze: 7200,
          silver: 2300,
          gold: 450,
          platinum: 50
        },
        projectedCosts: {
          monthly: 8337,
          yearly: 100050
        },
        engagementIncrease: -16,
        retentionRate: 68.8
      }
    }
  ]

  const selectedScenarioData = scenarios.find(s => s.id === selectedScenario)

  const runSimulation = () => {
    setIsRunning(true)
    setSimulationProgress(0)
    
    // Simulate progress
    const interval = setInterval(() => {
      setSimulationProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsRunning(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  const getTierColor = (tier: string) => {
    const colors = {
      bronze: 'text-orange-600 bg-orange-100',
      silver: 'text-gray-600 bg-gray-100',
      gold: 'text-yellow-600 bg-yellow-100',
      platinum: 'text-purple-600 bg-purple-100'
    }
    return colors[tier as keyof typeof colors] || 'text-gray-600 bg-gray-100'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Point Simulation</h1>
            <p className="text-gray-400">Model and predict gamification point distribution and costs</p>
          </div>
          <div className="flex space-x-3">
            <button 
              onClick={() => setShowCreateModal(true)}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              <Plus size={18} />
              <span>New Scenario</span>
            </button>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
              <Download size={18} />
              <span>Export Report</span>
            </button>
            <button 
              onClick={runSimulation}
              disabled={isRunning}
              className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              {isRunning ? <Pause size={18} /> : <Play size={18} />}
              <span>{isRunning ? 'Running...' : 'Run Simulation'}</span>
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Scenario Selection */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Settings className="mr-2" size={20} />
            Simulation Scenarios
          </h3>
          <div className="space-y-3">
            {scenarios.map((scenario) => (
              <button
                key={scenario.id}
                onClick={() => setSelectedScenario(scenario.id)}
                className={`w-full text-left p-4 rounded-lg border transition-colors ${
                  selectedScenario === scenario.id
                    ? 'border-red-500 bg-red-900/20'
                    : 'border-gray-700 hover:border-gray-600 bg-gray-700'
                }`}
              >
                <h4 className="text-white font-medium mb-1">{scenario.name}</h4>
                <p className="text-gray-400 text-sm">{scenario.description}</p>
                {scenario.results && (
                  <div className="mt-2 text-xs text-gray-500">
                    Avg: {scenario.results.averagePointsPerUser} points/user • 
                    Cost: {formatCurrency(scenario.results.projectedCosts.monthly)}/mo
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Parameter Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Calculator className="mr-2" size={20} />
            Parameters
          </h3>
          {selectedScenarioData && (
            <div className="space-y-4">
              <div>
                <h4 className="text-white font-medium mb-2">User Base</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Total Users:</span>
                    <span className="text-white">{selectedScenarioData.parameters.totalUsers.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Daily Active:</span>
                    <span className="text-white">{selectedScenarioData.parameters.dailyActiveUsers.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Avg Engagement:</span>
                    <span className="text-white">{selectedScenarioData.parameters.averageEngagement}x</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">Points per Action</h4>
                <div className="space-y-2 text-sm">
                  {Object.entries(selectedScenarioData.parameters.pointsPerAction).map(([action, points]) => (
                    <div key={action} className="flex justify-between">
                      <span className="text-gray-400 capitalize">{action}:</span>
                      <span className="text-white">{points} pts</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">Reward Thresholds</h4>
                <div className="space-y-2 text-sm">
                  {Object.entries(selectedScenarioData.parameters.rewardThresholds).map(([tier, threshold]) => (
                    <div key={tier} className="flex justify-between">
                      <span className={`capitalize px-2 py-1 rounded text-xs ${getTierColor(tier)}`}>
                        {tier}
                      </span>
                      <span className="text-white">{threshold.toLocaleString()} pts</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <BarChart3 className="mr-2" size={20} />
            Simulation Results
          </h3>
          {selectedScenarioData?.results ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-700 rounded-lg p-3">
                  <div className="text-2xl font-bold text-blue-400">
                    {selectedScenarioData.results.totalPointsDistributed.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-400">Total Points</div>
                </div>
                <div className="bg-gray-700 rounded-lg p-3">
                  <div className="text-2xl font-bold text-green-400">
                    {selectedScenarioData.results.averagePointsPerUser}
                  </div>
                  <div className="text-xs text-gray-400">Avg per User</div>
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">User Distribution</h4>
                <div className="space-y-2">
                  {Object.entries(selectedScenarioData.results.userDistribution).map(([tier, count]) => (
                    <div key={tier} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${getTierColor(tier).split(' ')[1]}`}></div>
                        <span className="text-gray-400 capitalize">{tier}</span>
                      </div>
                      <span className="text-white">{count.toLocaleString()}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">Projected Costs</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Monthly:</span>
                    <span className="text-white font-medium">
                      {formatCurrency(selectedScenarioData.results.projectedCosts.monthly)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Yearly:</span>
                    <span className="text-white font-medium">
                      {formatCurrency(selectedScenarioData.results.projectedCosts.yearly)}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">Impact Metrics</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Engagement Change:</span>
                    <span className={`font-medium ${
                      selectedScenarioData.results.engagementIncrease >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {selectedScenarioData.results.engagementIncrease >= 0 ? '+' : ''}
                      {selectedScenarioData.results.engagementIncrease}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Retention Rate:</span>
                    <span className="text-white font-medium">
                      {selectedScenarioData.results.retentionRate}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <TrendingUp className="mx-auto text-gray-600 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-400 mb-2">No Results Yet</h3>
              <p className="text-gray-500 text-sm mb-4">Run a simulation to see predicted outcomes</p>
              <button 
                onClick={runSimulation}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
              >
                Run Simulation
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      {isRunning && (
        <div className="mt-6 bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-white font-medium">Running Simulation...</span>
            <span className="text-gray-400">{simulationProgress}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-red-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${simulationProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Comparison Chart */}
      <div className="mt-6 bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Scenario Comparison</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {scenarios.map((scenario) => scenario.results && (
            <div key={scenario.id} className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-white font-medium mb-3">{scenario.name}</h4>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Monthly Cost:</span>
                  <span className="text-white">{formatCurrency(scenario.results.projectedCosts.monthly)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Engagement:</span>
                  <span className={scenario.results.engagementIncrease >= 0 ? 'text-green-400' : 'text-red-400'}>
                    {scenario.results.engagementIncrease >= 0 ? '+' : ''}{scenario.results.engagementIncrease}%
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Retention:</span>
                  <span className="text-white">{scenario.results.retentionRate}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Avg Points:</span>
                  <span className="text-white">{scenario.results.averagePointsPerUser}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create Scenario Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-white mb-4">Create New Scenario</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Scenario Name</label>
                <input 
                  type="text" 
                  placeholder="e.g., Holiday Boost Campaign"
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea 
                  rows={3}
                  placeholder="Describe the scenario and its goals..."
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Total Users</label>
                  <input 
                    type="number" 
                    defaultValue="10000"
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Daily Active Users</label>
                  <input 
                    type="number" 
                    defaultValue="3500"
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Points per Action</h4>
                <div className="grid grid-cols-2 gap-4">
                  {['login', 'purchase', 'review', 'referral', 'social'].map((action) => (
                    <div key={action}>
                      <label className="block text-sm text-gray-400 mb-1 capitalize">{action}</label>
                      <input 
                        type="number" 
                        className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                Create Scenario
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}