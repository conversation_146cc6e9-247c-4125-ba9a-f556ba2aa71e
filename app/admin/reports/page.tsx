'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  FileText, 
  Calendar, 
  Download, 
  Plus, 
  RefreshCw,
  BarChart3,
  <PERSON><PERSON>hart,
  TrendingUp,
  Clock,
  Settings,
  Eye,
  Play,
  Pause,
  Filter,
  Search,
  Mail,
  Share,
  Archive
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface Report {
  id: string
  name: string
  description: string
  type: 'sales' | 'users' | 'inventory' | 'performance' | 'custom'
  format: 'pdf' | 'excel' | 'csv' | 'json'
  schedule: 'manual' | 'daily' | 'weekly' | 'monthly' | 'quarterly'
  isActive: boolean
  lastGenerated: Date
  nextScheduled?: Date
  recipients: string[]
  parameters: Record<string, any>
  createdBy: string
  createdAt: Date
  fileSize?: number
  downloadCount: number
}

interface ReportTemplate {
  id: string
  name: string
  description: string
  type: string
  category: string
  isSystem: boolean
  parameters: string[]
  estimatedTime: number
}

export default function AdvancedReportsPage() {
  const [reports, setReports] = useState<Report[]>([])
  const [templates, setTemplates] = useState<ReportTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'reports' | 'scheduled' | 'templates' | 'builder'>('reports')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [showCreateReport, setShowCreateReport] = useState(false)

  useEffect(() => {
    loadReportsData()
  }, [])

  const loadReportsData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockReports: Report[] = [
        {
          id: 'rpt_001',
          name: 'Monthly Sales Report',
          description: 'Comprehensive monthly sales analysis with trends and forecasts',
          type: 'sales',
          format: 'pdf',
          schedule: 'monthly',
          isActive: true,
          lastGenerated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          nextScheduled: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
          recipients: ['<EMAIL>', '<EMAIL>'],
          parameters: {
            dateRange: 'last_month',
            includeForecasts: true,
            breakdown: 'category'
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-01'),
          fileSize: 2.4,
          downloadCount: 15
        },
        {
          id: 'rpt_002',
          name: 'User Engagement Analytics',
          description: 'Weekly user behavior and engagement metrics',
          type: 'users',
          format: 'excel',
          schedule: 'weekly',
          isActive: true,
          lastGenerated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          nextScheduled: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000),
          recipients: ['<EMAIL>'],
          parameters: {
            metrics: ['engagement_score', 'session_duration', 'page_views'],
            segmentation: 'tier'
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-05'),
          fileSize: 1.8,
          downloadCount: 8
        },
        {
          id: 'rpt_003',
          name: 'Inventory Status Report',
          description: 'Current inventory levels and reorder recommendations',
          type: 'inventory',
          format: 'csv',
          schedule: 'daily',
          isActive: false,
          lastGenerated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          recipients: ['<EMAIL>'],
          parameters: {
            includeAlerts: true,
            threshold: 'low_stock'
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-10'),
          fileSize: 0.5,
          downloadCount: 23
        }
      ]

      const mockTemplates: ReportTemplate[] = [
        {
          id: 'tpl_001',
          name: 'Sales Performance Dashboard',
          description: 'Comprehensive sales metrics with revenue, conversion, and trend analysis',
          type: 'sales',
          category: 'Revenue',
          isSystem: true,
          parameters: ['date_range', 'product_categories', 'sales_channels'],
          estimatedTime: 5
        },
        {
          id: 'tpl_002',
          name: 'Customer Behavior Analysis',
          description: 'User engagement, retention, and behavioral pattern analysis',
          type: 'users',
          category: 'Analytics',
          isSystem: true,
          parameters: ['user_segments', 'time_period', 'metrics'],
          estimatedTime: 8
        },
        {
          id: 'tpl_003',
          name: 'Inventory Management Report',
          description: 'Stock levels, turnover rates, and reorder recommendations',
          type: 'inventory',
          category: 'Operations',
          isSystem: true,
          parameters: ['warehouse_locations', 'product_categories', 'alert_thresholds'],
          estimatedTime: 3
        },
        {
          id: 'tpl_004',
          name: 'Financial Summary',
          description: 'Revenue, expenses, profit margins, and financial KPIs',
          type: 'custom',
          category: 'Finance',
          isSystem: true,
          parameters: ['reporting_period', 'cost_centers', 'currency'],
          estimatedTime: 10
        }
      ]

      setReports(mockReports)
      setTemplates(mockTemplates)
    } catch (error) {
      console.error('Error loading reports data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || report.type === filterType
    return matchesSearch && matchesType
  })

  const scheduledReports = reports.filter(report => report.schedule !== 'manual' && report.isActive)

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sales': return 'text-green-400 bg-green-900/20'
      case 'users': return 'text-blue-400 bg-blue-900/20'
      case 'inventory': return 'text-purple-400 bg-purple-900/20'
      case 'performance': return 'text-yellow-400 bg-yellow-900/20'
      case 'custom': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getScheduleColor = (schedule: string) => {
    switch (schedule) {
      case 'daily': return 'text-red-400 bg-red-900/20'
      case 'weekly': return 'text-yellow-400 bg-yellow-900/20'
      case 'monthly': return 'text-blue-400 bg-blue-900/20'
      case 'quarterly': return 'text-purple-400 bg-purple-900/20'
      case 'manual': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatFileSize = (sizeInMB: number) => {
    if (sizeInMB < 1) return `${(sizeInMB * 1024).toFixed(0)} KB`
    return `${sizeInMB.toFixed(1)} MB`
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Less than an hour ago'
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }

  const toggleReportStatus = async (reportId: string) => {
    setReports(prev => prev.map(report => 
      report.id === reportId 
        ? { ...report, isActive: !report.isActive }
        : report
    ))
  }

  const generateReport = async (reportId: string) => {
    console.log('Generating report:', reportId)
    // Simulate report generation
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Advanced Reporting System</h1>
            <p className="text-gray-400">Custom report builder, scheduled reports, and automated data export</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadReportsData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateReport(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Report
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Reports</p>
              <p className="text-2xl font-bold text-white">{reports.length}</p>
              <p className="text-xs text-green-400 mt-1">+3 this month</p>
            </div>
            <FileText className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Scheduled Reports</p>
              <p className="text-2xl font-bold text-white">{scheduledReports.length}</p>
              <p className="text-xs text-blue-400 mt-1">Active automation</p>
            </div>
            <Calendar className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Downloads</p>
              <p className="text-2xl font-bold text-white">
                {reports.reduce((sum, report) => sum + report.downloadCount, 0)}
              </p>
              <p className="text-xs text-green-400 mt-1">This month</p>
            </div>
            <Download className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Templates</p>
              <p className="text-2xl font-bold text-white">{templates.length}</p>
              <p className="text-xs text-gray-400 mt-1">Available</p>
            </div>
            <BarChart3 className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'reports', label: 'All Reports', count: reports.length },
            { id: 'scheduled', label: 'Scheduled', count: scheduledReports.length },
            { id: 'templates', label: 'Templates', count: templates.length },
            { id: 'builder', label: 'Report Builder', count: null }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Search and Filters */}
      {(activeTab === 'reports' || activeTab === 'scheduled') && (
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search reports..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>
            
            <div className="flex gap-3">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Types</option>
                <option value="sales">Sales</option>
                <option value="users">Users</option>
                <option value="inventory">Inventory</option>
                <option value="performance">Performance</option>
                <option value="custom">Custom</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'reports' && (
        <div className="space-y-4">
          {filteredReports.length === 0 ? (
            <div className="bg-gray-800 p-8 rounded-lg text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Reports Found</h3>
              <p className="text-gray-400">Create your first report or adjust your search criteria.</p>
            </div>
          ) : (
            filteredReports.map((report) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{report.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(report.type)}`}>
                        {report.type}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getScheduleColor(report.schedule)}`}>
                        {report.schedule}
                      </span>
                      {report.isActive ? (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-green-400 bg-green-900/20">
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-400 bg-gray-900/20">
                          Inactive
                        </span>
                      )}
                    </div>
                    <p className="text-gray-400 text-sm mb-3">{report.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Format:</span>
                        <span className="text-white ml-1 uppercase">{report.format}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Last Generated:</span>
                        <span className="text-white ml-1">{formatTimeAgo(report.lastGenerated)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Size:</span>
                        <span className="text-white ml-1">{report.fileSize ? formatFileSize(report.fileSize) : 'N/A'}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Downloads:</span>
                        <span className="text-white ml-1">{report.downloadCount}</span>
                      </div>
                    </div>

                    {report.recipients.length > 0 && (
                      <div className="mt-3">
                        <span className="text-gray-400 text-sm">Recipients: </span>
                        <span className="text-white text-sm">{report.recipients.join(', ')}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => generateReport(report.id)}
                      className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded"
                      title="Generate Now"
                    >
                      <Play size={16} />
                    </button>
                    <button
                      onClick={() => toggleReportStatus(report.id)}
                      className={`p-2 rounded ${
                        report.isActive 
                          ? 'bg-orange-600 hover:bg-orange-700 text-white' 
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                      title={report.isActive ? 'Pause' : 'Activate'}
                    >
                      {report.isActive ? <Pause size={16} /> : <Play size={16} />}
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded" title="Download">
                      <Download size={16} />
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded" title="Settings">
                      <Settings size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      )}

      {activeTab === 'templates' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white">{template.name}</h3>
                  <p className="text-gray-400 text-sm mt-1">{template.description}</p>
                </div>
                {template.isSystem && (
                  <span className="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-900/20 text-blue-400">
                    System
                  </span>
                )}
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Category:</span>
                  <span className="text-white">{template.category}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Est. Time:</span>
                  <span className="text-white">{template.estimatedTime} min</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Parameters:</span>
                  <span className="text-white">{template.parameters.length}</span>
                </div>
              </div>

              <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition-colors">
                Use Template
              </button>
            </motion.div>
          ))}
        </div>
      )}

      {(activeTab === 'scheduled' || activeTab === 'builder') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'scheduled' ? 'Scheduled Reports' : 'Report Builder'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'scheduled' ? <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" /> : <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'scheduled' ? 'Scheduled Reports Management' : 'Advanced Report Builder'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'scheduled' 
                ? 'Manage automated report generation and delivery schedules.'
                : 'Create custom reports with drag-and-drop interface and advanced filtering.'}
            </p>
          </div>
        </div>
      )}

      {/* Create Report Modal */}
      {showCreateReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Create New Report</h3>
            <p className="text-gray-400 mb-4">
              Advanced report builder will allow you to create custom reports with 
              data sources, visualizations, scheduling, and automated delivery.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowCreateReport(false)}
                className="flex-1 py-2 px-4 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
