'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Shield, 
  Lock, 
  Key, 
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  FileText,
  Database,
  Globe,
  Settings,
  RefreshCw,
  Plus,
  Download,
  Upload,
  Filter,
  Search,
  Activity,
  Zap,
  Target,
  BarChart3,
  Cpu,
  Server,
  Wifi,
  Smartphone,
  Monitor,
  HardDrive
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface SecurityPolicy {
  id: string
  name: string
  description: string
  category: 'access_control' | 'data_protection' | 'network_security' | 'compliance' | 'incident_response'
  status: 'active' | 'inactive' | 'draft' | 'under_review'
  severity: 'low' | 'medium' | 'high' | 'critical'
  compliance: string[] // GDPR, SOC2, HIPAA, etc.
  rules: SecurityRule[]
  lastUpdated: Date
  nextReview: Date
  owner: string
  approvedBy?: string
  version: string
}

interface SecurityRule {
  id: string
  name: string
  description: string
  type: 'allow' | 'deny' | 'monitor' | 'alert'
  conditions: {
    field: string
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in_range'
    value: any
  }[]
  actions: {
    type: 'block' | 'alert' | 'log' | 'quarantine' | 'notify'
    parameters: Record<string, any>
  }[]
  isActive: boolean
  priority: number
}

interface ComplianceFramework {
  id: string
  name: string
  description: string
  type: 'regulation' | 'standard' | 'certification' | 'internal'
  status: 'compliant' | 'non_compliant' | 'in_progress' | 'not_assessed'
  requirements: ComplianceRequirement[]
  assessments: ComplianceAssessment[]
  lastAssessment?: Date
  nextAssessment: Date
  certificationExpiry?: Date
  owner: string
  auditor?: string
}

interface ComplianceRequirement {
  id: string
  title: string
  description: string
  category: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  status: 'compliant' | 'non_compliant' | 'partial' | 'not_applicable'
  evidence: string[]
  controls: string[]
  lastVerified?: Date
  assignedTo: string
}

interface ComplianceAssessment {
  id: string
  frameworkId: string
  assessmentDate: Date
  assessor: string
  scope: string
  findings: {
    compliant: number
    nonCompliant: number
    partial: number
    notApplicable: number
  }
  recommendations: string[]
  nextActions: {
    action: string
    priority: 'low' | 'medium' | 'high' | 'critical'
    dueDate: Date
    assignedTo: string
  }[]
  overallScore: number
}

interface SecurityIncident {
  id: string
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: 'data_breach' | 'unauthorized_access' | 'malware' | 'phishing' | 'ddos' | 'insider_threat' | 'other'
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed'
  reportedAt: Date
  detectedAt: Date
  resolvedAt?: Date
  reportedBy: string
  assignedTo: string
  affectedSystems: string[]
  affectedUsers: number
  dataCompromised: boolean
  timeline: {
    timestamp: Date
    event: string
    details: string
    user: string
  }[]
  response: {
    containmentActions: string[]
    investigationFindings: string[]
    remediationSteps: string[]
    preventiveMeasures: string[]
  }
  compliance: {
    notificationRequired: boolean
    regulatorsNotified: boolean
    customersNotified: boolean
    notificationDeadline?: Date
  }
}

interface SecurityMetrics {
  timeRange: '24h' | '7d' | '30d' | '90d'
  threatDetection: {
    totalThreats: number
    blockedThreats: number
    falsePositives: number
    averageResponseTime: number
  }
  accessControl: {
    loginAttempts: number
    failedLogins: number
    suspiciousActivity: number
    mfaAdoption: number
  }
  dataProtection: {
    encryptedData: number
    backupSuccess: number
    dataLeaks: number
    accessRequests: number
  }
  compliance: {
    overallScore: number
    frameworksCompliant: number
    openFindings: number
    overdueActions: number
  }
  vulnerabilities: {
    critical: number
    high: number
    medium: number
    low: number
    patched: number
  }
}

interface VulnerabilityAssessment {
  id: string
  name: string
  description: string
  type: 'automated' | 'manual' | 'penetration_test' | 'code_review'
  status: 'scheduled' | 'running' | 'completed' | 'failed'
  scope: string[]
  startDate: Date
  endDate?: Date
  findings: {
    critical: number
    high: number
    medium: number
    low: number
    info: number
  }
  vulnerabilities: {
    id: string
    title: string
    severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
    cvss: number
    description: string
    affectedSystems: string[]
    remediation: string
    status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk'
  }[]
  recommendations: string[]
  assessor: string
  nextAssessment?: Date
}

export default function SecurityCompliancePage() {
  const [securityPolicies, setSecurityPolicies] = useState<SecurityPolicy[]>([])
  const [complianceFrameworks, setComplianceFrameworks] = useState<ComplianceFramework[]>([])
  const [securityIncidents, setSecurityIncidents] = useState<SecurityIncident[]>([])
  const [vulnerabilityAssessments, setVulnerabilityAssessments] = useState<VulnerabilityAssessment[]>([])
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'policies' | 'compliance' | 'incidents' | 'vulnerabilities'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadSecurityData()
  }, [])

  const loadSecurityData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual security API integration
      const mockSecurityPolicies: SecurityPolicy[] = [
        {
          id: 'policy_001',
          name: 'Multi-Factor Authentication Policy',
          description: 'Mandatory MFA for all admin and privileged accounts',
          category: 'access_control',
          status: 'active',
          severity: 'high',
          compliance: ['SOC2', 'ISO27001'],
          rules: [
            {
              id: 'rule_001',
              name: 'Admin MFA Requirement',
              description: 'All admin users must have MFA enabled',
              type: 'deny',
              conditions: [
                { field: 'user_role', operator: 'equals', value: 'admin' },
                { field: 'mfa_enabled', operator: 'equals', value: false }
              ],
              actions: [
                { type: 'block', parameters: { message: 'MFA required for admin access' } },
                { type: 'alert', parameters: { severity: 'high' } }
              ],
              isActive: true,
              priority: 1
            }
          ],
          lastUpdated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          nextReview: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
          owner: '<EMAIL>',
          approvedBy: '<EMAIL>',
          version: '2.1'
        },
        {
          id: 'policy_002',
          name: 'Data Encryption Policy',
          description: 'Encryption requirements for data at rest and in transit',
          category: 'data_protection',
          status: 'active',
          severity: 'critical',
          compliance: ['GDPR', 'SOC2', 'PCI-DSS'],
          rules: [
            {
              id: 'rule_002',
              name: 'Database Encryption',
              description: 'All databases must be encrypted with AES-256',
              type: 'monitor',
              conditions: [
                { field: 'resource_type', operator: 'equals', value: 'database' },
                { field: 'encryption_enabled', operator: 'equals', value: false }
              ],
              actions: [
                { type: 'alert', parameters: { severity: 'critical' } },
                { type: 'notify', parameters: { recipients: ['<EMAIL>'] } }
              ],
              isActive: true,
              priority: 1
            }
          ],
          lastUpdated: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          nextReview: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
          owner: '<EMAIL>',
          approvedBy: '<EMAIL>',
          version: '1.3'
        }
      ]

      const mockComplianceFrameworks: ComplianceFramework[] = [
        {
          id: 'framework_001',
          name: 'SOC 2 Type II',
          description: 'Service Organization Control 2 Type II compliance framework',
          type: 'certification',
          status: 'compliant',
          requirements: [
            {
              id: 'req_001',
              title: 'Access Control Management',
              description: 'Implement logical access controls to protect against unauthorized access',
              category: 'Security',
              priority: 'high',
              status: 'compliant',
              evidence: ['access_control_policy.pdf', 'user_access_review_q4.xlsx'],
              controls: ['CC6.1', 'CC6.2', 'CC6.3'],
              lastVerified: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              assignedTo: '<EMAIL>'
            },
            {
              id: 'req_002',
              title: 'System Monitoring',
              description: 'Monitor system activities and detect security events',
              category: 'Security',
              priority: 'high',
              status: 'compliant',
              evidence: ['monitoring_logs.json', 'incident_response_plan.pdf'],
              controls: ['CC7.1', 'CC7.2'],
              lastVerified: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
              assignedTo: '<EMAIL>'
            }
          ],
          assessments: [
            {
              id: 'assessment_001',
              frameworkId: 'framework_001',
              assessmentDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
              assessor: 'External Auditor Inc.',
              scope: 'Full SOC 2 Type II assessment',
              findings: {
                compliant: 45,
                nonCompliant: 2,
                partial: 3,
                notApplicable: 5
              },
              recommendations: [
                'Implement automated access review process',
                'Enhance incident response documentation',
                'Improve change management controls'
              ],
              nextActions: [
                {
                  action: 'Implement automated access reviews',
                  priority: 'high',
                  dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                  assignedTo: '<EMAIL>'
                }
              ],
              overallScore: 92
            }
          ],
          lastAssessment: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
          nextAssessment: new Date(Date.now() + 275 * 24 * 60 * 60 * 1000),
          certificationExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
          owner: '<EMAIL>',
          auditor: 'External Auditor Inc.'
        },
        {
          id: 'framework_002',
          name: 'GDPR Compliance',
          description: 'General Data Protection Regulation compliance framework',
          type: 'regulation',
          status: 'compliant',
          requirements: [
            {
              id: 'req_003',
              title: 'Data Subject Rights',
              description: 'Implement processes for handling data subject requests',
              category: 'Privacy',
              priority: 'critical',
              status: 'compliant',
              evidence: ['data_subject_request_process.pdf', 'privacy_policy.pdf'],
              controls: ['Art. 15-22'],
              lastVerified: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
              assignedTo: '<EMAIL>'
            }
          ],
          assessments: [],
          nextAssessment: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
          owner: '<EMAIL>'
        }
      ]

      const mockSecurityIncidents: SecurityIncident[] = [
        {
          id: 'incident_001',
          title: 'Suspicious Login Activity Detected',
          description: 'Multiple failed login attempts from unusual geographic locations',
          severity: 'medium',
          category: 'unauthorized_access',
          status: 'resolved',
          reportedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          detectedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 - 30 * 60 * 1000),
          resolvedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          reportedBy: 'security-system',
          assignedTo: '<EMAIL>',
          affectedSystems: ['authentication-service', 'user-portal'],
          affectedUsers: 1,
          dataCompromised: false,
          timeline: [
            {
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 - 30 * 60 * 1000),
              event: 'Suspicious activity detected',
              details: 'Multiple failed login attempts from IP *************',
              user: 'security-system'
            },
            {
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
              event: 'Incident reported',
              details: 'Automated alert triggered security team notification',
              user: 'security-system'
            },
            {
              timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 - 2 * 60 * 60 * 1000),
              event: 'Investigation started',
              details: 'Security team began analysis of login patterns',
              user: '<EMAIL>'
            },
            {
              timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
              event: 'Incident resolved',
              details: 'IP blocked, user account secured with forced password reset',
              user: '<EMAIL>'
            }
          ],
          response: {
            containmentActions: ['Blocked suspicious IP address', 'Temporarily locked user account'],
            investigationFindings: ['Brute force attack attempt', 'No successful unauthorized access'],
            remediationSteps: ['Forced password reset for affected user', 'Enhanced monitoring implemented'],
            preventiveMeasures: ['Updated rate limiting rules', 'Improved geographic anomaly detection']
          },
          compliance: {
            notificationRequired: false,
            regulatorsNotified: false,
            customersNotified: false
          }
        }
      ]

      const mockVulnerabilityAssessments: VulnerabilityAssessment[] = [
        {
          id: 'vuln_001',
          name: 'Q1 2025 Penetration Test',
          description: 'Quarterly penetration testing of web applications and infrastructure',
          type: 'penetration_test',
          status: 'completed',
          scope: ['web-application', 'api-endpoints', 'network-infrastructure'],
          startDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          findings: {
            critical: 0,
            high: 2,
            medium: 5,
            low: 8,
            info: 12
          },
          vulnerabilities: [
            {
              id: 'vuln_001_001',
              title: 'SQL Injection in Search Function',
              severity: 'high',
              cvss: 8.1,
              description: 'SQL injection vulnerability in product search functionality',
              affectedSystems: ['web-application'],
              remediation: 'Implement parameterized queries and input validation',
              status: 'resolved'
            },
            {
              id: 'vuln_001_002',
              title: 'Cross-Site Scripting (XSS)',
              severity: 'medium',
              cvss: 6.1,
              description: 'Reflected XSS vulnerability in user profile page',
              affectedSystems: ['web-application'],
              remediation: 'Implement proper output encoding and CSP headers',
              status: 'in_progress'
            }
          ],
          recommendations: [
            'Implement Web Application Firewall (WAF)',
            'Regular security code reviews',
            'Automated vulnerability scanning in CI/CD pipeline'
          ],
          assessor: 'CyberSec Consulting',
          nextAssessment: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
        }
      ]

      const mockSecurityMetrics: SecurityMetrics = {
        timeRange: '30d',
        threatDetection: {
          totalThreats: 1247,
          blockedThreats: 1198,
          falsePositives: 23,
          averageResponseTime: 4.2
        },
        accessControl: {
          loginAttempts: 45230,
          failedLogins: 892,
          suspiciousActivity: 34,
          mfaAdoption: 87
        },
        dataProtection: {
          encryptedData: 98.5,
          backupSuccess: 99.2,
          dataLeaks: 0,
          accessRequests: 156
        },
        compliance: {
          overallScore: 94,
          frameworksCompliant: 3,
          openFindings: 5,
          overdueActions: 1
        },
        vulnerabilities: {
          critical: 0,
          high: 2,
          medium: 8,
          low: 15,
          patched: 89
        }
      }

      setSecurityPolicies(mockSecurityPolicies)
      setComplianceFrameworks(mockComplianceFrameworks)
      setSecurityIncidents(mockSecurityIncidents)
      setVulnerabilityAssessments(mockVulnerabilityAssessments)
      setSecurityMetrics(mockSecurityMetrics)
    } catch (error) {
      console.error('Error loading security data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'compliant': case 'resolved': case 'completed': return 'text-green-400 bg-green-900/20'
      case 'investigating': case 'in_progress': case 'running': return 'text-blue-400 bg-blue-900/20'
      case 'open': case 'non_compliant': case 'failed': return 'text-red-400 bg-red-900/20'
      case 'contained': case 'under_review': case 'scheduled': return 'text-yellow-400 bg-yellow-900/20'
      case 'inactive': case 'draft': case 'closed': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      case 'info': return 'text-blue-400 bg-blue-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'access_control': return 'text-blue-400 bg-blue-900/20'
      case 'data_protection': return 'text-green-400 bg-green-900/20'
      case 'network_security': return 'text-purple-400 bg-purple-900/20'
      case 'compliance': return 'text-yellow-400 bg-yellow-900/20'
      case 'incident_response': return 'text-red-400 bg-red-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const formatDuration = (hours: number) => {
    if (hours < 1) return `${(hours * 60).toFixed(0)} minutes`
    if (hours < 24) return `${hours.toFixed(1)} hours`
    return `${(hours / 24).toFixed(1)} days`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Shield className="w-8 h-8 text-red-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Security & Compliance</h1>
            <p className="text-gray-400">Enterprise security management, compliance frameworks, and risk assessment</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadSecurityData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/security-compliance/policies/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Policy
          </Link>
        </div>
      </div>

      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Security Score</p>
              <p className="text-2xl font-bold text-white">
                {securityMetrics ? securityMetrics.compliance.overallScore : 0}%
              </p>
              <p className="text-xs text-green-400 mt-1">
                {securityMetrics ? securityMetrics.compliance.frameworksCompliant : 0} frameworks compliant
              </p>
            </div>
            <Shield className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Threats Blocked</p>
              <p className="text-2xl font-bold text-white">
                {securityMetrics ? securityMetrics.threatDetection.blockedThreats.toLocaleString() : 0}
              </p>
              <p className="text-xs text-blue-400 mt-1">Last 30 days</p>
            </div>
            <Target className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Critical Vulnerabilities</p>
              <p className="text-2xl font-bold text-white">
                {securityMetrics ? securityMetrics.vulnerabilities.critical : 0}
              </p>
              <p className="text-xs text-green-400 mt-1">
                {securityMetrics ? securityMetrics.vulnerabilities.patched : 0} patched
              </p>
            </div>
            <AlertTriangle className="text-red-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">MFA Adoption</p>
              <p className="text-2xl font-bold text-white">
                {securityMetrics ? formatPercentage(securityMetrics.accessControl.mfaAdoption) : '0%'}
              </p>
              <p className="text-xs text-purple-400 mt-1">User accounts</p>
            </div>
            <Key className="text-purple-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'policies', label: 'Security Policies', icon: Shield, count: securityPolicies.length },
            { id: 'compliance', label: 'Compliance', icon: FileText, count: complianceFrameworks.length },
            { id: 'incidents', label: 'Security Incidents', icon: AlertTriangle, count: securityIncidents.length },
            { id: 'vulnerabilities', label: 'Vulnerabilities', icon: Target, count: vulnerabilityAssessments.length }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Security Metrics Dashboard */}
          {securityMetrics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Threat Detection</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Threats Detected</span>
                    <span className="text-white font-medium">{securityMetrics.threatDetection.totalThreats.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Threats Blocked</span>
                    <span className="text-green-400 font-medium">{securityMetrics.threatDetection.blockedThreats.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">False Positives</span>
                    <span className="text-yellow-400 font-medium">{securityMetrics.threatDetection.falsePositives}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Avg Response Time</span>
                    <span className="text-white font-medium">{formatDuration(securityMetrics.threatDetection.averageResponseTime)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Access Control</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Login Attempts</span>
                    <span className="text-white font-medium">{securityMetrics.accessControl.loginAttempts.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Failed Logins</span>
                    <span className="text-red-400 font-medium">{securityMetrics.accessControl.failedLogins.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Suspicious Activity</span>
                    <span className="text-yellow-400 font-medium">{securityMetrics.accessControl.suspiciousActivity}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">MFA Adoption</span>
                    <span className="text-green-400 font-medium">{formatPercentage(securityMetrics.accessControl.mfaAdoption)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Data Protection</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Data Encrypted</span>
                    <span className="text-green-400 font-medium">{formatPercentage(securityMetrics.dataProtection.encryptedData)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Backup Success Rate</span>
                    <span className="text-green-400 font-medium">{formatPercentage(securityMetrics.dataProtection.backupSuccess)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Data Leaks</span>
                    <span className="text-green-400 font-medium">{securityMetrics.dataProtection.dataLeaks}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Access Requests</span>
                    <span className="text-white font-medium">{securityMetrics.dataProtection.accessRequests}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Vulnerability Status</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Critical</span>
                    <span className="text-red-400 font-medium">{securityMetrics.vulnerabilities.critical}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">High</span>
                    <span className="text-orange-400 font-medium">{securityMetrics.vulnerabilities.high}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Medium</span>
                    <span className="text-yellow-400 font-medium">{securityMetrics.vulnerabilities.medium}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Low</span>
                    <span className="text-green-400 font-medium">{securityMetrics.vulnerabilities.low}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Recent Security Events */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Recent Security Events</h3>
            <div className="space-y-3">
              {securityIncidents.slice(0, 5).map((incident) => (
                <div key={incident.id} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded ${getSeverityColor(incident.severity)}`}>
                      <AlertTriangle size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">{incident.title}</p>
                      <p className="text-xs text-gray-400">
                        {incident.category.replace('_', ' ')} • {incident.reportedAt.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(incident.severity)}`}>
                      {incident.severity}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(incident.status)}`}>
                      {incident.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Compliance Status */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Compliance Framework Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {complianceFrameworks.map((framework) => (
                <div key={framework.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-white">{framework.name}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(framework.status)}`}>
                      {framework.status.replace('_', ' ')}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400 mb-3">{framework.description}</p>

                  {framework.assessments.length > 0 && (
                    <div className="text-xs">
                      <div className="flex justify-between mb-1">
                        <span className="text-gray-400">Overall Score:</span>
                        <span className="text-white">{framework.assessments[0].overallScore}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Last Assessment:</span>
                        <span className="text-white">{framework.lastAssessment?.toLocaleDateString()}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'policies' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search security policies..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Categories</option>
                  <option value="access_control">Access Control</option>
                  <option value="data_protection">Data Protection</option>
                  <option value="network_security">Network Security</option>
                  <option value="compliance">Compliance</option>
                  <option value="incident_response">Incident Response</option>
                </select>

                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="draft">Draft</option>
                  <option value="under_review">Under Review</option>
                </select>
              </div>
            </div>
          </div>

          {/* Security Policies List */}
          <div className="space-y-4">
            {securityPolicies.map((policy) => (
              <motion.div
                key={policy.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{policy.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(policy.status)}`}>
                        {policy.status.replace('_', ' ')}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(policy.severity)}`}>
                        {policy.severity}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(policy.category)}`}>
                        {policy.category.replace('_', ' ')}
                      </span>
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{policy.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Version:</span>
                        <span className="text-white ml-1">{policy.version}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Rules:</span>
                        <span className="text-white ml-1">{policy.rules.length}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Owner:</span>
                        <span className="text-white ml-1">{policy.owner}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Next Review:</span>
                        <span className="text-white ml-1">{policy.nextReview.toLocaleDateString()}</span>
                      </div>
                    </div>

                    {policy.compliance.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3">
                        {policy.compliance.map((comp) => (
                          <span key={comp} className="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-900/30 text-blue-300">
                            {comp}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Settings size={16} />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white p-2 rounded" title="Test Policy">
                      <Activity size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'compliance' || activeTab === 'incidents' || activeTab === 'vulnerabilities') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'compliance' && 'Compliance Framework Management'}
            {activeTab === 'incidents' && 'Security Incident Response'}
            {activeTab === 'vulnerabilities' && 'Vulnerability Assessment & Management'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'compliance' && <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'incidents' && <AlertTriangle className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'vulnerabilities' && <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'compliance' && 'Enterprise Compliance Management'}
              {activeTab === 'incidents' && 'Advanced Incident Response'}
              {activeTab === 'vulnerabilities' && 'Comprehensive Vulnerability Management'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'compliance' && 'Manage compliance frameworks including SOC 2, GDPR, HIPAA, and custom requirements with automated assessments.'}
              {activeTab === 'incidents' && 'Track and manage security incidents with automated response workflows, forensic analysis, and compliance reporting.'}
              {activeTab === 'vulnerabilities' && 'Conduct vulnerability assessments, penetration testing, and security audits with automated remediation tracking.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
