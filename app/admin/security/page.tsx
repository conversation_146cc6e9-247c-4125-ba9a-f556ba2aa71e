'use client'

import React, { useState } from 'react'
import { 
  Shield, 
  Lock, 
  Eye, 
  AlertTriangle, 
  Key, 
  Globe,
  Users,
  Activity,
  FileText,
  Settings,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp
} from 'lucide-react'

interface SecurityEvent {
  id: string
  type: 'login_success' | 'login_failed' | 'permission_denied' | 'data_breach' | 'suspicious_activity'
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: Date
  user: string
  description: string
  ip: string
  location: string
}

export default function SecurityCenterPage() {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'events' | 'policies' | 'monitoring'>('overview')

  // Mock security events - replace with actual data
  const securityEvents: SecurityEvent[] = [
    {
      id: '1',
      type: 'login_failed',
      severity: 'medium',
      timestamp: new Date(Date.now() - 300000),
      user: 'unknown',
      description: 'Multiple failed login attempts from suspicious IP',
      ip: '*************',
      location: 'Unknown'
    },
    {
      id: '2',
      type: 'permission_denied',
      severity: 'low',
      timestamp: new Date(Date.now() - 600000),
      user: '<EMAIL>',
      description: 'Attempted to access admin management without permissions',
      ip: '*********',
      location: 'San Francisco, CA'
    },
    {
      id: '3',
      type: 'login_success',
      severity: 'low',
      timestamp: new Date(Date.now() - 1200000),
      user: '<EMAIL>',
      description: 'Successful admin login',
      ip: '*********',
      location: 'San Francisco, CA'
    }
  ]

  const getSeverityColor = (severity: string) => {
    const colors = {
      low: 'bg-green-100 text-green-800 border-green-200',
      medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      high: 'bg-orange-100 text-orange-800 border-orange-200',
      critical: 'bg-red-100 text-red-800 border-red-200'
    }
    return colors[severity as keyof typeof colors] || colors.low
  }

  const getEventIcon = (type: string) => {
    const icons = {
      login_success: CheckCircle,
      login_failed: XCircle,
      permission_denied: Lock,
      data_breach: AlertTriangle,
      suspicious_activity: Eye
    }
    return icons[type as keyof typeof icons] || AlertTriangle
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Security Center</h1>
            <p className="text-gray-400">Monitor security events, manage policies, and protect your admin panel</p>
          </div>
          <div className="flex space-x-3">
            <button className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
              <AlertTriangle size={18} />
              <span>Security Scan</span>
            </button>
            <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
              <Shield size={18} />
              <span>Emergency Lock</span>
            </button>
          </div>
        </div>
      </div>

      {/* Security Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Security Score</p>
              <p className="text-2xl font-bold text-green-400">95/100</p>
            </div>
            <Shield className="text-green-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Excellent security posture</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Failed Attempts</p>
              <p className="text-2xl font-bold text-yellow-400">12</p>
            </div>
            <XCircle className="text-yellow-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Last 24 hours</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Sessions</p>
              <p className="text-2xl font-bold text-blue-400">8</p>
            </div>
            <Users className="text-blue-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Current admin sessions</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Security Events</p>
              <p className="text-2xl font-bold text-red-400">3</p>
            </div>
            <AlertTriangle className="text-red-400" size={32} />
          </div>
          <p className="text-xs text-gray-500 mt-2">Requiring attention</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: 'Security Overview', icon: Shield },
              { id: 'events', label: 'Security Events', icon: Activity },
              { id: 'policies', label: 'Security Policies', icon: FileText },
              { id: 'monitoring', label: 'Real-time Monitoring', icon: Eye }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Security Overview Tab */}
      {selectedTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Key className="mr-2" size={20} />
              Authentication Security
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Two-Factor Authentication</span>
                <span className="text-green-400 flex items-center">
                  <CheckCircle size={16} className="mr-1" />
                  Enabled
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Password Policy</span>
                <span className="text-green-400 flex items-center">
                  <CheckCircle size={16} className="mr-1" />
                  Strong
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Session Timeout</span>
                <span className="text-blue-400">30 minutes</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Login Attempt Limit</span>
                <span className="text-blue-400">5 attempts</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Globe className="mr-2" size={20} />
              Network Security
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">IP Whitelist</span>
                <span className="text-green-400 flex items-center">
                  <CheckCircle size={16} className="mr-1" />
                  Active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">SSL/TLS</span>
                <span className="text-green-400 flex items-center">
                  <CheckCircle size={16} className="mr-1" />
                  Enforced
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Rate Limiting</span>
                <span className="text-green-400 flex items-center">
                  <CheckCircle size={16} className="mr-1" />
                  Enabled
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">VPN Required</span>
                <span className="text-yellow-400 flex items-center">
                  <Clock size={16} className="mr-1" />
                  Optional
                </span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 lg:col-span-2">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <TrendingUp className="mr-2" size={20} />
              Security Trends (Last 30 Days)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">156</div>
                <div className="text-sm text-gray-400">Successful Logins</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">23</div>
                <div className="text-sm text-gray-400">Failed Attempts</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">2</div>
                <div className="text-sm text-gray-400">Security Incidents</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Events Tab */}
      {selectedTab === 'events' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Recent Security Events</h3>
            <div className="flex space-x-2">
              <select className="bg-gray-700 text-white rounded px-3 py-1 text-sm">
                <option>All Severities</option>
                <option>Critical</option>
                <option>High</option>
                <option>Medium</option>
                <option>Low</option>
              </select>
              <select className="bg-gray-700 text-white rounded px-3 py-1 text-sm">
                <option>All Events</option>
                <option>Login Events</option>
                <option>Permission Events</option>
                <option>Security Breaches</option>
              </select>
            </div>
          </div>
          <div className="space-y-4">
            {securityEvents.map((event) => {
              const EventIcon = getEventIcon(event.type)
              return (
                <div key={event.id} className="flex items-start space-x-4 p-4 rounded-lg border border-gray-700 hover:border-gray-600">
                  <EventIcon className="text-gray-400 mt-1" size={20} />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">{event.description}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(event.severity)}`}>
                        {event.severity.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400 grid grid-cols-2 md:grid-cols-4 gap-4">
                      <span>User: {event.user}</span>
                      <span>IP: {event.ip}</span>
                      <span>Location: {event.location}</span>
                      <span>Time: {event.timestamp.toLocaleTimeString()}</span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Security Policies Tab */}
      {selectedTab === 'policies' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Password Policy</h3>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                Minimum 12 characters
              </div>
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                Uppercase and lowercase letters
              </div>
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                Numbers and special characters
              </div>
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                Password rotation every 90 days
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Access Control</h3>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                Role-based permissions
              </div>
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                Multi-factor authentication
              </div>
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                Session management
              </div>
              <div className="flex items-center text-gray-300">
                <CheckCircle className="text-green-400 mr-2" size={16} />
                IP address restrictions
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Real-time Monitoring Tab */}
      {selectedTab === 'monitoring' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-6">Real-time Security Monitoring</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-white font-medium mb-3">Active Threats</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <span className="text-gray-300">Brute Force Attempts</span>
                  <span className="text-yellow-400">2 active</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <span className="text-gray-300">Suspicious IPs</span>
                  <span className="text-red-400">1 blocked</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <span className="text-gray-300">DDoS Attempts</span>
                  <span className="text-green-400">0 detected</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-white font-medium mb-3">System Health</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <span className="text-gray-300">Firewall Status</span>
                  <span className="text-green-400">Active</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <span className="text-gray-300">SSL Certificate</span>
                  <span className="text-green-400">Valid</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                  <span className="text-gray-300">Security Updates</span>
                  <span className="text-green-400">Current</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}