'use client'

import React, { useState } from 'react'
import { 
  Settings, 
  Save, 
  Globe, 
  Mail, 
  Shield, 
  Database,
  Bell,
  Palette,
  Users,
  Lock,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Upload,
  Download
} from 'lucide-react'

export default function GlobalSettingsPage() {
  const [selectedTab, setSelectedTab] = useState<'general' | 'security' | 'email' | 'notifications' | 'appearance' | 'advanced'>('general')
  const [showSecrets, setShowSecrets] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const handleSave = () => {
    // Simulate save operation
    setHasChanges(false)
    // Show success message
  }

  const handleInputChange = () => {
    setHasChanges(true)
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Global Settings</h1>
            <p className="text-gray-400">Configure system-wide settings and preferences</p>
          </div>
          <div className="flex space-x-3">
            <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
              <Download size={18} />
              <span>Export Config</span>
            </button>
            <button 
              onClick={handleSave}
              disabled={!hasChanges}
              className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <Save size={18} />
              <span>Save Changes</span>
            </button>
          </div>
        </div>
        {hasChanges && (
          <div className="mt-4 bg-yellow-900 border border-yellow-700 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="text-yellow-400" size={16} />
              <span className="text-yellow-200 text-sm">You have unsaved changes</span>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'general', label: 'General', icon: Settings },
              { id: 'security', label: 'Security', icon: Shield },
              { id: 'email', label: 'Email', icon: Mail },
              { id: 'notifications', label: 'Notifications', icon: Bell },
              { id: 'appearance', label: 'Appearance', icon: Palette },
              { id: 'advanced', label: 'Advanced', icon: Database }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* General Settings Tab */}
      {selectedTab === 'general' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Globe className="mr-2" size={20} />
              Site Configuration
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Site Name</label>
                <input 
                  type="text" 
                  defaultValue="Syndicaps Admin"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Site URL</label>
                <input 
                  type="url" 
                  defaultValue="https://admin.syndicaps.com"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Admin Email</label>
                <input 
                  type="email" 
                  defaultValue="<EMAIL>"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
                <select 
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                >
                  <option>UTC</option>
                  <option>America/New_York</option>
                  <option>America/Los_Angeles</option>
                  <option>Europe/London</option>
                  <option>Asia/Tokyo</option>
                </select>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Users className="mr-2" size={20} />
              User Management
            </h3>
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox" 
                  defaultChecked
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Allow user registration</span>
              </label>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox" 
                  defaultChecked
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Email verification required</span>
              </label>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox"
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Admin approval for new users</span>
              </label>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Default User Role</label>
                <select 
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                >
                  <option>user</option>
                  <option>subscriber</option>
                  <option>contributor</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Settings Tab */}
      {selectedTab === 'security' && (
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Lock className="mr-2" size={20} />
              Authentication & Access Control
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Session Timeout (minutes)</label>
                  <input 
                    type="number" 
                    defaultValue="30"
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Login Attempts</label>
                  <input 
                    type="number" 
                    defaultValue="5"
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Lockout Duration (minutes)</label>
                  <input 
                    type="number" 
                    defaultValue="15"
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox" 
                    defaultChecked
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">Require 2FA for admins</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox" 
                    defaultChecked
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">Force HTTPS</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox"
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">IP whitelist only</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox" 
                    defaultChecked
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">Log all admin actions</span>
                </label>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Shield className="mr-2" size={20} />
              API Keys & Secrets
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-300">Show sensitive information</span>
                <button
                  onClick={() => setShowSecrets(!showSecrets)}
                  className="flex items-center space-x-2 text-gray-400 hover:text-white"
                >
                  {showSecrets ? <EyeOff size={16} /> : <Eye size={16} />}
                  <span>{showSecrets ? 'Hide' : 'Show'}</span>
                </button>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Firebase API Key</label>
                <input 
                  type={showSecrets ? "text" : "password"}
                  defaultValue="AIzaSyDmock_firebase_key_12345"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Database URL</label>
                <input 
                  type={showSecrets ? "text" : "password"}
                  defaultValue="postgresql://user:pass@localhost:5432/syndicaps"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">JWT Secret</label>
                <input 
                  type={showSecrets ? "text" : "password"}
                  defaultValue="super_secure_jwt_secret_key_2024"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Email Settings Tab */}
      {selectedTab === 'email' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Mail className="mr-2" size={20} />
            Email Configuration
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">SMTP Host</label>
                <input 
                  type="text" 
                  defaultValue="smtp.gmail.com"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">SMTP Port</label>
                <input 
                  type="number" 
                  defaultValue="587"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
                <input 
                  type="text" 
                  defaultValue="<EMAIL>"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Password</label>
                <input 
                  type={showSecrets ? "text" : "password"}
                  defaultValue="email_password_123"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">From Name</label>
                <input 
                  type="text" 
                  defaultValue="Syndicaps Admin"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">From Email</label>
                <input 
                  type="email" 
                  defaultValue="<EMAIL>"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                />
              </div>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox" 
                  defaultChecked
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Use TLS/SSL</span>
              </label>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                Test Email Configuration
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notifications Tab */}
      {selectedTab === 'notifications' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Bell className="mr-2" size={20} />
              Admin Notifications
            </h3>
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox" 
                  defaultChecked
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">New user registrations</span>
              </label>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox" 
                  defaultChecked
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Security alerts</span>
              </label>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox"
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">System errors</span>
              </label>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox" 
                  defaultChecked
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Backup status</span>
              </label>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox"
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Performance alerts</span>
              </label>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Notification Recipients</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                <span className="text-white"><EMAIL></span>
                <span className="text-green-400 text-sm">All notifications</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                <span className="text-white"><EMAIL></span>
                <span className="text-yellow-400 text-sm">Security only</span>
              </div>
              <button className="w-full p-3 border-2 border-dashed border-gray-600 rounded text-gray-400 hover:border-gray-500 hover:text-gray-300">
                + Add recipient
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Appearance Tab */}
      {selectedTab === 'appearance' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Palette className="mr-2" size={20} />
            Admin Panel Appearance
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Theme</label>
                <select 
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                >
                  <option>Dark (Default)</option>
                  <option>Light</option>
                  <option>Auto (System)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Accent Color</label>
                <div className="flex space-x-3">
                  <button className="w-8 h-8 bg-red-600 rounded border-2 border-red-400"></button>
                  <button className="w-8 h-8 bg-blue-600 rounded border-2 border-transparent"></button>
                  <button className="w-8 h-8 bg-green-600 rounded border-2 border-transparent"></button>
                  <button className="w-8 h-8 bg-purple-600 rounded border-2 border-transparent"></button>
                  <button className="w-8 h-8 bg-yellow-600 rounded border-2 border-transparent"></button>
                </div>
              </div>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox" 
                  defaultChecked
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Compact mode</span>
              </label>
              <label className="flex items-center space-x-3">
                <input 
                  type="checkbox"
                  onChange={handleInputChange}
                  className="rounded text-red-600 focus:ring-red-500" 
                />
                <span className="text-gray-300">Hide sidebar by default</span>
              </label>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Custom Logo</label>
                <div className="flex items-center space-x-3">
                  <input 
                    type="file" 
                    accept="image/*"
                    onChange={handleInputChange}
                    className="hidden" 
                    id="logo-upload"
                  />
                  <label 
                    htmlFor="logo-upload"
                    className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg cursor-pointer flex items-center space-x-2"
                  >
                    <Upload size={16} />
                    <span>Upload Logo</span>
                  </label>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Custom CSS</label>
                <textarea 
                  rows={4}
                  placeholder="/* Custom CSS overrides */"
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none font-mono text-sm"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Advanced Settings Tab */}
      {selectedTab === 'advanced' && (
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Database className="mr-2" size={20} />
              System Configuration
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Cache TTL (seconds)</label>
                  <input 
                    type="number" 
                    defaultValue="3600"
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Upload Size (MB)</label>
                  <input 
                    type="number" 
                    defaultValue="50"
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">API Rate Limit (requests/minute)</label>
                  <input 
                    type="number" 
                    defaultValue="1000"
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox" 
                    defaultChecked
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">Enable Redis caching</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox"
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">Debug mode</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox" 
                    defaultChecked
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">Enable CDN</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input 
                    type="checkbox"
                    onChange={handleInputChange}
                    className="rounded text-red-600 focus:ring-red-500" 
                  />
                  <span className="text-gray-300">Maintenance mode</span>
                </label>
              </div>
            </div>
          </div>

          <div className="bg-red-900 border border-red-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-300 mb-4 flex items-center">
              <AlertTriangle className="mr-2" size={20} />
              Danger Zone
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-200 font-medium">Clear All Cache</h4>
                  <p className="text-red-300 text-sm">Remove all cached data from the system</p>
                </div>
                <button className="bg-red-700 hover:bg-red-800 text-white px-4 py-2 rounded-lg">
                  Clear Cache
                </button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-200 font-medium">Reset to Defaults</h4>
                  <p className="text-red-300 text-sm">Reset all settings to their default values</p>
                </div>
                <button className="bg-red-700 hover:bg-red-800 text-white px-4 py-2 rounded-lg">
                  Reset Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}