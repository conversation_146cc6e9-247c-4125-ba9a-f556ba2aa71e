/**
 * Admin Status Page
 * 
 * Simple status page to check authentication state and session validity
 * without complex components that might trigger the JSON error.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect } from 'react';
import { useUser } from '../../../src/lib/useUser';

const AdminStatusPage: React.FC = () => {
  const { user, profile, loading } = useUser();
  const [cookies, setCookies] = useState<Record<string, string>>({});
  const [apiTest, setApiTest] = useState<{ status: string; data?: any; error?: string }>({ status: 'idle' });

  useEffect(() => {
    // Get all cookies
    if (typeof document !== 'undefined') {
      const cookieString = document.cookie;
      const cookieObj: Record<string, string> = {};
      
      cookieString.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
          cookieObj[name] = decodeURIComponent(value);
        }
      });
      
      setCookies(cookieObj);
    }
  }, []);

  const testApiEndpoint = async () => {
    setApiTest({ status: 'loading' });
    
    try {
      const response = await fetch('/api/admin/diagnostics?test=basic');
      const data = await response.json();
      
      setApiTest({ 
        status: response.ok ? 'success' : 'error', 
        data: data 
      });
    } catch (error) {
      setApiTest({ 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  };

  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        backgroundColor: '#0f172a', 
        color: 'white', 
        padding: '2rem',
        fontFamily: 'monospace'
      }}>
        <h1>🔄 Loading...</h1>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#0f172a', 
      color: 'white', 
      padding: '2rem',
      fontFamily: 'monospace',
      lineHeight: '1.6'
    }}>
      <h1 style={{ color: '#60a5fa', marginBottom: '2rem' }}>
        🔍 Admin Authentication Status
      </h1>

      {/* User Authentication Status */}
      <div style={{ 
        backgroundColor: '#1e293b', 
        padding: '1.5rem', 
        borderRadius: '8px', 
        marginBottom: '2rem',
        border: '1px solid #334155'
      }}>
        <h2 style={{ color: '#34d399', marginBottom: '1rem' }}>👤 User Authentication</h2>
        
        <div style={{ marginBottom: '1rem' }}>
          <strong>Status:</strong> {user ? '✅ Authenticated' : '❌ Not Authenticated'}
        </div>
        
        {user && (
          <>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Email:</strong> {user.email}
            </div>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>UID:</strong> {user.uid}
            </div>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Email Verified:</strong> {user.emailVerified ? '✅ Yes' : '❌ No'}
            </div>
          </>
        )}
      </div>

      {/* Profile Status */}
      <div style={{ 
        backgroundColor: '#1e293b', 
        padding: '1.5rem', 
        borderRadius: '8px', 
        marginBottom: '2rem',
        border: '1px solid #334155'
      }}>
        <h2 style={{ color: '#34d399', marginBottom: '1rem' }}>📋 Profile Information</h2>
        
        <div style={{ marginBottom: '1rem' }}>
          <strong>Status:</strong> {profile ? '✅ Loaded' : '❌ Not Loaded'}
        </div>
        
        {profile && (
          <>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Role:</strong> {profile.role}
            </div>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Points:</strong> {profile.points || 0}
            </div>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Display Name:</strong> {profile.displayName || 'Not set'}
            </div>
          </>
        )}
      </div>

      {/* Cookies Status */}
      <div style={{ 
        backgroundColor: '#1e293b', 
        padding: '1.5rem', 
        borderRadius: '8px', 
        marginBottom: '2rem',
        border: '1px solid #334155'
      }}>
        <h2 style={{ color: '#34d399', marginBottom: '1rem' }}>🍪 Authentication Cookies</h2>
        
        <div style={{ fontSize: '0.9rem' }}>
          {Object.entries(cookies).filter(([key]) => 
            key.includes('firebase') || 
            key.includes('admin') || 
            key.includes('user') || 
            key.includes('mfa')
          ).map(([key, value]) => (
            <div key={key} style={{ marginBottom: '0.5rem' }}>
              <strong>{key}:</strong> {value.length > 50 ? `${value.substring(0, 50)}...` : value}
            </div>
          ))}
        </div>
      </div>

      {/* API Test */}
      <div style={{ 
        backgroundColor: '#1e293b', 
        padding: '1.5rem', 
        borderRadius: '8px', 
        marginBottom: '2rem',
        border: '1px solid #334155'
      }}>
        <h2 style={{ color: '#34d399', marginBottom: '1rem' }}>🔌 API Connectivity Test</h2>
        
        <button 
          onClick={testApiEndpoint}
          disabled={apiTest.status === 'loading'}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '4px',
            cursor: apiTest.status === 'loading' ? 'not-allowed' : 'pointer',
            marginBottom: '1rem'
          }}
        >
          {apiTest.status === 'loading' ? '🔄 Testing...' : '🧪 Test API Endpoint'}
        </button>
        
        {apiTest.status !== 'idle' && (
          <div style={{ 
            padding: '1rem', 
            backgroundColor: apiTest.status === 'success' ? '#065f46' : '#7f1d1d',
            borderRadius: '4px',
            border: `1px solid ${apiTest.status === 'success' ? '#10b981' : '#ef4444'}`
          }}>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Status:</strong> {apiTest.status === 'success' ? '✅ Success' : '❌ Error'}
            </div>
            
            {apiTest.data && (
              <div style={{ fontSize: '0.9rem' }}>
                <strong>Response:</strong>
                <pre style={{ 
                  marginTop: '0.5rem', 
                  padding: '0.5rem', 
                  backgroundColor: '#000', 
                  borderRadius: '4px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(apiTest.data, null, 2)}
                </pre>
              </div>
            )}
            
            {apiTest.error && (
              <div style={{ fontSize: '0.9rem' }}>
                <strong>Error:</strong> {apiTest.error}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Environment Info */}
      <div style={{ 
        backgroundColor: '#1e293b', 
        padding: '1.5rem', 
        borderRadius: '8px', 
        border: '1px solid #334155'
      }}>
        <h2 style={{ color: '#34d399', marginBottom: '1rem' }}>⚙️ Environment Information</h2>
        
        <div style={{ fontSize: '0.9rem' }}>
          <div style={{ marginBottom: '0.5rem' }}>
            <strong>Environment:</strong> {process.env.NODE_ENV}
          </div>
          <div style={{ marginBottom: '0.5rem' }}>
            <strong>Firebase Project:</strong> {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'Not configured'}
          </div>
          <div style={{ marginBottom: '0.5rem' }}>
            <strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}
          </div>
          <div style={{ marginBottom: '0.5rem' }}>
            <strong>User Agent:</strong> {typeof navigator !== 'undefined' ? navigator.userAgent.substring(0, 100) + '...' : 'N/A'}
          </div>
        </div>
      </div>

      {/* Navigation Links */}
      <div style={{ marginTop: '2rem', textAlign: 'center' }}>
        <a 
          href="/admin/dashboard" 
          style={{ 
            color: '#60a5fa', 
            textDecoration: 'none', 
            marginRight: '2rem',
            padding: '0.5rem 1rem',
            border: '1px solid #60a5fa',
            borderRadius: '4px'
          }}
        >
          🏠 Go to Dashboard
        </a>
        <a 
          href="/admin/diagnostics" 
          style={{ 
            color: '#60a5fa', 
            textDecoration: 'none',
            padding: '0.5rem 1rem',
            border: '1px solid #60a5fa',
            borderRadius: '4px'
          }}
        >
          🔍 Full Diagnostics
        </a>
      </div>
    </div>
  );
};

export default AdminStatusPage;
