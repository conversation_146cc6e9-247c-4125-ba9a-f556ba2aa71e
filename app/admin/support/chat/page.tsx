'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  MessageSquare, 
  Send, 
  User, 
  Clock, 
  Phone,
  Video,
  Paperclip,
  Smile,
  MoreVertical,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

interface ChatMessage {
  id: string
  senderId: string
  senderName: string
  senderType: 'admin' | 'user'
  content: string
  timestamp: Date
  type: 'text' | 'image' | 'file' | 'system'
  status: 'sent' | 'delivered' | 'read'
}

interface ChatSession {
  id: string
  userId: string
  userName: string
  userEmail: string
  userAvatar?: string
  status: 'active' | 'waiting' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  category: 'general' | 'technical' | 'billing' | 'orders' | 'refunds'
  startedAt: Date
  lastMessageAt: Date
  assignedTo?: string
  messages: ChatMessage[]
  unreadCount: number
}

export default function LiveChatPage() {
  const [selectedChat, setSelectedChat] = useState<string | null>(null)
  const [messageInput, setMessageInput] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('active')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Mock chat sessions data
  const chatSessions: ChatSession[] = [
    {
      id: '1',
      userId: 'user123',
      userName: 'John Smith',
      userEmail: '<EMAIL>',
      status: 'active',
      priority: 'high',
      category: 'technical',
      startedAt: new Date(Date.now() - 1800000), // 30 minutes ago
      lastMessageAt: new Date(Date.now() - 300000), // 5 minutes ago
      assignedTo: '<EMAIL>',
      unreadCount: 2,
      messages: [
        {
          id: '1',
          senderId: 'user123',
          senderName: 'John Smith',
          senderType: 'user',
          content: 'Hi, I\'m having trouble with my recent order. The payment went through but I haven\'t received a confirmation.',
          timestamp: new Date(Date.now() - 1800000),
          type: 'text',
          status: 'read'
        },
        {
          id: '2',
          senderId: 'admin1',
          senderName: 'Sarah (Support)',
          senderType: 'admin',
          content: 'Hello John! I\'m sorry to hear about that. Let me look up your order details. Can you please provide your order number?',
          timestamp: new Date(Date.now() - 1620000),
          type: 'text',
          status: 'read'
        },
        {
          id: '3',
          senderId: 'user123',
          senderName: 'John Smith',
          senderType: 'user',
          content: 'Sure, it\'s #ORD-2024-001523',
          timestamp: new Date(Date.now() - 1500000),
          type: 'text',
          status: 'read'
        },
        {
          id: '4',
          senderId: 'user123',
          senderName: 'John Smith',
          senderType: 'user',
          content: 'I\'ve been waiting for almost 30 minutes now. Is anyone there?',
          timestamp: new Date(Date.now() - 300000),
          type: 'text',
          status: 'delivered'
        }
      ]
    },
    {
      id: '2',
      userId: 'user456',
      userName: 'Emily Johnson',
      userEmail: '<EMAIL>',
      status: 'waiting',
      priority: 'medium',
      category: 'billing',
      startedAt: new Date(Date.now() - 600000), // 10 minutes ago
      lastMessageAt: new Date(Date.now() - 600000),
      unreadCount: 1,
      messages: [
        {
          id: '5',
          senderId: 'user456',
          senderName: 'Emily Johnson',
          senderType: 'user',
          content: 'Hello! I need help understanding my subscription charges. There seems to be an unexpected charge on my card.',
          timestamp: new Date(Date.now() - 600000),
          type: 'text',
          status: 'delivered'
        }
      ]
    },
    {
      id: '3',
      userId: 'user789',
      userName: 'Michael Brown',
      userEmail: '<EMAIL>',
      status: 'closed',
      priority: 'low',
      category: 'general',
      startedAt: new Date(Date.now() - 3600000), // 1 hour ago
      lastMessageAt: new Date(Date.now() - 1800000), // 30 minutes ago
      unreadCount: 0,
      messages: [
        {
          id: '6',
          senderId: 'user789',
          senderName: 'Michael Brown',
          senderType: 'user',
          content: 'Hi, just wanted to say thanks for the great service!',
          timestamp: new Date(Date.now() - 3600000),
          type: 'text',
          status: 'read'
        },
        {
          id: '7',
          senderId: 'admin1',
          senderName: 'Sarah (Support)',
          senderType: 'admin',
          content: 'Thank you so much for the kind words, Michael! We really appreciate your feedback. Is there anything else I can help you with today?',
          timestamp: new Date(Date.now() - 3300000),
          type: 'text',
          status: 'read'
        },
        {
          id: '8',
          senderId: 'user789',
          senderName: 'Michael Brown',
          senderType: 'user',
          content: 'No, that\'s all. Have a great day!',
          timestamp: new Date(Date.now() - 1800000),
          type: 'text',
          status: 'read'
        }
      ]
    }
  ]

  const selectedChatData = chatSessions.find(chat => chat.id === selectedChat)

  const filteredChats = chatSessions.filter(chat => {
    const matchesSearch = chat.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         chat.userEmail.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || chat.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [selectedChatData?.messages])

  const sendMessage = () => {
    if (!messageInput.trim() || !selectedChat) return

    // Add message logic here
    setMessageInput('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority as keyof typeof colors] || colors.low
  }

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'text-green-400',
      waiting: 'text-yellow-400',
      closed: 'text-gray-400'
    }
    return colors[status as keyof typeof colors] || colors.waiting
  }

  const getStatusIcon = (status: string) => {
    const icons = {
      active: CheckCircle,
      waiting: AlertCircle,
      closed: XCircle
    }
    return icons[status as keyof typeof icons] || AlertCircle
  }

  return (
    <div className="h-screen bg-gray-950 flex flex-col">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Live Chat Support</h1>
            <p className="text-gray-400">Manage customer conversations in real-time</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="bg-gray-800 rounded-lg p-3">
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-gray-300">Online</span>
                </div>
                <div className="text-gray-400">|</div>
                <div className="text-gray-300">
                  {chatSessions.filter(c => c.status === 'active').length} Active Chats
                </div>
                <div className="text-gray-400">|</div>
                <div className="text-gray-300">
                  {chatSessions.filter(c => c.status === 'waiting').length} Waiting
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Chat List Sidebar */}
        <div className="w-80 bg-gray-900 border-r border-gray-800 flex flex-col">
          {/* Search and Filters */}
          <div className="p-4 border-b border-gray-800">
            <div className="relative mb-3">
              <Search className="absolute left-3 top-3 text-gray-400" size={16} />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
            >
              <option value="all">All Conversations</option>
              <option value="active">Active</option>
              <option value="waiting">Waiting</option>
              <option value="closed">Closed</option>
            </select>
          </div>

          {/* Chat List */}
          <div className="flex-1 overflow-y-auto">
            {filteredChats.map((chat) => {
              const StatusIcon = getStatusIcon(chat.status)
              return (
                <div
                  key={chat.id}
                  onClick={() => setSelectedChat(chat.id)}
                  className={`p-4 border-b border-gray-800 cursor-pointer hover:bg-gray-800 transition-colors ${
                    selectedChat === chat.id ? 'bg-gray-800 border-r-2 border-red-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="text-white" size={20} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-white font-medium truncate">{chat.userName}</h3>
                        <div className="flex items-center space-x-1">
                          <StatusIcon className={getStatusColor(chat.status)} size={14} />
                          {chat.unreadCount > 0 && (
                            <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                              {chat.unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-400 text-sm truncate">
                        {chat.messages[chat.messages.length - 1]?.content || 'No messages'}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(chat.priority)}`}>
                          {chat.priority.toUpperCase()}
                        </span>
                        <span className="text-gray-500 text-xs">
                          {new Date(chat.lastMessageAt).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedChatData ? (
            <>
              {/* Chat Header */}
              <div className="bg-gray-900 border-b border-gray-800 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                      <User className="text-white" size={24} />
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">{selectedChatData.userName}</h3>
                      <p className="text-gray-400 text-sm">{selectedChatData.userEmail}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(selectedChatData.priority)}`}>
                        {selectedChatData.priority.toUpperCase()}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
                        {selectedChatData.category.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg">
                      <Phone size={18} />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg">
                      <Video size={18} />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg">
                      <MoreVertical size={18} />
                    </button>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {selectedChatData.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.senderType === 'admin' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.senderType === 'admin'
                          ? 'bg-red-600 text-white'
                          : 'bg-gray-700 text-white'
                      }`}
                    >
                      {message.senderType === 'user' && (
                        <p className="text-xs text-gray-400 mb-1">{message.senderName}</p>
                      )}
                      <p className="text-sm">{message.content}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs opacity-75">
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                        {message.senderType === 'admin' && (
                          <div className="flex items-center space-x-1">
                            {message.status === 'sent' && <Clock size={12} className="opacity-75" />}
                            {message.status === 'delivered' && <CheckCircle size={12} className="opacity-75" />}
                            {message.status === 'read' && <CheckCircle size={12} className="text-blue-400" />}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="bg-gray-900 border-t border-gray-800 p-4">
                <div className="flex items-end space-x-3">
                  <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg">
                    <Paperclip size={18} />
                  </button>
                  <div className="flex-1">
                    <textarea
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message..."
                      className="w-full px-4 py-3 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none resize-none"
                      rows={1}
                    />
                  </div>
                  <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg">
                    <Smile size={18} />
                  </button>
                  <button
                    onClick={sendMessage}
                    disabled={!messageInput.trim()}
                    className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white p-3 rounded-lg transition-colors"
                  >
                    <Send size={18} />
                  </button>
                </div>
              </div>
            </>
          ) : (
            /* No Chat Selected */
            <div className="flex-1 flex items-center justify-center bg-gray-900">
              <div className="text-center">
                <MessageSquare className="mx-auto text-gray-600 mb-4" size={64} />
                <h3 className="text-xl font-semibold text-gray-400 mb-2">Select a conversation</h3>
                <p className="text-gray-500">Choose a chat from the sidebar to start helping customers</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}