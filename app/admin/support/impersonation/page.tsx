'use client'

import React, { useState } from 'react'
import { 
  UserCheck, 
  Search, 
  Eye, 
  LogIn, 
  LogOut, 
  AlertTriangle, 
  Shield,
  Clock,
  User,
  Mail,
  Calendar,
  Activity,
  ExternalLink,
  History,
  Lock
} from 'lucide-react'

interface User {
  id: string
  email: string
  name: string
  role: string
  avatar?: string
  status: 'active' | 'inactive' | 'suspended'
  lastLogin: Date
  registeredAt: Date
  verified: boolean
  totalOrders: number
  totalSpent: number
}

interface ImpersonationSession {
  id: string
  adminId: string
  adminName: string
  userId: string
  userName: string
  userEmail: string
  startedAt: Date
  endedAt?: Date
  duration?: number
  reason: string
  ipAddress: string
  actions: ImpersonationAction[]
}

interface ImpersonationAction {
  id: string
  action: string
  page: string
  timestamp: Date
  details?: string
}

export default function UserImpersonationPage() {
  const [selectedTab, setSelectedTab] = useState<'search' | 'active' | 'history'>('search')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [impersonationReason, setImpersonationReason] = useState('')
  const [currentSession, setCurrentSession] = useState<ImpersonationSession | null>(null)
  const [showConfirmModal, setShowConfirmModal] = useState(false)

  // Mock users data
  const users: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'John Doe',
      role: 'customer',
      status: 'active',
      lastLogin: new Date(Date.now() - 3600000),
      registeredAt: new Date(Date.now() - 2592000000),
      verified: true,
      totalOrders: 15,
      totalSpent: 1247.50
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Jane Smith',
      role: 'premium',
      status: 'active',
      lastLogin: new Date(Date.now() - 86400000),
      registeredAt: new Date(Date.now() - 7776000000),
      verified: true,
      totalOrders: 28,
      totalSpent: 3842.75
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: 'Mike Wilson',
      role: 'customer',
      status: 'inactive',
      lastLogin: new Date(Date.now() - 2592000000),
      registeredAt: new Date(Date.now() - 15552000000),
      verified: false,
      totalOrders: 3,
      totalSpent: 157.25
    }
  ]

  // Mock impersonation sessions
  const impersonationSessions: ImpersonationSession[] = [
    {
      id: '1',
      adminId: 'admin1',
      adminName: 'Sarah (Support)',
      userId: '1',
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      startedAt: new Date(Date.now() - 7200000),
      endedAt: new Date(Date.now() - 5400000),
      duration: 1800000, // 30 minutes
      reason: 'Customer reported checkout issues, needed to reproduce bug',
      ipAddress: '*************',
      actions: [
        {
          id: '1',
          action: 'Viewed Profile',
          page: '/profile',
          timestamp: new Date(Date.now() - 7200000),
          details: 'Checked account settings'
        },
        {
          id: '2',
          action: 'Added Product to Cart',
          page: '/products/123',
          timestamp: new Date(Date.now() - 6900000),
          details: 'Added Premium Sneakers - Size 10'
        },
        {
          id: '3',
          action: 'Attempted Checkout',
          page: '/checkout',
          timestamp: new Date(Date.now() - 6600000),
          details: 'Reproduced payment gateway error'
        }
      ]
    },
    {
      id: '2',
      adminId: 'admin2',
      adminName: 'Tom (Technical)',
      userId: '2',
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
      startedAt: new Date(Date.now() - *********),
      endedAt: new Date(Date.now() - *********),
      duration: 3600000, // 1 hour
      reason: 'User unable to access premium features, investigating permissions',
      ipAddress: '*********',
      actions: [
        {
          id: '4',
          action: 'Viewed Dashboard',
          page: '/dashboard',
          timestamp: new Date(Date.now() - *********)
        },
        {
          id: '5',
          action: 'Accessed Premium Content',
          page: '/premium/exclusive',
          timestamp: new Date(Date.now() - 171000000),
          details: 'Verified premium access working correctly'
        }
      ]
    }
  ]

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800 border-green-200',
      inactive: 'bg-gray-100 text-gray-800 border-gray-200',
      suspended: 'bg-red-100 text-red-800 border-red-200'
    }
    return colors[status as keyof typeof colors] || colors.inactive
  }

  const startImpersonation = () => {
    if (!selectedUser || !impersonationReason.trim()) return
    
    setShowConfirmModal(false)
    // Start impersonation session
    const newSession: ImpersonationSession = {
      id: Date.now().toString(),
      adminId: 'current_admin',
      adminName: 'Current Admin',
      userId: selectedUser.id,
      userName: selectedUser.name,
      userEmail: selectedUser.email,
      startedAt: new Date(),
      reason: impersonationReason,
      ipAddress: '************',
      actions: []
    }
    setCurrentSession(newSession)
    setImpersonationReason('')
    setSelectedUser(null)
  }

  const endImpersonation = () => {
    if (currentSession) {
      const updatedSession = {
        ...currentSession,
        endedAt: new Date(),
        duration: Date.now() - currentSession.startedAt.getTime()
      }
      setCurrentSession(null)
      // Save session to history
    }
  }

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const hours = Math.floor(minutes / 60)
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Warning Banner */}
      <div className="bg-red-900 border border-red-700 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="text-red-400 mt-0.5" size={20} />
          <div>
            <h3 className="text-red-300 font-medium">Security Notice: User Impersonation</h3>
            <p className="text-red-200 text-sm mt-1">
              User impersonation is a powerful feature that allows you to experience the system as if you were logged in as another user. 
              All actions are logged and audited. Only use this feature for legitimate support purposes.
            </p>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">User Impersonation</h1>
            <p className="text-gray-400">Access user accounts for support and troubleshooting</p>
          </div>
          {currentSession && (
            <button
              onClick={endImpersonation}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              <LogOut size={18} />
              <span>End Impersonation</span>
            </button>
          )}
        </div>
      </div>

      {/* Current Session Alert */}
      {currentSession && (
        <div className="bg-yellow-900 border border-yellow-700 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <UserCheck className="text-yellow-400" size={20} />
              <div>
                <h3 className="text-yellow-300 font-medium">
                  Currently impersonating: {currentSession.userName}
                </h3>
                <p className="text-yellow-200 text-sm">
                  Started {currentSession.startedAt.toLocaleTimeString()} • 
                  Reason: {currentSession.reason}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="text-yellow-400" size={16} />
              <span className="text-yellow-300 font-mono text-sm">
                {formatDuration(Date.now() - currentSession.startedAt.getTime())}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'search', label: 'Search Users', icon: Search },
              { id: 'active', label: 'Active Sessions', icon: Activity },
              { id: 'history', label: 'Session History', icon: History }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Search Users Tab */}
      {selectedTab === 'search' && (
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="relative mb-6">
              <Search className="absolute left-3 top-3 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search users by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
              />
            </div>

            <div className="space-y-4">
              {filteredUsers.map((user) => (
                <div key={user.id} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                        <User className="text-white" size={24} />
                      </div>
                      <div>
                        <h3 className="text-white font-medium">{user.name}</h3>
                        <p className="text-gray-400 text-sm">{user.email}</p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <span>Role: {user.role}</span>
                          <span>Orders: {user.totalOrders}</span>
                          <span>Spent: ${user.totalSpent}</span>
                          <span>Last login: {user.lastLogin.toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(user.status)}`}>
                        {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                      </span>
                      <button
                        onClick={() => setSelectedUser(user)}
                        disabled={user.status !== 'active' || !!currentSession}
                        className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                      >
                        <LogIn size={16} />
                        <span>Impersonate</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Active Sessions Tab */}
      {selectedTab === 'active' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Active Impersonation Sessions</h3>
          {currentSession ? (
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <div>
                    <h4 className="text-white font-medium">
                      {currentSession.adminName} → {currentSession.userName}
                    </h4>
                    <p className="text-gray-400 text-sm">{currentSession.userEmail}</p>
                    <p className="text-gray-500 text-xs mt-1">
                      Reason: {currentSession.reason}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-white font-medium">
                    {formatDuration(Date.now() - currentSession.startedAt.getTime())}
                  </div>
                  <div className="text-gray-400 text-sm">
                    Started {currentSession.startedAt.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <UserCheck className="mx-auto text-gray-600 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-400 mb-2">No Active Sessions</h3>
              <p className="text-gray-500">No impersonation sessions are currently active</p>
            </div>
          )}
        </div>
      )}

      {/* Session History Tab */}
      {selectedTab === 'history' && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Impersonation History</h3>
          <div className="space-y-4">
            {impersonationSessions.map((session) => (
              <div key={session.id} className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-white font-medium">
                        {session.adminName} → {session.userName}
                      </h4>
                      <span className="text-gray-400 text-sm">({session.userEmail})</span>
                    </div>
                    <p className="text-gray-300 text-sm mb-3">{session.reason}</p>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-500">
                      <div>
                        <span className="block text-gray-400">Started:</span>
                        {session.startedAt.toLocaleString()}
                      </div>
                      <div>
                        <span className="block text-gray-400">Ended:</span>
                        {session.endedAt?.toLocaleString() || 'Active'}
                      </div>
                      <div>
                        <span className="block text-gray-400">Duration:</span>
                        {session.duration ? formatDuration(session.duration) : 'Ongoing'}
                      </div>
                      <div>
                        <span className="block text-gray-400">Actions:</span>
                        {session.actions.length}
                      </div>
                    </div>
                    <div className="mt-3">
                      <details className="text-sm">
                        <summary className="text-gray-400 cursor-pointer hover:text-white">
                          View Actions ({session.actions.length})
                        </summary>
                        <div className="mt-2 space-y-2">
                          {session.actions.map((action) => (
                            <div key={action.id} className="flex items-center justify-between bg-gray-800 p-2 rounded">
                              <div>
                                <span className="text-white">{action.action}</span>
                                {action.details && (
                                  <span className="text-gray-400 ml-2">- {action.details}</span>
                                )}
                              </div>
                              <div className="flex items-center space-x-2 text-xs text-gray-500">
                                <span>{action.page}</span>
                                <span>{action.timestamp.toLocaleTimeString()}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </details>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Impersonation Confirmation Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="text-yellow-400" size={24} />
              <h3 className="text-lg font-semibold text-white">Confirm User Impersonation</h3>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="text-white font-medium">{selectedUser.name}</h4>
                  <p className="text-gray-400 text-sm">{selectedUser.email}</p>
                  <p className="text-gray-500 text-xs">Role: {selectedUser.role} • Status: {selectedUser.status}</p>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Reason for Impersonation <span className="text-red-400">*</span>
              </label>
              <textarea
                value={impersonationReason}
                onChange={(e) => setImpersonationReason(e.target.value)}
                placeholder="Explain why you need to impersonate this user..."
                className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:outline-none"
                rows={3}
                required
              />
            </div>

            <div className="bg-yellow-900 border border-yellow-700 rounded-lg p-3 mb-4">
              <p className="text-yellow-200 text-xs">
                By proceeding, you acknowledge that this session will be logged and audited. 
                All actions performed will be recorded for security purposes.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button 
                onClick={() => {
                  setSelectedUser(null)
                  setImpersonationReason('')
                }}
                className="px-4 py-2 text-gray-400 hover:text-white"
              >
                Cancel
              </button>
              <button 
                onClick={startImpersonation}
                disabled={!impersonationReason.trim()}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                <Shield size={16} />
                <span>Start Impersonation</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}