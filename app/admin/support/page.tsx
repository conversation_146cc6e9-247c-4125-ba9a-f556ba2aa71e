'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Headphones, 
  Ticket, 
  MessageSquare, 
  Users, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Timer,
  BarChart3,
  RefreshCw,
  Plus,
  Filter,
  Download,
  Settings
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface SupportStats {
  totalTickets: number
  openTickets: number
  inProgressTickets: number
  resolvedToday: number
  avgResponseTime: number
  avgResolutionTime: number
  satisfactionScore: number
  slaBreaches: number
  activeChatSessions: number
  queuedChats: number
}

interface RecentTicket {
  id: string
  ticketNumber: string
  subject: string
  customer: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'open' | 'in_progress' | 'waiting_customer' | 'resolved' | 'closed'
  assignee?: string
  createdAt: Date
  updatedAt: Date
}

export default function CustomerSupportPage() {
  const [stats, setStats] = useState<SupportStats>({
    totalTickets: 0,
    openTickets: 0,
    inProgressTickets: 0,
    resolvedToday: 0,
    avgResponseTime: 0,
    avgResolutionTime: 0,
    satisfactionScore: 0,
    slaBreaches: 0,
    activeChatSessions: 0,
    queuedChats: 0
  })
  const [recentTickets, setRecentTickets] = useState<RecentTicket[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSupportData()
  }, [])

  const loadSupportData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockStats: SupportStats = {
        totalTickets: 1247,
        openTickets: 23,
        inProgressTickets: 15,
        resolvedToday: 8,
        avgResponseTime: 45, // minutes
        avgResolutionTime: 180, // minutes
        satisfactionScore: 4.6,
        slaBreaches: 2,
        activeChatSessions: 7,
        queuedChats: 3
      }

      const mockTickets: RecentTicket[] = [
        {
          id: '1',
          ticketNumber: 'TKT-2025-001',
          subject: 'Unable to complete checkout process',
          customer: '<EMAIL>',
          priority: 'high',
          status: 'open',
          assignee: 'Sarah Chen',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: '2',
          ticketNumber: 'TKT-2025-002',
          subject: 'Keycap compatibility question',
          customer: '<EMAIL>',
          priority: 'normal',
          status: 'in_progress',
          assignee: 'Mike Johnson',
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 15 * 60 * 1000)
        },
        {
          id: '3',
          ticketNumber: 'TKT-2025-003',
          subject: 'Refund request for damaged item',
          customer: '<EMAIL>',
          priority: 'urgent',
          status: 'waiting_customer',
          assignee: 'Emma Davis',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 45 * 60 * 1000)
        }
      ]

      setStats(mockStats)
      setRecentTickets(mockTickets)
    } catch (error) {
      console.error('Error loading support data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'normal': return 'text-blue-400 bg-blue-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-red-400 bg-red-900/20'
      case 'in_progress': return 'text-blue-400 bg-blue-900/20'
      case 'waiting_customer': return 'text-yellow-400 bg-yellow-900/20'
      case 'resolved': return 'text-green-400 bg-green-900/20'
      case 'closed': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Headphones className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Customer Support</h1>
            <p className="text-gray-400">Comprehensive ticket management and customer support tools</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadSupportData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/support/tickets/new"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            New Ticket
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Open Tickets</p>
              <p className="text-2xl font-bold text-white">{stats.openTickets}</p>
              <p className="text-xs text-gray-500 mt-1">
                {stats.inProgressTickets} in progress
              </p>
            </div>
            <Ticket className="text-red-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Response Time</p>
              <p className="text-2xl font-bold text-white">{formatTime(stats.avgResponseTime)}</p>
              <p className="text-xs text-green-400 mt-1">
                ↓ 12% from last week
              </p>
            </div>
            <Timer className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Satisfaction Score</p>
              <p className="text-2xl font-bold text-white">{stats.satisfactionScore}/5</p>
              <p className="text-xs text-green-400 mt-1">
                ↑ 0.2 from last month
              </p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Chats</p>
              <p className="text-2xl font-bold text-white">{stats.activeChatSessions}</p>
              <p className="text-xs text-gray-400 mt-1">
                {stats.queuedChats} in queue
              </p>
            </div>
            <MessageSquare className="text-purple-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* SLA Alerts */}
      {stats.slaBreaches > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-900/20 border border-red-500/30 rounded-lg p-4"
        >
          <div className="flex items-center">
            <AlertTriangle className="text-red-400 mr-3" size={20} />
            <div>
              <h3 className="text-lg font-semibold text-red-400">SLA Breach Alert</h3>
              <p className="text-gray-300">
                {stats.slaBreaches} ticket(s) have exceeded their SLA deadline and require immediate attention.
              </p>
            </div>
            <Link
              href="/admin/support/tickets?filter=sla_breach"
              className="ml-auto bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              View Tickets
            </Link>
          </div>
        </motion.div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link
          href="/admin/support/tickets"
          className="bg-gray-800 hover:bg-gray-700 p-6 rounded-lg transition-colors group"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white group-hover:text-purple-400">
                Ticket Management
              </h3>
              <p className="text-gray-400 text-sm mt-1">
                View and manage all support tickets
              </p>
            </div>
            <Ticket className="text-gray-400 group-hover:text-purple-400" size={24} />
          </div>
        </Link>

        <Link
          href="/admin/support/chat"
          className="bg-gray-800 hover:bg-gray-700 p-6 rounded-lg transition-colors group"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white group-hover:text-purple-400">
                Live Chat
              </h3>
              <p className="text-gray-400 text-sm mt-1">
                Real-time customer chat support
              </p>
            </div>
            <MessageSquare className="text-gray-400 group-hover:text-purple-400" size={24} />
          </div>
        </Link>

        <Link
          href="/admin/support/impersonation"
          className="bg-gray-800 hover:bg-gray-700 p-6 rounded-lg transition-colors group"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white group-hover:text-purple-400">
                User Impersonation
              </h3>
              <p className="text-gray-400 text-sm mt-1">
                Secure user account access
              </p>
            </div>
            <Users className="text-gray-400 group-hover:text-purple-400" size={24} />
          </div>
        </Link>

        <Link
          href="/admin/support/analytics"
          className="bg-gray-800 hover:bg-gray-700 p-6 rounded-lg transition-colors group"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white group-hover:text-purple-400">
                Support Analytics
              </h3>
              <p className="text-gray-400 text-sm mt-1">
                Performance metrics and insights
              </p>
            </div>
            <BarChart3 className="text-gray-400 group-hover:text-purple-400" size={24} />
          </div>
        </Link>
      </div>

      {/* Recent Tickets */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Recent Tickets</h3>
          <div className="flex gap-2">
            <button className="text-gray-400 hover:text-white">
              <Filter size={16} />
            </button>
            <Link href="/admin/support/tickets" className="text-purple-400 hover:text-purple-300 text-sm">
              View All
            </Link>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Ticket</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Subject</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Customer</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Priority</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Status</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Assignee</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Updated</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                    Loading tickets...
                  </td>
                </tr>
              ) : recentTickets.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                    No recent tickets
                  </td>
                </tr>
              ) : (
                recentTickets.map((ticket) => (
                  <tr key={ticket.id} className="hover:bg-gray-750">
                    <td className="px-4 py-3">
                      <Link
                        href={`/admin/support/tickets/${ticket.id}`}
                        className="text-purple-400 hover:text-purple-300 font-mono text-sm"
                      >
                        {ticket.ticketNumber}
                      </Link>
                    </td>
                    <td className="px-4 py-3 text-sm text-white max-w-xs truncate">
                      {ticket.subject}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      {ticket.customer}
                    </td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(ticket.status)}`}>
                        {ticket.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      {ticket.assignee || 'Unassigned'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-400">
                      {new Date(ticket.updatedAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
