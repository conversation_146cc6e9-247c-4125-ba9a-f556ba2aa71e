'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Ticket, 
  Search, 
  Filter, 
  Plus, 
  RefreshCw, 
  Download,
  Eye,
  MessageSquare,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  MoreHorizontal,
  ArrowUpDown,
  Calendar
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../../src/admin/components/common/BackButton'

interface SupportTicket {
  id: string
  ticketNumber: string
  subject: string
  description: string
  customer: {
    id: string
    name: string
    email: string
    tier: string
  }
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'open' | 'in_progress' | 'waiting_customer' | 'resolved' | 'closed'
  category: 'technical' | 'billing' | 'product' | 'shipping' | 'account' | 'other'
  assignee?: {
    id: string
    name: string
    email: string
  }
  tags: string[]
  messageCount: number
  slaDeadline: Date
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
}

export default function TicketManagementPage() {
  const [tickets, setTickets] = useState<SupportTicket[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterPriority, setFilterPriority] = useState<string>('all')
  const [filterAssignee, setFilterAssignee] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('updated_desc')
  const [selectedTickets, setSelectedTickets] = useState<string[]>([])

  useEffect(() => {
    loadTickets()
  }, [])

  const loadTickets = async () => {
    setLoading(true)
    try {
      // Simulate API call - replace with actual API integration
      const mockTickets: SupportTicket[] = [
        {
          id: '1',
          ticketNumber: 'TKT-2025-001',
          subject: 'Unable to complete checkout process',
          description: 'Customer experiencing issues during checkout with payment processing',
          customer: {
            id: 'cust_1',
            name: 'John Doe',
            email: '<EMAIL>',
            tier: 'Premium'
          },
          priority: 'high',
          status: 'open',
          category: 'technical',
          assignee: {
            id: 'admin_1',
            name: 'Sarah Chen',
            email: '<EMAIL>'
          },
          tags: ['checkout', 'payment', 'urgent'],
          messageCount: 3,
          slaDeadline: new Date(Date.now() + 2 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: '2',
          ticketNumber: 'TKT-2025-002',
          subject: 'Keycap compatibility question',
          description: 'Customer asking about Cherry MX compatibility with specific keyset',
          customer: {
            id: 'cust_2',
            name: 'Alice Smith',
            email: '<EMAIL>',
            tier: 'Standard'
          },
          priority: 'normal',
          status: 'in_progress',
          category: 'product',
          assignee: {
            id: 'admin_2',
            name: 'Mike Johnson',
            email: '<EMAIL>'
          },
          tags: ['compatibility', 'product-info'],
          messageCount: 5,
          slaDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 15 * 60 * 1000)
        },
        {
          id: '3',
          ticketNumber: 'TKT-2025-003',
          subject: 'Refund request for damaged item',
          description: 'Customer received damaged keycaps and requesting full refund',
          customer: {
            id: 'cust_3',
            name: 'Bob Wilson',
            email: '<EMAIL>',
            tier: 'VIP'
          },
          priority: 'urgent',
          status: 'waiting_customer',
          category: 'billing',
          assignee: {
            id: 'admin_3',
            name: 'Emma Davis',
            email: '<EMAIL>'
          },
          tags: ['refund', 'damaged', 'vip'],
          messageCount: 7,
          slaDeadline: new Date(Date.now() + 1 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 45 * 60 * 1000)
        }
      ]

      setTickets(mockTickets)
    } catch (error) {
      console.error('Error loading tickets:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredTickets = tickets
    .filter(ticket => {
      const matchesSearch = ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           ticket.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           ticket.customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = filterStatus === 'all' || ticket.status === filterStatus
      const matchesPriority = filterPriority === 'all' || ticket.priority === filterPriority
      const matchesAssignee = filterAssignee === 'all' || 
                             (filterAssignee === 'unassigned' && !ticket.assignee) ||
                             (ticket.assignee && ticket.assignee.id === filterAssignee)
      
      return matchesSearch && matchesStatus && matchesPriority && matchesAssignee
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'created_desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'created_asc':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'updated_desc':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        case 'updated_asc':
          return new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
        case 'priority':
          const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 }
          return priorityOrder[a.priority] - priorityOrder[b.priority]
        case 'sla':
          return new Date(a.slaDeadline).getTime() - new Date(b.slaDeadline).getTime()
        default:
          return 0
      }
    })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'normal': return 'text-blue-400 bg-blue-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-red-400 bg-red-900/20'
      case 'in_progress': return 'text-blue-400 bg-blue-900/20'
      case 'waiting_customer': return 'text-yellow-400 bg-yellow-900/20'
      case 'resolved': return 'text-green-400 bg-green-900/20'
      case 'closed': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const isSLABreached = (deadline: Date) => {
    return new Date() > deadline
  }

  const formatTimeRemaining = (deadline: Date) => {
    const now = new Date()
    const diff = deadline.getTime() - now.getTime()
    
    if (diff <= 0) return 'Overdue'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  const toggleTicketSelection = (ticketId: string) => {
    setSelectedTickets(prev => 
      prev.includes(ticketId) 
        ? prev.filter(id => id !== ticketId)
        : [...prev, ticketId]
    )
  }

  const selectAllTickets = () => {
    setSelectedTickets(
      selectedTickets.length === filteredTickets.length 
        ? [] 
        : filteredTickets.map(ticket => ticket.id)
    )
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Ticket Management</h1>
          <p className="text-gray-400">Manage and track all customer support tickets</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadTickets}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <button className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
            <Download size={20} className="mr-2" />
            Export
          </button>
          <Link
            href="/admin/support/tickets/new"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            New Ticket
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search tickets by subject, customer, or ticket number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              />
            </div>
          </div>
          
          <div className="flex gap-3">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
            >
              <option value="all">All Status</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="waiting_customer">Waiting Customer</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
            
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
            >
              <option value="all">All Priority</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="normal">Normal</option>
              <option value="low">Low</option>
            </select>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
            >
              <option value="updated_desc">Latest Updated</option>
              <option value="updated_asc">Oldest Updated</option>
              <option value="created_desc">Newest First</option>
              <option value="created_asc">Oldest First</option>
              <option value="priority">Priority</option>
              <option value="sla">SLA Deadline</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedTickets.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4"
        >
          <div className="flex items-center justify-between">
            <span className="text-purple-300">
              {selectedTickets.length} ticket(s) selected
            </span>
            <div className="flex gap-2">
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm">
                Assign
              </button>
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm">
                Change Status
              </button>
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm">
                Add Tags
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Tickets Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedTickets.length === filteredTickets.length && filteredTickets.length > 0}
                    onChange={selectAllTickets}
                    className="rounded"
                  />
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Ticket</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Subject</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Customer</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Priority</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Status</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Assignee</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">SLA</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Updated</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={10} className="px-4 py-8 text-center text-gray-400">
                    Loading tickets...
                  </td>
                </tr>
              ) : filteredTickets.length === 0 ? (
                <tr>
                  <td colSpan={10} className="px-4 py-8 text-center text-gray-400">
                    No tickets found matching your criteria
                  </td>
                </tr>
              ) : (
                filteredTickets.map((ticket) => (
                  <tr key={ticket.id} className="hover:bg-gray-750">
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedTickets.includes(ticket.id)}
                        onChange={() => toggleTicketSelection(ticket.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <Link
                        href={`/admin/support/tickets/${ticket.id}`}
                        className="text-purple-400 hover:text-purple-300 font-mono text-sm"
                      >
                        {ticket.ticketNumber}
                      </Link>
                    </td>
                    <td className="px-4 py-3">
                      <div className="max-w-xs">
                        <p className="text-sm font-medium text-white truncate">{ticket.subject}</p>
                        <div className="flex items-center mt-1 space-x-2">
                          <MessageSquare size={12} className="text-gray-400" />
                          <span className="text-xs text-gray-400">{ticket.messageCount} messages</span>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div>
                        <p className="text-sm font-medium text-white">{ticket.customer.name}</p>
                        <p className="text-xs text-gray-400">{ticket.customer.email}</p>
                        <span className="inline-flex px-1 py-0.5 text-xs font-medium rounded bg-blue-900/20 text-blue-400">
                          {ticket.customer.tier}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(ticket.status)}`}>
                        {ticket.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      {ticket.assignee ? (
                        <div className="flex items-center">
                          <User size={14} className="mr-1" />
                          {ticket.assignee.name}
                        </div>
                      ) : (
                        <span className="text-gray-500">Unassigned</span>
                      )}
                    </td>
                    <td className="px-4 py-3">
                      <div className={`text-xs ${isSLABreached(ticket.slaDeadline) ? 'text-red-400' : 'text-gray-300'}`}>
                        {isSLABreached(ticket.slaDeadline) && (
                          <AlertTriangle size={12} className="inline mr-1" />
                        )}
                        {formatTimeRemaining(ticket.slaDeadline)}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-400">
                      {new Date(ticket.updatedAt).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex space-x-2">
                        <Link
                          href={`/admin/support/tickets/${ticket.id}`}
                          className="text-purple-400 hover:text-purple-300"
                        >
                          <Eye size={16} />
                        </Link>
                        <button className="text-gray-400 hover:text-white">
                          <MoreHorizontal size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-400">
          Showing {filteredTickets.length} of {tickets.length} tickets
        </p>
        <div className="flex space-x-2">
          <button className="px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600">
            Previous
          </button>
          <button className="px-3 py-1 bg-purple-600 text-white rounded text-sm">
            1
          </button>
          <button className="px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600">
            Next
          </button>
        </div>
      </div>
    </div>
  )
}
