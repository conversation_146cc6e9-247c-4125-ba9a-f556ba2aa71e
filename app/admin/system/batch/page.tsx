/**
 * Admin Batch Processing Page
 *
 * Main page for managing scheduled tasks and background processing.
 * Handles data cleanup, automated reports, maintenance tasks, and system optimization.
 *
 * Features:
 * - Scheduled task management and monitoring
 * - Background job processing and queue management
 * - Data cleanup and maintenance automation
 * - Automated report generation and distribution
 * - System optimization and performance tasks
 * - Task scheduling with cron-like expressions
 * - Job status tracking and error handling
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { BatchProcessingManager } from '../../../../src/admin/components/batch/BatchProcessingManager';

export const metadata: Metadata = {
  title: 'Batch Processing | Syndicaps Admin',
  description: 'Manage scheduled tasks, background jobs, and automated system processes.',
  robots: 'noindex, nofollow'
};

/**
 * Admin Batch Processing Page Component
 * 
 * Protected admin route that requires system_batch read permission.
 * Renders the comprehensive batch processing management interface.
 */
export default function AdminBatchPage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'system_batch', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <BatchProcessingManager />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
