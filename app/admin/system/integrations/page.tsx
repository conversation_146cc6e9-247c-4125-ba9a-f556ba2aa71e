/**
 * Admin External Services Page
 *
 * Main page for managing external service integrations.
 * Handles email notifications, webhooks, API endpoints, and third-party services.
 *
 * Features:
 * - Email notification service configuration
 * - Webhook management and testing
 * - API endpoint configuration and monitoring
 * - Third-party service integrations
 * - Service health monitoring and status tracking
 * - Integration testing and validation
 * - Configuration backup and restore
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { ExternalServiceManager } from '../../../../src/admin/components/integrations/ExternalServiceManager';

export const metadata: Metadata = {
  title: 'External Services | Syndicaps Admin',
  description: 'Manage external service integrations, webhooks, and API configurations.',
  robots: 'noindex, nofollow'
};

/**
 * Admin External Services Page Component
 * 
 * Protected admin route that requires system_integrations read permission.
 * Renders the comprehensive external service management interface.
 */
export default function AdminIntegrationsPage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'system_integrations', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <ExternalServiceManager />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
