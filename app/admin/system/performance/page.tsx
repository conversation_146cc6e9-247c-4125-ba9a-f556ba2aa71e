/**
 * Admin Performance Monitor Page
 *
 * Main page for system performance monitoring and optimization.
 * Provides real-time metrics, query performance, and system health insights.
 *
 * Features:
 * - Real-time system performance metrics
 * - Database query performance tracking
 * - Cache efficiency monitoring
 * - Memory and CPU usage tracking
 * - Performance alerts and notifications
 * - Historical performance trends
 * - Optimization recommendations
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Metadata } from 'next';
import { ProtectedAdminRoute } from '../../../../src/admin/components/auth/ProtectedAdminRoute';
import { PerformanceMonitor } from '../../../../src/admin/components/optimization/PerformanceMonitor';

export const metadata: Metadata = {
  title: 'Performance Monitor | Syndicaps Admin',
  description: 'Real-time system performance monitoring with optimization insights and alerts.',
  robots: 'noindex, nofollow'
};

/**
 * Admin Performance Monitor Page Component
 * 
 * Protected admin route that requires system_monitoring read permission.
 * Renders the comprehensive performance monitoring dashboard.
 */
export default function AdminPerformancePage() {
  return (
    <ProtectedAdminRoute 
      requiredPermissions={[
        { resource: 'system_monitoring', actions: ['read'] }
      ]}
      fallbackPath="/admin/dashboard"
    >
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <PerformanceMonitor />
        </div>
      </div>
    </ProtectedAdminRoute>
  );
}
