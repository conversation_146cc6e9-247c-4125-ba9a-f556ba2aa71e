'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  Target, 
  TrendingUp, 
  Brain, 
  Filter,
  Plus,
  RefreshCw,
  BarChart3,
  Eye,
  Settings,
  Download,
  MessageSquare,
  Mail,
  Zap,
  Clock,
  DollarSign,
  Activity
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../../src/admin/components/common/BackButton'

interface UserSegment {
  id: string
  name: string
  description: string
  userCount: number
  criteria: SegmentCriteria[]
  logic: 'AND' | 'OR'
  isDynamic: boolean
  color: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  lastCalculated: Date
  analytics: SegmentAnalytics
}

interface SegmentCriteria {
  type: 'demographic' | 'behavioral' | 'transactional' | 'engagement' | 'custom'
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between'
  value: any
  label: string
}

interface SegmentAnalytics {
  totalUsers: number
  activeUsers: number
  averageLifetimeValue: number
  conversionRate: number
  retentionRate: number
  engagementScore: number
  churnRisk: number
  growthRate: number
  topActions: string[]
  revenueContribution: number
}

interface BehavioralInsight {
  id: string
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  confidence: number
  recommendation: string
  affectedSegments: string[]
  metrics: Record<string, number>
}

export default function UserSegmentationPage() {
  const [segments, setSegments] = useState<UserSegment[]>([])
  const [behavioralInsights, setBehavioralInsights] = useState<BehavioralInsight[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null)
  const [showCreateSegment, setShowCreateSegment] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'segments' | 'insights' | 'campaigns'>('overview')

  useEffect(() => {
    loadSegmentationData()
  }, [])

  const loadSegmentationData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockSegments: UserSegment[] = [
        {
          id: 'power_users',
          name: 'Power Users',
          description: 'Highly engaged users with frequent purchases and high lifetime value',
          userCount: 156,
          criteria: [
            { type: 'transactional', field: 'total_spent', operator: 'greater_than', value: 500, label: 'Total Spent > $500' },
            { type: 'behavioral', field: 'login_frequency', operator: 'greater_than', value: 10, label: 'Login Frequency > 10/month' },
            { type: 'engagement', field: 'engagement_score', operator: 'greater_than', value: 80, label: 'Engagement Score > 80' }
          ],
          logic: 'AND',
          isDynamic: true,
          color: '#10B981',
          tags: ['high-value', 'engaged'],
          createdAt: new Date('2025-01-01'),
          updatedAt: new Date(),
          lastCalculated: new Date(),
          analytics: {
            totalUsers: 156,
            activeUsers: 142,
            averageLifetimeValue: 1250,
            conversionRate: 0.85,
            retentionRate: 0.92,
            engagementScore: 87,
            churnRisk: 0.05,
            growthRate: 12.5,
            topActions: ['product_view', 'purchase', 'review_write'],
            revenueContribution: 0.45
          }
        },
        {
          id: 'at_risk',
          name: 'At Risk Users',
          description: 'Users showing signs of decreased engagement and potential churn',
          userCount: 89,
          criteria: [
            { type: 'behavioral', field: 'days_since_login', operator: 'greater_than', value: 30, label: 'Last Login > 30 days ago' },
            { type: 'engagement', field: 'engagement_score', operator: 'less_than', value: 40, label: 'Engagement Score < 40' },
            { type: 'transactional', field: 'days_since_purchase', operator: 'greater_than', value: 60, label: 'Last Purchase > 60 days ago' }
          ],
          logic: 'AND',
          isDynamic: true,
          color: '#F59E0B',
          tags: ['churn-risk', 'retention'],
          createdAt: new Date('2025-01-05'),
          updatedAt: new Date(),
          lastCalculated: new Date(),
          analytics: {
            totalUsers: 89,
            activeUsers: 23,
            averageLifetimeValue: 180,
            conversionRate: 0.12,
            retentionRate: 0.25,
            engagementScore: 32,
            churnRisk: 0.78,
            growthRate: -8.3,
            topActions: ['email_open', 'notification_view'],
            revenueContribution: 0.03
          }
        },
        {
          id: 'new_users',
          name: 'New Users',
          description: 'Recently registered users in their first 30 days',
          userCount: 234,
          criteria: [
            { type: 'demographic', field: 'registration_date', operator: 'greater_than', value: '30_days_ago', label: 'Registered < 30 days ago' }
          ],
          logic: 'AND',
          isDynamic: true,
          color: '#8B5CF6',
          tags: ['onboarding', 'new'],
          createdAt: new Date('2025-01-10'),
          updatedAt: new Date(),
          lastCalculated: new Date(),
          analytics: {
            totalUsers: 234,
            activeUsers: 198,
            averageLifetimeValue: 45,
            conversionRate: 0.28,
            retentionRate: 0.67,
            engagementScore: 65,
            churnRisk: 0.35,
            growthRate: 23.1,
            topActions: ['profile_setup', 'product_browse', 'wishlist_add'],
            revenueContribution: 0.08
          }
        }
      ]

      const mockInsights: BehavioralInsight[] = [
        {
          id: '1',
          title: 'Weekend Purchase Behavior',
          description: 'Users are 45% more likely to make purchases on weekends, particularly Saturday afternoons',
          impact: 'high',
          confidence: 0.87,
          recommendation: 'Schedule promotional campaigns for Friday evenings and Saturday mornings',
          affectedSegments: ['power_users', 'new_users'],
          metrics: {
            weekend_conversion_rate: 0.34,
            weekday_conversion_rate: 0.23,
            saturday_peak_hour: 14
          }
        },
        {
          id: '2',
          title: 'Mobile vs Desktop Engagement',
          description: 'Mobile users have 23% higher engagement but 15% lower conversion rates',
          impact: 'medium',
          confidence: 0.92,
          recommendation: 'Optimize mobile checkout flow and implement mobile-specific promotions',
          affectedSegments: ['new_users'],
          metrics: {
            mobile_engagement_score: 73,
            desktop_engagement_score: 59,
            mobile_conversion_rate: 0.18,
            desktop_conversion_rate: 0.21
          }
        },
        {
          id: '3',
          title: 'Email Engagement Patterns',
          description: 'Users who engage with emails within 2 hours have 3x higher lifetime value',
          impact: 'high',
          confidence: 0.78,
          recommendation: 'Implement real-time email personalization and send-time optimization',
          affectedSegments: ['power_users', 'at_risk'],
          metrics: {
            quick_responder_ltv: 890,
            slow_responder_ltv: 295,
            optimal_send_time: 10
          }
        }
      ]

      setSegments(mockSegments)
      setBehavioralInsights(mockInsights)
    } catch (error) {
      console.error('Error loading segmentation data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSegmentColor = (color: string) => {
    return {
      backgroundColor: `${color}20`,
      borderColor: color,
      color: color
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-400 bg-red-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Target className="w-8 h-8 text-purple-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Advanced User Segmentation</h1>
            <p className="text-gray-400">Behavioral analysis, custom user groups, and targeted communication</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadSegmentationData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateSegment(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Segment
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'segments', label: 'Segments', icon: Target },
            { id: 'insights', label: 'Behavioral Insights', icon: Brain },
            { id: 'campaigns', label: 'Targeted Campaigns', icon: MessageSquare }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Segments</p>
                  <p className="text-2xl font-bold text-white">{segments.length}</p>
                  <p className="text-xs text-green-400 mt-1">+2 this month</p>
                </div>
                <Target className="text-purple-400" size={24} />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Segmented Users</p>
                  <p className="text-2xl font-bold text-white">
                    {segments.reduce((sum, segment) => sum + segment.userCount, 0)}
                  </p>
                  <p className="text-xs text-blue-400 mt-1">87% of total users</p>
                </div>
                <Users className="text-blue-400" size={24} />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Avg. Engagement</p>
                  <p className="text-2xl font-bold text-white">
                    {Math.round(segments.reduce((sum, segment) => sum + segment.analytics.engagementScore, 0) / segments.length)}
                  </p>
                  <p className="text-xs text-green-400 mt-1">+5.2% this week</p>
                </div>
                <Activity className="text-green-400" size={24} />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Revenue Impact</p>
                  <p className="text-2xl font-bold text-white">
                    {formatCurrency(segments.reduce((sum, segment) => sum + (segment.analytics.averageLifetimeValue * segment.userCount), 0))}
                  </p>
                  <p className="text-xs text-green-400 mt-1">+12.8% this month</p>
                </div>
                <DollarSign className="text-green-400" size={24} />
              </div>
            </motion.div>
          </div>

          {/* Segment Performance Chart */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Segment Performance Overview</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-3">User Distribution</h4>
                <div className="space-y-3">
                  {segments.map((segment) => (
                    <div key={segment.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-3"
                          style={{ backgroundColor: segment.color }}
                        ></div>
                        <span className="text-white text-sm">{segment.name}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-white font-medium">{segment.userCount}</span>
                        <span className="text-gray-400 text-xs ml-2">
                          ({((segment.userCount / segments.reduce((sum, s) => sum + s.userCount, 0)) * 100).toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-3">Revenue Contribution</h4>
                <div className="space-y-3">
                  {segments.map((segment) => (
                    <div key={segment.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-3"
                          style={{ backgroundColor: segment.color }}
                        ></div>
                        <span className="text-white text-sm">{segment.name}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-white font-medium">
                          {formatPercentage(segment.analytics.revenueContribution)}
                        </span>
                        <span className="text-gray-400 text-xs ml-2">
                          {formatCurrency(segment.analytics.averageLifetimeValue)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'segments' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {segments.map((segment) => (
              <motion.div
                key={segment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-6 border-l-4"
                style={{ borderLeftColor: segment.color }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white">{segment.name}</h3>
                    <p className="text-gray-400 text-sm mt-1">{segment.description}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-gray-400 hover:text-white">
                      <Eye size={16} />
                    </button>
                    <button className="text-gray-400 hover:text-white">
                      <Settings size={16} />
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Users</span>
                    <span className="text-white font-medium">{segment.userCount}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Engagement</span>
                    <span className="text-white font-medium">{segment.analytics.engagementScore}/100</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Avg. LTV</span>
                    <span className="text-white font-medium">{formatCurrency(segment.analytics.averageLifetimeValue)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Conversion</span>
                    <span className="text-white font-medium">{formatPercentage(segment.analytics.conversionRate)}</span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-700">
                  <div className="flex flex-wrap gap-1 mb-3">
                    {segment.tags.map((tag) => (
                      <span key={tag} className="inline-flex px-2 py-1 text-xs font-medium rounded bg-gray-700 text-gray-300">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <button className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-3 rounded text-sm transition-colors">
                      <MessageSquare size={14} className="inline mr-1" />
                      Campaign
                    </button>
                    <button className="flex-1 bg-gray-600 hover:bg-gray-500 text-white py-2 px-3 rounded text-sm transition-colors">
                      <BarChart3 size={14} className="inline mr-1" />
                      Analyze
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'insights' && (
        <div className="space-y-6">
          <div className="space-y-4">
            {behavioralInsights.map((insight) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{insight.title}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getImpactColor(insight.impact)}`}>
                        {insight.impact} impact
                      </span>
                      <span className="text-xs text-gray-400">
                        {(insight.confidence * 100).toFixed(0)}% confidence
                      </span>
                    </div>
                    <p className="text-gray-300 mb-3">{insight.description}</p>
                    <div className="bg-gray-700 p-3 rounded border-l-4 border-purple-500">
                      <p className="text-purple-300 text-sm font-medium">Recommendation:</p>
                      <p className="text-gray-300 text-sm">{insight.recommendation}</p>
                    </div>
                  </div>
                  <Brain className="text-purple-400 ml-4" size={24} />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  {Object.entries(insight.metrics).map(([key, value]) => (
                    <div key={key} className="bg-gray-700 p-3 rounded">
                      <p className="text-gray-400 text-xs uppercase tracking-wider">
                        {key.replace(/_/g, ' ')}
                      </p>
                      <p className="text-white font-medium">
                        {typeof value === 'number' && value < 1 ? formatPercentage(value) : value}
                      </p>
                    </div>
                  ))}
                </div>

                <div className="mt-4 flex items-center justify-between">
                  <div className="flex space-x-2">
                    {insight.affectedSegments.map((segmentId) => {
                      const segment = segments.find(s => s.id === segmentId)
                      return segment ? (
                        <span key={segmentId} className="inline-flex px-2 py-1 text-xs font-medium rounded bg-gray-700 text-gray-300">
                          {segment.name}
                        </span>
                      ) : null
                    })}
                  </div>
                  <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm transition-colors">
                    Apply Recommendation
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'campaigns' && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Targeted Campaigns</h3>
          <div className="text-center py-8">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-white mb-2">Campaign Management</h4>
            <p className="text-gray-400 mb-4">
              Create and manage targeted campaigns for specific user segments.
            </p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
              Create Campaign
            </button>
          </div>
        </div>
      )}

      {/* Create Segment Modal */}
      {showCreateSegment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Create User Segment</h3>
            <p className="text-gray-400 mb-4">
              Advanced segment builder will allow you to create custom user segments with 
              behavioral criteria, demographic filters, and engagement metrics.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowCreateSegment(false)}
                className="flex-1 py-2 px-4 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
