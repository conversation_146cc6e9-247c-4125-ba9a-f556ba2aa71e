'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Workflow,
  Play,
  Pause,
  Square,
  Settings,
  Plus,
  RefreshCw,
  Eye,
  Edit,
  Copy,
  Trash2,
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  Mail,
  ShoppingCart,
  FileText,
  Zap,
  GitBranch,
  Filter,
  Search,
  BarChart3,
  Target,
  Calendar
} from 'lucide-react'
import Link from 'next/link'
import BackButton from '../../../src/admin/components/common/BackButton'

interface WorkflowRule {
  id: string
  name: string
  description: string
  category: 'user_management' | 'order_processing' | 'inventory' | 'marketing' | 'support' | 'custom'
  trigger: {
    type: 'event' | 'schedule' | 'condition' | 'manual'
    event?: string
    schedule?: {
      frequency: 'once' | 'daily' | 'weekly' | 'monthly'
      time?: string
      days?: string[]
    }
    conditions?: WorkflowCondition[]
  }
  actions: WorkflowAction[]
  approvals?: ApprovalStep[]
  isActive: boolean
  priority: 'low' | 'medium' | 'high' | 'critical'
  metrics: {
    triggered: number
    completed: number
    failed: number
    avgExecutionTime: number
    successRate: number
  }
  createdBy: string
  createdAt: Date
  lastExecuted?: Date
  lastModified: Date
}

interface WorkflowCondition {
  id: string
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

interface WorkflowAction {
  id: string
  type: 'email' | 'notification' | 'update_field' | 'create_record' | 'api_call' | 'wait' | 'condition'
  config: Record<string, any>
  delay?: {
    amount: number
    unit: 'minutes' | 'hours' | 'days'
  }
  onSuccess?: string // next action id
  onFailure?: string // next action id
}

interface ApprovalStep {
  id: string
  name: string
  approvers: string[]
  requiredApprovals: number
  timeoutHours: number
  escalationTo?: string[]
  autoApprove?: {
    conditions: WorkflowCondition[]
  }
}

interface WorkflowExecution {
  id: string
  workflowId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'waiting_approval'
  startedAt: Date
  completedAt?: Date
  currentStep: string
  context: Record<string, any>
  logs: ExecutionLog[]
  approvals?: {
    stepId: string
    status: 'pending' | 'approved' | 'rejected'
    approvedBy?: string
    approvedAt?: Date
    comments?: string
  }[]
}

interface ExecutionLog {
  id: string
  timestamp: Date
  level: 'info' | 'warning' | 'error'
  message: string
  details?: Record<string, any>
}

export default function WorkflowAutomationPage() {
  const [workflows, setWorkflows] = useState<WorkflowRule[]>([])
  const [executions, setExecutions] = useState<WorkflowExecution[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'workflows' | 'executions' | 'approvals' | 'analytics'>('workflows')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    loadWorkflowData()
  }, [])

  const loadWorkflowData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API integration
      const mockWorkflows: WorkflowRule[] = [
        {
          id: 'wf_001',
          name: 'New User Onboarding',
          description: 'Automated onboarding sequence for new user registrations',
          category: 'user_management',
          trigger: {
            type: 'event',
            event: 'user_registered'
          },
          actions: [
            {
              id: 'action_1',
              type: 'email',
              config: {
                template: 'welcome_email',
                to: '{{user.email}}',
                subject: 'Welcome to Syndicaps!'
              }
            },
            {
              id: 'action_2',
              type: 'wait',
              config: {},
              delay: { amount: 24, unit: 'hours' }
            },
            {
              id: 'action_3',
              type: 'email',
              config: {
                template: 'onboarding_tips',
                to: '{{user.email}}',
                subject: 'Getting started with your keycap journey'
              }
            },
            {
              id: 'action_4',
              type: 'update_field',
              config: {
                entity: 'user',
                field: 'onboarding_completed',
                value: true
              }
            }
          ],
          isActive: true,
          priority: 'medium',
          metrics: {
            triggered: 234,
            completed: 221,
            failed: 13,
            avgExecutionTime: 1440, // minutes
            successRate: 0.94
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-01'),
          lastExecuted: new Date(Date.now() - 2 * 60 * 60 * 1000),
          lastModified: new Date()
        },
        {
          id: 'wf_002',
          name: 'High-Value Order Processing',
          description: 'Special handling for orders over $500 with approval workflow',
          category: 'order_processing',
          trigger: {
            type: 'event',
            event: 'order_created',
            conditions: [
              {
                id: 'cond_1',
                field: 'order.total',
                operator: 'greater_than',
                value: 500
              }
            ]
          },
          actions: [
            {
              id: 'action_1',
              type: 'notification',
              config: {
                type: 'admin_alert',
                message: 'High-value order requires review: {{order.id}}',
                recipients: ['<EMAIL>', '<EMAIL>']
              }
            },
            {
              id: 'action_2',
              type: 'update_field',
              config: {
                entity: 'order',
                field: 'status',
                value: 'pending_review'
              }
            }
          ],
          approvals: [
            {
              id: 'approval_1',
              name: 'Sales Manager Approval',
              approvers: ['<EMAIL>'],
              requiredApprovals: 1,
              timeoutHours: 24,
              escalationTo: ['<EMAIL>']
            }
          ],
          isActive: true,
          priority: 'high',
          metrics: {
            triggered: 45,
            completed: 42,
            failed: 3,
            avgExecutionTime: 720, // minutes
            successRate: 0.93
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-05'),
          lastExecuted: new Date(Date.now() - 6 * 60 * 60 * 1000),
          lastModified: new Date()
        },
        {
          id: 'wf_003',
          name: 'Low Stock Alert System',
          description: 'Automated alerts and reordering for low inventory items',
          category: 'inventory',
          trigger: {
            type: 'schedule',
            schedule: {
              frequency: 'daily',
              time: '09:00'
            }
          },
          actions: [
            {
              id: 'action_1',
              type: 'condition',
              config: {
                conditions: [
                  {
                    id: 'cond_1',
                    field: 'inventory.quantity',
                    operator: 'less_than',
                    value: '{{inventory.reorder_point}}'
                  }
                ]
              }
            },
            {
              id: 'action_2',
              type: 'email',
              config: {
                template: 'low_stock_alert',
                to: '<EMAIL>',
                subject: 'Low Stock Alert: {{product.name}}'
              }
            },
            {
              id: 'action_3',
              type: 'api_call',
              config: {
                url: '/api/inventory/auto-reorder',
                method: 'POST',
                data: {
                  productId: '{{product.id}}',
                  quantity: '{{inventory.reorder_quantity}}'
                }
              }
            }
          ],
          isActive: true,
          priority: 'medium',
          metrics: {
            triggered: 15,
            completed: 14,
            failed: 1,
            avgExecutionTime: 5, // minutes
            successRate: 0.93
          },
          createdBy: '<EMAIL>',
          createdAt: new Date('2025-01-08'),
          lastExecuted: new Date(Date.now() - 12 * 60 * 60 * 1000),
          lastModified: new Date()
        }
      ]

      const mockExecutions: WorkflowExecution[] = [
        {
          id: 'exec_001',
          workflowId: 'wf_001',
          status: 'completed',
          startedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          currentStep: 'action_4',
          context: {
            user: {
              id: 'user_123',
              email: '<EMAIL>',
              name: 'John Doe'
            }
          },
          logs: [
            {
              id: 'log_1',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              level: 'info',
              message: 'Workflow started for user registration'
            },
            {
              id: 'log_2',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000),
              level: 'info',
              message: 'Welcome email sent successfully'
            },
            {
              id: 'log_3',
              timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
              level: 'info',
              message: 'Onboarding completed, user field updated'
            }
          ]
        },
        {
          id: 'exec_002',
          workflowId: 'wf_002',
          status: 'waiting_approval',
          startedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
          currentStep: 'approval_1',
          context: {
            order: {
              id: 'order_456',
              total: 750,
              customer: '<EMAIL>'
            }
          },
          logs: [
            {
              id: 'log_1',
              timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
              level: 'info',
              message: 'High-value order detected, starting approval workflow'
            },
            {
              id: 'log_2',
              timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000 + 60000),
              level: 'info',
              message: 'Admin notification sent, order status updated'
            }
          ],
          approvals: [
            {
              stepId: 'approval_1',
              status: 'pending'
            }
          ]
        }
      ]

      setWorkflows(mockWorkflows)
      setExecutions(mockExecutions)
    } catch (error) {
      console.error('Error loading workflow data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = filterCategory === 'all' || workflow.category === filterCategory
    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'active' && workflow.isActive) ||
                         (filterStatus === 'inactive' && !workflow.isActive)
    return matchesSearch && matchesCategory && matchesStatus
  })

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'user_management': return 'text-blue-400 bg-blue-900/20'
      case 'order_processing': return 'text-green-400 bg-green-900/20'
      case 'inventory': return 'text-purple-400 bg-purple-900/20'
      case 'marketing': return 'text-pink-400 bg-pink-900/20'
      case 'support': return 'text-yellow-400 bg-yellow-900/20'
      case 'custom': return 'text-gray-400 bg-gray-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-green-400 bg-green-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-900/20'
      case 'running': return 'text-blue-400 bg-blue-900/20'
      case 'pending': return 'text-yellow-400 bg-yellow-900/20'
      case 'failed': return 'text-red-400 bg-red-900/20'
      case 'cancelled': return 'text-gray-400 bg-gray-900/20'
      case 'waiting_approval': return 'text-purple-400 bg-purple-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const toggleWorkflow = async (workflowId: string) => {
    setWorkflows(prev => prev.map(workflow =>
      workflow.id === workflowId
        ? { ...workflow, isActive: !workflow.isActive }
        : workflow
    ))
  }

  const formatExecutionTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <BackButton />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Workflow className="w-8 h-8 text-purple-400" />
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Workflow Automation</h1>
            <p className="text-gray-400">Rule-based automation, trigger systems, and approval workflows</p>
          </div>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadWorkflowData}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw size={20} className="mr-2" />
            Refresh
          </button>
          <Link
            href="/admin/workflows/create"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Create Workflow
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Workflows</p>
              <p className="text-2xl font-bold text-white">
                {workflows.filter(w => w.isActive).length}
              </p>
              <p className="text-xs text-green-400 mt-1">+3 this month</p>
            </div>
            <Workflow className="text-purple-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Executions</p>
              <p className="text-2xl font-bold text-white">
                {workflows.reduce((sum, w) => sum + w.metrics.triggered, 0).toLocaleString()}
              </p>
              <p className="text-xs text-blue-400 mt-1">This month</p>
            </div>
            <Play className="text-blue-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-2xl font-bold text-white">
                {workflows.length > 0
                  ? (workflows.reduce((sum, w) => sum + w.metrics.successRate, 0) / workflows.length * 100).toFixed(1)
                  : 0}%
              </p>
              <p className="text-xs text-green-400 mt-1">+2.1% vs last month</p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Pending Approvals</p>
              <p className="text-2xl font-bold text-white">
                {executions.filter(e => e.status === 'waiting_approval').length}
              </p>
              <p className="text-xs text-yellow-400 mt-1">Require attention</p>
            </div>
            <Clock className="text-yellow-400" size={24} />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'workflows', label: 'Workflows', count: workflows.length },
            { id: 'executions', label: 'Executions', count: executions.length },
            { id: 'approvals', label: 'Approvals', count: executions.filter(e => e.status === 'waiting_approval').length },
            { id: 'analytics', label: 'Analytics', count: null }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className="ml-2 bg-gray-600 text-xs px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Search and Filters */}
      {activeTab === 'workflows' && (
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex gap-3">
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Categories</option>
                <option value="user_management">User Management</option>
                <option value="order_processing">Order Processing</option>
                <option value="inventory">Inventory</option>
                <option value="marketing">Marketing</option>
                <option value="support">Support</option>
                <option value="custom">Custom</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'workflows' && (
        <div className="space-y-4">
          {filteredWorkflows.length === 0 ? (
            <div className="bg-gray-800 p-8 rounded-lg text-center">
              <Workflow className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Workflows Found</h3>
              <p className="text-gray-400">Create your first automation workflow or adjust your search criteria.</p>
            </div>
          ) : (
            filteredWorkflows.map((workflow) => (
              <motion.div
                key={workflow.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 p-6 rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{workflow.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(workflow.category)}`}>
                        {workflow.category.replace('_', ' ')}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(workflow.priority)}`}>
                        {workflow.priority}
                      </span>
                      {workflow.isActive ? (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-green-400 bg-green-900/20">
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-400 bg-gray-900/20">
                          Inactive
                        </span>
                      )}
                    </div>

                    <p className="text-gray-400 text-sm mb-3">{workflow.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-400">Triggered:</span>
                        <span className="text-white ml-1">{workflow.metrics.triggered.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Success Rate:</span>
                        <span className="text-white ml-1">{(workflow.metrics.successRate * 100).toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Avg. Time:</span>
                        <span className="text-white ml-1">{formatExecutionTime(workflow.metrics.avgExecutionTime)}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Actions:</span>
                        <span className="text-white ml-1">{workflow.actions.length}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>By {workflow.createdBy}</span>
                      <span>Created: {workflow.createdAt.toLocaleDateString()}</span>
                      {workflow.lastExecuted && (
                        <span>Last run: {workflow.lastExecuted.toLocaleString()}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View">
                      <Eye size={16} />
                    </button>
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded" title="Edit">
                      <Edit size={16} />
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded" title="Duplicate">
                      <Copy size={16} />
                    </button>
                    <button
                      onClick={() => toggleWorkflow(workflow.id)}
                      className={`p-2 rounded ${
                        workflow.isActive
                          ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                      title={workflow.isActive ? 'Deactivate' : 'Activate'}
                    >
                      {workflow.isActive ? <Pause size={16} /> : <Play size={16} />}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      )}

      {activeTab === 'executions' && (
        <div className="space-y-4">
          {executions.map((execution) => (
            <motion.div
              key={execution.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 p-6 rounded-lg"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">
                      {workflows.find(w => w.id === execution.workflowId)?.name || 'Unknown Workflow'}
                    </h3>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(execution.status)}`}>
                      {execution.status.replace('_', ' ')}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-gray-400">Started:</span>
                      <span className="text-white ml-1">{execution.startedAt.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Current Step:</span>
                      <span className="text-white ml-1">{execution.currentStep}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Duration:</span>
                      <span className="text-white ml-1">
                        {execution.completedAt
                          ? formatExecutionTime(Math.floor((execution.completedAt.getTime() - execution.startedAt.getTime()) / 60000))
                          : formatExecutionTime(Math.floor((Date.now() - execution.startedAt.getTime()) / 60000))
                        }
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Logs:</span>
                      <span className="text-white ml-1">{execution.logs.length}</span>
                    </div>
                  </div>

                  {execution.status === 'waiting_approval' && execution.approvals && (
                    <div className="mt-3 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded">
                      <div className="flex items-center mb-2">
                        <Clock className="text-yellow-400 mr-2" size={16} />
                        <span className="text-yellow-400 font-medium">Pending Approval</span>
                      </div>
                      {execution.approvals.map((approval) => (
                        <div key={approval.stepId} className="text-sm text-gray-300">
                          Step: {approval.stepId} - Status: {approval.status}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex space-x-2 ml-4">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded" title="View Details">
                    <Eye size={16} />
                  </button>
                  {execution.status === 'running' && (
                    <button className="bg-red-600 hover:bg-red-700 text-white p-2 rounded" title="Cancel">
                      <Square size={16} />
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {(activeTab === 'approvals' || activeTab === 'analytics') && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            {activeTab === 'approvals' && 'Approval Management'}
            {activeTab === 'analytics' && 'Workflow Analytics'}
          </h3>
          <div className="text-center py-8">
            {activeTab === 'approvals' && <CheckCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            {activeTab === 'analytics' && <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />}
            <h4 className="text-lg font-medium text-white mb-2">
              {activeTab === 'approvals' && 'Workflow Approval System'}
              {activeTab === 'analytics' && 'Workflow Performance Analytics'}
            </h4>
            <p className="text-gray-400 mb-4">
              {activeTab === 'approvals' && 'Manage pending approvals, approval chains, and escalation workflows.'}
              {activeTab === 'analytics' && 'Comprehensive analytics for workflow performance, execution times, and success rates.'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}