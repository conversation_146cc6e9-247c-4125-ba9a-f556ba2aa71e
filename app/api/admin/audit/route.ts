/**
 * Admin Audit Logging API
 * 
 * Provides comprehensive audit logging functionality for admin actions
 * with IP tracking, session context, and real-time monitoring.
 * 
 * Features:
 * - Create audit log entries
 * - Fetch audit logs with filtering
 * - Real-time audit statistics
 * - Security event tracking
 * - IP address and session tracking
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { createRateLimitMiddleware, addRateLimitHeaders, checkRateLimit, RATE_LIMIT_CONFIGS } from '../../../../src/admin/lib/rateLimiter'
import { validateAdminCSRF } from '../../../../src/admin/lib/csrfProtection'

export interface AuditLogEntry {
  id: string
  timestamp: Date
  adminId: string
  adminName: string
  adminEmail: string
  action: string
  category: 'points' | 'achievements' | 'tiers' | 'users' | 'system' | 'security'
  severity: 'low' | 'medium' | 'high' | 'critical'
  targetType: 'user' | 'achievement' | 'tier' | 'point_rule' | 'system_setting'
  targetId?: string
  targetName?: string
  changes?: {
    field: string
    oldValue: any
    newValue: any
  }[]
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  sessionId?: string
}

/**
 * Get client IP address from request
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const clientIP = request.headers.get('x-client-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || clientIP || request.ip || 'unknown'
}

/**
 * Get session information from request
 */
function getSessionInfo(request: NextRequest): {
  sessionId?: string
  adminId?: string
  adminToken?: string
} {
  const sessionTimestamp = request.cookies.get('session-timestamp')?.value
  const adminToken = request.cookies.get('admin-token')?.value
  const userId = request.cookies.get('user-id')?.value
  
  return {
    sessionId: sessionTimestamp ? `session_${sessionTimestamp}` : undefined,
    adminId: userId,
    adminToken
  }
}

/**
 * Validate admin authentication for audit operations
 */
function validateAdminAuth(request: NextRequest): boolean {
  const userRole = request.cookies.get('user-role')?.value
  const adminAccess = request.cookies.get('admin-access')?.value
  const authToken = request.cookies.get('firebase-auth-token')?.value
  
  const hasAdminRole = userRole === 'admin' || userRole === 'superadmin'
  const hasAdminAccess = adminAccess === 'true'
  const hasAuthToken = Boolean(authToken)
  
  return hasAdminRole && hasAdminAccess && hasAuthToken
}

/**
 * POST - Create audit log entry
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate CSRF token
    const csrfValidation = validateAdminCSRF(request)
    if (!csrfValidation.valid) {
      return NextResponse.json(
        { error: 'CSRF validation failed', message: csrfValidation.reason },
        { status: 403 }
      )
    }

    // Validate admin authentication
    if (!validateAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      action,
      category,
      severity = 'low',
      targetType,
      targetId,
      targetName,
      changes,
      metadata = {}
    } = body

    // Validate required fields
    if (!action || !category || !targetType) {
      return NextResponse.json(
        { error: 'Missing required fields: action, category, targetType' },
        { status: 400 }
      )
    }

    // Get request context
    const ipAddress = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const sessionInfo = getSessionInfo(request)
    
    // Get admin info from cookies
    const adminId = request.cookies.get('user-id')?.value || 'unknown'
    const adminEmail = request.cookies.get('admin-email')?.value || '<EMAIL>'
    const adminName = request.cookies.get('admin-name')?.value || 'Admin User'

    // Create audit log entry
    const auditEntry: AuditLogEntry = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      adminId,
      adminName,
      adminEmail,
      action,
      category,
      severity,
      targetType,
      targetId,
      targetName,
      changes,
      metadata: {
        ...metadata,
        requestUrl: request.url,
        referer: request.headers.get('referer') || undefined
      },
      ipAddress,
      userAgent,
      sessionId: sessionInfo.sessionId
    }

    // TODO: Store in database (Firestore)
    // For now, log to console and return success
    console.log('🔍 Audit Log Entry Created:', {
      id: auditEntry.id,
      action: auditEntry.action,
      category: auditEntry.category,
      severity: auditEntry.severity,
      adminId: auditEntry.adminId,
      ipAddress: auditEntry.ipAddress,
      timestamp: auditEntry.timestamp
    })

    // TODO: Implement real-time notifications for critical events
    if (severity === 'critical') {
      console.log('🚨 CRITICAL AUDIT EVENT:', auditEntry)
    }

    return NextResponse.json({
      success: true,
      auditId: auditEntry.id,
      timestamp: auditEntry.timestamp
    })

  } catch (error) {
    console.error('❌ Error creating audit log entry:', error)
    return NextResponse.json(
      { error: 'Failed to create audit log entry' },
      { status: 500 }
    )
  }
}

/**
 * GET - Fetch audit logs with filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate admin authentication
    if (!validateAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const severity = searchParams.get('severity')
    const adminId = searchParams.get('adminId')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // TODO: Implement actual database query
    // For now, return mock data
    const mockLogs: AuditLogEntry[] = [
      {
        id: 'audit_1',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        adminId: 'admin1',
        adminName: 'John Admin',
        adminEmail: '<EMAIL>',
        action: 'create_achievement',
        category: 'achievements',
        severity: 'medium',
        targetType: 'achievement',
        targetId: 'ach_123',
        targetName: 'New Achievement',
        changes: [
          { field: 'name', oldValue: null, newValue: 'New Achievement' },
          { field: 'points', oldValue: null, newValue: 100 }
        ],
        metadata: { reason: 'User request' },
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
        sessionId: 'session_123'
      }
    ]

    // Apply filters
    let filteredLogs = mockLogs
    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category)
    }
    if (severity) {
      filteredLogs = filteredLogs.filter(log => log.severity === severity)
    }
    if (adminId) {
      filteredLogs = filteredLogs.filter(log => log.adminId === adminId)
    }

    // Apply pagination
    const paginatedLogs = filteredLogs.slice(offset, offset + limit)

    return NextResponse.json({
      logs: paginatedLogs,
      total: filteredLogs.length,
      limit,
      offset,
      hasMore: offset + limit < filteredLogs.length
    })

  } catch (error) {
    console.error('❌ Error fetching audit logs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    )
  }
}
