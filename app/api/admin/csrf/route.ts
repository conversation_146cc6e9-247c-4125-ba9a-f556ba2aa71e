/**
 * Admin CSRF Token Management API
 * 
 * Provides CSRF token generation and refresh functionality
 * for admin dashboard security.
 * 
 * Features:
 * - CSRF token generation
 * - Token refresh
 * - Token validation
 * - Secure cookie management
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { createCSRFRefreshResponse, generateAdminCSRFResponse } from '../../../../src/admin/lib/csrfProtection'
import { createRateLimitMiddleware } from '../../../../src/admin/lib/rateLimiter'

/**
 * Validate admin authentication for CSRF operations
 */
function validateAdminAuth(request: NextRequest): boolean {
  const userRole = request.cookies.get('user-role')?.value
  const adminAccess = request.cookies.get('admin-access')?.value
  const authToken = request.cookies.get('firebase-auth-token')?.value
  
  const hasAdminRole = userRole === 'admin' || userRole === 'superadmin'
  const hasAdminAccess = adminAccess === 'true'
  const hasAuthToken = Boolean(authToken)
  
  return hasAdminRole && hasAdminAccess && hasAuthToken
}

/**
 * GET - Get new CSRF token
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate admin authentication
    if (!validateAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    // Generate new CSRF token
    const { token, cookieValue, cookieOptions } = generateAdminCSRFResponse()

    const response = NextResponse.json({
      success: true,
      token,
      expiresAt: new Date(Date.now() + cookieOptions.maxAge * 1000),
      message: 'CSRF token generated successfully'
    })

    // Set CSRF cookie
    response.cookies.set('admin-csrf-token', cookieValue, cookieOptions)

    // Add CSRF token to response header
    response.headers.set('X-CSRF-Token', token)

    console.log('🛡️ CSRF token generated for admin session')

    return response

  } catch (error) {
    console.error('❌ Error generating CSRF token:', error)
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    )
  }
}

/**
 * POST - Refresh CSRF token
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate admin authentication
    if (!validateAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    // Use the helper function to create refresh response
    const response = createCSRFRefreshResponse()

    console.log('🔄 CSRF token refreshed for admin session')

    return response

  } catch (error) {
    console.error('❌ Error refreshing CSRF token:', error)
    return NextResponse.json(
      { error: 'Failed to refresh CSRF token' },
      { status: 500 }
    )
  }
}

/**
 * DELETE - Clear CSRF token (logout)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate admin authentication
    if (!validateAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const response = NextResponse.json({
      success: true,
      message: 'CSRF token cleared successfully'
    })

    // Clear CSRF cookie
    response.cookies.set('admin-csrf-token', '', {
      httpOnly: true,
      secure: true,
      sameSite: 'strict',
      maxAge: 0,
      path: '/admin'
    })

    console.log('🧹 CSRF token cleared for admin session')

    return response

  } catch (error) {
    console.error('❌ Error clearing CSRF token:', error)
    return NextResponse.json(
      { error: 'Failed to clear CSRF token' },
      { status: 500 }
    )
  }
}
