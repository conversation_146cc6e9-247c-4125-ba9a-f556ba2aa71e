/**
 * Admin Diagnostics API Route
 * 
 * API endpoint for testing JSON responses and diagnosing
 * potential issues with API communication.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const test = searchParams.get('test') || 'basic';

    switch (test) {
      case 'basic':
        return NextResponse.json({
          success: true,
          message: 'API is working correctly',
          timestamp: new Date().toISOString(),
          test: 'basic'
        });

      case 'auth':
        // Test authentication headers
        const authHeader = request.headers.get('authorization');
        const cookies = request.headers.get('cookie');
        
        return NextResponse.json({
          success: true,
          message: 'Authentication test',
          hasAuthHeader: !!authHeader,
          hasCookies: !!cookies,
          timestamp: new Date().toISOString(),
          test: 'auth'
        });

      case 'error':
        // Test error handling
        throw new Error('Test error for diagnostics');

      case 'slow':
        // Test slow response
        await new Promise(resolve => setTimeout(resolve, 2000));
        return NextResponse.json({
          success: true,
          message: 'Slow response test completed',
          timestamp: new Date().toISOString(),
          test: 'slow'
        });

      case 'large':
        // Test large response
        const largeData = Array.from({ length: 1000 }, (_, i) => ({
          id: i,
          name: `Item ${i}`,
          description: `This is a test item with ID ${i}`,
          timestamp: new Date().toISOString()
        }));

        return NextResponse.json({
          success: true,
          message: 'Large response test',
          data: largeData,
          count: largeData.length,
          timestamp: new Date().toISOString(),
          test: 'large'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown test type',
          availableTests: ['basic', 'auth', 'error', 'slow', 'large'],
          timestamp: new Date().toISOString()
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Diagnostics API error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error',
        type: 'DiagnosticsError',
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    return NextResponse.json({
      success: true,
      message: 'POST request processed successfully',
      receivedData: body,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Diagnostics POST error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error',
        type: 'DiagnosticsPostError',
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}
