/**
 * Admin Inventory Management API
 * 
 * Provides comprehensive inventory management functionality for admin
 * dashboard including stock tracking, alerts, and automated reordering.
 * 
 * Features:
 * - Inventory item management
 * - Stock level monitoring
 * - Alert management
 * - Automated reordering
 * - Inventory reporting
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { createRateLimitMiddleware } from '../../../../src/admin/lib/rateLimiter'
import { validateAdminCSRF } from '../../../../src/admin/lib/csrfProtection'
import { addAdminSecurityHeaders } from '../../../../src/admin/lib/securityHeaders'
import { InventoryManager } from '../../../../src/lib/ecommerce/inventoryManagement'

/**
 * Validate admin authentication
 */
function validateAdminAuth(request: NextRequest): {
  isValid: boolean
  adminId?: string
  adminRole?: string
} {
  const userRole = request.cookies.get('user-role')?.value
  const adminAccess = request.cookies.get('admin-access')?.value
  const authToken = request.cookies.get('firebase-auth-token')?.value
  const adminId = request.cookies.get('user-id')?.value

  const hasAdminRole = userRole === 'admin' || userRole === 'superadmin'
  const hasAdminAccess = adminAccess === 'true'
  const hasAuthToken = Boolean(authToken)

  return {
    isValid: hasAdminRole && hasAdminAccess && hasAuthToken,
    adminId,
    adminRole: userRole
  }
}

/**
 * GET - Get inventory data
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate admin authentication
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const inventoryManager = new InventoryManager()

    switch (action) {
      case 'items': {
        // Get all inventory items
        const items = await inventoryManager.getAllInventoryItems()
        
        const response = NextResponse.json({
          success: true,
          items,
          total: items.length,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'alerts': {
        // Get inventory alerts
        const alerts = await inventoryManager.getInventoryAlerts()
        
        const response = NextResponse.json({
          success: true,
          alerts,
          total: alerts.length,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'stats': {
        // Get inventory statistics
        const items = await inventoryManager.getAllInventoryItems()
        const alerts = await inventoryManager.getInventoryAlerts()
        
        const stats = {
          totalItems: items.length,
          lowStockItems: items.filter(item => item.status === 'low_stock').length,
          outOfStockItems: items.filter(item => item.status === 'out_of_stock').length,
          totalValue: items.reduce((sum, item) => sum + item.totalValue, 0),
          activeAlerts: alerts.filter(alert => !alert.resolved).length,
          criticalAlerts: alerts.filter(alert => alert.severity === 'critical' && !alert.resolved).length
        }

        const response = NextResponse.json({
          success: true,
          stats,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'movements': {
        // Get inventory movements
        const productId = searchParams.get('productId')
        const limit = parseInt(searchParams.get('limit') || '50')
        
        if (!productId) {
          return NextResponse.json(
            { error: 'productId parameter required for movements' },
            { status: 400 }
          )
        }

        const movements = await inventoryManager.getInventoryMovements(productId, limit)
        
        const response = NextResponse.json({
          success: true,
          movements,
          productId,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'reorder-rules': {
        // Get automated reorder rules
        const rules = await inventoryManager.getAutoReorderRules()
        
        const response = NextResponse.json({
          success: true,
          rules,
          total: rules.length,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'purchase-orders': {
        // Get recent purchase orders
        const days = parseInt(searchParams.get('days') || '30')
        const orders = await inventoryManager.getRecentPurchaseOrders(days)
        
        const response = NextResponse.json({
          success: true,
          orders,
          total: orders.length,
          days,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: items, alerts, stats, movements, reorder-rules, or purchase-orders' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in inventory API:', error)
    return NextResponse.json(
      { error: 'Inventory operation failed' },
      { status: 500 }
    )
  }
}

/**
 * POST - Create or update inventory data
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate CSRF token
    const csrfValidation = validateAdminCSRF(request)
    if (!csrfValidation.valid) {
      return NextResponse.json(
        { error: 'CSRF validation failed', message: csrfValidation.reason },
        { status: 403 }
      )
    }

    // Validate admin authentication
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action } = body
    const inventoryManager = new InventoryManager()

    switch (action) {
      case 'adjust-stock': {
        // Adjust stock levels
        const { productId, quantity, movementType, reason, metadata } = body
        
        if (!productId || quantity === undefined || !movementType || !reason) {
          return NextResponse.json(
            { error: 'productId, quantity, movementType, and reason are required' },
            { status: 400 }
          )
        }

        await inventoryManager.updateStock(
          productId,
          quantity,
          movementType,
          reason,
          { ...metadata, adminId: auth.adminId, adminAction: true }
        )

        const response = NextResponse.json({
          success: true,
          message: 'Stock adjusted successfully',
          productId,
          quantity,
          movementType,
          reason,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'create-reorder-rule': {
        // Create automated reorder rule
        const { productId, reorderPoint, orderQuantity, supplierId, leadTimeDays } = body
        
        if (!productId || !reorderPoint || !orderQuantity || !supplierId) {
          return NextResponse.json(
            { error: 'productId, reorderPoint, orderQuantity, and supplierId are required' },
            { status: 400 }
          )
        }

        const rule = await inventoryManager.createAutoReorderRule({
          productId,
          reorderPoint,
          orderQuantity,
          supplierId,
          leadTimeDays: leadTimeDays || 7,
          enabled: true
        })

        const response = NextResponse.json({
          success: true,
          message: 'Reorder rule created successfully',
          rule,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'trigger-reorder': {
        // Manually trigger reorder
        const { productId } = body
        
        if (!productId) {
          return NextResponse.json(
            { error: 'productId is required' },
            { status: 400 }
          )
        }

        const purchaseOrder = await inventoryManager.triggerReorder(productId)

        const response = NextResponse.json({
          success: true,
          message: 'Reorder triggered successfully',
          purchaseOrder,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'resolve-alert': {
        // Resolve inventory alert
        const { alertId } = body
        
        if (!alertId) {
          return NextResponse.json(
            { error: 'alertId is required' },
            { status: 400 }
          )
        }

        await inventoryManager.resolveAlert(alertId)

        const response = NextResponse.json({
          success: true,
          message: 'Alert resolved successfully',
          alertId,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: adjust-stock, create-reorder-rule, trigger-reorder, or resolve-alert' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in inventory POST API:', error)
    return NextResponse.json(
      { error: 'Inventory operation failed' },
      { status: 500 }
    )
  }
}

/**
 * PUT - Update inventory settings
 */
export async function PUT(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate CSRF token
    const csrfValidation = validateAdminCSRF(request)
    if (!csrfValidation.valid) {
      return NextResponse.json(
        { error: 'CSRF validation failed', message: csrfValidation.reason },
        { status: 403 }
      )
    }

    // Validate admin authentication
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action } = body
    const inventoryManager = new InventoryManager()

    switch (action) {
      case 'update-reorder-rule': {
        // Update reorder rule
        const { ruleId, updates } = body
        
        if (!ruleId || !updates) {
          return NextResponse.json(
            { error: 'ruleId and updates are required' },
            { status: 400 }
          )
        }

        await inventoryManager.updateAutoReorderRule(ruleId, updates)

        const response = NextResponse.json({
          success: true,
          message: 'Reorder rule updated successfully',
          ruleId,
          updates,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: update-reorder-rule' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in inventory PUT API:', error)
    return NextResponse.json(
      { error: 'Inventory update failed' },
      { status: 500 }
    )
  }
}
