/**
 * Admin MFA Verification API
 * 
 * Handles MFA verification during admin login process.
 * This endpoint is called after initial authentication to verify
 * the second factor before granting admin access.
 * 
 * Features:
 * - TOTP token verification
 * - Backup code verification
 * - Session management with MFA
 * - Failed attempt tracking
 * - Rate limiting protection
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { authenticator } from 'otplib'

export interface MFAVerificationRequest {
  adminId: string
  token?: string
  backupCode?: string
  sessionToken: string
}

export interface MFAVerificationResponse {
  success: boolean
  message: string
  sessionCookie?: string
  expiresAt?: Date
}

/**
 * Track failed MFA attempts (in-memory for now)
 * TODO: Move to database for production
 */
const failedAttempts = new Map<string, { count: number; lastAttempt: Date }>()

/**
 * Check if admin is rate limited
 */
function isRateLimited(adminId: string): boolean {
  const attempts = failedAttempts.get(adminId)
  if (!attempts) return false

  const now = new Date()
  const timeDiff = now.getTime() - attempts.lastAttempt.getTime()
  const fiveMinutes = 5 * 60 * 1000

  // Reset if more than 5 minutes have passed
  if (timeDiff > fiveMinutes) {
    failedAttempts.delete(adminId)
    return false
  }

  // Rate limit after 5 failed attempts
  return attempts.count >= 5
}

/**
 * Record failed attempt
 */
function recordFailedAttempt(adminId: string): void {
  const existing = failedAttempts.get(adminId) || { count: 0, lastAttempt: new Date() }
  failedAttempts.set(adminId, {
    count: existing.count + 1,
    lastAttempt: new Date()
  })
}

/**
 * Clear failed attempts on successful verification
 */
function clearFailedAttempts(adminId: string): void {
  failedAttempts.delete(adminId)
}

/**
 * Validate session token
 */
function validateSessionToken(sessionToken: string): boolean {
  // TODO: Implement proper session token validation
  // For now, check basic format
  return sessionToken && sessionToken.startsWith('session_') && sessionToken.length > 20
}

/**
 * POST - Verify MFA token or backup code
 */
export async function POST(request: NextRequest) {
  try {
    const body: MFAVerificationRequest = await request.json()
    const { adminId, token, backupCode, sessionToken } = body

    // Validate required fields
    if (!adminId || !sessionToken) {
      return NextResponse.json(
        { error: 'Missing required fields: adminId, sessionToken' },
        { status: 400 }
      )
    }

    if (!token && !backupCode) {
      return NextResponse.json(
        { error: 'Either token or backupCode is required' },
        { status: 400 }
      )
    }

    // Validate session token
    if (!validateSessionToken(sessionToken)) {
      return NextResponse.json(
        { error: 'Invalid session token' },
        { status: 401 }
      )
    }

    // Check rate limiting
    if (isRateLimited(adminId)) {
      return NextResponse.json(
        { error: 'Too many failed attempts. Please try again in 5 minutes.' },
        { status: 429 }
      )
    }

    let verificationSuccess = false
    let verificationMethod = ''

    if (token) {
      // Verify TOTP token
      // TODO: Get MFA secret from database
      const storedSecret = 'TEMP_SECRET' // Replace with actual database lookup
      
      verificationSuccess = authenticator.verify({ token, secret: storedSecret })
      verificationMethod = 'TOTP'
      
      console.log('🔐 TOTP verification attempt for admin:', adminId, 'Result:', verificationSuccess)
    } else if (backupCode) {
      // Verify backup code
      // TODO: Check backup code against database
      // For now, simulate verification (8-character uppercase alphanumeric)
      verificationSuccess = /^[A-Z0-9]{8}$/.test(backupCode)
      verificationMethod = 'Backup Code'
      
      console.log('🔑 Backup code verification attempt for admin:', adminId, 'Result:', verificationSuccess)
    }

    if (verificationSuccess) {
      // Clear failed attempts
      clearFailedAttempts(adminId)

      // Generate secure session cookie
      const sessionCookie = `mfa_verified_${adminId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const expiresAt = new Date(Date.now() + 4 * 60 * 60 * 1000) // 4 hours

      // TODO: Store verified session in database
      console.log('✅ MFA verification successful for admin:', adminId, 'Method:', verificationMethod)

      // Create response with secure cookie
      const response = NextResponse.json({
        success: true,
        message: 'MFA verification successful',
        expiresAt
      } as MFAVerificationResponse)

      // Set MFA verification cookie
      response.cookies.set('mfa-verified', sessionCookie, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 4 * 60 * 60, // 4 hours
        path: '/admin'
      })

      // Set MFA timestamp for session management
      response.cookies.set('mfa-timestamp', Date.now().toString(), {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 4 * 60 * 60, // 4 hours
        path: '/admin'
      })

      return response
    } else {
      // Record failed attempt
      recordFailedAttempt(adminId)

      console.log('❌ MFA verification failed for admin:', adminId, 'Method:', verificationMethod)

      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid verification code',
          attemptsRemaining: Math.max(0, 5 - (failedAttempts.get(adminId)?.count || 0))
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('❌ Error in MFA verification:', error)
    return NextResponse.json(
      { error: 'MFA verification failed' },
      { status: 500 }
    )
  }
}

/**
 * GET - Check MFA verification status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const adminId = searchParams.get('adminId')

    if (!adminId) {
      return NextResponse.json(
        { error: 'adminId parameter required' },
        { status: 400 }
      )
    }

    // Check MFA verification cookie
    const mfaVerified = request.cookies.get('mfa-verified')?.value
    const mfaTimestamp = request.cookies.get('mfa-timestamp')?.value

    let isVerified = false
    let expiresAt: Date | undefined

    if (mfaVerified && mfaTimestamp) {
      const timestamp = parseInt(mfaTimestamp)
      const fourHours = 4 * 60 * 60 * 1000
      const now = Date.now()

      // Check if MFA verification is still valid
      if (now - timestamp < fourHours) {
        isVerified = true
        expiresAt = new Date(timestamp + fourHours)
      }
    }

    // Check failed attempts
    const attempts = failedAttempts.get(adminId)
    const isRateLimited = attempts ? isRateLimited(adminId) : false

    return NextResponse.json({
      isVerified,
      expiresAt,
      isRateLimited,
      failedAttempts: attempts?.count || 0,
      nextAttemptAllowed: attempts && isRateLimited 
        ? new Date(attempts.lastAttempt.getTime() + 5 * 60 * 1000)
        : undefined
    })

  } catch (error) {
    console.error('❌ Error checking MFA status:', error)
    return NextResponse.json(
      { error: 'Failed to check MFA status' },
      { status: 500 }
    )
  }
}

/**
 * DELETE - Clear MFA verification (logout)
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const adminId = searchParams.get('adminId')

    if (!adminId) {
      return NextResponse.json(
        { error: 'adminId parameter required' },
        { status: 400 }
      )
    }

    // Clear failed attempts
    clearFailedAttempts(adminId)

    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'MFA verification cleared'
    })

    // Clear MFA verification cookies
    response.cookies.set('mfa-verified', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/admin'
    })

    response.cookies.set('mfa-timestamp', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/admin'
    })

    console.log('🧹 MFA verification cleared for admin:', adminId)

    return response

  } catch (error) {
    console.error('❌ Error clearing MFA verification:', error)
    return NextResponse.json(
      { error: 'Failed to clear MFA verification' },
      { status: 500 }
    )
  }
}
