/**
 * Admin Rate Limit Monitoring API
 * 
 * Provides rate limit monitoring and management functionality
 * for admin oversight and security monitoring.
 * 
 * Features:
 * - View all rate limit entries
 * - Clear rate limits (admin override)
 * - Rate limit statistics
 * - Security monitoring
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { 
  getAllRateLimitEntries, 
  clearRateLimit, 
  getRateLimitStatus,
  RATE_LIMIT_CONFIGS 
} from '../../../../src/admin/lib/rateLimiter'

/**
 * Validate admin authentication with superadmin check
 */
function validateAdminAuth(request: NextRequest): {
  isValid: boolean
  isSuperAdmin: boolean
  adminId?: string
} {
  const userRole = request.cookies.get('user-role')?.value
  const adminAccess = request.cookies.get('admin-access')?.value
  const authToken = request.cookies.get('firebase-auth-token')?.value
  const adminId = request.cookies.get('user-id')?.value

  const hasAdminRole = userRole === 'admin' || userRole === 'superadmin'
  const hasAdminAccess = adminAccess === 'true'
  const hasAuthToken = Boolean(authToken)
  const isSuperAdmin = userRole === 'superadmin'

  return {
    isValid: hasAdminRole && hasAdminAccess && hasAuthToken,
    isSuperAdmin,
    adminId
  }
}

/**
 * GET - Get rate limit monitoring data
 */
export async function GET(request: NextRequest) {
  try {
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const endpoint = searchParams.get('endpoint')
    const identifier = searchParams.get('identifier')

    if (endpoint && identifier) {
      // Get specific rate limit status
      const config = RATE_LIMIT_CONFIGS[endpoint as keyof typeof RATE_LIMIT_CONFIGS]
      if (!config) {
        return NextResponse.json(
          { error: 'Invalid endpoint type' },
          { status: 400 }
        )
      }

      const status = getRateLimitStatus(identifier, endpoint, config)
      return NextResponse.json({
        endpoint,
        identifier,
        status,
        config: {
          maxRequests: config.maxRequests,
          windowMs: config.windowMs
        }
      })
    }

    // Get all rate limit entries
    const entries = getAllRateLimitEntries()
    
    // Calculate statistics
    const stats = {
      totalEntries: entries.length,
      activeBlocks: entries.filter(e => e.count >= RATE_LIMIT_CONFIGS.admin.maxRequests).length,
      topOffenders: entries.slice(0, 10),
      endpointBreakdown: entries.reduce((acc, entry) => {
        acc[entry.endpoint] = (acc[entry.endpoint] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }

    return NextResponse.json({
      entries,
      stats,
      configs: RATE_LIMIT_CONFIGS,
      timestamp: new Date()
    })

  } catch (error) {
    console.error('❌ Error fetching rate limit data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch rate limit data' },
      { status: 500 }
    )
  }
}

/**
 * DELETE - Clear rate limit for specific identifier/endpoint
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    // Only superadmin can clear rate limits
    if (!auth.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Superadmin access required to clear rate limits' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const endpoint = searchParams.get('endpoint')
    const identifier = searchParams.get('identifier')

    if (!endpoint || !identifier) {
      return NextResponse.json(
        { error: 'endpoint and identifier parameters required' },
        { status: 400 }
      )
    }

    const cleared = clearRateLimit(identifier, endpoint)

    if (cleared) {
      console.log(`🧹 Rate limit cleared by admin ${auth.adminId} for ${endpoint}:${identifier}`)
      
      return NextResponse.json({
        success: true,
        message: `Rate limit cleared for ${endpoint}:${identifier}`,
        clearedBy: auth.adminId,
        timestamp: new Date()
      })
    } else {
      return NextResponse.json(
        { error: 'Rate limit entry not found' },
        { status: 404 }
      )
    }

  } catch (error) {
    console.error('❌ Error clearing rate limit:', error)
    return NextResponse.json(
      { error: 'Failed to clear rate limit' },
      { status: 500 }
    )
  }
}

/**
 * POST - Manually add rate limit entry (for testing/security)
 */
export async function POST(request: NextRequest) {
  try {
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    // Only superadmin can manually add rate limits
    if (!auth.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Superadmin access required to add rate limits' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { endpoint, identifier, reason } = body

    if (!endpoint || !identifier) {
      return NextResponse.json(
        { error: 'endpoint and identifier are required' },
        { status: 400 }
      )
    }

    // TODO: Implement manual rate limit addition
    // For now, just log the action
    console.log(`⚠️ Manual rate limit added by admin ${auth.adminId} for ${endpoint}:${identifier}, reason: ${reason}`)

    return NextResponse.json({
      success: true,
      message: `Rate limit manually added for ${endpoint}:${identifier}`,
      addedBy: auth.adminId,
      reason: reason || 'Manual admin action',
      timestamp: new Date()
    })

  } catch (error) {
    console.error('❌ Error adding manual rate limit:', error)
    return NextResponse.json(
      { error: 'Failed to add rate limit' },
      { status: 500 }
    )
  }
}
