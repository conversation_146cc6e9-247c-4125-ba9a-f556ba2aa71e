/**
 * Admin Session Management API
 * 
 * Provides comprehensive session management functionality for admin
 * accounts including session monitoring, management, and security.
 * 
 * Features:
 * - Session listing and monitoring
 * - Session termination
 * - Session statistics
 * - Concurrent session management
 * - Security monitoring
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { 
  getAdminSessions, 
  getSessionStats, 
  removeAdminSession, 
  destroyAdminSessions,
  validateAdminSession 
} from '../../../../src/admin/lib/sessionManager'
import { createRateLimitMiddleware } from '../../../../src/admin/lib/rateLimiter'

/**
 * Validate admin authentication
 */
function validateAdminAuth(request: NextRequest): {
  isValid: boolean
  isSuperAdmin: boolean
  adminId?: string
  sessionId?: string
} {
  const userRole = request.cookies.get('user-role')?.value
  const adminAccess = request.cookies.get('admin-access')?.value
  const authToken = request.cookies.get('firebase-auth-token')?.value
  const adminId = request.cookies.get('user-id')?.value
  const sessionId = request.cookies.get('admin-session')?.value

  const hasAdminRole = userRole === 'admin' || userRole === 'superadmin'
  const hasAdminAccess = adminAccess === 'true'
  const hasAuthToken = Boolean(authToken)
  const isSuperAdmin = userRole === 'superadmin'

  return {
    isValid: hasAdminRole && hasAdminAccess && hasAuthToken,
    isSuperAdmin,
    adminId,
    sessionId
  }
}

/**
 * GET - Get session information
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const targetAdminId = searchParams.get('adminId')

    switch (action) {
      case 'stats': {
        // Get session statistics (superadmin only)
        if (!auth.isSuperAdmin) {
          return NextResponse.json(
            { error: 'Superadmin access required for session statistics' },
            { status: 403 }
          )
        }

        const stats = getSessionStats()
        return NextResponse.json({
          stats,
          timestamp: new Date()
        })
      }

      case 'list': {
        // List sessions for admin or all admins (superadmin)
        const adminIdToQuery = targetAdminId || auth.adminId
        
        if (targetAdminId && targetAdminId !== auth.adminId && !auth.isSuperAdmin) {
          return NextResponse.json(
            { error: 'Cannot view other admin sessions without superadmin access' },
            { status: 403 }
          )
        }

        if (!adminIdToQuery) {
          return NextResponse.json(
            { error: 'Admin ID required' },
            { status: 400 }
          )
        }

        const sessions = getAdminSessions(adminIdToQuery)
        
        // Filter sensitive information for non-superadmin
        const filteredSessions = sessions.map(session => ({
          sessionId: session.sessionId,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
          expiresAt: session.expiresAt,
          ipAddress: auth.isSuperAdmin ? session.ipAddress : session.ipAddress.replace(/\.\d+$/, '.***'),
          userAgent: session.userAgent.substring(0, 50) + '...',
          isActive: session.isActive,
          mfaVerified: session.mfaVerified,
          isCurrent: session.sessionId === auth.sessionId
        }))

        return NextResponse.json({
          adminId: adminIdToQuery,
          sessions: filteredSessions,
          totalSessions: sessions.length,
          timestamp: new Date()
        })
      }

      case 'current': {
        // Get current session information
        if (!auth.sessionId) {
          return NextResponse.json(
            { error: 'No active session found' },
            { status: 404 }
          )
        }

        const currentSession = validateAdminSession(auth.sessionId)
        if (!currentSession) {
          return NextResponse.json(
            { error: 'Session not found or expired' },
            { status: 404 }
          )
        }

        return NextResponse.json({
          session: {
            sessionId: currentSession.sessionId,
            adminId: currentSession.adminId,
            adminEmail: currentSession.adminEmail,
            adminRole: currentSession.adminRole,
            createdAt: currentSession.createdAt,
            lastActivity: currentSession.lastActivity,
            expiresAt: currentSession.expiresAt,
            mfaVerified: currentSession.mfaVerified,
            mfaVerifiedAt: currentSession.mfaVerifiedAt
          },
          timestamp: new Date()
        })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: stats, list, or current' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in session management:', error)
    return NextResponse.json(
      { error: 'Session management operation failed' },
      { status: 500 }
    )
  }
}

/**
 * DELETE - Terminate sessions
 */
export async function DELETE(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const adminId = searchParams.get('adminId')
    const action = searchParams.get('action')

    if (action === 'all' && adminId) {
      // Destroy all sessions for an admin
      if (adminId !== auth.adminId && !auth.isSuperAdmin) {
        return NextResponse.json(
          { error: 'Cannot terminate other admin sessions without superadmin access' },
          { status: 403 }
        )
      }

      const destroyedCount = destroyAdminSessions(adminId)
      
      console.log(`🗑️ All sessions destroyed for admin ${adminId} by ${auth.adminId}`)
      
      return NextResponse.json({
        success: true,
        message: `Destroyed ${destroyedCount} sessions for admin ${adminId}`,
        destroyedCount,
        destroyedBy: auth.adminId,
        timestamp: new Date()
      })
    }

    if (sessionId) {
      // Terminate specific session
      const session = validateAdminSession(sessionId)
      if (!session) {
        return NextResponse.json(
          { error: 'Session not found' },
          { status: 404 }
        )
      }

      // Check permissions
      if (session.adminId !== auth.adminId && !auth.isSuperAdmin) {
        return NextResponse.json(
          { error: 'Cannot terminate other admin sessions without superadmin access' },
          { status: 403 }
        )
      }

      const removed = removeAdminSession(sessionId)
      if (removed) {
        console.log(`🗑️ Session ${sessionId} terminated by ${auth.adminId}`)
        
        return NextResponse.json({
          success: true,
          message: `Session ${sessionId} terminated successfully`,
          terminatedBy: auth.adminId,
          timestamp: new Date()
        })
      } else {
        return NextResponse.json(
          { error: 'Failed to terminate session' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      { error: 'sessionId or adminId with action=all required' },
      { status: 400 }
    )

  } catch (error) {
    console.error('❌ Error terminating session:', error)
    return NextResponse.json(
      { error: 'Session termination failed' },
      { status: 500 }
    )
  }
}

/**
 * POST - Session management actions
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, sessionId } = body

    switch (action) {
      case 'refresh': {
        // Refresh current session
        if (!auth.sessionId) {
          return NextResponse.json(
            { error: 'No active session to refresh' },
            { status: 400 }
          )
        }

        const session = validateAdminSession(auth.sessionId)
        if (!session) {
          return NextResponse.json(
            { error: 'Session not found or expired' },
            { status: 404 }
          )
        }

        // Session is automatically refreshed by validateAdminSession
        return NextResponse.json({
          success: true,
          message: 'Session refreshed successfully',
          expiresAt: session.expiresAt,
          timestamp: new Date()
        })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in session action:', error)
    return NextResponse.json(
      { error: 'Session action failed' },
      { status: 500 }
    )
  }
}
