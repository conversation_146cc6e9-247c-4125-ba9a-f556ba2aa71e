/**
 * Admin Support Management API
 * 
 * Provides comprehensive customer support management functionality
 * including ticket management, live chat, and user impersonation.
 * 
 * Features:
 * - Support ticket CRUD operations
 * - Ticket assignment and status management
 * - Live chat session management
 * - User impersonation controls
 * - Support analytics and reporting
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { createRateLimitMiddleware } from '../../../../src/admin/lib/rateLimiter'
import { validateAdminCSRF } from '../../../../src/admin/lib/csrfProtection'
import { addAdminSecurityHeaders } from '../../../../src/admin/lib/securityHeaders'

/**
 * Validate admin authentication
 */
function validateAdminAuth(request: NextRequest): {
  isValid: boolean
  adminId?: string
  adminRole?: string
} {
  const userRole = request.cookies.get('user-role')?.value
  const adminAccess = request.cookies.get('admin-access')?.value
  const authToken = request.cookies.get('firebase-auth-token')?.value
  const adminId = request.cookies.get('user-id')?.value

  const hasAdminRole = userRole === 'admin' || userRole === 'superadmin'
  const hasAdminAccess = adminAccess === 'true'
  const hasAuthToken = Boolean(authToken)

  return {
    isValid: hasAdminRole && hasAdminAccess && hasAuthToken,
    adminId,
    adminRole: userRole
  }
}

/**
 * Generate mock support data
 */
function generateMockSupportData() {
  return {
    stats: {
      totalTickets: 1247,
      openTickets: 23,
      inProgressTickets: 15,
      resolvedToday: 8,
      avgResponseTime: 45, // minutes
      avgResolutionTime: 180, // minutes
      satisfactionScore: 4.6,
      slaBreaches: 2,
      activeChatSessions: 7,
      queuedChats: 3
    },
    tickets: [
      {
        id: '1',
        ticketNumber: 'TKT-2025-001',
        subject: 'Unable to complete checkout process',
        description: 'Customer experiencing issues during checkout with payment processing',
        customer: {
          id: 'cust_1',
          name: 'John Doe',
          email: '<EMAIL>',
          tier: 'Premium'
        },
        priority: 'high',
        status: 'open',
        category: 'technical',
        assignee: {
          id: 'admin_1',
          name: 'Sarah Chen',
          email: '<EMAIL>'
        },
        tags: ['checkout', 'payment', 'urgent'],
        messageCount: 3,
        slaDeadline: new Date(Date.now() + 2 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 30 * 60 * 1000)
      },
      {
        id: '2',
        ticketNumber: 'TKT-2025-002',
        subject: 'Keycap compatibility question',
        description: 'Customer asking about Cherry MX compatibility with specific keyset',
        customer: {
          id: 'cust_2',
          name: 'Alice Smith',
          email: '<EMAIL>',
          tier: 'Standard'
        },
        priority: 'normal',
        status: 'in_progress',
        category: 'product',
        assignee: {
          id: 'admin_2',
          name: 'Mike Johnson',
          email: '<EMAIL>'
        },
        tags: ['compatibility', 'product-info'],
        messageCount: 5,
        slaDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 15 * 60 * 1000)
      }
    ],
    chatSessions: [
      {
        id: 'chat_1',
        customerId: 'cust_1',
        customerName: 'John Doe',
        status: 'active',
        priority: 'normal',
        assignedTo: 'admin_1',
        messageCount: 12,
        waitTime: 0,
        startedAt: new Date(Date.now() - 15 * 60 * 1000)
      }
    ]
  }
}

/**
 * GET - Get support data
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate admin authentication
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'stats': {
        // Get support statistics
        const data = generateMockSupportData()
        
        const response = NextResponse.json({
          success: true,
          stats: data.stats,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'tickets': {
        // Get support tickets with filtering
        const status = searchParams.get('status')
        const priority = searchParams.get('priority')
        const assignee = searchParams.get('assignee')
        const limit = parseInt(searchParams.get('limit') || '50')
        const offset = parseInt(searchParams.get('offset') || '0')

        const data = generateMockSupportData()
        let tickets = data.tickets

        // Apply filters
        if (status && status !== 'all') {
          tickets = tickets.filter(ticket => ticket.status === status)
        }
        if (priority && priority !== 'all') {
          tickets = tickets.filter(ticket => ticket.priority === priority)
        }
        if (assignee && assignee !== 'all') {
          if (assignee === 'unassigned') {
            tickets = tickets.filter(ticket => !ticket.assignee)
          } else {
            tickets = tickets.filter(ticket => ticket.assignee?.id === assignee)
          }
        }

        // Apply pagination
        const paginatedTickets = tickets.slice(offset, offset + limit)

        const response = NextResponse.json({
          success: true,
          tickets: paginatedTickets,
          total: tickets.length,
          limit,
          offset,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'chat-sessions': {
        // Get active chat sessions
        const data = generateMockSupportData()
        
        const response = NextResponse.json({
          success: true,
          sessions: data.chatSessions,
          total: data.chatSessions.length,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'analytics': {
        // Get support analytics
        const analytics = {
          ticketTrends: {
            daily: Array.from({ length: 7 }, (_, i) => ({
              date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000),
              created: Math.floor(Math.random() * 20) + 5,
              resolved: Math.floor(Math.random() * 18) + 3
            })),
            weekly: Array.from({ length: 4 }, (_, i) => ({
              week: `Week ${i + 1}`,
              created: Math.floor(Math.random() * 100) + 50,
              resolved: Math.floor(Math.random() * 95) + 45
            }))
          },
          responseTimeMetrics: {
            average: 45,
            median: 38,
            p95: 120,
            trend: 'improving'
          },
          satisfactionMetrics: {
            average: 4.6,
            distribution: {
              5: 65,
              4: 25,
              3: 8,
              2: 1,
              1: 1
            }
          },
          agentPerformance: [
            { name: 'Sarah Chen', ticketsResolved: 45, avgResponseTime: 32, satisfaction: 4.8 },
            { name: 'Mike Johnson', ticketsResolved: 38, avgResponseTime: 41, satisfaction: 4.7 },
            { name: 'Emma Davis', ticketsResolved: 52, avgResponseTime: 28, satisfaction: 4.9 }
          ]
        }

        const response = NextResponse.json({
          success: true,
          analytics,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: stats, tickets, chat-sessions, or analytics' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in support API:', error)
    return NextResponse.json(
      { error: 'Support operation failed' },
      { status: 500 }
    )
  }
}

/**
 * POST - Create or update support data
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware('admin')(request)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate CSRF token
    const csrfValidation = validateAdminCSRF(request)
    if (!csrfValidation.valid) {
      return NextResponse.json(
        { error: 'CSRF validation failed', message: csrfValidation.reason },
        { status: 403 }
      )
    }

    // Validate admin authentication
    const auth = validateAdminAuth(request)
    if (!auth.isValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'create-ticket': {
        // Create new support ticket
        const { subject, description, customerId, customerEmail, priority, category } = body
        
        if (!subject || !description) {
          return NextResponse.json(
            { error: 'subject and description are required' },
            { status: 400 }
          )
        }

        const ticket = {
          id: `ticket_${Date.now()}`,
          ticketNumber: `TKT-2025-${String(Date.now()).slice(-3)}`,
          subject,
          description,
          customerId,
          customerEmail,
          priority: priority || 'normal',
          category: category || 'general',
          status: 'open',
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: auth.adminId
        }

        const response = NextResponse.json({
          success: true,
          message: 'Ticket created successfully',
          ticket,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'update-ticket': {
        // Update existing ticket
        const { ticketId, updates } = body
        
        if (!ticketId || !updates) {
          return NextResponse.json(
            { error: 'ticketId and updates are required' },
            { status: 400 }
          )
        }

        const response = NextResponse.json({
          success: true,
          message: 'Ticket updated successfully',
          ticketId,
          updates: {
            ...updates,
            updatedAt: new Date(),
            updatedBy: auth.adminId
          },
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'assign-ticket': {
        // Assign ticket to admin
        const { ticketId, assigneeId } = body
        
        if (!ticketId || !assigneeId) {
          return NextResponse.json(
            { error: 'ticketId and assigneeId are required' },
            { status: 400 }
          )
        }

        const response = NextResponse.json({
          success: true,
          message: 'Ticket assigned successfully',
          ticketId,
          assigneeId,
          assignedBy: auth.adminId,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      case 'start-impersonation': {
        // Start user impersonation session
        const { userId, reason } = body
        
        if (!userId || !reason) {
          return NextResponse.json(
            { error: 'userId and reason are required' },
            { status: 400 }
          )
        }

        // Only superadmin can impersonate users
        if (auth.adminRole !== 'superadmin') {
          return NextResponse.json(
            { error: 'Superadmin access required for user impersonation' },
            { status: 403 }
          )
        }

        const impersonationSession = {
          id: `imp_${Date.now()}`,
          userId,
          adminId: auth.adminId,
          reason,
          startedAt: new Date(),
          isActive: true
        }

        const response = NextResponse.json({
          success: true,
          message: 'Impersonation session started',
          session: impersonationSession,
          timestamp: new Date()
        })

        return addAdminSecurityHeaders(response)
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: create-ticket, update-ticket, assign-ticket, or start-impersonation' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in support POST API:', error)
    return NextResponse.json(
      { error: 'Support operation failed' },
      { status: 500 }
    )
  }
}
