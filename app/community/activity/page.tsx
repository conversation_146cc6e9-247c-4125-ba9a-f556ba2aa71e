/**
 * Community Activity Page
 * 
 * Real-time community activity feed and updates.
 * 
 * <AUTHOR> Team
 */

import { Suspense } from 'react'
import { Metadata } from 'next'
import SimpleErrorBoundary from "@/components/error/SimpleErrorBoundary"
import ActivityTab from '@/components/community/tabs/activity/ActivityTab'
import { CommunityAuthProvider } from '@/contexts/CommunityAuthContext'
import CommunityLayout from '@/components/community/CommunityLayout'

export const metadata: Metadata = {
  title: 'Activity - Syndicaps Community',
  description: 'Stay updated with real-time community activity',
  keywords: ['community', 'activity', 'feed', 'updates', 'real-time', 'syndicaps'],
  openGraph: {
    title: 'Activity - Syndicaps Community',
    description: 'Stay updated with real-time community activity',
    url: '/community/activity',
    type: 'website',
  },
}

export default function CommunityActivityPage() {
  return (
    <CommunityAuthProvider>
      <SimpleErrorBoundary>
        <CommunityLayout>
          <Suspense fallback={
            <div className="animate-pulse space-y-6">
              <div className="h-12 bg-gray-800 rounded-lg"></div>
              <div className="space-y-4">
                <div className="h-16 bg-gray-800 rounded-lg"></div>
                <div className="h-16 bg-gray-800 rounded-lg"></div>
                <div className="h-16 bg-gray-800 rounded-lg"></div>
                <div className="h-16 bg-gray-800 rounded-lg"></div>
                <div className="h-16 bg-gray-800 rounded-lg"></div>
              </div>
            </div>
          }>
            <ActivityTab />
          </Suspense>
        </CommunityLayout>
      </SimpleErrorBoundary>
    </CommunityAuthProvider>
  )
}