/**
 * Individual Contest Page
 *
 * Detailed view of a specific contest including submissions gallery,
 * contest information, and participation options.
 *
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Trophy, 
  Calendar, 
  Users, 
  Eye,
  Star,
  Upload,
  Vote,
  Share2,
  Flag,
  Clock,
  Award
} from 'lucide-react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useUser } from '../../../../src/lib/useUser'
import { useContest, useContestSubmissions } from '../../../../src/hooks/useContests'
import { 
  ContestStatusCard, 
  ContestSubmissionGallery, 
  UserSubmissionManager 
} from '../../../../src/components/contests'

export default function ContestDetailPage() {
  const params = useParams()
  const contestId = params.id as string
  const { user } = useUser()
  const { contest, loading: contestLoading } = useContest(contestId)
  const { submissions, loading: submissionsLoading } = useContestSubmissions(contestId)
  const [activeTab, setActiveTab] = useState<'overview' | 'submissions' | 'my-submissions' | 'rules'>('overview')

  // Get user's submissions for this contest
  const userSubmissions = useMemo(() => {
    if (!user || !submissions) return []
    return submissions.filter(submission => submission.userId === user.uid)
  }, [user, submissions])

  // Check if user can participate
  const canParticipate = useMemo(() => {
    if (!user || !contest) return false
    return contest.status === 'active' && 
           userSubmissions.length < contest.requirements.maxSubmissions
  }, [user, contest, userSubmissions])

  if (contestLoading) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  if (!contest) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Contest Not Found</h1>
          <p className="text-gray-400 mb-6">The contest you're looking for doesn't exist or has been removed.</p>
          <Link 
            href="/community/contests"
            className="px-6 py-3 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors"
          >
            Browse Contests
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Header */}
      <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm text-gray-400 mb-6">
            <Link href="/community" className="hover:text-white transition-colors">
              Community
            </Link>
            <span>/</span>
            <Link href="/community/contests" className="hover:text-white transition-colors">
              Contests
            </Link>
            <span>/</span>
            <span className="text-white">{contest.title}</span>
          </div>

          {/* Contest Header */}
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-6 lg:space-y-0">
            <div className="flex-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center space-x-3 mb-4"
              >
                <div className="p-2 bg-accent-500/20 rounded-lg">
                  <Trophy className="w-6 h-6 text-accent-400" />
                </div>
                {contest.featured && (
                  <span className="px-3 py-1 bg-accent-500 text-white text-sm font-medium rounded-full">
                    Featured
                  </span>
                )}
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-3xl md:text-4xl font-bold text-white mb-3"
              >
                {contest.title}
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-xl text-gray-300 mb-4"
              >
                {contest.theme}
              </motion.p>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-gray-400 leading-relaxed max-w-3xl"
              >
                {contest.description}
              </motion.p>

              {/* Tags */}
              {contest.tags.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="flex flex-wrap gap-2 mt-4"
                >
                  {contest.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </motion.div>
              )}
            </div>

            {/* Contest Status & Actions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="lg:w-80 space-y-4"
            >
              <ContestStatusCard contest={contest} showDetails={true} />
              
              {/* Action Buttons */}
              <div className="space-y-3">
                {canParticipate && (
                  <button
                    onClick={() => setActiveTab('my-submissions')}
                    className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors"
                  >
                    <Upload className="w-5 h-5" />
                    <span>Submit Entry</span>
                  </button>
                )}
                
                {contest.status === 'voting' && (
                  <button
                    onClick={() => setActiveTab('submissions')}
                    className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors"
                  >
                    <Vote className="w-5 h-5" />
                    <span>Vote Now</span>
                  </button>
                )}

                <div className="flex space-x-2">
                  <button className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                  <button className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                    <Flag className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-900 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: Trophy },
              { id: 'submissions', label: 'Submissions', icon: Eye },
              ...(user ? [{ id: 'my-submissions', label: 'My Submissions', icon: Upload }] : []),
              { id: 'rules', label: 'Rules & Prizes', icon: Award }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-accent-500 text-accent-400'
                      : 'border-transparent text-gray-400 hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <ContestOverview contest={contest} />
        )}

        {activeTab === 'submissions' && (
          <ContestSubmissionGallery contest={contest} />
        )}

        {activeTab === 'my-submissions' && user && (
          <UserSubmissionManager 
            contest={contest} 
            userSubmissions={userSubmissions}
            onSubmissionUpdate={() => {
              // Refresh submissions
              window.location.reload()
            }}
          />
        )}

        {activeTab === 'rules' && (
          <ContestRulesAndPrizes contest={contest} />
        )}
      </div>
    </div>
  )
}

/**
 * Contest Overview Component
 */
interface ContestOverviewProps {
  contest: any // Contest type
}

const ContestOverview: React.FC<ContestOverviewProps> = ({ contest }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Main Content */}
      <div className="lg:col-span-2 space-y-8">
        {/* Description */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">About This Contest</h2>
          <p className="text-gray-300 leading-relaxed">{contest.description}</p>
        </div>

        {/* Timeline */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Contest Timeline</h2>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <div>
                <p className="text-white font-medium">Submissions Open</p>
                <p className="text-gray-400 text-sm">
                  {contest.submissionStart.toDate().toLocaleDateString()} at {contest.submissionStart.toDate().toLocaleTimeString()}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div>
                <p className="text-white font-medium">Voting Begins</p>
                <p className="text-gray-400 text-sm">
                  {contest.votingStart.toDate().toLocaleDateString()} at {contest.votingStart.toDate().toLocaleTimeString()}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <p className="text-white font-medium">Results Announced</p>
                <p className="text-gray-400 text-sm">
                  {contest.resultsDate.toDate().toLocaleDateString()} at {contest.resultsDate.toDate().toLocaleTimeString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Stats */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-white mb-4">Contest Stats</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Participants</span>
              <span className="text-white font-medium">{contest.stats.participants}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Submissions</span>
              <span className="text-white font-medium">{contest.stats.submissions}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Total Votes</span>
              <span className="text-white font-medium">{contest.stats.totalVotes}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Category</span>
              <span className="text-white font-medium capitalize">
                {contest.category.replace('_', ' ')}
              </span>
            </div>
          </div>
        </div>

        {/* Requirements */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-white mb-4">Requirements</h3>
          <div className="space-y-2 text-sm">
            <p className="text-gray-400">
              Max submissions: <span className="text-white">{contest.requirements.maxSubmissions}</span>
            </p>
            <p className="text-gray-400">
              File types: <span className="text-white">{contest.requirements.fileTypes.join(', ').toUpperCase()}</span>
            </p>
            <p className="text-gray-400">
              Max file size: <span className="text-white">{(contest.requirements.maxFileSize / (1024 * 1024)).toFixed(1)}MB</span>
            </p>
            {contest.requirements.minImageDimensions && (
              <p className="text-gray-400">
                Min dimensions: <span className="text-white">
                  {contest.requirements.minImageDimensions.width}×{contest.requirements.minImageDimensions.height}px
                </span>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Contest Rules and Prizes Component
 */
interface ContestRulesAndPrizesProps {
  contest: any // Contest type
}

const ContestRulesAndPrizes: React.FC<ContestRulesAndPrizesProps> = ({ contest }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Rules */}
      <div className="bg-gray-800/50 rounded-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Contest Rules</h2>
        <div className="space-y-3">
          {contest.rules.map((rule: string, index: number) => (
            <div key={index} className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-accent-500 text-white text-sm font-medium rounded-full flex items-center justify-center">
                {index + 1}
              </span>
              <p className="text-gray-300">{rule}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Prizes */}
      <div className="bg-gray-800/50 rounded-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Prizes</h2>
        <div className="space-y-4">
          {/* First Place */}
          <div className="p-4 bg-gradient-to-r from-yellow-900/20 to-yellow-800/20 border border-yellow-500/30 rounded-lg">
            <h3 className="text-yellow-400 font-bold mb-2">🥇 First Place</h3>
            <div className="space-y-1 text-sm">
              <p className="text-white">{contest.prizes.first.points} points</p>
              {contest.prizes.first.storeCredit > 0 && (
                <p className="text-white">${contest.prizes.first.storeCredit} store credit</p>
              )}
              {contest.prizes.first.physicalPrize && (
                <p className="text-white">{contest.prizes.first.physicalPrize}</p>
              )}
            </div>
          </div>

          {/* Second Place */}
          <div className="p-4 bg-gradient-to-r from-gray-700/20 to-gray-600/20 border border-gray-500/30 rounded-lg">
            <h3 className="text-gray-300 font-bold mb-2">🥈 Second Place</h3>
            <div className="space-y-1 text-sm">
              <p className="text-white">{contest.prizes.second.points} points</p>
              {contest.prizes.second.storeCredit > 0 && (
                <p className="text-white">${contest.prizes.second.storeCredit} store credit</p>
              )}
              {contest.prizes.second.physicalPrize && (
                <p className="text-white">{contest.prizes.second.physicalPrize}</p>
              )}
            </div>
          </div>

          {/* Third Place */}
          <div className="p-4 bg-gradient-to-r from-orange-900/20 to-orange-800/20 border border-orange-500/30 rounded-lg">
            <h3 className="text-orange-400 font-bold mb-2">🥉 Third Place</h3>
            <div className="space-y-1 text-sm">
              <p className="text-white">{contest.prizes.third.points} points</p>
              {contest.prizes.third.storeCredit > 0 && (
                <p className="text-white">${contest.prizes.third.storeCredit} store credit</p>
              )}
              {contest.prizes.third.physicalPrize && (
                <p className="text-white">{contest.prizes.third.physicalPrize}</p>
              )}
            </div>
          </div>

          {/* Participation */}
          <div className="p-4 bg-gradient-to-r from-blue-900/20 to-blue-800/20 border border-blue-500/30 rounded-lg">
            <h3 className="text-blue-400 font-bold mb-2">🎖️ Participation Prize</h3>
            <p className="text-white text-sm">{contest.prizes.participation.points} points</p>
          </div>
        </div>
      </div>
    </div>
  )
}
