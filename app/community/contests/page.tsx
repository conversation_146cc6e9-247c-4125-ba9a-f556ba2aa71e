/**
 * Community Contests Page
 *
 * Main page for discovering and participating in community contests.
 * Uses the tab-based community layout with the ContestsTab component.
 *
 * <AUTHOR> Team
 */

import React, { Suspense } from 'react'
import { Metadata } from 'next'
import CommunityLayout from '../../../src/components/community/CommunityLayout'
import ContestsTab from '../../../src/components/community/tabs/contests/ContestsTab'

export const metadata: Metadata = {
  title: 'Contests - Syndicaps Community',
  description: 'Participate in design contests and showcase your creativity',
  keywords: ['contests', 'design', 'competition', 'community', 'syndicaps'],
  openGraph: {
    title: 'Contests - Syndicaps Community',
    description: 'Participate in design contests and showcase your creativity',
    url: '/community/contests',
    type: 'website',
  },
}

export default function CommunityContestsPage() {
  return (
    <CommunityLayout>
      <Suspense fallback={
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-700 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="bg-gray-800/50 rounded-lg p-6">
                <div className="h-4 bg-gray-700 rounded w-3/4 mb-3"></div>
                <div className="h-3 bg-gray-700 rounded w-1/2 mb-4"></div>
                <div className="h-20 bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      }>
        <ContestsTab />
      </Suspense>
    </CommunityLayout>
  )
}


