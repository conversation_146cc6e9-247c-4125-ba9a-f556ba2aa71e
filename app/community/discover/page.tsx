/**
 * Community Discover Page
 * 
 * Main discover page for featured content, trending discussions,
 * and community highlights.
 * 
 * <AUTHOR> Team
 */

import { Suspense } from 'react'
import { Metadata } from 'next'
import SimpleErrorBoundary from "@/components/error/SimpleErrorBoundary"
import DiscoverTab from '@/components/community/tabs/discover/DiscoverTab'
import { CommunityAuthProvider } from '@/contexts/CommunityAuthContext'
import CommunityLayout from '@/components/community/CommunityLayout'

export const metadata: Metadata = {
  title: 'Discover - Syndicaps Community',
  description: 'Explore featured content, trending discussions, and community highlights',
  keywords: ['community', 'discover', 'featured', 'trending', 'syndicaps'],
  openGraph: {
    title: 'Discover - Syndicaps Community',
    description: 'Explore featured content and community highlights',
    url: '/community/discover',
    type: 'website',
  },
}

export default function CommunityDiscoverPage() {
  return (
    <CommunityAuthProvider>
      <SimpleErrorBoundary>
        <CommunityLayout>
          <Suspense fallback={
            <div className="animate-pulse space-y-6">
              <div className="h-32 bg-gray-800 rounded-lg"></div>
              <div className="h-64 bg-gray-800 rounded-lg"></div>
              <div className="h-48 bg-gray-800 rounded-lg"></div>
            </div>
          }>
            <DiscoverTab />
          </Suspense>
        </CommunityLayout>
      </SimpleErrorBoundary>
    </CommunityAuthProvider>
  )
}