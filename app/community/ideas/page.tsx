/**
 * Community Ideas Page
 * 
 * Share ideas and vote on community proposals.
 * 
 * <AUTHOR> Team
 */

import { Suspense } from 'react'
import { Metadata } from 'next'
import SimpleErrorBoundary from "@/components/error/SimpleErrorBoundary"
import IdeasTab from '@/components/community/tabs/ideas/IdeasTab'
import { CommunityAuthProvider } from '@/contexts/CommunityAuthContext'
import CommunityLayout from '@/components/community/CommunityLayout'

export const metadata: Metadata = {
  title: 'Ideas - Syndicaps Community',
  description: 'Share ideas and vote on community proposals',
  keywords: ['community', 'ideas', 'proposals', 'voting', 'suggestions', 'syndicaps'],
  openGraph: {
    title: 'Ideas - Syndicaps Community',
    description: 'Share ideas and vote on community proposals',
    url: '/community/ideas',
    type: 'website',
  },
}

export default function CommunityIdeasPage() {
  return (
    <CommunityAuthProvider>
      <SimpleErrorBoundary>
        <CommunityLayout>
          <Suspense fallback={
            <div className="animate-pulse space-y-6">
              <div className="h-16 bg-gray-800 rounded-lg"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="h-48 bg-gray-800 rounded-lg"></div>
                <div className="h-48 bg-gray-800 rounded-lg"></div>
              </div>
              <div className="space-y-4">
                <div className="h-24 bg-gray-800 rounded-lg"></div>
                <div className="h-24 bg-gray-800 rounded-lg"></div>
                <div className="h-24 bg-gray-800 rounded-lg"></div>
              </div>
            </div>
          }>
            <IdeasTab />
          </Suspense>
        </CommunityLayout>
      </SimpleErrorBoundary>
    </CommunityAuthProvider>
  )
}