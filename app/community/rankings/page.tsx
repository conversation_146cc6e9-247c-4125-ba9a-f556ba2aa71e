/**
 * Community Rankings Page
 * 
 * Leaderboards and user rankings for the community.
 * 
 * <AUTHOR> Team
 */

import { Suspense } from 'react'
import { Metadata } from 'next'
import SimpleErrorBoundary from "@/components/error/SimpleErrorBoundary"
import RankingsTab from '@/components/community/tabs/rankings/RankingsTab'
import { CommunityAuthProvider } from '@/contexts/CommunityAuthContext'
import CommunityLayout from '@/components/community/CommunityLayout'

export const metadata: Metadata = {
  title: 'Rankings - Syndicaps Community',
  description: 'View community leaderboards and user rankings',
  keywords: ['community', 'rankings', 'leaderboard', 'competition', 'syndicaps'],
  openGraph: {
    title: 'Rankings - Syndicaps Community',
    description: 'View community leaderboards and user rankings',
    url: '/community/rankings',
    type: 'website',
  },
}

export default function CommunityRankingsPage() {
  return (
    <CommunityAuthProvider>
      <SimpleErrorBoundary>
        <CommunityLayout>
          <Suspense fallback={
            <div className="animate-pulse space-y-6">
              <div className="h-12 bg-gray-800 rounded-lg"></div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="h-32 bg-gray-800 rounded-lg"></div>
                <div className="h-32 bg-gray-800 rounded-lg"></div>
                <div className="h-32 bg-gray-800 rounded-lg"></div>
              </div>
              <div className="h-96 bg-gray-800 rounded-lg"></div>
            </div>
          }>
            <RankingsTab />
          </Suspense>
        </CommunityLayout>
      </SimpleErrorBoundary>
    </CommunityAuthProvider>
  )
}