/**
 * Community Submission Upload Page
 * 
 * Page for uploading new community submissions including artwork,
 * builds, photos, and other creative content.
 * 
 * <AUTHOR> Team
 */

import { Suspense } from 'react'
import { Metadata } from 'next'
import SimpleErrorBoundary from "@/components/error/SimpleErrorBoundary"
import SubmissionUpload from '@/components/community/submissions/SubmissionUpload'
import { CommunityAuthProvider } from '@/contexts/CommunityAuthContext'
import CommunityLayout from '@/components/community/CommunityLayout'

export const metadata: Metadata = {
  title: 'Upload Submission - Syndicaps Community',
  description: 'Share your creative work with the Syndicaps community',
  keywords: ['upload', 'submit', 'community', 'artwork', 'keycaps', 'syndicaps'],
  openGraph: {
    title: 'Upload Submission - Syndicaps Community',
    description: 'Share your creative work with the community',
    url: '/community/submissions/upload',
    type: 'website',
  },
}

export default function SubmissionUploadPage() {
  return (
    <CommunityAuthProvider>
      <SimpleErrorBoundary>
        <CommunityLayout>
          <Suspense fallback={
            <div className="animate-pulse space-y-6 p-6">
              <div className="h-8 bg-gray-800 rounded-lg w-1/3"></div>
              <div className="h-64 bg-gray-800 rounded-lg"></div>
              <div className="h-12 bg-gray-800 rounded-lg w-1/4"></div>
            </div>
          }>
            <SubmissionUpload />
          </Suspense>
        </CommunityLayout>
      </SimpleErrorBoundary>
    </CommunityAuthProvider>
  )
}