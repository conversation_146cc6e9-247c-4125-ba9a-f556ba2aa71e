'use client'

/**
 * Contact Information Page Redirect
 * 
 * Redirects users from the legacy contact information page to the
 * unified profile editor with the contact tab active.
 * 
 * This maintains backward compatibility while consolidating the
 * profile management experience.
 * 
 * <AUTHOR> Team
 */

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function ContactRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to unified profile editor with contact tab
    router.replace('/profile/edit#contact')
  }, [router])

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Redirecting to unified profile editor...</p>
      </div>
    </div>
  )
}
