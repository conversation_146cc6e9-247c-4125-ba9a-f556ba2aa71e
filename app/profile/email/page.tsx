/**
 * Email Management Page - DEPRECATED
 * 
 * This page has been consolidated into the unified profile editor.
 * Users are automatically redirected to /profile/edit#contact
 * 
 * @deprecated Use /profile/edit instead
 * <AUTHOR> Team
 */

'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function EmailPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to unified profile editor with contact tab
    router.replace('/profile/edit#contact')
  }, [router])

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Redirecting to unified profile editor...</p>
      </div>
    </div>
  )
}
