/**
 * Rewards Page
 * 
 * Main rewards interface where users can browse and purchase rewards
 * using their earned points. Now separate from the shop.
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import SimpleRewardShop from '@/components/gamification/SimpleRewardShop'

/**
 * Rewards Page Component
 */
export default function RewardsPage() {
  return (
    <div className="min-h-screen bg-gray-950">
      <SimpleRewardShop className="p-8" />
    </div>
  )
}