/**
 * Level System Test Environment
 * 
 * Comprehensive simulation and testing environment for the user level/ranking system.
 * Includes interactive demos, simulation controls, and visual testing.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Play,
  RotateCcw,
  Settings,
  Users,
  Trophy,
  Zap,
  Gift,
  TestTube,
  Monitor,
  Sparkles,
  ChevronRight,
  Info
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  LevelBadge,
  LevelProgressBar,
  XPGainNotification,
  useXPNotificationQueue
} from '@/components/level'
import { 
  mockUsers, 
  mockLevelRewards, 
  simulationScenarios,
  generateRandomXP
} from '@/lib/testing/mockLevelData'
import { UserProfileWithLevel } from '@/types/levelProfile'
import { XPTransaction } from '@/lib/levelSystem'

// ===== MAIN COMPONENT =====

export default function LevelSystemTestPage() {
  const [selectedUser, setSelectedUser] = useState<UserProfileWithLevel>(mockUsers[1])
  const [selectedScenario, setSelectedScenario] = useState('levelUpReady')
  const [testResults, setTestResults] = useState<string[]>([])
  
  // Notification system
  const { currentNotification, addNotification, dismissCurrent } = useXPNotificationQueue()

  // Add test result
  const addTestResult = (result: string) => {
    setTestResults(prev => [`${new Date().toLocaleTimeString()}: ${result}`, ...prev.slice(0, 9)])
  }

  // Simulate XP gain
  const simulateXPGain = (source: XPTransaction['source'], amount?: number) => {
    const xpAmount = amount || generateRandomXP(10, 200)

    // Show notification
    addNotification({
      xpAmount,
      source,
      description: `Test ${source} XP gain`,
      duration: 3000
    })

    // Update user XP (simulation)
    const newTotalXP = selectedUser.level.totalXP + xpAmount
    const newCurrentXP = selectedUser.level.currentXP + xpAmount
    const nextLevelXP = selectedUser.level.nextLevelXP

    // Calculate new progress percentage
    const newProgressToNext = Math.min(100, (newCurrentXP / nextLevelXP) * 100)

    // Check for level up
    let newLevel = selectedUser.level.current
    let newLevelName = selectedUser.level.levelName
    let newTier = selectedUser.level.tier
    let adjustedCurrentXP = newCurrentXP

    if (newCurrentXP >= nextLevelXP) {
      newLevel = selectedUser.level.current + 1
      adjustedCurrentXP = newCurrentXP - nextLevelXP

      // Update level name and tier based on new level
      if (newLevel <= 10) {
        newTier = 'novice'
        newLevelName = `Level ${newLevel} Novice`
      } else if (newLevel <= 25) {
        newTier = 'intermediate'
        newLevelName = `Level ${newLevel} Intermediate`
      } else if (newLevel <= 40) {
        newTier = 'advanced'
        newLevelName = `Level ${newLevel} Advanced`
      } else {
        newTier = 'expert'
        newLevelName = `Level ${newLevel} Expert`
      }
    }

    // Update the selected user state
    setSelectedUser(prev => ({
      ...prev,
      level: {
        ...prev.level,
        current: newLevel,
        currentXP: adjustedCurrentXP,
        totalXP: newTotalXP,
        levelName: newLevelName,
        tier: newTier,
        progressToNext: newLevel === selectedUser.level.current ? newProgressToNext : (adjustedCurrentXP / nextLevelXP) * 100
      }
    }))

    addTestResult(`+${xpAmount} XP from ${source}${newLevel > selectedUser.level.current ? ` → Level ${newLevel}!` : ''}`)
  }

  // Load scenario
  const loadScenario = (scenarioKey: string) => {
    const scenario = simulationScenarios[scenarioKey as keyof typeof simulationScenarios]
    if (scenario) {
      setSelectedUser(scenario.user)
      setSelectedScenario(scenarioKey)
      addTestResult(`Loaded scenario: ${scenario.name}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white p-6">
      {/* Header */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center gap-3">
              <TestTube className="w-8 h-8 text-purple-400" />
              Level System Test Environment
            </h1>
            <p className="text-gray-400 mt-2">
              Comprehensive simulation and testing for the Syndicaps level/ranking system
            </p>
          </div>
          
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => setTestResults([])}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Clear Results
            </Button>
            <Badge className="bg-green-500/20 text-green-400">
              Test Environment
            </Badge>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-gray-900">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="components">Components</TabsTrigger>
            <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
            <TabsTrigger value="integration">Integration</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Current User */}
              <Card className="lg:col-span-2 bg-gray-900/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-blue-400" />
                    Current Test User
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-white font-semibold">{selectedUser.displayName}</h3>
                      <p className="text-gray-400 text-sm">{selectedUser.level.levelName}</p>
                    </div>
                    <LevelBadge
                      level={selectedUser.level.current}
                      levelName={selectedUser.level.levelName}
                      tier={selectedUser.level.tier}
                      size="lg"
                      showName={true}
                      glow={true}
                    />
                  </div>

                  <LevelProgressBar
                    currentXP={selectedUser.level.currentXP}
                    nextLevelXP={selectedUser.level.nextLevelXP}
                    progressPercentage={selectedUser.level.progressToNext}
                    currentLevel={selectedUser.level.current}
                    nextLevel={selectedUser.level.current + 1}
                    tier={selectedUser.level.tier}
                    showXP={true}
                    showPercentage={true}
                    animated={true}
                  />
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="bg-gray-900/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="w-5 h-5 text-green-400" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    onClick={() => simulateXPGain('purchase', 150)}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    <Zap className="w-4 h-4 mr-2" />
                    Purchase XP
                  </Button>
                  <Button 
                    onClick={() => simulateXPGain('activity', 25)}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    <Trophy className="w-4 h-4 mr-2" />
                    Activity XP
                  </Button>
                  <Button 
                    onClick={() => simulateXPGain('bonus', 100)}
                    className="w-full bg-purple-600 hover:bg-purple-700"
                  >
                    <Gift className="w-4 h-4 mr-2" />
                    Bonus XP
                  </Button>
                  <Button 
                    onClick={() => simulateXPGain('event', 300)}
                    className="w-full bg-yellow-600 hover:bg-yellow-700"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Event XP
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Test Results */}
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="w-5 h-5 text-orange-400" />
                  Test Results Log
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {testResults.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">
                      No test results yet. Try some actions above!
                    </p>
                  ) : (
                    testResults.map((result, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        className="text-sm text-gray-300 font-mono bg-gray-800/50 p-2 rounded"
                      >
                        {result}
                      </motion.div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Components Tab */}
          <TabsContent value="components" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Level Badges */}
              <Card className="bg-gray-900/50 border-gray-700">
                <CardHeader>
                  <CardTitle>Level Badges</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-400 w-16">XS:</span>
                      <LevelBadge level={15} levelName="Artisan Admirer" tier="intermediate" size="xs" />
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-400 w-16">SM:</span>
                      <LevelBadge level={15} levelName="Artisan Admirer" tier="intermediate" size="sm" />
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-400 w-16">MD:</span>
                      <LevelBadge level={15} levelName="Artisan Admirer" tier="intermediate" size="md" />
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-400 w-16">LG:</span>
                      <LevelBadge level={15} levelName="Artisan Admirer" tier="intermediate" size="lg" showName={true} />
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-400 w-16">XL:</span>
                      <LevelBadge level={50} levelName="Syndicaps Legend" tier="expert" size="xl" showName={true} glow={true} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Progress Bars */}
              <Card className="bg-gray-900/50 border-gray-700">
                <CardHeader>
                  <CardTitle>Progress Bars</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <p className="text-sm text-gray-400 mb-2">Linear Progress</p>
                    <LevelProgressBar
                      currentXP={750}
                      nextLevelXP={1000}
                      progressPercentage={75}
                      currentLevel={15}
                      nextLevel={16}
                      tier="intermediate"
                      showXP={true}
                    />
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-400 mb-2">Compact Progress</p>
                    <LevelProgressBar
                      currentXP={200}
                      nextLevelXP={300}
                      progressPercentage={67}
                      currentLevel={8}
                      tier="novice"
                      variant="compact"
                      showXP={true}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Scenarios Tab */}
          <TabsContent value="scenarios" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Object.entries(simulationScenarios).map(([key, scenario]) => (
                <Card 
                  key={key}
                  className={`bg-gray-900/50 border-gray-700 cursor-pointer transition-all hover:border-purple-500/50 ${
                    selectedScenario === key ? 'border-purple-500 bg-purple-500/10' : ''
                  }`}
                  onClick={() => loadScenario(key)}
                >
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-white mb-2">{scenario.name}</h3>
                    <p className="text-sm text-gray-400 mb-3">{scenario.description}</p>
                    <div className="flex items-center justify-between">
                      <LevelBadge 
                        level={scenario.user.level.current}
                        levelName={scenario.user.level.levelName}
                        tier={scenario.user.level.tier}
                        size="sm"
                      />
                      <ChevronRight className="w-4 h-4 text-gray-500" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Integration Tab */}
          <TabsContent value="integration" className="space-y-6">
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="w-5 h-5 text-blue-400" />
                  Integration Testing
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-green-500/10 border border-green-500/30 rounded">
                    <h4 className="text-green-400 font-semibold mb-2">✅ Level System Components</h4>
                    <p className="text-gray-300 text-sm">
                      All level system components are working correctly and displaying proper styling.
                    </p>
                  </div>
                  
                  <div className="p-4 bg-blue-500/10 border border-blue-500/30 rounded">
                    <h4 className="text-blue-400 font-semibold mb-2">ℹ️ Test Environment Ready</h4>
                    <p className="text-gray-300 text-sm">
                      The test environment is successfully running and ready for comprehensive testing.
                    </p>
                  </div>
                  
                  <div className="p-4 bg-purple-500/10 border border-purple-500/30 rounded">
                    <h4 className="text-purple-400 font-semibold mb-2">🎮 Interactive Testing</h4>
                    <p className="text-gray-300 text-sm">
                      Use the Quick Actions above to test XP notifications and level progression.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Notifications */}
      {currentNotification && (
        <XPGainNotification
          {...currentNotification}
          isVisible={true}
          onDismiss={dismissCurrent}
        />
      )}
    </div>
  )
}
