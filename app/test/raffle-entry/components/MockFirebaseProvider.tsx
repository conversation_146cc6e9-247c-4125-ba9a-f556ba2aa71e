/**
 * Mock Firebase Provider for Testing
 * 
 * Provides mock Firebase functionality to avoid CSP issues and external dependencies during testing
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'

// Mock Firebase types
interface MockUser {
  uid: string
  email: string
  displayName: string
}

interface MockProfile {
  discordLinked: boolean
  followInstagram: boolean
  joinTelegram: boolean
  subscribeNewsletter: boolean
}

interface MockFirebaseContext {
  user: MockUser | null
  profile: MockProfile | null
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<MockProfile>) => Promise<void>
}

// Create context
const MockFirebaseContext = createContext<MockFirebaseContext | null>(null)

/**
 * Mock Firebase Provider Component
 */
export const MockFirebaseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<MockUser | null>(null)
  const [profile, setProfile] = useState<MockProfile | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  /**
   * Initialize mock user on mount
   */
  useEffect(() => {
    // Simulate loading
    setIsLoading(true)

    // Reduced timeout for faster test loading
    setTimeout(() => {
      // Set mock user
      setUser({
        uid: 'test-user-123',
        email: '<EMAIL>',
        displayName: 'Test User'
      })

      setProfile({
        discordLinked: false,
        followInstagram: false,
        joinTelegram: false,
        subscribeNewsletter: false
      })

      setIsLoading(false)
      console.log('[MockFirebase] User initialized for testing')
    }, 100) // Reduced from 1000ms to 100ms for faster loading
  }, [])

  /**
   * Mock sign in
   */
  const signIn = async (email: string, password: string) => {
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setUser({
      uid: 'test-user-123',
      email,
      displayName: 'Test User'
    })
    
    setIsLoading(false)
    toast.success('Signed in successfully (mock)')
  }

  /**
   * Mock sign out
   */
  const signOut = async () => {
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    setUser(null)
    setProfile(null)
    setIsLoading(false)
    toast.success('Signed out successfully (mock)')
  }

  /**
   * Mock profile update
   */
  const updateProfile = async (updates: Partial<MockProfile>) => {
    if (!profile) return
    
    setProfile(prev => ({ ...prev!, ...updates }))
    toast.success('Profile updated successfully (mock)')
    console.log('[MockFirebase] Profile updated:', updates)
  }

  const value: MockFirebaseContext = {
    user,
    profile,
    isLoading,
    signIn,
    signOut,
    updateProfile
  }

  return (
    <MockFirebaseContext.Provider value={value}>
      {children}
    </MockFirebaseContext.Provider>
  )
}

/**
 * Hook to use mock Firebase context
 */
export const useMockFirebase = () => {
  const context = useContext(MockFirebaseContext)
  if (!context) {
    throw new Error('useMockFirebase must be used within MockFirebaseProvider')
  }
  return context
}

/**
 * Mock Firestore functions for testing
 */
export const mockFirestore = {
  /**
   * Mock create raffle entry
   */
  createRaffleEntry: async (entryData: any) => {
    console.log('[MockFirestore] Creating raffle entry:', entryData)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Simulate random success/failure for testing
    const success = Math.random() > 0.1 // 90% success rate
    
    if (success) {
      const entryId = `entry-${Date.now()}`
      console.log('[MockFirestore] Raffle entry created:', entryId)
      return entryId
    } else {
      throw new Error('Mock submission failure for testing')
    }
  },

  /**
   * Mock get active raffles
   */
  getActiveRaffles: async () => {
    console.log('[MockFirestore] Getting active raffles')
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800))
    
    return [
      {
        id: 'raffle-1',
        name: 'Test Raffle',
        description: 'Mock raffle for testing',
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        products: [] // Will be populated by mock products
      }
    ]
  },

  /**
   * Mock get user shipping addresses
   */
  getUserShippingAddresses: async (userId: string) => {
    console.log('[MockFirestore] Getting shipping addresses for:', userId)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600))
    
    // Return mock addresses based on test scenario
    return []
  },

  /**
   * Mock create shipping address
   */
  createShippingAddress: async (userId: string, addressData: any) => {
    console.log('[MockFirestore] Creating shipping address:', addressData)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const addressId = `addr-${Date.now()}`
    console.log('[MockFirestore] Shipping address created:', addressId)
    return addressId
  },

  /**
   * Mock check raffle entry eligibility
   */
  checkRaffleEntryEligibility: async (userId: string, raffleId: string) => {
    console.log('[MockFirestore] Checking eligibility:', { userId, raffleId })
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400))
    
    // Always return true for testing
    return true
  }
}

/**
 * Mock performance utilities to replace the real ones during testing
 */
export const mockPerformanceUtils = {
  /**
   * Mock performance measurement
   */
  measurePerformance: (name: string, fn: () => void) => {
    const start = performance.now()
    fn()
    const end = performance.now()
    const duration = end - start
    
    console.log(`[MockPerformance] ${name}: ${duration.toFixed(2)}ms`)
    return duration
  },

  /**
   * Mock web vitals
   */
  reportWebVitals: (metric: any) => {
    const { name, value, rating } = metric
    console.log(`[MockPerformance] ${name}: ${value}ms (${rating})`)
  }
}

/**
 * Test environment detector
 */
export const isTestEnvironment = () => {
  return typeof window !== 'undefined' && window.location.pathname.includes('/test/')
}

/**
 * Conditional Firebase provider that uses mock in test environment
 */
export const ConditionalFirebaseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  if (isTestEnvironment()) {
    return <MockFirebaseProvider>{children}</MockFirebaseProvider>
  }
  
  // In production, would return the real Firebase provider
  return <>{children}</>
}
