/**
 * Individual Step Testing Component
 * 
 * Allows testing each step component in isolation with different states
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Play, RotateCcw, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'

// Import step components
import {
  RequirementsStep,
  ProductsStep,
  AddressStep,
  InformationStep,
  ReviewStep,
  SuccessStep
} from '@/components/raffle/RaffleEntry'

// Import types and mock data
import { 
  RaffleFormState, 
  INITIAL_FORM_STATE,
  DEFAULT_SHIPPING_METHODS 
} from '@/components/raffle/RaffleEntry/types/raffleTypes'
import { mockProducts, mockAddresses, validationScenarios } from './TestUtils'

/**
 * Step test configuration
 */
interface StepTestConfig {
  id: string
  name: string
  component: React.ComponentType<any>
  description: string
  testScenarios: {
    valid: any
    invalid: any
    edge: any
  }
}

/**
 * Step configurations
 */
const STEP_CONFIGS: StepTestConfig[] = [
  {
    id: 'requirements',
    name: 'Requirements Step',
    component: RequirementsStep,
    description: 'Tests Discord verification and requirement completion',
    testScenarios: {
      valid: { raffleRequirements: validationScenarios.requirements.valid },
      invalid: { raffleRequirements: validationScenarios.requirements.invalid },
      edge: { raffleRequirements: { discordLinked: true, followInstagram: true, joinTelegram: true, subscribeNewsletter: true } }
    }
  },
  {
    id: 'products',
    name: 'Products Step',
    component: ProductsStep,
    description: 'Tests product variant selection and validation',
    testScenarios: {
      valid: { selectedVariants: validationScenarios.products.valid },
      invalid: { selectedVariants: validationScenarios.products.invalid },
      edge: { selectedVariants: ['variant-1', 'variant-2', 'variant-3', 'variant-4', 'variant-5'] }
    }
  },
  {
    id: 'address',
    name: 'Address Step',
    component: AddressStep,
    description: 'Tests new address form and validation',
    testScenarios: {
      valid: { newAddress: validationScenarios.address.valid },
      invalid: { newAddress: validationScenarios.address.invalid },
      edge: { newAddress: { ...validationScenarios.address.valid, country: 'JP' } }
    }
  },
  {
    id: 'information',
    name: 'Information Step',
    component: InformationStep,
    description: 'Tests address selection and shipping method choice',
    testScenarios: {
      valid: { selectedAddress: 'addr-1', shippingMethod: 'express' },
      invalid: { selectedAddress: '', shippingMethod: '' },
      edge: { selectedAddress: 'addr-3', shippingMethod: 'economy' }
    }
  },
  {
    id: 'review',
    name: 'Review Step',
    component: ReviewStep,
    description: 'Tests complete entry review and validation',
    testScenarios: {
      valid: {
        selectedVariants: ['variant-1', 'variant-2'],
        selectedAddress: 'addr-1',
        shippingMethod: 'express',
        raffleRequirements: validationScenarios.requirements.valid
      },
      invalid: {
        selectedVariants: [],
        selectedAddress: '',
        shippingMethod: '',
        raffleRequirements: validationScenarios.requirements.invalid
      },
      edge: {
        selectedVariants: ['variant-1', 'variant-4', 'variant-7', 'variant-9'],
        selectedAddress: 'addr-2',
        shippingMethod: 'economy',
        raffleRequirements: { discordLinked: true, followInstagram: true, joinTelegram: true, subscribeNewsletter: true }
      }
    }
  },
  {
    id: 'success',
    name: 'Success Step',
    component: SuccessStep,
    description: 'Tests success confirmation and next actions',
    testScenarios: {
      valid: { selectedVariants: ['variant-1', 'variant-2'] },
      invalid: { selectedVariants: [] },
      edge: { selectedVariants: ['variant-1', 'variant-2', 'variant-3', 'variant-4', 'variant-5'] }
    }
  }
]

/**
 * Individual Step Tester Component
 */
export const StepTester: React.FC = () => {
  const [selectedStep, setSelectedStep] = useState<StepTestConfig>(STEP_CONFIGS[0])
  const [currentScenario, setCurrentScenario] = useState<'valid' | 'invalid' | 'edge'>('valid')
  const [formState, setFormState] = useState<RaffleFormState>(INITIAL_FORM_STATE)
  const [testResults, setTestResults] = useState<{
    passed: number
    failed: number
    warnings: number
  }>({ passed: 0, failed: 0, warnings: 0 })

  /**
   * Update form state
   */
  const updateFormState = (updates: Partial<RaffleFormState>) => {
    setFormState(prev => ({ ...prev, ...updates }))
  }

  /**
   * Load test scenario
   */
  const loadScenario = (scenario: 'valid' | 'invalid' | 'edge') => {
    setCurrentScenario(scenario)
    const scenarioData = selectedStep.testScenarios[scenario]
    
    // Reset form state and apply scenario data
    const newFormState = {
      ...INITIAL_FORM_STATE,
      currentStep: selectedStep.id as any,
      ...scenarioData
    }
    
    setFormState(newFormState)
    
    // Log the scenario load
    console.log(`Loaded ${scenario} scenario for ${selectedStep.name}:`, scenarioData)
  }

  /**
   * Run validation test
   */
  const runValidationTest = () => {
    // This would typically call the validation function from the hook
    // For now, we'll simulate validation results
    const isValid = currentScenario === 'valid'
    const hasWarnings = currentScenario === 'edge'
    
    setTestResults(prev => ({
      passed: prev.passed + (isValid ? 1 : 0),
      failed: prev.failed + (!isValid ? 1 : 0),
      warnings: prev.warnings + (hasWarnings ? 1 : 0)
    }))
    
    console.log(`Validation test result: ${isValid ? 'PASSED' : 'FAILED'}`)
  }

  /**
   * Reset test results
   */
  const resetResults = () => {
    setTestResults({ passed: 0, failed: 0, warnings: 0 })
    setFormState(INITIAL_FORM_STATE)
  }

  /**
   * Render current step component
   */
  const renderStepComponent = () => {
    const StepComponent = selectedStep.component
    
    const stepProps = {
      formState,
      onUpdateFormState: updateFormState,
      onNext: () => console.log('Next step triggered'),
      onPrev: () => console.log('Previous step triggered'),
      products: mockProducts,
      addresses: mockAddresses,
      shippingMethods: DEFAULT_SHIPPING_METHODS
    }

    return <StepComponent {...stepProps} />
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Step Component Tester</h1>
          <p className="text-gray-400">Test individual step components in isolation with different scenarios</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          
          {/* Step Selection */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-6">
              <h2 className="text-lg font-semibold text-white mb-4">Select Step</h2>
              
              <div className="space-y-2">
                {STEP_CONFIGS.map(step => (
                  <button
                    key={step.id}
                    onClick={() => setSelectedStep(step)}
                    className={`w-full p-3 rounded-lg text-left transition-all ${
                      selectedStep.id === step.id
                        ? 'bg-accent-600/20 border border-accent-500'
                        : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <div className="font-medium text-white">{step.name}</div>
                    <div className="text-sm text-gray-400 mt-1">{step.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Test Controls */}
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-6">
              <h2 className="text-lg font-semibold text-white mb-4">Test Scenarios</h2>
              
              <div className="space-y-3">
                {(['valid', 'invalid', 'edge'] as const).map(scenario => (
                  <button
                    key={scenario}
                    onClick={() => loadScenario(scenario)}
                    className={`w-full p-3 rounded-lg text-left transition-all ${
                      currentScenario === scenario
                        ? 'bg-green-600/20 border border-green-500'
                        : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {scenario === 'valid' && <CheckCircle size={16} className="text-green-400" />}
                      {scenario === 'invalid' && <XCircle size={16} className="text-red-400" />}
                      {scenario === 'edge' && <AlertTriangle size={16} className="text-yellow-400" />}
                      <span className="font-medium text-white capitalize">{scenario}</span>
                    </div>
                  </button>
                ))}
              </div>
              
              <div className="mt-4 space-y-2">
                <button
                  onClick={runValidationTest}
                  className="w-full px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <Play size={16} />
                  Run Test
                </button>
                
                <button
                  onClick={resetResults}
                  className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <RotateCcw size={16} />
                  Reset
                </button>
              </div>
            </div>

            {/* Test Results */}
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-lg font-semibold text-white mb-4">Test Results</h2>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Passed:</span>
                  <span className="text-green-400 font-bold">{testResults.passed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Failed:</span>
                  <span className="text-red-400 font-bold">{testResults.failed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Warnings:</span>
                  <span className="text-yellow-400 font-bold">{testResults.warnings}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Step Component Display */}
          <div className="lg:col-span-3">
            <div className="bg-gray-800 rounded-lg border border-gray-700">
              
              {/* Component Header */}
              <div className="px-6 py-4 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-white">{selectedStep.name}</h2>
                    <p className="text-gray-400 mt-1">{selectedStep.description}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-400">Scenario:</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      currentScenario === 'valid' ? 'bg-green-600 text-white' :
                      currentScenario === 'invalid' ? 'bg-red-600 text-white' :
                      'bg-yellow-600 text-white'
                    }`}>
                      {currentScenario.toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Component Content */}
              <div className="p-6">
                <motion.div
                  key={`${selectedStep.id}-${currentScenario}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {renderStepComponent()}
                </motion.div>
              </div>
            </div>

            {/* Form State Debug */}
            <div className="mt-6 bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">Form State Debug</h3>
              <pre className="bg-gray-900 rounded p-4 text-sm text-gray-300 overflow-auto max-h-64">
                {JSON.stringify(formState, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
