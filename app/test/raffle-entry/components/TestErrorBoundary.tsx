/**
 * Test Error Boundary Component
 * 
 * Catches and displays errors in the test environment
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class TestErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[TestErrorBoundary] Error caught:', error, errorInfo)
    this.setState({ error, errorInfo })
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center p-6">
          <div className="max-w-2xl w-full">
            <div className="bg-red-500/20 border border-red-500 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertTriangle size={24} className="text-red-400" />
                <h2 className="text-xl font-bold text-red-400">Test Environment Error</h2>
              </div>
              
              <p className="text-gray-300 mb-4">
                An error occurred in the test environment. This is likely due to a component issue or missing dependency.
              </p>
              
              {this.state.error && (
                <div className="mb-4">
                  <h3 className="text-white font-medium mb-2">Error Details:</h3>
                  <div className="bg-gray-800 rounded p-3 font-mono text-sm text-red-300">
                    {this.state.error.message}
                  </div>
                </div>
              )}
              
              {this.state.errorInfo && (
                <div className="mb-6">
                  <h3 className="text-white font-medium mb-2">Component Stack:</h3>
                  <div className="bg-gray-800 rounded p-3 font-mono text-xs text-gray-400 max-h-40 overflow-y-auto">
                    {this.state.errorInfo.componentStack}
                  </div>
                </div>
              )}
              
              <div className="flex gap-3">
                <button
                  onClick={this.handleReset}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  <RefreshCw size={16} />
                  Try Again
                </button>
                
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  Reload Page
                </button>
              </div>
              
              <div className="mt-6 p-4 bg-blue-500/20 border border-blue-500 rounded-lg">
                <h3 className="text-blue-400 font-medium mb-2">Troubleshooting Tips:</h3>
                <ul className="text-blue-300 text-sm space-y-1">
                  <li>• Check the browser console for additional error details</li>
                  <li>• Ensure all required dependencies are installed</li>
                  <li>• Verify that the refactored components are properly exported</li>
                  <li>• Try refreshing the page to clear any cached issues</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default TestErrorBoundary
