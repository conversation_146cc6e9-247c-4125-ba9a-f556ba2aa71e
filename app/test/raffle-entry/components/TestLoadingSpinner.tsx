/**
 * Test Loading Spinner Component
 * 
 * Simple loading spinner for test environment
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'

interface TestLoadingSpinnerProps {
  message?: string
  size?: 'sm' | 'md' | 'lg'
}

export const TestLoadingSpinner: React.FC<TestLoadingSpinnerProps> = ({
  message = 'Loading...',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <div className={`animate-spin rounded-full border-b-2 border-accent-500 ${sizeClasses[size]}`}></div>
      {message && (
        <p className="text-gray-400 mt-4 text-sm">{message}</p>
      )}
    </div>
  )
}

export default TestLoadingSpinner
