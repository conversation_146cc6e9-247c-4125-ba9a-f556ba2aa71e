/**
 * Test Raffle Entry Wrapper
 * 
 * Wrapper component that provides mock data and bypasses loading issues for testing
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, X } from 'lucide-react'
import { toast } from 'react-hot-toast'

// Import the refactored step components directly
import {
  RequirementsStep,
  ProductsStep,
  AddressStep,
  InformationStep,
  ReviewStep,
  SuccessStep
} from '@/components/raffle/RaffleEntry'

// Import types and hooks
import { 
  RaffleFormState, 
  INITIAL_FORM_STATE,
  DEFAULT_SHIPPING_METHODS,
  STEP_TITLES
} from '@/components/raffle/RaffleEntry/types/raffleTypes'

// Import mock data
import { mockProducts, mockAddresses } from './TestUtils'

/**
 * Test wrapper props
 */
interface TestRaffleEntryWrapperProps {
  onClose: () => void
  preSelectedProductId?: string
  scenario?: 'new-user' | 'existing-user' | 'premium-user' | 'error-scenarios'
}

/**
 * Test Raffle Entry Wrapper Component
 */
export const TestRaffleEntryWrapper: React.FC<TestRaffleEntryWrapperProps> = ({
  onClose,
  preSelectedProductId,
  scenario = 'new-user'
}) => {
  const [formState, setFormState] = useState<RaffleFormState>(INITIAL_FORM_STATE)
  const [isSubmitting, setIsSubmitting] = useState(false)

  /**
   * Update form state
   */
  const updateFormState = (updates: Partial<RaffleFormState>) => {
    setFormState(prev => ({ ...prev, ...updates }))
    console.log('[TestWrapper] Form state updated:', updates)
  }

  /**
   * Initialize form based on scenario
   */
  useEffect(() => {
    const initializeScenario = () => {
      console.log('[TestWrapper] Initializing scenario:', scenario)
      
      switch (scenario) {
        case 'existing-user':
          // User with saved addresses
          updateFormState({
            currentStep: 'requirements'
          })
          break
          
        case 'premium-user':
          // Premium user with pre-selected product
          if (preSelectedProductId) {
            updateFormState({
              currentStep: 'requirements',
              selectedVariants: [preSelectedProductId]
            })
          }
          break
          
        case 'error-scenarios':
          // Start with some validation errors
          updateFormState({
            currentStep: 'requirements',
            fieldErrors: {
              requirements: 'Please complete at least one requirement'
            }
          })
          break
          
        default: // new-user
          updateFormState({
            currentStep: 'requirements'
          })
      }
    }

    initializeScenario()
  }, [scenario, preSelectedProductId])

  /**
   * Handle next step
   */
  const handleNext = () => {
    const stepOrder = ['requirements', 'products', 'address', 'information', 'review', 'success']
    const currentIndex = stepOrder.indexOf(formState.currentStep)
    
    if (currentIndex < stepOrder.length - 1) {
      const nextStep = stepOrder[currentIndex + 1]
      
      // Skip address step if user has addresses (go to information)
      if (nextStep === 'address' && scenario === 'existing-user') {
        updateFormState({ currentStep: 'information' })
      } else {
        updateFormState({ currentStep: nextStep as any })
      }
      
      console.log('[TestWrapper] Moving to next step:', nextStep)
    } else if (formState.currentStep === 'review') {
      handleSubmit()
    }
  }

  /**
   * Handle previous step
   */
  const handlePrev = () => {
    const stepOrder = ['requirements', 'products', 'address', 'information', 'review', 'success']
    const currentIndex = stepOrder.indexOf(formState.currentStep)
    
    if (currentIndex > 0) {
      const prevStep = stepOrder[currentIndex - 1]
      
      // Skip address step if user has addresses (go to products)
      if (prevStep === 'address' && scenario === 'existing-user') {
        updateFormState({ currentStep: 'products' })
      } else {
        updateFormState({ currentStep: prevStep as any })
      }
      
      console.log('[TestWrapper] Moving to previous step:', prevStep)
    }
  }

  /**
   * Handle form submission
   */
  const handleSubmit = async () => {
    console.log('[TestWrapper] Submitting form:', formState)
    setIsSubmitting(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Simulate random success/failure for testing
      const success = Math.random() > 0.1 // 90% success rate
      
      if (success) {
        updateFormState({ currentStep: 'success' })
        toast.success('Raffle entry submitted successfully!')
      } else {
        throw new Error('Mock submission failure for testing')
      }
    } catch (error) {
      console.error('[TestWrapper] Submission error:', error)
      toast.error('Failed to submit raffle entry')
    } finally {
      setIsSubmitting(false)
    }
  }

  /**
   * Get step progress
   */
  const getStepProgress = () => {
    const stepOrder = ['requirements', 'products', 'address', 'information', 'review', 'success']
    const currentIndex = stepOrder.indexOf(formState.currentStep)
    return {
      current: currentIndex + 1,
      total: stepOrder.length,
      percentage: ((currentIndex + 1) / stepOrder.length) * 100
    }
  }

  /**
   * Render current step component
   */
  const renderCurrentStep = () => {
    const stepProps = {
      formState,
      onUpdateFormState: updateFormState,
      onNext: handleNext,
      onPrev: handlePrev,
      products: mockProducts,
      addresses: scenario === 'existing-user' ? mockAddresses : [],
      shippingMethods: DEFAULT_SHIPPING_METHODS
    }

    switch (formState.currentStep) {
      case 'requirements':
        return <RequirementsStep {...stepProps} />
      case 'products':
        return <ProductsStep {...stepProps} />
      case 'address':
        return <AddressStep {...stepProps} />
      case 'information':
        return <InformationStep {...stepProps} />
      case 'review':
        return <ReviewStep {...stepProps} />
      case 'success':
        return <SuccessStep {...stepProps} />
      default:
        return null
    }
  }

  const progress = getStepProgress()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-white">
                {STEP_TITLES[formState.currentStep]} (Test Mode)
              </h2>
              {formState.currentStep !== 'success' && (
                <div className="flex items-center gap-2 mt-2">
                  <div className="w-48 bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-accent-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress.percentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-400">
                    {progress.current} of {progress.total}
                  </span>
                </div>
              )}
            </div>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6 max-h-[calc(90vh-200px)] overflow-y-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={formState.currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {renderCurrentStep()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        {formState.currentStep !== 'success' && (
          <div className="px-6 py-4 bg-gray-800 border-t border-gray-700">
            <div className="flex items-center justify-between">
              {formState.currentStep !== 'requirements' ? (
                <button
                  onClick={handlePrev}
                  className="flex items-center gap-2 px-4 py-2 text-gray-300 hover:text-white transition-colors"
                >
                  <ChevronLeft size={20} />
                  Previous
                </button>
              ) : (
                <div />
              )}

              <button
                onClick={handleNext}
                disabled={isSubmitting}
                className="flex items-center gap-2 px-6 py-2 bg-accent-600 hover:bg-accent-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Submitting...
                  </>
                ) : formState.currentStep === 'review' ? (
                  'SUBMIT ENTRY'
                ) : (
                  <>
                    NEXT STEP
                    <ChevronRight size={20} />
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default TestRaffleEntryWrapper
