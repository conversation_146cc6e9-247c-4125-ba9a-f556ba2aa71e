/**
 * Test Utilities and Mock Data
 * 
 * Comprehensive mock data and testing utilities for the raffle entry system
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { Product, ShippingAddress } from '@/components/raffle/RaffleEntry/types/raffleTypes'

/**
 * Mock Products Data
 */
export const mockProducts: Product[] = [
  {
    id: 'product-1',
    name: 'Artisan Keycap Set - Ocean Wave',
    price: 89.99,
    image: '/images/keycaps/ocean-wave.jpg',
    category: 'artisan',
    isRaffle: true,
    soldOut: false,
    variants: [
      {
        id: 'variant-1',
        name: 'Cherry MX Compatible',
        price: 89.99,
        image: '/images/keycaps/ocean-wave-cherry.jpg',
        stock: 50,
        description: 'Compatible with Cherry MX switches'
      },
      {
        id: 'variant-2',
        name: 'Topre Compatible',
        price: 94.99,
        image: '/images/keycaps/ocean-wave-topre.jpg',
        stock: 30,
        description: 'Compatible with Topre switches'
      },
      {
        id: 'variant-3',
        name: 'Alps Compatible',
        price: 92.99,
        image: '/images/keycaps/ocean-wave-alps.jpg',
        stock: 20,
        description: 'Compatible with Alps switches'
      }
    ]
  },
  {
    id: 'product-2',
    name: 'Limited Edition - Cyberpunk 2077',
    price: 149.99,
    image: '/images/keycaps/cyberpunk.jpg',
    category: 'limited',
    isRaffle: true,
    soldOut: false,
    variants: [
      {
        id: 'variant-4',
        name: 'Neon Blue',
        price: 149.99,
        image: '/images/keycaps/cyberpunk-blue.jpg',
        stock: 25,
        description: 'Neon blue with RGB underglow'
      },
      {
        id: 'variant-5',
        name: 'Neon Pink',
        price: 149.99,
        image: '/images/keycaps/cyberpunk-pink.jpg',
        stock: 25,
        description: 'Neon pink with RGB underglow'
      },
      {
        id: 'variant-6',
        name: 'Holographic',
        price: 179.99,
        image: '/images/keycaps/cyberpunk-holo.jpg',
        stock: 10,
        description: 'Holographic finish with color-changing effect'
      }
    ]
  },
  {
    id: 'product-3',
    name: 'Minimalist White Set',
    price: 59.99,
    image: '/images/keycaps/minimalist-white.jpg',
    category: 'standard',
    isRaffle: true,
    soldOut: false,
    variants: [
      {
        id: 'variant-7',
        name: 'Standard Profile',
        price: 59.99,
        image: '/images/keycaps/minimalist-white-standard.jpg',
        stock: 100,
        description: 'OEM profile keycaps'
      },
      {
        id: 'variant-8',
        name: 'Low Profile',
        price: 64.99,
        image: '/images/keycaps/minimalist-white-low.jpg',
        stock: 75,
        description: 'Low profile keycaps'
      }
    ]
  },
  {
    id: 'product-4',
    name: 'Retro Gaming Collection',
    price: 119.99,
    image: '/images/keycaps/retro-gaming.jpg',
    category: 'themed',
    isRaffle: true,
    soldOut: false,
    variants: [
      {
        id: 'variant-9',
        name: '8-Bit Style',
        price: 119.99,
        image: '/images/keycaps/retro-8bit.jpg',
        stock: 40,
        description: 'Classic 8-bit gaming inspired design'
      },
      {
        id: 'variant-10',
        name: '16-Bit Style',
        price: 129.99,
        image: '/images/keycaps/retro-16bit.jpg',
        stock: 35,
        description: 'Enhanced 16-bit gaming inspired design'
      },
      {
        id: 'variant-11',
        name: 'Arcade Style',
        price: 139.99,
        image: '/images/keycaps/retro-arcade.jpg',
        stock: 15,
        description: 'Vintage arcade machine inspired design'
      }
    ]
  }
]

/**
 * Mock Shipping Addresses
 */
export const mockAddresses: ShippingAddress[] = [
  {
    id: 'addr-1',
    name: 'John Doe',
    address: '123 Main Street, Apt 4B',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    country: 'US',
    phone: '+****************',
    isDefault: true
  },
  {
    id: 'addr-2',
    name: 'John Doe',
    address: '456 Work Plaza, Suite 200',
    city: 'New York',
    state: 'NY',
    zipCode: '10002',
    country: 'US',
    phone: '+****************',
    isDefault: false
  },
  {
    id: 'addr-3',
    name: 'Jane Smith',
    address: '789 Family Lane',
    city: 'Brooklyn',
    state: 'NY',
    zipCode: '11201',
    country: 'US',
    phone: '+****************',
    isDefault: false
  }
]

/**
 * Mock User Data
 */
export const mockUser = {
  uid: 'test-user-123',
  email: '<EMAIL>',
  displayName: 'Test User',
  profile: {
    discordLinked: false,
    followInstagram: false,
    joinTelegram: false,
    subscribeNewsletter: false
  }
}

/**
 * Test Control Panel Component
 */
export const TestControlPanel: React.FC<{
  onStepChange: (step: string) => void
  onUserStateChange: (state: 'new' | 'existing' | 'premium') => void
  onAddressToggle: (hasAddresses: boolean) => void
  currentStep: string
  userState: string
  hasAddresses: boolean
}> = ({ 
  onStepChange, 
  onUserStateChange, 
  onAddressToggle, 
  currentStep, 
  userState, 
  hasAddresses 
}) => {
  const steps = [
    'requirements',
    'products', 
    'address',
    'information',
    'review',
    'success'
  ]

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
      <h3 className="text-white font-medium mb-4">Test Controls</h3>
      
      {/* Step Navigation */}
      <div className="mb-4">
        <label className="block text-sm text-gray-400 mb-2">Jump to Step:</label>
        <select
          value={currentStep}
          onChange={(e) => onStepChange(e.target.value)}
          className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
        >
          {steps.map(step => (
            <option key={step} value={step}>
              {step.charAt(0).toUpperCase() + step.slice(1)}
            </option>
          ))}
        </select>
      </div>

      {/* User State */}
      <div className="mb-4">
        <label className="block text-sm text-gray-400 mb-2">User State:</label>
        <select
          value={userState}
          onChange={(e) => onUserStateChange(e.target.value as any)}
          className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
        >
          <option value="new">New User</option>
          <option value="existing">Existing User</option>
          <option value="premium">Premium User</option>
        </select>
      </div>

      {/* Address Toggle */}
      <div className="mb-4">
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={hasAddresses}
            onChange={(e) => onAddressToggle(e.target.checked)}
            className="w-4 h-4 text-accent-600 bg-gray-700 border-gray-600 rounded"
          />
          <span className="text-sm text-gray-400">User has saved addresses</span>
        </label>
      </div>
    </div>
  )
}

/**
 * Test Metrics Component
 */
export const TestMetrics: React.FC<{
  metrics: {
    startTime: number
    stepTimes: Record<string, number>
    errors: string[]
    warnings: string[]
  }
}> = ({ metrics }) => {
  const duration = metrics.startTime ? Date.now() - metrics.startTime : 0

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
      <h3 className="text-white font-medium mb-4">Performance Metrics</h3>
      
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-gray-400">Total Duration:</span>
          <span className="text-white">{Math.floor(duration / 1000)}s</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Errors:</span>
          <span className={`${metrics.errors.length > 0 ? 'text-red-400' : 'text-green-400'}`}>
            {metrics.errors.length}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Warnings:</span>
          <span className={`${metrics.warnings.length > 0 ? 'text-yellow-400' : 'text-green-400'}`}>
            {metrics.warnings.length}
          </span>
        </div>
        
        {Object.keys(metrics.stepTimes).length > 0 && (
          <div>
            <div className="text-gray-400 text-sm mb-2">Step Times:</div>
            {Object.entries(metrics.stepTimes).map(([step, time]) => (
              <div key={step} className="flex justify-between text-sm">
                <span className="text-gray-500">{step}:</span>
                <span className="text-gray-300">{time}ms</span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * Test Logger Component
 */
export const TestLogger: React.FC<{
  logs: string[]
  onClear: () => void
}> = ({ logs, onClear }) => {
  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white font-medium">Console Logs</h3>
        <button
          onClick={onClear}
          className="text-sm text-gray-400 hover:text-white"
        >
          Clear
        </button>
      </div>
      
      <div className="bg-gray-900 rounded p-3 h-48 overflow-y-auto font-mono text-xs">
        {logs.length > 0 ? (
          logs.map((log, index) => (
            <div key={index} className="text-gray-300 mb-1">
              {log}
            </div>
          ))
        ) : (
          <div className="text-gray-500 italic">No logs yet...</div>
        )}
      </div>
    </div>
  )
}

/**
 * Validation Test Scenarios
 */
export const validationScenarios = {
  requirements: {
    valid: { discordLinked: true, followInstagram: false, joinTelegram: false, subscribeNewsletter: false },
    invalid: { discordLinked: false, followInstagram: false, joinTelegram: false, subscribeNewsletter: false }
  },
  products: {
    valid: ['variant-1', 'variant-2'],
    invalid: []
  },
  address: {
    valid: {
      name: 'John Doe',
      address: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'US',
      phone: '******-123-4567'
    },
    invalid: {
      name: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US',
      phone: 'invalid-phone'
    }
  }
}
