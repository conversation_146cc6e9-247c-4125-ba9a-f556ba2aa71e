/**
 * Test Layout for Raffle Entry Test Suite
 *
 * Provides isolated environment for testing with mock providers and CSP-safe configuration
 *
 * <AUTHOR> Team
 */

import React from 'react'
import { Metadata } from 'next'
import './test-styles.css'

export const metadata: Metadata = {
  title: 'Raffle Entry Test Suite | Syndicaps',
  description: 'Comprehensive testing interface for the refactored raffle entry system',
  robots: 'noindex, nofollow', // Prevent indexing of test pages
}

/**
 * Test layout component
 */
export default function TestLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="test-environment">
      {/* Test Environment Banner */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-yellow-600 text-black text-center py-2 text-sm font-medium">
        🧪 TEST ENVIRONMENT - Refactored Raffle Entry System Testing Suite
      </div>

      {/* Content with top padding to account for banner */}
      <div className="pt-10">
        {children}
      </div>
    </div>
  )
}
