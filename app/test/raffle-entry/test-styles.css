/**
 * Test Environment Styles
 * 
 * CSS styles for the raffle entry test suite
 * Provides isolated styling for test environment
 * 
 * <AUTHOR> Team
 */

.test-environment {
  /* Override any production styles that might interfere */
  --test-primary: #6366f1;
  --test-success: #10b981;
  --test-warning: #f59e0b;
  --test-error: #ef4444;
}

/* Disable Firebase performance monitoring in test environment */
.test-environment [data-firebase-performance] {
  display: none !important;
}

/* Test-specific console styling */
.test-console {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Performance metrics styling */
.performance-metric {
  transition: all 0.3s ease;
}

.performance-metric.good {
  color: var(--test-success);
}

.performance-metric.warning {
  color: var(--test-warning);
}

.performance-metric.poor {
  color: var(--test-error);
}

/* Test scenario buttons */
.test-scenario-button {
  transition: all 0.2s ease;
}

.test-scenario-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.test-scenario-button.active {
  background: linear-gradient(135deg, var(--test-primary), #8b5cf6);
  border-color: var(--test-primary);
}

/* Test logs styling */
.test-logs {
  background: #0f172a;
  border: 1px solid #334155;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
}

.test-logs .log-entry {
  margin-bottom: 4px;
  padding: 2px 0;
}

.test-logs .log-entry.info {
  color: #94a3b8;
}

.test-logs .log-entry.success {
  color: var(--test-success);
}

.test-logs .log-entry.warning {
  color: var(--test-warning);
}

.test-logs .log-entry.error {
  color: var(--test-error);
}

/* Performance monitor styling */
.performance-monitor {
  background: #1e293b;
  border: 1px solid #475569;
  border-radius: 12px;
  padding: 20px;
}

.performance-metric-card {
  background: #334155;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.performance-metric-card:hover {
  background: #475569;
  transform: translateY(-1px);
}

.performance-metric-value {
  font-size: 18px;
  font-weight: bold;
  margin-top: 4px;
}

.performance-metric-value.good {
  color: var(--test-success);
}

.performance-metric-value.warning {
  color: var(--test-warning);
}

.performance-metric-value.poor {
  color: var(--test-error);
}

/* Test controls styling */
.test-controls {
  background: #1e293b;
  border: 1px solid #475569;
  border-radius: 12px;
  padding: 20px;
}

.test-control-button {
  background: #475569;
  border: 1px solid #64748b;
  border-radius: 8px;
  padding: 12px 20px;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.test-control-button:hover {
  background: #64748b;
  border-color: #94a3b8;
  transform: translateY(-1px);
}

.test-control-button:active {
  transform: translateY(0);
}

.test-control-button.primary {
  background: var(--test-primary);
  border-color: var(--test-primary);
}

.test-control-button.primary:hover {
  background: #5855eb;
  border-color: #5855eb;
}

.test-control-button.success {
  background: var(--test-success);
  border-color: var(--test-success);
}

.test-control-button.success:hover {
  background: #059669;
  border-color: #059669;
}

.test-control-button.warning {
  background: var(--test-warning);
  border-color: var(--test-warning);
}

.test-control-button.warning:hover {
  background: #d97706;
  border-color: #d97706;
}

.test-control-button.error {
  background: var(--test-error);
  border-color: var(--test-error);
}

.test-control-button.error:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.test-control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Test status indicators */
.test-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.test-status-indicator.running {
  background: rgba(16, 185, 129, 0.1);
  color: var(--test-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.test-status-indicator.idle {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border: 1px solid rgba(148, 163, 184, 0.3);
}

.test-status-indicator.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--test-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Test progress bar */
.test-progress-bar {
  width: 100%;
  height: 8px;
  background: #334155;
  border-radius: 4px;
  overflow: hidden;
}

.test-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--test-primary), #8b5cf6);
  transition: width 0.3s ease;
  border-radius: 4px;
}

/* Test grid layout */
.test-grid {
  display: grid;
  gap: 24px;
}

.test-grid.two-column {
  grid-template-columns: 1fr 2fr;
}

.test-grid.three-column {
  grid-template-columns: 1fr 1fr 1fr;
}

.test-grid.four-column {
  grid-template-columns: repeat(4, 1fr);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .test-grid.two-column {
    grid-template-columns: 1fr;
  }
  
  .test-grid.three-column {
    grid-template-columns: 1fr 1fr;
  }
  
  .test-grid.four-column {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 640px) {
  .test-grid.three-column,
  .test-grid.four-column {
    grid-template-columns: 1fr;
  }
}

/* Animation utilities */
.test-fade-in {
  animation: testFadeIn 0.3s ease-out;
}

.test-slide-up {
  animation: testSlideUp 0.3s ease-out;
}

@keyframes testFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes testSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
