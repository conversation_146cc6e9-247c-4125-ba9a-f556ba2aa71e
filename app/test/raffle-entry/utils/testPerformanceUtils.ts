/**
 * Test Performance Utilities
 * 
 * Safe performance monitoring utilities for the test environment
 * Avoids CSP issues and external dependencies
 * 
 * <AUTHOR> Team
 */

/**
 * Performance metric interface
 */
export interface PerformanceMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  timestamp: number
}

/**
 * Component performance tracker
 */
export class TestPerformanceTracker {
  private metrics: Map<string, PerformanceMetric[]> = new Map()
  private observers: ((metric: PerformanceMetric) => void)[] = []

  /**
   * Start measuring a performance metric
   */
  startMeasure(name: string): () => number {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      const metric: PerformanceMetric = {
        name,
        value: duration,
        rating: this.getRating(name, duration),
        timestamp: Date.now()
      }
      
      this.recordMetric(metric)
      return duration
    }
  }

  /**
   * Measure a function execution time
   */
  measure<T>(name: string, fn: () => T): T {
    const endMeasure = this.startMeasure(name)
    const result = fn()
    endMeasure()
    return result
  }

  /**
   * Measure an async function execution time
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const endMeasure = this.startMeasure(name)
    const result = await fn()
    endMeasure()
    return result
  }

  /**
   * Record a metric manually
   */
  recordMetric(metric: PerformanceMetric) {
    const existing = this.metrics.get(metric.name) || []
    existing.push(metric)
    
    // Keep only last 50 measurements per metric
    if (existing.length > 50) {
      existing.shift()
    }
    
    this.metrics.set(metric.name, existing)
    
    // Notify observers
    this.observers.forEach(observer => observer(metric))
    
    // Log to console in test environment
    console.log(`[TestPerformance] ${metric.name}: ${metric.value.toFixed(2)}ms (${metric.rating})`)
  }

  /**
   * Get rating for a performance metric
   */
  private getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = {
      'component-render': { good: 30, poor: 100 },
      'step-transition': { good: 200, poor: 500 },
      'validation': { good: 20, poor: 50 },
      'form-update': { good: 10, poor: 30 },
      'api-call': { good: 1000, poor: 3000 },
      'default': { good: 100, poor: 300 }
    }
    
    const threshold = thresholds[name as keyof typeof thresholds] || thresholds.default
    
    if (value <= threshold.good) return 'good'
    if (value <= threshold.poor) return 'needs-improvement'
    return 'poor'
  }

  /**
   * Get metrics for a specific measurement
   */
  getMetrics(name: string): PerformanceMetric[] {
    return this.metrics.get(name) || []
  }

  /**
   * Get all metrics
   */
  getAllMetrics(): Map<string, PerformanceMetric[]> {
    return new Map(this.metrics)
  }

  /**
   * Get average value for a metric
   */
  getAverage(name: string): number {
    const metrics = this.getMetrics(name)
    if (metrics.length === 0) return 0
    
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0)
    return sum / metrics.length
  }

  /**
   * Subscribe to metric updates
   */
  subscribe(observer: (metric: PerformanceMetric) => void): () => void {
    this.observers.push(observer)
    
    return () => {
      const index = this.observers.indexOf(observer)
      if (index > -1) {
        this.observers.splice(index, 1)
      }
    }
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics.clear()
  }

  /**
   * Generate performance report
   */
  generateReport(): {
    summary: Record<string, { average: number, count: number, rating: string }>
    details: Record<string, PerformanceMetric[]>
  } {
    const summary: Record<string, { average: number, count: number, rating: string }> = {}
    const details: Record<string, PerformanceMetric[]> = {}
    
    for (const [name, metrics] of this.metrics) {
      const average = this.getAverage(name)
      const rating = this.getRating(name, average)
      
      summary[name] = {
        average,
        count: metrics.length,
        rating
      }
      
      details[name] = [...metrics]
    }
    
    return { summary, details }
  }
}

/**
 * Global test performance tracker instance
 */
export const testPerformanceTracker = new TestPerformanceTracker()

/**
 * Mock Web Vitals for test environment
 */
export const mockWebVitals = {
  /**
   * Mock LCP (Largest Contentful Paint)
   */
  getLCP: (callback: (metric: PerformanceMetric) => void) => {
    // Simulate LCP measurement
    setTimeout(() => {
      const value = Math.random() * 3000 + 1000 // 1-4 seconds
      callback({
        name: 'LCP',
        value,
        rating: value < 2500 ? 'good' : value < 4000 ? 'needs-improvement' : 'poor',
        timestamp: Date.now()
      })
    }, 2000)
  },

  /**
   * Mock FID (First Input Delay)
   */
  getFID: (callback: (metric: PerformanceMetric) => void) => {
    // Simulate FID measurement
    setTimeout(() => {
      const value = Math.random() * 200 + 10 // 10-210ms
      callback({
        name: 'FID',
        value,
        rating: value < 100 ? 'good' : value < 300 ? 'needs-improvement' : 'poor',
        timestamp: Date.now()
      })
    }, 1000)
  },

  /**
   * Mock CLS (Cumulative Layout Shift)
   */
  getCLS: (callback: (metric: PerformanceMetric) => void) => {
    // Simulate CLS measurement
    setTimeout(() => {
      const value = Math.random() * 0.3 // 0-0.3
      callback({
        name: 'CLS',
        value,
        rating: value < 0.1 ? 'good' : value < 0.25 ? 'needs-improvement' : 'poor',
        timestamp: Date.now()
      })
    }, 1500)
  }
}

/**
 * Performance monitoring hook for React components
 */
export const useTestPerformance = (componentName: string) => {
  const measureRender = () => {
    return testPerformanceTracker.startMeasure(`${componentName}-render`)
  }

  const measureUpdate = () => {
    return testPerformanceTracker.startMeasure(`${componentName}-update`)
  }

  const measureEffect = () => {
    return testPerformanceTracker.startMeasure(`${componentName}-effect`)
  }

  return {
    measureRender,
    measureUpdate,
    measureEffect,
    tracker: testPerformanceTracker
  }
}

/**
 * Memory usage tracker (simplified for test environment)
 */
export const getMemoryUsage = (): number => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return memory.usedJSHeapSize / 1024 / 1024 // Convert to MB
  }
  
  // Fallback simulation for browsers without memory API
  return Math.random() * 20 + 30 // 30-50MB
}

/**
 * Bundle size estimator
 */
export const estimateBundleSize = (): number => {
  // Estimate based on refactored component count and average size
  const componentCount = 9 // Refactored components
  const averageComponentSize = 25 // KB per component
  const hookSize = 15 // KB for hooks
  const typeSize = 5 // KB for types
  
  return componentCount * averageComponentSize + hookSize + typeSize
}
