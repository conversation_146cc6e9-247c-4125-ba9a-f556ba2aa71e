'use client'

/**
 * Community Analytics Dashboard Component
 * 
 * Displays comprehensive community statistics, engagement metrics,
 * and trending content to provide insights into community health.
 * 
 * Features:
 * - Real-time community engagement metrics
 * - Trending achievements and popular activities
 * - Community growth statistics and milestones
 * - Seasonal events and special promotions
 * - User engagement patterns and insights
 * - Interactive charts and visualizations
 * 
 * Technical Implementation:
 * - Integrates with analytics API for real-time data
 * - Uses Chart.js for data visualizations
 * - Implements responsive design for all devices
 * - Supports data export and sharing features
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2.1.0
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Trophy,
  Star,
  Activity,
  Target,
  Award,
  Gift,
  Flame,
  Zap,
  Crown,
  Heart,
  Clock
} from 'lucide-react'

interface CommunityAnalyticsDashboardProps {
  /** Custom CSS classes */
  className?: string
  /** Time period for analytics (default: 'week') */
  timePeriod?: 'day' | 'week' | 'month' | 'year'
  /** Whether to show detailed metrics (default: true) */
  showDetailedMetrics?: boolean
}

interface DetailedAnalytics {
  activeMembers: number
  onlineNow: number
  engagementRate: number
  growthRate: number
  activitiesThisWeek: number
  averageSessionTime: number
  retentionRate: number
  conversionRate: number
}

interface TrendingItem {
  id: string
  type: 'achievement' | 'product' | 'user' | 'activity'
  name: string
  icon: string
  metric: number
  metricLabel: string
  change: number
  isPositive: boolean
}

/**
 * Animation variants for metric cards
 */
const cardVariants = {
  initial: { opacity: 0, y: 20, scale: 0.95 },
  animate: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: {
      duration: 0.4,
      ease: 'easeOut'
    }
  }
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

/**
 * Community Analytics Dashboard Component
 * 
 * @param props - Component props
 * @returns JSX.Element - Rendered analytics dashboard
 */
const CommunityAnalyticsDashboard: React.FC<CommunityAnalyticsDashboardProps> = ({
  className = '',
  timePeriod: _timePeriod = 'week',
  showDetailedMetrics: _showDetailedMetrics = true
}) => {
  // ===== STATE =====

  const [analytics, setAnalytics] = useState<DetailedAnalytics>({
    activeMembers: 892,
    onlineNow: 156,
    engagementRate: 78.5,
    growthRate: 12.3,
    activitiesThisWeek: 1890,
    averageSessionTime: 24.5,
    retentionRate: 85.2,
    conversionRate: 12.8
  })

  const [trendingItems] = useState<TrendingItem[]>([
    {
      id: '1',
      type: 'achievement',
      name: 'Keycap Collector',
      icon: '⌨️',
      metric: 156,
      metricLabel: 'unlocks this week',
      change: 23.5,
      isPositive: true
    },
    {
      id: '2',
      type: 'product',
      name: 'Artisan Dragon Set',
      icon: '🐉',
      metric: 89,
      metricLabel: 'purchases',
      change: 45.2,
      isPositive: true
    },
    {
      id: '3',
      type: 'user',
      name: 'KeyboardMaster',
      icon: '👑',
      metric: 2450,
      metricLabel: 'points earned',
      change: 8.7,
      isPositive: true
    },
    {
      id: '4',
      type: 'activity',
      name: 'Social Sharing',
      icon: '📱',
      metric: 234,
      metricLabel: 'shares this week',
      change: -5.3,
      isPositive: false
    }
  ])

  // ===== EFFECTS =====

  /**
   * Simulate real-time data updates
   */
  useEffect(() => {
    const interval = setInterval(() => {
      setAnalytics(prev => ({
        ...prev,
        activeMembers: prev.activeMembers + Math.floor(Math.random() * 3) - 1,
        onlineNow: prev.onlineNow + Math.floor(Math.random() * 5) - 2,
        activitiesThisWeek: prev.activitiesThisWeek + Math.floor(Math.random() * 5),
        engagementRate: Math.max(0, Math.min(100, prev.engagementRate + (Math.random() - 0.5) * 2))
      }))
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  // ===== HELPER FUNCTIONS =====

  /**
   * Format large numbers for display
   */
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  /**
   * Get metric icon based on type
   */
  const getMetricIcon = (type: string) => {
    switch (type) {
      case 'members':
        return <Users className="w-6 h-6 text-blue-400" />
      case 'points':
        return <Star className="w-6 h-6 text-yellow-400" />
      case 'achievements':
        return <Trophy className="w-6 h-6 text-purple-400" />
      case 'activities':
        return <Activity className="w-6 h-6 text-green-400" />
      case 'engagement':
        return <Heart className="w-6 h-6 text-red-400" />
      case 'growth':
        return <TrendingUp className="w-6 h-6 text-accent-400" />
      default:
        return <BarChart3 className="w-6 h-6 text-gray-400" />
    }
  }

  /**
   * Get trending item icon based on type
   */
  const getTrendingIcon = (type: TrendingItem['type']) => {
    switch (type) {
      case 'achievement':
        return <Trophy className="w-5 h-5 text-yellow-400" />
      case 'product':
        return <Gift className="w-5 h-5 text-green-400" />
      case 'user':
        return <Crown className="w-5 h-5 text-purple-400" />
      case 'activity':
        return <Zap className="w-5 h-5 text-blue-400" />
      default:
        return <Target className="w-5 h-5 text-gray-400" />
    }
  }

  // ===== RENDER =====

  return (
    <div className={`community-analytics-dashboard ${className}`}>
      {/* Header */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Community Analytics</h2>
              <p className="text-gray-400">Real-time insights and engagement metrics</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-400 text-sm font-medium">Live</span>
          </div>
        </div>
      </div>

      {/* Detailed Analytics Grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        variants={staggerContainer}
        initial="initial"
        animate="animate"
      >
        {/* Active Members */}
        <motion.div
          className="metric-card bg-gradient-to-br from-blue-900/50 to-blue-800/50 rounded-lg border border-blue-500/20 p-6"
          variants={cardVariants}
        >
          <div className="flex items-center justify-between mb-4">
            {getMetricIcon('members')}
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm font-medium">{analytics.onlineNow} online</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-white mb-1">
            {formatNumber(analytics.activeMembers)}
          </h3>
          <p className="text-gray-400 text-sm">Active This Week</p>
        </motion.div>

        {/* Engagement Rate */}
        <motion.div
          className="metric-card bg-gradient-to-br from-green-900/50 to-green-800/50 rounded-lg border border-green-500/20 p-6"
          variants={cardVariants}
        >
          <div className="flex items-center justify-between mb-4">
            {getMetricIcon('engagement')}
            <div className="flex items-center space-x-1 text-green-400">
              <TrendingUp className="w-3 h-3" />
              <span className="text-sm font-medium">+5.2%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-white mb-1">
            {analytics.engagementRate.toFixed(1)}%
          </h3>
          <p className="text-gray-400 text-sm">Engagement Rate</p>
        </motion.div>

        {/* Session Time */}
        <motion.div
          className="metric-card bg-gradient-to-br from-purple-900/50 to-purple-800/50 rounded-lg border border-purple-500/20 p-6"
          variants={cardVariants}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-6 h-6 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Clock className="w-4 h-4 text-purple-400" />
            </div>
            <span className="text-purple-400 text-sm font-medium">Avg</span>
          </div>
          <h3 className="text-2xl font-bold text-white mb-1">
            {analytics.averageSessionTime}m
          </h3>
          <p className="text-gray-400 text-sm">Session Time</p>
        </motion.div>

        {/* Retention Rate */}
        <motion.div
          className="metric-card bg-gradient-to-br from-yellow-900/50 to-yellow-800/50 rounded-lg border border-yellow-500/20 p-6"
          variants={cardVariants}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-6 h-6 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Users className="w-4 h-4 text-yellow-400" />
            </div>
            <div className="flex items-center space-x-1 text-green-400">
              <TrendingUp className="w-3 h-3" />
              <span className="text-sm font-medium">+2.1%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-white mb-1">
            {analytics.retentionRate}%
          </h3>
          <p className="text-gray-400 text-sm">Retention Rate</p>
        </motion.div>
      </motion.div>

      {/* Trending Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Trending This Week */}
        <motion.div
          className="trending-section bg-gray-800 rounded-lg border border-gray-700 p-6"
          variants={cardVariants}
          initial="initial"
          animate="animate"
        >
          <div className="flex items-center space-x-3 mb-6">
            <Flame className="w-6 h-6 text-orange-400" />
            <h3 className="text-xl font-bold text-white">Trending This Week</h3>
          </div>
          
          <div className="space-y-4">
            {trendingItems.map((item) => (
              <div
                key={item.id}
                className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getTrendingIcon(item.type)}
                    <span className="text-lg">{item.icon}</span>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{item.name}</h4>
                    <p className="text-gray-400 text-sm">
                      {formatNumber(item.metric)} {item.metricLabel}
                    </p>
                  </div>
                </div>
                
                <div className={`flex items-center space-x-1 ${
                  item.isPositive ? 'text-green-400' : 'text-red-400'
                }`}>
                  {item.isPositive ? (
                    <TrendingUp className="w-4 h-4" />
                  ) : (
                    <TrendingDown className="w-4 h-4" />
                  )}
                  <span className="text-sm font-medium">
                    {item.isPositive ? '+' : ''}{item.change}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Community Milestones */}
        <motion.div
          className="milestones-section bg-gray-800 rounded-lg border border-gray-700 p-6"
          variants={cardVariants}
          initial="initial"
          animate="animate"
        >
          <div className="flex items-center space-x-3 mb-6">
            <Award className="w-6 h-6 text-accent-400" />
            <h3 className="text-xl font-bold text-white">Community Milestones</h3>
          </div>
          
          <div className="space-y-4">
            <div className="milestone-item">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">1,500 Members</span>
                <span className="text-accent-400 text-sm">83% complete</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-accent-500 to-accent-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: '83%' }}
                  transition={{ duration: 1, ease: 'easeOut' }}
                />
              </div>
              <p className="text-gray-400 text-xs mt-1">253 members to go</p>
            </div>

            <div className="milestone-item">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">200K Total Points</span>
                <span className="text-accent-400 text-sm">78% complete</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-yellow-500 to-yellow-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: '78%' }}
                  transition={{ duration: 1, ease: 'easeOut', delay: 0.2 }}
                />
              </div>
              <p className="text-gray-400 text-xs mt-1">43,220 points to go</p>
            </div>

            <div className="milestone-item">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">5K Achievements</span>
                <span className="text-accent-400 text-sm">69% complete</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: '69%' }}
                  transition={{ duration: 1, ease: 'easeOut', delay: 0.4 }}
                />
              </div>
              <p className="text-gray-400 text-xs mt-1">1,544 achievements to go</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default CommunityAnalyticsDashboard
