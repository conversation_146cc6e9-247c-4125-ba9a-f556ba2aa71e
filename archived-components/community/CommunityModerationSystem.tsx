/**
 * Community Moderation System Component
 * 
 * Comprehensive moderation interface with AI-powered content filtering,
 * community reporting, and admin moderation tools.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Flag,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  MessageSquare,
  User,
  Clock,
  TrendingDown,
  Brain,
  Users,
  Gavel,
  BarChart3,
  Filter,
  Search,
  MoreHorizontal
} from 'lucide-react'
import { useUser } from '@/lib/useUser'

interface ModerationItem {
  id: string
  type: 'post' | 'comment' | 'user' | 'image'
  contentId: string
  content: string
  authorId: string
  authorName: string
  reportedBy: string[]
  reportReasons: ReportReason[]
  aiAnalysis: AIAnalysis
  status: 'pending' | 'approved' | 'rejected' | 'escalated'
  priority: 'low' | 'medium' | 'high' | 'critical'
  createdAt: Date
  moderatedAt?: Date
  moderatedBy?: string
  moderatorNotes?: string
}

interface ReportReason {
  type: 'spam' | 'harassment' | 'inappropriate' | 'misinformation' | 'copyright' | 'other'
  description: string
  reportedBy: string
  timestamp: Date
}

interface AIAnalysis {
  toxicityScore: number
  spamProbability: number
  sentimentScore: number
  languageDetection: string
  flaggedKeywords: string[]
  confidence: number
  recommendation: 'approve' | 'review' | 'reject'
}

interface ModerationAction {
  id: string
  type: 'approve' | 'reject' | 'hide' | 'warn' | 'suspend' | 'ban'
  reason: string
  duration?: number // in days
  notifyUser: boolean
}

interface CommunityModerationSystemProps {
  userRole: 'user' | 'moderator' | 'admin'
  contentId?: string
  contentType?: 'post' | 'comment'
  showReportButton?: boolean
  className?: string
}

/**
 * Report reason configurations
 */
const REPORT_REASONS = [
  {
    type: 'spam',
    label: 'Spam or Unwanted Content',
    description: 'Repetitive, promotional, or irrelevant content',
    icon: AlertTriangle
  },
  {
    type: 'harassment',
    label: 'Harassment or Bullying',
    description: 'Targeting individuals with harmful content',
    icon: Shield
  },
  {
    type: 'inappropriate',
    label: 'Inappropriate Content',
    description: 'Content that violates community guidelines',
    icon: EyeOff
  },
  {
    type: 'misinformation',
    label: 'Misinformation',
    description: 'False or misleading information',
    icon: AlertTriangle
  },
  {
    type: 'copyright',
    label: 'Copyright Violation',
    description: 'Unauthorized use of copyrighted material',
    icon: Shield
  },
  {
    type: 'other',
    label: 'Other',
    description: 'Other violations not listed above',
    icon: MoreHorizontal
  }
]

/**
 * Mock moderation data
 */
const MOCK_MODERATION_ITEMS: ModerationItem[] = [
  {
    id: '1',
    type: 'post',
    contentId: 'post_123',
    content: 'This is a sample post that has been reported for review...',
    authorId: 'user_456',
    authorName: 'John Doe',
    reportedBy: ['user_789', 'user_101'],
    reportReasons: [
      {
        type: 'spam',
        description: 'This looks like promotional content',
        reportedBy: 'user_789',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
      }
    ],
    aiAnalysis: {
      toxicityScore: 0.15,
      spamProbability: 0.75,
      sentimentScore: 0.2,
      languageDetection: 'en',
      flaggedKeywords: ['buy now', 'limited time'],
      confidence: 0.85,
      recommendation: 'review'
    },
    status: 'pending',
    priority: 'medium',
    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000)
  }
]

/**
 * Community Moderation System Component
 */
export const CommunityModerationSystem: React.FC<CommunityModerationSystemProps> = ({
  userRole,
  contentId,
  contentType,
  showReportButton = true,
  className = ''
}) => {
  const { user } = useUser()
  const [moderationItems, setModerationItems] = useState<ModerationItem[]>(MOCK_MODERATION_ITEMS)
  const [showReportModal, setShowReportModal] = useState(false)
  const [selectedReportReason, setSelectedReportReason] = useState<string>('')
  const [reportDescription, setReportDescription] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterPriority, setFilterPriority] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isSubmittingReport, setIsSubmittingReport] = useState(false)

  /**
   * Submit content report
   */
  const submitReport = useCallback(async () => {
    if (!user || !selectedReportReason || isSubmittingReport) return

    setIsSubmittingReport(true)

    try {
      const reportData = {
        contentId,
        contentType,
        reason: selectedReportReason,
        description: reportDescription,
        reportedBy: user.uid
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In real implementation: await api.submitReport(reportData)
      
      setShowReportModal(false)
      setSelectedReportReason('')
      setReportDescription('')
      
      // Show success message
      console.log('Report submitted successfully')
      
    } catch (error) {
      console.error('Failed to submit report:', error)
    } finally {
      setIsSubmittingReport(false)
    }
  }, [user, selectedReportReason, reportDescription, contentId, contentType, isSubmittingReport])

  /**
   * Handle moderation action
   */
  const handleModerationAction = useCallback(async (
    itemId: string, 
    action: ModerationAction
  ) => {
    try {
      // Optimistic update
      setModerationItems(prev => prev.map(item => 
        item.id === itemId 
          ? {
              ...item,
              status: action.type === 'approve' ? 'approved' : 'rejected',
              moderatedAt: new Date(),
              moderatedBy: user?.uid,
              moderatorNotes: action.reason
            }
          : item
      ))

      // Make API call
      // await api.moderateContent(itemId, action)
      
    } catch (error) {
      console.error('Failed to moderate content:', error)
      // Revert optimistic update
    }
  }, [user])

  /**
   * Get priority color
   */
  const getPriorityColor = useCallback((priority: ModerationItem['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/20'
      case 'high': return 'text-orange-500 bg-orange-500/10 border-orange-500/20'
      case 'medium': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/20'
      case 'low': return 'text-green-500 bg-green-500/10 border-green-500/20'
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/20'
    }
  }, [])

  /**
   * Get AI recommendation color
   */
  const getAIRecommendationColor = useCallback((recommendation: string) => {
    switch (recommendation) {
      case 'approve': return 'text-green-500'
      case 'review': return 'text-yellow-500'
      case 'reject': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }, [])

  /**
   * Filter moderation items
   */
  const filteredItems = moderationItems.filter(item => {
    if (filterStatus !== 'all' && item.status !== filterStatus) return false
    if (filterPriority !== 'all' && item.priority !== filterPriority) return false
    if (searchQuery && !item.content.toLowerCase().includes(searchQuery.toLowerCase())) return false
    return true
  })

  /**
   * Render report button
   */
  const renderReportButton = () => {
    if (!showReportButton || !user) return null

    return (
      <button
        onClick={() => setShowReportModal(true)}
        className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-red-400 transition-colors"
      >
        <Flag size={14} />
        <span>Report</span>
      </button>
    )
  }

  /**
   * Render report modal
   */
  const renderReportModal = () => {
    if (!showReportModal) return null

    return (
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="bg-gray-800 border border-gray-700 rounded-lg p-6 w-full max-w-md"
        >
          <h3 className="text-white font-semibold mb-4">Report Content</h3>
          
          <div className="space-y-4">
            {/* Report Reasons */}
            <div>
              <label className="block text-gray-300 text-sm mb-2">
                Why are you reporting this content?
              </label>
              <div className="space-y-2">
                {REPORT_REASONS.map((reason) => {
                  const IconComponent = reason.icon
                  
                  return (
                    <label
                      key={reason.type}
                      className={`flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedReportReason === reason.type
                          ? 'border-accent-500 bg-accent-500/10'
                          : 'border-gray-600 hover:border-gray-500'
                      }`}
                    >
                      <input
                        type="radio"
                        name="reportReason"
                        value={reason.type}
                        checked={selectedReportReason === reason.type}
                        onChange={(e) => setSelectedReportReason(e.target.value)}
                        className="mt-1"
                      />
                      <IconComponent size={16} className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-white text-sm font-medium">
                          {reason.label}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {reason.description}
                        </p>
                      </div>
                    </label>
                  )
                })}
              </div>
            </div>

            {/* Additional Description */}
            <div>
              <label className="block text-gray-300 text-sm mb-2">
                Additional details (optional)
              </label>
              <textarea
                value={reportDescription}
                onChange={(e) => setReportDescription(e.target.value)}
                placeholder="Provide more context about why you're reporting this content..."
                rows={3}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 text-sm"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 mt-6">
            <button
              onClick={() => setShowReportModal(false)}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={submitReport}
              disabled={!selectedReportReason || isSubmittingReport}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded transition-colors"
            >
              {isSubmittingReport ? 'Submitting...' : 'Submit Report'}
            </button>
          </div>
        </motion.div>
      </div>
    )
  }

  /**
   * Render moderation queue (for moderators/admins)
   */
  const renderModerationQueue = () => {
    if (userRole === 'user') return null

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-white">Moderation Queue</h2>
            <p className="text-gray-400 mt-1">
              Review and moderate reported content
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-400">
              {filteredItems.length} items pending
            </span>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="bg-gray-700 text-white text-sm rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>

          <select
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
            className="bg-gray-700 text-white text-sm rounded px-3 py-2"
          >
            <option value="all">All Priority</option>
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>

          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search content..."
              className="bg-gray-700 text-white text-sm rounded px-3 py-2 w-64"
            />
          </div>
        </div>

        {/* Moderation Items */}
        <div className="space-y-4">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className="bg-gray-800 border border-gray-700 rounded-lg p-6"
            >
              {/* Item Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium border ${getPriorityColor(item.priority)}`}>
                    {item.priority}
                  </span>
                  <span className="text-gray-400 text-sm">
                    {item.type} by {item.authorName}
                  </span>
                  <span className="text-gray-500 text-sm">
                    {item.createdAt.toLocaleDateString()}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Brain size={16} className={getAIRecommendationColor(item.aiAnalysis.recommendation)} />
                  <span className={`text-sm ${getAIRecommendationColor(item.aiAnalysis.recommendation)}`}>
                    AI: {item.aiAnalysis.recommendation}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="mb-4">
                <p className="text-white mb-2">{item.content}</p>
              </div>

              {/* AI Analysis */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-3 bg-gray-700 rounded">
                <div>
                  <span className="text-gray-400 text-xs">Toxicity</span>
                  <p className="text-white font-medium">
                    {(item.aiAnalysis.toxicityScore * 100).toFixed(1)}%
                  </p>
                </div>
                <div>
                  <span className="text-gray-400 text-xs">Spam</span>
                  <p className="text-white font-medium">
                    {(item.aiAnalysis.spamProbability * 100).toFixed(1)}%
                  </p>
                </div>
                <div>
                  <span className="text-gray-400 text-xs">Sentiment</span>
                  <p className="text-white font-medium">
                    {item.aiAnalysis.sentimentScore > 0 ? 'Positive' : 'Negative'}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400 text-xs">Confidence</span>
                  <p className="text-white font-medium">
                    {(item.aiAnalysis.confidence * 100).toFixed(1)}%
                  </p>
                </div>
              </div>

              {/* Report Reasons */}
              <div className="mb-4">
                <h4 className="text-white font-medium mb-2">Reports ({item.reportReasons.length})</h4>
                <div className="space-y-2">
                  {item.reportReasons.map((report, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-700 rounded">
                      <div>
                        <span className="text-white text-sm font-medium capitalize">
                          {report.type}
                        </span>
                        <p className="text-gray-400 text-xs">{report.description}</p>
                      </div>
                      <span className="text-gray-500 text-xs">
                        {report.timestamp.toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Actions */}
              {item.status === 'pending' && (
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleModerationAction(item.id, {
                      id: 'approve',
                      type: 'approve',
                      reason: 'Content approved after review',
                      notifyUser: true
                    })}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
                  >
                    <CheckCircle size={16} />
                    <span>Approve</span>
                  </button>

                  <button
                    onClick={() => handleModerationAction(item.id, {
                      id: 'reject',
                      type: 'reject',
                      reason: 'Content violates community guidelines',
                      notifyUser: true
                    })}
                    className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                  >
                    <XCircle size={16} />
                    <span>Reject</span>
                  </button>

                  <button className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors">
                    <Gavel size={16} />
                    <span>More Actions</span>
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Shield className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-400 mb-2">
              No Items to Review
            </h3>
            <p className="text-gray-500">
              All reported content has been reviewed
            </p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Report Button */}
      {renderReportButton()}

      {/* Moderation Queue */}
      {renderModerationQueue()}

      {/* Report Modal */}
      <AnimatePresence>
        {renderReportModal()}
      </AnimatePresence>
    </div>
  )
}

export default CommunityModerationSystem
