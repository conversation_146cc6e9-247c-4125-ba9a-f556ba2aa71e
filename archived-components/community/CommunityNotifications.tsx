'use client'

/**
 * Community Notifications Component
 * 
 * Enhanced notification system for community features with
 * real-time updates, categorization, and interaction capabilities.
 * 
 * Features:
 * - Real-time notification updates
 * - Category-based filtering
 * - Interactive notification actions
 * - Mark as read/unread functionality
 * - Notification preferences
 * - Mobile-responsive design
 * 
 * <AUTHOR> Team
 */

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Bell,
  BellOff,
  X,
  Check,
  CheckChe<PERSON>,
  Trophy,
  MessageCircle,
  Heart,
  Star,
  Users,
  Image as ImageIcon,
  Settings,
  Filter,
  Trash2
} from 'lucide-react'
import { useNotifications } from '../../hooks/useNotifications'
import { useRealTimeListener } from '../../hooks/useRealTimeCommunity'
import { useUser } from '../../lib/useUser'

interface CommunityNotificationsProps {
  isOpen: boolean
  onClose: () => void
  className?: string
}

/**
 * Notification type icons
 */
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'challenge_update':
    case 'challenge_participation':
      return Trophy
    case 'submission_interaction':
    case 'submission_update':
      return ImageIcon
    case 'discussion_reply':
    case 'discussion_interaction':
      return MessageCircle
    case 'like':
      return Heart
    case 'follow':
      return Users
    case 'featured':
      return Star
    default:
      return Bell
  }
}

/**
 * Notification type colors
 */
const getNotificationColor = (type: string) => {
  switch (type) {
    case 'challenge_update':
    case 'challenge_participation':
      return 'text-yellow-400'
    case 'submission_interaction':
    case 'submission_update':
      return 'text-blue-400'
    case 'discussion_reply':
    case 'discussion_interaction':
      return 'text-green-400'
    case 'like':
      return 'text-red-400'
    case 'follow':
      return 'text-purple-400'
    case 'featured':
      return 'text-accent-400'
    default:
      return 'text-gray-400'
  }
}

/**
 * Community Notifications Component
 */
const CommunityNotifications: React.FC<CommunityNotificationsProps> = ({
  isOpen,
  onClose,
  className = ''
}) => {
  const { user } = useUser()
  const { 
    notifications, 
    unreadCount, 
    loading, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification,
    refresh 
  } = useNotifications()
  
  const [filter, setFilter] = useState<'all' | 'unread' | 'community'>('all')
  const [showSettings, setShowSettings] = useState(false)

  // Listen for real-time notification updates
  useRealTimeListener('notification', (data: any) => {
    if (data.userId === user?.uid) {
      refresh()
    }
  }, [user?.uid])

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') return !notification.read
    if (filter === 'community') {
      return ['challenge_update', 'challenge_participation', 'submission_interaction', 
              'submission_update', 'discussion_reply', 'discussion_interaction'].includes(notification.type)
    }
    return true
  })

  const handleMarkAsRead = useCallback(async (notificationId: string) => {
    await markAsRead(notificationId)
  }, [markAsRead])

  const handleMarkAllAsRead = useCallback(async () => {
    await markAllAsRead()
  }, [markAllAsRead])

  const handleDelete = useCallback(async (notificationId: string) => {
    await deleteNotification(notificationId)
  }, [deleteNotification])

  const formatRelativeTime = (timestamp: any): string => {
    if (!timestamp || !timestamp.toDate) return 'Unknown'
    
    const now = new Date()
    const date = timestamp.toDate()
    const diff = now.getTime() - date.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    if (days < 7) return `${days}d ago`
    
    return date.toLocaleDateString()
  }

  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 z-50 flex items-start justify-center pt-20"
      onClick={onClose}
    >
      <motion.div
        initial={{ opacity: 0, y: -20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.95 }}
        className={`bg-gray-800 border border-gray-700 rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden ${className}`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Bell className="w-6 h-6 text-accent-400" />
              <h2 className="text-lg font-bold text-white">Notifications</h2>
              {unreadCount > 0 && (
                <span className="bg-accent-600 text-white text-xs px-2 py-1 rounded-full">
                  {unreadCount}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 text-gray-400 hover:text-white transition-colors"
              >
                <Settings className="w-4 h-4" />
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-2">
            {(['all', 'unread', 'community'] as const).map((filterType) => (
              <button
                key={filterType}
                onClick={() => setFilter(filterType)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  filter === filterType
                    ? 'bg-accent-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
              </button>
            ))}
          </div>

          {/* Actions */}
          {unreadCount > 0 && (
            <div className="flex items-center justify-between mt-3">
              <button
                onClick={handleMarkAllAsRead}
                className="text-sm text-accent-400 hover:text-accent-300 transition-colors flex items-center space-x-1"
              >
                <CheckCheck className="w-4 h-4" />
                <span>Mark all as read</span>
              </button>
            </div>
          )}
        </div>

        {/* Notifications List */}
        <div className="overflow-y-auto max-h-96">
          {loading ? (
            <div className="p-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-start space-x-3 p-3 animate-pulse">
                  <div className="w-10 h-10 bg-gray-700 rounded-full" />
                  <div className="flex-1">
                    <div className="h-4 bg-gray-700 rounded mb-2" />
                    <div className="h-3 bg-gray-700 rounded w-3/4" />
                  </div>
                </div>
              ))}
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="p-8 text-center">
              <BellOff className="w-12 h-12 text-gray-600 mx-auto mb-3" />
              <h3 className="text-white font-medium mb-1">No notifications</h3>
              <p className="text-gray-400 text-sm">
                {filter === 'unread' 
                  ? "You're all caught up!"
                  : "You'll see notifications here when you have them."
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-700">
              <AnimatePresence>
                {filteredNotifications.map((notification) => {
                  const Icon = getNotificationIcon(notification.type)
                  const iconColor = getNotificationColor(notification.type)
                  
                  return (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className={`p-4 hover:bg-gray-750 transition-colors ${
                        !notification.read ? 'bg-gray-750/50' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        {/* Icon */}
                        <div className={`p-2 rounded-full bg-gray-700 ${iconColor}`}>
                          <Icon className="w-4 h-4" />
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <p className="text-white text-sm font-medium mb-1">
                            {notification.title}
                          </p>
                          <p className="text-gray-400 text-sm mb-2 line-clamp-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">
                              {formatRelativeTime(notification.createdAt)}
                            </span>
                            <div className="flex items-center space-x-2">
                              {!notification.read && (
                                <button
                                  onClick={() => handleMarkAsRead(notification.id!)}
                                  className="text-xs text-accent-400 hover:text-accent-300 transition-colors"
                                >
                                  Mark as read
                                </button>
                              )}
                              <button
                                onClick={() => handleDelete(notification.id!)}
                                className="text-xs text-gray-500 hover:text-red-400 transition-colors"
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Unread indicator */}
                        {!notification.read && (
                          <div className="w-2 h-2 bg-accent-500 rounded-full mt-2" />
                        )}
                      </div>
                    </motion.div>
                  )
                })}
              </AnimatePresence>
            </div>
          )}
        </div>

        {/* Settings Panel */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="border-t border-gray-700 overflow-hidden"
            >
              <div className="p-4">
                <h3 className="text-white font-medium mb-3">Notification Preferences</h3>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Challenge updates</span>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Submission interactions</span>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Discussion replies</span>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">New followers</span>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </label>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  )
}

export default CommunityNotifications
