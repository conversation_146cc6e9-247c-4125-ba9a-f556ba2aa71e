/**
 * Enhanced Community Timeline Component
 * 
 * Advanced timeline with filtering, search, activity aggregation,
 * real-time updates, and intelligent content organization.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Filter, 
  Calendar, 
  TrendingUp,
  MessageSquare,
  Heart,
  Share2,
  Bookmark,
  Eye,
  Star,
  Trophy,
  Target,
  Users,
  Zap,
  Clock,
  ChevronDown,
  ChevronUp,
  MoreHorizontal,
  Pin,
  Flag,
  X
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { useUser } from '@/lib/useUser'
import { useAccessibility } from '@/components/accessibility/AccessibilityProvider'

interface TimelineActivity {
  id: string
  type: 'post' | 'challenge' | 'submission' | 'discussion' | 'achievement' | 'collaboration' | 'announcement'
  title: string
  content: string
  authorId: string
  authorName: string
  authorAvatar?: string
  authorBadges?: string[]
  timestamp: Date
  metadata: {
    likes: number
    comments: number
    shares: number
    views: number
    bookmarks: number
    tags?: string[]
    category?: string
    challengeId?: string
    submissionId?: string
    discussionId?: string
    images?: string[]
    attachments?: any[]
  }
  isPinned?: boolean
  isHighlighted?: boolean
  reactions?: { [key: string]: number }
  userInteractions?: {
    liked: boolean
    bookmarked: boolean
    shared: boolean
  }
}

interface TimelineFilters {
  type: string[]
  dateRange: 'today' | 'week' | 'month' | 'all'
  category: string[]
  author: string[]
  sortBy: 'recent' | 'popular' | 'trending'
  showPinned: boolean
}

export default function EnhancedTimeline() {
  const { user } = useUser()
  const { announce } = useAccessibility()
  
  // State
  const [activities, setActivities] = useState<TimelineActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<TimelineFilters>({
    type: [],
    dateRange: 'all',
    category: [],
    author: [],
    sortBy: 'recent',
    showPinned: true
  })
  const [expandedActivities, setExpandedActivities] = useState<Set<string>>(new Set())
  const [showFilters, setShowFilters] = useState(false)
  const [selectedActivity, setSelectedActivity] = useState<TimelineActivity | null>(null)

  // Load activities
  useEffect(() => {
    loadActivities()
  }, [filters])

  const loadActivities = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual API call
      const mockActivities: TimelineActivity[] = [
        {
          id: '1',
          type: 'challenge',
          title: 'Cyberpunk Keycap Challenge Started!',
          content: 'Join our latest design challenge and create futuristic cyberpunk-themed keycaps. Prize pool: ₹50,000!',
          authorId: 'admin',
          authorName: 'Syndicaps Team',
          authorAvatar: '/avatars/admin.jpg',
          authorBadges: ['Admin', 'Verified'],
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          metadata: {
            likes: 245,
            comments: 67,
            shares: 89,
            views: 1234,
            bookmarks: 156,
            tags: ['challenge', 'cyberpunk', 'design'],
            category: 'challenges',
            challengeId: 'cyberpunk-2024'
          },
          isPinned: true,
          isHighlighted: true,
          reactions: { '🔥': 45, '💜': 32, '⚡': 28 },
          userInteractions: { liked: false, bookmarked: false, shared: false }
        },
        {
          id: '2',
          type: 'submission',
          title: 'Neon Dreams Keycap Set',
          content: 'My latest creation inspired by neon-lit cityscapes. What do you think?',
          authorId: 'user1',
          authorName: 'Alex Rodriguez',
          authorAvatar: '/avatars/user1.jpg',
          authorBadges: ['Creator', 'Top Contributor'],
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
          metadata: {
            likes: 189,
            comments: 34,
            shares: 23,
            views: 567,
            bookmarks: 78,
            tags: ['neon', 'cyberpunk', 'artisan'],
            category: 'submissions',
            submissionId: 'neon-dreams-001',
            images: ['/submissions/neon1.jpg', '/submissions/neon2.jpg']
          },
          reactions: { '😍': 67, '🔥': 45, '💎': 23 },
          userInteractions: { liked: true, bookmarked: true, shared: false }
        },
        {
          id: '3',
          type: 'discussion',
          title: 'Best Switch Types for Gaming?',
          content: 'I\'m building my first gaming keyboard and need advice on switch types. Linear vs tactile vs clicky?',
          authorId: 'user2',
          authorName: 'Sarah Chen',
          authorAvatar: '/avatars/user2.jpg',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
          metadata: {
            likes: 45,
            comments: 89,
            shares: 12,
            views: 234,
            bookmarks: 34,
            tags: ['switches', 'gaming', 'advice'],
            category: 'discussions',
            discussionId: 'gaming-switches-help'
          },
          userInteractions: { liked: false, bookmarked: false, shared: false }
        },
        {
          id: '4',
          type: 'achievement',
          title: 'Community Milestone Reached!',
          content: 'We\'ve reached 10,000 active community members! Thank you all for making this community amazing.',
          authorId: 'admin',
          authorName: 'Syndicaps Team',
          authorAvatar: '/avatars/admin.jpg',
          authorBadges: ['Admin', 'Verified'],
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
          metadata: {
            likes: 567,
            comments: 123,
            shares: 234,
            views: 2345,
            bookmarks: 345,
            tags: ['milestone', 'community', 'celebration'],
            category: 'announcements'
          },
          isHighlighted: true,
          reactions: { '🎉': 123, '❤️': 89, '🚀': 67 },
          userInteractions: { liked: true, bookmarked: false, shared: true }
        }
      ]
      
      setActivities(mockActivities)
    } catch (error) {
      console.error('Failed to load activities:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search activities
  const filteredActivities = useMemo(() => {
    let filtered = activities

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(activity =>
        activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.authorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.metadata.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Type filter
    if (filters.type.length > 0) {
      filtered = filtered.filter(activity => filters.type.includes(activity.type))
    }

    // Date range filter
    const now = new Date()
    if (filters.dateRange !== 'all') {
      const cutoff = new Date()
      switch (filters.dateRange) {
        case 'today':
          cutoff.setHours(0, 0, 0, 0)
          break
        case 'week':
          cutoff.setDate(now.getDate() - 7)
          break
        case 'month':
          cutoff.setMonth(now.getMonth() - 1)
          break
      }
      filtered = filtered.filter(activity => activity.timestamp >= cutoff)
    }

    // Category filter
    if (filters.category.length > 0) {
      filtered = filtered.filter(activity => 
        activity.metadata.category && filters.category.includes(activity.metadata.category)
      )
    }

    // Sort
    filtered.sort((a, b) => {
      if (filters.showPinned && a.isPinned !== b.isPinned) {
        return a.isPinned ? -1 : 1
      }

      switch (filters.sortBy) {
        case 'popular':
          return (b.metadata.likes + b.metadata.comments) - (a.metadata.likes + a.metadata.comments)
        case 'trending':
          const aScore = (a.metadata.likes + a.metadata.comments + a.metadata.shares) / 
                        Math.max(1, (now.getTime() - a.timestamp.getTime()) / (1000 * 60 * 60))
          const bScore = (b.metadata.likes + b.metadata.comments + b.metadata.shares) / 
                        Math.max(1, (now.getTime() - b.timestamp.getTime()) / (1000 * 60 * 60))
          return bScore - aScore
        default: // recent
          return b.timestamp.getTime() - a.timestamp.getTime()
      }
    })

    return filtered
  }, [activities, searchQuery, filters])

  const handleLike = useCallback(async (activityId: string) => {
    setActivities(prev => prev.map(activity => {
      if (activity.id === activityId) {
        const isLiked = activity.userInteractions?.liked || false
        return {
          ...activity,
          metadata: {
            ...activity.metadata,
            likes: activity.metadata.likes + (isLiked ? -1 : 1)
          },
          userInteractions: {
            ...activity.userInteractions,
            liked: !isLiked
          }
        }
      }
      return activity
    }))
    
    announce('Activity liked')
  }, [announce])

  const handleBookmark = useCallback(async (activityId: string) => {
    setActivities(prev => prev.map(activity => {
      if (activity.id === activityId) {
        const isBookmarked = activity.userInteractions?.bookmarked || false
        return {
          ...activity,
          metadata: {
            ...activity.metadata,
            bookmarks: activity.metadata.bookmarks + (isBookmarked ? -1 : 1)
          },
          userInteractions: {
            ...activity.userInteractions,
            bookmarked: !isBookmarked
          }
        }
      }
      return activity
    }))
    
    announce(activities.find(a => a.id === activityId)?.userInteractions?.bookmarked ? 'Removed from bookmarks' : 'Added to bookmarks')
  }, [announce, activities])

  const handleShare = useCallback(async (activityId: string) => {
    // Implement share functionality
    const activity = activities.find(a => a.id === activityId)
    if (activity) {
      if (navigator.share) {
        await navigator.share({
          title: activity.title,
          text: activity.content,
          url: window.location.href
        })
      } else {
        await navigator.clipboard.writeText(window.location.href)
        announce('Link copied to clipboard')
      }
    }
  }, [activities, announce])

  const toggleExpanded = (activityId: string) => {
    setExpandedActivities(prev => {
      const newSet = new Set(prev)
      if (newSet.has(activityId)) {
        newSet.delete(activityId)
      } else {
        newSet.add(activityId)
      }
      return newSet
    })
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'challenge': return Target
      case 'submission': return Star
      case 'discussion': return MessageSquare
      case 'achievement': return Trophy
      case 'collaboration': return Users
      case 'announcement': return Zap
      default: return MessageSquare
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'challenge': return 'text-accent-400'
      case 'submission': return 'text-yellow-400'
      case 'discussion': return 'text-blue-400'
      case 'achievement': return 'text-purple-400'
      case 'collaboration': return 'text-green-400'
      case 'announcement': return 'text-orange-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Community Timeline</h1>
          <p className="text-gray-400">Stay updated with the latest community activities</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="border-gray-600 text-gray-300"
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {/* Search and Quick Filters */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search activities, people, or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 text-white"
              />
            </div>

            {/* Quick Filters */}
            <div className="flex items-center space-x-4">
              <Tabs value={filters.sortBy} onValueChange={(value: string) => setFilters(prev => ({ ...prev, sortBy: value as 'recent' | 'popular' | 'trending' }))}>
                <TabsList className="bg-gray-800">
                  <TabsTrigger value="recent" className="data-[state=active]:bg-accent-600">
                    <Clock className="w-4 h-4 mr-2" />
                    Recent
                  </TabsTrigger>
                  <TabsTrigger value="popular" className="data-[state=active]:bg-accent-600">
                    <Heart className="w-4 h-4 mr-2" />
                    Popular
                  </TabsTrigger>
                  <TabsTrigger value="trending" className="data-[state=active]:bg-accent-600">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Trending
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              <div className="flex items-center space-x-2">
                {['challenge', 'submission', 'discussion', 'achievement'].map(type => (
                  <Button
                    key={type}
                    variant={filters.type.includes(type) ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setFilters(prev => ({
                        ...prev,
                        type: prev.type.includes(type)
                          ? prev.type.filter(t => t !== type)
                          : [...prev.type, type]
                      }))
                    }}
                    className={filters.type.includes(type) ? "bg-accent-600" : "border-gray-600 text-gray-300"}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      <div className="space-y-4">
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <Card key={i} className="bg-gray-900/50 border-gray-700">
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-700 rounded-full"></div>
                      <div className="space-y-2 flex-1">
                        <div className="h-4 bg-gray-700 rounded w-1/4"></div>
                        <div className="h-3 bg-gray-700 rounded w-1/6"></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-700 rounded w-1/2"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredActivities.length === 0 ? (
          <Card className="bg-gray-900/50 border-gray-700">
            <CardContent className="p-12 text-center">
              <MessageSquare className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">No activities found</h3>
              <p className="text-gray-500">Try adjusting your search or filter criteria</p>
            </CardContent>
          </Card>
        ) : (
          <AnimatePresence>
            {filteredActivities.map((activity, index) => (
              <TimelineActivityCard
                key={activity.id}
                activity={activity}
                isExpanded={expandedActivities.has(activity.id)}
                onToggleExpanded={() => toggleExpanded(activity.id)}
                onLike={() => handleLike(activity.id)}
                onBookmark={() => handleBookmark(activity.id)}
                onShare={() => handleShare(activity.id)}
                index={index}
              />
            ))}
          </AnimatePresence>
        )}
      </div>
    </div>
  )
}

/**
 * Timeline Activity Card Component
 */
interface TimelineActivityCardProps {
  activity: TimelineActivity
  isExpanded: boolean
  onToggleExpanded: () => void
  onLike: () => void
  onBookmark: () => void
  onShare: () => void
  index: number
}

function TimelineActivityCard({
  activity,
  isExpanded,
  onToggleExpanded,
  onLike,
  onBookmark,
  onShare,
  index
}: TimelineActivityCardProps) {
  const Icon = getActivityIcon(activity.type)
  const iconColor = getActivityColor(activity.type)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ delay: index * 0.1 }}
    >
      <Card className={`bg-gray-900/50 border-gray-700 hover:border-gray-600 transition-all duration-300 ${
        activity.isHighlighted ? 'ring-2 ring-accent-500/30' : ''
      } ${activity.isPinned ? 'border-yellow-500/30' : ''}`}>
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-start space-x-3 flex-1">
              {/* Activity Icon */}
              <div className={`w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center ${iconColor}`}>
                <Icon className="w-5 h-5" />
              </div>

              {/* Author Info */}
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={activity.authorAvatar} />
                    <AvatarFallback className="text-xs">{activity.authorName.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span className="font-medium text-white">{activity.authorName}</span>
                  {activity.authorBadges?.map(badge => (
                    <Badge key={badge} variant="secondary" className="text-xs">
                      {badge}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <span>{formatDistanceToNow(activity.timestamp, { addSuffix: true, locale: id })}</span>
                  <span>•</span>
                  <Badge variant="outline" className="text-xs capitalize">
                    {activity.type}
                  </Badge>
                  {activity.metadata.category && (
                    <>
                      <span>•</span>
                      <span className="capitalize">{activity.metadata.category}</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Action Menu */}
            <div className="flex items-center space-x-2">
              {activity.isPinned && (
                <Pin className="w-4 h-4 text-yellow-500" />
              )}
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white p-1"
              >
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">{activity.title}</h3>
            <p className={`text-gray-300 ${isExpanded ? '' : 'line-clamp-3'}`}>
              {activity.content}
            </p>

            {activity.content.length > 200 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleExpanded}
                className="text-accent-400 hover:text-accent-300 p-0 h-auto mt-2"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="w-4 h-4 mr-1" />
                    Show less
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-4 h-4 mr-1" />
                    Show more
                  </>
                )}
              </Button>
            )}
          </div>

          {/* Media */}
          {activity.metadata.images && activity.metadata.images.length > 0 && (
            <div className="mb-4">
              <div className="grid grid-cols-2 gap-2">
                {activity.metadata.images.slice(0, 4).map((image, index) => (
                  <div key={index} className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src={image}
                      alt={`Activity image ${index + 1}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                ))}
              </div>
              {activity.metadata.images.length > 4 && (
                <p className="text-sm text-gray-400 mt-2">
                  +{activity.metadata.images.length - 4} more images
                </p>
              )}
            </div>
          )}

          {/* Tags */}
          {activity.metadata.tags && activity.metadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {activity.metadata.tags.map(tag => (
                <Badge key={tag} variant="outline" className="text-xs">
                  #{tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Reactions */}
          {activity.reactions && Object.keys(activity.reactions).length > 0 && (
            <div className="flex items-center space-x-2 mb-4">
              {Object.entries(activity.reactions).map(([emoji, count]) => (
                <Button
                  key={emoji}
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white p-1 h-auto"
                >
                  <span className="mr-1">{emoji}</span>
                  <span className="text-xs">{count}</span>
                </Button>
              ))}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-700">
            <div className="flex items-center space-x-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={onLike}
                className={`text-gray-400 hover:text-red-400 transition-colors ${
                  activity.userInteractions?.liked ? 'text-red-400' : ''
                }`}
              >
                <Heart className={`w-4 h-4 mr-2 ${activity.userInteractions?.liked ? 'fill-current' : ''}`} />
                {activity.metadata.likes}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-blue-400 transition-colors"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                {activity.metadata.comments}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={onShare}
                className="text-gray-400 hover:text-green-400 transition-colors"
              >
                <Share2 className="w-4 h-4 mr-2" />
                {activity.metadata.shares}
              </Button>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 text-sm text-gray-400">
                <Eye className="w-4 h-4" />
                <span>{activity.metadata.views}</span>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={onBookmark}
                className={`text-gray-400 hover:text-yellow-400 transition-colors ${
                  activity.userInteractions?.bookmarked ? 'text-yellow-400' : ''
                }`}
              >
                <Bookmark className={`w-4 h-4 ${activity.userInteractions?.bookmarked ? 'fill-current' : ''}`} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

function getActivityIcon(type: string) {
  switch (type) {
    case 'challenge': return Target
    case 'submission': return Star
    case 'discussion': return MessageSquare
    case 'achievement': return Trophy
    case 'collaboration': return Users
    case 'announcement': return Zap
    default: return MessageSquare
  }
}

function getActivityColor(type: string) {
  switch (type) {
    case 'challenge': return 'text-accent-400'
    case 'submission': return 'text-yellow-400'
    case 'discussion': return 'text-blue-400'
    case 'achievement': return 'text-purple-400'
    case 'collaboration': return 'text-green-400'
    case 'announcement': return 'text-orange-400'
    default: return 'text-gray-400'
  }
}
