/**
 * Enhanced Voting System Component
 * 
 * Advanced voting interface with multiple voting types, tier-based weighting,
 * real-time updates, and comprehensive analytics.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronUp,
  ChevronDown,
  Star,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Smile,
  Frown,
  Meh,
  Zap,
  Fire,
  Award,
  TrendingUp,
  BarChart3,
  Users,
  Clock,
  Shield
} from 'lucide-react'
import { useUser } from '@/lib/useUser'

interface VoteData {
  id: string
  type: 'updown' | 'star' | 'emoji' | 'poll'
  totalVotes: number
  userVote?: any
  breakdown: VoteBreakdown
  analytics: VoteAnalytics
  settings: VoteSettings
}

interface VoteBreakdown {
  upvotes?: number
  downvotes?: number
  stars?: { [key: number]: number } // 1-5 star ratings
  emojis?: { [key: string]: number } // emoji reactions
  pollOptions?: { [key: string]: number } // poll choices
}

interface VoteAnalytics {
  participationRate: number
  averageRating?: number
  trendDirection: 'up' | 'down' | 'stable'
  topContributors: Array<{
    userId: string
    userName: string
    userTier: string
    voteWeight: number
  }>
  timeDistribution: Array<{
    hour: number
    votes: number
  }>
}

interface VoteSettings {
  allowAnonymous: boolean
  tierWeighting: boolean
  showResults: 'always' | 'after_vote' | 'never'
  votingPeriod?: {
    start: Date
    end: Date
  }
  moderationEnabled: boolean
}

interface EnhancedVotingSystemProps {
  contentId: string
  contentType: 'post' | 'comment' | 'product' | 'poll'
  voteType: 'updown' | 'star' | 'emoji' | 'poll'
  pollOptions?: string[]
  initialData?: VoteData
  onVoteChange?: (voteData: VoteData) => void
  showAnalytics?: boolean
  className?: string
}

/**
 * Emoji reaction options
 */
const EMOJI_REACTIONS = [
  { emoji: '👍', name: 'thumbs_up', label: 'Like' },
  { emoji: '❤️', name: 'heart', label: 'Love' },
  { emoji: '😂', name: 'laugh', label: 'Funny' },
  { emoji: '😮', name: 'wow', label: 'Wow' },
  { emoji: '😢', name: 'sad', label: 'Sad' },
  { emoji: '😡', name: 'angry', label: 'Angry' },
  { emoji: '🔥', name: 'fire', label: 'Fire' },
  { emoji: '⚡', name: 'zap', label: 'Amazing' }
]

/**
 * Tier weight multipliers
 */
const TIER_WEIGHTS = {
  bronze: 1.0,
  silver: 1.2,
  gold: 1.5,
  platinum: 2.0
}

/**
 * Enhanced Voting System Component
 */
export const EnhancedVotingSystem: React.FC<EnhancedVotingSystemProps> = ({
  contentId,
  contentType,
  voteType,
  pollOptions = [],
  initialData,
  onVoteChange,
  showAnalytics = false,
  className = ''
}) => {
  const { user, profile } = useUser()
  const [voteData, setVoteData] = useState<VoteData>(
    initialData || {
      id: contentId,
      type: voteType,
      totalVotes: 0,
      breakdown: {},
      analytics: {
        participationRate: 0,
        trendDirection: 'stable',
        topContributors: [],
        timeDistribution: []
      },
      settings: {
        allowAnonymous: true,
        tierWeighting: true,
        showResults: 'after_vote',
        moderationEnabled: true
      }
    }
  )
  const [isVoting, setIsVoting] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [selectedPollOption, setSelectedPollOption] = useState<string>('')

  /**
   * Calculate user's vote weight based on tier
   */
  const getUserVoteWeight = useCallback(() => {
    if (!profile || !voteData.settings.tierWeighting) return 1.0
    return TIER_WEIGHTS[profile.tier as keyof typeof TIER_WEIGHTS] || 1.0
  }, [profile, voteData.settings.tierWeighting])

  /**
   * Handle vote submission
   */
  const handleVote = useCallback(async (vote: any) => {
    if (!user || isVoting) return

    setIsVoting(true)

    try {
      const voteWeight = getUserVoteWeight()
      
      // Optimistic update
      const updatedVoteData = { ...voteData }
      
      // Remove previous vote if exists
      if (voteData.userVote) {
        // Logic to remove previous vote from breakdown
        updatedVoteData.totalVotes -= voteWeight
      }

      // Add new vote
      updatedVoteData.userVote = vote
      updatedVoteData.totalVotes += voteWeight

      // Update breakdown based on vote type
      switch (voteType) {
        case 'updown':
          if (!updatedVoteData.breakdown.upvotes) updatedVoteData.breakdown.upvotes = 0
          if (!updatedVoteData.breakdown.downvotes) updatedVoteData.breakdown.downvotes = 0
          
          if (vote === 'up') {
            updatedVoteData.breakdown.upvotes += voteWeight
          } else {
            updatedVoteData.breakdown.downvotes += voteWeight
          }
          break

        case 'star':
          if (!updatedVoteData.breakdown.stars) updatedVoteData.breakdown.stars = {}
          updatedVoteData.breakdown.stars[vote] = (updatedVoteData.breakdown.stars[vote] || 0) + voteWeight
          break

        case 'emoji':
          if (!updatedVoteData.breakdown.emojis) updatedVoteData.breakdown.emojis = {}
          updatedVoteData.breakdown.emojis[vote] = (updatedVoteData.breakdown.emojis[vote] || 0) + voteWeight
          break

        case 'poll':
          if (!updatedVoteData.breakdown.pollOptions) updatedVoteData.breakdown.pollOptions = {}
          updatedVoteData.breakdown.pollOptions[vote] = (updatedVoteData.breakdown.pollOptions[vote] || 0) + voteWeight
          break
      }

      setVoteData(updatedVoteData)
      setShowResults(true)

      // Make API call to persist vote
      // await api.submitVote(contentId, contentType, vote, voteWeight)

      // Notify parent component
      onVoteChange?.(updatedVoteData)

    } catch (error) {
      console.error('Failed to submit vote:', error)
      // Revert optimistic update on error
    } finally {
      setIsVoting(false)
    }
  }, [user, isVoting, voteData, voteType, getUserVoteWeight, contentId, contentType, onVoteChange])

  /**
   * Render updown voting interface
   */
  const renderUpDownVoting = () => {
    const upvotes = voteData.breakdown.upvotes || 0
    const downvotes = voteData.breakdown.downvotes || 0
    const score = upvotes - downvotes

    return (
      <div className="flex items-center space-x-2">
        <button
          onClick={() => handleVote('up')}
          disabled={isVoting}
          className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
            voteData.userVote === 'up'
              ? 'bg-green-600 text-white'
              : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
          }`}
        >
          <ChevronUp size={16} />
          <span className="text-sm font-medium">{upvotes}</span>
        </button>

        <div className="flex flex-col items-center">
          <span className={`text-lg font-bold ${
            score > 0 ? 'text-green-500' : score < 0 ? 'text-red-500' : 'text-gray-400'
          }`}>
            {score > 0 ? '+' : ''}{score}
          </span>
          <span className="text-xs text-gray-500">score</span>
        </div>

        <button
          onClick={() => handleVote('down')}
          disabled={isVoting}
          className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
            voteData.userVote === 'down'
              ? 'bg-red-600 text-white'
              : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
          }`}
        >
          <ChevronDown size={16} />
          <span className="text-sm font-medium">{downvotes}</span>
        </button>
      </div>
    )
  }

  /**
   * Render star rating interface
   */
  const renderStarRating = () => {
    const stars = voteData.breakdown.stars || {}
    const totalStarVotes = Object.values(stars).reduce((sum, count) => sum + count, 0)
    const averageRating = totalStarVotes > 0 
      ? Object.entries(stars).reduce((sum, [rating, count]) => sum + (parseInt(rating) * count), 0) / totalStarVotes
      : 0

    return (
      <div className="space-y-3">
        {/* Star Selection */}
        <div className="flex items-center space-x-1">
          {[1, 2, 3, 4, 5].map((rating) => (
            <button
              key={rating}
              onClick={() => handleVote(rating)}
              disabled={isVoting}
              className={`p-1 transition-colors ${
                voteData.userVote >= rating
                  ? 'text-yellow-500'
                  : 'text-gray-600 hover:text-yellow-400'
              }`}
            >
              <Star size={20} className={voteData.userVote >= rating ? 'fill-current' : ''} />
            </button>
          ))}
        </div>

        {/* Rating Summary */}
        {showResults && totalStarVotes > 0 && (
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <Star size={14} className="text-yellow-500 fill-current" />
              <span className="text-white font-medium">
                {averageRating.toFixed(1)}
              </span>
            </div>
            <span className="text-gray-400">
              ({totalStarVotes} {totalStarVotes === 1 ? 'rating' : 'ratings'})
            </span>
          </div>
        )}
      </div>
    )
  }

  /**
   * Render emoji reactions interface
   */
  const renderEmojiReactions = () => {
    const emojis = voteData.breakdown.emojis || {}

    return (
      <div className="space-y-3">
        {/* Emoji Selection */}
        <div className="flex flex-wrap gap-2">
          {EMOJI_REACTIONS.map((reaction) => {
            const count = emojis[reaction.name] || 0
            const isSelected = voteData.userVote === reaction.name

            return (
              <button
                key={reaction.name}
                onClick={() => handleVote(reaction.name)}
                disabled={isVoting}
                className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                  isSelected
                    ? 'bg-accent-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                }`}
                title={reaction.label}
              >
                <span className="text-lg">{reaction.emoji}</span>
                {count > 0 && (
                  <span className="text-sm font-medium">{count}</span>
                )}
              </button>
            )
          })}
        </div>
      </div>
    )
  }

  /**
   * Render poll voting interface
   */
  const renderPollVoting = () => {
    const pollResults = voteData.breakdown.pollOptions || {}
    const totalPollVotes = Object.values(pollResults).reduce((sum, count) => sum + count, 0)

    return (
      <div className="space-y-3">
        {pollOptions.map((option, index) => {
          const votes = pollResults[option] || 0
          const percentage = totalPollVotes > 0 ? (votes / totalPollVotes) * 100 : 0
          const isSelected = voteData.userVote === option

          return (
            <div key={index} className="space-y-2">
              <button
                onClick={() => handleVote(option)}
                disabled={isVoting || !!voteData.userVote}
                className={`w-full text-left p-3 rounded-lg border transition-colors ${
                  isSelected
                    ? 'border-accent-500 bg-accent-500/10'
                    : 'border-gray-600 hover:border-gray-500 bg-gray-700'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-white">{option}</span>
                  {showResults && (
                    <span className="text-sm text-gray-400">
                      {votes} votes ({percentage.toFixed(1)}%)
                    </span>
                  )}
                </div>
              </button>

              {/* Progress Bar */}
              {showResults && totalPollVotes > 0 && (
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${percentage}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-accent-500 h-2 rounded-full"
                  />
                </div>
              )}
            </div>
          )
        })}

        {showResults && (
          <div className="text-sm text-gray-400 text-center pt-2">
            Total votes: {totalPollVotes}
          </div>
        )}
      </div>
    )
  }

  /**
   * Render voting analytics
   */
  const renderAnalytics = () => {
    if (!showAnalytics || !showResults) return null

    return (
      <div className="mt-6 p-4 bg-gray-700 rounded-lg">
        <h4 className="text-white font-medium mb-3 flex items-center space-x-2">
          <BarChart3 size={16} />
          <span>Voting Analytics</span>
        </h4>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Participation Rate</span>
            <p className="text-white font-medium">
              {voteData.analytics.participationRate.toFixed(1)}%
            </p>
          </div>

          <div>
            <span className="text-gray-400">Trend</span>
            <div className="flex items-center space-x-1">
              {voteData.analytics.trendDirection === 'up' && (
                <TrendingUp size={14} className="text-green-500" />
              )}
              <span className="text-white font-medium capitalize">
                {voteData.analytics.trendDirection}
              </span>
            </div>
          </div>

          {voteData.analytics.averageRating && (
            <div>
              <span className="text-gray-400">Average Rating</span>
              <p className="text-white font-medium">
                {voteData.analytics.averageRating.toFixed(1)}/5
              </p>
            </div>
          )}

          <div>
            <span className="text-gray-400">Total Votes</span>
            <p className="text-white font-medium">
              {voteData.totalVotes}
            </p>
          </div>
        </div>

        {/* Top Contributors */}
        {voteData.analytics.topContributors.length > 0 && (
          <div className="mt-4">
            <h5 className="text-gray-400 text-sm mb-2">Top Contributors</h5>
            <div className="space-y-1">
              {voteData.analytics.topContributors.slice(0, 3).map((contributor, index) => (
                <div key={contributor.userId} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-300">{contributor.userName}</span>
                    <span className="text-xs text-gray-500 capitalize">
                      {contributor.userTier}
                    </span>
                  </div>
                  <span className="text-gray-400">
                    {contributor.voteWeight.toFixed(1)}x weight
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  /**
   * Show results based on settings
   */
  useEffect(() => {
    if (voteData.settings.showResults === 'always') {
      setShowResults(true)
    } else if (voteData.settings.showResults === 'after_vote' && voteData.userVote) {
      setShowResults(true)
    }
  }, [voteData.settings.showResults, voteData.userVote])

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Voting Interface */}
      <div className="space-y-3">
        {voteType === 'updown' && renderUpDownVoting()}
        {voteType === 'star' && renderStarRating()}
        {voteType === 'emoji' && renderEmojiReactions()}
        {voteType === 'poll' && renderPollVoting()}
      </div>

      {/* Vote Weight Indicator */}
      {user && voteData.settings.tierWeighting && (
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Shield size={12} />
          <span>
            Your vote weight: {getUserVoteWeight().toFixed(1)}x ({profile?.tier || 'bronze'} tier)
          </span>
        </div>
      )}

      {/* Analytics */}
      {renderAnalytics()}

      {/* Voting Period */}
      {voteData.settings.votingPeriod && (
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Clock size={12} />
          <span>
            Voting ends: {voteData.settings.votingPeriod.end.toLocaleDateString()}
          </span>
        </div>
      )}
    </div>
  )
}

export default EnhancedVotingSystem
