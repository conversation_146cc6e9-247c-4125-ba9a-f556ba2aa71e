'use client'

/**
 * Interactive Points Guide Component
 * 
 * Collapsible section providing comprehensive breakdown of all point-earning
 * activities with smooth animations and engaging visual design.
 * 
 * Features:
 * - Collapsible/expandable interface with smooth animations
 * - Comprehensive breakdown of all point-earning activities
 * - Categorized point earning methods with visual icons
 * - Tips and examples for maximizing point earnings
 * - Direct links to relevant sections for immediate action
 * - Mobile-responsive design with touch-friendly interactions
 * 
 * Technical Implementation:
 * - Uses Framer Motion for smooth collapse/expand animations
 * - Implements proper ARIA labels for accessibility
 * - Supports keyboard navigation and screen readers
 * - Integrates with existing routing and navigation
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2.1.0
 */

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChevronDown, 
  ChevronUp,
  Star, 
  User, 
  Mail, 
  Gift,
  ShoppingBag,
  MessageSquare,
  Users,
  Share2,
  Calendar,
  Target,
  ExternalLink,
  Lightbulb,
  Zap
} from 'lucide-react'
import Link from 'next/link'

interface InteractivePointsGuideProps {
  /** Custom CSS classes */
  className?: string
  /** Initial expanded state (default: false) */
  initialExpanded?: boolean
  /** Whether to show tips and examples (default: true) */
  showTips?: boolean
}

interface PointEarningMethod {
  id: string
  category: 'engagement' | 'purchases' | 'social' | 'special'
  name: string
  points: number | string
  description: string
  icon: React.ReactNode
  link?: string
  linkText?: string
  tip?: string
  example?: string
}

/**
 * Animation variants for the collapsible content
 */
const contentVariants = {
  collapsed: {
    height: 0,
    opacity: 0,
    transition: {
      height: { duration: 0.4, ease: 'easeInOut' },
      opacity: { duration: 0.3, ease: 'easeInOut' }
    }
  },
  expanded: {
    height: 'auto',
    opacity: 1,
    transition: {
      height: { duration: 0.4, ease: 'easeInOut' },
      opacity: { duration: 0.3, ease: 'easeInOut', delay: 0.1 }
    }
  }
}

const staggerContainer = {
  expanded: {
    transition: {
      staggerChildren: 0.05
    }
  }
}

const itemVariants = {
  collapsed: { opacity: 0, y: -10 },
  expanded: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.3, ease: 'easeOut' }
  }
}

const buttonVariants = {
  hover: { scale: 1.02 },
  tap: { scale: 0.98 }
}

/**
 * Interactive Points Guide Component
 * 
 * @param props - Component props
 * @returns JSX.Element - Rendered points guide
 */
const InteractivePointsGuide: React.FC<InteractivePointsGuideProps> = ({
  className = '',
  initialExpanded = false,
  showTips = true
}) => {
  // ===== STATE =====
  
  const [isExpanded, setIsExpanded] = useState(initialExpanded)

  // ===== DATA =====

  const pointEarningMethods: PointEarningMethod[] = [
    // Engagement Category
    {
      id: 'signup',
      category: 'engagement',
      name: 'Account Registration',
      points: 200,
      description: 'Welcome bonus for joining our community',
      icon: <User className="w-5 h-5" />,
      link: '/auth/register',
      linkText: 'Sign Up Now',
      tip: 'Complete your profile after signing up for additional points!'
    },
    {
      id: 'profile_completion',
      category: 'engagement',
      name: 'Profile Completion',
      points: 50,
      description: 'Complete your user profile with all details',
      icon: <User className="w-5 h-5" />,
      link: '/profile',
      linkText: 'Complete Profile',
      example: 'Add your name, bio, and preferences'
    },
    {
      id: 'newsletter',
      category: 'engagement',
      name: 'Newsletter Subscription',
      points: 100,
      description: 'Stay updated with our latest news and releases',
      icon: <Mail className="w-5 h-5" />,
      tip: 'Get exclusive deals and early access to new products!'
    },
    {
      id: 'daily_login',
      category: 'engagement',
      name: 'Daily Login',
      points: 5,
      description: 'Visit our site daily to earn consistent points',
      icon: <Calendar className="w-5 h-5" />,
      example: 'Login streak bonuses available for consecutive days'
    },
    
    // Purchases Category
    {
      id: 'purchases',
      category: 'purchases',
      name: 'Product Purchases',
      points: '$1 = 1pt',
      description: 'Earn points on every dollar spent',
      icon: <ShoppingBag className="w-5 h-5" />,
      link: '/shop',
      linkText: 'Shop Now',
      tip: 'Look for double point events and special promotions!'
    },
    {
      id: 'reviews',
      category: 'purchases',
      name: 'Product Reviews',
      points: '20-70',
      description: 'Write detailed reviews for products you\'ve purchased',
      icon: <MessageSquare className="w-5 h-5" />,
      example: 'Detailed reviews with photos earn maximum points'
    },
    
    // Social Category
    {
      id: 'referrals',
      category: 'social',
      name: 'Friend Referrals',
      points: 500,
      description: 'Invite friends to join our community',
      icon: <Users className="w-5 h-5" />,
      link: '/referrals',
      linkText: 'Invite Friends',
      tip: 'Both you and your friend earn points when they make their first purchase!'
    },
    {
      id: 'social_sharing',
      category: 'social',
      name: 'Social Media Sharing',
      points: 150,
      description: 'Share products or achievements on social media',
      icon: <Share2 className="w-5 h-5" />,
      example: 'Share on Instagram, Twitter, or Facebook'
    },
    
    // Special Category
    {
      id: 'birthday',
      category: 'special',
      name: 'Birthday Bonus',
      points: 500,
      description: 'Special birthday gift from our community',
      icon: <Gift className="w-5 h-5" />,
      tip: 'Make sure to add your birthday to your profile!'
    },
    {
      id: 'monthly_missions',
      category: 'special',
      name: 'Monthly Missions',
      points: '200-500',
      description: 'Complete special monthly challenges and objectives',
      icon: <Target className="w-5 h-5" />,
      example: 'Missions change monthly - check back regularly!'
    }
  ]

  // Group methods by category
  const categorizedMethods = {
    engagement: pointEarningMethods.filter(method => method.category === 'engagement'),
    purchases: pointEarningMethods.filter(method => method.category === 'purchases'),
    social: pointEarningMethods.filter(method => method.category === 'social'),
    special: pointEarningMethods.filter(method => method.category === 'special')
  }

  // ===== HANDLERS =====

  /**
   * Toggle expanded state
   */
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev)
  }, [])

  /**
   * Handle keyboard navigation
   */
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      toggleExpanded()
    }
  }, [toggleExpanded])

  // ===== HELPER FUNCTIONS =====

  /**
   * Get category display information
   */
  const getCategoryInfo = (category: string) => {
    switch (category) {
      case 'engagement':
        return { name: 'Engagement Activities', color: 'text-blue-400', bgColor: 'bg-blue-500/10' }
      case 'purchases':
        return { name: 'Shopping & Reviews', color: 'text-green-400', bgColor: 'bg-green-500/10' }
      case 'social':
        return { name: 'Social Activities', color: 'text-purple-400', bgColor: 'bg-purple-500/10' }
      case 'special':
        return { name: 'Special Bonuses', color: 'text-yellow-400', bgColor: 'bg-yellow-500/10' }
      default:
        return { name: 'Other', color: 'text-gray-400', bgColor: 'bg-gray-500/10' }
    }
  }

  // ===== RENDER =====

  return (
    <div className={`interactive-points-guide ${className}`}>
      <div className="bg-gradient-to-br from-gray-800 via-gray-800 to-gray-900 rounded-lg border border-gray-700 overflow-hidden">
        {/* Toggle Button Header */}
        <motion.button
          className="w-full p-6 text-left hover:bg-gray-700/30 transition-colors focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-inset"
          onClick={toggleExpanded}
          onKeyDown={handleKeyDown}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          aria-expanded={isExpanded}
          aria-controls="points-guide-content"
          role="button"
          tabIndex={0}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center">
                <Lightbulb className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl sm:text-2xl font-bold text-white mb-1">
                  How to Earn Points
                </h2>
                <p className="text-gray-400 text-sm sm:text-base">
                  Discover all the ways to earn points and climb the leaderboard
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="hidden sm:flex items-center space-x-2 text-accent-400">
                <Zap className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {isExpanded ? 'Hide Guide' : 'Show Guide'}
                </span>
              </div>
              <motion.div
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              >
                <ChevronDown className="w-6 h-6 text-gray-400" />
              </motion.div>
            </div>
          </div>
        </motion.button>

        {/* Collapsible Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              id="points-guide-content"
              className="overflow-hidden"
              variants={contentVariants}
              initial="collapsed"
              animate="expanded"
              exit="collapsed"
            >
              <div className="p-6 pt-0 border-t border-gray-700">
                <motion.div
                  className="space-y-8"
                  variants={staggerContainer}
                  initial="collapsed"
                  animate="expanded"
                >
                  {/* Categories */}
                  {Object.entries(categorizedMethods).map(([category, methods]) => {
                    const categoryInfo = getCategoryInfo(category)
                    
                    return (
                      <motion.div
                        key={category}
                        className="category-section"
                        variants={itemVariants}
                      >
                        <div className={`flex items-center space-x-3 mb-4 p-3 rounded-lg ${categoryInfo.bgColor}`}>
                          <h3 className={`text-lg font-semibold ${categoryInfo.color}`}>
                            {categoryInfo.name}
                          </h3>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {methods.map(method => (
                            <motion.div
                              key={method.id}
                              className="method-card bg-gray-700/30 rounded-lg p-4 border border-gray-600/50 hover:border-gray-500/50 transition-colors"
                              variants={itemVariants}
                            >
                              <div className="flex items-start space-x-3">
                                <div className={`w-10 h-10 ${categoryInfo.bgColor} rounded-lg flex items-center justify-center flex-shrink-0`}>
                                  <div className={categoryInfo.color}>
                                    {method.icon}
                                  </div>
                                </div>
                                
                                <div className="flex-grow min-w-0">
                                  <div className="flex items-center justify-between mb-2">
                                    <h4 className="text-white font-medium text-sm">
                                      {method.name}
                                    </h4>
                                    <div className="flex items-center space-x-1 flex-shrink-0">
                                      <Star className="w-4 h-4 text-accent-500" fill="currentColor" />
                                      <span className="text-accent-400 font-bold text-sm">
                                        {method.points}
                                      </span>
                                    </div>
                                  </div>
                                  
                                  <p className="text-gray-300 text-xs mb-3 leading-relaxed">
                                    {method.description}
                                  </p>
                                  
                                  {/* Tips and Examples */}
                                  {showTips && (method.tip || method.example) && (
                                    <div className="space-y-2 mb-3">
                                      {method.tip && (
                                        <div className="flex items-start space-x-2">
                                          <Lightbulb className="w-3 h-3 text-yellow-400 mt-0.5 flex-shrink-0" />
                                          <p className="text-yellow-300 text-xs">
                                            <span className="font-medium">Tip:</span> {method.tip}
                                          </p>
                                        </div>
                                      )}
                                      {method.example && (
                                        <div className="flex items-start space-x-2">
                                          <Target className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                                          <p className="text-blue-300 text-xs">
                                            <span className="font-medium">Example:</span> {method.example}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  
                                  {/* Action Link */}
                                  {method.link && method.linkText && (
                                    <Link
                                      href={method.link}
                                      className="inline-flex items-center space-x-1 text-accent-400 hover:text-accent-300 text-xs font-medium transition-colors"
                                    >
                                      <span>{method.linkText}</span>
                                      <ExternalLink className="w-3 h-3" />
                                    </Link>
                                  )}
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )
                  })}

                  {/* Footer Call to Action */}
                  <motion.div
                    className="text-center p-6 bg-gradient-to-r from-accent-900/30 to-accent-800/30 rounded-lg border border-accent-500/20"
                    variants={itemVariants}
                  >
                    <h3 className="text-white font-bold text-lg mb-2">
                      Ready to Start Earning?
                    </h3>
                    <p className="text-gray-300 text-sm mb-4">
                      Join our community and start earning points today!
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Link
                        href="/shop"
                        className="btn-primary flex items-center justify-center space-x-2"
                      >
                        <ShoppingBag className="w-4 h-4" />
                        <span>Start Shopping</span>
                      </Link>
                      <Link
                        href="/profile"
                        className="btn-secondary flex items-center justify-center space-x-2"
                      >
                        <User className="w-4 h-4" />
                        <span>Complete Profile</span>
                      </Link>
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default InteractivePointsGuide
