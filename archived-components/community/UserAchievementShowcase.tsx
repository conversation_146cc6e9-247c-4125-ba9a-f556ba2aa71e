'use client'

/**
 * User Achievement Showcase Component
 * 
 * Displays a comprehensive gallery of achievements with progress tracking,
 * categories, and user completion statistics.
 * 
 * Features:
 * - Achievement gallery with categories and difficulty levels
 * - Progress tracking and completion rates
 * - Recent achievement unlocks and celebrations
 * - Achievement search and filtering
 * - User achievement statistics and comparisons
 * - Interactive achievement details and requirements
 * 
 * Technical Implementation:
 * - Integrates with achievement system API
 * - Uses Framer Motion for engaging animations
 * - Supports grid and list view modes
 * - Implements achievement unlock celebrations
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2.1.0
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Trophy, 
  Award, 
  Star, 
  Lock, 
  Unlock,
  Search, 
  Filter,
  Grid,
  List,
  TrendingUp,
  Users,
  Calendar,
  Target,
  CheckCircle,
  Clock
} from 'lucide-react'
import { useUser } from '../../lib/useUser'
import { useAchievements } from '../../hooks/useGamificationAPI'

interface UserAchievementShowcaseProps {
  /** Custom CSS classes */
  className?: string
  /** View mode (default: 'grid') */
  viewMode?: 'grid' | 'list'
  /** Whether to show user-specific progress (default: true) */
  showUserProgress?: boolean
}

interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'purchase' | 'engagement' | 'social' | 'milestone' | 'special'
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary'
  points: number
  requirements: {
    type: 'count' | 'value' | 'streak' | 'date'
    target: number
    action: string
  }[]
  unlockRate: number // Percentage of users who have unlocked this
  isUnlocked?: boolean
  progress?: number
  unlockedAt?: Date
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
}

interface AchievementStats {
  totalAchievements: number
  unlockedAchievements: number
  totalPoints: number
  earnedPoints: number
  completionRate: number
  rank: number
  recentUnlocks: Achievement[]
}

/**
 * Animation variants for achievement cards
 */
const cardVariants = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: {
      duration: 0.3,
      ease: 'easeOut'
    }
  },
  exit: { 
    opacity: 0, 
    scale: 0.9,
    transition: { duration: 0.2 }
  }
}

const unlockCelebrationVariants = {
  initial: { scale: 0, rotate: -180 },
  animate: { 
    scale: [0, 1.2, 1], 
    rotate: [0, 360, 0],
    transition: {
      duration: 0.8,
      ease: 'easeOut'
    }
  }
}

/**
 * User Achievement Showcase Component
 * 
 * @param props - Component props
 * @returns JSX.Element - Rendered achievement showcase
 */
const UserAchievementShowcase: React.FC<UserAchievementShowcaseProps> = ({
  className = '',
  viewMode = 'grid',
  showUserProgress = true
}) => {
  // ===== HOOKS =====
  
  const { user } = useUser()
  const { achievements, loading, error } = useAchievements()
  
  // ===== STATE =====
  
  const [achievementData, setAchievementData] = useState<Achievement[]>([])
  const [filteredAchievements, setFilteredAchievements] = useState<Achievement[]>([])
  const [achievementStats, setAchievementStats] = useState<AchievementStats | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterDifficulty, setFilterDifficulty] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'unlocked' | 'locked'>('all')
  const [currentViewMode, setCurrentViewMode] = useState(viewMode)

  // ===== EFFECTS =====

  /**
   * Generate mock achievement data
   */
  useEffect(() => {
    const mockAchievements: Achievement[] = [
      {
        id: '1',
        name: 'First Steps',
        description: 'Complete your first purchase',
        icon: '🎯',
        category: 'purchase',
        difficulty: 'easy',
        points: 100,
        requirements: [{ type: 'count', target: 1, action: 'purchase' }],
        unlockRate: 85,
        isUnlocked: true,
        progress: 1,
        unlockedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        rarity: 'common'
      },
      {
        id: '2',
        name: 'Social Butterfly',
        description: 'Share 5 products on social media',
        icon: '🦋',
        category: 'social',
        difficulty: 'medium',
        points: 250,
        requirements: [{ type: 'count', target: 5, action: 'social_share' }],
        unlockRate: 45,
        isUnlocked: false,
        progress: 3,
        rarity: 'uncommon'
      },
      {
        id: '3',
        name: 'Keycap Collector',
        description: 'Purchase 10 different keycap sets',
        icon: '⌨️',
        category: 'purchase',
        difficulty: 'hard',
        points: 500,
        requirements: [{ type: 'count', target: 10, action: 'unique_purchase' }],
        unlockRate: 15,
        isUnlocked: false,
        progress: 4,
        rarity: 'rare'
      },
      {
        id: '4',
        name: 'Review Master',
        description: 'Write 20 detailed product reviews',
        icon: '⭐',
        category: 'engagement',
        difficulty: 'hard',
        points: 400,
        requirements: [{ type: 'count', target: 20, action: 'review' }],
        unlockRate: 25,
        isUnlocked: true,
        progress: 20,
        unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        rarity: 'rare'
      },
      {
        id: '5',
        name: 'Legend',
        description: 'Reach 10,000 total points',
        icon: '👑',
        category: 'milestone',
        difficulty: 'legendary',
        points: 1000,
        requirements: [{ type: 'value', target: 10000, action: 'total_points' }],
        unlockRate: 2,
        isUnlocked: false,
        progress: 2450,
        rarity: 'legendary'
      },
      {
        id: '6',
        name: 'Early Bird',
        description: 'Join the community in the first month',
        icon: '🐦',
        category: 'special',
        difficulty: 'medium',
        points: 300,
        requirements: [{ type: 'date', target: 1, action: 'early_signup' }],
        unlockRate: 8,
        isUnlocked: true,
        progress: 1,
        unlockedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
        rarity: 'epic'
      }
    ]

    setAchievementData(mockAchievements)

    // Calculate stats
    const unlockedCount = mockAchievements.filter(a => a.isUnlocked).length
    const totalPoints = mockAchievements.reduce((sum, a) => sum + a.points, 0)
    const earnedPoints = mockAchievements.filter(a => a.isUnlocked).reduce((sum, a) => sum + a.points, 0)
    const recentUnlocks = mockAchievements
      .filter(a => a.isUnlocked && a.unlockedAt)
      .sort((a, b) => (b.unlockedAt?.getTime() || 0) - (a.unlockedAt?.getTime() || 0))
      .slice(0, 3)

    setAchievementStats({
      totalAchievements: mockAchievements.length,
      unlockedAchievements: unlockedCount,
      totalPoints,
      earnedPoints,
      completionRate: Math.round((unlockedCount / mockAchievements.length) * 100),
      rank: 12, // Mock rank
      recentUnlocks
    })
  }, [user])

  /**
   * Filter achievements based on search and filters
   */
  useEffect(() => {
    let filtered = [...achievementData]

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(achievement =>
        achievement.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        achievement.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply category filter
    if (filterCategory !== 'all') {
      filtered = filtered.filter(achievement => achievement.category === filterCategory)
    }

    // Apply difficulty filter
    if (filterDifficulty !== 'all') {
      filtered = filtered.filter(achievement => achievement.difficulty === filterDifficulty)
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(achievement => 
        filterStatus === 'unlocked' ? achievement.isUnlocked : !achievement.isUnlocked
      )
    }

    setFilteredAchievements(filtered)
  }, [achievementData, searchTerm, filterCategory, filterDifficulty, filterStatus])

  // ===== HELPER FUNCTIONS =====

  /**
   * Get difficulty color
   */
  const getDifficultyColor = (difficulty: Achievement['difficulty']) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 bg-green-500/20'
      case 'medium': return 'text-yellow-400 bg-yellow-500/20'
      case 'hard': return 'text-red-400 bg-red-500/20'
      case 'legendary': return 'text-purple-400 bg-purple-500/20'
    }
  }

  /**
   * Get category color
   */
  const getCategoryColor = (category: Achievement['category']) => {
    switch (category) {
      case 'purchase': return 'text-blue-400 bg-blue-500/20'
      case 'engagement': return 'text-green-400 bg-green-500/20'
      case 'social': return 'text-purple-400 bg-purple-500/20'
      case 'milestone': return 'text-yellow-400 bg-yellow-500/20'
      case 'special': return 'text-pink-400 bg-pink-500/20'
    }
  }

  /**
   * Get rarity color
   */
  const getRarityColor = (rarity: Achievement['rarity']) => {
    switch (rarity) {
      case 'common': return 'border-gray-500'
      case 'uncommon': return 'border-green-500'
      case 'rare': return 'border-blue-500'
      case 'epic': return 'border-purple-500'
      case 'legendary': return 'border-yellow-500'
    }
  }

  /**
   * Calculate progress percentage
   */
  const getProgressPercentage = (achievement: Achievement) => {
    if (!achievement.progress || !achievement.requirements[0]) return 0
    return Math.min((achievement.progress / achievement.requirements[0].target) * 100, 100)
  }

  // ===== RENDER =====

  if (loading) {
    return (
      <div className={`user-achievement-showcase ${className}`}>
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading achievements...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`user-achievement-showcase ${className}`}>
      {/* Achievement Stats */}
      {achievementStats && showUserProgress && (
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-white mb-2">Your Achievement Progress</h2>
              <p className="text-gray-400">Track your progress and unlock new achievements</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-accent-400">
                {achievementStats.completionRate}%
              </div>
              <div className="text-gray-400 text-sm">Complete</div>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">
                {achievementStats.unlockedAchievements}
              </div>
              <div className="text-gray-400 text-sm">Unlocked</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">
                {achievementStats.totalAchievements}
              </div>
              <div className="text-gray-400 text-sm">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-accent-400 mb-1">
                {achievementStats.earnedPoints}
              </div>
              <div className="text-gray-400 text-sm">Points Earned</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">
                #{achievementStats.rank}
              </div>
              <div className="text-gray-400 text-sm">Rank</div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
            <motion.div
              className="bg-gradient-to-r from-accent-500 to-accent-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${achievementStats.completionRate}%` }}
              transition={{ duration: 1, ease: 'easeOut' }}
            />
          </div>

          {/* Recent unlocks */}
          {achievementStats.recentUnlocks.length > 0 && (
            <div>
              <h3 className="text-white font-medium mb-3">Recent Unlocks</h3>
              <div className="flex space-x-3">
                {achievementStats.recentUnlocks.map(achievement => (
                  <div
                    key={achievement.id}
                    className="flex items-center space-x-2 bg-gray-700/50 rounded-lg p-2"
                  >
                    <span className="text-lg">{achievement.icon}</span>
                    <div>
                      <div className="text-white text-sm font-medium">{achievement.name}</div>
                      <div className="text-gray-400 text-xs">
                        {achievement.unlockedAt?.toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Filters and Search */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-bold text-white">All Achievements</h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentViewMode('grid')}
                className={`p-2 rounded-lg transition-colors ${
                  currentViewMode === 'grid' 
                    ? 'bg-accent-600 text-white' 
                    : 'bg-gray-700 text-gray-400 hover:text-white'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setCurrentViewMode('list')}
                className={`p-2 rounded-lg transition-colors ${
                  currentViewMode === 'list' 
                    ? 'bg-accent-600 text-white' 
                    : 'bg-gray-700 text-gray-400 hover:text-white'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search achievements..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-500 w-full sm:w-64"
              />
            </div>

            {/* Filters */}
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-accent-500"
            >
              <option value="all">All Categories</option>
              <option value="purchase">Purchase</option>
              <option value="engagement">Engagement</option>
              <option value="social">Social</option>
              <option value="milestone">Milestone</option>
              <option value="special">Special</option>
            </select>

            <select
              value={filterDifficulty}
              onChange={(e) => setFilterDifficulty(e.target.value)}
              className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-accent-500"
            >
              <option value="all">All Difficulties</option>
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
              <option value="legendary">Legendary</option>
            </select>

            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as 'all' | 'unlocked' | 'locked')}
              className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-accent-500"
            >
              <option value="all">All Status</option>
              <option value="unlocked">Unlocked</option>
              <option value="locked">Locked</option>
            </select>
          </div>
        </div>
      </div>

      {/* Achievement Grid/List */}
      <div className={`achievement-container ${
        currentViewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
          : 'space-y-4'
      }`}>
        <AnimatePresence>
          {filteredAchievements.map((achievement, index) => (
            <motion.div
              key={achievement.id}
              className={`achievement-card bg-gray-800 rounded-lg border-2 ${getRarityColor(achievement.rarity)} overflow-hidden ${
                achievement.isUnlocked ? 'opacity-100' : 'opacity-75'
              }`}
              variants={cardVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              layout
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="text-4xl"
                      variants={achievement.isUnlocked ? unlockCelebrationVariants : undefined}
                      initial={achievement.isUnlocked ? "initial" : undefined}
                      animate={achievement.isUnlocked ? "animate" : undefined}
                    >
                      {achievement.icon}
                    </motion.div>
                    <div>
                      <h3 className="text-white font-bold">{achievement.name}</h3>
                      <p className="text-gray-400 text-sm">{achievement.description}</p>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    {achievement.isUnlocked ? (
                      <CheckCircle className="w-6 h-6 text-green-400" />
                    ) : (
                      <Lock className="w-6 h-6 text-gray-500" />
                    )}
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(achievement.category)}`}>
                    {achievement.category}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(achievement.difficulty)}`}>
                    {achievement.difficulty}
                  </span>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
                    {achievement.unlockRate}% unlocked
                  </span>
                </div>

                {/* Progress */}
                {!achievement.isUnlocked && achievement.progress !== undefined && achievement.requirements[0] && (
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-400 text-sm">Progress</span>
                      <span className="text-white text-sm font-medium">
                        {achievement.progress}/{achievement.requirements[0].target}
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-accent-500 to-accent-600 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${getProgressPercentage(achievement)}%` }}
                        transition={{ duration: 0.8, ease: 'easeOut' }}
                      />
                    </div>
                  </div>
                )}

                {/* Points reward */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-accent-500" fill="currentColor" />
                    <span className="text-accent-400 font-medium">{achievement.points} points</span>
                  </div>
                  {achievement.isUnlocked && achievement.unlockedAt && (
                    <span className="text-gray-400 text-xs">
                      Unlocked {achievement.unlockedAt.toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {filteredAchievements.length === 0 && (
        <div className="text-center py-12">
          <Trophy className="w-16 h-16 text-gray-500 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-400 mb-2">No achievements found</h3>
          <p className="text-gray-500">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  )
}

export default UserAchievementShowcase
