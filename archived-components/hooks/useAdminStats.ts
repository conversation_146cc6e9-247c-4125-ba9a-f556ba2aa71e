/**
 * Admin Statistics Hook
 *
 * Custom React hook for fetching and managing admin dashboard statistics.
 * Provides real-time statistics data for the admin dashboard with loading
 * states and error handling.
 *
 * Features:
 * - Dashboard statistics fetching
 * - Real-time data updates
 * - Loading state management
 * - Error handling
 * - Automatic data refresh
 *
 * <AUTHOR> Team
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { getAdminStats, AdminStats } from '../lib/adminFirestore'

/**
 * Admin statistics hook return type
 */
interface UseAdminStatsReturn {
  /** Statistics data */
  stats: AdminStats | null
  /** Loading state */
  loading: boolean
  /** Error state */
  error: string | null
  /** Refresh statistics */
  refresh: () => Promise<void>
  /** Last updated timestamp */
  lastUpdated: Date | null
}

/**
 * Custom hook for admin dashboard statistics
 *
 * @param autoRefresh - Whether to automatically refresh data
 * @param refreshInterval - Refresh interval in milliseconds (default: 5 minutes)
 * @returns UseAdminStatsReturn - Statistics state and controls
 */
export const useAdminStats = (
  autoRefresh: boolean = true,
  refreshInterval: number = 5 * 60 * 1000 // 5 minutes
): UseAdminStatsReturn => {
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  /**
   * Fetch statistics data
   */
  const fetchStats = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const statsData = await getAdminStats()
      setStats(statsData)
      setLastUpdated(new Date())
    } catch (err) {
      console.error('Error fetching admin stats:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch statistics')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * Refresh statistics manually
   */
  const refresh = useCallback(async () => {
    await fetchStats()
  }, [fetchStats])

  // Initial fetch
  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      fetchStats()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchStats])

  return {
    stats,
    loading,
    error,
    refresh,
    lastUpdated
  }
}

/**
 * Hook for specific statistic values with formatting
 *
 * @returns Object with formatted statistics
 */
export const useFormattedAdminStats = () => {
  const { stats, loading, error } = useAdminStats()

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const formattedStats = stats ? {
    totalProducts: formatNumber(stats.totalProducts),
    totalOrders: formatNumber(stats.totalOrders),
    totalUsers: formatNumber(stats.totalUsers),
    pendingReviews: formatNumber(stats.pendingReviews),
    totalRaffleEntries: formatNumber(stats.totalRaffleEntries),
    totalRevenue: formatCurrency(stats.totalRevenue),
    recentOrders: formatNumber(stats.recentOrders),
    recentRevenue: formatCurrency(stats.recentRevenue)
  } : null

  return {
    stats: formattedStats,
    rawStats: stats,
    loading,
    error
  }
}
