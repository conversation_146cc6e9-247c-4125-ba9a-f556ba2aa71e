/**
 * Cache-Aware Data Hooks
 * 
 * React hooks that integrate intelligent caching with data fetching
 * for improved performance and offline capabilities.
 * 
 * Features:
 * - Intelligent caching with TTL
 * - Offline-first strategies
 * - Cache invalidation
 * - Prefetching
 * - Loading states
 * 
 * <AUTHOR> Team
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { globalCache, globalDataFetcher } from '@/lib/cache/intelligentCache'
import { globalOfflineCache, globalOfflineDataFetcher } from '@/lib/cache/offlineCache'
import { usePerformanceMonitor } from '@/lib/performance/stateOptimization'

/**
 * Cache strategy options
 */
export enum CacheStrategy {
  CACHE_FIRST = 'CACHE_FIRST',
  NETWORK_FIRST = 'NETWORK_FIRST',
  OFFLINE_FIRST = 'OFFLINE_FIRST',
  CACHE_ONLY = 'CACHE_ONLY',
  NETWORK_ONLY = 'NETWORK_ONLY'
}

/**
 * Cache options interface
 */
interface CacheOptions {
  strategy?: CacheStrategy
  ttl?: number
  tags?: string[]
  priority?: number
  enableOffline?: boolean
  prefetch?: boolean
  staleWhileRevalidate?: boolean
}

/**
 * Hook return type
 */
interface UseCachedDataReturn<T> {
  data: T | null
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
  invalidate: () => void
  prefetch: () => Promise<void>
  cacheStats: {
    hitRate: number
    lastUpdated: number | null
    isFromCache: boolean
  }
}

/**
 * Main cached data hook
 */
export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {}
): UseCachedDataReturn<T> {
  const {
    strategy = CacheStrategy.CACHE_FIRST,
    ttl = 300000, // 5 minutes
    tags = [],
    priority = 1,
    enableOffline = true,
    prefetch = false,
    staleWhileRevalidate = false
  } = options

  // Performance monitoring
  usePerformanceMonitor('useCachedData', process.env.NODE_ENV === 'development')

  // State management
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [cacheStats, setCacheStats] = useState({
    hitRate: 0,
    lastUpdated: null as number | null,
    isFromCache: false
  })

  // Refs for cleanup and tracking
  const abortControllerRef = useRef<AbortController | null>(null)
  const mountedRef = useRef(true)

  /**
   * Fetch data based on strategy
   */
  const fetchData = useCallback(async (forceRefresh = false) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setLoading(true)
    setError(null)

    try {
      let result: T
      let isFromCache = false

      switch (strategy) {
        case CacheStrategy.CACHE_FIRST:
          result = await globalDataFetcher.fetchWithCache(
            key,
            fetcher,
            { ttl, tags, priority, forceRefresh }
          )
          isFromCache = !forceRefresh && await globalCache.get(key) !== null
          break

        case CacheStrategy.NETWORK_FIRST:
          try {
            result = await fetcher()
            globalCache.set(key, result, { ttl, tags, priority })
            isFromCache = false
          } catch (networkError) {
            const cached = await globalCache.get<T>(key)
            if (cached !== null) {
              result = cached
              isFromCache = true
            } else {
              throw networkError
            }
          }
          break

        case CacheStrategy.OFFLINE_FIRST:
          if (enableOffline) {
            result = await globalOfflineDataFetcher.fetch(key, fetcher, { tags })
            isFromCache = await globalOfflineCache.get(key) !== null
          } else {
            result = await fetcher()
            isFromCache = false
          }
          break

        case CacheStrategy.CACHE_ONLY:
          const cachedOnly = await globalCache.get<T>(key)
          if (cachedOnly === null) {
            throw new Error(`No cached data available for key: ${key}`)
          }
          result = cachedOnly
          isFromCache = true
          break

        case CacheStrategy.NETWORK_ONLY:
          result = await fetcher()
          isFromCache = false
          break

        default:
          result = await globalDataFetcher.fetchWithCache(
            key,
            fetcher,
            { ttl, tags, priority, forceRefresh }
          )
          isFromCache = !forceRefresh && await globalCache.get(key) !== null
      }

      if (mountedRef.current) {
        setData(result)
        setCacheStats({
          hitRate: globalCache.getStats().hitRate,
          lastUpdated: Date.now(),
          isFromCache
        })

        // Stale-while-revalidate: if data is from cache, update in background
        if (staleWhileRevalidate && isFromCache && strategy !== CacheStrategy.CACHE_ONLY) {
          setTimeout(async () => {
            try {
              const freshData = await fetcher()
              globalCache.set(key, freshData, { ttl, tags, priority })
              if (mountedRef.current) {
                setData(freshData)
                setCacheStats(prev => ({ ...prev, lastUpdated: Date.now() }))
              }
            } catch (bgError) {
              console.warn(`Background refresh failed for ${key}:`, bgError)
            }
          }, 0)
        }
      }
    } catch (err) {
      if (mountedRef.current && err.name !== 'AbortError') {
        setError(err as Error)
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false)
      }
    }
  }, [key, fetcher, strategy, ttl, tags, priority, enableOffline, staleWhileRevalidate])

  /**
   * Invalidate cache
   */
  const invalidate = useCallback(() => {
    globalCache.invalidate(key)
    if (enableOffline) {
      globalOfflineCache.delete(key)
    }
  }, [key, enableOffline])

  /**
   * Prefetch data
   */
  const prefetchData = useCallback(async () => {
    try {
      await globalDataFetcher.prefetch(key, fetcher, { ttl, tags, priority })
    } catch (error) {
      console.warn(`Prefetch failed for ${key}:`, error)
    }
  }, [key, fetcher, ttl, tags, priority])

  /**
   * Refetch data
   */
  const refetch = useCallback(async () => {
    await fetchData(true)
  }, [fetchData])

  // Initial data fetch
  useEffect(() => {
    fetchData()

    // Prefetch if enabled
    if (prefetch) {
      prefetchData()
    }

    return () => {
      mountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchData, prefetch, prefetchData])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  return {
    data,
    loading,
    error,
    refetch,
    invalidate,
    prefetch: prefetchData,
    cacheStats
  }
}

/**
 * Hook for cached list data with pagination
 */
export function useCachedList<T>(
  baseKey: string,
  fetcher: (page: number, limit: number) => Promise<T[]>,
  options: CacheOptions & { pageSize?: number } = {}
) {
  const { pageSize = 20, ...cacheOptions } = options
  const [page, setPage] = useState(1)
  const [allData, setAllData] = useState<T[]>([])
  const [hasMore, setHasMore] = useState(true)

  const key = `${baseKey}_page_${page}_limit_${pageSize}`

  const { data, loading, error, refetch } = useCachedData(
    key,
    () => fetcher(page, pageSize),
    cacheOptions
  )

  useEffect(() => {
    if (data) {
      if (page === 1) {
        setAllData(data)
      } else {
        setAllData(prev => [...prev, ...data])
      }
      setHasMore(data.length === pageSize)
    }
  }, [data, page, pageSize])

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1)
    }
  }, [loading, hasMore])

  const reset = useCallback(() => {
    setPage(1)
    setAllData([])
    setHasMore(true)
  }, [])

  return {
    data: allData,
    loading,
    error,
    hasMore,
    loadMore,
    reset,
    refetch: () => {
      reset()
      return refetch()
    }
  }
}

/**
 * Hook for cache management
 */
export function useCacheManager() {
  const [stats, setStats] = useState(globalCache.getStats())

  const updateStats = useCallback(() => {
    setStats(globalCache.getStats())
  }, [])

  const clearCache = useCallback(() => {
    globalCache.clear()
    updateStats()
  }, [updateStats])

  const invalidateByTag = useCallback((tag: string) => {
    globalCache.invalidate(tag, true)
    updateStats()
  }, [updateStats])

  useEffect(() => {
    const interval = setInterval(updateStats, 5000) // Update every 5 seconds
    return () => clearInterval(interval)
  }, [updateStats])

  return {
    stats,
    clearCache,
    invalidateByTag,
    updateStats
  }
}

export default useCachedData
