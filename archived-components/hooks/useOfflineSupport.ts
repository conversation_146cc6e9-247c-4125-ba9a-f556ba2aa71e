/**
 * Offline Support Hook
 * 
 * Provides offline detection and fallback handling for community features
 * 
 * <AUTHOR> Team
 */

'use client'

import { useState, useEffect, useCallback } from 'react'

interface OfflineState {
  isOnline: boolean
  isInitialLoad: boolean
  lastOnline: Date | null
  retryCount: number
}

export const useOfflineSupport = () => {
  const [offlineState, setOfflineState] = useState<OfflineState>({
    isOnline: typeof window !== 'undefined' ? navigator.onLine : true,
    isInitialLoad: true,
    lastOnline: null,
    retryCount: 0
  })

  const [cachedData, setCachedData] = useState<Record<string, any>>({})

  // Handle online/offline status changes
  useEffect(() => {
    const handleOnline = () => {
      setOfflineState(prev => ({
        ...prev,
        isOnline: true,
        retryCount: 0
      }))
    }

    const handleOffline = () => {
      setOfflineState(prev => ({
        ...prev,
        isOnline: false,
        lastOnline: new Date()
      }))
    }

    // Set initial state after component mounts
    setOfflineState(prev => ({
      ...prev,
      isInitialLoad: false
    }))

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Cache data for offline use
  const cacheData = useCallback((key: string, data: any) => {
    setCachedData(prev => ({
      ...prev,
      [key]: {
        data,
        timestamp: new Date(),
        isStale: false
      }
    }))

    // Also store in localStorage for persistence
    try {
      localStorage.setItem(`cache_${key}`, JSON.stringify({
        data,
        timestamp: new Date().toISOString()
      }))
    } catch (error) {
      console.warn('Failed to cache data in localStorage:', error)
    }
  }, [])

  // Get cached data
  const getCachedData = useCallback((key: string) => {
    // First try memory cache
    if (cachedData[key]) {
      return cachedData[key].data
    }

    // Fallback to localStorage
    try {
      const cached = localStorage.getItem(`cache_${key}`)
      if (cached) {
        const parsed = JSON.parse(cached)
        const cacheAge = Date.now() - new Date(parsed.timestamp).getTime()
        
        // Return cached data if it's less than 5 minutes old
        if (cacheAge < 5 * 60 * 1000) {
          return parsed.data
        }
      }
    } catch (error) {
      console.warn('Failed to get cached data from localStorage:', error)
    }

    return null
  }, [cachedData])

  // Check if data is stale
  const isDataStale = useCallback((key: string, maxAge: number = 5 * 60 * 1000) => {
    const cached = cachedData[key]
    if (!cached) return true

    const age = Date.now() - cached.timestamp.getTime()
    return age > maxAge
  }, [cachedData])

  // Retry function with exponential backoff
  const retryWithBackoff = useCallback(async (
    operation: () => Promise<any>,
    maxRetries: number = 3
  ) => {
    const attempt = async (retryCount: number): Promise<any> => {
      try {
        setOfflineState(prev => ({ ...prev, retryCount }))
        const result = await operation()
        setOfflineState(prev => ({ ...prev, retryCount: 0 }))
        return result
      } catch (error) {
        if (retryCount < maxRetries) {
          const delay = Math.pow(2, retryCount) * 1000 // 1s, 2s, 4s
          await new Promise(resolve => setTimeout(resolve, delay))
          return attempt(retryCount + 1)
        }
        throw error
      }
    }

    return attempt(0)
  }, [])

  // Get offline-aware data
  const getOfflineAwareData = useCallback(async (
    key: string,
    fetchFunction: () => Promise<any>,
    fallbackData?: any
  ) => {
    // If offline, return cached data or fallback
    if (!offlineState.isOnline) {
      const cached = getCachedData(key)
      if (cached) {
        return cached
      }
      if (fallbackData) {
        return fallbackData
      }
      throw new Error('No cached data available offline')
    }

    // If online, try to fetch fresh data
    try {
      const data = await retryWithBackoff(fetchFunction)
      cacheData(key, data)
      return data
    } catch (error) {
      // If fetch fails, fall back to cached data
      const cached = getCachedData(key)
      if (cached) {
        console.warn('Using cached data due to fetch error:', error)
        return cached
      }
      
      // If no cached data, use fallback or throw
      if (fallbackData) {
        return fallbackData
      }
      throw error
    }
  }, [offlineState.isOnline, getCachedData, retryWithBackoff, cacheData])

  // Clear cache
  const clearCache = useCallback((key?: string) => {
    if (key) {
      setCachedData(prev => {
        const newData = { ...prev }
        delete newData[key]
        return newData
      })
      localStorage.removeItem(`cache_${key}`)
    } else {
      setCachedData({})
      // Clear all cache items from localStorage
      Object.keys(localStorage).forEach(k => {
        if (k.startsWith('cache_')) {
          localStorage.removeItem(k)
        }
      })
    }
  }, [])

  return {
    isOnline: offlineState.isOnline,
    isInitialLoad: offlineState.isInitialLoad,
    lastOnline: offlineState.lastOnline,
    retryCount: offlineState.retryCount,
    cacheData,
    getCachedData,
    getOfflineAwareData,
    isDataStale,
    clearCache,
    retryWithBackoff
  }
}