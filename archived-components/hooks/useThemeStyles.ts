/**
 * Theme Styles Hook
 * 
 * React hook for generating theme-aware CSS classes and styles.
 * Provides utilities for responsive design, animations, and gamification styling.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { useMemo, useCallback } from 'react'
import { useTheme, useGamificationTheme } from '../lib/theme/ThemeProvider'

// ===== TYPES =====

export interface StyleVariants {
  [key: string]: string | { [breakpoint: string]: string }
}

export interface ComponentStyleConfig {
  base?: string
  variants?: {
    [variantName: string]: string | StyleVariants
  }
  sizes?: StyleVariants
  states?: StyleVariants
  responsive?: boolean
  darkMode?: boolean
}

export interface AnimationConfig {
  enabled?: boolean
  duration?: 'fast' | 'normal' | 'slow'
  easing?: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out'
  delay?: number
  respectMotionPreference?: boolean
}

// ===== MAIN HOOK =====

export function useThemeStyles() {
  const { 
    colors, 
    config, 
    isDark, 
    isHighContrast, 
    systemPrefersReducedMotion,
    getColor 
  } = useTheme()

  const {
    gamificationColors,
    getAchievementColor,
    getTierColor,
    getProgressColor
  } = useGamificationTheme()

  /**
   * Generate theme-aware CSS custom properties
   */
  const cssVariables = useMemo(() => {
    const vars: Record<string, string> = {}
    
    // Add commonly used colors as CSS variables
    vars['--bg-primary'] = colors.components.background.primary
    vars['--bg-secondary'] = colors.components.background.secondary
    vars['--bg-tertiary'] = colors.components.background.tertiary
    vars['--bg-elevated'] = colors.components.background.elevated
    
    vars['--text-primary'] = colors.components.text.primary
    vars['--text-secondary'] = colors.components.text.secondary
    vars['--text-tertiary'] = colors.components.text.tertiary
    
    vars['--border-primary'] = colors.components.border.primary
    vars['--border-secondary'] = colors.components.border.secondary
    vars['--border-focus'] = colors.components.border.focus
    
    vars['--accent-500'] = colors.accent[500]
    vars['--accent-600'] = colors.accent[600]
    vars['--accent-700'] = colors.accent[700]

    return vars
  }, [colors])

  /**
   * Create theme-aware component classes
   */
  const createComponent = useCallback((config: ComponentStyleConfig) => {
    return (props: {
      variant?: string
      size?: string
      state?: string
      className?: string
    } = {}) => {
      const classes: string[] = []
      
      // Base classes
      if (config.base) {
        classes.push(config.base)
      }
      
      // Variant classes
      if (props.variant && config.variants?.[props.variant]) {
        const variantClasses = config.variants[props.variant]
        if (typeof variantClasses === 'string') {
          classes.push(variantClasses)
        } else {
          // Handle responsive variants
          Object.entries(variantClasses).forEach(([breakpoint, cls]) => {
            if (breakpoint === 'default') {
              classes.push(cls)
            } else {
              classes.push(`${breakpoint}:${cls}`)
            }
          })
        }
      }
      
      // Size classes
      if (props.size && config.sizes?.[props.size]) {
        const sizeClasses = config.sizes[props.size]
        classes.push(typeof sizeClasses === 'string' ? sizeClasses : sizeClasses.default || '')
      }
      
      // State classes
      if (props.state && config.states?.[props.state]) {
        const stateClasses = config.states[props.state]
        classes.push(typeof stateClasses === 'string' ? stateClasses : stateClasses.default || '')
      }
      
      // Dark mode handling
      if (config.darkMode && isDark) {
        classes.push('dark')
      }
      
      // High contrast handling
      if (isHighContrast) {
        classes.push('high-contrast')
      }
      
      // Custom className
      if (props.className) {
        classes.push(props.className)
      }
      
      return classes.join(' ')
    }
  }, [isDark, isHighContrast])

  /**
   * Generate responsive classes
   */
  const responsive = useCallback((
    classes: {
      default?: string
      sm?: string
      md?: string
      lg?: string
      xl?: string
      '2xl'?: string
    }
  ) => {
    const responsiveClasses: string[] = []
    
    if (classes.default) responsiveClasses.push(classes.default)
    if (classes.sm) responsiveClasses.push(`sm:${classes.sm}`)
    if (classes.md) responsiveClasses.push(`md:${classes.md}`)
    if (classes.lg) responsiveClasses.push(`lg:${classes.lg}`)
    if (classes.xl) responsiveClasses.push(`xl:${classes.xl}`)
    if (classes['2xl']) responsiveClasses.push(`2xl:${classes['2xl']}`)
    
    return responsiveClasses.join(' ')
  }, [])

  /**
   * Generate animation classes with motion preference respect
   */
  const animation = useCallback((config: AnimationConfig & { className: string }) => {
    if (!config.enabled || (config.respectMotionPreference && systemPrefersReducedMotion)) {
      return 'motion-reduce:transform-none motion-reduce:transition-none'
    }
    
    const classes: string[] = [config.className]
    
    // Duration classes
    if (config.duration) {
      const durationMap = {
        fast: 'duration-150',
        normal: 'duration-300',
        slow: 'duration-500'
      }
      classes.push(durationMap[config.duration])
    }
    
    // Easing classes
    if (config.easing) {
      const easingMap = {
        ease: 'ease',
        'ease-in': 'ease-in',
        'ease-out': 'ease-out',
        'ease-in-out': 'ease-in-out'
      }
      classes.push(`transition-${easingMap[config.easing]}`)
    }
    
    // Delay classes
    if (config.delay) {
      classes.push(`delay-${config.delay}`)
    }
    
    return classes.join(' ')
  }, [systemPrefersReducedMotion])

  /**
   * Gamification-specific styling utilities
   */
  const gamification = useMemo(() => ({
    achievement: {
      /**
       * Get achievement card classes based on rarity and state
       */
      card: (rarity: string, isUnlocked: boolean = false) => {
        const baseClasses = 'relative rounded-lg border-2 p-4 transition-all duration-200'
        const rarityColor = getAchievementColor(rarity)
        
        if (isUnlocked) {
          return `${baseClasses} border-current bg-opacity-10 shadow-lg`
        } else {
          return `${baseClasses} border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 opacity-75`
        }
      },
      
      /**
       * Get achievement badge classes
       */
      badge: (rarity: string) => {
        const rarityStyles = {
          common: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
          uncommon: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
          rare: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
          epic: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
          legendary: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
        }
        
        const baseClasses = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium'
        return `${baseClasses} ${rarityStyles[rarity as keyof typeof rarityStyles] || rarityStyles.common}`
      }
    },
    
    tier: {
      /**
       * Get tier badge classes
       */
      badge: (tier: string) => {
        const tierColor = getTierColor(tier)
        return `inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold text-white`
      },
      
      /**
       * Get tier card classes
       */
      card: (tier: string, isActive: boolean = false) => {
        const baseClasses = 'rounded-lg border-2 p-6 transition-all duration-200'
        
        if (isActive) {
          return `${baseClasses} border-current shadow-lg transform scale-105`
        } else {
          return `${baseClasses} border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500`
        }
      }
    },
    
    progress: {
      /**
       * Get progress bar classes
       */
      bar: (type: 'empty' | 'partial' | 'complete' | 'overflow' = 'partial') => {
        const colorMap = {
          empty: 'bg-gray-200 dark:bg-gray-700',
          partial: 'bg-accent-500',
          complete: 'bg-green-500',
          overflow: 'bg-orange-500'
        }
        
        return `h-2 rounded-full transition-all duration-300 ${colorMap[type]}`
      },
      
      /**
       * Get progress container classes
       */
      container: () => {
        return 'w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden'
      }
    },
    
    points: {
      /**
       * Get points display classes based on change
       */
      display: (change?: number) => {
        const baseClasses = 'font-semibold transition-colors duration-200'
        
        if (change === undefined) {
          return `${baseClasses} text-gray-900 dark:text-gray-100`
        } else if (change > 0) {
          return `${baseClasses} text-green-600 dark:text-green-400`
        } else if (change < 0) {
          return `${baseClasses} text-red-600 dark:text-red-400`
        } else {
          return `${baseClasses} text-gray-600 dark:text-gray-400`
        }
      }
    },
    
    challenge: {
      /**
       * Get challenge card classes based on status
       */
      card: (status: 'available' | 'joined' | 'completed' | 'expired') => {
        const baseClasses = 'rounded-lg border-2 p-4 transition-all duration-200'
        
        const statusStyles = {
          available: 'border-gray-300 dark:border-gray-600 hover:border-accent-300 dark:hover:border-accent-600',
          joined: 'border-accent-500 bg-accent-50 dark:bg-accent-900/20',
          completed: 'border-green-500 bg-green-50 dark:bg-green-900/20',
          expired: 'border-gray-300 dark:border-gray-600 opacity-60'
        }
        
        return `${baseClasses} ${statusStyles[status]}`
      }
    }
  }), [getAchievementColor, getTierColor, getProgressColor])

  /**
   * Common component patterns
   */
  const patterns = useMemo(() => ({
    card: createComponent({
      base: 'rounded-lg border shadow-sm transition-shadow duration-200',
      variants: {
        default: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
        elevated: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg',
        outlined: 'bg-transparent border-gray-300 dark:border-gray-600',
        accent: 'bg-accent-50 dark:bg-accent-900/20 border-accent-200 dark:border-accent-700'
      },
      sizes: {
        sm: 'p-3',
        md: 'p-4',
        lg: 'p-6',
        xl: 'p-8'
      }
    }),
    
    button: createComponent({
      base: 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
      variants: {
        primary: 'bg-accent-600 hover:bg-accent-700 focus:ring-accent-500 text-white',
        secondary: 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:ring-gray-500 text-gray-900 dark:text-gray-100',
        ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-gray-500 text-gray-700 dark:text-gray-300'
      },
      sizes: {
        sm: 'px-3 py-2 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base',
        xl: 'px-8 py-4 text-lg'
      },
      states: {
        disabled: 'opacity-50 cursor-not-allowed',
        loading: 'opacity-75 cursor-wait'
      }
    }),
    
    input: createComponent({
      base: 'block w-full rounded-lg border shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
      variants: {
        default: 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 focus:ring-accent-500 focus:border-accent-500 text-gray-900 dark:text-gray-100',
        error: 'bg-white dark:bg-gray-800 border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100'
      },
      sizes: {
        sm: 'px-3 py-2 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-4 py-3 text-base'
      }
    })
  }), [createComponent])

  return {
    // Core utilities
    colors,
    cssVariables,
    isDark,
    isHighContrast,
    getColor,
    
    // Component builders
    createComponent,
    responsive,
    animation,
    
    // Gamification utilities
    gamification,
    
    // Common patterns
    patterns,
    
    // Theme-aware classes
    text: {
      primary: 'text-gray-900 dark:text-gray-100',
      secondary: 'text-gray-600 dark:text-gray-400',
      tertiary: 'text-gray-500 dark:text-gray-500',
      accent: 'text-accent-600 dark:text-accent-400',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-400',
      error: 'text-red-600 dark:text-red-400'
    },
    
    background: {
      primary: 'bg-white dark:bg-gray-900',
      secondary: 'bg-gray-50 dark:bg-gray-800',
      tertiary: 'bg-gray-100 dark:bg-gray-700',
      accent: 'bg-accent-600 dark:bg-accent-500',
      success: 'bg-green-600 dark:bg-green-500',
      warning: 'bg-yellow-600 dark:bg-yellow-500',
      error: 'bg-red-600 dark:bg-red-500'
    },
    
    border: {
      primary: 'border-gray-200 dark:border-gray-700',
      secondary: 'border-gray-300 dark:border-gray-600',
      accent: 'border-accent-300 dark:border-accent-600',
      success: 'border-green-300 dark:border-green-600',
      warning: 'border-yellow-300 dark:border-yellow-600',
      error: 'border-red-300 dark:border-red-600'
    }
  }
}

export default useThemeStyles