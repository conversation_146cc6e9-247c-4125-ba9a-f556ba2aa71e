/**
 * Timeline Real-time Updates Hook
 * 
 * Provides real-time updates for timeline activities using Firestore listeners.
 * Handles new activities, updates, and deletions with proper error handling
 * and performance optimization.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  Timestamp,
  QuerySnapshot,
  DocumentData
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { TimelineActivity, TimelineFilters } from '@/types/timeline'

interface UseTimelineRealtimeOptions {
  /** Whether real-time updates are enabled */
  enabled?: boolean
  /** Filters to apply to the real-time query */
  filters?: TimelineFilters
  /** Maximum number of activities to track in real-time */
  maxActivities?: number
  /** Callback when new activities are received */
  onNewActivity?: (activity: TimelineActivity) => void
  /** Callback when activities are updated */
  onActivityUpdated?: (activity: TimelineActivity) => void
  /** Callback when activities are deleted */
  onActivityDeleted?: (activityId: string) => void
  /** Callback for connection status changes */
  onConnectionChange?: (connected: boolean) => void
}

interface UseTimelineRealtimeReturn {
  /** Real-time activities */
  activities: TimelineActivity[]
  /** Whether the listener is connected */
  connected: boolean
  /** Any error that occurred */
  error: string | null
  /** Whether the initial load is complete */
  initialLoaded: boolean
  /** Manually refresh the data */
  refresh: () => void
  /** Get count of new activities since last check */
  newActivityCount: number
  /** Mark new activities as seen */
  markActivitiesSeen: () => void
}

/**
 * Timeline Real-time Updates Hook
 */
export function useTimelineRealtime(
  options: UseTimelineRealtimeOptions = {}
): UseTimelineRealtimeReturn {
  const {
    enabled = true,
    filters = {},
    maxActivities = 50,
    onNewActivity,
    onActivityUpdated,
    onActivityDeleted,
    onConnectionChange
  } = options

  // ===== STATE =====
  
  const [activities, setActivities] = useState<TimelineActivity[]>([])
  const [connected, setConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [initialLoaded, setInitialLoaded] = useState(false)
  const [newActivityCount, setNewActivityCount] = useState(0)
  const [lastSeenTimestamp, setLastSeenTimestamp] = useState<Timestamp | null>(null)

  // ===== REFS =====
  
  const unsubscribeRef = useRef<(() => void) | null>(null)
  const previousActivitiesRef = useRef<Map<string, TimelineActivity>>(new Map())
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // ===== EFFECTS =====

  /**
   * Set up real-time listener
   */
  useEffect(() => {
    if (!enabled) {
      cleanup()
      return
    }

    setupRealtimeListener()

    return cleanup
  }, [enabled, filters, maxActivities])

  /**
   * Connection status effect
   */
  useEffect(() => {
    onConnectionChange?.(connected)
  }, [connected, onConnectionChange])

  // ===== HANDLERS =====

  /**
   * Set up Firestore real-time listener
   */
  const setupRealtimeListener = useCallback(() => {
    try {
      // Clean up existing listener
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }

      // Build query
      let q = query(collection(db, 'community_timeline'))

      // Apply filters
      if (filters.types && filters.types.length > 0) {
        q = query(q, where('type', 'in', filters.types))
      }

      if (filters.categories && filters.categories.length > 0) {
        q = query(q, where('category', 'in', filters.categories))
      }

      if (filters.userId) {
        q = query(q, where('userId', '==', filters.userId))
      }

      if (filters.featured !== undefined) {
        q = query(q, where('isFeatured', '==', filters.featured))
      }

      if (filters.trending !== undefined) {
        q = query(q, where('trending', '==', filters.trending))
      }

      // Always filter for visible and approved content
      q = query(q, where('isVisible', '==', true))
      q = query(q, where('moderationStatus', '==', 'approved'))

      // Order and limit
      q = query(q, orderBy('createdAt', 'desc'))
      q = query(q, limit(maxActivities))

      // Set up listener
      const unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          handleSnapshotUpdate(snapshot)
          setConnected(true)
          setError(null)
          
          // Clear reconnect timeout on successful connection
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current)
            reconnectTimeoutRef.current = null
          }
        },
        (err) => {
          console.error('Timeline real-time listener error:', err)
          setError(err.message)
          setConnected(false)
          
          // Attempt to reconnect after a delay
          scheduleReconnect()
        }
      )

      unsubscribeRef.current = unsubscribe
    } catch (err) {
      console.error('Error setting up timeline real-time listener:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setConnected(false)
    }
  }, [filters, maxActivities])

  /**
   * Handle Firestore snapshot updates
   */
  const handleSnapshotUpdate = useCallback((snapshot: QuerySnapshot<DocumentData>) => {
    const newActivities: TimelineActivity[] = []
    const updatedActivities: TimelineActivity[] = []
    const deletedActivityIds: string[] = []

    // Process document changes
    snapshot.docChanges().forEach((change) => {
      const activity = {
        id: change.doc.id,
        ...change.doc.data()
      } as TimelineActivity

      switch (change.type) {
        case 'added':
          newActivities.push(activity)
          onNewActivity?.(activity)
          break
        
        case 'modified':
          updatedActivities.push(activity)
          onActivityUpdated?.(activity)
          break
        
        case 'removed':
          deletedActivityIds.push(change.doc.id)
          onActivityDeleted?.(change.doc.id)
          break
      }
    })

    // Update activities state
    const allActivities = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as TimelineActivity[]

    setActivities(allActivities)

    // Track new activities for notification
    if (initialLoaded && newActivities.length > 0) {
      setNewActivityCount(prev => prev + newActivities.length)
    }

    // Update previous activities reference
    const newPreviousActivities = new Map()
    allActivities.forEach(activity => {
      newPreviousActivities.set(activity.id, activity)
    })
    previousActivitiesRef.current = newPreviousActivities

    if (!initialLoaded) {
      setInitialLoaded(true)
      // Set initial last seen timestamp
      if (allActivities.length > 0) {
        setLastSeenTimestamp(allActivities[0].createdAt)
      }
    }
  }, [initialLoaded, onNewActivity, onActivityUpdated, onActivityDeleted])

  /**
   * Schedule reconnection attempt
   */
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    reconnectTimeoutRef.current = setTimeout(() => {
      console.log('Attempting to reconnect timeline real-time listener...')
      setupRealtimeListener()
    }, 5000) // Retry after 5 seconds
  }, [setupRealtimeListener])

  /**
   * Cleanup function
   */
  const cleanup = useCallback(() => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current()
      unsubscribeRef.current = null
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    setConnected(false)
    setError(null)
  }, [])

  /**
   * Manual refresh
   */
  const refresh = useCallback(() => {
    cleanup()
    setupRealtimeListener()
  }, [cleanup, setupRealtimeListener])

  /**
   * Mark activities as seen
   */
  const markActivitiesSeen = useCallback(() => {
    setNewActivityCount(0)
    if (activities.length > 0) {
      setLastSeenTimestamp(activities[0].createdAt)
    }
  }, [activities])

  // ===== RETURN =====

  return {
    activities,
    connected,
    error,
    initialLoaded,
    refresh,
    newActivityCount,
    markActivitiesSeen
  }
}

/**
 * Simplified hook for basic real-time timeline
 */
export function useBasicTimelineRealtime(filters?: TimelineFilters) {
  return useTimelineRealtime({
    enabled: true,
    filters,
    maxActivities: 20
  })
}

/**
 * Hook for user-specific timeline
 */
export function useUserTimelineRealtime(userId: string) {
  return useTimelineRealtime({
    enabled: !!userId,
    filters: { userId },
    maxActivities: 30
  })
}

/**
 * Hook for featured timeline activities
 */
export function useFeaturedTimelineRealtime() {
  return useTimelineRealtime({
    enabled: true,
    filters: { featured: true },
    maxActivities: 10
  })
}
