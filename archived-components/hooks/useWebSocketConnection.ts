/**
 * Enhanced WebSocket Connection Hook
 * 
 * Provides proper connection lifecycle management with reference counting,
 * automatic cleanup, and memory leak prevention.
 * 
 * Features:
 * - Connection reference tracking
 * - Automatic cleanup on unmount
 * - Event listener management
 * - Connection state monitoring
 * - Memory leak prevention
 * 
 * <AUTHOR> Team
 */

import { useEffect, useRef, useCallback, useState } from 'react'
import { websocketService, ConnectionState, WebSocketMessage } from '@/lib/realtime/websocketService'
import { useUser } from '@/lib/useUser'

/**
 * WebSocket connection options
 */
interface UseWebSocketConnectionOptions {
  /** Channels to automatically subscribe to */
  channels?: string[]
  /** Whether to connect automatically */
  autoConnect?: boolean
  /** Custom connection reference (for debugging) */
  connectionRef?: string
  /** Whether to use user ID as auth token */
  useUserAuth?: boolean
}

/**
 * WebSocket connection hook return type
 */
interface UseWebSocketConnectionReturn {
  /** Current connection state */
  connectionState: ConnectionState
  /** Whether connected */
  isConnected: boolean
  /** Connection statistics */
  stats: ReturnType<typeof websocketService.getConnectionStats>
  /** Send message */
  send: (message: Omit<WebSocketMessage, 'id' | 'timestamp'>) => boolean
  /** Subscribe to channel */
  subscribe: (channel: string) => void
  /** Unsubscribe from channel */
  unsubscribe: (channel: string) => void
  /** Add event listener */
  addEventListener: (event: string, listener: (...args: any[]) => void) => void
  /** Remove event listener */
  removeEventListener: (event: string, listener: (...args: any[]) => void) => void
  /** Manual connect */
  connect: () => Promise<void>
  /** Manual disconnect */
  disconnect: () => void
  /** Connection reference for debugging */
  connectionRef: string
}

/**
 * Enhanced WebSocket connection hook
 */
export function useWebSocketConnection(
  options: UseWebSocketConnectionOptions = {}
): UseWebSocketConnectionReturn {
  const { user } = useUser()
  const {
    channels = [],
    autoConnect = true,
    connectionRef: customRef,
    useUserAuth = true
  } = options

  // Generate stable connection reference
  const connectionRef = useRef(
    customRef || `hook_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  ).current

  // State management
  const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected')
  const [stats, setStats] = useState(websocketService.getConnectionStats())

  // Track subscribed channels for cleanup
  const subscribedChannels = useRef(new Set<string>())
  const eventListeners = useRef(new Map<string, (...args: any[]) => void>())

  // Connection state listener
  const handleConnectionStateChange = useCallback((state: ConnectionState) => {
    setConnectionState(state)
    setStats(websocketService.getConnectionStats())
  }, [])

  // Connect function
  const connect = useCallback(async () => {
    try {
      const authToken = useUserAuth && user?.uid ? await user.getIdToken() : undefined
      await websocketService.connect(authToken, connectionRef)
      
      // Subscribe to channels after connection
      channels.forEach(channel => {
        websocketService.subscribe(channel)
        subscribedChannels.current.add(channel)
      })
    } catch (error) {
      console.error('Failed to connect WebSocket:', error)
    }
  }, [user, useUserAuth, channels, connectionRef])

  // Disconnect function
  const disconnect = useCallback(() => {
    // Unsubscribe from all channels
    subscribedChannels.current.forEach(channel => {
      websocketService.unsubscribe(channel)
    })
    subscribedChannels.current.clear()

    // Remove all event listeners
    eventListeners.current.forEach((listener, event) => {
      websocketService.removeListener(event, listener, connectionRef)
    })
    eventListeners.current.clear()

    // Disconnect with reference
    websocketService.disconnect(connectionRef)
  }, [connectionRef])

  // Send message
  const send = useCallback((message: Omit<WebSocketMessage, 'id' | 'timestamp'>) => {
    return websocketService.send(message)
  }, [])

  // Subscribe to channel
  const subscribe = useCallback((channel: string) => {
    websocketService.subscribe(channel)
    subscribedChannels.current.add(channel)
  }, [])

  // Unsubscribe from channel
  const unsubscribe = useCallback((channel: string) => {
    websocketService.unsubscribe(channel)
    subscribedChannels.current.delete(channel)
  }, [])

  // Add event listener with reference tracking
  const addEventListener = useCallback((event: string, listener: (...args: any[]) => void) => {
    websocketService.addListener(event, listener, connectionRef)
    eventListeners.current.set(event, listener)
  }, [connectionRef])

  // Remove event listener with reference tracking
  const removeEventListener = useCallback((event: string, listener: (...args: any[]) => void) => {
    websocketService.removeListener(event, listener, connectionRef)
    eventListeners.current.delete(event)
  }, [connectionRef])

  // Setup connection state listener
  useEffect(() => {
    websocketService.addListener('connectionStateChange', handleConnectionStateChange, connectionRef)
    
    // Initialize state
    setConnectionState(websocketService.getConnectionState())
    setStats(websocketService.getConnectionStats())

    return () => {
      websocketService.removeListener('connectionStateChange', handleConnectionStateChange, connectionRef)
    }
  }, [handleConnectionStateChange, connectionRef])

  // Auto-connect effect
  useEffect(() => {
    if (autoConnect && user?.uid) {
      connect()
    }

    // Cleanup on unmount or user change
    return () => {
      disconnect()
    }
  }, [autoConnect, user?.uid, connect, disconnect])

  // Update stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(websocketService.getConnectionStats())
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return {
    connectionState,
    isConnected: connectionState === 'connected',
    stats,
    send,
    subscribe,
    unsubscribe,
    addEventListener,
    removeEventListener,
    connect,
    disconnect,
    connectionRef
  }
}

export default useWebSocketConnection
