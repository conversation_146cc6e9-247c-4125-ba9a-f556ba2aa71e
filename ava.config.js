/**
 * AVA Configuration for Syndicaps
 * 
 * Focused on Node.js/server-side testing to complement <PERSON><PERSON>
 * for React component testing.
 */

export default {
  // Test file patterns
  files: [
    'tests/ava/**/*.test.js',
    'tests/ava/**/*.test.ts',
    'src/lib/**/*.ava.test.js',
    'src/lib/**/*.ava.test.ts',
  ],

  // Watch mode configuration
  watchMode: {
    ignoreChanges: [
      'coverage/**',
      'dist/**',
      '.next/**',
      'node_modules/**',
    ],
  },

  // TypeScript support
  extensions: {
    ts: 'module',
    tsx: 'module',
  },

  // Node.js API support
  nodeArguments: [
    '--import=tsx/esm',
    '--experimental-specifier-resolution=node',
  ],

  // Environment setup
  environmentVariables: {
    NODE_ENV: 'test',
    FIREBASE_PROJECT_ID: 'test-project',
    FIREBASE_API_KEY: 'test-api-key',
  },

  // Parallel execution (AVA's strength)
  concurrency: 4,
  failFast: false,
  verbose: true,

  // Timeout for async operations
  timeout: '30s',

  // Coverage (optional, can use nyc)
  // tap: true, // For CI integration
};
