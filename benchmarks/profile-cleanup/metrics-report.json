{"timestamp": "2025-07-22T08:35:32.269Z", "branch": "feature/profile-cleanup-phase1", "results": {"files": {"components": 45, "lines": 15551, "pages": 29, "componentsDirSize": "608K", "pagesDirSize": "532K", "avgComponentSize": 346, "componentsPerPage": 1.55}, "cleanup": {"cleanup": {"phase1": {"components": 5, "lines": 1489}, "phase2": {"components": 7, "lines": 2136}, "total": {"components": 12, "lines": 3625}}, "original": {"components": 57, "lines": 19176}, "current": {"components": 45, "lines": 15551, "pages": 29, "componentsDirSize": "608K", "pagesDirSize": "532K", "avgComponentSize": 346, "componentsPerPage": 1.55}, "reduction": {"components": "21.1", "lines": "18.9"}}, "imports": {"total": 62, "barrel": 0, "direct": 56, "external": 0, "efficiency": 1.48}, "quality": {"files": 49, "anyTypes": 12, "todos": 3, "consoleLogs": 2, "docComments": 184, "docCoverage": 375.5, "qualityScore": 416}}, "summary": {"bundleOptimization": 18.9, "codeQuality": 416, "architecture": "simplified", "maintainability": "improved"}, "executionTime": 520}