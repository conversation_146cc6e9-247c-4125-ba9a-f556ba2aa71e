{"timestamp": "2025-07-22T07:07:59.914Z", "branch": "feature/profile-cleanup-phase1", "cleanup": {"phase1": {"components": 5, "lines": 1489}, "phase2": {"components": 7, "lines": 2136}, "total": {"components": 12, "lines": 3625}}, "benchmarks": {"bundleSize": {"timestamp": "2025-07-22T07:07:46.038Z", "remainingComponents": 42, "remainingLines": 14668, "profilePages": 29, "cleanup": {"components": 12, "linesRemoved": 3625, "estimatedKB": 1.77}, "efficiency": {"componentsPerPage": 1.45, "linesPerComponent": 349.24}}, "buildPerformance": null, "dependencies": {"profileImports": 62, "circularDependencies": 25, "externalDependencies": 88, "importEfficiency": 1.38}, "codeQuality": {"typeScriptFiles": 43, "anyTypes": 20, "todoComments": 3, "consoleLogs": 3, "qualityScore": 0}}, "summary": {"bundleOptimization": "Significant improvement", "buildPerformance": "Analysis failed", "codeQuality": "0/100", "dependencies": "Optimized"}}