'use client'

import React, { useState, useEffect } from 'react'
import { 
  ChartBarIcon, 
  CogIcon, 
  ServerIcon, 
  ShieldCheckIcon,
  CurrencyDollarIcon,
  BoltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
// Dashboard section components will be imported as they are created
// import { DashboardOverview } from './dashboard/DashboardOverview'
// import { PerformanceMonitoring } from './dashboard/PerformanceMonitoring'
// import { InfrastructureManagement } from './dashboard/InfrastructureManagement'
// import { OptimizationDashboard } from './dashboard/OptimizationDashboard'
// import { UsageCostMonitoring } from './dashboard/UsageCostMonitoring'
// import { SecurityCompliance } from './dashboard/SecurityCompliance'

// Temporary placeholder components
const DashboardOverview = ({ systemStatus, metrics, isLoading, onRefresh }: any) => (
  <div className="text-center py-8">
    <h2 className="text-2xl font-bold mb-4">Dashboard Overview</h2>
    <p className="text-gray-400">Overview component will be implemented next</p>
  </div>
)

const PerformanceMonitoring = ({ systemStatus, metrics, isLoading, onRefresh }: any) => (
  <div className="text-center py-8">
    <h2 className="text-2xl font-bold mb-4">Performance Monitoring</h2>
    <p className="text-gray-400">Performance monitoring component will be implemented next</p>
  </div>
)

const InfrastructureManagement = ({ systemStatus, metrics, isLoading, onRefresh }: any) => (
  <div className="text-center py-8">
    <h2 className="text-2xl font-bold mb-4">Infrastructure Management</h2>
    <p className="text-gray-400">Infrastructure management component will be implemented next</p>
  </div>
)

const OptimizationDashboard = ({ systemStatus, metrics, isLoading, onRefresh }: any) => (
  <div className="text-center py-8">
    <h2 className="text-2xl font-bold mb-4">Optimization Dashboard</h2>
    <p className="text-gray-400">Optimization dashboard component will be implemented next</p>
  </div>
)

const UsageCostMonitoring = ({ systemStatus, metrics, isLoading, onRefresh }: any) => (
  <div className="text-center py-8">
    <h2 className="text-2xl font-bold mb-4">Usage & Cost Monitoring</h2>
    <p className="text-gray-400">Usage and cost monitoring component will be implemented next</p>
  </div>
)

const SecurityCompliance = ({ systemStatus, metrics, isLoading, onRefresh }: any) => (
  <div className="text-center py-8">
    <h2 className="text-2xl font-bold mb-4">Security & Compliance</h2>
    <p className="text-gray-400">Security and compliance component will be implemented next</p>
  </div>
)

export interface DashboardTab {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  component: React.ComponentType
  badge?: number
  status?: 'success' | 'warning' | 'error' | 'info'
}

export interface SystemStatus {
  overall: 'healthy' | 'warning' | 'critical'
  workers: 'online' | 'degraded' | 'offline'
  r2Storage: 'healthy' | 'warning' | 'error'
  firebase: 'connected' | 'degraded' | 'disconnected'
  optimization: 'active' | 'paused' | 'error'
  lastUpdated: Date
}

export interface DashboardMetrics {
  activeUsers: number
  requestsPerMinute: number
  errorRate: number
  averageResponseTime: number
  cacheHitRate: number
  totalCost: number
  optimizationsToday: number
  systemUptime: number
}

export const CloudflareHybridDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    overall: 'healthy',
    workers: 'online',
    r2Storage: 'healthy',
    firebase: 'connected',
    optimization: 'active',
    lastUpdated: new Date()
  })
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    activeUsers: 0,
    requestsPerMinute: 0,
    errorRate: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    totalCost: 0,
    optimizationsToday: 0,
    systemUptime: 99.9
  })
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  // Dashboard tabs configuration
  const tabs: DashboardTab[] = [
    {
      id: 'overview',
      name: 'Overview',
      icon: ChartBarIcon,
      component: DashboardOverview,
      status: systemStatus.overall === 'healthy' ? 'success' : 
              systemStatus.overall === 'warning' ? 'warning' : 'error'
    },
    {
      id: 'performance',
      name: 'Performance',
      icon: BoltIcon,
      component: PerformanceMonitoring,
      badge: metrics.errorRate > 5 ? Math.round(metrics.errorRate) : undefined,
      status: metrics.errorRate > 10 ? 'error' : 
              metrics.errorRate > 5 ? 'warning' : 'success'
    },
    {
      id: 'infrastructure',
      name: 'Infrastructure',
      icon: ServerIcon,
      component: InfrastructureManagement,
      status: systemStatus.workers === 'online' && systemStatus.r2Storage === 'healthy' ? 'success' : 'warning'
    },
    {
      id: 'optimization',
      name: 'Optimization',
      icon: CogIcon,
      component: OptimizationDashboard,
      badge: metrics.optimizationsToday,
      status: systemStatus.optimization === 'active' ? 'success' : 
              systemStatus.optimization === 'paused' ? 'warning' : 'error'
    },
    {
      id: 'usage',
      name: 'Usage & Cost',
      icon: CurrencyDollarIcon,
      component: UsageCostMonitoring,
      status: metrics.totalCost > 1000 ? 'warning' : 'success'
    },
    {
      id: 'security',
      name: 'Security',
      icon: ShieldCheckIcon,
      component: SecurityCompliance,
      status: 'success'
    }
  ]

  // Real-time data fetching
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)
        
        // Fetch system status
        const statusResponse = await fetch('/api/admin/dashboard/status')
        if (statusResponse.ok) {
          const statusData = await statusResponse.json()
          setSystemStatus(statusData)
        }

        // Fetch metrics
        const metricsResponse = await fetch('/api/admin/dashboard/metrics')
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json()
          setMetrics(metricsData)
        }

        setLastRefresh(new Date())
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    // Initial fetch
    fetchDashboardData()

    // Set up real-time updates
    const interval = setInterval(fetchDashboardData, 30000) // 30 seconds

    // WebSocket connection for real-time updates
    const ws = new WebSocket(process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001')
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        if (data.type === 'metrics_update') {
          setMetrics(prev => ({ ...prev, ...data.metrics }))
        } else if (data.type === 'status_update') {
          setSystemStatus(prev => ({ ...prev, ...data.status }))
        }
        setLastRefresh(new Date())
      } catch (error) {
        console.error('WebSocket message error:', error)
      }
    }

    ws.onerror = (error) => {
      console.error('WebSocket error:', error)
    }

    return () => {
      clearInterval(interval)
      ws.close()
    }
  }, [])

  // Get status indicator color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
      case 'healthy':
      case 'online':
      case 'connected':
      case 'active':
        return 'text-green-400'
      case 'warning':
      case 'degraded':
      case 'paused':
        return 'text-yellow-400'
      case 'error':
      case 'critical':
      case 'offline':
      case 'disconnected':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
      case 'healthy':
      case 'online':
      case 'connected':
      case 'active':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'warning':
      case 'degraded':
      case 'paused':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      case 'error':
      case 'critical':
      case 'offline':
      case 'disconnected':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      default:
        return <div className="h-4 w-4 rounded-full bg-gray-400" />
    }
  }

  // Render active tab component
  const renderActiveTab = () => {
    const activeTabConfig = tabs.find(tab => tab.id === activeTab)
    if (!activeTabConfig) return null

    const Component = activeTabConfig.component
    return (
      <Component 
        systemStatus={systemStatus}
        metrics={metrics}
        isLoading={isLoading}
        onRefresh={() => setLastRefresh(new Date())}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-white">
                Cloudflare Hybrid Dashboard
              </h1>
              <div className="ml-4 flex items-center space-x-2">
                <div className={`flex items-center space-x-1 ${getStatusColor(systemStatus.overall)}`}>
                  {getStatusIcon(systemStatus.overall)}
                  <span className="text-sm font-medium capitalize">
                    {systemStatus.overall}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-400">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </div>
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"></div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${isActive
                      ? 'border-purple-500 text-purple-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                    }
                  `}
                >
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.name}</span>
                  
                  {/* Status indicator */}
                  {tab.status && (
                    <div className={`h-2 w-2 rounded-full ${
                      tab.status === 'success' ? 'bg-green-400' :
                      tab.status === 'warning' ? 'bg-yellow-400' :
                      tab.status === 'error' ? 'bg-red-400' : 'bg-blue-400'
                    }`} />
                  )}
                  
                  {/* Badge */}
                  {tab.badge !== undefined && tab.badge > 0 && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      {tab.badge}
                    </span>
                  )}
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderActiveTab()}
      </div>

      {/* System Status Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700 px-4 py-2">
        <div className="max-w-7xl mx-auto flex items-center justify-between text-sm">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">Workers:</span>
              <span className={getStatusColor(systemStatus.workers)}>
                {systemStatus.workers}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">R2:</span>
              <span className={getStatusColor(systemStatus.r2Storage)}>
                {systemStatus.r2Storage}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">Firebase:</span>
              <span className={getStatusColor(systemStatus.firebase)}>
                {systemStatus.firebase}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">Optimization:</span>
              <span className={getStatusColor(systemStatus.optimization)}>
                {systemStatus.optimization}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-gray-400">
            <span>Uptime: {metrics.systemUptime}%</span>
            <span>•</span>
            <span>Requests/min: {metrics.requestsPerMinute.toLocaleString()}</span>
            <span>•</span>
            <span>Error Rate: {metrics.errorRate.toFixed(2)}%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CloudflareHybridDashboard
