'use client'

import React, { useState, useEffect } from 'react'
import {
  FlagIcon,
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

export interface FeatureFlag {
  id: string
  name: string
  description: string
  enabled: boolean
  rolloutPercentage: number
  environment: 'development' | 'staging' | 'production'
  category: 'hybrid' | 'optimization' | 'security' | 'experimental'
  conditions?: {
    userSegments?: string[]
    geographicRegions?: string[]
    timeRange?: {
      start: string
      end: string
    }
  }
  metrics: {
    activeUsers: number
    successRate: number
    errorRate: number
    performanceImpact: number
  }
  lastModified: Date
  modifiedBy: string
  emergencyKillSwitch: boolean
}

interface FeatureFlagManagementProps {
  systemStatus: any
  metrics: any
  isLoading: boolean
  onRefresh: () => void
}

export const FeatureFlagManagement: React.FC<FeatureFlagManagementProps> = ({
  systemStatus,
  metrics,
  isLoading,
  onRefresh
}) => {
  const [featureFlags, setFeatureFlags] = useState<FeatureFlag[]>([])
  const [selectedFlag, setSelectedFlag] = useState<FeatureFlag | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterEnvironment, setFilterEnvironment] = useState<string>('all')

  // Mock feature flags data
  useEffect(() => {
    const mockFlags: FeatureFlag[] = [
      {
        id: 'hybrid-r2-storage',
        name: 'Hybrid R2 Storage',
        description: 'Enable Cloudflare R2 storage for image uploads with Firebase fallback',
        enabled: true,
        rolloutPercentage: 85,
        environment: 'production',
        category: 'hybrid',
        conditions: {
          userSegments: ['premium', 'beta'],
          geographicRegions: ['US', 'EU']
        },
        metrics: {
          activeUsers: 1250,
          successRate: 98.5,
          errorRate: 1.5,
          performanceImpact: 23.4
        },
        lastModified: new Date('2024-01-15T10:30:00Z'),
        modifiedBy: '<EMAIL>',
        emergencyKillSwitch: false
      },
      {
        id: 'image-optimization-worker',
        name: 'Image Optimization Worker',
        description: 'Cloudflare Worker for dynamic image optimization and resizing',
        enabled: true,
        rolloutPercentage: 100,
        environment: 'production',
        category: 'optimization',
        metrics: {
          activeUsers: 2100,
          successRate: 99.2,
          errorRate: 0.8,
          performanceImpact: 45.2
        },
        lastModified: new Date('2024-01-14T15:45:00Z'),
        modifiedBy: '<EMAIL>',
        emergencyKillSwitch: false
      },
      {
        id: 'api-cache-worker',
        name: 'API Cache Worker',
        description: 'Edge caching for API responses with intelligent invalidation',
        enabled: true,
        rolloutPercentage: 75,
        environment: 'production',
        category: 'optimization',
        metrics: {
          activeUsers: 1800,
          successRate: 97.8,
          errorRate: 2.2,
          performanceImpact: 38.7
        },
        lastModified: new Date('2024-01-13T09:20:00Z'),
        modifiedBy: '<EMAIL>',
        emergencyKillSwitch: false
      },
      {
        id: 'advanced-optimization-engine',
        name: 'Advanced Optimization Engine',
        description: 'Automated performance optimization with rule-based improvements',
        enabled: false,
        rolloutPercentage: 0,
        environment: 'staging',
        category: 'optimization',
        metrics: {
          activeUsers: 0,
          successRate: 0,
          errorRate: 0,
          performanceImpact: 0
        },
        lastModified: new Date('2024-01-12T14:10:00Z'),
        modifiedBy: '<EMAIL>',
        emergencyKillSwitch: false
      },
      {
        id: 'ddos-protection-enhanced',
        name: 'Enhanced DDoS Protection',
        description: 'Advanced DDoS protection with machine learning detection',
        enabled: true,
        rolloutPercentage: 100,
        environment: 'production',
        category: 'security',
        metrics: {
          activeUsers: 2500,
          successRate: 99.9,
          errorRate: 0.1,
          performanceImpact: 2.1
        },
        lastModified: new Date('2024-01-11T11:30:00Z'),
        modifiedBy: '<EMAIL>',
        emergencyKillSwitch: false
      },
      {
        id: 'experimental-cdn-routing',
        name: 'Experimental CDN Routing',
        description: 'New intelligent routing algorithm for CDN optimization',
        enabled: false,
        rolloutPercentage: 5,
        environment: 'staging',
        category: 'experimental',
        metrics: {
          activeUsers: 25,
          successRate: 94.2,
          errorRate: 5.8,
          performanceImpact: 12.3
        },
        lastModified: new Date('2024-01-10T16:45:00Z'),
        modifiedBy: '<EMAIL>',
        emergencyKillSwitch: false
      }
    ]
    setFeatureFlags(mockFlags)
  }, [])

  // Filter flags based on category and environment
  const filteredFlags = featureFlags.filter(flag => {
    const categoryMatch = filterCategory === 'all' || flag.category === filterCategory
    const environmentMatch = filterEnvironment === 'all' || flag.environment === filterEnvironment
    return categoryMatch && environmentMatch
  })

  // Handle flag toggle
  const handleToggleFlag = async (flagId: string) => {
    try {
      const flag = featureFlags.find(f => f.id === flagId)
      if (!flag) return

      const updatedFlag = { ...flag, enabled: !flag.enabled }
      setFeatureFlags(prev => prev.map(f => f.id === flagId ? updatedFlag : f))

      // API call to update flag
      await fetch(`/api/admin/feature-flags/${flagId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: updatedFlag.enabled })
      })
    } catch (error) {
      console.error('Failed to toggle feature flag:', error)
    }
  }

  // Handle rollout percentage change
  const handleRolloutChange = async (flagId: string, percentage: number) => {
    try {
      const updatedFlag = featureFlags.find(f => f.id === flagId)
      if (!updatedFlag) return

      updatedFlag.rolloutPercentage = percentage
      setFeatureFlags(prev => prev.map(f => f.id === flagId ? updatedFlag : f))

      // API call to update rollout
      await fetch(`/api/admin/feature-flags/${flagId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rolloutPercentage: percentage })
      })
    } catch (error) {
      console.error('Failed to update rollout percentage:', error)
    }
  }

  // Emergency kill switch
  const handleEmergencyKill = async (flagId: string) => {
    try {
      const updatedFlag = featureFlags.find(f => f.id === flagId)
      if (!updatedFlag) return

      updatedFlag.enabled = false
      updatedFlag.rolloutPercentage = 0
      updatedFlag.emergencyKillSwitch = true
      setFeatureFlags(prev => prev.map(f => f.id === flagId ? updatedFlag : f))

      // API call for emergency kill
      await fetch(`/api/admin/feature-flags/${flagId}/emergency-kill`, {
        method: 'POST'
      })
    } catch (error) {
      console.error('Failed to execute emergency kill:', error)
    }
  }

  // Get status color based on flag state
  const getStatusColor = (flag: FeatureFlag) => {
    if (flag.emergencyKillSwitch) return 'text-red-400'
    if (!flag.enabled) return 'text-gray-400'
    if (flag.rolloutPercentage === 100) return 'text-green-400'
    if (flag.rolloutPercentage > 0) return 'text-yellow-400'
    return 'text-gray-400'
  }

  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'hybrid': return 'bg-purple-100 text-purple-800'
      case 'optimization': return 'bg-blue-100 text-blue-800'
      case 'security': return 'bg-red-100 text-red-800'
      case 'experimental': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get environment badge color
  const getEnvironmentColor = (environment: string) => {
    switch (environment) {
      case 'production': return 'bg-green-100 text-green-800'
      case 'staging': return 'bg-yellow-100 text-yellow-800'
      case 'development': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Feature Flag Management</h2>
        <div className="flex items-center space-x-4">
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Categories</option>
            <option value="hybrid">Hybrid</option>
            <option value="optimization">Optimization</option>
            <option value="security">Security</option>
            <option value="experimental">Experimental</option>
          </select>
          <select
            value={filterEnvironment}
            onChange={(e) => setFilterEnvironment(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Environments</option>
            <option value="production">Production</option>
            <option value="staging">Staging</option>
            <option value="development">Development</option>
          </select>
          <button
            onClick={onRefresh}
            className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-md text-white font-medium transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Total Flags</h3>
            <FlagIcon className="h-6 w-6 text-purple-400" />
          </div>
          <div className="text-3xl font-bold text-white">
            {featureFlags.length}
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Active Flags</h3>
            <CheckCircleIcon className="h-6 w-6 text-green-400" />
          </div>
          <div className="text-3xl font-bold text-white">
            {featureFlags.filter(f => f.enabled).length}
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Production Flags</h3>
            <PlayIcon className="h-6 w-6 text-blue-400" />
          </div>
          <div className="text-3xl font-bold text-white">
            {featureFlags.filter(f => f.environment === 'production').length}
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Avg Success Rate</h3>
            <ArrowPathIcon className="h-6 w-6 text-yellow-400" />
          </div>
          <div className="text-3xl font-bold text-white">
            {(featureFlags.reduce((acc, f) => acc + f.metrics.successRate, 0) / featureFlags.length).toFixed(1)}%
          </div>
        </div>
      </div>

      {/* Feature Flags Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Feature Flags</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Flag
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Rollout
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Metrics
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Environment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-gray-800 divide-y divide-gray-700">
              {filteredFlags.map((flag) => (
                <tr key={flag.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-white">
                          {flag.name}
                        </div>
                        <div className="text-sm text-gray-400">
                          {flag.description}
                        </div>
                        <div className="mt-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(flag.category)}`}>
                            {flag.category}
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <button
                        onClick={() => handleToggleFlag(flag.id)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          flag.enabled ? 'bg-purple-600' : 'bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            flag.enabled ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                      <span className={`ml-3 text-sm ${getStatusColor(flag)}`}>
                        {flag.emergencyKillSwitch ? 'Emergency Kill' : flag.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={flag.rolloutPercentage}
                        onChange={(e) => handleRolloutChange(flag.id, parseInt(e.target.value))}
                        className="w-20 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                        disabled={!flag.enabled || flag.emergencyKillSwitch}
                      />
                      <span className="text-sm text-white font-medium w-12">
                        {flag.rolloutPercentage}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    <div className="space-y-1">
                      <div>Users: {flag.metrics.activeUsers.toLocaleString()}</div>
                      <div>Success: {flag.metrics.successRate}%</div>
                      <div>Performance: +{flag.metrics.performanceImpact}%</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEnvironmentColor(flag.environment)}`}>
                      {flag.environment}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setSelectedFlag(flag)
                          setIsModalOpen(true)
                        }}
                        className="text-purple-400 hover:text-purple-300"
                      >
                        <CogIcon className="h-5 w-5" />
                      </button>
                      {flag.enabled && !flag.emergencyKillSwitch && (
                        <button
                          onClick={() => handleEmergencyKill(flag.id)}
                          className="text-red-400 hover:text-red-300"
                          title="Emergency Kill Switch"
                        >
                          <XCircleIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default FeatureFlagManagement
