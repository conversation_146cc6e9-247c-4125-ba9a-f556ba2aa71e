'use client'

import React, { useState, useEffect } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import {
  ClockIcon,
  ChartBarIcon,
  GlobeAltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
)

export interface PerformanceMetrics {
  responseTime: number[]
  timestamps: string[]
  cacheHitRate: number
  errorRate: number
  throughput: number
  coreWebVitals: {
    lcp: number // Largest Contentful Paint
    fid: number // First Input Delay
    cls: number // Cumulative Layout Shift
  }
  geographicData: {
    region: string
    responseTime: number
    requests: number
  }[]
  errorBreakdown: {
    type: string
    count: number
    percentage: number
  }[]
}

interface PerformanceMonitoringProps {
  systemStatus: any
  metrics: any
  isLoading: boolean
  onRefresh: () => void
}

export const PerformanceMonitoring: React.FC<PerformanceMonitoringProps> = ({
  systemStatus,
  metrics,
  isLoading,
  onRefresh
}) => {
  const [performanceData, setPerformanceData] = useState<PerformanceMetrics>({
    responseTime: [],
    timestamps: [],
    cacheHitRate: 0,
    errorRate: 0,
    throughput: 0,
    coreWebVitals: { lcp: 0, fid: 0, cls: 0 },
    geographicData: [],
    errorBreakdown: []
  })
  const [timeRange, setTimeRange] = useState('1h')
  const [selectedMetric, setSelectedMetric] = useState('responseTime')

  // Fetch performance data
  useEffect(() => {
    const fetchPerformanceData = async () => {
      try {
        const response = await fetch(`/api/admin/dashboard/performance?range=${timeRange}`)
        if (response.ok) {
          const data = await response.json()
          setPerformanceData(data)
        }
      } catch (error) {
        console.error('Failed to fetch performance data:', error)
      }
    }

    fetchPerformanceData()
    const interval = setInterval(fetchPerformanceData, 30000)
    return () => clearInterval(interval)
  }, [timeRange])

  // Chart configurations
  const responseTimeChartData = {
    labels: performanceData.timestamps,
    datasets: [
      {
        label: 'Response Time (ms)',
        data: performanceData.responseTime,
        borderColor: 'rgb(139, 92, 246)',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        fill: true,
        tension: 0.4
      }
    ]
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: 'rgb(156, 163, 175)'
        }
      }
    },
    scales: {
      x: {
        ticks: {
          color: 'rgb(156, 163, 175)'
        },
        grid: {
          color: 'rgba(75, 85, 99, 0.3)'
        }
      },
      y: {
        ticks: {
          color: 'rgb(156, 163, 175)'
        },
        grid: {
          color: 'rgba(75, 85, 99, 0.3)'
        }
      }
    }
  }

  const geographicChartData = {
    labels: performanceData.geographicData.map(d => d.region),
    datasets: [
      {
        label: 'Response Time by Region (ms)',
        data: performanceData.geographicData.map(d => d.responseTime),
        backgroundColor: [
          'rgba(139, 92, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(59, 130, 246, 0.8)'
        ]
      }
    ]
  }

  const errorBreakdownData = {
    labels: performanceData.errorBreakdown.map(e => e.type),
    datasets: [
      {
        data: performanceData.errorBreakdown.map(e => e.percentage),
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)'
        ]
      }
    ]
  }

  // Core Web Vitals status
  const getWebVitalStatus = (metric: string, value: number) => {
    const thresholds = {
      lcp: { good: 2500, poor: 4000 },
      fid: { good: 100, poor: 300 },
      cls: { good: 0.1, poor: 0.25 }
    }

    const threshold = thresholds[metric as keyof typeof thresholds]
    if (value <= threshold.good) return 'good'
    if (value <= threshold.poor) return 'needs-improvement'
    return 'poor'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-400'
      case 'needs-improvement': return 'text-yellow-400'
      case 'poor': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircleIcon className="h-5 w-5" />
      case 'needs-improvement': return <ExclamationTriangleIcon className="h-5 w-5" />
      case 'poor': return <ExclamationTriangleIcon className="h-5 w-5" />
      default: return <ClockIcon className="h-5 w-5" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Performance Monitoring</h2>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
          </select>
          <button
            onClick={onRefresh}
            className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-md text-white font-medium transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Core Web Vitals */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Largest Contentful Paint</h3>
            <div className={`flex items-center space-x-2 ${getStatusColor(getWebVitalStatus('lcp', performanceData.coreWebVitals.lcp))}`}>
              {getStatusIcon(getWebVitalStatus('lcp', performanceData.coreWebVitals.lcp))}
            </div>
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {performanceData.coreWebVitals.lcp.toFixed(0)}ms
          </div>
          <div className="text-sm text-gray-400">
            Good: ≤2.5s, Poor: >4.0s
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">First Input Delay</h3>
            <div className={`flex items-center space-x-2 ${getStatusColor(getWebVitalStatus('fid', performanceData.coreWebVitals.fid))}`}>
              {getStatusIcon(getWebVitalStatus('fid', performanceData.coreWebVitals.fid))}
            </div>
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {performanceData.coreWebVitals.fid.toFixed(0)}ms
          </div>
          <div className="text-sm text-gray-400">
            Good: ≤100ms, Poor: >300ms
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Cumulative Layout Shift</h3>
            <div className={`flex items-center space-x-2 ${getStatusColor(getWebVitalStatus('cls', performanceData.coreWebVitals.cls))}`}>
              {getStatusIcon(getWebVitalStatus('cls', performanceData.coreWebVitals.cls))}
            </div>
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {performanceData.coreWebVitals.cls.toFixed(3)}
          </div>
          <div className="text-sm text-gray-400">
            Good: ≤0.1, Poor: >0.25
          </div>
        </div>
      </div>

      {/* Performance Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Cache Hit Rate</h3>
            <ChartBarIcon className="h-6 w-6 text-purple-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {performanceData.cacheHitRate.toFixed(1)}%
          </div>
          <div className="flex items-center text-sm">
            {performanceData.cacheHitRate > 80 ? (
              <ArrowTrendingUpIcon className="h-4 w-4 text-green-400 mr-1" />
            ) : (
              <ArrowTrendingDownIcon className="h-4 w-4 text-red-400 mr-1" />
            )}
            <span className={performanceData.cacheHitRate > 80 ? 'text-green-400' : 'text-red-400'}>
              {performanceData.cacheHitRate > 80 ? 'Excellent' : 'Needs Improvement'}
            </span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Error Rate</h3>
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {performanceData.errorRate.toFixed(2)}%
          </div>
          <div className="flex items-center text-sm">
            {performanceData.errorRate < 1 ? (
              <CheckCircleIcon className="h-4 w-4 text-green-400 mr-1" />
            ) : (
              <ExclamationTriangleIcon className="h-4 w-4 text-yellow-400 mr-1" />
            )}
            <span className={performanceData.errorRate < 1 ? 'text-green-400' : 'text-yellow-400'}>
              {performanceData.errorRate < 1 ? 'Good' : 'Monitor'}
            </span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Throughput</h3>
            <GlobeAltIcon className="h-6 w-6 text-blue-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {performanceData.throughput.toLocaleString()}
          </div>
          <div className="text-sm text-gray-400">
            Requests per minute
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Avg Response Time</h3>
            <ClockIcon className="h-6 w-6 text-green-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {performanceData.responseTime.length > 0 
              ? Math.round(performanceData.responseTime.reduce((a, b) => a + b, 0) / performanceData.responseTime.length)
              : 0}ms
          </div>
          <div className="text-sm text-gray-400">
            Last {timeRange}
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time Chart */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Response Time Trend</h3>
          <div className="h-64">
            <Line data={responseTimeChartData} options={chartOptions} />
          </div>
        </div>

        {/* Geographic Performance */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Performance by Region</h3>
          <div className="h-64">
            <Bar data={geographicChartData} options={chartOptions} />
          </div>
        </div>

        {/* Error Breakdown */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Error Breakdown</h3>
          <div className="h-64">
            <Doughnut 
              data={errorBreakdownData} 
              options={{
                ...chartOptions,
                plugins: {
                  ...chartOptions.plugins,
                  legend: {
                    position: 'bottom' as const,
                    labels: {
                      color: 'rgb(156, 163, 175)'
                    }
                  }
                }
              }} 
            />
          </div>
        </div>

        {/* Performance Summary */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Performance Summary</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Overall Health</span>
              <span className="text-green-400 font-semibold">Excellent</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Cache Efficiency</span>
              <span className="text-green-400 font-semibold">
                {performanceData.cacheHitRate > 80 ? 'High' : 'Medium'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Error Impact</span>
              <span className={performanceData.errorRate < 1 ? 'text-green-400' : 'text-yellow-400'}>
                {performanceData.errorRate < 1 ? 'Low' : 'Medium'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Optimization Score</span>
              <span className="text-purple-400 font-semibold">92/100</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PerformanceMonitoring
