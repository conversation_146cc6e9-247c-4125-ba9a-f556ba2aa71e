'use client'

import React, { useState, useEffect } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import {
  CurrencyDollarIcon,
  CloudIcon,
  GlobeAltIcon,
  ServerIcon,
  ExclamationTriangleIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  BellIcon
} from '@heroicons/react/24/outline'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

export interface UsageCostData {
  r2Storage: {
    totalStorage: number // GB
    totalRequests: number
    monthlyCost: number
    trend: number // percentage change
    breakdown: {
      storage: number
      classAOperations: number
      classBOperations: number
      dataTransfer: number
    }
  }
  bandwidth: {
    totalBandwidth: number // GB
    monthlyCost: number
    trend: number
    regions: {
      region: string
      bandwidth: number
      cost: number
    }[]
  }
  workers: {
    totalRequests: number
    cpuTime: number // milliseconds
    monthlyCost: number
    trend: number
    breakdown: {
      requests: number
      cpuTime: number
      kvOperations: number
    }
  }
  totalCost: {
    current: number
    projected: number
    budget: number
    lastMonth: number
  }
  alerts: {
    id: string
    type: 'warning' | 'critical'
    message: string
    threshold: number
    current: number
  }[]
}

interface UsageCostMonitoringProps {
  systemStatus: any
  metrics: any
  isLoading: boolean
  onRefresh: () => void
}

export const UsageCostMonitoring: React.FC<UsageCostMonitoringProps> = ({
  systemStatus,
  metrics,
  isLoading,
  onRefresh
}) => {
  const [usageData, setUsageData] = useState<UsageCostData>({
    r2Storage: {
      totalStorage: 0,
      totalRequests: 0,
      monthlyCost: 0,
      trend: 0,
      breakdown: { storage: 0, classAOperations: 0, classBOperations: 0, dataTransfer: 0 }
    },
    bandwidth: {
      totalBandwidth: 0,
      monthlyCost: 0,
      trend: 0,
      regions: []
    },
    workers: {
      totalRequests: 0,
      cpuTime: 0,
      monthlyCost: 0,
      trend: 0,
      breakdown: { requests: 0, cpuTime: 0, kvOperations: 0 }
    },
    totalCost: {
      current: 0,
      projected: 0,
      budget: 0,
      lastMonth: 0
    },
    alerts: []
  })
  const [timeRange, setTimeRange] = useState('30d')
  const [costHistory, setCostHistory] = useState<{ date: string; cost: number }[]>([])

  // Fetch usage and cost data
  useEffect(() => {
    const fetchUsageData = async () => {
      try {
        // Mock data for demonstration
        const mockData: UsageCostData = {
          r2Storage: {
            totalStorage: 245.7, // GB
            totalRequests: 1250000,
            monthlyCost: 12.35,
            trend: 15.2,
            breakdown: {
              storage: 6.14, // $0.025 per GB
              classAOperations: 3.75, // $4.50 per million
              classBOperations: 1.25, // $0.36 per million
              dataTransfer: 1.21 // $0.09 per GB
            }
          },
          bandwidth: {
            totalBandwidth: 1847.3, // GB
            monthlyCost: 45.67,
            trend: -8.4,
            regions: [
              { region: 'North America', bandwidth: 892.1, cost: 22.30 },
              { region: 'Europe', bandwidth: 654.2, cost: 16.36 },
              { region: 'Asia Pacific', bandwidth: 301.0, cost: 7.01 }
            ]
          },
          workers: {
            totalRequests: 8750000,
            cpuTime: 2450000, // milliseconds
            monthlyCost: 8.92,
            trend: 23.1,
            breakdown: {
              requests: 4.38, // $0.50 per million
              cpuTime: 2.45, // $0.02 per 50k CPU-ms
              kvOperations: 2.09 // $0.50 per million
            }
          },
          totalCost: {
            current: 66.94,
            projected: 78.50,
            budget: 100.00,
            lastMonth: 58.23
          },
          alerts: [
            {
              id: 'bandwidth-warning',
              type: 'warning',
              message: 'Bandwidth usage approaching 80% of monthly budget',
              threshold: 80,
              current: 76.2
            },
            {
              id: 'r2-growth',
              type: 'warning',
              message: 'R2 storage costs increased by 15% this month',
              threshold: 10,
              current: 15.2
            }
          ]
        }

        // Mock cost history
        const mockHistory = Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          cost: 45 + Math.random() * 25 + (i * 0.5)
        }))

        setUsageData(mockData)
        setCostHistory(mockHistory)
      } catch (error) {
        console.error('Failed to fetch usage data:', error)
      }
    }

    fetchUsageData()
    const interval = setInterval(fetchUsageData, 60000) // Update every minute
    return () => clearInterval(interval)
  }, [timeRange])

  // Chart configurations
  const costTrendChartData = {
    labels: costHistory.map(d => new Date(d.date).toLocaleDateString()),
    datasets: [
      {
        label: 'Daily Cost ($)',
        data: costHistory.map(d => d.cost),
        borderColor: 'rgb(139, 92, 246)',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        fill: true,
        tension: 0.4
      }
    ]
  }

  const costBreakdownData = {
    labels: ['R2 Storage', 'Bandwidth', 'Workers', 'Other'],
    datasets: [
      {
        data: [
          usageData.r2Storage.monthlyCost,
          usageData.bandwidth.monthlyCost,
          usageData.workers.monthlyCost,
          usageData.totalCost.current - usageData.r2Storage.monthlyCost - usageData.bandwidth.monthlyCost - usageData.workers.monthlyCost
        ],
        backgroundColor: [
          'rgba(139, 92, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(107, 114, 128, 0.8)'
        ]
      }
    ]
  }

  const bandwidthByRegionData = {
    labels: usageData.bandwidth.regions.map(r => r.region),
    datasets: [
      {
        label: 'Bandwidth (GB)',
        data: usageData.bandwidth.regions.map(r => r.bandwidth),
        backgroundColor: 'rgba(16, 185, 129, 0.8)'
      },
      {
        label: 'Cost ($)',
        data: usageData.bandwidth.regions.map(r => r.cost),
        backgroundColor: 'rgba(139, 92, 246, 0.8)'
      }
    ]
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: 'rgb(156, 163, 175)'
        }
      }
    },
    scales: {
      x: {
        ticks: {
          color: 'rgb(156, 163, 175)'
        },
        grid: {
          color: 'rgba(75, 85, 99, 0.3)'
        }
      },
      y: {
        ticks: {
          color: 'rgb(156, 163, 175)'
        },
        grid: {
          color: 'rgba(75, 85, 99, 0.3)'
        }
      }
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  // Format bytes
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 GB'
    const k = 1024
    const sizes = ['GB', 'TB', 'PB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Get trend color
  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-red-400'
    if (trend < 0) return 'text-green-400'
    return 'text-gray-400'
  }

  // Get trend icon
  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUpIcon className="h-4 w-4" />
    if (trend < 0) return <TrendingDownIcon className="h-4 w-4" />
    return null
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Usage & Cost Monitoring</h2>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <button
            onClick={onRefresh}
            className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-md text-white font-medium transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Cost Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Current Month</h3>
            <CurrencyDollarIcon className="h-6 w-6 text-green-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {formatCurrency(usageData.totalCost.current)}
          </div>
          <div className="flex items-center text-sm">
            <span className="text-gray-400">vs last month:</span>
            <span className={`ml-2 ${getTrendColor((usageData.totalCost.current - usageData.totalCost.lastMonth) / usageData.totalCost.lastMonth * 100)}`}>
              {((usageData.totalCost.current - usageData.totalCost.lastMonth) / usageData.totalCost.lastMonth * 100).toFixed(1)}%
            </span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Projected</h3>
            <TrendingUpIcon className="h-6 w-6 text-yellow-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {formatCurrency(usageData.totalCost.projected)}
          </div>
          <div className="text-sm text-gray-400">
            End of month estimate
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Budget</h3>
            <ServerIcon className="h-6 w-6 text-blue-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {formatCurrency(usageData.totalCost.budget)}
          </div>
          <div className="text-sm text-gray-400">
            {((usageData.totalCost.current / usageData.totalCost.budget) * 100).toFixed(1)}% used
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
            <div 
              className="bg-purple-600 h-2 rounded-full" 
              style={{ width: `${Math.min((usageData.totalCost.current / usageData.totalCost.budget) * 100, 100)}%` }}
            ></div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Savings</h3>
            <TrendingDownIcon className="h-6 w-6 text-green-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {formatCurrency(usageData.totalCost.budget - usageData.totalCost.projected)}
          </div>
          <div className="text-sm text-green-400">
            Under budget
          </div>
        </div>
      </div>

      {/* Service Usage Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* R2 Storage */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">R2 Storage</h3>
            <CloudIcon className="h-6 w-6 text-purple-400" />
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Storage:</span>
              <span className="text-white font-medium">{formatBytes(usageData.r2Storage.totalStorage)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Requests:</span>
              <span className="text-white font-medium">{usageData.r2Storage.totalRequests.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Monthly Cost:</span>
              <span className="text-white font-medium">{formatCurrency(usageData.r2Storage.monthlyCost)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Trend:</span>
              <div className={`flex items-center space-x-1 ${getTrendColor(usageData.r2Storage.trend)}`}>
                {getTrendIcon(usageData.r2Storage.trend)}
                <span>{Math.abs(usageData.r2Storage.trend).toFixed(1)}%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bandwidth */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Bandwidth</h3>
            <GlobeAltIcon className="h-6 w-6 text-green-400" />
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Total:</span>
              <span className="text-white font-medium">{formatBytes(usageData.bandwidth.totalBandwidth)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Monthly Cost:</span>
              <span className="text-white font-medium">{formatCurrency(usageData.bandwidth.monthlyCost)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Trend:</span>
              <div className={`flex items-center space-x-1 ${getTrendColor(usageData.bandwidth.trend)}`}>
                {getTrendIcon(usageData.bandwidth.trend)}
                <span>{Math.abs(usageData.bandwidth.trend).toFixed(1)}%</span>
              </div>
            </div>
            <div className="text-sm text-gray-400">
              Top region: {usageData.bandwidth.regions[0]?.region}
            </div>
          </div>
        </div>

        {/* Workers */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Workers</h3>
            <ServerIcon className="h-6 w-6 text-yellow-400" />
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Requests:</span>
              <span className="text-white font-medium">{usageData.workers.totalRequests.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">CPU Time:</span>
              <span className="text-white font-medium">{(usageData.workers.cpuTime / 1000).toFixed(1)}s</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Monthly Cost:</span>
              <span className="text-white font-medium">{formatCurrency(usageData.workers.monthlyCost)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Trend:</span>
              <div className={`flex items-center space-x-1 ${getTrendColor(usageData.workers.trend)}`}>
                {getTrendIcon(usageData.workers.trend)}
                <span>{Math.abs(usageData.workers.trend).toFixed(1)}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {usageData.alerts.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <BellIcon className="h-6 w-6 text-yellow-400 mr-2" />
            <h3 className="text-lg font-semibold text-white">Cost Alerts</h3>
          </div>
          <div className="space-y-3">
            {usageData.alerts.map((alert) => (
              <div key={alert.id} className={`flex items-center p-3 rounded-lg ${
                alert.type === 'critical' ? 'bg-red-900/20 border border-red-500/30' : 'bg-yellow-900/20 border border-yellow-500/30'
              }`}>
                <ExclamationTriangleIcon className={`h-5 w-5 mr-3 ${
                  alert.type === 'critical' ? 'text-red-400' : 'text-yellow-400'
                }`} />
                <div className="flex-1">
                  <p className="text-white font-medium">{alert.message}</p>
                  <p className="text-sm text-gray-400">
                    Current: {alert.current}% | Threshold: {alert.threshold}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cost Trend */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Cost Trend</h3>
          <div className="h-64">
            <Line data={costTrendChartData} options={chartOptions} />
          </div>
        </div>

        {/* Cost Breakdown */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Cost Breakdown</h3>
          <div className="h-64">
            <Doughnut 
              data={costBreakdownData} 
              options={{
                ...chartOptions,
                plugins: {
                  ...chartOptions.plugins,
                  legend: {
                    position: 'bottom' as const,
                    labels: {
                      color: 'rgb(156, 163, 175)'
                    }
                  }
                }
              }} 
            />
          </div>
        </div>

        {/* Bandwidth by Region */}
        <div className="bg-gray-800 rounded-lg p-6 lg:col-span-2">
          <h3 className="text-lg font-semibold text-white mb-4">Bandwidth Usage by Region</h3>
          <div className="h-64">
            <Bar data={bandwidthByRegionData} options={chartOptions} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default UsageCostMonitoring
