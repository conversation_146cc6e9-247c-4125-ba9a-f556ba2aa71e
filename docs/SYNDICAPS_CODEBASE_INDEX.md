# Syndicaps Codebase Index & Documentation

**Version:** 1.0.0  
**Last Updated:** 2025-07-25  
**Framework:** Next.js 15 with App Router  
**Firebase:** v11.10.0  

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Component Catalog](#component-catalog)
3. [API & Services Documentation](#api--services-documentation)
4. [Database Schema](#database-schema)
5. [Feature Mapping](#feature-mapping)
6. [File Structure](#file-structure)
7. [Dependencies & Integrations](#dependencies--integrations)
8. [Recent Enhancements](#recent-enhancements)
9. [Development Guidelines](#development-guidelines)

---

## 🏗️ Architecture Overview

### Core Technology Stack

```
Syndicaps Application Architecture
├── Frontend (Next.js 15.3.3)
│   ├── App Router (/app) - 25+ routes
│   ├── Components (/src/components) - 47+ directories
│   ├── Hooks (/src/hooks) - 35+ custom hooks
│   └── Utilities (/src/lib) - 25+ utility modules
├── Backend Services
│   ├── Firebase 11.10.0 (Auth, Firestore, Storage)
│   ├── Firebase Functions (Node.js)
│   └── PayPal Integration
├── State Management
│   ├── Zustand (Cart, Wishlist, Rewards)
│   ├── React Query (Data fetching)
│   └── Context API (Auth, Theme)
└── Testing Infrastructure
    ├── Jest (Unit tests)
    ├── Playwright (E2E tests)
    └── React Testing Library
```

### Key Architectural Patterns

- **App Router**: Next.js 15 with server-side rendering
- **Component Architecture**: Feature-based organization
- **State Management**: Zustand for global state, React hooks for local
- **Database**: Firebase Firestore with real-time subscriptions
- **Authentication**: Firebase Auth with MFA support
- **Styling**: Tailwind CSS with design system
- **Testing**: Multi-layer testing strategy

---

## 🧩 Component Catalog

### Authentication Components
- **`EnhancedLoginComponent`** - MFA-enabled login with Google OAuth
- **`LoginPopup`** - Modal login interface
- **`AuthErrorBoundary`** - Specialized error handling for auth
- **`AuthErrorHandler`** - Comprehensive error recovery

### Profile System Components
- **`ProfileLayout`** - Main profile container with navigation variants
- **`EnhancedProfileEditor`** - Unified profile editing interface
- **`ProfileCompletionTracker`** - Progress tracking and guidance
- **`ProfilePhotoUpload`** - Image upload with validation
- **`OptimizedUserProfileDropdown`** - Header profile dropdown

### E-commerce Components
- **`ShopComponent`** - Main shop interface with filtering
- **`ProductCard`** - Product display with variants
- **`EnhancedFilterSidebar`** - Advanced product filtering
- **`CartDrawer`** - Mobile-optimized cart interface

### Admin Dashboard Components
- **`AdminButton`** - Consistent admin UI styling
- **`AdminCard`** - Admin interface cards
- **`AdminTable`** - Data tables with sorting/filtering
- **`AdminModal`** - Modal dialogs for admin actions

### Gamification Components
- **`AnimatedPointsDisplay`** - Real-time points visualization
- **`LevelBadge`** - User level display
- **`AchievementCard`** - Achievement showcase
- **`LeaderboardComponent`** - Rankings display

### Community Features
- **`ChallengeCard`** - Challenge participation interface
- **`SubmissionGallery`** - Community submissions display
- **`DiscussionThread`** - Community discussions
- **`VotingInterface`** - Community voting system

---

## 🔌 API & Services Documentation

### Firebase Services

#### Authentication (`/src/lib/auth.ts`)
```typescript
// Core auth functions
signUp(email, password, displayName?)
signIn(email, password) // Returns MFA status
signInWithGoogle()
signOutUser()
```

#### Firestore Collections (`/src/lib/firestore.ts`)
```typescript
// Primary collections
collections = {
  products: 'products',
  orders: 'orders', 
  profiles: 'profiles',
  pointTransactions: 'pointTransactions',
  achievements: 'achievements',
  challenges: 'challenges',
  // ... 50+ collections
}
```

#### Cloud Functions (`/functions/src/`)
- **Payment Processing**: Stripe/PayPal integration
- **Gamification**: Points, achievements, tier updates
- **Notifications**: Email and push notifications
- **Admin Operations**: User management, analytics

### External Integrations

#### PayPal Services (`/src/lib/paypal/`)
- **Authentication**: OAuth token management
- **Invoicing**: Automated invoice generation
- **Payment Processing**: Order fulfillment

#### Sentry Monitoring
- **Error Tracking**: Comprehensive error reporting
- **Performance Monitoring**: Real-time performance metrics

---

## 🗄️ Database Schema

### Core Collections

#### Users & Profiles
```typescript
profiles: {
  userId: string
  displayName: string
  email: string
  avatar?: string
  gamification: {
    totalPointsEarned: number
    currentLevel: number
    tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  }
  preferences: UserPreferences
  createdAt: Timestamp
}
```

#### Gamification System
```typescript
pointTransactions: {
  userId: string
  amount: number
  type: 'earned' | 'spent'
  source: string
  description: string
  createdAt: Timestamp
}

achievements: {
  id: string
  title: string
  description: string
  category: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  requirements: Requirement[]
  rewards: { points: number }
}

userAchievements: {
  userId: string
  achievementId: string
  unlockedAt: Timestamp
  progress: number
}
```

#### E-commerce
```typescript
products: {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  category: string
  images: string[]
  inventory: number
  featured: boolean
}

orders: {
  userId: string
  items: OrderItem[]
  total: number
  status: 'pending' | 'processing' | 'shipped' | 'delivered'
  paymentMethod: string
  createdAt: Timestamp
}
```

---

## 🎯 Feature Mapping

### Authentication Flow
- **Components**: `EnhancedLoginComponent`, `LoginPopup`
- **Services**: `/src/lib/auth.ts`
- **Database**: `users`, `profiles` collections
- **Routes**: `/auth`, `/register`

### Profile Management
- **Components**: `ProfileLayout`, `EnhancedProfileEditor`
- **Services**: `/src/lib/profile.ts`
- **Database**: `profiles`, `userSettings` collections
- **Routes**: `/profile/*`

### E-commerce System
- **Components**: `ShopComponent`, `ProductCard`, `CartDrawer`
- **Services**: `/src/lib/firestore.ts`, `/src/store/cartStore.ts`
- **Database**: `products`, `orders`, `cart` collections
- **Routes**: `/shop`, `/products/*`, `/cart`

### Gamification Engine
- **Components**: `AnimatedPointsDisplay`, `AchievementCard`
- **Services**: `/src/lib/api/gamification/`
- **Database**: `pointTransactions`, `achievements`, `userAchievements`
- **Functions**: Gamification cloud functions

### Admin Dashboard
- **Components**: `/src/admin/components/`
- **Services**: `/src/admin/lib/`
- **Database**: `admins`, `auditLogs` collections
- **Routes**: `/admin/*`

---

## 📁 File Structure

```
syndicaps/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard pages
│   ├── auth/              # Authentication pages
│   ├── profile/           # Profile management pages
│   ├── shop/              # E-commerce pages
│   └── community/         # Community features
├── src/
│   ├── components/        # React components (47+ directories)
│   │   ├── auth/         # Authentication components
│   │   ├── profile/      # Profile components
│   │   ├── shop/         # E-commerce components
│   │   ├── admin/        # Admin components
│   │   └── gamification/ # Gamification components
│   ├── lib/              # Core utilities and services
│   │   ├── firebase/     # Firebase configuration
│   │   ├── api/          # API layer
│   │   └── gamification/ # Gamification engine
│   ├── hooks/            # Custom React hooks (35+)
│   ├── store/            # Zustand state management
│   ├── types/            # TypeScript definitions
│   └── admin/            # Admin-specific logic
├── functions/            # Firebase Cloud Functions
├── docs/                 # Documentation
├── tests/                # Test suites
└── scripts/              # Deployment and utility scripts
```

---

## 📦 Dependencies & Integrations

### Core Dependencies
```json
{
  "firebase": "^11.10.0",
  "next": "^15.3.3",
  "react": "^18.3.23",
  "framer-motion": "^11.18.2",
  "zustand": "^5.0.2",
  "@tanstack/react-query": "^5.81.2"
}
```

### UI & Styling
- **Tailwind CSS**: Utility-first styling
- **Radix UI**: Accessible component primitives
- **Lucide React**: Icon library
- **Class Variance Authority**: Component variants

### Development Tools
- **TypeScript**: Type safety
- **ESLint**: Code linting
- **Jest**: Unit testing
- **Playwright**: E2E testing
- **Sentry**: Error monitoring

### External Services
- **PayPal**: Payment processing
- **Firebase**: Backend services
- **Cloudflare Pages**: Hosting
- **Unsplash**: Image assets

---

## 🚀 Recent Enhancements

### Profile System Improvements
- Enhanced profile editor with tabbed interface
- Improved modal components with better UX
- Guided workflow for profile completion
- Mobile-optimized profile navigation

### Gamification Enhancements
- Multi-phase achievement system
- Level progression with XP mechanics
- Enhanced leaderboards with real-time updates
- Challenge system with team support

### Performance Optimizations
- Lazy loading for profile components
- Optimized Firestore queries
- Bundle size optimization
- Enhanced caching strategies

---

## 📋 Development Guidelines

### Code Organization
- Feature-based component organization
- Consistent naming conventions
- TypeScript for type safety
- Comprehensive error handling

### Testing Strategy
- Unit tests for utilities and hooks
- Integration tests for components
- E2E tests for critical user flows
- Performance testing for key features

### Deployment Process
- Automated CI/CD with quality gates
- Firebase hosting for functions
- Cloudflare Pages for frontend
- Comprehensive monitoring and alerting

---

## 🔧 Detailed Component Reference

### State Management Components

#### Zustand Stores
- **`useCartStore`** (`/src/store/cartStore.ts`)
  - Cart item management with Firestore sync
  - Optimistic updates for better UX
  - Automatic persistence and restoration

- **`useWishlistStore`** (`/src/store/wishlistStore.ts`)
  - Wishlist management with real-time sync
  - Migration support for data consistency
  - Enhanced error handling and recovery

- **`useRewardCartStore`** (`/src/store/rewardCartStore.ts`)
  - Reward redemption cart management
  - Points validation and spending logic

#### Custom Hooks
- **`useUser`** - User authentication state management
- **`usePointHistory`** - Gamification points tracking
- **`useMobileCartDrawer`** - Mobile cart interface logic
- **`useWishlistSummary`** - Wishlist analytics and insights

### Utility Components

#### Loading & Animation
- **`PageLoadingSkeleton`** - Page-level loading states
- **`ProductGridSkeleton`** - Product grid loading animation
- **`LoadingOverlay`** - Overlay loading indicators
- **`MicroInteractions`** - Subtle UI animations

#### Accessibility
- **`SkipLinks`** - Keyboard navigation support
- **`LiveRegion`** - Screen reader announcements
- **`AccessibilityEnhancements`** - WCAG compliance helpers

---

## 🎮 Gamification System Deep Dive

### Achievement Engine (`/src/lib/api/gamification/achievementEngine.ts`)

#### Achievement Categories
- **Onboarding**: Profile completion, first actions
- **Commerce**: Purchase milestones, spending tiers
- **Community**: Social interactions, contributions
- **Loyalty**: Streaks, long-term engagement
- **Special**: Seasonal events, limited-time achievements

#### Achievement Phases
1. **Phase 1**: Core achievements (60+ achievements)
2. **Phase 2**: Advanced social and loyalty achievements
3. **Phase 3**: Dynamic and evolving achievements

#### Points System Configuration
```typescript
GAMIFICATION_POINTS = {
  PURCHASE_PER_DOLLAR: 5,        // 5 points per $1 spent
  LARGE_ORDER_BONUS: 10%,        // 10% bonus for orders >$100
  SIGNUP_BONUS: 200,
  PROFILE_COMPLETION: 75,
  FIRST_PURCHASE: 200,
  RAFFLE_ENTRY: 10,
  COMMUNITY_POST: 20
}
```

### Level System (`/src/lib/levelSystem.ts`)

#### Level Tiers
- **Novice** (Levels 1-10): Switch Novice → Community Member
- **Intermediate** (Levels 11-25): Keycap Enthusiast → Switch Specialist
- **Advanced** (Levels 26-50): Artisan Collector → Master Builder
- **Expert** (Levels 51-100): Keycap Virtuoso → Legend

#### XP Sources
- Purchase activity (primary source)
- Community engagement
- Profile completion
- Achievement unlocks
- Special events and challenges

---

## 🏪 E-commerce System Architecture

### Product Management
- **Real-time inventory tracking**
- **Dynamic pricing with sale support**
- **Category-based filtering**
- **Search functionality with Firestore queries**
- **Image optimization with Next.js Image**

### Cart & Checkout Flow
1. **Add to Cart** → Zustand store update
2. **Cart Sync** → Firestore persistence
3. **Checkout** → PayPal integration
4. **Order Processing** → Cloud Functions
5. **Fulfillment** → Admin dashboard management

### Payment Integration
- **PayPal Checkout SDK** for payment processing
- **Automated invoicing** for raffle winners
- **Refund handling** through admin interface
- **Payment status tracking** with webhooks

---

## 🛡️ Security & Authentication

### Multi-Factor Authentication
- **TOTP support** with QR code generation
- **Trusted device management**
- **Security logging** for audit trails
- **Session management** with automatic cleanup

### Admin Security
- **Role-based access control** (RBAC)
- **Permission-based route protection**
- **Audit logging** for all admin actions
- **Session validation** with middleware

### Data Protection
- **Firestore security rules** for data access
- **Input validation** with Zod schemas
- **XSS protection** with content sanitization
- **CSRF protection** with Next.js built-ins

---

## 📊 Analytics & Monitoring

### Performance Monitoring
- **Sentry integration** for error tracking
- **Firebase Performance** for app metrics
- **Custom analytics** for user behavior
- **Real-time monitoring** dashboards

### Business Intelligence
- **User engagement metrics**
- **Conversion tracking**
- **Gamification effectiveness**
- **Revenue analytics**

---

## 🧪 Testing Strategy

### Test Coverage
- **Unit Tests**: 25% coverage (Jest + React Testing Library)
- **Integration Tests**: Component interactions
- **E2E Tests**: Critical user journeys (Playwright)
- **Performance Tests**: Load and stress testing

### Test Organization
```
tests/
├── unit/              # Component and utility tests
├── integration/       # Feature integration tests
├── e2e/              # End-to-end user flows
├── performance/      # Load and performance tests
└── fixtures/         # Test data and mocks
```

### Testing Tools
- **Jest**: Unit testing framework
- **React Testing Library**: Component testing
- **Playwright**: E2E testing
- **AVA**: Node.js/server-side testing
- **Firebase Functions Test**: Cloud function testing

---

## 🚀 Deployment & DevOps

### CI/CD Pipeline
1. **Code Quality Gates**: ESLint, TypeScript checks
2. **Test Execution**: Unit, integration, E2E tests
3. **Build Process**: Next.js optimization
4. **Firebase Deployment**: Functions and Firestore rules
5. **Frontend Deployment**: Cloudflare Pages

### Environment Management
- **Development**: Local Firebase emulators
- **Staging**: Firebase staging project
- **Production**: Firebase production with monitoring

### Performance Optimization
- **Bundle analysis** with webpack-bundle-analyzer
- **Image optimization** with Next.js Image
- **Code splitting** with dynamic imports
- **Caching strategies** for static assets

---

## 📚 Documentation Standards

### Code Documentation
- **JSDoc comments** for all public functions
- **TypeScript interfaces** for data structures
- **README files** for major features
- **Inline comments** for complex logic

### API Documentation
- **OpenAPI specs** for REST endpoints
- **GraphQL schema** documentation
- **Firebase rules** documentation
- **Cloud function** specifications

---

*This comprehensive documentation is maintained by the Syndicaps development team. For updates, questions, or contributions, contact <EMAIL>*

**Last Updated:** 2025-07-25
**Version:** 1.0.0
**Contributors:** Syndicaps Development Team
