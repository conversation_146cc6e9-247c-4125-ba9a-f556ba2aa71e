# AI Coordination System

This directory contains the coordination system for managing development work between Augment Agent and <PERSON> Code (Cursor AI).

## Quick Start

1. **Check current work**: Review `.ai-coordination.md` before starting
2. **Claim your work**: Update the file with your work area and timeline
3. **Use proper commits**: Use `[AUGMENT]` or `[CURSOR]` prefixes
4. **Update progress**: Add entries to `AI_WORK_LOG.md`
5. **Document handoffs**: Use `HANDOFF_NOTES.md` for work transitions

## Files Overview

- `.ai-coordination.md` - Main coordination file for claiming work areas
- `AI_WORK_LOG.md` - Daily progress tracking and communication
- `HANDOFF_NOTES.md` - Templates and documentation for work handoffs
- `scripts/setup-ai-coordination.sh` - Setup script for Git aliases and templates
- `scripts/ai-coordination-helpers.sh` - Helper functions for coordination

## Git Aliases

After running the setup script, you can use these aliases:

### Augment Agent
- `git augment-feat "description"` - Feature commits
- `git augment-fix "description"` - Bug fix commits
- `git augment-docs "description"` - Documentation commits
- `git augment-branch "branch-name"` - Create Augment branch

### Claude Code
- `git cursor-feat "description"` - Feature commits
- `git cursor-fix "description"` - Bug fix commits
- `git cursor-ui "description"` - UI improvement commits
- `git cursor-branch "branch-name"` - Create Cursor branch

### Collaborative
- `git collab-feat "description"` - Collaborative feature commits
- `git collab-merge "description"` - Merge commits

## Helper Functions

Source the helper script to use these functions:
```bash
source scripts/ai-coordination-helpers.sh

check-claims      # View current work claims
ai-status         # Quick coordination status
update-log "msg"  # Add entry to work log
handoff-template  # Show handoff template
```

## Best Practices

1. **Always check claims** before starting work
2. **Commit frequently** with descriptive messages
3. **Update logs daily** to maintain communication
4. **Use handoff templates** for work transitions
5. **Test before handoffs** to ensure clean transitions
6. **Document decisions** for future reference

## Troubleshooting

- **Merge conflicts**: Follow priority rules in `.ai-coordination.md`
- **Missing context**: Check `HANDOFF_NOTES.md` for previous work
- **Coordination issues**: Update `AI_WORK_LOG.md` with blockers
- **Setup problems**: Re-run `scripts/setup-ai-coordination.sh`
