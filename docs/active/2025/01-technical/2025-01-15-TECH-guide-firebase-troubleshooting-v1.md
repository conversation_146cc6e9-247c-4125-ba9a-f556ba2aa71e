# 🔥 Firebase Connection Troubleshooting Guide

## 🚨 Current Issue: Auth Token Fetch Failed

**Error Message:**
```
Firestore (11.10.0): Could not reach Cloud Firestore backend. Connection failed 1 times. 
Most recent error: FirebaseError: [code=unknown]: Fetching auth token failed: 
Firebase: Error (auth/network-request-failed).
```

## 🔍 Root Cause Analysis

### Primary Issues Identified:
1. **Missing Firebase Admin Private Key** - Placeholder value in `.env.local`
2. **Network Authentication Failure** - Auth token fetching fails
3. **Configuration Conflicts** - Multiple Firebase config files

## 🛠️ Step-by-Step Solution

### Step 1: Fix Firebase Admin Configuration

1. **Get Your Private Key:**
   ```bash
   # Go to Firebase Console
   https://console.firebase.google.com/project/syndicaps-fullpower/settings/serviceaccounts/adminsdk
   
   # Click "Generate new private key"
   # Download the JSON file
   ```

2. **Update .env.local:**
   ```bash
   # Replace the placeholder with your actual private key
   FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
   YOUR_ACTUAL_PRIVATE_KEY_CONTENT_HERE
   -----END PRIVATE KEY-----"
   ```

### Step 2: Test Network Connectivity

```bash
# Run our connection test script
node scripts/test-firebase-connection.js
```

### Step 3: Verify Firebase Project Settings

1. **Check Project Status:**
   - Ensure your Firebase project is active
   - Verify billing is enabled (required for production)
   - Check quotas and limits

2. **Verify Authentication Settings:**
   - Go to Firebase Console → Authentication
   - Ensure Email/Password and Google providers are enabled
   - Check authorized domains

### Step 4: Security Rules Verification

```bash
# Deploy latest security rules
firebase deploy --only firestore:rules

# Test rules
firebase firestore:rules:test
```

## 🔧 Quick Fixes

### Immediate Temporary Fix:
```bash
# 1. Restart your development server
npm run dev

# 2. Clear browser cache and localStorage
# Open DevTools → Application → Storage → Clear storage

# 3. Test with emulator (if needed)
export NEXT_PUBLIC_USE_FIREBASE_EMULATOR=true
npm run dev
```

### Network Issues:
```bash
# Check if you're behind a corporate firewall
curl -I https://firebase.googleapis.com
curl -I https://firestore.googleapis.com

# Test DNS resolution
nslookup firebase.googleapis.com
```

## 🚀 Testing Your Fix

### 1. Run Connection Test:
```bash
node scripts/test-firebase-connection.js
```

### 2. Test Authentication:
```bash
# Start your app
npm run dev

# Try logging in with a test account
# Check browser console for errors
```

### 3. Test Firestore:
```bash
# Try reading/writing data
# Monitor Firebase Console for activity
```

## 🔍 Common Error Patterns

### Auth Token Errors:
- `auth/network-request-failed` → Network/firewall issues
- `auth/invalid-api-key` → Wrong API key in config
- `auth/project-not-found` → Wrong project ID

### Firestore Errors:
- `permission-denied` → Security rules blocking access
- `unavailable` → Network connectivity issues
- `deadline-exceeded` → Timeout issues

## 📊 Monitoring & Prevention

### Add Connection Monitoring:
```javascript
// Add to your Firebase config
import { enableNetwork, disableNetwork } from 'firebase/firestore'

window.addEventListener('online', () => {
  enableNetwork(db)
  console.log('🟢 Network restored - Firestore reconnected')
})

window.addEventListener('offline', () => {
  console.log('🔴 Network lost - Firestore offline mode')
})
```

### Error Handling:
```javascript
// Enhanced error handling
try {
  // Firebase operations
} catch (error) {
  if (error.code === 'auth/network-request-failed') {
    // Handle network issues
    console.log('Network issue detected, retrying...')
  }
}
```

## 🆘 Emergency Rollback

If issues persist:

```bash
# 1. Revert to working commit
git log --oneline -10
git reset --hard <working_commit_hash>

# 2. Use Firebase emulator
export NEXT_PUBLIC_USE_FIREBASE_EMULATOR=true
firebase emulators:start

# 3. Contact support
# Check Firebase Status: https://status.firebase.google.com/
```

## 📞 Support Resources

- **Firebase Status**: https://status.firebase.google.com/
- **Firebase Support**: https://firebase.google.com/support/
- **Community**: https://stackoverflow.com/questions/tagged/firebase

---

**Next Steps:**
1. Fix the private key in `.env.local`
2. Run the connection test script
3. Verify authentication works
4. Monitor for stability
