# API Cache Worker Testing Documentation

## Overview

This document provides comprehensive testing guidelines and procedures for the API Cache Worker system, including unit tests, integration tests, performance benchmarks, and validation criteria.

## Test Architecture

### Test Structure
```
workers/
├── __tests__/
│   ├── api-cache.test.ts          # Main test suite
│   ├── rate-limiter.test.ts       # Rate limiting tests
│   ├── analytics.test.ts          # Analytics tests
│   └── cache-invalidation.test.ts # Cache invalidation tests
├── scripts/
│   └── test-api-cache.ts          # Test runner script
└── vitest.config.ts               # Test configuration
```

### Test Categories

#### 1. Unit Tests
- **Cache Behavior**: Hit/miss/stale scenarios
- **Rate Limiting**: IP-based and user-based limits
- **Analytics**: Metrics collection and reporting
- **Cache Invalidation**: Tag-based, pattern-based, key-based
- **Error Handling**: Graceful degradation and fallbacks

#### 2. Integration Tests
- **End-to-End Cache Flow**: Complete request lifecycle
- **Multi-Component Interaction**: Cache + Rate Limiting + Analytics
- **Admin Operations**: Cache management and monitoring

#### 3. Performance Tests
- **Response Time Benchmarks**: Cache hits, misses, rate limiting
- **Throughput Testing**: Concurrent request handling
- **Memory Efficiency**: Large response handling
- **Stress Testing**: High volume request processing

## Test Execution

### Running Tests

```bash
# Run all tests
npm test

# Run specific test suite
npx vitest run __tests__/api-cache.test.ts

# Run with coverage
npx vitest run --coverage

# Run performance tests only
npx vitest run --grep="Performance"

# Run integration tests only
npx vitest run --grep="Integration"

# Run comprehensive test suite
npx tsx workers/scripts/test-api-cache.ts
```

### Test Environment Setup

```typescript
// Mock environment for testing
const mockEnv = {
  API_CACHE_KV: MockKVNamespace,
  API_METADATA_KV: MockKVNamespace,
  RATE_LIMIT_KV: MockKVNamespace,
  FIREBASE_FUNCTIONS_URL: 'https://us-central1-syndicaps.cloudfunctions.net',
  FIREBASE_PROJECT_ID: 'syndicaps',
  FIREBASE_REGION: 'us-central1',
  ENVIRONMENT: 'test',
  RATE_LIMIT_ENABLED: 'true'
}
```

## Performance Targets

### Response Time Targets
- **Cache Hits**: < 50ms (P95)
- **Cache Misses**: < 500ms (P95)
- **Rate Limited Requests**: < 100ms (P95)
- **Admin Operations**: < 1000ms (P95)

### Throughput Targets
- **Overall Throughput**: > 1000 requests/second
- **Cache Hit Ratio**: > 85%
- **Error Rate**: < 1%
- **Availability**: > 99.9%

### Resource Utilization
- **Memory Usage**: < 128MB per worker
- **CPU Usage**: < 50ms per request
- **KV Operations**: < 10 per request
- **Cache Size**: < 100MB total

## Test Scenarios

### Cache Behavior Tests

#### Cache Hit Scenario
```typescript
it('should serve from cache on cache hit', async () => {
  // Setup cached data
  const cachedData = new ArrayBuffer(8)
  const metadata = {
    timestamp: Date.now() - 60000,
    ttl: 300,
    tags: ['products'],
    hitCount: 0,
    contentType: 'application/json',
    etag: '"test-etag"',
    size: 8
  }

  mockEnv.API_CACHE_KV.get.mockResolvedValue(cachedData)
  mockEnv.API_METADATA_KV.get.mockResolvedValue(JSON.stringify(metadata))

  const request = new Request('https://api-cache.syndicaps.com/api/products')
  const response = await worker.fetch(request, mockEnv, mockCtx)
  
  expect(response.status).toBe(200)
  expect(response.headers.get('X-Cache-Status')).toBe('HIT')
  expect(response.headers.get('ETag')).toBe('"test-etag"')
})
```

#### Stale-While-Revalidate Scenario
```typescript
it('should serve stale content and refresh in background', async () => {
  // Setup stale cached data
  const cachedData = new ArrayBuffer(8)
  const metadata = {
    timestamp: Date.now() - 400000, // 6.67 minutes ago (stale)
    ttl: 300, // 5 minutes
    tags: ['products'],
    hitCount: 5,
    contentType: 'application/json',
    etag: '"stale-etag"',
    size: 8
  }

  mockEnv.API_CACHE_KV.get.mockResolvedValue(cachedData)
  mockEnv.API_METADATA_KV.get.mockResolvedValue(JSON.stringify(metadata))

  const request = new Request('https://api-cache.syndicaps.com/api/products')
  const response = await worker.fetch(request, mockEnv, mockCtx)
  
  expect(response.status).toBe(200)
  expect(response.headers.get('X-Cache-Status')).toBe('STALE')
  expect(mockCtx.waitUntil).toHaveBeenCalled() // Background refresh
})
```

### Rate Limiting Tests

#### Rate Limit Enforcement
```typescript
it('should block requests exceeding rate limit', async () => {
  mockEnv.RATE_LIMIT_KV.get.mockResolvedValue(
    JSON.stringify({ count: 150, window: Date.now() })
  )
  
  const request = new Request('https://api-cache.syndicaps.com/api/products', {
    headers: { 'CF-Connecting-IP': '***********' }
  })
  
  const response = await worker.fetch(request, mockEnv, mockCtx)
  expect(response.status).toBe(429)
  expect(response.headers.get('Retry-After')).toBeDefined()
})
```

### Analytics Tests

#### Metrics Collection
```typescript
it('should record analytics for all requests', async () => {
  const request = new Request('https://api-cache.syndicaps.com/api/products')
  await worker.fetch(request, mockEnv, mockCtx)
  
  // Verify analytics recording was called
  expect(mockEnv.API_METADATA_KV.put).toHaveBeenCalledWith(
    expect.stringMatching(/analytics:/),
    expect.any(String),
    expect.any(Object)
  )
})
```

## Validation Criteria

### Cache Performance Validation
```typescript
export class ApiCacheValidator {
  static validateCacheHitRatio(metrics: any): boolean {
    const hitRatio = metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)
    return hitRatio >= 0.8 // 80% hit ratio target
  }

  static validateResponseTime(responseTime: number, cacheStatus: string): boolean {
    if (cacheStatus === 'HIT') {
      return responseTime < 50 // Cache hits should be very fast
    } else if (cacheStatus === 'MISS') {
      return responseTime < 500 // Cache misses should be reasonable
    }
    return responseTime < 1000 // Other operations
  }
}
```

### Rate Limiting Validation
```typescript
static validateRateLimit(requests: number, timeWindow: number, limit: number): boolean {
  const requestsPerSecond = requests / (timeWindow / 1000)
  const limitPerSecond = limit / 3600 // Convert hourly limit to per second
  return requestsPerSecond <= limitPerSecond
}
```

### Error Rate Validation
```typescript
static validateErrorRate(errors: number, total: number): boolean {
  const errorRate = total > 0 ? errors / total : 0
  return errorRate <= 0.01 // 1% error rate threshold
}
```

## Continuous Integration

### Test Pipeline
1. **Lint and Type Check**: ESLint + TypeScript
2. **Unit Tests**: Core functionality validation
3. **Integration Tests**: Component interaction validation
4. **Performance Tests**: Benchmark validation
5. **Coverage Report**: Minimum 80% coverage required

### Quality Gates
- **Test Pass Rate**: 100% required
- **Code Coverage**: 80% minimum
- **Performance Benchmarks**: Must meet targets
- **Security Scan**: No critical vulnerabilities
- **Dependency Audit**: No high-risk dependencies

## Monitoring and Alerting

### Test Metrics
- **Test Execution Time**: Track test suite performance
- **Test Reliability**: Monitor flaky tests
- **Coverage Trends**: Track coverage over time
- **Performance Regression**: Detect performance degradation

### Alert Conditions
- **Test Failures**: Immediate notification
- **Coverage Drop**: Below 80% threshold
- **Performance Regression**: > 20% degradation
- **Security Issues**: Critical vulnerabilities detected

## Best Practices

### Test Writing Guidelines
1. **Descriptive Test Names**: Clear intent and expected behavior
2. **Isolated Tests**: No dependencies between tests
3. **Comprehensive Mocking**: Mock all external dependencies
4. **Performance Awareness**: Include timing assertions
5. **Error Scenarios**: Test failure conditions

### Mock Strategy
1. **KV Storage**: Mock all KV operations
2. **External APIs**: Mock Firebase Functions calls
3. **Time-based Logic**: Mock Date.now() for consistency
4. **Random Values**: Use deterministic values in tests
5. **Network Conditions**: Simulate various network states

### Maintenance
1. **Regular Updates**: Keep tests current with code changes
2. **Performance Baselines**: Update benchmarks as needed
3. **Mock Data**: Maintain realistic test data
4. **Documentation**: Keep test docs synchronized
5. **Cleanup**: Remove obsolete tests and mocks
