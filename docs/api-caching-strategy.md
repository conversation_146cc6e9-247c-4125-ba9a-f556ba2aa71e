# API Caching Strategy - Cloudflare Workers

## Executive Summary

This document outlines the comprehensive API caching strategy for the Syndicaps hybrid deployment using Cloudflare Workers. The strategy implements intelligent caching, rate limiting, and performance optimization for Firebase Functions API endpoints with stale-while-revalidate patterns and endpoint-specific rules.

## Architecture Overview

### Request Flow
```
Client Request → Cloudflare Worker → Cache Check → Firebase Functions → Response
                      ↓                ↓              ↓
                 Rate Limiting    Cache Hit/Miss   Background Refresh
                      ↓                ↓              ↓
                 Analytics       Serve Cached    Update Cache
```

### Core Components
1. **API Cache Worker** (`api-cache.ts`)
2. **Cache Management System** (`lib/cache-manager.ts`)
3. **Rate Limiting Engine** (`lib/rate-limiter.ts`)
4. **Analytics Collector** (`lib/analytics.ts`)
5. **Configuration Manager** (`lib/config.ts`)

## Caching Strategy

### Cache Hierarchy
```
1. Edge Cache (Cloudflare CDN)     - 30 seconds to 5 minutes
2. KV Cache (Worker Storage)       - 5 minutes to 24 hours
3. Memory Cache (Worker Runtime)   - 30 seconds to 5 minutes
4. Origin (Firebase Functions)     - Always fresh
```

### Endpoint-Specific Caching Rules

#### 1. Product Catalog APIs
```typescript
'/api/products': {
  ttl: 300,              // 5 minutes
  staleWhileRevalidate: 600,  // 10 minutes
  maxAge: 1800,          // 30 minutes
  tags: ['products', 'catalog'],
  varyBy: ['user-type', 'region']
}
```

#### 2. User Profile APIs
```typescript
'/api/user/profile': {
  ttl: 60,               // 1 minute
  staleWhileRevalidate: 300,  // 5 minutes
  maxAge: 900,           // 15 minutes
  tags: ['user', 'profile'],
  varyBy: ['user-id'],
  private: true
}
```

#### 3. Authentication APIs
```typescript
'/api/auth/*': {
  ttl: 0,                // No caching
  staleWhileRevalidate: 0,
  maxAge: 0,
  tags: ['auth'],
  bypass: true
}
```

#### 4. Static Content APIs
```typescript
'/api/content/static': {
  ttl: 3600,             // 1 hour
  staleWhileRevalidate: 7200,  // 2 hours
  maxAge: 86400,         // 24 hours
  tags: ['content', 'static'],
  varyBy: ['locale']
}
```

#### 5. Real-time APIs
```typescript
'/api/notifications': {
  ttl: 30,               // 30 seconds
  staleWhileRevalidate: 60,   // 1 minute
  maxAge: 300,           // 5 minutes
  tags: ['notifications', 'realtime'],
  varyBy: ['user-id']
}
```

#### 6. Analytics APIs
```typescript
'/api/analytics/*': {
  ttl: 900,              // 15 minutes
  staleWhileRevalidate: 1800, // 30 minutes
  maxAge: 3600,          // 1 hour
  tags: ['analytics'],
  varyBy: ['date-range', 'user-role']
}
```

## Rate Limiting Strategy

### Rate Limit Tiers

#### 1. Anonymous Users
```typescript
{
  requests: 100,         // per hour
  burst: 20,             // per minute
  window: 3600,          // 1 hour
  identifier: 'ip'
}
```

#### 2. Authenticated Users
```typescript
{
  requests: 1000,        // per hour
  burst: 100,            // per minute
  window: 3600,          // 1 hour
  identifier: 'user-id'
}
```

#### 3. Premium Users
```typescript
{
  requests: 5000,        // per hour
  burst: 500,            // per minute
  window: 3600,          // 1 hour
  identifier: 'user-id'
}
```

#### 4. Admin Users
```typescript
{
  requests: 10000,       // per hour
  burst: 1000,           // per minute
  window: 3600,          // 1 hour
  identifier: 'user-id'
}
```

### Rate Limiting Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Retry-After: 3600
```

## Stale-While-Revalidate Implementation

### Pattern Overview
1. **Cache Hit (Fresh)**: Serve immediately
2. **Cache Hit (Stale)**: Serve stale + background refresh
3. **Cache Miss**: Fetch from origin + cache result

### Background Refresh Logic
```typescript
if (cacheAge > ttl && cacheAge < staleWhileRevalidate) {
  // Serve stale content immediately
  respondWithStale(cachedResponse)
  
  // Trigger background refresh
  ctx.waitUntil(refreshCache(request))
}
```

### Refresh Strategies
- **Immediate**: For critical endpoints
- **Scheduled**: For batch operations
- **On-demand**: For user-triggered updates
- **Conditional**: Based on cache hit ratio

## Cache Invalidation Strategy

### Invalidation Methods

#### 1. Tag-Based Invalidation
```typescript
// Invalidate all product-related caches
await cacheManager.invalidateByTag('products')

// Invalidate specific user caches
await cacheManager.invalidateByTag('user', userId)
```

#### 2. Pattern-Based Invalidation
```typescript
// Invalidate all user profile endpoints
await cacheManager.invalidateByPattern('/api/user/*/profile')

// Invalidate all analytics for specific date
await cacheManager.invalidateByPattern('/api/analytics/2024-01-*')
```

#### 3. Time-Based Invalidation
```typescript
// Automatic TTL expiration
// Manual purge for emergency updates
await cacheManager.purgeAll()
```

### Invalidation Triggers
- **Data Updates**: Firestore document changes
- **User Actions**: Profile updates, purchases
- **Admin Actions**: Content management, configuration changes
- **Scheduled**: Daily cache refresh for analytics
- **Emergency**: Manual purge for critical updates

## Performance Optimization

### Cache Warming Strategies
```typescript
// Pre-populate frequently accessed endpoints
const warmupEndpoints = [
  '/api/products/featured',
  '/api/content/homepage',
  '/api/categories/popular'
]

// Scheduled warming during low traffic
await scheduleWarmup(warmupEndpoints, '0 2 * * *') // 2 AM daily
```

### Compression and Optimization
```typescript
{
  compression: 'gzip',
  minifyJson: true,
  removeWhitespace: true,
  compressThreshold: 1024  // 1KB
}
```

### Edge Cache Optimization
```typescript
// Cloudflare Cache API integration
const cacheKey = new Request(url, {
  headers: {
    'CF-Cache-Tag': tags.join(','),
    'Cache-Control': `public, max-age=${maxAge}, s-maxage=${ttl}`
  }
})
```

## Monitoring and Analytics

### Key Metrics
- **Cache Hit Ratio**: Target >85%
- **Response Time**: P95 <200ms (cached), P95 <2000ms (origin)
- **Error Rate**: <1%
- **Rate Limit Violations**: <0.1%

### Analytics Collection
```typescript
interface CacheMetrics {
  endpoint: string
  method: string
  cacheStatus: 'HIT' | 'MISS' | 'STALE' | 'BYPASS'
  responseTime: number
  originTime?: number
  cacheAge?: number
  userType: string
  timestamp: number
}
```

### Alerting Thresholds
- Cache hit ratio <80% for 10 minutes
- Response time P95 >500ms for 5 minutes
- Error rate >5% for 2 minutes
- Rate limit violations >1% for 5 minutes

## Security Considerations

### Cache Security
- **Private Data**: Never cache sensitive user data
- **Authentication**: Bypass cache for auth endpoints
- **CORS**: Proper CORS headers for cached responses
- **Headers**: Sanitize sensitive headers before caching

### Rate Limiting Security
- **DDoS Protection**: Aggressive rate limiting for suspicious patterns
- **Bot Detection**: Enhanced limits for detected bots
- **IP Reputation**: Dynamic limits based on IP reputation
- **Bypass Mechanisms**: Admin override for legitimate high-volume usage

## Configuration Management

### Environment-Specific Settings

#### Development
```typescript
{
  defaultTtl: 60,        // 1 minute
  maxTtl: 300,           // 5 minutes
  rateLimitMultiplier: 10, // More lenient
  enableDebugHeaders: true
}
```

#### Staging
```typescript
{
  defaultTtl: 300,       // 5 minutes
  maxTtl: 1800,          // 30 minutes
  rateLimitMultiplier: 2,  // Slightly lenient
  enableDebugHeaders: true
}
```

#### Production
```typescript
{
  defaultTtl: 600,       // 10 minutes
  maxTtl: 3600,          // 1 hour
  rateLimitMultiplier: 1,  // Standard limits
  enableDebugHeaders: false
}
```

### Feature Flags Integration
```typescript
const cacheConfig = {
  enableApiCache: await featureFlags.isEnabled('api-cache', request),
  enableRateLimit: await featureFlags.isEnabled('rate-limiting', request),
  enableStaleWhileRevalidate: await featureFlags.isEnabled('swr', request)
}
```

## Implementation Phases

### Phase 1: Basic Caching (Week 8)
- Core cache worker implementation
- Basic TTL-based caching
- Simple rate limiting
- Firebase Functions integration

### Phase 2: Advanced Features (Week 9)
- Stale-while-revalidate implementation
- Tag-based invalidation
- Advanced rate limiting
- Performance monitoring

### Phase 3: Optimization (Week 10)
- Cache warming strategies
- Advanced analytics
- Performance optimization
- Production deployment

## Success Criteria

### Performance Targets
- **Cache Hit Ratio**: >85%
- **Response Time Improvement**: 50% reduction for cached endpoints
- **Origin Load Reduction**: 70% reduction in Firebase Functions calls
- **Rate Limit Effectiveness**: <0.1% false positives

### Reliability Targets
- **Uptime**: >99.9%
- **Error Rate**: <1%
- **Cache Consistency**: 100% for critical data
- **Failover Time**: <30 seconds to origin

This comprehensive API caching strategy provides the foundation for implementing a high-performance, scalable, and reliable caching layer that will significantly improve the Syndicaps application performance while reducing costs and server load.
