# Augment Code Integration Guide for Syndicaps

## 🎯 Quick Reference for AI Assistants

### Essential Context Files
When working on Syndicaps documentation, always reference:
```yaml
# Core Standards
- file: docs/25-07-15-technical-documentation/syndicaps-documentation-standards.md
  why: Complete documentation and coding standards

# Project Patterns  
- file: CLAUDE.md
  why: Global AI assistant rules and conventions

- file: PRPs/templates/syndicaps_base_prp.md
  why: Base patterns for all implementations

# Examples Library
- file: examples/README.md
  why: Code patterns and implementation examples
```

### Code Snippet Best Practices

#### ✅ Correct Usage
<augment_code_snippet path="src/components/UserCard.tsx" mode="EXCERPT">
```typescript
interface UserCardProps {
  user: UserProfile;
  showStats?: boolean;
}