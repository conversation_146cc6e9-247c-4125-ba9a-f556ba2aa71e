# Syndicaps Gamification System - Admin Guide

## Overview

This guide provides comprehensive documentation for administrators managing the Syndicaps gamification system. It covers system configuration, user management, content creation, analytics, and troubleshooting.

## Table of Contents

1. [Admin Dashboard Access](#admin-dashboard-access)
2. [System Configuration](#system-configuration)
3. [Achievement Management](#achievement-management)
4. [Reward Management](#reward-management)
5. [User Management](#user-management)
6. [Analytics & Reporting](#analytics--reporting)
7. [Content Moderation](#content-moderation)
8. [System Maintenance](#system-maintenance)
9. [Troubleshooting](#troubleshooting)
10. [API Reference](#api-reference)

## Admin Dashboard Access

### ⚠️ Security Notice
**IMPORTANT**: Current admin authentication has known vulnerabilities. Security fixes are in progress. Contact IT security team for current access procedures.

### Authentication

1. **Admin Login**: Use your admin credentials at `/admin/login`
2. **Two-Factor Authentication**: ⚠️ Currently being implemented - will be required for all admin accounts
3. **Session Management**: ⚠️ Enhanced session management being implemented
4. **Role Verification**: ⚠️ System role verification being strengthened

### Admin Roles

#### **Super Admin**
- Full system access and configuration
- User role management
- System-wide settings and maintenance
- Database access and backups

#### **Content Admin**
- Achievement and reward management
- Content moderation and approval
- User-generated content oversight
- Community management

#### **Analytics Admin**
- Reporting and analytics access
- Performance monitoring
- User behavior analysis
- Business intelligence tools

#### **Support Admin**
- User support and assistance
- Account management (limited)
- Issue resolution and escalation
- Customer service tools

## System Configuration

### Gamification Settings

Access: **Admin Dashboard > Settings > Gamification**

#### **Points Configuration**
```javascript
{
  pointsPerDollar: 5,           // Points earned per dollar spent
  dailyLoginPoints: 5,          // Daily login reward
  reviewPoints: 50,             // Base points for reviews
  socialSharePoints: 25,        // Points for social sharing
  referralPoints: 500,          // Points for successful referrals
  birthdayBonus: 500,           // Birthday bonus points
  newsletterSignupPoints: 100,  // Newsletter subscription points
  profileCompletionPoints: 50,  // Profile completion bonus
  firstPurchaseBonus: 200       // First purchase bonus
}
```

#### **Tier Thresholds**
```javascript
{
  bronze: 0,        // Bronze tier minimum
  silver: 1000,     // Silver tier minimum
  gold: 5000,       // Gold tier minimum
  platinum: 15000,  // Platinum tier minimum
  diamond: 50000    // Diamond tier minimum
}
```

#### **Rate Limiting**
```javascript
{
  pointsPerHour: 1000,          // Max points per user per hour
  achievementsPerDay: 50,       // Max achievements per user per day
  rewardsPerDay: 10,            // Max reward redemptions per day
  apiCallsPerMinute: 100        // API rate limit per user
}
```

### Feature Flags

Control system features with toggles:

- **Gamification Enabled**: Master switch for entire system
- **Achievements Enabled**: Enable/disable achievement system
- **Rewards Enabled**: Enable/disable reward shop
- **Leaderboards Enabled**: Enable/disable public leaderboards
- **Social Sharing Enabled**: Enable/disable social media integration
- **Beta Features**: Enable experimental features for testing

## Achievement Management

### Creating Achievements

Access: **Admin Dashboard > Gamification > Achievements > Create New**

#### **Required Fields**
- **Name**: Achievement title (max 50 characters)
- **Description**: Clear description of requirements (max 200 characters)
- **Icon**: Emoji or icon identifier
- **Category**: purchase, social, milestone, special
- **Rarity**: common, rare, epic, legendary
- **Points Awarded**: Point reward for unlocking
- **Requirements**: Detailed requirement configuration

#### **Requirement Types**

**Count Requirements**
```javascript
{
  type: "count",
  target: 10,           // Target count
  field: "purchases"    // Field to count
}
```

**Amount Requirements**
```javascript
{
  type: "amount",
  target: 100,          // Target amount
  field: "order_total"  // Field to sum
}
```

**Streak Requirements**
```javascript
{
  type: "streak",
  target: 7,            // Consecutive days
  field: "daily_login"  // Activity to track
}
```

**Time Requirements**
```javascript
{
  type: "time",
  target: 1640995200000  // Unix timestamp
}
```

### Achievement Categories

#### **Purchase Achievements**
- Order-based achievements
- Spending milestones
- Product-specific achievements
- Loyalty rewards

#### **Social Achievements**
- Review and rating achievements
- Social media sharing
- Community participation
- Referral rewards

#### **Milestone Achievements**
- Point accumulation milestones
- Time-based achievements
- Collection achievements
- Anniversary rewards

#### **Special Achievements**
- Limited-time events
- Beta testing participation
- Community contributions
- Exclusive recognitions

### Bulk Operations

- **Import Achievements**: CSV upload for bulk creation
- **Export Data**: Download achievement data and statistics
- **Bulk Edit**: Modify multiple achievements simultaneously
- **Batch Activation**: Enable/disable multiple achievements

## Reward Management

### Creating Rewards

Access: **Admin Dashboard > Gamification > Rewards > Create New**

#### **Reward Types**

**Discount Coupons**
```javascript
{
  type: "discount",
  pointsCost: 500,
  metadata: {
    discountPercent: 10,
    validDays: 30,
    minimumOrder: 50
  }
}
```

**Physical Products**
```javascript
{
  type: "physical",
  pointsCost: 2000,
  stock: 10,
  metadata: {
    shippingRequired: true,
    weight: 0.1,
    dimensions: "5x5x2"
  }
}
```

**Digital Rewards**
```javascript
{
  type: "digital",
  pointsCost: 800,
  metadata: {
    deliveryMethod: "automatic",
    validDays: 90
  }
}
```

**Access Rewards**
```javascript
{
  type: "access",
  pointsCost: 1500,
  metadata: {
    accessType: "early_access",
    duration: 24
  }
}
```

### Inventory Management

- **Stock Tracking**: Monitor available quantities
- **Auto-Disable**: Automatically disable out-of-stock rewards
- **Restock Alerts**: Notifications when stock runs low
- **Purchase History**: Track redemption patterns and popularity

### Fulfillment Process

1. **Automatic Processing**: Digital rewards delivered instantly
2. **Manual Review**: Physical rewards require admin approval
3. **Shipping Integration**: Connect with shipping providers
4. **Status Updates**: Automatic customer notifications
5. **Completion Tracking**: Monitor fulfillment success rates

## User Management

### User Overview

Access: **Admin Dashboard > Users > Gamification Overview**

#### **User Statistics**
- Total points earned and spent
- Achievement unlock count and progress
- Reward redemption history
- Tier status and progression
- Activity timeline and engagement

#### **Bulk User Operations**
- **Point Adjustments**: Add or remove points with reason
- **Achievement Grants**: Manually unlock achievements
- **Tier Adjustments**: Modify user tier status
- **Account Flags**: Mark accounts for special handling

### User Support Tools

#### **Point Management**
```javascript
// Award points to user
await awardPoints(userId, amount, source, description, metadata)

// Deduct points (with reason)
await deductPoints(userId, amount, reason, adminId)

// View point history
await getPointHistory(userId, limit, filters)
```

#### **Achievement Support**
```javascript
// Manually unlock achievement
await unlockAchievement(userId, achievementId, adminOverride: true)

// Reset achievement progress
await resetAchievementProgress(userId, achievementId)

// View achievement progress
await getAchievementProgress(userId)
```

#### **Account Management**
- **Profile Verification**: Verify user profiles and information
- **Fraud Detection**: Monitor for suspicious activity patterns
- **Account Recovery**: Assist with lost points or achievements
- **Data Export**: Provide user data exports for GDPR compliance

## Analytics & Reporting

### Dashboard Metrics

Access: **Admin Dashboard > Analytics > Gamification**

#### **Key Performance Indicators**
- **Active Users**: Daily/weekly/monthly active users in gamification
- **Engagement Rate**: Percentage of users earning points daily
- **Achievement Unlock Rate**: Average achievements per user
- **Reward Redemption Rate**: Percentage of points spent vs earned
- **Tier Distribution**: User distribution across member tiers

#### **Revenue Impact**
- **Gamification ROI**: Revenue increase attributed to gamification
- **Purchase Frequency**: Order frequency by tier and engagement
- **Average Order Value**: AOV impact of gamification features
- **Customer Lifetime Value**: CLV improvement from gamification

### Custom Reports

#### **User Engagement Reports**
- Point earning patterns and trends
- Achievement completion rates by category
- Reward redemption preferences
- User journey and progression analysis

#### **System Performance Reports**
- API response times and error rates
- Database query performance
- Cache hit rates and optimization
- System resource utilization

#### **Business Intelligence**
- Cohort analysis of gamified users
- A/B testing results and insights
- Feature adoption and usage patterns
- Predictive analytics for user behavior

### Data Export

- **CSV Exports**: Raw data for external analysis
- **API Access**: Programmatic data access for integrations
- **Scheduled Reports**: Automated report generation and delivery
- **Real-time Dashboards**: Live metrics and monitoring

## Content Moderation

### Review Management

#### **User-Generated Content**
- **Review Moderation**: Approve/reject product reviews
- **Achievement Verification**: Verify user-submitted achievements
- **Social Content**: Monitor shared content and posts
- **Community Guidelines**: Enforce community standards

#### **Automated Moderation**
- **Spam Detection**: Automatic spam and abuse detection
- **Content Filtering**: Filter inappropriate content automatically
- **Pattern Recognition**: Identify suspicious behavior patterns
- **Alert System**: Notifications for content requiring review

### Fraud Prevention

#### **Point Fraud Detection**
- **Unusual Activity**: Monitor for abnormal point earning patterns
- **Multiple Accounts**: Detect and prevent multi-account abuse
- **Bot Detection**: Identify automated or scripted behavior
- **Referral Fraud**: Monitor referral program for abuse

#### **Achievement Fraud**
- **Progress Validation**: Verify achievement requirements are met
- **Time-based Checks**: Ensure realistic completion timeframes
- **Cross-reference Data**: Validate against order and activity data
- **Manual Review**: Flag suspicious achievement unlocks

## System Maintenance

### Database Management

#### **Data Cleanup**
- **Old Transactions**: Archive old point transactions
- **Inactive Users**: Clean up data for inactive accounts
- **Expired Rewards**: Remove expired reward redemptions
- **Log Rotation**: Manage system and audit logs

#### **Performance Optimization**
- **Index Optimization**: Maintain database indexes for performance
- **Query Analysis**: Monitor and optimize slow queries
- **Cache Management**: Optimize caching strategies
- **Resource Monitoring**: Track database resource usage

### Backup and Recovery

#### **Automated Backups**
- **Daily Backups**: Automatic daily database backups
- **Point-in-time Recovery**: Restore to specific timestamps
- **Cross-region Replication**: Backup data across regions
- **Backup Verification**: Regular backup integrity checks

#### **Disaster Recovery**
- **Recovery Procedures**: Step-by-step recovery processes
- **Failover Systems**: Automatic failover to backup systems
- **Data Validation**: Verify data integrity after recovery
- **Communication Plans**: User communication during outages

### System Updates

#### **Feature Deployment**
- **Staging Environment**: Test new features before production
- **Feature Flags**: Gradual rollout of new functionality
- **Rollback Procedures**: Quick rollback for problematic updates
- **User Communication**: Notify users of new features

#### **Security Updates**
- **Regular Patches**: Apply security patches promptly
- **Vulnerability Scanning**: Regular security assessments
- **Access Reviews**: Periodic review of admin access
- **Audit Logging**: Comprehensive audit trail maintenance

## Troubleshooting

### Common Issues

#### **Points Not Crediting**
1. **Check Order Status**: Verify order is confirmed and processed
2. **Review Point Rules**: Ensure activity meets point criteria
3. **Check Rate Limits**: Verify user hasn't exceeded limits
4. **Manual Credit**: Award points manually if system error

#### **Achievement Not Unlocking**
1. **Verify Requirements**: Check all achievement criteria
2. **Review Progress**: Examine user's progress data
3. **Check Dependencies**: Ensure prerequisite achievements are met
4. **Manual Unlock**: Grant achievement manually if appropriate

#### **Reward Redemption Failures**
1. **Point Balance**: Verify sufficient point balance
2. **Stock Availability**: Check reward inventory levels
3. **Account Status**: Ensure account is in good standing
4. **System Errors**: Check for technical issues

### Diagnostic Tools

#### **User Activity Logs**
- Complete history of user gamification activities
- Point transactions with detailed metadata
- Achievement progress and unlock events
- Reward redemption attempts and status

#### **System Health Monitoring**
- Real-time system performance metrics
- Error rates and response times
- Database connection and query performance
- Cache hit rates and memory usage

#### **Debug Mode**
- Detailed logging for troubleshooting
- Step-by-step process tracking
- Error stack traces and context
- Performance profiling tools

## API Reference

### Authentication

All admin API endpoints require authentication:

```javascript
// Headers required for admin API calls
{
  'Authorization': 'Bearer <admin_jwt_token>',
  'Content-Type': 'application/json',
  'X-Admin-Role': '<admin_role>'
}
```

### Core Endpoints

#### **User Management**
```javascript
// Get user gamification data
GET /api/admin/users/{userId}/gamification

// Award points to user
POST /api/admin/users/{userId}/points
{
  "amount": 100,
  "source": "admin_adjustment",
  "description": "Customer service compensation",
  "adminId": "admin_user_id"
}

// Unlock achievement for user
POST /api/admin/users/{userId}/achievements
{
  "achievementId": "achievement_id",
  "adminOverride": true,
  "reason": "Manual unlock for customer service"
}
```

#### **Content Management**
```javascript
// Create achievement
POST /api/admin/achievements
{
  "name": "Achievement Name",
  "description": "Achievement description",
  "category": "purchase",
  "rarity": "rare",
  "pointsAwarded": 250,
  "requirements": [...]
}

// Create reward
POST /api/admin/rewards
{
  "name": "Reward Name",
  "description": "Reward description",
  "pointsCost": 500,
  "category": "discount",
  "stock": 100,
  "metadata": {...}
}
```

#### **Analytics**
```javascript
// Get system analytics
GET /api/admin/analytics/overview
GET /api/admin/analytics/users
GET /api/admin/analytics/achievements
GET /api/admin/analytics/rewards

// Export data
GET /api/admin/export/users
GET /api/admin/export/transactions
GET /api/admin/export/achievements
```

### Rate Limiting

Admin API endpoints have higher rate limits:
- **Standard Endpoints**: 1000 requests per hour
- **Bulk Operations**: 100 requests per hour
- **Export Endpoints**: 10 requests per hour

### Error Handling

```javascript
// Standard error response format
{
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "Admin role required for this operation",
    "details": {
      "requiredRole": "content_admin",
      "userRole": "support_admin"
    }
  }
}
```

---

**For additional support or questions, contact the development <NAME_EMAIL>**

*Last updated: December 2024*
