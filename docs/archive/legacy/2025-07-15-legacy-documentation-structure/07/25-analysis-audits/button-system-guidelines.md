# Button System Guidelines - Syndicaps Design System

**Document Version**: 1.0  
**Date**: January 2025  
**Author**: Syndicaps Development Team  

## 📋 Overview

This document defines the unified button system for the Syndicaps website, providing consistent interactive elements with comprehensive variants, states, and accessibility features.

## 🎯 Design Principles

### **1. Visual Consistency**
- Unified styling across all button variants
- Consistent spacing, typography, and interaction states
- Gaming-inspired gradients and effects

### **2. Accessibility First**
- Minimum 44px touch targets
- Proper focus indicators and ARIA support
- Screen reader compatibility

### **3. Performance Optimized**
- Smooth animations with hardware acceleration
- Reduced motion support
- Optimized bundle size

---

## 🔧 Button Component API

### **Basic Usage**

```typescript
import { Button } from '@/components/ui/button';

// Basic button
<Button variant="default" size="md">
  Click me
</Button>

// Button with icon
<Button variant="default" icon={ArrowRight} iconPosition="right">
  Continue
</Button>

// Loading button
<Button variant="default" loading={true}>
  Saving...
</Button>

// As Link component
<Button asChild variant="outline">
  <Link href="/shop">Shop Now</Link>
</Button>
```

### **Props Interface**

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Visual variant */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning';
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon' | 'xl';
  /** Render as child component */
  asChild?: boolean;
  /** Loading state */
  loading?: boolean;
  /** Icon component */
  icon?: LucideIcon;
  /** Icon position */
  iconPosition?: 'left' | 'right';
  /** Full width button */
  fullWidth?: boolean;
  /** Enable motion animations */
  animated?: boolean;
}
```

---

## 🎨 Button Variants

### **1. Default (Primary)**
**Use Case**: Primary actions, main CTAs  
**Style**: Accent gradient with shadow  
**Example**: "Shop Collection", "Add to Cart"

```typescript
<Button variant="default" size="lg">
  Shop Collection
</Button>
```

### **2. Secondary**
**Use Case**: Secondary actions, alternative options  
**Style**: Gray gradient with subtle shadow  
**Example**: "Cancel", "Back"

```typescript
<Button variant="secondary" size="md">
  Cancel
</Button>
```

### **3. Outline**
**Use Case**: Tertiary actions, less prominent options  
**Style**: Transparent with border  
**Example**: "Learn More", "View Details"

```typescript
<Button variant="outline" size="md">
  Learn More
</Button>
```

### **4. Destructive**
**Use Case**: Dangerous actions, deletions  
**Style**: Red gradient with shadow  
**Example**: "Delete", "Remove"

```typescript
<Button variant="destructive" size="md">
  Delete Item
</Button>
```

### **5. Success**
**Use Case**: Positive actions, confirmations  
**Style**: Green gradient with shadow  
**Example**: "Confirm", "Save"

```typescript
<Button variant="success" size="md">
  Confirm Order
</Button>
```

### **6. Warning**
**Use Case**: Caution actions, warnings  
**Style**: Yellow gradient with shadow  
**Example**: "Proceed with Caution"

```typescript
<Button variant="warning" size="md">
  Proceed Anyway
</Button>
```

### **7. Ghost**
**Use Case**: Minimal actions, subtle interactions  
**Style**: Transparent with hover background  
**Example**: Menu items, subtle actions

```typescript
<Button variant="ghost" size="md">
  Menu Item
</Button>
```

### **8. Link**
**Use Case**: Text links, navigation  
**Style**: Underlined text with accent color  
**Example**: "Read more", "View all"

```typescript
<Button variant="link" size="md">
  Read more
</Button>
```

---

## 📏 Button Sizes

### **Size Guidelines**

| Size | Height | Padding | Use Case | Touch Target |
|------|--------|---------|----------|--------------|
| **sm** | 40px | 12px | Compact spaces, inline actions | ✅ 40px |
| **default** | 44px | 16px | Standard buttons, forms | ✅ 44px |
| **lg** | 48px | 32px | Hero CTAs, important actions | ✅ 48px |
| **xl** | 56px | 40px | Landing page CTAs | ✅ 56px |
| **icon** | 44x44px | - | Icon-only buttons | ✅ 44px |

### **Size Examples**

```typescript
// Small button for compact spaces
<Button variant="default" size="sm">Small</Button>

// Default size for most use cases
<Button variant="default" size="default">Default</Button>

// Large for important actions
<Button variant="default" size="lg">Large CTA</Button>

// Extra large for hero sections
<Button variant="default" size="xl">Hero CTA</Button>

// Icon-only button
<Button variant="default" size="icon" icon={Search} />
```

---

## 🎭 Button States

### **Loading State**

```typescript
// Automatic loading spinner
<Button variant="default" loading={isLoading}>
  {isLoading ? 'Saving...' : 'Save Changes'}
</Button>

// Custom loading text
<Button variant="default" loading={true}>
  Processing...
</Button>
```

### **Disabled State**

```typescript
// Disabled button
<Button variant="default" disabled={true}>
  Disabled Button
</Button>

// Conditionally disabled
<Button variant="default" disabled={!isValid}>
  Submit Form
</Button>
```

### **Icon Integration**

```typescript
// Icon on the left
<Button variant="default" icon={ArrowRight} iconPosition="left">
  Continue
</Button>

// Icon on the right
<Button variant="default" icon={ExternalLink} iconPosition="right">
  Open Link
</Button>

// Icon-only button
<Button variant="default" size="icon" icon={Search} />
```

---

## 📱 Implementation Examples

### **Homepage Hero Section**

```typescript
// Before (Custom styling)
<Link
  href="/shop"
  className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center"
>
  Shop Collection
  <ArrowRight className="ml-2 w-5 h-5" />
</Link>

// After (Unified Button)
<Button
  asChild
  variant="default"
  size="lg"
  icon={ArrowRight}
  iconPosition="right"
  animated={true}
>
  <Link href="/shop">Shop Collection</Link>
</Button>
```

### **Contact Form**

```typescript
// Before (Custom button)
<button
  type="submit"
  disabled={isSubmitting}
  className={`inline-flex items-center px-8 py-3 rounded-lg font-medium transition-all duration-200 ${
    isSubmitting ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-accent-500 hover:bg-accent-600 text-white'
  }`}
>
  {isSubmitting ? 'Sending...' : 'Send Message'}
</button>

// After (Unified Button)
<Button
  type="submit"
  variant="default"
  size="lg"
  loading={isSubmitting}
  icon={Send}
  iconPosition="left"
>
  {isSubmitting ? 'Sending...' : 'Send Message'}
</Button>
```

### **Shop Page Actions**

```typescript
// Add to cart button
<Button
  variant="default"
  size="default"
  loading={isAdding}
  icon={ShoppingCart}
  iconPosition="left"
  fullWidth={true}
  onClick={handleAddToCart}
>
  Add to Cart
</Button>

// Wishlist button
<Button
  variant="outline"
  size="icon"
  icon={Heart}
  onClick={handleWishlist}
  aria-label="Add to wishlist"
/>

// Quick view button
<Button
  variant="ghost"
  size="sm"
  icon={Eye}
  iconPosition="left"
  onClick={handleQuickView}
>
  Quick View
</Button>
```

---

## ♿ Accessibility Features

### **Touch Targets**

All button sizes meet minimum touch target requirements:
- **Minimum**: 40px (sm size)
- **Recommended**: 44px (default size)
- **Large**: 48px+ (lg, xl sizes)

### **Focus Management**

```typescript
// Automatic focus indicators
<Button variant="default">
  Focused Button
</Button>
// Renders with focus-visible:ring-2 focus-visible:ring-accent-500
```

### **Screen Reader Support**

```typescript
// Icon-only buttons need aria-label
<Button variant="default" size="icon" icon={Search} aria-label="Search products" />

// Loading state announcements
<Button variant="default" loading={true}>
  Saving... {/* Automatically announced to screen readers */}
</Button>
```

### **Keyboard Navigation**

- All buttons are keyboard accessible
- Enter and Space key activation
- Proper tab order
- Focus trapping in modals

---

## 🎬 Animation and Motion

### **Motion Settings**

```typescript
// Enable animations (default)
<Button variant="default" animated={true}>
  Animated Button
</Button>

// Disable animations
<Button variant="default" animated={false}>
  Static Button
</Button>
```

### **Reduced Motion Support**

Automatically respects `prefers-reduced-motion` setting:

```css
@media (prefers-reduced-motion: reduce) {
  .motion-button {
    transition: none;
    animation: none;
  }
}
```

---

## 🚀 Performance Considerations

### **Bundle Optimization**

- Tree-shaking for unused variants
- Lazy loading for complex animations
- Optimized icon imports

### **Animation Performance**

- Hardware-accelerated transforms
- Optimized for 60fps
- Minimal layout thrashing

---

## 📊 Migration Checklist

### **Phase 1: Core Components** ✅
- [x] Homepage: Hero CTAs updated
- [x] Contact form: Submit button updated
- [x] Newsletter: Subscribe button updated

### **Phase 2: Additional Components**
- [ ] Shop page: Product action buttons
- [ ] Community page: Interaction buttons
- [ ] Profile page: Settings buttons
- [ ] Admin panel: Management buttons

### **Phase 3: Specialized Cases**
- [ ] Modal action buttons
- [ ] Dropdown menu items
- [ ] Pagination controls
- [ ] Filter toggle buttons

---

## 🔍 Testing Guidelines

### **Visual Testing**

- Test all variants and sizes
- Verify hover and focus states
- Check loading animations
- Validate touch targets

### **Accessibility Testing**

- Screen reader compatibility
- Keyboard navigation
- Color contrast verification
- Touch target measurements

### **Performance Testing**

- Animation frame rates
- Bundle size impact
- Loading state responsiveness

---

**Document Status**: ✅ Complete  
**Implementation Status**: Phase 1 Complete  
**Next Review**: Phase 2 completion  
**Related Files**: `src/components/ui/button.tsx`
