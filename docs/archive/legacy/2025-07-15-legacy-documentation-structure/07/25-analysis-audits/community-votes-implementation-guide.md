# Community Votes Implementation Guide

## 🚀 Quick Start

The enhanced community votes feature is now available in the admin dashboard at:
```
/admin/gamification/community-votes
```

## 📁 File Structure

```
app/admin/gamification/community-votes/
├── page.tsx                    # Main admin interface
└── components/                 # Future component modules

src/lib/api/
├── gamification.ts            # Enhanced with discussion & settings APIs

tests/admin/
├── community-votes.test.tsx   # Test suite for new features

docs/
├── community-votes-enhancement-summary.md
└── community-votes-implementation-guide.md
```

## 🔧 API Usage

### Discussion Management

```typescript
import {
  getCampaignDiscussions,
  createCampaignDiscussion,
  toggleCampaignDiscussionLike,
  moderateCampaignDiscussion
} from '@/lib/api/gamification'

// Get all discussions for a campaign
const discussions = await getCampaignDiscussions('campaign-id')

// Create a new discussion
const discussionId = await createCampaignDiscussion(
  'campaign-id',
  'user-id',
  'Discussion message',
  'parent-id' // optional for replies
)

// Like/unlike a discussion
const isLiked = await toggleCampaignDiscussionLike('discussion-id', 'user-id')

// Moderate a discussion
await moderateCampaignDiscussion(
  'discussion-id',
  'moderator-id',
  'hide', // 'hide' | 'delete' | 'approve'
  'Reason for moderation'
)
```

### Settings Management

```typescript
import {
  getVoteSettings,
  updateVoteSettings,
  getDefaultVoteSettings
} from '@/lib/api/gamification'

// Get current settings
const settings = await getVoteSettings()

// Update settings
await updateVoteSettings({
  duration: {
    defaultDuration: 30,
    minDuration: 7,
    maxDuration: 90
  },
  eligibility: {
    minPoints: 100,
    allowedTiers: ['bronze', 'silver', 'gold', 'platinum', 'diamond']
  }
})

// Get default settings
const defaultSettings = getDefaultVoteSettings()
```

## 🎨 Component Usage

### Using Admin Components

```typescript
import { AdminCard, AdminButton, AdminModal } from '@/admin/components/common'

// Admin Card with consistent styling
<AdminCard title="Discussion Stats" subtitle="Community engagement metrics">
  <div className="space-y-4">
    {/* Content */}
  </div>
</AdminCard>

// Admin Button with loading states
<AdminButton
  variant="primary"
  icon={Settings}
  onClick={handleSaveSettings}
  loading={loading}
>
  Save Settings
</AdminButton>

// Admin Modal for confirmations
<AdminModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  title="Confirm Action"
  size="md"
>
  {/* Modal content */}
</AdminModal>
```

### TypeScript Interfaces

```typescript
import type { CommunityDiscussion, VoteSettings } from '@/lib/api/gamification'

// Discussion component props
interface DiscussionProps {
  discussion: CommunityDiscussion
  onLike: (discussionId: string) => Promise<void>
  onModerate: (discussionId: string, action: 'hide' | 'delete' | 'approve') => Promise<void>
}

// Settings component props
interface SettingsProps {
  settings: VoteSettings
  onSave: (settings: Partial<VoteSettings>) => Promise<void>
  loading?: boolean
}
```

## 🎯 Design Patterns

### Syndicaps Color Scheme

```css
/* Primary Colors */
--accent-600: #7c3aed;     /* Purple primary */
--accent-500: #8b5cf6;     /* Purple secondary */
--accent-400: #a78bfa;     /* Purple light */

/* Status Colors */
--green-400: #4ade80;      /* Success */
--red-400: #f87171;        /* Error */
--yellow-400: #facc15;     /* Warning */
--blue-400: #60a5fa;       /* Info */

/* Tier Colors */
--cyan-400: #22d3ee;       /* Diamond */
--gray-300: #d1d5db;       /* Platinum */
--yellow-400: #facc15;     /* Gold */
--gray-400: #9ca3af;       /* Silver */
--orange-400: #fb923c;     /* Bronze */
```

### Responsive Breakpoints

```css
/* Mobile First */
.grid-cols-1 { /* Mobile */ }
.sm:grid-cols-2 { /* 640px+ */ }
.md:grid-cols-3 { /* 768px+ */ }
.lg:grid-cols-4 { /* 1024px+ */ }
.xl:grid-cols-6 { /* 1280px+ */ }
```

### Touch Targets

```css
/* Minimum 44px touch targets */
.min-w-[44px] .min-h-[44px]
```

## 🔒 Security Considerations

### Input Validation

```typescript
// Validate discussion content
const validateDiscussion = (message: string): boolean => {
  return message.length >= 10 && message.length <= 1000
}

// Sanitize user input
const sanitizeMessage = (message: string): string => {
  return message.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
}
```

### Permission Checks

```typescript
// Check moderation permissions
const canModerate = (userId: string, userRole: string): boolean => {
  return userRole === 'admin' || userRole === 'moderator'
}

// Check voting eligibility
const canVote = (user: User, settings: VoteSettings): boolean => {
  return user.points >= settings.eligibility.minPoints &&
         settings.eligibility.allowedTiers.includes(user.tier) &&
         user.emailVerified === settings.eligibility.requireEmailVerification
}
```

## 📊 Analytics Integration

### Tracking Events

```typescript
// Track discussion interactions
const trackDiscussionEvent = (event: string, data: any) => {
  if (settings.integrations.analyticsTracking) {
    analytics.track(event, {
      ...data,
      timestamp: new Date(),
      source: 'community_votes'
    })
  }
}

// Usage examples
trackDiscussionEvent('discussion_created', { campaignId, userId })
trackDiscussionEvent('discussion_liked', { discussionId, userId })
trackDiscussionEvent('discussion_moderated', { discussionId, action, moderatorId })
```

## 🧪 Testing

### Unit Tests

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { CommunityDiscussion } from '@/lib/api/gamification'

describe('Discussion Component', () => {
  const mockDiscussion: CommunityDiscussion = {
    id: 'test-1',
    campaignId: 'campaign-1',
    userId: 'user-1',
    userName: 'TestUser',
    userTier: 'gold',
    message: 'Test discussion message',
    likes: 5,
    replies: [],
    // ... other required fields
  }

  test('renders discussion with user info', () => {
    render(<DiscussionItem discussion={mockDiscussion} />)
    
    expect(screen.getByText('TestUser')).toBeInTheDocument()
    expect(screen.getByText('Test discussion message')).toBeInTheDocument()
    expect(screen.getByText('5 likes')).toBeInTheDocument()
  })
})
```

### Integration Tests

```typescript
describe('Discussion API Integration', () => {
  test('creates and retrieves discussions', async () => {
    const discussionId = await createCampaignDiscussion(
      'test-campaign',
      'test-user',
      'Test message'
    )
    
    const discussions = await getCampaignDiscussions('test-campaign')
    
    expect(discussions).toHaveLength(1)
    expect(discussions[0].id).toBe(discussionId)
    expect(discussions[0].message).toBe('Test message')
  })
})
```

## 🚀 Deployment

### Environment Variables

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id

# Optional: Discord Integration
DISCORD_WEBHOOK_URL=your_webhook_url

# Optional: Analytics
ANALYTICS_TRACKING_ID=your_tracking_id
```

### Database Setup

```javascript
// Initialize Firestore collections
const initializeCollections = async () => {
  // Create indexes for performance
  await db.collection('community_vote_discussions').createIndex({
    campaignId: 1,
    createdAt: -1
  })
  
  await db.collection('vote_settings').createIndex({
    updatedAt: -1
  })
}
```

## 📞 Support

For questions or issues with the community votes enhancement:

1. **Documentation**: Check this guide and the summary document
2. **Code Examples**: Review the demo page at `/test/community-votes-demo`
3. **API Reference**: See `src/lib/api/gamification.ts` for complete API
4. **Testing**: Run the test suite with `npm test tests/admin/community-votes.test.tsx`

## 🎉 Success!

The community votes enhancement is now fully implemented and ready for production use. The feature provides a solid foundation for community engagement and can be extended with additional functionality as needed.
