# Syndicaps Color Palette Documentation

## Overview
This document provides a comprehensive overview of all color values used throughout the Syndicaps website. The design system follows a dark theme with gaming/tech-inspired elements, neon accents, and consistent branding.

## 1. Primary Brand Colors

### Primary Blue Scale
- **primary-50**: `#f0f9ff` - Lightest blue tint
- **primary-100**: `#e0f2fe` - Very light blue
- **primary-200**: `#bae6fd` - Light blue
- **primary-300**: `#7dd3fc` - Medium light blue
- **primary-400**: `#38bdf8` - Medium blue
- **primary-500**: `#0ea5e9` - Main primary blue
- **primary-600**: `#0284c7` - Darker blue
- **primary-700**: `#0369a1` - Dark blue
- **primary-800**: `#075985` - Very dark blue
- **primary-900**: `#0c4a6e` - Darkest blue
- **primary-950**: `#082f49` - Ultra dark blue

**Usage**: Main branding, primary buttons, key UI elements
**CSS Variables**: `hsl(var(--primary))`, `hsl(var(--primary-foreground))`

## 2. Accent Colors

### Accent Orange/Amber Scale
- **accent-50**: `#fef7ee` - Lightest orange tint
- **accent-100**: `#fdedd3` - Very light orange
- **accent-200**: `#fbd7a5` - Light orange
- **accent-300**: `#f8bb6d` - Medium light orange
- **accent-400**: `#f59e0b` - Medium orange
- **accent-500**: `#d97706` - Main accent orange
- **accent-600**: `#c2410c` - Darker orange
- **accent-700**: `#9a3412` - Dark orange
- **accent-800**: `#7c2d12` - Very dark orange
- **accent-900**: `#431407` - Darkest orange
- **accent-950**: `#1c0a00` - Ultra dark orange

**Usage**: Highlights, interactive elements, call-to-action buttons
**CSS Variables**: `hsl(var(--accent))`, `hsl(var(--accent-foreground))`

## 3. Gaming/Tech Theme Colors

### Neon Colors
- **neon-cyan**: `#00ffff` - Bright cyan for tech effects
- **neon-purple**: `#8b5cf6` - Purple accent for gaming theme
- **neon-pink**: `#ff00ff` - Magenta for highlights
- **neon-green**: `#00ff00` - Bright green for success states
- **neon-orange**: `#ff6600` - Orange for warnings/energy
- **neon-blue**: `#0066ff` - Blue for information

**Usage**: Neon text effects, gaming-themed highlights, tech-inspired accents

### Gaming Background Colors
- **gaming-dark**: `#0f172a` - Primary dark background
- **gaming-darker**: `#020617` - Ultra dark background
- **gaming-accent**: `#8b5cf6` - Purple accent
- **gaming-secondary**: `#334155` - Secondary gray
- **gaming-border**: `rgba(139, 92, 246, 0.3)` - Semi-transparent purple border

**Usage**: Card backgrounds, gaming-themed components, tech interfaces

## 4. Background Colors

### Main Backgrounds
- **Background RGB**: `rgb(17, 24, 39)` - Main page background (gray-900)
- **Foreground RGB**: `rgb(255, 255, 255)` - Main text color

### Semantic Background Colors (HSL)
- **Light Theme**:
  - `--background`: `0 0% 100%` - White background
  - `--card`: `0 0% 100%` - White card background
  - `--popover`: `0 0% 100%` - White popover background

- **Dark Theme**:
  - `--background`: `0 0% 3.9%` - Very dark gray
  - `--card`: `0 0% 3.9%` - Very dark gray for cards
  - `--popover`: `0 0% 3.9%` - Very dark gray for popovers

**Usage**: Page backgrounds, card containers, modal backgrounds

## 5. Text Colors

### Primary Text Colors
- **Light Theme**:
  - `--foreground`: `0 0% 3.9%` - Very dark gray text
  - `--card-foreground`: `0 0% 3.9%` - Card text
  - `--muted-foreground`: `0 0% 45.1%` - Muted text

- **Dark Theme**:
  - `--foreground`: `0 0% 98%` - Near white text
  - `--card-foreground`: `0 0% 98%` - Card text
  - `--muted-foreground`: `0 0% 63.9%` - Muted text

### Component-Specific Text Colors
- **text-white**: Primary text on dark backgrounds
- **text-gray-300**: Secondary text (light gray)
- **text-gray-400**: Muted text and descriptions
- **text-gray-500**: Disabled or inactive text
- **text-accent-400**: Accent text color
- **text-accent-500**: Stronger accent text

**Usage**: Headings, body text, descriptions, labels

## 6. Status/State Colors

### Success Colors
- **bg-green-800**: `#166534` - Success background
- **border-green-600**: `#16a34a` - Success border
- **text-green-100**: `#dcfce7` - Success text
- **text-green-400**: `#4ade80` - Success icon color

### Error Colors
- **bg-red-800**: `#991b1b` - Error background
- **border-red-600**: `#dc2626` - Error border
- **text-red-100**: `#fecaca` - Error text
- **text-red-400**: `#f87171` - Error icon color

### Warning Colors
- **bg-yellow-800**: `#854d0e` - Warning background
- **border-yellow-600**: `#ca8a04` - Warning border
- **text-yellow-100**: `#fefce8` - Warning text
- **text-yellow-400**: `#facc15` - Warning icon color

### Info Colors
- **bg-blue-800**: `#1e40af` - Info background
- **border-blue-600**: `#2563eb` - Info border
- **text-blue-100**: `#dbeafe` - Info text
- **text-blue-400**: `#60a5fa` - Info icon color

**Usage**: Toast notifications, alerts, status indicators

## 7. Interactive Element Colors

### Button Variants
- **Default**: `bg-primary text-primary-foreground hover:bg-primary/90`
- **Destructive**: `bg-destructive text-destructive-foreground hover:bg-destructive/90`
- **Secondary**: `bg-secondary text-secondary-foreground hover:bg-secondary/80`
- **Ghost**: `hover:bg-accent hover:text-accent-foreground`
- **Outline**: `border border-input bg-background hover:bg-accent`

### Hover States
- **bg-gray-700**: Hover state for gray-800 backgrounds
- **bg-gray-800**: Base state for interactive cards
- **hover:text-accent-400**: Text color change on hover
- **hover:text-neon-cyan**: Gaming-themed hover effects

**Usage**: Buttons, cards, interactive elements

## 8. Rarity System Colors

### Gamification Rarity Colors
- **Common**: 
  - Background: `from-gray-800 to-gray-900`
  - Border: `border-gray-500`
  - Text: `text-gray-300`

- **Rare**:
  - Background: `from-blue-800 to-blue-900`
  - Border: `border-blue-500`
  - Text: `text-blue-300`

- **Epic**:
  - Background: `from-purple-800 to-purple-900`
  - Border: `border-purple-500`
  - Text: `text-purple-300`

- **Legendary**:
  - Background: `from-yellow-700 to-orange-800`
  - Border: `border-yellow-500`
  - Text: `text-yellow-300`

**Usage**: Reward items, achievements, gamification elements

## 9. Chart Colors

### Data Visualization Colors
- **chart-1**: `12 76% 61%` (Orange-red)
- **chart-2**: `173 58% 39%` (Teal)
- **chart-3**: `197 37% 24%` (Dark blue)
- **chart-4**: `43 74% 66%` (Yellow)
- **chart-5**: `27 87% 67%` (Orange)

**Usage**: Analytics charts, data visualization components

## 10. Utility Colors

### Border Colors
- **border**: `hsl(var(--border))` - Standard border color
- **input**: `hsl(var(--input))` - Input field borders
- **ring**: `hsl(var(--ring))` - Focus ring color

### Special Effects
- **Glass Effect**: `rgba(17, 24, 39, 0.8)` with backdrop blur
- **Gradient Overlays**: Purple to blue gradients for visual effects
- **Shadow Colors**: Various opacity levels of purple and cyan for glows

**Usage**: Borders, focus states, visual effects

## 11. PWA/Manifest Colors

### App Theme Colors
- **background_color**: `#1f2937` - App background (gray-800)
- **theme_color**: `#6366f1` - Browser theme color (indigo-500)

**Usage**: Progressive Web App theming, browser UI

## Implementation Notes

1. **CSS Variables**: Most colors use CSS custom properties for theme switching
2. **Tailwind Classes**: Standard Tailwind color classes are used throughout
3. **Gaming Theme**: Special neon and gaming colors for tech-inspired elements
4. **Accessibility**: High contrast mode support with adjusted colors
5. **Responsive**: Color intensity may be reduced on mobile for better readability

## 12. Gaming Theme CSS Classes

### Button Classes (Legacy - Being Phased Out)
- **btn-gaming**: Gaming-themed button with tech gradient
- **btn-neon**: Neon-styled button with glow effects
- **btn-cyber**: Cyberpunk-inspired button styling

### Card Classes (Legacy - Being Phased Out)
- **card-gaming**: Gaming-themed card with tech borders
- **card-neon**: Neon-accented card with glow effects

### Text Effects (Legacy - Being Phased Out)
- **text-neon**: Cyan neon text with glow effect
- **text-neon-purple**: Purple neon text with glow effect
- **text-cyber**: Gradient text effect with cyberpunk styling

**Note**: These gaming-themed classes are being replaced with standard Tailwind classes for better maintainability.

## 13. Gradient Definitions

### Gaming Gradients (CSS Variables)
- **--gradient-cyber**: `linear-gradient(135deg, #8b5cf6 0%, #00ffff 50%, #8b5cf6 100%)`
- **--gradient-neon**: `linear-gradient(45deg, #ff00ff 0%, #00ffff 50%, #ff00ff 100%)`
- **--gradient-tech**: `linear-gradient(135deg, #1e293b 0%, #334155 50%, #1e293b 100%)`
- **--gradient-glow**: `linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(0, 255, 255, 0.2) 100%)`

### Common Tailwind Gradients
- **from-gray-800 to-gray-900**: Standard dark card gradient
- **from-purple-800 to-purple-900**: Epic rarity gradient
- **from-blue-800 to-blue-900**: Rare rarity gradient
- **from-yellow-700 to-orange-800**: Legendary rarity gradient

**Usage**: Background gradients, button effects, card styling

## 14. Shadow and Glow Effects

### Gaming Shadows (CSS Variables)
- **--shadow-neon**: `0 0 20px rgba(139, 92, 246, 0.5)`
- **--shadow-cyber**: `0 0 30px rgba(0, 255, 255, 0.3)`
- **--shadow-tech**: `0 4px 20px rgba(0, 0, 0, 0.5)`
- **--shadow-glow**: `0 0 40px rgba(139, 92, 246, 0.2)`

### Rarity Glow Effects
- **shadow-gray-500/20**: Common item glow
- **shadow-blue-500/30**: Rare item glow
- **shadow-purple-500/40**: Epic item glow
- **shadow-yellow-500/50**: Legendary item glow

**Usage**: Card hover effects, button glows, achievement highlights

## 15. Border Definitions

### Gaming Borders (CSS Variables)
- **--border-tech**: `1px solid rgba(139, 92, 246, 0.3)`
- **--border-neon**: `1px solid rgba(0, 255, 255, 0.5)`
- **--border-glow**: `1px solid rgba(255, 255, 255, 0.1)`

### Rarity Borders
- **border-gray-500/30**: Common item border
- **border-blue-500/30**: Rare item border
- **border-purple-500/30**: Epic item border
- **border-yellow-500/30**: Legendary item border

**Usage**: Card borders, input fields, gaming-themed elements

## 16. Color Usage Examples

### Navigation Components
```css
/* Smart Navigation */
.nav-item {
  @apply bg-gray-800 hover:bg-gray-700;
  @apply text-white hover:text-accent-400;
  @apply border-transparent hover:border-accent-500;
}
```

### Gamification Components
```css
/* Achievement Cards */
.achievement-common {
  @apply bg-gradient-to-br from-gray-800 to-gray-900;
  @apply border-gray-500 text-gray-300;
}

.achievement-legendary {
  @apply bg-gradient-to-br from-yellow-700 to-orange-800;
  @apply border-yellow-500 text-yellow-300;
}
```

### Toast Notifications
```css
.toast-success {
  @apply bg-green-800 border-green-600 text-green-100;
}

.toast-error {
  @apply bg-red-800 border-red-600 text-red-100;
}
```

## 17. Accessibility Considerations

### High Contrast Mode
- Neon colors change to white in high contrast mode
- Border weights increase for better visibility
- Gaming effects are disabled for accessibility

### Color Contrast Ratios
- All text/background combinations meet WCAG AA standards (4.5:1 minimum)
- Interactive elements have sufficient contrast for focus states
- Status colors maintain readability across all themes

### Reduced Motion Support
- Gaming animations and glow effects are disabled
- Transitions are removed for users who prefer reduced motion
- Static color states are maintained for accessibility

## Maintenance Guidelines

- Use semantic color variables when possible (`--primary`, `--accent`)
- Maintain consistent color relationships across light/dark themes
- Test color combinations for WCAG AA compliance
- Document any new color additions in this file
- Use the established rarity color system for gamification elements
- Phase out gaming-themed CSS classes in favor of standard Tailwind utilities
- Ensure all new colors work in both light and dark themes
- Test with high contrast and reduced motion preferences
