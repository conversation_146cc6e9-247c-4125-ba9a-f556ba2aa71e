# Syndicaps API Documentation

Comprehensive API reference for the Syndicaps platform covering all endpoints, authentication, and integration patterns.

## 🔐 Authentication

### Firebase Authentication
All API endpoints require Firebase Authentication tokens except where noted.

```javascript
// Client-side authentication
import { auth } from '@/lib/firebase'
import { signInWithEmailAndPassword } from 'firebase/auth'

const { user } = await signInWithEmailAndPassword(auth, email, password)
const token = await user.getIdToken()

// Include token in requests
const response = await fetch('/api/endpoint', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

### Admin Authentication
Admin endpoints require elevated permissions and admin role verification.

```javascript
// Admin token verification
const adminToken = await admin.auth().createCustomToken(uid, { admin: true })
```

## 🎮 Gamification API

### Points System

#### Get User Points
```http
GET /api/gamification/points
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "currentPoints": 1250,
    "totalEarned": 2500,
    "totalSpent": 1250,
    "level": 5,
    "nextLevelPoints": 1500,
    "pointsToNextLevel": 250
  }
}
```

#### Award Points
```http
POST /api/gamification/points/award
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 100,
  "source": "achievement",
  "description": "First Purchase Achievement",
  "metadata": {
    "achievementId": "first-purchase"
  }
}
```

#### Spend Points
```http
POST /api/gamification/points/spend
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 500,
  "source": "reward",
  "description": "Discount Code Redemption",
  "rewardId": "discount-10-percent"
}
```

#### Get Points History
```http
GET /api/gamification/points/history?limit=20&offset=0&type=earned
Authorization: Bearer <token>
```

### Achievements System

#### Get User Achievements
```http
GET /api/gamification/achievements
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "unlocked": [
      {
        "id": "first-purchase",
        "name": "First Purchase",
        "description": "Make your first purchase",
        "icon": "shopping-cart",
        "points": 100,
        "category": "shopping",
        "unlockedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "available": [
      {
        "id": "social-butterfly",
        "name": "Social Butterfly",
        "description": "Connect all social media accounts",
        "icon": "users",
        "points": 200,
        "category": "social",
        "progress": {
          "current": 2,
          "required": 3
        }
      }
    ],
    "stats": {
      "totalUnlocked": 5,
      "totalAvailable": 45,
      "pointsFromAchievements": 750
    }
  }
}
```

#### Check Achievement Progress
```http
POST /api/gamification/achievements/check
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "purchase",
  "metadata": {
    "orderId": "order-123",
    "amount": 149.99
  }
}
```

### Leaderboard API

#### Get Leaderboard
```http
GET /api/gamification/leaderboard?period=monthly&limit=50
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "leaderboard": [
      {
        "rank": 1,
        "userId": "user-123",
        "displayName": "KeycapMaster",
        "points": 5000,
        "level": 12,
        "avatar": "https://example.com/avatar.jpg"
      }
    ],
    "userRank": {
      "rank": 25,
      "points": 1250,
      "level": 5
    },
    "period": "monthly",
    "totalParticipants": 1247
  }
}
```

### Reward Shop API

#### Get Available Rewards
```http
GET /api/gamification/rewards?category=discounts&available=true
Authorization: Bearer <token>
```

#### Purchase Reward
```http
POST /api/gamification/rewards/purchase
Authorization: Bearer <token>
Content-Type: application/json

{
  "rewardId": "discount-10-percent",
  "quantity": 1
}
```

## 🎲 Raffle System API

### Raffle Management

#### Get Active Raffles
```http
GET /api/raffles?status=active&featured=true
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "raffle-123",
      "productName": "Limited Edition Keycap Set",
      "description": "Exclusive artisan keycap collection",
      "images": ["image1.jpg", "image2.jpg"],
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-01-31T23:59:59Z",
      "status": "active",
      "entryCount": 1247,
      "maxEntries": 5000,
      "socialRequirements": {
        "instagram": { "required": true, "action": "follow" },
        "discord": { "required": true, "action": "join" }
      },
      "products": [
        {
          "id": "product-123",
          "name": "Artisan Keycap Alpha",
          "tier": 1,
          "quantity": 1
        }
      ]
    }
  ]
}
```

#### Enter Raffle
```http
POST /api/raffles/enter
Authorization: Bearer <token>
Content-Type: application/json

{
  "raffleId": "raffle-123",
  "selectedProducts": ["product-123"],
  "socialVerification": {
    "instagram": "verified",
    "discord": "verified"
  },
  "shippingAddress": {
    "name": "John Doe",
    "address": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zipCode": "12345",
    "country": "United States"
  }
}
```

#### Get User Raffle Entries
```http
GET /api/raffles/entries?status=active
Authorization: Bearer <token>
```

### Raffle Analytics (Admin)

#### Get Raffle Analytics
```http
GET /api/admin/raffles/analytics?raffleId=raffle-123
Authorization: Bearer <admin-token>
```

## 🛒 E-commerce API

### Product Management

#### Get Products
```http
GET /api/products?category=keycaps&featured=true&limit=20&offset=0
```

#### Get Product Details
```http
GET /api/products/product-123
```

#### Get Product Recommendations
```http
GET /api/products/recommendations?userId=user-123&context=product&currentProduct=product-123
Authorization: Bearer <token>
```

### Shopping Cart

#### Get Cart
```http
GET /api/cart
Authorization: Bearer <token>
```

#### Add to Cart
```http
POST /api/cart/add
Authorization: Bearer <token>
Content-Type: application/json

{
  "productId": "product-123",
  "quantity": 1,
  "selectedColor": "blue",
  "selectedCompatibility": "mx"
}
```

#### Update Cart Item
```http
PUT /api/cart/items/item-123
Authorization: Bearer <token>
Content-Type: application/json

{
  "quantity": 2
}
```

#### Remove from Cart
```http
DELETE /api/cart/items/item-123
Authorization: Bearer <token>
```

### Checkout & Orders

#### Create Checkout Session
```http
POST /api/checkout/session
Authorization: Bearer <token>
Content-Type: application/json

{
  "items": [
    {
      "productId": "product-123",
      "quantity": 1,
      "selectedColor": "blue"
    }
  ]
}
```

#### Process Payment
```http
POST /api/checkout/payment
Authorization: Bearer <token>
Content-Type: application/json

{
  "sessionId": "checkout-session-123",
  "paymentMethod": {
    "type": "paypal",
    "provider": "paypal"
  },
  "paymentData": {
    "orderID": "paypal-order-123"
  }
}
```

#### Get Orders
```http
GET /api/orders?status=completed&limit=10
Authorization: Bearer <token>
```

## 🔧 Admin API

### User Management

#### Get Users
```http
GET /api/admin/users?search=john&role=user&limit=50&offset=0
Authorization: Bearer <admin-token>
```

#### Update User
```http
PUT /api/admin/users/user-123
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "role": "admin",
  "status": "active",
  "points": 1000
}
```

#### Bulk User Operations
```http
POST /api/admin/users/bulk
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "operation": "update",
  "targets": ["user-123", "user-456"],
  "parameters": {
    "status": "active"
  }
}
```

### Analytics & Reporting

#### Get Dashboard Analytics
```http
GET /api/admin/analytics/dashboard?period=30d
Authorization: Bearer <admin-token>
```

#### Export Data
```http
POST /api/admin/export
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "entityType": "users",
  "format": "csv",
  "filters": [
    {
      "field": "createdAt",
      "operator": "greater_than",
      "value": "2024-01-01"
    }
  ],
  "fields": ["id", "email", "points", "level", "createdAt"]
}
```

## 📊 Performance & Monitoring API

### Performance Metrics
```http
GET /api/monitoring/performance?metric=core-web-vitals&period=24h
Authorization: Bearer <admin-token>
```

### Security Events
```http
GET /api/monitoring/security?severity=high&limit=100
Authorization: Bearer <admin-token>
```

## 🔄 Webhooks

### PayPal Webhook
```http
POST /api/webhooks/paypal
Content-Type: application/json

{
  "event_type": "PAYMENT.CAPTURE.COMPLETED",
  "resource": {
    "id": "payment-123",
    "status": "COMPLETED",
    "amount": {
      "currency_code": "USD",
      "value": "149.99"
    }
  }
}
```

### Firebase Webhook (Custom)
```http
POST /api/webhooks/firebase
Content-Type: application/json

{
  "eventType": "user.created",
  "data": {
    "uid": "user-123",
    "email": "<EMAIL>"
  }
}
```

## 📝 Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req-123"
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED` - Missing or invalid authentication
- `AUTHORIZATION_DENIED` - Insufficient permissions
- `VALIDATION_ERROR` - Invalid input parameters
- `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error

## 🚀 Rate Limiting

### Default Limits
- **General API**: 100 requests per 15 minutes per IP
- **Authentication**: 5 requests per 15 minutes per IP
- **Admin API**: 1000 requests per 15 minutes per admin user

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## 📚 SDK & Libraries

### JavaScript/TypeScript SDK
```bash
npm install @syndicaps/sdk
```

```javascript
import { SyndicapsSDK } from '@syndicaps/sdk'

const sdk = new SyndicapsSDK({
  apiKey: 'your-api-key',
  environment: 'production'
})

// Award points
await sdk.gamification.awardPoints(userId, 100, 'achievement')

// Get leaderboard
const leaderboard = await sdk.gamification.getLeaderboard('monthly')
```

---

**API Version**: v1  
**Last Updated**: December 2024  
**Base URL**: `https://api.syndicaps.com/v1`
