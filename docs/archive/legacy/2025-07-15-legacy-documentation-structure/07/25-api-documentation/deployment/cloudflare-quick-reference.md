# Cloudflare Pages Quick Reference - Syndicaps Deployment

## 🚀 Essential Commands

### Wrangler CLI Setup
```bash
# Install Wrangler CLI
npm install -g wrangler

# Login to Cloudflare
wrangler login

# Check authentication
wrangler whoami
```

### Project Management
```bash
# Create new Pages project
wrangler pages project create syndicaps

# List all projects
wrangler pages project list

# Deploy to Pages
wrangler pages deploy out --project-name syndicaps

# Check deployment status
wrangler pages deployment list --project-name syndicaps
```

### Environment Variables
```bash
# Set environment variable
wrangler pages secret put VARIABLE_NAME --project-name syndicaps

# List all secrets
wrangler pages secret list --project-name syndicaps

# Delete secret
wrangler pages secret delete VARIABLE_NAME --project-name syndicaps
```

### Domain Management
```bash
# Add custom domain
wrangler pages domain add syndicaps.com --project-name syndicaps

# List domains
wrangler pages domain list --project-name syndicaps

# Remove domain
wrangler pages domain remove syndicaps.com --project-name syndicaps
```

## 📊 Critical Monitoring URLs

### Cloudflare Dashboard
- **Main Dashboard**: https://dash.cloudflare.com/
- **Analytics**: https://dash.cloudflare.com/analytics
- **Security Events**: https://dash.cloudflare.com/security/events
- **Page Rules**: https://dash.cloudflare.com/page-rules

### Performance Monitoring
- **Core Web Vitals**: https://pagespeed.web.dev/
- **GTmetrix**: https://gtmetrix.com/
- **WebPageTest**: https://www.webpagetest.org/
- **Lighthouse CI**: Built into deployment pipeline

### Security Monitoring
- **SSL Labs**: https://www.ssllabs.com/ssltest/
- **Security Headers**: https://securityheaders.com/
- **Mozilla Observatory**: https://observatory.mozilla.org/

## 🔧 Configuration Snippets

### Next.js Config for Cloudflare Pages
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: { unoptimized: true },
  experimental: {
    optimizePackageImports: ['lucide-react', 'framer-motion']
  }
};
module.exports = nextConfig;
```

### Essential Environment Variables
```bash
# Firebase (Required)
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_PROJECT_ID=syndicaps-prod
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=syndicaps-prod.firebaseapp.com

# Security (Required)
ADMIN_EMAIL=<EMAIL>
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your_recaptcha_key

# Analytics (Recommended)
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
SENTRY_DSN=https://<EMAIL>/project
```

### Cache Headers (_headers file)
```
/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY

/*.html
  Cache-Control: public, max-age=3600

/api/*
  Cache-Control: public, max-age=300

/admin/*
  Cache-Control: no-cache, no-store, must-revalidate
```

## 🚨 Emergency Procedures

### Rollback Deployment
```bash
# List recent deployments
wrangler pages deployment list --project-name syndicaps

# Rollback to specific deployment
wrangler pages deployment rollback DEPLOYMENT_ID --project-name syndicaps

# Quick rollback to previous
wrangler pages deployment rollback --project-name syndicaps
```

### DNS Emergency Switch
```bash
# Temporarily point to backup
# Update A record to backup server IP
# TTL should be set to 300 seconds for quick changes

# Cloudflare API for emergency DNS update
curl -X PUT "https://api.cloudflare.com/client/v4/zones/ZONE_ID/dns_records/RECORD_ID" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{"type":"A","name":"syndicaps.com","content":"BACKUP_IP","ttl":300}'
```

### Maintenance Mode
```javascript
// workers/maintenance.js
export default {
  async fetch(request) {
    const url = new URL(request.url);
    
    // Allow admin access
    if (url.pathname.startsWith('/admin')) {
      return fetch(request);
    }
    
    // Show maintenance page
    return new Response(`
      <!DOCTYPE html>
      <html>
        <head><title>Maintenance - Syndicaps</title></head>
        <body>
          <h1>We'll be right back!</h1>
          <p>Syndicaps is currently undergoing maintenance.</p>
        </body>
      </html>
    `, {
      headers: { 'Content-Type': 'text/html' },
      status: 503
    });
  }
};
```

## 📈 Performance Targets

### Core Web Vitals
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1
- **TTFB (Time to First Byte)**: < 600ms

### E-commerce Specific
- **Homepage Load**: < 2s
- **Product Page Load**: < 2s
- **Search Results**: < 1.5s
- **Cart Operations**: < 1s
- **Checkout Process**: < 3s total

### Availability Targets
- **Uptime**: 99.9% (8.76 hours downtime/year)
- **Error Rate**: < 0.1%
- **Cache Hit Ratio**: > 95%

## 🔒 Security Checklist

### SSL/TLS Configuration
- [ ] SSL/TLS mode: Full (strict)
- [ ] HSTS enabled (6 months)
- [ ] Always Use HTTPS: On
- [ ] Minimum TLS version: 1.2

### WAF Rules (Business Plan+)
```javascript
// Essential WAF rules
[
  {
    expression: "http.request.uri.path contains \"/admin\" and ip.geoip.country ne \"US\"",
    action: "challenge"
  },
  {
    expression: "rate(1m) > 100",
    action: "challenge"
  },
  {
    expression: "http.request.uri.query contains \"union\" and http.request.uri.query contains \"select\"",
    action: "block"
  }
]
```

### Security Headers
```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: geolocation=(), microphone=(), camera=()
```

## 💰 Cost Monitoring

### Plan Recommendations
- **Development**: Free Plan
- **Staging**: Pro Plan ($20/month)
- **Production**: Business Plan ($200/month)
- **Enterprise**: Custom pricing

### Monthly Cost Estimates
```
Pro Plan Total: ~$50/month
- Base plan: $20
- Workers: $5-10
- Images: $15-25
- Argo: $5

Business Plan Total: ~$220/month
- Base plan: $200
- Workers: $5-10
- Images: included
- Argo: $5
```

## 📞 Support Contacts

### Cloudflare Support
- **Community**: https://community.cloudflare.com/
- **Documentation**: https://developers.cloudflare.com/
- **Status Page**: https://www.cloudflarestatus.com/
- **Support Portal**: https://support.cloudflare.com/

### Emergency Escalation
1. **Level 1**: Development team
2. **Level 2**: DevOps/Infrastructure team
3. **Level 3**: Cloudflare Business Support
4. **Level 4**: Emergency contacts

### Key Metrics Dashboard
```
Real-time monitoring:
- Uptime: https://status.syndicaps.com
- Performance: Cloudflare Analytics
- Errors: Sentry Dashboard
- Business: Google Analytics
```

## 🔄 Regular Maintenance

### Daily Tasks
- [ ] Check error rates and response times
- [ ] Review security events
- [ ] Monitor Core Web Vitals

### Weekly Tasks
- [ ] Analyze traffic patterns
- [ ] Review cost usage
- [ ] Update security rules if needed

### Monthly Tasks
- [ ] Performance optimization review
- [ ] Security audit
- [ ] Cost analysis and optimization
- [ ] Update documentation

---

**Quick Access Links:**
- [Full Deployment Guide](./cloudflare-pages-deployment-guide.md)
- [Deployment Checklist](./cloudflare-deployment-checklist.md)
- [Cost Calculator](./cloudflare-cost-calculator.md)

**Emergency Hotline**: [Your emergency contact]
**Last Updated**: [Current date]
