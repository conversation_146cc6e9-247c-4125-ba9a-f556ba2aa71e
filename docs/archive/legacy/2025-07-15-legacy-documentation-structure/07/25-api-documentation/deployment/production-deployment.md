# Production Deployment Guide

This guide covers the complete deployment process for the Syndicaps platform to production environments.

## 🎯 Pre-Deployment Checklist

### Code Quality
- [ ] All tests passing (`npm run test:ci`)
- [ ] Code coverage ≥ 75% (`npm run test:coverage`)
- [ ] Quality gates passed (`npm run quality-gates`)
- [ ] No ESLint errors (`npm run lint`)
- [ ] TypeScript compilation successful (`npm run type-check`)
- [ ] Security audit clean (`npm audit`)

### Environment Setup
- [ ] Production Firebase project configured
- [ ] PayPal production credentials obtained
- [ ] Redis instance provisioned
- [ ] Domain and SSL certificates ready
- [ ] CDN configured (if applicable)
- [ ] Monitoring tools set up

### Security Verification
- [ ] Environment variables secured
- [ ] API keys rotated for production
- [ ] Rate limiting configured
- [ ] CORS settings verified
- [ ] Security headers implemented

## 🔧 Environment Configuration

### 1. Firebase Production Setup

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase project
firebase init

# Configure Firestore security rules
firebase deploy --only firestore:rules

# Configure Storage security rules
firebase deploy --only storage
```

### 2. Environment Variables

Create production environment file:

```env
# Production Environment Variables
NODE_ENV=production

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=prod_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=syndicaps-prod.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=syndicaps-prod
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=syndicaps-prod.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=prod_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=prod_app_id

# Firebase Admin (Server-side)
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_PROJECT_ID=syndicaps-prod

# PayPal Production
NEXT_PUBLIC_PAYPAL_CLIENT_ID=prod_paypal_client_id
PAYPAL_CLIENT_SECRET=prod_paypal_secret
PAYPAL_ENVIRONMENT=production

# Security Configuration
ENCRYPTION_KEY=your_32_character_production_key
JWT_SECRET=your_production_jwt_secret
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://syndicaps.com

# Performance & Caching
REDIS_URL=redis://prod-redis-instance:6379
REDIS_PASSWORD=your_redis_password
CACHE_STRATEGY=hybrid
CACHE_DEFAULT_TTL=300

# Monitoring & Analytics
PERFORMANCE_MONITORING_ENABLED=true
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_ga_id

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
```

## 🚀 Deployment Process

### 1. Vercel Deployment (Recommended)

```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy to production
vercel --prod

# Configure environment variables in Vercel dashboard
# Or use CLI:
vercel env add NEXT_PUBLIC_FIREBASE_API_KEY production
vercel env add FIREBASE_ADMIN_PRIVATE_KEY production
# ... add all environment variables
```

### 2. Alternative: Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

```bash
# Build and deploy with Docker
docker build -t syndicaps .
docker run -p 3000:3000 syndicaps
```

### 3. Database Migration

```bash
# Run database initialization
npm run db:init:prod

# Migrate existing data (if applicable)
npm run db:migrate

# Seed initial data
npm run db:seed:prod
```

## 📊 Post-Deployment Verification

### 1. Health Checks

```bash
# Check application health
curl https://syndicaps.com/api/health

# Check database connectivity
curl https://syndicaps.com/api/health/database

# Check external services
curl https://syndicaps.com/api/health/services
```

### 2. Performance Testing

```bash
# Run performance tests
npm run test:performance:prod

# Check Core Web Vitals
npm run test:lighthouse

# Load testing (if configured)
npm run test:load
```

### 3. Security Verification

```bash
# Security scan
npm run security:scan

# SSL certificate check
openssl s_client -connect syndicaps.com:443

# Security headers check
curl -I https://syndicaps.com
```

## 🔍 Monitoring Setup

### 1. Application Monitoring

```javascript
// Configure Sentry for error tracking
import * as Sentry from "@sentry/nextjs"

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
})
```

### 2. Performance Monitoring

```javascript
// Configure performance monitoring
import { performanceMonitor } from '@/lib/performance/performanceMonitoring'

// Start monitoring in production
if (process.env.NODE_ENV === 'production') {
  performanceMonitor.startMonitoring()
}
```

### 3. Uptime Monitoring

Set up external uptime monitoring:
- **Pingdom**: Monitor application availability
- **StatusPage**: Public status page for users
- **PagerDuty**: Alert management for incidents

## 🚨 Rollback Procedures

### 1. Vercel Rollback

```bash
# List deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url]
```

### 2. Database Rollback

```bash
# Backup current database
npm run db:backup

# Restore from backup
npm run db:restore [backup-file]
```

### 3. Emergency Procedures

1. **Immediate Response**
   - Put application in maintenance mode
   - Notify stakeholders
   - Begin rollback process

2. **Investigation**
   - Check error logs
   - Review monitoring dashboards
   - Identify root cause

3. **Resolution**
   - Apply hotfix if possible
   - Complete rollback if necessary
   - Verify system stability

## 📈 Performance Optimization

### 1. CDN Configuration

```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['cdn.syndicaps.com'],
    loader: 'cloudinary',
    path: 'https://res.cloudinary.com/syndicaps/',
  },
  experimental: {
    optimizeCss: true,
    optimizeImages: true,
  },
}
```

### 2. Caching Strategy

```javascript
// Configure Redis caching
const cacheConfig = {
  redis: {
    url: process.env.REDIS_URL,
    ttl: {
      products: 1800,      // 30 minutes
      users: 900,          // 15 minutes
      achievements: 3600,  // 1 hour
      leaderboard: 600,    // 10 minutes
    }
  }
}
```

### 3. Database Optimization

```javascript
// Firestore indexes for production
// Create composite indexes for:
// - users: (points, level, createdAt)
// - achievements: (category, difficulty, points)
// - raffles: (status, endDate, featured)
// - orders: (userId, status, createdAt)
```

## 🔐 Security Hardening

### 1. Security Headers

```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' *.paypal.com *.google-analytics.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: *.firebase.com *.googleusercontent.com;"
          }
        ]
      }
    ]
  }
}
```

### 2. Rate Limiting

```javascript
// Configure production rate limits
const rateLimits = {
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  },
  auth: {
    windowMs: 15 * 60 * 1000,
    max: 5 // limit login attempts
  }
}
```

## 📋 Maintenance Procedures

### 1. Regular Updates

```bash
# Weekly security updates
npm audit fix
npm update

# Monthly dependency updates
npm outdated
npm update --save

# Quarterly major updates
npm-check-updates -u
npm install
```

### 2. Database Maintenance

```bash
# Weekly database backup
npm run db:backup:weekly

# Monthly performance optimization
npm run db:optimize

# Quarterly data cleanup
npm run db:cleanup
```

### 3. Performance Reviews

- **Weekly**: Review performance metrics
- **Monthly**: Analyze user behavior and optimize
- **Quarterly**: Comprehensive performance audit

## 📞 Support & Escalation

### Contact Information
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **On-call Engineer**: +1-XXX-XXX-XXXX

### Escalation Matrix
1. **Level 1**: Application errors, minor performance issues
2. **Level 2**: Service outages, security incidents
3. **Level 3**: Data breaches, critical system failures

---

**Last Updated**: December 2024  
**Version**: 1.0  
**Maintained by**: Syndicaps DevOps Team
