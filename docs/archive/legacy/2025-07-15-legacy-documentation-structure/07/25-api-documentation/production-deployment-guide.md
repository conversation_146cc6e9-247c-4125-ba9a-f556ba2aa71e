# Production Deployment Guide - Syndicaps Gamification System

## Overview
This guide covers the production deployment of the Syndicaps gamification system, including environment configuration, security settings, monitoring, and optimization strategies.

## Environment Configuration

### Required Environment Variables

#### Firebase Configuration
```bash
# Firebase Core Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_production_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_production_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Firebase Admin SDK (Server-side only)
FIREBASE_ADMIN_PROJECT_ID=your_production_project_id
FIREBASE_ADMIN_CLIENT_EMAIL=your_service_account_email
FIREBASE_ADMIN_PRIVATE_KEY=your_private_key
```

#### Gamification Configuration
```bash
# Points System Configuration
GAMIFICATION_POINTS_PER_DOLLAR=1
GAMIFICATION_DAILY_LOGIN_POINTS=5
GAMIFICATION_REVIEW_POINTS=50
GAMIFICATION_REFERRAL_POINTS=500
GAMIFICATION_BIRTHDAY_BONUS=500

# Achievement System
GAMIFICATION_ACHIEVEMENT_CACHE_TTL=3600
GAMIFICATION_LEADERBOARD_UPDATE_INTERVAL=300

# Reward System
GAMIFICATION_REWARD_FULFILLMENT_WEBHOOK=https://your-domain.com/api/webhooks/reward-fulfillment
GAMIFICATION_REWARD_NOTIFICATION_EMAIL=<EMAIL>
```

#### Security Configuration
```bash
# API Security
API_SECRET_KEY=your_secure_random_key
JWT_SECRET=your_jwt_secret_key
WEBHOOK_SECRET=your_webhook_secret

# Rate Limiting
RATE_LIMIT_POINTS_PER_HOUR=1000
RATE_LIMIT_ACHIEVEMENTS_PER_DAY=50
RATE_LIMIT_REWARDS_PER_DAY=10

# CORS Configuration
ALLOWED_ORIGINS=https://syndicaps.com,https://www.syndicaps.com
```

#### Monitoring & Analytics
```bash
# Error Tracking
SENTRY_DSN=your_sentry_dsn
SENTRY_ENVIRONMENT=production

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_id
MIXPANEL_TOKEN=your_mixpanel_token

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=your_newrelic_key
DATADOG_API_KEY=your_datadog_key
```

## Security Configuration

### Firebase Security Rules
The production Firestore security rules are already configured in `firestore.rules`. Key security features:

1. **User Data Isolation**: Users can only access their own gamification data
2. **Admin Controls**: Administrative functions require admin role verification
3. **Immutable Records**: Activity logs and transactions are write-once, read-many
4. **Rate Limiting**: Built-in protection against abuse

### API Security Best Practices

#### 1. Authentication Middleware
```typescript
// Verify Firebase ID tokens on all protected routes
export async function verifyAuthToken(req: NextRequest) {
  const token = req.headers.get('authorization')?.replace('Bearer ', '')
  if (!token) throw new Error('No authentication token')
  
  const decodedToken = await admin.auth().verifyIdToken(token)
  return decodedToken
}
```

#### 2. Rate Limiting
```typescript
// Implement rate limiting for gamification endpoints
export const rateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 1000, // Limit each IP to 1000 requests per hour
  message: 'Too many requests from this IP'
})
```

#### 3. Input Validation
```typescript
// Validate all gamification inputs
export function validatePointsRequest(data: any) {
  const schema = z.object({
    amount: z.number().min(1).max(10000),
    source: z.string().min(1).max(100),
    description: z.string().min(1).max(500)
  })
  return schema.parse(data)
}
```

## Performance Optimization

### Database Optimization

#### 1. Firestore Indexes
Create composite indexes for common queries:
```javascript
// Required indexes for gamification queries
{
  collectionGroup: "point_history",
  fields: [
    { fieldPath: "userId", order: "ASCENDING" },
    { fieldPath: "createdAt", order: "DESCENDING" }
  ]
},
{
  collectionGroup: "user_achievements",
  fields: [
    { fieldPath: "userId", order: "ASCENDING" },
    { fieldPath: "unlockedAt", order: "DESCENDING" }
  ]
}
```

#### 2. Data Caching Strategy
```typescript
// Implement Redis caching for frequently accessed data
export class GamificationCache {
  static async getUserPoints(userId: string): Promise<number> {
    const cached = await redis.get(`points:${userId}`)
    if (cached) return parseInt(cached)
    
    const points = await getUserPointBalance(userId)
    await redis.setex(`points:${userId}`, 300, points) // 5 min cache
    return points
  }
}
```

### Frontend Optimization

#### 1. Code Splitting
```typescript
// Lazy load gamification components
const GamificationDashboard = lazy(() => import('./GamificationDashboard'))
const RewardShop = lazy(() => import('./RewardShop'))
const AchievementProgress = lazy(() => import('./AchievementProgress'))
```

#### 2. Data Prefetching
```typescript
// Prefetch critical gamification data
export function useGamificationPrefetch() {
  const { user } = useUser()
  
  useEffect(() => {
    if (user) {
      // Prefetch user's points and recent achievements
      Promise.all([
        getUserPointBalance(user.uid),
        getUserAchievements(user.uid)
      ])
    }
  }, [user])
}
```

## Monitoring & Analytics

### Error Tracking Setup

#### 1. Sentry Configuration
```typescript
// Configure Sentry for error tracking
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
  beforeSend(event) {
    // Filter out sensitive gamification data
    if (event.extra?.pointBalance) {
      delete event.extra.pointBalance
    }
    return event
  }
})
```

#### 2. Custom Error Tracking
```typescript
// Track gamification-specific errors
export function trackGamificationError(error: Error, context: any) {
  Sentry.withScope(scope => {
    scope.setTag('component', 'gamification')
    scope.setContext('gamification', context)
    Sentry.captureException(error)
  })
}
```

### Performance Monitoring

#### 1. Custom Metrics
```typescript
// Track gamification performance metrics
export function trackGamificationMetrics() {
  // Track point transaction latency
  const startTime = performance.now()
  // ... perform operation
  const duration = performance.now() - startTime
  
  analytics.track('Gamification Performance', {
    operation: 'point_transaction',
    duration,
    userId: user.uid
  })
}
```

#### 2. User Engagement Analytics
```typescript
// Track gamification engagement
export function trackGamificationEngagement(event: string, properties: any) {
  analytics.track(`Gamification: ${event}`, {
    ...properties,
    timestamp: new Date().toISOString(),
    sessionId: getSessionId()
  })
}
```

## Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Firebase security rules deployed
- [ ] Database indexes created
- [ ] Error tracking configured
- [ ] Performance monitoring setup
- [ ] Rate limiting implemented
- [ ] SSL certificates configured

### Deployment Steps
1. **Build Optimization**
   ```bash
   npm run build
   npm run analyze # Check bundle sizes
   ```

2. **Database Migration**
   ```bash
   node scripts/initializeGamificationDatabase.js
   ```

3. **Security Rules Deployment**
   ```bash
   firebase deploy --only firestore:rules
   ```

4. **Application Deployment**
   ```bash
   npm run deploy:production
   ```

### Post-Deployment
- [ ] Smoke tests passed
- [ ] Monitoring dashboards active
- [ ] Error rates within acceptable limits
- [ ] Performance metrics baseline established
- [ ] User acceptance testing completed

## Maintenance & Updates

### Regular Maintenance Tasks
1. **Weekly**: Review error logs and performance metrics
2. **Monthly**: Update dependencies and security patches
3. **Quarterly**: Performance optimization review
4. **Annually**: Security audit and penetration testing

### Backup Strategy
1. **Firestore Backups**: Automated daily backups
2. **Configuration Backups**: Version-controlled environment configs
3. **Code Backups**: Git repository with multiple remotes

### Scaling Considerations
1. **Database Scaling**: Firestore auto-scales, monitor usage
2. **API Scaling**: Implement horizontal scaling for API endpoints
3. **Cache Scaling**: Redis cluster for high-traffic scenarios
4. **CDN**: Use CDN for static gamification assets

## Troubleshooting

### Common Issues
1. **High Latency**: Check database indexes and caching
2. **Rate Limiting**: Monitor API usage patterns
3. **Memory Leaks**: Profile React components and hooks
4. **Security Violations**: Review Firestore security rules

### Emergency Procedures
1. **System Outage**: Rollback deployment procedure
2. **Data Corruption**: Restore from backup procedure
3. **Security Breach**: Incident response procedure
4. **Performance Degradation**: Emergency optimization procedure
