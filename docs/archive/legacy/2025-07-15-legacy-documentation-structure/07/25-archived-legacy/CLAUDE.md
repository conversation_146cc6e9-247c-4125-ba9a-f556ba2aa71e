### 🔄 Project Awareness & Context
- **Always read `README.md`** at the start of a new conversation to understand the project's architecture, goals, and setup instructions.
- **Check the git status and recent commits** to understand current development state and any ongoing work.
- **Use consistent naming conventions, file structure, and architecture patterns** as established in the existing codebase.
- **This is a Next.js 15 project** using React, TypeScript, Tailwind CSS, Firebase, and Framer Motion.

### 🧱 Code Structure & Modularity
- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.
- **Organize code into clearly separated modules**, grouped by feature or responsibility:
  - `src/components/` - Reusable React components organized by feature
  - `src/lib/` - Utility functions, Firebase configurations, and shared logic
  - `src/hooks/` - Custom React hooks
  - `app/` - Next.js 15 app router pages and layouts
  - `src/admin/` - Admin-specific components and utilities
- **Use clear, consistent imports** (prefer absolute imports with the `@/` alias).
- **Follow React component patterns**: Use TypeScript interfaces for props, implement proper error boundaries, and handle loading states.
- **Use environment variables** with Next.js conventions (`NEXT_PUBLIC_` prefix for client-side variables).

### 🧪 Testing & Reliability
- **Always create unit tests for new features** using Jest and React Testing Library.
- **After updating any logic**, check whether existing unit tests need to be updated.
- **Tests should live in a `__tests__` folder** alongside the component or in the root `/tests` folder.
- **Include error boundaries and defensive programming** to prevent crashes.
- **Test Firebase integration carefully** and provide fallbacks when Firebase is unavailable.

### ✅ Task Completion
- **Use the TodoWrite tool** to track multi-step tasks and provide visibility to the user.
- **Mark todos as completed immediately** after finishing them.
- **Run linting and type checking** after making changes (`npm run lint`, `npm run typecheck`).

### 📎 Style & Conventions
- **Use TypeScript** with strict type checking enabled.
- **Follow React and Next.js best practices** including proper use of Server and Client Components.
- **Use Tailwind CSS** for styling with the established design system (colors: gray-950, accent-500, etc.).
- **Use Framer Motion** for animations following the established patterns in the codebase.
- **Follow Firebase best practices** including proper error handling and offline support.
- **Write TypeScript interfaces for all data structures**:
  ```typescript
  interface ComponentProps {
    title: string;
    isVisible?: boolean;
  }
  ```

### 🔥 Firebase Integration Rules
- **Always check if Firebase services are available** before using them (db, auth, storage can be null).
- **Use defensive programming** with try-catch blocks around Firebase operations.
- **Implement proper loading states** and error handling for all Firebase interactions.
- **Never assume Firebase is initialized** - always check service availability.
- **Use Firestore security rules** and validate data on both client and server sides.

### 🎨 UI/UX Guidelines
- **Maintain design consistency** with the established Syndicaps brand (dark theme, purple accents).
- **Use semantic HTML** and proper ARIA labels for accessibility.
- **Implement responsive design** with mobile-first approach.
- **Use loading states and skeletons** to improve perceived performance.
- **Handle error states gracefully** with user-friendly messages and recovery options.

### 📚 Documentation & Explainability
- **Update `README.md`** when new features are added, dependencies change, or setup steps are modified.
- **Comment complex logic** and ensure everything is understandable to a mid-level React developer.
- **Use JSDoc comments for complex functions**:
  ```typescript
  /**
   * Calculates raffle countdown and updates status
   * @param raffle - The raffle object with start/end dates
   * @param currentStatus - Current raffle status to avoid infinite loops
   * @returns Updated time remaining and status
   */
  ```

### 🧠 AI Behavior Rules
- **Never assume missing context. Ask questions if uncertain.**
- **Never hallucinate libraries or APIs** – only use known, verified packages from package.json.
- **Always confirm file paths exist** before referencing them in imports or modifications.
- **Never delete or overwrite existing code** unless explicitly instructed or fixing critical bugs.
- **Always consider performance implications** especially for client-side code and Firebase queries.
- **Respect the existing architecture** - don't introduce new patterns without discussion.

### ⚡ Performance & Optimization Rules
- **Minimize client-side JavaScript bundles** by using proper code splitting and dynamic imports.
- **Optimize Firebase queries** by using appropriate indexes and limiting data transfer.
- **Use React.memo, useMemo, and useCallback** judiciously to prevent unnecessary re-renders.
- **Implement proper error boundaries** to prevent entire page crashes.
- **Use loading skeletons** instead of generic loading spinners when possible.

### 🛡️ Security Guidelines
- **Never commit secrets or API keys** to the repository.
- **Use environment variables** for all configuration values.
- **Validate user input** on both client and server sides.
- **Follow Content Security Policy** rules defined in next.config.js.
- **Implement proper authentication checks** before accessing protected resources.
- **Use Firebase Security Rules** to protect Firestore collections and Storage buckets.

### 🔧 Development Workflow
- **Test changes locally** before suggesting commits.
- **Follow the existing component patterns** for consistency.
- **Use the established error handling patterns** throughout the codebase.
- **Check for console errors** and fix them before considering a task complete.
- **Ensure accessibility compliance** with proper ARIA labels and keyboard navigation.