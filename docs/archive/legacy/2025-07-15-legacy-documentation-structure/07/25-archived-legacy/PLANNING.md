# 📋 Syndicaps Project Planning & Architecture

## 🎯 Project Overview

**Syndicaps** is a comprehensive e-commerce platform for artisan keycaps featuring advanced gamification, raffle systems, and admin management capabilities. The platform combines traditional e-commerce functionality with sophisticated gaming mechanics and community features.

### Business Goals
- Create a premium marketplace for artisan keycap collectors
- Build an engaged community through gamification and raffles
- Provide comprehensive admin tools for business management
- Ensure scalable, secure, and high-performance operations

### Target Users
- **Customers**: Mechanical keyboard enthusiasts and collectors
- **Admins**: Business operators managing products, raffles, and users
- **Community**: Active participants in gamification and social features

## 🏗️ Technical Architecture

### Core Technology Stack

#### Frontend
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS v3.4+ with custom design system
- **Animations**: Framer Motion v11+
- **State Management**: Zustand for global state
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Custom components + Radix UI primitives

#### Backend & Database
- **Database**: Firebase Firestore (NoSQL)
- **Authentication**: Firebase Auth
- **Storage**: Firebase Storage for images/files
- **Functions**: Firebase Cloud Functions (Node.js)
- **Analytics**: Firebase Analytics + Google Analytics

#### Development & Deployment
- **Package Manager**: npm
- **Testing**: Jest + React Testing Library + Playwright
- **Linting**: ESLint + TypeScript ESLint
- **Deployment**: Vercel (production) + Firebase Hosting (staging)
- **CI/CD**: GitHub Actions with quality gates

### Performance & Security
- **CSP**: Content Security Policy for XSS protection
- **Image Optimization**: Next.js Image with Unsplash integration
- **Bundle Optimization**: Code splitting with webpack
- **Monitoring**: Firebase Performance + Sentry error tracking
- **PWA**: Service Worker for offline capabilities

## 📁 Project Structure

```
syndicaps/
├── app/                          # Next.js 15 App Router
│   ├── (auth)/                   # Auth-related pages
│   ├── admin/                    # Admin dashboard pages
│   │   ├── blog/                 # Blog management
│   │   ├── gamification/         # Gamification admin
│   │   ├── products/             # Product management
│   │   └── raffles/              # Raffle management
│   ├── api/                      # API routes
│   ├── products/                 # Product pages
│   ├── shop/                     # Shopping pages
│   └── layout.tsx                # Root layout
│
├── src/                          # Source code
│   ├── components/               # Reusable React components
│   │   ├── admin/                # Admin-specific components
│   │   ├── auth/                 # Authentication components
│   │   ├── cart/                 # Shopping cart components
│   │   ├── gamification/         # Points, achievements, rewards
│   │   ├── layout/               # Layout components (Header, Footer)
│   │   ├── products/             # Product display components
│   │   ├── raffle/               # Raffle system components
│   │   └── ui/                   # Base UI components
│   │
│   ├── hooks/                    # Custom React hooks
│   ├── lib/                      # Utilities and configurations
│   │   ├── firebase/             # Firebase configuration & helpers
│   │   ├── utils/                # General utilities
│   │   └── validation/           # Zod schemas
│   │
│   ├── types/                    # TypeScript type definitions
│   └── admin/                    # Admin-specific modules
│
├── functions/                    # Firebase Cloud Functions
├── scripts/                      # Database setup & utility scripts
├── tests/                        # Test files
│   ├── e2e/                      # End-to-end tests (Playwright)
│   ├── integration/              # Integration tests
│   └── unit/                     # Unit tests (Jest)
│
├── public/                       # Static assets
└── docs/                         # Documentation
```

## 🎮 Core Features & Architecture

### 1. Gamification System
**Location**: `src/components/gamification/`, `app/admin/gamification/`

**Components**:
- **Points System**: Earn points through purchases, reviews, social shares
- **Achievements**: 50+ achievements across multiple categories
- **Leaderboards**: Competitive rankings with seasonal resets
- **Reward Shop**: Points-based purchasing system
- **Progress Tracking**: Visual indicators and celebrations

**Key Files**:
- `src/lib/gamification/`
- `src/components/gamification/`
- `app/admin/gamification/`

### 2. Raffle System
**Location**: `src/components/raffle/`, `app/admin/raffles/`

**Features**:
- Multi-winner support with prize tiers
- Social media verification requirements
- Real-time analytics and insights
- Mobile-optimized entry flow
- Smart notification system

**Key Files**:
- `src/components/raffle/RaffleCountdown.tsx`
- `src/lib/raffleNotifications.ts`
- `app/admin/raffles/`

### 3. E-commerce Platform
**Location**: `src/components/products/`, `app/products/`, `app/shop/`

**Features**:
- Product catalog with filtering
- Shopping cart and checkout
- PayPal integration
- Inventory management
- Order tracking

### 4. Admin Dashboard
**Location**: `app/admin/`, `src/admin/`

**Features**:
- User management and analytics
- Product and inventory management
- Raffle creation and monitoring
- Bulk operations system
- Data export capabilities

## 🎨 Design System & Conventions

### Color Palette
```css
/* Primary Colors */
--color-gray-950: #030712    /* Primary background */
--color-accent-500: #8b5cf6  /* Primary accent (purple) */
--color-accent-600: #7c3aed  /* Accent hover */

/* Semantic Colors */
--color-success: #10b981
--color-warning: #f59e0b  
--color-error: #ef4444
--color-info: #3b82f6
```

### Component Conventions
- **File Naming**: PascalCase for components (`UserProfile.tsx`)
- **Props**: TypeScript interfaces with `Props` suffix
- **Styling**: Tailwind classes with responsive design
- **State**: Local state with useState, global with Zustand
- **Error Handling**: Error boundaries for component isolation

### Code Organization
- **One component per file** (max 500 lines)
- **Co-located tests** in `__tests__` folders
- **Absolute imports** using `@/` alias
- **Type-first development** with strict TypeScript

## 🔥 Firebase Integration

### Collections Structure
```
firestore/
├── users/                    # User profiles and settings
├── products/                 # Product catalog
├── orders/                   # Order management
├── raffles/                  # Raffle configurations
├── raffle_entries/          # User raffle entries
├── points_transactions/     # Gamification points
├── achievements/            # Achievement definitions
├── user_achievements/       # User progress
├── notifications/           # System notifications
└── admin_logs/              # Admin activity logs
```

### Security Rules
- **Authentication required** for all user data
- **Admin-only access** for admin collections
- **Granular permissions** based on user roles
- **Data validation** using Firestore rules

### Performance Optimization
- **Compound indexes** for complex queries
- **Pagination** for large datasets
- **Real-time listeners** only where necessary
- **Offline support** with cached data

## 🧪 Testing Strategy

### Test Coverage Requirements
- **Unit Tests**: 75%+ coverage (Jest + React Testing Library)
- **Integration Tests**: Critical user flows
- **E2E Tests**: Complete workflows (Playwright)
- **Performance Tests**: Core Web Vitals monitoring

### Testing Patterns
```typescript
// Component Testing
import { render, screen } from '@testing-library/react'
import { ComponentName } from './ComponentName'

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: mockFirestore,
  auth: mockAuth
}))

// Integration Testing
import { test, expect } from '@playwright/test'

test('user can complete raffle entry', async ({ page }) => {
  // E2E test implementation
})
```

## 🚀 Development Workflow

### Branch Strategy
- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/***: Individual feature development
- **hotfix/***: Critical production fixes

### Development Process
1. **Feature Planning**: Document in TASK.md
2. **Development**: Follow CLAUDE.md guidelines
3. **Testing**: Run all test suites
4. **Code Review**: PR review with quality checks
5. **Deployment**: Automated via Vercel

### Quality Gates
```bash
# Required checks before merge
npm run lint          # ESLint + TypeScript
npm run test:ci       # All tests with coverage
npm run build         # Production build
npm run quality-gates # Custom quality checks
```

## 🔧 Environment Configuration

### Environment Variables
```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=

# Payment Integration
NEXT_PUBLIC_PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=
NEXT_PUBLIC_SENTRY_DSN=

# Development
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
```

### Local Development Setup
```bash
# Install dependencies
npm install

# Set up environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Initialize Firebase emulators (optional)
firebase init emulators

# Start development server
npm run dev

# Run tests
npm run test:watch
```

## 📊 Performance Targets

### Core Web Vitals
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

### Bundle Size Targets
- **Initial JS Bundle**: < 200KB gzipped
- **Page-specific bundles**: < 50KB gzipped
- **Image optimization**: WebP format, responsive sizing

### Performance Monitoring
- Firebase Performance SDK
- Real User Monitoring (RUM)
- Core Web Vitals tracking
- Bundle analysis on builds

## 🔒 Security Considerations

### Content Security Policy
- Strict CSP headers in `next.config.js`
- Allowed domains for external resources
- Inline script restrictions

### Data Protection
- **PII Encryption**: Sensitive user data encrypted
- **Access Logs**: Admin actions logged and monitored
- **Rate Limiting**: API endpoints protected
- **Input Validation**: All user inputs validated

### Authentication & Authorization
- Firebase Auth for user management
- Role-based access control (RBAC)
- Session management and timeouts
- Multi-factor authentication support

## 📈 Analytics & Monitoring

### Business Metrics
- User engagement and retention
- Conversion rates and revenue
- Gamification participation
- Raffle entry patterns

### Technical Metrics
- Application performance
- Error rates and types
- API response times
- Database query performance

### Monitoring Stack
- Firebase Analytics for user behavior
- Google Analytics for business metrics
- Sentry for error tracking
- Firebase Performance for technical metrics

## 🎯 Success Criteria

### Technical KPIs
- **Uptime**: 99.9% availability
- **Performance**: < 3s page load times
- **Quality**: 90%+ test coverage
- **Security**: Zero critical vulnerabilities

### Business KPIs
- **User Engagement**: 70%+ monthly active users
- **Conversion**: 5%+ purchase conversion rate
- **Gamification**: 60%+ user participation
- **Customer Satisfaction**: 4.5+ star rating

---

## 📝 Development Guidelines

### Code Standards
- Follow all rules in `CLAUDE.md`
- Use TypeScript strict mode
- Implement proper error boundaries
- Write comprehensive tests
- Document complex logic

### Component Patterns
- Functional components with hooks
- Props validation with TypeScript
- Error handling with try-catch
- Loading states for async operations
- Responsive design mobile-first

### Firebase Best Practices
- Always check service availability
- Implement offline fallbacks
- Use proper error handling
- Optimize query patterns
- Follow security rules

This planning document serves as the single source of truth for the Syndicaps project architecture and should be updated as the project evolves.