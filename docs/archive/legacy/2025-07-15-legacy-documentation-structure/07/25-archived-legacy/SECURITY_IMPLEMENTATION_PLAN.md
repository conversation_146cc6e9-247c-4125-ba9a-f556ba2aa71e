# Security Implementation Plan - Critical Vulnerabilities

## 🚨 Executive Summary

This plan addresses **critical security vulnerabilities** discovered in the Syndicaps codebase that require **immediate attention**. The most severe issues involve XSS vulnerabilities through unsafe HTML injection and improper input sanitization.

**Risk Level**: **CRITICAL** - Production deployment unsafe until fixes implemented

## Critical Security Findings

### 1. XSS Vulnerabilities (CRITICAL - CVE Risk)
**Risk Score**: 9.5/10 | **Estimated Impact**: Complete site compromise

- **9 files** using `dangerouslySetInnerHTML` without sanitization
- **Direct HTML injection** from user-controlled data
- **Admin dashboard exposure** to code injection
- **Content management system** vulnerable to stored XSS

### 2. Type Safety Vulnerabilities (HIGH)
**Risk Score**: 7.5/10 | **Estimated Impact**: Data corruption/injection

- **322+ files** with `any` types bypassing security checks
- **API responses** not properly validated
- **User input** not type-checked before database storage

### 3. Dependency Vulnerabilities (MEDIUM)
**Risk Score**: 6.0/10 | **Estimated Impact**: Supply chain attacks

- **39 unused production dependencies** increasing attack surface
- **Version conflicts** potentially exposing known vulnerabilities

## Immediate Action Required (Next 24 Hours)

### Phase 0: Emergency Response (Day 1)

#### Hour 1-2: Threat Assessment
```bash
# 1. Audit production deployment
git log --oneline -10  # Check recent changes
npm audit                # Check for known vulnerabilities
npm outdated            # Check for security updates

# 2. Check for active exploitation
grep -r "script>" src/   # Look for potential injected scripts
grep -r "javascript:" src/  # Look for javascript: URLs
```

#### Hour 3-4: Immediate Mitigations
```bash
# 1. Apply temporary CSP headers
# Update next.config.js immediately
```

```javascript
// next.config.js - IMMEDIATE MITIGATION
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.firebase.com https://*.googleapis.com; frame-src 'none'; object-src 'none';",
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          }
        ],
      },
    ];
  },
};
```

#### Hour 5-8: Install Security Dependencies
```bash
# Install DOMPurify for HTML sanitization
npm install dompurify
npm install --save-dev @types/dompurify

# Install validation libraries
npm install zod          # Runtime type validation
npm install validator    # Input validation
npm install helmet       # Security headers
```

#### Hour 9-24: Critical Patch Implementation
**Target**: Fix all 9 XSS vulnerabilities

## Phase 1: XSS Vulnerability Fixes (Day 1-2)

### Step 1: Create Sanitization Utility (Priority: CRITICAL)

```typescript
// src/lib/security/sanitization.ts
import DOMPurify from 'dompurify';

export interface SanitizationConfig {
  allowedTags?: string[];
  allowedAttributes?: string[];
  stripScripts?: boolean;
  stripComments?: boolean;
}

export const DEFAULT_SANITIZATION: SanitizationConfig = {
  allowedTags: [
    'p', 'br', 'strong', 'em', 'u', 'i', 'b',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'blockquote',
    'a', 'img', 'code', 'pre'
  ],
  allowedAttributes: ['href', 'src', 'alt', 'title', 'class'],
  stripScripts: true,
  stripComments: true,
};

export const STRICT_SANITIZATION: SanitizationConfig = {
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: ['class'],
  stripScripts: true,
  stripComments: true,
};

export const sanitizeHtml = (
  html: string, 
  config: SanitizationConfig = DEFAULT_SANITIZATION
): string => {
  if (!html || typeof html !== 'string') {
    return '';
  }

  const purifyConfig = {
    ALLOWED_TAGS: config.allowedTags,
    ALLOWED_ATTR: config.allowedAttributes,
    FORBID_SCRIPT: config.stripScripts,
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
    FORBID_ATTR: [
      'onerror', 'onload', 'onclick', 'onmouseover', 'onfocus', 'onblur',
      'onkeypress', 'onkeydown', 'onkeyup', 'onsubmit', 'onreset',
      'onselect', 'onchange', 'onabort', 'onunload'
    ],
    KEEP_CONTENT: false,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_TRUSTED_TYPE: false
  };

  return DOMPurify.sanitize(html, purifyConfig);
};

// Validation functions
export const validateHtmlContent = (content: string): boolean => {
  const sanitized = sanitizeHtml(content, STRICT_SANITIZATION);
  return sanitized.length > 0 && sanitized === content;
};

export const isContentSafe = (content: string): boolean => {
  // Check for obvious script injection attempts
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /data:text\/html/i,
    /vbscript:/i,
    /<iframe/i,
    /<object/i,
    /<embed/i
  ];

  return !dangerousPatterns.some(pattern => pattern.test(content));
};
```

### Step 2: Fix Critical Files (Execute in Order)

#### File 1: `/src/components/content/RichTextEditor.tsx`
**Status**: CRITICAL - Content creation vulnerability

```typescript
// BEFORE (VULNERABLE):
interface ContentBlock {
  content: any; // ❌ XSS RISK
  metadata?: Record<string, any>;
}

const renderContent = (block: ContentBlock) => {
  return <div dangerouslySetInnerHTML={{ __html: block.content }} />; // ❌ CRITICAL XSS
};

// AFTER (SECURE):
import { sanitizeHtml, DEFAULT_SANITIZATION } from '@/lib/security/sanitization';

interface ContentBlock {
  type: 'text' | 'html' | 'markdown';
  content: string;
  metadata?: ContentMetadata;
}

interface ContentMetadata {
  author: string;
  timestamp: Date;
  version: number;
  sanitized: boolean;
}

const SecureContentRenderer: React.FC<{ block: ContentBlock }> = ({ block }) => {
  const [sanitizedContent, setSanitizedContent] = useState<string>('');
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const validateAndSanitize = async () => {
      setIsValidating(true);
      
      try {
        // Validate content first
        if (!isContentSafe(block.content)) {
          console.warn('Potentially dangerous content detected, applying strict sanitization');
        }

        // Sanitize based on content type
        const config = block.type === 'html' ? DEFAULT_SANITIZATION : STRICT_SANITIZATION;
        const sanitized = sanitizeHtml(block.content, config);
        
        setSanitizedContent(sanitized);
      } catch (error) {
        console.error('Content sanitization failed:', error);
        setSanitizedContent('Content could not be displayed safely');
      } finally {
        setIsValidating(false);
      }
    };

    validateAndSanitize();
  }, [block.content, block.type]);

  if (isValidating) {
    return <div className="animate-pulse bg-gray-200 h-4 rounded"></div>;
  }

  return (
    <div 
      className="prose max-w-none"
      dangerouslySetInnerHTML={{ __html: sanitizedContent }}
    />
  );
};
```

#### File 2: `/src/components/search/SearchComponents.tsx`
**Status**: CRITICAL - Search result injection

```typescript
// BEFORE (VULNERABLE):
const renderHighlights = (content: any) => {
  return <div dangerouslySetInnerHTML={{ __html: content }} />; // ❌ CRITICAL XSS
};

// AFTER (SECURE):
import { sanitizeHtml, STRICT_SANITIZATION } from '@/lib/security/sanitization';

interface SearchHighlight {
  text: string;
  isHighlighted: boolean;
}

interface SecureSearchResultProps {
  title: string;
  content: string;
  highlights: SearchHighlight[];
}

const SecureSearchResult: React.FC<SecureSearchResultProps> = ({ 
  title, 
  content, 
  highlights 
}) => {
  const sanitizedTitle = sanitizeHtml(title, STRICT_SANITIZATION);
  const sanitizedContent = sanitizeHtml(content, STRICT_SANITIZATION);

  return (
    <div className="search-result">
      <h3 dangerouslySetInnerHTML={{ __html: sanitizedTitle }} />
      <p dangerouslySetInnerHTML={{ __html: sanitizedContent }} />
      <div className="highlights">
        {highlights.map((highlight, index) => (
          <span 
            key={index}
            className={highlight.isHighlighted ? 'bg-yellow-200' : ''}
          >
            {highlight.text} {/* Safe - no HTML */}
          </span>
        ))}
      </div>
    </div>
  );
};
```

#### File 3: `/src/admin/components/search/SearchResultsPage.tsx`
**Status**: CRITICAL - Admin panel vulnerability

```typescript
// BEFORE (VULNERABLE):
const SearchResult = ({ result }: { result: any }) => {
  return (
    <div dangerouslySetInnerHTML={{ __html: result.highlightedContent }} /> // ❌ ADMIN XSS
  );
};

// AFTER (SECURE):
import { sanitizeHtml, DEFAULT_SANITIZATION, isContentSafe } from '@/lib/security/sanitization';
import { useAuth } from '@/hooks/useAuth';

interface AdminSearchResult {
  id: string;
  type: 'user' | 'content' | 'product' | 'order';
  title: string;
  content: string;
  highlightedContent?: string;
  metadata: {
    createdAt: Date;
    author?: string;
    status: string;
  };
}

const SecureAdminSearchResult: React.FC<{ result: AdminSearchResult }> = ({ result }) => {
  const { user } = useAuth();
  const [sanitizedContent, setSanitizedContent] = useState<string>('');
  const [securityWarning, setSecurityWarning] = useState<string | null>(null);

  useEffect(() => {
    const content = result.highlightedContent || result.content;
    
    // Admin-level security check
    if (!isContentSafe(content)) {
      setSecurityWarning('⚠️ This content contains potentially unsafe elements');
    }

    // Sanitize with admin-appropriate config
    const sanitized = sanitizeHtml(content, {
      ...DEFAULT_SANITIZATION,
      allowedTags: [...DEFAULT_SANITIZATION.allowedTags!, 'mark', 'span'], // Allow highlights
      allowedAttributes: [...DEFAULT_SANITIZATION.allowedAttributes!, 'data-highlight']
    });

    setSanitizedContent(sanitized);
  }, [result]);

  return (
    <div className="admin-search-result border rounded-lg p-4">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-semibold">{result.title}</h3>
        <span className="text-sm text-gray-500">{result.type}</span>
      </div>
      
      {securityWarning && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded mb-2 text-sm">
          {securityWarning}
        </div>
      )}
      
      <div 
        className="prose prose-sm max-w-none"
        dangerouslySetInnerHTML={{ __html: sanitizedContent }}
      />
      
      <div className="mt-2 text-xs text-gray-500">
        Created: {result.metadata.createdAt.toLocaleDateString()}
        {result.metadata.author && ` | Author: ${result.metadata.author}`}
      </div>
    </div>
  );
};
```

#### File 4-9: Remaining Vulnerable Files
**Apply similar pattern to**:
- `/src/components/content/ContentPreview.tsx`
- `/app/admin/blog/edit/[id]/page.tsx`
- `/app/admin/blog/create/page.tsx`
- `/app/blog/[slug]/page.tsx`
- Any other files with `dangerouslySetInnerHTML`

### Step 3: Input Validation Layer

```typescript
// src/lib/security/input-validation.ts
import { z } from 'zod';
import validator from 'validator';

// Content validation schemas
export const ContentSchema = z.object({
  type: z.enum(['text', 'html', 'markdown']),
  content: z.string()
    .min(1, 'Content cannot be empty')
    .max(50000, 'Content too large')
    .refine((content) => {
      // Check for script injection
      return !/<script/i.test(content);
    }, 'Script tags not allowed')
    .refine((content) => {
      // Check for javascript URLs
      return !/javascript:/i.test(content);
    }, 'JavaScript URLs not allowed'),
  metadata: z.object({
    author: z.string().min(1),
    timestamp: z.date(),
    version: z.number().positive(),
  }),
});

export const UserInputSchema = z.object({
  name: z.string()
    .min(1, 'Name required')
    .max(100, 'Name too long')
    .refine((name) => validator.isAlphanumeric(name.replace(/\s/g, '')), 'Invalid characters'),
  email: z.string().email('Invalid email format'),
  content: z.string()
    .max(10000, 'Content too long')
    .refine((content) => isContentSafe(content), 'Content contains unsafe elements'),
});

// API validation middleware
export const validateInput = <T>(schema: z.ZodSchema<T>) => {
  return (data: unknown): T => {
    try {
      return schema.parse(data);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  };
};
```

## Phase 2: API Security Hardening (Day 2-3)

### Step 1: Request Validation Middleware

```typescript
// src/middleware/security.ts
import { NextRequest, NextResponse } from 'next/server';
import rateLimit from 'express-rate-limit';

// Rate limiting configuration
const createRateLimiter = (windowMs: number, max: number) => {
  return rateLimit({
    windowMs,
    max,
    message: 'Too many requests from this IP',
    standardHeaders: true,
    legacyHeaders: false,
  });
};

export const rateLimiters = {
  general: createRateLimiter(15 * 60 * 1000, 100), // 100 requests per 15 minutes
  auth: createRateLimiter(15 * 60 * 1000, 5),      // 5 auth requests per 15 minutes
  api: createRateLimiter(1 * 60 * 1000, 30),       // 30 API requests per minute
};

// Security headers middleware
export async function securityMiddleware(request: NextRequest) {
  const response = NextResponse.next();

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // CSRF protection
  const origin = request.headers.get('origin');
  const host = request.headers.get('host');
  
  if (request.method !== 'GET' && origin && !origin.includes(host!)) {
    return NextResponse.json({ error: 'CSRF attack detected' }, { status: 403 });
  }

  return response;
}

// Input sanitization for API routes
export const sanitizeApiInput = (input: any): any => {
  if (typeof input === 'string') {
    return validator.escape(input);
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeApiInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[validator.escape(key)] = sanitizeApiInput(value);
    }
    return sanitized;
  }
  
  return input;
};
```

### Step 2: Secure API Route Template

```typescript
// src/lib/api/secure-route.ts
import { NextRequest, NextResponse } from 'next/server';
import { validateInput } from '@/lib/security/input-validation';
import { sanitizeApiInput } from '@/middleware/security';
import { z } from 'zod';

interface SecureRouteConfig<TInput, TOutput> {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  schema?: z.ZodSchema<TInput>;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  rateLimit?: 'general' | 'auth' | 'api';
}

export function createSecureRoute<TInput = any, TOutput = any>(
  config: SecureRouteConfig<TInput, TOutput>,
  handler: (data: TInput, request: NextRequest) => Promise<TOutput>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Method validation
      if (request.method !== config.method) {
        return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
      }

      // Rate limiting (implement based on config.rateLimit)
      
      // Authentication check
      if (config.requireAuth) {
        const authResult = await validateAuthentication(request);
        if (!authResult.valid) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
        
        if (config.requireAdmin && !authResult.isAdmin) {
          return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
        }
      }

      // Input validation and sanitization
      let validatedData: TInput;
      
      if (config.schema) {
        const rawData = await request.json();
        const sanitizedData = sanitizeApiInput(rawData);
        validatedData = validateInput(config.schema)(sanitizedData);
      } else {
        validatedData = {} as TInput;
      }

      // Execute handler
      const result = await handler(validatedData, request);

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('API route error:', error);
      
      return NextResponse.json({
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Internal server error',
          code: 'INTERNAL_ERROR',
        },
        timestamp: new Date().toISOString(),
      }, { status: 500 });
    }
  };
}

// Usage example
export const POST = createSecureRoute(
  {
    method: 'POST',
    schema: UserInputSchema,
    requireAuth: true,
    rateLimit: 'api',
  },
  async (data, request) => {
    // Safe handler implementation
    return { message: 'Success', id: 'user-123' };
  }
);
```

## Phase 3: Dependency Security (Day 3-4)

### Step 1: Immediate Dependency Audit

```bash
# Run comprehensive security audit
npm audit --audit-level moderate
npm audit fix --force

# Check for outdated packages with security issues
npm outdated

# Remove unused dependencies (identified earlier)
npm uninstall @google/generative-ai @paypal/checkout-server-sdk prisma quill react-confetti-explosion

# Update critical security packages
npm update next react react-dom firebase
```

### Step 2: Dependency Security Monitoring

```json
// package.json - Add security scripts
{
  "scripts": {
    "security:audit": "npm audit --audit-level moderate",
    "security:fix": "npm audit fix",
    "security:check": "npm-check-updates --target minor",
    "security:update": "npm update && npm audit fix"
  },
  "dependencies": {
    "helmet": "^7.1.0",
    "dompurify": "^3.0.5",
    "zod": "^3.22.4",
    "validator": "^13.11.0"
  }
}
```

## Phase 4: Authentication & Authorization Security (Day 4-5)

### Step 1: Secure Authentication Flow

```typescript
// src/lib/security/auth-security.ts
import { signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { z } from 'zod';
import validator from 'validator';

const AuthInputSchema = z.object({
  email: z.string().email().refine((email) => validator.isEmail(email)),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           'Password must contain uppercase, lowercase, number, and special character'),
});

interface SecureAuthResult {
  success: boolean;
  user?: any;
  error?: {
    code: string;
    message: string;
    attempts?: number;
  };
}

// Rate limiting for auth attempts
const authAttempts = new Map<string, { count: number; lastAttempt: Date }>();

const isRateLimited = (identifier: string): boolean => {
  const attempts = authAttempts.get(identifier);
  if (!attempts) return false;
  
  const now = new Date();
  const timeDiff = now.getTime() - attempts.lastAttempt.getTime();
  
  // Reset after 15 minutes
  if (timeDiff > 15 * 60 * 1000) {
    authAttempts.delete(identifier);
    return false;
  }
  
  return attempts.count >= 5; // Max 5 attempts per 15 minutes
};

const recordAuthAttempt = (identifier: string): void => {
  const existing = authAttempts.get(identifier) || { count: 0, lastAttempt: new Date() };
  authAttempts.set(identifier, {
    count: existing.count + 1,
    lastAttempt: new Date(),
  });
};

export const secureSignIn = async (
  email: string, 
  password: string, 
  ipAddress: string
): Promise<SecureAuthResult> => {
  try {
    // Rate limiting check
    const identifier = `${email}:${ipAddress}`;
    if (isRateLimited(identifier)) {
      return {
        success: false,
        error: {
          code: 'RATE_LIMITED',
          message: 'Too many failed attempts. Please try again later.',
          attempts: authAttempts.get(identifier)?.count || 0,
        },
      };
    }

    // Input validation
    const validatedInput = AuthInputSchema.parse({ email, password });

    // Attempt authentication
    const userCredential = await signInWithEmailAndPassword(
      auth, 
      validatedInput.email, 
      validatedInput.password
    );

    // Clear rate limiting on success
    authAttempts.delete(identifier);

    // Log successful authentication
    console.log(`✅ Successful login: ${email} from ${ipAddress}`);

    return {
      success: true,
      user: userCredential.user,
    };

  } catch (error: any) {
    // Record failed attempt
    recordAuthAttempt(`${email}:${ipAddress}`);

    // Log security event
    console.warn(`❌ Failed login attempt: ${email} from ${ipAddress}`);

    return {
      success: false,
      error: {
        code: error.code || 'AUTH_ERROR',
        message: 'Invalid email or password',
        attempts: authAttempts.get(`${email}:${ipAddress}`)?.count || 1,
      },
    };
  }
};
```

### Step 2: Session Security

```typescript
// src/lib/security/session-security.ts
import { NextRequest } from 'next/server';
import { auth } from '@/lib/firebase-admin';

interface SessionValidationResult {
  valid: boolean;
  user?: {
    uid: string;
    email: string;
    role: string;
  };
  error?: string;
}

export const validateSession = async (request: NextRequest): Promise<SessionValidationResult> => {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { valid: false, error: 'No valid authorization header' };
    }

    const token = authHeader.split('Bearer ')[1];
    if (!token) {
      return { valid: false, error: 'No token provided' };
    }

    // Verify Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    
    // Additional security checks
    const now = Date.now() / 1000;
    if (decodedToken.exp < now) {
      return { valid: false, error: 'Token expired' };
    }

    if (decodedToken.iat > now) {
      return { valid: false, error: 'Token issued in future' };
    }

    return {
      valid: true,
      user: {
        uid: decodedToken.uid,
        email: decodedToken.email || '',
        role: decodedToken.role || 'user',
      },
    };

  } catch (error) {
    console.error('Session validation error:', error);
    return { valid: false, error: 'Invalid token' };
  }
};
```

## Monitoring & Detection

### Step 1: Security Event Logging

```typescript
// src/lib/security/security-logger.ts
interface SecurityEvent {
  type: 'XSS_ATTEMPT' | 'AUTH_FAILURE' | 'RATE_LIMIT' | 'CSRF_ATTEMPT' | 'INJECTION_ATTEMPT';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress: string;
  userAgent: string;
  details: Record<string, any>;
  timestamp: Date;
}

export const logSecurityEvent = async (event: SecurityEvent): Promise<void> => {
  // Log to console (development)
  console.warn(`🚨 SECURITY EVENT [${event.severity.toUpperCase()}]: ${event.type}`, event);

  // Log to external service (production)
  if (process.env.NODE_ENV === 'production') {
    try {
      // Send to security monitoring service
      await fetch(process.env.SECURITY_WEBHOOK_URL!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...event,
          environment: 'production',
          application: 'syndicaps',
        }),
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  // Store in database for analysis
  try {
    await db.collection('security_events').add(event);
  } catch (error) {
    console.error('Failed to store security event:', error);
  }
};

// Usage examples
export const detectXSSAttempt = (content: string, context: string): void => {
  if (!isContentSafe(content)) {
    logSecurityEvent({
      type: 'XSS_ATTEMPT',
      severity: 'high',
      ipAddress: 'unknown', // Extract from request
      userAgent: 'unknown', // Extract from request
      details: {
        content: content.substring(0, 500), // Truncate for logging
        context,
        detectedPatterns: getDangerousPatterns(content),
      },
      timestamp: new Date(),
    });
  }
};
```

### Step 2: Real-time Monitoring Dashboard

```typescript
// src/components/admin/SecurityDashboard.tsx
export const SecurityDashboard: React.FC = () => {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);

  useEffect(() => {
    // Real-time monitoring of security events
    const unsubscribe = onSnapshot(
      query(
        collection(db, 'security_events'),
        where('timestamp', '>=', new Date(Date.now() - 24 * 60 * 60 * 1000)),
        orderBy('timestamp', 'desc')
      ),
      (snapshot) => {
        const events = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        setSecurityEvents(events);
        
        // Check for critical events
        const criticalEvents = events.filter(e => e.severity === 'critical');
        if (criticalEvents.length > 0) {
          setAlerts(prev => [...prev, ...criticalEvents.map(createAlert)]);
        }
      }
    );

    return unsubscribe;
  }, []);

  return (
    <div className="security-dashboard">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <SecurityMetricCard
          title="XSS Attempts (24h)"
          value={securityEvents.filter(e => e.type === 'XSS_ATTEMPT').length}
          status="warning"
        />
        <SecurityMetricCard
          title="Auth Failures (24h)"
          value={securityEvents.filter(e => e.type === 'AUTH_FAILURE').length}
          status="info"
        />
        <SecurityMetricCard
          title="Critical Events"
          value={securityEvents.filter(e => e.severity === 'critical').length}
          status="critical"
        />
      </div>

      <SecurityEventsList events={securityEvents} />
      <SecurityAlerts alerts={alerts} onDismiss={dismissAlert} />
    </div>
  );
};
```

## Testing & Validation

### Step 1: Security Test Suite

```typescript
// src/__tests__/security/xss-protection.test.ts
import { sanitizeHtml, isContentSafe } from '@/lib/security/sanitization';

describe('XSS Protection', () => {
  test('blocks script injection', () => {
    const maliciousContent = '<script>alert("XSS")</script><p>Safe content</p>';
    const sanitized = sanitizeHtml(maliciousContent);
    
    expect(sanitized).not.toContain('<script>');
    expect(sanitized).toContain('<p>Safe content</p>');
  });

  test('blocks event handler injection', () => {
    const maliciousContent = '<img src="x" onerror="alert(\'XSS\')" />';
    const sanitized = sanitizeHtml(maliciousContent);
    
    expect(sanitized).not.toContain('onerror');
    expect(sanitized).toMatch(/<img[^>]*src="x"[^>]*>/);
  });

  test('blocks javascript URLs', () => {
    const maliciousContent = '<a href="javascript:alert(\'XSS\')">Click me</a>';
    const sanitized = sanitizeHtml(maliciousContent);
    
    expect(sanitized).not.toContain('javascript:');
  });

  test('content safety detection', () => {
    expect(isContentSafe('<p>Safe content</p>')).toBe(true);
    expect(isContentSafe('<script>alert("XSS")</script>')).toBe(false);
    expect(isContentSafe('<img onerror="alert(1)" src="x">')).toBe(false);
  });
});
```

### Step 2: Penetration Testing Checklist

```typescript
// scripts/security-tests.ts
export const runSecurityTests = async () => {
  const tests = [
    {
      name: 'XSS Protection',
      test: testXSSProtection,
      critical: true,
    },
    {
      name: 'CSRF Protection',
      test: testCSRFProtection,
      critical: true,
    },
    {
      name: 'Authentication Security',
      test: testAuthSecurity,
      critical: true,
    },
    {
      name: 'Input Validation',
      test: testInputValidation,
      critical: false,
    },
    {
      name: 'Rate Limiting',
      test: testRateLimiting,
      critical: false,
    },
  ];

  console.log('🔒 Running security tests...\n');

  for (const test of tests) {
    try {
      const result = await test.test();
      const status = result.passed ? '✅' : '❌';
      const severity = test.critical ? '[CRITICAL]' : '[INFO]';
      
      console.log(`${status} ${severity} ${test.name}: ${result.message}`);
      
      if (!result.passed && test.critical) {
        console.error(`🚨 CRITICAL SECURITY TEST FAILED: ${test.name}`);
        process.exit(1);
      }
    } catch (error) {
      console.error(`💥 Security test error [${test.name}]:`, error);
      if (test.critical) {
        process.exit(1);
      }
    }
  }

  console.log('\n🎉 Security tests completed!');
};
```

## Deployment Security

### Step 1: Pre-deployment Checklist

```bash
#!/bin/bash
# scripts/pre-deployment-security-check.sh

echo "🔒 Running pre-deployment security checks..."

# 1. Run security tests
npm run security:test
if [ $? -ne 0 ]; then
  echo "❌ Security tests failed"
  exit 1
fi

# 2. Check for XSS vulnerabilities
if grep -r "dangerouslySetInnerHTML" src/ | grep -v "sanitizeHtml"; then
  echo "❌ Found unsanitized dangerouslySetInnerHTML usage"
  exit 1
fi

# 3. Check for any types in security-critical files
if grep -r ": any" src/lib/security/ src/lib/auth.ts; then
  echo "❌ Found 'any' types in security-critical files"
  exit 1
fi

# 4. Audit dependencies
npm audit --audit-level moderate
if [ $? -ne 0 ]; then
  echo "❌ Dependency security issues found"
  exit 1
fi

# 5. Check environment variables
if [ -z "$FIREBASE_ADMIN_PRIVATE_KEY" ]; then
  echo "❌ Missing required environment variables"
  exit 1
fi

echo "✅ All security checks passed!"
```

### Step 2: Production Security Configuration

```typescript
// next.config.js - Production security
const nextConfig = {
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com",
              "img-src 'self' data: https:",
              "connect-src 'self' https://*.firebase.com https://*.googleapis.com",
              "frame-src 'none'",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
            ].join('; '),
          },
        ],
      },
    ];
  },

  // Disable x-powered-by header
  poweredByHeader: false,

  // Enable SWC minification for security
  swcMinify: true,

  // Strict mode
  reactStrictMode: true,
};
```

## Timeline & Success Criteria

### Phase 0: Emergency (24 hours)
- [ ] CSP headers deployed
- [ ] DOMPurify installed
- [ ] All 9 XSS vulnerabilities patched

### Phase 1: Critical Fixes (48 hours)
- [ ] All HTML injection points secured
- [ ] Input validation implemented
- [ ] Security logging active

### Phase 2: API Security (72 hours)
- [ ] API routes secured with validation
- [ ] Rate limiting implemented
- [ ] Authentication hardened

### Phase 3: Dependencies (96 hours)
- [ ] Unused dependencies removed
- [ ] Security audit clean
- [ ] Monitoring dashboard deployed

### Success Criteria
- [ ] **Zero** XSS vulnerabilities
- [ ] **Zero** critical dependency vulnerabilities
- [ ] **100%** API routes with input validation
- [ ] **Real-time** security monitoring active
- [ ] **Automated** security testing in CI/CD

## Post-Implementation Monitoring

### Daily Security Checks
- Review security event logs
- Monitor authentication failure rates
- Check for new XSS attempts
- Validate CSP violation reports

### Weekly Security Reviews
- Dependency security audit
- Review failed security tests
- Analyze attack patterns
- Update security policies

### Monthly Security Assessments
- Penetration testing
- Code security review
- Security training updates
- Incident response testing

---

**⚠️ CRITICAL REMINDER**: This plan addresses **immediate security threats** that could compromise user data and system integrity. Implementation should begin **immediately** with Phase 0 emergency response measures.