# Syndicaps Gamification API Reference

## Overview

The Syndicaps Gamification API provides comprehensive access to the gamification system, including points management, achievements, rewards, and user progress tracking. This reference covers all available endpoints, data models, and integration patterns.

## Table of Contents

1. [Authentication](#authentication)
2. [Data Models](#data-models)
3. [Points API](#points-api)
4. [Achievements API](#achievements-api)
5. [Rewards API](#rewards-api)
6. [User Progress API](#user-progress-api)
7. [Leaderboards API](#leaderboards-api)
8. [Analytics API](#analytics-api)
9. [Webhooks](#webhooks)
10. [SDKs and Libraries](#sdks-and-libraries)
11. [Rate Limiting](#rate-limiting)
12. [Error Handling](#error-handling)

## Authentication

### Firebase Authentication

All API endpoints require Firebase Authentication tokens:

```javascript
// Client-side authentication
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth'

const auth = getAuth()
const userCredential = await signInWithEmailAndPassword(auth, email, password)
const idToken = await userCredential.user.getIdToken()

// Include token in API requests
const response = await fetch('/api/gamification/points', {
  headers: {
    'Authorization': `Bearer ${idToken}`,
    'Content-Type': 'application/json'
  }
})
```

### Admin Authentication

Admin endpoints require additional role verification:

```javascript
// Admin API requests
const response = await fetch('/api/admin/gamification/users', {
  headers: {
    'Authorization': `Bearer ${adminIdToken}`,
    'X-Admin-Role': 'content_admin',
    'Content-Type': 'application/json'
  }
})
```

## Data Models

### User Profile

```typescript
interface UserProfile {
  uid: string
  email: string
  displayName?: string
  points: number
  totalPointsEarned: number
  totalPointsSpent: number
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
  achievements: string[]
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### Point Transaction

```typescript
interface PointTransaction {
  id: string
  userId: string
  type: 'earned' | 'spent' | 'bonus' | 'refund'
  amount: number
  source: string
  description: string
  metadata?: Record<string, any>
  createdAt: Timestamp
}
```

### Achievement

```typescript
interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'purchase' | 'social' | 'milestone' | 'special'
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  pointsAwarded: number
  requirements: AchievementRequirement[]
  isActive: boolean
  createdAt: Timestamp
}

interface AchievementRequirement {
  type: 'count' | 'amount' | 'streak' | 'time'
  target: number
  field?: string
}
```

### User Achievement

```typescript
interface UserAchievement {
  id: string
  userId: string
  achievementId: string
  unlockedAt: Timestamp
  progress?: number
  metadata?: Record<string, any>
}
```

### Reward

```typescript
interface Reward {
  id: string
  name: string
  description: string
  icon: string
  category: 'discount' | 'physical' | 'digital' | 'access'
  pointsCost: number
  stock?: number
  isActive: boolean
  metadata?: Record<string, any>
  createdAt: Timestamp
}
```

### Reward Purchase

```typescript
interface RewardPurchase {
  id: string
  userId: string
  rewardId: string
  pointsCost: number
  status: 'pending' | 'fulfilled' | 'cancelled'
  purchasedAt: Timestamp
  fulfilledAt?: Timestamp
  metadata?: Record<string, any>
}
```

## Points API

### Get User Points

```http
GET /api/gamification/points
Authorization: Bearer <firebase_id_token>
```

**Response:**
```json
{
  "balance": 1250,
  "totalEarned": 3500,
  "totalSpent": 2250,
  "tier": "silver"
}
```

### Get Point History

```http
GET /api/gamification/points/history?limit=50&offset=0
Authorization: Bearer <firebase_id_token>
```

**Response:**
```json
{
  "transactions": [
    {
      "id": "txn_123",
      "type": "earned",
      "amount": 100,
      "source": "purchase",
      "description": "Order #12345",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "hasMore": true,
  "total": 125
}
```

### Award Points (Admin Only)

```http
POST /api/admin/gamification/points/award
Authorization: Bearer <admin_id_token>
X-Admin-Role: content_admin
Content-Type: application/json

{
  "userId": "user_123",
  "amount": 500,
  "source": "admin_bonus",
  "description": "Customer service compensation",
  "metadata": {
    "ticketId": "TICKET-456",
    "adminId": "admin_789"
  }
}
```

**Response:**
```json
{
  "success": true,
  "transactionId": "txn_456",
  "newBalance": 1750
}
```

## Achievements API

### Get All Achievements

```http
GET /api/gamification/achievements
```

**Response:**
```json
{
  "achievements": [
    {
      "id": "first_purchase",
      "name": "First Purchase",
      "description": "Made your first purchase on Syndicaps!",
      "icon": "🛒",
      "category": "purchase",
      "rarity": "common",
      "pointsAwarded": 100,
      "requirements": [
        {
          "type": "count",
          "target": 1,
          "field": "purchases"
        }
      ]
    }
  ]
}
```

### Get User Achievements

```http
GET /api/gamification/achievements/user
Authorization: Bearer <firebase_id_token>
```

**Response:**
```json
{
  "achievements": [
    {
      "id": "ach_123",
      "achievementId": "first_purchase",
      "unlockedAt": "2024-01-15T10:30:00Z",
      "pointsAwarded": 100
    }
  ],
  "progress": [
    {
      "achievementId": "big_spender",
      "currentProgress": 75,
      "targetProgress": 100,
      "percentage": 75
    }
  ]
}
```

### Check Achievement Progress

```http
POST /api/gamification/achievements/check
Authorization: Bearer <firebase_id_token>
Content-Type: application/json

{
  "action": "purchase",
  "metadata": {
    "orderId": "order_123",
    "amount": 150,
    "items": ["item1", "item2"]
  }
}
```

**Response:**
```json
{
  "newAchievements": [
    {
      "id": "big_spender",
      "name": "Big Spender",
      "pointsAwarded": 250
    }
  ],
  "updatedProgress": [
    {
      "achievementId": "loyal_customer",
      "progress": 3,
      "target": 10
    }
  ]
}
```

## Rewards API

### Get Available Rewards

```http
GET /api/gamification/rewards?category=discount&limit=20
```

**Response:**
```json
{
  "rewards": [
    {
      "id": "discount_10",
      "name": "10% Discount Coupon",
      "description": "Get 10% off your next purchase",
      "icon": "🎫",
      "category": "discount",
      "pointsCost": 500,
      "stock": 100,
      "metadata": {
        "discountPercent": 10,
        "validDays": 30
      }
    }
  ]
}
```

### Purchase Reward

```http
POST /api/gamification/rewards/purchase
Authorization: Bearer <firebase_id_token>
Content-Type: application/json

{
  "rewardId": "discount_10",
  "metadata": {
    "deliveryEmail": "<EMAIL>"
  }
}
```

**Response:**
```json
{
  "success": true,
  "purchaseId": "purchase_123",
  "pointsSpent": 500,
  "newBalance": 750,
  "fulfillment": {
    "method": "email",
    "estimatedDelivery": "immediate",
    "trackingInfo": "COUPON-ABC123"
  }
}
```

### Get User Reward Purchases

```http
GET /api/gamification/rewards/purchases
Authorization: Bearer <firebase_id_token>
```

**Response:**
```json
{
  "purchases": [
    {
      "id": "purchase_123",
      "rewardId": "discount_10",
      "rewardName": "10% Discount Coupon",
      "pointsCost": 500,
      "status": "fulfilled",
      "purchasedAt": "2024-01-15T10:30:00Z",
      "fulfilledAt": "2024-01-15T10:31:00Z"
    }
  ]
}
```

## User Progress API

### Get User Stats

```http
GET /api/gamification/user/stats
Authorization: Bearer <firebase_id_token>
```

**Response:**
```json
{
  "points": {
    "current": 1250,
    "totalEarned": 3500,
    "totalSpent": 2250
  },
  "tier": {
    "current": "silver",
    "progress": 1250,
    "nextTier": "gold",
    "nextTierThreshold": 5000,
    "progressPercentage": 25
  },
  "achievements": {
    "total": 45,
    "unlocked": 12,
    "completionPercentage": 26.7,
    "byRarity": {
      "common": { "total": 20, "unlocked": 8 },
      "rare": { "total": 15, "unlocked": 3 },
      "epic": { "total": 8, "unlocked": 1 },
      "legendary": { "total": 2, "unlocked": 0 }
    }
  },
  "activity": {
    "lastLogin": "2024-01-15T10:30:00Z",
    "totalLogins": 45,
    "streak": 7,
    "totalPurchases": 12,
    "totalReviews": 8
  }
}
```

### Get Activity Timeline

```http
GET /api/gamification/user/activity?limit=20
Authorization: Bearer <firebase_id_token>
```

**Response:**
```json
{
  "activities": [
    {
      "id": "activity_123",
      "type": "achievement",
      "action": "unlocked",
      "description": "Unlocked 'Big Spender' achievement",
      "pointsEarned": 250,
      "createdAt": "2024-01-15T10:30:00Z",
      "metadata": {
        "achievementId": "big_spender",
        "achievementName": "Big Spender"
      }
    }
  ]
}
```

## Leaderboards API

### Get Points Leaderboard

```http
GET /api/gamification/leaderboards/points?limit=10&period=monthly
```

**Response:**
```json
{
  "leaderboard": [
    {
      "rank": 1,
      "userId": "user_123",
      "displayName": "KeycapMaster",
      "points": 15750,
      "tier": "platinum",
      "avatar": "https://example.com/avatar.jpg"
    }
  ],
  "userRank": {
    "rank": 45,
    "points": 1250,
    "percentile": 75
  },
  "period": "monthly",
  "totalParticipants": 1250
}
```

### Get Achievement Leaderboard

```http
GET /api/gamification/leaderboards/achievements?limit=10
```

**Response:**
```json
{
  "leaderboard": [
    {
      "rank": 1,
      "userId": "user_456",
      "displayName": "AchievementHunter",
      "achievementCount": 42,
      "completionPercentage": 93.3,
      "rareAchievements": 15
    }
  ]
}
```

## Analytics API (Admin Only)

### Get System Overview

```http
GET /api/admin/gamification/analytics/overview
Authorization: Bearer <admin_id_token>
X-Admin-Role: analytics_admin
```

**Response:**
```json
{
  "users": {
    "total": 10500,
    "active": 3200,
    "engaged": 1800
  },
  "points": {
    "totalEarned": 2500000,
    "totalSpent": 1200000,
    "averageBalance": 238
  },
  "achievements": {
    "totalUnlocks": 45000,
    "averagePerUser": 4.3,
    "popularAchievements": [...]
  },
  "rewards": {
    "totalRedemptions": 8500,
    "totalValue": 425000,
    "popularRewards": [...]
  }
}
```

### Get User Engagement Metrics

```http
GET /api/admin/gamification/analytics/engagement?period=30d
Authorization: Bearer <admin_id_token>
X-Admin-Role: analytics_admin
```

**Response:**
```json
{
  "dailyActiveUsers": [
    { "date": "2024-01-15", "count": 1250 }
  ],
  "pointsEarned": [
    { "date": "2024-01-15", "total": 15000 }
  ],
  "achievementUnlocks": [
    { "date": "2024-01-15", "count": 85 }
  ],
  "rewardRedemptions": [
    { "date": "2024-01-15", "count": 25 }
  ]
}
```

## Webhooks

### Webhook Events

The gamification system can send webhooks for various events:

#### Achievement Unlocked
```json
{
  "event": "achievement.unlocked",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "userId": "user_123",
    "achievementId": "big_spender",
    "achievementName": "Big Spender",
    "pointsAwarded": 250
  }
}
```

#### Reward Purchased
```json
{
  "event": "reward.purchased",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "userId": "user_123",
    "rewardId": "discount_10",
    "rewardName": "10% Discount Coupon",
    "pointsCost": 500,
    "purchaseId": "purchase_123"
  }
}
```

#### Tier Changed
```json
{
  "event": "tier.changed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "userId": "user_123",
    "previousTier": "silver",
    "newTier": "gold",
    "totalPoints": 5000
  }
}
```

### Webhook Configuration

```http
POST /api/admin/webhooks
Authorization: Bearer <admin_id_token>
X-Admin-Role: super_admin
Content-Type: application/json

{
  "url": "https://your-app.com/webhooks/gamification",
  "events": ["achievement.unlocked", "reward.purchased", "tier.changed"],
  "secret": "your_webhook_secret"
}
```

## SDKs and Libraries

### JavaScript/TypeScript SDK

```bash
npm install @syndicaps/gamification-sdk
```

```typescript
import { GamificationSDK } from '@syndicaps/gamification-sdk'

const sdk = new GamificationSDK({
  apiKey: 'your_api_key',
  environment: 'production'
})

// Get user points
const points = await sdk.points.getBalance()

// Check achievements
const achievements = await sdk.achievements.checkProgress({
  action: 'purchase',
  metadata: { amount: 100 }
})

// Purchase reward
const purchase = await sdk.rewards.purchase('discount_10')
```

### React Hooks

```typescript
import { usePoints, useAchievements, useRewards } from '@syndicaps/gamification-react'

function GamificationDashboard() {
  const { balance, history, award } = usePoints()
  const { achievements, unlock } = useAchievements()
  const { rewards, purchase } = useRewards()

  return (
    <div>
      <h2>Points: {balance}</h2>
      <AchievementList achievements={achievements} />
      <RewardShop rewards={rewards} onPurchase={purchase} />
    </div>
  )
}
```

## Rate Limiting

### Standard Limits

- **User Endpoints**: 1000 requests per hour per user
- **Public Endpoints**: 10000 requests per hour per IP
- **Admin Endpoints**: 5000 requests per hour per admin

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

### Rate Limit Response

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again in 3600 seconds.",
    "retryAfter": 3600
  }
}
```

## Error Handling

### Standard Error Format

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "Additional error details"
    },
    "requestId": "req_123456789"
  }
}
```

### Common Error Codes

- `AUTHENTICATION_REQUIRED`: Missing or invalid authentication token
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `INSUFFICIENT_POINTS`: User doesn't have enough points for operation
- `ACHIEVEMENT_ALREADY_UNLOCKED`: Achievement already unlocked by user
- `REWARD_OUT_OF_STOCK`: Requested reward is out of stock
- `RATE_LIMIT_EXCEEDED`: API rate limit exceeded
- `VALIDATION_ERROR`: Request data validation failed
- `INTERNAL_ERROR`: Internal server error

### Error Handling Best Practices

```typescript
try {
  const result = await sdk.rewards.purchase('discount_10')
} catch (error) {
  switch (error.code) {
    case 'INSUFFICIENT_POINTS':
      showMessage('You need more points for this reward')
      break
    case 'REWARD_OUT_OF_STOCK':
      showMessage('This reward is currently out of stock')
      break
    default:
      showMessage('Something went wrong. Please try again.')
  }
}
```

## Testing

### Test Environment

Use the test environment for development and testing:

```typescript
const sdk = new GamificationSDK({
  apiKey: 'test_api_key',
  environment: 'test',
  baseUrl: 'https://test-api.syndicaps.com'
})
```

### Mock Data

Test endpoints provide mock data for development:

```http
GET /api/test/gamification/mock-achievements
GET /api/test/gamification/mock-rewards
GET /api/test/gamification/mock-users
```

### Integration Testing

```typescript
// Example integration test
describe('Gamification API', () => {
  test('should award points for purchase', async () => {
    const result = await sdk.points.award({
      amount: 100,
      source: 'purchase',
      description: 'Test order'
    })

    expect(result.success).toBe(true)
    expect(result.newBalance).toBeGreaterThan(0)
  })
})
```

---

**For additional support or questions, contact the API <NAME_EMAIL>**

*Last updated: December 2024*
