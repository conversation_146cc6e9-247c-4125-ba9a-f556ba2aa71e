# 🎲 COMPLETE RAFFLE TESTING GUIDE - STEP BY STEP

## 📊 **TESTING OVERVIEW**

**Purpose**: Create a complete raffle flow test from product creation to raffle participation  
**Scope**: Product creation, raffle setup, homepage countdown, and user participation  
**Result**: Fully functional raffle system ready for testing and demonstration

---

## 🎯 **STEP-BY-STEP TESTING WORKFLOW**

### **Step 1: Create Test Product** 🛍️

#### **✅ Navigate to Product Creation:**
1. **Go to**: `http://localhost:3000/admin/products/add`
2. **Fill in the form** with test data:

```
📝 Product Details:
Product Name: "Dragon Artisan Keycap - Raffle Test"
Price: 89.99
Category: "Artisan Keycaps" (or "Giveaway" for giveaway raffles)
Stock Quantity: 1
Description: "Premium handcrafted dragon-themed artisan keycap. Limited edition piece perfect for mechanical keyboard enthusiasts. Features intricate details and premium resin construction."

Image URL: "https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500&h=500&fit=crop"
(or any keycap image URL)

☑️ Featured Product: Checked
☑️ Raffle Item: Unchecked (we'll set this up through raffle creation)
```

3. **Click** "Create Product"
4. **Verify** success message and product creation

### **Step 2: Create Raffle for the Product** 🎲

#### **✅ Navigate to Raffle Creation:**
1. **Go to**: `http://localhost:3000/admin/raffles/create`
2. **Fill in the raffle form**:

```
🎯 Raffle Configuration:
Title: "Dragon Keycap Raffle - January 2025"
Description: "Win this exclusive dragon artisan keycap! Limited to one winner. Enter now for your chance to own this premium piece."

📅 Timing (for immediate testing):
Start Date: [Current date/time + 1 minute]
End Date: [Current date/time + 10 minutes]

🏆 Winners & Requirements:
Number of Winners: 1
Max Entries: 50 (optional)

Entry Requirements:
☑️ Instagram Follow
☑️ Reddit Join
☑️ Discord Join
☐ Purchase Required

📦 Product Selection:
☑️ Select your "Dragon Artisan Keycap - Raffle Test" product
```

3. **Click** "Create Raffle"
4. **Verify** success message and redirect to admin raffles

### **Step 3: Test Homepage Countdown** 🏠

#### **✅ Verify Upcoming Raffle Display:**
1. **Navigate to**: `http://localhost:3000`
2. **Scroll to** raffle countdown section
3. **Verify** displays:
   - "Next Raffle" title
   - Product image and name
   - Countdown to start time
   - "Notify Me" button

#### **✅ Test Notification Signup:**
1. **Click** "Notify Me" button
2. **Enter** test email address
3. **Verify** success message
4. **Check** button changes to "Unnotify Me"

### **Step 4: Test Raffle Activation** ⚡

#### **✅ Wait for Raffle to Start:**
1. **Wait** for countdown to reach zero (1 minute)
2. **Watch** automatic transition:
   - Title changes to "Active Raffle"
   - "LIVE NOW!" indicator appears
   - Button changes to "🎉 Join Now!"
   - Animations and color changes

#### **✅ Verify Database Update:**
1. **Open** browser developer tools
2. **Check** console for:
   - "📝 Updating raffle [ID] from upcoming to active"
   - "✅ Active raffle data: {...}"

### **Step 5: Test Raffle Participation** 🎮

#### **✅ Join the Raffle:**
1. **Click** "Join Raffle" button
2. **Verify** redirects to product page
3. **Check** product page shows:
   - Raffle countdown (time until end)
   - "Join Raffle" button
   - Raffle information

#### **✅ Complete Raffle Entry:**
1. **Click** "Join Raffle" on product page
2. **Fill in** entry form:
   - Name
   - Email
   - Social media requirements
3. **Submit** entry
4. **Verify** success message

### **Step 6: Test Admin Raffle Management** 👨‍💼

#### **✅ View Raffle Entries:**
1. **Navigate to**: `http://localhost:3000/admin/raffles`
2. **Find** your test raffle
3. **Click** "View Entries"
4. **Verify** your test entry appears
5. **Check** entry count updates

#### **✅ Test Winner Selection:**
1. **Click** "Select Winner" (if available)
2. **Use** roulette system to select winner
3. **Verify** winner selection works
4. **Check** winner display and notifications

### **Step 7: Test Raffle End Transition** 🏁

#### **✅ Wait for Raffle to End:**
1. **Wait** for raffle end time (10 minutes)
2. **Watch** automatic transition:
   - Status updates to "ended"
   - Page refreshes automatically
   - Next upcoming raffle loads (if any)

#### **✅ Verify End State:**
1. **Check** admin raffles shows "ended" status
2. **Verify** product page shows "Raffle Ended"
3. **Confirm** no more entries accepted

---

## 🧪 **TESTING CHECKLIST**

### **✅ Product Creation Testing:**
```
☐ Product form submits successfully
☐ Product appears in admin products list
☐ Product data saved correctly in Firestore
☐ Product images display properly
☐ Product accessible via direct URL
```

### **✅ Raffle Creation Testing:**
```
☐ Raffle form submits successfully
☐ Raffle document created in Firestore
☐ Product marked as raffle item
☐ Raffle appears in admin management
☐ Status set correctly (upcoming/active)
```

### **✅ Homepage Countdown Testing:**
```
☐ Upcoming raffle displays correctly
☐ Countdown shows accurate time
☐ "Notify Me" button works
☐ Automatic activation at start time
☐ Visual animations and feedback
☐ "Join Raffle" button appears when active
```

### **✅ Raffle Participation Testing:**
```
☐ "Join Raffle" button redirects correctly
☐ Product page shows raffle information
☐ Entry form submits successfully
☐ Entry appears in admin view
☐ Entry count updates correctly
```

### **✅ Admin Management Testing:**
```
☐ Raffle list shows all raffles
☐ "View Entries" displays participants
☐ Winner selection system works
☐ Roulette animation functions
☐ Winner notifications sent
```

### **✅ Status Transition Testing:**
```
☐ Upcoming → Active transition works
☐ Database status updates correctly
☐ Active → Ended transition works
☐ Automatic refresh to next raffle
☐ Console debugging shows correct flow
```

---

## 🎨 **SAMPLE TEST DATA**

### **✅ Product Test Data:**
```json
{
  "name": "Dragon Artisan Keycap - Raffle Test",
  "description": "Premium handcrafted dragon-themed artisan keycap. Limited edition piece perfect for mechanical keyboard enthusiasts.",
  "price": 89.99,
  "category": "artisan",
  "stock": 1,
  "image": "https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500&h=500&fit=crop",
  "featured": true
}
```

### **✅ Raffle Test Data:**
```json
{
  "title": "Dragon Keycap Raffle - January 2025",
  "description": "Win this exclusive dragon artisan keycap! Limited to one winner.",
  "startDate": "2025-01-XX 14:30:00",
  "endDate": "2025-01-XX 14:40:00",
  "winnerTotal": 1,
  "maxEntries": 50,
  "entryRequirements": {
    "instagram": true,
    "reddit": true,
    "discord": true,
    "purchase": false
  }
}
```

---

## 🎉 **EXPECTED RESULTS**

### **🏆 Successful Test Completion:**

#### **✅ Homepage Experience:**
- Smooth countdown display
- Automatic raffle activation
- Professional animations
- Clear call-to-action buttons

#### **✅ User Participation:**
- Easy raffle entry process
- Clear requirements and instructions
- Success feedback and confirmations
- Professional user interface

#### **✅ Admin Management:**
- Complete raffle oversight
- Entry tracking and management
- Winner selection system
- Professional admin interface

#### **✅ Technical Performance:**
- Real-time status updates
- Database synchronization
- Error-free operation
- Responsive design

## **🚀 YOUR COMPLETE RAFFLE SYSTEM IS READY FOR TESTING!**

**Follow this guide to create a complete raffle flow test and verify all functionality works perfectly from product creation to winner selection!** 🎲✨

---

## 📋 **QUICK START COMMANDS**

### **✅ URLs for Testing:**
```
🛍️ Create Product: http://localhost:3000/admin/products/add
🎲 Create Raffle: http://localhost:3000/admin/raffles/create
🏠 Homepage Test: http://localhost:3000
👨‍💼 Admin Raffles: http://localhost:3000/admin/raffles
📊 Admin Products: http://localhost:3000/admin/products
```

### **✅ Test Timing:**
```
⏰ Recommended Schedule:
- Start Date: Current time + 1 minute
- End Date: Current time + 10 minutes
- Total Test Duration: ~15 minutes
- Allows testing all transitions
```

**Your complete raffle testing environment is ready!** 🏆
