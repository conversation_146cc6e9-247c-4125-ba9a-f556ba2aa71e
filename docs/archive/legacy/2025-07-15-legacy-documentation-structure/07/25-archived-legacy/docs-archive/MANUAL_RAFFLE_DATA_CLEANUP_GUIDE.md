# 🗑️ MANUAL RAFFLE DATA CLEANUP GUIDE

## 📊 **CLEANUP SUMMARY**

**Purpose**: Clear raffle data for arief.ross<PERSON><PERSON><EMAIL> to test duplicate entry prevention  
**Target User**: arief.ross<PERSON><PERSON><EMAIL>  
**Data to Clear**: Raffle entries and shipping addresses  
**Goal**: Enable fresh testing of raffle entry submission and duplicate prevention

---

## 🎯 **CLEANUP METHODS**

### **Method 1: <PERSON>rowser Console Script (Recommended)**

#### **✅ Step-by-Step Instructions:**
```
1. Open your web application in browser
2. Login as admin or ensure you have Firebase access
3. Open browser developer tools (F12)
4. Go to Console tab
5. Copy and paste the script below
6. Press Enter to execute
```

#### **✅ Browser Console Script:**
```javascript
// Raffle Data Cleanup <NAME_EMAIL>
async function clearUserRaffleData() {
  const userEmail = 'arief.rossira<PERSON><EMAIL>';
  
  try {
    console.log('🔍 Starting cleanup for:', userEmail);
    
    // Import Firebase (adjust path if needed)
    const { db } = await import('./src/lib/firebase.js');
    const { collection, query, where, getDocs, deleteDoc, doc } = await import('firebase/firestore');
    
    // Find user by email
    const profilesQuery = query(
      collection(db, 'profiles'),
      where('email', '==', userEmail)
    );
    
    const profileSnapshot = await getDocs(profilesQuery);
    
    if (profileSnapshot.empty) {
      console.log('❌ User not found');
      return;
    }
    
    const userId = profileSnapshot.docs[0].id;
    console.log('✅ Found user ID:', userId);
    
    // Delete raffle entries
    const raffleQuery = query(
      collection(db, 'raffle_entries'),
      where('userId', '==', userId)
    );
    
    const raffleSnapshot = await getDocs(raffleQuery);
    console.log(`🎲 Found ${raffleSnapshot.size} raffle entries`);
    
    for (const doc of raffleSnapshot.docs) {
      await deleteDoc(doc.ref);
      console.log('🗑️ Deleted raffle entry:', doc.id);
    }
    
    // Delete shipping addresses
    const addressQuery = query(
      collection(db, 'shipping_addresses'),
      where('userId', '==', userId)
    );
    
    const addressSnapshot = await getDocs(addressQuery);
    console.log(`📍 Found ${addressSnapshot.size} shipping addresses`);
    
    for (const doc of addressSnapshot.docs) {
      await deleteDoc(doc.ref);
      console.log('🗑️ Deleted address:', doc.id);
    }
    
    console.log('✅ Cleanup complete! User can test again.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the cleanup
clearUserRaffleData();
```

### **Method 2: Firebase Console (Manual)**

#### **✅ Firebase Console Steps:**
```
1. Go to: https://console.firebase.google.com/
2. Select your project: syndicaps-fullpower
3. Go to Firestore Database
4. Navigate to Collections

Step 1: Find User ID
- Go to 'profiles' collection
- Find document where email = '<EMAIL>'
- Copy the document ID (this is the userId)

Step 2: Delete Raffle Entries
- Go to 'raffle_entries' collection
- Find all documents where userId = [copied userId]
- Delete each raffle entry document

Step 3: Delete Shipping Addresses
- Go to 'shipping_addresses' collection
- Find all documents where userId = [copied userId]
- Delete each shipping address document
```

### **Method 3: Admin Dashboard (If Available)**

#### **✅ Admin Dashboard Steps:**
```
1. Login to admin dashboard
2. Go to User Management
3. Search for: <EMAIL>
4. View user profile
5. Clear raffle entries and shipping addresses
6. Confirm deletion
```

---

## 🧪 **VERIFICATION STEPS**

### **✅ Confirm Cleanup Success:**
```
1. Check Firestore Console:
   - raffle_entries collection: No documents with userId
   - shipping_addresses collection: No documents with userId

2. Check Application:
   - <NAME_EMAIL>
   - Go to /profile/raffles
   - Should show "No raffle entries found"
   - Go to /profile/account
   - Should show clean statistics (0 entries)
```

---

## 🎮 **TESTING WORKFLOW AFTER CLEANUP**

### **✅ Test Duplicate Entry Prevention:**

#### **Test 1: First Entry (Should Work)**
```
1. <NAME_EMAIL>
2. Navigate to /raffle-entry
3. Complete all form steps
4. Select products (e.g., product1, product2)
5. Submit entry
6. Should see success popup
7. Check /profile/raffles - entry should appear
```

#### **Test 2: Exact Duplicate (Should Fail)**
```
1. Navigate to /raffle-entry again
2. Select same products (product1, product2)
3. Complete form and submit
4. Should see duplicate entry error:
   "⚠️ Duplicate Entry Not Allowed
   You have already entered a raffle for one or more of these products..."
```

#### **Test 3: Partial Overlap (Should Fail)**
```
1. Navigate to /raffle-entry again
2. Select some same + some new products (product1, product3)
3. Submit entry
4. Should see duplicate entry error (product1 overlap)
```

#### **Test 4: Different Products (Should Work)**
```
1. Navigate to /raffle-entry again
2. Select completely different products (product4, product5)
3. Submit entry
4. Should succeed (no overlap)
5. Check /profile/raffles - should see both entries
```

---

## 🎯 **EXPECTED RESULTS**

### **✅ After Cleanup:**
```
📊 User Profile State:
- Total raffle entries: 0
- Shipping addresses: 0
- Profile statistics reset
- Clean slate for testing

🎲 Raffle Entry Testing:
- First submission: ✅ Success
- Duplicate submission: ❌ Blocked with clear error
- Partial overlap: ❌ Blocked with clear error
- Different products: ✅ Success

🔄 Duplicate Prevention:
- Database validation working
- User-friendly error messages
- Profile entries display correctly
```

---

## 🚨 **IMPORTANT NOTES**

### **⚠️ Cleanup Considerations:**
```
🔒 Data Safety:
- Only clears data for specified user
- Does not affect other users
- Preserves user profile and account data
- Only removes raffle entries and shipping addresses

🔄 Reversibility:
- Deleted raffle entries cannot be recovered
- User can create new entries after cleanup
- Shipping addresses will be recreated on next submission

📊 Testing Impact:
- Enables fresh testing of duplicate prevention
- Allows verification of business rules
- Confirms error handling works correctly
```

### **✅ Verification Checklist:**
```
Before Testing:
□ User raffle entries deleted from Firestore
□ User shipping addresses deleted from Firestore
□ Profile page shows 0 raffle entries
□ Account statistics reset to clean state

During Testing:
□ First raffle entry succeeds
□ Duplicate entry attempt fails with clear error
□ Partial overlap detected and blocked
□ Different products allowed
□ Profile page updates correctly

After Testing:
□ Duplicate prevention working as expected
□ Error messages clear and helpful
□ User experience professional
□ Business rules enforced correctly
```

---

## 🎉 **CLEANUP COMPLETION**

### **🏆 READY FOR TESTING!**

**Once you've completed the cleanup using any of the methods above, <NAME_EMAIL> will have a clean slate for testing the duplicate entry prevention system.**

#### **🎯 What's Been Cleared:**
- ✅ **Raffle Entries** - All previous raffle submissions removed
- ✅ **Shipping Addresses** - All saved addresses cleared
- ✅ **Clean Profile** - Profile shows 0 entries and clean statistics

#### **🎮 Ready to Test:**
- **First Entry** - Should work normally with success popup
- **Duplicate Prevention** - Should block repeat submissions with clear error
- **Partial Overlap** - Should detect and prevent overlapping product entries
- **Different Products** - Should allow entries for non-overlapping products

## **🚀 YOUR TEST USER IS NOW READY FOR DUPLICATE ENTRY TESTING!**

**Use any of the cleanup methods above to clear the raffle data and then test the duplicate entry prevention system!** 🎲✨
