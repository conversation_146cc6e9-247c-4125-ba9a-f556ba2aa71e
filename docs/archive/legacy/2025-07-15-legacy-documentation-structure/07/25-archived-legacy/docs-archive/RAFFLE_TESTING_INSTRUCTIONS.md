# 🎲 RAFFLE TESTING INSTRUCTIONS - <PERSON><PERSON><PERSON><PERSON> START

## 🚀 **IMMEDIATE TESTING STEPS**

### **Step 1: Create Test Product** (2 minutes)

#### **Option A: Manual Creation (Recommended)**
1. **Navigate to**: `http://localhost:3000/admin/products/add`
2. **Fill in the form**:
   ```
   Product Name: Dragon Artisan Keycap - Raffle Test
   Price: 89.99
   Category: Artisan Keycaps
   Stock: 1
   Description: Premium handcrafted dragon-themed artisan keycap for raffle testing.
   Image URL: https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500&h=500&fit=crop
   ✓ Featured Product
   ```
3. **Click** "Create Product"

#### **Option B: Console <PERSON>t (Advanced)**
1. **Open** browser console on any page
2. **Copy/paste** script from `scripts/create-test-product.js`
3. **Run** `createTestProduct()`

### **Step 2: Create Active Raffle** (1 minute)

1. **Navigate to**: `http://localhost:3000/admin/raffles/create`
2. **Fill in the form**:
   ```
   Title: Dragon Keycap Raffle - Test
   Description: Test raffle for dragon artisan keycap
   
   Start Date: [Current time + 30 seconds]
   End Date: [Current time + 5 minutes]
   
   Number of Winners: 1
   Max Entries: 10
   
   Entry Requirements:
   ✓ Instagram Follow
   ✓ Reddit Join
   ✓ Discord Join
   
   Product Selection:
   ✓ Select your Dragon Artisan Keycap
   ```
3. **Click** "Create Raffle"

### **Step 3: Test Homepage Countdown** (30 seconds)

1. **Navigate to**: `http://localhost:3000`
2. **Scroll to** raffle countdown section
3. **Wait** for raffle to activate (30 seconds)
4. **Watch** transition from "Notify Me" to "Join Raffle"

### **Step 4: Test Raffle Participation** (1 minute)

1. **Click** "Join Raffle" button
2. **Verify** redirects to product page
3. **Click** "Join Raffle" on product page
4. **Fill in** entry form and submit

### **Step 5: Verify Admin Management** (1 minute)

1. **Navigate to**: `http://localhost:3000/admin/raffles`
2. **Find** your test raffle
3. **Click** "View Entries"
4. **Verify** your entry appears

---

## 🎯 **EXPECTED RESULTS**

### **✅ Homepage Countdown:**
- Shows "Next Raffle" initially
- Automatically changes to "Active Raffle" when time starts
- "LIVE NOW!" indicator appears with animations
- Button changes from "Notify Me" to "Join Raffle"

### **✅ Product Page:**
- Shows raffle countdown
- "Join Raffle" button works
- Entry form submits successfully
- Success message appears

### **✅ Admin Management:**
- Raffle appears in admin list
- Entry count updates
- "View Entries" shows participants
- Winner selection available

---

## 🔧 **TROUBLESHOOTING**

### **❌ If Homepage Shows "No Active Raffles":**
1. **Check** browser console for debugging output
2. **Verify** raffle start time is in the past
3. **Refresh** the page
4. **Check** Firestore for raffle status

### **❌ If "Join Raffle" Shows Product Missing:**
1. **Verify** product was created successfully
2. **Check** product ID matches raffle productId
3. **Navigate** directly to `/shop/[product-id]`

### **❌ If Raffle Creation Fails:**
1. **Check** all required fields are filled
2. **Verify** start date is before end date
3. **Ensure** at least one product is selected

---

## 📊 **DEBUGGING CONSOLE OUTPUT**

### **✅ Successful Flow:**
```
🎲 Fetching raffle data...
🔄 Checking all raffle statuses...
📝 Updating raffle abc123 from upcoming to active
✅ Raffle status check complete
🎯 Active raffles found: 1
✅ Active raffle data: { id: "abc123", status: "active", ... }
```

### **❌ No Raffles Found:**
```
🎲 Fetching raffle data...
🔄 Checking all raffle statuses...
✅ Raffle status check complete
🎯 Active raffles found: 0
⏳ No active raffles, checking upcoming...
📅 Upcoming raffles found: 0
❌ No raffles available
```

---

## 🎮 **COMPLETE TEST SCENARIO**

### **🏆 Full 5-Minute Test:**

**Minute 0-1:** Create product and raffle (start in 30 seconds)
**Minute 0.5-1:** Watch homepage countdown show upcoming raffle
**Minute 1:** Raffle activates automatically with animations
**Minute 1-2:** Test "Join Raffle" flow and entry submission
**Minute 2-3:** Verify admin management and entry tracking
**Minute 3-5:** Wait for raffle to end and test transitions

---

## 🎉 **SUCCESS CRITERIA**

### **✅ Test Passes If:**
- Product creates successfully
- Raffle creates and links to product
- Homepage countdown displays and activates automatically
- "Join Raffle" redirects to correct product page
- Entry form submits and appears in admin
- All animations and transitions work smoothly
- Console shows successful debugging output

## **🚀 START YOUR RAFFLE TEST NOW!**

**Follow the 5 steps above to test the complete raffle flow in under 5 minutes!** 🎲✨

---

## 📞 **QUICK LINKS**

```
🛍️ Create Product: http://localhost:3000/admin/products/add
🎲 Create Raffle: http://localhost:3000/admin/raffles/create
🏠 Test Homepage: http://localhost:3000
👨‍💼 Admin Raffles: http://localhost:3000/admin/raffles
📊 Admin Products: http://localhost:3000/admin/products
```

**Your raffle system is ready for comprehensive testing!** 🏆
