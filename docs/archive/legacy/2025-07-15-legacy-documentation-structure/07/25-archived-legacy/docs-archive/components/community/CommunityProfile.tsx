'use client'

/**
 * Community Profile Component
 * 
 * Enhanced user profile display for community features with
 * activity tracking, achievements, and social interactions.
 * 
 * Features:
 * - User statistics and achievements
 * - Activity timeline
 * - Social interactions (follow/unfollow)
 * - Content galleries (submissions, discussions)
 * - Real-time status indicators
 * - Mobile-responsive design
 * 
 * <AUTHOR> Team
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  User,
  Trophy,
  Star,
  Heart,
  MessageCircle,
  Image as ImageIcon,
  Calendar,
  MapPin,
  Link as LinkIcon,
  UserPlus,
  UserMinus,
  Settings,
  Share2,
  Award,
  Target,
  Zap,
  Clock,
  Eye,
  Users
} from 'lucide-react'
import { useUser } from '../../lib/useUser'
import { useRealTimePresence } from '../../hooks/useRealTimeCommunity'
import { RealTimeStatusBadge, ActivityPulse } from './RealTimeIndicator'

interface CommunityProfileProps {
  userId: string
  showActions?: boolean
  variant?: 'full' | 'compact' | 'card'
  className?: string
}

interface UserProfile {
  id: string
  username: string
  displayName: string
  avatar?: string
  bio?: string
  location?: string
  website?: string
  joinDate: Date
  isOnline: boolean
  lastSeen?: Date
  
  // Community stats
  stats: {
    points: number
    level: number
    rank: number
    submissions: number
    discussions: number
    challenges: number
    followers: number
    following: number
    likes: number
    views: number
  }
  
  // Achievements
  achievements: Array<{
    id: string
    name: string
    description: string
    icon: string
    rarity: 'common' | 'rare' | 'epic' | 'legendary'
    unlockedAt: Date
  }>
  
  // Recent activity
  recentActivity: Array<{
    id: string
    type: string
    title: string
    date: Date
    metadata?: any
  }>
}

/**
 * Community Profile Component
 */
const CommunityProfile: React.FC<CommunityProfileProps> = ({
  userId,
  showActions = true,
  variant = 'full',
  className = ''
}) => {
  const { user: currentUser } = useUser()
  const { onlineUsers } = useRealTimePresence()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [isFollowing, setIsFollowing] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const isOwnProfile = currentUser?.uid === userId
  const isOnline = onlineUsers.includes(userId)

  // Mock profile data - replace with actual API call
  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Mock profile data
      const mockProfile: UserProfile = {
        id: userId,
        username: 'keyboardenthusiast',
        displayName: 'Keyboard Enthusiast',
        bio: 'Passionate about mechanical keyboards and custom builds. Always looking for the perfect switch!',
        location: 'San Francisco, CA',
        website: 'https://example.com',
        joinDate: new Date('2023-01-15'),
        isOnline,
        lastSeen: new Date(),
        stats: {
          points: 2450,
          level: 12,
          rank: 156,
          submissions: 23,
          discussions: 45,
          challenges: 8,
          followers: 234,
          following: 189,
          likes: 567,
          views: 12340
        },
        achievements: [
          {
            id: '1',
            name: 'First Submission',
            description: 'Posted your first submission',
            icon: '🎯',
            rarity: 'common',
            unlockedAt: new Date('2023-02-01')
          },
          {
            id: '2',
            name: 'Challenge Winner',
            description: 'Won a community challenge',
            icon: '🏆',
            rarity: 'epic',
            unlockedAt: new Date('2023-06-15')
          },
          {
            id: '3',
            name: 'Community Helper',
            description: 'Helped 50+ community members',
            icon: '🤝',
            rarity: 'rare',
            unlockedAt: new Date('2023-08-20')
          }
        ],
        recentActivity: [
          {
            id: '1',
            type: 'submission',
            title: 'Posted new build: Custom 75% with Gateron Yellows',
            date: new Date('2023-12-01')
          },
          {
            id: '2',
            type: 'discussion',
            title: 'Started discussion: Best budget switches for beginners?',
            date: new Date('2023-11-28')
          },
          {
            id: '3',
            type: 'challenge',
            title: 'Joined Winter Keycap Design Challenge',
            date: new Date('2023-11-25')
          }
        ]
      }
      
      setProfile(mockProfile)
      setLoading(false)
    }

    fetchProfile()
  }, [userId, isOnline])

  const handleFollow = async () => {
    // Implement follow/unfollow logic
    setIsFollowing(!isFollowing)
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k'
    return num.toString()
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-yellow-400 bg-yellow-400/10'
      case 'epic': return 'text-purple-400 bg-purple-400/10'
      case 'rare': return 'text-blue-400 bg-blue-400/10'
      default: return 'text-gray-400 bg-gray-400/10'
    }
  }

  if (loading) {
    return (
      <div className={`bg-gray-800 rounded-lg p-6 animate-pulse ${className}`}>
        <div className="flex items-start space-x-4">
          <div className="w-20 h-20 bg-gray-700 rounded-full" />
          <div className="flex-1">
            <div className="h-6 bg-gray-700 rounded mb-2" />
            <div className="h-4 bg-gray-700 rounded w-3/4 mb-4" />
            <div className="grid grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-gray-700 rounded" />
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className={`bg-gray-800 rounded-lg p-6 text-center ${className}`}>
        <User className="w-12 h-12 text-gray-600 mx-auto mb-3" />
        <h3 className="text-white font-medium mb-1">Profile not found</h3>
        <p className="text-gray-400 text-sm">This user profile could not be loaded.</p>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={`bg-gray-800 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-gray-400" />
            </div>
            {isOnline && <ActivityPulse isActive={true} className="w-12 h-12" />}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-white font-medium truncate">{profile.displayName}</h3>
              <RealTimeStatusBadge />
            </div>
            <p className="text-gray-400 text-sm">Level {profile.stats.level} • {formatNumber(profile.stats.points)} points</p>
          </div>
          {showActions && !isOwnProfile && (
            <button
              onClick={handleFollow}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                isFollowing
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-accent-600 text-white hover:bg-accent-700'
              }`}
            >
              {isFollowing ? 'Following' : 'Follow'}
            </button>
          )}
        </div>
      </div>
    )
  }

  if (variant === 'card') {
    return (
      <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>
        <div className="text-center mb-4">
          <div className="relative inline-block">
            <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3">
              <User className="w-8 h-8 text-gray-400" />
            </div>
            {isOnline && <ActivityPulse isActive={true} className="w-16 h-16" />}
          </div>
          <h3 className="text-white font-bold mb-1">{profile.displayName}</h3>
          <p className="text-gray-400 text-sm mb-3">@{profile.username}</p>
          <RealTimeStatusBadge />
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center">
            <div className="text-xl font-bold text-white">{formatNumber(profile.stats.points)}</div>
            <div className="text-xs text-gray-400">Points</div>
          </div>
          <div className="text-center">
            <div className="text-xl font-bold text-white">{profile.stats.level}</div>
            <div className="text-xs text-gray-400">Level</div>
          </div>
        </div>

        {showActions && !isOwnProfile && (
          <button
            onClick={handleFollow}
            className={`w-full py-2 rounded font-medium transition-colors ${
              isFollowing
                ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                : 'bg-accent-600 text-white hover:bg-accent-700'
            }`}
          >
            {isFollowing ? 'Following' : 'Follow'}
          </button>
        )}
      </div>
    )
  }

  // Full profile view
  return (
    <div className={`bg-gray-800 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="relative">
              <div className="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center">
                <User className="w-10 h-10 text-gray-400" />
              </div>
              {isOnline && <ActivityPulse isActive={true} className="w-20 h-20" />}
            </div>
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-white">{profile.displayName}</h1>
                <RealTimeStatusBadge />
              </div>
              <p className="text-gray-400 mb-2">@{profile.username}</p>
              {profile.bio && (
                <p className="text-gray-300 mb-3 max-w-md">{profile.bio}</p>
              )}
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                {profile.location && (
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4" />
                    <span>{profile.location}</span>
                  </div>
                )}
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>Joined {profile.joinDate.toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex items-center space-x-3">
              {!isOwnProfile ? (
                <>
                  <button
                    onClick={handleFollow}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
                      isFollowing
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        : 'bg-accent-600 text-white hover:bg-accent-700'
                    }`}
                  >
                    {isFollowing ? <UserMinus className="w-4 h-4" /> : <UserPlus className="w-4 h-4" />}
                    <span>{isFollowing ? 'Unfollow' : 'Follow'}</span>
                  </button>
                  <button className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <Share2 className="w-4 h-4 text-gray-300" />
                  </button>
                </>
              ) : (
                <button className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2">
                  <Settings className="w-4 h-4" />
                  <span>Edit Profile</span>
                </button>
              )}
            </div>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{formatNumber(profile.stats.points)}</div>
            <div className="text-sm text-gray-400">Points</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{profile.stats.level}</div>
            <div className="text-sm text-gray-400">Level</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{formatNumber(profile.stats.followers)}</div>
            <div className="text-sm text-gray-400">Followers</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{formatNumber(profile.stats.following)}</div>
            <div className="text-sm text-gray-400">Following</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{profile.stats.submissions}</div>
            <div className="text-sm text-gray-400">Submissions</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{profile.stats.discussions}</div>
            <div className="text-sm text-gray-400">Discussions</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'overview', label: 'Overview', icon: User },
            { id: 'achievements', label: 'Achievements', icon: Trophy },
            { id: 'activity', label: 'Activity', icon: Zap }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 border-b-2 font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'border-accent-500 text-accent-400'
                    : 'border-transparent text-gray-400 hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-bold text-white mb-4">Recent Achievements</h3>
                  <div className="space-y-3">
                    {profile.achievements.slice(0, 3).map((achievement) => (
                      <div key={achievement.id} className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
                        <span className="text-2xl">{achievement.icon}</span>
                        <div>
                          <h4 className="text-white font-medium">{achievement.name}</h4>
                          <p className="text-gray-400 text-sm">{achievement.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    {profile.recentActivity.slice(0, 3).map((activity) => (
                      <div key={activity.id} className="p-3 bg-gray-700 rounded-lg">
                        <p className="text-white text-sm mb-1">{activity.title}</p>
                        <p className="text-gray-400 text-xs">{activity.date.toLocaleDateString()}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'achievements' && (
            <motion.div
              key="achievements"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {profile.achievements.map((achievement) => (
                  <div key={achievement.id} className={`p-4 rounded-lg border ${getRarityColor(achievement.rarity)}`}>
                    <div className="text-center">
                      <span className="text-4xl mb-2 block">{achievement.icon}</span>
                      <h4 className="text-white font-bold mb-1">{achievement.name}</h4>
                      <p className="text-gray-400 text-sm mb-2">{achievement.description}</p>
                      <span className={`text-xs px-2 py-1 rounded-full ${getRarityColor(achievement.rarity)}`}>
                        {achievement.rarity.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === 'activity' && (
            <motion.div
              key="activity"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <div className="space-y-4">
                {profile.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3 p-4 bg-gray-700 rounded-lg">
                    <div className="w-2 h-2 bg-accent-500 rounded-full mt-2" />
                    <div className="flex-1">
                      <p className="text-white mb-1">{activity.title}</p>
                      <p className="text-gray-400 text-sm">{activity.date.toLocaleDateString()}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default CommunityProfile
