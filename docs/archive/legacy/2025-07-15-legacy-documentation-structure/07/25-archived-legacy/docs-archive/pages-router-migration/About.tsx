'use client'

import React from 'react';
import { motion } from 'framer-motion';

const About: React.FC = () => {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="pt-24 pb-20"
    >
      {/* Hero Section */}
      <section className="relative mb-20">
        <div className="absolute inset-0 bg-black">
          <img 
            src="https://images.pexels.com/photos/977430/pexels-photo-977430.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
            alt="Studio workshop"
            className="w-full h-full object-cover opacity-30"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900 via-gray-900/80 to-transparent"></div>
        </div>
        
        <div className="container-custom relative z-10 py-20">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-2xl"
          >
            <h1 className="heading-xl text-white mb-6">
              Our <span className="text-accent-500">Story</span>
            </h1>
            <p className="text-lg text-gray-300">
              Dedicated to the art of creating unique, handcrafted artisan keycaps
              for keyboard enthusiasts around the world.
            </p>
          </motion.div>
        </div>
      </section>
      
      {/* Our Mission */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="heading-lg text-white mb-6">Our Mission</h2>
              <p className="text-gray-300 mb-6">
                At Syndicaps, our mission is to transform mechanical keyboards into works of art, one keycap at a time. 
                We believe that the tools we use every day should reflect our personalities and bring joy to our workspace.
              </p>
              <p className="text-gray-300 mb-6">
                We're passionate about combining artistry with functionality, creating keycaps that are both beautiful to 
                behold and satisfying to use. Each piece is meticulously crafted to ensure the highest quality and uniqueness.
              </p>
              <p className="text-gray-300">
                Our goal is to foster a community of keyboard enthusiasts who appreciate the artistry and craftsmanship 
                that goes into every Syndicaps creation.
              </p>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <img 
                src="https://images.pexels.com/photos/3165335/pexels-photo-3165335.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
                alt="Artisan keycap crafting"
                className="rounded-lg shadow-xl"
              />
            </motion.div>
          </div>
        </div>
      </section>
      
      {/* Our Process */}
      <section className="py-16 bg-gray-900">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="heading-lg text-white mb-4">Our Crafting Process</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Each artisan keycap undergoes a meticulous crafting process, ensuring that every piece 
              meets our high standards of quality and artistic excellence.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: 'Design',
                description: 'Every keycap begins as a concept, carefully designed to balance aesthetics and functionality.',
                image: 'https://images.pexels.com/photos/5082579/pexels-photo-5082579.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              },
              {
                title: 'Sculpting',
                description: 'Our artists meticulously sculpt each prototype by hand, paying close attention to every detail.',
                image: 'https://images.pexels.com/photos/14936126/pexels-photo-14936126.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              },
              {
                title: 'Casting',
                description: 'Using premium-quality resins, we cast each keycap with precision to ensure durability and beauty.',
                image: 'https://images.pexels.com/photos/7129161/pexels-photo-7129161.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              },
              {
                title: 'Finishing',
                description: 'Each keycap is polished and quality-checked to ensure it meets our exacting standards.',
                image: 'https://images.pexels.com/photos/3683056/pexels-photo-3683056.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              }
            ].map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                className="bg-gray-800 rounded-lg overflow-hidden"
              >
                <div className="h-48 overflow-hidden">
                  <img 
                    src={step.image} 
                    alt={step.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-2">{index + 1}. {step.title}</h3>
                  <p className="text-gray-400">{step.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Team Section */}
      <section className="py-16">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="heading-lg text-white mb-4">Meet Our Team</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              The talented artists and craftspeople behind our unique creations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: 'Arief R.R',
                role: 'Founder & Lead Designer',
                bio: 'With over 10 years of experience in artisan crafts, Alex brings a unique vision to every keycap design.',
                image: 'https://images.pexels.com/photos/8088448/pexels-photo-8088448.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              },
              {
                name: 'Zildjian Soeharto',
                role: 'Master Sculptor',
                bio: 'Zildjian\'s background in fine arts brings an unparalleled attention to detail to our sculptural designs.',
                image: 'https://images.pexels.com/photos/8193683/pexels-photo-8193683.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              },
              {
                name: '???',
                role: '???????',
                bio: '????????????????.',
                image: 'https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                className="bg-gray-900 rounded-lg overflow-hidden"
              >
                <div className="aspect-w-1 aspect-h-1 relative">
                  <img 
                    src={member.image} 
                    alt={member.name}
                    className="w-full h-64 object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-1">{member.name}</h3>
                  <p className="text-accent-500 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-400">{member.bio}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </motion.div>
  );
};

export default About;