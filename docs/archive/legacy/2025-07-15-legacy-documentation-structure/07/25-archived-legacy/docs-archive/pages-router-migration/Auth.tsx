'use client'

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Mail, Lock, Eye, EyeOff, CheckCircle } from 'lucide-react';
import { useAuth } from '../lib/hooks/useAuth';
import { useUser } from '../lib/useUser';
import { useActivityTracking } from '../lib/hooks/useActivityTracking';
import AuthErrorHandler from '../components/auth/AuthErrorHandler';

const Auth: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUser();
  const { signIn, signInWithGoogle, resetPassword } = useAuth();
  const { trackActivity } = useActivityTracking(); // Add activity tracking hook
  const [form, setForm] = useState({ email: '', password: '' });
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [showEmailForm, setShowEmailForm] = useState(false);

  const redirectPath = searchParams?.get('redirect') || '/profile/account';

  useEffect(() => {
    if (user) {
      console.log('✅ Auth: User already logged in, redirecting to:', redirectPath);
      router.push(redirectPath);
    }
  }, [user, redirectPath, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const { user, error } = await signIn(form.email, form.password);
      
      if (error) {
        console.log('❌ Login error:', error);
        setError(error);
        // Track failed login attempt (non-blocking)
        trackActivity(
          form.email,
          'AUTH',
          'LOGIN',
          'Failed login attempt',
          false,
          { errorType: error.code || 'UNKNOWN_ERROR' }
        ).catch(() => {}); // Silently ignore tracking errors
      } else {
        console.log('✅ Login successful, redirecting to:', redirectPath);
        // Track successful login (non-blocking)
        if (user) {
          trackActivity(
            user.uid,
            'AUTH',
            'LOGIN',
            'User logged in successfully',
            true,
            { provider: 'email', redirectPath }
          ).catch(() => {}); // Silently ignore tracking errors
        }
        router.push(redirectPath);
      }
    } catch (err) {
      const error = err as Error;
      console.error('❌ Unexpected error during login:', error);
      setError(error);
      // Track error (non-blocking)
      trackActivity(
        form.email,
        'AUTH',
        'LOGIN',
        'Login error occurred',
        false,
        { errorType: 'UNEXPECTED_ERROR' }
      ).catch(() => {}); // Silently ignore tracking errors
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setGoogleLoading(true);
    setError(null);

    try {
      const { user, error } = await signInWithGoogle();
      
      if (error) {
        console.log('❌ Google login error:', error);
        setError(error);
        // Track failed Google login (non-blocking)
        trackActivity(
          'anonymous',
          'AUTH',
          'GOOGLE_LOGIN',
          'Failed Google login attempt',
          false,
          { errorType: error.code || 'UNKNOWN_ERROR' }
        ).catch(() => {}); // Silently ignore tracking errors
      } else if (user) {
        console.log('✅ Google login successful, redirecting to:', redirectPath);
        // Track successful Google login (non-blocking)
        trackActivity(
          user.uid,
          'AUTH',
          'GOOGLE_LOGIN',
          'User logged in with Google successfully',
          true,
          { provider: 'google', redirectPath }
        ).catch(() => {}); // Silently ignore tracking errors
        router.push(redirectPath);
      }
    } catch (err) {
      const error = err as Error;
      console.error('❌ Unexpected error during Google login:', error);
      setError(error);
      // Track error (non-blocking)
      trackActivity(
        'anonymous',
        'AUTH',
        'GOOGLE_LOGIN',
        'Google login error occurred',
        false,
        { errorType: 'UNEXPECTED_ERROR' }
      ).catch(() => {}); // Silently ignore tracking errors
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleResetPassword = async () => {
    try {
      const { error } = await resetPassword(form.email);
      if (error) {
        setError(error);
      } else {
        setVerificationSent(true);
      }
    } catch (error) {
      setError(error);
    }
  };

  const handleRetryGoogle = () => {
    setError(null);
    handleGoogleSignIn();
  };

  const handleFallbackToEmail = () => {
    setError(null);
    setShowEmailForm(true);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-950 px-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md"
      >
        <div className="bg-gray-900 rounded-lg shadow-xl p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white">Welcome Back</h1>
            <p className="text-gray-400 mt-2">Sign in to your account</p>
          </div>

          {error && (
            <AuthErrorHandler
              error={error}
              onRetry={handleRetryGoogle}
              onFallback={handleFallbackToEmail}
            />
          )}

          {showEmailForm || !error ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-400">
                  Email Address
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    type="email"
                    value={form.email}
                    onChange={(e) => setForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full pl-10 pr-3 py-2 border border-gray-700 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:border-accent-500"
                    placeholder="Enter your email"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400">
                  Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    value={form.password}
                    onChange={(e) => setForm(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full pl-10 pr-10 py-2 border border-gray-700 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:border-accent-500"
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <div className="text-right">
                <button
                  type="button"
                  onClick={handleResetPassword}
                  className="text-sm text-accent-500 hover:text-accent-400 transition-colors"
                >
                  Forgot password?
                </button>
              </div>

              <button
                type="submit"
                disabled={loading}
                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-accent-600 hover:bg-accent-700 focus:outline-none transition-colors ${
                  loading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {loading ? 'Signing in...' : 'Sign in'}
              </button>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-700" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-gray-900 text-gray-400">Or continue with</span>
                </div>
              </div>

              <button
                type="button"
                onClick={handleGoogleSignIn}
                disabled={googleLoading}
                className={`w-full flex items-center justify-center py-2 px-4 border border-gray-700 rounded-lg text-sm font-medium text-white hover:bg-gray-800 transition-colors ${
                  googleLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24">
                  <path
                    fill="#EA4335"
                    d="M12.0003 4.75C13.7703 4.75 15.3553 5.36002 16.6053 6.54998L20.0303 3.125C17.9502 1.19 15.2353 0 12.0003 0C7.31024 0 3.25527 2.69 1.28027 6.60998L5.27028 9.70498C6.21525 6.86002 8.87025 4.75 12.0003 4.75Z"
                  />
                  <path
                    fill="#34A853"
                    d="M23.49 12.275C23.49 11.49 23.415 10.73 23.3 10H12V14.51H18.47C18.18 15.99 17.34 17.25 16.08 18.1L19.945 21.1C22.2 19.01 23.49 15.92 23.49 12.275Z"
                  />
                  <path
                    fill="#4A90E2"
                    d="M5.26498 14.2949C5.02498 13.5699 4.88501 12.7999 4.88501 11.9999C4.88501 11.1999 5.01996 10.4299 5.26996 9.7049L1.27997 6.60986C0.45997 8.22986 0 10.0599 0 11.9999C0 13.9399 0.45997 15.7699 1.28497 17.3899L5.26498 14.2949Z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M12.0004 24C15.2404 24 17.9654 22.935 19.9454 21.095L16.0804 18.095C15.0054 18.82 13.6204 19.245 12.0004 19.245C8.8704 19.245 6.21537 17.135 5.2654 14.29L1.27539 17.385C3.25539 21.31 7.3104 24 12.0004 24Z"
                  />
                </svg>
                {googleLoading ? 'Signing in...' : 'Sign in with Google'}
              </button>
            </form>
          ) : null}
        </div>

        <div className="text-center mt-4">
          <p className="text-sm text-gray-400">
            Don't have an account?{' '}
            <Link href="/register" className="text-accent-500 hover:text-accent-400 font-medium transition-colors">
              Create account
            </Link>
          </p>
        </div>

        {verificationSent && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-green-900/20 border border-green-700 rounded-lg flex items-center space-x-3"
          >
            <CheckCircle className="text-green-400" size={20} />
            <p className="text-sm text-green-300">
              Password reset email sent! Check your inbox.
            </p>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default Auth;