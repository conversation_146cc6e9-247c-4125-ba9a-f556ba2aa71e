'use client'

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Minus, Plus, Trash2, ShoppingBag, MapPin, Plus as PlusIcon, Edit } from 'lucide-react';
import { useCartStore } from '../store/cartStore';
import { useUser } from '../lib/useUser';
import { getUserShippingAddresses, createShippingAddress, ShippingAddress, createOrder } from '../lib/firestore';
import { PayPalScriptProvider, PayPalButtons } from '@paypal/react-paypal-js';
import { toast } from 'react-hot-toast';
import { PointsSystem } from '../lib/pointsSystem';
import { AchievementSystem } from '../lib/achievementSystem';
import { usePointHistory } from '../hooks/usePointHistory';

const Cart: React.FC = () => {
  const router = useRouter();
  const { user, loading: userLoading, profile } = useUser();
  const { items, removeItem, updateQuantity, total, clearCart, loading } = useCartStore();
  const { currentBalance } = usePointHistory(user?.uid || null);

  // Address state
  const [addresses, setAddresses] = useState<ShippingAddress[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<ShippingAddress | null>(null);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [pointsToEarn, setPointsToEarn] = useState(0);

  // New address form state
  const [newAddress, setNewAddress] = useState({
    name: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    phone: ''
  });

  // Check authentication and load addresses
  useEffect(() => {
    if (!userLoading) {
      if (!user) {
        // Redirect to login if not authenticated
        router.push('/auth?redirect=/cart');
        return;
      }

      // Load user's addresses
      loadAddresses();
    }
  }, [user, userLoading, router]);

  // Calculate points to earn from current cart
  useEffect(() => {
    const cartTotal = total();
    const basePoints = Math.floor(cartTotal); // 1 point per dollar
    const bonusPoints = cartTotal >= 300 ? Math.floor(cartTotal * 0.1) : 0; // 10% bonus for orders over $300
    setPointsToEarn(basePoints + bonusPoints);
  }, [items, total]);

  const loadAddresses = async () => {
    if (!user) return;

    setLoadingAddresses(true);
    try {
      const userAddresses = await getUserShippingAddresses(user.uid);
      setAddresses(userAddresses);

      // Auto-select first address if available
      if (userAddresses.length > 0 && !selectedAddress) {
        setSelectedAddress(userAddresses[0]);
      }
    } catch (error) {
      console.error('Error loading addresses:', error);
    } finally {
      setLoadingAddresses(false);
    }
  };

  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      const addressData = {
        ...newAddress,
        userId: user.uid,
        isDefault: false // or set to true if you want this to be the default address
      };

      const addressId = await createShippingAddress(addressData);
      const createdAddress = { ...addressData, id: addressId };

      setAddresses(prev => [...prev, createdAddress]);
      setSelectedAddress(createdAddress);
      setShowAddressForm(false);
      setNewAddress({
        name: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
        phone: ''
      });
    } catch (error) {
      console.error('Error creating address:', error);
      alert('Failed to save address. Please try again.');
    }
  };

  const handlePaypalSuccess = async (details: any) => {
    try {
      if (!user || !selectedAddress) {
        toast.error('User or shipping address missing.');
        return;
      }
      // Build order items
      const orderItems = items.map((item) => ({
        productId: item.product.id,
        productName: item.product.name,
        price: item.product.price,
        quantity: item.quantity,
      }));
      // Build order data
      const orderData = {
        userId: user.uid,
        items: orderItems,
        totalAmount: total(),
        status: 'pending' as const,
        shippingAddress: selectedAddress,
        paymentMethod: 'paypal',
        paymentStatus: 'paid' as const,
      };
      // Create order in Firestore
      const orderId = await createOrder(orderData);

      // Award points for the purchase
      try {
        const pointsAwarded = await PointsSystem.awardPurchasePoints(user.uid, total(), orderId);

        // Check for achievements
        if (profile) {
          await AchievementSystem.checkAchievements(user.uid, profile, {
            type: 'purchase',
            data: { amount: total(), itemCount: orderItems.length }
          });
        }

        toast.success(`Order placed successfully! You earned ${pointsAwarded} points!`);
      } catch (pointsError) {
        console.error('Points award failed:', pointsError);
        toast.success('Order placed successfully!');
      }

      clearCart();
      // Optionally redirect or show confirmation
      router.push('/profile/orders?success=1');
    } catch (error) {
      console.error('Order creation failed:', error);
      toast.error('Order creation failed. Please contact support.');
    }
  };

  // Show loading spinner when syncing cart
  if (loading || userLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="pt-24 pb-20"
      >
        <div className="container-custom">
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Syncing cart...</p>
          </div>
        </div>
      </motion.div>
    );
  }

  if (items.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="pt-20 sm:pt-24 pb-16 sm:pb-20"
      >
        <div className="container-custom mobile-container">
          <div className="text-center py-16">
            <ShoppingBag size={48} className="mx-auto text-gray-400 mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">Your cart is empty</h2>
            <p className="text-gray-400 mb-8">Add some artisan keycaps to your cart!</p>
            <Link href="/products" className="btn-primary">
              Browse Products
            </Link>
          </div>
        </div>
      </motion.div>
    );
  }

  // Wrap cart actions with toast notifications
  const handleRemoveItem = async (productId: string, selectedColor?: string, selectedCompatibility?: string) => {
    try {
      await removeItem(productId, selectedColor, selectedCompatibility);
      toast.success('Item removed from cart');
    } catch (e) {
      toast.error('Failed to remove item');
    }
  };

  const handleUpdateQuantity = async (productId: string, quantity: number, selectedColor?: string, selectedCompatibility?: string) => {
    try {
      await updateQuantity(productId, quantity, selectedColor, selectedCompatibility);
      toast.success('Cart updated');
    } catch (e) {
      toast.error('Failed to update cart');
    }
  };

  const handleClearCart = async () => {
    try {
      await clearCart();
      toast.success('Cart cleared');
    } catch (e) {
      toast.error('Failed to clear cart');
    }
  };

  // Disable checkout button if cart is syncing or empty
  const isCheckoutDisabled = loading || items.length === 0;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="pt-24 pb-20"
    >
      <div className="container-custom">
        <h1 className="text-3xl font-bold text-white mb-8">Shopping Cart</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            {/* Cart Items */}
            <div className="bg-gray-900 rounded-lg p-6 space-y-6">
              {items.map((item) => (
                <div key={item.product.id} className="flex items-center space-x-4 py-4 border-b border-gray-800 last:border-0">
                  <div className="w-24 h-24 flex-shrink-0">
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  </div>
                  
                  <div className="flex-grow">
                    <h3 className="text-white font-medium">{item.product.name}</h3>
                    <p className="text-gray-400 text-sm">${item.product.price.toFixed(2)}</p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
                      className="p-1 hover:bg-gray-800 rounded"
                    >
                      <Minus size={16} className="text-gray-400" />
                    </button>
                    <span className="text-white w-8 text-center">{item.quantity}</span>
                    <button
                      onClick={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
                      className="p-1 hover:bg-gray-800 rounded"
                    >
                      <Plus size={16} className="text-gray-400" />
                    </button>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-white font-medium">
                      ${(item.product.price * item.quantity).toFixed(2)}
                    </p>
                    <button
                      onClick={() => handleRemoveItem(item.product.id)}
                      className="text-red-500 hover:text-red-400 mt-1"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Shipping Address Section */}
            <div className="bg-gray-900 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-white flex items-center">
                  <MapPin size={20} className="mr-2" />
                  Shipping Address
                </h2>
                <button
                  onClick={() => setShowAddressForm(true)}
                  className="flex items-center text-accent-500 hover:text-accent-400 transition-colors"
                >
                  <PlusIcon size={16} className="mr-1" />
                  Add New
                </button>
              </div>

              {loadingAddresses ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-accent-500 mx-auto mb-2"></div>
                  <p className="text-gray-400 text-sm">Loading addresses...</p>
                </div>
              ) : addresses.length === 0 ? (
                <div className="text-center py-8">
                  <MapPin size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-400 mb-4">No saved addresses found</p>
                  <button
                    onClick={() => setShowAddressForm(true)}
                    className="btn-primary"
                  >
                    Add Your First Address
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {addresses.map((address) => (
                    <div
                      key={address.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedAddress?.id === address.id
                          ? 'border-accent-500 bg-accent-500/10'
                          : 'border-gray-700 hover:border-gray-600'
                      }`}
                      onClick={() => setSelectedAddress(address)}
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-white font-medium">{address.name}</h3>
                          <p className="text-gray-400 text-sm mt-1">
                            {address.address}
                          </p>
                          <p className="text-gray-400 text-sm">
                            {address.city}, {address.state} {address.zipCode}
                          </p>
                          <p className="text-gray-400 text-sm">{address.country}</p>
                          {address.phone && (
                            <p className="text-gray-400 text-sm">Phone: {address.phone}</p>
                          )}
                        </div>
                        <div className="flex items-center">
                          {selectedAddress?.id === address.id && (
                            <div className="w-4 h-4 bg-accent-500 rounded-full mr-2"></div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Add Address Form */}
              {showAddressForm && (
                <div className="mt-6 p-4 border border-gray-700 rounded-lg">
                  <h3 className="text-white font-medium mb-4">Add New Address</h3>
                  <form onSubmit={handleAddressSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 text-sm mb-1">Full Name *</label>
                        <input
                          type="text"
                          required
                          value={newAddress.name}
                          onChange={(e) => setNewAddress(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                        />
                      </div>
                      <div>
                        <label className="block text-gray-300 text-sm mb-1">Phone</label>
                        <input
                          type="tel"
                          value={newAddress.phone}
                          onChange={(e) => setNewAddress(prev => ({ ...prev, phone: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-300 text-sm mb-1">Address *</label>
                      <input
                        type="text"
                        required
                        value={newAddress.address}
                        onChange={(e) => setNewAddress(prev => ({ ...prev, address: e.target.value }))}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-gray-300 text-sm mb-1">City *</label>
                        <input
                          type="text"
                          required
                          value={newAddress.city}
                          onChange={(e) => setNewAddress(prev => ({ ...prev, city: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                        />
                      </div>
                      <div>
                        <label className="block text-gray-300 text-sm mb-1">State *</label>
                        <input
                          type="text"
                          required
                          value={newAddress.state}
                          onChange={(e) => setNewAddress(prev => ({ ...prev, state: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                        />
                      </div>
                      <div>
                        <label className="block text-gray-300 text-sm mb-1">ZIP Code *</label>
                        <input
                          type="text"
                          required
                          value={newAddress.zipCode}
                          onChange={(e) => setNewAddress(prev => ({ ...prev, zipCode: e.target.value }))}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-300 text-sm mb-1">Country *</label>
                      <input
                        type="text"
                        required
                        value={newAddress.country}
                        onChange={(e) => setNewAddress(prev => ({ ...prev, country: e.target.value }))}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                      />
                    </div>

                    <div className="flex space-x-3">
                      <button
                        type="submit"
                        className="bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded transition-colors"
                      >
                        Save Address
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowAddressForm(false)}
                        className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
          
          <div className="lg:col-span-1">
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-bold text-white mb-4">Order Summary</h2>

              {/* Selected Address Display */}
              {selectedAddress && (
                <div className="mb-6 p-4 bg-gray-800 rounded-lg">
                  <h3 className="text-white font-medium mb-2 flex items-center">
                    <MapPin size={16} className="mr-2" />
                    Shipping To:
                  </h3>
                  <div className="text-gray-300 text-sm">
                    <p className="font-medium">{selectedAddress.name}</p>
                    <p>{selectedAddress.address}</p>
                    <p>{selectedAddress.city}, {selectedAddress.state} {selectedAddress.zipCode}</p>
                    <p>{selectedAddress.country}</p>
                  </div>
                </div>
              )}
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-gray-400">
                  <span>Subtotal</span>
                  <span>${total().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-gray-400">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>
                <div className="border-t border-gray-800 pt-3">
                  <div className="flex justify-between text-white font-medium">
                    <span>Total</span>
                    <span>${total().toFixed(2)}</span>
                  </div>
                </div>

                {/* Points Preview */}
                {user && (
                  <div className="border-t border-gray-800 pt-3">
                    <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-purple-400 font-medium flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          Points to Earn
                        </span>
                        <span className="text-purple-400 font-bold">+{pointsToEarn}</span>
                      </div>
                      {total() >= 300 && (
                        <p className="text-xs text-purple-300">
                          🎉 Bonus! 10% extra points for orders over $300
                        </p>
                      )}
                      <p className="text-xs text-gray-400 mt-1">
                        Current balance: {currentBalance || 0} points
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {!selectedAddress ? (
                <div className="text-center py-4">
                  <p className="text-gray-400 text-sm mb-4">Please select a shipping address to continue</p>
                  <button
                    disabled
                    className="w-full bg-gray-700 text-gray-500 py-3 rounded-lg cursor-not-allowed"
                  >
                    Select Address to Checkout
                  </button>
                </div>
              ) : (
                <PayPalScriptProvider options={{ clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || 'demo-client-id', currency: "USD" }}>
                  <PayPalButtons
                    style={{ layout: "vertical" }}
                    createOrder={(_, actions) => {
                      return actions.order.create({
                        purchase_units: [{
                          amount: { currency_code: "USD", value: total().toFixed(2) }
                        }],
                        intent: 'CAPTURE'
                      });
                    }}
                    onApprove={async (_, actions) => {
                      if (actions.order) {
                        const details = await actions.order.capture();
                        handlePaypalSuccess(details);
                      } else {
                        console.error('PayPal actions.order is undefined');
                      }
                    }}
                  />
                </PayPalScriptProvider>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default Cart;