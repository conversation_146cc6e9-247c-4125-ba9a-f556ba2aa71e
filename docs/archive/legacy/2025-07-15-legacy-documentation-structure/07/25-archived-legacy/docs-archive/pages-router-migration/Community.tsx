/**
 * Community Page Component
 * 
 * Displays the community leaderboard and social features for the Syndicaps platform.
 * Renamed from "Leaderboard" to better reflect the community-focused nature of the page.
 * 
 * Features:
 * - User point rankings and leaderboards
 * - Community achievements and milestones
 * - Points earning system explanation
 * - Social engagement metrics
 * - User profiles and activity
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Trophy, 
  Share2, 
  ShoppingBag, 
  Instagram, 
  Image, 
  Users, 
  Award,
  Star,
  Gift,
  Calendar
} from 'lucide-react';
import LeaderboardCard from '../components/leaderboard/LeaderboardCard';
import FullCommunityLeaderboard from '../components/community/FullCommunityLeaderboard';
import UserAchievementShowcase from '../components/community/UserAchievementShowcase';
import CommunityActivityFeed from '../components/community/CommunityActivityFeed';
import CommunityAnalyticsDashboard from '../components/community/CommunityAnalyticsDashboard';
import CommunityStatisticsHeader from '../components/community/CommunityStatisticsHeader';
import InteractivePointsGuide from '../components/community/InteractivePointsGuide';
import { leaderboardData, pointsSystem } from '../data/leaderboard';
import { useUser } from '../lib/useUser';

/**
 * Community component displaying user rankings and community features
 * 
 * @returns JSX.Element - Rendered community page with leaderboards and social features
 */
const Community: React.FC = () => {
  const { user } = useUser();
  const sortedLeaderboard = [...leaderboardData].sort((a, b) => b.points - a.points);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="pt-24 pb-20"
    >
      <div className="container-custom">
        {/* Enhanced Community Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-12"
        >
          <CommunityStatisticsHeader
            realTimeUpdates={true}
            updateInterval={30000}
          />
        </motion.div>

        {/* Interactive Points Guide */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-12"
        >
          <InteractivePointsGuide
            initialExpanded={false}
            showTips={true}
          />
        </motion.div>



        {/* Community Analytics Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-12"
        >
          <CommunityAnalyticsDashboard
            timePeriod="week"
            showDetailedMetrics={true}
          />
        </motion.div>

        {/* User Achievement Showcase */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mb-12"
        >
          <UserAchievementShowcase
            showUserProgress={!!user}
            viewMode="grid"
          />
        </motion.div>

        {/* Community Activity Feed */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mb-12"
        >
          <CommunityActivityFeed
            initialLoadCount={10}
            realTimeUpdates={true}
          />
        </motion.div>

        {/* Enhanced Community Leaderboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <FullCommunityLeaderboard
            pageSize={20}
            showUserProfiles={true}
          />
        </motion.div>

        {/* Community Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mt-12 text-center bg-gradient-to-r from-accent-600 to-accent-700 p-8 rounded-lg"
        >
          <h3 className="text-2xl font-bold text-white mb-4">Join Our Community!</h3>
          <p className="text-accent-100 mb-6 max-w-2xl mx-auto">
            Connect with fellow keyboard enthusiasts, share your setups, and climb the rankings. 
            Start earning points today and unlock exclusive rewards!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/register"
              className="bg-white text-accent-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              Join Community
            </a>
            <a
              href="/shop"
              className="bg-accent-800 text-white px-6 py-3 rounded-lg font-medium hover:bg-accent-900 transition-colors"
            >
              Start Shopping
            </a>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Community;
