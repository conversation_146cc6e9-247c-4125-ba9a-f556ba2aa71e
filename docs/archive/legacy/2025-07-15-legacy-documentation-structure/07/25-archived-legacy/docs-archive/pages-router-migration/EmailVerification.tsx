'use client'

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Mail, CheckCircle, RefreshCw, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { sendVerificationEmail, checkEmailVerification } from '../lib/auth';
import { useUser } from '../lib/useUser';

const EmailVerification: React.FC = () => {
  const router = useRouter();
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.emailVerified) {
      setIsVerified(true);
    }
  }, [user]);

  const handleResendVerification = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const { error } = await sendVerificationEmail();
      if (error) {
        setError(error.message);
      } else {
        setVerificationSent(true);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCheckVerification = async () => {
    setChecking(true);
    setError(null);
    
    try {
      const { verified, error } = await checkEmailVerification();
      if (error) {
        setError(error.message);
      } else if (verified) {
        setIsVerified(true);
        setTimeout(() => {
          router.push('/profile');
        }, 2000);
      } else {
        setError('Email not yet verified. Please check your inbox and click the verification link.');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setChecking(false);
    }
  };

  if (isVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-950 px-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-gray-900 p-8 rounded-lg shadow-lg w-full max-w-md border border-gray-800 text-center"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <CheckCircle className="text-white" size={32} />
          </motion.div>
          
          <h2 className="text-2xl font-bold text-white mb-4">Email Verified!</h2>
          <p className="text-gray-400 mb-6">
            Your email has been successfully verified. You can now access all features of your account.
          </p>
          
          <Link
            href="/profile"
            className="btn-primary inline-block"
          >
            Go to Profile
          </Link>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-950 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gray-900 p-8 rounded-lg shadow-lg w-full max-w-md border border-gray-800"
      >
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Mail className="text-white" size={32} />
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">Verify Your Email</h2>
          <p className="text-gray-400">
            We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.
          </p>
        </div>

        {user?.email && (
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <p className="text-gray-400 text-sm mb-1">Email sent to:</p>
            <p className="text-white font-medium">{user.email}</p>
          </div>
        )}

        {/* Verification Sent Message */}
        {verificationSent && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-900/20 border border-green-500/50 text-green-400 px-4 py-3 rounded-lg text-sm mb-6"
          >
            <div className="flex items-center space-x-2">
              <CheckCircle size={16} />
              <span>Verification email sent! Please check your inbox.</span>
            </div>
          </motion.div>
        )}

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/20 border border-red-500/50 text-red-400 px-4 py-3 rounded-lg text-sm mb-6"
          >
            {error}
          </motion.div>
        )}

        <div className="space-y-4">
          {/* Check Verification Button */}
          <button
            onClick={handleCheckVerification}
            disabled={checking}
            className="w-full bg-accent-600 hover:bg-accent-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            {checking ? (
              <>
                <RefreshCw className="animate-spin" size={18} />
                <span>Checking...</span>
              </>
            ) : (
              <>
                <CheckCircle size={18} />
                <span>I've Verified My Email</span>
              </>
            )}
          </button>

          {/* Resend Verification Button */}
          <button
            onClick={handleResendVerification}
            disabled={loading}
            className="w-full bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            {loading ? (
              <>
                <RefreshCw className="animate-spin" size={18} />
                <span>Sending...</span>
              </>
            ) : (
              <>
                <Mail size={18} />
                <span>Resend Verification Email</span>
              </>
            )}
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-6 text-center">
          <p className="text-gray-500 text-sm mb-4">
            Didn't receive the email? Check your spam folder or try resending.
          </p>
          
          <Link
            href="/auth"
            className="text-accent-500 hover:text-accent-400 text-sm font-medium transition-colors inline-flex items-center space-x-1"
          >
            <ArrowLeft size={16} />
            <span>Back to Sign In</span>
          </Link>
        </div>
      </motion.div>
    </div>
  );
};

export default EmailVerification;
