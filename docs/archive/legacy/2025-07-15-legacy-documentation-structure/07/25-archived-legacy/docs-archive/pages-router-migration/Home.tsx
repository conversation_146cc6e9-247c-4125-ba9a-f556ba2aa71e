'use client'

/**
 * Homepage Component - Main Landing Page for Syndicaps
 *
 * This is the primary landing page that showcases the Syndicaps brand and products.
 * Features an auto-rotating hero section, raffle countdown, featured products,
 * customer reviews, and newsletter signup.
 *
 * Key Features:
 * - Auto-slider hero section with rotating background images
 * - Integrated raffle countdown as main hero content
 * - Featured products showcase with loading states
 * - Customer reviews carousel with auto-rotation
 * - Newsletter subscription form
 * - Semantic HTML5 structure for accessibility
 * - Responsive design with mobile optimization
 * - Smooth animations and transitions
 *
 * Technical Implementation:
 * - Uses Framer Motion for animations and page transitions
 * - Implements semantic HTML5 elements (main, section, article, aside)
 * - Fetches featured products from Firestore database
 * - Integrates with raffle system for active raffle display
 * - Responsive grid layouts for different screen sizes
 *
 * Customization Notes:
 * - Hero images can be modified in the heroImages array
 * - Animation timings can be adjusted in motion components
 * - Featured products limit can be changed in getProducts call
 * - Section order can be rearranged by moving JSX blocks
 *
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 1.0.0
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, ArrowDown } from 'lucide-react';
import Link from 'next/link';
import ProductCard from '@/components/products/ProductCard';
import RaffleCountdown from '@/components/raffle/RaffleCountdown';
import CustomerReviewsCarousel from '@/components/reviews/CustomerReviewsCarousel';
import AnnouncementBanner, { sampleAnnouncements } from '@/components/announcements/AnnouncementBanner';
import Toast from '@/components/ui/Toast';
import { getProducts, Product } from '@/lib/firestore';
import { useUser } from '@/lib/useUser';
import HomepageGamificationWrapper from '@/components/gamification/homepage/HomepageGamificationWrapper';
import DailyChallengeCard from '@/components/gamification/homepage/DailyChallengeCard';
import CommunityLeaderboard from '@/components/gamification/homepage/CommunityLeaderboard';
import SeasonalEventBanner from '@/components/gamification/homepage/SeasonalEventBanner';

/**
 * Hero section background images for auto-slider
 */
const heroImages = [
  {
    url: "https://images.unsplash.com/photo-1625130694338-4110ba634e59?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80",
    alt: "Premium artisan keycaps on mechanical keyboard"
  },
  {
    url: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80",
    alt: "Artisan keycap creation process in workshop"
  },
  {
    url: "https://images.unsplash.com/photo-1587829741301-dc798b83add3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80",
    alt: "Colorful mechanical keyboard with custom keycaps"
  },
  {
    url: "https://images.unsplash.com/photo-1541140532154-b024d705b90a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80",
    alt: "Gaming setup with premium mechanical keyboard"
  }
];

/**
 * Animation variants for consistent motion design
 */
const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

const fadeInLeft = {
  initial: { opacity: 0, x: -30 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -30 }
};

const fadeInRight = {
  initial: { opacity: 0, x: 30 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 30 }
};

/**
 * Main Homepage Component
 *
 * @returns The complete homepage with all sections
 */
const Home: React.FC = () => {
  // ===== HOOKS =====

  /** User authentication and profile data */
  const { user } = useUser();

  // ===== STATE MANAGEMENT =====

  /** Featured products from database */
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);

  /** Loading state for featured products */
  const [loading, setLoading] = useState(true);

  /** Current hero image index for auto-slider */
  const [currentHeroImage, setCurrentHeroImage] = useState(0);

  /** Newsletter subscription toast state */
  const [showNewsletterToast, setShowNewsletterToast] = useState(false);

  /** Newsletter email input state */
  const [newsletterEmail, setNewsletterEmail] = useState('');

  /** Manual navigation state for hero section */
  const [isManualNavigation, setIsManualNavigation] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartY, setDragStartY] = useState(0);
  const [dragThreshold] = useState(50); // Minimum drag distance to trigger navigation

  /** Ref for timeout cleanup */
  const manualNavigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // ===== EFFECTS AND HANDLERS =====

  /**
   * Fetch featured products from Firestore on component mount
   * Handles loading states and error scenarios gracefully
   */
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        // Fetch up to 6 featured products for homepage display
        const products = await getProducts({ featured: true, limitCount: 6 });
        setFeaturedProducts(products);
      } catch (error) {
        console.error('Error fetching featured products:', error);
        // TODO: Add toast notification for error handling
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  /**
   * Auto-slider effect for hero section background images
   * Rotates through heroImages array every 5 seconds, pauses during manual navigation
   */
  useEffect(() => {
    if (isManualNavigation) return; // Pause auto-rotation during manual navigation

    const interval = setInterval(() => {
      setCurrentHeroImage((prev) => (prev + 1) % heroImages.length);
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, [isManualNavigation]);

  /**
   * Cleanup effect for manual navigation timeout
   */
  useEffect(() => {
    return () => {
      if (manualNavigationTimeoutRef.current) {
        clearTimeout(manualNavigationTimeoutRef.current);
      }
    };
  }, []);

  /**
   * Handle smooth scroll to raffle section with header offset
   * Centers the raffle section in viewport accounting for fixed header
   */
  const scrollToRaffle = useCallback(() => {
    const raffleSection = document.getElementById('raffle');
    if (raffleSection) {
      const headerHeight = 80; // Estimated fixed header height
      const elementPosition = raffleSection.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  }, []);

  /**
   * Handle newsletter subscription form submission
   * Shows success toast and resets form on successful submission
   */
  const handleNewsletterSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();

    // Basic email validation
    if (!newsletterEmail || !newsletterEmail.includes('@')) {
      return;
    }

    // TODO: Implement actual newsletter subscription API call
    // For now, we'll simulate a successful subscription
    console.log('Newsletter subscription:', newsletterEmail);

    // Show success toast
    setShowNewsletterToast(true);

    // Reset form
    setNewsletterEmail('');
  }, [newsletterEmail]);

  /**
   * Handle manual navigation to previous image
   */
  const navigateToPrevious = useCallback(() => {
    setCurrentHeroImage((prev) => (prev - 1 + heroImages.length) % heroImages.length);
    setIsManualNavigation(true);

    // Clear any existing timeout
    if (manualNavigationTimeoutRef.current) {
      clearTimeout(manualNavigationTimeoutRef.current);
    }

    // Set new timeout and store reference
    manualNavigationTimeoutRef.current = setTimeout(() => {
      setIsManualNavigation(false);
      manualNavigationTimeoutRef.current = null;
    }, 3000);
  }, []);

  /**
   * Handle manual navigation to next image
   */
  const navigateToNext = useCallback(() => {
    setCurrentHeroImage((prev) => (prev + 1) % heroImages.length);
    setIsManualNavigation(true);

    // Clear any existing timeout
    if (manualNavigationTimeoutRef.current) {
      clearTimeout(manualNavigationTimeoutRef.current);
    }

    // Set new timeout and store reference
    manualNavigationTimeoutRef.current = setTimeout(() => {
      setIsManualNavigation(false);
      manualNavigationTimeoutRef.current = null;
    }, 3000);
  }, []);



  // ===== RENDER =====

  return (
    <HomepageGamificationWrapper enabled={true} className="homepage-gamification">
      <main
        className="homepage-container"
        role="main"
        aria-label="Syndicaps Homepage"
      >
      {/* ===== ANNOUNCEMENT BANNERS SECTION ===== */}
      <aside
        className="announcements-section"
        role="complementary"
        aria-label="Site announcements"
      >
        <AnnouncementBanner
          announcements={sampleAnnouncements}
          position="top"
          maxVisible={2}
        />
      </aside>

      {/* ===== HERO SECTION WITH AUTO-SLIDER ===== */}
      <section
        className="hero-section relative min-h-screen h-screen flex items-center tech-grid"
        style={{ minHeight: 'calc(100vh - 4rem)' }}
        role="banner"
        aria-label="Main hero section with auto-rotating backgrounds"
      >
        {/* Auto-rotating background images with manual navigation */}
        <div
          className="absolute inset-0 bg-black overflow-hidden select-none"
          style={{ userSelect: 'none' }}
        >
          <AnimatePresence mode="wait">
            <motion.img
              key={currentHeroImage}
              src={heroImages[currentHeroImage].url}
              alt={heroImages[currentHeroImage].alt}
              className="w-full h-full object-cover opacity-50"
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.5, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            />
          </AnimatePresence>

          {/* Gradient overlay for text readability */}
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900 via-gray-900/80 to-transparent"></div>

          {/* Interactive navigation areas */}
          <div className="absolute inset-0 flex pointer-events-none">
            {/* Left navigation area */}
            <div
              className="flex-1 flex items-center justify-start pl-8 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-auto cursor-pointer"
              style={{ background: 'linear-gradient(to right, rgba(0,0,0,0.1), transparent)' }}
              onClick={navigateToPrevious}
              onMouseDown={(e) => {
                setIsDragging(true);
                setDragStartX(e.clientX);
                setDragStartY(e.clientY);
              }}
              onMouseUp={(e) => {
                if (!isDragging) return;
                const deltaX = e.clientX - dragStartX;
                const deltaY = e.clientY - dragStartY;

                if (Math.abs(deltaX) > dragThreshold && Math.abs(deltaX) > Math.abs(deltaY)) {
                  if (deltaX > 0) {
                    navigateToNext();
                  } else {
                    navigateToPrevious();
                  }
                }
                setIsDragging(false);
                setDragStartX(0);
                setDragStartY(0);
              }}
              onTouchStart={(e) => {
                const touch = e.touches[0];
                setIsDragging(true);
                setDragStartX(touch.clientX);
                setDragStartY(touch.clientY);
              }}
              onTouchEnd={(e) => {
                if (!isDragging) return;
                const touch = e.changedTouches[0];
                const deltaX = touch.clientX - dragStartX;
                const deltaY = touch.clientY - dragStartY;

                if (Math.abs(deltaX) > dragThreshold && Math.abs(deltaX) > Math.abs(deltaY)) {
                  if (deltaX > 0) {
                    navigateToNext();
                  } else {
                    navigateToPrevious();
                  }
                }
                setIsDragging(false);
                setDragStartX(0);
                setDragStartY(0);
              }}
            >
              <div className="text-white/70 text-sm font-medium bg-black/30 px-3 py-1 rounded-full backdrop-blur-sm pointer-events-none">
                ← Previous
              </div>
            </div>

            {/* Right navigation area */}
            <div
              className="flex-1 flex items-center justify-end pr-8 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-auto cursor-pointer"
              style={{ background: 'linear-gradient(to left, rgba(0,0,0,0.1), transparent)' }}
              onClick={navigateToNext}
              onMouseDown={(e) => {
                setIsDragging(true);
                setDragStartX(e.clientX);
                setDragStartY(e.clientY);
              }}
              onMouseUp={(e) => {
                if (!isDragging) return;
                const deltaX = e.clientX - dragStartX;
                const deltaY = e.clientY - dragStartY;

                if (Math.abs(deltaX) > dragThreshold && Math.abs(deltaX) > Math.abs(deltaY)) {
                  if (deltaX > 0) {
                    navigateToNext();
                  } else {
                    navigateToPrevious();
                  }
                }
                setIsDragging(false);
                setDragStartX(0);
                setDragStartY(0);
              }}
              onTouchStart={(e) => {
                const touch = e.touches[0];
                setIsDragging(true);
                setDragStartX(touch.clientX);
                setDragStartY(touch.clientY);
              }}
              onTouchEnd={(e) => {
                if (!isDragging) return;
                const touch = e.changedTouches[0];
                const deltaX = touch.clientX - dragStartX;
                const deltaY = touch.clientY - dragStartY;

                if (Math.abs(deltaX) > dragThreshold && Math.abs(deltaX) > Math.abs(deltaY)) {
                  if (deltaX > 0) {
                    navigateToNext();
                  } else {
                    navigateToPrevious();
                  }
                }
                setIsDragging(false);
                setDragStartX(0);
                setDragStartY(0);
              }}
            >
              <div className="text-white/70 text-sm font-medium bg-black/30 px-3 py-1 rounded-full backdrop-blur-sm pointer-events-none">
                Next →
              </div>
            </div>
          </div>

          {/* Image indicators */}
          <div className="absolute bottom-20 right-8 flex space-x-2 z-20">
            {heroImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentHeroImage(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentHeroImage
                    ? 'bg-neon-purple scale-110 animate-pulse-neon'
                    : 'bg-white/30 hover:bg-neon-cyan/50'
                }`}
                aria-label={`Go to hero image ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Hero content container */}
        <div className="container-custom relative z-10 px-4 sm:px-6 lg:px-8">
          <motion.article
            className="hero-content max-w-2xl mx-auto text-center sm:text-left"
            variants={fadeInUp}
            initial="initial"
            animate="animate"
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <header className="hero-header mb-6 sm:mb-8">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-white mb-4 sm:mb-6">
                Artisan Keycaps For <span className="text-cyber animate-glow">Enthusiasts</span>
              </h1>
              <p className="text-base sm:text-lg text-gray-300 leading-relaxed max-w-xl mx-auto sm:mx-0">
                Elevate your mechanical keyboard with our handcrafted artisan keycaps.
                Each piece is meticulously designed and crafted for collectors and enthusiasts.
              </p>
            </header>

            {/* Call-to-action buttons */}
            <nav className="hero-actions flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link
                href="/shop"
                className="btn-gaming"
                aria-label="Browse our keycap collection"
              >
                Shop Collection
              </Link>
              <Link
                href="/about"
                className="btn-outline"
                aria-label="Learn about Syndicaps story"
              >
                Our Story
              </Link>
            </nav>
          </motion.article>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-6 sm:bottom-10 left-0 right-0 flex justify-center">
          <motion.button
            onClick={scrollToRaffle}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: 1,
              repeat: Infinity,
              repeatType: 'reverse',
              repeatDelay: 0.5
            }}
            className="text-white hover:text-accent-400 transition-colors p-3 rounded-full hover:bg-white/10 touch-target focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent"
            aria-label="Scroll to raffle countdown section"
          >
            <ArrowDown size={30} />
          </motion.button>
        </div>

      </section>

      {/* ===== RAFFLE COUNTDOWN SECTION ===== */}
      <section
        id="raffle"
        className="raffle-section"
        role="region"
        aria-label="Current raffle information and countdown"
      >
        <RaffleCountdown />
      </section>

      {/* ===== FEATURED PRODUCTS SECTION ===== */}
      <section
        id="featured"
        className="featured-products-section py-20 bg-gray-950"
        role="region"
        aria-label="Featured artisan keycaps collection"
      >
        <div className="container-custom">
          {/* Section header */}
          <header className="section-header text-center mb-16">
            <motion.h2
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="heading-lg text-white mb-4"
            >
              Featured <span className="text-accent-500">Creations</span>
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-gray-400 max-w-2xl mx-auto leading-relaxed"
            >
              Discover our most coveted artisan keycaps, each one a unique piece of functional art
              for your mechanical keyboard. Handcrafted with precision and passion.
            </motion.p>
          </header>

          {/* Products grid with loading states */}
          <div className="products-grid-container">
            {loading ? (
              <div className="loading-state text-center py-12" role="status" aria-live="polite">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
                <p className="text-gray-400 text-lg">Loading featured products...</p>
              </div>
            ) : featuredProducts.length > 0 ? (
              <div className="products-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {featuredProducts.map((product, index) => (
                  <motion.article
                    key={product.id}
                    className="product-card-wrapper"
                    variants={fadeInUp}
                    initial="initial"
                    whileInView="animate"
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                  >
                    <ProductCard product={product} />
                  </motion.article>
                ))}
              </div>
            ) : (
              <div className="empty-state text-center py-12" role="status">
                <div className="mb-4">
                  <Star size={48} className="text-gray-600 mx-auto mb-4" />
                </div>
                <p className="text-gray-400 text-lg mb-2">No featured products available.</p>
                <p className="text-gray-500 text-sm">
                  Visit the admin panel to seed the database with sample products.
                </p>
              </div>
            )}
          </div>

          {/* View all products link */}
          <footer className="section-footer mt-12 text-center">
            <Link
              href="/shop"
              className="btn-outline"
              aria-label="View all products in our shop"
            >
              View All Products
            </Link>
          </footer>
        </div>
      </section>

      {/* ===== GAMIFICATION FEATURES SECTION ===== */}
      <section
        id="gamification"
        className="gamification-section py-20 bg-gray-900"
        role="region"
        aria-label="Community challenges and leaderboard"
      >
        <div className="container-custom">
          {/* Section header */}
          <header className="section-header text-center mb-16">
            <motion.h2
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="heading-lg text-white mb-4"
            >
              Join the <span className="text-accent-500">Community</span>
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-gray-400 max-w-2xl mx-auto leading-relaxed"
            >
              Take on daily challenges, compete with fellow enthusiasts, and earn exclusive rewards
              in our vibrant keycap community.
            </motion.p>
          </header>

          {/* Gamification grid */}
          <div className="gamification-grid grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Daily Challenge */}
            <motion.div
              className="challenge-container"
              variants={fadeInLeft}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <DailyChallengeCard
                userId={user?.uid}
                className="h-full"
                onComplete={(challengeId: string, points: number) => {
                  console.log(`Challenge ${challengeId} completed for ${points} points`)
                  // TODO: Integrate with points system
                }}
              />
            </motion.div>

            {/* Community Leaderboard */}
            <motion.div
              className="leaderboard-container"
              variants={fadeInRight}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <CommunityLeaderboard
                className="h-full"
                topCount={5}
                period="weekly"
                showUserRank={true}
              />
            </motion.div>
          </div>

          {/* Seasonal Event Banner */}
          <motion.div
            className="seasonal-event-container mt-12"
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <SeasonalEventBanner
              dismissible={true}
              onDismiss={() => {
                console.log('Seasonal event banner dismissed')
              }}
            />
          </motion.div>
        </div>
      </section>

      {/* ===== CUSTOMER REVIEWS SECTION ===== */}
      <section
        className="reviews-section py-20 bg-gray-900"
        role="region"
        aria-label="Customer reviews and testimonials"
      >
        <div className="container-custom">
          {/* Section header */}
          <motion.header
            className="section-header text-center mb-12"
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="heading-lg text-white mb-4">
              What Our <span className="text-accent-500">Customers Say</span>
            </h2>
            <p className="text-gray-400 max-w-2xl mx-auto leading-relaxed">
              Don't just take our word for it. Here's what our community of keyboard enthusiasts
              has to say about their Syndicaps experience and craftsmanship.
            </p>
          </motion.header>

          {/* Reviews carousel */}
          <motion.div
            className="reviews-carousel-container"
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <CustomerReviewsCarousel
              className="max-w-4xl mx-auto"
              autoRotate={true}
              rotationInterval={6000}
              showNavigation={true}
            />
          </motion.div>
        </div>
      </section>

      {/* ===== ABOUT SECTION PREVIEW ===== */}
      <section
        className="about-preview-section py-20 bg-gray-950"
        role="region"
        aria-label="About Syndicaps craftsmanship and process"
      >
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content column */}
            <motion.article
              className="about-content"
              variants={fadeInLeft}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <header className="mb-8">
                <h2 className="heading-lg text-white mb-6">
                  Crafted With <span className="text-accent-500">Passion</span>
                </h2>
              </header>

              <div className="content-body space-y-6">
                <p className="text-gray-300 leading-relaxed">
                  At Syndicaps, we believe that every keyboard deserves to be unique. Our artisan keycaps are
                  meticulously handcrafted by skilled artists who pour their passion into each creation.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  From concept to casting, every step of our process is handled with care and precision,
                  ensuring that each keycap meets our exceptional standards of quality and artistic integrity.
                </p>
              </div>

              <footer className="mt-8">
                <Link
                  href="/about"
                  className="btn-primary"
                  aria-label="Learn more about Syndicaps story and process"
                >
                  Learn More About Us
                </Link>
              </footer>
            </motion.article>

            {/* Image column */}
            <motion.aside
              className="about-image-container relative"
              variants={fadeInRight}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <img
                src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1260&q=80"
                alt="Artisan keycap creation process showing detailed craftsmanship"
                className="rounded-lg shadow-2xl w-full"
                loading="lazy"
              />

              {/* Testimonial overlay */}
              <div className="testimonial-overlay absolute -bottom-6 -right-6 bg-gray-800 p-6 rounded-lg shadow-xl max-w-xs">
                <div className="rating flex items-center space-x-1 text-accent-500 mb-2" aria-label="5 star rating">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                <blockquote className="text-white text-sm font-medium mb-1">
                  "Absolutely stunning craftsmanship!"
                </blockquote>
                <cite className="text-gray-400 text-xs">— Keyboard Enthusiast</cite>
              </div>
            </motion.aside>
          </div>
        </div>
      </section>

      {/* ===== NEWSLETTER SECTION ===== */}
      <section
        className="newsletter-section py-20 bg-gradient-to-r from-accent-900 to-primary-900"
        role="region"
        aria-label="Newsletter subscription"
      >
        <div className="container-custom text-center">
          <motion.div
            className="newsletter-content"
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <header className="newsletter-header mb-8">
              <h2 className="heading-lg text-white mb-4">Stay Updated</h2>
              <p className="text-gray-200 leading-relaxed max-w-2xl mx-auto">
                Join our newsletter to receive updates on new releases, restocks, and exclusive offers.
                Be the first to know when our limited edition keycaps become available.
              </p>
            </header>

            {/* Newsletter signup form */}
            <form
              className="newsletter-form max-w-md mx-auto"
              onSubmit={handleNewsletterSubmit}
              aria-label="Newsletter subscription form"
            >
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0">
                <label htmlFor="newsletter-email" className="sr-only">
                  Email address for newsletter subscription
                </label>
                <input
                  id="newsletter-email"
                  type="email"
                  placeholder="Your email address"
                  value={newsletterEmail}
                  onChange={(e) => setNewsletterEmail(e.target.value)}
                  required
                  className="flex-grow px-4 py-3 rounded-l-md sm:rounded-l-md sm:rounded-r-none rounded-r-md focus:outline-none focus:ring-2 focus:ring-white/50 bg-white/10 text-white placeholder:text-gray-300 transition-all touch-target"
                  aria-describedby="newsletter-description"
                />
                <button
                  type="submit"
                  disabled={!newsletterEmail || !newsletterEmail.includes('@')}
                  className="bg-white text-accent-900 font-medium px-6 py-3 rounded-r-md sm:rounded-r-md sm:rounded-l-none rounded-l-md hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 disabled:opacity-50 disabled:cursor-not-allowed touch-target"
                  aria-label="Subscribe to newsletter"
                >
                  Subscribe
                </button>
              </div>
              <p id="newsletter-description" className="sr-only">
                Subscribe to receive updates on new keycap releases and exclusive offers
              </p>
            </form>
          </motion.div>
        </div>
      </section>

      {/* Newsletter Success Toast */}
      <Toast
        type="success"
        title="Thank you for subscribing!"
        message="You'll receive updates on new releases and exclusive offers."
        isVisible={showNewsletterToast}
        onClose={() => setShowNewsletterToast(false)}
        duration={5000}
      />
    </main>
    </HomepageGamificationWrapper>
  );
};

export default Home;