'use client'

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ChevronLeft, ShoppingCart, Heart, Share2, Timer, Star, Bell } from 'lucide-react';
import { useCartStore } from '../store/cartStore';
import { useWishlistStore } from '../store/wishlistStore';
import { getProduct, getProducts, getProductReviews, Product, Review } from '../lib/firestore';
import { db } from '../lib/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import RaffleNotificationButton from '../components/raffle/RaffleNotificationButton';
import { ShopPointsPreview } from '../components/gamification/ShopPointsPreview';
import { useUser } from '../lib/useUser';
import RaffleProductDisplay from '../components/raffle/RaffleProductDisplay';

const ProductDetail: React.FC = () => {
  const params = useParams<{ id: string }>();
  const id = params?.id;
  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Add state for selections
  const [selectedCompatibility, setSelectedCompatibility] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [showPopup, setShowPopup] = useState(false);

  // Raffle state management (moved to top to fix hooks order)
  const [raffleData, setRaffleData] = useState<any>(null);
  const [raffleStatus, setRaffleStatus] = useState<'upcoming' | 'active' | 'ended' | null>(null);

  const router = useRouter();
  const { addToWishlist } = useWishlistStore();
  const { user } = useUser();

  // Handle add to cart function
  const handleAddToCart = () => {
    if (product) {
      useCartStore.getState().addItem(product);
      setShowPopup(true);
    }
  };

  const handleJoinRaffle = () => {
    if (product) {
      router.push(`/raffle-entry?productId=${product.id}`);
    }
  };

  useEffect(() => {
    const fetchProductData = async () => {
      if (!id) return;

      try {
        const [productData, allProducts] = await Promise.all([
          getProduct(id),
          getProducts({ limitCount: 4 })
        ]);

        setProduct(productData);
        setRelatedProducts(allProducts.filter(p => p.id !== id));
      } catch (error) {
        console.error('Error fetching product:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [id]);

  // Fetch raffle data if product is a raffle (moved to top to fix hooks order)
  useEffect(() => {
    const fetchRaffleData = async () => {
      if (product?.isRaffle) {
        try {
          const raffleQuery = query(
            collection(db, 'raffles'),
            where('productId', '==', product.id)
          );
          const raffleSnapshot = await getDocs(raffleQuery);

          if (!raffleSnapshot.empty) {
            const raffleDoc = raffleSnapshot.docs[0];
            const raffleData = raffleDoc.data() as {
              startDate?: import('firebase/firestore').Timestamp;
              endDate?: import('firebase/firestore').Timestamp;
              [key: string]: any;
            };
            const raffle = { id: raffleDoc.id, ...raffleData };
            setRaffleData(raffle);

            // Determine raffle status based on current time
            const now = new Date();
            const startDate = raffle.startDate?.toDate();
            const endDate = raffle.endDate?.toDate();

            if (startDate && endDate) {
              if (now < startDate) {
                setRaffleStatus('upcoming');
              } else if (now >= startDate && now <= endDate) {
                setRaffleStatus('active');
              } else {
                setRaffleStatus('ended');
              }
            }
          }
        } catch (error) {
          console.error('Error fetching raffle data:', error);
        }
      } else {
        // Reset raffle state if product is not a raffle
        setRaffleData(null);
        setRaffleStatus(null);
      }
    };

    if (product) {
      fetchRaffleData();
    }
  }, [product?.id, product?.isRaffle]);

  if (loading) {
    return (
      <div className="pt-24 pb-20">
        <div className="container-custom text-center">
          <p className="text-gray-400 text-lg">Loading product...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen pt-24 pb-20 flex flex-col items-center justify-center">
        <h2 className="text-2xl text-white mb-4">Product Not Found</h2>
        <p className="text-gray-400 mb-8">The product you're looking for doesn't exist or has been removed.</p>
        <Link href="/products" className="btn-primary">
          Back to Products
        </Link>
      </div>
    );
  }

  const images = [product.image]; // Simplified since we don't have gallery in our schema

  // Check if raffle is active
  const isRaffleActive = product.isRaffle && raffleStatus === 'active';
  
  const getRemainingTime = () => {
    if (!raffleData) return null;

    const now = new Date();
    let targetDate = null;

    // For upcoming raffles, show time until start
    if (raffleStatus === 'upcoming' && raffleData.startDate) {
      targetDate = new Date(raffleData.startDate.toDate());
    }
    // For active raffles, show time until end
    else if (raffleStatus === 'active' && raffleData.endDate) {
      targetDate = new Date(raffleData.endDate.toDate());
    }

    if (!targetDate) return null;

    const diff = targetDate.getTime() - now.getTime();
    if (diff <= 0) return null;

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    return { days, hours, minutes };
  };

  const remainingTime = getRemainingTime();

  // If this is a raffle product with active raffle, use the dedicated raffle display
  if (product.isRaffle && raffleData && raffleStatus === 'active') {
    const raffleInfo = {
      id: raffleData.id,
      productId: product.id,
      status: raffleStatus,
      startDate: raffleData.startDate?.toDate() || new Date(),
      endDate: raffleData.endDate?.toDate() || new Date(),
      maxEntries: raffleData.maxEntries,
      winnerTotal: raffleData.winnerTotal || 1,
      entryCount: raffleData.entryCount || 0,
      expectedShipping: raffleData.expectedShipping
    };

    const productForRaffle = {
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      images: product.image ? [product.image] : [],
      isRaffle: product.isRaffle
    };

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="pt-20 sm:pt-24 pb-16 sm:pb-20"
      >
        <div className="container-custom mobile-container">
          {/* Breadcrumb */}
          <div className="mb-4 sm:mb-8">
            <Link href="/shop" className="flex items-center text-gray-400 hover:text-accent-400 transition-colors touch-target-sm focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-950 rounded-lg">
              <ChevronLeft size={16} className="mr-1" />
              Back to Shop
            </Link>
          </div>

          <RaffleProductDisplay product={productForRaffle} raffle={raffleInfo} />
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="pt-20 sm:pt-24 pb-16 sm:pb-20"
    >
      <div className="container-custom mobile-container">
        {/* Breadcrumb */}
        <div className="mb-4 sm:mb-8">
          <Link href="/products" className="flex items-center text-gray-400 hover:text-accent-400 transition-colors">
            <ChevronLeft size={16} className="mr-1" />
            Back to Products
          </Link>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
          {/* Product Images */}
          <div>
            <div className="bg-gray-900 rounded-lg overflow-hidden">
              <motion.img 
                key={currentImageIndex}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                src={images[currentImageIndex]} 
                alt={product.name}
                className="w-full h-auto object-cover aspect-square"
              />
            </div>
            
            {images.length > 1 && (
              <div className="mt-4 grid grid-cols-4 gap-4">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`rounded-lg overflow-hidden border-2 ${
                      currentImageIndex === index ? 'border-accent-500' : 'border-transparent'
                    }`}
                  >
                    <img 
                      src={image} 
                      alt={`${product.name} thumbnail ${index + 1}`}
                      className="w-full h-auto aspect-square object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
          
          {/* Product Info */}
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">{product.name}</h1>
            
            <div className="flex flex-wrap items-center gap-2 mb-6">
              <span className="text-xl sm:text-2xl font-bold text-green-400">${product.price.toFixed(2)}</span>
              {product.featured && !product.isRaffle && (
                <span className="bg-accent-600 text-white text-xs px-2 py-1 rounded-full">
                  Featured
                </span>
              )}
              {product.soldOut && !product.isRaffle && (
                <span className="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">
                  Sold Out
                </span>
              )}
              {product.isRaffle && (
                <span className="bg-accent-600 text-white text-xs px-2 py-1 rounded-full flex items-center">
                  <Timer size={12} className="mr-1" />
                  Raffle
                </span>
              )}
            </div>

            {/* Points Preview - only show for regular products and logged in users */}
            {!product.isRaffle && user && (
              <div className="mb-6">
                <ShopPointsPreview
                  price={product.price}
                  variant="product"
                  showBonus={true}
                />
              </div>
            )}
            
            {product.isRaffle && remainingTime && (
              <div className="mb-6 bg-gray-800 p-4 rounded-lg">
                <h3 className="text-white font-medium mb-3">
                  {raffleStatus === 'upcoming' ? 'Raffle Starts In:' : 'Raffle Ends In:'}
                </h3>
                <div className="flex space-x-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent-500">{remainingTime.days}</div>
                    <div className="text-sm text-gray-400">Days</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent-500">{remainingTime.hours}</div>
                    <div className="text-sm text-gray-400">Hours</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent-500">{remainingTime.minutes}</div>
                    <div className="text-sm text-gray-400">Minutes</div>
                  </div>
                </div>

                {raffleStatus === 'upcoming' && (
                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-400 mb-3">
                      Get notified when this raffle becomes available
                    </p>
                    <RaffleNotificationButton
                      productId={product.id}
                      productName={product.name}
                      isRaffleActive={false}
                      raffleStartDate={raffleData?.startDate?.toDate()}
                      raffleEndDate={raffleData?.endDate?.toDate()}
                      onJoinRaffle={handleJoinRaffle}
                    />
                  </div>
                )}
              </div>
            )}
            
            <p className="text-gray-300 mb-6">{product.description}</p>
            
            {/* Product Details */}
            <div className="mb-6">
              <h3 className="text-white font-medium mb-2">Details</h3>
              <div className="text-gray-400">
                <p>Category: {product.category}</p>
                <p>Stock: {product.stock} available</p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 mb-8">
              {product.isRaffle ? (
                raffleStatus === 'upcoming' ? (
                  // For upcoming raffles, show notification button
                  <div className="flex-1">
                    <RaffleNotificationButton
                      productId={product.id}
                      productName={product.name}
                      isRaffleActive={false}
                      raffleStartDate={raffleData?.startDate?.toDate()}
                      raffleEndDate={raffleData?.endDate?.toDate()}
                      onJoinRaffle={handleJoinRaffle}
                      className="w-full"
                    />
                  </div>
                ) : (
                  // For active/ended raffles, show join button
                  <button
                    disabled={!isRaffleActive}
                    onClick={handleJoinRaffle}
                    className={`btn flex-1 ${
                      isRaffleActive
                        ? 'btn-primary'
                        : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <Timer size={18} className="mr-2" />
                    {isRaffleActive ? 'Join Raffle' : 'Raffle Ended'}
                  </button>
                )
              ) : (
                <button 
                  disabled={product.soldOut}
                  onClick={handleAddToCart}
                  className={`btn flex-1 ${
                    product.soldOut
                      ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                      : 'btn-primary'
                  }`}
                >
                  <ShoppingCart size={18} className="mr-2" />
                  {product.soldOut ? 'Sold Out' : 'Add to Cart'}
                </button>
              )}
              
              <button
                className="btn-outline"
                onClick={() => addToWishlist(product)}
              >
                <Heart size={18} className="mr-2" />
                Wishlist
              </button>
              
              <button className="btn-outline sm:flex-grow-0">
                <Share2 size={18} />
              </button>
            </div>

            {/* POP UP */}
            {showPopup && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
                <div className="bg-gray-900 rounded-lg p-8 shadow-lg max-w-sm w-full text-center">
                  <h2 className="text-xl font-bold text-white mb-4">Added to Cart!</h2>
                  <p className="text-gray-300 mb-6">
                    {product.name} has been added to your cart.
                  </p>
                  <div className="flex justify-center gap-4">
                    <button
                      className="btn btn-primary"
                      onClick={() => setShowPopup(false)}
                    >
                      Continue Shopping
                    </button>
                    <Link
                      href="/cart"
                      className="btn btn-outline"
                      onClick={() => setShowPopup(false)}
                    >
                      View Cart
                    </Link>
                  </div>
                </div>
              </div>
            )}
            
            <div className="border-t border-gray-800 pt-6">
              <div className="mb-4">
                <h3 className="text-white font-medium mb-2">Category</h3>
                <p className="text-gray-400 capitalize">{product.category}</p>
              </div>

              <div>
                <h3 className="text-white font-medium mb-2">Created</h3>
                <p className="text-gray-400">{product.createdAt?.toDate().toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Related Products */}
        <div className="mt-20">
          <h2 className="heading-md text-white mb-8">You Might Also Like</h2>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProducts
              .filter(p => !p.isRaffle)
              .slice(0, 4)
              .map(relatedProduct => (
                <motion.div
                  key={relatedProduct.id}
                  whileHover={{ y: -5 }}
                  transition={{ duration: 0.3 }}
                >
                  <Link href={`/products/${relatedProduct.id}`} className="block">
                    <div className="bg-gray-900 rounded-lg overflow-hidden">
                      <div className="aspect-w-1 aspect-h-1 relative">
                        <img 
                          src={relatedProduct.image} 
                          alt={relatedProduct.name}
                          className="w-full h-48 object-cover"
                        />
                      </div>
                      <div className="p-4">
                        <h3 className="text-white font-medium mb-1">{relatedProduct.name}</h3>
                        <span className="text-green-400 font-semibold">${relatedProduct.price.toFixed(2)}</span>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProductDetail;

