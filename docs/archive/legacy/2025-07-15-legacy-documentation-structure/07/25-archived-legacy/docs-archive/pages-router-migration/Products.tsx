'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Filter, Search, X } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import ProductCard from '../components/products/ProductCard';
import { getProducts, Product } from '../lib/firestore';
import ShopModeToggle, { ShopMode } from '../components/shop/ShopModeToggle';
import PointBalanceWidget from '../components/shop/PointBalanceWidget';

import ShopGamification, { useShopGamification } from '../components/gamification/ShopGamification';
import { GamificationErrorBoundary } from '../components/gamification/error/GamificationErrorBoundary';
import { OfflineIndicator } from '../components/gamification/error/OfflineIndicator';
import { triggerAchievementNotification } from '../hooks/useAchievementNotifications';
import { PageLoadingSkeleton, ProductGridSkeleton } from '../components/ui/SkeletonLoader';
import Pagination from '../components/ui/Pagination';
import { useUser } from '../lib/useUser';
import { usePointHistory } from '../hooks/usePointHistory';


// Enhanced filter types for better UX
type FilterOption =
  | 'all'
  | 'available'
  | 'limited'
  | 'sold-out'
  | 'raffle'
  | 'sale'
  | 'pre-order'     // For upcoming releases
  | 'exclusive'     // For rewards-only items
  | 'in-stock'      // Immediate availability
  | 'low-stock';    // Limited quantity

type CategoryOption =
  | 'all'
  | 'artisan'       // Parent category
  | 'resin'         // Most common type
  | 'sculpted'      // Artistic focus
  | 'metal'         // CNC, aluminum, brass
  | 'wood'          // Wooden keycaps
  | 'accessories'   // Tools, pullers, stands
  | 'sets'          // Multi-key collections
  | 'singles'       // Individual keycaps
  | 'raffle'
  | 'giveaway'
  | 'limited-edition';

type SortOption =
  | 'newest'
  | 'oldest'
  | 'price-low'
  | 'price-high'
  | 'points-low'    // For point-based pricing
  | 'points-high'
  | 'name'
  | 'popularity'    // Most redeemed/viewed
  | 'ending-soon'   // For limited time items
  | 'rarity';       // Scarcity-based sorting

// Filter groups for better organization
interface FilterGroups {
  availability: FilterOption[];
  pricing: FilterOption[];
  stock: FilterOption[];
}

// Advanced filters interface
interface AdvancedFilters {
  pointRange: [number, number];
  maker: string[];
  compatibility: ('cherry-mx' | 'choc' | 'alps')[];
  profile: ('cherry' | 'oem' | 'sa' | 'xda')[];
  colorway: string[];
  showCounts: boolean;
}

const Products: React.FC = () => {
  const searchParams = useSearchParams();
  const { user } = useUser();
  const { trackAction } = useShopGamification();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterOption, setFilterOption] = useState<FilterOption>('all');
  const [categoryOption, setCategoryOption] = useState<CategoryOption>('all');
  const [sortOption, setSortOption] = useState<SortOption>('newest');
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [shopMode, setShopMode] = useState<ShopMode>('regular');

  // Enhanced filter state - Phase 2: Default collapsed state
  const [expandedSections, setExpandedSections] = useState({
    availability: false,  // Collapsed by default
    categories: false,    // Collapsed by default
    advanced: false       // Keep collapsed by default
  });

  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilters>({
    pointRange: [0, 1000],
    maker: [],
    compatibility: [],
    profile: [],
    colorway: [],
    showCounts: true
  });

  // Phase 3: Advanced filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMakers, setSelectedMakers] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  // Get user's point balance for reward shop
  const {
    currentBalance,
    totalEarned,
    totalRedeemed,
    loading: balanceLoading
  } = usePointHistory(user?.uid || null);

  // Phase 2: Context-aware initialization and URL parameters
  useEffect(() => {
    const category = searchParams?.get('category');
    const sort = searchParams?.get('sort');
    const filter = searchParams?.get('filter');
    const sale = searchParams?.get('sale');
    const userType = searchParams?.get('userType') as 'new' | 'collector' | 'budget' | null;

    // Apply context-aware defaults for new users
    if (userType && !category && !filter && !sort) {
      const defaults = getContextualDefaults(userType);
      setFilterOption(defaults.filter);
      setCategoryOption(defaults.category);
      setSortOption(defaults.sort);

      // Auto-expand relevant sections for guided experience
      if (defaults.filter !== 'all') {
        setExpandedSections(prev => ({ ...prev, availability: true }));
      }
      if (defaults.category !== 'all') {
        setExpandedSections(prev => ({ ...prev, categories: true }));
      }
      return;
    }

    // Handle URL parameters
    if (category === 'raffle') {
      setCategoryOption('raffle');
      setFilterOption('raffle');
      setExpandedSections(prev => ({ ...prev, availability: true, categories: true }));
    } else if (category && ['resin', 'sculpted', 'metal', 'wood', 'accessories', 'sets', 'singles', 'artisan', 'limited-edition'].includes(category.toLowerCase())) {
      setCategoryOption(category.toLowerCase() as CategoryOption);
      setExpandedSections(prev => ({ ...prev, categories: true }));
    }

    if (sort && ['newest', 'oldest', 'price-low', 'price-high', 'points-low', 'points-high', 'popularity', 'ending-soon', 'rarity', 'name'].includes(sort)) {
      setSortOption(sort as SortOption);
    }

    if (filter && ['available', 'limited', 'sold-out', 'raffle', 'sale', 'pre-order', 'exclusive', 'in-stock', 'low-stock'].includes(filter)) {
      setFilterOption(filter as FilterOption);
      setExpandedSections(prev => ({ ...prev, availability: true }));
    }

    // Handle flash sale parameter
    if (sale === 'flash') {
      setFilterOption('sale');
      setExpandedSections(prev => ({ ...prev, availability: true }));
    }
  }, [searchParams]);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const fetchedProducts = await getProducts();
        setProducts(fetchedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);
  
  // Phase 3: Enhanced filtering with advanced options
  const filteredProducts = useMemo(() => {
    let filtered = products;

    // Phase 3: Apply search query first
    if (searchQuery.trim()) {
      filtered = searchProducts(filtered, searchQuery);
    }

    filtered = filtered.filter(product => {
      // Apply shop mode filter
      if (shopMode === 'rewards') {
        // Only show products that can be purchased with points
        if (!product.pointsCost || product.pointsCost <= 0) return false;
      } else {
        // Regular shop - exclude point-only products
        if (product.pointsOnly) return false;
      }

      // Apply enhanced filter options
      if (filterOption === 'available' && product.soldOut) return false;
      if (filterOption === 'limited' && !product.featured) return false;
      if (filterOption === 'sold-out' && !product.soldOut) return false;
      if (filterOption === 'raffle' && !product.isRaffle) return false;
      if (filterOption === 'sale' && (!product.tags?.includes('sale') && !product.tags?.includes('flash-sale') && !product.featured)) return false;
      if (filterOption === 'pre-order' && !product.tags?.includes('pre-order')) return false;
      if (filterOption === 'exclusive' && (!product.pointsCost || product.pointsCost <= 0)) return false;
      if (filterOption === 'in-stock' && (product.soldOut || (product.stock && product.stock <= 0))) return false;
      if (filterOption === 'low-stock' && (!product.stock || product.stock > 5)) return false;

      // Apply enhanced category filters
      if (categoryOption === 'raffle' && !product.isRaffle) return false;
      if (categoryOption === 'giveaway' && product.category !== 'Giveaway') return false;
      if (categoryOption === 'artisan' && !['resin', 'sculpted', 'metal', 'wood'].includes(product.category.toLowerCase())) return false;
      if (categoryOption === 'resin' && product.category.toLowerCase() !== 'resin') return false;
      if (categoryOption === 'sculpted' && product.category.toLowerCase() !== 'sculpted') return false;
      if (categoryOption === 'metal' && product.category.toLowerCase() !== 'metal') return false;
      if (categoryOption === 'wood' && product.category.toLowerCase() !== 'wood') return false;
      if (categoryOption === 'accessories' && product.category.toLowerCase() !== 'accessories') return false;
      if (categoryOption === 'sets' && !product.tags?.includes('set')) return false;
      if (categoryOption === 'singles' && product.tags?.includes('set')) return false;
      if (categoryOption === 'limited-edition' && !product.featured) return false;

      // Phase 3: Apply advanced filters
      // Price range filter
      if (product.price < priceRange[0] || product.price > priceRange[1]) return false;

      // Maker filter
      if (selectedMakers.length > 0) {
        const productMakers = product.tags?.filter(tag => tag.startsWith('maker:'))
          .map(tag => tag.replace('maker:', '')) || [];

        // Also check product name for maker
        const name = product.name.toLowerCase();
        if (name.includes('artkey')) productMakers.push('Artkey');
        if (name.includes('dwarf factory')) productMakers.push('Dwarf Factory');
        if (name.includes('jelly key')) productMakers.push('Jelly Key');
        if (name.includes('alpha keycaps')) productMakers.push('Alpha Keycaps');

        const hasSelectedMaker = selectedMakers.some(maker =>
          productMakers.includes(maker)
        );
        if (!hasSelectedMaker) return false;
      }

      // Advanced filters from state
      if (advancedFilters.maker.length > 0) {
        const productMakers = product.tags?.filter(tag => tag.startsWith('maker:'))
          .map(tag => tag.replace('maker:', '')) || [];
        const hasSelectedMaker = advancedFilters.maker.some(maker =>
          productMakers.includes(maker)
        );
        if (!hasSelectedMaker) return false;
      }

      // Compatibility filter
      if (advancedFilters.compatibility.length > 0) {
        const hasCompatibility = advancedFilters.compatibility.some(compat =>
          product.tags?.includes(`compatibility:${compat}`)
        );
        if (!hasCompatibility) return false;
      }

      // Profile filter
      if (advancedFilters.profile.length > 0) {
        const hasProfile = advancedFilters.profile.some(profile =>
          product.tags?.includes(`profile:${profile}`)
        );
        if (!hasProfile) return false;
      }

      // Colorway filter
      if (advancedFilters.colorway.length > 0) {
        const hasColorway = advancedFilters.colorway.some(colorway =>
          product.tags?.includes(`colorway:${colorway}`)
        );
        if (!hasColorway) return false;
      }

      return true;
    });

    // Enhanced sorting with new options
    return filtered.sort((a, b) => {
      switch (sortOption) {
        case 'newest':
          return new Date(b.createdAt?.toDate?.() || b.createdAt || 0).getTime() -
                 new Date(a.createdAt?.toDate?.() || a.createdAt || 0).getTime();
        case 'oldest':
          return new Date(a.createdAt?.toDate?.() || a.createdAt || 0).getTime() -
                 new Date(b.createdAt?.toDate?.() || b.createdAt || 0).getTime();
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'points-low':
          return (a.pointsCost || 0) - (b.pointsCost || 0);
        case 'points-high':
          return (b.pointsCost || 0) - (a.pointsCost || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'popularity':
          return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
        case 'ending-soon':
          // Sort by creation date for now (can be enhanced with actual end dates later)
          return new Date(a.createdAt?.toDate?.() || a.createdAt || 0).getTime() -
                 new Date(b.createdAt?.toDate?.() || b.createdAt || 0).getTime();
        case 'rarity':
          // Sort by stock level (lower stock = more rare)
          return (a.stock || 999) - (b.stock || 999);
        default:
          return 0;
      }
    });
  }, [products, shopMode, filterOption, categoryOption, sortOption, searchQuery, priceRange, selectedMakers, advancedFilters]);

  // Helper function to get filter counts
  const getFilterCounts = useMemo(() => {
    return {
      all: products.length,
      available: products.filter(p => !p.soldOut).length,
      limited: products.filter(p => p.featured).length,
      'sold-out': products.filter(p => p.soldOut).length,
      raffle: products.filter(p => p.isRaffle).length,
      sale: products.filter(p => p.tags?.includes('sale') || p.tags?.includes('flash-sale')).length,
      'pre-order': products.filter(p => p.tags?.includes('pre-order')).length,
      exclusive: products.filter(p => p.pointsCost && p.pointsCost > 0).length,
      'in-stock': products.filter(p => !p.soldOut && (!p.stock || p.stock > 0)).length,
      'low-stock': products.filter(p => p.stock && p.stock <= 5 && p.stock > 0).length,

      // Category counts
      artisan: products.filter(p => ['resin', 'sculpted', 'metal', 'wood'].includes(p.category.toLowerCase())).length,
      resin: products.filter(p => p.category.toLowerCase() === 'resin').length,
      sculpted: products.filter(p => p.category.toLowerCase() === 'sculpted').length,
      metal: products.filter(p => p.category.toLowerCase() === 'metal').length,
      wood: products.filter(p => p.category.toLowerCase() === 'wood').length,
      accessories: products.filter(p => p.category.toLowerCase() === 'accessories').length,
      sets: products.filter(p => p.tags?.includes('set')).length,
      singles: products.filter(p => !p.tags?.includes('set')).length,
      giveaway: products.filter(p => p.category === 'Giveaway').length,
      'limited-edition': products.filter(p => p.featured).length
    };
  }, [products]);

  // Phase 3: Extract available makers from product tags
  const availableMakers = useMemo(() => {
    const makers = new Set<string>();
    products.forEach(product => {
      // Extract makers from tags
      product.tags?.forEach(tag => {
        if (tag.startsWith('maker:')) {
          makers.add(tag.replace('maker:', ''));
        }
      });
      // Also add some common makers based on product names
      const name = product.name.toLowerCase();
      if (name.includes('artkey')) makers.add('Artkey');
      if (name.includes('dwarf factory')) makers.add('Dwarf Factory');
      if (name.includes('jelly key')) makers.add('Jelly Key');
      if (name.includes('alpha keycaps')) makers.add('Alpha Keycaps');
    });
    return Array.from(makers).sort();
  }, [products]);

  // Phase 3: Extract available colorways
  const availableColorways = useMemo(() => {
    const colorways = new Set<string>();
    products.forEach(product => {
      product.tags?.forEach(tag => {
        if (tag.startsWith('colorway:')) {
          colorways.add(tag.replace('colorway:', ''));
        }
      });
    });
    return Array.from(colorways).sort();
  }, [products]);

  // Phase 3: Enhanced search functionality
  const searchProducts = (products: Product[], query: string) => {
    if (!query.trim()) return products;

    const searchTerms = query.toLowerCase().split(' ');
    return products.filter(product => {
      const searchableText = [
        product.name,
        product.description,
        product.category,
        ...(product.tags || [])
      ].join(' ').toLowerCase();

      return searchTerms.every(term => searchableText.includes(term));
    });
  };

  // Phase 2: Enhanced smart sort with more sophisticated logic
  const getSmartSort = (filter: FilterOption, category: CategoryOption): SortOption => {
    // Context-aware sorting based on filter and category combination
    if (filter === 'raffle') return 'ending-soon';
    if (filter === 'sale') return 'price-low';
    if (filter === 'limited' || filter === 'pre-order') return 'ending-soon';
    if (filter === 'exclusive' || shopMode === 'rewards') return 'points-low';
    if (filter === 'low-stock') return 'rarity';
    if (filter === 'available' && category === 'sets') return 'price-high'; // Sets are usually premium
    if (category === 'limited-edition') return 'ending-soon';
    if (category === 'accessories') return 'price-low'; // Accessories are usually cheaper
    return 'newest';
  };

  // Phase 2: Context-aware default filters based on user patterns
  const getContextualDefaults = (userType: 'new' | 'collector' | 'budget' | 'regular') => {
    switch (userType) {
      case 'new':
        return { filter: 'available' as FilterOption, category: 'artisan' as CategoryOption, sort: 'popularity' as SortOption };
      case 'collector':
        return { filter: 'limited' as FilterOption, category: 'limited-edition' as CategoryOption, sort: 'ending-soon' as SortOption };
      case 'budget':
        return { filter: 'sale' as FilterOption, category: 'singles' as CategoryOption, sort: 'price-low' as SortOption };
      default:
        return { filter: 'all' as FilterOption, category: 'all' as CategoryOption, sort: 'newest' as SortOption };
    }
  };

  // Phase 2: Intelligent filter combination logic
  const getSmartFilterSuggestions = (currentFilter: FilterOption, currentCategory: CategoryOption) => {
    const suggestions: { filter?: FilterOption; category?: CategoryOption; reason: string }[] = [];

    // Smart combinations based on current selection
    if (currentFilter === 'raffle' && currentCategory === 'all') {
      suggestions.push({ category: 'limited-edition', reason: 'Raffle items are usually limited edition' });
    }

    if (currentFilter === 'sale' && currentCategory === 'all') {
      suggestions.push({ category: 'singles', reason: 'Single keycaps often have better sale prices' });
    }

    if (currentCategory === 'sets' && currentFilter === 'all') {
      suggestions.push({ filter: 'available', reason: 'Check availability for complete sets' });
    }

    if (currentFilter === 'exclusive' && currentCategory === 'all') {
      suggestions.push({ category: 'limited-edition', reason: 'Exclusive items are often limited edition' });
    }

    if (currentCategory === 'accessories' && currentFilter === 'all') {
      suggestions.push({ filter: 'in-stock', reason: 'Accessories are usually in stock' });
    }

    return suggestions;
  };

  // Phase 2: Auto-apply smart filter combinations
  const applySmartFilterCombination = (newFilter: FilterOption) => {
    // Auto-expand relevant sections when filters are applied
    if (newFilter !== 'all') {
      setExpandedSections(prev => ({ ...prev, availability: true }));
    }

    // Smart category suggestions
    if (newFilter === 'raffle' && categoryOption === 'all') {
      setCategoryOption('raffle');
      setExpandedSections(prev => ({ ...prev, categories: true }));
    }

    if (newFilter === 'exclusive' && categoryOption === 'all') {
      setCategoryOption('limited-edition');
      setExpandedSections(prev => ({ ...prev, categories: true }));
    }

    // Auto-apply smart sort
    const smartSort = getSmartSort(newFilter, categoryOption);
    if (smartSort !== sortOption) {
      setSortOption(smartSort);
    }
  };

  // Phase 2: Smart category combination logic
  const applySmartCategoryCombination = (newCategory: CategoryOption) => {
    // Auto-expand relevant sections when categories are applied
    if (newCategory !== 'all') {
      setExpandedSections(prev => ({ ...prev, categories: true }));
    }

    // Smart filter suggestions
    if (newCategory === 'limited-edition' && filterOption === 'all') {
      setFilterOption('limited');
      setExpandedSections(prev => ({ ...prev, availability: true }));
    }

    if (newCategory === 'accessories' && filterOption === 'all') {
      setFilterOption('in-stock');
      setExpandedSections(prev => ({ ...prev, availability: true }));
    }

    // Auto-apply smart sort
    const smartSort = getSmartSort(filterOption, newCategory);
    if (smartSort !== sortOption) {
      setSortOption(smartSort);
    }
  };

  // Toggle expanded sections
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Calculate pagination values
  const totalPages = Math.ceil(filteredProducts.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [shopMode, filterOption, categoryOption]);

  // Phase 2: Smart behavior when switching shop modes
  useEffect(() => {
    if (shopMode === 'rewards') {
      // Auto-suggest exclusive filter for rewards mode
      if (filterOption === 'all') {
        setFilterOption('exclusive');
        setExpandedSections(prev => ({ ...prev, availability: true }));
      }
      // Auto-apply points-based sorting
      if (!['points-low', 'points-high'].includes(sortOption)) {
        setSortOption('points-low');
      }
    } else {
      // Reset exclusive filter when leaving rewards mode
      if (filterOption === 'exclusive') {
        setFilterOption('all');
      }
    }
  }, [shopMode]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };
  
  // Show skeleton loading for initial load
  if (loading && products.length === 0) {
    return <PageLoadingSkeleton />;
  }

  return (
    <GamificationErrorBoundary>
      <OfflineIndicator showDetails={true} />
      <motion.main
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="pt-24 pb-20"
        role="main"
        aria-label="Shop page"
      >
      {/* Enhanced Fixed Left Sidebar - Desktop */}
      <aside className="hidden xl:block fixed left-0 top-16 h-screen w-80 bg-gray-950 border-r border-gray-800 z-sticky overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-900">
        <div className="p-6 space-y-4">
          {/* Point Balance Widget - only show for logged in users */}
          {user && (
            <PointBalanceWidget
              balance={currentBalance || 0}
              totalEarned={totalEarned || 0}
              totalRedeemed={totalRedeemed || 0}
              loading={balanceLoading}
              showStats={true}
            />
          )}

          {/* Phase 2: Reward Shop Mode Toggle */}
          {user && (
            <div className="bg-gray-900 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-white">Shop Mode</h3>
                <div className="flex items-center space-x-2">
                  <span className={`text-xs ${shopMode === 'regular' ? 'text-purple-400' : 'text-gray-500'}`}>
                    Regular
                  </span>
                  <button
                    onClick={() => setShopMode(shopMode === 'regular' ? 'rewards' : 'regular')}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 ${
                      shopMode === 'rewards' ? 'bg-purple-600' : 'bg-gray-600'
                    }`}
                    role="switch"
                    aria-checked={shopMode === 'rewards'}
                    aria-label="Toggle reward shop mode"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        shopMode === 'rewards' ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                  <span className={`text-xs ${shopMode === 'rewards' ? 'text-purple-400' : 'text-gray-500'}`}>
                    Rewards
                  </span>
                </div>
              </div>
              <p className="text-xs text-gray-400">
                {shopMode === 'rewards'
                  ? 'Browse products available for points redemption'
                  : 'Browse all products for regular purchase'
                }
              </p>
            </div>
          )}

          {/* Phase 3: Advanced Search Bar */}
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <h3 className="text-sm font-medium text-white">Search Products</h3>
              <button
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
              >
                {showAdvancedSearch ? 'Simple' : 'Advanced'}
              </button>
            </div>

            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by name, maker, colorway..."
                className="w-full px-3 py-2 pl-9 text-sm bg-gray-800 text-white border border-gray-600 rounded-lg focus:outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
              />
              <svg
                className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-2 top-2 p-1 text-gray-400 hover:text-white transition-colors"
                >
                  <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>

            {/* Advanced Search Options */}
            {showAdvancedSearch && (
              <div className="mt-4 space-y-3 pt-3 border-t border-gray-700">
                {/* Price Range */}
                <div>
                  <label className="block text-xs text-gray-300 mb-2">Price Range</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      value={priceRange[0]}
                      onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                      className="w-20 px-2 py-1 text-xs bg-gray-800 text-white border border-gray-600 rounded"
                      placeholder="Min"
                    />
                    <span className="text-gray-500 text-xs">-</span>
                    <input
                      type="number"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 1000])}
                      className="w-20 px-2 py-1 text-xs bg-gray-800 text-white border border-gray-600 rounded"
                      placeholder="Max"
                    />
                  </div>
                </div>

                {/* Maker Selection */}
                {availableMakers.length > 0 && (
                  <div>
                    <label className="block text-xs text-gray-300 mb-2">Makers</label>
                    <div className="max-h-24 overflow-y-auto space-y-1">
                      {availableMakers.slice(0, 5).map(maker => (
                        <label key={maker} className="flex items-center text-xs">
                          <input
                            type="checkbox"
                            checked={selectedMakers.includes(maker)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedMakers([...selectedMakers, maker]);
                              } else {
                                setSelectedMakers(selectedMakers.filter(m => m !== maker));
                              }
                            }}
                            className="w-3 h-3 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 mr-2"
                          />
                          <span className="text-gray-300">{maker}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Phase 3: Active Filters Summary */}
          {(() => {
            const activeFilters = [];
            if (filterOption !== 'all') activeFilters.push(`Filter: ${filterOption}`);
            if (categoryOption !== 'all') activeFilters.push(`Category: ${categoryOption}`);
            if (searchQuery) activeFilters.push(`Search: "${searchQuery}"`);
            if (selectedMakers.length > 0) activeFilters.push(`Makers: ${selectedMakers.length}`);
            if (priceRange[0] > 0 || priceRange[1] < 1000) activeFilters.push('Price Range');
            if (advancedFilters.compatibility.length > 0) activeFilters.push(`Compatibility: ${advancedFilters.compatibility.length}`);
            if (advancedFilters.profile.length > 0) activeFilters.push(`Profile: ${advancedFilters.profile.length}`);
            if (advancedFilters.colorway.length > 0) activeFilters.push(`Colorways: ${advancedFilters.colorway.length}`);

            return activeFilters.length > 0 ? (
              <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-lg p-3 mb-4 border border-purple-500/20">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-xs font-medium text-purple-300">Active Filters ({activeFilters.length})</h4>
                  <span className="text-xs text-gray-400">{filteredProducts.length} results</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {activeFilters.slice(0, 3).map((filter, index) => (
                    <span key={index} className="px-2 py-0.5 text-xs bg-purple-600/20 text-purple-300 rounded-full">
                      {filter}
                    </span>
                  ))}
                  {activeFilters.length > 3 && (
                    <span className="px-2 py-0.5 text-xs bg-gray-600/20 text-gray-400 rounded-full">
                      +{activeFilters.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            ) : null;
          })()}

          {/* Quick Actions */}
          <div className="flex gap-2 mb-4">
            <button
              onClick={() => {
                setFilterOption('all');
                setCategoryOption('all');
                setSearchQuery('');
                setSelectedMakers([]);
                setPriceRange([0, 1000]);
                setAdvancedFilters({
                  pointRange: [0, 1000],
                  maker: [],
                  compatibility: [],
                  profile: [],
                  colorway: [],
                  showCounts: true
                });
                setExpandedSections({ availability: false, categories: false, advanced: false });
              }}
              className="flex-1 px-3 py-1.5 text-xs bg-gray-800 text-gray-300 rounded-lg hover:bg-gray-700 transition-all duration-200 hover:scale-105"
              title="Reset all filters to default"
            >
              Clear All
            </button>
            <button
              onClick={() => {
                const smartSort = getSmartSort(filterOption, categoryOption);
                setSortOption(smartSort);
              }}
              className="flex-1 px-3 py-1.5 text-xs bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-500 hover:to-purple-600 transition-all duration-200 hover:scale-105"
              title={`Apply smart sort for ${filterOption} filter and ${categoryOption} category`}
            >
              Smart Sort
            </button>
          </div>

          {/* Phase 2: Smart Suggestions */}
          {(() => {
            const suggestions = getSmartFilterSuggestions(filterOption, categoryOption);
            return suggestions.length > 0 && (filterOption !== 'all' || categoryOption !== 'all') ? (
              <div className="bg-gradient-to-r from-purple-900/30 to-blue-900/30 rounded-lg p-3 mb-4 border border-purple-500/20">
                <h4 className="text-xs font-medium text-purple-300 mb-2 flex items-center">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2"></span>
                  Smart Suggestions
                </h4>
                <div className="space-y-1">
                  {suggestions.slice(0, 2).map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        if (suggestion.filter) {
                          setFilterOption(suggestion.filter);
                          applySmartFilterCombination(suggestion.filter);
                        }
                        if (suggestion.category) {
                          setCategoryOption(suggestion.category);
                          applySmartCategoryCombination(suggestion.category);
                        }
                      }}
                      className="block w-full text-left text-xs text-gray-300 hover:text-purple-300 transition-colors"
                    >
                      <span className="text-purple-400">→</span> {suggestion.reason}
                    </button>
                  ))}
                </div>
              </div>
            ) : null;
          })()}

          {/* Availability Filters - Expandable */}
          <div className="bg-gray-900 rounded-lg overflow-hidden">
            <button
              onClick={() => toggleSection('availability')}
              className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium">Availability</span>
                {filterOption !== 'all' && (
                  <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                )}
              </div>
              <span className={`transform transition-transform duration-200 ${expandedSections.availability ? 'rotate-180' : ''}`}>
                ▼
              </span>
            </button>

            {expandedSections.availability && (
              <div className="px-4 pb-4 space-y-2">
                {[
                  { key: 'all', label: 'All Products', count: getFilterCounts.all },
                  { key: 'available', label: 'Available', count: getFilterCounts.available },
                  { key: 'in-stock', label: 'In Stock', count: getFilterCounts['in-stock'] },
                  { key: 'limited', label: 'Limited Edition', count: getFilterCounts.limited },
                  { key: 'low-stock', label: 'Low Stock', count: getFilterCounts['low-stock'] },
                  { key: 'pre-order', label: 'Pre-Order', count: getFilterCounts['pre-order'] },
                  { key: 'exclusive', label: 'Exclusive', count: getFilterCounts.exclusive },
                  { key: 'sale', label: '🔥 Sale', count: getFilterCounts.sale, special: true },
                  { key: 'raffle', label: 'Raffle', count: getFilterCounts.raffle },
                  { key: 'sold-out', label: 'Sold Out', count: getFilterCounts['sold-out'] }
                ].map(({ key, label, count, special }) => (
                  <label key={key} className="flex items-center justify-between group cursor-pointer">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        name="availability"
                        checked={filterOption === key}
                        onChange={() => {
                          setFilterOption(key as FilterOption);
                          applySmartFilterCombination(key as FilterOption);
                        }}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className={`ml-2 text-sm group-hover:text-white transition-colors ${
                        filterOption === key ? 'text-purple-400' : special ? 'text-red-400' : 'text-gray-300'
                      }`}>
                        {label}
                      </span>
                    </div>
                    {advancedFilters.showCounts && (
                      <span className="text-xs text-gray-500 bg-gray-800 px-2 py-0.5 rounded-full">
                        {count}
                      </span>
                    )}
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Category Filters - Expandable */}
          <div className="bg-gray-900 rounded-lg overflow-hidden">
            <button
              onClick={() => toggleSection('categories')}
              className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium">Categories</span>
                {categoryOption !== 'all' && (
                  <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                )}
              </div>
              <span className={`transform transition-transform duration-200 ${expandedSections.categories ? 'rotate-180' : ''}`}>
                ▼
              </span>
            </button>

            {expandedSections.categories && (
              <div className="px-4 pb-4 space-y-2">
                {[
                  { key: 'all', label: 'All Categories', count: getFilterCounts.all },
                  { key: 'artisan', label: 'Artisan Keycaps', count: getFilterCounts.artisan },
                  { key: 'resin', label: 'Resin', count: getFilterCounts.resin },
                  { key: 'sculpted', label: 'Sculpted', count: getFilterCounts.sculpted },
                  { key: 'metal', label: 'Metal', count: getFilterCounts.metal },
                  { key: 'wood', label: 'Wood', count: getFilterCounts.wood },
                  { key: 'accessories', label: 'Accessories', count: getFilterCounts.accessories },
                  { key: 'sets', label: 'Sets', count: getFilterCounts.sets },
                  { key: 'singles', label: 'Singles', count: getFilterCounts.singles },
                  { key: 'limited-edition', label: 'Limited Edition', count: getFilterCounts['limited-edition'] },
                  { key: 'raffle', label: 'Raffle Items', count: getFilterCounts.raffle },
                  { key: 'giveaway', label: 'Giveaway', count: getFilterCounts.giveaway }
                ].map(({ key, label, count }) => (
                  <label key={key} className="flex items-center justify-between group cursor-pointer">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        checked={categoryOption === key}
                        onChange={() => {
                          setCategoryOption(key as CategoryOption);
                          applySmartCategoryCombination(key as CategoryOption);
                        }}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className={`ml-2 text-sm group-hover:text-white transition-colors ${
                        categoryOption === key ? 'text-purple-400' : 'text-gray-300'
                      }`}>
                        {label}
                      </span>
                    </div>
                    {advancedFilters.showCounts && (
                      <span className="text-xs text-gray-500 bg-gray-800 px-2 py-0.5 rounded-full">
                        {count}
                      </span>
                    )}
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Advanced Filters - Expandable */}
          <div className="bg-gray-900 rounded-lg overflow-hidden">
            <button
              onClick={() => toggleSection('advanced')}
              className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-gray-800 transition-colors"
            >
              <span className="font-medium">Advanced Filters</span>
              <span className={`transform transition-transform ${expandedSections.advanced ? 'rotate-180' : ''}`}>
                ▼
              </span>
            </button>

            {expandedSections.advanced && (
              <div className="px-4 pb-4 space-y-4">
                {/* Show Counts Toggle */}
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">Show Counts</span>
                  <input
                    type="checkbox"
                    checked={advancedFilters.showCounts}
                    onChange={(e) => setAdvancedFilters(prev => ({ ...prev, showCounts: e.target.checked }))}
                    className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500"
                  />
                </label>

                {/* Point Range */}
                <div>
                  <label className="block text-sm text-gray-300 mb-2">Point Range</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      value={advancedFilters.pointRange[0]}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        pointRange: [parseInt(e.target.value) || 0, prev.pointRange[1]]
                      }))}
                      className="w-20 px-2 py-1 text-xs bg-gray-800 text-white border border-gray-600 rounded"
                      placeholder="Min"
                    />
                    <span className="text-gray-500">-</span>
                    <input
                      type="number"
                      value={advancedFilters.pointRange[1]}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        pointRange: [prev.pointRange[0], parseInt(e.target.value) || 1000]
                      }))}
                      className="w-20 px-2 py-1 text-xs bg-gray-800 text-white border border-gray-600 rounded"
                      placeholder="Max"
                    />
                  </div>
                </div>

                {/* Phase 3: Compatibility Filter */}
                <div>
                  <label className="block text-sm text-gray-300 mb-2">Switch Compatibility</label>
                  <div className="space-y-1">
                    {['cherry-mx', 'choc', 'alps'].map(compat => (
                      <label key={compat} className="flex items-center text-xs">
                        <input
                          type="checkbox"
                          checked={advancedFilters.compatibility.includes(compat as any)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setAdvancedFilters(prev => ({
                                ...prev,
                                compatibility: [...prev.compatibility, compat as any]
                              }));
                            } else {
                              setAdvancedFilters(prev => ({
                                ...prev,
                                compatibility: prev.compatibility.filter(c => c !== compat)
                              }));
                            }
                          }}
                          className="w-3 h-3 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 mr-2"
                        />
                        <span className="text-gray-300 capitalize">{compat.replace('-', ' ')}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Phase 3: Profile Filter */}
                <div>
                  <label className="block text-sm text-gray-300 mb-2">Keycap Profile</label>
                  <div className="space-y-1">
                    {['cherry', 'oem', 'sa', 'xda'].map(profile => (
                      <label key={profile} className="flex items-center text-xs">
                        <input
                          type="checkbox"
                          checked={advancedFilters.profile.includes(profile as any)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setAdvancedFilters(prev => ({
                                ...prev,
                                profile: [...prev.profile, profile as any]
                              }));
                            } else {
                              setAdvancedFilters(prev => ({
                                ...prev,
                                profile: prev.profile.filter(p => p !== profile)
                              }));
                            }
                          }}
                          className="w-3 h-3 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 mr-2"
                        />
                        <span className="text-gray-300 uppercase">{profile}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Phase 3: Colorway Filter */}
                {availableColorways.length > 0 && (
                  <div>
                    <label className="block text-sm text-gray-300 mb-2">Colorways</label>
                    <div className="max-h-20 overflow-y-auto space-y-1">
                      {availableColorways.slice(0, 5).map(colorway => (
                        <label key={colorway} className="flex items-center text-xs">
                          <input
                            type="checkbox"
                            checked={advancedFilters.colorway.includes(colorway)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setAdvancedFilters(prev => ({
                                  ...prev,
                                  colorway: [...prev.colorway, colorway]
                                }));
                              } else {
                                setAdvancedFilters(prev => ({
                                  ...prev,
                                  colorway: prev.colorway.filter(c => c !== colorway)
                                }));
                              }
                            }}
                            className="w-3 h-3 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 mr-2"
                          />
                          <span className="text-gray-300">{colorway}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                {/* Clear Advanced Filters */}
                <button
                  onClick={() => {
                    setAdvancedFilters({
                      pointRange: [0, 1000],
                      maker: [],
                      compatibility: [],
                      profile: [],
                      colorway: [],
                      showCounts: true
                    });
                    setSelectedMakers([]);
                    setPriceRange([0, 1000]);
                    setSearchQuery('');
                  }}
                  className="w-full px-3 py-1.5 text-xs bg-gray-800 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Clear Advanced Filters
                </button>
              </div>
            )}
          </div>

          {/* Reward Shop Info */}
          {shopMode === 'rewards' && (
            <div className="bg-gray-900 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3">Reward Shop</h3>
              <p className="text-gray-400 text-sm mb-4">
                Redeem your earned points for exclusive rewards and discounts.
              </p>
              <div className="space-y-2 text-sm text-gray-300">
                <p>• Exclusive member-only products</p>
                <p>• Special discount coupons</p>
                <p>• Free shipping rewards</p>
                <p>• Limited edition items</p>
              </div>
            </div>
          )}
        </div>
      </aside>

      {/* Mobile Filters Overlay */}
      {showMobileFilters && (
        <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-modal-backdrop" onClick={() => setShowMobileFilters(false)}>
          <div className="fixed left-0 top-0 h-full w-80 bg-gray-950 overflow-y-auto shadow-2xl z-modal" onClick={(e) => e.stopPropagation()}>
            <div className="p-6 space-y-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Filters</h2>
                <button
                  onClick={() => setShowMobileFilters(false)}
                  className="text-gray-400 hover:text-white touch-target-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-950"
                  aria-label="Close filters"
                >
                  ✕
                </button>
              </div>

              {/* Point Balance Widget - only show for logged in users */}
              {user && (
                <PointBalanceWidget
                  balance={currentBalance || 0}
                  totalEarned={totalEarned || 0}
                  totalRedeemed={totalRedeemed || 0}
                  loading={balanceLoading}
                  showStats={true}
                />
              )}

              {/* Mobile Reward Shop Mode Toggle */}
              {user && (
                <div className="bg-gray-900 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-white">Shop Mode</h3>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs ${shopMode === 'regular' ? 'text-purple-400' : 'text-gray-500'}`}>
                        Regular
                      </span>
                      <button
                        onClick={() => setShopMode(shopMode === 'regular' ? 'rewards' : 'regular')}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 ${
                          shopMode === 'rewards' ? 'bg-purple-600' : 'bg-gray-600'
                        }`}
                        role="switch"
                        aria-checked={shopMode === 'rewards'}
                        aria-label="Toggle reward shop mode"
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            shopMode === 'rewards' ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                      <span className={`text-xs ${shopMode === 'rewards' ? 'text-purple-400' : 'text-gray-500'}`}>
                        Rewards
                      </span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-400">
                    {shopMode === 'rewards'
                      ? 'Browse products available for points redemption'
                      : 'Browse all products for regular purchase'
                    }
                  </p>
                </div>
              )}

              {/* Mobile Filters - Same content as desktop */}
              <div className="bg-gray-900 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Filters</h3>

                {/* Availability Filters */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-300 mb-3">Availability</h4>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-availability"
                        checked={filterOption === 'all'}
                        onChange={() => setFilterOption('all')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">All Products</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-availability"
                        checked={filterOption === 'available'}
                        onChange={() => setFilterOption('available')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">Available</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-availability"
                        checked={filterOption === 'limited'}
                        onChange={() => setFilterOption('limited')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">Limited Edition</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-availability"
                        checked={filterOption === 'raffle'}
                        onChange={() => setFilterOption('raffle')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">Raffle</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-availability"
                        checked={filterOption === 'sale'}
                        onChange={() => setFilterOption('sale')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-red-400">🔥 Sale</span>
                    </label>
                  </div>
                </div>

                {/* Category Filters */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-300 mb-3">Categories</h4>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-category"
                        checked={categoryOption === 'all'}
                        onChange={() => setCategoryOption('all')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">All Categories</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-category"
                        checked={categoryOption === 'resin'}
                        onChange={() => setCategoryOption('resin')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">Resin</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-category"
                        checked={categoryOption === 'sculpted'}
                        onChange={() => setCategoryOption('sculpted')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">Sculpted</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-category"
                        checked={categoryOption === 'raffle'}
                        onChange={() => setCategoryOption('raffle')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">Raffle Items</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobile-category"
                        checked={categoryOption === 'giveaway'}
                        onChange={() => setCategoryOption('giveaway')}
                        className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-300">Giveaway</span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Reward Shop Info */}
              {shopMode === 'rewards' && (
                <div className="bg-gray-900 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-3">Reward Shop</h3>
                  <p className="text-gray-400 text-sm mb-4">
                    Redeem your earned points for exclusive rewards and discounts.
                  </p>
                  <div className="space-y-2 text-sm text-gray-300">
                    <p>• Exclusive member-only products</p>
                    <p>• Special discount coupons</p>
                    <p>• Free shipping rewards</p>
                    <p>• Limited edition items</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content Area - Adjusted for left sidebar */}
      <div className="xl:ml-80 transition-all duration-300 ease-in-out">
        <div className="container-custom">
          {/* Mobile Filters Toggle */}
          <div className="xl:hidden mb-6">
            <button
              onClick={() => setShowMobileFilters(!showMobileFilters)}
              className="w-full bg-gray-900 text-white px-3 sm:px-4 py-3 rounded-lg flex items-center justify-between touch-target hover:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-950 text-sm sm:text-base"
              aria-expanded={showMobileFilters}
              aria-controls="mobile-filters"
              aria-label={showMobileFilters ? "Hide filters" : "Show filters"}
            >
              <span className="font-medium">Filters & Categories</span>
              <span className={`transform transition-transform ${showMobileFilters ? 'rotate-180' : ''}`}>
                ▼
              </span>
            </button>
          </div>
          {/* Phase 3: Enhanced Product Grid Header */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 pb-4 border-b border-gray-800 gap-4">
            <div>
              <h2 className="text-xl font-semibold text-white flex items-center">
                {filterOption === 'sale' ? (
                  <>🔥 Flash Sale Products</>
                ) : searchQuery ? (
                  <>Search Results</>
                ) : shopMode === 'rewards' ? (
                  <>🎁 Reward Shop</>
                ) : (
                  <>All Products</>
                )}
                {loading && (
                  <div className="ml-3 w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                )}
              </h2>
              <div className="flex items-center space-x-4 mt-1">
                <p className="text-sm text-gray-400">
                  {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'} found
                </p>
                {searchQuery && (
                  <span className="text-xs text-purple-400 bg-purple-900/20 px-2 py-0.5 rounded-full">
                    Searching: "{searchQuery}"
                  </span>
                )}
                {shopMode === 'rewards' && (
                  <span className="text-xs text-yellow-400 bg-yellow-900/20 px-2 py-0.5 rounded-full">
                    Points Only
                  </span>
                )}
              </div>
            </div>

            {/* Enhanced Sort Dropdown */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400 hidden sm:block">Sort by:</span>
              <select
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value as SortOption)}
                className="bg-gray-800 text-white border border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors w-full sm:w-auto hover:bg-gray-750"
                aria-label="Sort products by"
              >
                <option value="newest">Recommended</option>
                <option value="popularity">Most Popular</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                {shopMode === 'rewards' && (
                  <>
                    <option value="points-low">Points: Low to High</option>
                    <option value="points-high">Points: High to Low</option>
                  </>
                )}
                <option value="ending-soon">Ending Soon</option>
                <option value="rarity">Rarity (Low Stock)</option>
                <option value="name">Name: A to Z</option>
                <option value="oldest">Oldest First</option>
              </select>
            </div>
          </div>

          {/* Products Grid */}
          <section aria-label="Product catalog" className="product-grid">
            {loading && products.length === 0 ? (
              <ProductGridSkeleton count={9} />
            ) : filteredProducts.length > 0 ? (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6" role="list">
                  {paginatedProducts.map((product, index) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 * (index % 3) }}
                      role="listitem"
                    >
                      <ProductCard product={product} />
                    </motion.div>
                  ))}
                </div>

                {/* Pagination */}
                {filteredProducts.length > pageSize && (
                  <div className="mt-8">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      totalItems={filteredProducts.length}
                      itemsPerPage={pageSize}
                      onPageChange={handlePageChange}
                      onPageSizeChange={handlePageSizeChange}
                      showPageSizeSelector={true}
                      showItemsCount={true}
                      showJumpToPage={filteredProducts.length > 100}
                      variant="default"
                      className="pagination-container"
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12" role="status" aria-live="polite">
                <p className="text-gray-400 text-lg">No products match your current filters.</p>
                <button
                  onClick={() => {
                    setFilterOption('all');
                    setCategoryOption('all');
                  }}
                  className="mt-4 btn-outline"
                  aria-label="Clear all filters and show all products"
                >
                  Reset Filters
                </button>
              </div>
            )}
          </section>
        </div>
      </div>

      {/* Shop Gamification */}
      <ShopGamification
        enableNotifications={true}
        trackActions={true}
      />
    </motion.main>
    </GamificationErrorBoundary>
  );
};

export default Products;