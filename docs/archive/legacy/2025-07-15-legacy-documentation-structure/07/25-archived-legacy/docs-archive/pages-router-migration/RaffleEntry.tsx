'use client'

/**
 * Raffle Entry Page
 * 
 * Updated to use the unified raffle entry component
 * 
 * <AUTHOR> Team
 */

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useUser } from '../lib/useUser'
import UnifiedRaffleEntry from '../components/raffle/UnifiedRaffleEntry'

/**
 * Main raffle entry page component
 */
const RaffleEntry: React.FC = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const selectedProductId = searchParams?.get('productId')
  const { user } = useUser()
  
  const [showRaffleForm, setShowRaffleForm] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/auth?redirect=' + encodeURIComponent('/raffle-entry' + (selectedProductId ? `?productId=${selectedProductId}` : '')))
      return
    }
    
    // Show the raffle form once user is authenticated
    setShowRaffleForm(true)
  }, [user, router, selectedProductId])

  /**
   * Handle raffle form close
   */
  const handleClose = () => {
    router.push('/shop?category=raffle')
  }

  // Show loading while checking authentication
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-white">Checking authentication...</p>
        </div>
      </div>
    )
  }

  // Show the unified raffle entry form
  if (showRaffleForm) {
    return (
      <UnifiedRaffleEntry
        onClose={handleClose}
        preSelectedProductId={selectedProductId || undefined}
      />
    )
  }

  return null
}

export default RaffleEntry
