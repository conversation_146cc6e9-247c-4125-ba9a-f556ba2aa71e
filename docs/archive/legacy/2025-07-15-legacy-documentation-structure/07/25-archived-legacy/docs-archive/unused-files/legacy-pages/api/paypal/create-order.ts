// src/pages/api/paypal/create-order.ts

import type { NextApiRequest, NextApiResponse } from 'next';
// @ts-ignore - PayPal SDK doesn't have type declarations
import paypal from '@paypal/checkout-server-sdk';

// Set up PayPal environment with your sandbox credentials
const environment = new paypal.core.SandboxEnvironment(
  process.env.PAYPAL_CLIENT_ID!,
  process.env.PAYPAL_CLIENT_SECRET!
);
const client = new paypal.core.PayPalHttpClient(environment);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const request = new paypal.orders.OrdersCreateRequest();
    request.prefer('return=representation');
    request.requestBody({
      intent: 'CAPTURE',
      purchase_units: [
        {
          amount: {
            currency_code: 'USD', // Make sure this matches your business account's country/currency
            value: '10.00',
          },
        },
      ],
    });

    const order = await client.execute(request);
    res.status(200).json({ id: order.result.id });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
}