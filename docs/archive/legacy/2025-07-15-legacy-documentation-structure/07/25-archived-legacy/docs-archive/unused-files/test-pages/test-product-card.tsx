/**
 * Test page for ProductCard layout verification
 * 
 * This page demonstrates the new ProductCard layout matching the reference design
 */

import React from 'react';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import ProductCardTest from '../src/components/products/ProductCardTest';

/**
 * Test page component for ProductCard layout
 */
const TestProductCardPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>ProductCard Layout Test - Syndicaps</title>
        <meta name="description" content="Test page for ProductCard layout verification" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <ProductCardTest />
    </>
  );
};

export const getStaticProps: GetStaticProps = async () => {
  return {
    props: {},
  };
};

export default TestProductCardPage;
