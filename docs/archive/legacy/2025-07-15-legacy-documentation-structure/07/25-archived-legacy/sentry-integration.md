# Sentry Error Tracking Integration

**Document Version**: 1.0  
**Date**: 2025-07-03  
**Author**: Syndicaps Development Team  
**Status**: ✅ **IMPLEMENTED**  

---

## 📋 Overview

This document outlines the comprehensive Sentry error tracking integration implemented for the Syndicaps website. Sentry provides centralized error monitoring, performance tracking, and user context for better debugging and system reliability.

### 🎯 **Implementation Goals**
- ✅ Centralized error tracking and monitoring
- ✅ Real-time error alerts and notifications  
- ✅ User context and breadcrumb tracking
- ✅ Performance monitoring and optimization
- ✅ Integration with existing error handling system

---

## 🚀 **Implementation Details**

### **1. Package Installation**
```bash
npm install @sentry/nextjs --legacy-peer-deps
```

### **2. Configuration Files**

#### **Client Configuration** (`sentry.client.config.js`)
- Browser-side error tracking
- Session replay integration
- User interaction monitoring
- Error filtering for browser extensions

#### **Server Configuration** (`sentry.server.config.js`)
- Server-side error tracking
- API error monitoring
- Node.js integration
- Server performance tracking

#### **Edge Configuration** (`sentry.edge.config.js`)
- Middleware error tracking
- Edge runtime monitoring
- Minimal configuration for performance

### **3. Next.js Integration**
Updated `next.config.js` with Sentry webpack plugin:
- Automatic source map upload
- Release tracking
- Build-time integration
- Production optimizations

---

## 🔧 **Environment Variables**

Add to your `.env.local` file:

```bash
# Sentry Configuration
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project
SENTRY_AUTH_TOKEN=your_sentry_auth_token
NEXT_PUBLIC_SENTRY_RELEASE=development
```

### **Getting Sentry Credentials**
1. Create account at [sentry.io](https://sentry.io)
2. Create new project for "Next.js"
3. Copy DSN from project settings
4. Generate auth token for source map uploads

---

## 📚 **Usage Guide**

### **1. Using the Sentry Hook**

```typescript
import { useSentry } from '@/hooks/useSentry';

function MyComponent() {
  const sentry = useSentry();

  const handleError = () => {
    try {
      // Some operation that might fail
    } catch (error) {
      sentry.captureError(error, { component: 'MyComponent' });
    }
  };

  const handleUserAction = () => {
    sentry.trackAction('button_click', { buttonName: 'Submit' });
  };

  return (
    <button onClick={handleUserAction}>
      Submit
    </button>
  );
}
```

### **2. Error Boundaries**

Error boundaries automatically report to Sentry:

```typescript
import { ErrorBoundary } from '@/components/error/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary>
      <MyComponent />
    </ErrorBoundary>
  );
}
```

### **3. User Context Tracking**

```typescript
import { initializeSentryUser, clearSentryUser } from '@/lib/sentry/init';

// On login
initializeSentryUser({
  id: user.uid,
  email: user.email,
  role: user.role
});

// On logout
clearSentryUser();
```

### **4. E-commerce Tracking**

```typescript
const sentry = useSentry();

// Track purchases
sentry.trackPurchase({
  orderId: 'order-123',
  total: 99.99,
  items: ['product-1', 'product-2']
});

// Track cart actions
sentry.trackAddToCart('product-123', {
  productName: 'Cool Product',
  price: 29.99
});
```

### **5. Performance Monitoring**

```typescript
const sentry = useSentry();

// Start performance transaction
const transaction = sentry.startPerformanceTransaction('page_load', 'navigation');

// Finish when done
transaction?.setStatus('ok');
transaction?.finish();
```

---

## 🧪 **Testing**

### **Test Page**
Visit `/test/sentry` in development to test integration:
- Error tracking
- Message logging
- User action tracking
- API call monitoring
- E-commerce events
- Gamification tracking
- Performance monitoring

### **Manual Testing**
```typescript
// Test error capture
throw new Error('Test error for Sentry');

// Test message logging
sentry.captureMessage('Test message', 'info');

// Test user action
sentry.trackAction('test_action');
```

---

## 🔍 **Error Filtering**

Sentry is configured to filter out non-actionable errors:

### **Client-side Filtering**
- Browser extension errors
- Ad blocker related errors
- Network request failures
- Chunk loading errors

### **Server-side Filtering**
- Expected Firebase connection errors
- Network timeout errors
- Non-critical server errors

### **Custom Filtering**
```typescript
// In sentry config files
beforeSend(event, hint) {
  // Custom filtering logic
  if (shouldIgnoreError(event)) {
    return null;
  }
  return event;
}
```

---

## 📊 **Monitoring Dashboard**

### **Key Metrics to Monitor**
1. **Error Rate**: Percentage of sessions with errors
2. **Performance**: Page load times and API response times
3. **User Impact**: Number of users affected by errors
4. **Release Health**: Error rates by deployment version

### **Alerts Configuration**
- Critical errors: Immediate notification
- High error rate: Alert if >1% of sessions
- Performance degradation: Alert if >3s load time
- New error types: Alert on first occurrence

---

## 🔒 **Security & Privacy**

### **Data Protection**
- Sensitive data filtering in place
- User PII scrubbing enabled
- Session replay masks sensitive inputs
- GDPR compliance considerations

### **Configuration**
```typescript
// Mask sensitive data
replaysIntegration({
  maskAllText: true,
  blockAllMedia: true,
});
```

---

## 🚀 **Production Deployment**

### **Pre-deployment Checklist**
- [ ] Sentry DSN configured
- [ ] Source maps upload working
- [ ] Error filtering tested
- [ ] Performance monitoring enabled
- [ ] User context tracking verified

### **Post-deployment Verification**
1. Check Sentry dashboard for incoming events
2. Verify source maps are uploaded
3. Test error reporting with controlled errors
4. Monitor performance metrics
5. Validate user context tracking

---

## 🔧 **Troubleshooting**

### **Common Issues**

#### **No Events in Sentry**
- Check DSN configuration
- Verify network connectivity
- Check browser console for Sentry errors
- Ensure Sentry is initialized

#### **Source Maps Not Working**
- Verify SENTRY_AUTH_TOKEN
- Check build output for upload logs
- Ensure correct org/project settings

#### **Too Many Events**
- Review error filtering configuration
- Adjust sample rates
- Implement custom filtering

### **Debug Commands**
```bash
# Check Sentry status
npm run build -- --debug

# Test Sentry configuration
node -e "console.log(require('./sentry.client.config.js'))"
```

---

## 📈 **Performance Impact**

### **Bundle Size Impact**
- Client bundle: +~50KB gzipped
- Server bundle: +~30KB
- Edge bundle: +~20KB

### **Runtime Performance**
- Minimal impact on page load
- Async error reporting
- Configurable sampling rates

---

## 🔄 **Integration Status**

### ✅ **Completed Features**
- [x] Basic error tracking
- [x] Error boundary integration
- [x] User context tracking
- [x] Performance monitoring
- [x] E-commerce event tracking
- [x] Gamification event tracking
- [x] Custom error filtering
- [x] Source map upload
- [x] Test page implementation

### 🔄 **Future Enhancements**
- [ ] Advanced alerting rules
- [ ] Custom dashboard creation
- [ ] Integration with CI/CD pipeline
- [ ] Automated error triage
- [ ] Performance budget alerts

---

## 📞 **Support**

For Sentry-related issues:
1. Check this documentation
2. Visit [Sentry Documentation](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
3. Contact development team
4. Check Sentry community forums

---

**Implementation Complete**: Sentry error tracking is now fully integrated and ready for production use. Monitor the dashboard for real-time error tracking and performance insights.
