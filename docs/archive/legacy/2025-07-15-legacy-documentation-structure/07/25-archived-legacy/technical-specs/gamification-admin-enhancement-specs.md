# Gamification Admin Enhancement - Technical Specifications

## Overview

This document provides detailed technical specifications for enhancing the Syndicaps gamification admin interface. It includes component specifications, API endpoints, database schema changes, and implementation guidelines.

## Table of Contents

1. [Component Specifications](#component-specifications)
2. [API Endpoint Specifications](#api-endpoint-specifications)
3. [Database Schema Enhancements](#database-schema-enhancements)
4. [UI/UX Design Specifications](#uiux-design-specifications)
5. [Integration Patterns](#integration-patterns)

---

## 1. Component Specifications

### 1.1 Enhanced Points Management Components

#### AdvancedPointsManager Component
```typescript
interface AdvancedPointsManagerProps {
  initialRules: PointRule[]
  userSegments: UserSegment[]
  onRuleUpdate: (rule: PointRule) => Promise<void>
  onBulkOperation: (operation: BulkOperation) => Promise<void>
}

interface PointRule {
  id: string
  name: string
  description: string
  category: 'purchase' | 'engagement' | 'social' | 'achievement' | 'bonus'
  conditions: RuleCondition[]
  actions: RuleAction[]
  schedule: RuleSchedule
  isActive: boolean
  priority: number
  analytics: RuleAnalytics
  createdAt: Date
  updatedAt: Date
}

interface RuleCondition {
  type: 'user_tier' | 'purchase_amount' | 'time_range' | 'user_segment' | 'custom'
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'in_range'
  value: any
  metadata?: Record<string, any>
}

interface RuleAction {
  type: 'award_points' | 'multiply_points' | 'award_achievement' | 'send_notification'
  value: number | string
  metadata?: Record<string, any>
}
```

#### BulkOperationsCenter Component
```typescript
interface BulkOperationsCenterProps {
  userSegments: UserSegment[]
  onExecuteOperation: (operation: BulkOperationRequest) => Promise<BulkOperationResult>
  onScheduleOperation: (operation: ScheduledOperation) => Promise<void>
}

interface BulkOperationRequest {
  type: 'award_points' | 'deduct_points' | 'set_points' | 'award_achievement'
  targetSegment: string
  value: number | string
  reason: string
  scheduledFor?: Date
  notifyUsers: boolean
}

interface UserSegment {
  id: string
  name: string
  description: string
  criteria: SegmentCriteria[]
  userCount: number
  lastUpdated: Date
}

interface SegmentCriteria {
  field: 'tier' | 'points' | 'last_activity' | 'registration_date' | 'purchase_history'
  operator: 'equals' | 'greater_than' | 'less_than' | 'in_range' | 'contains'
  value: any
}
```

#### RealTimeMonitoringDashboard Component
```typescript
interface RealTimeMonitoringProps {
  refreshInterval: number
  alertThresholds: AlertThreshold[]
  onAlertTriggered: (alert: Alert) => void
}

interface AlertThreshold {
  metric: 'transaction_volume' | 'error_rate' | 'response_time' | 'user_activity'
  threshold: number
  comparison: 'above' | 'below'
  timeWindow: number // minutes
}

interface MonitoringMetrics {
  pointTransactions: {
    total: number
    perMinute: number
    errorRate: number
  }
  userActivity: {
    activeUsers: number
    newSignups: number
    engagementRate: number
  }
  systemHealth: {
    responseTime: number
    errorCount: number
    uptime: number
  }
}
```

### 1.2 Enhanced Achievement Management Components

#### AchievementBuilder Component
```typescript
interface AchievementBuilderProps {
  achievement?: Achievement
  onSave: (achievement: AchievementData) => Promise<void>
  onPreview: (achievement: AchievementData) => void
}

interface AchievementData {
  name: string
  description: string
  icon: string
  category: AchievementCategory
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary'
  requirements: AchievementRequirement[]
  rewards: AchievementReward[]
  badge: BadgeDesign
  isSecret: boolean
  isActive: boolean
  validFrom?: Date
  validTo?: Date
}

interface AchievementRequirement {
  type: 'count' | 'value' | 'streak' | 'time_based' | 'conditional'
  target: number
  action: string
  conditions?: RequirementCondition[]
}

interface BadgeDesign {
  template: string
  colors: {
    primary: string
    secondary: string
    accent: string
  }
  animation?: 'none' | 'glow' | 'pulse' | 'rotate'
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}
```

#### ProgressTrackingDashboard Component
```typescript
interface ProgressTrackingProps {
  achievements: Achievement[]
  userProgress: UserAchievementProgress[]
  onUpdateProgress: (userId: string, achievementId: string, progress: number) => Promise<void>
}

interface UserAchievementProgress {
  userId: string
  userName: string
  achievementId: string
  currentProgress: number
  targetProgress: number
  completionPercentage: number
  estimatedCompletion?: Date
  lastUpdated: Date
}
```

### 1.3 Enhanced Reward Shop Components

#### AdvancedRewardManager Component
```typescript
interface AdvancedRewardManagerProps {
  rewards: RewardItem[]
  categories: RewardCategory[]
  onCreateReward: (reward: RewardData) => Promise<void>
  onUpdateReward: (id: string, reward: Partial<RewardData>) => Promise<void>
  onDeleteReward: (id: string) => Promise<void>
}

interface RewardData {
  name: string
  description: string
  pointsCost: number
  category: string
  image?: string
  stock: number
  isActive: boolean
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  estimatedDelivery?: string
  restrictions?: string[]
  dynamicPricing: {
    enabled: boolean
    basePrice: number
    demandMultiplier: number
    stockThreshold: number
  }
  availability: {
    startDate?: Date
    endDate?: Date
    userTiers?: string[]
    maxPerUser?: number
  }
}

interface InventoryTracker {
  rewardId: string
  currentStock: number
  reservedStock: number
  lowStockThreshold: number
  autoRestock: {
    enabled: boolean
    threshold: number
    quantity: number
  }
  stockHistory: StockTransaction[]
}
```

---

## 2. API Endpoint Specifications

### 2.1 Points Management API

#### GET /api/admin/gamification/points/rules
```typescript
// Get all point rules with filtering and pagination
interface GetPointRulesRequest {
  page?: number
  limit?: number
  category?: string
  isActive?: boolean
  search?: string
}

interface GetPointRulesResponse {
  rules: PointRule[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}
```

#### POST /api/admin/gamification/points/rules
```typescript
// Create new point rule
interface CreatePointRuleRequest {
  name: string
  description: string
  category: string
  conditions: RuleCondition[]
  actions: RuleAction[]
  schedule?: RuleSchedule
  isActive: boolean
}

interface CreatePointRuleResponse {
  rule: PointRule
  validationErrors?: ValidationError[]
}
```

#### POST /api/admin/gamification/points/bulk-operations
```typescript
// Execute bulk point operations
interface BulkPointOperationRequest {
  type: 'award' | 'deduct' | 'set'
  targetSegment: string
  amount: number
  reason: string
  notifyUsers: boolean
  scheduledFor?: Date
}

interface BulkPointOperationResponse {
  operationId: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  estimatedCompletion?: Date
  affectedUsers: number
}
```

#### GET /api/admin/gamification/points/analytics
```typescript
// Get points system analytics
interface PointsAnalyticsRequest {
  timeRange: 'day' | 'week' | 'month' | 'quarter' | 'year'
  startDate?: Date
  endDate?: Date
  groupBy?: 'day' | 'week' | 'month'
}

interface PointsAnalyticsResponse {
  totalPointsAwarded: number
  totalPointsSpent: number
  pointsInCirculation: number
  transactionVolume: TimeSeriesData[]
  topEarningActions: ActionStats[]
  userEngagement: EngagementMetrics
}
```

### 2.2 Achievement Management API

#### POST /api/admin/gamification/achievements
```typescript
// Create new achievement
interface CreateAchievementRequest {
  name: string
  description: string
  icon: string
  category: string
  requirements: AchievementRequirement[]
  rewards: AchievementReward[]
  badge: BadgeDesign
  isSecret: boolean
  validFrom?: Date
  validTo?: Date
}

interface CreateAchievementResponse {
  achievement: Achievement
  validationErrors?: ValidationError[]
}
```

#### GET /api/admin/gamification/achievements/progress
```typescript
// Get achievement progress analytics
interface AchievementProgressRequest {
  achievementId?: string
  userId?: string
  timeRange?: string
}

interface AchievementProgressResponse {
  achievements: AchievementProgressStats[]
  userProgress: UserAchievementProgress[]
  completionRates: CompletionRateData[]
}
```

### 2.3 User Segmentation API

#### POST /api/admin/gamification/users/segments
```typescript
// Create user segment
interface CreateSegmentRequest {
  name: string
  description: string
  criteria: SegmentCriteria[]
}

interface CreateSegmentResponse {
  segment: UserSegment
  userCount: number
  previewUsers: UserPreview[]
}
```

#### GET /api/admin/gamification/users/segments/{id}/users
```typescript
// Get users in segment
interface GetSegmentUsersRequest {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

interface GetSegmentUsersResponse {
  users: SegmentUser[]
  pagination: PaginationInfo
  summary: SegmentSummary
}
```

---

## 3. Database Schema Enhancements

### 3.1 New Collections

#### gamificationRules Collection
```typescript
interface GamificationRule {
  id: string
  type: 'points' | 'achievement' | 'reward'
  name: string
  description: string
  conditions: RuleCondition[]
  actions: RuleAction[]
  schedule: {
    startDate?: Timestamp
    endDate?: Timestamp
    timeZone: string
    recurring?: {
      frequency: 'daily' | 'weekly' | 'monthly'
      interval: number
      daysOfWeek?: number[]
    }
  }
  isActive: boolean
  priority: number
  analytics: {
    executionCount: number
    lastExecuted?: Timestamp
    averageExecutionTime: number
    errorCount: number
  }
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

#### userSegments Collection
```typescript
interface UserSegment {
  id: string
  name: string
  description: string
  criteria: SegmentCriteria[]
  userCount: number
  lastCalculated: Timestamp
  isActive: boolean
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

#### bulkOperations Collection
```typescript
interface BulkOperation {
  id: string
  type: 'points' | 'achievement' | 'notification' | 'tier'
  operation: {
    action: string
    value: any
    metadata?: Record<string, any>
  }
  targetSegment: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: {
    total: number
    processed: number
    successful: number
    failed: number
  }
  results?: {
    successfulUsers: string[]
    failedUsers: Array<{
      userId: string
      error: string
    }>
    summary: Record<string, any>
  }
  scheduledFor?: Timestamp
  startedAt?: Timestamp
  completedAt?: Timestamp
  createdBy: string
  createdAt: Timestamp
}
```

### 3.2 Enhanced Existing Collections

#### Enhanced profiles Collection
```typescript
interface EnhancedUserProfile {
  // Existing fields...
  
  // Enhanced gamification fields
  gamification: {
    tier: string
    tierProgress: number
    nextTierThreshold: number
    totalPointsEarned: number
    totalPointsSpent: number
    currentPoints: number
    pointsHistory: {
      daily: number[]
      weekly: number[]
      monthly: number[]
    }
    achievements: {
      total: number
      byCategory: Record<string, number>
      recent: string[]
    }
    engagement: {
      lastActivity: Timestamp
      streaks: {
        login: number
        purchase: number
        social: number
      }
      preferences: {
        notifications: boolean
        publicProfile: boolean
        shareAchievements: boolean
      }
    }
  }
}
```

#### Enhanced pointHistory Collection
```typescript
interface EnhancedPointTransaction {
  // Existing fields...
  
  // Enhanced tracking fields
  metadata: {
    ruleId?: string
    ruleName?: string
    bulkOperationId?: string
    adminId?: string
    adminNote?: string
    originalAmount?: number
    multiplier?: number
    category: string
    tags: string[]
  }
  analytics: {
    userTierAtTime: string
    userEngagementScore: number
    sessionId?: string
    deviceType?: string
    location?: {
      country: string
      region: string
    }
  }
}
```

---

## 4. UI/UX Design Specifications

### 4.1 Design System Compliance

#### Color Palette
```scss
// Primary Colors (Dark Theme)
$bg-primary: #1a1a1a;
$bg-secondary: #2d2d2d;
$bg-tertiary: #404040;

// Accent Colors
$accent-primary: #8b5cf6;    // Purple
$accent-secondary: #06b6d4;  // Cyan
$accent-success: #10b981;    // Green
$accent-warning: #f59e0b;    // Amber
$accent-danger: #ef4444;     // Red

// Text Colors
$text-primary: #ffffff;
$text-secondary: #d1d5db;
$text-tertiary: #9ca3af;
$text-muted: #6b7280;
```

#### Typography Scale
```scss
// Font Sizes
$text-xs: 0.75rem;    // 12px
$text-sm: 0.875rem;   // 14px
$text-base: 1rem;     // 16px
$text-lg: 1.125rem;   // 18px
$text-xl: 1.25rem;    // 20px
$text-2xl: 1.5rem;    // 24px
$text-3xl: 1.875rem;  // 30px

// Font Weights
$font-normal: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;
```

#### Spacing System
```scss
// Spacing Scale (8px base)
$space-1: 0.25rem;   // 4px
$space-2: 0.5rem;    // 8px
$space-3: 0.75rem;   // 12px
$space-4: 1rem;      // 16px
$space-6: 1.5rem;    // 24px
$space-8: 2rem;      // 32px
$space-12: 3rem;     // 48px
$space-16: 4rem;     // 64px
```

### 4.2 Component Design Specifications

#### AdminCard Enhancement
```typescript
interface AdminCardProps {
  title?: string
  description?: string
  icon?: React.ComponentType
  actions?: React.ReactNode
  loading?: boolean
  error?: string
  className?: string
  children: React.ReactNode
  
  // Enhanced props
  collapsible?: boolean
  defaultCollapsed?: boolean
  refreshable?: boolean
  onRefresh?: () => Promise<void>
  headerActions?: React.ReactNode
}
```

#### StatWidget Component
```typescript
interface StatWidgetProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon: React.ComponentType
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red'
  loading?: boolean
  onClick?: () => void
  size?: 'sm' | 'md' | 'lg'
}
```

#### DataTable Enhancement
```typescript
interface DataTableProps<T> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: PaginationConfig
  sorting?: SortingConfig
  filtering?: FilteringConfig
  selection?: SelectionConfig
  actions?: TableAction<T>[]
  
  // Enhanced props
  exportable?: boolean
  searchable?: boolean
  refreshable?: boolean
  onRefresh?: () => Promise<void>
  bulkActions?: BulkAction<T>[]
  customRowRenderer?: (row: T) => React.ReactNode
}
```

### 4.3 Responsive Design Requirements

#### Breakpoints
```scss
// Responsive Breakpoints
$breakpoint-sm: 640px;   // Small devices
$breakpoint-md: 768px;   // Medium devices
$breakpoint-lg: 1024px;  // Large devices
$breakpoint-xl: 1280px;  // Extra large devices
$breakpoint-2xl: 1536px; // 2X large devices
```

#### Mobile Optimizations
- Touch-friendly button sizes (minimum 44px)
- Collapsible navigation for mobile
- Responsive data tables with horizontal scroll
- Optimized modal sizes for mobile screens
- Gesture support for common actions

---

## 5. Integration Patterns

### 5.1 State Management

#### Redux Store Structure
```typescript
interface GamificationState {
  points: {
    rules: PointRule[]
    transactions: PointTransaction[]
    analytics: PointsAnalytics
    loading: boolean
    error: string | null
  }
  achievements: {
    list: Achievement[]
    userProgress: UserAchievementProgress[]
    analytics: AchievementAnalytics
    loading: boolean
    error: string | null
  }
  rewards: {
    items: RewardItem[]
    purchases: RewardPurchase[]
    inventory: InventoryData[]
    analytics: RewardAnalytics
    loading: boolean
    error: string | null
  }
  users: {
    segments: UserSegment[]
    bulkOperations: BulkOperation[]
    analytics: UserAnalytics
    loading: boolean
    error: string | null
  }
}
```

### 5.2 API Integration Patterns

#### React Query Integration
```typescript
// Custom hooks for data fetching
export const usePointRules = (filters?: PointRuleFilters) => {
  return useQuery({
    queryKey: ['pointRules', filters],
    queryFn: () => fetchPointRules(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useCreatePointRule = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createPointRule,
    onSuccess: () => {
      queryClient.invalidateQueries(['pointRules'])
    },
  })
}
```

#### Error Handling Pattern
```typescript
interface APIError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: Date
}

const handleAPIError = (error: APIError) => {
  // Log error for monitoring
  console.error('API Error:', error)
  
  // Show user-friendly message
  toast.error(error.message || 'An unexpected error occurred')
  
  // Report to error tracking service
  errorTracker.captureException(error)
}
```

### 5.3 Real-time Updates

#### WebSocket Integration
```typescript
interface WebSocketMessage {
  type: 'point_transaction' | 'achievement_unlocked' | 'bulk_operation_update'
  data: any
  timestamp: Date
}

const useRealTimeUpdates = () => {
  const [socket, setSocket] = useState<WebSocket | null>(null)
  
  useEffect(() => {
    const ws = new WebSocket(process.env.NEXT_PUBLIC_WS_URL!)
    
    ws.onmessage = (event) => {
      const message: WebSocketMessage = JSON.parse(event.data)
      handleRealTimeUpdate(message)
    }
    
    setSocket(ws)
    
    return () => {
      ws.close()
    }
  }, [])
  
  return socket
}
```

---

## Implementation Guidelines

### Development Standards
- Follow TypeScript strict mode
- Implement comprehensive error handling
- Use React Query for data fetching
- Follow existing component patterns
- Maintain 90%+ test coverage
- Document all public APIs

### Performance Requirements
- Page load time < 2 seconds
- API response time < 500ms
- Real-time updates < 100ms latency
- Support 1000+ concurrent admin users

### Security Considerations
- Validate all admin permissions
- Sanitize user inputs
- Implement rate limiting
- Log all admin actions
- Encrypt sensitive data

This technical specification provides the foundation for implementing enhanced gamification admin features while maintaining consistency with the existing Syndicaps design system and architecture.
