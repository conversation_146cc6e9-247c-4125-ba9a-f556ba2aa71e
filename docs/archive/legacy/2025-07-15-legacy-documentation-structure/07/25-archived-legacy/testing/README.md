# Testing Guide

Comprehensive testing strategy and guidelines for the Syndicaps platform.

## 🎯 Testing Philosophy

Our testing approach follows the testing pyramid with emphasis on:
- **Unit Tests (70%)**: Fast, isolated component and function tests
- **Integration Tests (20%)**: API and service integration tests  
- **End-to-End Tests (10%)**: Critical user journey tests

### Quality Gates
- **Minimum Coverage**: 75% overall, 90% for critical modules
- **Performance**: All tests must complete within 30 seconds
- **Reliability**: Tests must be deterministic and stable

## 🧪 Test Types

### 1. Unit Tests
Test individual components, functions, and modules in isolation.

```bash
# Run unit tests
npm run test:unit

# Run with coverage
npm run test:unit -- --coverage

# Watch mode for development
npm run test:unit -- --watch
```

**Example Unit Test:**
```typescript
// src/lib/pointsSystem.test.ts
import { PointsSystem } from './pointsSystem'
import { mockUser, mockAchievement } from '@/tests/mocks'

describe('PointsSystem', () => {
  let pointsSystem: PointsSystem

  beforeEach(() => {
    pointsSystem = new PointsSystem()
  })

  describe('awardPoints', () => {
    it('should award points correctly', async () => {
      const result = await pointsSystem.awardPoints(
        mockUser.uid,
        100,
        'achievement',
        'First Purchase'
      )

      expect(result.success).toBe(true)
      expect(result.newBalance).toBe(mockUser.points + 100)
      expect(result.transaction).toMatchObject({
        amount: 100,
        type: 'earned',
        source: 'achievement'
      })
    })

    it('should handle invalid point amounts', async () => {
      await expect(
        pointsSystem.awardPoints(mockUser.uid, -100, 'achievement', 'Invalid')
      ).rejects.toThrow('Point amount must be positive')
    })
  })
})
```

### 2. Integration Tests
Test interactions between different parts of the system.

```bash
# Run integration tests
npm run test:integration
```

**Example Integration Test:**
```typescript
// tests/integration/gamification.test.ts
import { testUtils } from '@/tests/utils'
import { gamificationService } from '@/lib/gamification'

describe('Gamification Integration', () => {
  beforeEach(async () => {
    await testUtils.setupTestDatabase()
  })

  afterEach(async () => {
    await testUtils.cleanupTestDatabase()
  })

  it('should complete achievement flow', async () => {
    const user = await testUtils.createTestUser()
    
    // Trigger achievement
    await gamificationService.checkAchievements(user.uid, 'purchase', {
      amount: 149.99,
      orderId: 'test-order'
    })

    // Verify achievement unlocked
    const achievements = await gamificationService.getUserAchievements(user.uid)
    expect(achievements.unlocked).toContainEqual(
      expect.objectContaining({
        id: 'first-purchase',
        points: 100
      })
    )

    // Verify points awarded
    const pointsHistory = await gamificationService.getPointsHistory(user.uid)
    expect(pointsHistory[0]).toMatchObject({
      amount: 100,
      source: 'achievement',
      description: 'First Purchase achievement'
    })
  })
})
```

### 3. End-to-End Tests
Test complete user workflows using Playwright.

```bash
# Run E2E tests
npm run test:e2e

# Run in headed mode
npm run test:e2e -- --headed

# Run specific test
npm run test:e2e -- --grep "checkout flow"
```

**Example E2E Test:**
```typescript
// tests/e2e/checkout.spec.ts
import { test, expect } from '@playwright/test'
import { testUtils } from '../utils/e2e-utils'

test.describe('Checkout Flow', () => {
  test.beforeEach(async ({ page }) => {
    await testUtils.setupTestUser(page)
    await testUtils.addProductToCart(page, 'test-product-1')
  })

  test('should complete purchase with PayPal', async ({ page }) => {
    // Navigate to cart
    await page.click('[data-testid="cart-button"]')
    await expect(page.locator('[data-testid="cart-item"]')).toBeVisible()

    // Proceed to checkout
    await page.click('[data-testid="checkout-button"]')
    
    // Fill shipping information
    await page.fill('[data-testid="shipping-name"]', 'John Doe')
    await page.fill('[data-testid="shipping-address"]', '123 Main St')
    await page.fill('[data-testid="shipping-city"]', 'Anytown')
    await page.fill('[data-testid="shipping-state"]', 'CA')
    await page.fill('[data-testid="shipping-zip"]', '12345')
    
    // Select shipping method
    await page.click('[data-testid="shipping-standard"]')
    
    // Select PayPal payment
    await page.click('[data-testid="payment-paypal"]')
    
    // Mock PayPal flow
    await testUtils.mockPayPalPayment(page)
    
    // Complete order
    await page.click('[data-testid="complete-order"]')
    
    // Verify success
    await expect(page.locator('[data-testid="order-success"]')).toBeVisible()
    await expect(page.locator('[data-testid="order-number"]')).toContainText(/ORD-\d+/)
  })
})
```

### 4. Performance Tests
Test application performance and load handling.

```bash
# Run performance tests
npm run test:performance
```

**Example Performance Test:**
```typescript
// tests/performance/api.test.ts
import { performance } from 'perf_hooks'
import { gamificationService } from '@/lib/gamification'

describe('API Performance', () => {
  it('should respond to leaderboard requests within 500ms', async () => {
    const start = performance.now()
    
    await gamificationService.getLeaderboard('monthly', 50)
    
    const duration = performance.now() - start
    expect(duration).toBeLessThan(500)
  })

  it('should handle concurrent point awards', async () => {
    const promises = Array.from({ length: 10 }, (_, i) =>
      gamificationService.awardPoints(`user-${i}`, 100, 'test', 'Concurrent test')
    )

    const start = performance.now()
    await Promise.all(promises)
    const duration = performance.now() - start

    expect(duration).toBeLessThan(2000) // 2 seconds for 10 concurrent operations
  })
})
```

### 5. Security Tests
Test security measures and vulnerability prevention.

```bash
# Run security tests
npm run test:security
```

**Example Security Test:**
```typescript
// tests/security/auth.test.ts
import { securityService } from '@/lib/security'
import { testUtils } from '@/tests/utils'

describe('Security Tests', () => {
  describe('Rate Limiting', () => {
    it('should block excessive requests', async () => {
      const requests = Array.from({ length: 101 }, () =>
        testUtils.makeRequest('/api/test', { ip: '***********' })
      )

      const responses = await Promise.allSettled(requests)
      const blocked = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 429
      )

      expect(blocked.length).toBeGreaterThan(0)
    })
  })

  describe('Input Validation', () => {
    it('should sanitize malicious input', async () => {
      const maliciousInput = '<script>alert("xss")</script>'
      
      const result = await securityService.validateInput(
        { comment: maliciousInput },
        commentSchema
      )

      expect(result.sanitized.comment).not.toContain('<script>')
      expect(result.riskScore).toBeGreaterThan(50)
    })
  })
})
```

## 🛠️ Test Utilities

### Mock Data
```typescript
// tests/mocks/index.ts
export const mockUser = {
  uid: 'test-user-123',
  email: '<EMAIL>',
  displayName: 'Test User',
  points: 1000,
  level: 5,
  achievements: ['first-purchase']
}

export const mockProduct = {
  id: 'test-product-123',
  name: 'Test Keycap Set',
  price: 149.99,
  category: 'keycaps',
  inStock: true,
  featured: false
}

export const mockAchievement = {
  id: 'first-purchase',
  name: 'First Purchase',
  description: 'Make your first purchase',
  points: 100,
  category: 'shopping',
  requirements: {
    type: 'purchase_count',
    value: 1
  }
}
```

### Test Utilities
```typescript
// tests/utils/index.ts
export const testUtils = {
  // Database utilities
  async setupTestDatabase() {
    // Initialize test database with fixtures
  },

  async cleanupTestDatabase() {
    // Clean up test data
  },

  async createTestUser(overrides = {}) {
    return {
      ...mockUser,
      ...overrides,
      uid: `test-user-${Date.now()}`
    }
  },

  // API utilities
  async makeAuthenticatedRequest(endpoint: string, options = {}) {
    const token = await this.getTestToken()
    return fetch(endpoint, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    })
  },

  async getTestToken() {
    // Generate test JWT token
    return 'test-token-123'
  },

  // Time utilities
  async waitFor(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  async flushPromises() {
    return new Promise(resolve => setImmediate(resolve))
  }
}
```

## 📊 Coverage Requirements

### Global Coverage Targets
- **Statements**: 75%
- **Branches**: 75%
- **Functions**: 75%
- **Lines**: 75%

### Critical Module Targets
- **Security modules**: 90%
- **Points system**: 90%
- **Achievement system**: 85%
- **Payment processing**: 85%
- **Authentication**: 85%

### Coverage Reports
```bash
# Generate coverage report
npm run test:coverage

# View HTML coverage report
open coverage/lcov-report/index.html

# Check coverage thresholds
npm run test:coverage -- --passWithNoTests
```

## 🚀 CI/CD Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run unit tests
        run: npm run test:unit -- --coverage
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Run quality gates
        run: npm run quality-gates
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

## 🔧 Test Configuration

### Jest Configuration Highlights
```javascript
// jest.config.js key settings
module.exports = {
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    }
  },
  
  // Test environment
  testEnvironment: 'jest-environment-jsdom',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  
  // Module mapping
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
}
```

### Playwright Configuration
```typescript
// playwright.config.ts
import { defineConfig } from '@playwright/test'

export default defineConfig({
  testDir: './tests/e2e',
  timeout: 30000,
  retries: 2,
  
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ]
})
```

## 📝 Best Practices

### Writing Good Tests
1. **Descriptive Names**: Use clear, descriptive test names
2. **Single Responsibility**: Each test should test one thing
3. **Arrange-Act-Assert**: Structure tests clearly
4. **Independent Tests**: Tests should not depend on each other
5. **Mock External Dependencies**: Use mocks for external services

### Test Data Management
1. **Use Factories**: Create test data with factory functions
2. **Clean State**: Reset state between tests
3. **Realistic Data**: Use realistic test data
4. **Edge Cases**: Test boundary conditions

### Performance Considerations
1. **Parallel Execution**: Run tests in parallel when possible
2. **Selective Testing**: Use test patterns for focused testing
3. **Mock Heavy Operations**: Mock expensive operations
4. **Cleanup**: Properly clean up resources

---

**Last Updated**: December 2024  
**Maintained by**: Syndicaps QA Team
