# IdeaCapsule SaaS - Technical Architecture Design

## Current Syndicaps Architecture Analysis

### **Technology Stack Assessment**
```typescript
// Current Stack
Frontend: Next.js 14 + TypeScript + Tailwind CSS + Framer Motion
Backend: Firebase (Firestore + Auth + Functions + Storage)
Hosting: Vercel (Frontend) + Firebase (Backend)
Database: Firestore with 20+ collections
Authentication: Firebase Auth with role-based access
State Management: React Context + Custom hooks
```

### **Existing Database Collections**
```typescript
// Core Collections (20+ total)
const collections = {
  // E-commerce
  products: 'products',
  orders: 'orders',
  reviews: 'reviews',
  
  // User Management
  profiles: 'profiles',
  userPreferences: 'userPreferences',
  shippingAddresses: 'shipping_addresses',
  
  // Gamification
  pointTransactions: 'pointTransactions',
  achievements: 'achievements',
  userAchievements: 'user_achievements',
  rewards: 'rewards',
  
  // Community Features
  raffles: 'raffles',
  raffleEntries: 'raffle_entries',
  notifications: 'notifications',
  wishlist: 'wishlist',
  
  // Content Management
  blogPosts: 'blog_posts',
  blogCategories: 'blog_categories',
  blogTags: 'blog_tags',
  blogComments: 'blog_comments'
}
```

---

## Multi-Tenant Architecture Strategy

### **Tenant Isolation Model: Database-Level Segregation**

#### **Option 1: Tenant-Prefixed Collections (Recommended)**
```typescript
// Tenant-specific collections with prefix
const getTenantCollection = (tenantId: string, collection: string) => {
  return `tenant_${tenantId}_${collection}`
}

// Examples:
// tenant_syndicaps_products
// tenant_artgallery_products  
// tenant_craftmarket_products
```

**Advantages:**
- Complete data isolation
- Simplified security rules
- Easy tenant-specific backups
- No cross-tenant data leakage risk

**Disadvantages:**
- Higher Firestore costs (more collections)
- Complex collection management
- Requires careful indexing strategy

#### **Option 2: Shared Collections with Tenant Field**
```typescript
// Shared collections with tenant filtering
interface TenantDocument {
  tenantId: string;
  // ... other fields
}

// Firestore query with tenant filter
const getProducts = (tenantId: string) => {
  return query(
    collection(db, 'products'),
    where('tenantId', '==', tenantId)
  )
}
```

**Advantages:**
- Lower collection count
- Easier cross-tenant analytics
- Simpler indexing

**Disadvantages:**
- Risk of data leakage
- Complex security rules
- Performance impact with large datasets

### **Recommended Approach: Hybrid Model**

```typescript
// Tenant management (shared)
const sharedCollections = [
  'tenants',           // Tenant configuration
  'subscriptions',     // Billing information
  'templates',         // Shared templates
  'integrations'       // Available integrations
]

// Tenant-specific (isolated)
const tenantCollections = [
  'products',
  'orders', 
  'users',
  'raffles',
  'achievements',
  // ... all business data
]
```

---

## Database Schema Design

### **Tenant Management Schema**

```typescript
// /tenants/{tenantId}
interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  customDomain?: string;
  
  // Branding
  branding: {
    logo: string;
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    customCSS?: string;
  };
  
  // Features
  features: {
    ecommerce: boolean;
    raffles: boolean;
    gamification: boolean;
    blog: boolean;
    community: boolean;
  };
  
  // Subscription
  subscription: {
    plan: 'starter' | 'professional' | 'enterprise' | 'custom';
    status: 'active' | 'suspended' | 'cancelled';
    billingCycle: 'monthly' | 'yearly';
    nextBillingDate: Timestamp;
  };
  
  // Limits
  limits: {
    products: number;
    users: number;
    storage: number; // in GB
    bandwidth: number; // in GB
  };
  
  // Metadata
  createdAt: Timestamp;
  updatedAt: Timestamp;
  ownerId: string;
}
```

### **Tenant-Specific Collection Schema**

```typescript
// /tenant_{tenantId}_products/{productId}
interface TenantProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  
  // Tenant-specific fields
  customFields: Record<string, any>;
  
  // Standard fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
}

// /tenant_{tenantId}_users/{userId}
interface TenantUser {
  id: string;
  email: string;
  displayName: string;
  role: 'admin' | 'moderator' | 'user';
  
  // Gamification (if enabled)
  points?: number;
  tier?: string;
  achievements?: string[];
  
  // Metadata
  createdAt: Timestamp;
  lastLoginAt: Timestamp;
}
```

---

## Authentication & Authorization

### **Multi-Tenant Authentication Flow**

```typescript
// Enhanced auth context for multi-tenancy
interface AuthContext {
  user: User | null;
  tenant: Tenant | null;
  userRole: string;
  permissions: string[];
  loading: boolean;
}

// Tenant-aware authentication hook
export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthContext>({
    user: null,
    tenant: null,
    userRole: 'user',
    permissions: [],
    loading: true
  });

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // Get tenant from subdomain or custom domain
        const tenant = await getTenantFromDomain();
        
        // Get user role within tenant
        const userRole = await getUserRole(tenant.id, user.uid);
        
        // Get permissions based on role and tenant features
        const permissions = getPermissions(userRole, tenant.features);
        
        setAuthState({
          user,
          tenant,
          userRole,
          permissions,
          loading: false
        });
      } else {
        setAuthState({
          user: null,
          tenant: null,
          userRole: 'user',
          permissions: [],
          loading: false
        });
      }
    });

    return unsubscribe;
  }, []);

  return authState;
};
```

### **Firestore Security Rules for Multi-Tenancy**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getTenantId() {
      return request.auth.token.tenantId;
    }
    
    function isTenantAdmin(tenantId) {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/tenant_$(tenantId)_users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/tenant_$(tenantId)_users/$(request.auth.uid)).data.role in ['admin', 'owner'];
    }
    
    // Tenant management (platform admin only)
    match /tenants/{tenantId} {
      allow read: if isAuthenticated() && 
                     (request.auth.token.platformAdmin == true || 
                      request.auth.token.tenantId == tenantId);
      allow write: if request.auth.token.platformAdmin == true;
    }
    
    // Tenant-specific collections
    match /tenant_{tenantId}_{collection}/{docId} {
      allow read, write: if isAuthenticated() && 
                           request.auth.token.tenantId == tenantId;
      allow create, update, delete: if isTenantAdmin(tenantId);
    }
    
    // Tenant user management
    match /tenant_{tenantId}_users/{userId} {
      allow read: if isAuthenticated() && request.auth.token.tenantId == tenantId;
      allow write: if isAuthenticated() && 
                      (request.auth.uid == userId || isTenantAdmin(tenantId));
    }
  }
}
```

---

## Infrastructure & Scalability

### **Hosting Architecture**

```yaml
# Infrastructure Components
Frontend:
  - Vercel (Next.js hosting)
  - Cloudflare (CDN + DNS + SSL)
  - Custom domain routing

Backend:
  - Firebase Functions (API endpoints)
  - Firebase Firestore (Database)
  - Firebase Storage (File uploads)
  - Firebase Auth (Authentication)

Additional Services:
  - Stripe (Billing & subscriptions)
  - SendGrid (Email delivery)
  - Sentry (Error monitoring)
  - Google Analytics (Usage tracking)
```

### **Tenant Routing Strategy**

```typescript
// Subdomain routing
const getTenantFromSubdomain = (hostname: string): string | null => {
  const parts = hostname.split('.');
  if (parts.length >= 3) {
    const subdomain = parts[0];
    if (subdomain !== 'www' && subdomain !== 'app') {
      return subdomain;
    }
  }
  return null;
};

// Custom domain routing
const getTenantFromCustomDomain = async (hostname: string): Promise<string | null> => {
  const tenant = await db.collection('tenants')
    .where('customDomain', '==', hostname)
    .limit(1)
    .get();
    
  return tenant.empty ? null : tenant.docs[0].id;
};

// Next.js middleware for tenant resolution
export async function middleware(request: NextRequest) {
  const hostname = request.nextUrl.hostname;
  
  // Try subdomain first
  let tenantId = getTenantFromSubdomain(hostname);
  
  // Fallback to custom domain
  if (!tenantId) {
    tenantId = await getTenantFromCustomDomain(hostname);
  }
  
  if (tenantId) {
    // Add tenant to request headers
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-tenant-id', tenantId);
    
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }
  
  // Redirect to main platform if no tenant found
  return NextResponse.redirect(new URL('https://ideacapsule.io', request.url));
}
```

### **Database Scaling Strategy**

```typescript
// Automatic tenant distribution across Firebase projects
interface TenantSharding {
  maxTenantsPerProject: number;
  currentProjects: {
    projectId: string;
    tenantCount: number;
    region: string;
  }[];
}

const getOptimalProject = async (): Promise<string> => {
  const sharding = await getShardingConfig();
  
  // Find project with lowest tenant count
  const optimalProject = sharding.currentProjects
    .filter(p => p.tenantCount < sharding.maxTenantsPerProject)
    .sort((a, b) => a.tenantCount - b.tenantCount)[0];
    
  if (!optimalProject) {
    // Create new Firebase project
    return await createNewFirebaseProject();
  }
  
  return optimalProject.projectId;
};
```

---

## Performance Optimization

### **Caching Strategy**

```typescript
// Redis caching for frequently accessed data
interface CacheStrategy {
  tenantConfig: '1h';      // Tenant configuration
  userSessions: '24h';     // User session data
  productCatalog: '30m';   // Product listings
  achievements: '6h';      // Achievement definitions
}

// Implementation
const getCachedTenantConfig = async (tenantId: string): Promise<Tenant> => {
  const cacheKey = `tenant:${tenantId}`;
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Fetch from database
  const tenant = await getTenantFromDB(tenantId);
  
  // Cache for 1 hour
  await redis.setex(cacheKey, 3600, JSON.stringify(tenant));
  
  return tenant;
};
```

### **Database Optimization**

```typescript
// Composite indexes for tenant-specific queries
const requiredIndexes = [
  // Tenant-specific product queries
  {
    collection: 'tenant_{tenantId}_products',
    fields: ['category', 'createdAt'],
    order: 'desc'
  },
  
  // User achievement queries
  {
    collection: 'tenant_{tenantId}_userAchievements',
    fields: ['userId', 'unlockedAt'],
    order: 'desc'
  },
  
  // Order history queries
  {
    collection: 'tenant_{tenantId}_orders',
    fields: ['userId', 'status', 'createdAt'],
    order: 'desc'
  }
];
```

---

## Monitoring & Analytics

### **Tenant-Specific Monitoring**

```typescript
// Custom metrics for each tenant
interface TenantMetrics {
  activeUsers: number;
  dailyTransactions: number;
  storageUsage: number;
  bandwidthUsage: number;
  apiCalls: number;
  errorRate: number;
  responseTime: number;
}

// Real-time monitoring dashboard
const TenantDashboard = ({ tenantId }: { tenantId: string }) => {
  const [metrics, setMetrics] = useState<TenantMetrics>();
  
  useEffect(() => {
    const unsubscribe = subscribeToTenantMetrics(tenantId, setMetrics);
    return unsubscribe;
  }, [tenantId]);
  
  return (
    <div className="grid grid-cols-3 gap-4">
      <MetricCard title="Active Users" value={metrics?.activeUsers} />
      <MetricCard title="Transactions" value={metrics?.dailyTransactions} />
      <MetricCard title="Storage" value={`${metrics?.storageUsage}GB`} />
    </div>
  );
};
```

This technical architecture provides a robust foundation for scaling the Syndicaps platform into a multi-tenant SaaS solution while maintaining performance, security, and flexibility for diverse creative communities.
