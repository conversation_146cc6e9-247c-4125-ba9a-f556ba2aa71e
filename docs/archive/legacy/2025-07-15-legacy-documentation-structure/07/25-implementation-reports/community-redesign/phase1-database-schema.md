# Phase 1: Database Schema Planning
## Syndicaps Community Page Redesign

**Date**: 2025-06-29  
**Status**: Complete  
**Author**: Syndicaps Development Team

---

## 🗄️ Enhanced Firestore Collections

### **Existing Collections (Enhanced)**

#### **1. profiles (Enhanced)**
```typescript
interface UserProfile {
  // Existing fields...
  id: string
  email: string
  displayName: string
  points: number
  achievements: UserAchievement[]
  
  // New community fields
  community: {
    reputation: number
    tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
    joinedAt: Timestamp
    lastActive: Timestamp
    preferences: {
      notifications: boolean
      publicProfile: boolean
      showInLeaderboard: boolean
      allowDirectMessages: boolean
    }
    stats: {
      challengesParticipated: number
      challengesWon: number
      submissionsCount: number
      discussionPosts: number
      helpfulVotes: number
      mentorshipSessions: number
    }
    social: {
      following: string[]
      followers: string[]
      blockedUsers: string[]
    }
    moderation: {
      warnings: number
      strikes: number
      isModerator: boolean
      canModerate: string[] // content types they can moderate
    }
  }
}
```

---

### **New Collections**

#### **2. challenges**
```typescript
interface Challenge {
  id: string
  title: string
  description: string
  shortDescription: string
  theme: string
  type: 'design' | 'photo' | 'build' | 'creative' | 'collaboration'
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  
  // Status and timing
  status: 'draft' | 'upcoming' | 'active' | 'voting' | 'completed' | 'cancelled'
  startDate: Timestamp
  endDate: Timestamp
  votingStartDate: Timestamp
  votingEndDate: Timestamp
  
  // Rules and requirements
  rules: string[]
  requirements: {
    minTier?: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
    maxSubmissions: number
    allowTeams: boolean
    requiresApproval: boolean
    allowedFileTypes: string[]
    maxFileSize: number // in MB
  }
  
  // Rewards and incentives
  rewards: {
    winner: { points: number, badge?: string, specialReward?: string }
    runnerUp: { points: number, badge?: string }
    participation: { points: number }
    featured: { points: number } // for featured submissions
  }
  
  // Content and media
  media: {
    bannerImage: string
    thumbnailImage: string
    inspirationImages?: string[]
    videoUrl?: string
  }
  
  // Categorization
  tags: string[]
  category: string
  
  // Statistics
  stats: {
    participants: number
    submissions: number
    totalVotes: number
    views: number
    shares: number
  }
  
  // Administration
  createdBy: string
  moderators: string[]
  featured: boolean
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

#### **3. submissions**
```typescript
interface Submission {
  id: string
  challengeId?: string // null for general submissions
  userId: string
  
  // Content details
  title: string
  description: string
  type: 'image' | 'video' | 'build' | 'design' | 'tutorial'
  
  // Media content
  content: {
    images: Array<{
      url: string
      caption?: string
      order: number
      thumbnail: string
    }>
    videos?: Array<{
      url: string
      thumbnail: string
      duration: number
    }>
    files?: Array<{
      url: string
      filename: string
      fileType: string
      size: number
    }>
  }
  
  // Metadata
  tags: string[]
  category: string
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
  
  // Status and visibility
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'featured'
  visibility: 'public' | 'community' | 'followers' | 'private'
  
  // Interaction metrics
  interactions: {
    likes: number
    comments: number
    shares: number
    views: number
    saves: number
    reports: number
  }
  
  // User interactions tracking
  userInteractions: {
    likedBy: string[]
    savedBy: string[]
    sharedBy: string[]
    reportedBy: string[]
  }
  
  // Moderation
  moderation: {
    flagged: boolean
    flagCount: number
    flagReasons: string[]
    moderatedBy?: string
    moderatedAt?: Timestamp
    moderationNotes?: string
    autoModerated: boolean
  }
  
  // Competition specific
  competition?: {
    challengeId: string
    submissionOrder: number
    votes: number
    rank?: number
    isWinner: boolean
    isFeatured: boolean
  }
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
  publishedAt?: Timestamp
}
```

#### **4. discussions**
```typescript
interface Discussion {
  id: string
  title: string
  content: string
  
  // Categorization
  category: 'general' | 'builds' | 'reviews' | 'help' | 'showcase' | 'trading'
  subcategory?: string
  tags: string[]
  
  // Author information
  authorId: string
  
  // Status
  status: 'active' | 'locked' | 'archived' | 'deleted'
  isPinned: boolean
  isAnnouncement: boolean
  
  // Content metadata
  hasImages: boolean
  hasVideos: boolean
  hasPolls: boolean
  
  // Interaction metrics
  interactions: {
    replies: number
    likes: number
    views: number
    shares: number
    reports: number
    lastActivity: Timestamp
    lastReplyBy?: string
  }
  
  // User interactions
  userInteractions: {
    likedBy: string[]
    followedBy: string[] // users following this discussion
    reportedBy: string[]
  }
  
  // Moderation
  moderation: {
    flagged: boolean
    flagCount: number
    flagReasons: string[]
    moderatedBy?: string
    moderatedAt?: Timestamp
    moderationNotes?: string
  }
  
  // Related content
  relatedSubmissions?: string[]
  relatedChallenges?: string[]
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
  lastActivityAt: Timestamp
}
```

#### **5. discussion_replies**
```typescript
interface DiscussionReply {
  id: string
  discussionId: string
  parentReplyId?: string // for nested replies
  authorId: string
  
  // Content
  content: string
  hasImages: boolean
  hasVideos: boolean
  
  // Media
  media?: {
    images: string[]
    videos: string[]
  }
  
  // Interaction metrics
  interactions: {
    likes: number
    reports: number
    replies: number // if this reply has sub-replies
  }
  
  // User interactions
  userInteractions: {
    likedBy: string[]
    reportedBy: string[]
  }
  
  // Status
  status: 'active' | 'deleted' | 'hidden'
  isAnswer: boolean // marked as helpful answer
  isModerator: boolean // reply from moderator
  
  // Moderation
  moderation: {
    flagged: boolean
    flagCount: number
    moderatedBy?: string
    moderatedAt?: Timestamp
  }
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

#### **6. co_creation_campaigns**
```typescript
interface CoCreationCampaign {
  id: string
  title: string
  description: string
  type: 'product_design' | 'feature_request' | 'collaboration' | 'community_choice'
  
  // Status and timing
  status: 'upcoming' | 'idea_submission' | 'voting' | 'completed' | 'implemented'
  ideaSubmissionStart: Timestamp
  ideaSubmissionEnd: Timestamp
  votingStart: Timestamp
  votingEnd: Timestamp
  
  // Voting rules
  votingRules: {
    minTier?: string
    pointCost: number
    maxVotesPerUser: number
    tierMultipliers: {
      bronze: number
      silver: number
      gold: number
      platinum: number
      diamond: number
    }
  }
  
  // Content
  media: {
    bannerImage: string
    descriptionImages?: string[]
  }
  
  // Statistics
  stats: {
    totalIdeas: number
    totalVotes: number
    totalParticipants: number
    pointsSpent: number
  }
  
  // Results
  results?: {
    winnerId: string
    winnerVotes: number
    implementationStatus: 'planned' | 'in_progress' | 'completed'
    implementationDate?: Timestamp
    implementationNotes?: string
  }
  
  // Administration
  createdBy: string
  moderators: string[]
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

#### **7. co_creation_ideas**
```typescript
interface CoCreationIdea {
  id: string
  campaignId: string
  authorId: string
  
  // Content
  title: string
  description: string
  
  // Media
  media: {
    images?: string[]
    sketches?: string[]
    references?: string[]
  }
  
  // Voting
  votes: {
    total: number
    weightedTotal: number
    voterCount: number
    breakdown: {
      bronze: number
      silver: number
      gold: number
      platinum: number
      diamond: number
    }
  }
  
  // Status
  status: 'submitted' | 'approved' | 'rejected' | 'winner'
  
  // Moderation
  moderation: {
    approved: boolean
    approvedBy?: string
    approvedAt?: Timestamp
    rejectionReason?: string
  }
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

#### **8. co_creation_votes**
```typescript
interface CoCreationVote {
  id: string
  campaignId: string
  ideaId: string
  userId: string
  
  // Vote details
  voteWeight: number
  pointsSpent: number
  userTier: string
  
  // Metadata
  metadata?: {
    reason?: string
    confidence: number
  }
  
  // Timestamps
  createdAt: Timestamp
}
```

#### **9. artist_profiles**
```typescript
interface ArtistProfile {
  id: string
  userId: string
  
  // Profile information
  displayName: string
  bio: string
  location?: string
  specialties: string[]
  
  // Portfolio
  portfolio: {
    featured: string[] // submission IDs
    gallery: string[] // submission IDs
    collaborations: string[] // collaboration IDs
    achievements: string[] // special achievements
  }
  
  // Social links
  social: {
    instagram?: string
    twitter?: string
    website?: string
    youtube?: string
    behance?: string
  }
  
  // Verification
  verification: {
    verified: boolean
    verifiedAt?: Timestamp
    verifiedBy?: string
    verificationType: 'community' | 'official' | 'partner'
    verificationBadge?: string
  }
  
  // Statistics
  stats: {
    followers: number
    following: number
    submissions: number
    collaborations: number
    featuredCount: number
    totalLikes: number
    totalViews: number
  }
  
  // Collaboration preferences
  collaboration: {
    openToCollabs: boolean
    preferredTypes: string[]
    contactMethod: 'dm' | 'email' | 'social'
    rates?: {
      commission: boolean
      licensing: boolean
      partnership: boolean
    }
  }
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

---

## 📊 Collection Relationships

### **Primary Relationships**
```
profiles (1) ←→ (many) submissions
profiles (1) ←→ (many) discussions
profiles (1) ←→ (many) discussion_replies
profiles (1) ←→ (many) co_creation_votes
profiles (1) ←→ (1) artist_profiles

challenges (1) ←→ (many) submissions
discussions (1) ←→ (many) discussion_replies
co_creation_campaigns (1) ←→ (many) co_creation_ideas
co_creation_ideas (1) ←→ (many) co_creation_votes
```

### **Index Strategy**
```typescript
// Critical indexes for performance
const indexes = {
  challenges: ['status', 'startDate', 'featured', 'type'],
  submissions: ['userId', 'challengeId', 'status', 'createdAt', 'featured'],
  discussions: ['category', 'status', 'isPinned', 'lastActivityAt'],
  discussion_replies: ['discussionId', 'createdAt', 'parentReplyId'],
  co_creation_campaigns: ['status', 'votingStart', 'votingEnd'],
  co_creation_votes: ['campaignId', 'userId', 'ideaId'],
  artist_profiles: ['verification.verified', 'stats.followers']
}
```

---

## 🔐 Security Rules

### **Firestore Security Rules Structure**
```javascript
// Example security rules for new collections
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Challenges - read by all, write by admins
    match /challenges/{challengeId} {
      allow read: if true;
      allow write: if isAdmin() || isModerator();
    }
    
    // Submissions - read by all, write by owner
    match /submissions/{submissionId} {
      allow read: if resource.data.visibility == 'public' || 
                     resource.data.userId == request.auth.uid ||
                     isAdmin();
      allow create: if request.auth != null && 
                       request.resource.data.userId == request.auth.uid;
      allow update: if resource.data.userId == request.auth.uid || 
                       isAdmin();
    }
    
    // Discussions - read by all, write by authenticated users
    match /discussions/{discussionId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update: if resource.data.authorId == request.auth.uid || 
                       isAdmin();
    }
  }
}
```

---

**Database Schema Planning Complete**: Ready to proceed to Component Structure Planning
