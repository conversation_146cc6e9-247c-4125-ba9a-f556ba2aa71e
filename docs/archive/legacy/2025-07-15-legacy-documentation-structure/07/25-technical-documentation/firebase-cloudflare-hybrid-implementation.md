# Firebase + Cloudflare Hybrid Implementation Guide

## Executive Summary

This document provides a comprehensive implementation guide for integrating Cloudflare infrastructure with the existing Firebase backend to create an optimized hybrid solution for SyndiCaps.

**Implementation Timeline**: 12 weeks  
**Estimated Cost**: $7,000-12,000  
**Risk Level**: Low  
**Business Disruption**: Minimal  

---

## 1. Implementation Overview

### Current Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │───▶│ Cloudflare Pages │───▶│   Firebase      │
│                 │    │   (Hosting)     │    │   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Target Hybrid Architecture
```
┌─────────────────┐    ┌─────────────────────────────────────────┐
│   Next.js App   │───▶│           Cloudflare Edge               │
│                 │    │  ┌─────────────┐  ┌─────────────────┐   │
└─────────────────┘    │  │    Pages    │  │    Workers      │   │
                       │  │  (Hosting)  │  │ (Image/Cache)   │   │
                       │  └─────────────┘  └─────────────────┘   │
                       │  ┌─────────────┐  ┌─────────────────┐   │
                       │  │     R2      │  │      CDN        │   │
                       │  │ (Storage)   │  │   (Global)      │   │
                       │  └─────────────┘  └─────────────────┘   │
                       └─────────────────────────────────────────┘
                                              │
                                              ▼
                       ┌─────────────────────────────────────────┐
                       │              Firebase                   │
                       │  ┌─────────────┐  ┌─────────────────┐   │
                       │  │  Firestore  │  │      Auth       │   │
                       │  │ (Database)  │  │   (Identity)    │   │
                       │  └─────────────┘  └─────────────────┘   │
                       │  ┌─────────────┐  ┌─────────────────┐   │
                       │  │  Functions  │  │   Analytics     │   │
                       │  │ (Business)  │  │ (Monitoring)    │   │
                       │  └─────────────┘  └─────────────────┘   │
                       └─────────────────────────────────────────┘
```

---

## 2. Phase 1: Foundation Setup (Weeks 1-2)

### 2.1 Cloudflare R2 Storage Setup

#### Create R2 Bucket
```bash
# Install Wrangler CLI (if not already installed)
npm install -g wrangler

# Login to Cloudflare
wrangler login

# Create R2 bucket for images
wrangler r2 bucket create syndicaps-images

# Create R2 bucket for backups
wrangler r2 bucket create syndicaps-backups

# List buckets to verify
wrangler r2 bucket list
```

#### Configure R2 Access
```typescript
// src/lib/cloudflare/r2-config.ts
export const R2_CONFIG = {
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
  accessKeyId: process.env.R2_ACCESS_KEY_ID,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  buckets: {
    images: 'syndicaps-images',
    backups: 'syndicaps-backups'
  },
  endpoints: {
    images: `https://syndicaps-images.${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    backups: `https://syndicaps-backups.${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`
  }
}
```

### 2.2 Environment Variables Setup
```bash
# Add to .env.local
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_API_TOKEN=your_api_token
CLOUDFLARE_ZONE_ID=your_zone_id
R2_ACCESS_KEY_ID=your_r2_access_key
R2_SECRET_ACCESS_KEY=your_r2_secret_key

# Image optimization settings
CLOUDFLARE_IMAGES_ACCOUNT_HASH=your_images_hash
CLOUDFLARE_IMAGES_API_TOKEN=your_images_token

# CDN settings
CDN_BASE_URL=https://cdn.syndicaps.com
CACHE_TTL_IMAGES=604800
CACHE_TTL_STATIC=86400
```

### 2.3 Create R2 Storage Service
```typescript
// src/lib/cloudflare/r2-storage.ts
import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { R2_CONFIG } from './r2-config'

class R2StorageService {
  private client: S3Client

  constructor() {
    this.client = new S3Client({
      region: 'auto',
      endpoint: `https://${R2_CONFIG.accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: R2_CONFIG.accessKeyId!,
        secretAccessKey: R2_CONFIG.secretAccessKey!,
      },
    })
  }

  async uploadImage(
    file: Buffer, 
    key: string, 
    contentType: string
  ): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: R2_CONFIG.buckets.images,
      Key: key,
      Body: file,
      ContentType: contentType,
      CacheControl: 'public, max-age=********',
    })

    await this.client.send(command)
    return `${R2_CONFIG.endpoints.images}/${key}`
  }

  async deleteImage(key: string): Promise<void> {
    const command = new DeleteObjectCommand({
      Bucket: R2_CONFIG.buckets.images,
      Key: key,
    })

    await this.client.send(command)
  }

  async uploadBackup(
    data: Buffer, 
    key: string
  ): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: R2_CONFIG.buckets.backups,
      Key: key,
      Body: data,
      ContentType: 'application/json',
    })

    await this.client.send(command)
    return `${R2_CONFIG.endpoints.backups}/${key}`
  }
}

export const r2Storage = new R2StorageService()
```

---

## 3. Phase 2: Image Migration (Weeks 3-4)

### 3.1 Create Image Migration Script
```typescript
// scripts/migrate-images-to-r2.ts
import { initializeApp } from 'firebase/app'
import { getStorage, ref, getDownloadURL, listAll } from 'firebase/storage'
import { r2Storage } from '../src/lib/cloudflare/r2-storage'
import { db } from '../src/lib/firebase'
import { collection, getDocs, updateDoc, doc } from 'firebase/firestore'

interface MigrationProgress {
  total: number
  migrated: number
  failed: number
  errors: string[]
}

class ImageMigrationService {
  private progress: MigrationProgress = {
    total: 0,
    migrated: 0,
    failed: 0,
    errors: []
  }

  async migrateProductImages(): Promise<MigrationProgress> {
    console.log('🚀 Starting product image migration...')
    
    // Get all products from Firestore
    const productsSnapshot = await getDocs(collection(db, 'products'))
    this.progress.total = productsSnapshot.size

    for (const productDoc of productsSnapshot.docs) {
      try {
        const productData = productDoc.data()
        const productId = productDoc.id

        // Migrate main image
        if (productData.imageUrl) {
          const newImageUrl = await this.migrateImage(
            productData.imageUrl, 
            `products/${productId}/main.jpg`
          )
          
          await updateDoc(doc(db, 'products', productId), {
            imageUrl: newImageUrl,
            'metadata.migratedToR2': true,
            'metadata.migrationDate': new Date()
          })
        }

        // Migrate gallery images
        if (productData.gallery && Array.isArray(productData.gallery)) {
          const newGallery = []
          
          for (let i = 0; i < productData.gallery.length; i++) {
            const galleryImageUrl = await this.migrateImage(
              productData.gallery[i], 
              `products/${productId}/gallery/${i}.jpg`
            )
            newGallery.push(galleryImageUrl)
          }

          await updateDoc(doc(db, 'products', productId), {
            gallery: newGallery
          })
        }

        this.progress.migrated++
        console.log(`✅ Migrated product ${productId} (${this.progress.migrated}/${this.progress.total})`)

      } catch (error) {
        this.progress.failed++
        this.progress.errors.push(`Product ${productDoc.id}: ${error}`)
        console.error(`❌ Failed to migrate product ${productDoc.id}:`, error)
      }
    }

    return this.progress
  }

  private async migrateImage(firebaseUrl: string, r2Key: string): Promise<string> {
    // Download from Firebase Storage
    const response = await fetch(firebaseUrl)
    const imageBuffer = Buffer.from(await response.arrayBuffer())
    
    // Upload to R2
    const r2Url = await r2Storage.uploadImage(
      imageBuffer, 
      r2Key, 
      response.headers.get('content-type') || 'image/jpeg'
    )

    return r2Url
  }

  async generateMigrationReport(): Promise<void> {
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.progress,
      recommendations: [
        'Update CDN cache settings for new R2 URLs',
        'Monitor image loading performance',
        'Schedule Firebase Storage cleanup after verification'
      ]
    }

    console.log('📊 Migration Report:', JSON.stringify(report, null, 2))
  }
}

// Run migration
async function runMigration() {
  const migrationService = new ImageMigrationService()
  
  try {
    const result = await migrationService.migrateProductImages()
    await migrationService.generateMigrationReport()
    
    console.log('🎉 Migration completed!')
    console.log(`✅ Success: ${result.migrated}`)
    console.log(`❌ Failed: ${result.failed}`)
    
  } catch (error) {
    console.error('💥 Migration failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  runMigration()
}
```

### 3.2 Update Image Upload Service
```typescript
// src/lib/services/imageUploadService.ts
import { r2Storage } from '../cloudflare/r2-storage'
import { v4 as uuidv4 } from 'uuid'

export class ImageUploadService {
  async uploadProductImage(
    file: File, 
    productId: string, 
    type: 'main' | 'gallery'
  ): Promise<string> {
    const fileExtension = file.name.split('.').pop()
    const fileName = `${uuidv4()}.${fileExtension}`
    const key = `products/${productId}/${type}/${fileName}`
    
    const buffer = Buffer.from(await file.arrayBuffer())
    
    return await r2Storage.uploadImage(buffer, key, file.type)
  }

  async uploadUserAvatar(file: File, userId: string): Promise<string> {
    const fileExtension = file.name.split('.').pop()
    const key = `users/${userId}/avatar.${fileExtension}`
    
    const buffer = Buffer.from(await file.arrayBuffer())
    
    return await r2Storage.uploadImage(buffer, key, file.type)
  }

  async deleteImage(imageUrl: string): Promise<void> {
    // Extract key from R2 URL
    const key = this.extractKeyFromUrl(imageUrl)
    if (key) {
      await r2Storage.deleteImage(key)
    }
  }

  private extractKeyFromUrl(url: string): string | null {
    const match = url.match(/\/([^\/]+\/[^\/]+\/[^\/]+)$/)
    return match ? match[1] : null
  }
}

export const imageUploadService = new ImageUploadService()
```

---

## 4. Phase 3: Cloudflare Workers Implementation (Weeks 5-6)

### 4.1 Image Optimization Worker
```typescript
// workers/image-optimizer.ts
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url)
    const { pathname, searchParams } = url

    // Extract image parameters
    const width = searchParams.get('w') || '800'
    const quality = searchParams.get('q') || '85'
    const format = searchParams.get('f') || 'auto'

    // Get original image from R2
    const imageKey = pathname.slice(1) // Remove leading slash
    const r2Object = await env.SYNDICAPS_IMAGES.get(imageKey)

    if (!r2Object) {
      return new Response('Image not found', { status: 404 })
    }

    // Apply transformations using Cloudflare Images
    const transformedImageUrl = `https://imagedelivery.net/${env.CLOUDFLARE_IMAGES_ACCOUNT_HASH}/${imageKey}/w=${width},q=${quality},f=${format}`

    // Fetch transformed image
    const transformedResponse = await fetch(transformedImageUrl)

    // Return with caching headers
    return new Response(transformedResponse.body, {
      headers: {
        'Content-Type': transformedResponse.headers.get('Content-Type') || 'image/jpeg',
        'Cache-Control': 'public, max-age=********, immutable',
        'CDN-Cache-Control': 'public, max-age=********',
        'Cloudflare-CDN-Cache-Control': 'public, max-age=********'
      }
    })
  }
}
```

### 4.2 API Cache Worker
```typescript
// workers/api-cache.ts
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url)
    const cacheKey = new Request(url.toString(), request)
    const cache = caches.default

    // Check cache first
    let response = await cache.match(cacheKey)
    
    if (response) {
      // Add cache hit header
      response = new Response(response.body, {
        ...response,
        headers: {
          ...response.headers,
          'CF-Cache-Status': 'HIT'
        }
      })
      return response
    }

    // Forward to Firebase Functions
    const firebaseUrl = `https://us-central1-syndicaps-fullpower.cloudfunctions.net${url.pathname}`
    response = await fetch(firebaseUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    })

    // Cache successful responses
    if (response.ok) {
      const cacheResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          ...response.headers,
          'Cache-Control': 'public, max-age=300', // 5 minutes
          'CF-Cache-Status': 'MISS'
        }
      })

      // Store in cache
      await cache.put(cacheKey, cacheResponse.clone())
      return cacheResponse
    }

    return response
  }
}
```

---

## 5. Phase 4: Performance Optimization (Weeks 7-8)

### 5.1 CDN Configuration
```typescript
// src/lib/cloudflare/cdn-config.ts
export const CDN_RULES = {
  // Static assets - long cache
  static: {
    pattern: '*.{js,css,woff,woff2,ttf,eot}',
    cacheControl: 'public, max-age=********, immutable',
    edgeCacheTtl: ********
  },
  
  // Images - medium cache with optimization
  images: {
    pattern: '*.{jpg,jpeg,png,gif,webp,avif}',
    cacheControl: 'public, max-age=604800',
    edgeCacheTtl: 604800,
    optimization: {
      webp: true,
      avif: true,
      quality: 85
    }
  },
  
  // API responses - short cache
  api: {
    pattern: '/api/*',
    cacheControl: 'public, max-age=300',
    edgeCacheTtl: 300,
    bypassOnCookie: true
  },
  
  // HTML pages - very short cache
  html: {
    pattern: '*.html',
    cacheControl: 'public, max-age=3600',
    edgeCacheTtl: 3600
  }
}
```

### 5.2 Performance Monitoring Service
```typescript
// src/lib/monitoring/performanceMonitor.ts
export class HybridPerformanceMonitor {
  private metrics: Map<string, number[]> = new Map()

  recordImageLoadTime(url: string, loadTime: number): void {
    const source = url.includes('r2.cloudflarestorage.com') ? 'r2' : 'firebase'
    const key = `image_load_${source}`
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, [])
    }
    
    this.metrics.get(key)!.push(loadTime)
  }

  recordApiResponseTime(endpoint: string, responseTime: number): void {
    const key = `api_${endpoint}`
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, [])
    }
    
    this.metrics.get(key)!.push(responseTime)
  }

  generatePerformanceReport(): PerformanceReport {
    const report: PerformanceReport = {
      timestamp: new Date().toISOString(),
      metrics: {}
    }

    for (const [key, values] of this.metrics.entries()) {
      const avg = values.reduce((a, b) => a + b, 0) / values.length
      const p95 = this.calculatePercentile(values, 95)
      
      report.metrics[key] = {
        average: Math.round(avg),
        p95: Math.round(p95),
        samples: values.length
      }
    }

    return report
  }

  private calculatePercentile(values: number[], percentile: number): number {
    const sorted = values.sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[index]
  }
}

interface PerformanceReport {
  timestamp: string
  metrics: Record<string, {
    average: number
    p95: number
    samples: number
  }>
}

export const performanceMonitor = new HybridPerformanceMonitor()
```

---

## 6. Testing & Validation (Weeks 9-10)

### 6.1 Integration Tests
```typescript
// tests/integration/hybrid-infrastructure.test.ts
import { r2Storage } from '../../src/lib/cloudflare/r2-storage'
import { imageUploadService } from '../../src/lib/services/imageUploadService'
import { performanceMonitor } from '../../src/lib/monitoring/performanceMonitor'

describe('Hybrid Infrastructure Integration', () => {
  describe('R2 Storage', () => {
    it('should upload and retrieve images', async () => {
      const testImage = Buffer.from('test image data')
      const key = 'test/image.jpg'
      
      const url = await r2Storage.uploadImage(testImage, key, 'image/jpeg')
      expect(url).toContain('r2.cloudflarestorage.com')
      
      // Cleanup
      await r2Storage.deleteImage(key)
    })

    it('should handle upload failures gracefully', async () => {
      const invalidBuffer = null as any
      
      await expect(
        r2Storage.uploadImage(invalidBuffer, 'test.jpg', 'image/jpeg')
      ).rejects.toThrow()
    })
  })

  describe('Image Upload Service', () => {
    it('should upload product images to R2', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      const url = await imageUploadService.uploadProductImage(
        mockFile, 
        'test-product', 
        'main'
      )
      
      expect(url).toContain('products/test-product/main/')
    })
  })

  describe('Performance Monitoring', () => {
    it('should record and report metrics', () => {
      performanceMonitor.recordImageLoadTime('https://r2.example.com/test.jpg', 150)
      performanceMonitor.recordImageLoadTime('https://r2.example.com/test2.jpg', 200)
      
      const report = performanceMonitor.generatePerformanceReport()
      
      expect(report.metrics['image_load_r2']).toBeDefined()
      expect(report.metrics['image_load_r2'].average).toBe(175)
    })
  })
})
```

---

## 7. Deployment & Rollout (Weeks 11-12)

### 7.1 Deployment Checklist
```markdown
## Pre-Deployment Checklist

### Infrastructure Setup
- [ ] R2 buckets created and configured
- [ ] Cloudflare Workers deployed
- [ ] CDN rules configured
- [ ] Environment variables set

### Migration Verification
- [ ] Image migration completed successfully
- [ ] Database references updated
- [ ] Performance benchmarks recorded
- [ ] Rollback plan prepared

### Monitoring Setup
- [ ] Performance monitoring active
- [ ] Error tracking configured
- [ ] Alerting rules set up
- [ ] Dashboard created

### Testing Completed
- [ ] Integration tests passing
- [ ] Performance tests completed
- [ ] Load testing verified
- [ ] User acceptance testing done
```

### 7.2 Rollout Strategy
```typescript
// src/lib/config/featureFlags.ts
export const FEATURE_FLAGS = {
  USE_R2_STORAGE: {
    enabled: process.env.FEATURE_R2_STORAGE === 'true',
    rolloutPercentage: parseInt(process.env.R2_ROLLOUT_PERCENTAGE || '0')
  },
  
  USE_CLOUDFLARE_WORKERS: {
    enabled: process.env.FEATURE_CF_WORKERS === 'true',
    rolloutPercentage: parseInt(process.env.CF_WORKERS_ROLLOUT_PERCENTAGE || '0')
  }
}

export function shouldUseR2Storage(userId?: string): boolean {
  if (!FEATURE_FLAGS.USE_R2_STORAGE.enabled) return false
  
  if (FEATURE_FLAGS.USE_R2_STORAGE.rolloutPercentage === 100) return true
  
  // Gradual rollout based on user ID hash
  if (userId) {
    const hash = hashString(userId)
    return (hash % 100) < FEATURE_FLAGS.USE_R2_STORAGE.rolloutPercentage
  }
  
  return false
}

function hashString(str: string): number {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash)
}
```

---

## 8. Monitoring & Maintenance

### 8.1 Performance Dashboards
```typescript
// src/components/admin/HybridPerformanceDashboard.tsx
import { useEffect, useState } from 'react'
import { performanceMonitor } from '@/lib/monitoring/performanceMonitor'

export function HybridPerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceReport | null>(null)

  useEffect(() => {
    const interval = setInterval(() => {
      const report = performanceMonitor.generatePerformanceReport()
      setMetrics(report)
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <MetricCard
        title="R2 Image Load Time"
        value={metrics?.metrics['image_load_r2']?.average}
        unit="ms"
        trend="down"
      />
      <MetricCard
        title="Firebase Image Load Time"
        value={metrics?.metrics['image_load_firebase']?.average}
        unit="ms"
        trend="stable"
      />
      <MetricCard
        title="API Response Time"
        value={metrics?.metrics['api_products']?.p95}
        unit="ms"
        trend="down"
      />
    </div>
  )
}
```

### 8.2 Cost Monitoring
```typescript
// src/lib/monitoring/costMonitor.ts
export class CostMonitor {
  async getCloudflareUsage(): Promise<CloudflareUsage> {
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/billing/usage`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    )

    return response.json()
  }

  async getFirebaseUsage(): Promise<FirebaseUsage> {
    // Firebase usage tracking implementation
    // Note: Firebase doesn't provide direct API access to billing
    // This would need to be tracked through Firebase Console or estimated
    return {
      firestore: {
        reads: 0,
        writes: 0,
        deletes: 0
      },
      storage: {
        stored: 0,
        bandwidth: 0
      },
      functions: {
        invocations: 0,
        computeTime: 0
      }
    }
  }

  async generateCostReport(): Promise<CostReport> {
    const [cloudflareUsage, firebaseUsage] = await Promise.all([
      this.getCloudflareUsage(),
      this.getFirebaseUsage()
    ])

    return {
      timestamp: new Date().toISOString(),
      cloudflare: {
        r2Storage: cloudflareUsage.r2?.storage || 0,
        r2Requests: cloudflareUsage.r2?.requests || 0,
        workers: cloudflareUsage.workers?.requests || 0,
        bandwidth: cloudflareUsage.bandwidth || 0
      },
      firebase: firebaseUsage,
      estimatedMonthlyCost: this.calculateEstimatedCost(cloudflareUsage, firebaseUsage)
    }
  }

  private calculateEstimatedCost(cf: CloudflareUsage, fb: FirebaseUsage): number {
    // Cloudflare costs
    const r2StorageCost = (cf.r2?.storage || 0) * 0.015 / 1000 // $0.015 per GB
    const r2RequestsCost = (cf.r2?.requests || 0) * 0.36 / 1000000 // $0.36 per million
    const workersCost = Math.max((cf.workers?.requests || 0) * 0.15 / 1000000, 5) // $5 minimum

    // Firebase costs (estimated)
    const firestoreCost = (fb.firestore.reads * 0.36 + fb.firestore.writes * 1.08) / 1000000
    const storageCost = fb.storage.stored * 0.026 / 1000 // $0.026 per GB
    const functionsCost = fb.functions.invocations * 0.40 / 1000000

    return r2StorageCost + r2RequestsCost + workersCost + firestoreCost + storageCost + functionsCost
  }
}

interface CloudflareUsage {
  r2?: {
    storage: number
    requests: number
  }
  workers?: {
    requests: number
  }
  bandwidth: number
}

interface FirebaseUsage {
  firestore: {
    reads: number
    writes: number
    deletes: number
  }
  storage: {
    stored: number
    bandwidth: number
  }
  functions: {
    invocations: number
    computeTime: number
  }
}

interface CostReport {
  timestamp: string
  cloudflare: {
    r2Storage: number
    r2Requests: number
    workers: number
    bandwidth: number
  }
  firebase: FirebaseUsage
  estimatedMonthlyCost: number
}
```

---

## 9. Troubleshooting Guide

### 9.1 Common Issues and Solutions

#### R2 Storage Issues
```typescript
// src/lib/troubleshooting/r2-diagnostics.ts
export class R2Diagnostics {
  async testConnection(): Promise<DiagnosticResult> {
    try {
      // Test basic connectivity
      const testKey = `diagnostics/test-${Date.now()}.txt`
      const testData = Buffer.from('test data')

      await r2Storage.uploadImage(testData, testKey, 'text/plain')
      await r2Storage.deleteImage(testKey)

      return {
        status: 'success',
        message: 'R2 connection successful'
      }
    } catch (error) {
      return {
        status: 'error',
        message: `R2 connection failed: ${error}`,
        troubleshooting: [
          'Check R2 credentials in environment variables',
          'Verify bucket exists and is accessible',
          'Check Cloudflare account permissions'
        ]
      }
    }
  }

  async testImageOptimization(): Promise<DiagnosticResult> {
    try {
      const testImageUrl = 'https://syndicaps-images.account.r2.cloudflarestorage.com/test.jpg'
      const optimizedUrl = `${testImageUrl}?w=400&q=80`

      const response = await fetch(optimizedUrl)

      if (response.ok) {
        return {
          status: 'success',
          message: 'Image optimization working'
        }
      } else {
        return {
          status: 'warning',
          message: 'Image optimization may not be configured',
          troubleshooting: [
            'Check Cloudflare Images configuration',
            'Verify image optimization worker deployment'
          ]
        }
      }
    } catch (error) {
      return {
        status: 'error',
        message: `Image optimization test failed: ${error}`
      }
    }
  }
}

interface DiagnosticResult {
  status: 'success' | 'warning' | 'error'
  message: string
  troubleshooting?: string[]
}
```

#### Performance Issues
```typescript
// src/lib/troubleshooting/performance-diagnostics.ts
export class PerformanceDiagnostics {
  async analyzeImageLoadTimes(): Promise<PerformanceAnalysis> {
    const r2Times: number[] = []
    const firebaseTimes: number[] = []

    // Test R2 image loading
    for (let i = 0; i < 10; i++) {
      const start = performance.now()
      await fetch('https://syndicaps-images.account.r2.cloudflarestorage.com/sample.jpg')
      const end = performance.now()
      r2Times.push(end - start)
    }

    // Test Firebase image loading (if any remain)
    for (let i = 0; i < 10; i++) {
      const start = performance.now()
      await fetch('https://firebasestorage.googleapis.com/sample.jpg')
      const end = performance.now()
      firebaseTimes.push(end - start)
    }

    return {
      r2: {
        average: r2Times.reduce((a, b) => a + b, 0) / r2Times.length,
        p95: this.calculatePercentile(r2Times, 95)
      },
      firebase: {
        average: firebaseTimes.reduce((a, b) => a + b, 0) / firebaseTimes.length,
        p95: this.calculatePercentile(firebaseTimes, 95)
      },
      recommendation: this.generateRecommendation(r2Times, firebaseTimes)
    }
  }

  private calculatePercentile(values: number[], percentile: number): number {
    const sorted = values.sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[index]
  }

  private generateRecommendation(r2Times: number[], firebaseTimes: number[]): string {
    const r2Avg = r2Times.reduce((a, b) => a + b, 0) / r2Times.length
    const fbAvg = firebaseTimes.reduce((a, b) => a + b, 0) / firebaseTimes.length

    if (r2Avg < fbAvg * 0.8) {
      return 'R2 is performing significantly better. Migration successful.'
    } else if (r2Avg > fbAvg * 1.2) {
      return 'R2 performance is worse than Firebase. Check CDN configuration.'
    } else {
      return 'Performance is similar. Monitor over longer period.'
    }
  }
}

interface PerformanceAnalysis {
  r2: {
    average: number
    p95: number
  }
  firebase: {
    average: number
    p95: number
  }
  recommendation: string
}
```

---

## 10. Success Metrics & KPIs

### 10.1 Performance Metrics
```typescript
// src/lib/metrics/successMetrics.ts
export class SuccessMetrics {
  private readonly targets = {
    imageLoadTime: 200, // ms
    apiResponseTime: 300, // ms
    cacheHitRate: 85, // %
    costReduction: 60, // %
    uptime: 99.9 // %
  }

  async calculateSuccessScore(): Promise<SuccessScore> {
    const metrics = await this.gatherMetrics()

    const scores = {
      performance: this.scorePerformance(metrics),
      cost: this.scoreCost(metrics),
      reliability: this.scoreReliability(metrics),
      userExperience: this.scoreUserExperience(metrics)
    }

    const overallScore = Object.values(scores).reduce((a, b) => a + b, 0) / 4

    return {
      overall: overallScore,
      breakdown: scores,
      metrics,
      recommendations: this.generateRecommendations(scores, metrics)
    }
  }

  private scorePerformance(metrics: GatheredMetrics): number {
    let score = 100

    if (metrics.avgImageLoadTime > this.targets.imageLoadTime) {
      score -= 20
    }

    if (metrics.avgApiResponseTime > this.targets.apiResponseTime) {
      score -= 20
    }

    if (metrics.cacheHitRate < this.targets.cacheHitRate) {
      score -= 15
    }

    return Math.max(0, score)
  }

  private scoreCost(metrics: GatheredMetrics): number {
    const actualReduction = ((metrics.previousCost - metrics.currentCost) / metrics.previousCost) * 100

    if (actualReduction >= this.targets.costReduction) {
      return 100
    } else if (actualReduction >= this.targets.costReduction * 0.8) {
      return 80
    } else if (actualReduction >= this.targets.costReduction * 0.6) {
      return 60
    } else {
      return 40
    }
  }

  private scoreReliability(metrics: GatheredMetrics): number {
    if (metrics.uptime >= this.targets.uptime) {
      return 100
    } else if (metrics.uptime >= 99.5) {
      return 80
    } else if (metrics.uptime >= 99.0) {
      return 60
    } else {
      return 40
    }
  }

  private scoreUserExperience(metrics: GatheredMetrics): number {
    let score = 100

    if (metrics.errorRate > 0.1) {
      score -= 30
    }

    if (metrics.userSatisfactionScore < 4.0) {
      score -= 20
    }

    return Math.max(0, score)
  }

  private generateRecommendations(scores: any, metrics: GatheredMetrics): string[] {
    const recommendations: string[] = []

    if (scores.performance < 80) {
      recommendations.push('Optimize CDN cache settings and image compression')
    }

    if (scores.cost < 80) {
      recommendations.push('Review usage patterns and optimize resource allocation')
    }

    if (scores.reliability < 90) {
      recommendations.push('Implement additional monitoring and alerting')
    }

    if (scores.userExperience < 80) {
      recommendations.push('Investigate user feedback and error patterns')
    }

    return recommendations
  }

  private async gatherMetrics(): Promise<GatheredMetrics> {
    // Implementation would gather actual metrics from monitoring systems
    return {
      avgImageLoadTime: 150,
      avgApiResponseTime: 250,
      cacheHitRate: 88,
      currentCost: 45,
      previousCost: 120,
      uptime: 99.95,
      errorRate: 0.05,
      userSatisfactionScore: 4.2
    }
  }
}

interface GatheredMetrics {
  avgImageLoadTime: number
  avgApiResponseTime: number
  cacheHitRate: number
  currentCost: number
  previousCost: number
  uptime: number
  errorRate: number
  userSatisfactionScore: number
}

interface SuccessScore {
  overall: number
  breakdown: {
    performance: number
    cost: number
    reliability: number
    userExperience: number
  }
  metrics: GatheredMetrics
  recommendations: string[]
}
```

---

## 11. Rollback Plan

### 11.1 Emergency Rollback Procedure
```bash
#!/bin/bash
# scripts/emergency-rollback.sh

echo "🚨 Starting emergency rollback to Firebase-only infrastructure..."

# 1. Disable Cloudflare Workers
echo "Disabling Cloudflare Workers..."
wrangler pages deployment create --project-name syndicaps --compatibility-date 2024-01-01 --env production

# 2. Update environment variables to disable R2
echo "Disabling R2 storage..."
wrangler pages secret put FEATURE_R2_STORAGE --project-name syndicaps
# Input: false

# 3. Revert DNS if needed
echo "Checking DNS configuration..."
# Manual step: Verify DNS points to Firebase hosting if needed

# 4. Clear Cloudflare cache
echo "Clearing Cloudflare cache..."
curl -X POST "https://api.cloudflare.com/client/v4/zones/${CLOUDFLARE_ZONE_ID}/purge_cache" \
  -H "Authorization: Bearer ${CLOUDFLARE_API_TOKEN}" \
  -H "Content-Type: application/json" \
  --data '{"purge_everything":true}'

# 5. Verify rollback
echo "Verifying rollback..."
curl -I https://syndicaps.com/api/health

echo "✅ Rollback completed. Monitor application for 30 minutes."
```

### 11.2 Gradual Rollback Strategy
```typescript
// src/lib/rollback/gradualRollback.ts
export class GradualRollback {
  async initiateRollback(component: 'r2' | 'workers' | 'all', percentage: number = 100): Promise<void> {
    switch (component) {
      case 'r2':
        await this.rollbackR2Storage(percentage)
        break
      case 'workers':
        await this.rollbackWorkers(percentage)
        break
      case 'all':
        await this.rollbackAll(percentage)
        break
    }
  }

  private async rollbackR2Storage(percentage: number): Promise<void> {
    // Gradually reduce R2 usage by updating feature flags
    const newPercentage = Math.max(0, 100 - percentage)

    await this.updateFeatureFlag('R2_ROLLOUT_PERCENTAGE', newPercentage.toString())

    console.log(`R2 storage usage reduced to ${newPercentage}%`)
  }

  private async rollbackWorkers(percentage: number): Promise<void> {
    // Disable Workers gradually
    const newPercentage = Math.max(0, 100 - percentage)

    await this.updateFeatureFlag('CF_WORKERS_ROLLOUT_PERCENTAGE', newPercentage.toString())

    console.log(`Cloudflare Workers usage reduced to ${newPercentage}%`)
  }

  private async rollbackAll(percentage: number): Promise<void> {
    await Promise.all([
      this.rollbackR2Storage(percentage),
      this.rollbackWorkers(percentage)
    ])
  }

  private async updateFeatureFlag(flag: string, value: string): Promise<void> {
    // Update environment variable in Cloudflare Pages
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/pages/projects/syndicaps/deployments`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          env_vars: {
            [flag]: value
          }
        })
      }
    )

    if (!response.ok) {
      throw new Error(`Failed to update feature flag ${flag}`)
    }
  }
}
```

This comprehensive implementation guide provides a structured approach to integrating Cloudflare infrastructure with Firebase, ensuring minimal disruption while maximizing performance benefits. The guide includes detailed code examples, monitoring strategies, troubleshooting procedures, and rollback plans to ensure a successful hybrid implementation.
