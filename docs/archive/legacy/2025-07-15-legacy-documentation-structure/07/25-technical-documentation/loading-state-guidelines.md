# Loading State Guidelines - Syndicaps Design System

**Document Version**: 1.0  
**Date**: January 2025  
**Author**: Syndicaps Development Team  

## 📋 Overview

This document defines the unified loading state system for the Syndicaps website, providing consistent user feedback during data loading and processing operations.

## 🎯 Design Principles

### **1. Consistency**
- All loading states use the same visual language
- Consistent timing and animation patterns
- Unified component API across the application

### **2. Accessibility**
- Proper ARIA labels and live regions
- Screen reader announcements
- Keyboard navigation support

### **3. Performance**
- Lightweight animations
- Optimized for mobile devices
- Reduced motion support

---

## 🔧 LoadingState Component API

### **Basic Usage**

```typescript
import { LoadingState } from '@/components/ui/SkeletonLoader';

// Spinner loading
<LoadingState type="spinner" size="md" message="Loading products..." />

// Skeleton loading
<LoadingState type="skeleton" width="100%" height="2rem" />

// Pulse loading
<LoadingState type="pulse" width="200px" height="1.5rem" />
```

### **Props Interface**

```typescript
interface LoadingStateProps {
  /** Type of loading indicator */
  type: 'spinner' | 'skeleton' | 'pulse';
  /** Size of the loading indicator */
  size?: 'sm' | 'md' | 'lg';
  /** Custom message to display */
  message?: string;
  /** Additional CSS classes */
  className?: string;
  /** Custom width for skeleton/pulse */
  width?: string | number;
  /** Custom height for skeleton/pulse */
  height?: string | number;
}
```

---

## 📊 Loading State Types

### **1. Spinner Loading**
**Use Case**: Form submissions, API calls, short operations  
**Duration**: 0.5-3 seconds  
**Visual**: Rotating accent-colored spinner with optional message

```typescript
// Form submission
<LoadingState 
  type="spinner" 
  size="sm" 
  message="Sending message..." 
/>

// Page loading
<LoadingState 
  type="spinner" 
  size="lg" 
  message="Loading page..." 
/>
```

### **2. Skeleton Loading**
**Use Case**: Content placeholders, product grids, lists  
**Duration**: 1-5 seconds  
**Visual**: Gray placeholder blocks matching content structure

```typescript
// Product card placeholder
<LoadingState 
  type="skeleton" 
  width="100%" 
  height="300px" 
  className="rounded-lg" 
/>

// Text placeholder
<LoadingState 
  type="skeleton" 
  width="80%" 
  height="1.5rem" 
/>
```

### **3. Pulse Loading**
**Use Case**: Interactive elements, buttons, dynamic content  
**Duration**: Continuous until loaded  
**Visual**: Pulsing opacity animation

```typescript
// Button loading state
<LoadingState 
  type="pulse" 
  width="120px" 
  height="44px" 
  className="rounded-lg" 
/>
```

---

## 🎨 Specialized Loading Components

### **ProductGridSkeleton**
**Use Case**: Product listing pages  
**Features**: Responsive grid, multiple cards, realistic proportions

```typescript
import { ProductGridSkeleton } from '@/components/ui/SkeletonLoader';

<ProductGridSkeleton count={6} />
```

### **ProductCardSkeleton**
**Use Case**: Individual product placeholders  
**Features**: Image, title, price, button placeholders

```typescript
import { ProductCardSkeleton } from '@/components/ui/SkeletonLoader';

<ProductCardSkeleton />
```

### **PageLoadingSkeleton**
**Use Case**: Full page loading states  
**Features**: Header, content, sidebar placeholders

```typescript
import { PageLoadingSkeleton } from '@/components/ui/SkeletonLoader';

<PageLoadingSkeleton />
```

---

## 📱 Implementation Examples

### **Homepage Implementation**

```typescript
// Before (Inconsistent)
{loading ? (
  <div className="loading-state text-center py-12">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
    <p className="text-gray-400 text-lg">Loading featured products...</p>
  </div>
) : (
  <ProductGrid products={products} />
)}

// After (Unified)
{loading ? (
  <ProductGridSkeleton count={6} />
) : (
  <ProductGrid products={products} />
)}
```

### **Contact Form Implementation**

```typescript
// Before (Custom spinner)
{isSubmitting ? (
  <>
    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
    Sending...
  </>
) : (
  <>Send Message</>
)}

// After (Unified)
{isSubmitting ? (
  <>
    <LoadingState type="spinner" size="sm" className="mr-2" />
    Sending...
  </>
) : (
  <>Send Message</>
)}
```

### **Shop Page Implementation**

```typescript
// Already using unified system
{loading ? (
  <ProductGridSkeleton />
) : paginatedProducts.length > 0 ? (
  <ProductGrid products={paginatedProducts} />
) : (
  <EmptyState />
)}
```

---

## 🎯 Usage Guidelines

### **When to Use Each Type**

| Loading Type | Use Case | Duration | Example |
|--------------|----------|----------|---------|
| **Spinner** | Form submissions, API calls | 0.5-3s | Contact form, login |
| **Skeleton** | Content placeholders | 1-5s | Product grids, articles |
| **Pulse** | Interactive elements | Continuous | Buttons, dynamic content |

### **Size Guidelines**

| Size | Spinner | Use Case |
|------|---------|----------|
| **sm** | 16px | Inline elements, buttons |
| **md** | 24px | Cards, sections |
| **lg** | 32px | Page-level loading |

### **Message Guidelines**

- **Be specific**: "Loading products..." not "Loading..."
- **Use present tense**: "Sending message..." not "Will send..."
- **Keep it short**: Maximum 3-4 words
- **Match user action**: "Saving changes..." for save operations

---

## ♿ Accessibility Features

### **ARIA Support**

```typescript
// Automatic ARIA attributes
<LoadingState type="spinner" message="Loading..." />
// Renders with:
// role="status"
// aria-live="polite"
// aria-label="Loading..."
```

### **Screen Reader Support**

- All loading states announce to screen readers
- Hidden text for context: `<span className="sr-only">Loading...</span>`
- Live regions update when loading state changes

### **Reduced Motion Support**

```css
@media (prefers-reduced-motion: reduce) {
  .animate-spin,
  .animate-pulse {
    animation: none;
  }
}
```

---

## 🚀 Performance Considerations

### **Animation Optimization**

- Use `transform` and `opacity` for smooth animations
- Avoid layout-triggering properties
- Optimize for 60fps on mobile devices

### **Bundle Size**

- Lazy load specialized skeletons
- Tree-shake unused variants
- Minimize animation libraries

### **Mobile Optimization**

- Faster animations on mobile (0.7x duration)
- Touch-friendly loading indicators
- Reduced complexity for slower devices

---

## 📊 Migration Checklist

### **Phase 1: Core Components** ✅
- [x] Homepage: Custom spinner → ProductGridSkeleton
- [x] Contact: Custom spinner → LoadingState spinner
- [x] Shop: Already using ProductGridSkeleton

### **Phase 2: Additional Components**
- [ ] Community page loading states
- [ ] Profile page loading states
- [ ] Admin panel loading states
- [ ] Blog page loading states

### **Phase 3: Specialized Cases**
- [ ] Image loading placeholders
- [ ] Chart/graph loading states
- [ ] Modal loading states
- [ ] Infinite scroll loading

---

## 🔍 Testing Guidelines

### **Visual Testing**

- Test all loading states in Storybook
- Verify animations on different devices
- Check reduced motion compliance

### **Accessibility Testing**

- Screen reader announcement testing
- Keyboard navigation testing
- Color contrast verification

### **Performance Testing**

- Animation frame rate monitoring
- Bundle size impact measurement
- Mobile device testing

---

**Document Status**: ✅ Complete  
**Implementation Status**: Phase 1 Complete  
**Next Review**: Phase 2 completion  
**Related Files**: `src/components/ui/SkeletonLoader.tsx`
