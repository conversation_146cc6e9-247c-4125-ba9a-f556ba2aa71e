# Community Setup Documentation

This document provides comprehensive instructions for setting up the Syndicaps community system with Firebase integration.

## Overview

The community system includes:
- **Real-time Activity Feed** - Live community activity tracking
- **Community Statistics** - Live member counts and engagement metrics
- **Search System** - Multi-collection search across submissions, discussions, challenges, and members
- **Submission Upload** - Complete file upload system with Firebase Storage
- **Challenge System** - Community challenges and competitions
- **Discussion Forums** - Community discussions and support
- **Voting System** - Community-driven decision making
- **Leaderboards** - Member rankings and achievements
- **Spotlight System** - Monthly member highlights

## Prerequisites

### 1. Firebase Project Setup
- Firebase project with Firestore and Storage enabled
- Firebase Admin SDK service account key
- Proper IAM permissions for database administration

### 2. Environment Configuration
Ensure your `.env.local` file contains:
```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Admin SDK (for production setup)
FIREBASE_SERVICE_ACCOUNT_PATH=./firebase-service-account.json
```

### 3. Firebase Service Account
Download your Firebase Admin SDK service account key and place it as `firebase-service-account.json` in the project root.

## Setup Commands

### Quick Setup (Development)
```bash
# Basic community setup with client SDK
npm run community:setup
```

### Production Setup
```bash
# Dry run to preview changes
npm run community:setup:dry

# Full production setup with Admin SDK
npm run community:setup:prod
```

### Complete Database Setup
```bash
# Setup everything including community
npm run db:full
```

## Setup Process

### Phase 1: Security Rules ✅
- Firestore security rules for all 12 community collections
- Firebase Storage rules for user submissions
- Proper authentication and authorization

### Phase 2: Data Integration ✅
- Firebase service architecture with 8 specialized services
- Real-time data subscriptions
- Mock data replacement with live Firebase integration

### Phase 3: Feature Implementation ✅
- Community search across all collections
- File upload system with image management
- Error handling with retry mechanisms
- Performance optimizations

## Collections Created

The setup script creates the following Firestore collections:

### Core Collections
- `community_stats` - Global community statistics
- `community_members` - Member profiles and preferences
- `activities` - Real-time activity feed
- `spotlight_members` - Monthly member highlights

### Content Collections
- `challenges` - Community challenges and competitions
- `submissions` - User submissions with file attachments
- `discussions` - Community discussions and forums
- `votes` - Community voting items

### Engagement Collections
- `leaderboard` - Member rankings and achievements

## Firebase Storage Structure

```
community-submissions/
├── {userId}/
│   ├── {submissionId}/
│   │   ├── image_1.jpg
│   │   ├── image_2.jpg
│   │   └── ...
│   └── ...
├── community-challenges/
│   ├── {challengeId}/
│   │   └── banner.jpg
│   └── ...
└── member-avatars/
    ├── {userId}/
    │   └── avatar.jpg
    └── ...
```

## Security Rules

### Firestore Rules
```javascript
// Example rule for submissions
match /submissions/{submissionId} {
  allow read: if resource.data.status == 'approved' || 
                 isOwner(resource.data.authorId) || 
                 isAdmin();
  allow create: if isAuthenticated() && 
                   isOwner(request.resource.data.authorId);
  allow update: if isOwner(resource.data.authorId) || isAdmin();
  allow delete: if isOwner(resource.data.authorId) || isAdmin();
}
```

### Storage Rules
```javascript
// Example rule for submission images
match /community-submissions/{userId}/{submissionId}/{allPaths=**} {
  allow read: if true; // Public read for community content
  allow write: if isOwner(userId) && 
                  isValidImageFile() &&
                  request.resource.size < 15 * 1024 * 1024; // 15MB limit
}
```

## Services Architecture

### 1. ActivityFeedService
- Real-time activity subscriptions
- Activity creation and engagement tracking
- Cleanup and memory management

### 2. CommunityStatsService
- Live community statistics
- Real-time updates and subscriptions
- Fallback data handling

### 3. FeaturedContentService
- Featured submissions and trending discussions
- Content curation and filtering

### 4. SubmissionUploadService
- Complete file upload workflow
- Image validation and processing
- Firebase Storage integration

### 5. CommunitySearchService
- Multi-collection search functionality
- Client-side filtering for performance
- Trending searches tracking

### 6. VotingService
- Community voting and decision making
- Vote tracking and management

### 7. LeaderboardService
- Member rankings and achievements
- Real-time leaderboard updates

### 8. SpotlightService
- Monthly member highlights
- Member work showcasing

## Error Handling

### CommunityErrorBoundary
- Specialized error boundary for community features
- Automatic retry mechanisms
- User-friendly error messages

### useCommunityError Hook
- Consistent error handling patterns
- Error categorization and retry logic
- Fallback data management

## Performance Optimizations

### 1. Service Singleton Pattern
```typescript
export const communityStatsService = CommunityStatsService.getInstance()
```

### 2. Subscription Cleanup
```typescript
useEffect(() => {
  const unsubscribe = service.subscribe(callback)
  return () => unsubscribe()
}, [])
```

### 3. Fallback Data
- Offline support with meaningful fallback data
- Progressive loading with skeleton states
- Client-side caching strategies

## Testing

### 1. Development Testing
```bash
# Test with development environment
npm run dev
# Navigate to /community/discover
```

### 2. Error Testing
- Test Firebase connection failures
- Test authentication errors
- Test permission denied scenarios

### 3. Performance Testing
- Load testing with multiple subscriptions
- Memory leak testing with cleanup
- Image upload stress testing

## Troubleshooting

### Common Issues

#### 1. Permission Denied Errors
```
Error: Permission denied (Firebase rules)
```
**Solution**: Check Firestore security rules and user authentication

#### 2. Storage Upload Failures
```
Error: Firebase Storage not initialized
```
**Solution**: Verify Storage configuration and rules deployment

#### 3. Admin SDK Authentication
```
Error: Service account file not found
```
**Solution**: Ensure `firebase-service-account.json` is in project root

### Debug Commands
```bash
# Check Firebase connection
npm run test:firebase

# Verify database structure
npm run verify:database

# Test admin access
npm run test:admin
```

## Monitoring

### 1. Firebase Console
- Monitor collection usage and performance
- Track Storage usage and costs
- Review security rule analytics

### 2. Application Monitoring
- Error tracking with Sentry integration
- Performance monitoring with built-in hooks
- User engagement analytics

## Production Deployment

### 1. Pre-deployment Checklist
- [ ] Security rules deployed and tested
- [ ] Service account permissions verified
- [ ] Environment variables configured
- [ ] Storage buckets properly configured
- [ ] Database indexes created for performance

### 2. Deployment Steps
```bash
# 1. Deploy Firebase rules
firebase deploy --only firestore:rules,storage

# 2. Run production setup
npm run community:setup:prod

# 3. Verify deployment
npm run verify:database
```

### 3. Post-deployment Verification
- Test all community features
- Verify real-time subscriptions
- Test file uploads and downloads
- Check error handling and fallbacks

## Maintenance

### Regular Tasks
- Monitor storage usage and costs
- Review and update security rules
- Clean up old activity feed entries
- Update featured content and spotlights
- Monitor and optimize database queries

### Data Management
```bash
# Clear test data
npm run community:clear

# Reset with fresh data
npm run community:reset

# Update specific collections
npm run community:setup:prod --collections=stats,activities
```

## Support

For issues with community setup:
1. Check this documentation
2. Review Firebase console for errors
3. Test with dry run mode first
4. Contact development team with specific error messages

---

*Last updated: December 2024*
*Version: 1.0.0*