# Community Timeline Database Schema

## Overview
This document defines the database schema for the unified community timeline system that aggregates blog posts, user activities, achievements, and community milestones into a single, chronological feed.

## 1. Core Timeline Collection

### `community_timeline` Collection

```typescript
interface TimelineActivity {
  // Core identification
  id: string
  
  // Content classification
  type: TimelineActivityType
  category: ActivityCategory
  priority: ActivityPriority
  
  // Content data
  title: string
  description: string
  content?: string // Full content for blog posts, detailed descriptions
  excerpt?: string // Short preview text
  
  // User attribution
  userId?: string // User who performed the action (null for system activities)
  userName?: string // Display name for the user
  userAvatar?: string // User profile image URL
  userTier?: string // User tier for display (Bronze, Silver, Gold, Platinum)
  
  // Content references
  sourceId?: string // ID of the source content (blog post ID, achievement ID, etc.)
  sourceType?: string // Type of source content
  sourceUrl?: string // Direct link to source content
  
  // Media and visual content
  imageUrl?: string // Featured image or activity-related image
  thumbnailUrl?: string // Thumbnail version of image
  mediaType?: 'image' | 'video' | 'gif' | 'none'
  
  // Interaction data
  likes: number
  comments: number
  shares: number
  views: number
  
  // User interactions tracking
  likedBy: string[] // Array of user IDs who liked this activity
  sharedBy: string[] // Array of user IDs who shared this activity
  
  // Gamification integration
  pointsAwarded?: number // Points awarded for this activity
  achievementData?: {
    achievementId: string
    achievementName: string
    achievementIcon: string
    achievementRarity: 'common' | 'rare' | 'epic' | 'legendary'
  }
  
  // Blog post specific data
  blogData?: {
    slug: string
    category: string
    tags: string[]
    readTime: number // Estimated reading time in minutes
    featured: boolean
  }
  
  // Purchase/order specific data
  purchaseData?: {
    orderId: string
    productName: string
    productImage: string
    amount: number
    productCategory: string
  }
  
  // Raffle specific data
  raffleData?: {
    raffleId: string
    raffleName: string
    productName: string
    isWinner: boolean
    entryCount?: number
  }
  
  // Review specific data
  reviewData?: {
    productId: string
    productName: string
    rating: number
    reviewText: string
  }
  
  // Metadata and system fields
  metadata: Record<string, any> // Flexible metadata for future extensions
  isVisible: boolean // Visibility control for moderation
  isFeatured: boolean // Featured activities get priority display
  isSystemGenerated: boolean // Distinguishes user vs system activities
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
  publishedAt?: Timestamp // For scheduled content
  
  // Moderation and admin
  moderationStatus: 'approved' | 'pending' | 'rejected'
  moderatedBy?: string // Admin user ID who moderated
  moderatedAt?: Timestamp
  reportCount: number // Number of user reports
  
  // Performance optimization
  searchKeywords: string[] // For search functionality
  trending: boolean // Trending content flag
  engagementScore: number // Calculated engagement score for sorting
}
```

### Timeline Activity Types

```typescript
type TimelineActivityType = 
  // Blog and content
  | 'blog_post_published'
  | 'blog_post_featured'
  
  // User achievements and milestones
  | 'achievement_unlocked'
  | 'user_registered'
  | 'user_tier_upgraded'
  | 'milestone_reached'
  
  // E-commerce activities
  | 'purchase_made'
  | 'review_posted'
  | 'wishlist_shared'
  
  // Raffle system
  | 'raffle_won'
  | 'raffle_entry'
  | 'raffle_started'
  | 'raffle_ended'
  
  // Social and community
  | 'social_share'
  | 'community_milestone'
  | 'user_spotlight'
  
  // System and admin
  | 'system_announcement'
  | 'maintenance_notice'
  | 'feature_release'

type ActivityCategory = 
  | 'content' // Blog posts, announcements
  | 'achievement' // User achievements, milestones
  | 'commerce' // Purchases, reviews
  | 'social' // Shares, community interactions
  | 'system' // System messages, maintenance

type ActivityPriority = 
  | 'low' // Regular activities
  | 'medium' // Important user activities
  | 'high' // Featured content, major achievements
  | 'critical' // System announcements, major milestones
```

## 2. Timeline Comments Collection

### `timeline_comments` Collection

```typescript
interface TimelineComment {
  id: string
  timelineActivityId: string // Reference to timeline activity
  parentCommentId?: string // For nested replies
  
  // User data
  userId: string
  userName: string
  userAvatar?: string
  userTier?: string
  
  // Comment content
  content: string
  mentions: string[] // Array of mentioned user IDs
  
  // Interaction data
  likes: number
  likedBy: string[]
  
  // Moderation
  isVisible: boolean
  moderationStatus: 'approved' | 'pending' | 'rejected'
  reportCount: number
  
  // Timestamps
  createdAt: Timestamp
  updatedAt: Timestamp
  
  // Metadata
  metadata: Record<string, any>
}
```

## 3. Timeline User Interactions Collection

### `timeline_interactions` Collection

```typescript
interface TimelineInteraction {
  id: string
  timelineActivityId: string
  userId: string
  
  // Interaction type
  type: 'like' | 'share' | 'view' | 'click' | 'report'
  
  // Share specific data
  shareData?: {
    platform: 'twitter' | 'facebook' | 'instagram' | 'discord' | 'copy_link'
    pointsAwarded: number // 150 points for social sharing
  }
  
  // Timestamps
  createdAt: Timestamp
  
  // Metadata
  metadata: Record<string, any>
}
```

## 4. Database Indexes and Performance

### Required Indexes

```javascript
// Primary timeline queries
db.community_timeline.createIndex({ "createdAt": -1 })
db.community_timeline.createIndex({ "type": 1, "createdAt": -1 })
db.community_timeline.createIndex({ "category": 1, "createdAt": -1 })
db.community_timeline.createIndex({ "userId": 1, "createdAt": -1 })

// Visibility and moderation
db.community_timeline.createIndex({ "isVisible": 1, "moderationStatus": 1, "createdAt": -1 })
db.community_timeline.createIndex({ "isFeatured": 1, "createdAt": -1 })

// Engagement and trending
db.community_timeline.createIndex({ "trending": 1, "engagementScore": -1 })
db.community_timeline.createIndex({ "engagementScore": -1, "createdAt": -1 })

// Search functionality
db.community_timeline.createIndex({ "searchKeywords": 1 })
db.community_timeline.createIndex({ "title": "text", "description": "text", "content": "text" })

// Comments
db.timeline_comments.createIndex({ "timelineActivityId": 1, "createdAt": -1 })
db.timeline_comments.createIndex({ "userId": 1, "createdAt": -1 })
db.timeline_comments.createIndex({ "parentCommentId": 1, "createdAt": -1 })

// Interactions
db.timeline_interactions.createIndex({ "timelineActivityId": 1, "type": 1 })
db.timeline_interactions.createIndex({ "userId": 1, "type": 1, "createdAt": -1 })
```

## 5. Integration with Existing Collections

### Blog Posts Integration

When a blog post is published, create a timeline activity:

```typescript
// Triggered on blog post publish
const createBlogTimelineActivity = async (blogPost: BlogPost) => {
  const timelineActivity: TimelineActivity = {
    type: 'blog_post_published',
    category: 'content',
    priority: blogPost.featured ? 'high' : 'medium',
    title: blogPost.title,
    description: blogPost.excerpt,
    content: blogPost.content,
    sourceId: blogPost.id,
    sourceType: 'blog_post',
    sourceUrl: `/blog/${blogPost.slug}`,
    imageUrl: blogPost.featuredImage,
    blogData: {
      slug: blogPost.slug,
      category: blogPost.category,
      tags: blogPost.tags,
      readTime: calculateReadTime(blogPost.content),
      featured: blogPost.featured
    },
    searchKeywords: [...blogPost.tags, blogPost.category, ...extractKeywords(blogPost.title)],
    isFeatured: blogPost.featured,
    isSystemGenerated: false,
    // ... other fields
  }
}
```

### Gamification Integration

Connect with existing `user_activities` collection:

```typescript
// Triggered on achievement unlock
const createAchievementTimelineActivity = async (userAchievement: UserAchievement) => {
  const timelineActivity: TimelineActivity = {
    type: 'achievement_unlocked',
    category: 'achievement',
    priority: getAchievementPriority(userAchievement.rarity),
    title: `${userAchievement.userName} unlocked "${userAchievement.achievementName}"`,
    description: userAchievement.achievementDescription,
    userId: userAchievement.userId,
    userName: userAchievement.userName,
    userAvatar: userAchievement.userAvatar,
    pointsAwarded: userAchievement.pointsAwarded,
    achievementData: {
      achievementId: userAchievement.achievementId,
      achievementName: userAchievement.achievementName,
      achievementIcon: userAchievement.achievementIcon,
      achievementRarity: userAchievement.rarity
    },
    // ... other fields
  }
}
```

## 6. Data Migration Strategy

### Phase 1: Create New Collections
1. Create `community_timeline` collection with indexes
2. Create `timeline_comments` and `timeline_interactions` collections
3. Set up proper security rules

### Phase 2: Backfill Historical Data
1. Migrate recent blog posts (last 6 months) to timeline
2. Migrate significant user achievements and milestones
3. Create system milestone activities for major platform events

### Phase 3: Real-time Integration
1. Set up triggers for new blog posts
2. Integrate with gamification system for real-time activity creation
3. Implement user interaction tracking

## 7. Security Rules

```javascript
// Firestore Security Rules for timeline collections
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Timeline activities - read for all, write for system only
    match /community_timeline/{activityId} {
      allow read: if true; // Public read access
      allow write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid) ||
        hasAdminRole(request.auth.uid);
    }
    
    // Comments - authenticated users can create/edit their own
    match /timeline_comments/{commentId} {
      allow read: if resource.data.isVisible == true;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.userId || hasAdminRole(request.auth.uid));
    }
    
    // Interactions - users can create their own interactions
    match /timeline_interactions/{interactionId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
  }
}
```
