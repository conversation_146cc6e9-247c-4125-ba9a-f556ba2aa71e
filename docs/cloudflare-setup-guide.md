# Cloudflare Setup Guide for Syndicaps

This guide walks you through setting up all required Cloudflare services and API tokens for the Syndicaps hybrid deployment.

## Prerequisites

- Cloudflare account (free tier is sufficient for initial setup)
- Domain registered and managed through Cloudflare (optional for initial testing)
- Wrangler CLI installed and authenticated

## 1. Account Setup

### 1.1 Get Account ID
Your account ID is already configured: `24a0c424061d2684ac21f5dd6284f906`

### 1.2 Get Zone ID (Optional for Custom Domain)
If you have a domain managed by Cloudflare:
1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Select your domain
3. Copy the Zone ID from the right sidebar
4. Update `CLOUDFLARE_ZONE_ID` in `.env.local`

## 2. API Token Setup

### 2.1 Create API Token
1. Go to [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. Click "Create Token"
3. Use "Custom token" template
4. Configure permissions:
   - **Account**: `Cloudflare Workers:Edit`, `Account Settings:Read`
   - **Zone**: `Zone:Read`, `Zone Settings:Edit` (if using custom domain)
   - **Zone Resources**: Include all zones or specific zone
5. Add IP restrictions if needed
6. Click "Continue to summary" → "Create Token"
7. Copy the token and update `CLOUDFLARE_API_TOKEN` in `.env.local`

### 2.2 Verify API Token
```bash
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json"
```

## 3. R2 Storage Setup

### 3.1 Create R2 API Token
1. Go to [Cloudflare R2](https://dash.cloudflare.com/r2)
2. Click "Manage R2 API tokens"
3. Click "Create API token"
4. Configure:
   - **Token name**: `syndicaps-r2-access`
   - **Permissions**: `Object Read & Write`
   - **Specify bucket**: Select all Syndicaps buckets or leave blank for all
5. Click "Create API token"
6. Copy the Access Key ID and Secret Access Key
7. Update `.env.local`:
   ```
   R2_ACCESS_KEY_ID=your_access_key_id
   R2_SECRET_ACCESS_KEY=your_secret_access_key
   ```

### 3.2 Verify R2 Access
```bash
# Test bucket access
npx wrangler r2 bucket list
```

## 4. Environment Variables Validation

Run the validation script to ensure all variables are properly configured:

```bash
node scripts/validate-cloudflare-env.js
```

## 5. Infrastructure Verification

### 5.1 Verify R2 Buckets
```bash
npx wrangler r2 bucket list
```

Expected buckets:
- `syndicaps-images-dev`
- `syndicaps-backups-dev`
- `syndicaps-images-staging`
- `syndicaps-backups-staging`
- `syndicaps-images`
- `syndicaps-backups`

### 5.2 Verify KV Namespaces
```bash
npx wrangler kv namespace list
```

Expected namespaces:
- `CACHE_KV_DEV` (ID: 71cabead02ef4f448b6b9ebf392fee0c)
- `SESSION_KV_DEV` (ID: 800ef077f93240b791cd22e4e7d222d2)
- `CACHE_KV_STAGING` (ID: 17ddf6927df24c45a47cab108095526a)
- `SESSION_KV_STAGING` (ID: 8ed4d59d0fbf4a31a5b9f4e78dd1a75f)
- `CACHE_KV_PROD` (ID: a0cc4dfdf2ac4655be3369661d79455b)
- `SESSION_KV_PROD` (ID: c25f365c2b06419ea1cf1f4cdafe02e9)

## 6. Security Best Practices

### 6.1 API Token Security
- Store tokens securely in environment variables
- Use least privilege principle for token permissions
- Rotate tokens regularly
- Monitor token usage in Cloudflare dashboard

### 6.2 Environment Separation
- Use different tokens for development, staging, and production
- Implement proper access controls
- Monitor resource usage and costs

## 7. Troubleshooting

### 7.1 Common Issues

**Authentication Errors**
```bash
# Re-authenticate Wrangler
npx wrangler logout
npx wrangler login
```

**Permission Errors**
- Verify API token permissions
- Check account and zone access
- Ensure token is not expired

**Resource Not Found**
- Verify account ID and zone ID
- Check resource names and IDs
- Ensure resources exist in correct account

### 7.2 Validation Commands

```bash
# Validate environment
node scripts/validate-cloudflare-env.js

# Test Wrangler authentication
npx wrangler whoami

# Test R2 connectivity
npx wrangler r2 bucket list

# Test KV connectivity
npx wrangler kv namespace list
```

## 8. Next Steps

After completing this setup:

1. Run infrastructure validation: `bash scripts/setup-cloudflare-infrastructure.sh`
2. Deploy test Workers: `npx wrangler deploy --env development`
3. Test hybrid functionality
4. Configure monitoring and alerting

## 9. Cost Monitoring

### 9.1 Free Tier Limits
- **Workers**: 100,000 requests/day
- **KV**: 100,000 reads/day, 1,000 writes/day
- **R2**: 10GB storage, Class A operations: 1M/month, Class B: 10M/month

### 9.2 Monitoring Usage
- Check [Cloudflare Analytics](https://dash.cloudflare.com/analytics)
- Set up billing alerts
- Monitor resource usage regularly

## Support

For issues or questions:
- Check [Cloudflare Documentation](https://developers.cloudflare.com/)
- Visit [Cloudflare Community](https://community.cloudflare.com/)
- Contact <EMAIL>
