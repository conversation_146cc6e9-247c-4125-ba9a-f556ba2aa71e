# Disaster Recovery Procedures - Cloudflare Hybrid Deployment

## Executive Summary

This document outlines comprehensive disaster recovery procedures for the Syndicaps Cloudflare hybrid deployment system. It provides step-by-step emergency procedures, rollback protocols, and disaster recovery strategies to ensure business continuity and minimize downtime during critical incidents.

## 🚨 Emergency Response Overview

### Incident Classification

#### Severity Levels

**P0 - Critical (System Down)**
- Complete system outage
- Data loss or corruption
- Security breach with data exposure
- **Response Time**: Immediate (< 15 minutes)
- **Escalation**: CEO, CTO, Security Team

**P1 - High (Major Impact)**
- Major functionality unavailable
- Significant performance degradation (>50% slower)
- Partial data loss
- **Response Time**: 1 hour
- **Escalation**: Engineering Manager, DevOps Lead

**P2 - Medium (Moderate Impact)**
- Minor functionality issues
- Moderate performance impact (20-50% slower)
- Non-critical feature failures
- **Response Time**: 4 hours
- **Escalation**: On-call Engineer

**P3 - Low (Minor Impact)**
- Cosmetic issues
- Documentation problems
- Non-urgent improvements
- **Response Time**: Next business day
- **Escalation**: Development Team

### Emergency Contact Tree

```
┌─────────────────┐
│   Incident      │
│   Detected      │
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │ On-Call   │
    │ Engineer  │
    └─────┬─────┘
          │
    ┌─────▼─────┐
    │ DevOps    │
    │ Lead      │
    └─────┬─────┘
          │
    ┌─────▼─────┐
    │Engineering│
    │ Manager   │
    └─────┬─────┘
          │
    ┌─────▼─────┐
    │   CTO     │
    └───────────┘
```

### Immediate Response Checklist

#### First 5 Minutes
- [ ] Acknowledge incident in monitoring system
- [ ] Assess scope and severity
- [ ] Notify incident commander
- [ ] Create incident channel (#incident-YYYY-MM-DD-HH-MM)
- [ ] Begin status page updates

#### First 15 Minutes
- [ ] Assemble incident response team
- [ ] Identify affected systems and users
- [ ] Implement immediate mitigation if available
- [ ] Communicate with stakeholders
- [ ] Begin detailed investigation

#### First 30 Minutes
- [ ] Determine root cause
- [ ] Implement temporary fixes
- [ ] Escalate if needed
- [ ] Update status page with ETA
- [ ] Prepare rollback plan if applicable

## 🔄 Rollback Procedures

### Application Rollback

#### Next.js Application Rollback

**Scenario**: New deployment causing issues

**Steps**:
1. **Identify Last Known Good Version**
   ```bash
   # Check deployment history
   wrangler pages deployment list --project-name syndicaps
   
   # Identify stable deployment ID
   STABLE_DEPLOYMENT_ID="abc123def456"
   ```

2. **Immediate Rollback**
   ```bash
   # Promote previous deployment
   wrangler pages deployment promote $STABLE_DEPLOYMENT_ID
   
   # Verify rollback
   curl -I https://syndicaps.com
   ```

3. **Verification**
   - [ ] Check homepage loads correctly
   - [ ] Verify critical user flows
   - [ ] Confirm admin dashboard access
   - [ ] Test API endpoints

**Estimated Time**: 5-10 minutes

#### Worker Rollback

**Scenario**: Worker deployment causing errors

**Image Optimization Worker**:
```bash
# Navigate to worker directory
cd workers/image-optimizer

# Check deployment history
wrangler deployments list

# Rollback to previous version
wrangler rollback --name image-optimizer

# Verify functionality
curl "https://images.syndicaps.com/optimize?width=400&height=300&url=https://example.com/test.jpg"
```

**API Cache Worker**:
```bash
# Navigate to worker directory
cd workers/api-cache

# Rollback deployment
wrangler rollback --name api-cache

# Test API caching
curl -H "Cache-Control: no-cache" "https://api.syndicaps.com/api/products"
```

**Estimated Time**: 3-5 minutes per worker

### Database Rollback

#### Firestore Rollback

**Scenario**: Data corruption or incorrect migration

**Point-in-Time Recovery**:
```bash
# List available backups
gcloud firestore backups list --location=us-central1

# Restore from backup
gcloud firestore databases restore \
  --source-backup=projects/syndicaps/locations/us-central1/backups/backup-20240127 \
  --destination-database=syndicaps-restored

# Switch application to restored database
# Update FIREBASE_PROJECT_ID in environment variables
```

**Estimated Time**: 30-60 minutes

#### Configuration Rollback

**Feature Flags Emergency Disable**:
```bash
# Disable all non-critical feature flags
curl -X POST "https://syndicaps.com/api/admin/feature-flags/emergency-disable" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{"reason": "Emergency rollback", "incident_id": "INC-001"}'
```

**Environment Variables Rollback**:
```bash
# Revert to previous environment configuration
git checkout HEAD~1 -- .env.production
wrangler secret put FIREBASE_PRIVATE_KEY < previous_key.txt
```

**Estimated Time**: 2-5 minutes

## 🏥 Disaster Recovery Scenarios

### Scenario 1: Complete Cloudflare Outage

**Impact**: All services unavailable
**Probability**: Low
**RTO**: 2 hours
**RPO**: 15 minutes

#### Immediate Actions
1. **Activate Emergency Mode**
   ```bash
   # Switch DNS to backup infrastructure
   # Update A records to point to backup servers
   dig @******* syndicaps.com
   ```

2. **Deploy to Backup Infrastructure**
   ```bash
   # Deploy to Vercel as backup
   vercel --prod
   
   # Update DNS records
   # A record: @ → vercel-ip
   # CNAME: www → vercel-domain
   ```

3. **Restore Critical Services**
   - [ ] Homepage and product catalog
   - [ ] User authentication
   - [ ] Order processing
   - [ ] Admin dashboard (limited)

#### Recovery Steps
1. **Monitor Cloudflare Status**
   - Check status.cloudflare.com
   - Monitor incident updates
   - Estimate restoration time

2. **Communicate with Users**
   - Update status page
   - Send email notifications
   - Post social media updates

3. **Restore Full Functionality**
   - Switch back to Cloudflare when available
   - Verify all services
   - Update DNS records
   - Monitor for issues

### Scenario 2: R2 Storage Failure

**Impact**: Image uploads/downloads affected
**Probability**: Medium
**RTO**: 30 minutes
**RPO**: 5 minutes

#### Immediate Actions
1. **Enable Firebase Storage Fallback**
   ```bash
   # Disable R2 feature flag
   curl -X POST "https://syndicaps.com/api/admin/feature-flags/toggle" \
     -H "Authorization: Bearer $ADMIN_TOKEN" \
     -d '{"flag": "hybrid-r2-storage", "enabled": false}'
   ```

2. **Verify Fallback Functionality**
   - [ ] Test image uploads
   - [ ] Verify existing images load
   - [ ] Check admin media library

#### Recovery Steps
1. **Monitor R2 Service Status**
2. **Sync Missing Data**
   ```bash
   # Sync new uploads from Firebase to R2 when restored
   node scripts/sync-firebase-to-r2.js --since="2024-01-27T10:00:00Z"
   ```

3. **Re-enable R2 Storage**
   ```bash
   # Re-enable feature flag
   curl -X POST "https://syndicaps.com/api/admin/feature-flags/toggle" \
     -H "Authorization: Bearer $ADMIN_TOKEN" \
     -d '{"flag": "hybrid-r2-storage", "enabled": true}'
   ```

### Scenario 3: Firebase Outage

**Impact**: Authentication, database, and core functionality affected
**Probability**: Low
**RTO**: 1 hour
**RPO**: 10 minutes

#### Immediate Actions
1. **Enable Read-Only Mode**
   ```bash
   # Deploy read-only version
   wrangler pages deploy dist-readonly --project-name syndicaps-readonly
   
   # Update DNS to point to read-only version
   ```

2. **Activate Cached Data**
   - Serve cached product data
   - Display cached user profiles
   - Disable write operations

#### Recovery Steps
1. **Monitor Firebase Status**
2. **Restore Write Operations Gradually**
   - Enable authentication first
   - Restore database writes
   - Enable full functionality

3. **Data Consistency Check**
   ```bash
   # Run data consistency validation
   node scripts/validate-data-consistency.js
   ```

### Scenario 4: Complete System Compromise

**Impact**: Security breach, all systems potentially compromised
**Probability**: Very Low
**RTO**: 4-8 hours
**RPO**: 1 hour

#### Immediate Actions
1. **Isolate Systems**
   ```bash
   # Disable all workers
   wrangler delete image-optimizer
   wrangler delete api-cache
   
   # Take down main site
   wrangler pages deployment create --compatibility-date=1970-01-01
   ```

2. **Secure Communications**
   - Use out-of-band communication
   - Activate incident war room
   - Contact security team

3. **Preserve Evidence**
   - Capture system logs
   - Document timeline
   - Preserve forensic data

#### Recovery Steps
1. **Security Assessment**
   - Identify breach scope
   - Assess data exposure
   - Determine attack vector

2. **Clean Rebuild**
   - Deploy from clean codebase
   - Rotate all credentials
   - Rebuild infrastructure

3. **Gradual Restoration**
   - Restore services incrementally
   - Monitor for suspicious activity
   - Implement additional security measures

## 🔧 Recovery Tools and Scripts

### Automated Recovery Scripts

#### System Health Check
```bash
#!/bin/bash
# scripts/health-check.sh

echo "🔍 Running system health check..."

# Check Cloudflare Workers
echo "Checking Image Optimization Worker..."
curl -f "https://images.syndicaps.com/health" || echo "❌ Image worker down"

echo "Checking API Cache Worker..."
curl -f "https://api.syndicaps.com/health" || echo "❌ API worker down"

# Check R2 Storage
echo "Checking R2 Storage..."
aws s3 ls s3://syndicaps-images --endpoint-url=$R2_ENDPOINT || echo "❌ R2 storage down"

# Check Firebase
echo "Checking Firebase..."
curl -f "https://syndicaps.com/api/health/firebase" || echo "❌ Firebase down"

echo "✅ Health check complete"
```

#### Emergency Deployment Script
```bash
#!/bin/bash
# scripts/emergency-deploy.sh

DEPLOYMENT_TYPE=$1
INCIDENT_ID=$2

echo "🚨 Emergency deployment initiated"
echo "Type: $DEPLOYMENT_TYPE"
echo "Incident: $INCIDENT_ID"

case $DEPLOYMENT_TYPE in
  "rollback")
    echo "Rolling back to last known good state..."
    git checkout HEAD~1
    npm run build
    wrangler pages deploy dist --project-name syndicaps
    ;;
  "readonly")
    echo "Deploying read-only mode..."
    npm run build:readonly
    wrangler pages deploy dist-readonly --project-name syndicaps
    ;;
  "maintenance")
    echo "Deploying maintenance page..."
    wrangler pages deploy maintenance --project-name syndicaps
    ;;
esac

echo "✅ Emergency deployment complete"
```

### Recovery Validation Checklist

#### Post-Recovery Validation
- [ ] **Homepage loads correctly**
  - [ ] Main navigation functional
  - [ ] Product images display
  - [ ] Search functionality works

- [ ] **User Authentication**
  - [ ] Login/logout functional
  - [ ] Registration process works
  - [ ] Password reset functional

- [ ] **E-commerce Functions**
  - [ ] Product browsing works
  - [ ] Cart functionality
  - [ ] Checkout process
  - [ ] Order history access

- [ ] **Admin Functions**
  - [ ] Dashboard accessible
  - [ ] User management
  - [ ] Product management
  - [ ] System monitoring

- [ ] **Performance Metrics**
  - [ ] Response times < 2 seconds
  - [ ] Error rate < 1%
  - [ ] Cache hit rate > 80%
  - [ ] Core Web Vitals in "Good" range

## 📊 Recovery Metrics and SLAs

### Recovery Time Objectives (RTO)

| Component | Target RTO | Maximum RTO |
|-----------|------------|-------------|
| Homepage | 5 minutes | 15 minutes |
| User Auth | 10 minutes | 30 minutes |
| E-commerce | 15 minutes | 45 minutes |
| Admin Dashboard | 30 minutes | 1 hour |
| Full System | 1 hour | 4 hours |

### Recovery Point Objectives (RPO)

| Data Type | Target RPO | Maximum RPO |
|-----------|------------|-------------|
| User Data | 5 minutes | 15 minutes |
| Product Data | 15 minutes | 1 hour |
| Analytics | 1 hour | 24 hours |
| Logs | 5 minutes | 1 hour |
| Configuration | 1 hour | 4 hours |

### Success Criteria

#### Technical Metrics
- **Availability**: 99.9% uptime restored
- **Performance**: Response times within 10% of baseline
- **Functionality**: All critical features operational
- **Data Integrity**: No data loss or corruption

#### Business Metrics
- **User Impact**: < 5% of users affected
- **Revenue Impact**: < 1% of daily revenue lost
- **Customer Satisfaction**: No significant complaints
- **Reputation**: Minimal negative publicity

## 📞 Emergency Contacts

### Internal Escalation

**Primary On-Call**
- Name: [On-Call Engineer]
- Phone: +1-XXX-XXX-XXXX
- Email: <EMAIL>
- Slack: @oncall

**DevOps Lead**
- Name: [DevOps Lead]
- Phone: +1-XXX-XXX-XXXX
- Email: <EMAIL>
- Slack: @devops-lead

**Engineering Manager**
- Name: [Engineering Manager]
- Phone: +1-XXX-XXX-XXXX
- Email: <EMAIL>
- Slack: @eng-manager

**CTO**
- Name: [CTO]
- Phone: +1-XXX-XXX-XXXX
- Email: <EMAIL>
- Slack: @cto

### External Vendors

**Cloudflare Enterprise Support**
- Portal: https://dash.cloudflare.com/support
- Phone: ******-99-FLARE
- Priority: Enterprise (24/7)

**Firebase/Google Cloud Support**
- Portal: https://console.cloud.google.com/support
- Phone: ******-836-1987
- Priority: Premium Support

**Emergency Services**
- Security Incident: <EMAIL>
- Legal Counsel: <EMAIL>
- PR/Communications: <EMAIL>

## 📝 Post-Incident Procedures

### Immediate Post-Recovery (0-2 hours)
- [ ] Verify all systems fully operational
- [ ] Update status page to "All Systems Operational"
- [ ] Send recovery notification to stakeholders
- [ ] Begin preliminary incident analysis
- [ ] Document timeline and actions taken

### Short-term Follow-up (2-24 hours)
- [ ] Conduct detailed post-mortem meeting
- [ ] Identify root cause and contributing factors
- [ ] Document lessons learned
- [ ] Create action items for prevention
- [ ] Update monitoring and alerting

### Long-term Follow-up (1-7 days)
- [ ] Implement preventive measures
- [ ] Update disaster recovery procedures
- [ ] Conduct team training if needed
- [ ] Review and update SLAs
- [ ] Share learnings with broader team

### Documentation Updates
- [ ] Update this disaster recovery document
- [ ] Revise operational runbooks
- [ ] Update monitoring playbooks
- [ ] Enhance automated recovery scripts
- [ ] Review and update contact information

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-27  
**Next Review**: 2025-04-27  
**Owner**: Syndicaps DevOps Team  
**Approved By**: CTO, Engineering Manager
