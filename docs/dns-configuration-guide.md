# DNS Configuration Guide for Syndicaps Hybrid Deployment

## Overview

This guide covers DNS configuration for the Syndicaps hybrid deployment architecture using Cloudflare as both DNS provider and CDN/edge computing platform.

## DNS Records Configuration

### Primary Domain Records

```dns
# A Records (IPv4)
syndicaps.com.          A       *********    (Cloudflare Proxy: ON)
www.syndicaps.com.      A       *********    (Cloudflare Proxy: ON)

# AAAA Records (IPv6) - Optional but recommended
syndicaps.com.          AAAA    2001:db8::1  (Cloudflare Proxy: ON)
www.syndicaps.com.      AAAA    2001:db8::1  (Cloudflare Proxy: ON)

# CNAME Records for subdomains
api.syndicaps.com.      CNAME   syndicaps.com.
cdn.syndicaps.com.      CNAME   syndicaps.com.
images.syndicaps.com.   CNAME   syndicaps.com.
assets.syndicaps.com.   CNAME   syndicaps.com.
```

### Cloudflare Pages Integration

```dns
# Pages custom domain (if using custom domain)
pages.syndicaps.com.    CNAME   syndicaps.pages.dev.

# R2 Storage custom domain (optional)
r2.syndicaps.com.       CNAME   syndicaps-images.r2.dev.
```

### Email and Security Records

```dns
# MX Records for email
syndicaps.com.          MX      10 mail.syndicaps.com.

# SPF Record
syndicaps.com.          TXT     "v=spf1 include:_spf.google.com ~all"

# DKIM Record (example)
default._domainkey.syndicaps.com. TXT "v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC..."

# DMARC Record
_dmarc.syndicaps.com.   TXT     "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"

# Security.txt
_security.syndicaps.com. TXT    "security_policy=https://syndicaps.com/.well-known/security.txt"
```

### CAA Records for SSL Certificate Authority

```dns
# Certificate Authority Authorization
syndicaps.com.          CAA     0 issue "letsencrypt.org"
syndicaps.com.          CAA     0 issue "digicert.com"
syndicaps.com.          CAA     0 iodef "mailto:<EMAIL>"
```

## SSL/TLS Configuration

### Cloudflare SSL Settings

1. **SSL/TLS Encryption Mode**: Full (Strict)
2. **Edge Certificates**: Universal SSL enabled
3. **Origin Server**: Generate Cloudflare Origin Certificate
4. **TLS Version**: Minimum TLS 1.2
5. **HSTS**: Enabled with max-age=31536000, includeSubDomains, preload

### SSL Certificate Chain

```
Root CA: DigiCert Global Root CA
Intermediate: Cloudflare Inc ECC CA-3
Leaf: *.syndicaps.com
```

## Cloudflare Zone Settings

### Security Settings

```yaml
Security Level: Medium
Challenge Passage: 30 minutes
Browser Integrity Check: On
Privacy Pass: On
Security Header (HSTS): Enabled
  Max Age: 31536000 seconds
  Include Subdomains: Yes
  Preload: Yes
```

### Speed Settings

```yaml
Auto Minify:
  JavaScript: On
  CSS: On
  HTML: On

Brotli: On
Early Hints: On
HTTP/2: On
HTTP/3 (with QUIC): On
0-RTT Connection Resumption: On
```

### Caching Settings

```yaml
Caching Level: Standard
Browser Cache TTL: Respect Existing Headers
Development Mode: Off
Query String Sort: Off
Always Online: On
```

## DNS Validation Script

Create a validation script to verify DNS configuration:

```bash
#!/bin/bash
# DNS Configuration Validation Script

DOMAIN="syndicaps.com"
EXPECTED_IP="*********"  # Replace with actual IP

echo "🔍 Validating DNS Configuration for $DOMAIN"
echo "================================================"

# Check A record
echo "📍 Checking A record..."
A_RECORD=$(dig +short A $DOMAIN)
if [ "$A_RECORD" = "$EXPECTED_IP" ]; then
    echo "✅ A record correct: $A_RECORD"
else
    echo "❌ A record incorrect. Expected: $EXPECTED_IP, Got: $A_RECORD"
fi

# Check CNAME records
echo "📍 Checking CNAME records..."
for subdomain in api cdn images assets; do
    CNAME_RECORD=$(dig +short CNAME $subdomain.$DOMAIN)
    if [ "$CNAME_RECORD" = "$DOMAIN." ]; then
        echo "✅ $subdomain CNAME correct: $CNAME_RECORD"
    else
        echo "❌ $subdomain CNAME incorrect. Expected: $DOMAIN., Got: $CNAME_RECORD"
    fi
done

# Check SSL certificate
echo "📍 Checking SSL certificate..."
SSL_INFO=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -subject -dates)
if [ $? -eq 0 ]; then
    echo "✅ SSL certificate valid"
    echo "$SSL_INFO"
else
    echo "❌ SSL certificate invalid or not accessible"
fi

# Check security headers
echo "📍 Checking security headers..."
HEADERS=$(curl -s -I https://$DOMAIN)
if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
    echo "✅ HSTS header present"
else
    echo "❌ HSTS header missing"
fi

if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
    echo "✅ X-Content-Type-Options header present"
else
    echo "❌ X-Content-Type-Options header missing"
fi

echo "================================================"
echo "🎯 DNS validation complete!"
```

## Troubleshooting

### Common Issues

**1. DNS Propagation Delays**
- DNS changes can take 24-48 hours to propagate globally
- Use `dig` or online DNS checkers to verify propagation
- Clear local DNS cache: `sudo dscacheutil -flushcache` (macOS)

**2. SSL Certificate Issues**
- Ensure DNS is pointing to Cloudflare before enabling proxy
- Check CAA records don't block Cloudflare certificate issuance
- Verify origin server certificate if using Full (Strict) mode

**3. Cloudflare Proxy Issues**
- Orange cloud (proxied) vs Grey cloud (DNS only)
- Some services require grey cloud (e.g., email, FTP)
- API subdomains can be proxied for additional security

**4. CNAME Flattening**
- Cloudflare automatically flattens CNAME records at root domain
- No need for ALIAS records when using Cloudflare

### DNS Testing Commands

```bash
# Check A record
dig A syndicaps.com

# Check CNAME record
dig CNAME api.syndicaps.com

# Check MX record
dig MX syndicaps.com

# Check TXT records
dig TXT syndicaps.com

# Check from specific DNS server
dig @******* A syndicaps.com

# Check DNS propagation
dig +trace syndicaps.com

# Check reverse DNS
dig -x *********
```

## Performance Optimization

### DNS Performance Settings

1. **TTL Values**:
   - A/AAAA records: 300 seconds (5 minutes)
   - CNAME records: 300 seconds
   - MX records: 3600 seconds (1 hour)
   - TXT records: 3600 seconds

2. **Cloudflare Features**:
   - Argo Smart Routing: Enabled
   - Load Balancing: Configure if multiple origins
   - Rate Limiting: Configure for API endpoints

### Monitoring and Alerts

1. **DNS Monitoring**:
   - Set up monitoring for DNS resolution times
   - Monitor SSL certificate expiration
   - Track DNS query response codes

2. **Cloudflare Analytics**:
   - Monitor cache hit ratios
   - Track bandwidth usage
   - Review security events

## Security Considerations

### DNS Security

1. **DNSSEC**: Enable if supported by registrar
2. **DNS Filtering**: Use Cloudflare for Families or *******
3. **DDoS Protection**: Automatic with Cloudflare proxy
4. **Rate Limiting**: Configure for sensitive endpoints

### Certificate Management

1. **Certificate Transparency**: Monitor CT logs
2. **Certificate Pinning**: Consider for mobile apps
3. **OCSP Stapling**: Enabled by default with Cloudflare
4. **Certificate Rotation**: Automatic with Cloudflare

## Deployment Checklist

- [ ] DNS records configured and propagated
- [ ] SSL certificates issued and valid
- [ ] Security headers configured
- [ ] Performance optimizations enabled
- [ ] Monitoring and alerts set up
- [ ] Backup DNS provider configured (optional)
- [ ] Documentation updated
- [ ] Team trained on DNS management

## Emergency Procedures

### DNS Failover

1. **Cloudflare Issues**:
   - Switch to grey cloud (DNS only)
   - Update A records to point directly to origin
   - Communicate with users about potential performance impact

2. **Origin Server Issues**:
   - Enable "Always Online" mode
   - Activate maintenance page
   - Implement load balancer failover

### Contact Information

- **DNS Provider**: Cloudflare Support
- **Domain Registrar**: [Your Registrar]
- **Emergency Contact**: <EMAIL>
- **Technical Lead**: [Technical Lead Contact]
