# Cloudflare Hybrid Deployment Feature Flags Guide

## Overview

The Syndicaps feature flag system enables safe, gradual rollout of Cloudflare hybrid deployment features with comprehensive monitoring, user targeting, and emergency controls.

## Features

### 🎯 **Gradual Rollout**
- Percentage-based user targeting (0-100%)
- Consistent user experience across sessions
- Hash-based user assignment for fairness

### 👥 **User Targeting**
- User type targeting (admin, premium, beta, regular)
- Include/exclude specific users
- Dependency-based feature activation

### 🚨 **Emergency Controls**
- Emergency kill switches for instant rollback
- Automatic rollback on high error rates
- Manual rollback with reason tracking

### 📊 **Monitoring & Analytics**
- Real-time success/error rate tracking
- Health status monitoring (healthy/warning/critical)
- Feature usage analytics

## Available Feature Flags

### Core Cloudflare Features

| Flag | Description | Dependencies |
|------|-------------|--------------|
| `USE_R2_STORAGE` | Cloudflare R2 for image/asset storage | None |
| `USE_CLOUDFLARE_WORKERS` | Edge computing with Workers | None |
| `USE_CLOUDFLARE_IMAGES` | Image optimization service | `USE_R2_STORAGE` |
| `USE_HYBRID_CDN` | Hybrid CDN configuration | None |
| `USE_EDGE_CACHING` | API response caching at edge | `USE_CLOUDFLARE_WORKERS` |

### Advanced Features

| Flag | Description | Dependencies |
|------|-------------|--------------|
| `USE_WORKER_IMAGE_OPTIMIZATION` | Worker-based image optimization | `USE_CLOUDFLARE_WORKERS`, `USE_R2_STORAGE` |
| `USE_WORKER_API_CACHE` | Worker-based API caching | `USE_CLOUDFLARE_WORKERS` |
| `USE_ADVANCED_ANALYTICS` | Enhanced monitoring and analytics | `USE_PERFORMANCE_MONITORING` |
| `USE_EMERGENCY_FALLBACK` | Automatic fallback to Firebase | None (always enabled) |

## Configuration

### Environment Variables

```bash
# Core Features
FEATURE_R2_STORAGE=false
FEATURE_CF_WORKERS=false
FEATURE_CF_IMAGES=false
FEATURE_HYBRID_CDN=false
FEATURE_EDGE_CACHING=false

# Advanced Features
FEATURE_WORKER_IMAGE_OPT=false
FEATURE_WORKER_API_CACHE=false
FEATURE_ADVANCED_ANALYTICS=true

# Rollout Percentages (0-100)
R2_ROLLOUT_PERCENTAGE=0
CF_WORKERS_ROLLOUT_PERCENTAGE=0
CF_IMAGES_ROLLOUT_PERCENTAGE=0
HYBRID_CDN_ROLLOUT_PERCENTAGE=0
EDGE_CACHING_ROLLOUT_PERCENTAGE=0
WORKER_IMAGE_OPT_ROLLOUT_PERCENTAGE=0
WORKER_API_CACHE_ROLLOUT_PERCENTAGE=0
ADVANCED_ANALYTICS_ROLLOUT_PERCENTAGE=100

# Emergency Kill Switches
R2_EMERGENCY_DISABLE=false
CF_WORKERS_EMERGENCY_DISABLE=false
CF_IMAGES_EMERGENCY_DISABLE=false
HYBRID_CDN_EMERGENCY_DISABLE=false
EDGE_CACHING_EMERGENCY_DISABLE=false
WORKER_IMAGE_OPT_EMERGENCY_DISABLE=false
WORKER_API_CACHE_EMERGENCY_DISABLE=false
```

## Usage Examples

### Basic Feature Check

```typescript
import { shouldUseFeature } from '@/lib/config/featureFlags'

// Check if R2 storage should be used for a user
const useR2 = shouldUseFeature('USE_R2_STORAGE', userId, userType)

if (useR2) {
  // Use Cloudflare R2 for storage
  await uploadToR2(file)
} else {
  // Fallback to Firebase
  await uploadToFirebase(file)
}
```

### API Route Protection

```typescript
import { withFeatureFlag } from '@/lib/config/featureFlags'

const handler = async (req, res) => {
  // Your API logic here
  res.json({ success: true })
}

// Protect route with feature flag
export default withFeatureFlag('USE_CLOUDFLARE_WORKERS', handler)
```

### React Component Usage

```typescript
import { useFeatureFlag } from '@/lib/config/featureFlags'

function ImageComponent({ userId, userType }) {
  const useCloudflareImages = useFeatureFlag('USE_CLOUDFLARE_IMAGES', userId, userType)
  
  return (
    <img 
      src={useCloudflareImages ? cloudflareImageUrl : firebaseImageUrl}
      alt="Product image"
    />
  )
}
```

## Admin Dashboard Management

### Accessing Feature Flags

1. Navigate to Admin Dashboard → System → Feature Flags
2. View real-time status of all feature flags
3. Monitor health status and metrics
4. Adjust rollout percentages
5. Trigger emergency rollbacks

### Health Status Indicators

- **🟢 Healthy**: Error rate < 10%, functioning normally
- **🟡 Warning**: Error rate 10-25%, monitor closely
- **🔴 Critical**: Error rate > 25%, consider rollback

### Emergency Procedures

1. **Manual Rollback**: Click "Emergency Rollback" button
2. **Automatic Rollback**: Triggered when error rate > 50%
3. **Kill Switch**: Set `*_EMERGENCY_DISABLE=true` in environment

## Rollout Strategy

### Phase 1: Internal Testing (0-5%)
- Enable for admin and beta users only
- Monitor metrics closely
- Test all functionality

### Phase 2: Limited Rollout (5-25%)
- Expand to premium users
- Monitor performance impact
- Gather user feedback

### Phase 3: Gradual Expansion (25-75%)
- Roll out to regular users gradually
- Monitor system stability
- Adjust based on metrics

### Phase 4: Full Deployment (75-100%)
- Complete rollout to all users
- Maintain monitoring
- Document lessons learned

## Monitoring and Alerts

### Key Metrics
- **Success Rate**: Percentage of successful feature usage
- **Error Rate**: Percentage of failed feature usage
- **Rollout Percentage**: Current user coverage
- **Health Status**: Overall feature health

### Alert Conditions
- Error rate > 25% (Warning)
- Error rate > 50% (Critical - Auto rollback)
- Dependency failures
- Emergency kill switch activation

## Testing

Run the feature flag test suite:

```bash
node scripts/test-feature-flags.js
```

This tests:
- Feature flag evaluation logic
- User targeting consistency
- Dependency resolution
- Emergency rollback functionality
- Metrics tracking

## Best Practices

### 1. **Start Small**
- Begin with 0-5% rollout for new features
- Test with internal users first
- Monitor metrics closely

### 2. **Monitor Continuously**
- Check health status regularly
- Set up alerts for critical issues
- Review metrics weekly

### 3. **Plan Dependencies**
- Understand feature dependencies
- Test dependency chains
- Have fallback strategies

### 4. **Document Changes**
- Record rollout decisions
- Document rollback reasons
- Track performance impact

### 5. **Emergency Preparedness**
- Know how to trigger rollbacks
- Have communication plans
- Test emergency procedures

## Troubleshooting

### Common Issues

**Feature not activating for users:**
- Check rollout percentage
- Verify user targeting settings
- Confirm dependencies are met
- Check for emergency kill switches

**High error rates:**
- Review application logs
- Check Cloudflare service status
- Verify configuration settings
- Consider temporary rollback

**Inconsistent behavior:**
- Verify user ID consistency
- Check for caching issues
- Review feature flag logic
- Test with specific users

### Support

For issues with the feature flag system:
1. Check the admin dashboard for health status
2. Review application logs for errors
3. Test with the feature flag test suite
4. Contact the development team with specific details

## Security Considerations

- Feature flags are server-side evaluated
- User targeting data is not exposed to clients
- Emergency controls require admin access
- All changes are logged for audit trails
