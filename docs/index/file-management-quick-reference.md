# Documentation File Management - Quick Reference
**Last Updated**: 2025-07-21  
**Purpose**: Fast reference for adding new documentation files  
**Full Guide**: [Complete File Management Guide](../active/2025/01-technical/2025-07-21-TECH-guide-documentation-file-management-v1.md)

---

## 🚀 Quick Start Checklist

### ✅ Before You Start
- [ ] Know your document's purpose and audience
- [ ] Have content ready or outlined
- [ ] Understand which category it belongs to

### ✅ File Creation Process
1. [ ] **Select Template** from [templates folder](../standards/document-templates/)
2. [ ] **Apply Naming Convention**: `YYYY-MM-DD-CATEGORY-TYPE-SUBJECT-VERSION.md`
3. [ ] **Place in Correct Folder** using category decision tree
4. [ ] **Fill Template Content** with your information
5. [ ] **Run Compliance Check** using [checklist](../standards/2025-07-21-ARCH-ref-compliance-checklist-v1.md)
6. [ ] **Update Navigation** indexes and cross-references

---

## 🏷️ Naming Convention Cheat Sheet

### Format (MANDATORY)
```
YYYY-MM-DD-CATEGORY-TYPE-SUBJECT-VERSION.md
```

### Quick Examples
- `2025-07-21-TECH-guide-api-integration-v1.md`
- `2025-07-21-ANAL-audit-security-assessment-v1.md`
- `2025-07-21-USER-guide-account-setup-v1.md`
- `2025-07-21-IMPL-report-feature-deployment-v1.md`

### Category Codes
| Code | Category | Folder |
|------|----------|--------|
| **TECH** | Technical | `01-technical/` |
| **ANAL** | Analysis/Audits | `02-analysis-audits/` |
| **IMPL** | Implementation | `03-implementation/` |
| **USER** | User Guides | `05-user-guides/` |
| **ADMIN** | Admin Procedures | `04-admin/` |
| **BIZ** | Business Strategy | `06-business/` |
| **SEC** | Security/Compliance | `07-security/` |
| **API** | API Documentation | `08-api/` |
| **ARCH** | Standards/Archive | `standards/` |

### Type Options
- **analysis** - Gap analysis, evaluations
- **audit** - Comprehensive audits
- **plan** - Implementation plans
- **guide** - How-to documentation
- **spec** - Technical specifications
- **report** - Status reports, summaries
- **ref** - Reference documentation

---

## 🌳 Category Decision Tree

### Quick Decision Questions
1. **Is it for developers/technical?** → TECH
2. **Is it analysis or audit?** → ANAL
3. **Is it project implementation?** → IMPL
4. **Is it for end users?** → USER
5. **Is it admin procedures?** → ADMIN
6. **Is it business strategy?** → BIZ
7. **Is it security/compliance?** → SEC
8. **Is it API documentation?** → API
9. **Is it standards/templates?** → ARCH
10. **Doesn't fit above?** → Use `09-misc/`

### Content Type → Category Mapping
| Content Type | Category | Example |
|--------------|----------|---------|
| Development guides | TECH | Database setup guide |
| System troubleshooting | TECH | Firebase connection issues |
| Gap analysis | ANAL | User experience analysis |
| System audits | ANAL | Security assessment |
| Project reports | IMPL | Feature completion report |
| Implementation plans | IMPL | Rollout strategy |
| User tutorials | USER | Account setup guide |
| Community rules | USER | Points system rules |
| Admin workflows | ADMIN | User management procedures |
| Business planning | BIZ | Market analysis |
| Security protocols | SEC | Data protection policy |
| API specifications | API | Endpoint documentation |

---

## 📋 Template Selection Guide

### Choose Your Template
| Document Purpose | Template | Example Use |
|------------------|----------|-------------|
| **Analysis/Assessment** | [Analysis Template](../standards/document-templates/2025-07-21-ARCH-template-analysis-v1.md) | Gap analysis, system evaluation |
| **Comprehensive Audit** | [Audit Template](../standards/document-templates/2025-07-21-ARCH-template-audit-v1.md) | Security audit, compliance review |
| **Implementation Work** | [Implementation Template](../standards/document-templates/2025-07-21-ARCH-template-implementation-v1.md) | Project reports, deployment docs |
| **User Documentation** | [Guide Template](../standards/document-templates/2025-07-21-ARCH-template-guide-v1.md) | How-to guides, tutorials |

### Template Usage Steps
1. **Copy Template**: `cp template.md new-document.md`
2. **Update Metadata**: Fill in category, type, version, author, date
3. **Replace Placeholders**: Fill in all [bracketed] content
4. **Customize Sections**: Add/remove sections as needed
5. **Maintain Structure**: Keep required sections (Executive Summary, etc.)

---

## ⚡ Common Commands

### File Creation
```bash
# Create new document from template
cp docs/standards/document-templates/[template].md docs/active/2025/[category]/[filename].md

# Quick template copy examples
cp docs/standards/document-templates/2025-07-21-ARCH-template-guide-v1.md docs/active/2025/05-user-guides/2025-07-21-USER-guide-new-feature-v1.md
```

### Validation
```bash
# Check filename format
echo "2025-07-21-TECH-guide-api-setup-v1.md" | grep -E '^[0-9]{4}-[0-9]{2}-[0-9]{2}-(TECH|ANAL|IMPL|USER|ADMIN|BIZ|SEC|API|ARCH)-(analysis|audit|plan|guide|spec|report|ref)-[a-z0-9-]+-v[0-9]+(\.[0-9]+)*\.md$'

# Find documents by category
find docs/active/2025/ -name "*TECH*" -type f
```

### Navigation Updates
```bash
# After creating new document, update these files:
# 1. docs/index/by-category.md - Add to appropriate category
# 2. docs/index/by-priority.md - Add if high priority
# 3. docs/index/recent-updates.md - Add to recent changes
# 4. Related documents - Add cross-references
```

---

## 🚨 Common Mistakes to Avoid

### ❌ Naming Mistakes
- **Wrong date format**: `25-07-21` instead of `2025-07-21`
- **Wrong case**: `TECH-Guide` instead of `TECH-guide`
- **Spaces in subject**: `user account` instead of `user-account`
- **Missing version**: `guide-setup.md` instead of `guide-setup-v1.md`

### ❌ Placement Mistakes
- **Wrong folder**: Technical guide in user-guides folder
- **Missing category**: File in root instead of category folder
- **Wrong category**: Business doc in technical folder

### ❌ Content Mistakes
- **No metadata header**: Missing category, type, version info
- **No executive summary**: Required for all documents
- **No cross-references**: Missing links to related documents
- **Template not used**: Starting from scratch instead of template

---

## 🔧 Troubleshooting

### Problem: "I don't know which category to use"
**Solution**: 
1. Use the decision tree above
2. Ask: "Who is the primary audience?"
3. Check similar existing documents
4. When in doubt, use `09-misc/` and recategorize later

### Problem: "The filename is too long"
**Solution**:
1. Shorten the subject to 3-6 words
2. Use abbreviations: `auth` instead of `authentication`
3. Remove unnecessary words: `the`, `and`, `for`
4. Focus on key concepts

### Problem: "I need a template that doesn't exist"
**Solution**:
1. Use the closest existing template
2. Customize sections as needed
3. Document the customization need
4. Suggest new template in feedback

### Problem: "My document fits multiple categories"
**Solution**:
1. Choose the primary purpose/audience
2. Add cross-references to other relevant categories
3. Update multiple category indexes
4. Consider splitting into multiple focused documents

---

## 📞 Getting Help

### Self-Service Resources
- **[Complete Guide](../active/2025/01-technical/2025-07-21-TECH-guide-documentation-file-management-v1.md)** - Full detailed procedures
- **[Compliance Checklist](../standards/2025-07-21-ARCH-ref-compliance-checklist-v1.md)** - Quality control verification
- **[Template Collection](../standards/2025-07-21-ARCH-ref-document-templates-v1.md)** - Template usage guide
- **[Naming Conventions](../standards/2025-07-21-ARCH-ref-naming-conventions-v1.md)** - Detailed naming rules

### Quick Help
- **Naming Questions**: Check naming convention examples above
- **Category Confusion**: Use decision tree and content mapping
- **Template Issues**: Review template selection guide
- **Quality Problems**: Run through compliance checklist

### Process
1. **Check this quick reference** for immediate answers
2. **Consult full guide** for detailed procedures
3. **Review similar documents** for patterns and examples
4. **Ask team** if still unclear

---

## 📊 Quality Checkpoints

### Before Publishing
- [ ] **Filename**: Follows `YYYY-MM-DD-CATEGORY-TYPE-SUBJECT-VERSION.md`
- [ ] **Location**: Placed in correct category folder
- [ ] **Template**: Used appropriate template
- [ ] **Metadata**: Complete header with all fields
- [ ] **Content**: Executive summary and required sections
- [ ] **Links**: Cross-references to related documents
- [ ] **Quality**: Professional writing and formatting

### After Publishing
- [ ] **Navigation**: Updated category and priority indexes
- [ ] **Recent Updates**: Added to recent changes
- [ ] **Cross-References**: Added links from related documents
- [ ] **Validation**: Verified all links work correctly

---

**Quick Reference Created**: 2025-07-21 | **Last Updated**: 2025-07-21  
**Full Documentation**: [Complete File Management Guide](../active/2025/01-technical/2025-07-21-TECH-guide-documentation-file-management-v1.md)  
**Update Frequency**: Monthly or as needed
