# Profile System - Component API Documentation

## Overview

Comprehensive API documentation for all profile system components, including props, methods, events, and usage examples.

---

## Core Components

### ProfileLayout

**Purpose**: Main layout wrapper for all profile pages with navigation and responsive design.

**Props**:
```typescript
interface ProfileLayoutProps {
  children: React.ReactNode
  variant?: 'full' | 'simple' | 'mobile'
  navigation?: 'smart' | 'simple' | 'bottom'
  showHeader?: boolean
  showWelcome?: boolean
  showSkeleton?: boolean
  className?: string
}
```

**Usage**:
```tsx
import { ProfileLayout } from '@/components/profile'

<ProfileLayout variant="full" navigation="smart">
  <YourProfileContent />
</ProfileLayout>
```

**Features**:
- Responsive navigation switching
- Welcome modal for new users
- Loading skeleton states
- Mobile optimization

---

### EnhancedProfileEditor

**Purpose**: Advanced profile editing component with real-time validation and auto-save.

**Props**:
```typescript
interface EnhancedProfileEditorProps {
  initialData?: Partial<UserProfile>
  onSave?: (data: UserProfile) => Promise<void>
  onCancel?: () => void
  autoSave?: boolean
  validationMode?: 'onChange' | 'onBlur' | 'onSubmit'
}
```

**Usage**:
```tsx
import { EnhancedProfileEditor } from '@/components/profile'

<EnhancedProfileEditor
  initialData={userProfile}
  onSave={handleSave}
  autoSave={true}
  validationMode="onChange"
/>
```

**Features**:
- Real-time validation
- Auto-save functionality
- Photo upload integration
- Progress tracking

---

### ProgressiveDashboard

**Purpose**: Main dashboard component with progressive loading and dynamic content.

**Props**:
```typescript
interface ProgressiveDashboardProps {
  userId: string
  showAchievements?: boolean
  showRecommendations?: boolean
  showActivity?: boolean
  loadingStrategy?: 'eager' | 'lazy' | 'progressive'
}
```

**Usage**:
```tsx
import { ProgressiveDashboard } from '@/components/profile'

<ProgressiveDashboard
  userId={user.id}
  showAchievements={true}
  loadingStrategy="progressive"
/>
```

**Features**:
- Progressive content loading
- Achievement highlights
- Activity timeline
- Recommendation engine integration

---

## Navigation Components

### SmartNavigation

**Purpose**: Intelligent navigation with search, categories, and badges.

**Props**:
```typescript
interface SmartNavigationProps {
  currentPath: string
  showSearch?: boolean
  showCategories?: boolean
  showBadges?: boolean
  onNavigate?: (path: string) => void
}
```

**Usage**:
```tsx
import { SmartNavigation } from '@/components/profile'

<SmartNavigation
  currentPath="/profile/account"
  showSearch={true}
  showBadges={true}
  onNavigate={handleNavigation}
/>
```

---

### ProfileBottomNav

**Purpose**: Mobile-optimized bottom navigation for profile pages.

**Props**:
```typescript
interface ProfileBottomNavProps {
  currentPath: string
  wishlistCount?: number
  showLabels?: boolean
  onNavigate?: (path: string) => void
}
```

**Usage**:
```tsx
import { ProfileBottomNav } from '@/components/profile'

<ProfileBottomNav
  currentPath="/profile/social"
  wishlistCount={5}
  showLabels={true}
/>
```

---

## Social Components

### SocialProfileHeader

**Purpose**: Social profile header with stats, connections, and sharing.

**Props**:
```typescript
interface SocialProfileHeaderProps {
  profile: UserProfile
  isOwnProfile?: boolean
  showStats?: boolean
  showActions?: boolean
  onFollow?: () => void
  onMessage?: () => void
}
```

**Usage**:
```tsx
import { SocialProfileHeader } from '@/components/profile'

<SocialProfileHeader
  profile={userProfile}
  isOwnProfile={false}
  showStats={true}
  onFollow={handleFollow}
/>
```

---

### SocialActivityTimeline

**Purpose**: Activity timeline showing user's social interactions.

**Props**:
```typescript
interface SocialActivityTimelineProps {
  userId: string
  limit?: number
  showFilters?: boolean
  activityTypes?: ActivityType[]
  onLoadMore?: () => void
}
```

**Usage**:
```tsx
import { SocialActivityTimeline } from '@/components/profile'

<SocialActivityTimeline
  userId={user.id}
  limit={20}
  showFilters={true}
  onLoadMore={loadMoreActivities}
/>
```

---

## Utility Components

### ProfileCompletionTracker

**Purpose**: Gamified profile completion tracking with progress indicators.

**Props**:
```typescript
interface ProfileCompletionTrackerProps {
  profile: UserProfile
  showProgress?: boolean
  showRewards?: boolean
  onComplete?: (section: string) => void
}
```

**Usage**:
```tsx
import { ProfileCompletionTracker } from '@/components/profile'

<ProfileCompletionTracker
  profile={userProfile}
  showProgress={true}
  showRewards={true}
  onComplete={handleSectionComplete}
/>
```

---

### ProfilePhotoUpload

**Purpose**: Advanced photo upload with cropping and optimization.

**Props**:
```typescript
interface ProfilePhotoUploadProps {
  currentPhoto?: string
  onUpload: (file: File) => Promise<string>
  onRemove?: () => void
  maxSize?: number
  allowedTypes?: string[]
  showCropper?: boolean
}
```

**Usage**:
```tsx
import { ProfilePhotoUpload } from '@/components/profile'

<ProfilePhotoUpload
  currentPhoto={user.photoURL}
  onUpload={handlePhotoUpload}
  maxSize={5 * 1024 * 1024} // 5MB
  showCropper={true}
/>
```

---

## Dropdown Components

### OptimizedUserProfileDropdown

**Purpose**: Optimized user profile dropdown with lazy loading and caching.

**Props**:
```typescript
interface OptimizedUserProfileDropdownProps {
  user: User
  profile: UserProfile
  showThemeToggle?: boolean
  showNotifications?: boolean
  onSignOut?: () => void
}
```

**Usage**:
```tsx
import { OptimizedUserProfileDropdown } from '@/components/profile'

<OptimizedUserProfileDropdown
  user={user}
  profile={profile}
  showThemeToggle={true}
  onSignOut={handleSignOut}
/>
```

---

## Performance Components

### LazyComponents

**Purpose**: Lazy loading wrappers for heavy profile components.

**Available Components**:
- `EnhancedProfileEditor`
- `RecommendationsDashboard`
- `AdvancedSearchDashboard`
- `OnboardingWizard`
- `MobileOptimizations`

**Usage**:
```tsx
import { 
  EnhancedProfileEditor,
  preloadProfileEditor 
} from '@/components/profile/utils/LazyComponents'

// Preload on hover for better UX
<button onMouseEnter={preloadProfileEditor}>
  Edit Profile
</button>

// Use lazy component
<EnhancedProfileEditor {...props} />
```

**Preloading Hooks**:
```tsx
import { useProfileComponentPreloader } from '@/components/profile/utils/LazyComponents'

const { preloadOnHover, preloadOnFocus } = useProfileComponentPreloader()

<button 
  onMouseEnter={preloadOnHover.profileEditor}
  onFocus={preloadOnFocus.profileEditor}
>
  Edit Profile
</button>
```

---

## Performance Monitoring

### PerformanceMonitor

**Purpose**: Development performance tracking and optimization insights.

**Usage**:
```tsx
import { 
  useProfilePerformanceTracking,
  withPerformanceTracking 
} from '@/components/profile/utils/PerformanceMonitor'

// Hook usage
const MyComponent = () => {
  const { startRender, endRender, trackMount } = useProfilePerformanceTracking('MyComponent')
  
  useEffect(() => {
    trackMount()
  }, [])
  
  // Component logic
}

// HOC usage
const TrackedComponent = withPerformanceTracking(MyComponent, 'MyComponent')
```

**Browser Console Access**:
```javascript
// Access performance data in development
window.profilePerformanceMonitor.getPerformanceSummary()
window.profilePerformanceMonitor.logSummary()
```

---

## Error Boundaries

### ProfileErrorBoundary

**Purpose**: Error boundary for profile components with fallback UI.

**Props**:
```typescript
interface ProfileErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error }>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}
```

**Usage**:
```tsx
import { ProfileErrorBoundary } from '@/components/profile'

<ProfileErrorBoundary onError={logError}>
  <ProfileComponent />
</ProfileErrorBoundary>
```

---

## Best Practices

### Import Patterns

**Recommended**:
```tsx
// Use barrel exports for better tree shaking
import { ProfileLayout, EnhancedProfileEditor } from '@/components/profile'

// Use lazy components for heavy features
import { EnhancedProfileEditor } from '@/components/profile/utils/LazyComponents'
```

**Avoid**:
```tsx
// Direct imports bypass optimization
import ProfileLayout from '@/components/profile/ProfileLayout'
```

### Performance Optimization

1. **Use Lazy Loading**: For heavy components like editors and dashboards
2. **Preload on Interaction**: Use hover/focus preloading for better UX
3. **Monitor Performance**: Use performance tracking in development
4. **Error Boundaries**: Wrap profile sections in error boundaries

### Accessibility

1. **Keyboard Navigation**: All components support keyboard navigation
2. **Screen Readers**: Proper ARIA labels and descriptions
3. **Focus Management**: Logical focus order and visible focus indicators
4. **Color Contrast**: Meets WCAG 2.1 AA standards

---

## Migration Guide

### From Legacy Components

**Old Pattern**:
```tsx
import UserProfileDropdown from '@/components/profile/UserProfileDropdown'
import EditProfileModal from '@/components/profile/EditProfileModal'
```

**New Pattern**:
```tsx
import { OptimizedUserProfileDropdown } from '@/components/profile'
import { EnhancedProfileEditor } from '@/components/profile/utils/LazyComponents'
```

### Performance Upgrades

1. Replace direct imports with barrel exports
2. Use lazy components for heavy features
3. Add performance monitoring in development
4. Implement preloading strategies

---

**Last Updated**: 2025-07-22  
**Version**: Phase 3  
**Maintainer**: Syndicaps Development Team
