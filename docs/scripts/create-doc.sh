#!/bin/bash
# create-doc.sh - Automated Syndicaps Documentation Creation
# Usage: ./create-doc.sh CATEGORY TYPE SUBJECT [TEMPLATE_TYPE]
# Example: ./create-doc.sh TECH guide api-integration
# Example: ./create-doc.sh ANAL audit security-assessment audit

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 CATEGORY TYPE SUBJECT [TEMPLATE_TYPE]"
    echo ""
    echo "CATEGORY options:"
    echo "  TECH  - Technical Documentation"
    echo "  ANAL  - Analysis & Audits"
    echo "  IMPL  - Implementation Reports"
    echo "  USER  - User Guides"
    echo "  ADMIN - Administrative Procedures"
    echo "  BIZ   - Business Strategy"
    echo "  SEC   - Security & Compliance"
    echo "  API   - API Documentation"
    echo "  ARCH  - Archive & Standards"
    echo ""
    echo "TYPE options:"
    echo "  analysis - Gap analysis, evaluations"
    echo "  audit    - Comprehensive audits"
    echo "  plan     - Implementation plans"
    echo "  guide    - How-to documentation"
    echo "  spec     - Technical specifications"
    echo "  report   - Status reports, summaries"
    echo "  ref      - Reference documentation"
    echo ""
    echo "SUBJECT:"
    echo "  - Use kebab-case (lowercase with hyphens)"
    echo "  - 3-6 words maximum"
    echo "  - Descriptive and concise"
    echo "  - Example: api-integration, user-authentication, database-setup"
    echo ""
    echo "TEMPLATE_TYPE (optional):"
    echo "  analysis - Use analysis template"
    echo "  audit    - Use audit template"
    echo "  implementation - Use implementation template"
    echo "  guide    - Use guide template (default)"
    echo ""
    echo "Examples:"
    echo "  $0 TECH guide api-integration"
    echo "  $0 ANAL audit security-assessment audit"
    echo "  $0 USER guide account-setup guide"
    echo "  $0 IMPL report feature-deployment implementation"
}

# Check if help is requested
if [[ "$1" == "-h" || "$1" == "--help" || $# -lt 3 ]]; then
    show_usage
    exit 0
fi

# Parse arguments
CATEGORY=$1
TYPE=$2
SUBJECT=$3
TEMPLATE_TYPE=${4:-$TYPE}  # Default to TYPE if not specified
DATE=$(date +%Y-%m-%d)
VERSION="v1"

print_status "Creating new Syndicaps documentation file..."
print_status "Category: $CATEGORY"
print_status "Type: $TYPE"
print_status "Subject: $SUBJECT"
print_status "Template: $TEMPLATE_TYPE"
print_status "Date: $DATE"

# Validate category
if [[ ! "$CATEGORY" =~ ^(TECH|ANAL|IMPL|USER|ADMIN|BIZ|SEC|API|ARCH)$ ]]; then
    print_error "Invalid category: $CATEGORY"
    print_error "Valid categories: TECH, ANAL, IMPL, USER, ADMIN, BIZ, SEC, API, ARCH"
    exit 1
fi

# Validate type
if [[ ! "$TYPE" =~ ^(analysis|audit|plan|guide|spec|report|ref)$ ]]; then
    print_error "Invalid type: $TYPE"
    print_error "Valid types: analysis, audit, plan, guide, spec, report, ref"
    exit 1
fi

# Validate subject format (kebab-case)
if [[ ! "$SUBJECT" =~ ^[a-z0-9]+(-[a-z0-9]+)*$ ]]; then
    print_error "Invalid subject format: $SUBJECT"
    print_error "Subject must be kebab-case (lowercase with hyphens)"
    print_error "Example: api-integration, user-authentication, database-setup"
    exit 1
fi

# Check subject length (reasonable limit)
WORD_COUNT=$(echo "$SUBJECT" | tr '-' ' ' | wc -w | tr -d ' ')
if [[ $WORD_COUNT -gt 6 ]]; then
    print_warning "Subject has $WORD_COUNT words (recommended: 3-6 words)"
    print_warning "Consider shortening: $SUBJECT"
fi

# Generate filename
FILENAME="${DATE}-${CATEGORY}-${TYPE}-${SUBJECT}-${VERSION}.md"
print_status "Generated filename: $FILENAME"

# Determine target folder based on category
case $CATEGORY in
    TECH)
        FOLDER="docs/active/2025/01-technical/"
        CATEGORY_NAME="Technical Documentation"
        ;;
    ANAL)
        FOLDER="docs/active/2025/02-analysis-audits/"
        CATEGORY_NAME="Analysis & Audits"
        ;;
    IMPL)
        FOLDER="docs/active/2025/03-implementation/"
        CATEGORY_NAME="Implementation Reports"
        ;;
    USER)
        FOLDER="docs/active/2025/05-user-guides/"
        CATEGORY_NAME="User Guides"
        ;;
    ADMIN)
        FOLDER="docs/active/2025/04-admin/"
        CATEGORY_NAME="Administrative Procedures"
        ;;
    BIZ)
        FOLDER="docs/active/2025/06-business/"
        CATEGORY_NAME="Business Strategy"
        ;;
    SEC)
        FOLDER="docs/active/2025/07-security/"
        CATEGORY_NAME="Security & Compliance"
        ;;
    API)
        FOLDER="docs/active/2025/08-api/"
        CATEGORY_NAME="API Documentation"
        ;;
    ARCH)
        FOLDER="docs/standards/"
        CATEGORY_NAME="Archive & Standards"
        ;;
    *)
        FOLDER="docs/active/2025/09-misc/"
        CATEGORY_NAME="Miscellaneous"
        ;;
esac

print_status "Target folder: $FOLDER"
print_status "Category: $CATEGORY_NAME"

# Check if target folder exists
if [[ ! -d "$FOLDER" ]]; then
    print_error "Target folder does not exist: $FOLDER"
    print_error "Please ensure the documentation structure is properly set up"
    exit 1
fi

# Check if file already exists
FULL_PATH="${FOLDER}${FILENAME}"
if [[ -f "$FULL_PATH" ]]; then
    print_error "File already exists: $FULL_PATH"
    print_error "Choose a different subject or increment the version number"
    exit 1
fi

# Determine template file
case $TEMPLATE_TYPE in
    analysis)
        TEMPLATE="docs/standards/document-templates/2025-07-21-ARCH-template-analysis-v1.md"
        ;;
    audit)
        TEMPLATE="docs/standards/document-templates/2025-07-21-ARCH-template-audit-v1.md"
        ;;
    implementation)
        TEMPLATE="docs/standards/document-templates/2025-07-21-ARCH-template-implementation-v1.md"
        ;;
    guide|spec|report|ref|*)
        TEMPLATE="docs/standards/document-templates/2025-07-21-ARCH-template-guide-v1.md"
        ;;
esac

print_status "Using template: $TEMPLATE"

# Check if template exists
if [[ ! -f "$TEMPLATE" ]]; then
    print_error "Template not found: $TEMPLATE"
    print_error "Please ensure the documentation templates are properly set up"
    exit 1
fi

# Copy template to new location
cp "$TEMPLATE" "$FULL_PATH"

# Update the metadata header in the new file
TITLE=$(echo "$SUBJECT" | sed 's/-/ /g' | sed 's/\b\w/\U&/g')
TITLE="Syndicaps $TITLE"

# Use sed to update the template placeholders
sed -i.bak "s/# \[Document Title\]/# $TITLE/g" "$FULL_PATH" 2>/dev/null || \
sed -i "s/# \[Document Title\]/# $TITLE/g" "$FULL_PATH" 2>/dev/null || \
print_warning "Could not automatically update document title"

sed -i.bak "s/\*\*Category\*\*: \[CATEGORY\]/\*\*Category\*\*: $CATEGORY/g" "$FULL_PATH" 2>/dev/null || \
sed -i "s/\*\*Category\*\*: \[CATEGORY\]/\*\*Category\*\*: $CATEGORY/g" "$FULL_PATH" 2>/dev/null || \
print_warning "Could not automatically update category"

sed -i.bak "s/\*\*Type\*\*: \[TYPE\]/\*\*Type\*\*: $TYPE/g" "$FULL_PATH" 2>/dev/null || \
sed -i "s/\*\*Type\*\*: \[TYPE\]/\*\*Type\*\*: $TYPE/g" "$FULL_PATH" 2>/dev/null || \
print_warning "Could not automatically update type"

sed -i.bak "s/\*\*Date\*\*: \[YYYY-MM-DD\]/\*\*Date\*\*: $DATE/g" "$FULL_PATH" 2>/dev/null || \
sed -i "s/\*\*Date\*\*: \[YYYY-MM-DD\]/\*\*Date\*\*: $DATE/g" "$FULL_PATH" 2>/dev/null || \
print_warning "Could not automatically update date"

# Clean up backup files if they exist
rm -f "${FULL_PATH}.bak" 2>/dev/null || true

print_success "Document created successfully!"
print_success "File: $FULL_PATH"
print_success "Template: $TEMPLATE"

echo ""
print_status "Next steps:"
echo "1. Edit the document content: $FULL_PATH"
echo "2. Fill in the Executive Summary and all template sections"
echo "3. Add cross-references to related documents"
echo "4. Run compliance check: docs/scripts/validate-doc.sh $FILENAME"
echo "5. Update navigation indexes:"
echo "   - docs/index/by-category.md"
echo "   - docs/index/by-priority.md (if high priority)"
echo "   - docs/index/recent-updates.md"

echo ""
print_status "Quick edit command:"
echo "code $FULL_PATH"  # VS Code
echo "# or"
echo "vim $FULL_PATH"   # Vim
echo "# or"
echo "nano $FULL_PATH"  # Nano

echo ""
print_status "Documentation guidelines:"
echo "- Full guide: docs/active/2025/01-technical/2025-07-21-TECH-guide-documentation-file-management-v1.md"
echo "- Quick reference: docs/index/file-management-quick-reference.md"
echo "- Compliance checklist: docs/standards/2025-07-21-ARCH-ref-compliance-checklist-v1.md"

exit 0
