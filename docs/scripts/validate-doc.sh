#!/bin/bash
# validate-doc.sh - Syndicaps Documentation Validation
# Usage: ./validate-doc.sh filename.md
# Example: ./validate-doc.sh docs/active/2025/01-technical/2025-07-21-TECH-guide-api-setup-v1.md

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
ERRORS=0
WARNINGS=0
CHECKS=0

# Function to print colored output
print_check() {
    ((CHECKS++))
    echo -e "${BLUE}[CHECK $CHECKS]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    ((WARNINGS++))
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    ((ERRORS++))
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 filename.md"
    echo ""
    echo "Validates Syndicaps documentation files for:"
    echo "- Filename format compliance"
    echo "- File location correctness"
    echo "- Template structure adherence"
    echo "- Content quality requirements"
    echo "- Cross-reference integrity"
    echo ""
    echo "Examples:"
    echo "  $0 docs/active/2025/01-technical/2025-07-21-TECH-guide-api-setup-v1.md"
    echo "  $0 2025-07-21-USER-guide-account-setup-v1.md"
}

# Check if help is requested or no arguments
if [[ "$1" == "-h" || "$1" == "--help" || $# -eq 0 ]]; then
    show_usage
    exit 0
fi

FILE=$1

print_info "Validating Syndicaps documentation file: $FILE"
echo "=================================================="

# Check if file exists
print_check "File existence"
if [[ ! -f "$FILE" ]]; then
    print_error "File does not exist: $FILE"
    exit 1
fi
print_pass "File exists"

# Extract filename from path
FILENAME=$(basename "$FILE")
FILEPATH=$(dirname "$FILE")

print_info "Filename: $FILENAME"
print_info "Path: $FILEPATH"

# 1. Validate filename format
print_check "Filename format validation"
FILENAME_REGEX='^[0-9]{4}-[0-9]{2}-[0-9]{2}-(TECH|ANAL|IMPL|USER|ADMIN|BIZ|SEC|API|ARCH)-(analysis|audit|plan|guide|spec|report|ref)-[a-z0-9]+(-[a-z0-9]+)*-v[0-9]+(\.[0-9]+)*\.md$'

if [[ ! "$FILENAME" =~ $FILENAME_REGEX ]]; then
    print_error "Filename does not follow naming convention"
    print_error "Expected format: YYYY-MM-DD-CATEGORY-TYPE-SUBJECT-VERSION.md"
    print_error "Your filename: $FILENAME"
    print_error "Requirements:"
    print_error "  - Date: YYYY-MM-DD format"
    print_error "  - Category: TECH|ANAL|IMPL|USER|ADMIN|BIZ|SEC|API|ARCH"
    print_error "  - Type: analysis|audit|plan|guide|spec|report|ref"
    print_error "  - Subject: kebab-case (lowercase with hyphens)"
    print_error "  - Version: v1, v1.1, v2.0, etc."
else
    print_pass "Filename follows naming convention"
fi

# Extract components from filename
if [[ "$FILENAME" =~ ^([0-9]{4}-[0-9]{2}-[0-9]{2})-(TECH|ANAL|IMPL|USER|ADMIN|BIZ|SEC|API|ARCH)-(analysis|audit|plan|guide|spec|report|ref)-([a-z0-9-]+)-(v[0-9]+(\.[0-9]+)*)\.md$ ]]; then
    FILE_DATE="${BASH_REMATCH[1]}"
    FILE_CATEGORY="${BASH_REMATCH[2]}"
    FILE_TYPE="${BASH_REMATCH[3]}"
    FILE_SUBJECT="${BASH_REMATCH[4]}"
    FILE_VERSION="${BASH_REMATCH[5]}"
    
    print_info "Parsed components:"
    print_info "  Date: $FILE_DATE"
    print_info "  Category: $FILE_CATEGORY"
    print_info "  Type: $FILE_TYPE"
    print_info "  Subject: $FILE_SUBJECT"
    print_info "  Version: $FILE_VERSION"
fi

# 2. Validate file location
print_check "File location validation"
EXPECTED_FOLDER=""
case $FILE_CATEGORY in
    TECH) EXPECTED_FOLDER="docs/active/2025/01-technical" ;;
    ANAL) EXPECTED_FOLDER="docs/active/2025/02-analysis-audits" ;;
    IMPL) EXPECTED_FOLDER="docs/active/2025/03-implementation" ;;
    USER) EXPECTED_FOLDER="docs/active/2025/05-user-guides" ;;
    ADMIN) EXPECTED_FOLDER="docs/active/2025/04-admin" ;;
    BIZ) EXPECTED_FOLDER="docs/active/2025/06-business" ;;
    SEC) EXPECTED_FOLDER="docs/active/2025/07-security" ;;
    API) EXPECTED_FOLDER="docs/active/2025/08-api" ;;
    ARCH) EXPECTED_FOLDER="docs/standards" ;;
    *) EXPECTED_FOLDER="docs/active/2025/09-misc" ;;
esac

# Normalize paths for comparison
NORMALIZED_FILEPATH=$(echo "$FILEPATH" | sed 's|^\./||' | sed 's|/$||')
NORMALIZED_EXPECTED=$(echo "$EXPECTED_FOLDER" | sed 's|^\./||' | sed 's|/$||')

if [[ "$NORMALIZED_FILEPATH" != "$NORMALIZED_EXPECTED" ]]; then
    print_error "File is in wrong location"
    print_error "Current location: $FILEPATH"
    print_error "Expected location: $EXPECTED_FOLDER"
    print_error "Category $FILE_CATEGORY should be in $EXPECTED_FOLDER/"
else
    print_pass "File is in correct location"
fi

# 3. Validate date format
print_check "Date format validation"
if [[ "$FILE_DATE" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
    # Check if date is valid (basic check)
    YEAR=$(echo "$FILE_DATE" | cut -d'-' -f1)
    MONTH=$(echo "$FILE_DATE" | cut -d'-' -f2)
    DAY=$(echo "$FILE_DATE" | cut -d'-' -f3)
    
    if [[ $YEAR -lt 2020 || $YEAR -gt 2030 ]]; then
        print_warning "Date year seems unusual: $YEAR"
    fi
    
    if [[ $MONTH -lt 1 || $MONTH -gt 12 ]]; then
        print_error "Invalid month: $MONTH"
    fi
    
    if [[ $DAY -lt 1 || $DAY -gt 31 ]]; then
        print_error "Invalid day: $DAY"
    fi
    
    if [[ $ERRORS -eq 0 ]]; then
        print_pass "Date format is valid"
    fi
else
    print_error "Date format is invalid: $FILE_DATE"
fi

# 4. Validate subject format
print_check "Subject format validation"
if [[ "$FILE_SUBJECT" =~ ^[a-z0-9]+(-[a-z0-9]+)*$ ]]; then
    print_pass "Subject follows kebab-case format"
    
    # Check subject length
    WORD_COUNT=$(echo "$FILE_SUBJECT" | tr '-' ' ' | wc -w | tr -d ' ')
    if [[ $WORD_COUNT -gt 6 ]]; then
        print_warning "Subject has $WORD_COUNT words (recommended: 3-6 words)"
        print_warning "Consider shortening: $FILE_SUBJECT"
    fi
else
    print_error "Subject does not follow kebab-case format: $FILE_SUBJECT"
    print_error "Subject must be lowercase letters, numbers, and hyphens only"
fi

# 5. Validate file content structure
print_check "Content structure validation"

# Check for required metadata header
if grep -q "^\*\*Category\*\*:" "$FILE" && grep -q "^\*\*Type\*\*:" "$FILE" && grep -q "^\*\*Version\*\*:" "$FILE"; then
    print_pass "Metadata header present"
    
    # Validate metadata values
    CONTENT_CATEGORY=$(grep "^\*\*Category\*\*:" "$FILE" | sed 's/.*: //' | tr -d ' ')
    CONTENT_TYPE=$(grep "^\*\*Type\*\*:" "$FILE" | sed 's/.*: //' | tr -d ' ')
    
    if [[ "$CONTENT_CATEGORY" == "$FILE_CATEGORY" ]]; then
        print_pass "Category in metadata matches filename"
    else
        print_error "Category mismatch - Filename: $FILE_CATEGORY, Content: $CONTENT_CATEGORY"
    fi
    
    if [[ "$CONTENT_TYPE" == "$FILE_TYPE" ]]; then
        print_pass "Type in metadata matches filename"
    else
        print_error "Type mismatch - Filename: $FILE_TYPE, Content: $CONTENT_TYPE"
    fi
else
    print_error "Missing required metadata header"
    print_error "Required format:"
    print_error "**Category**: CATEGORY | **Type**: TYPE | **Version**: VERSION"
fi

# Check for Executive Summary
if grep -q "## Executive Summary" "$FILE"; then
    print_pass "Executive Summary section present"
else
    print_error "Missing Executive Summary section (required for all documents)"
fi

# Check for proper markdown structure
if grep -q "^# " "$FILE"; then
    print_pass "Document has main title (H1)"
else
    print_warning "Document should have a main title (# Title)"
fi

# 6. Check for cross-references
print_check "Cross-references validation"
if grep -q "Related Documents" "$FILE" || grep -q "\[.*\](.*\.md)" "$FILE"; then
    print_pass "Document contains cross-references"
else
    print_warning "Document should include cross-references to related documents"
fi

# 7. Check for review schedule
print_check "Review schedule validation"
if grep -q "Next Review" "$FILE"; then
    print_pass "Review schedule present"
else
    print_warning "Document should include review schedule (Next Review: YYYY-MM-DD)"
fi

# 8. Validate markdown syntax (basic check)
print_check "Markdown syntax validation"
# Check for common markdown issues
if grep -q "^\s*#\s*$" "$FILE"; then
    print_warning "Found empty headers"
fi

if grep -q "\[.*\](" "$FILE" && ! grep -q "\[.*\](.*)" "$FILE"; then
    print_warning "Found incomplete markdown links"
fi

# 9. Check file size
print_check "File size validation"
FILE_SIZE=$(wc -c < "$FILE")
if [[ $FILE_SIZE -lt 100 ]]; then
    print_warning "File is very small ($FILE_SIZE bytes) - ensure content is complete"
elif [[ $FILE_SIZE -gt 100000 ]]; then
    print_warning "File is very large ($FILE_SIZE bytes) - consider splitting into multiple documents"
else
    print_pass "File size is reasonable ($FILE_SIZE bytes)"
fi

# 10. Template compliance check
print_check "Template compliance validation"
EXPECTED_TEMPLATE=""
case $FILE_TYPE in
    analysis) EXPECTED_TEMPLATE="analysis" ;;
    audit) EXPECTED_TEMPLATE="audit" ;;
    plan|report) EXPECTED_TEMPLATE="implementation" ;;
    guide|spec|ref) EXPECTED_TEMPLATE="guide" ;;
esac

# Check if document follows expected template structure
case $EXPECTED_TEMPLATE in
    analysis)
        if grep -q "## Current State Assessment" "$FILE" && grep -q "## Technical Gap Analysis" "$FILE"; then
            print_pass "Document follows analysis template structure"
        else
            print_warning "Document should follow analysis template structure"
        fi
        ;;
    audit)
        if grep -q "## Audit Methodology" "$FILE" && grep -q "## Findings" "$FILE"; then
            print_pass "Document follows audit template structure"
        else
            print_warning "Document should follow audit template structure"
        fi
        ;;
    implementation)
        if grep -q "## Implementation Details" "$FILE" || grep -q "## Testing" "$FILE"; then
            print_pass "Document follows implementation template structure"
        else
            print_warning "Document should follow implementation template structure"
        fi
        ;;
    guide)
        if grep -q "## Prerequisites" "$FILE" || grep -q "## Instructions" "$FILE" || grep -q "## Examples" "$FILE"; then
            print_pass "Document follows guide template structure"
        else
            print_warning "Document should follow guide template structure"
        fi
        ;;
esac

# Summary
echo ""
echo "=================================================="
print_info "Validation Summary"
echo "=================================================="
print_info "Total checks performed: $CHECKS"

if [[ $ERRORS -eq 0 && $WARNINGS -eq 0 ]]; then
    print_pass "✅ Document passes all validation checks!"
    print_pass "Document is ready for publication"
elif [[ $ERRORS -eq 0 ]]; then
    print_pass "✅ Document passes validation with $WARNINGS warnings"
    print_pass "Document is acceptable for publication"
    echo ""
    print_info "Consider addressing warnings to improve quality"
else
    print_error "❌ Document has $ERRORS errors and $WARNINGS warnings"
    print_error "Please fix errors before publication"
    echo ""
    print_info "Next steps:"
    print_info "1. Fix all errors listed above"
    print_info "2. Address warnings to improve quality"
    print_info "3. Re-run validation: $0 $FILE"
    print_info "4. Use compliance checklist: docs/standards/2025-07-21-ARCH-ref-compliance-checklist-v1.md"
fi

echo ""
print_info "Documentation resources:"
print_info "- File management guide: docs/active/2025/01-technical/2025-07-21-TECH-guide-documentation-file-management-v1.md"
print_info "- Quick reference: docs/index/file-management-quick-reference.md"
print_info "- Compliance checklist: docs/standards/2025-07-21-ARCH-ref-compliance-checklist-v1.md"
print_info "- Templates: docs/standards/document-templates/"

# Exit with appropriate code
if [[ $ERRORS -gt 0 ]]; then
    exit 1
else
    exit 0
fi
