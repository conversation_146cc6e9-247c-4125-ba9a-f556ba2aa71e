# Syndicaps Documentation Naming Conventions
**Category**: ARCH | **Type**: ref | **Version**: v1  
**Author**: Syndicaps Team | **Date**: 2025-07-21 | **Status**: APPROVED

---

## Executive Summary

This document establishes standardized naming conventions for all Syndicaps documentation to ensure consistency, discoverability, and maintainability across the entire documentation ecosystem. The conventions support historical accuracy while enabling clear identification of new work.

### Key Principles
- **Consistency**: All documents follow the same naming pattern
- **Chronological Order**: ISO 8601 date format ensures proper sorting
- **Clear Categorization**: Category codes enable quick identification
- **Historical Integrity**: Preserve original dates for existing documents
- **Version Control**: Semantic versioning for document updates

---

## Standard Naming Format

### Primary Format
```
YYYY-MM-DD-[CATEGORY]-[TYPE]-[SUBJECT]-[VERSION].md
```

### Components Breakdown

#### Date (YYYY-MM-DD)
- **Format**: ISO 8601 standard (2025-07-21)
- **Historical Documents**: Preserve original creation date
- **New Documents**: Use actual creation date
- **Reorganization Documents**: Use 2025-07-21 (reorganization start date)

#### Category Codes
| Code | Category | Description |
|------|----------|-------------|
| `TECH` | Technical Documentation | Architecture, system design, development guides |
| `ANAL` | Analysis & Audits | Gap analysis, performance audits, assessments |
| `IMPL` | Implementation Reports | Phase summaries, completion reports, roadmaps |
| `ADMIN` | Admin Documentation | Dashboard guides, user management, workflows |
| `USER` | User Guides | Community rules, gamification, user procedures |
| `BIZ` | Business Strategy | PRD, market analysis, monetization plans |
| `SEC` | Security & Compliance | Security guidelines, vulnerability assessments |
| `API` | API Documentation | Endpoints, integration specs, deployment guides |
| `ARCH` | Archived & Legacy | Historical documentation, standards, templates |

#### Document Types
| Type | Purpose | Required Sections |
|------|---------|-------------------|
| `audit` | Comprehensive system audits | Executive Summary, Current State, Findings, Recommendations |
| `analysis` | Gap analysis, technical analysis | Executive Summary, Gap Analysis, Priority Matrix, Roadmap |
| `plan` | Implementation plans, roadmaps | Executive Summary, Implementation Steps, Timeline, Dependencies |
| `guide` | User guides, how-to documents | Overview, Step-by-step Instructions, Examples, Troubleshooting |
| `spec` | Technical specifications | Overview, Technical Details, Requirements, Examples |
| `report` | Status reports, summaries | Executive Summary, Progress, Achievements, Next Steps |
| `ref` | Reference documentation | Overview, Standards, Guidelines, Examples |

#### Subject
- **Format**: Lowercase with hyphens (kebab-case)
- **Length**: Maximum 50 characters
- **Descriptive**: Clear indication of document content
- **Specific**: Avoid generic terms like "system" or "general"

#### Version
- **Format**: `v[MAJOR].[MINOR]` (e.g., v1.0, v2.1)
- **Major**: Significant content changes, restructuring
- **Minor**: Updates, corrections, additions
- **Initial**: Always start with v1

---

## Date Usage Guidelines

### When to Preserve Historical Dates
✅ **Preserve Original Date When:**
- Moving existing documents to new structure
- Archiving completed phase documentation
- Maintaining audit trails for historical work
- Documents represent actual completion dates

### When to Use Current Date (2025-07-21)
🆕 **Use Current Date When:**
- Creating new templates during reorganization
- Creating new standards documentation
- Creating implementation tracking documents
- Creating any new documentation as part of reorganization effort

### Examples

#### Historical Documents (Preserve Dates)
```
Original: 25-01-18-community-admin-analysis.md
New: 2025-01-18-ANAL-analysis-community-admin-v1.md
Rationale: Preserve original analysis completion date
```

#### New Documents (Use 2025-07-21)
```
New: 2025-07-21-ARCH-ref-naming-conventions-v1.md
Purpose: Standards documentation created during reorganization
```

---

## Naming Examples

### Analysis Documents
```
2025-01-18-ANAL-analysis-community-admin-v1.md
2025-01-20-ANAL-audit-codebase-optimization-v1.md
2025-07-21-ANAL-analysis-documentation-gaps-v1.md
```

### Implementation Documents
```
2025-01-19-IMPL-report-phase-1-summary-final.md
2025-01-20-IMPL-plan-header-cleanup-v1.md
2025-07-21-IMPL-plan-documentation-reorganization-v1.md
```

### Technical Documentation
```
2025-01-15-TECH-guide-firebase-troubleshooting-v1.md
2025-07-21-TECH-spec-database-optimization-v1.md
2025-07-21-TECH-guide-development-setup-v2.md
```

### User Guides
```
2025-07-13-USER-spec-achievements-50plus-v1.md
2025-07-14-USER-guide-gamification-system-v1.md
2025-07-21-USER-guide-documentation-navigation-v1.md
```

---

## File Extension Standards

### Markdown (.md)
- **Primary Format**: All documentation uses Markdown
- **Consistency**: Enables uniform formatting and rendering
- **Compatibility**: Works across all platforms and tools

### Legacy Formats
- **Convert .txt to .md**: All text files should be converted
- **Maintain Content**: Preserve all original content during conversion
- **Update Formatting**: Apply proper Markdown formatting

---

## Special Cases and Edge Cases

### Multi-Part Documents
```
2025-07-21-ANAL-analysis-community-system-part1-v1.md
2025-07-21-ANAL-analysis-community-system-part2-v1.md
```

### Collaborative Documents
```
2025-07-21-BIZ-plan-profit-sharing-stakeholder-v1.md
```

### Temporary/Draft Documents
```
2025-07-21-IMPL-report-progress-draft-v1.md
```

### Updated Versions
```
Original: 2025-01-15-TECH-guide-firebase-troubleshooting-v1.md
Updated: 2025-01-15-TECH-guide-firebase-troubleshooting-v2.md
```

---

## Quality Control

### Validation Checklist
- [ ] Date format is YYYY-MM-DD
- [ ] Category code is valid and appropriate
- [ ] Document type matches content
- [ ] Subject is descriptive and specific
- [ ] Version follows semantic versioning
- [ ] File extension is .md
- [ ] Total filename length < 100 characters

### Common Mistakes to Avoid
❌ **Incorrect**: `25-01-18-analysis.md` (wrong date format)
❌ **Incorrect**: `2025-01-18-analysis-community.md` (missing category/type)
❌ **Incorrect**: `2025-01-18-ANAL-community.md` (missing type)
✅ **Correct**: `2025-01-18-ANAL-analysis-community-admin-v1.md`

---

## Implementation Guidelines

### Migration Process
1. **Identify Original Date**: Research actual creation/completion date
2. **Determine Category**: Classify document content appropriately
3. **Select Type**: Choose most appropriate document type
4. **Create Subject**: Develop clear, descriptive subject
5. **Apply Version**: Start with v1 for migrated documents
6. **Document Decision**: Record in migration log

### New Document Creation
1. **Use Current Date**: 2025-07-21 for reorganization documents
2. **Select Appropriate Category**: Match content to category
3. **Choose Document Type**: Select based on purpose
4. **Create Clear Subject**: Descriptive and specific
5. **Start with v1**: Initial version for new documents

---

## Maintenance and Updates

### Regular Reviews
- **Monthly**: Review naming consistency across new documents
- **Quarterly**: Audit compliance with naming conventions
- **Annually**: Update conventions based on lessons learned

### Version Updates
- **Minor Updates**: Corrections, additions, clarifications
- **Major Updates**: Significant changes, restructuring
- **Documentation**: Record all changes in version history

---

**Related Documents**: 
- [Quality Guidelines](./2025-07-21-ARCH-ref-quality-guidelines-v1.md)
- [Document Templates](./document-templates/)
- [Migration Decision Log](../active/2025/03-implementation/2025-07-21-IMPL-log-migration-decisions-v1.md)

**Next Review**: 2025-08-21 | **Update Frequency**: Quarterly
