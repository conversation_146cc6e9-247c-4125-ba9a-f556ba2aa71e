# Worker Routing Configuration

## Overview

This document outlines the routing configuration for Syndicaps Cloudflare Workers, including domain mapping, traffic distribution, and routing rules.

## Current Deployment URLs

### Staging Environment
- **Image Optimizer**: `https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev`
- **API Cache**: `https://syndicaps-api-cache-staging.syndicaps22.workers.dev`

### Production Environment (To be configured)
- **Image Optimizer**: `https://syndicaps-image-optimizer.syndicaps22.workers.dev`
- **API Cache**: `https://syndicaps-api-cache.syndicaps22.workers.dev`

## Desired Custom Domain Configuration

### Production Domains (Requires Paid Plan)
- **Image Optimizer**: `https://images.syndicaps.com`
- **API Cache**: `https://api-cache.syndicaps.com`

### Staging Domains (Requires Paid Plan)
- **Image Optimizer**: `https://images-staging.syndicaps.com`
- **API Cache**: `https://api-cache-staging.syndicaps.com`

## Routing Rules

### Image Optimizer Routes
```
# Production
images.syndicaps.com/*
  - Route: /optimize/* → Image optimization with parameters
  - Route: /health → Health check endpoint
  - Route: /admin/* → Admin endpoints (authenticated)
  - Route: /* → Default image serving

# Staging
images-staging.syndicaps.com/*
  - Same routes as production for testing
```

### API Cache Routes
```
# Production
api-cache.syndicaps.com/*
  - Route: /api/* → Cached API endpoints
  - Route: /health → Health check endpoint
  - Route: /admin/* → Cache management endpoints
  - Route: /stats → Analytics endpoints

# Staging
api-cache-staging.syndicaps.com/*
  - Same routes as production for testing
```

## Traffic Distribution Strategy

### Load Balancing
- **Geographic Distribution**: Automatic via Cloudflare's global network
- **Failover**: Automatic failover to origin on worker failure
- **Rate Limiting**: Implemented within workers

### Caching Strategy
- **Edge Caching**: 30 seconds to 5 minutes for dynamic content
- **Browser Caching**: 1 hour to 24 hours for static content
- **KV Caching**: 5 minutes to 24 hours for API responses

## Configuration Files

### Image Optimizer (wrangler.toml)
```toml
# Production environment
[env.production]
name = "syndicaps-image-optimizer"
route = { pattern = "images.syndicaps.com/*", zone_name = "syndicaps.com" }
vars = { ENVIRONMENT = "production" }
```

### API Cache (wrangler-api-cache.toml)
```toml
# Production environment
[env.production]
name = "syndicaps-api-cache"
route = { pattern = "api-cache.syndicaps.com/*", zone_name = "syndicaps.com" }
[env.production.vars]
ENVIRONMENT = "production"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps.cloudfunctions.net"
```

## DNS Configuration

### Required DNS Records (When Custom Domains Are Available)
```
# A Records (or CNAME to workers.dev)
images.syndicaps.com → Cloudflare Worker
api-cache.syndicaps.com → Cloudflare Worker
images-staging.syndicaps.com → Cloudflare Worker
api-cache-staging.syndicaps.com → Cloudflare Worker

# SSL Certificates
- Automatic via Cloudflare Universal SSL
- Custom certificates can be uploaded if needed
```

## Current Implementation

### Free Plan Limitations
- Custom domains require a paid Cloudflare plan
- Currently using `*.workers.dev` subdomains
- Route configuration disabled for free plan compatibility

### Workaround Solutions
1. **Subdomain Mapping**: Use `*.workers.dev` URLs with clear naming
2. **Reverse Proxy**: Configure main application to proxy requests
3. **CNAME Records**: Point subdomains to `*.workers.dev` URLs

## Implementation Steps

### Phase 1: Free Plan Setup (Current)
1. ✅ Deploy workers to `*.workers.dev` subdomains
2. ✅ Configure environment-specific naming
3. ✅ Test functionality on worker URLs
4. ⏳ Update application to use worker URLs

### Phase 2: Custom Domain Setup (Future)
1. Upgrade to Cloudflare paid plan
2. Add custom domains to Cloudflare
3. Configure DNS records
4. Update wrangler.toml with custom routes
5. Deploy with custom domain routing
6. Update application configuration

## Testing and Validation

### Health Check Endpoints
```bash
# Staging
curl https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev/health
curl https://syndicaps-api-cache-staging.syndicaps22.workers.dev/health

# Production (when deployed)
curl https://syndicaps-image-optimizer.syndicaps22.workers.dev/health
curl https://syndicaps-api-cache.syndicaps22.workers.dev/health
```

### Functional Testing
```bash
# Image Optimizer
curl "https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev/test.jpg?w=100&h=100"

# API Cache
curl "https://syndicaps-api-cache-staging.syndicaps22.workers.dev/api/test"
```

## Monitoring and Analytics

### Performance Metrics
- Response time monitoring
- Cache hit/miss ratios
- Error rate tracking
- Geographic distribution analysis

### Alerting
- Health check failures
- Performance degradation
- Error rate spikes
- Resource usage alerts

## Security Considerations

### Access Control
- Admin endpoints require authentication
- Rate limiting per IP and user
- CORS configuration for cross-origin requests

### SSL/TLS
- Automatic HTTPS via Cloudflare
- TLS 1.3 support
- HSTS headers enabled

## Maintenance and Updates

### Deployment Process
1. Deploy to staging environment
2. Run comprehensive tests
3. Validate performance metrics
4. Deploy to production
5. Monitor for issues

### Rollback Procedures
- Automatic rollback on health check failure
- Manual rollback commands available
- Version tracking for easy reversion

## Integration with Main Application

### Frontend Integration
```javascript
// Configuration for worker URLs
const WORKER_CONFIG = {
  imageOptimizer: {
    staging: 'https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev',
    production: 'https://syndicaps-image-optimizer.syndicaps22.workers.dev'
  },
  apiCache: {
    staging: 'https://syndicaps-api-cache-staging.syndicaps22.workers.dev',
    production: 'https://syndicaps-api-cache.syndicaps22.workers.dev'
  }
}
```

### Backend Integration
```javascript
// Proxy configuration for API caching
const API_CACHE_URL = process.env.NODE_ENV === 'production'
  ? 'https://syndicaps-api-cache.syndicaps22.workers.dev'
  : 'https://syndicaps-api-cache-staging.syndicaps22.workers.dev'
```

## Future Enhancements

### Advanced Routing
- A/B testing with traffic splitting
- Canary deployments
- Geographic routing rules

### Performance Optimization
- Edge-side includes (ESI)
- Dynamic content optimization
- Advanced caching strategies

### Monitoring Improvements
- Real-time dashboards
- Custom metrics collection
- Integration with external monitoring tools

## Support and Troubleshooting

### Common Issues
1. **Worker not responding**: Check deployment status and logs
2. **Routing errors**: Verify DNS and route configuration
3. **Performance issues**: Monitor resource usage and optimize

### Debug Commands
```bash
# View worker logs
wrangler tail syndicaps-image-optimizer-staging
wrangler tail syndicaps-api-cache-staging

# Check deployment status
wrangler deployments list --env staging
wrangler deployments list --env production
```

## Contact Information
- **Development Team**: <EMAIL>
- **Operations Team**: <EMAIL>
- **Emergency Contact**: <EMAIL>
