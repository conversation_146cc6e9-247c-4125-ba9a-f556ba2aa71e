# Workers Deployment Guide

## Overview

This document provides comprehensive guidance for deploying Syndicaps Cloudflare Workers to staging and production environments.

## Architecture

### Workers Overview
- **Image Optimizer Worker**: Handles dynamic image optimization, resizing, and format conversion
- **API Cache Worker**: Provides intelligent API caching with rate limiting and analytics

### Deployment Environments
- **Staging**: `*-staging.syndicaps.com` - Testing and validation environment
- **Production**: `*.syndicaps.com` - Live production environment

## Prerequisites

### Required Tools
```bash
# Install required dependencies
npm install -g wrangler@latest
npm install -g tsx

# Verify installations
wrangler --version
node --version
npm --version
```

### Environment Variables
```bash
# Required for all deployments
export CLOUDFLARE_API_TOKEN="your-api-token"
export CLOUDFLARE_ACCOUNT_ID="your-account-id"

# Optional for production notifications
export DEPLOYMENT_WEBHOOK_URL="your-webhook-url"
```

### Authentication Setup
```bash
# Login to Cloudflare
wrangler auth login

# Verify authentication
wrangler whoami
```

## Deployment Methods

### 1. Automated Deployment (Recommended)

#### Using Make Commands
```bash
# Full staging deployment with validation
make full-deploy-staging

# Full production deployment with validation
make full-deploy-production

# Quick deployments (skip some checks)
make quick-deploy-staging
make quick-deploy-production
```

#### Using NPM Scripts
```bash
# Complete CI/CD pipeline
npm run cd:staging
npm run cd:production

# Individual worker deployments
npm run deploy:staging:image-optimizer
npm run deploy:staging:api-cache
npm run deploy:production:image-optimizer
npm run deploy:production:api-cache
```

#### Using Deployment Script
```bash
# Advanced deployment with comprehensive validation
npm run deploy:script:staging
npm run deploy:script:production

# Direct script execution
npx tsx scripts/deploy.ts staging
npx tsx scripts/deploy.ts production
```

### 2. Manual Deployment

#### Step-by-Step Process
```bash
# 1. Navigate to workers directory
cd workers

# 2. Install dependencies
npm ci

# 3. Run validation
npm run validate

# 4. Build workers
npm run build

# 5. Deploy to staging
wrangler deploy image-optimizer.ts --name syndicaps-image-optimizer-staging --env staging
wrangler deploy api-cache.ts --name syndicaps-api-cache-staging --env staging

# 6. Health check
npm run health-check:staging

# 7. Deploy to production (after staging validation)
wrangler deploy image-optimizer.ts --name syndicaps-image-optimizer --env production
wrangler deploy api-cache.ts --name syndicaps-api-cache --env production

# 8. Production health check
npm run health-check:production
```

### 3. GitHub Actions (CI/CD)

#### Automatic Triggers
- **Staging**: Push to `develop` branch
- **Production**: Push to `main` branch
- **Manual**: Workflow dispatch with environment selection

#### Workflow Features
- Quality checks (linting, type checking, testing)
- Security scanning
- Performance testing
- Automated deployment
- Health checks
- Rollback capabilities
- Notifications

## Monitoring and Validation

### Health Checks
```bash
# Check all workers
npm run health-check:production
npm run health-check:staging

# Individual worker health checks
curl https://images.syndicaps.com/health
curl https://api-cache.syndicaps.com/health
```

### Performance Monitoring
```bash
# View real-time logs
npm run logs
npm run logs:staging

# Monitor specific workers
npm run monitor:image-optimizer
npm run monitor:api-cache

# Check deployment status
npm run status
npm run status:staging
```

### Testing Deployed Workers
```bash
# Run comprehensive tests against deployed workers
npm run test:comprehensive

# Performance validation
npm run test:performance

# Integration tests
npm run test:integration
```

## Rollback Procedures

### Automatic Rollback
The deployment script includes automatic rollback on failure:
- Failed deployments trigger immediate rollback
- Health check failures initiate rollback
- All successful deployments are rolled back if any worker fails

### Manual Rollback
```bash
# Rollback all workers
make rollback
npm run deploy:rollback

# Rollback individual workers
make rollback-image
make rollback-api
npm run deploy:rollback:image-optimizer
npm run deploy:rollback:api-cache
```

### Emergency Rollback
```bash
# Emergency procedures
make emergency-rollback
make emergency-status
```

## Configuration Management

### Environment-Specific Settings

#### Staging Configuration
```typescript
// wrangler.toml [env.staging]
name = "syndicaps-workers-staging"
compatibility_date = "2024-07-27"
workers_dev = false
route = { pattern = "*-staging.syndicaps.com/*", zone_name = "syndicaps.com" }

[[env.staging.kv_namespaces]]
binding = "CACHE_KV"
id = "staging-cache-kv-id"
```

#### Production Configuration
```typescript
// wrangler.toml [env.production]
name = "syndicaps-workers"
compatibility_date = "2024-07-27"
workers_dev = false
route = { pattern = "*.syndicaps.com/*", zone_name = "syndicaps.com" }

[[env.production.kv_namespaces]]
binding = "CACHE_KV"
id = "production-cache-kv-id"
```

### Feature Flags
Workers support feature flag-based deployment:
```typescript
// Enable gradual rollout
const FEATURE_FLAGS = {
  imageOptimization: 0.5,  // 50% traffic
  apiCaching: 1.0,         // 100% traffic
  advancedAnalytics: 0.1   // 10% traffic
}
```

## Troubleshooting

### Common Issues

#### Deployment Failures
```bash
# Check wrangler configuration
wrangler whoami
wrangler kv:namespace list

# Validate worker syntax
npm run type-check
npm run lint

# Test locally
npm run dev:image-optimizer
npm run dev:api-cache
```

#### Performance Issues
```bash
# Monitor worker performance
wrangler tail --env production

# Check resource usage
wrangler deployments list --env production

# Validate optimization
npm run test:performance
```

#### Health Check Failures
```bash
# Debug health endpoints
curl -v https://images.syndicaps.com/health
curl -v https://api-cache.syndicaps.com/health

# Check worker logs
npm run logs
npm run monitor
```

### Debug Commands
```bash
# Comprehensive debugging
npm run validate
npm run test:comprehensive
npm run health-check:production

# Worker-specific debugging
wrangler dev image-optimizer.ts --local
wrangler dev api-cache.ts --local

# Log analysis
wrangler tail syndicaps-image-optimizer --env production
wrangler tail syndicaps-api-cache --env production
```

## Security Considerations

### API Token Management
- Use least-privilege API tokens
- Rotate tokens regularly
- Store tokens securely in CI/CD secrets

### Environment Isolation
- Separate staging and production environments
- Use different KV namespaces
- Implement proper access controls

### Monitoring and Alerting
- Monitor deployment success/failure
- Set up alerts for health check failures
- Track performance degradation

## Best Practices

### Pre-Deployment
1. Run comprehensive tests
2. Validate in staging environment
3. Review performance metrics
4. Check security vulnerabilities

### During Deployment
1. Use gradual rollout strategies
2. Monitor health checks continuously
3. Have rollback plan ready
4. Communicate with stakeholders

### Post-Deployment
1. Validate functionality
2. Monitor performance metrics
3. Check error rates
4. Update documentation

## Support and Maintenance

### Regular Maintenance
```bash
# Update dependencies
npm run update-deps

# Security audit
npm run security-audit

# Performance optimization
npm run perf-test
```

### Documentation Updates
- Keep deployment procedures current
- Document configuration changes
- Update troubleshooting guides
- Maintain runbooks

## Contact Information

- **Development Team**: <EMAIL>
- **Operations Team**: <EMAIL>
- **Emergency Contact**: <EMAIL>

## Additional Resources

- [Cloudflare Workers Documentation](https://developers.cloudflare.com/workers/)
- [Wrangler CLI Reference](https://developers.cloudflare.com/workers/wrangler/)
- [Syndicaps Architecture Documentation](./architecture.md)
- [Performance Monitoring Guide](./performance-monitoring.md)
