/**
 * Jest Configuration for Phase 1 Testing
 * Specialized configuration for Cloudflare hybrid deployment Phase 1 tests
 */

module.exports = {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup/phase1-setup.ts'],
  
  // Test patterns
  testMatch: [
    '<rootDir>/src/__tests__/lib/monitoring/**/*.test.ts',
    '<rootDir>/src/__tests__/lib/config/**/*.test.ts',
    '<rootDir>/src/__tests__/lib/cloudflare/**/*.test.ts',
    '<rootDir>/src/__tests__/integration/phase1-*.test.ts'
  ],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/lib/monitoring/**/*.ts',
    'src/lib/config/featureFlags.ts',
    'src/lib/cloudflare/**/*.ts',
    'src/components/admin/PerformanceDashboard.tsx',
    'src/components/admin/CacheStatistics.tsx',
    'src/components/admin/SystemHealth.tsx',
    '!src/**/*.d.ts',
    '!src/**/*.stories.ts',
    '!src/**/*.test.ts'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/lib/monitoring/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    },
    './src/lib/config/featureFlags.ts': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    }
  },
  
  // Module name mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1'
  },
  
  // Transform configuration
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: {
        jsx: 'react-jsx'
      }
    }]
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Test timeout
  testTimeout: 10000,
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Global setup/teardown
  globalSetup: '<rootDir>/src/__tests__/setup/global-setup.ts',
  globalTeardown: '<rootDir>/src/__tests__/setup/global-teardown.ts',
  
  // Reporter configuration
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './test-reports/phase1',
      filename: 'phase1-test-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'Phase 1 Test Report'
    }]
  ],
  
  // Coverage reporters
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  
  // Coverage directory
  coverageDirectory: './coverage/phase1'
}
