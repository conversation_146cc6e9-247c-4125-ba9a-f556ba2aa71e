/**
 * Jest Configuration for Integration Tests
 * Specialized configuration for R2 storage and migration integration tests
 */

const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  // Test environment
  testEnvironment: 'node',

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup/integration.setup.ts'],

  // Test patterns
  testMatch: [
    '<rootDir>/src/__tests__/integration/**/*.test.ts',
    '<rootDir>/src/__tests__/integration/**/*.test.tsx'
  ],

  // Module paths
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
  },

  // Transform configuration
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },

  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Coverage configuration
  collectCoverageFrom: [
    'src/lib/cloudflare/**/*.{ts,tsx}',
    'src/lib/migration/**/*.{ts,tsx}',
    'src/lib/feature-flags/**/*.{ts,tsx}',
    '!src/lib/**/*.d.ts',
    '!src/lib/**/*.test.{ts,tsx}',
    '!src/lib/**/__tests__/**',
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    'src/lib/cloudflare/r2StorageService.ts': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    'src/lib/migration/imageMigrator.ts': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75,
    },
  },

  // Test timeout
  testTimeout: 60000, // 60 seconds for integration tests

  // Verbose output
  verbose: true,

  // Detect open handles
  detectOpenHandles: true,

  // Force exit after tests complete
  forceExit: true,

  // Maximum worker processes
  maxWorkers: 2,

  // Global setup and teardown
  globalSetup: '<rootDir>/src/__tests__/setup/global.setup.ts',
  globalTeardown: '<rootDir>/src/__tests__/setup/global.teardown.ts',

  // Environment variables for testing
  setupFiles: ['<rootDir>/src/__tests__/setup/env.setup.ts'],

  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/src/__tests__/unit/',
    '<rootDir>/src/__tests__/e2e/',
  ],

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,

  // Reset modules between tests
  resetModules: true,

  // Error on deprecated features
  errorOnDeprecated: true,

  // Notify mode
  notify: false,

  // Bail on first test failure in CI
  bail: process.env.CI ? 1 : 0,

  // Cache directory
  cacheDirectory: '<rootDir>/.jest-cache',

  // Reporters
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/test-results/integration',
        outputName: 'junit.xml',
        suiteName: 'Integration Tests',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true,
      },
    ],
    [
      'jest-html-reporters',
      {
        publicPath: '<rootDir>/test-results/integration',
        filename: 'report.html',
        expand: true,
        hideIcon: false,
        pageTitle: 'Integration Test Report',
      },
    ],
  ],

  // Custom matchers
  setupFilesAfterEnv: [
    '<rootDir>/src/__tests__/setup/integration.setup.ts',
    '<rootDir>/src/__tests__/setup/custom-matchers.ts',
  ],
}

// Create the Jest config
module.exports = createJestConfig(customJestConfig)
