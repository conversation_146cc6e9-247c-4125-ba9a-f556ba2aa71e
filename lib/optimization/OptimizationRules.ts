/**
 * Optimization Rules for Common Scenarios
 * Specific optimization rules for slow images, cache optimization, and API performance
 */

import { OptimizationRule, RuleCategory } from './HybridPerformanceOptimizer'

export class OptimizationRules {
  /**
   * Get all predefined optimization rules
   */
  static getAllRules(): OptimizationRule[] {
    return [
      ...this.getCacheOptimizationRules(),
      ...this.getImageOptimizationRules(),
      ...this.getAPIPerformanceRules(),
      ...this.getCostOptimizationRules(),
      ...this.getSecurityOptimizationRules()
    ]
  }

  /**
   * Cache Optimization Rules
   */
  static getCacheOptimizationRules(): OptimizationRule[] {
    return [
      {
        id: 'cache-001',
        name: 'Low Cache Hit Rate Optimization',
        description: 'Increase cache TTL when hit rate is below 80%',
        category: 'cache' as RuleCategory,
        priority: 85,
        conditions: [
          {
            metric: 'cacheHitRate',
            operator: 'lt',
            value: 80,
            timeWindow: '15m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'cache_update',
            parameters: {
              action: 'increase_ttl',
              multiplier: 1.5,
              maxTTL: 86400 // 24 hours
            },
            rollback: {
              type: 'cache_update',
              parameters: {
                action: 'restore_ttl'
              }
            },
            validation: {
              metric: 'cacheHitRate',
              expectedChange: 'increase',
              threshold: 85,
              timeWindow: '10m'
            }
          }
        ],
        cooldown: 1800000, // 30 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['cache', 'performance', 'hit-rate'],
          documentation: 'Automatically increases cache TTL when hit rate drops below threshold'
        }
      },
      {
        id: 'cache-002',
        name: 'High Cache Miss Rate Alert',
        description: 'Alert when cache miss rate exceeds 30%',
        category: 'cache' as RuleCategory,
        priority: 70,
        conditions: [
          {
            metric: 'cacheMissRate',
            operator: 'gt',
            value: 30,
            timeWindow: '10m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'alert',
            parameters: {
              severity: 'warning',
              message: 'High cache miss rate detected',
              channels: ['slack', 'email']
            }
          }
        ],
        cooldown: 3600000, // 1 hour
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['cache', 'alerting', 'miss-rate']
        }
      },
      {
        id: 'cache-003',
        name: 'Intelligent Cache Purging',
        description: 'Purge stale cache entries when storage usage is high',
        category: 'cache' as RuleCategory,
        priority: 60,
        conditions: [
          {
            metric: 'cacheStorageUsage',
            operator: 'gt',
            value: 85,
            timeWindow: '5m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'cache_update',
            parameters: {
              action: 'purge_stale',
              ageThreshold: 3600, // 1 hour
              usageThreshold: 80
            }
          }
        ],
        cooldown: 900000, // 15 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['cache', 'storage', 'purging']
        }
      }
    ]
  }

  /**
   * Image Optimization Rules
   */
  static getImageOptimizationRules(): OptimizationRule[] {
    return [
      {
        id: 'image-001',
        name: 'Slow Image Loading Optimization',
        description: 'Enable aggressive image optimization when load times exceed 2 seconds',
        category: 'image' as RuleCategory,
        priority: 90,
        conditions: [
          {
            metric: 'imageLoadTime',
            operator: 'gt',
            value: 2000,
            timeWindow: '10m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'worker_update',
            parameters: {
              worker: 'image-optimizer',
              config: {
                quality: 75,
                format: 'webp',
                enableProgressive: true,
                enableLazyLoading: true
              }
            },
            rollback: {
              type: 'worker_update',
              parameters: {
                worker: 'image-optimizer',
                config: {
                  quality: 85,
                  format: 'auto',
                  enableProgressive: false,
                  enableLazyLoading: false
                }
              }
            },
            validation: {
              metric: 'imageLoadTime',
              expectedChange: 'decrease',
              threshold: 1500,
              timeWindow: '15m'
            }
          }
        ],
        cooldown: 2700000, // 45 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['image', 'performance', 'loading'],
          documentation: 'Automatically optimizes image settings when load times are slow'
        }
      },
      {
        id: 'image-002',
        name: 'Large Image Detection and Compression',
        description: 'Automatically compress images larger than 1MB',
        category: 'image' as RuleCategory,
        priority: 75,
        conditions: [
          {
            metric: 'averageImageSize',
            operator: 'gt',
            value: 1048576, // 1MB
            timeWindow: '30m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'worker_update',
            parameters: {
              worker: 'image-optimizer',
              config: {
                maxWidth: 1920,
                maxHeight: 1080,
                quality: 70,
                enableCompression: true
              }
            },
            validation: {
              metric: 'averageImageSize',
              expectedChange: 'decrease',
              threshold: 524288, // 512KB
              timeWindow: '20m'
            }
          }
        ],
        cooldown: 3600000, // 1 hour
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['image', 'compression', 'size']
        }
      },
      {
        id: 'image-003',
        name: 'Progressive Image Loading',
        description: 'Enable progressive loading for images when bandwidth is limited',
        category: 'image' as RuleCategory,
        priority: 65,
        conditions: [
          {
            metric: 'averageBandwidth',
            operator: 'lt',
            value: 1000000, // 1 Mbps
            timeWindow: '15m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'feature_flag',
            parameters: {
              flag: 'progressive_image_loading',
              enabled: true,
              rolloutPercentage: 100
            },
            rollback: {
              type: 'feature_flag',
              parameters: {
                flag: 'progressive_image_loading',
                enabled: false
              }
            }
          }
        ],
        cooldown: 1800000, // 30 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['image', 'progressive', 'bandwidth']
        }
      }
    ]
  }

  /**
   * API Performance Rules
   */
  static getAPIPerformanceRules(): OptimizationRule[] {
    return [
      {
        id: 'api-001',
        name: 'Slow API Response Optimization',
        description: 'Enable API caching when response times exceed 1 second',
        category: 'api' as RuleCategory,
        priority: 95,
        conditions: [
          {
            metric: 'apiResponseTime',
            operator: 'gt',
            value: 1000,
            timeWindow: '10m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'feature_flag',
            parameters: {
              flag: 'api_caching',
              enabled: true,
              rolloutPercentage: 100,
              config: {
                ttl: 300, // 5 minutes
                staleWhileRevalidate: true
              }
            },
            rollback: {
              type: 'feature_flag',
              parameters: {
                flag: 'api_caching',
                enabled: false
              }
            },
            validation: {
              metric: 'apiResponseTime',
              expectedChange: 'decrease',
              threshold: 500,
              timeWindow: '15m'
            }
          }
        ],
        cooldown: 1800000, // 30 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['api', 'performance', 'caching'],
          documentation: 'Automatically enables API caching for slow endpoints'
        }
      },
      {
        id: 'api-002',
        name: 'High API Error Rate Mitigation',
        description: 'Implement circuit breaker when error rate exceeds 10%',
        category: 'api' as RuleCategory,
        priority: 100,
        conditions: [
          {
            metric: 'apiErrorRate',
            operator: 'gt',
            value: 10,
            timeWindow: '5m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'feature_flag',
            parameters: {
              flag: 'api_circuit_breaker',
              enabled: true,
              config: {
                errorThreshold: 10,
                timeWindow: 60000, // 1 minute
                fallbackResponse: true
              }
            },
            rollback: {
              type: 'feature_flag',
              parameters: {
                flag: 'api_circuit_breaker',
                enabled: false
              }
            }
          },
          {
            type: 'alert',
            parameters: {
              severity: 'critical',
              message: 'High API error rate - circuit breaker activated',
              channels: ['slack', 'email', 'pagerduty']
            }
          }
        ],
        cooldown: 600000, // 10 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['api', 'reliability', 'circuit-breaker']
        }
      },
      {
        id: 'api-003',
        name: 'Rate Limiting Optimization',
        description: 'Adjust rate limits based on traffic patterns',
        category: 'api' as RuleCategory,
        priority: 70,
        conditions: [
          {
            metric: 'apiRequestRate',
            operator: 'gt',
            value: 1000,
            timeWindow: '5m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'worker_update',
            parameters: {
              worker: 'api-cache',
              config: {
                rateLimitPerMinute: 1500,
                burstLimit: 100,
                enableAdaptiveRateLimit: true
              }
            },
            rollback: {
              type: 'worker_update',
              parameters: {
                worker: 'api-cache',
                config: {
                  rateLimitPerMinute: 1000,
                  burstLimit: 50,
                  enableAdaptiveRateLimit: false
                }
              }
            }
          }
        ],
        cooldown: 1800000, // 30 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['api', 'rate-limiting', 'traffic']
        }
      }
    ]
  }

  /**
   * Cost Optimization Rules
   */
  static getCostOptimizationRules(): OptimizationRule[] {
    return [
      {
        id: 'cost-001',
        name: 'High R2 Storage Cost Optimization',
        description: 'Implement lifecycle policies when storage costs are high',
        category: 'cost' as RuleCategory,
        priority: 80,
        conditions: [
          {
            metric: 'r2StorageCost',
            operator: 'gt',
            value: 100, // $100 per month
            timeWindow: '1d',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'storage_optimize',
            parameters: {
              action: 'implement_lifecycle',
              rules: [
                {
                  name: 'archive_old_images',
                  condition: 'age > 90 days',
                  action: 'transition_to_infrequent_access'
                },
                {
                  name: 'delete_unused_images',
                  condition: 'age > 365 days AND access_count = 0',
                  action: 'delete'
                }
              ]
            }
          }
        ],
        cooldown: 86400000, // 24 hours
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['cost', 'storage', 'lifecycle']
        }
      },
      {
        id: 'cost-002',
        name: 'Bandwidth Cost Optimization',
        description: 'Optimize compression when bandwidth costs are high',
        category: 'cost' as RuleCategory,
        priority: 75,
        conditions: [
          {
            metric: 'bandwidthCost',
            operator: 'gt',
            value: 50, // $50 per month
            timeWindow: '1d',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'worker_update',
            parameters: {
              worker: 'image-optimizer',
              config: {
                enableAggresiveCompression: true,
                quality: 65,
                enableWebP: true,
                enableAVIF: true
              }
            },
            rollback: {
              type: 'worker_update',
              parameters: {
                worker: 'image-optimizer',
                config: {
                  enableAggresiveCompression: false,
                  quality: 85,
                  enableWebP: false,
                  enableAVIF: false
                }
              }
            }
          }
        ],
        cooldown: 3600000, // 1 hour
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['cost', 'bandwidth', 'compression']
        }
      }
    ]
  }

  /**
   * Security Optimization Rules
   */
  static getSecurityOptimizationRules(): OptimizationRule[] {
    return [
      {
        id: 'security-001',
        name: 'DDoS Attack Mitigation',
        description: 'Activate DDoS protection when unusual traffic patterns detected',
        category: 'security' as RuleCategory,
        priority: 100,
        conditions: [
          {
            metric: 'requestRate',
            operator: 'gt',
            value: 10000,
            timeWindow: '1m',
            aggregation: 'max'
          }
        ],
        actions: [
          {
            type: 'feature_flag',
            parameters: {
              flag: 'ddos_protection',
              enabled: true,
              config: {
                challengeThreshold: 100,
                blockThreshold: 1000,
                enableCaptcha: true
              }
            },
            rollback: {
              type: 'feature_flag',
              parameters: {
                flag: 'ddos_protection',
                enabled: false
              }
            }
          },
          {
            type: 'alert',
            parameters: {
              severity: 'critical',
              message: 'Potential DDoS attack detected - protection activated',
              channels: ['slack', 'email', 'pagerduty']
            }
          }
        ],
        cooldown: 300000, // 5 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['security', 'ddos', 'protection']
        }
      },
      {
        id: 'security-002',
        name: 'Suspicious IP Activity',
        description: 'Block IPs with suspicious activity patterns',
        category: 'security' as RuleCategory,
        priority: 90,
        conditions: [
          {
            metric: 'suspiciousIPCount',
            operator: 'gt',
            value: 5,
            timeWindow: '10m',
            aggregation: 'count'
          }
        ],
        actions: [
          {
            type: 'feature_flag',
            parameters: {
              flag: 'ip_blocking',
              enabled: true,
              config: {
                blockDuration: 3600, // 1 hour
                enableGeoBlocking: true,
                whitelist: ['trusted_ips']
              }
            }
          }
        ],
        cooldown: 1800000, // 30 minutes
        enabled: true,
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['security', 'ip-blocking', 'suspicious-activity']
        }
      }
    ]
  }

  /**
   * Get rules by category
   */
  static getRulesByCategory(category: RuleCategory): OptimizationRule[] {
    return this.getAllRules().filter(rule => rule.category === category)
  }

  /**
   * Get rules by priority range
   */
  static getRulesByPriority(minPriority: number, maxPriority: number): OptimizationRule[] {
    return this.getAllRules().filter(rule => 
      rule.priority >= minPriority && rule.priority <= maxPriority
    )
  }

  /**
   * Get enabled rules only
   */
  static getEnabledRules(): OptimizationRule[] {
    return this.getAllRules().filter(rule => rule.enabled)
  }

  /**
   * Create custom rule template
   */
  static createCustomRule(
    id: string,
    name: string,
    description: string,
    category: RuleCategory,
    priority: number
  ): Partial<OptimizationRule> {
    return {
      id,
      name,
      description,
      category,
      priority,
      conditions: [],
      actions: [],
      cooldown: 1800000, // 30 minutes default
      enabled: false, // Disabled by default for custom rules
      metadata: {
        author: 'Custom',
        version: '1.0',
        lastModified: new Date().toISOString().split('T')[0],
        tags: ['custom', category]
      }
    }
  }
}
