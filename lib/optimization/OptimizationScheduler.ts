/**
 * Optimization Scheduler
 * Automated optimization scheduling with configurable intervals and conditions
 */

import { HybridPerformanceOptimizer, OptimizationResult } from './HybridPerformanceOptimizer'
import { OptimizationRules } from './OptimizationRules'

export interface ScheduleConfig {
  id: string
  name: string
  description: string
  interval: number // milliseconds
  enabled: boolean
  conditions?: ScheduleCondition[]
  ruleCategories?: string[]
  maxConcurrentOptimizations?: number
  quietHours?: QuietHours
  metadata: ScheduleMetadata
}

export interface ScheduleCondition {
  metric: string
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte'
  value: number
  required: boolean
}

export interface QuietHours {
  enabled: boolean
  startHour: number // 0-23
  endHour: number // 0-23
  timezone: string
}

export interface ScheduleMetadata {
  author: string
  version: string
  lastModified: string
  tags: string[]
}

export interface ScheduleExecution {
  scheduleId: string
  startTime: Date
  endTime?: Date
  status: 'running' | 'completed' | 'failed' | 'skipped'
  results: OptimizationResult[]
  error?: string
  metrics?: ScheduleMetrics
}

export interface ScheduleMetrics {
  executionTime: number
  rulesEvaluated: number
  rulesExecuted: number
  successRate: number
  performanceImpact: number
}

export class OptimizationScheduler {
  private schedules: Map<string, ScheduleConfig> = new Map()
  private activeTimers: Map<string, NodeJS.Timeout> = new Map()
  private executionHistory: ScheduleExecution[] = []
  private isRunning: boolean = false

  constructor(
    private optimizer: HybridPerformanceOptimizer,
    private config: SchedulerConfig
  ) {
    this.initializeDefaultSchedules()
  }

  /**
   * Start the scheduler
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ Scheduler is already running')
      return
    }

    console.log('🚀 Starting Optimization Scheduler')
    this.isRunning = true

    // Start all enabled schedules
    for (const schedule of this.schedules.values()) {
      if (schedule.enabled) {
        await this.startSchedule(schedule.id)
      }
    }

    console.log(`✅ Scheduler started with ${this.activeTimers.size} active schedules`)
  }

  /**
   * Stop the scheduler
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('⚠️ Scheduler is not running')
      return
    }

    console.log('🛑 Stopping Optimization Scheduler')
    this.isRunning = false

    // Clear all active timers
    for (const [scheduleId, timer] of this.activeTimers) {
      clearInterval(timer)
      console.log(`⏹️ Stopped schedule: ${scheduleId}`)
    }

    this.activeTimers.clear()
    console.log('✅ Scheduler stopped')
  }

  /**
   * Add a new schedule
   */
  addSchedule(schedule: ScheduleConfig): void {
    this.validateSchedule(schedule)
    this.schedules.set(schedule.id, schedule)
    
    if (this.isRunning && schedule.enabled) {
      this.startSchedule(schedule.id)
    }

    console.log(`➕ Added schedule: ${schedule.name}`)
  }

  /**
   * Remove a schedule
   */
  removeSchedule(scheduleId: string): boolean {
    const schedule = this.schedules.get(scheduleId)
    if (!schedule) {
      return false
    }

    // Stop the schedule if it's running
    this.stopSchedule(scheduleId)
    
    // Remove from schedules
    const removed = this.schedules.delete(scheduleId)
    
    if (removed) {
      console.log(`➖ Removed schedule: ${schedule.name}`)
    }

    return removed
  }

  /**
   * Enable/disable a schedule
   */
  toggleSchedule(scheduleId: string, enabled: boolean): boolean {
    const schedule = this.schedules.get(scheduleId)
    if (!schedule) {
      return false
    }

    schedule.enabled = enabled

    if (this.isRunning) {
      if (enabled) {
        this.startSchedule(scheduleId)
      } else {
        this.stopSchedule(scheduleId)
      }
    }

    console.log(`${enabled ? '✅' : '❌'} ${enabled ? 'Enabled' : 'Disabled'} schedule: ${schedule.name}`)
    return true
  }

  /**
   * Get schedule statistics
   */
  getScheduleStats(): ScheduleStats {
    const totalSchedules = this.schedules.size
    const enabledSchedules = Array.from(this.schedules.values()).filter(s => s.enabled).length
    const activeSchedules = this.activeTimers.size
    
    const recentExecutions = this.executionHistory.slice(-100) // Last 100 executions
    const successfulExecutions = recentExecutions.filter(e => e.status === 'completed').length
    const failedExecutions = recentExecutions.filter(e => e.status === 'failed').length
    
    const averageExecutionTime = recentExecutions.length > 0
      ? recentExecutions.reduce((sum, e) => sum + (e.metrics?.executionTime || 0), 0) / recentExecutions.length
      : 0

    const averageSuccessRate = recentExecutions.length > 0
      ? recentExecutions.reduce((sum, e) => sum + (e.metrics?.successRate || 0), 0) / recentExecutions.length
      : 0

    return {
      totalSchedules,
      enabledSchedules,
      activeSchedules,
      totalExecutions: this.executionHistory.length,
      successfulExecutions,
      failedExecutions,
      successRate: recentExecutions.length > 0 ? (successfulExecutions / recentExecutions.length) * 100 : 0,
      averageExecutionTime,
      averageSuccessRate,
      lastExecutionTime: this.executionHistory.length > 0 
        ? this.executionHistory[this.executionHistory.length - 1].startTime
        : null
    }
  }

  /**
   * Get execution history
   */
  getExecutionHistory(limit: number = 50): ScheduleExecution[] {
    return this.executionHistory.slice(-limit)
  }

  /**
   * Execute a schedule manually
   */
  async executeScheduleManually(scheduleId: string): Promise<ScheduleExecution> {
    const schedule = this.schedules.get(scheduleId)
    if (!schedule) {
      throw new Error(`Schedule not found: ${scheduleId}`)
    }

    console.log(`🔧 Manually executing schedule: ${schedule.name}`)
    return await this.executeSchedule(schedule)
  }

  /**
   * Start a specific schedule
   */
  private async startSchedule(scheduleId: string): Promise<void> {
    const schedule = this.schedules.get(scheduleId)
    if (!schedule) {
      console.error(`❌ Schedule not found: ${scheduleId}`)
      return
    }

    // Stop existing timer if any
    this.stopSchedule(scheduleId)

    // Create new timer
    const timer = setInterval(async () => {
      try {
        await this.executeSchedule(schedule)
      } catch (error) {
        console.error(`❌ Schedule execution failed: ${schedule.name}`, error)
      }
    }, schedule.interval)

    this.activeTimers.set(scheduleId, timer)
    console.log(`⏰ Started schedule: ${schedule.name} (interval: ${schedule.interval}ms)`)
  }

  /**
   * Stop a specific schedule
   */
  private stopSchedule(scheduleId: string): void {
    const timer = this.activeTimers.get(scheduleId)
    if (timer) {
      clearInterval(timer)
      this.activeTimers.delete(scheduleId)
    }
  }

  /**
   * Execute a schedule
   */
  private async executeSchedule(schedule: ScheduleConfig): Promise<ScheduleExecution> {
    const execution: ScheduleExecution = {
      scheduleId: schedule.id,
      startTime: new Date(),
      status: 'running',
      results: []
    }

    try {
      // Check quiet hours
      if (this.isQuietHours(schedule)) {
        execution.status = 'skipped'
        execution.endTime = new Date()
        this.executionHistory.push(execution)
        console.log(`😴 Skipped schedule during quiet hours: ${schedule.name}`)
        return execution
      }

      // Check conditions
      if (schedule.conditions && !(await this.checkConditions(schedule.conditions))) {
        execution.status = 'skipped'
        execution.endTime = new Date()
        this.executionHistory.push(execution)
        console.log(`⏭️ Skipped schedule due to unmet conditions: ${schedule.name}`)
        return execution
      }

      console.log(`🔄 Executing schedule: ${schedule.name}`)

      // Load rules for this schedule
      await this.loadRulesForSchedule(schedule)

      // Execute optimization cycle
      const results = await this.optimizer.executeOptimizationCycle()
      execution.results = results

      // Calculate metrics
      execution.metrics = this.calculateScheduleMetrics(results, execution.startTime)
      execution.status = 'completed'
      execution.endTime = new Date()

      console.log(`✅ Schedule completed: ${schedule.name}`)
      console.log(`   Executed ${results.length} optimizations`)
      console.log(`   Success rate: ${execution.metrics.successRate.toFixed(1)}%`)

    } catch (error) {
      execution.status = 'failed'
      execution.endTime = new Date()
      execution.error = error instanceof Error ? error.message : String(error)
      console.error(`❌ Schedule failed: ${schedule.name}`, error)
    }

    // Store execution history
    this.executionHistory.push(execution)

    // Limit history size
    if (this.executionHistory.length > this.config.maxHistorySize) {
      this.executionHistory = this.executionHistory.slice(-this.config.maxHistorySize)
    }

    return execution
  }

  /**
   * Check if current time is within quiet hours
   */
  private isQuietHours(schedule: ScheduleConfig): boolean {
    if (!schedule.quietHours?.enabled) {
      return false
    }

    const now = new Date()
    const currentHour = now.getHours()
    const { startHour, endHour } = schedule.quietHours

    if (startHour <= endHour) {
      // Same day quiet hours (e.g., 2 AM to 6 AM)
      return currentHour >= startHour && currentHour < endHour
    } else {
      // Overnight quiet hours (e.g., 10 PM to 6 AM)
      return currentHour >= startHour || currentHour < endHour
    }
  }

  /**
   * Check schedule conditions
   */
  private async checkConditions(conditions: ScheduleCondition[]): Promise<boolean> {
    // This would typically check current system metrics
    // For now, we'll simulate condition checking
    
    for (const condition of conditions) {
      if (condition.required) {
        // Simulate metric checking
        const currentValue = await this.getCurrentMetricValue(condition.metric)
        
        if (!this.evaluateCondition(condition, currentValue)) {
          return false
        }
      }
    }

    return true
  }

  /**
   * Get current metric value (placeholder)
   */
  private async getCurrentMetricValue(metric: string): Promise<number> {
    // This would integrate with actual metrics collection
    // For now, return simulated values
    const simulatedValues: Record<string, number> = {
      'systemLoad': Math.random() * 100,
      'errorRate': Math.random() * 10,
      'responseTime': Math.random() * 2000,
      'cacheHitRate': 70 + Math.random() * 30
    }

    return simulatedValues[metric] || 0
  }

  /**
   * Evaluate a condition
   */
  private evaluateCondition(condition: ScheduleCondition, currentValue: number): boolean {
    switch (condition.operator) {
      case 'gt':
        return currentValue > condition.value
      case 'gte':
        return currentValue >= condition.value
      case 'lt':
        return currentValue < condition.value
      case 'lte':
        return currentValue <= condition.value
      case 'eq':
        return currentValue === condition.value
      default:
        return false
    }
  }

  /**
   * Load rules for a specific schedule
   */
  private async loadRulesForSchedule(schedule: ScheduleConfig): Promise<void> {
    // Clear existing rules
    const allRules = OptimizationRules.getAllRules()
    
    // Filter rules based on schedule configuration
    let rulesToLoad = allRules

    if (schedule.ruleCategories && schedule.ruleCategories.length > 0) {
      rulesToLoad = allRules.filter(rule => 
        schedule.ruleCategories!.includes(rule.category)
      )
    }

    // Add rules to optimizer
    for (const rule of rulesToLoad) {
      this.optimizer.addRule(rule)
    }

    console.log(`📋 Loaded ${rulesToLoad.length} rules for schedule: ${schedule.name}`)
  }

  /**
   * Calculate schedule metrics
   */
  private calculateScheduleMetrics(results: OptimizationResult[], startTime: Date): ScheduleMetrics {
    const executionTime = Date.now() - startTime.getTime()
    const rulesExecuted = results.length
    const successfulRules = results.filter(r => r.success).length
    const successRate = rulesExecuted > 0 ? (successfulRules / rulesExecuted) * 100 : 0
    
    const performanceImpact = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.impact.performanceChange, 0) / successfulRules || 0

    return {
      executionTime,
      rulesEvaluated: rulesExecuted, // This would be different in real implementation
      rulesExecuted,
      successRate,
      performanceImpact
    }
  }

  /**
   * Validate schedule configuration
   */
  private validateSchedule(schedule: ScheduleConfig): void {
    if (!schedule.id || !schedule.name) {
      throw new Error('Schedule must have id and name')
    }

    if (schedule.interval < 60000) { // Minimum 1 minute
      throw new Error('Schedule interval must be at least 1 minute')
    }

    if (schedule.quietHours?.enabled) {
      const { startHour, endHour } = schedule.quietHours
      if (startHour < 0 || startHour > 23 || endHour < 0 || endHour > 23) {
        throw new Error('Quiet hours must be between 0 and 23')
      }
    }
  }

  /**
   * Initialize default schedules
   */
  private initializeDefaultSchedules(): void {
    const defaultSchedules: ScheduleConfig[] = [
      {
        id: 'hourly-performance-check',
        name: 'Hourly Performance Check',
        description: 'Quick performance optimization check every hour',
        interval: 3600000, // 1 hour
        enabled: true,
        ruleCategories: ['cache', 'api'],
        maxConcurrentOptimizations: 3,
        quietHours: {
          enabled: true,
          startHour: 2,
          endHour: 6,
          timezone: 'UTC'
        },
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['performance', 'hourly', 'cache', 'api']
        }
      },
      {
        id: 'daily-comprehensive-optimization',
        name: 'Daily Comprehensive Optimization',
        description: 'Comprehensive optimization analysis and execution',
        interval: 86400000, // 24 hours
        enabled: true,
        ruleCategories: ['cache', 'image', 'api', 'cost'],
        maxConcurrentOptimizations: 10,
        quietHours: {
          enabled: true,
          startHour: 3,
          endHour: 5,
          timezone: 'UTC'
        },
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['comprehensive', 'daily', 'all-categories']
        }
      },
      {
        id: 'security-monitoring',
        name: 'Security Monitoring',
        description: 'Continuous security optimization monitoring',
        interval: 300000, // 5 minutes
        enabled: true,
        ruleCategories: ['security'],
        maxConcurrentOptimizations: 5,
        conditions: [
          {
            metric: 'systemLoad',
            operator: 'lt',
            value: 80,
            required: true
          }
        ],
        metadata: {
          author: 'Syndicaps Optimization Engine',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['security', 'monitoring', 'continuous']
        }
      }
    ]

    for (const schedule of defaultSchedules) {
      this.schedules.set(schedule.id, schedule)
    }

    console.log(`📅 Initialized ${defaultSchedules.length} default schedules`)
  }
}

// Supporting interfaces
export interface SchedulerConfig {
  maxHistorySize: number
  enableLogging: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}

export interface ScheduleStats {
  totalSchedules: number
  enabledSchedules: number
  activeSchedules: number
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  successRate: number
  averageExecutionTime: number
  averageSuccessRate: number
  lastExecutionTime: Date | null
}
