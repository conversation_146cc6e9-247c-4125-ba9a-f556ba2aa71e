/**
 * Optimization Impact Tracker
 * System to track optimization results, measure improvements, and generate reports
 */

import { OptimizationResult, OptimizationImpact } from './HybridPerformanceOptimizer'
import { writeFileSync } from 'fs'
import { join } from 'path'

export interface ImpactRecord {
  id: string
  timestamp: Date
  ruleId: string
  ruleName: string
  category: string
  beforeMetrics: MetricSnapshot
  afterMetrics: MetricSnapshot
  impact: OptimizationImpact
  success: boolean
  duration: number
  rollbackExecuted: boolean
  tags: string[]
}

export interface MetricSnapshot {
  timestamp: Date
  responseTime: number
  errorRate: number
  cacheHitRate: number
  throughput: number
  cpuUsage: number
  memoryUsage: number
  bandwidthUsage: number
  cost: number
  customMetrics: Record<string, number>
}

export interface ImpactSummary {
  timeRange: TimeRange
  totalOptimizations: number
  successfulOptimizations: number
  failedOptimizations: number
  successRate: number
  averageImpact: OptimizationImpact
  categoryBreakdown: CategoryBreakdown[]
  topPerformingRules: RulePerformance[]
  trends: ImpactTrends
  recommendations: string[]
}

export interface TimeRange {
  start: Date
  end: Date
  duration: number
}

export interface CategoryBreakdown {
  category: string
  count: number
  successRate: number
  averageImpact: number
  totalImpact: number
}

export interface RulePerformance {
  ruleId: string
  ruleName: string
  executionCount: number
  successRate: number
  averageImpact: number
  totalImpact: number
  lastExecution: Date
}

export interface ImpactTrends {
  performanceTrend: TrendData
  costTrend: TrendData
  reliabilityTrend: TrendData
  optimizationFrequency: TrendData
}

export interface TrendData {
  direction: 'improving' | 'degrading' | 'stable'
  changeRate: number
  confidence: number
  dataPoints: DataPoint[]
}

export interface DataPoint {
  timestamp: Date
  value: number
}

export interface ImpactReport {
  id: string
  title: string
  generatedAt: Date
  timeRange: TimeRange
  summary: ImpactSummary
  detailedAnalysis: DetailedAnalysis
  visualizations: VisualizationData[]
  recommendations: RecommendationSection[]
  appendix: ReportAppendix
}

export interface DetailedAnalysis {
  performanceAnalysis: PerformanceAnalysis
  costAnalysis: CostAnalysis
  reliabilityAnalysis: ReliabilityAnalysis
  categoryAnalysis: CategoryAnalysis[]
}

export interface PerformanceAnalysis {
  overallImprovement: number
  responseTimeImprovement: number
  throughputImprovement: number
  cacheEfficiencyImprovement: number
  topPerformanceGains: ImpactRecord[]
  performanceRegressions: ImpactRecord[]
}

export interface CostAnalysis {
  totalCostSavings: number
  bandwidthSavings: number
  storageSavings: number
  computeSavings: number
  costOptimizationOpportunities: string[]
}

export interface ReliabilityAnalysis {
  errorRateImprovement: number
  uptimeImprovement: number
  stabilityMetrics: Record<string, number>
  reliabilityIssues: string[]
}

export interface CategoryAnalysis {
  category: string
  impact: OptimizationImpact
  rulePerformance: RulePerformance[]
  insights: string[]
}

export interface VisualizationData {
  type: 'line' | 'bar' | 'pie' | 'scatter'
  title: string
  data: any[]
  config: Record<string, any>
}

export interface RecommendationSection {
  title: string
  priority: 'high' | 'medium' | 'low'
  recommendations: string[]
  expectedImpact: string
}

export interface ReportAppendix {
  methodology: string
  dataQuality: DataQualityInfo
  limitations: string[]
  glossary: Record<string, string>
}

export interface DataQualityInfo {
  completeness: number
  accuracy: number
  timeliness: number
  consistency: number
}

export class OptimizationTracker {
  private impactRecords: ImpactRecord[] = []
  private metricSnapshots: Map<string, MetricSnapshot> = new Map()

  constructor(private config: TrackerConfig) {}

  /**
   * Record optimization impact
   */
  async recordImpact(
    result: OptimizationResult,
    ruleName: string,
    category: string,
    beforeMetrics: MetricSnapshot,
    afterMetrics: MetricSnapshot
  ): Promise<string> {
    const record: ImpactRecord = {
      id: this.generateRecordId(),
      timestamp: new Date(),
      ruleId: result.ruleId,
      ruleName,
      category,
      beforeMetrics,
      afterMetrics,
      impact: result.impact,
      success: result.success,
      duration: result.executionTime,
      rollbackExecuted: result.rollbackExecuted || false,
      tags: this.generateTags(category, result)
    }

    this.impactRecords.push(record)
    
    // Limit records to prevent memory issues
    if (this.impactRecords.length > this.config.maxRecords) {
      this.impactRecords = this.impactRecords.slice(-this.config.maxRecords)
    }

    console.log(`📊 Recorded optimization impact: ${ruleName} (${result.success ? 'success' : 'failed'})`)
    
    return record.id
  }

  /**
   * Take metric snapshot
   */
  async takeMetricSnapshot(label: string): Promise<MetricSnapshot> {
    const snapshot: MetricSnapshot = {
      timestamp: new Date(),
      responseTime: await this.getCurrentMetric('responseTime'),
      errorRate: await this.getCurrentMetric('errorRate'),
      cacheHitRate: await this.getCurrentMetric('cacheHitRate'),
      throughput: await this.getCurrentMetric('throughput'),
      cpuUsage: await this.getCurrentMetric('cpuUsage'),
      memoryUsage: await this.getCurrentMetric('memoryUsage'),
      bandwidthUsage: await this.getCurrentMetric('bandwidthUsage'),
      cost: await this.getCurrentMetric('cost'),
      customMetrics: await this.getCustomMetrics()
    }

    this.metricSnapshots.set(label, snapshot)
    return snapshot
  }

  /**
   * Generate impact summary for time range
   */
  generateImpactSummary(startDate: Date, endDate: Date): ImpactSummary {
    const records = this.getRecordsInRange(startDate, endDate)
    
    const totalOptimizations = records.length
    const successfulOptimizations = records.filter(r => r.success).length
    const failedOptimizations = totalOptimizations - successfulOptimizations
    const successRate = totalOptimizations > 0 ? (successfulOptimizations / totalOptimizations) * 100 : 0

    const averageImpact = this.calculateAverageImpact(records.filter(r => r.success))
    const categoryBreakdown = this.generateCategoryBreakdown(records)
    const topPerformingRules = this.getTopPerformingRules(records)
    const trends = this.calculateTrends(records)
    const recommendations = this.generateRecommendations(records, trends)

    return {
      timeRange: {
        start: startDate,
        end: endDate,
        duration: endDate.getTime() - startDate.getTime()
      },
      totalOptimizations,
      successfulOptimizations,
      failedOptimizations,
      successRate,
      averageImpact,
      categoryBreakdown,
      topPerformingRules,
      trends,
      recommendations
    }
  }

  /**
   * Generate comprehensive impact report
   */
  async generateImpactReport(startDate: Date, endDate: Date): Promise<ImpactReport> {
    const summary = this.generateImpactSummary(startDate, endDate)
    const records = this.getRecordsInRange(startDate, endDate)

    const report: ImpactReport = {
      id: this.generateReportId(),
      title: `Optimization Impact Report - ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`,
      generatedAt: new Date(),
      timeRange: summary.timeRange,
      summary,
      detailedAnalysis: this.generateDetailedAnalysis(records),
      visualizations: this.generateVisualizations(records),
      recommendations: this.generateRecommendationSections(records, summary.trends),
      appendix: this.generateReportAppendix()
    }

    // Save report to file
    await this.saveReport(report)

    return report
  }

  /**
   * Get optimization statistics
   */
  getOptimizationStats(): OptimizationStats {
    const totalRecords = this.impactRecords.length
    const successfulRecords = this.impactRecords.filter(r => r.success).length
    const recentRecords = this.impactRecords.slice(-100) // Last 100 records

    const averageImpact = this.calculateAverageImpact(this.impactRecords.filter(r => r.success))
    
    const categoryStats = this.generateCategoryStats()
    const ruleStats = this.generateRuleStats()

    return {
      totalOptimizations: totalRecords,
      successfulOptimizations: successfulRecords,
      successRate: totalRecords > 0 ? (successfulRecords / totalRecords) * 100 : 0,
      averagePerformanceImpact: averageImpact.performanceChange,
      averageCostImpact: averageImpact.costChange,
      averageReliabilityImpact: averageImpact.reliabilityChange,
      categoryStats,
      ruleStats,
      lastOptimization: totalRecords > 0 ? this.impactRecords[totalRecords - 1].timestamp : null
    }
  }

  /**
   * Export impact data
   */
  async exportImpactData(format: 'json' | 'csv', startDate?: Date, endDate?: Date): Promise<string> {
    let records = this.impactRecords

    if (startDate && endDate) {
      records = this.getRecordsInRange(startDate, endDate)
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `optimization-impact-${timestamp}.${format}`
    const filepath = join(this.config.exportPath, filename)

    if (format === 'json') {
      writeFileSync(filepath, JSON.stringify(records, null, 2))
    } else if (format === 'csv') {
      const csv = this.convertToCSV(records)
      writeFileSync(filepath, csv)
    }

    console.log(`📄 Exported impact data to: ${filepath}`)
    return filepath
  }

  /**
   * Get records in date range
   */
  private getRecordsInRange(startDate: Date, endDate: Date): ImpactRecord[] {
    return this.impactRecords.filter(record => 
      record.timestamp >= startDate && record.timestamp <= endDate
    )
  }

  /**
   * Calculate average impact
   */
  private calculateAverageImpact(records: ImpactRecord[]): OptimizationImpact {
    if (records.length === 0) {
      return { performanceChange: 0, costChange: 0, reliabilityChange: 0, metrics: {} }
    }

    const totalImpact = records.reduce((sum, record) => ({
      performanceChange: sum.performanceChange + record.impact.performanceChange,
      costChange: sum.costChange + record.impact.costChange,
      reliabilityChange: sum.reliabilityChange + record.impact.reliabilityChange,
      metrics: sum.metrics
    }), { performanceChange: 0, costChange: 0, reliabilityChange: 0, metrics: {} })

    return {
      performanceChange: totalImpact.performanceChange / records.length,
      costChange: totalImpact.costChange / records.length,
      reliabilityChange: totalImpact.reliabilityChange / records.length,
      metrics: {}
    }
  }

  /**
   * Generate category breakdown
   */
  private generateCategoryBreakdown(records: ImpactRecord[]): CategoryBreakdown[] {
    const categories = new Map<string, ImpactRecord[]>()

    // Group records by category
    for (const record of records) {
      if (!categories.has(record.category)) {
        categories.set(record.category, [])
      }
      categories.get(record.category)!.push(record)
    }

    // Generate breakdown for each category
    const breakdown: CategoryBreakdown[] = []
    for (const [category, categoryRecords] of categories) {
      const successfulRecords = categoryRecords.filter(r => r.success)
      const successRate = categoryRecords.length > 0 ? (successfulRecords.length / categoryRecords.length) * 100 : 0
      const averageImpact = this.calculateAverageImpact(successfulRecords).performanceChange
      const totalImpact = successfulRecords.reduce((sum, r) => sum + r.impact.performanceChange, 0)

      breakdown.push({
        category,
        count: categoryRecords.length,
        successRate,
        averageImpact,
        totalImpact
      })
    }

    return breakdown.sort((a, b) => b.totalImpact - a.totalImpact)
  }

  /**
   * Get top performing rules
   */
  private getTopPerformingRules(records: ImpactRecord[]): RulePerformance[] {
    const rules = new Map<string, ImpactRecord[]>()

    // Group records by rule
    for (const record of records) {
      if (!rules.has(record.ruleId)) {
        rules.set(record.ruleId, [])
      }
      rules.get(record.ruleId)!.push(record)
    }

    // Generate performance data for each rule
    const performance: RulePerformance[] = []
    for (const [ruleId, ruleRecords] of rules) {
      const successfulRecords = ruleRecords.filter(r => r.success)
      const successRate = ruleRecords.length > 0 ? (successfulRecords.length / ruleRecords.length) * 100 : 0
      const averageImpact = this.calculateAverageImpact(successfulRecords).performanceChange
      const totalImpact = successfulRecords.reduce((sum, r) => sum + r.impact.performanceChange, 0)
      const lastExecution = ruleRecords.reduce((latest, r) => 
        r.timestamp > latest ? r.timestamp : latest, new Date(0)
      )

      performance.push({
        ruleId,
        ruleName: ruleRecords[0].ruleName,
        executionCount: ruleRecords.length,
        successRate,
        averageImpact,
        totalImpact,
        lastExecution
      })
    }

    return performance.sort((a, b) => b.totalImpact - a.totalImpact).slice(0, 10)
  }

  /**
   * Calculate trends
   */
  private calculateTrends(records: ImpactRecord[]): ImpactTrends {
    const sortedRecords = records.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
    
    return {
      performanceTrend: this.calculateTrendData(sortedRecords, 'performanceChange'),
      costTrend: this.calculateTrendData(sortedRecords, 'costChange'),
      reliabilityTrend: this.calculateTrendData(sortedRecords, 'reliabilityChange'),
      optimizationFrequency: this.calculateFrequencyTrend(sortedRecords)
    }
  }

  /**
   * Calculate trend data for a specific metric
   */
  private calculateTrendData(records: ImpactRecord[], metric: keyof OptimizationImpact): TrendData {
    if (records.length < 2) {
      return {
        direction: 'stable',
        changeRate: 0,
        confidence: 0,
        dataPoints: []
      }
    }

    const dataPoints: DataPoint[] = records.map(record => ({
      timestamp: record.timestamp,
      value: record.impact[metric] as number
    }))

    // Simple linear regression to determine trend
    const n = dataPoints.length
    const sumX = dataPoints.reduce((sum, point, index) => sum + index, 0)
    const sumY = dataPoints.reduce((sum, point) => sum + point.value, 0)
    const sumXY = dataPoints.reduce((sum, point, index) => sum + index * point.value, 0)
    const sumXX = dataPoints.reduce((sum, point, index) => sum + index * index, 0)

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    const changeRate = slope * 100 // Convert to percentage

    let direction: 'improving' | 'degrading' | 'stable'
    if (Math.abs(changeRate) < 1) {
      direction = 'stable'
    } else if (changeRate > 0) {
      direction = 'improving'
    } else {
      direction = 'degrading'
    }

    // Calculate confidence based on data consistency
    const avgValue = sumY / n
    const variance = dataPoints.reduce((sum, point) => sum + Math.pow(point.value - avgValue, 2), 0) / n
    const confidence = Math.max(0, Math.min(100, 100 - (variance / Math.abs(avgValue)) * 100))

    return {
      direction,
      changeRate: Math.abs(changeRate),
      confidence,
      dataPoints
    }
  }

  /**
   * Calculate optimization frequency trend
   */
  private calculateFrequencyTrend(records: ImpactRecord[]): TrendData {
    // Group records by day
    const dailyCounts = new Map<string, number>()
    
    for (const record of records) {
      const day = record.timestamp.toISOString().split('T')[0]
      dailyCounts.set(day, (dailyCounts.get(day) || 0) + 1)
    }

    const dataPoints: DataPoint[] = Array.from(dailyCounts.entries()).map(([day, count]) => ({
      timestamp: new Date(day),
      value: count
    }))

    return this.calculateTrendData(records, 'performanceChange') // Reuse trend calculation logic
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(records: ImpactRecord[], trends: ImpactTrends): string[] {
    const recommendations: string[] = []

    // Performance recommendations
    if (trends.performanceTrend.direction === 'degrading') {
      recommendations.push('Performance trend is degrading - review recent optimizations')
    }

    // Success rate recommendations
    const successRate = records.filter(r => r.success).length / records.length * 100
    if (successRate < 80) {
      recommendations.push('Low optimization success rate - review rule conditions and thresholds')
    }

    // Category-specific recommendations
    const categoryBreakdown = this.generateCategoryBreakdown(records)
    for (const category of categoryBreakdown) {
      if (category.successRate < 70) {
        recommendations.push(`${category.category} optimizations have low success rate - review rules`)
      }
    }

    // Frequency recommendations
    if (trends.optimizationFrequency.direction === 'degrading') {
      recommendations.push('Optimization frequency is decreasing - check scheduler configuration')
    }

    return recommendations
  }

  /**
   * Generate detailed analysis
   */
  private generateDetailedAnalysis(records: ImpactRecord[]): DetailedAnalysis {
    const successfulRecords = records.filter(r => r.success)
    
    return {
      performanceAnalysis: {
        overallImprovement: this.calculateAverageImpact(successfulRecords).performanceChange,
        responseTimeImprovement: 0, // Would calculate from actual metrics
        throughputImprovement: 0,
        cacheEfficiencyImprovement: 0,
        topPerformanceGains: successfulRecords
          .sort((a, b) => b.impact.performanceChange - a.impact.performanceChange)
          .slice(0, 5),
        performanceRegressions: successfulRecords
          .filter(r => r.impact.performanceChange < 0)
          .sort((a, b) => a.impact.performanceChange - b.impact.performanceChange)
          .slice(0, 5)
      },
      costAnalysis: {
        totalCostSavings: successfulRecords.reduce((sum, r) => sum + r.impact.costChange, 0),
        bandwidthSavings: 0,
        storageSavings: 0,
        computeSavings: 0,
        costOptimizationOpportunities: []
      },
      reliabilityAnalysis: {
        errorRateImprovement: this.calculateAverageImpact(successfulRecords).reliabilityChange,
        uptimeImprovement: 0,
        stabilityMetrics: {},
        reliabilityIssues: []
      },
      categoryAnalysis: this.generateCategoryBreakdown(records).map(category => ({
        category: category.category,
        impact: { performanceChange: category.averageImpact, costChange: 0, reliabilityChange: 0, metrics: {} },
        rulePerformance: [],
        insights: []
      }))
    }
  }

  /**
   * Generate visualizations
   */
  private generateVisualizations(records: ImpactRecord[]): VisualizationData[] {
    return [
      {
        type: 'line',
        title: 'Performance Impact Over Time',
        data: records.map(r => ({
          timestamp: r.timestamp,
          impact: r.impact.performanceChange
        })),
        config: { xAxis: 'timestamp', yAxis: 'impact' }
      },
      {
        type: 'pie',
        title: 'Optimizations by Category',
        data: this.generateCategoryBreakdown(records).map(c => ({
          category: c.category,
          count: c.count
        })),
        config: { labelField: 'category', valueField: 'count' }
      }
    ]
  }

  /**
   * Generate recommendation sections
   */
  private generateRecommendationSections(records: ImpactRecord[], trends: ImpactTrends): RecommendationSection[] {
    return [
      {
        title: 'Performance Optimization',
        priority: 'high',
        recommendations: this.generateRecommendations(records, trends).slice(0, 3),
        expectedImpact: 'High'
      }
    ]
  }

  /**
   * Generate report appendix
   */
  private generateReportAppendix(): ReportAppendix {
    return {
      methodology: 'Impact tracking based on before/after metric comparison',
      dataQuality: {
        completeness: 95,
        accuracy: 90,
        timeliness: 98,
        consistency: 92
      },
      limitations: [
        'Metrics collection may have brief gaps during system maintenance',
        'Some optimizations may have delayed impact not captured in immediate measurements'
      ],
      glossary: {
        'Performance Change': 'Percentage change in response time (positive = improvement)',
        'Cost Change': 'Percentage change in operational costs (positive = savings)',
        'Reliability Change': 'Percentage change in error rate (positive = improvement)'
      }
    }
  }

  /**
   * Save report to file
   */
  private async saveReport(report: ImpactReport): Promise<void> {
    const filename = `impact-report-${report.id}.json`
    const filepath = join(this.config.reportsPath, filename)
    
    writeFileSync(filepath, JSON.stringify(report, null, 2))
    console.log(`📊 Impact report saved: ${filepath}`)
  }

  /**
   * Convert records to CSV format
   */
  private convertToCSV(records: ImpactRecord[]): string {
    const headers = [
      'ID', 'Timestamp', 'Rule ID', 'Rule Name', 'Category', 'Success',
      'Performance Change', 'Cost Change', 'Reliability Change', 'Duration'
    ]

    const rows = records.map(record => [
      record.id,
      record.timestamp.toISOString(),
      record.ruleId,
      record.ruleName,
      record.category,
      record.success,
      record.impact.performanceChange,
      record.impact.costChange,
      record.impact.reliabilityChange,
      record.duration
    ])

    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }

  /**
   * Get current metric value (placeholder)
   */
  private async getCurrentMetric(metric: string): Promise<number> {
    // This would integrate with actual metrics collection
    const simulatedValues: Record<string, number> = {
      'responseTime': 500 + Math.random() * 1000,
      'errorRate': Math.random() * 5,
      'cacheHitRate': 70 + Math.random() * 30,
      'throughput': 100 + Math.random() * 200,
      'cpuUsage': 30 + Math.random() * 40,
      'memoryUsage': 40 + Math.random() * 30,
      'bandwidthUsage': 50 + Math.random() * 50,
      'cost': 100 + Math.random() * 50
    }

    return simulatedValues[metric] || 0
  }

  /**
   * Get custom metrics
   */
  private async getCustomMetrics(): Promise<Record<string, number>> {
    return {
      'workerExecutions': Math.floor(Math.random() * 1000),
      'r2Operations': Math.floor(Math.random() * 500),
      'kvOperations': Math.floor(Math.random() * 2000)
    }
  }

  /**
   * Generate category statistics
   */
  private generateCategoryStats(): Record<string, any> {
    const categories = new Map<string, ImpactRecord[]>()
    
    for (const record of this.impactRecords) {
      if (!categories.has(record.category)) {
        categories.set(record.category, [])
      }
      categories.get(record.category)!.push(record)
    }

    const stats: Record<string, any> = {}
    for (const [category, records] of categories) {
      const successfulRecords = records.filter(r => r.success)
      stats[category] = {
        total: records.length,
        successful: successfulRecords.length,
        successRate: records.length > 0 ? (successfulRecords.length / records.length) * 100 : 0,
        averageImpact: this.calculateAverageImpact(successfulRecords).performanceChange
      }
    }

    return stats
  }

  /**
   * Generate rule statistics
   */
  private generateRuleStats(): Record<string, any> {
    const rules = new Map<string, ImpactRecord[]>()
    
    for (const record of this.impactRecords) {
      if (!rules.has(record.ruleId)) {
        rules.set(record.ruleId, [])
      }
      rules.get(record.ruleId)!.push(record)
    }

    const stats: Record<string, any> = {}
    for (const [ruleId, records] of rules) {
      const successfulRecords = records.filter(r => r.success)
      stats[ruleId] = {
        total: records.length,
        successful: successfulRecords.length,
        successRate: records.length > 0 ? (successfulRecords.length / records.length) * 100 : 0,
        averageImpact: this.calculateAverageImpact(successfulRecords).performanceChange,
        lastExecution: records.reduce((latest, r) => r.timestamp > latest ? r.timestamp : latest, new Date(0))
      }
    }

    return stats
  }

  /**
   * Generate unique record ID
   */
  private generateRecordId(): string {
    return `impact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Generate unique report ID
   */
  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Generate tags for record
   */
  private generateTags(category: string, result: OptimizationResult): string[] {
    const tags = [category]
    
    if (result.success) {
      tags.push('success')
    } else {
      tags.push('failed')
    }
    
    if (result.rollbackExecuted) {
      tags.push('rollback')
    }
    
    if (result.impact.performanceChange > 10) {
      tags.push('high-impact')
    }
    
    return tags
  }
}

// Supporting interfaces
export interface TrackerConfig {
  maxRecords: number
  exportPath: string
  reportsPath: string
  enableRealTimeTracking: boolean
}

export interface OptimizationStats {
  totalOptimizations: number
  successfulOptimizations: number
  successRate: number
  averagePerformanceImpact: number
  averageCostImpact: number
  averageReliabilityImpact: number
  categoryStats: Record<string, any>
  ruleStats: Record<string, any>
  lastOptimization: Date | null
}
