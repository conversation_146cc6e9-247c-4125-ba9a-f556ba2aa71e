import { NextApiRequest, NextApiResponse } from 'next'
import { getAuth } from 'firebase-admin/auth'
import { adminAuth } from '../../../../lib/firebase/admin'

export interface SystemStatus {
  overall: 'healthy' | 'warning' | 'critical'
  workers: 'online' | 'degraded' | 'offline'
  r2Storage: 'healthy' | 'warning' | 'error'
  firebase: 'connected' | 'degraded' | 'disconnected'
  optimization: 'active' | 'paused' | 'error'
  lastUpdated: Date
}

/**
 * Dashboard System Status API
 * Returns current status of all hybrid deployment components
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify authentication
    const authHeader = req.headers.authorization
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    const token = authHeader.split('Bearer ')[1]
    const decodedToken = await adminAuth.verifyIdToken(token)
    
    // Verify admin role
    if (!decodedToken.admin) {
      return res.status(403).json({ error: 'Admin access required' })
    }

    // Check Cloudflare Workers status
    const workersStatus = await checkWorkersStatus()
    
    // Check R2 Storage status
    const r2Status = await checkR2StorageStatus()
    
    // Check Firebase status
    const firebaseStatus = await checkFirebaseStatus()
    
    // Check optimization engine status
    const optimizationStatus = await checkOptimizationStatus()
    
    // Determine overall system status
    const overall = determineOverallStatus([
      workersStatus,
      r2Status,
      firebaseStatus,
      optimizationStatus
    ])

    const systemStatus: SystemStatus = {
      overall,
      workers: workersStatus,
      r2Storage: r2Status,
      firebase: firebaseStatus,
      optimization: optimizationStatus,
      lastUpdated: new Date()
    }

    // Cache response for 30 seconds
    res.setHeader('Cache-Control', 'public, s-maxage=30, stale-while-revalidate=60')
    
    return res.status(200).json(systemStatus)

  } catch (error) {
    console.error('Dashboard status API error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Check Cloudflare Workers status
 */
async function checkWorkersStatus(): Promise<'online' | 'degraded' | 'offline'> {
  try {
    // Check image optimization worker
    const imageWorkerResponse = await fetch(`${process.env.CLOUDFLARE_WORKER_IMAGE_URL}/health`, {
      method: 'GET',
      timeout: 5000
    })

    // Check API cache worker
    const apiWorkerResponse = await fetch(`${process.env.CLOUDFLARE_WORKER_API_URL}/health`, {
      method: 'GET',
      timeout: 5000
    })

    const imageWorkerOk = imageWorkerResponse.ok
    const apiWorkerOk = apiWorkerResponse.ok

    if (imageWorkerOk && apiWorkerOk) {
      return 'online'
    } else if (imageWorkerOk || apiWorkerOk) {
      return 'degraded'
    } else {
      return 'offline'
    }
  } catch (error) {
    console.error('Workers status check failed:', error)
    return 'offline'
  }
}

/**
 * Check R2 Storage status
 */
async function checkR2StorageStatus(): Promise<'healthy' | 'warning' | 'error'> {
  try {
    // Check R2 bucket accessibility
    const response = await fetch(`${process.env.CLOUDFLARE_R2_ENDPOINT}/syndicaps-images?list-type=2&max-keys=1`, {
      method: 'GET',
      headers: {
        'Authorization': `AWS4-HMAC-SHA256 Credential=${process.env.CLOUDFLARE_R2_ACCESS_KEY_ID}`,
        'X-Amz-Content-Sha256': 'UNSIGNED-PAYLOAD'
      },
      timeout: 5000
    })

    if (response.ok) {
      return 'healthy'
    } else if (response.status >= 400 && response.status < 500) {
      return 'warning'
    } else {
      return 'error'
    }
  } catch (error) {
    console.error('R2 storage status check failed:', error)
    return 'error'
  }
}

/**
 * Check Firebase status
 */
async function checkFirebaseStatus(): Promise<'connected' | 'degraded' | 'disconnected'> {
  try {
    // Test Firebase Admin SDK connection
    await adminAuth.listUsers(1)
    
    // Test Firestore connection
    const { getFirestore } = await import('firebase-admin/firestore')
    const db = getFirestore()
    await db.collection('_health').limit(1).get()

    return 'connected'
  } catch (error) {
    console.error('Firebase status check failed:', error)
    
    // Check if it's a temporary issue
    if (error.code === 'unavailable' || error.code === 'deadline-exceeded') {
      return 'degraded'
    }
    
    return 'disconnected'
  }
}

/**
 * Check optimization engine status
 */
async function checkOptimizationStatus(): Promise<'active' | 'paused' | 'error'> {
  try {
    // Check if optimization scheduler is running
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/admin/optimization/status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.INTERNAL_API_TOKEN}`
      },
      timeout: 5000
    })

    if (response.ok) {
      const data = await response.json()
      return data.status === 'running' ? 'active' : 'paused'
    } else {
      return 'error'
    }
  } catch (error) {
    console.error('Optimization status check failed:', error)
    return 'error'
  }
}

/**
 * Determine overall system status based on component statuses
 */
function determineOverallStatus(statuses: string[]): 'healthy' | 'warning' | 'critical' {
  const criticalStatuses = ['offline', 'disconnected', 'error']
  const warningStatuses = ['degraded', 'warning', 'paused']

  // If any component is critical, overall is critical
  if (statuses.some(status => criticalStatuses.includes(status))) {
    return 'critical'
  }

  // If any component has warnings, overall is warning
  if (statuses.some(status => warningStatuses.includes(status))) {
    return 'warning'
  }

  // All components are healthy
  return 'healthy'
}
