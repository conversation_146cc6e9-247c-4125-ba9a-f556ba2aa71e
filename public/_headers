# Syndicaps Cloudflare Pages Headers Configuration
# Enhanced for hybrid deployment with security, performance, and caching

# Global security headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://js.sentry-cdn.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.syndicaps.com https://firebaseapp.com https://*.firebaseio.com https://www.google-analytics.com https://sentry.io; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  X-DNS-Prefetch-Control: on
  X-Permitted-Cross-Domain-Policies: none

# Static assets - long cache with immutable
/*.js
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000
  Vary: Accept-Encoding

/*.css
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000
  Vary: Accept-Encoding

/*.woff
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000
  Access-Control-Allow-Origin: *

/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000
  Access-Control-Allow-Origin: *

/*.ttf
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000
  Access-Control-Allow-Origin: *

/*.eot
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000
  Access-Control-Allow-Origin: *

/*.ico
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000

# Images - medium cache with optimization
/*.jpg
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800
  Vary: Accept

/*.jpeg
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800
  Vary: Accept

/*.png
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800
  Vary: Accept

/*.gif
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800

/*.webp
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800

/*.avif
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800

/*.svg
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800

# HTML pages - short cache
/*.html
  Cache-Control: public, max-age=3600
  CDN-Cache-Control: public, max-age=3600

# API routes - very short cache with CORS
/api/*
  Cache-Control: public, max-age=0, s-maxage=300
  CDN-Cache-Control: public, max-age=300
  Vary: Accept-Encoding, Authorization
  Access-Control-Allow-Origin: https://syndicaps.com
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-User-ID, X-User-Type
  Access-Control-Max-Age: 86400
  Access-Control-Allow-Credentials: true

# Admin routes - no cache, enhanced security
/admin/*
  Cache-Control: private, no-cache, no-store, must-revalidate
  CDN-Cache-Control: no-cache
  Pragma: no-cache
  Expires: 0
  X-Robots-Tag: noindex, nofollow
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

# Service Worker - edge cache only
/sw.js
  Cache-Control: public, max-age=0, s-maxage=86400
  CDN-Cache-Control: public, max-age=86400
  Service-Worker-Allowed: /

# Manifest - medium cache
/manifest.json
  Cache-Control: public, max-age=86400
  CDN-Cache-Control: public, max-age=86400
  Content-Type: application/manifest+json

# Additional routes and optimizations
/robots.txt
  Cache-Control: public, max-age=86400
  Content-Type: text/plain

/sitemap.xml
  Cache-Control: public, max-age=86400
  Content-Type: application/xml

# Profile routes - no cache for dynamic content
/profile/*
  Cache-Control: private, no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Authentication routes - no cache
/auth/*
  Cache-Control: private, no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Cart and checkout - no cache
/cart
  Cache-Control: private, no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

/checkout/*
  Cache-Control: private, no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Next.js specific files
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable
  CDN-Cache-Control: public, max-age=31536000

/_next/image*
  Cache-Control: public, max-age=604800
  CDN-Cache-Control: public, max-age=604800
  Vary: Accept

# Error pages
/404.html
  Cache-Control: public, max-age=3600

/500.html
  Cache-Control: public, max-age=300

# Feature flag endpoints
/api/feature-flags/*
  Cache-Control: private, no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Health check endpoints
/api/health
  Cache-Control: public, max-age=60

# Metrics and monitoring
/api/metrics/*
  Cache-Control: private, no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Cloudflare R2 integration
/uploads/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, HEAD
  Cache-Control: public, max-age=2592000
