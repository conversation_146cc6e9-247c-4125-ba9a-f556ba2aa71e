# Syndicaps Cloudflare Pages Redirects Configuration
# Enhanced for hybrid deployment with security, SEO, and performance

# Force HTTPS
http://syndicaps.com/* https://syndicaps.com/:splat 301!
http://www.syndicaps.com/* https://syndicaps.com/:splat 301!

# Canonical domain (www to non-www)
https://www.syndicaps.com/* https://syndicaps.com/:splat 301!

# Legacy URL redirects
/products/* /shop/:splat 301
/product/* /shop/:splat 301
/store/* /shop/:splat 301
/user/* /profile/:splat 301
/account/* /profile/:splat 301
/my-account/* /profile/:splat 301

# Admin panel redirects
/dashboard/* /admin/:splat 301
/management/* /admin/:splat 301
/control-panel/* /admin/:splat 301
/backend/* /admin/:splat 301

# Authentication redirects
/login /auth/login 301
/register /auth/register 301
/signup /auth/register 301
/signin /auth/login 301
/logout /auth/logout 301

# Community redirects
/forum/* /community/:splat 301
/discussions/* /community/:splat 301
/leaderboard /community/leaderboard 301

# Security redirects - block common attack vectors
/wp-admin/* /404 404
/wp-login.php /404 404
/wp-content/* /404 404
/wordpress/* /404 404
/admin.php /404 404
/phpmyadmin/* /404 404
/phpMyAdmin/* /404 404
/.env /404 404
/.git/* /404 404
/config/* /404 404
/*.php /404 404
/*.asp /404 404
/*.aspx /404 404
/*.jsp /404 404

# API redirects - hybrid deployment with feature flags
/api/v1/* https://us-central1-syndicaps-fullpower.cloudfunctions.net/api/v1/:splat 200
/api/* https://us-central1-syndicaps-fullpower.cloudfunctions.net/api/:splat 200

# Cloudflare Workers routes (when enabled)
/worker/* /api/worker/:splat 200
/edge/* /api/edge/:splat 200

# Image optimization redirects (R2 storage)
/images/* https://syndicaps-images.r2.dev/:splat 200
/uploads/* https://syndicaps-images.r2.dev/uploads/:splat 200
/storage/* /api/storage/:splat 200
/img/* /api/images/:splat 200

# Feature flag management
/feature-flags /admin/system/feature-flags 301
/flags /admin/system/feature-flags 301

# Health checks and monitoring
/ping /api/health 200
/status /api/health 200
/health-check /api/health 200

# Cache management
/purge-cache /api/cache/purge 200
/clear-cache /api/cache/clear 200

# Social media and marketing redirects
/instagram https://instagram.com/syndicaps 302
/discord https://discord.gg/syndicaps 302
/twitter https://twitter.com/syndicaps 302
/facebook https://facebook.com/syndicaps 302

# SEO-friendly redirects
/keycaps /shop?category=keycaps 302
/keyboards /shop?category=keyboards 302
/switches /shop?category=switches 302
/accessories /shop?category=accessories 302

# Support and legal redirects
/help /support 301
/contact-us /contact 301
/faq /support/faq 301
/terms-of-service /legal/terms 301
/privacy-policy /legal/privacy 301
/tos /legal/terms 301

# Gamification redirects
/points /profile/points 301
/badges /profile/badges 301
/achievements /profile/achievements 301
/rewards /profile/rewards 301

# E-commerce redirects
/cart /shop/cart 301
/checkout /shop/checkout 301
/orders /profile/orders 301
/order-history /profile/orders 301

# Analytics and metrics (admin only)
/analytics /admin/analytics 301
/metrics /admin/metrics 301
/stats /admin/analytics 301

# Newsletter
/subscribe /newsletter 301
/unsubscribe /newsletter/unsubscribe 301

# Sitemap and robots
/sitemap /sitemap.xml 301
/robots /robots.txt 301

# Error page redirects
/404 /not-found 200
/500 /error 200
/error /404 404
/not-found /404 404
/server-error /500 500

# Maintenance mode (commented out by default)
# /* /maintenance.html 503

# SPA fallback for client-side routing - MUST BE LAST
/* /index.html 200
