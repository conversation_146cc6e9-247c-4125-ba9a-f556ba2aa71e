<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Syndicaps</title>
    <meta name="description" content="You're currently offline. Your progress is saved and will sync when you're back online.">
    
    <!-- Theme colors -->
    <meta name="theme-color" content="#a855f7">
    <meta name="msapplication-TileColor" content="#a855f7">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: #f9fafb;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .container {
            max-width: 500px;
            width: 100%;
            text-align: center;
            padding: 2rem;
            background: rgba(31, 41, 55, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            border: 1px solid rgba(75, 85, 99, 0.3);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #a855f7, #ec4899);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subtitle {
            font-size: 1.25rem;
            color: #d1d5db;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .description {
            font-size: 1rem;
            color: #9ca3af;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .features {
            text-align: left;
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(55, 65, 81, 0.5);
            border-radius: 0.75rem;
            border: 1px solid rgba(75, 85, 99, 0.3);
        }

        .features h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #f3f4f6;
        }

        .features ul {
            list-style: none;
        }

        .features li {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            color: #d1d5db;
        }

        .features li:before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            margin-right: 0.75rem;
            font-size: 1rem;
        }

        .buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.875rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 1rem;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(168, 85, 247, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px 0 rgba(168, 85, 247, 0.5);
        }

        .btn-secondary {
            background: rgba(75, 85, 99, 0.8);
            color: #f3f4f6;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(75, 85, 99, 1);
            border-color: rgba(156, 163, 175, 0.5);
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 0.5rem;
            font-size: 0.875rem;
            color: #fca5a5;
        }

        .status-dot {
            width: 0.5rem;
            height: 0.5rem;
            background: #ef4444;
            border-radius: 50%;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .sync-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.875rem;
            color: #93c5fd;
        }

        @media (max-width: 480px) {
            .container {
                padding: 1.5rem;
                margin: 1rem;
            }

            .title {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1.1rem;
            }

            .buttons {
                gap: 0.75rem;
            }
        }

        /* Dark mode detection */
        @media (prefers-color-scheme: light) {
            body {
                background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
                color: #111827;
            }

            .container {
                background: rgba(255, 255, 255, 0.9);
                border-color: rgba(209, 213, 219, 0.5);
                color: #111827;
            }

            .subtitle {
                color: #4b5563;
            }

            .description {
                color: #6b7280;
            }

            .features {
                background: rgba(249, 250, 251, 0.8);
                border-color: rgba(209, 213, 219, 0.5);
            }

            .features h3 {
                color: #111827;
            }

            .features li {
                color: #4b5563;
            }

            .btn-secondary {
                background: rgba(249, 250, 251, 0.9);
                color: #374151;
                border-color: rgba(209, 213, 219, 0.5);
            }

            .btn-secondary:hover {
                background: #f9fafb;
                border-color: rgba(209, 213, 219, 0.8);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        
        <h1 class="title">You're Offline</h1>
        
        <p class="subtitle">
            No internet connection detected
        </p>
        
        <p class="description">
            Don't worry! Your achievements, challenge progress, and points are saved locally and will automatically sync when you're back online.
        </p>

        <div class="features">
            <h3>✨ Offline Features Available:</h3>
            <ul>
                <li>View your profile and achievements</li>
                <li>Continue working on challenges</li>
                <li>Track your progress locally</li>
                <li>Browse rewards catalog</li>
                <li>Review leaderboards (cached data)</li>
            </ul>
        </div>

        <div class="sync-info">
            <strong>🔄 Auto-Sync:</strong> Any progress you make will be automatically synced when your connection returns.
        </div>

        <div class="status">
            <div class="status-dot"></div>
            <span id="status-text">Offline Mode Active</span>
        </div>

        <div class="buttons">
            <button class="btn btn-primary" onclick="checkConnection()">
                <span>🔄</span>
                Try Again
            </button>
            
            <a href="/" class="btn btn-secondary">
                <span>🏠</span>
                Go to Home
            </a>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusText = document.getElementById('status-text');
            const statusDot = document.querySelector('.status-dot');
            
            if (navigator.onLine) {
                statusText.textContent = 'Connection Restored';
                statusDot.style.background = '#10b981';
                statusDot.style.animation = 'none';
                
                // Reload page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                statusText.textContent = 'Offline Mode Active';
                statusDot.style.background = '#ef4444';
                statusDot.style.animation = 'blink 1s infinite';
            }
        }

        // Check connection button
        function checkConnection() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<span>⏳</span>Checking...';
            btn.disabled = true;
            
            setTimeout(() => {
                updateConnectionStatus();
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 1000);
        }

        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);

        // Service worker check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                console.log('Service Worker is ready');
            });
        }

        // Show sync queue status if available
        function checkSyncQueue() {
            const syncData = localStorage.getItem('sync-queue');
            if (syncData) {
                try {
                    const queue = JSON.parse(syncData);
                    if (queue.length > 0) {
                        const syncInfo = document.querySelector('.sync-info');
                        syncInfo.innerHTML = `
                            <strong>🔄 Auto-Sync:</strong> ${queue.length} items queued for sync when online.
                        `;
                    }
                } catch (e) {
                    console.log('No sync queue data');
                }
            }
        }

        checkSyncQueue();
    </script>
</body>
</html>