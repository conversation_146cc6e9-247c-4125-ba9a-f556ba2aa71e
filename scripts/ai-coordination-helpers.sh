#!/bin/bash

# AI Coordination Helper Functions

# Check current work claims
check-claims() {
    echo "🔍 Current Work Claims:"
    echo "====================="
    if [ -f ".ai-coordination.md" ]; then
        grep -A 10 "## Current Work Claims" .ai-coordination.md
    else
        echo "❌ .ai-coordination.md not found!"
    fi
}

# Claim work area
claim-work() {
    if [ -z "$1" ]; then
        echo "Usage: claim-work 'description of work'"
        return 1
    fi
    
    echo "📝 Claiming work: $1"
    echo "⏰ Estimated completion: $(date -d '+2 hours' '+%Y-%m-%d %H:%M')"
    echo ""
    echo "Please manually update .ai-coordination.md with your claim!"
}

# Quick status check
ai-status() {
    echo "🤖 AI Coordination Status"
    echo "========================="
    echo ""
    
    echo "📋 Current Branch: $(git branch --show-current)"
    echo "📊 Uncommitted Changes: $(git status --porcelain | wc -l) files"
    echo ""
    
    if [ -f ".ai-coordination.md" ]; then
        echo "🔍 Active Claims:"
        grep -A 5 "Currently working on:" .ai-coordination.md | grep -v "Currently working on:" | head -5
    fi
    
    echo ""
    echo "📝 Recent AI Commits:"
    git log --oneline -5 --grep="\[AUGMENT\]\|\[CURSOR\]\|\[COLLAB\]"
}

# Update work log
update-log() {
    if [ -z "$1" ]; then
        echo "Usage: update-log 'progress update'"
        return 1
    fi
    
    echo "📝 Adding to work log: $1"
    echo "$(date '+%Y-%m-%d %H:%M') - $1" >> AI_WORK_LOG.md
    echo "✅ Work log updated!"
}

# Show handoff template
handoff-template() {
    echo "📋 Handoff Template:"
    echo "==================="
    echo ""
    echo "## Handoff: [Feature Name]"
    echo "**From**: [Your AI]"
    echo "**To**: [Target AI]"
    echo "**Date**: $(date '+%Y-%m-%d %H:%M')"
    echo ""
    echo "### Completed Work"
    echo "- [List what you finished]"
    echo ""
    echo "### Next Steps"
    echo "- [What needs to be done next]"
    echo ""
    echo "### Files Modified"
    echo "- [Key files changed]"
    echo ""
    echo "### Important Notes"
    echo "- [Any gotchas or important context]"
}

# Export functions
export -f check-claims
export -f claim-work
export -f ai-status
export -f update-log
export -f handoff-template
