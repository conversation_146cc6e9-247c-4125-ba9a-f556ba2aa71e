#!/bin/bash

# Augment Agent Configuration Script
# Applies the comprehensive system prompt and validates setup

echo "🤖 Configuring Augment Agent for Syndicaps Project..."
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "src" ]; then
    echo "❌ Error: This script must be run from the Syndicaps project root directory"
    exit 1
fi

# Validate system prompt exists
if [ ! -f "SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md" ]; then
    echo "❌ Error: System prompt file not found!"
    echo "Please ensure SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md exists in the project root"
    exit 1
fi

echo "✅ System prompt file found"

# Validate coordination system
echo "🔍 Validating AI coordination system..."

REQUIRED_FILES=(
    ".ai-coordination.md"
    "AI_WORK_LOG.md"
    "HANDOFF_NOTES.md"
    "scripts/ai-coordination-helpers.sh"
    "AUGMENT_AGENT_QUICK_REFERENCE.md"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Missing required file: $file"
        echo "Please run the AI coordination setup first"
        exit 1
    fi
done

echo "✅ All coordination files present"

# Check Git configuration
echo "🔧 Validating Git configuration..."

# Check if Git aliases are configured
if ! git config --get alias.augment-feat > /dev/null; then
    echo "⚠️ Git aliases not configured. Running setup..."
    if [ -f "scripts/setup-ai-coordination.sh" ]; then
        chmod +x scripts/setup-ai-coordination.sh
        ./scripts/setup-ai-coordination.sh
    else
        echo "❌ Setup script not found!"
        exit 1
    fi
fi

echo "✅ Git configuration validated"

# Validate project structure
echo "📁 Validating project structure..."

EXPECTED_DIRS=(
    "src/lib"
    "src/components"
    "src/contexts"
    "src/hooks"
    "src/types"
    "app/admin"
    "functions"
    "docs"
    "scripts"
    "tests"
)

for dir in "${EXPECTED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        echo "⚠️ Directory not found: $dir (this may be normal for new projects)"
    fi
done

echo "✅ Project structure validated"

# Create configuration summary
echo "📋 Creating configuration summary..."

cat > AUGMENT_AGENT_CONFIG_STATUS.md << 'EOF'
# Augment Agent Configuration Status

## Configuration Applied
- **Date**: $(date '+%Y-%m-%d %H:%M:%S')
- **System Prompt**: SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md
- **Quick Reference**: AUGMENT_AGENT_QUICK_REFERENCE.md
- **Coordination System**: Fully configured

## Validated Components
- [x] System prompt file
- [x] AI coordination system
- [x] Git aliases and templates
- [x] Project structure
- [x] Helper functions

## Ready for Operation
The Augment Agent is now configured with:
- Comprehensive project context awareness
- Automatic task classification
- AI coordination protocols
- Quality standards and workflows
- Emergency response procedures

## Next Steps
1. Review system prompt for any project-specific adjustments
2. Test coordination workflow with Claude Code
3. Begin development following established protocols

## Quick Commands
```bash
# Check coordination status
source scripts/ai-coordination-helpers.sh
ai-status

# Start new work
check-claims
git augment-branch feature-name

# View system prompt
cat SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md

# View quick reference
cat AUGMENT_AGENT_QUICK_REFERENCE.md
```
EOF

# Replace the date placeholder
sed -i.bak "s/\$(date '+%Y-%m-%d %H:%M:%S')/$(date '+%Y-%m-%d %H:%M:%S')/g" AUGMENT_AGENT_CONFIG_STATUS.md
rm AUGMENT_AGENT_CONFIG_STATUS.md.bak 2>/dev/null || true

echo "✅ Configuration summary created"

# Test helper functions
echo "🧪 Testing helper functions..."

if source scripts/ai-coordination-helpers.sh 2>/dev/null; then
    echo "✅ Helper functions loaded successfully"
    
    # Test ai-status function
    if command -v ai-status > /dev/null; then
        echo "✅ ai-status function available"
    else
        echo "⚠️ ai-status function not available"
    fi
    
    # Test check-claims function
    if command -v check-claims > /dev/null; then
        echo "✅ check-claims function available"
    else
        echo "⚠️ check-claims function not available"
    fi
else
    echo "⚠️ Helper functions could not be loaded"
fi

# Validate system prompt content
echo "📖 Validating system prompt content..."

REQUIRED_SECTIONS=(
    "Identity and Role Definition"
    "Project Context: Syndicaps Platform"
    "AI Coordination Protocols"
    "Task Classification Matrix"
    "Default Action Protocols"
    "Development Approach Preferences"
    "Context Retention"
)

for section in "${REQUIRED_SECTIONS[@]}"; do
    if grep -q "$section" SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md; then
        echo "✅ Section found: $section"
    else
        echo "⚠️ Section missing: $section"
    fi
done

# Create activation checklist
echo "📝 Creating activation checklist..."

cat > AUGMENT_AGENT_ACTIVATION_CHECKLIST.md << 'EOF'
# Augment Agent Activation Checklist

Use this checklist when starting work on any Syndicaps development task:

## Pre-Work Validation
- [ ] System prompt reviewed and understood
- [ ] Project context is clear
- [ ] Task classification completed
- [ ] Coordination status checked

## Coordination Protocol
- [ ] Checked `.ai-coordination.md` for conflicts
- [ ] Claimed work area with timeline
- [ ] Created appropriate branch (`git augment-branch name`)
- [ ] Helper functions loaded (`source scripts/ai-coordination-helpers.sh`)

## Work Execution
- [ ] Gathered comprehensive context (codebase-retrieval if needed)
- [ ] Planned approach following established preferences
- [ ] Using proper commit format (`[AUGMENT] type: description`)
- [ ] Updating progress in work log for major milestones

## Quality Assurance
- [ ] Following Syndicaps documentation standards
- [ ] Implementing proper error handling
- [ ] Prioritizing system stability
- [ ] Testing thoroughly before completion

## Handoff Preparation (if needed)
- [ ] Completed logical units of work
- [ ] Prepared handoff documentation
- [ ] Updated coordination files
- [ ] Ready for Claude Code continuation

## Completion
- [ ] Removed work claims
- [ ] Updated final progress in work log
- [ ] Documentation reflects changes
- [ ] All tests passing

---

**Remember**: Always prioritize crash prevention and system stability. When in doubt, refer to the full system prompt or quick reference guide.
EOF

echo "✅ Activation checklist created"

# Final validation and summary
echo ""
echo "🎉 Augment Agent Configuration Complete!"
echo "======================================="
echo ""
echo "📋 Configuration Summary:"
echo "✅ System prompt: SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md"
echo "✅ Quick reference: AUGMENT_AGENT_QUICK_REFERENCE.md"
echo "✅ Activation checklist: AUGMENT_AGENT_ACTIVATION_CHECKLIST.md"
echo "✅ Configuration status: AUGMENT_AGENT_CONFIG_STATUS.md"
echo "✅ AI coordination system: Fully operational"
echo "✅ Git aliases: Configured and ready"
echo "✅ Helper functions: Available"
echo ""
echo "🚀 Ready for Development!"
echo ""
echo "📖 Quick Start:"
echo "1. Review system prompt: cat SYNDICAPS_AUGMENT_AGENT_SYSTEM_PROMPT.md"
echo "2. Check coordination: source scripts/ai-coordination-helpers.sh && ai-status"
echo "3. Start work: Follow activation checklist"
echo ""
echo "🔧 Helper Commands:"
echo "source scripts/ai-coordination-helpers.sh  # Load helper functions"
echo "check-claims                                # View current work claims"
echo "ai-status                                   # Quick coordination status"
echo "git augment-feat 'description'             # Make feature commit"
echo ""
echo "✅ Augment Agent is now fully configured and ready for Syndicaps development!"
