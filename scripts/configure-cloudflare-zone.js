#!/usr/bin/env node

/**
 * Cloudflare Zone Configuration Script for Syndicaps
 * Configures DNS records, SSL settings, and performance optimizations
 */

const https = require('https');
const { promisify } = require('util');

// Validate environment variables first
const zoneId = process.env.CLOUDFLARE_ZONE_ID;
const apiToken = process.env.CLOUDFLARE_API_TOKEN;
const accountId = process.env.CLOUDFLARE_ACCOUNT_ID;

if (!zoneId || !apiToken) {
  console.error('❌ Missing required environment variables:');
  console.error('   CLOUDFLARE_ZONE_ID');
  console.error('   CLOUDFLARE_API_TOKEN');
  process.exit(1);
}

// Configuration - now guaranteed to have valid values
const config = {
  domain: 'syndicaps.com',
  zoneId: zoneId,
  apiToken: apiToken,
  accountId: accountId,
};

// API helper function
async function cloudflareAPI(method, endpoint, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.cloudflare.com',
      port: 443,
      path: `/client/v4${endpoint}`,
      method: method,
      headers: {
        'Authorization': `Bearer ${config.apiToken}`,
        'Content-Type': 'application/json',
      },
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (response.success) {
            resolve(response.result);
          } else {
            reject(new Error(`API Error: ${JSON.stringify(response.errors)}`));
          }
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// DNS Records to create/update
const dnsRecords = [
  {
    type: 'A',
    name: config.domain,
    content: '*********', // Placeholder - will be updated by Cloudflare
    proxied: true,
    comment: 'Main domain A record'
  },
  {
    type: 'A',
    name: `www.${config.domain}`,
    content: '*********', // Placeholder - will be updated by Cloudflare
    proxied: true,
    comment: 'WWW subdomain A record'
  },
  {
    type: 'CNAME',
    name: `api.${config.domain}`,
    content: config.domain,
    proxied: true,
    comment: 'API subdomain for hybrid deployment'
  },
  {
    type: 'CNAME',
    name: `cdn.${config.domain}`,
    content: config.domain,
    proxied: true,
    comment: 'CDN subdomain for asset delivery'
  },
  {
    type: 'CNAME',
    name: `images.${config.domain}`,
    content: config.domain,
    proxied: true,
    comment: 'Images subdomain for R2 storage'
  },
  {
    type: 'CNAME',
    name: `assets.${config.domain}`,
    content: config.domain,
    proxied: true,
    comment: 'Assets subdomain for static files'
  },
];

// Zone settings to configure
const zoneSettings = [
  {
    id: 'ssl',
    value: 'full'
  },
  {
    id: 'always_use_https',
    value: 'on'
  },
  {
    id: 'min_tls_version',
    value: '1.2'
  },
  {
    id: 'tls_1_3',
    value: 'on'
  },
  {
    id: 'automatic_https_rewrites',
    value: 'on'
  },
  {
    id: 'security_level',
    value: 'medium'
  },
  {
    id: 'challenge_ttl',
    value: 1800
  },
  {
    id: 'browser_check',
    value: 'on'
  },
  {
    id: 'hotlink_protection',
    value: 'off'
  },
  {
    id: 'server_side_exclude',
    value: 'on'
  },
  {
    id: 'development_mode',
    value: 'off'
  },
  {
    id: 'cache_level',
    value: 'aggressive'
  },
  {
    id: 'browser_cache_ttl',
    value: 14400
  },
  {
    id: 'sort_query_string_for_cache',
    value: 'off'
  },
  {
    id: 'always_online',
    value: 'on'
  },
  {
    id: 'minify',
    value: {
      css: 'on',
      html: 'on',
      js: 'on'
    }
  },
  {
    id: 'brotli',
    value: 'on'
  },
  {
    id: 'early_hints',
    value: 'on'
  },
  {
    id: 'http2',
    value: 'on'
  },
  {
    id: 'http3',
    value: 'on'
  },
  {
    id: '0rtt',
    value: 'on'
  },
  {
    id: 'pseudo_ipv4',
    value: 'off'
  },
  {
    id: 'ip_geolocation',
    value: 'on'
  },
  {
    id: 'email_obfuscation',
    value: 'on'
  },
  {
    id: 'server_side_exclude',
    value: 'on'
  },
  {
    id: 'rocket_loader',
    value: 'off'
  },
  {
    id: 'mirage',
    value: 'off'
  },
  {
    id: 'polish',
    value: 'lossless'
  }
];

// Get existing DNS records
async function getExistingRecords() {
  console.log('🔍 Fetching existing DNS records...');
  try {
    const records = await cloudflareAPI('GET', `/zones/${config.zoneId}/dns_records`);
    console.log(`✅ Found ${records.length} existing DNS records`);
    return records;
  } catch (error) {
    console.error('❌ Failed to fetch DNS records:', error.message);
    throw error;
  }
}

// Create or update DNS record
async function createOrUpdateRecord(record, existingRecords) {
  const existing = existingRecords.find(r => 
    r.type === record.type && r.name === record.name
  );

  try {
    if (existing) {
      console.log(`🔄 Updating ${record.type} record for ${record.name}...`);
      await cloudflareAPI('PUT', `/zones/${config.zoneId}/dns_records/${existing.id}`, record);
      console.log(`✅ Updated ${record.type} record for ${record.name}`);
    } else {
      console.log(`➕ Creating ${record.type} record for ${record.name}...`);
      await cloudflareAPI('POST', `/zones/${config.zoneId}/dns_records`, record);
      console.log(`✅ Created ${record.type} record for ${record.name}`);
    }
  } catch (error) {
    console.error(`❌ Failed to create/update ${record.type} record for ${record.name}:`, error.message);
    throw error;
  }
}

// Configure zone settings
async function configureZoneSettings() {
  console.log('⚙️  Configuring zone settings...');
  
  for (const setting of zoneSettings) {
    try {
      console.log(`🔧 Setting ${setting.id}...`);
      await cloudflareAPI('PATCH', `/zones/${config.zoneId}/settings/${setting.id}`, {
        value: setting.value
      });
      console.log(`✅ Configured ${setting.id}`);
    } catch (error) {
      console.error(`❌ Failed to configure ${setting.id}:`, error.message);
      // Continue with other settings
    }
  }
}

// Enable HSTS
async function enableHSTS() {
  console.log('🔒 Configuring HSTS...');
  
  try {
    await cloudflareAPI('PATCH', `/zones/${config.zoneId}/settings/security_header`, {
      value: {
        strict_transport_security: {
          enabled: true,
          max_age: 31536000,
          include_subdomains: true,
          preload: true
        }
      }
    });
    console.log('✅ HSTS configured successfully');
  } catch (error) {
    console.error('❌ Failed to configure HSTS:', error.message);
  }
}

// Create page rules for enhanced caching
async function createPageRules() {
  console.log('📄 Creating page rules...');
  
  const pageRules = [
    {
      targets: [
        {
          target: 'url',
          constraint: {
            operator: 'matches',
            value: `${config.domain}/api/*`
          }
        }
      ],
      actions: [
        {
          id: 'cache_level',
          value: 'bypass'
        },
        {
          id: 'security_level',
          value: 'high'
        }
      ],
      priority: 1,
      status: 'active'
    },
    {
      targets: [
        {
          target: 'url',
          constraint: {
            operator: 'matches',
            value: `${config.domain}/admin/*`
          }
        }
      ],
      actions: [
        {
          id: 'cache_level',
          value: 'bypass'
        },
        {
          id: 'security_level',
          value: 'high'
        },
        {
          id: 'disable_apps',
          value: true
        }
      ],
      priority: 2,
      status: 'active'
    },
    {
      targets: [
        {
          target: 'url',
          constraint: {
            operator: 'matches',
            value: `${config.domain}/*.css`
          }
        }
      ],
      actions: [
        {
          id: 'cache_level',
          value: 'cache_everything'
        },
        {
          id: 'edge_cache_ttl',
          value: 31536000
        }
      ],
      priority: 3,
      status: 'active'
    }
  ];

  for (const rule of pageRules) {
    try {
      await cloudflareAPI('POST', `/zones/${config.zoneId}/pagerules`, rule);
      console.log(`✅ Created page rule for ${rule.targets[0].constraint.value}`);
    } catch (error) {
      console.error(`❌ Failed to create page rule:`, error.message);
    }
  }
}

// Main configuration function
async function main() {
  console.log('🚀 Starting Cloudflare zone configuration for', config.domain);
  console.log('================================================');
  
  try {
    // Verify zone access
    console.log('🔍 Verifying zone access...');
    const zone = await cloudflareAPI('GET', `/zones/${config.zoneId}`);
    console.log(`✅ Zone verified: ${zone.name} (${zone.status})`);
    
    // Configure DNS records
    console.log('\n📡 Configuring DNS records...');
    const existingRecords = await getExistingRecords();
    
    for (const record of dnsRecords) {
      await createOrUpdateRecord(record, existingRecords);
    }
    
    // Configure zone settings
    console.log('\n⚙️  Configuring zone settings...');
    await configureZoneSettings();
    
    // Enable HSTS
    console.log('\n🔒 Configuring security settings...');
    await enableHSTS();
    
    // Create page rules
    console.log('\n📄 Creating page rules...');
    await createPageRules();
    
    console.log('\n🎉 Cloudflare zone configuration completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Run DNS validation: ./scripts/validate-dns-config.sh');
    console.log('   2. Test SSL certificate: curl -I https://' + config.domain);
    console.log('   3. Verify security headers are active');
    console.log('   4. Monitor Cloudflare analytics for performance');
    
  } catch (error) {
    console.error('\n❌ Configuration failed:', error.message);
    process.exit(1);
  }
}

// Run the configuration
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { cloudflareAPI, config };
