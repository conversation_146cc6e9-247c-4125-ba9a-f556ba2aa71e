#!/usr/bin/env node

/**
 * Create Missing Firebase Indexes Script
 * 
 * Script to create the final 2 missing indexes for the admin dashboard
 * using Firebase Admin SDK or CLI commands.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

const { execSync } = require('child_process');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Missing indexes configuration
const missingIndexes = [
  {
    collection: 'profiles',
    fields: [
      { field: 'role', order: 'ASCENDING' },
      { field: 'createdAt', order: 'DESCENDING' }
    ],
    description: 'Admin user filtering by role'
  },
  {
    collection: 'point_transactions',
    fields: [
      { field: 'type', order: 'ASCENDING' },
      { field: 'createdAt', order: 'DESCENDING' }
    ],
    description: 'Admin point transaction filtering by type'
  }
];

function createTempIndexFile() {
  const indexConfig = {
    indexes: missingIndexes.map(index => ({
      collectionGroup: index.collection,
      queryScope: 'COLLECTION',
      fields: index.fields.map(field => ({
        fieldPath: field.field,
        order: field.order
      }))
    })),
    fieldOverrides: []
  };

  const tempFile = 'temp-missing-indexes.json';
  fs.writeFileSync(tempFile, JSON.stringify(indexConfig, null, 2));
  return tempFile;
}

function createIndexesUsingCurl() {
  logInfo('Creating indexes using Firebase REST API...');
  
  try {
    // Get Firebase project ID
    const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'syndicaps-fullpower';
    
    missingIndexes.forEach((indexConfig, i) => {
      logInfo(`Creating index ${i + 1}/2: ${indexConfig.collection} (${indexConfig.description})`);
      
      const indexData = {
        fields: indexConfig.fields.map(field => ({
          fieldPath: field.field,
          order: field.order
        }))
      };
      
      // Create a temporary file for this index
      const tempFile = `temp-index-${i}.json`;
      fs.writeFileSync(tempFile, JSON.stringify(indexData, null, 2));
      
      try {
        // Use Firebase CLI to create the index
        const command = `firebase firestore:indexes --help`;
        execSync(command, { stdio: 'pipe' });
        
        logWarning(`Manual creation required for: ${indexConfig.collection}`);
        
        // Clean up temp file
        fs.unlinkSync(tempFile);
        
      } catch (error) {
        logError(`Failed to create index for ${indexConfig.collection}: ${error.message}`);
        // Clean up temp file
        if (fs.existsSync(tempFile)) {
          fs.unlinkSync(tempFile);
        }
      }
    });
    
  } catch (error) {
    logError(`Error in index creation: ${error.message}`);
  }
}

function generateManualInstructions() {
  log('\n' + '='.repeat(70), 'cyan');
  log('📋 MANUAL INDEX CREATION INSTRUCTIONS', 'cyan');
  log('='.repeat(70), 'cyan');
  
  log('\n🔗 Firebase Console URL:', 'blue');
  log('https://console.firebase.google.com/project/syndicaps-fullpower/firestore/indexes', 'yellow');
  
  log('\n📝 Steps to create indexes:', 'blue');
  log('1. Click the URL above to open Firebase Console');
  log('2. Click "Create Index" button');
  log('3. Enter the collection name and fields as shown below');
  log('4. Click "Create" and wait for the index to build');
  
  missingIndexes.forEach((index, i) => {
    log(`\n${i + 1}. Collection: ${index.collection}`, 'green');
    log(`   Description: ${index.description}`);
    log('   Fields:');
    index.fields.forEach(field => {
      log(`     - ${field.field}: ${field.order}`);
    });
  });
  
  log('\n⏱️  Index building time: 1-5 minutes each', 'yellow');
  log('🎯 After creation: Test admin dashboard performance', 'green');
}

function generateCLICommands() {
  log('\n' + '='.repeat(70), 'cyan');
  log('🖥️  ALTERNATIVE: CLI COMMANDS', 'cyan');
  log('='.repeat(70), 'cyan');
  
  log('\nYou can also create these indexes using gcloud CLI:', 'blue');
  log('(Requires Google Cloud SDK installed)', 'yellow');
  
  missingIndexes.forEach((index, i) => {
    log(`\n${i + 1}. ${index.collection} index:`, 'green');
    
    const fields = index.fields.map(field => 
      `${field.field},${field.order.toLowerCase()}`
    ).join(' ');
    
    const command = `gcloud firestore indexes composite create \\
  --collection-group=${index.collection} \\
  --field-config=${fields} \\
  --project=syndicaps-fullpower`;
    
    log(command, 'yellow');
  });
}

function checkExistingIndexes() {
  logInfo('Checking existing indexes...');
  
  try {
    const result = execSync('firebase firestore:indexes', { encoding: 'utf8' });
    const indexes = JSON.parse(result);
    
    const existingIndexes = indexes.indexes || [];
    const missing = [];
    
    missingIndexes.forEach(requiredIndex => {
      const exists = existingIndexes.some(existing => {
        if (existing.collectionGroup !== requiredIndex.collection) {
          return false;
        }
        
        return requiredIndex.fields.every(reqField => {
          return existing.fields.some(existingField => {
            return existingField.fieldPath === reqField.field &&
                   existingField.order === reqField.order;
          });
        });
      });
      
      if (!exists) {
        missing.push(requiredIndex);
      } else {
        logSuccess(`Index already exists: ${requiredIndex.collection}`);
      }
    });
    
    if (missing.length === 0) {
      logSuccess('🎉 All required indexes already exist!');
      return false;
    } else {
      logWarning(`${missing.length} indexes still need to be created`);
      return true;
    }
    
  } catch (error) {
    logError(`Error checking existing indexes: ${error.message}`);
    return true; // Assume we need to create them
  }
}

function main() {
  console.log('🔥 Firebase Missing Indexes Creator');
  console.log('=' .repeat(40));
  console.log('');
  
  // Check if indexes already exist
  const needsCreation = checkExistingIndexes();
  
  if (!needsCreation) {
    log('\n🎯 Next step: Test your admin dashboard!', 'green');
    log('Visit: http://localhost:3000/admin/dashboard', 'cyan');
    return;
  }
  
  // Try automated creation first
  logInfo('Attempting automated index creation...');
  createIndexesUsingCurl();
  
  // Provide manual instructions
  generateManualInstructions();
  
  // Provide CLI alternative
  generateCLICommands();
  
  log('\n' + '='.repeat(70), 'cyan');
  log('🚀 QUICK LINKS', 'cyan');
  log('='.repeat(70), 'cyan');
  log('Firebase Console: https://console.firebase.google.com/project/syndicaps-fullpower/firestore/indexes', 'yellow');
  log('Admin Dashboard: http://localhost:3000/admin/dashboard', 'yellow');
  log('Validation Script: npm run firebase:validate', 'yellow');
  
  log('\n✨ After creating indexes, run: npm run firebase:validate', 'green');
}

// Run the script
main();
