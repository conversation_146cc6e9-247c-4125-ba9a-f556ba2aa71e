/**
 * Create Community Collections with Sample Data
 * 
 * This script sets up all necessary Firebase collections for the community
 * discover functionality with realistic sample data.
 * 
 * Usage: npm run community:setup
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { initializeApp } = require('firebase/app');
const { getFirestore, doc, setDoc, collection, Timestamp } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Sample data generators
const generateUserReference = (userId, userName, level = 1) => ({
  userId,
  userName,
  userAvatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userName}`,
  userLevel: level,
  userTier: level > 10 ? 'gold' : level > 5 ? 'silver' : 'bronze'
});

const generateTimestamp = (daysAgo = 0) => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return Timestamp.fromDate(date);
};

async function createCommunityStats() {
  console.log('Creating community stats...');
  
  const statsData = {
    totalUsers: 2847,
    activeUsers: 156,
    totalPoints: 45230,
    totalSubmissions: 234,
    totalChallenges: 12,
    totalDiscussions: 89,
    totalVotes: 145,
    lastUpdated: generateTimestamp()
  };

  await setDoc(doc(db, 'community_stats', 'global'), statsData);
  console.log('✅ Community stats created');
}

async function createChallenges() {
  console.log('Creating community challenges...');
  
  const challenges = [
    {
      id: 'keycap-artistry-2024',
      title: 'Keycap Artistry Challenge',
      description: 'Design and create your most artistic keycap. Show us your creativity and craftsmanship!',
      type: 'current',
      status: 'active',
      difficulty: 'medium',
      participants: 47,
      maxParticipants: 100,
      startDate: generateTimestamp(7),
      endDate: generateTimestamp(-14),
      reward: 'Limited Edition Badge + 500 Points',
      points: 500,
      image: '/images/challenges/artistry-challenge.jpg',
      tags: ['artisan', 'creative', 'design'],
      rules: [
        'Must be original work',
        'Submit high-quality photos',
        'Include build process'
      ],
      createdBy: generateUserReference('admin', 'Syndicaps Team', 15),
      createdAt: generateTimestamp(10),
      updatedAt: generateTimestamp(1)
    },
    {
      id: 'color-matching-master',
      title: 'Color Matching Master',
      description: 'Create a perfect color-coordinated keycap set that matches a specific theme.',
      type: 'current',
      status: 'active',
      difficulty: 'easy',
      participants: 23,
      maxParticipants: 50,
      startDate: generateTimestamp(3),
      endDate: generateTimestamp(-10),
      reward: 'Color Master Badge + 250 Points',
      points: 250,
      image: '/images/challenges/color-challenge.jpg',
      tags: ['color', 'theme', 'coordination'],
      rules: [
        'Must use at least 5 different colors',
        'Provide color palette reference',
        'Explain your color choices'
      ],
      createdBy: generateUserReference('admin', 'Syndicaps Team', 15),
      createdAt: generateTimestamp(5),
      updatedAt: generateTimestamp()
    }
  ];

  for (const challenge of challenges) {
    await setDoc(doc(db, 'challenges', challenge.id), challenge);
  }
  console.log('✅ Community challenges created');
}

async function createSubmissions() {
  console.log('Creating community submissions...');
  
  const submissions = [
    {
      id: 'sub-001',
      title: 'Sakura Cherry Blossom Artisan',
      description: 'Hand-sculpted cherry blossom keycap with translucent resin petals',
      category: 'artisan',
      author: generateUserReference('user_123', 'ArtisanMaster', 8),
      image: '/images/submissions/sakura-keycap.jpg',
      images: [
        '/images/submissions/sakura-keycap.jpg',
        '/images/submissions/sakura-process-1.jpg',
        '/images/submissions/sakura-process-2.jpg'
      ],
      likes: 134,
      views: 567,
      comments: 23,
      submittedAt: generateTimestamp(2),
      challengeId: 'keycap-artistry-2024',
      tags: ['sakura', 'resin', 'translucent', 'nature'],
      isPublic: true,
      moderationStatus: 'approved',
      createdAt: generateTimestamp(2),
      updatedAt: generateTimestamp(1)
    },
    {
      id: 'sub-002',
      title: 'Cyberpunk Neon Setup',
      description: 'Full cyberpunk-themed keyboard with custom RGB underglow',
      category: 'setup',
      author: generateUserReference('user_456', 'CyberKeeb', 6),
      image: '/images/submissions/cyberpunk-setup.jpg',
      images: [
        '/images/submissions/cyberpunk-setup.jpg',
        '/images/submissions/cyberpunk-detail.jpg'
      ],
      likes: 89,
      views: 432,
      comments: 15,
      submittedAt: generateTimestamp(1),
      tags: ['cyberpunk', 'rgb', 'neon', 'setup'],
      isPublic: true,
      moderationStatus: 'approved',
      createdAt: generateTimestamp(1),
      updatedAt: generateTimestamp()
    },
    {
      id: 'sub-003',
      title: 'Minimalist Wooden Keycaps',
      description: 'Handcrafted wooden keycaps with natural grain patterns',
      category: 'artisan',
      author: generateUserReference('user_789', 'WoodWorker', 7),
      image: '/images/submissions/wooden-keycaps.jpg',
      likes: 76,
      views: 298,
      comments: 11,
      submittedAt: generateTimestamp(3),
      challengeId: 'color-matching-master',
      tags: ['wood', 'natural', 'minimalist', 'handcrafted'],
      isPublic: true,
      moderationStatus: 'approved',
      createdAt: generateTimestamp(3),
      updatedAt: generateTimestamp(2)
    }
  ];

  for (const submission of submissions) {
    await setDoc(doc(db, 'submissions', submission.id), submission);
  }
  console.log('✅ Community submissions created');
}

async function createDiscussionThreads() {
  console.log('Creating discussion threads...');
  
  const threads = [
    {
      id: 'thread-001',
      title: 'Best practices for resin casting?',
      content: 'I\'m new to resin casting and looking for tips on getting bubble-free results. What are your go-to techniques?',
      author: generateUserReference('user_111', 'ResinNewbie', 2),
      category: 'techniques',
      tags: ['resin', 'casting', 'tips', 'beginner'],
      replies: 18,
      views: 234,
      likes: 45,
      lastReply: generateTimestamp(),
      lastReplyBy: generateUserReference('user_222', 'ResinExpert', 12),
      isPinned: false,
      isLocked: false,
      moderationStatus: 'approved',
      createdAt: generateTimestamp(4),
      updatedAt: generateTimestamp()
    },
    {
      id: 'thread-002',
      title: 'Show off your latest builds!',
      content: 'Weekly thread to share your latest keyboard builds and get feedback from the community.',
      author: generateUserReference('admin', 'ModeratorBot', 15),
      category: 'showcase',
      tags: ['builds', 'showcase', 'weekly', 'feedback'],
      replies: 67,
      views: 1234,
      likes: 89,
      lastReply: generateTimestamp(),
      lastReplyBy: generateUserReference('user_333', 'BuildMaster', 9),
      isPinned: true,
      isLocked: false,
      moderationStatus: 'approved',
      createdAt: generateTimestamp(7),
      updatedAt: generateTimestamp()
    }
  ];

  for (const thread of threads) {
    await setDoc(doc(db, 'discussion_threads', thread.id), thread);
  }
  console.log('✅ Discussion threads created');
}

async function createActivities() {
  console.log('Creating community activities...');
  
  const activities = [
    {
      id: 'activity-001',
      type: 'submission',
      user: generateUserReference('user_123', 'ArtisanMaster', 8),
      title: 'New Submission Posted',
      description: 'ArtisanMaster shared "Sakura Cherry Blossom Artisan"',
      metadata: {
        submissionId: 'sub-001',
        submissionTitle: 'Sakura Cherry Blossom Artisan',
        category: 'artisan'
      },
      relatedId: 'sub-001',
      points: 50,
      isPublic: true,
      timestamp: generateTimestamp(2),
      createdAt: generateTimestamp(2),
      updatedAt: generateTimestamp(2)
    },
    {
      id: 'activity-002',
      type: 'challenge_join',
      user: generateUserReference('user_456', 'CyberKeeb', 6),
      title: 'Challenge Joined',
      description: 'CyberKeeb joined the Keycap Artistry Challenge',
      metadata: {
        challengeId: 'keycap-artistry-2024',
        challengeTitle: 'Keycap Artistry Challenge'
      },
      relatedId: 'keycap-artistry-2024',
      isPublic: true,
      timestamp: generateTimestamp(1),
      createdAt: generateTimestamp(1),
      updatedAt: generateTimestamp(1)
    },
    {
      id: 'activity-003',
      type: 'achievement',
      user: generateUserReference('user_789', 'WoodWorker', 7),
      title: 'Achievement Unlocked',
      description: 'WoodWorker earned the "First Submission" badge',
      metadata: {
        achievementId: 'first-submission',
        achievementTitle: 'First Submission',
        badgeIcon: '🏆'
      },
      points: 100,
      isPublic: true,
      timestamp: generateTimestamp(3),
      createdAt: generateTimestamp(3),
      updatedAt: generateTimestamp(3)
    }
  ];

  for (const activity of activities) {
    await setDoc(doc(db, 'activities', activity.id), activity);
  }
  console.log('✅ Community activities created');
}

async function createCommunityMembers() {
  console.log('Creating community member profiles...');
  
  const members = [
    {
      id: 'user_123',
      userId: 'user_123',
      userName: 'ArtisanMaster',
      displayName: 'Artisan Master',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ArtisanMaster',
      level: 8,
      points: 2450,
      tier: 'silver',
      joinedAt: generateTimestamp(45),
      lastActive: generateTimestamp(),
      badges: ['first-submission', 'artisan-expert', 'helpful-member'],
      achievements: ['first-submission', 'five-submissions', 'popular-creator'],
      stats: {
        submissions: 12,
        likes: 234,
        comments: 45,
        challengesCompleted: 3
      },
      bio: 'Passionate about creating unique artisan keycaps with nature-inspired themes.',
      location: 'Portland, OR',
      website: 'https://artisanmaster.com',
      social: {
        instagram: 'artisan_master_keys',
        twitter: 'artisanmaster'
      },
      preferences: {
        publicProfile: true,
        emailNotifications: true,
        showOnLeaderboard: true
      },
      createdAt: generateTimestamp(45),
      updatedAt: generateTimestamp()
    }
  ];

  for (const member of members) {
    await setDoc(doc(db, 'community_members', member.id), member);
  }
  console.log('✅ Community members created');
}

async function createVoteItems() {
  console.log('Creating vote items...');
  
  const voteItems = [
    {
      id: 'vote-001',
      title: 'Next Challenge Theme',
      description: 'Vote for the theme of our next community challenge!',
      category: 'challenge',
      author: generateUserReference('admin', 'Syndicaps Team', 15),
      image: '/images/votes/challenge-theme.jpg',
      upvotes: 89,
      downvotes: 12,
      totalVotes: 101,
      status: 'active',
      endDate: generateTimestamp(-7),
      tags: ['challenge', 'theme', 'community'],
      requirements: {
        minLevel: 1,
        minPoints: 0
      },
      createdAt: generateTimestamp(5),
      updatedAt: generateTimestamp(1)
    }
  ];

  for (const voteItem of voteItems) {
    await setDoc(doc(db, 'vote_items', voteItem.id), voteItem);
  }
  console.log('✅ Vote items created');
}

async function main() {
  try {
    console.log('🚀 Starting community collections setup...\n');
    
    await createCommunityStats();
    await createChallenges();
    await createSubmissions();
    await createDiscussionThreads();
    await createActivities();
    await createCommunityMembers();
    await createVoteItems();
    
    console.log('\n🎉 Community collections setup completed successfully!');
    console.log('\nCreated collections:');
    console.log('  • community_stats/global');
    console.log('  • challenges/ (2 documents)');
    console.log('  • submissions/ (3 documents)');
    console.log('  • discussion_threads/ (2 documents)');
    console.log('  • activities/ (3 documents)');
    console.log('  • community_members/ (1 document)');
    console.log('  • vote_items/ (1 document)');
    
  } catch (error) {
    console.error('❌ Error setting up community collections:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().then(() => process.exit(0));
}

module.exports = { main };