// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { initializeApp } = require('firebase/app');
const { getFirestore, doc, setDoc, serverTimestamp } = require('firebase/firestore');
const { getAuth, createUserWithEmailAndPassword } = require('firebase/auth');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "demo-api-key",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "demo-project.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "demo-project",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "demo-project.appspot.com",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "demo-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

async function createSuperAdmin() {
  try {
    console.log('🔥 Creating super admin user...');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'SuperAdmin123!@#';
    
    // Create user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
    const user = userCredential.user;
    
    console.log('✅ Super admin user created in Firebase Auth:', user.uid);
    
    // Create super admin profile in Firestore
    await setDoc(doc(db, 'profiles', user.uid), {
      email: adminEmail,
      displayName: 'Super Administrator',
      role: 'superadmin',
      permissions: {
        canManageUsers: true,
        canManageAdmins: true,
        canManageSystem: true,
        canViewAnalytics: true,
        canManageContent: true,
        canManageProducts: true,
        canManageRaffles: true,
        canAccessAllPages: true,
        canManageDatabase: true,
        canManageSecurity: true
      },
      points: 10000,
      isActive: true,
      isSuperAdmin: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLogin: null,
      loginCount: 0
    });
    
    console.log('✅ Super admin profile created in Firestore');
    
    // Also create admin permissions document
    await setDoc(doc(db, 'adminPermissions', user.uid), {
      userId: user.uid,
      email: adminEmail,
      role: 'superadmin',
      permissions: {
        dashboard: true,
        users: true,
        products: true,
        raffles: true,
        analytics: true,
        content: true,
        settings: true,
        security: true,
        system: true,
        audit: true
      },
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: 'system',
      isActive: true
    });
    
    console.log('✅ Admin permissions document created');
    console.log('');
    console.log('🎉 Super Admin account created successfully!');
    console.log('');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: SuperAdmin123!@#');
    console.log('🔐 Role: Super Administrator');
    console.log('');
    console.log('🌐 Access admin panel at: http://localhost:3000/admin/dashboard');
    console.log('');
    console.log('⚠️  IMPORTANT: Please change the password after first login!');
    
  } catch (error) {
    if (error.code === 'auth/email-already-in-use') {
      console.log('⚠️  Super admin user already exists');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: SuperAdmin123!@#');
      console.log('');
      console.log('🔄 Updating profile to ensure super admin permissions...');
      
      // Try to update existing user profile
      try {
        // Note: We can't get the user ID without auth, so we'll need to handle this differently
        console.log('ℹ️  If you need to update permissions, please use the admin dashboard');
      } catch (updateError) {
        console.error('❌ Error updating profile:', updateError.message);
      }
    } else {
      console.error('❌ Error creating super admin user:', error.message);
      console.error('Full error:', error);
    }
  }
}

// Run the script
createSuperAdmin()
  .then(() => {
    console.log('✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });