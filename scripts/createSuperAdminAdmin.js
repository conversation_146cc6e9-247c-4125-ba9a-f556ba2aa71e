// Load environment variables
require('dotenv').config({ path: '.env.local' });

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
try {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    }),
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  });
} catch (error) {
  // App might already be initialized
  if (error.code !== 'app/duplicate-app') {
    throw error;
  }
}

const db = admin.firestore();
const auth = admin.auth();

async function createSuperAdminUser() {
  try {
    console.log('🔥 Creating super admin user with Firebase Admin SDK...');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'SuperAdmin123!@#';
    
    // Create user with Firebase Admin Auth
    let userRecord;
    try {
      userRecord = await auth.createUser({
        email: adminEmail,
        password: adminPassword,
        displayName: 'Super Administrator',
        emailVerified: true,
      });
      console.log('✅ Super admin user created in Firebase Auth:', userRecord.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        console.log('⚠️  User already exists, fetching user record...');
        userRecord = await auth.getUserByEmail(adminEmail);
        console.log('✅ Found existing user:', userRecord.uid);
      } else {
        throw error;
      }
    }
    
    // Set custom claims for admin privileges
    await auth.setCustomUserClaims(userRecord.uid, {
      admin: true,
      superadmin: true,
      role: 'superadmin',
      permissions: {
        canManageUsers: true,
        canManageAdmins: true,
        canManageSystem: true,
        canViewAnalytics: true,
        canManageContent: true,
        canManageProducts: true,
        canManageRaffles: true,
        canAccessAllPages: true,
        canManageDatabase: true,
        canManageSecurity: true
      }
    });
    
    console.log('✅ Custom claims set for super admin privileges');
    
    // Create super admin profile in Firestore using Admin SDK
    const profileData = {
      email: adminEmail,
      displayName: 'Super Administrator',
      role: 'superadmin',
      permissions: {
        canManageUsers: true,
        canManageAdmins: true,
        canManageSystem: true,
        canViewAnalytics: true,
        canManageContent: true,
        canManageProducts: true,
        canManageRaffles: true,
        canAccessAllPages: true,
        canManageDatabase: true,
        canManageSecurity: true
      },
      points: 10000,
      isActive: true,
      isSuperAdmin: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      lastLogin: null,
      loginCount: 0,
      tier: 'legendary',
      badges: [],
      achievements: [],
      moderation: {
        isSuspended: false,
        restrictions: {
          canPost: true,
          canComment: true,
          canVote: true,
          canParticipate: true
        }
      }
    };
    
    await db.collection('profiles').doc(userRecord.uid).set(profileData);
    console.log('✅ Super admin profile created in Firestore');
    
    // Create admin permissions document
    const permissionsData = {
      userId: userRecord.uid,
      email: adminEmail,
      role: 'superadmin',
      permissions: {
        dashboard: true,
        users: true,
        products: true,
        raffles: true,
        analytics: true,
        content: true,
        settings: true,
        security: true,
        system: true,
        audit: true,
        database: true,
        admin_management: true
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: 'system',
      isActive: true,
      level: 'superadmin'
    };
    
    await db.collection('adminPermissions').doc(userRecord.uid).set(permissionsData);
    console.log('✅ Admin permissions document created');
    
    // Create initial community member profile
    const communityData = {
      userId: userRecord.uid,
      displayName: 'Super Administrator',
      email: adminEmail,
      role: 'superadmin',
      tier: 'legendary',
      points: 10000,
      badges: [],
      achievements: [],
      joinedAt: admin.firestore.FieldValue.serverTimestamp(),
      lastActiveAt: admin.firestore.FieldValue.serverTimestamp(),
      stats: {
        postsCount: 0,
        commentsCount: 0,
        likesReceived: 0,
        challengesCompleted: 0,
        submissionsCount: 0
      },
      moderation: {
        isSuspended: false,
        warningsCount: 0,
        restrictions: {
          canPost: true,
          canComment: true,
          canVote: true,
          canParticipate: true
        }
      }
    };
    
    await db.collection('community_members').doc(userRecord.uid).set(communityData);
    console.log('✅ Community member profile created');
    
    console.log('');
    console.log('🎉 Super Admin account created successfully!');
    console.log('');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: SuperAdmin123!@#');
    console.log('👤 User ID:', userRecord.uid);
    console.log('🔐 Role: Super Administrator');
    console.log('');
    console.log('🌐 Access admin panel at: http://localhost:3000/admin/dashboard');
    console.log('');
    console.log('⚠️  IMPORTANT: Please change the password after first login!');
    console.log('');
    console.log('🔧 Custom claims set for immediate admin access');
    console.log('📊 All permissions enabled');
    
    return {
      uid: userRecord.uid,
      email: adminEmail,
      success: true
    };
    
  } catch (error) {
    console.error('❌ Error creating super admin user:', error.message);
    console.error('Full error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the script
if (require.main === module) {
  createSuperAdminUser()
    .then((result) => {
      if (result.success) {
        console.log('✅ Script completed successfully');
        process.exit(0);
      } else {
        console.error('❌ Script failed');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = createSuperAdminUser;