// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { initializeApp } = require('firebase/app');
const { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } = require('firebase/auth');
const { getFirestore, doc, setDoc, serverTimestamp, updateDoc } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

async function createSuperAdminUser() {
  try {
    console.log('🔥 Creating super admin user...');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'SuperAdmin123!@#';
    
    // First, try to sign in with existing admin to get authenticated context
    let existingAdmin = null;
    try {
      await signInWithEmailAndPassword(auth, '<EMAIL>', 'AdminPass123!');
      existingAdmin = auth.currentUser;
      console.log('✅ Signed in as existing admin for elevated permissions');
    } catch (adminError) {
      console.log('⚠️  No existing admin found or couldn\'t sign in');
    }
    
    // Create new super admin user
    let userCredential;
    try {
      userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
      console.log('✅ Super admin user created in Firebase Auth:', userCredential.user.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        console.log('⚠️  Super admin user already exists');
        // Try to sign in as the super admin to update profile
        try {
          userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
          console.log('✅ Signed in as existing super admin:', userCredential.user.uid);
        } catch (signInError) {
          console.error('❌ Could not sign in as existing super admin:', signInError.message);
          return;
        }
      } else {
        throw error;
      }
    }
    
    const user = userCredential.user;
    
    // Create/update super admin profile in Firestore
    const profileData = {
      email: adminEmail,
      displayName: 'Super Administrator',
      role: 'superadmin',
      permissions: {
        canManageUsers: true,
        canManageAdmins: true,
        canManageSystem: true,
        canViewAnalytics: true,
        canManageContent: true,
        canManageProducts: true,
        canManageRaffles: true,
        canAccessAllPages: true,
        canManageDatabase: true,
        canManageSecurity: true
      },
      points: 10000,
      isActive: true,
      isSuperAdmin: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLogin: null,
      loginCount: 0,
      tier: 'legendary',
      badges: [],
      achievements: [],
      moderation: {
        isSuspended: false,
        restrictions: {
          canPost: true,
          canComment: true,
          canVote: true,
          canParticipate: true
        }
      }
    };
    
    await setDoc(doc(db, 'profiles', user.uid), profileData);
    console.log('✅ Super admin profile created in Firestore');
    
    // Create admin permissions document
    const permissionsData = {
      userId: user.uid,
      email: adminEmail,
      role: 'superadmin',
      permissions: {
        dashboard: true,
        users: true,
        products: true,
        raffles: true,
        analytics: true,
        content: true,
        settings: true,
        security: true,
        system: true,
        audit: true,
        database: true,
        admin_management: true
      },
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: 'system',
      isActive: true,
      level: 'superadmin'
    };
    
    await setDoc(doc(db, 'adminPermissions', user.uid), permissionsData);
    console.log('✅ Admin permissions document created');
    
    // Create community member profile
    const communityData = {
      userId: user.uid,
      displayName: 'Super Administrator',
      email: adminEmail,
      role: 'superadmin',
      tier: 'legendary',
      points: 10000,
      badges: [],
      achievements: [],
      joinedAt: serverTimestamp(),
      lastActiveAt: serverTimestamp(),
      stats: {
        postsCount: 0,
        commentsCount: 0,
        likesReceived: 0,
        challengesCompleted: 0,
        submissionsCount: 0
      },
      moderation: {
        isSuspended: false,
        warningsCount: 0,
        restrictions: {
          canPost: true,
          canComment: true,
          canVote: true,
          canParticipate: true
        }
      }
    };
    
    await setDoc(doc(db, 'community_members', user.uid), communityData);
    console.log('✅ Community member profile created');
    
    // Sign out
    await signOut(auth);
    
    console.log('');
    console.log('🎉 Super Admin account created successfully!');
    console.log('');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: SuperAdmin123!@#');
    console.log('👤 User ID:', user.uid);
    console.log('🔐 Role: Super Administrator');
    console.log('');
    console.log('🌐 Access admin panel at: http://localhost:3000/admin/dashboard');
    console.log('');
    console.log('⚠️  IMPORTANT: Please change the password after first login!');
    console.log('');
    console.log('📋 Super admin has been granted:');
    console.log('- Full system access');
    console.log('- User management permissions');
    console.log('- Database management rights');
    console.log('- Security settings access');
    console.log('- All admin dashboard features');
    
  } catch (error) {
    console.error('❌ Error creating super admin user:', error.message);
    console.error('Error code:', error.code);
    
    if (error.code === 'permission-denied') {
      console.log('');
      console.log('🔐 Permission denied. This could be due to:');
      console.log('1. Firestore security rules preventing write access');
      console.log('2. Need to update your Firestore rules to allow admin creation');
      console.log('3. User needs to be authenticated as admin first');
      console.log('');
      console.log('💡 Try running this script after signing in as an existing admin');
      console.log('   or temporarily adjust your Firestore rules for admin creation');
    }
  }
}

// Run the script
createSuperAdminUser()
  .then(() => {
    console.log('✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });