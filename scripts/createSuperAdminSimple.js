// Load environment variables
require('dotenv').config({ path: '.env.local' });

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK with simpler approach
try {
  // Try to use service account from environment or file
  let credential;
  
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    // Use service account file
    credential = admin.credential.applicationDefault();
  } else if (process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
    // Use environment variables
    credential = admin.credential.cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    });
  } else {
    // Use project ID only (for local development)
    console.log('⚠️  No service account credentials found. Using project ID only.');
    console.log('ℹ️  This may not work in production environments.');
    
    admin.initializeApp({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    });
  }
  
  if (credential) {
    admin.initializeApp({
      credential: credential,
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    });
  }
} catch (error) {
  if (error.code !== 'app/duplicate-app') {
    console.error('❌ Error initializing Firebase Admin:', error.message);
    console.log('');
    console.log('📋 To fix this, you need to:');
    console.log('1. Download your Firebase service account key from the Firebase Console');
    console.log('2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable to the path of the key file');
    console.log('3. Or set FIREBASE_PRIVATE_KEY and FIREBASE_CLIENT_EMAIL environment variables');
    console.log('');
    console.log('🌐 Firebase Console: https://console.firebase.google.com/');
    console.log('📖 More info: https://firebase.google.com/docs/admin/setup#initialize-sdk');
    process.exit(1);
  }
}

const db = admin.firestore();
const auth = admin.auth();

async function createSuperAdminUser() {
  try {
    console.log('🔥 Creating super admin user...');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'SuperAdmin123!@#';
    
    // Create user with Firebase Admin Auth
    let userRecord;
    try {
      userRecord = await auth.createUser({
        email: adminEmail,
        password: adminPassword,
        displayName: 'Super Administrator',
        emailVerified: true,
      });
      console.log('✅ Super admin user created in Firebase Auth:', userRecord.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        console.log('⚠️  User already exists, fetching user record...');
        userRecord = await auth.getUserByEmail(adminEmail);
        console.log('✅ Found existing user:', userRecord.uid);
      } else {
        throw error;
      }
    }
    
    // Set custom claims for admin privileges
    await auth.setCustomUserClaims(userRecord.uid, {
      admin: true,
      superadmin: true,
      role: 'superadmin'
    });
    
    console.log('✅ Custom claims set for super admin privileges');
    
    // Create super admin profile in Firestore using Admin SDK
    const profileData = {
      email: adminEmail,
      displayName: 'Super Administrator',
      role: 'superadmin',
      permissions: {
        canManageUsers: true,
        canManageAdmins: true,
        canManageSystem: true,
        canViewAnalytics: true,
        canManageContent: true,
        canManageProducts: true,
        canManageRaffles: true,
        canAccessAllPages: true,
        canManageDatabase: true,
        canManageSecurity: true
      },
      points: 10000,
      isActive: true,
      isSuperAdmin: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      lastLogin: null,
      loginCount: 0,
      tier: 'legendary',
      badges: [],
      achievements: []
    };
    
    await db.collection('profiles').doc(userRecord.uid).set(profileData);
    console.log('✅ Super admin profile created in Firestore');
    
    // Create admin permissions document
    const permissionsData = {
      userId: userRecord.uid,
      email: adminEmail,
      role: 'superadmin',
      permissions: {
        dashboard: true,
        users: true,
        products: true,
        raffles: true,
        analytics: true,
        content: true,
        settings: true,
        security: true,
        system: true,
        audit: true
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: 'system',
      isActive: true
    };
    
    await db.collection('adminPermissions').doc(userRecord.uid).set(permissionsData);
    console.log('✅ Admin permissions document created');
    
    console.log('');
    console.log('🎉 Super Admin account created successfully!');
    console.log('');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: SuperAdmin123!@#');
    console.log('👤 User ID:', userRecord.uid);
    console.log('🔐 Role: Super Administrator');
    console.log('');
    console.log('🌐 Access admin panel at: http://localhost:3000/admin/dashboard');
    console.log('');
    console.log('⚠️  IMPORTANT: Please change the password after first login!');
    
  } catch (error) {
    console.error('❌ Error creating super admin user:', error.message);
    if (error.code === 'auth/insufficient-permission') {
      console.log('');
      console.log('🔐 This error usually means you need proper Firebase Admin credentials.');
      console.log('📋 Please ensure you have:');
      console.log('1. Downloaded your Firebase service account key');
      console.log('2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable');
      console.log('3. Or configured Firebase Admin SDK properly');
    }
  }
}

// Run the script
createSuperAdminUser()
  .then(() => {
    console.log('✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });