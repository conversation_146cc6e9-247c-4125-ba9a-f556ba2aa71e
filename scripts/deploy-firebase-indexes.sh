#!/bin/bash

# Firebase Indexes Deployment Script
# 
# Comprehensive script for deploying Firestore indexes and rules
# for the Syndicaps admin dashboard with proper error handling.
# 
# Author: Syndicaps Team
# Version: 1.0.0

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${FIREBASE_PROJECT_ID:-"syndicaps"}
INDEXES_FILE="firestore.indexes.json"
RULES_FILE="firestore.rules"

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Firebase CLI is installed
    if ! command -v firebase &> /dev/null; then
        log_error "Firebase CLI is not installed. Please install it first:"
        echo "npm install -g firebase-tools"
        exit 1
    fi
    
    # Check if user is logged in
    if ! firebase projects:list &> /dev/null; then
        log_error "You are not logged in to Firebase. Please run:"
        echo "firebase login"
        exit 1
    fi
    
    # Check if indexes file exists
    if [ ! -f "$INDEXES_FILE" ]; then
        log_error "Firestore indexes file not found: $INDEXES_FILE"
        exit 1
    fi
    
    # Check if rules file exists
    if [ ! -f "$RULES_FILE" ]; then
        log_warning "Firestore rules file not found: $RULES_FILE"
        log_info "Creating basic rules file..."
        create_basic_rules
    fi
    
    log_success "Prerequisites check passed"
}

create_basic_rules() {
    cat > "$RULES_FILE" << 'EOF'
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Admin access rules
    match /{document=**} {
      allow read, write: if request.auth != null && 
        (request.auth.token.role == 'admin' || request.auth.token.role == 'superadmin');
    }
    
    // User profile access
    match /profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public read access for products
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && 
        (request.auth.token.role == 'admin' || request.auth.token.role == 'superadmin');
    }
    
    // Orders access
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.token.role == 'admin' || 
         request.auth.token.role == 'superadmin');
    }
    
    // Reviews access
    match /reviews/{reviewId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Raffle entries
    match /raffle_entries/{entryId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.token.role == 'admin' || 
         request.auth.token.role == 'superadmin');
    }
  }
}
EOF
    log_success "Basic Firestore rules created"
}

validate_indexes() {
    log_info "Validating indexes file..."
    
    # Check if the indexes file is valid JSON
    if ! python3 -m json.tool "$INDEXES_FILE" > /dev/null 2>&1; then
        log_error "Invalid JSON in $INDEXES_FILE"
        exit 1
    fi
    
    # Count indexes
    local index_count=$(python3 -c "
import json
with open('$INDEXES_FILE', 'r') as f:
    data = json.load(f)
    print(len(data.get('indexes', [])))
")
    
    log_success "Indexes file is valid JSON with $index_count indexes"
}

backup_current_indexes() {
    log_info "Creating backup of current indexes..."
    
    local backup_file="firestore.indexes.backup.$(date +%Y%m%d_%H%M%S).json"
    
    if firebase firestore:indexes > "$backup_file" 2>/dev/null; then
        log_success "Current indexes backed up to: $backup_file"
    else
        log_warning "Could not backup current indexes (this is normal for new projects)"
    fi
}

deploy_indexes() {
    log_info "Deploying Firestore indexes..."
    
    # Deploy indexes
    if firebase deploy --only firestore:indexes --project "$PROJECT_ID"; then
        log_success "Firestore indexes deployed successfully"
    else
        log_error "Failed to deploy Firestore indexes"
        exit 1
    fi
}

deploy_rules() {
    log_info "Deploying Firestore rules..."
    
    # Deploy rules
    if firebase deploy --only firestore:rules --project "$PROJECT_ID"; then
        log_success "Firestore rules deployed successfully"
    else
        log_error "Failed to deploy Firestore rules"
        exit 1
    fi
}

show_index_status() {
    log_info "Checking index build status..."
    
    echo ""
    echo "🔍 Index Status:"
    echo "=================="
    
    # Show current indexes
    firebase firestore:indexes --project "$PROJECT_ID" || true
    
    echo ""
    log_info "Note: New indexes may take several minutes to build."
    log_info "You can monitor progress in the Firebase Console:"
    echo "https://console.firebase.google.com/project/$PROJECT_ID/firestore/indexes"
}

main() {
    echo "🚀 Firebase Indexes Deployment Script"
    echo "======================================"
    echo ""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --project)
                PROJECT_ID="$2"
                shift 2
                ;;
            --indexes-only)
                DEPLOY_RULES=false
                shift
                ;;
            --rules-only)
                DEPLOY_INDEXES=false
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo ""
                echo "Options:"
                echo "  --project PROJECT_ID    Firebase project ID"
                echo "  --indexes-only          Deploy only indexes"
                echo "  --rules-only           Deploy only rules"
                echo "  --help                 Show this help message"
                echo ""
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Set defaults
    DEPLOY_INDEXES=${DEPLOY_INDEXES:-true}
    DEPLOY_RULES=${DEPLOY_RULES:-true}
    
    log_info "Using Firebase project: $PROJECT_ID"
    echo ""
    
    # Run deployment steps
    check_prerequisites
    validate_indexes
    backup_current_indexes
    
    if [ "$DEPLOY_INDEXES" = true ]; then
        deploy_indexes
    fi
    
    if [ "$DEPLOY_RULES" = true ]; then
        deploy_rules
    fi
    
    show_index_status
    
    echo ""
    log_success "🎉 Firebase deployment completed successfully!"
    echo ""
    log_info "Next steps:"
    echo "1. Monitor index build progress in Firebase Console"
    echo "2. Test admin dashboard functionality"
    echo "3. Check for any remaining indexing errors"
    echo ""
}

# Run main function with all arguments
main "$@"
