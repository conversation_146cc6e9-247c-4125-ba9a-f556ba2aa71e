#!/bin/bash

# Syndicaps Cloudflare Workers Deployment Script
# 
# Deploys image optimization and API cache workers with proper configuration
# and health checks to ensure successful deployment.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WORKERS_DIR="workers"
DIST_DIR="$WORKERS_DIR/dist"
LOG_FILE="deployment-$(date +%Y%m%d-%H%M%S).log"

# Logging functions
log() {
  echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
  exit 1
}

warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
  echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
  log "Checking prerequisites..."
  
  if ! command -v wrangler &> /dev/null; then
    error "Wrangler CLI not found. Please install with: npm install -g wrangler"
  fi
  
  if ! command -v node &> /dev/null; then
    error "Node.js not found. Please install Node.js"
  fi
  
  if ! command -v npm &> /dev/null; then
    error "npm not found. Please install npm"
  fi
  
  # Check if logged in to Cloudflare
  if ! wrangler whoami &> /dev/null; then
    error "Not logged in to Cloudflare. Please run: wrangler login"
  fi
  
  # Check if wrangler.toml exists
  if [ ! -f "wrangler.toml" ]; then
    error "wrangler.toml not found. Please create the configuration file."
  fi
  
  log "Prerequisites check passed"
}

# Validate environment variables
validate_environment() {
  log "Validating environment variables..."
  
  local required_vars=(
    "CLOUDFLARE_ACCOUNT_ID"
    "CLOUDFLARE_ZONE_ID"
    "R2_ACCESS_KEY_ID"
    "R2_SECRET_ACCESS_KEY"
  )
  
  local missing_vars=()
  
  for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
      missing_vars+=("$var")
    fi
  done
  
  if [ ${#missing_vars[@]} -ne 0 ]; then
    error "Missing required environment variables: ${missing_vars[*]}"
  fi
  
  log "Environment validation passed"
}

# Install dependencies
install_dependencies() {
  log "Installing worker dependencies..."
  
  cd "$WORKERS_DIR"
  
  if [ ! -f "package.json" ]; then
    error "package.json not found in workers directory"
  fi
  
  npm ci
  
  cd ..
  
  log "Dependencies installed successfully"
}

# Build workers
build_workers() {
  log "Building workers..."
  
  cd "$WORKERS_DIR"
  
  # Clean previous builds
  rm -rf "$DIST_DIR"
  mkdir -p "$DIST_DIR"
  
  # Build image optimizer
  log "Building image optimizer worker..."
  npm run build:image-optimizer
  
  # Build API cache worker
  log "Building API cache worker..."
  npm run build:api-cache
  
  cd ..
  
  log "Workers built successfully"
}

# Run tests
run_tests() {
  log "Running worker tests..."
  
  cd "$WORKERS_DIR"
  
  # Run TypeScript type checking
  npm run type-check
  
  # Run linting
  npm run lint
  
  # Run unit tests
  npm run test
  
  cd ..
  
  log "All tests passed"
}

# Deploy to staging
deploy_staging() {
  log "Deploying workers to staging..."
  
  cd "$WORKERS_DIR"
  
  # Deploy image optimizer to staging
  log "Deploying image optimizer to staging..."
  wrangler deploy image-optimizer.ts --name syndicaps-image-optimizer-staging --env staging
  
  # Deploy API cache to staging
  log "Deploying API cache to staging..."
  wrangler deploy api-cache.ts --name syndicaps-api-cache-staging --env staging
  
  cd ..
  
  log "Staging deployment completed"
}

# Test staging deployment
test_staging() {
  log "Testing staging deployment..."
  
  local staging_domain="staging.syndicaps.com"
  
  # Test image optimizer
  log "Testing image optimizer on staging..."
  local image_test_url="https://${staging_domain}/test-image.jpg?w=300&q=80"
  
  if curl -f -s "$image_test_url" > /dev/null; then
    log "✅ Image optimizer staging test passed"
  else
    warning "⚠️ Image optimizer staging test failed"
  fi
  
  # Test API cache
  log "Testing API cache on staging..."
  local api_test_url="https://${staging_domain}/api/health"
  
  if curl -f -s "$api_test_url" > /dev/null; then
    log "✅ API cache staging test passed"
  else
    warning "⚠️ API cache staging test failed"
  fi
  
  log "Staging tests completed"
}

# Deploy to production
deploy_production() {
  log "Deploying workers to production..."
  
  cd "$WORKERS_DIR"
  
  # Deploy image optimizer to production
  log "Deploying image optimizer to production..."
  wrangler deploy image-optimizer.ts --name syndicaps-image-optimizer --env production
  
  # Deploy API cache to production
  log "Deploying API cache to production..."
  wrangler deploy api-cache.ts --name syndicaps-api-cache --env production
  
  cd ..
  
  log "Production deployment completed"
}

# Test production deployment
test_production() {
  log "Testing production deployment..."
  
  local production_domain="syndicaps.com"
  
  # Test image optimizer
  log "Testing image optimizer on production..."
  local image_test_url="https://${production_domain}/test-image.jpg?w=300&q=80"
  
  if curl -f -s "$image_test_url" > /dev/null; then
    log "✅ Image optimizer production test passed"
  else
    error "❌ Image optimizer production test failed"
  fi
  
  # Test API cache
  log "Testing API cache on production..."
  local api_test_url="https://${production_domain}/api/health"
  
  if curl -f -s "$api_test_url" > /dev/null; then
    log "✅ API cache production test passed"
  else
    error "❌ API cache production test failed"
  fi
  
  log "Production tests completed"
}

# Configure routes
configure_routes() {
  log "Configuring worker routes..."
  
  # Configure image optimizer routes
  log "Configuring image optimizer routes..."
  wrangler route put "syndicaps.com/images/*" syndicaps-image-optimizer --env production
  wrangler route put "*.syndicaps.com/images/*" syndicaps-image-optimizer --env production
  
  # Configure API cache routes
  log "Configuring API cache routes..."
  wrangler route put "syndicaps.com/api/*" syndicaps-api-cache --env production
  wrangler route put "*.syndicaps.com/api/*" syndicaps-api-cache --env production
  
  log "Routes configured successfully"
}

# Monitor deployment
monitor_deployment() {
  log "Monitoring deployment health..."
  
  local max_attempts=5
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    log "Health check attempt $attempt/$max_attempts..."
    
    # Check worker health endpoints
    if curl -f -s "https://syndicaps.com/health" > /dev/null; then
      log "✅ Workers are healthy"
      return 0
    fi
    
    warning "Health check failed, retrying in 30 seconds..."
    sleep 30
    ((attempt++))
  done
  
  error "❌ Workers health check failed after $max_attempts attempts"
}

# Generate deployment report
generate_report() {
  log "Generating deployment report..."
  
  local report_file="workers-deployment-report-$(date +%Y%m%d-%H%M%S).json"
  
  cat > "$report_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "deployment": {
    "environment": "production",
    "workers": [
      {
        "name": "syndicaps-image-optimizer",
        "status": "deployed",
        "routes": ["syndicaps.com/images/*", "*.syndicaps.com/images/*"]
      },
      {
        "name": "syndicaps-api-cache",
        "status": "deployed",
        "routes": ["syndicaps.com/api/*", "*.syndicaps.com/api/*"]
      }
    ]
  },
  "tests": {
    "staging": "passed",
    "production": "passed"
  },
  "performance": {
    "buildTime": "$(date +%s)",
    "deploymentTime": "$(date +%s)"
  }
}
EOF
  
  log "Deployment report saved to: $report_file"
}

# Rollback function
rollback() {
  warning "Initiating rollback..."
  
  # Remove worker routes
  wrangler route delete "syndicaps.com/images/*" --env production || true
  wrangler route delete "syndicaps.com/api/*" --env production || true
  
  # Delete workers
  wrangler delete syndicaps-image-optimizer --env production || true
  wrangler delete syndicaps-api-cache --env production || true
  
  warning "Rollback completed"
}

# Main deployment function
main() {
  local environment="${1:-production}"
  local skip_tests="${2:-false}"
  
  log "Starting Cloudflare Workers deployment..."
  log "Environment: $environment"
  log "Skip tests: $skip_tests"
  
  # Trap errors for rollback
  trap 'error "Deployment failed. Initiating rollback..."; rollback' ERR
  
  check_prerequisites
  validate_environment
  install_dependencies
  build_workers
  
  if [ "$skip_tests" != "true" ]; then
    run_tests
  fi
  
  if [ "$environment" = "staging" ] || [ "$environment" = "both" ]; then
    deploy_staging
    test_staging
  fi
  
  if [ "$environment" = "production" ] || [ "$environment" = "both" ]; then
    deploy_production
    configure_routes
    test_production
    monitor_deployment
  fi
  
  generate_report
  
  log "🎉 Workers deployment completed successfully!"
  log "Deployment log saved to: $LOG_FILE"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --environment|-e)
      ENVIRONMENT="$2"
      shift 2
      ;;
    --skip-tests)
      SKIP_TESTS="true"
      shift
      ;;
    --rollback)
      rollback
      exit 0
      ;;
    --help|-h)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --environment, -e    Deployment environment (staging|production|both)"
      echo "  --skip-tests         Skip running tests"
      echo "  --rollback           Rollback deployment"
      echo "  --help, -h           Show this help message"
      exit 0
      ;;
    *)
      error "Unknown option: $1"
      ;;
  esac
done

# Run main function
main "${ENVIRONMENT:-production}" "${SKIP_TESTS:-false}"
