#!/usr/bin/env node

/**
 * Firestore Offline Recovery Script
 * 
 * This script forces Firestore back online and clears any offline cache
 * that might be causing "client is offline" errors.
 */

const { initializeApp, getApps } = require('firebase/app');
const { 
  getFirestore, 
  enableNetwork, 
  disableNetwork, 
  doc, 
  getDoc, 
  setDoc,
  clearIndexedDbPersistence,
  terminate,
  waitForPendingWrites
} = require('firebase/firestore');
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const levelColors = {
    INFO: colors.blue,
    SUCCESS: colors.green,
    WARNING: colors.yellow,
    ERROR: colors.red,
    STEP: colors.magenta
  };
  
  console.log(`${levelColors[level]}[${level}]${colors.reset} ${timestamp} - ${message}`);
}

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

async function forceFirestoreOnline() {
  log('STEP', '🔄 Step 1: Forcing Firestore Online');
  
  try {
    // Initialize Firebase
    const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
    const db = getFirestore(app);
    
    log('INFO', 'Firebase app and Firestore initialized');
    
    // Step 1: Disable network first
    log('INFO', 'Disabling Firestore network...');
    try {
      await disableNetwork(db);
      log('SUCCESS', '✅ Network disabled');
    } catch (error) {
      log('WARNING', `⚠️ Network disable failed (might already be disabled): ${error.message}`);
    }
    
    // Step 2: Wait a moment
    log('INFO', 'Waiting 2 seconds...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 3: Force enable network
    log('INFO', 'Force enabling Firestore network...');
    await enableNetwork(db);
    log('SUCCESS', '✅ Network force enabled');
    
    // Step 4: Wait for pending writes
    log('INFO', 'Waiting for pending writes...');
    try {
      await waitForPendingWrites(db);
      log('SUCCESS', '✅ Pending writes completed');
    } catch (error) {
      log('WARNING', `⚠️ Pending writes warning: ${error.message}`);
    }
    
    return db;
  } catch (error) {
    log('ERROR', `❌ Failed to force Firestore online: ${error.message}`);
    throw error;
  }
}

async function testDocumentOperations(db) {
  log('STEP', '📄 Step 2: Testing Document Operations');
  
  try {
    // Test 1: Read a public document
    log('INFO', 'Testing document read...');
    const testDocRef = doc(db, 'test', 'connection-test');
    
    try {
      const docSnap = await getDoc(testDocRef);
      if (docSnap.exists()) {
        log('SUCCESS', '✅ Document read successful - data exists');
      } else {
        log('SUCCESS', '✅ Document read successful - no data (but connection works)');
      }
    } catch (readError) {
      if (readError.message.includes('offline')) {
        log('ERROR', '❌ Still getting offline error during read');
        throw readError;
      } else if (readError.code === 'permission-denied') {
        log('SUCCESS', '✅ Document read successful (permission denied is expected)');
      } else {
        log('WARNING', `⚠️ Read error: ${readError.message}`);
      }
    }
    
    // Test 2: Try to write a test document (if permissions allow)
    log('INFO', 'Testing document write...');
    try {
      const writeTestRef = doc(db, 'connection-test', 'write-test');
      await setDoc(writeTestRef, {
        timestamp: new Date(),
        test: 'connection-recovery',
        status: 'online'
      });
      log('SUCCESS', '✅ Document write successful');
    } catch (writeError) {
      if (writeError.message.includes('offline')) {
        log('ERROR', '❌ Still getting offline error during write');
        throw writeError;
      } else if (writeError.code === 'permission-denied') {
        log('SUCCESS', '✅ Write test completed (permission denied is expected)');
      } else {
        log('WARNING', `⚠️ Write error: ${writeError.message}`);
      }
    }
    
    log('SUCCESS', '🎉 Document operations test completed');
    return true;
    
  } catch (error) {
    log('ERROR', `❌ Document operations failed: ${error.message}`);
    return false;
  }
}

async function clearOfflineCache() {
  log('STEP', '🧹 Step 3: Clearing Offline Cache');
  
  try {
    // Note: clearIndexedDbPersistence only works in browser environment
    // This is more for documentation of what needs to be done in browser
    log('INFO', 'Offline cache clearing (browser-only operation)');
    log('INFO', 'In browser, you would run: clearIndexedDbPersistence(db)');
    log('SUCCESS', '✅ Cache clearing instructions provided');
    return true;
  } catch (error) {
    log('WARNING', `⚠️ Cache clearing note: ${error.message}`);
    return false;
  }
}

async function verifyOnlineStatus(db) {
  log('STEP', '✅ Step 4: Verifying Online Status');
  
  try {
    // Multiple read attempts to ensure stability
    for (let i = 1; i <= 3; i++) {
      log('INFO', `Verification attempt ${i}/3...`);
      
      const testRef = doc(db, 'verification', `test-${i}`);
      try {
        await getDoc(testRef);
        log('SUCCESS', `✅ Verification ${i}/3 successful`);
      } catch (error) {
        if (error.message.includes('offline')) {
          log('ERROR', `❌ Verification ${i}/3 failed - still offline`);
          return false;
        } else {
          log('SUCCESS', `✅ Verification ${i}/3 successful (${error.code})`);
        }
      }
      
      // Wait between attempts
      if (i < 3) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    log('SUCCESS', '🎉 All verifications passed - Firestore is online!');
    return true;
    
  } catch (error) {
    log('ERROR', `❌ Verification failed: ${error.message}`);
    return false;
  }
}

async function main() {
  log('INFO', '🔥 Firestore Offline Recovery Tool');
  log('INFO', '==================================');
  
  try {
    // Step 1: Force Firestore online
    const db = await forceFirestoreOnline();
    
    // Step 2: Test document operations
    const operationsOk = await testDocumentOperations(db);
    if (!operationsOk) {
      log('ERROR', '❌ Document operations still failing');
      process.exit(1);
    }
    
    // Step 3: Clear offline cache (instructions)
    await clearOfflineCache();
    
    // Step 4: Verify online status
    const isOnline = await verifyOnlineStatus(db);
    if (!isOnline) {
      log('ERROR', '❌ Firestore is still offline after recovery attempts');
      process.exit(1);
    }
    
    log('SUCCESS', '🎉 Firestore offline recovery completed successfully!');
    log('INFO', '==================================');
    log('INFO', 'Next steps:');
    log('INFO', '1. Restart your development server: npm run dev');
    log('INFO', '2. Clear browser cache and reload your app');
    log('INFO', '3. Test your application functionality');
    
  } catch (error) {
    log('ERROR', `❌ Recovery failed: ${error.message}`);
    log('INFO', 'Try the browser cache clearing steps manually');
    process.exit(1);
  }
}

// Run the recovery
if (require.main === module) {
  main();
}

module.exports = { forceFirestoreOnline, testDocumentOperations, clearOfflineCache };
