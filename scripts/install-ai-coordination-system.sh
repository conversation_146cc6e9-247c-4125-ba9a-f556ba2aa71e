#!/bin/bash

# AI Coordination System - Complete Installation Script
# This script automates the entire setup process from the comprehensive guide

echo "🤖 Installing AI Coordination System for Syndicaps..."
echo "===================================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}📋 Step $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "src" ]; then
    print_error "This script must be run from the Syndicaps project root directory"
    echo "Please navigate to the project root and run again."
    exit 1
fi

print_success "Project directory verified"

# Step 1: Create required directories
print_step "1" "Creating required directories"
mkdir -p docs scripts .vscode .github/workflows
print_success "Directories created"

# Step 2: Create main coordination file
print_step "2" "Creating main coordination file"
cat > .ai-coordination.md << 'EOF'
# AI Development Coordination

This file manages work coordination between Augment Agent and Claude Code (Cursor AI) to prevent conflicts and ensure efficient collaboration.

## Current Work Claims

### Augment Agent
*Currently working on:*
- No active claims

*Reserved for future work:*
- Architecture and system design tasks
- Cross-file refactoring projects
- Documentation creation and updates
- Complex debugging requiring codebase-wide context

### Claude Code (Cursor AI)
*Currently working on:*
- No active claims

*Reserved for future work:*
- Real-time coding and implementation
- UI/UX component development
- Interactive debugging sessions
- Single-file modifications and bug fixes

## Handoff Queue

*No pending handoffs*

## Work Area Assignments

### Augment Agent Primary Areas
- `docs/` - All documentation
- `scripts/` - Database and deployment scripts
- `src/lib/` - Core library functions and utilities
- `src/contexts/` - React contexts and providers
- `src/hooks/` - Custom React hooks
- `app/admin/` - Admin dashboard backend logic
- `functions/` - Firebase Cloud Functions
- Configuration files (`.config.js`, `.json`, etc.)

### Claude Code Primary Areas
- `src/components/` - React components and UI
- `app/` - Next.js pages and routes (except admin backend)
- `src/styles/` - CSS and styling
- `public/` - Static assets
- `tests/` - Test files and test implementation
- Component-specific styling and interactions

### Shared Areas (Coordination Required)
- `src/types/` - TypeScript type definitions
- `src/store/` - State management
- `middleware.ts` - Authentication middleware
- Root configuration files

## Coordination Protocols

### Before Starting Work
1. **Check this file** for current claims and conflicts
2. **Claim your work area** by updating the "Current Work Claims" section
3. **Estimate completion time** and add it to your claim
4. **Check for dependencies** on other AI's work

### During Work
1. **Commit frequently** with clear, descriptive messages
2. **Use conventional commit format**: `[AI] type: description`
3. **Update progress** in the work log if working on major features
4. **Communicate blockers** by updating this file

### After Completing Work
1. **Remove your claim** from the "Current Work Claims" section
2. **Add handoff notes** if another AI needs to continue the work
3. **Update documentation** if you made architectural changes
4. **Run tests** before marking work as complete

## Emergency Protocols

### Immediate Fixes (Claude Code Priority)
- Production bugs affecting user experience
- UI/UX issues preventing normal operation
- Build failures blocking development

### Architecture Decisions (Augment Agent Priority)
- Database schema changes
- API design modifications
- Security implementation changes
- Performance optimization strategies

## Branch Naming Conventions

### Augment Agent
- `augment/feature-name` - New features
- `augment/analysis-topic` - Analysis and documentation
- `augment/refactor-area` - Code refactoring
- `augment/fix-issue` - Bug fixes requiring deep analysis

### Claude Code
- `cursor/feature-name` - New features
- `cursor/ui-improvements` - UI/UX enhancements
- `cursor/fix-issue` - Bug fixes and quick improvements
- `cursor/component-name` - Component-specific work

### Collaborative
- `collab/feature-name` - Features requiring both AIs
- `collab/integration-name` - Integration work

## Conflict Resolution

### File Conflicts
1. **Backup current work**: `git stash` or create backup branch
2. **Identify conflict owner** based on file type and primary areas
3. **Priority AI resolves conflict** and communicates resolution
4. **Test thoroughly** before final merge
5. **Document resolution** in work log

### Priority Rules
1. **Emergency fixes**: Claude Code has priority
2. **Architecture decisions**: Augment Agent has priority
3. **Shared files**: Sequential work only, no simultaneous editing
4. **Feature development**: First to claim has priority

## Communication Guidelines

### Commit Messages
```bash
[AUGMENT] feat: implement user level system architecture
[CURSOR] fix: resolve button hover animation glitch
[COLLAB] merge: integrate auth system with UI components
```

### Handoff Notes Format
```markdown
## Handoff: [Feature/Area Name]
**From**: [AI Name]
**To**: [AI Name]
**Date**: [YYYY-MM-DD]

### Completed Work
- [List of completed tasks]

### Next Steps
- [List of tasks for receiving AI]

### Important Notes
- [Any critical information, gotchas, or dependencies]

### Files Modified
- [List of key files changed]
```

---

**Last Updated**: $(date '+%Y-%m-%d')
**Next Review**: Check daily for active claims and conflicts
EOF

print_success "Main coordination file created"

# Step 3: Create work log file
print_step "3" "Creating work log file"
cat > AI_WORK_LOG.md << EOF
# AI Development Work Log

This log tracks daily progress, coordination, and communication between Augment Agent and Claude Code.

## $(date '+%Y-%m-%d')

### Augment Agent
- ✅ **Setup**: AI coordination system installed
- 📋 **Next**: Ready for coordinated development tasks

### Claude Code (Cursor AI)
- 📋 **Ready**: Awaiting coordination system configuration
- 📋 **Next**: Run setup commands and begin collaborative development

### Coordination Notes
- ✅ AI coordination system fully installed and configured
- 📝 Both AIs should follow established protocols in .ai-coordination.md
- 🔄 Daily sync recommended to update this log with progress
- 📋 No current conflicts or overlapping work areas

### System Status
- **Repository**: AI coordination system installed
- **Branches**: Main branch stable
- **Tests**: System validation pending
- **Deployment**: Ready for coordinated development

---

## Daily Log Template

Copy this template for each new day:

\`\`\`markdown
## YYYY-MM-DD

### Augment Agent
- ✅ **Completed**: [List completed tasks]
- 🔄 **In Progress**: [Current work with estimated completion]
- 📋 **Next**: [Planned next tasks]
- 🚫 **Blocked**: [Any blockers or dependencies]

### Claude Code (Cursor AI)
- ✅ **Completed**: [List completed tasks]
- 🔄 **In Progress**: [Current work with estimated completion]
- 📋 **Next**: [Planned next tasks]
- 🚫 **Blocked**: [Any blockers or dependencies]

### Coordination Notes
- [Any important coordination information]
- [Handoffs completed or planned]
- [Conflicts resolved]
- [Changes to work assignments]

### System Status
- **Repository**: [Clean/Conflicts/Issues]
- **Branches**: [Active branches and their status]
- **Tests**: [Test status and any failures]
- **Deployment**: [Production status]
\`\`\`

---

**Log Maintenance**: Update daily, review weekly, archive monthly
EOF

print_success "Work log file created"

# Step 4: Run the setup script (if it exists) or create it
print_step "4" "Setting up Git aliases and templates"

if [ ! -f "scripts/setup-ai-coordination.sh" ]; then
    # Create the setup script if it doesn't exist
    cat > scripts/setup-ai-coordination.sh << 'EOF'
#!/bin/bash

echo "🤖 Setting up AI Coordination Git Configuration..."

# Augment Agent aliases
git config alias.augment-commit '!f() { git commit -m "[AUGMENT] $1"; }; f'
git config alias.augment-feat '!f() { git commit -m "[AUGMENT] feat: $1"; }; f'
git config alias.augment-fix '!f() { git commit -m "[AUGMENT] fix: $1"; }; f'
git config alias.augment-docs '!f() { git commit -m "[AUGMENT] docs: $1"; }; f'
git config alias.augment-refactor '!f() { git commit -m "[AUGMENT] refactor: $1"; }; f'
git config alias.augment-test '!f() { git commit -m "[AUGMENT] test: $1"; }; f'

# Claude Code aliases
git config alias.cursor-commit '!f() { git commit -m "[CURSOR] $1"; }; f'
git config alias.cursor-feat '!f() { git commit -m "[CURSOR] feat: $1"; }; f'
git config alias.cursor-fix '!f() { git commit -m "[CURSOR] fix: $1"; }; f'
git config alias.cursor-ui '!f() { git commit -m "[CURSOR] ui: $1"; }; f'
git config alias.cursor-style '!f() { git commit -m "[CURSOR] style: $1"; }; f'
git config alias.cursor-test '!f() { git commit -m "[CURSOR] test: $1"; }; f'

# Collaborative aliases
git config alias.collab-commit '!f() { git commit -m "[COLLAB] $1"; }; f'
git config alias.collab-feat '!f() { git commit -m "[COLLAB] feat: $1"; }; f'
git config alias.collab-merge '!f() { git commit -m "[COLLAB] merge: $1"; }; f'

# Branch creation aliases
git config alias.augment-branch '!f() { git checkout -b "augment/$1"; }; f'
git config alias.cursor-branch '!f() { git checkout -b "cursor/$1"; }; f'
git config alias.collab-branch '!f() { git checkout -b "collab/$1"; }; f'

# Handoff aliases
git config alias.handoff-commit '!f() { git commit -m "[HANDOFF] $1 - Ready for handoff"; }; f'

echo "✅ Git aliases configured!"

# Create commit message template
cat > .gitmessage << 'TEMPLATE'
# AI Coordination Commit Template
# 
# Format: [AI] type: description
# 
# AI Options:
# [AUGMENT] - Augment Agent work
# [CURSOR]  - Claude Code work  
# [COLLAB]  - Collaborative work
# [HANDOFF] - Work ready for handoff
#
# Type Options:
# feat:     New feature
# fix:      Bug fix
# docs:     Documentation changes
# style:    Code style changes (formatting, etc.)
# refactor: Code refactoring
# test:     Adding or updating tests
# ui:       UI/UX improvements
# merge:    Merging branches
TEMPLATE

git config commit.template .gitmessage

echo "✅ Commit template created!"
EOF
fi

chmod +x scripts/setup-ai-coordination.sh
./scripts/setup-ai-coordination.sh

print_success "Git configuration completed"

# Step 5: Create helper functions (if they don't exist)
print_step "5" "Creating helper functions"

if [ ! -f "scripts/ai-coordination-helpers.sh" ]; then
    cat > scripts/ai-coordination-helpers.sh << 'EOF'
#!/bin/bash

# AI Coordination Helper Functions

check-claims() {
    echo "🔍 Current Work Claims:"
    echo "====================="
    if [ -f ".ai-coordination.md" ]; then
        grep -A 10 "## Current Work Claims" .ai-coordination.md
    else
        echo "❌ .ai-coordination.md not found!"
    fi
}

ai-status() {
    echo "🤖 AI Coordination Status"
    echo "========================="
    echo ""
    echo "📋 Current Branch: $(git branch --show-current)"
    echo "📊 Uncommitted Changes: $(git status --porcelain | wc -l) files"
    echo ""
    if [ -f ".ai-coordination.md" ]; then
        echo "🔍 Active Claims:"
        grep -A 5 "Currently working on:" .ai-coordination.md | grep -v "Currently working on:" | head -5
    fi
    echo ""
    echo "📝 Recent AI Commits:"
    git log --oneline -5 --grep="\[AUGMENT\]\|\[CURSOR\]\|\[COLLAB\]"
}

update-log() {
    if [ -z "$1" ]; then
        echo "Usage: update-log 'progress update'"
        return 1
    fi
    echo "📝 Adding to work log: $1"
    echo "$(date '+%Y-%m-%d %H:%M') - $1" >> AI_WORK_LOG.md
    echo "✅ Work log updated!"
}

handoff-template() {
    echo "📋 Handoff Template:"
    echo "==================="
    echo ""
    echo "## Handoff: [Feature Name]"
    echo "**From**: [Your AI]"
    echo "**To**: [Target AI]"
    echo "**Date**: $(date '+%Y-%m-%d %H:%M')"
    echo ""
    echo "### Completed Work"
    echo "- [List what you finished]"
    echo ""
    echo "### Next Steps"
    echo "- [What needs to be done next]"
    echo ""
    echo "### Files Modified"
    echo "- [Key files changed]"
    echo ""
    echo "### Important Notes"
    echo "- [Any gotchas or important context]"
}

export -f check-claims
export -f ai-status
export -f update-log
export -f handoff-template
EOF
fi

chmod +x scripts/ai-coordination-helpers.sh
print_success "Helper functions created"

# Step 6: Create handoff notes template (if it doesn't exist)
print_step "6" "Creating handoff documentation"

if [ ! -f "HANDOFF_NOTES.md" ]; then
    cat > HANDOFF_NOTES.md << 'EOF'
# AI Handoff Documentation

This file contains templates and active handoff notes for coordinating work between Augment Agent and Claude Code.

## Active Handoffs

*No active handoffs currently*

---

## Standard Handoff Template

```markdown
## Handoff: [Feature/Component/Area Name]

**From**: [Augment Agent / Claude Code]
**To**: [Augment Agent / Claude Code]
**Date**: [YYYY-MM-DD HH:MM]
**Priority**: [High / Medium / Low]

### Work Completed
- [ ] [Specific task 1]
- [ ] [Specific task 2]

### Next Steps Required
1. **Immediate**: [What needs to be done first]
2. **Follow-up**: [Subsequent tasks]
3. **Testing**: [What needs to be tested]

### Technical Context
**Architecture Decisions Made**:
- [Decision 1 and rationale]

**Dependencies**:
- [External dependencies]
- [Internal code dependencies]

**Known Issues/Gotchas**:
- [Issue 1 and workaround]

### Files Modified
**Primary Files**:
- `path/to/file1.ts` - [Brief description of changes]

**Tests**:
- `tests/path/to/test.spec.ts` - [Test coverage added]

### Handoff Checklist
- [ ] All code committed and pushed
- [ ] Tests passing
- [ ] Documentation updated
- [ ] Receiving AI notified
- [ ] Work claim updated in `.ai-coordination.md`
```

---

**Template Usage**: Copy appropriate template, fill in details, commit to repository
EOF
fi

print_success "Handoff documentation created"

# Step 7: Create validation script
print_step "7" "Creating validation script"

cat > scripts/validate-coordination-setup.sh << 'EOF'
#!/bin/bash

echo "🧪 Validating AI Coordination Setup..."

RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

TESTS_PASSED=0
TESTS_FAILED=0

print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# Test coordination files
REQUIRED_FILES=(".ai-coordination.md" "AI_WORK_LOG.md" "HANDOFF_NOTES.md")
FILES_MISSING=0

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        FILES_MISSING=1
    fi
done

print_result $FILES_MISSING "Coordination files present"

# Test Git aliases
ALIASES=("augment-feat" "cursor-feat" "augment-branch" "cursor-branch")
ALIASES_MISSING=0

for alias in "${ALIASES[@]}"; do
    if ! git config --get alias.$alias > /dev/null 2>&1; then
        ALIASES_MISSING=1
    fi
done

print_result $ALIASES_MISSING "Git aliases configured"

# Test helper functions
if source scripts/ai-coordination-helpers.sh 2>/dev/null; then
    if command -v ai-status > /dev/null 2>&1 && command -v check-claims > /dev/null 2>&1; then
        print_result 0 "Helper functions working"
    else
        print_result 1 "Helper functions not working"
    fi
else
    print_result 1 "Cannot load helper functions"
fi

echo ""
echo "📊 Validation Summary:"
echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! AI coordination system is ready!${NC}"
else
    echo ""
    echo -e "${RED}⚠️ Some tests failed. Please check the issues above.${NC}"
fi
EOF

chmod +x scripts/validate-coordination-setup.sh

print_success "Validation script created"

# Step 8: Create Claude Code setup instructions
print_step "8" "Creating Claude Code setup instructions"

cat > CLAUDE_CODE_SETUP_INSTRUCTIONS.md << 'EOF'
# Claude Code (Cursor AI) Setup Instructions

## Quick Setup Commands

**Copy and paste these commands in your Cursor AI terminal:**

```bash
# 1. Navigate to project root
cd /path/to/syndicaps

# 2. Load helper functions
source scripts/ai-coordination-helpers.sh

# 3. Test setup
ai-status
check-claims

# 4. Test Git aliases
git cursor-feat "test coordination setup"
```

## Your Primary Work Areas

- `src/components/` - React components and UI
- `app/` - Next.js pages and routes (UI parts)
- `src/styles/` - CSS and styling
- `public/` - Static assets
- `tests/` - Test implementation

## Daily Workflow

### Starting Work
```bash
source scripts/ai-coordination-helpers.sh
ai-status
# Edit .ai-coordination.md to claim work area
git cursor-branch feature-name
```

### During Development
```bash
git cursor-feat "implement component"
git cursor-ui "add animations"
update-log "Completed user interface components"
```

### Completing Work
```bash
# Remove claim from .ai-coordination.md
update-log "Feature complete - ready for integration"
```

## Validation

Run this to verify your setup:
```bash
./scripts/validate-coordination-setup.sh
```

All tests should pass (✅)
EOF

print_success "Claude Code setup instructions created"

# Step 9: Final validation
print_step "9" "Running final validation"

./scripts/validate-coordination-setup.sh

# Step 10: Test helper functions
print_step "10" "Testing helper functions"

source scripts/ai-coordination-helpers.sh
echo ""
echo "Testing ai-status function:"
ai-status

echo ""
echo "Testing check-claims function:"
check-claims

# Final summary
echo ""
echo "🎉 AI Coordination System Installation Complete!"
echo "==============================================="
echo ""
print_success "All core files created and configured"
print_success "Git aliases and templates set up"
print_success "Helper functions installed and tested"
print_success "Validation scripts ready"
print_success "Claude Code integration prepared"
echo ""
echo "📋 Quick Start Commands:"
echo "source scripts/ai-coordination-helpers.sh  # Load helper functions"
echo "ai-status                                   # Check coordination status"
echo "check-claims                                # View current work claims"
echo "git augment-feat 'description'             # Make Augment Agent commits"
echo "git cursor-feat 'description'              # Make Claude Code commits"
echo ""
echo "📖 Next Steps:"
echo "1. Review .ai-coordination.md for work area assignments"
echo "2. Share CLAUDE_CODE_SETUP_INSTRUCTIONS.md with Claude Code"
echo "3. Begin coordinated development following the established protocols"
echo ""
echo "✅ The AI coordination system is now fully operational!"
