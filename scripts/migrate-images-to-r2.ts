#!/usr/bin/env tsx

/**
 * Syndicaps Image Migration Script
 * 
 * Migrates images from Firebase Storage to Cloudflare R2
 * with comprehensive progress tracking and rollback capabilities.
 */

import { initializeApp } from 'firebase/app'
import { getStorage, ref, getDownloadURL, listAll } from 'firebase/storage'
import { getFirestore, collection, getDocs, updateDoc, doc, writeBatch } from 'firebase/firestore'
import { r2Storage } from '../src/lib/cloudflare/r2Storage'
import fs from 'fs/promises'
import path from 'path'

interface MigrationProgress {
  total: number
  migrated: number
  failed: number
  skipped: number
  errors: MigrationError[]
  startTime: number
  estimatedTimeRemaining: number
}

interface MigrationError {
  id: string
  url: string
  error: string
  timestamp: number
}

interface MigrationConfig {
  batchSize: number
  maxRetries: number
  delayBetweenBatches: number
  dryRun: boolean
  collections: string[]
  backupEnabled: boolean
}

class ImageMigrationService {
  private progress: MigrationProgress
  private config: MigrationConfig
  private db: any
  private storage: any
  private logFile: string

  constructor(config: Partial<MigrationConfig> = {}) {
    this.config = {
      batchSize: 10,
      maxRetries: 3,
      delayBetweenBatches: 1000,
      dryRun: false,
      collections: ['products', 'users', 'community'],
      backupEnabled: true,
      ...config
    }

    this.progress = {
      total: 0,
      migrated: 0,
      failed: 0,
      skipped: 0,
      errors: [],
      startTime: Date.now(),
      estimatedTimeRemaining: 0
    }

    this.logFile = `migration-log-${new Date().toISOString().split('T')[0]}.json`
  }

  /**
   * Initialize Firebase services
   */
  async initialize(): Promise<void> {
    try {
      const firebaseConfig = {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
      }

      const app = initializeApp(firebaseConfig)
      this.db = getFirestore(app)
      this.storage = getStorage(app)

      console.log('✅ Firebase initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Firebase:', error)
      throw error
    }
  }

  /**
   * Test R2 connection
   */
  async testR2Connection(): Promise<void> {
    console.log('🔍 Testing R2 connection...')
    
    const isConnected = await r2Storage.testConnection()
    
    if (!isConnected) {
      throw new Error('R2 connection test failed')
    }
    
    console.log('✅ R2 connection test passed')
  }

  /**
   * Scan collections for images
   */
  async scanForImages(): Promise<string[]> {
    console.log('🔍 Scanning collections for images...')
    
    const imageUrls: string[] = []
    
    for (const collectionName of this.config.collections) {
      console.log(`  Scanning ${collectionName}...`)
      
      const snapshot = await getDocs(collection(this.db, collectionName))
      
      snapshot.forEach(doc => {
        const data = doc.data()
        
        // Extract image URLs from various fields
        const urls = this.extractImageUrls(data)
        imageUrls.push(...urls)
      })
    }
    
    // Remove duplicates and filter Firebase Storage URLs
    const uniqueUrls = [...new Set(imageUrls)]
    const firebaseUrls = uniqueUrls.filter(url => 
      url.includes('firebasestorage.googleapis.com') ||
      url.includes('firebase.com')
    )
    
    console.log(`📊 Found ${firebaseUrls.length} Firebase images to migrate`)
    return firebaseUrls
  }

  /**
   * Extract image URLs from document data
   */
  private extractImageUrls(data: any): string[] {
    const urls: string[] = []
    
    const checkValue = (value: any) => {
      if (typeof value === 'string' && this.isImageUrl(value)) {
        urls.push(value)
      } else if (Array.isArray(value)) {
        value.forEach(checkValue)
      } else if (typeof value === 'object' && value !== null) {
        Object.values(value).forEach(checkValue)
      }
    }
    
    checkValue(data)
    return urls
  }

  /**
   * Check if string is an image URL
   */
  private isImageUrl(url: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.avif', '.svg']
    return imageExtensions.some(ext => url.toLowerCase().includes(ext)) ||
           url.includes('firebasestorage.googleapis.com')
  }

  /**
   * Migrate single image
   */
  async migrateImage(firebaseUrl: string, retryCount = 0): Promise<string | null> {
    try {
      // Generate R2 key from Firebase URL
      const key = this.generateR2Key(firebaseUrl)
      
      // Check if already migrated
      if (await r2Storage.objectExists(key)) {
        console.log(`⏭️  Skipping ${key} (already exists)`)
        this.progress.skipped++
        return `${process.env.R2_PUBLIC_URL}/${key}`
      }
      
      if (this.config.dryRun) {
        console.log(`🔍 [DRY RUN] Would migrate: ${firebaseUrl} -> ${key}`)
        return firebaseUrl
      }
      
      // Download from Firebase
      console.log(`⬇️  Downloading: ${firebaseUrl}`)
      const response = await fetch(firebaseUrl)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const imageBuffer = Buffer.from(await response.arrayBuffer())
      const contentType = response.headers.get('content-type') || 'image/jpeg'
      
      // Upload to R2
      console.log(`⬆️  Uploading to R2: ${key}`)
      const result = await r2Storage.uploadImage(imageBuffer, key, {
        contentType,
        cacheControl: 'public, max-age=31536000',
        metadata: {
          'original-url': firebaseUrl,
          'migrated-at': new Date().toISOString()
        }
      })
      
      this.progress.migrated++
      console.log(`✅ Migrated: ${firebaseUrl} -> ${result.url}`)
      
      return result.url
      
    } catch (error) {
      console.error(`❌ Migration failed for ${firebaseUrl}:`, error)
      
      this.progress.errors.push({
        id: this.generateR2Key(firebaseUrl),
        url: firebaseUrl,
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now()
      })
      
      // Retry logic
      if (retryCount < this.config.maxRetries) {
        console.log(`🔄 Retrying ${firebaseUrl} (attempt ${retryCount + 1}/${this.config.maxRetries})`)
        await this.delay(1000 * (retryCount + 1)) // Exponential backoff
        return this.migrateImage(firebaseUrl, retryCount + 1)
      }
      
      this.progress.failed++
      return null
    }
  }

  /**
   * Generate R2 key from Firebase URL
   */
  private generateR2Key(firebaseUrl: string): string {
    try {
      const url = new URL(firebaseUrl)
      const pathParts = url.pathname.split('/')
      
      // Extract meaningful path from Firebase URL
      const fileName = pathParts[pathParts.length - 1]
      const folder = pathParts[pathParts.length - 2] || 'images'
      
      // Clean up the filename
      const cleanFileName = fileName.split('?')[0] // Remove query params
      
      return `migrated/${folder}/${cleanFileName}`
    } catch (error) {
      // Fallback to hash-based key
      const hash = Buffer.from(firebaseUrl).toString('base64').replace(/[^a-zA-Z0-9]/g, '')
      return `migrated/unknown/${hash}.jpg`
    }
  }

  /**
   * Update database references
   */
  async updateDatabaseReferences(urlMappings: Map<string, string>): Promise<void> {
    if (this.config.dryRun) {
      console.log('🔍 [DRY RUN] Would update database references')
      return
    }
    
    console.log('📝 Updating database references...')
    
    for (const collectionName of this.config.collections) {
      console.log(`  Updating ${collectionName}...`)
      
      const snapshot = await getDocs(collection(this.db, collectionName))
      const batch = writeBatch(this.db)
      let batchCount = 0
      
      for (const docSnapshot of snapshot.docs) {
        const data = docSnapshot.data()
        const updatedData = this.replaceImageUrls(data, urlMappings)
        
        if (JSON.stringify(data) !== JSON.stringify(updatedData)) {
          batch.update(doc(this.db, collectionName, docSnapshot.id), updatedData)
          batchCount++
          
          // Commit batch when it reaches limit
          if (batchCount >= 500) {
            await batch.commit()
            console.log(`    Committed batch of ${batchCount} updates`)
            batchCount = 0
          }
        }
      }
      
      // Commit remaining updates
      if (batchCount > 0) {
        await batch.commit()
        console.log(`    Committed final batch of ${batchCount} updates`)
      }
    }
    
    console.log('✅ Database references updated')
  }

  /**
   * Replace image URLs in data object
   */
  private replaceImageUrls(data: any, urlMappings: Map<string, string>): any {
    if (typeof data === 'string' && urlMappings.has(data)) {
      return urlMappings.get(data)
    } else if (Array.isArray(data)) {
      return data.map(item => this.replaceImageUrls(item, urlMappings))
    } else if (typeof data === 'object' && data !== null) {
      const result: any = {}
      for (const [key, value] of Object.entries(data)) {
        result[key] = this.replaceImageUrls(value, urlMappings)
      }
      return result
    }
    
    return data
  }

  /**
   * Create backup before migration
   */
  async createBackup(): Promise<void> {
    if (!this.config.backupEnabled) {
      console.log('⏭️  Backup disabled, skipping...')
      return
    }
    
    console.log('💾 Creating backup...')
    
    const backup: any = {}
    
    for (const collectionName of this.config.collections) {
      const snapshot = await getDocs(collection(this.db, collectionName))
      backup[collectionName] = {}
      
      snapshot.forEach(doc => {
        backup[collectionName][doc.id] = doc.data()
      })
    }
    
    const backupKey = `backups/pre-migration-${Date.now()}.json`
    await r2Storage.uploadBackup(
      JSON.stringify(backup, null, 2),
      backupKey,
      {
        contentType: 'application/json',
        metadata: {
          'backup-type': 'pre-migration',
          'collections': this.config.collections.join(','),
          'created-at': new Date().toISOString()
        }
      }
    )
    
    console.log(`✅ Backup created: ${backupKey}`)
  }

  /**
   * Run migration process
   */
  async migrate(): Promise<void> {
    try {
      console.log('🚀 Starting image migration to Cloudflare R2...')
      
      await this.initialize()
      await this.testR2Connection()
      
      if (this.config.backupEnabled) {
        await this.createBackup()
      }
      
      const imageUrls = await this.scanForImages()
      this.progress.total = imageUrls.length
      
      if (imageUrls.length === 0) {
        console.log('ℹ️  No images found to migrate')
        return
      }
      
      console.log(`📊 Migration plan:`)
      console.log(`   Total images: ${this.progress.total}`)
      console.log(`   Batch size: ${this.config.batchSize}`)
      console.log(`   Dry run: ${this.config.dryRun}`)
      
      const urlMappings = new Map<string, string>()
      
      // Process images in batches
      for (let i = 0; i < imageUrls.length; i += this.config.batchSize) {
        const batch = imageUrls.slice(i, i + this.config.batchSize)
        
        console.log(`\n📦 Processing batch ${Math.floor(i / this.config.batchSize) + 1}/${Math.ceil(imageUrls.length / this.config.batchSize)}`)
        
        const promises = batch.map(url => this.migrateImage(url))
        const results = await Promise.all(promises)
        
        // Map successful migrations
        batch.forEach((originalUrl, index) => {
          const newUrl = results[index]
          if (newUrl) {
            urlMappings.set(originalUrl, newUrl)
          }
        })
        
        // Update progress
        this.updateProgress()
        
        // Delay between batches
        if (i + this.config.batchSize < imageUrls.length) {
          await this.delay(this.config.delayBetweenBatches)
        }
      }
      
      // Update database references
      if (urlMappings.size > 0) {
        await this.updateDatabaseReferences(urlMappings)
      }
      
      // Generate final report
      await this.generateReport()
      
      console.log('\n🎉 Migration completed!')
      this.printSummary()
      
    } catch (error) {
      console.error('💥 Migration failed:', error)
      await this.generateReport()
      throw error
    }
  }

  /**
   * Update progress and estimate time remaining
   */
  private updateProgress(): void {
    const completed = this.progress.migrated + this.progress.failed + this.progress.skipped
    const elapsed = Date.now() - this.progress.startTime
    const rate = completed / elapsed // items per ms
    const remaining = this.progress.total - completed
    
    this.progress.estimatedTimeRemaining = remaining / rate
    
    console.log(`📊 Progress: ${completed}/${this.progress.total} (${Math.round(completed / this.progress.total * 100)}%)`)
    console.log(`   ✅ Migrated: ${this.progress.migrated}`)
    console.log(`   ⏭️  Skipped: ${this.progress.skipped}`)
    console.log(`   ❌ Failed: ${this.progress.failed}`)
    
    if (this.progress.estimatedTimeRemaining > 0) {
      const eta = new Date(Date.now() + this.progress.estimatedTimeRemaining)
      console.log(`   ⏱️  ETA: ${eta.toLocaleTimeString()}`)
    }
  }

  /**
   * Generate migration report
   */
  async generateReport(): Promise<void> {
    const report = {
      timestamp: new Date().toISOString(),
      config: this.config,
      progress: this.progress,
      duration: Date.now() - this.progress.startTime,
      successRate: this.progress.total > 0 ? 
        (this.progress.migrated / this.progress.total) * 100 : 0
    }
    
    await fs.writeFile(this.logFile, JSON.stringify(report, null, 2))
    console.log(`📄 Report saved to: ${this.logFile}`)
  }

  /**
   * Print migration summary
   */
  private printSummary(): void {
    const duration = Date.now() - this.progress.startTime
    const durationMinutes = Math.round(duration / 60000)
    
    console.log('\n📊 Migration Summary:')
    console.log(`   Duration: ${durationMinutes} minutes`)
    console.log(`   Total: ${this.progress.total}`)
    console.log(`   ✅ Migrated: ${this.progress.migrated}`)
    console.log(`   ⏭️  Skipped: ${this.progress.skipped}`)
    console.log(`   ❌ Failed: ${this.progress.failed}`)
    console.log(`   Success Rate: ${Math.round((this.progress.migrated / this.progress.total) * 100)}%`)
    
    if (this.progress.errors.length > 0) {
      console.log('\n❌ Errors:')
      this.progress.errors.slice(0, 5).forEach(error => {
        console.log(`   ${error.id}: ${error.error}`)
      })
      
      if (this.progress.errors.length > 5) {
        console.log(`   ... and ${this.progress.errors.length - 5} more errors`)
      }
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// CLI execution
async function main() {
  const args = process.argv.slice(2)
  const dryRun = args.includes('--dry-run')
  const batchSize = parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1] || '10')
  
  const migrationService = new ImageMigrationService({
    dryRun,
    batchSize,
    backupEnabled: !args.includes('--no-backup')
  })
  
  try {
    await migrationService.migrate()
    process.exit(0)
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
