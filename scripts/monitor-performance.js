#!/usr/bin/env node

/**
 * Performance Monitoring Script for Cloudflare Hybrid Deployment
 * Monitors and reports on CDN performance, cache hit ratios, and feature flag metrics
 */

const https = require('https');
const fs = require('fs').promises;
const path = require('path');
const { performance } = require('perf_hooks');

// Configuration
const config = {
  domain: process.env.MONITOR_DOMAIN || 'syndicaps.com',
  interval: parseInt(process.env.MONITOR_INTERVAL) || 300000, // 5 minutes
  logFile: process.env.MONITOR_LOG_FILE || 'performance-monitor.log',
  alertThresholds: {
    responseTime: 2000, // ms
    errorRate: 5, // percentage
    cacheHitRatio: 70, // percentage
  },
};

// Monitoring state
const state = {
  isRunning: false,
  intervalId: null,
  metrics: [],
  alerts: [],
};

// Utility functions
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}`;
  console.log(logEntry);
  
  // Append to log file
  appendToLogFile(logEntry);
}

async function appendToLogFile(entry) {
  try {
    await fs.appendFile(config.logFile, entry + '\n');
  } catch (error) {
    console.error('Failed to write to log file:', error.message);
  }
}

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 10000,
    }, (res) => {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body,
          responseTime,
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.end();
  });
}

// Collect performance metrics
async function collectMetrics() {
  const timestamp = new Date().toISOString();
  const metrics = {
    timestamp,
    domain: config.domain,
    responseTime: null,
    statusCode: null,
    cacheStatus: null,
    compression: null,
    securityHeaders: {},
    errors: [],
  };

  try {
    // Test main page
    log('Collecting performance metrics...');
    const response = await makeRequest(`https://${config.domain}`);
    
    metrics.responseTime = response.responseTime;
    metrics.statusCode = response.statusCode;
    metrics.cacheStatus = response.headers['cf-cache-status'];
    metrics.compression = response.headers['content-encoding'];
    
    // Check security headers
    const securityHeaders = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'content-security-policy',
    ];
    
    securityHeaders.forEach(header => {
      metrics.securityHeaders[header] = !!response.headers[header];
    });
    
    // Test API endpoints
    const apiEndpoints = [
      '/api/health',
      '/api/cache/analytics',
      '/api/feature-flags',
    ];
    
    for (const endpoint of apiEndpoints) {
      try {
        const apiResponse = await makeRequest(`https://${config.domain}${endpoint}`);
        metrics[`api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`] = {
          statusCode: apiResponse.statusCode,
          responseTime: apiResponse.responseTime,
        };
      } catch (error) {
        metrics.errors.push(`API ${endpoint}: ${error.message}`);
      }
    }
    
    log(`Metrics collected - Response time: ${metrics.responseTime.toFixed(2)}ms, Status: ${metrics.statusCode}`);
    
  } catch (error) {
    metrics.errors.push(`Main request failed: ${error.message}`);
    log(`Error collecting metrics: ${error.message}`, 'ERROR');
  }
  
  return metrics;
}

// Get cache analytics from API
async function getCacheAnalytics() {
  try {
    const response = await makeRequest(`https://${config.domain}/api/cache/analytics?period=1h`);
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.body);
      if (data.success) {
        return data.data;
      }
    }
    
    return null;
  } catch (error) {
    log(`Failed to get cache analytics: ${error.message}`, 'WARN');
    return null;
  }
}

// Check for performance alerts
function checkAlerts(metrics, cacheAnalytics) {
  const alerts = [];
  
  // Response time alert
  if (metrics.responseTime && metrics.responseTime > config.alertThresholds.responseTime) {
    alerts.push({
      type: 'HIGH_RESPONSE_TIME',
      message: `Response time ${metrics.responseTime.toFixed(2)}ms exceeds threshold ${config.alertThresholds.responseTime}ms`,
      severity: 'WARNING',
      timestamp: metrics.timestamp,
    });
  }
  
  // Error rate alert
  if (metrics.statusCode && metrics.statusCode >= 400) {
    alerts.push({
      type: 'HTTP_ERROR',
      message: `HTTP error status: ${metrics.statusCode}`,
      severity: 'ERROR',
      timestamp: metrics.timestamp,
    });
  }
  
  // Cache hit ratio alert
  if (cacheAnalytics && cacheAnalytics.cacheHitRatio < config.alertThresholds.cacheHitRatio) {
    alerts.push({
      type: 'LOW_CACHE_HIT_RATIO',
      message: `Cache hit ratio ${cacheAnalytics.cacheHitRatio.toFixed(1)}% below threshold ${config.alertThresholds.cacheHitRatio}%`,
      severity: 'WARNING',
      timestamp: metrics.timestamp,
    });
  }
  
  // Security headers alert
  const missingHeaders = Object.entries(metrics.securityHeaders)
    .filter(([header, present]) => !present)
    .map(([header]) => header);
  
  if (missingHeaders.length > 0) {
    alerts.push({
      type: 'MISSING_SECURITY_HEADERS',
      message: `Missing security headers: ${missingHeaders.join(', ')}`,
      severity: 'WARNING',
      timestamp: metrics.timestamp,
    });
  }
  
  return alerts;
}

// Process and log alerts
function processAlerts(alerts) {
  alerts.forEach(alert => {
    log(`ALERT [${alert.severity}] ${alert.type}: ${alert.message}`, 'ALERT');
    state.alerts.push(alert);
  });
  
  // Keep only recent alerts (last 24 hours)
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  state.alerts = state.alerts.filter(alert => new Date(alert.timestamp) > oneDayAgo);
}

// Generate performance report
function generateReport() {
  const recentMetrics = state.metrics.slice(-12); // Last 12 measurements (1 hour if 5min intervals)
  
  if (recentMetrics.length === 0) {
    return 'No metrics available';
  }
  
  const avgResponseTime = recentMetrics
    .filter(m => m.responseTime)
    .reduce((sum, m) => sum + m.responseTime, 0) / recentMetrics.length;
  
  const errorCount = recentMetrics.filter(m => m.statusCode >= 400).length;
  const errorRate = (errorCount / recentMetrics.length) * 100;
  
  const cacheHits = recentMetrics.filter(m => m.cacheStatus === 'HIT').length;
  const cacheHitRate = (cacheHits / recentMetrics.length) * 100;
  
  const recentAlerts = state.alerts.filter(alert => 
    new Date(alert.timestamp) > new Date(Date.now() - 60 * 60 * 1000) // Last hour
  );
  
  return `
Performance Report (Last Hour):
==============================
Average Response Time: ${avgResponseTime.toFixed(2)}ms
Error Rate: ${errorRate.toFixed(1)}%
Cache Hit Rate: ${cacheHitRate.toFixed(1)}%
Recent Alerts: ${recentAlerts.length}
Total Measurements: ${recentMetrics.length}
Last Updated: ${new Date().toISOString()}
`;
}

// Main monitoring function
async function runMonitoring() {
  try {
    // Collect metrics
    const metrics = await collectMetrics();
    state.metrics.push(metrics);
    
    // Get cache analytics
    const cacheAnalytics = await getCacheAnalytics();
    
    // Check for alerts
    const alerts = checkAlerts(metrics, cacheAnalytics);
    processAlerts(alerts);
    
    // Keep only recent metrics (last 24 hours)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    state.metrics = state.metrics.filter(m => new Date(m.timestamp) > oneDayAgo);
    
    // Log summary
    if (cacheAnalytics) {
      log(`Cache Analytics - Hit Ratio: ${cacheAnalytics.cacheHitRatio.toFixed(1)}%, Requests: ${cacheAnalytics.requests}`);
    }
    
    if (alerts.length > 0) {
      log(`Generated ${alerts.length} alerts`, 'WARN');
    }
    
  } catch (error) {
    log(`Monitoring error: ${error.message}`, 'ERROR');
  }
}

// Start monitoring
function startMonitoring() {
  if (state.isRunning) {
    log('Monitoring is already running', 'WARN');
    return;
  }
  
  log(`Starting performance monitoring for ${config.domain}`);
  log(`Monitoring interval: ${config.interval / 1000} seconds`);
  log(`Log file: ${config.logFile}`);
  
  state.isRunning = true;
  
  // Run initial monitoring
  runMonitoring();
  
  // Set up interval
  state.intervalId = setInterval(runMonitoring, config.interval);
  
  log('Performance monitoring started');
}

// Stop monitoring
function stopMonitoring() {
  if (!state.isRunning) {
    log('Monitoring is not running', 'WARN');
    return;
  }
  
  if (state.intervalId) {
    clearInterval(state.intervalId);
    state.intervalId = null;
  }
  
  state.isRunning = false;
  log('Performance monitoring stopped');
}

// CLI interface
function handleCommand(command) {
  switch (command) {
    case 'start':
      startMonitoring();
      break;
    case 'stop':
      stopMonitoring();
      break;
    case 'status':
      log(`Monitoring status: ${state.isRunning ? 'RUNNING' : 'STOPPED'}`);
      log(`Metrics collected: ${state.metrics.length}`);
      log(`Active alerts: ${state.alerts.length}`);
      break;
    case 'report':
      console.log(generateReport());
      break;
    case 'test':
      runMonitoring();
      break;
    default:
      console.log(`
Usage: node monitor-performance.js [command]

Commands:
  start   - Start continuous monitoring
  stop    - Stop monitoring
  status  - Show monitoring status
  report  - Generate performance report
  test    - Run single monitoring cycle

Environment Variables:
  MONITOR_DOMAIN    - Domain to monitor (default: syndicaps.com)
  MONITOR_INTERVAL  - Monitoring interval in ms (default: 300000)
  MONITOR_LOG_FILE  - Log file path (default: performance-monitor.log)
`);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('Received SIGINT, stopping monitoring...');
  stopMonitoring();
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM, stopping monitoring...');
  stopMonitoring();
  process.exit(0);
});

// Run CLI
if (require.main === module) {
  const command = process.argv[2] || 'help';
  handleCommand(command);
}

module.exports = {
  startMonitoring,
  stopMonitoring,
  runMonitoring,
  generateReport,
  config,
  state,
};
