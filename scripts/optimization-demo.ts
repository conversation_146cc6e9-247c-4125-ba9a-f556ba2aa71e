#!/usr/bin/env tsx
/**
 * Optimization System Demo
 * Comprehensive demonstration of the optimization system with real scenarios
 */

import { HybridPerformanceOptimizer, OptimizerConfig } from '../lib/optimization/HybridPerformanceOptimizer'
import { OptimizationRules } from '../lib/optimization/OptimizationRules'
import { OptimizationScheduler, SchedulerConfig } from '../lib/optimization/OptimizationScheduler'
import { OptimizationTracker, TrackerConfig, MetricSnapshot } from '../lib/optimization/OptimizationTracker'

class OptimizationDemo {
  private optimizer: HybridPerformanceOptimizer
  private scheduler: OptimizationScheduler
  private tracker: OptimizationTracker

  constructor() {
    // Initialize configurations
    const optimizerConfig: OptimizerConfig = {
      metricsCollectionInterval: 5000,
      maxConcurrentOptimizations: 10,
      enableRollback: true,
      validationTimeout: 30000
    }

    const schedulerConfig: SchedulerConfig = {
      maxHistorySize: 1000,
      enableLogging: true,
      logLevel: 'info'
    }

    const trackerConfig: TrackerConfig = {
      maxRecords: 10000,
      exportPath: './optimization-exports',
      reportsPath: './optimization-reports',
      enableRealTimeTracking: true
    }

    // Initialize components
    this.optimizer = new HybridPerformanceOptimizer(optimizerConfig)
    this.tracker = new OptimizationTracker(trackerConfig)
    this.scheduler = new OptimizationScheduler(this.optimizer, schedulerConfig)
  }

  /**
   * Run the complete optimization demo
   */
  async runDemo(): Promise<void> {
    console.log('🚀 Starting Optimization System Demo')
    console.log('=====================================')

    try {
      // Step 1: Initialize the system
      await this.initializeSystem()

      // Step 2: Demonstrate rule management
      await this.demonstrateRuleManagement()

      // Step 3: Simulate performance scenarios
      await this.simulatePerformanceScenarios()

      // Step 4: Demonstrate scheduling
      await this.demonstrateScheduling()

      // Step 5: Generate reports
      await this.generateReports()

      // Step 6: Show system statistics
      await this.showSystemStatistics()

      console.log('\n✅ Optimization System Demo Completed Successfully!')
      console.log('Check the generated reports in ./optimization-reports/')

    } catch (error) {
      console.error('❌ Demo failed:', error)
      throw error
    }
  }

  /**
   * Initialize the optimization system
   */
  private async initializeSystem(): Promise<void> {
    console.log('\n📋 Step 1: Initializing Optimization System')
    console.log('-------------------------------------------')

    await this.optimizer.initialize()
    console.log('✅ Optimizer initialized')

    // Load all predefined rules
    const allRules = OptimizationRules.getAllRules()
    console.log(`📚 Loading ${allRules.length} optimization rules...`)

    allRules.forEach(rule => {
      this.optimizer.addRule(rule)
      console.log(`   ➕ Added rule: ${rule.name} (${rule.category})`)
    })

    console.log(`✅ Loaded ${allRules.length} optimization rules`)
  }

  /**
   * Demonstrate rule management capabilities
   */
  private async demonstrateRuleManagement(): Promise<void> {
    console.log('\n🔧 Step 2: Demonstrating Rule Management')
    console.log('----------------------------------------')

    // Show rules by category
    const categories = ['cache', 'image', 'api', 'cost', 'security']
    for (const category of categories) {
      const categoryRules = OptimizationRules.getRulesByCategory(category as any)
      console.log(`   ${category.toUpperCase()}: ${categoryRules.length} rules`)
    }

    // Show high priority rules
    const highPriorityRules = OptimizationRules.getRulesByPriority(80, 100)
    console.log(`   HIGH PRIORITY: ${highPriorityRules.length} rules`)

    // Show enabled rules
    const enabledRules = OptimizationRules.getEnabledRules()
    console.log(`   ENABLED: ${enabledRules.length} rules`)

    // Create a custom rule
    const customRule = OptimizationRules.createCustomRule(
      'demo-custom-001',
      'Demo Custom Rule',
      'Custom rule created for demonstration',
      'cache',
      85
    )
    console.log(`   ➕ Created custom rule: ${customRule.name}`)
  }

  /**
   * Simulate various performance scenarios
   */
  private async simulatePerformanceScenarios(): Promise<void> {
    console.log('\n🎭 Step 3: Simulating Performance Scenarios')
    console.log('-------------------------------------------')

    const scenarios = [
      {
        name: 'High Response Time Scenario',
        beforeMetrics: this.createMetricSnapshot({
          responseTime: 2500,
          errorRate: 3,
          cacheHitRate: 65,
          throughput: 80
        }),
        afterMetrics: this.createMetricSnapshot({
          responseTime: 800,
          errorRate: 1,
          cacheHitRate: 92,
          throughput: 150
        })
      },
      {
        name: 'Low Cache Hit Rate Scenario',
        beforeMetrics: this.createMetricSnapshot({
          responseTime: 1200,
          errorRate: 2,
          cacheHitRate: 45,
          throughput: 100
        }),
        afterMetrics: this.createMetricSnapshot({
          responseTime: 600,
          errorRate: 1,
          cacheHitRate: 88,
          throughput: 180
        })
      },
      {
        name: 'High Error Rate Scenario',
        beforeMetrics: this.createMetricSnapshot({
          responseTime: 1000,
          errorRate: 12,
          cacheHitRate: 75,
          throughput: 90
        }),
        afterMetrics: this.createMetricSnapshot({
          responseTime: 900,
          errorRate: 2,
          cacheHitRate: 78,
          throughput: 110
        })
      }
    ]

    for (const scenario of scenarios) {
      console.log(`\n   🎬 Running: ${scenario.name}`)
      
      // Take before snapshot
      await this.tracker.takeMetricSnapshot(`before-${scenario.name}`)
      
      // Execute optimization cycle
      const results = await this.optimizer.executeOptimizationCycle()
      console.log(`      🔄 Executed ${results.length} optimizations`)
      
      // Record impacts for successful optimizations
      const successfulResults = results.filter(r => r.success)
      for (const result of successfulResults) {
        const impact = this.calculateImpact(scenario.beforeMetrics, scenario.afterMetrics)
        
        await this.tracker.recordImpact(
          { ...result, impact },
          `Scenario Rule ${result.ruleId}`,
          'cache', // Simplified for demo
          scenario.beforeMetrics,
          scenario.afterMetrics
        )
      }
      
      console.log(`      ✅ Recorded ${successfulResults.length} successful optimizations`)
      
      // Show improvements
      const performanceImprovement = this.calculatePerformanceImprovement(
        scenario.beforeMetrics,
        scenario.afterMetrics
      )
      console.log(`      📈 Performance improvement: ${performanceImprovement.toFixed(1)}%`)
    }
  }

  /**
   * Demonstrate scheduling capabilities
   */
  private async demonstrateScheduling(): Promise<void> {
    console.log('\n⏰ Step 4: Demonstrating Scheduling')
    console.log('----------------------------------')

    // Start scheduler
    await this.scheduler.start()
    console.log('✅ Scheduler started')

    // Execute a manual schedule
    const execution = await this.scheduler.executeScheduleManually('hourly-performance-check')
    console.log(`   🔧 Manual execution: ${execution.status}`)
    console.log(`   ⏱️  Execution time: ${execution.endTime ? 
      (execution.endTime.getTime() - execution.startTime.getTime()) : 'N/A'}ms`)

    // Show scheduler statistics
    const scheduleStats = this.scheduler.getScheduleStats()
    console.log(`   📊 Total schedules: ${scheduleStats.totalSchedules}`)
    console.log(`   ✅ Enabled schedules: ${scheduleStats.enabledSchedules}`)
    console.log(`   🏃 Active schedules: ${scheduleStats.activeSchedules}`)
    console.log(`   📈 Success rate: ${scheduleStats.successRate.toFixed(1)}%`)

    // Stop scheduler for demo
    await this.scheduler.stop()
    console.log('⏹️  Scheduler stopped')
  }

  /**
   * Generate comprehensive reports
   */
  private async generateReports(): Promise<void> {
    console.log('\n📊 Step 5: Generating Reports')
    console.log('-----------------------------')

    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000) // 24 hours ago

    // Generate impact summary
    const summary = this.tracker.generateImpactSummary(startDate, endDate)
    console.log(`   📋 Impact Summary:`)
    console.log(`      Total optimizations: ${summary.totalOptimizations}`)
    console.log(`      Success rate: ${summary.successRate.toFixed(1)}%`)
    console.log(`      Average performance impact: ${summary.averageImpact.performanceChange.toFixed(1)}%`)
    console.log(`      Categories analyzed: ${summary.categoryBreakdown.length}`)
    console.log(`      Top performing rules: ${summary.topPerformingRules.length}`)
    console.log(`      Recommendations: ${summary.recommendations.length}`)

    // Generate comprehensive report
    const report = await this.tracker.generateImpactReport(startDate, endDate)
    console.log(`   📄 Generated comprehensive report: ${report.id}`)
    console.log(`      Title: ${report.title}`)
    console.log(`      Visualizations: ${report.visualizations.length}`)
    console.log(`      Recommendation sections: ${report.recommendations.length}`)

    // Export data
    const jsonExport = await this.tracker.exportImpactData('json', startDate, endDate)
    console.log(`   💾 Exported JSON data: ${jsonExport}`)

    const csvExport = await this.tracker.exportImpactData('csv', startDate, endDate)
    console.log(`   💾 Exported CSV data: ${csvExport}`)
  }

  /**
   * Show system statistics
   */
  private async showSystemStatistics(): Promise<void> {
    console.log('\n📈 Step 6: System Statistics')
    console.log('----------------------------')

    // Optimizer statistics
    const optimizerStats = this.optimizer.getOptimizationStats()
    console.log(`   🔧 Optimizer Statistics:`)
    console.log(`      Total rules: ${optimizerStats.totalRules}`)
    console.log(`      Enabled rules: ${optimizerStats.enabledRules}`)
    console.log(`      Total executions: ${optimizerStats.totalExecutions}`)
    console.log(`      Success rate: ${optimizerStats.successRate.toFixed(1)}%`)
    console.log(`      Average performance impact: ${optimizerStats.averagePerformanceImpact.toFixed(1)}%`)

    // Tracker statistics
    const trackerStats = this.tracker.getOptimizationStats()
    console.log(`   📊 Tracker Statistics:`)
    console.log(`      Total optimizations tracked: ${trackerStats.totalOptimizations}`)
    console.log(`      Successful optimizations: ${trackerStats.successfulOptimizations}`)
    console.log(`      Success rate: ${trackerStats.successRate.toFixed(1)}%`)
    console.log(`      Average performance impact: ${trackerStats.averagePerformanceImpact.toFixed(1)}%`)
    console.log(`      Average cost impact: ${trackerStats.averageCostImpact.toFixed(1)}%`)
    console.log(`      Average reliability impact: ${trackerStats.averageReliabilityImpact.toFixed(1)}%`)

    // Show category breakdown
    console.log(`   📂 Category Breakdown:`)
    Object.entries(trackerStats.categoryStats).forEach(([category, stats]: [string, any]) => {
      console.log(`      ${category.toUpperCase()}: ${stats.total} total, ${stats.successRate.toFixed(1)}% success`)
    })
  }

  /**
   * Create a metric snapshot with specified values
   */
  private createMetricSnapshot(overrides: Partial<MetricSnapshot>): MetricSnapshot {
    return {
      timestamp: new Date(),
      responseTime: 1000,
      errorRate: 2,
      cacheHitRate: 80,
      throughput: 100,
      cpuUsage: 50,
      memoryUsage: 60,
      bandwidthUsage: 70,
      cost: 100,
      customMetrics: {},
      ...overrides
    }
  }

  /**
   * Calculate impact between before and after metrics
   */
  private calculateImpact(before: MetricSnapshot, after: MetricSnapshot) {
    const performanceChange = ((before.responseTime - after.responseTime) / before.responseTime) * 100
    const costChange = ((before.cost - after.cost) / before.cost) * 100
    const reliabilityChange = ((before.errorRate - after.errorRate) / before.errorRate) * 100

    return {
      performanceChange,
      costChange,
      reliabilityChange,
      metrics: {
        responseTime: after.responseTime,
        errorRate: after.errorRate,
        cacheHitRate: after.cacheHitRate,
        throughput: after.throughput
      }
    }
  }

  /**
   * Calculate performance improvement percentage
   */
  private calculatePerformanceImprovement(before: MetricSnapshot, after: MetricSnapshot): number {
    return ((before.responseTime - after.responseTime) / before.responseTime) * 100
  }
}

// Run the demo if this script is executed directly
if (require.main === module) {
  const demo = new OptimizationDemo()
  demo.runDemo().catch(error => {
    console.error('Demo failed:', error)
    process.exit(1)
  })
}
