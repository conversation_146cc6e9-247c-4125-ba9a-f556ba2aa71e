/**
 * Performance Baseline Measurement Script
 * Measures current performance metrics before hybrid deployment
 */

import { performance } from 'perf_hooks'
import fetch from 'node-fetch'
import fs from 'fs/promises'
import path from 'path'

interface BaselineMetric {
  name: string
  value: number
  unit: string
  timestamp: string
  source: 'client' | 'server' | 'network' | 'database'
  category: 'response_time' | 'throughput' | 'resource_usage' | 'core_web_vitals'
  metadata?: Record<string, any>
}

interface BaselineReport {
  timestamp: string
  environment: string
  version: string
  metrics: BaselineMetric[]
  summary: {
    avgResponseTime: number
    totalRequests: number
    errorRate: number
    memoryUsage: number
    cpuUsage: number
  }
  recommendations: string[]
}

class PerformanceBaseline {
  private metrics: BaselineMetric[] = []
  private baseUrl: string
  private testEndpoints: string[]

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
    this.testEndpoints = [
      '/',
      '/shop',
      '/api/products',
      '/api/categories',
      '/api/auth/session',
      '/profile',
      '/admin/dashboard'
    ]
  }

  async measureBaseline(): Promise<BaselineReport> {
    console.log('🔍 Starting performance baseline measurement...')
    
    // Measure network performance
    await this.measureNetworkPerformance()
    
    // Measure API response times
    await this.measureApiPerformance()
    
    // Measure resource loading
    await this.measureResourcePerformance()
    
    // Measure system resources
    await this.measureSystemResources()
    
    // Generate report
    const report = this.generateReport()
    
    // Save report
    await this.saveReport(report)
    
    console.log('✅ Baseline measurement complete')
    return report
  }

  private async measureNetworkPerformance(): Promise<void> {
    console.log('📡 Measuring network performance...')
    
    for (const endpoint of this.testEndpoints) {
      const url = `${this.baseUrl}${endpoint}`
      const iterations = 5
      const responseTimes: number[] = []
      
      for (let i = 0; i < iterations; i++) {
        try {
          const startTime = performance.now()
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'User-Agent': 'Performance-Baseline-Tool/1.0'
            }
          })
          const endTime = performance.now()
          
          const responseTime = endTime - startTime
          responseTimes.push(responseTime)
          
          this.addMetric({
            name: `response_time_${endpoint.replace(/\//g, '_')}`,
            value: responseTime,
            unit: 'ms',
            source: 'network',
            category: 'response_time',
            metadata: {
              endpoint,
              status: response.status,
              iteration: i + 1
            }
          })
          
          // Small delay between requests
          await this.delay(100)
        } catch (error) {
          console.warn(`Failed to measure ${endpoint}:`, error)
          this.addMetric({
            name: `error_${endpoint.replace(/\//g, '_')}`,
            value: 1,
            unit: 'count',
            source: 'network',
            category: 'response_time',
            metadata: {
              endpoint,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          })
        }
      }
      
      if (responseTimes.length > 0) {
        const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
        this.addMetric({
          name: `avg_response_time_${endpoint.replace(/\//g, '_')}`,
          value: avgResponseTime,
          unit: 'ms',
          source: 'network',
          category: 'response_time',
          metadata: {
            endpoint,
            iterations,
            min: Math.min(...responseTimes),
            max: Math.max(...responseTimes)
          }
        })
      }
    }
  }

  private async measureApiPerformance(): Promise<void> {
    console.log('🔌 Measuring API performance...')
    
    const apiEndpoints = [
      '/api/products?limit=10',
      '/api/categories',
      '/api/auth/session',
      '/api/admin/stats'
    ]
    
    for (const endpoint of apiEndpoints) {
      const url = `${this.baseUrl}${endpoint}`
      
      try {
        const startTime = performance.now()
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Performance-Baseline-Tool/1.0'
          }
        })
        const endTime = performance.now()
        
        const responseTime = endTime - startTime
        const contentLength = response.headers.get('content-length')
        
        this.addMetric({
          name: `api_response_time_${endpoint.replace(/[\/\?=]/g, '_')}`,
          value: responseTime,
          unit: 'ms',
          source: 'server',
          category: 'response_time',
          metadata: {
            endpoint,
            status: response.status,
            contentLength: contentLength ? parseInt(contentLength) : null
          }
        })
        
        if (contentLength) {
          this.addMetric({
            name: `api_payload_size_${endpoint.replace(/[\/\?=]/g, '_')}`,
            value: parseInt(contentLength),
            unit: 'bytes',
            source: 'server',
            category: 'throughput',
            metadata: { endpoint }
          })
        }
      } catch (error) {
        console.warn(`Failed to measure API ${endpoint}:`, error)
      }
    }
  }

  private async measureResourcePerformance(): Promise<void> {
    console.log('📦 Measuring resource performance...')
    
    const resourceUrls = [
      '/favicon.ico',
      '/_next/static/css/app.css',
      '/_next/static/js/app.js',
      '/images/logo.png'
    ]
    
    for (const resourceUrl of resourceUrls) {
      const url = `${this.baseUrl}${resourceUrl}`
      
      try {
        const startTime = performance.now()
        const response = await fetch(url, {
          method: 'HEAD' // Use HEAD to avoid downloading large files
        })
        const endTime = performance.now()
        
        const responseTime = endTime - startTime
        const contentLength = response.headers.get('content-length')
        const cacheControl = response.headers.get('cache-control')
        
        this.addMetric({
          name: `resource_response_time_${resourceUrl.replace(/[\/\.]/g, '_')}`,
          value: responseTime,
          unit: 'ms',
          source: 'network',
          category: 'response_time',
          metadata: {
            resourceUrl,
            status: response.status,
            contentLength: contentLength ? parseInt(contentLength) : null,
            cacheControl,
            contentType: response.headers.get('content-type')
          }
        })
      } catch (error) {
        console.warn(`Failed to measure resource ${resourceUrl}:`, error)
      }
    }
  }

  private async measureSystemResources(): Promise<void> {
    console.log('💻 Measuring system resources...')
    
    // Memory usage
    const memoryUsage = process.memoryUsage()
    this.addMetric({
      name: 'memory_heap_used',
      value: memoryUsage.heapUsed,
      unit: 'bytes',
      source: 'server',
      category: 'resource_usage',
      metadata: {
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss
      }
    })
    
    // CPU usage (approximate)
    const startCpuUsage = process.cpuUsage()
    await this.delay(1000) // Wait 1 second
    const endCpuUsage = process.cpuUsage(startCpuUsage)
    
    const cpuPercent = (endCpuUsage.user + endCpuUsage.system) / 10000 // Convert to percentage
    this.addMetric({
      name: 'cpu_usage',
      value: cpuPercent,
      unit: 'percentage',
      source: 'server',
      category: 'resource_usage',
      metadata: {
        user: endCpuUsage.user,
        system: endCpuUsage.system
      }
    })
  }

  private addMetric(metric: Omit<BaselineMetric, 'timestamp'>): void {
    this.metrics.push({
      ...metric,
      timestamp: new Date().toISOString()
    })
  }

  private generateReport(): BaselineReport {
    const responseTimeMetrics = this.metrics.filter(m => m.category === 'response_time')
    const avgResponseTime = responseTimeMetrics.length > 0
      ? responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length
      : 0
    
    const totalRequests = this.metrics.filter(m => m.name.includes('response_time')).length
    const errorMetrics = this.metrics.filter(m => m.name.includes('error'))
    const errorRate = totalRequests > 0 ? (errorMetrics.length / totalRequests) * 100 : 0
    
    const memoryMetric = this.metrics.find(m => m.name === 'memory_heap_used')
    const cpuMetric = this.metrics.find(m => m.name === 'cpu_usage')
    
    const recommendations: string[] = []
    
    if (avgResponseTime > 1000) {
      recommendations.push('Average response time is high (>1s). Consider implementing CDN caching.')
    }
    
    if (errorRate > 5) {
      recommendations.push('Error rate is high (>5%). Investigate failing endpoints.')
    }
    
    if (memoryMetric && memoryMetric.value > 100 * 1024 * 1024) { // 100MB
      recommendations.push('Memory usage is high (>100MB). Consider optimizing memory allocation.')
    }
    
    if (cpuMetric && cpuMetric.value > 80) {
      recommendations.push('CPU usage is high (>80%). Consider optimizing computational tasks.')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Performance metrics are within acceptable ranges.')
    }
    
    return {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      metrics: this.metrics,
      summary: {
        avgResponseTime,
        totalRequests,
        errorRate,
        memoryUsage: memoryMetric?.value || 0,
        cpuUsage: cpuMetric?.value || 0
      },
      recommendations
    }
  }

  private async saveReport(report: BaselineReport): Promise<void> {
    const reportsDir = path.join(process.cwd(), 'performance-reports')
    
    try {
      await fs.mkdir(reportsDir, { recursive: true })
    } catch (error) {
      // Directory might already exist
    }
    
    const filename = `baseline-${new Date().toISOString().split('T')[0]}.json`
    const filepath = path.join(reportsDir, filename)
    
    await fs.writeFile(filepath, JSON.stringify(report, null, 2))
    console.log(`📊 Baseline report saved to: ${filepath}`)
    
    // Also save a summary
    const summaryFilename = `baseline-summary-${new Date().toISOString().split('T')[0]}.md`
    const summaryFilepath = path.join(reportsDir, summaryFilename)
    
    const summaryContent = this.generateMarkdownSummary(report)
    await fs.writeFile(summaryFilepath, summaryContent)
    console.log(`📋 Summary report saved to: ${summaryFilepath}`)
  }

  private generateMarkdownSummary(report: BaselineReport): string {
    return `# Performance Baseline Report

**Generated:** ${report.timestamp}
**Environment:** ${report.environment}
**Version:** ${report.version}

## Summary

- **Average Response Time:** ${report.summary.avgResponseTime.toFixed(2)}ms
- **Total Requests:** ${report.summary.totalRequests}
- **Error Rate:** ${report.summary.errorRate.toFixed(2)}%
- **Memory Usage:** ${(report.summary.memoryUsage / 1024 / 1024).toFixed(2)}MB
- **CPU Usage:** ${report.summary.cpuUsage.toFixed(2)}%

## Key Metrics

${report.metrics
  .filter(m => m.name.includes('avg_response_time'))
  .map(m => `- **${m.metadata?.endpoint || m.name}:** ${m.value.toFixed(2)}${m.unit}`)
  .join('\n')}

## Recommendations

${report.recommendations.map(r => `- ${r}`).join('\n')}

## Next Steps

1. Implement Cloudflare CDN caching
2. Configure performance monitoring
3. Set up automated alerts
4. Compare post-deployment metrics with this baseline
`
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Run baseline measurement if called directly
if (require.main === module) {
  const baseline = new PerformanceBaseline()
  baseline.measureBaseline()
    .then(() => {
      console.log('🎉 Baseline measurement completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Baseline measurement failed:', error)
      process.exit(1)
    })
}

export { PerformanceBaseline, BaselineMetric, BaselineReport }
