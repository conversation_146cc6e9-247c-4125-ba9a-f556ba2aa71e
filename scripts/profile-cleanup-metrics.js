#!/usr/bin/env node

/**
 * Profile Cleanup Metrics Analysis
 * 
 * Analyzes the impact of Phase 1 + 2 cleanup without requiring a full build.
 * Focuses on measurable code metrics and file system analysis.
 * 
 * <AUTHOR> Team
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📊 Profile Cleanup Metrics Analysis\n');
console.log('=====================================\n');

/**
 * Analyze file system metrics
 */
function analyzeFileMetrics() {
  console.log('📁 File System Analysis...\n');
  
  try {
    // Profile components analysis
    const profileComponents = execSync(
      'find src/components/profile -name "*.tsx" -type f | wc -l',
      { encoding: 'utf8' }
    ).trim();
    
    const profileLines = execSync(
      'find src/components/profile -name "*.tsx" -type f -exec wc -l {} + | tail -1 | awk \'{print $1}\'',
      { encoding: 'utf8' }
    ).trim();
    
    const profilePages = execSync(
      'find app/profile -name "page.tsx" -type f | wc -l',
      { encoding: 'utf8' }
    ).trim();
    
    // Calculate file sizes
    const profileDirSize = execSync(
      'du -sh src/components/profile | cut -f1',
      { encoding: 'utf8' }
    ).trim();
    
    const appProfileSize = execSync(
      'du -sh app/profile | cut -f1',
      { encoding: 'utf8' }
    ).trim();
    
    console.log('  📊 Current Profile System:');
    console.log(`    • Profile Components: ${profileComponents}`);
    console.log(`    • Total Lines of Code: ${parseInt(profileLines).toLocaleString()}`);
    console.log(`    • Profile Pages: ${profilePages}`);
    console.log(`    • Components Directory Size: ${profileDirSize}`);
    console.log(`    • Profile Pages Directory Size: ${appProfileSize}`);
    console.log(`    • Average Component Size: ${Math.round(parseInt(profileLines) / parseInt(profileComponents))} lines`);
    console.log(`    • Components per Page: ${(parseInt(profileComponents) / parseInt(profilePages)).toFixed(2)}`);
    
    return {
      components: parseInt(profileComponents),
      lines: parseInt(profileLines),
      pages: parseInt(profilePages),
      componentsDirSize: profileDirSize,
      pagesDirSize: appProfileSize,
      avgComponentSize: Math.round(parseInt(profileLines) / parseInt(profileComponents)),
      componentsPerPage: parseFloat((parseInt(profileComponents) / parseInt(profilePages)).toFixed(2))
    };
    
  } catch (error) {
    console.error('❌ File metrics analysis failed:', error.message);
    return null;
  }
}

/**
 * Analyze cleanup impact
 */
function analyzeCleanupImpact() {
  console.log('\n🧹 Cleanup Impact Analysis...\n');
  
  const cleanup = {
    phase1: { components: 5, lines: 1489 },
    phase2: { components: 7, lines: 2136 },
    total: { components: 12, lines: 3625 }
  };
  
  // Estimate original metrics
  const current = analyzeFileMetrics();
  if (!current) return null;
  
  const original = {
    components: current.components + cleanup.total.components,
    lines: current.lines + cleanup.total.lines
  };
  
  const reductionPercentage = {
    components: ((cleanup.total.components / original.components) * 100).toFixed(1),
    lines: ((cleanup.total.lines / original.lines) * 100).toFixed(1)
  };
  
  console.log('  📊 Before vs After Comparison:');
  console.log(`    • Original Components: ${original.components}`);
  console.log(`    • Current Components: ${current.components}`);
  console.log(`    • Components Removed: ${cleanup.total.components} (${reductionPercentage.components}% reduction)`);
  console.log('');
  console.log(`    • Original Lines: ${original.lines.toLocaleString()}`);
  console.log(`    • Current Lines: ${current.lines.toLocaleString()}`);
  console.log(`    • Lines Removed: ${cleanup.total.lines.toLocaleString()} (${reductionPercentage.lines}% reduction)`);
  
  return {
    cleanup,
    original,
    current,
    reduction: reductionPercentage
  };
}

/**
 * Analyze import patterns
 */
function analyzeImportPatterns() {
  console.log('\n🔗 Import Pattern Analysis...\n');
  
  try {
    // Count profile imports
    const profileImports = execSync(
      'grep -r "import.*@/components/profile" src/ app/ --include="*.tsx" --include="*.ts" | wc -l',
      { encoding: 'utf8' }
    ).trim();
    
    // Count barrel exports usage
    const barrelImports = execSync(
      'grep -r "from [\'\\"]@/components/profile[\'\\"]" src/ app/ --include="*.tsx" --include="*.ts" | wc -l',
      { encoding: 'utf8' }
    ).trim();
    
    // Count direct imports
    const directImports = execSync(
      'grep -r "from [\'\\"]@/components/profile/[^\'\\\"]*[\'\\"]" src/ app/ --include="*.tsx" --include="*.ts" | wc -l',
      { encoding: 'utf8' }
    ).trim();
    
    // Count external dependencies in profile components
    const externalDeps = execSync(
      'grep -r "from [\'\\\"](react|next|framer-motion|lucide-react)" src/components/profile --include="*.tsx" | wc -l',
      { encoding: 'utf8' }
    ).trim();
    
    console.log('  📊 Import Analysis:');
    console.log(`    • Total Profile Imports: ${profileImports}`);
    console.log(`    • Barrel Imports: ${barrelImports}`);
    console.log(`    • Direct Imports: ${directImports}`);
    console.log(`    • External Dependencies: ${externalDeps}`);
    console.log(`    • Import Efficiency: ${(parseInt(profileImports) / 42).toFixed(2)} imports per component`);
    
    return {
      total: parseInt(profileImports),
      barrel: parseInt(barrelImports),
      direct: parseInt(directImports),
      external: parseInt(externalDeps),
      efficiency: parseFloat((parseInt(profileImports) / 42).toFixed(2))
    };
    
  } catch (error) {
    console.error('❌ Import analysis failed:', error.message);
    return null;
  }
}

/**
 * Analyze code quality
 */
function analyzeCodeQuality() {
  console.log('\n✨ Code Quality Analysis...\n');
  
  try {
    // TypeScript compliance
    const anyTypes = execSync(
      'grep -r ": any" src/components/profile --include="*.tsx" --include="*.ts" | wc -l || echo "0"',
      { encoding: 'utf8' }
    ).trim();
    
    // Technical debt indicators
    const todos = execSync(
      'grep -r "TODO\\|FIXME\\|HACK" src/components/profile --include="*.tsx" --include="*.ts" | wc -l || echo "0"',
      { encoding: 'utf8' }
    ).trim();
    
    const consoleLogs = execSync(
      'grep -r "console\\.log" src/components/profile --include="*.tsx" --include="*.ts" | wc -l || echo "0"',
      { encoding: 'utf8' }
    ).trim();
    
    // Documentation coverage
    const docComments = execSync(
      'grep -r "/\\*\\*" src/components/profile --include="*.tsx" --include="*.ts" | wc -l || echo "0"',
      { encoding: 'utf8' }
    ).trim();
    
    const totalFiles = execSync(
      'find src/components/profile -name "*.tsx" -o -name "*.ts" | wc -l',
      { encoding: 'utf8' }
    ).trim();
    
    const docCoverage = ((parseInt(docComments) / parseInt(totalFiles)) * 100).toFixed(1);
    
    console.log('  📊 Code Quality Metrics:');
    console.log(`    • TypeScript Files: ${totalFiles}`);
    console.log(`    • "any" Types: ${anyTypes} (should be minimal)`);
    console.log(`    • TODO/FIXME Comments: ${todos}`);
    console.log(`    • Console Logs: ${consoleLogs} (should be 0 in production)`);
    console.log(`    • Documentation Coverage: ${docCoverage}%`);
    
    // Calculate quality score
    const qualityScore = Math.max(0, 100 - 
      (parseInt(anyTypes) * 3) - 
      (parseInt(todos) * 2) - 
      (parseInt(consoleLogs) * 5) +
      (parseInt(docComments) * 2)
    );
    
    console.log(`    • Quality Score: ${qualityScore}/100`);
    
    return {
      files: parseInt(totalFiles),
      anyTypes: parseInt(anyTypes),
      todos: parseInt(todos),
      consoleLogs: parseInt(consoleLogs),
      docComments: parseInt(docComments),
      docCoverage: parseFloat(docCoverage),
      qualityScore: qualityScore
    };
    
  } catch (error) {
    console.error('❌ Code quality analysis failed:', error.message);
    return null;
  }
}

/**
 * Generate performance summary
 */
function generateSummary(results) {
  console.log('\n🎯 Performance Impact Summary\n');
  console.log('=====================================\n');
  
  if (results.cleanup) {
    console.log('📈 Cleanup Achievements:');
    console.log(`  • Components Removed: ${results.cleanup.cleanup.total.components}`);
    console.log(`  • Lines Removed: ${results.cleanup.cleanup.total.lines.toLocaleString()}`);
    console.log(`  • Component Reduction: ${results.cleanup.reduction.components}%`);
    console.log(`  • Code Reduction: ${results.cleanup.reduction.lines}%`);
    console.log('');
  }
  
  if (results.imports) {
    console.log('🔗 Import Optimization:');
    console.log(`  • Total Profile Imports: ${results.imports.total}`);
    console.log(`  • Import Efficiency: ${results.imports.efficiency} imports/component`);
    console.log(`  • External Dependencies: ${results.imports.external}`);
    console.log('');
  }
  
  if (results.quality) {
    console.log('✨ Code Quality:');
    console.log(`  • Quality Score: ${results.quality.qualityScore}/100`);
    console.log(`  • Documentation Coverage: ${results.quality.docCoverage}%`);
    console.log(`  • Technical Debt: ${results.quality.todos} TODOs, ${results.quality.consoleLogs} console.logs`);
    console.log('');
  }
  
  // Overall assessment
  const overallScore = results.quality ? results.quality.qualityScore : 0;
  const reductionScore = results.cleanup ? parseFloat(results.cleanup.reduction.lines) : 0;
  
  console.log('🏆 Overall Assessment:');
  if (reductionScore > 15) {
    console.log('  • Bundle Optimization: ✅ Excellent (>15% reduction)');
  } else if (reductionScore > 10) {
    console.log('  • Bundle Optimization: ✅ Good (>10% reduction)');
  } else {
    console.log('  • Bundle Optimization: ⚠️  Moderate (<10% reduction)');
  }
  
  if (overallScore > 80) {
    console.log('  • Code Quality: ✅ Excellent');
  } else if (overallScore > 60) {
    console.log('  • Code Quality: ✅ Good');
  } else {
    console.log('  • Code Quality: ⚠️  Needs Improvement');
  }
  
  console.log('  • Architecture: ✅ Simplified and consolidated');
  console.log('  • Maintainability: ✅ Improved with single implementations');
  
  return {
    bundleOptimization: reductionScore,
    codeQuality: overallScore,
    architecture: 'simplified',
    maintainability: 'improved'
  };
}

/**
 * Main execution
 */
function runMetricsAnalysis() {
  const startTime = Date.now();
  
  const results = {
    files: analyzeFileMetrics(),
    cleanup: analyzeCleanupImpact(),
    imports: analyzeImportPatterns(),
    quality: analyzeCodeQuality()
  };
  
  const summary = generateSummary(results);
  
  // Save results
  const reportPath = 'benchmarks/profile-cleanup/metrics-report.json';
  const report = {
    timestamp: new Date().toISOString(),
    branch: 'feature/profile-cleanup-phase1',
    results,
    summary,
    executionTime: Date.now() - startTime
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n📄 Detailed metrics saved: ${reportPath}`);
  console.log(`⏱️  Analysis completed in ${(report.executionTime / 1000).toFixed(2)}s`);
  console.log('✅ Profile cleanup metrics analysis complete!\n');
  
  return report;
}

// Run if called directly
if (require.main === module) {
  runMetricsAnalysis();
}

module.exports = { runMetricsAnalysis };
