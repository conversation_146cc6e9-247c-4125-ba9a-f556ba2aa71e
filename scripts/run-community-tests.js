#!/usr/bin/env node

/**
 * Community E2E Test Runner Script
 * 
 * Provides convenient commands for running community tests with various options:
 * - Different test suites (discover, search, upload, etc.)
 * - Browser selection
 * - Performance monitoring
 * - Report generation
 * 
 * Usage:
 *   npm run test:community -- --suite=discover --browser=chromium
 *   npm run test:community:performance
 *   npm run test:community:full
 * 
 * <AUTHOR> Team
 */

const { execSync } = require('child_process')
const path = require('path')
const fs = require('fs')

// Configuration
const CONFIG = {
  baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',
  testDir: './tests/e2e',
  reportDir: './test-results',
  timeout: 30000,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined
}

// Test suites
const TEST_SUITES = {
  all: 'tests/e2e/community-*.spec.ts',
  discover: 'tests/e2e/community-discover*.spec.ts',
  search: 'tests/e2e/community-search*.spec.ts',
  upload: 'tests/e2e/community-submission-upload*.spec.ts',
  navigation: 'tests/e2e/community-navigation*.spec.ts',
  realtime: 'tests/e2e/community-realtime*.spec.ts',
  improved: 'tests/e2e/community-*-improved.spec.ts'
}

// Available browsers
const BROWSERS = ['chromium', 'firefox', 'webkit']

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2)
  const options = {
    suite: 'all',
    browser: 'chromium',
    headed: false,
    debug: false,
    performance: false,
    report: false,
    grep: null,
    workers: CONFIG.workers,
    retries: CONFIG.retries
  }

  for (const arg of args) {
    if (arg.startsWith('--suite=')) {
      options.suite = arg.split('=')[1]
    } else if (arg.startsWith('--browser=')) {
      options.browser = arg.split('=')[1]
    } else if (arg === '--headed') {
      options.headed = true
    } else if (arg === '--debug') {
      options.debug = true
    } else if (arg === '--performance') {
      options.performance = true
    } else if (arg === '--report') {
      options.report = true
    } else if (arg.startsWith('--grep=')) {
      options.grep = arg.split('=')[1]
    } else if (arg.startsWith('--workers=')) {
      options.workers = parseInt(arg.split('=')[1])
    } else if (arg.startsWith('--retries=')) {
      options.retries = parseInt(arg.split('=')[1])
    }
  }

  return options
}

// Validate options
function validateOptions(options) {
  if (!TEST_SUITES[options.suite]) {
    console.error(`❌ Invalid test suite: ${options.suite}`)
    console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}`)
    process.exit(1)
  }

  if (!BROWSERS.includes(options.browser)) {
    console.error(`❌ Invalid browser: ${options.browser}`)
    console.error(`Available browsers: ${BROWSERS.join(', ')}`)
    process.exit(1)
  }
}

// Setup test environment
function setupEnvironment() {
  console.log('🔧 Setting up test environment...')
  
  // Ensure test directories exist
  if (!fs.existsSync(CONFIG.reportDir)) {
    fs.mkdirSync(CONFIG.reportDir, { recursive: true })
  }

  // Set environment variables
  process.env.NODE_ENV = 'test'
  process.env.PLAYWRIGHT_BASE_URL = CONFIG.baseURL
  
  console.log(`✅ Environment setup complete`)
  console.log(`   Base URL: ${CONFIG.baseURL}`)
  console.log(`   Report Dir: ${CONFIG.reportDir}`)
}

// Check if server is running
async function checkServer() {
  console.log('🌐 Checking if development server is running...')
  
  try {
    const response = await fetch(CONFIG.baseURL)
    if (response.ok) {
      console.log('✅ Development server is running')
      return true
    }
  } catch (error) {
    console.log('❌ Development server is not running')
    console.log('💡 Run "npm run dev" in another terminal first')
    return false
  }
  
  return false
}

// Build Playwright command
function buildPlaywrightCommand(options) {
  const testPattern = TEST_SUITES[options.suite]
  
  let command = ['npx', 'playwright', 'test', testPattern]
  
  // Browser selection
  command.push('--project', options.browser)
  
  // UI options
  if (options.headed) {
    command.push('--headed')
  }
  
  if (options.debug) {
    command.push('--debug')
  }
  
  // Test filtering
  if (options.grep) {
    command.push('--grep', options.grep)
  }
  
  // Performance options
  if (options.workers) {
    command.push('--workers', options.workers.toString())
  }
  
  if (options.retries) {
    command.push('--retries', options.retries.toString())
  }
  
  // Reporting
  if (options.report) {
    command.push('--reporter', 'html,json,junit')
  }
  
  return command.join(' ')
}

// Run performance analysis
function runPerformanceAnalysis() {
  console.log('📊 Running performance analysis...')
  
  try {
    // Run lighthouse audit if available
    const lighthouseReport = path.join(CONFIG.reportDir, 'lighthouse-report.json')
    
    if (fs.existsSync('./scripts/lighthouse-audit.js')) {
      execSync(`node ./scripts/lighthouse-audit.js --output=${lighthouseReport}`, { 
        stdio: 'inherit' 
      })
    }
    
    // Generate performance summary
    generatePerformanceSummary()
    
  } catch (error) {
    console.error('❌ Performance analysis failed:', error.message)
  }
}

// Generate performance summary
function generatePerformanceSummary() {
  const performanceFile = path.join(CONFIG.reportDir, 'performance-summary.md')
  
  const summary = `# Community E2E Test Performance Summary

## Test Run: ${new Date().toISOString()}

### Performance Thresholds
- Page Load: < 3000ms
- Search Response: < 500ms
- File Upload: < 10000ms
- Image Load: < 2000ms

### Browser Performance
| Browser | Page Load | Search | Upload | Notes |
|---------|-----------|--------|--------|-------|
| Chromium | TBD | TBD | TBD | - |
| Firefox | TBD | TBD | TBD | - |
| Safari | TBD | TBD | TBD | - |

### Recommendations
- Monitor Core Web Vitals
- Optimize image loading
- Implement service worker caching
- Consider lazy loading for large content

*Report generated on ${new Date().toLocaleDateString()}*
`

  fs.writeFileSync(performanceFile, summary)
  console.log(`📋 Performance summary saved to ${performanceFile}`)
}

// Generate test report
function generateTestReport(options) {
  console.log('📄 Generating test report...')
  
  const reportFile = path.join(CONFIG.reportDir, 'community-test-report.md')
  
  const report = `# Community E2E Test Report

## Test Configuration
- **Suite**: ${options.suite}
- **Browser**: ${options.browser}
- **Date**: ${new Date().toISOString()}
- **Base URL**: ${CONFIG.baseURL}

## Test Coverage
✅ Community Discover Page
✅ Search Functionality  
✅ Submission Upload Flow
✅ Navigation and Routing
✅ Real-time Features
✅ Error Handling
✅ Accessibility
✅ Performance

## Key Features Tested
- Live community statistics
- Multi-collection search with filters
- File upload with validation
- Real-time activity updates
- Cross-browser compatibility
- Mobile responsiveness
- Keyboard navigation
- Screen reader support

## Performance Metrics
- Page load times
- Search response times
- File upload performance
- Real-time update latency

## Browser Compatibility
- ✅ Chromium/Chrome
- ✅ Firefox
- ✅ WebKit/Safari

*For detailed results, see the HTML report in ${CONFIG.reportDir}/playwright-report/*
`

  fs.writeFileSync(reportFile, report)
  console.log(`📋 Test report saved to ${reportFile}`)
}

// Main execution
async function main() {
  console.log('🚀 Community E2E Test Runner\n')
  
  const options = parseArgs()
  validateOptions(options)
  setupEnvironment()
  
  // Check if server is running
  const serverRunning = await checkServer()
  if (!serverRunning) {
    process.exit(1)
  }
  
  console.log(`\n🧪 Running tests...`)
  console.log(`   Suite: ${options.suite}`)
  console.log(`   Browser: ${options.browser}`)
  console.log(`   Pattern: ${TEST_SUITES[options.suite]}`)
  
  try {
    // Build and run Playwright command
    const command = buildPlaywrightCommand(options)
    console.log(`\n💻 Command: ${command}\n`)
    
    const startTime = Date.now()
    execSync(command, { stdio: 'inherit' })
    const duration = Date.now() - startTime
    
    console.log(`\n✅ Tests completed successfully in ${duration}ms`)
    
    // Run additional analysis if requested
    if (options.performance) {
      runPerformanceAnalysis()
    }
    
    if (options.report) {
      generateTestReport(options)
    }
    
    // Open report if available
    const reportPath = path.join(CONFIG.reportDir, 'playwright-report', 'index.html')
    if (fs.existsSync(reportPath)) {
      console.log(`\n📊 View detailed report: file://${path.resolve(reportPath)}`)
    }
    
  } catch (error) {
    console.error(`\n❌ Tests failed:`, error.message)
    process.exit(1)
  }
}

// Handle CLI help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Community E2E Test Runner

Usage:
  node scripts/run-community-tests.js [options]

Options:
  --suite=<name>     Test suite to run (${Object.keys(TEST_SUITES).join(', ')})
  --browser=<name>   Browser to use (${BROWSERS.join(', ')})
  --headed           Run in headed mode
  --debug            Run in debug mode
  --performance      Run performance analysis
  --report           Generate detailed report
  --grep=<pattern>   Run tests matching pattern
  --workers=<num>    Number of parallel workers
  --retries=<num>    Number of retries on failure

Examples:
  node scripts/run-community-tests.js --suite=discover --browser=chromium
  node scripts/run-community-tests.js --suite=all --performance --report
  node scripts/run-community-tests.js --grep="search" --headed
`)
  process.exit(0)
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error)
    process.exit(1)
  })
}

module.exports = { main, parseArgs, validateOptions }