#!/bin/bash

# AI Coordination Setup Script
# Sets up Git aliases and templates for AI coordination workflow

echo "🤖 Setting up AI Coordination System..."

# Create Git aliases for AI-specific commits
echo "📝 Setting up Git aliases..."

# Augment Agent aliases
git config alias.augment-commit '!f() { git commit -m "[AUGMENT] $1"; }; f'
git config alias.augment-feat '!f() { git commit -m "[AUGMENT] feat: $1"; }; f'
git config alias.augment-fix '!f() { git commit -m "[AUGMENT] fix: $1"; }; f'
git config alias.augment-docs '!f() { git commit -m "[AUGMENT] docs: $1"; }; f'
git config alias.augment-refactor '!f() { git commit -m "[AUGMENT] refactor: $1"; }; f'
git config alias.augment-test '!f() { git commit -m "[AUGMENT] test: $1"; }; f'

# Claude Code aliases
git config alias.cursor-commit '!f() { git commit -m "[CURSOR] $1"; }; f'
git config alias.cursor-feat '!f() { git commit -m "[CURSOR] feat: $1"; }; f'
git config alias.cursor-fix '!f() { git commit -m "[CURSOR] fix: $1"; }; f'
git config alias.cursor-ui '!f() { git commit -m "[CURSOR] ui: $1"; }; f'
git config alias.cursor-style '!f() { git commit -m "[CURSOR] style: $1"; }; f'
git config alias.cursor-test '!f() { git commit -m "[CURSOR] test: $1"; }; f'

# Collaborative aliases
git config alias.collab-commit '!f() { git commit -m "[COLLAB] $1"; }; f'
git config alias.collab-feat '!f() { git commit -m "[COLLAB] feat: $1"; }; f'
git config alias.collab-merge '!f() { git commit -m "[COLLAB] merge: $1"; }; f'

# Handoff aliases
git config alias.handoff-commit '!f() { git commit -m "[HANDOFF] $1 - Ready for handoff"; }; f'

# Branch creation aliases
git config alias.augment-branch '!f() { git checkout -b "augment/$1"; }; f'
git config alias.cursor-branch '!f() { git checkout -b "cursor/$1"; }; f'
git config alias.collab-branch '!f() { git checkout -b "collab/$1"; }; f'

echo "✅ Git aliases configured!"

# Create commit message template
echo "📋 Creating commit message template..."

cat > .gitmessage << 'EOF'
# AI Coordination Commit Template
# 
# Format: [AI] type: description
# 
# AI Options:
# [AUGMENT] - Augment Agent work
# [CURSOR]  - Claude Code work  
# [COLLAB]  - Collaborative work
# [HANDOFF] - Work ready for handoff
#
# Type Options:
# feat:     New feature
# fix:      Bug fix
# docs:     Documentation changes
# style:    Code style changes (formatting, etc.)
# refactor: Code refactoring
# test:     Adding or updating tests
# ui:       UI/UX improvements
# merge:    Merging branches
#
# Examples:
# [AUGMENT] feat: implement user level system architecture
# [CURSOR] fix: resolve button hover animation glitch
# [COLLAB] merge: integrate auth system with UI components
# [HANDOFF] feat: auth backend ready for UI integration
#
# Remember to:
# - Keep subject line under 50 characters
# - Use imperative mood ("add" not "added")
# - Include context for the other AI if relevant
# - Reference issue numbers if applicable
EOF

git config commit.template .gitmessage

echo "✅ Commit template created!"

# Create coordination helper functions
echo "🔧 Creating coordination helper functions..."

cat > scripts/ai-coordination-helpers.sh << 'EOF'
#!/bin/bash

# AI Coordination Helper Functions

# Check current work claims
check-claims() {
    echo "🔍 Current Work Claims:"
    echo "====================="
    if [ -f ".ai-coordination.md" ]; then
        grep -A 10 "## Current Work Claims" .ai-coordination.md
    else
        echo "❌ .ai-coordination.md not found!"
    fi
}

# Claim work area
claim-work() {
    if [ -z "$1" ]; then
        echo "Usage: claim-work 'description of work'"
        return 1
    fi
    
    echo "📝 Claiming work: $1"
    echo "⏰ Estimated completion: $(date -d '+2 hours' '+%Y-%m-%d %H:%M')"
    echo ""
    echo "Please manually update .ai-coordination.md with your claim!"
}

# Quick status check
ai-status() {
    echo "🤖 AI Coordination Status"
    echo "========================="
    echo ""
    
    echo "📋 Current Branch: $(git branch --show-current)"
    echo "📊 Uncommitted Changes: $(git status --porcelain | wc -l) files"
    echo ""
    
    if [ -f ".ai-coordination.md" ]; then
        echo "🔍 Active Claims:"
        grep -A 5 "Currently working on:" .ai-coordination.md | grep -v "Currently working on:" | head -5
    fi
    
    echo ""
    echo "📝 Recent AI Commits:"
    git log --oneline -5 --grep="\[AUGMENT\]\|\[CURSOR\]\|\[COLLAB\]"
}

# Update work log
update-log() {
    if [ -z "$1" ]; then
        echo "Usage: update-log 'progress update'"
        return 1
    fi
    
    echo "📝 Adding to work log: $1"
    echo "$(date '+%Y-%m-%d %H:%M') - $1" >> AI_WORK_LOG.md
    echo "✅ Work log updated!"
}

# Show handoff template
handoff-template() {
    echo "📋 Handoff Template:"
    echo "==================="
    echo ""
    echo "## Handoff: [Feature Name]"
    echo "**From**: [Your AI]"
    echo "**To**: [Target AI]"
    echo "**Date**: $(date '+%Y-%m-%d %H:%M')"
    echo ""
    echo "### Completed Work"
    echo "- [List what you finished]"
    echo ""
    echo "### Next Steps"
    echo "- [What needs to be done next]"
    echo ""
    echo "### Files Modified"
    echo "- [Key files changed]"
    echo ""
    echo "### Important Notes"
    echo "- [Any gotchas or important context]"
}

# Export functions
export -f check-claims
export -f claim-work
export -f ai-status
export -f update-log
export -f handoff-template
EOF

chmod +x scripts/ai-coordination-helpers.sh

echo "✅ Helper functions created!"

# Create VS Code settings for AI coordination
echo "⚙️ Creating VS Code settings..."

mkdir -p .vscode

cat > .vscode/ai-coordination.code-snippets << 'EOF'
{
  "AI Handoff Note": {
    "prefix": "handoff",
    "body": [
      "## Handoff: ${1:Feature Name}",
      "",
      "**From**: ${2:AI Name}",
      "**To**: ${3:Target AI}",
      "**Date**: ${CURRENT_YEAR}-${CURRENT_MONTH}-${CURRENT_DATE} ${CURRENT_HOUR}:${CURRENT_MINUTE}",
      "",
      "### Completed Work",
      "- ${4:What was completed}",
      "",
      "### Next Steps",
      "- ${5:What needs to be done next}",
      "",
      "### Files Modified",
      "- `${6:file/path}` - ${7:description}",
      "",
      "### Important Notes",
      "- ${8:Any important context or gotchas}",
      ""
    ],
    "description": "Create AI handoff documentation"
  },
  
  "AI Work Claim": {
    "prefix": "claim",
    "body": [
      "### ${1:AI Name}",
      "*Currently working on:*",
      "- `${2:file/area}` (until ${CURRENT_YEAR}-${CURRENT_MONTH}-${CURRENT_DATE} ${3:time})",
      "- ${4:Description of work}",
      ""
    ],
    "description": "Claim work area in coordination file"
  },
  
  "Daily Log Entry": {
    "prefix": "dailylog",
    "body": [
      "## ${CURRENT_YEAR}-${CURRENT_MONTH}-${CURRENT_DATE}",
      "",
      "### Augment Agent",
      "- ✅ **Completed**: ${1:completed tasks}",
      "- 🔄 **In Progress**: ${2:current work}",
      "- 📋 **Next**: ${3:planned tasks}",
      "",
      "### Claude Code (Cursor AI)",
      "- ✅ **Completed**: ${4:completed tasks}",
      "- 🔄 **In Progress**: ${5:current work}",
      "- 📋 **Next**: ${6:planned tasks}",
      "",
      "### Coordination Notes",
      "- ${7:coordination information}",
      "",
      "### System Status",
      "- **Repository**: ${8:status}",
      "- **Tests**: ${9:test status}",
      ""
    ],
    "description": "Create daily log entry"
  }
}
EOF

echo "✅ VS Code snippets created!"

# Create README for the coordination system
echo "📚 Creating coordination README..."

cat > docs/AI_COORDINATION_README.md << 'EOF'
# AI Coordination System

This directory contains the coordination system for managing development work between Augment Agent and Claude Code (Cursor AI).

## Quick Start

1. **Check current work**: Review `.ai-coordination.md` before starting
2. **Claim your work**: Update the file with your work area and timeline
3. **Use proper commits**: Use `[AUGMENT]` or `[CURSOR]` prefixes
4. **Update progress**: Add entries to `AI_WORK_LOG.md`
5. **Document handoffs**: Use `HANDOFF_NOTES.md` for work transitions

## Files Overview

- `.ai-coordination.md` - Main coordination file for claiming work areas
- `AI_WORK_LOG.md` - Daily progress tracking and communication
- `HANDOFF_NOTES.md` - Templates and documentation for work handoffs
- `scripts/setup-ai-coordination.sh` - Setup script for Git aliases and templates
- `scripts/ai-coordination-helpers.sh` - Helper functions for coordination

## Git Aliases

After running the setup script, you can use these aliases:

### Augment Agent
- `git augment-feat "description"` - Feature commits
- `git augment-fix "description"` - Bug fix commits
- `git augment-docs "description"` - Documentation commits
- `git augment-branch "branch-name"` - Create Augment branch

### Claude Code
- `git cursor-feat "description"` - Feature commits
- `git cursor-fix "description"` - Bug fix commits
- `git cursor-ui "description"` - UI improvement commits
- `git cursor-branch "branch-name"` - Create Cursor branch

### Collaborative
- `git collab-feat "description"` - Collaborative feature commits
- `git collab-merge "description"` - Merge commits

## Helper Functions

Source the helper script to use these functions:
```bash
source scripts/ai-coordination-helpers.sh

check-claims      # View current work claims
ai-status         # Quick coordination status
update-log "msg"  # Add entry to work log
handoff-template  # Show handoff template
```

## Best Practices

1. **Always check claims** before starting work
2. **Commit frequently** with descriptive messages
3. **Update logs daily** to maintain communication
4. **Use handoff templates** for work transitions
5. **Test before handoffs** to ensure clean transitions
6. **Document decisions** for future reference

## Troubleshooting

- **Merge conflicts**: Follow priority rules in `.ai-coordination.md`
- **Missing context**: Check `HANDOFF_NOTES.md` for previous work
- **Coordination issues**: Update `AI_WORK_LOG.md` with blockers
- **Setup problems**: Re-run `scripts/setup-ai-coordination.sh`
EOF

echo "✅ Coordination README created!"

echo ""
echo "🎉 AI Coordination System Setup Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Review .ai-coordination.md for work area assignments"
echo "2. Use AI_WORK_LOG.md for daily progress tracking"
echo "3. Follow HANDOFF_NOTES.md templates for work transitions"
echo "4. Use Git aliases for consistent commit formatting"
echo "5. Source scripts/ai-coordination-helpers.sh for helper functions"
echo ""
echo "🔧 To use helper functions:"
echo "   source scripts/ai-coordination-helpers.sh"
echo "   check-claims"
echo "   ai-status"
echo ""
echo "✅ System ready for coordinated AI development!"
