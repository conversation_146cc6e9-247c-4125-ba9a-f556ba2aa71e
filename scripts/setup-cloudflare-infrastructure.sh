#!/bin/bash

# Syndicaps Cloudflare Infrastructure Setup Script
# This script sets up the basic Cloudflare infrastructure for hybrid deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
  echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
  exit 1
}

warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
  log "Checking prerequisites..."

  # Check if Node.js is available
  if ! command -v node &> /dev/null; then
    error "Node.js not found. Please install Node.js"
  fi

  # Check if npm is available
  if ! command -v npm &> /dev/null; then
    error "npm not found. Please install npm"
  fi

  # Check if wrangler is available (either global or local)
  if ! command -v wrangler &> /dev/null && ! npx wrangler --version &> /dev/null; then
    error "Wrangler CLI not found. Please install with: npm install wrangler"
  fi

  # Use npx wrangler if global wrangler is not available
  if ! command -v wrangler &> /dev/null; then
    WRANGLER_CMD="npx wrangler"
  else
    WRANGLER_CMD="wrangler"
  fi

  # Check if logged in to Cloudflare
  if ! $WRANGLER_CMD whoami &> /dev/null; then
    error "Not logged in to Cloudflare. Please run: $WRANGLER_CMD login"
  fi

  log "Prerequisites check passed (using: $WRANGLER_CMD)"
}

# Create R2 buckets
create_r2_buckets() {
  log "Creating R2 storage buckets..."

  # Development buckets
  if $WRANGLER_CMD r2 bucket create syndicaps-images-dev; then
    log "Created syndicaps-images-dev bucket"
  else
    warning "syndicaps-images-dev bucket may already exist"
  fi

  if $WRANGLER_CMD r2 bucket create syndicaps-backups-dev; then
    log "Created syndicaps-backups-dev bucket"
  else
    warning "syndicaps-backups-dev bucket may already exist"
  fi

  # Staging buckets
  if $WRANGLER_CMD r2 bucket create syndicaps-images-staging; then
    log "Created syndicaps-images-staging bucket"
  else
    warning "syndicaps-images-staging bucket may already exist"
  fi

  if $WRANGLER_CMD r2 bucket create syndicaps-backups-staging; then
    log "Created syndicaps-backups-staging bucket"
  else
    warning "syndicaps-backups-staging bucket may already exist"
  fi

  # Production buckets
  if $WRANGLER_CMD r2 bucket create syndicaps-images; then
    log "Created syndicaps-images bucket"
  else
    warning "syndicaps-images bucket may already exist"
  fi

  if $WRANGLER_CMD r2 bucket create syndicaps-backups; then
    log "Created syndicaps-backups bucket"
  else
    warning "syndicaps-backups bucket may already exist"
  fi

  # List all buckets to verify
  log "Listing all R2 buckets:"
  $WRANGLER_CMD r2 bucket list

  log "R2 buckets setup completed"
}

# Validate KV namespaces
validate_kv_namespaces() {
  log "Validating KV namespaces for all environments..."

  # List all KV namespaces
  log "Listing all KV namespaces:"
  $WRANGLER_CMD kv namespace list

  # Expected namespace IDs from .env.local
  EXPECTED_NAMESPACES=(
    "CACHE_KV_DEV:71cabead02ef4f448b6b9ebf392fee0c"
    "SESSION_KV_DEV:800ef077f93240b791cd22e4e7d222d2"
    "CACHE_KV_STAGING:17ddf6927df24c45a47cab108095526a"
    "SESSION_KV_STAGING:8ed4d59d0fbf4a31a5b9f4e78dd1a75f"
    "CACHE_KV_PROD:a0cc4dfdf2ac4655be3369661d79455b"
    "SESSION_KV_PROD:c25f365c2b06419ea1cf1f4cdafe02e9"
  )

  # Validate each namespace exists
  for namespace_info in "${EXPECTED_NAMESPACES[@]}"; do
    IFS=':' read -r name id <<< "$namespace_info"
    if $WRANGLER_CMD kv namespace list | grep -q "$id"; then
      log "✅ $name namespace exists (ID: $id)"
    else
      warning "❌ $name namespace not found (ID: $id)"
    fi
  done

  # Save all IDs to environment file
  log "Saving KV namespace IDs to .env.local..."
  cat >> .env.local << EOF

# KV Namespace IDs - Development
CLOUDFLARE_KV_CACHE_DEV_ID=$CACHE_KV_DEV_ID
CLOUDFLARE_KV_CACHE_DEV_PREVIEW_ID=$CACHE_KV_DEV_PREVIEW_ID
CLOUDFLARE_KV_SESSION_DEV_ID=$SESSION_KV_DEV_ID
CLOUDFLARE_KV_SESSION_DEV_PREVIEW_ID=$SESSION_KV_DEV_PREVIEW_ID

# KV Namespace IDs - Staging
CLOUDFLARE_KV_CACHE_STAGING_ID=$CACHE_KV_STAGING_ID
CLOUDFLARE_KV_SESSION_STAGING_ID=$SESSION_KV_STAGING_ID

# KV Namespace IDs - Production
CLOUDFLARE_KV_CACHE_PROD_ID=$CACHE_KV_PROD_ID
CLOUDFLARE_KV_SESSION_PROD_ID=$SESSION_KV_PROD_ID
EOF

  log "KV namespaces setup completed"
  log "Development Cache KV ID: $CACHE_KV_DEV_ID"
  log "Development Session KV ID: $SESSION_KV_DEV_ID"
  log "Staging Cache KV ID: $CACHE_KV_STAGING_ID"
  log "Production Cache KV ID: $CACHE_KV_PROD_ID"
}

# Validate Pages project (optional)
validate_pages_project() {
  log "Validating Cloudflare Pages project..."

  # List Pages projects
  if $WRANGLER_CMD pages project list &> /dev/null; then
    log "Pages projects:"
    $WRANGLER_CMD pages project list
  else
    info "Pages projects not configured yet. This is optional for initial setup."
  fi

  log "Pages validation completed"
}

# Configure DNS (if zone is managed by Cloudflare)
configure_dns() {
  log "Configuring DNS records..."
  
  # This would typically be done through the Cloudflare dashboard
  # or with specific API calls if you have the zone ID
  info "DNS configuration should be completed in Cloudflare dashboard:"
  info "1. Add A record for @ pointing to Cloudflare Pages"
  info "2. Add CNAME record for www pointing to syndicaps.com"
  info "3. Add CNAME record for api pointing to syndicaps.com"
  info "4. Add CNAME record for cdn pointing to syndicaps.com"
  
  log "DNS configuration guidance provided"
}

# Generate R2 credentials
generate_r2_credentials() {
  log "Generating R2 API credentials..."
  
  info "Please create R2 API tokens in Cloudflare dashboard:"
  info "1. Go to Cloudflare Dashboard > R2 > Manage R2 API tokens"
  info "2. Create token with 'Object Read & Write' permissions"
  info "3. Add the credentials to your .env.local file"
  
  log "R2 credentials guidance provided"
}

# Main execution
main() {
  log "Starting Cloudflare infrastructure validation for Syndicaps..."

  check_prerequisites
  create_r2_buckets
  validate_kv_namespaces
  validate_pages_project
  configure_dns
  generate_r2_credentials

  log "Cloudflare infrastructure validation completed successfully!"
  log "Infrastructure Summary:"
  info "✅ R2 Buckets: 6 buckets created (dev, staging, prod)"
  info "✅ KV Namespaces: 6 namespaces created (cache & session for each env)"
  info "✅ Wrangler CLI: Authenticated and functional"
  info "✅ Environment: Variables configured in .env.local"

  log "Next steps:"
  info "1. Configure API tokens using: bash scripts/setup-cloudflare-secrets.sh"
  info "2. Validate environment: node scripts/validate-cloudflare-env.js"
  info "3. Deploy test Workers: npx wrangler deploy --env development"
  info "4. Test connectivity and proceed to Week 2 tasks"
}

# Run main function
main "$@"
