#!/bin/bash

# Cloudflare Secrets Setup Script
# Interactive script to help configure Cloudflare API tokens and secrets

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if .env.local exists
check_env_file() {
    if [[ ! -f ".env.local" ]]; then
        log_error ".env.local file not found!"
        log_info "Please copy .env.cloudflare.example to .env.local first"
        exit 1
    fi
}

# Update environment variable in .env.local
update_env_var() {
    local var_name="$1"
    local var_value="$2"
    local env_file=".env.local"
    
    if grep -q "^${var_name}=" "$env_file"; then
        # Variable exists, update it
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s|^${var_name}=.*|${var_name}=${var_value}|" "$env_file"
        else
            # Linux
            sed -i "s|^${var_name}=.*|${var_name}=${var_value}|" "$env_file"
        fi
        log_success "Updated ${var_name} in .env.local"
    else
        # Variable doesn't exist, add it
        echo "${var_name}=${var_value}" >> "$env_file"
        log_success "Added ${var_name} to .env.local"
    fi
}

# Validate API token format
validate_api_token() {
    local token="$1"
    if [[ ${#token} -lt 40 ]]; then
        log_error "API token seems too short. Please verify the token."
        return 1
    fi
    return 0
}

# Test API token
test_api_token() {
    local token="$1"
    log_info "Testing API token..."
    
    response=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "API token is valid!"
        return 0
    else
        log_error "API token validation failed!"
        echo "Response: $response"
        return 1
    fi
}

# Setup Cloudflare API Token
setup_api_token() {
    log_header "\n🔑 Cloudflare API Token Setup"
    echo "============================================"
    
    log_info "To create an API token:"
    log_info "1. Go to https://dash.cloudflare.com/profile/api-tokens"
    log_info "2. Click 'Create Token'"
    log_info "3. Use 'Custom token' template"
    log_info "4. Add permissions:"
    log_info "   - Account: Cloudflare Workers:Edit, Account Settings:Read"
    log_info "   - Zone: Zone:Read, Zone Settings:Edit (if using custom domain)"
    log_info "5. Copy the generated token"
    
    echo ""
    read -p "Enter your Cloudflare API token: " -s api_token
    echo ""
    
    if [[ -z "$api_token" ]]; then
        log_warning "No API token provided. Skipping..."
        return 1
    fi
    
    if validate_api_token "$api_token" && test_api_token "$api_token"; then
        update_env_var "CLOUDFLARE_API_TOKEN" "$api_token"
        return 0
    else
        log_error "Invalid API token. Please try again."
        return 1
    fi
}

# Setup R2 API Credentials
setup_r2_credentials() {
    log_header "\n💾 R2 Storage API Credentials Setup"
    echo "============================================"
    
    log_info "To create R2 API credentials:"
    log_info "1. Go to https://dash.cloudflare.com/r2"
    log_info "2. Click 'Manage R2 API tokens'"
    log_info "3. Click 'Create API token'"
    log_info "4. Configure:"
    log_info "   - Token name: syndicaps-r2-access"
    log_info "   - Permissions: Object Read & Write"
    log_info "5. Copy the Access Key ID and Secret Access Key"
    
    echo ""
    read -p "Enter R2 Access Key ID: " r2_access_key
    read -p "Enter R2 Secret Access Key: " -s r2_secret_key
    echo ""
    
    if [[ -z "$r2_access_key" || -z "$r2_secret_key" ]]; then
        log_warning "R2 credentials not provided. Skipping..."
        return 1
    fi
    
    update_env_var "R2_ACCESS_KEY_ID" "$r2_access_key"
    update_env_var "R2_SECRET_ACCESS_KEY" "$r2_secret_key"
    
    log_success "R2 credentials configured!"
    return 0
}

# Setup Zone ID (optional)
setup_zone_id() {
    log_header "\n🌐 Cloudflare Zone ID Setup (Optional)"
    echo "============================================"
    
    log_info "Zone ID is required only if you're using a custom domain."
    log_info "To find your Zone ID:"
    log_info "1. Go to https://dash.cloudflare.com"
    log_info "2. Select your domain"
    log_info "3. Copy the Zone ID from the right sidebar"
    
    echo ""
    read -p "Enter your Cloudflare Zone ID (or press Enter to skip): " zone_id
    
    if [[ -n "$zone_id" ]]; then
        update_env_var "CLOUDFLARE_ZONE_ID" "$zone_id"
        log_success "Zone ID configured!"
    else
        log_info "Zone ID skipped. You can add it later if needed."
    fi
}

# Validate configuration
validate_configuration() {
    log_header "\n✅ Configuration Validation"
    echo "============================================"
    
    log_info "Running environment validation..."
    
    if command -v node >/dev/null 2>&1; then
        node scripts/validate-cloudflare-env.js
    else
        log_warning "Node.js not found. Skipping automated validation."
        log_info "Please run 'node scripts/validate-cloudflare-env.js' manually."
    fi
}

# Test infrastructure connectivity
test_infrastructure() {
    log_header "\n🧪 Infrastructure Connectivity Test"
    echo "============================================"
    
    log_info "Testing Wrangler authentication..."
    if npx wrangler whoami >/dev/null 2>&1; then
        log_success "Wrangler authentication successful!"
    else
        log_error "Wrangler authentication failed!"
        return 1
    fi
    
    log_info "Testing R2 bucket access..."
    if npx wrangler r2 bucket list >/dev/null 2>&1; then
        log_success "R2 bucket access successful!"
    else
        log_warning "R2 bucket access failed. Check your R2 credentials."
    fi
    
    log_info "Testing KV namespace access..."
    if npx wrangler kv namespace list >/dev/null 2>&1; then
        log_success "KV namespace access successful!"
    else
        log_warning "KV namespace access failed. Check your API token permissions."
    fi
}

# Main setup function
main() {
    log_header "🚀 Cloudflare Secrets Setup for Syndicaps"
    echo "============================================"
    
    check_env_file
    
    # Setup API token
    if setup_api_token; then
        log_success "API token setup completed!"
    fi
    
    # Setup R2 credentials
    if setup_r2_credentials; then
        log_success "R2 credentials setup completed!"
    fi
    
    # Setup Zone ID (optional)
    setup_zone_id
    
    # Validate configuration
    validate_configuration
    
    # Test infrastructure
    test_infrastructure
    
    log_header "\n🎉 Setup Complete!"
    echo "============================================"
    log_success "Cloudflare secrets have been configured!"
    log_info "Next steps:"
    log_info "1. Run 'bash scripts/setup-cloudflare-infrastructure.sh' to validate infrastructure"
    log_info "2. Deploy test Workers with 'npx wrangler deploy --env development'"
    log_info "3. Check the setup guide: docs/cloudflare-setup-guide.md"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
