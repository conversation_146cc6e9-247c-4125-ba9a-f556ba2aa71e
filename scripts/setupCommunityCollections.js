/**
 * Production-Ready Community Collections Setup
 * 
 * This script sets up all Firebase collections for the community system
 * with proper admin SDK authentication and error handling.
 * 
 * Requirements:
 * 1. Firebase Admin SDK service account key
 * 2. Appropriate database access permissions
 * 3. Environment variables properly configured
 * 
 * Usage: 
 *   npm run community:setup:prod
 * 
 * <AUTHOR> Team
 */

const { initializeApp, cert } = require('firebase-admin/app');
const { getFirestore, Timestamp } = require('firebase-admin/firestore');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Configuration
const CONFIG = {
  serviceAccountPath: process.env.FIREBASE_SERVICE_ACCOUNT_PATH || './firebase-service-account.json',
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  dryRun: process.env.DRY_RUN === 'true',
  overwrite: process.env.OVERWRITE_EXISTING === 'true'
};

let admin;
let db;

/**
 * Initialize Firebase Admin SDK
 */
function initializeFirebaseAdmin() {
  try {
    // Check if service account file exists
    const serviceAccountPath = path.resolve(CONFIG.serviceAccountPath);
    if (!fs.existsSync(serviceAccountPath)) {
      throw new Error(`Service account file not found: ${serviceAccountPath}`);
    }

    // Read service account key
    const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));

    // Initialize admin app
    const app = initializeApp({
      credential: cert(serviceAccount),
      projectId: CONFIG.projectId
    });

    db = getFirestore(app);
    console.log('✅ Firebase Admin SDK initialized successfully');
    return { app, db };
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin SDK:', error.message);
    throw error;
  }
}

/**
 * Utility functions
 */
const utils = {
  generateTimestamp: (daysAgo = 0) => {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return Timestamp.fromDate(date);
  },

  generateUserRef: (userId, userName, level = 1) => ({
    id: userId,
    name: userName,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userName}`,
    level: level
  }),

  logProgress: (message, details = null) => {
    console.log(`📝 ${message}`);
    if (details && process.env.NODE_ENV === 'development') {
      console.log(`   ${JSON.stringify(details, null, 2)}`);
    }
  },

  handleError: (operation, error) => {
    console.error(`❌ Error in ${operation}:`, error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
    throw error;
  }
};

/**
 * Check if document exists before creating
 */
async function documentExists(collection, docId) {
  try {
    const doc = await db.collection(collection).doc(docId).get();
    return doc.exists;
  } catch (error) {
    return false;
  }
}

/**
 * Safely create or update document
 */
async function safeCreateDocument(collection, docId, data, operation = 'create') {
  try {
    if (CONFIG.dryRun) {
      console.log(`🔍 [DRY RUN] Would ${operation} ${collection}/${docId}`);
      return;
    }

    const exists = await documentExists(collection, docId);
    
    if (exists && !CONFIG.overwrite) {
      console.log(`ℹ️  Document ${collection}/${docId} already exists, skipping`);
      return;
    }

    const action = exists ? 'Updated' : 'Created';
    await db.collection(collection).doc(docId).set(data, { merge: CONFIG.overwrite });
    console.log(`✅ ${action} ${collection}/${docId}`);
  } catch (error) {
    utils.handleError(`creating ${collection}/${docId}`, error);
  }
}

/**
 * Create community statistics collection
 */
async function createCommunityStats() {
  utils.logProgress('Setting up community statistics...');
  
  const statsData = {
    totalUsers: 2847,
    activeUsers: 234,
    totalChallenges: 12,
    activeChallenges: 8,
    totalSubmissions: 1856,
    featuredSubmissions: 24,
    totalDiscussions: 156,
    totalVotes: 89,
    totalPoints: 125640,
    weeklyGrowth: 15,
    monthlyActiveUsers: 1234,
    lastUpdated: utils.generateTimestamp(),
    createdAt: utils.generateTimestamp()
  };

  await safeCreateDocument('community_stats', 'global', statsData);
}

/**
 * Create sample challenges
 */
async function createChallenges() {
  utils.logProgress('Setting up community challenges...');
  
  const challenges = [
    {
      title: 'Winter Artisan Challenge 2024',
      description: 'Create winter-themed artisan keycaps showcasing seasonal beauty and craftsmanship.',
      category: 'Artisan Keycap',
      difficulty: 'intermediate',
      status: 'active',
      startDate: utils.generateTimestamp(7),
      endDate: utils.generateTimestamp(-21),
      bannerImage: '/images/challenges/winter-challenge.jpg',
      rewards: {
        points: 500,
        badges: ['Winter Artisan', 'Seasonal Creator'],
        prizes: ['Featured Gallery Spot', 'Community Spotlight']
      },
      requirements: [
        'Must be original winter-themed design',
        'Submit high-quality photos from multiple angles',
        'Include brief description of inspiration and process'
      ],
      submissions: 47,
      participants: 43,
      createdBy: utils.generateUserRef('admin', 'Syndicaps Team', 15),
      createdAt: utils.generateTimestamp(10),
      updatedAt: utils.generateTimestamp(1)
    },
    {
      title: 'RGB Color Harmony Challenge',
      description: 'Design a keyboard setup that demonstrates perfect RGB color harmony and coordination.',
      category: 'Setup Showcase',
      difficulty: 'beginner',
      status: 'active',
      startDate: utils.generateTimestamp(3),
      endDate: utils.generateTimestamp(-17),
      bannerImage: '/images/challenges/rgb-challenge.jpg',
      rewards: {
        points: 300,
        badges: ['Color Master', 'RGB Specialist'],
        prizes: ['RGB Keycap Set']
      },
      requirements: [
        'Demonstrate color theory principles',
        'Include lighting setup details',
        'Show color palette inspiration'
      ],
      submissions: 28,
      participants: 26,
      createdBy: utils.generateUserRef('admin', 'Syndicaps Team', 15),
      createdAt: utils.generateTimestamp(5),
      updatedAt: utils.generateTimestamp()
    },
    {
      title: 'Minimalist Mastery',
      description: 'Create the ultimate minimalist keyboard setup with clean lines and purposeful design.',
      category: 'Setup Showcase',
      difficulty: 'advanced',
      status: 'upcoming',
      startDate: utils.generateTimestamp(-7),
      endDate: utils.generateTimestamp(-35),
      bannerImage: '/images/challenges/minimalist-challenge.jpg',
      rewards: {
        points: 750,
        badges: ['Minimalist Master', 'Design Guru'],
        prizes: ['Premium Minimalist Keycap Set', 'Featured Article']
      },
      requirements: [
        'Maximum 3 colors in entire setup',
        'Demonstrate functionality over aesthetics',
        'Include design philosophy explanation'
      ],
      submissions: 0,
      participants: 156,
      createdBy: utils.generateUserRef('admin', 'Syndicaps Team', 15),
      createdAt: utils.generateTimestamp(1),
      updatedAt: utils.generateTimestamp()
    }
  ];

  for (let i = 0; i < challenges.length; i++) {
    const challenge = challenges[i];
    const docId = `challenge_${Date.now()}_${i}`;
    await safeCreateDocument('challenges', docId, challenge);
  }
}

/**
 * Create sample submissions
 */
async function createSubmissions() {
  utils.logProgress('Setting up community submissions...');
  
  const submissions = [
    {
      title: 'Frosted Winter Pine Artisan',
      description: 'Hand-sculpted pine tree keycap with frosted translucent resin and silver leaf accents. Inspired by winter mornings in the Pacific Northwest.',
      authorId: 'user_artisan_001',
      author: utils.generateUserRef('user_artisan_001', 'WinterCrafter', 12),
      images: [
        '/images/submissions/winter-pine-main.jpg',
        '/images/submissions/winter-pine-side.jpg',
        '/images/submissions/winter-pine-process.jpg'
      ],
      category: 'Artisan Keycap',
      tags: ['winter', 'pine', 'translucent', 'nature', 'handcrafted'],
      likes: 234,
      views: 1250,
      comments: 45,
      featured: true,
      status: 'approved',
      challengeId: 'winter-challenge-2024',
      submittedAt: utils.generateTimestamp(3),
      createdAt: utils.generateTimestamp(3),
      updatedAt: utils.generateTimestamp(1)
    },
    {
      title: 'Cyberpunk Neon Battlestation',
      description: 'Complete cyberpunk-themed setup with custom RGB underglow, neon keycaps, and atmospheric lighting.',
      authorId: 'user_setup_001',
      author: utils.generateUserRef('user_setup_001', 'NeonRunner', 8),
      images: [
        '/images/submissions/cyberpunk-main.jpg',
        '/images/submissions/cyberpunk-detail.jpg',
        '/images/submissions/cyberpunk-night.jpg'
      ],
      category: 'Setup Showcase',
      tags: ['cyberpunk', 'rgb', 'neon', 'battlestation', 'atmospheric'],
      likes: 189,
      views: 890,
      comments: 32,
      featured: true,
      status: 'approved',
      submittedAt: utils.generateTimestamp(5),
      createdAt: utils.generateTimestamp(5),
      updatedAt: utils.generateTimestamp(2)
    },
    {
      title: 'Handcrafted Walnut Keycap Set',
      description: 'Full keycap set carved from sustainably sourced walnut wood, featuring natural grain patterns and oil finish.',
      authorId: 'user_wood_001',
      author: utils.generateUserRef('user_wood_001', 'GrainMaster', 10),
      images: [
        '/images/submissions/walnut-set-main.jpg',
        '/images/submissions/walnut-detail.jpg'
      ],
      category: 'Artisan Keycap',
      tags: ['wood', 'walnut', 'natural', 'sustainable', 'handcrafted'],
      likes: 156,
      views: 678,
      comments: 28,
      featured: false,
      status: 'approved',
      submittedAt: utils.generateTimestamp(7),
      createdAt: utils.generateTimestamp(7),
      updatedAt: utils.generateTimestamp(4)
    },
    {
      title: 'Pastel Dream RGB Setup',
      description: 'Soft pastel RGB lighting with matching keycaps creating a dreamy, ethereal workspace atmosphere.',
      authorId: 'user_pastel_001',
      author: utils.generateUserRef('user_pastel_001', 'DreamKeeb', 6),
      images: [
        '/images/submissions/pastel-main.jpg',
        '/images/submissions/pastel-close.jpg'
      ],
      category: 'Setup Showcase',
      tags: ['pastel', 'rgb', 'dreamy', 'soft', 'aesthetic'],
      likes: 98,
      views: 445,
      comments: 19,
      featured: false,
      status: 'approved',
      challengeId: 'rgb-challenge-2024',
      submittedAt: utils.generateTimestamp(2),
      createdAt: utils.generateTimestamp(2),
      updatedAt: utils.generateTimestamp(1)
    }
  ];

  for (let i = 0; i < submissions.length; i++) {
    const submission = submissions[i];
    const docId = `submission_${Date.now()}_${i}`;
    await safeCreateDocument('submissions', docId, submission);
  }
}

/**
 * Create sample discussions
 */
async function createDiscussions() {
  utils.logProgress('Setting up community discussions...');
  
  const discussions = [
    {
      title: 'Best resin casting techniques for beginners?',
      content: 'I\'m just starting with resin casting for artisan keycaps and keep getting bubbles in my casts. What are the most important techniques for getting clean, bubble-free results? Any specific tools or materials you\'d recommend?',
      authorId: 'user_newbie_001',
      author: utils.generateUserRef('user_newbie_001', 'ResinNewbie', 2),
      category: 'Techniques',
      tags: ['resin', 'casting', 'beginner', 'tips', 'bubbles'],
      replies: 23,
      views: 567,
      isLocked: false,
      isPinned: false,
      isHot: true,
      lastActivity: utils.generateTimestamp(),
      createdAt: utils.generateTimestamp(4),
      updatedAt: utils.generateTimestamp()
    },
    {
      title: 'Weekly Build Showcase Thread',
      content: 'Share your latest keyboard builds, works in progress, and get feedback from the community! This is our weekly thread for showing off your creations and inspiring others.',
      authorId: 'admin',
      author: utils.generateUserRef('admin', 'ModeratorBot', 15),
      category: 'Showcase',
      tags: ['builds', 'showcase', 'weekly', 'community'],
      replies: 78,
      views: 2345,
      isLocked: false,
      isPinned: true,
      isHot: true,
      lastActivity: utils.generateTimestamp(),
      createdAt: utils.generateTimestamp(7),
      updatedAt: utils.generateTimestamp()
    },
    {
      title: 'Switch preferences for different activities?',
      content: 'Do you use different switch types for gaming vs typing vs programming? I\'m curious about how the community approaches switch selection for different use cases.',
      authorId: 'user_switcher_001',
      author: utils.generateUserRef('user_switcher_001', 'SwitchExplorer', 7),
      category: 'Hardware',
      tags: ['switches', 'gaming', 'typing', 'preferences'],
      replies: 34,
      views: 789,
      isLocked: false,
      isPinned: false,
      isHot: false,
      lastActivity: utils.generateTimestamp(1),
      createdAt: utils.generateTimestamp(6),
      updatedAt: utils.generateTimestamp(1)
    }
  ];

  for (let i = 0; i < discussions.length; i++) {
    const discussion = discussions[i];
    const docId = `discussion_${Date.now()}_${i}`;
    await safeCreateDocument('discussions', docId, discussion);
  }
}

/**
 * Create sample activities
 */
async function createActivities() {
  utils.logProgress('Setting up community activities...');
  
  const activities = [
    {
      type: 'submission',
      userId: 'user_artisan_001',
      user: utils.generateUserRef('user_artisan_001', 'WinterCrafter', 12),
      content: {
        title: 'New submission: Frosted Winter Pine Artisan',
        description: 'WinterCrafter shared a new artisan keycap creation',
        image: '/images/submissions/winter-pine-main.jpg'
      },
      target: {
        type: 'submission',
        id: 'submission_winter_pine_001',
        title: 'Frosted Winter Pine Artisan'
      },
      metadata: {
        category: 'Artisan Keycap',
        points: 50
      },
      engagement: {
        likes: 23,
        comments: 5,
        shares: 2
      },
      timestamp: utils.generateTimestamp(1)
    },
    {
      type: 'achievement',
      userId: 'user_newbie_001',
      user: utils.generateUserRef('user_newbie_001', 'ResinNewbie', 2),
      content: {
        title: 'Achievement unlocked: First Discussion',
        description: 'ResinNewbie started their first community discussion'
      },
      metadata: {
        badge: 'First Discussion',
        points: 25
      },
      engagement: {
        likes: 8,
        comments: 0,
        shares: 0
      },
      timestamp: utils.generateTimestamp(2)
    },
    {
      type: 'challenge',
      userId: 'user_setup_001',
      user: utils.generateUserRef('user_setup_001', 'NeonRunner', 8),
      content: {
        title: 'Joined Winter Artisan Challenge',
        description: 'NeonRunner joined the Winter Artisan Challenge 2024'
      },
      target: {
        type: 'challenge',
        id: 'winter-challenge-2024',
        title: 'Winter Artisan Challenge 2024'
      },
      engagement: {
        likes: 5,
        comments: 1,
        shares: 0
      },
      timestamp: utils.generateTimestamp(3)
    }
  ];

  for (let i = 0; i < activities.length; i++) {
    const activity = activities[i];
    const docId = `activity_${Date.now()}_${i}`;
    await safeCreateDocument('activities', docId, activity);
  }
}

/**
 * Create sample community members
 */
async function createCommunityMembers() {
  utils.logProgress('Setting up community member profiles...');
  
  const members = [
    {
      userId: 'user_artisan_001',
      username: 'WinterCrafter',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WinterCrafter',
      level: 12,
      points: 3450,
      badges: ['Artisan Expert', 'Winter Challenger', 'Community Helper'],
      joinDate: utils.generateTimestamp(120),
      lastActive: utils.generateTimestamp(),
      stats: {
        submissions: 18,
        discussions: 7,
        votes: 45,
        likes: 234
      }
    },
    {
      userId: 'user_setup_001',
      username: 'NeonRunner',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=NeonRunner',
      level: 8,
      points: 2100,
      badges: ['Setup Master', 'RGB Specialist'],
      joinDate: utils.generateTimestamp(80),
      lastActive: utils.generateTimestamp(),
      stats: {
        submissions: 12,
        discussions: 15,
        votes: 67,
        likes: 189
      }
    },
    {
      userId: 'user_wood_001',
      username: 'GrainMaster',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=GrainMaster',
      level: 10,
      points: 2800,
      badges: ['Wood Worker', 'Sustainable Creator', 'Craftsperson'],
      joinDate: utils.generateTimestamp(95),
      lastActive: utils.generateTimestamp(),
      stats: {
        submissions: 15,
        discussions: 9,
        votes: 34,
        likes: 156
      }
    }
  ];

  for (const member of members) {
    await safeCreateDocument('community_members', member.userId, member);
  }
}

/**
 * Create sample votes
 */
async function createVotes() {
  utils.logProgress('Setting up community votes...');
  
  const votes = [
    {
      title: 'Next Challenge Theme: Spring 2024',
      description: 'Help choose the theme for our upcoming Spring challenge! Vote for your favorite option.',
      authorId: 'admin',
      author: utils.generateUserRef('admin', 'Syndicaps Team', 15),
      category: 'Challenge Planning',
      tags: ['challenge', 'spring', 'voting', 'community'],
      votes: {
        up: 89,
        down: 12,
        userVotes: {}
      },
      status: 'voting',
      deadline: utils.generateTimestamp(-14),
      createdAt: utils.generateTimestamp(5),
      updatedAt: utils.generateTimestamp(1)
    }
  ];

  for (let i = 0; i < votes.length; i++) {
    const vote = votes[i];
    const docId = `vote_${Date.now()}_${i}`;
    await safeCreateDocument('votes', docId, vote);
  }
}

/**
 * Create sample leaderboard entries
 */
async function createLeaderboard() {
  utils.logProgress('Setting up community leaderboard...');
  
  const leaderboardEntries = [
    {
      userId: 'user_artisan_001',
      userName: 'WinterCrafter',
      userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WinterCrafter',
      points: 3450,
      level: 12,
      rank: 1,
      change: 2,
      badges: ['Artisan Expert', 'Winter Challenger'],
      streak: 15,
      period: 'weekly',
      lastUpdated: utils.generateTimestamp()
    },
    {
      userId: 'user_wood_001',
      userName: 'GrainMaster',
      userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=GrainMaster',
      points: 2800,
      level: 10,
      rank: 2,
      change: -1,
      badges: ['Wood Worker', 'Sustainable Creator'],
      streak: 8,
      period: 'weekly',
      lastUpdated: utils.generateTimestamp()
    },
    {
      userId: 'user_setup_001',
      userName: 'NeonRunner',
      userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=NeonRunner',
      points: 2100,
      level: 8,
      rank: 3,
      change: 1,
      badges: ['Setup Master', 'RGB Specialist'],
      streak: 5,
      period: 'weekly',
      lastUpdated: utils.generateTimestamp()
    }
  ];

  for (let i = 0; i < leaderboardEntries.length; i++) {
    const entry = leaderboardEntries[i];
    const docId = `leaderboard_${entry.period}_${Date.now()}_${i}`;
    await safeCreateDocument('leaderboard', docId, entry);
  }
}

/**
 * Create spotlight members
 */
async function createSpotlight() {
  utils.logProgress('Setting up community spotlight...');
  
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
  
  const spotlight = {
    userId: 'user_artisan_001',
    name: 'WinterCrafter',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WinterCrafter',
    level: 12,
    achievements: ['Artisan Expert', 'Winter Challenger', 'Community Helper'],
    joinDate: utils.generateTimestamp(120),
    bio: 'Passionate artisan keycap creator specializing in nature-inspired designs with intricate resin work.',
    period: currentMonth,
    createdAt: utils.generateTimestamp(1),
    updatedAt: utils.generateTimestamp()
  };

  await safeCreateDocument('spotlight_members', `spotlight_${currentMonth}`, spotlight);
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting production community collections setup...\n');
  
  if (CONFIG.dryRun) {
    console.log('🔍 DRY RUN MODE - No actual changes will be made\n');
  }

  try {
    // Initialize Firebase Admin
    initializeFirebaseAdmin();

    // Create all collections
    await createCommunityStats();
    await createChallenges();
    await createSubmissions();
    await createDiscussions();
    await createActivities();
    await createCommunityMembers();
    await createVotes();
    await createLeaderboard();
    await createSpotlight();

    console.log('\n🎉 Community collections setup completed successfully!');
    console.log('\nCreated collections:');
    console.log('  • community_stats (1 document)');
    console.log('  • challenges (3 documents)');
    console.log('  • submissions (4 documents)');
    console.log('  • discussions (3 documents)');
    console.log('  • activities (3 documents)');
    console.log('  • community_members (3 documents)');
    console.log('  • votes (1 document)');
    console.log('  • leaderboard (3 documents)');
    console.log('  • spotlight_members (1 document)');
    
    if (CONFIG.dryRun) {
      console.log('\n💡 This was a dry run. Set DRY_RUN=false to create actual data.');
    }

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().then(() => {
    console.log('\n✨ Setup complete!');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { 
  main, 
  initializeFirebaseAdmin,
  createCommunityStats,
  createChallenges,
  createSubmissions,
  createDiscussions,
  createActivities,
  createCommunityMembers,
  createVotes,
  createLeaderboard,
  createSpotlight
};