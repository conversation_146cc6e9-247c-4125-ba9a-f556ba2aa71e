#!/usr/bin/env node

/**
 * CDN Optimization and Feature Flags Test Suite
 * Comprehensive testing for Cloudflare hybrid deployment
 */

const https = require('https');
const { performance } = require('perf_hooks');

// Test configuration
const config = {
  domain: process.env.TEST_DOMAIN || 'syndicaps.com',
  protocol: 'https',
  timeout: 10000,
  retries: 3,
};

// Test results storage
const results = {
  passed: 0,
  failed: 0,
  tests: [],
};

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m',
    reset: '\x1b[0m',
  };
  
  const icons = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌',
  };
  
  console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
}

function logHeader(title) {
  console.log('\n' + '='.repeat(60));
  console.log(`🧪 ${title}`);
  console.log('='.repeat(60));
}

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: config.timeout,
    }, (res) => {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body,
          responseTime,
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.end();
  });
}

// Test function wrapper
async function runTest(name, testFn) {
  try {
    log(`Running: ${name}`, 'info');
    const result = await testFn();
    
    if (result.success) {
      log(`PASS: ${name}`, 'success');
      results.passed++;
    } else {
      log(`FAIL: ${name} - ${result.message}`, 'error');
      results.failed++;
    }
    
    results.tests.push({
      name,
      success: result.success,
      message: result.message,
      details: result.details,
    });
  } catch (error) {
    log(`ERROR: ${name} - ${error.message}`, 'error');
    results.failed++;
    results.tests.push({
      name,
      success: false,
      message: error.message,
      details: null,
    });
  }
}

// Test: Basic connectivity
async function testBasicConnectivity() {
  const url = `${config.protocol}://${config.domain}`;
  const response = await makeRequest(url);
  
  return {
    success: response.statusCode === 200,
    message: response.statusCode === 200 ? 'Site is accessible' : `HTTP ${response.statusCode}`,
    details: {
      statusCode: response.statusCode,
      responseTime: response.responseTime,
    },
  };
}

// Test: Cloudflare proxy detection
async function testCloudflareProxy() {
  const url = `${config.protocol}://${config.domain}`;
  const response = await makeRequest(url);
  
  const cfRay = response.headers['cf-ray'];
  const cfCacheStatus = response.headers['cf-cache-status'];
  
  return {
    success: !!cfRay,
    message: cfRay ? 'Cloudflare proxy detected' : 'Cloudflare proxy not detected',
    details: {
      cfRay,
      cfCacheStatus,
      server: response.headers.server,
    },
  };
}

// Test: Security headers
async function testSecurityHeaders() {
  const url = `${config.protocol}://${config.domain}`;
  const response = await makeRequest(url);
  
  const requiredHeaders = [
    'strict-transport-security',
    'x-content-type-options',
    'x-frame-options',
    'x-xss-protection',
  ];
  
  const missingHeaders = requiredHeaders.filter(header => !response.headers[header]);
  
  return {
    success: missingHeaders.length === 0,
    message: missingHeaders.length === 0 ? 'All security headers present' : `Missing headers: ${missingHeaders.join(', ')}`,
    details: {
      presentHeaders: requiredHeaders.filter(header => response.headers[header]),
      missingHeaders,
    },
  };
}

// Test: Compression (Brotli/Gzip)
async function testCompression() {
  const url = `${config.protocol}://${config.domain}`;
  
  // Test Brotli
  const brotliResponse = await makeRequest(url, {
    headers: { 'Accept-Encoding': 'br' },
  });
  
  // Test Gzip
  const gzipResponse = await makeRequest(url, {
    headers: { 'Accept-Encoding': 'gzip' },
  });
  
  const brotliSupported = brotliResponse.headers['content-encoding']?.includes('br');
  const gzipSupported = gzipResponse.headers['content-encoding']?.includes('gzip');
  
  return {
    success: brotliSupported || gzipSupported,
    message: brotliSupported ? 'Brotli compression active' : gzipSupported ? 'Gzip compression active' : 'No compression detected',
    details: {
      brotliSupported,
      gzipSupported,
      brotliEncoding: brotliResponse.headers['content-encoding'],
      gzipEncoding: gzipResponse.headers['content-encoding'],
    },
  };
}

// Test: Static asset caching
async function testStaticAssetCaching() {
  const assetUrls = [
    `${config.protocol}://${config.domain}/_next/static/css/app.css`,
    `${config.protocol}://${config.domain}/_next/static/js/app.js`,
    `${config.protocol}://${config.domain}/favicon.ico`,
  ];
  
  const results = [];
  
  for (const url of assetUrls) {
    try {
      const response = await makeRequest(url);
      const cacheControl = response.headers['cache-control'];
      const cfCacheStatus = response.headers['cf-cache-status'];
      
      results.push({
        url,
        statusCode: response.statusCode,
        cacheControl,
        cfCacheStatus,
        hasCaching: !!cacheControl && cacheControl.includes('max-age'),
      });
    } catch (error) {
      results.push({
        url,
        error: error.message,
        hasCaching: false,
      });
    }
  }
  
  const successfulTests = results.filter(r => r.hasCaching);
  
  return {
    success: successfulTests.length > 0,
    message: `${successfulTests.length}/${results.length} assets have proper caching`,
    details: results,
  };
}

// Test: API endpoint caching
async function testApiCaching() {
  const apiUrls = [
    `${config.protocol}://${config.domain}/api/health`,
    `${config.protocol}://${config.domain}/api/feature-flags`,
  ];
  
  const results = [];
  
  for (const url of apiUrls) {
    try {
      const response = await makeRequest(url);
      const cacheControl = response.headers['cache-control'];
      const cfCacheStatus = response.headers['cf-cache-status'];
      
      results.push({
        url,
        statusCode: response.statusCode,
        cacheControl,
        cfCacheStatus,
        properlyConfigured: response.statusCode < 400,
      });
    } catch (error) {
      results.push({
        url,
        error: error.message,
        properlyConfigured: false,
      });
    }
  }
  
  const workingApis = results.filter(r => r.properlyConfigured);
  
  return {
    success: workingApis.length > 0,
    message: `${workingApis.length}/${results.length} API endpoints accessible`,
    details: results,
  };
}

// Test: Performance metrics
async function testPerformanceMetrics() {
  const url = `${config.protocol}://${config.domain}`;
  const measurements = [];
  
  // Run multiple requests to get average
  for (let i = 0; i < 5; i++) {
    const response = await makeRequest(url);
    measurements.push(response.responseTime);
  }
  
  const avgResponseTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
  const minResponseTime = Math.min(...measurements);
  const maxResponseTime = Math.max(...measurements);
  
  return {
    success: avgResponseTime < 2000, // Less than 2 seconds
    message: `Average response time: ${avgResponseTime.toFixed(2)}ms`,
    details: {
      avgResponseTime,
      minResponseTime,
      maxResponseTime,
      measurements,
    },
  };
}

// Test: Feature flags API
async function testFeatureFlags() {
  const url = `${config.protocol}://${config.domain}/api/feature-flags`;
  
  try {
    const response = await makeRequest(url);
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.body);
      const hasFeatureFlags = data && typeof data === 'object';
      
      return {
        success: hasFeatureFlags,
        message: hasFeatureFlags ? 'Feature flags API working' : 'Feature flags API returned invalid data',
        details: {
          statusCode: response.statusCode,
          dataType: typeof data,
          flagCount: hasFeatureFlags ? Object.keys(data).length : 0,
        },
      };
    } else {
      return {
        success: false,
        message: `Feature flags API returned HTTP ${response.statusCode}`,
        details: { statusCode: response.statusCode },
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Feature flags API error: ${error.message}`,
      details: { error: error.message },
    };
  }
}

// Test: Cache management API
async function testCacheManagementApi() {
  const analyticsUrl = `${config.protocol}://${config.domain}/api/cache/analytics`;
  
  try {
    const response = await makeRequest(analyticsUrl);
    
    return {
      success: response.statusCode === 200 || response.statusCode === 503, // 503 if feature disabled
      message: response.statusCode === 200 ? 'Cache analytics API working' : 
               response.statusCode === 503 ? 'Cache API disabled (expected)' : 
               `Cache API returned HTTP ${response.statusCode}`,
      details: {
        statusCode: response.statusCode,
        responseTime: response.responseTime,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `Cache management API error: ${error.message}`,
      details: { error: error.message },
    };
  }
}

// Main test runner
async function runAllTests() {
  logHeader('CDN Optimization and Feature Flags Test Suite');
  
  log(`Testing domain: ${config.domain}`, 'info');
  log(`Protocol: ${config.protocol}`, 'info');
  log(`Timeout: ${config.timeout}ms`, 'info');
  
  // Run all tests
  await runTest('Basic Connectivity', testBasicConnectivity);
  await runTest('Cloudflare Proxy Detection', testCloudflareProxy);
  await runTest('Security Headers', testSecurityHeaders);
  await runTest('Compression Support', testCompression);
  await runTest('Static Asset Caching', testStaticAssetCaching);
  await runTest('API Endpoint Caching', testApiCaching);
  await runTest('Performance Metrics', testPerformanceMetrics);
  await runTest('Feature Flags API', testFeatureFlags);
  await runTest('Cache Management API', testCacheManagementApi);
  
  // Print summary
  logHeader('Test Results Summary');
  
  log(`Total tests: ${results.passed + results.failed}`, 'info');
  log(`Passed: ${results.passed}`, 'success');
  log(`Failed: ${results.failed}`, results.failed > 0 ? 'error' : 'info');
  
  const successRate = (results.passed / (results.passed + results.failed)) * 100;
  log(`Success rate: ${successRate.toFixed(1)}%`, successRate >= 80 ? 'success' : 'warning');
  
  // Print detailed results
  if (results.failed > 0) {
    logHeader('Failed Tests Details');
    results.tests
      .filter(test => !test.success)
      .forEach(test => {
        log(`${test.name}: ${test.message}`, 'error');
        if (test.details) {
          console.log('  Details:', JSON.stringify(test.details, null, 2));
        }
      });
  }
  
  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0);
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(error => {
    log(`Test suite error: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { runAllTests, config };
