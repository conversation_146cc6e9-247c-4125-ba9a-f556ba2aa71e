#!/usr/bin/env node

/**
 * Feature Flag System Test Script
 * 
 * Tests the Cloudflare hybrid deployment feature flag system including:
 * - Feature flag evaluation
 * - User targeting
 * - Emergency rollback
 * - Metrics tracking
 * - Dependency resolution
 */

const path = require('path')

// Mock environment variables for testing
process.env.FEATURE_R2_STORAGE = 'true'
process.env.R2_ROLLOUT_PERCENTAGE = '50'
process.env.FEATURE_CF_WORKERS = 'true'
process.env.CF_WORKERS_ROLLOUT_PERCENTAGE = '25'
process.env.FEATURE_CF_IMAGES = 'true'
process.env.CF_IMAGES_ROLLOUT_PERCENTAGE = '10'
process.env.PERFORMANCE_MONITORING_ENABLED = 'true'

// Colors for output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  purple: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logTest(testName) {
  log(`\n🧪 Testing: ${testName}`, 'cyan')
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

// Import the feature flag system (simulated)
const featureFlags = {
  shouldUseFeature: (featureName, userId, userType) => {
    // Simulate the feature flag logic
    const flags = {
      'USE_R2_STORAGE': { enabled: true, rolloutPercentage: 50, dependencies: [] },
      'USE_CLOUDFLARE_WORKERS': { enabled: true, rolloutPercentage: 25, dependencies: [] },
      'USE_CLOUDFLARE_IMAGES': { enabled: true, rolloutPercentage: 10, dependencies: ['USE_R2_STORAGE'] },
      'USE_PERFORMANCE_MONITORING': { enabled: true, rolloutPercentage: 100, dependencies: [] }
    }
    
    const flag = flags[featureName]
    if (!flag || !flag.enabled) return false
    
    // Check dependencies
    if (flag.dependencies) {
      for (const dep of flag.dependencies) {
        if (!featureFlags.shouldUseFeature(dep, userId, userType)) {
          return false
        }
      }
    }
    
    if (flag.rolloutPercentage === 100) return true
    if (flag.rolloutPercentage === 0) return false
    
    // Simple hash for consistent user targeting
    if (userId) {
      const hash = userId.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0)
        return a & a
      }, 0)
      return Math.abs(hash) % 100 < flag.rolloutPercentage
    }
    
    return Math.random() * 100 < flag.rolloutPercentage
  },
  
  getFeatureFlagAnalytics: () => {
    return {
      'USE_R2_STORAGE': {
        enabled: true,
        rolloutPercentage: 50,
        healthStatus: 'healthy',
        metrics: { successRate: 95.5, errorRate: 4.5 }
      },
      'USE_CLOUDFLARE_WORKERS': {
        enabled: true,
        rolloutPercentage: 25,
        healthStatus: 'warning',
        metrics: { successRate: 85.2, errorRate: 14.8 }
      }
    }
  },
  
  emergencyRollback: (featureName, reason) => {
    log(`🚨 Emergency rollback triggered for ${featureName}: ${reason}`, 'red')
    return true
  },
  
  adjustRolloutPercentage: (featureName, newPercentage, reason) => {
    log(`📊 Rollout adjusted for ${featureName}: ${newPercentage}% (${reason})`, 'blue')
    return true
  }
}

// Test Suite
async function runTests() {
  log('🚀 Starting Feature Flag System Tests', 'purple')
  log('=' * 50, 'purple')
  
  let passed = 0
  let failed = 0
  
  // Test 1: Basic Feature Flag Evaluation
  logTest('Basic Feature Flag Evaluation')
  try {
    const result1 = featureFlags.shouldUseFeature('USE_PERFORMANCE_MONITORING', 'user123')
    const result2 = featureFlags.shouldUseFeature('USE_R2_STORAGE', 'user123')
    
    if (result1 === true) {
      logSuccess('Performance monitoring enabled (100% rollout)')
      passed++
    } else {
      logError('Performance monitoring should be enabled')
      failed++
    }
    
    logSuccess(`R2 Storage result for user123: ${result2} (50% rollout)`)
    passed++
  } catch (error) {
    logError(`Basic evaluation failed: ${error.message}`)
    failed++
  }
  
  // Test 2: User Targeting Consistency
  logTest('User Targeting Consistency')
  try {
    const user1Results = []
    const user2Results = []
    
    // Test same user multiple times
    for (let i = 0; i < 5; i++) {
      user1Results.push(featureFlags.shouldUseFeature('USE_R2_STORAGE', 'consistent-user-1'))
      user2Results.push(featureFlags.shouldUseFeature('USE_R2_STORAGE', 'consistent-user-2'))
    }
    
    const user1Consistent = user1Results.every(r => r === user1Results[0])
    const user2Consistent = user2Results.every(r => r === user2Results[0])
    
    if (user1Consistent && user2Consistent) {
      logSuccess('User targeting is consistent across multiple evaluations')
      passed++
    } else {
      logError('User targeting is inconsistent')
      failed++
    }
  } catch (error) {
    logError(`User targeting test failed: ${error.message}`)
    failed++
  }
  
  // Test 3: Dependency Resolution
  logTest('Dependency Resolution')
  try {
    // CF Images depends on R2 Storage
    const mockShouldUseFeature = (featureName, userId) => {
      if (featureName === 'USE_R2_STORAGE') return false // R2 disabled
      if (featureName === 'USE_CLOUDFLARE_IMAGES') {
        // Should check dependency and return false
        return featureFlags.shouldUseFeature('USE_R2_STORAGE', userId) && true
      }
      return true
    }
    
    const result = mockShouldUseFeature('USE_CLOUDFLARE_IMAGES', 'user123')
    
    if (result === false) {
      logSuccess('Dependency resolution working: CF Images disabled when R2 disabled')
      passed++
    } else {
      logError('Dependency resolution failed')
      failed++
    }
  } catch (error) {
    logError(`Dependency test failed: ${error.message}`)
    failed++
  }
  
  // Test 4: Analytics and Metrics
  logTest('Analytics and Metrics')
  try {
    const analytics = featureFlags.getFeatureFlagAnalytics()
    
    if (analytics && typeof analytics === 'object') {
      logSuccess('Analytics data retrieved successfully')
      
      Object.entries(analytics).forEach(([flag, data]) => {
        log(`  ${flag}: ${data.rolloutPercentage}% rollout, ${data.healthStatus} status`, 'blue')
        if (data.metrics) {
          log(`    Success: ${data.metrics.successRate}%, Errors: ${data.metrics.errorRate}%`, 'blue')
        }
      })
      passed++
    } else {
      logError('Analytics data not available')
      failed++
    }
  } catch (error) {
    logError(`Analytics test failed: ${error.message}`)
    failed++
  }
  
  // Test 5: Emergency Rollback
  logTest('Emergency Rollback')
  try {
    const result = featureFlags.emergencyRollback('USE_R2_STORAGE', 'High error rate detected')
    
    if (result === true) {
      logSuccess('Emergency rollback executed successfully')
      passed++
    } else {
      logError('Emergency rollback failed')
      failed++
    }
  } catch (error) {
    logError(`Emergency rollback test failed: ${error.message}`)
    failed++
  }
  
  // Test 6: Rollout Adjustment
  logTest('Rollout Percentage Adjustment')
  try {
    const result = featureFlags.adjustRolloutPercentage('USE_CLOUDFLARE_WORKERS', 75, 'Increasing rollout after successful testing')
    
    if (result === true) {
      logSuccess('Rollout percentage adjusted successfully')
      passed++
    } else {
      logError('Rollout adjustment failed')
      failed++
    }
  } catch (error) {
    logError(`Rollout adjustment test failed: ${error.message}`)
    failed++
  }
  
  // Test Results Summary
  log('\n' + '=' * 50, 'purple')
  log('📊 Test Results Summary', 'purple')
  log('=' * 50, 'purple')
  
  logSuccess(`Passed: ${passed}`)
  if (failed > 0) {
    logError(`Failed: ${failed}`)
  } else {
    logSuccess(`Failed: ${failed}`)
  }
  
  const total = passed + failed
  const successRate = ((passed / total) * 100).toFixed(1)
  
  if (successRate >= 90) {
    logSuccess(`Overall Success Rate: ${successRate}% ✨`)
  } else if (successRate >= 70) {
    logWarning(`Overall Success Rate: ${successRate}% ⚠️`)
  } else {
    logError(`Overall Success Rate: ${successRate}% 🚨`)
  }
  
  log('\n🎯 Feature Flag System Test Complete!', 'purple')
  
  if (failed === 0) {
    log('🎉 All tests passed! Feature flag system is ready for deployment.', 'green')
    process.exit(0)
  } else {
    log('⚠️  Some tests failed. Please review and fix issues before deployment.', 'yellow')
    process.exit(1)
  }
}

// Run the tests
runTests().catch(error => {
  logError(`Test suite failed: ${error.message}`)
  process.exit(1)
})
