#!/usr/bin/env node

/**
 * Firebase Connection Test Script
 * 
 * This script tests Firebase connectivity and authentication
 * to help diagnose connection issues.
 * 
 * Usage: node scripts/test-firebase-connection.js
 */

const { initializeApp, getApps } = require('firebase/app');
const { getAuth, signInAnonymously } = require('firebase/auth');
const { getFirestore, doc, getDoc, enableNetwork, disableNetwork } = require('firebase/firestore');
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const levelColors = {
    INFO: colors.blue,
    SUCCESS: colors.green,
    WARNING: colors.yellow,
    ERROR: colors.red
  };
  
  console.log(`${levelColors[level]}[${level}]${colors.reset} ${timestamp} - ${message}`);
}

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

async function testNetworkConnectivity() {
  log('INFO', 'Testing network connectivity...');
  
  try {
    const https = require('https');
    const testUrls = [
      'https://firebase.googleapis.com',
      'https://firestore.googleapis.com',
      'https://identitytoolkit.googleapis.com'
    ];
    
    for (const url of testUrls) {
      await new Promise((resolve, reject) => {
        const req = https.get(url, (res) => {
          log('SUCCESS', `✅ ${url} - Status: ${res.statusCode}`);
          resolve();
        });
        
        req.on('error', (error) => {
          log('ERROR', `❌ ${url} - Error: ${error.message}`);
          reject(error);
        });
        
        req.setTimeout(5000, () => {
          log('ERROR', `❌ ${url} - Timeout`);
          req.destroy();
          reject(new Error('Timeout'));
        });
      });
    }
  } catch (error) {
    log('ERROR', `Network connectivity test failed: ${error.message}`);
    return false;
  }
  
  return true;
}

async function validateFirebaseConfig() {
  log('INFO', 'Validating Firebase configuration...');
  
  const requiredFields = [
    'apiKey',
    'authDomain', 
    'projectId',
    'storageBucket',
    'messagingSenderId',
    'appId'
  ];
  
  const missingFields = requiredFields.filter(field => !firebaseConfig[field]);
  
  if (missingFields.length > 0) {
    log('ERROR', `❌ Missing required Firebase config fields: ${missingFields.join(', ')}`);
    return false;
  }
  
  log('SUCCESS', '✅ Firebase configuration is valid');
  log('INFO', `Project ID: ${firebaseConfig.projectId}`);
  log('INFO', `Auth Domain: ${firebaseConfig.authDomain}`);
  
  return true;
}

async function testFirebaseConnection() {
  log('INFO', 'Testing Firebase connection...');
  
  try {
    // Initialize Firebase
    const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
    log('SUCCESS', '✅ Firebase app initialized successfully');
    
    // Test Firestore connection
    const db = getFirestore(app);
    log('INFO', 'Testing Firestore connection...');
    
    // Enable network explicitly
    await enableNetwork(db);
    log('SUCCESS', '✅ Firestore network enabled');
    
    // Test a simple read operation
    const testDoc = doc(db, 'test', 'connection');
    try {
      await getDoc(testDoc);
      log('SUCCESS', '✅ Firestore connection successful');
    } catch (firestoreError) {
      log('WARNING', `⚠️ Firestore read test failed: ${firestoreError.message}`);
      // This might be expected if the document doesn't exist or security rules block it
    }
    
    // Test Authentication
    const auth = getAuth(app);
    log('INFO', 'Testing Firebase Auth...');
    
    try {
      await signInAnonymously(auth);
      log('SUCCESS', '✅ Anonymous authentication successful');
    } catch (authError) {
      log('ERROR', `❌ Authentication failed: ${authError.message}`);
      throw authError;
    }
    
    return true;
  } catch (error) {
    log('ERROR', `❌ Firebase connection failed: ${error.message}`);
    return false;
  }
}

async function main() {
  log('INFO', '🔥 Starting Firebase Connection Test');
  log('INFO', '=====================================');
  
  try {
    // Step 1: Test network connectivity
    const networkOk = await testNetworkConnectivity();
    if (!networkOk) {
      log('ERROR', '❌ Network connectivity issues detected');
      process.exit(1);
    }
    
    // Step 2: Validate configuration
    const configOk = await validateFirebaseConfig();
    if (!configOk) {
      log('ERROR', '❌ Firebase configuration is invalid');
      process.exit(1);
    }
    
    // Step 3: Test Firebase connection
    const connectionOk = await testFirebaseConnection();
    if (!connectionOk) {
      log('ERROR', '❌ Firebase connection failed');
      process.exit(1);
    }
    
    log('SUCCESS', '🎉 All Firebase tests passed successfully!');
    log('INFO', '=====================================');
    
  } catch (error) {
    log('ERROR', `❌ Test failed with error: ${error.message}`);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main();
}

module.exports = { testNetworkConnectivity, validateFirebaseConfig, testFirebaseConnection };
