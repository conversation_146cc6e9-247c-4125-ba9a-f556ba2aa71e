#!/usr/bin/env node

/**
 * Test Firebase Connection in App Context
 * 
 * This script simulates how Firebase is used in your actual application
 * to verify the connection issues are resolved.
 */

const { initializeApp, getApps } = require('firebase/app');
const { getFirestore, doc, getDoc, enableNetwork } = require('firebase/firestore');
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const levelColors = {
    INFO: colors.blue,
    SUCCESS: colors.green,
    WARNING: colors.yellow,
    ERROR: colors.red
  };
  
  console.log(`${levelColors[level]}[${level}]${colors.reset} ${timestamp} - ${message}`);
}

// Firebase configuration (same as your app)
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

async function testAppFirebaseConnection() {
  log('INFO', '🔥 Testing Firebase Connection (App Context)');
  log('INFO', '============================================');
  
  try {
    // Initialize Firebase (same as your app)
    const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
    log('SUCCESS', '✅ Firebase app initialized');
    
    // Initialize Firestore (same as your app)
    const db = getFirestore(app);
    log('SUCCESS', '✅ Firestore initialized');
    
    // Enable network (same as your app)
    await enableNetwork(db);
    log('SUCCESS', '✅ Firestore network enabled');
    
    // Test reading a public document (like products)
    log('INFO', 'Testing Firestore read operation...');
    
    try {
      // Try to read from products collection (should be publicly readable)
      const productsRef = doc(db, 'products', 'test');
      const docSnap = await getDoc(productsRef);
      
      if (docSnap.exists()) {
        log('SUCCESS', '✅ Successfully read from Firestore');
      } else {
        log('INFO', '📄 Document does not exist (but connection works)');
      }
    } catch (firestoreError) {
      if (firestoreError.code === 'permission-denied') {
        log('WARNING', '⚠️ Permission denied (expected - security rules working)');
      } else if (firestoreError.code === 'unavailable') {
        log('ERROR', '❌ Firestore unavailable - connection issue');
        throw firestoreError;
      } else {
        log('WARNING', `⚠️ Firestore error: ${firestoreError.message}`);
      }
    }
    
    // Test connection monitoring
    log('INFO', 'Testing connection monitoring...');
    
    // Simulate network events
    const networkMonitor = {
      online: () => {
        enableNetwork(db);
        log('SUCCESS', '🟢 Network online - Firestore reconnected');
      },
      offline: () => {
        log('INFO', '🔴 Network offline - Firestore offline mode');
      }
    };
    
    // Test the monitoring functions
    networkMonitor.online();
    
    log('SUCCESS', '🎉 Firebase connection test completed successfully!');
    log('INFO', '============================================');
    
    return true;
    
  } catch (error) {
    log('ERROR', `❌ Firebase connection test failed: ${error.message}`);
    log('ERROR', `Error code: ${error.code || 'unknown'}`);
    
    // Provide specific guidance based on error
    if (error.message.includes('network-request-failed')) {
      log('INFO', '💡 This appears to be the original network issue');
      log('INFO', '   Check if you enabled anonymous authentication in Firebase Console');
    }
    
    return false;
  }
}

async function main() {
  const success = await testAppFirebaseConnection();
  process.exit(success ? 0 : 1);
}

// Run the test
if (require.main === module) {
  main();
}

module.exports = { testAppFirebaseConnection };
