#!/bin/bash

# Syndicaps Storage Setup Test Script
# Tests R2 buckets and KV namespaces connectivity and functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
  echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

# Test R2 bucket connectivity
test_r2_buckets() {
  log "Testing R2 bucket connectivity..."
  
  # List all buckets
  log "Listing all R2 buckets:"
  if wrangler r2 bucket list; then
    log "✅ R2 bucket listing successful"
  else
    error "❌ R2 bucket listing failed"
    return 1
  fi
  
  # Test upload to development bucket
  log "Testing upload to development bucket..."
  echo "test content" > test-file.txt
  
  if wrangler r2 object put syndicaps-images-dev/test-file.txt --file test-file.txt; then
    log "✅ Upload to syndicaps-images-dev successful"
  else
    error "❌ Upload to syndicaps-images-dev failed"
    return 1
  fi
  
  # Test download from development bucket
  log "Testing download from development bucket..."
  if wrangler r2 object get syndicaps-images-dev/test-file.txt --file downloaded-test.txt; then
    log "✅ Download from syndicaps-images-dev successful"
    
    # Verify content
    if diff test-file.txt downloaded-test.txt > /dev/null; then
      log "✅ File content verification successful"
    else
      error "❌ File content verification failed"
      return 1
    fi
  else
    error "❌ Download from syndicaps-images-dev failed"
    return 1
  fi
  
  # Cleanup test files
  wrangler r2 object delete syndicaps-images-dev/test-file.txt || true
  rm -f test-file.txt downloaded-test.txt
  
  log "R2 bucket tests completed successfully"
}

# Test KV namespace connectivity
test_kv_namespaces() {
  log "Testing KV namespace connectivity..."
  
  # Load environment variables
  if [ -f ".env.local" ]; then
    source .env.local
  else
    error "❌ .env.local file not found"
    return 1
  fi
  
  # Test development cache KV
  if [ -n "$CLOUDFLARE_KV_CACHE_DEV_ID" ]; then
    log "Testing development cache KV namespace..."
    
    # Put test value
    if wrangler kv:key put "test-key" "test-value" --namespace-id "$CLOUDFLARE_KV_CACHE_DEV_ID"; then
      log "✅ KV put operation successful"
    else
      error "❌ KV put operation failed"
      return 1
    fi
    
    # Get test value
    if VALUE=$(wrangler kv:key get "test-key" --namespace-id "$CLOUDFLARE_KV_CACHE_DEV_ID"); then
      if [ "$VALUE" = "test-value" ]; then
        log "✅ KV get operation successful"
      else
        error "❌ KV get operation returned wrong value: $VALUE"
        return 1
      fi
    else
      error "❌ KV get operation failed"
      return 1
    fi
    
    # Delete test value
    wrangler kv:key delete "test-key" --namespace-id "$CLOUDFLARE_KV_CACHE_DEV_ID" || true
    
  else
    error "❌ Development cache KV namespace ID not found"
    return 1
  fi
  
  log "KV namespace tests completed successfully"
}

# Test storage permissions and quotas
test_storage_quotas() {
  log "Testing storage quotas and permissions..."
  
  # Check R2 usage (if available)
  info "R2 Storage Information:"
  info "- Free Plan: Pay per use ($0.015/GB/month)"
  info "- No monthly storage fees"
  info "- Class A operations: $4.50/million"
  info "- Class B operations: $0.36/million"
  
  # Check KV usage limits
  info "KV Storage Limits (Free Plan):"
  info "- Storage: 1GB total"
  info "- Reads: 100,000/day"
  info "- Writes: 1,000/day"
  info "- Deletes: 1,000/day"
  
  log "Storage quota information displayed"
}

# Generate storage configuration summary
generate_storage_summary() {
  log "Generating storage configuration summary..."
  
  cat > storage-setup-summary.md << 'EOF'
# Syndicaps Storage Setup Summary

## R2 Buckets Created

### Development Environment
- `syndicaps-images-dev` - Development images storage
- `syndicaps-backups-dev` - Development backups storage

### Staging Environment  
- `syndicaps-images-staging` - Staging images storage
- `syndicaps-backups-staging` - Staging backups storage

### Production Environment
- `syndicaps-images` - Production images storage
- `syndicaps-backups` - Production backups storage

## KV Namespaces Created

### Development Environment
- Cache KV (with preview)
- Session KV (with preview)

### Staging Environment
- Cache KV
- Session KV

### Production Environment
- Cache KV
- Session KV

## Usage Guidelines

### R2 Storage
- Use development buckets for local testing
- Use staging buckets for pre-production validation
- Use production buckets for live deployment
- Monitor usage to stay within budget

### KV Storage
- Free plan limits: 1GB storage, 100k reads/day, 1k writes/day
- Use for caching API responses and session data
- Monitor usage to avoid hitting daily limits

## Next Steps
1. Configure R2 access in application code
2. Implement KV caching strategies
3. Set up monitoring for usage tracking
4. Test hybrid storage functionality
EOF

  log "Storage setup summary saved to storage-setup-summary.md"
}

# Main test execution
main() {
  log "Starting Cloudflare storage setup tests..."
  
  # Check prerequisites
  if ! command -v wrangler &> /dev/null; then
    error "Wrangler CLI not found. Please install and authenticate first."
    exit 1
  fi
  
  # Run tests
  test_r2_buckets
  test_kv_namespaces
  test_storage_quotas
  generate_storage_summary
  
  log "🎉 All storage tests completed successfully!"
  log "Storage resources are ready for hybrid deployment."
  
  info "Summary:"
  info "✅ R2 buckets created and tested"
  info "✅ KV namespaces created and tested"
  info "✅ Storage permissions verified"
  info "✅ Configuration summary generated"
}

# Run main function
main "$@"
