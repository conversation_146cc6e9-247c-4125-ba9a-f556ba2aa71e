// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { initializeApp } = require('firebase/app');
const { getAuth, signInWithEmailAndPassword, sendPasswordResetEmail } = require('firebase/auth');
const { getFirestore, doc, getDoc } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

async function testSuperAdminLogin() {
  try {
    console.log('🧪 Testing super admin login...');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'SuperAdmin123!@#';
    
    // Try to sign in
    try {
      const userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
      const user = userCredential.user;
      
      console.log('✅ Successfully signed in as super admin!');
      console.log('👤 User ID:', user.uid);
      console.log('📧 Email:', user.email);
      console.log('✅ Email verified:', user.emailVerified);
      
      // Check if profile exists in Firestore
      const profileRef = doc(db, 'profiles', user.uid);
      const profileSnap = await getDoc(profileRef);
      
      if (profileSnap.exists()) {
        const profileData = profileSnap.data();
        console.log('✅ Profile found in Firestore');
        console.log('🔐 Role:', profileData.role);
        console.log('👤 Display Name:', profileData.displayName);
        console.log('🎯 Super Admin:', profileData.isSuperAdmin);
        console.log('📊 Points:', profileData.points);
        
        // Check admin permissions
        const permissionsRef = doc(db, 'adminPermissions', user.uid);
        const permissionsSnap = await getDoc(permissionsRef);
        
        if (permissionsSnap.exists()) {
          const permissionsData = permissionsSnap.data();
          console.log('✅ Admin permissions found');
          console.log('🔑 Permission level:', permissionsData.level);
          console.log('📋 Permissions:', Object.keys(permissionsData.permissions).filter(p => permissionsData.permissions[p]));
        } else {
          console.log('⚠️  Admin permissions document not found');
        }
        
      } else {
        console.log('❌ Profile not found in Firestore');
      }
      
      console.log('');
      console.log('🎉 Super admin login test successful!');
      console.log('🌐 You can now access: http://localhost:3000/admin/dashboard');
      
    } catch (loginError) {
      console.error('❌ Login failed:', loginError.message);
      console.error('Error code:', loginError.code);
      
      if (loginError.code === 'auth/invalid-credential') {
        console.log('');
        console.log('🔧 The password might be incorrect. Let me try to reset it...');
        
        try {
          await sendPasswordResetEmail(auth, adminEmail);
          console.log('✅ Password reset email sent to:', adminEmail);
          console.log('📧 Check your email and reset the password');
        } catch (resetError) {
          console.error('❌ Failed to send password reset email:', resetError.message);
        }
      } else if (loginError.code === 'auth/user-not-found') {
        console.log('');
        console.log('👤 User not found. The super admin account may not have been created properly.');
        console.log('🔧 Try running: npm run create:superadmin');
      } else if (loginError.code === 'auth/too-many-requests') {
        console.log('');
        console.log('⏰ Too many failed login attempts. Please wait a few minutes and try again.');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSuperAdminLogin()
  .then(() => {
    console.log('✅ Test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });