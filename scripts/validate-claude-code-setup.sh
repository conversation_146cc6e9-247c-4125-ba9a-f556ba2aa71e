#!/bin/bash

# Claude Code Setup Validation Script
# Run this script to verify that Claude Code can properly use the AI coordination system

echo "🤖 Validating Claude Code AI Coordination Setup..."
echo "================================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# Test 1: Check if we're in the right directory
echo "🔍 Test 1: Verifying project directory..."
if [ -f "package.json" ] && [ -d "src" ] && [ -f ".ai-coordination.md" ]; then
    print_result 0 "Project directory structure is correct"
else
    print_result 1 "Not in correct Syndicaps project directory"
fi

# Test 2: Check coordination files exist
echo "🔍 Test 2: Checking coordination files..."
REQUIRED_FILES=(".ai-coordination.md" "AI_WORK_LOG.md" "HANDOFF_NOTES.md" "scripts/ai-coordination-helpers.sh")
FILES_MISSING=0

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}   Missing: $file${NC}"
        FILES_MISSING=1
    fi
done

if [ $FILES_MISSING -eq 0 ]; then
    print_result 0 "All coordination files present"
else
    print_result 1 "Some coordination files are missing"
fi

# Test 3: Check Git aliases for Claude Code
echo "🔍 Test 3: Validating Git aliases..."
CLAUDE_ALIASES=("cursor-feat" "cursor-fix" "cursor-ui" "cursor-branch")
ALIASES_MISSING=0

for alias in "${CLAUDE_ALIASES[@]}"; do
    if ! git config --get alias.$alias > /dev/null 2>&1; then
        echo -e "${RED}   Missing alias: $alias${NC}"
        ALIASES_MISSING=1
    fi
done

if [ $ALIASES_MISSING -eq 0 ]; then
    print_result 0 "All Claude Code Git aliases configured"
else
    print_result 1 "Some Git aliases are missing - run setup script"
fi

# Test 4: Check helper functions
echo "🔍 Test 4: Testing helper functions..."
if source scripts/ai-coordination-helpers.sh 2>/dev/null; then
    FUNCTIONS_WORKING=0
    
    # Test if functions are available
    if ! command -v ai-status > /dev/null 2>&1; then
        echo -e "${RED}   ai-status function not available${NC}"
        FUNCTIONS_WORKING=1
    fi
    
    if ! command -v check-claims > /dev/null 2>&1; then
        echo -e "${RED}   check-claims function not available${NC}"
        FUNCTIONS_WORKING=1
    fi
    
    if ! command -v update-log > /dev/null 2>&1; then
        echo -e "${RED}   update-log function not available${NC}"
        FUNCTIONS_WORKING=1
    fi
    
    if [ $FUNCTIONS_WORKING -eq 0 ]; then
        print_result 0 "Helper functions loaded and available"
    else
        print_result 1 "Some helper functions not working"
    fi
else
    print_result 1 "Cannot load helper functions script"
fi

# Test 5: Test Git workflow
echo "🔍 Test 5: Testing Git workflow..."
CURRENT_BRANCH=$(git branch --show-current)

# Test branch creation (but don't actually create it)
if git check-ref-format --branch "cursor/test-validation" > /dev/null 2>&1; then
    print_result 0 "Git branch naming format is valid"
else
    print_result 1 "Git branch naming format validation failed"
fi

# Test commit message format
if git config --get commit.template > /dev/null 2>&1; then
    print_result 0 "Git commit template is configured"
else
    print_result 1 "Git commit template not configured"
fi

# Test 6: Check work area understanding
echo "🔍 Test 6: Validating work area assignments..."
if grep -q "Claude Code" .ai-coordination.md && grep -q "src/components/" .ai-coordination.md; then
    print_result 0 "Work area assignments are documented"
else
    print_result 1 "Work area assignments not found in coordination file"
fi

# Test 7: Test coordination status check
echo "🔍 Test 7: Testing coordination status..."
if source scripts/ai-coordination-helpers.sh 2>/dev/null && ai-status > /dev/null 2>&1; then
    print_result 0 "Coordination status check working"
else
    print_result 1 "Coordination status check failed"
fi

# Test 8: Validate file permissions
echo "🔍 Test 8: Checking file permissions..."
if [ -x "scripts/setup-ai-coordination.sh" ] && [ -x "scripts/ai-coordination-helpers.sh" ]; then
    print_result 0 "Script files have correct permissions"
else
    print_result 1 "Script files missing execute permissions"
fi

# Summary
echo ""
echo "📊 Validation Summary:"
echo "====================="
echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! Claude Code setup is complete and ready for coordinated development.${NC}"
    echo ""
    echo "🚀 Next Steps:"
    echo "1. Load helper functions: source scripts/ai-coordination-helpers.sh"
    echo "2. Check coordination status: ai-status"
    echo "3. Review work assignments: check-claims"
    echo "4. Start development with: git cursor-branch feature-name"
    echo ""
    echo "📋 Quick Commands:"
    echo "source scripts/ai-coordination-helpers.sh  # Load functions"
    echo "check-claims                                # View current work"
    echo "git cursor-feat 'implement component'       # Make commits"
    echo "update-log 'progress update'                # Log progress"
else
    echo ""
    echo -e "${YELLOW}⚠️ Some tests failed. Please address the issues above before proceeding.${NC}"
    echo ""
    echo "🔧 Common fixes:"
    echo "1. Run setup script: ./scripts/setup-ai-coordination.sh"
    echo "2. Make scripts executable: chmod +x scripts/*.sh"
    echo "3. Load helper functions: source scripts/ai-coordination-helpers.sh"
    echo "4. Check project directory: ensure you're in the Syndicaps root"
fi

echo ""
echo "📖 For detailed setup instructions, see: CLAUDE_CODE_SETUP_GUIDE.md"
echo "🆘 If issues persist, update AI_WORK_LOG.md with your problem for Augment Agent assistance"
