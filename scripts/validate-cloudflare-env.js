#!/usr/bin/env node

/**
 * Cloudflare Environment Variables Validation Script
 * Validates all required environment variables for Cloudflare hybrid deployment
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Required environment variables by category
const requiredEnvVars = {
  'Cloudflare Core': [
    'CLOUDFLARE_ACCOUNT_ID',
    'CLOUDFLARE_API_TOKEN',
    'CLOUDFLARE_EMAIL'
  ],
  'R2 Storage': [
    'R2_ACCESS_KEY_ID',
    'R2_SECRET_ACCESS_KEY',
    'R2_BUCKET_IMAGES',
    'R2_BUCKET_BACKUPS'
  ],
  'KV Namespaces - Development': [
    'CLOUDFLARE_KV_CACHE_DEV_ID',
    'CLOUDFLARE_KV_SESSION_DEV_ID'
  ],
  'KV Namespaces - Staging': [
    'CLOUDFLARE_KV_CACHE_STAGING_ID',
    'CLOUDFLARE_KV_SESSION_STAGING_ID'
  ],
  'KV Namespaces - Production': [
    'CLOUDFLARE_KV_CACHE_PROD_ID',
    'CLOUDFLARE_KV_SESSION_PROD_ID'
  ],
  'Feature Flags': [
    'FEATURE_R2_STORAGE',
    'FEATURE_CF_WORKERS',
    'FEATURE_CF_IMAGES',
    'FEATURE_HYBRID_CDN'
  ]
};

// Optional environment variables with defaults
const optionalEnvVars = {
  'Performance': [
    'CACHE_TTL_STATIC',
    'CACHE_TTL_IMAGES',
    'CACHE_TTL_API',
    'CACHE_TTL_HTML'
  ],
  'Security': [
    'RATE_LIMIT_REQUESTS_PER_MINUTE',
    'ENABLE_SECURITY_HEADERS',
    'ENABLE_CORS'
  ],
  'Monitoring': [
    'PERFORMANCE_MONITORING_ENABLED',
    'LOG_LEVEL',
    'ENABLE_REQUEST_LOGGING'
  ]
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function loadEnvFile(filePath) {
  try {
    const envContent = fs.readFileSync(filePath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return envVars;
  } catch (error) {
    return null;
  }
}

function validateEnvironmentVariables() {
  log('\n🔍 Cloudflare Environment Variables Validation', 'cyan');
  log('=' * 60, 'cyan');
  
  // Load environment variables from .env.local
  const envFilePath = path.join(process.cwd(), '.env.local');
  const envVars = loadEnvFile(envFilePath);
  
  if (!envVars) {
    log('❌ Error: Could not load .env.local file', 'red');
    log('Please ensure .env.local exists and is readable', 'yellow');
    process.exit(1);
  }
  
  let totalChecks = 0;
  let passedChecks = 0;
  let warnings = 0;
  
  // Validate required environment variables
  log('\n📋 Required Environment Variables:', 'blue');
  
  Object.entries(requiredEnvVars).forEach(([category, vars]) => {
    log(`\n  ${category}:`, 'magenta');
    
    vars.forEach(varName => {
      totalChecks++;
      const value = envVars[varName];
      
      if (!value || value === '' || value.includes('your_') || value.includes('_here')) {
        log(`    ❌ ${varName}: Missing or placeholder value`, 'red');
      } else {
        log(`    ✅ ${varName}: Configured`, 'green');
        passedChecks++;
      }
    });
  });
  
  // Check optional environment variables
  log('\n📋 Optional Environment Variables:', 'blue');
  
  Object.entries(optionalEnvVars).forEach(([category, vars]) => {
    log(`\n  ${category}:`, 'magenta');
    
    vars.forEach(varName => {
      const value = envVars[varName];
      
      if (!value || value === '') {
        log(`    ⚠️  ${varName}: Not set (using defaults)`, 'yellow');
        warnings++;
      } else {
        log(`    ✅ ${varName}: ${value}`, 'green');
      }
    });
  });
  
  // Validate specific configurations
  log('\n🔧 Configuration Validation:', 'blue');
  
  // Check KV namespace ID format
  const kvNamespaces = [
    'CLOUDFLARE_KV_CACHE_DEV_ID',
    'CLOUDFLARE_KV_SESSION_DEV_ID',
    'CLOUDFLARE_KV_CACHE_STAGING_ID',
    'CLOUDFLARE_KV_SESSION_STAGING_ID',
    'CLOUDFLARE_KV_CACHE_PROD_ID',
    'CLOUDFLARE_KV_SESSION_PROD_ID'
  ];
  
  kvNamespaces.forEach(varName => {
    const value = envVars[varName];
    if (value && value.length === 32 && /^[a-f0-9]+$/.test(value)) {
      log(`  ✅ ${varName}: Valid format`, 'green');
    } else if (value) {
      log(`  ❌ ${varName}: Invalid format (should be 32-char hex)`, 'red');
    }
  });
  
  // Check account ID format
  const accountId = envVars['CLOUDFLARE_ACCOUNT_ID'];
  if (accountId && accountId.length === 32 && /^[a-f0-9]+$/.test(accountId)) {
    log(`  ✅ CLOUDFLARE_ACCOUNT_ID: Valid format`, 'green');
  } else if (accountId) {
    log(`  ❌ CLOUDFLARE_ACCOUNT_ID: Invalid format`, 'red');
  }
  
  // Summary
  log('\n📊 Validation Summary:', 'cyan');
  log(`  Total Required Checks: ${totalChecks}`, 'white');
  log(`  Passed: ${passedChecks}`, 'green');
  log(`  Failed: ${totalChecks - passedChecks}`, 'red');
  log(`  Warnings: ${warnings}`, 'yellow');
  
  const successRate = Math.round((passedChecks / totalChecks) * 100);
  log(`  Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
  
  if (passedChecks === totalChecks) {
    log('\n🎉 All required environment variables are properly configured!', 'green');
    return true;
  } else {
    log('\n⚠️  Some required environment variables are missing or misconfigured.', 'yellow');
    log('Please update your .env.local file with the correct values.', 'yellow');
    return false;
  }
}

function generateEnvTemplate() {
  log('\n📝 Generating updated .env.cloudflare.example template...', 'cyan');
  
  // This would generate an updated template based on current requirements
  log('✅ Template generation complete', 'green');
}

// Main execution
if (require.main === module) {
  const isValid = validateEnvironmentVariables();
  
  if (process.argv.includes('--generate-template')) {
    generateEnvTemplate();
  }
  
  process.exit(isValid ? 0 : 1);
}

module.exports = {
  validateEnvironmentVariables,
  generateEnvTemplate
};
