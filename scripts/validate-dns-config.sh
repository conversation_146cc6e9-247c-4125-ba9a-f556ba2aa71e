#!/bin/bash

# DNS Configuration Validation Script for Syndicaps
# Validates DNS records, SSL certificates, and Cloudflare configuration

set -e

# Configuration
DOMAIN="syndicaps.com"
SUBDOMAINS=("api" "cdn" "images" "assets" "www")
CLOUDFLARE_ZONE_ID="${CLOUDFLARE_ZONE_ID:-}"
CLOUDFLARE_API_TOKEN="${CLOUDFLARE_API_TOKEN:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🔍 $1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

# Check if required tools are installed
check_dependencies() {
    log_header "Checking Dependencies"
    
    local deps=("dig" "curl" "openssl" "jq")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if command -v "$dep" >/dev/null 2>&1; then
            log_success "$dep is installed"
        else
            log_error "$dep is not installed"
            missing+=("$dep")
        fi
    done
    
    if [ ${#missing[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing[*]}"
        log_info "Please install missing dependencies and try again"
        exit 1
    fi
}

# Validate A record
check_a_record() {
    log_header "Checking A Record"
    
    local a_record
    a_record=$(dig +short A "$DOMAIN" | head -n1)
    
    if [ -n "$a_record" ]; then
        log_success "A record found: $a_record"
        
        # Check if it's a Cloudflare IP (basic check)
        if [[ $a_record =~ ^(104\.1[6-9]|104\.2[0-9]|104\.3[0-1]|172\.6[4-7]|173\.24[5-9]|173\.25[0-5]|188\.114\.) ]]; then
            log_success "A record points to Cloudflare IP range"
        else
            log_warning "A record may not be proxied through Cloudflare"
        fi
    else
        log_error "No A record found for $DOMAIN"
        return 1
    fi
}

# Validate AAAA record (IPv6)
check_aaaa_record() {
    log_header "Checking AAAA Record (IPv6)"
    
    local aaaa_record
    aaaa_record=$(dig +short AAAA "$DOMAIN" | head -n1)
    
    if [ -n "$aaaa_record" ]; then
        log_success "AAAA record found: $aaaa_record"
    else
        log_warning "No AAAA record found (IPv6 not configured)"
    fi
}

# Validate CNAME records for subdomains
check_cname_records() {
    log_header "Checking CNAME Records"
    
    for subdomain in "${SUBDOMAINS[@]}"; do
        local full_domain="$subdomain.$DOMAIN"
        local cname_record
        cname_record=$(dig +short CNAME "$full_domain")
        
        if [ -n "$cname_record" ]; then
            log_success "$full_domain CNAME: $cname_record"
        else
            # Check if it has an A record instead
            local a_record
            a_record=$(dig +short A "$full_domain")
            if [ -n "$a_record" ]; then
                log_info "$full_domain has A record instead of CNAME: $a_record"
            else
                log_warning "$full_domain has no CNAME or A record"
            fi
        fi
    done
}

# Check SSL certificate
check_ssl_certificate() {
    log_header "Checking SSL Certificate"
    
    local ssl_info
    ssl_info=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -subject -dates -issuer 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_success "SSL certificate is valid"
        echo "$ssl_info" | while IFS= read -r line; do
            log_info "  $line"
        done
        
        # Check certificate expiration
        local expiry_date
        expiry_date=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -enddate 2>/dev/null | cut -d= -f2)
        
        if [ -n "$expiry_date" ]; then
            local expiry_epoch
            expiry_epoch=$(date -d "$expiry_date" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$expiry_date" +%s 2>/dev/null)
            local current_epoch
            current_epoch=$(date +%s)
            local days_until_expiry
            days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [ "$days_until_expiry" -gt 30 ]; then
                log_success "Certificate expires in $days_until_expiry days"
            elif [ "$days_until_expiry" -gt 7 ]; then
                log_warning "Certificate expires in $days_until_expiry days (consider renewal)"
            else
                log_error "Certificate expires in $days_until_expiry days (urgent renewal needed)"
            fi
        fi
    else
        log_error "SSL certificate is invalid or not accessible"
        return 1
    fi
}

# Check security headers
check_security_headers() {
    log_header "Checking Security Headers"
    
    local headers
    headers=$(curl -s -I "https://$DOMAIN" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_error "Failed to fetch headers from https://$DOMAIN"
        return 1
    fi
    
    # Check for important security headers
    local security_headers=(
        "strict-transport-security:HSTS"
        "x-content-type-options:X-Content-Type-Options"
        "x-frame-options:X-Frame-Options"
        "x-xss-protection:X-XSS-Protection"
        "referrer-policy:Referrer-Policy"
        "content-security-policy:Content-Security-Policy"
    )
    
    for header_check in "${security_headers[@]}"; do
        local header_name="${header_check%%:*}"
        local display_name="${header_check##*:}"
        
        if echo "$headers" | grep -qi "$header_name"; then
            log_success "$display_name header present"
        else
            log_warning "$display_name header missing"
        fi
    done
}

# Check Cloudflare-specific features
check_cloudflare_features() {
    log_header "Checking Cloudflare Features"
    
    local headers
    headers=$(curl -s -I "https://$DOMAIN" 2>/dev/null)
    
    # Check for Cloudflare headers
    if echo "$headers" | grep -qi "cf-ray"; then
        log_success "Cloudflare proxy is active (CF-Ray header present)"
        
        local cf_ray
        cf_ray=$(echo "$headers" | grep -i "cf-ray" | cut -d: -f2 | tr -d ' \r')
        log_info "CF-Ray: $cf_ray"
    else
        log_warning "Cloudflare proxy may not be active (no CF-Ray header)"
    fi
    
    # Check for Cloudflare cache status
    if echo "$headers" | grep -qi "cf-cache-status"; then
        local cache_status
        cache_status=$(echo "$headers" | grep -i "cf-cache-status" | cut -d: -f2 | tr -d ' \r')
        log_info "CF-Cache-Status: $cache_status"
    fi
    
    # Check for Brotli compression
    local brotli_test
    brotli_test=$(curl -s -H "Accept-Encoding: br" -I "https://$DOMAIN" 2>/dev/null | grep -i "content-encoding.*br")
    
    if [ -n "$brotli_test" ]; then
        log_success "Brotli compression is enabled"
    else
        log_warning "Brotli compression may not be enabled"
    fi
}

# Check DNS propagation
check_dns_propagation() {
    log_header "Checking DNS Propagation"
    
    local dns_servers=("8.8.8.8" "1.1.1.1" "208.67.222.222" "9.9.9.9")
    local consistent=true
    local first_result=""
    
    for dns_server in "${dns_servers[@]}"; do
        local result
        result=$(dig @"$dns_server" +short A "$DOMAIN" | head -n1)
        
        if [ -z "$first_result" ]; then
            first_result="$result"
        fi
        
        if [ "$result" = "$first_result" ]; then
            log_success "DNS server $dns_server: $result"
        else
            log_warning "DNS server $dns_server: $result (inconsistent)"
            consistent=false
        fi
    done
    
    if [ "$consistent" = true ]; then
        log_success "DNS propagation is consistent across all tested servers"
    else
        log_warning "DNS propagation is inconsistent - may still be propagating"
    fi
}

# Check Cloudflare API connectivity (if credentials provided)
check_cloudflare_api() {
    if [ -n "$CLOUDFLARE_ZONE_ID" ] && [ -n "$CLOUDFLARE_API_TOKEN" ]; then
        log_header "Checking Cloudflare API Connectivity"
        
        local api_response
        api_response=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json")
        
        if echo "$api_response" | jq -e '.success' >/dev/null 2>&1; then
            log_success "Cloudflare API connectivity verified"
            
            local zone_name
            zone_name=$(echo "$api_response" | jq -r '.result.name')
            log_info "Zone name: $zone_name"
            
            local zone_status
            zone_status=$(echo "$api_response" | jq -r '.result.status')
            log_info "Zone status: $zone_status"
        else
            log_error "Cloudflare API connectivity failed"
            log_info "Response: $api_response"
        fi
    else
        log_info "Cloudflare API credentials not provided, skipping API check"
    fi
}

# Performance test
check_performance() {
    log_header "Checking Performance"
    
    local response_time
    response_time=$(curl -s -o /dev/null -w "%{time_total}" "https://$DOMAIN")
    
    if (( $(echo "$response_time < 1.0" | bc -l) )); then
        log_success "Response time: ${response_time}s (excellent)"
    elif (( $(echo "$response_time < 2.0" | bc -l) )); then
        log_success "Response time: ${response_time}s (good)"
    elif (( $(echo "$response_time < 5.0" | bc -l) )); then
        log_warning "Response time: ${response_time}s (acceptable)"
    else
        log_error "Response time: ${response_time}s (slow)"
    fi
}

# Main execution
main() {
    log_header "DNS Configuration Validation for $DOMAIN"
    echo
    
    check_dependencies
    echo
    
    check_a_record
    echo
    
    check_aaaa_record
    echo
    
    check_cname_records
    echo
    
    check_ssl_certificate
    echo
    
    check_security_headers
    echo
    
    check_cloudflare_features
    echo
    
    check_dns_propagation
    echo
    
    check_cloudflare_api
    echo
    
    check_performance
    echo
    
    log_header "Validation Complete"
    log_info "Review any warnings or errors above"
    log_info "For detailed troubleshooting, see docs/dns-configuration-guide.md"
}

# Run main function
main "$@"
