#!/usr/bin/env node

/**
 * Firebase Setup Validation Script
 * 
 * Comprehensive validation script for Firebase configuration,
 * indexes, and rules for the Syndicaps admin dashboard.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Required admin dashboard queries that need indexes
const requiredIndexes = [
  {
    collection: 'orders',
    fields: [{ field: 'createdAt', order: 'desc' }],
    description: 'Admin orders list sorted by creation date'
  },
  {
    collection: 'orders',
    fields: [{ field: 'status', order: 'asc' }, { field: 'createdAt', order: 'desc' }],
    description: 'Admin orders filtered by status'
  },
  {
    collection: 'orders',
    fields: [{ field: 'paymentStatus', order: 'asc' }, { field: 'createdAt', order: 'desc' }],
    description: 'Admin orders filtered by payment status'
  },
  {
    collection: 'orders',
    fields: [{ field: 'userId', order: 'asc' }, { field: 'createdAt', order: 'desc' }],
    description: 'User-specific orders for admin view'
  },
  {
    collection: 'profiles',
    fields: [{ field: 'createdAt', order: 'desc' }],
    description: 'Admin users list sorted by creation date'
  },
  {
    collection: 'profiles',
    fields: [{ field: 'role', order: 'asc' }, { field: 'createdAt', order: 'desc' }],
    description: 'Admin users filtered by role'
  },
  {
    collection: 'profiles',
    fields: [{ field: 'points', order: 'desc' }],
    description: 'Admin users sorted by points'
  },
  {
    collection: 'profiles',
    fields: [{ field: 'lastLogin', order: 'desc' }],
    description: 'Admin users sorted by last login'
  },
  {
    collection: 'products',
    fields: [{ field: 'createdAt', order: 'desc' }],
    description: 'Admin products list sorted by creation date'
  },
  {
    collection: 'reviews',
    fields: [{ field: 'status', order: 'asc' }, { field: 'createdAt', order: 'desc' }],
    description: 'Admin reviews filtered by status (pending, approved, etc.)'
  },
  {
    collection: 'raffles',
    fields: [{ field: 'status', order: 'asc' }, { field: 'startDate', order: 'asc' }],
    description: 'Admin raffles filtered by status'
  },
  {
    collection: 'point_transactions',
    fields: [{ field: 'userId', order: 'asc' }, { field: 'createdAt', order: 'desc' }],
    description: 'User point transactions for admin view'
  },
  {
    collection: 'point_transactions',
    fields: [{ field: 'type', order: 'asc' }, { field: 'createdAt', order: 'desc' }],
    description: 'Point transactions filtered by type'
  }
];

function validateFirebaseConfig() {
  logInfo('Validating Firebase configuration...');
  
  const requiredEnvVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID'
  ];
  
  let allPresent = true;
  
  // Check .env.local file
  const envPath = path.join(process.cwd(), '.env.local');
  if (!fs.existsSync(envPath)) {
    logError('.env.local file not found');
    return false;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  requiredEnvVars.forEach(varName => {
    if (!envContent.includes(varName)) {
      logError(`Missing environment variable: ${varName}`);
      allPresent = false;
    } else {
      logSuccess(`Found environment variable: ${varName}`);
    }
  });
  
  return allPresent;
}

function validateIndexesFile() {
  logInfo('Validating firestore.indexes.json...');
  
  const indexesPath = path.join(process.cwd(), 'firestore.indexes.json');
  
  if (!fs.existsSync(indexesPath)) {
    logError('firestore.indexes.json file not found');
    return false;
  }
  
  try {
    const indexesContent = fs.readFileSync(indexesPath, 'utf8');
    const indexesData = JSON.parse(indexesContent);
    
    if (!indexesData.indexes || !Array.isArray(indexesData.indexes)) {
      logError('Invalid indexes file structure');
      return false;
    }
    
    logSuccess(`Found ${indexesData.indexes.length} indexes in configuration`);
    
    // Check for required admin indexes
    let missingIndexes = [];
    
    requiredIndexes.forEach(requiredIndex => {
      const found = indexesData.indexes.some(index => {
        if (index.collectionGroup !== requiredIndex.collection) {
          return false;
        }
        
        // Check if all required fields are present
        return requiredIndex.fields.every(reqField => {
          return index.fields.some(indexField => {
            return indexField.fieldPath === reqField.field &&
                   (reqField.order === 'asc' ? indexField.order === 'ASCENDING' : indexField.order === 'DESCENDING');
          });
        });
      });
      
      if (!found) {
        missingIndexes.push(requiredIndex);
      }
    });
    
    if (missingIndexes.length > 0) {
      logWarning(`Missing ${missingIndexes.length} required admin indexes:`);
      missingIndexes.forEach(index => {
        logWarning(`  - ${index.collection}: ${index.description}`);
      });
      return false;
    } else {
      logSuccess('All required admin indexes are present');
    }
    
    return true;
    
  } catch (error) {
    logError(`Error parsing indexes file: ${error.message}`);
    return false;
  }
}

function validateRulesFile() {
  logInfo('Validating firestore.rules...');
  
  const rulesPath = path.join(process.cwd(), 'firestore.rules');
  
  if (!fs.existsSync(rulesPath)) {
    logError('firestore.rules file not found');
    return false;
  }
  
  const rulesContent = fs.readFileSync(rulesPath, 'utf8');
  
  // Check for admin-specific rules
  const requiredRules = [
    'isAdmin()',
    'match /profiles/',
    'match /orders/',
    'match /products/',
    'match /reviews/',
    'match /raffles/'
  ];
  
  let allRulesPresent = true;
  
  requiredRules.forEach(rule => {
    if (!rulesContent.includes(rule)) {
      logError(`Missing rule pattern: ${rule}`);
      allRulesPresent = false;
    } else {
      logSuccess(`Found rule pattern: ${rule}`);
    }
  });
  
  return allRulesPresent;
}

function generateDeploymentCommands() {
  logInfo('Generating deployment commands...');
  
  console.log('\n' + '='.repeat(60));
  log('🚀 Firebase Deployment Commands', 'cyan');
  console.log('='.repeat(60));
  
  console.log('\n1. Deploy indexes only:');
  log('   firebase deploy --only firestore:indexes', 'yellow');
  
  console.log('\n2. Deploy rules only:');
  log('   firebase deploy --only firestore:rules', 'yellow');
  
  console.log('\n3. Deploy both indexes and rules:');
  log('   firebase deploy --only firestore', 'yellow');
  
  console.log('\n4. Deploy everything:');
  log('   firebase deploy', 'yellow');
  
  console.log('\n5. Use the deployment script:');
  log('   chmod +x scripts/deploy-firebase-indexes.sh', 'yellow');
  log('   ./scripts/deploy-firebase-indexes.sh', 'yellow');
  
  console.log('\n' + '='.repeat(60));
}

function main() {
  console.log('🔥 Firebase Setup Validation for Syndicaps Admin Dashboard');
  console.log('=' .repeat(65));
  console.log('');
  
  let allValid = true;
  
  // Validate Firebase configuration
  if (!validateFirebaseConfig()) {
    allValid = false;
  }
  
  console.log('');
  
  // Validate indexes
  if (!validateIndexesFile()) {
    allValid = false;
  }
  
  console.log('');
  
  // Validate rules
  if (!validateRulesFile()) {
    allValid = false;
  }
  
  console.log('');
  
  if (allValid) {
    logSuccess('🎉 All Firebase configurations are valid!');
    generateDeploymentCommands();
  } else {
    logError('❌ Some Firebase configurations need attention');
    console.log('\nPlease fix the issues above before deploying to Firebase.');
  }
  
  console.log('');
}

// Run the validation
main();
