#!/usr/bin/env node

/**
 * Verify Firebase Indexes Script
 * 
 * Quick verification script to check if the final 2 indexes
 * have been created successfully.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Required indexes to check
const requiredIndexes = [
  {
    collection: 'profiles',
    fields: ['role', 'createdAt'],
    description: 'Admin user filtering by role'
  },
  {
    collection: 'point_transactions', 
    fields: ['type', 'createdAt'],
    description: 'Admin point transaction filtering by type'
  }
];

function checkIndexes() {
  logInfo('Checking Firebase indexes...');
  
  try {
    // Get current indexes
    const result = execSync('firebase firestore:indexes --format=json', { 
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let indexes;
    try {
      indexes = JSON.parse(result);
    } catch (parseError) {
      logWarning('Could not parse indexes JSON, checking manually...');
      return checkIndexesManually();
    }
    
    const existingIndexes = indexes.indexes || [];
    let allFound = true;
    
    log('\n📋 Checking required admin dashboard indexes:', 'cyan');
    
    requiredIndexes.forEach((requiredIndex, i) => {
      const found = existingIndexes.some(existing => {
        if (existing.collectionGroup !== requiredIndex.collection) {
          return false;
        }
        
        // Check if all required fields are present
        return requiredIndex.fields.every(fieldName => {
          return existing.fields.some(existingField => {
            return existingField.fieldPath === fieldName;
          });
        });
      });
      
      if (found) {
        logSuccess(`${i + 1}. ${requiredIndex.collection}: ${requiredIndex.description}`);
      } else {
        logError(`${i + 1}. ${requiredIndex.collection}: MISSING - ${requiredIndex.description}`);
        allFound = false;
      }
    });
    
    return allFound;
    
  } catch (error) {
    logError(`Error checking indexes: ${error.message}`);
    return checkIndexesManually();
  }
}

function checkIndexesManually() {
  log('\n🔍 Manual verification needed:', 'yellow');
  log('1. Check Firebase Console: https://console.firebase.google.com/project/syndicaps-fullpower/firestore/indexes');
  log('2. Look for these indexes:');
  
  requiredIndexes.forEach((index, i) => {
    log(`   ${i + 1}. Collection: ${index.collection}`);
    log(`      Fields: ${index.fields.join(', ')}`);
    log(`      Status: Should show "Enabled"`);
  });
  
  return false;
}

function testAdminDashboard() {
  logInfo('Testing admin dashboard connectivity...');
  
  try {
    // Test if the development server is running
    const { execSync } = require('child_process');
    
    // Check if port 3000 is in use (Next.js dev server)
    try {
      execSync('lsof -i :3000', { stdio: 'pipe' });
      logSuccess('Development server is running on port 3000');
      log('🎯 Test your admin dashboard: http://localhost:3000/admin/dashboard', 'cyan');
      return true;
    } catch (error) {
      logWarning('Development server not detected on port 3000');
      log('💡 Start the dev server: npm run dev', 'yellow');
      return false;
    }
    
  } catch (error) {
    logWarning('Could not check development server status');
    return false;
  }
}

function showNextSteps(indexesComplete) {
  log('\n' + '='.repeat(60), 'cyan');
  log('🎯 NEXT STEPS', 'cyan');
  log('='.repeat(60), 'cyan');
  
  if (indexesComplete) {
    logSuccess('🎉 All indexes are ready!');
    log('\n1. Test admin dashboard performance:');
    log('   http://localhost:3000/admin/dashboard', 'yellow');
    log('\n2. Check these admin functions:');
    log('   - Orders list and filtering');
    log('   - User management by role');
    log('   - Point transaction filtering');
    log('   - Product management');
    log('\n3. Monitor for any remaining errors in browser console');
    
  } else {
    logWarning('Some indexes are still missing or building');
    log('\n1. Complete index creation in Firebase Console:');
    log('   https://console.firebase.google.com/project/syndicaps-fullpower/firestore/indexes', 'yellow');
    log('\n2. Wait for indexes to finish building (1-5 minutes each)');
    log('\n3. Run this script again: node scripts/verify-indexes.js');
    log('\n4. Then test the admin dashboard');
  }
  
  log('\n💡 Useful commands:');
  log('   npm run firebase:validate  - Full validation');
  log('   npm run dev               - Start development server');
  log('   firebase firestore:indexes - Check index status');
}

function main() {
  console.log('🔍 Firebase Indexes Verification');
  console.log('=' .repeat(35));
  console.log('');
  
  // Check if indexes exist
  const indexesComplete = checkIndexes();
  
  // Test development server
  const serverRunning = testAdminDashboard();
  
  // Show next steps
  showNextSteps(indexesComplete);
  
  if (indexesComplete && serverRunning) {
    log('\n🚀 Everything looks good! Your admin dashboard should be fully optimized.', 'green');
  }
}

// Run the verification
main();
