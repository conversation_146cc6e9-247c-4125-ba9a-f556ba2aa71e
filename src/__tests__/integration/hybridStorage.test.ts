/**
 * Hybrid Storage Functionality Validation Tests
 * Test feature flag-based storage selection, fallback mechanisms, and performance monitoring
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals'
import { featureFlags } from '../../lib/feature-flags/featureFlags'
import { R2StorageService } from '../../lib/cloudflare/r2StorageService'
import { r2PerformanceMonitor } from '../../lib/cloudflare/r2PerformanceMonitor'

// Test configuration
const TEST_CONFIG = {
  timeoutMs: 30000, // 30 seconds
  testImageSize: 10 * 1024, // 10KB
  performanceThresholds: {
    uploadTime: 5000, // 5 seconds
    downloadTime: 3000, // 3 seconds
    fallbackTime: 2000 // 2 seconds
  }
}

// Mock storage services
class MockFirebaseStorage {
  private mockData = new Map<string, ArrayBuffer>()
  private shouldFail = false

  setShouldFail(fail: boolean) {
    this.shouldFail = fail
  }

  async upload(path: string, data: ArrayBuffer): Promise<{ success: boolean; url?: string; error?: string }> {
    if (this.shouldFail) {
      return { success: false, error: 'Firebase storage unavailable' }
    }

    this.mockData.set(path, data)
    return {
      success: true,
      url: `https://firebasestorage.googleapis.com/test/${path}`
    }
  }

  async download(path: string): Promise<{ success: boolean; data?: ArrayBuffer; error?: string }> {
    if (this.shouldFail) {
      return { success: false, error: 'Firebase storage unavailable' }
    }

    const data = this.mockData.get(path)
    if (!data) {
      return { success: false, error: 'File not found' }
    }

    return { success: true, data }
  }

  async delete(path: string): Promise<{ success: boolean; error?: string }> {
    if (this.shouldFail) {
      return { success: false, error: 'Firebase storage unavailable' }
    }

    this.mockData.delete(path)
    return { success: true }
  }

  isAvailable(): boolean {
    return !this.shouldFail
  }
}

// Hybrid storage service that uses feature flags
class HybridStorageService {
  private r2Service: R2StorageService
  private firebaseService: MockFirebaseStorage

  constructor() {
    this.r2Service = new R2StorageService()
    this.firebaseService = new MockFirebaseStorage()
  }

  async upload(params: {
    key: string
    body: ArrayBuffer
    contentType: string
    metadata?: Record<string, string>
  }): Promise<{ success: boolean; url?: string; storage?: string; error?: string; fallbackUsed?: boolean }> {
    const useR2 = await featureFlags.getFlag('USE_R2_STORAGE')
    
    if (useR2) {
      try {
        const result = await this.r2Service.upload({
          bucketType: 'images',
          key: params.key,
          body: params.body,
          contentType: params.contentType,
          metadata: params.metadata
        })

        if (result.success) {
          return {
            success: true,
            url: result.url,
            storage: 'r2'
          }
        }

        // R2 failed, try fallback to Firebase
        const fallbackResult = await this.firebaseService.upload(params.key, params.body)
        return {
          success: fallbackResult.success,
          url: fallbackResult.url,
          storage: 'firebase',
          fallbackUsed: true,
          error: fallbackResult.error
        }
      } catch (error) {
        // R2 failed, try fallback to Firebase
        const fallbackResult = await this.firebaseService.upload(params.key, params.body)
        return {
          success: fallbackResult.success,
          url: fallbackResult.url,
          storage: 'firebase',
          fallbackUsed: true,
          error: fallbackResult.error || String(error)
        }
      }
    } else {
      // Use Firebase as primary
      const result = await this.firebaseService.upload(params.key, params.body)
      return {
        success: result.success,
        url: result.url,
        storage: 'firebase',
        error: result.error
      }
    }
  }

  async download(key: string): Promise<{ success: boolean; data?: ArrayBuffer; storage?: string; error?: string; fallbackUsed?: boolean }> {
    const useR2 = await featureFlags.getFlag('USE_R2_STORAGE')
    
    if (useR2) {
      try {
        const result = await this.r2Service.download({
          bucketType: 'images',
          key
        })

        if (result.success) {
          return {
            success: true,
            data: result.data as ArrayBuffer,
            storage: 'r2'
          }
        }

        // R2 failed, try fallback to Firebase
        const fallbackResult = await this.firebaseService.download(key)
        return {
          success: fallbackResult.success,
          data: fallbackResult.data,
          storage: 'firebase',
          fallbackUsed: true,
          error: fallbackResult.error
        }
      } catch (error) {
        // R2 failed, try fallback to Firebase
        const fallbackResult = await this.firebaseService.download(key)
        return {
          success: fallbackResult.success,
          data: fallbackResult.data,
          storage: 'firebase',
          fallbackUsed: true,
          error: fallbackResult.error || String(error)
        }
      }
    } else {
      // Use Firebase as primary
      const result = await this.firebaseService.download(key)
      return {
        success: result.success,
        data: result.data,
        storage: 'firebase',
        error: result.error
      }
    }
  }

  async delete(key: string): Promise<{ success: boolean; storage?: string; error?: string; fallbackUsed?: boolean }> {
    const useR2 = await featureFlags.getFlag('USE_R2_STORAGE')
    
    if (useR2) {
      try {
        const result = await this.r2Service.delete({
          bucketType: 'images',
          key
        })

        if (result.success) {
          return {
            success: true,
            storage: 'r2'
          }
        }

        // R2 failed, try fallback to Firebase
        const fallbackResult = await this.firebaseService.delete(key)
        return {
          success: fallbackResult.success,
          storage: 'firebase',
          fallbackUsed: true,
          error: fallbackResult.error
        }
      } catch (error) {
        // R2 failed, try fallback to Firebase
        const fallbackResult = await this.firebaseService.delete(key)
        return {
          success: fallbackResult.success,
          storage: 'firebase',
          fallbackUsed: true,
          error: fallbackResult.error || String(error)
        }
      }
    } else {
      // Use Firebase as primary
      const result = await this.firebaseService.delete(key)
      return {
        success: result.success,
        storage: 'firebase',
        error: result.error
      }
    }
  }

  // Expose Firebase service for testing
  getFirebaseService(): MockFirebaseStorage {
    return this.firebaseService
  }
}

describe('Hybrid Storage Functionality Validation', () => {
  let hybridStorage: HybridStorageService
  let testData: ArrayBuffer
  let uploadedKeys: string[] = []

  beforeAll(async () => {
    hybridStorage = new HybridStorageService()
    
    // Generate test image data
    testData = new ArrayBuffer(TEST_CONFIG.testImageSize)
    const view = new Uint8Array(testData)
    for (let i = 0; i < view.length; i++) {
      view[i] = i % 256
    }

    // Clear performance monitor
    r2PerformanceMonitor.clearMetrics()
  }, TEST_CONFIG.timeoutMs)

  afterAll(async () => {
    // Clean up uploaded test files
    for (const key of uploadedKeys) {
      try {
        await hybridStorage.delete(key)
      } catch (error) {
        console.warn(`Failed to cleanup test file ${key}:`, error)
      }
    }

    // Reset feature flags
    await featureFlags.setFlag('USE_R2_STORAGE', false)
  }, TEST_CONFIG.timeoutMs)

  beforeEach(() => {
    // Reset performance monitor before each test
    r2PerformanceMonitor.clearMetrics()
  })

  describe('Feature Flag-Based Storage Selection', () => {
    test('should use R2 storage when flag is enabled', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      
      const key = `test/flag-enabled/${Date.now()}-test.jpg`
      uploadedKeys.push(key)

      const result = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg',
        metadata: { testType: 'flag-enabled' }
      })

      expect(result.success).toBe(true)
      expect(result.storage).toBe('r2')
      expect(result.fallbackUsed).toBeUndefined()
      expect(result.url).toContain(key)
    }, TEST_CONFIG.timeoutMs)

    test('should use Firebase storage when flag is disabled', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', false)
      
      const key = `test/flag-disabled/${Date.now()}-test.jpg`

      const result = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg',
        metadata: { testType: 'flag-disabled' }
      })

      expect(result.success).toBe(true)
      expect(result.storage).toBe('firebase')
      expect(result.fallbackUsed).toBeUndefined()
      expect(result.url).toContain('firebasestorage.googleapis.com')
    }, TEST_CONFIG.timeoutMs)

    test('should switch storage based on flag changes', async () => {
      const key1 = `test/flag-switch-1/${Date.now()}-test.jpg`
      const key2 = `test/flag-switch-2/${Date.now()}-test.jpg`
      uploadedKeys.push(key1, key2)

      // First upload with R2 enabled
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      const result1 = await hybridStorage.upload({
        key: key1,
        body: testData,
        contentType: 'image/jpeg'
      })

      expect(result1.storage).toBe('r2')

      // Second upload with R2 disabled
      await featureFlags.setFlag('USE_R2_STORAGE', false)
      const result2 = await hybridStorage.upload({
        key: key2,
        body: testData,
        contentType: 'image/jpeg'
      })

      expect(result2.storage).toBe('firebase')
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Fallback Mechanisms', () => {
    test('should fallback to Firebase when R2 is unavailable', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      
      // Simulate R2 failure by using invalid credentials or endpoint
      const key = `test/r2-fallback/${Date.now()}-test.jpg`

      const result = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg',
        metadata: { testType: 'r2-fallback' }
      })

      // Should either succeed with R2 or fallback to Firebase
      expect(result.success).toBe(true)
      if (result.fallbackUsed) {
        expect(result.storage).toBe('firebase')
        expect(result.url).toContain('firebasestorage.googleapis.com')
      } else {
        expect(result.storage).toBe('r2')
      }
    }, TEST_CONFIG.timeoutMs)

    test('should handle Firebase unavailability gracefully', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', false)
      
      // Simulate Firebase failure
      const firebaseService = hybridStorage.getFirebaseService()
      firebaseService.setShouldFail(true)

      const key = `test/firebase-fail/${Date.now()}-test.jpg`

      const result = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg',
        metadata: { testType: 'firebase-fail' }
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Firebase storage unavailable')

      // Restore Firebase
      firebaseService.setShouldFail(false)
    }, TEST_CONFIG.timeoutMs)

    test('should measure fallback performance', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      
      const key = `test/fallback-performance/${Date.now()}-test.jpg`

      const startTime = Date.now()
      const result = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg',
        metadata: { testType: 'fallback-performance' }
      })
      const totalTime = Date.now() - startTime

      expect(result.success).toBe(true)
      expect(totalTime).toBeLessThan(TEST_CONFIG.performanceThresholds.fallbackTime * 2) // Allow extra time for fallback

      if (result.fallbackUsed) {
        console.log(`Fallback completed in ${totalTime}ms`)
      }
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Download Operations', () => {
    test('should download from correct storage based on flag', async () => {
      // Upload with R2 enabled
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      const key = `test/download-flag/${Date.now()}-test.jpg`
      uploadedKeys.push(key)

      const uploadResult = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg'
      })

      expect(uploadResult.success).toBe(true)

      // Download with same flag setting
      const downloadResult = await hybridStorage.download(key)

      expect(downloadResult.success).toBe(true)
      expect(downloadResult.data).toBeDefined()
      
      if (!downloadResult.fallbackUsed) {
        expect(downloadResult.storage).toBe('r2')
      }

      // Verify data integrity
      const downloadedView = new Uint8Array(downloadResult.data!)
      const originalView = new Uint8Array(testData)
      expect(downloadedView.length).toBe(originalView.length)
    }, TEST_CONFIG.timeoutMs)

    test('should handle download fallback scenarios', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      
      // Try to download non-existent file
      const nonExistentKey = `test/non-existent/${Date.now()}-fake.jpg`

      const result = await hybridStorage.download(nonExistentKey)

      // Should either fail gracefully or fallback
      if (result.success) {
        expect(result.fallbackUsed).toBe(true)
        expect(result.storage).toBe('firebase')
      } else {
        expect(result.error).toBeDefined()
      }
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Delete Operations', () => {
    test('should delete from correct storage based on flag', async () => {
      // Upload first
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      const key = `test/delete-flag/${Date.now()}-test.jpg`

      const uploadResult = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg'
      })

      expect(uploadResult.success).toBe(true)

      // Delete with same flag setting
      const deleteResult = await hybridStorage.delete(key)

      expect(deleteResult.success).toBe(true)
      if (!deleteResult.fallbackUsed) {
        expect(deleteResult.storage).toBe('r2')
      }

      // Verify deletion by trying to download
      const downloadResult = await hybridStorage.download(key)
      expect(downloadResult.success).toBe(false)
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Performance Monitoring Integration', () => {
    test('should track R2 operations in performance monitor', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      
      // Clear metrics
      r2PerformanceMonitor.clearMetrics()

      const key = `test/performance/${Date.now()}-test.jpg`
      uploadedKeys.push(key)

      // Perform operations
      await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg'
      })

      await hybridStorage.download(key)

      // Check performance metrics
      const summary = r2PerformanceMonitor.getPerformanceSummary(1)

      if (summary.totalOperations > 0) {
        expect(summary.totalOperations).toBeGreaterThanOrEqual(1)
        expect(summary.averageLatency).toBeGreaterThan(0)
      }
    }, TEST_CONFIG.timeoutMs)

    test('should not track Firebase operations in R2 monitor', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', false)
      
      // Clear metrics
      r2PerformanceMonitor.clearMetrics()

      const key = `test/firebase-no-track/${Date.now()}-test.jpg`

      // Perform Firebase operation
      await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg'
      })

      // R2 monitor should not track Firebase operations
      const summary = r2PerformanceMonitor.getPerformanceSummary(1)
      expect(summary.totalOperations).toBe(0)
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Error Handling and Recovery', () => {
    test('should handle partial failures gracefully', async () => {
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      
      const keys = [
        `test/partial-fail-1/${Date.now()}-test.jpg`,
        `test/partial-fail-2/${Date.now()}-test.jpg`,
        `test/partial-fail-3/${Date.now()}-test.jpg`
      ]

      const results = await Promise.all(
        keys.map(key => 
          hybridStorage.upload({
            key,
            body: testData,
            contentType: 'image/jpeg',
            metadata: { testType: 'partial-failure' }
          })
        )
      )

      uploadedKeys.push(...keys)

      // At least some operations should succeed
      const successCount = results.filter(r => r.success).length
      expect(successCount).toBeGreaterThan(0)

      // All operations should complete (not hang)
      expect(results.length).toBe(keys.length)
    }, TEST_CONFIG.timeoutMs)

    test('should maintain service availability during storage transitions', async () => {
      const key = `test/transition/${Date.now()}-test.jpg`
      uploadedKeys.push(key)

      // Upload with R2
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      const uploadResult = await hybridStorage.upload({
        key,
        body: testData,
        contentType: 'image/jpeg'
      })

      expect(uploadResult.success).toBe(true)

      // Switch to Firebase and try to download
      await featureFlags.setFlag('USE_R2_STORAGE', false)
      const downloadResult = await hybridStorage.download(key)

      // Should either succeed or fail gracefully
      if (downloadResult.success) {
        expect(downloadResult.data).toBeDefined()
      } else {
        expect(downloadResult.error).toBeDefined()
      }
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Configuration Validation', () => {
    test('should validate feature flag states', async () => {
      // Test flag enabled state
      await featureFlags.setFlag('USE_R2_STORAGE', true)
      const r2Enabled = await featureFlags.getFlag('USE_R2_STORAGE')
      expect(r2Enabled).toBe(true)

      // Test flag disabled state
      await featureFlags.setFlag('USE_R2_STORAGE', false)
      const r2Disabled = await featureFlags.getFlag('USE_R2_STORAGE')
      expect(r2Disabled).toBe(false)
    })

    test('should handle invalid flag values gracefully', async () => {
      // Test with undefined flag
      await featureFlags.setFlag('USE_R2_STORAGE', undefined as any)
      const undefinedFlag = await featureFlags.getFlag('USE_R2_STORAGE')
      
      // Should default to false for safety
      expect(undefinedFlag).toBe(false)
    })
  })
})
