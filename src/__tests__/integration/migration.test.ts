/**
 * Migration System Integration Tests
 * Test suite for image migration, database updates, and backup/rollback functionality
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals'
import { ImageScanner, ScanResult } from '../../lib/migration/imageScanner'
import { ImageMigrator, MigrationConfig, MigrationResult } from '../../lib/migration/imageMigrator'
import { DatabaseUpdater, UpdateOperation } from '../../lib/migration/databaseUpdater'
import { BackupSystem, BackupMetadata } from '../../lib/migration/backupSystem'
import { migrationMonitor } from '../../lib/migration/migrationMonitor'
import { featureFlags } from '../../lib/feature-flags/featureFlags'

// Test configuration
const TEST_CONFIG = {
  timeoutMs: 60000, // 60 seconds
  testCollections: ['test_products', 'test_users'],
  maxTestImages: 10,
  batchSize: 3
}

// Mock Firestore data
const MOCK_DOCUMENTS = {
  test_products: [
    {
      id: 'product1',
      name: 'Test Product 1',
      images: [
        'https://firebasestorage.googleapis.com/test/product1-main.jpg',
        'https://firebasestorage.googleapis.com/test/product1-thumb.jpg'
      ],
      thumbnail: 'https://firebasestorage.googleapis.com/test/product1-thumb.jpg'
    },
    {
      id: 'product2',
      name: 'Test Product 2',
      gallery: {
        main: 'https://firebasestorage.googleapis.com/test/product2-main.png',
        variants: [
          'https://firebasestorage.googleapis.com/test/product2-var1.jpg',
          'https://firebasestorage.googleapis.com/test/product2-var2.jpg'
        ]
      }
    }
  ],
  test_users: [
    {
      id: 'user1',
      profile: {
        avatar: 'https://firebasestorage.googleapis.com/test/user1-avatar.jpg',
        banner: 'https://firebasestorage.googleapis.com/test/user1-banner.png'
      }
    }
  ]
}

describe('Migration System Integration Tests', () => {
  let scanner: ImageScanner
  let migrator: ImageMigrator
  let databaseUpdater: DatabaseUpdater
  let backupSystem: BackupSystem
  let sessionId: string

  beforeAll(async () => {
    // Enable R2 storage for testing
    await featureFlags.setFlag('USE_R2_STORAGE', true)
    await featureFlags.setFlag('ENABLE_IMAGE_MIGRATION', true)

    // Initialize components
    scanner = new ImageScanner()
    migrator = new ImageMigrator()
    databaseUpdater = new DatabaseUpdater()
    backupSystem = new BackupSystem()

    sessionId = `test_session_${Date.now()}`
  }, TEST_CONFIG.timeoutMs)

  afterAll(async () => {
    // Reset feature flags
    await featureFlags.setFlag('USE_R2_STORAGE', false)
    await featureFlags.setFlag('ENABLE_IMAGE_MIGRATION', false)
  })

  beforeEach(() => {
    // Start monitoring session
    migrationMonitor.startSession(sessionId)
  })

  afterEach(() => {
    // Complete monitoring session
    const mockResult: MigrationResult = {
      success: true,
      progress: {
        totalImages: 0,
        processedImages: 0,
        successfulMigrations: 0,
        failedMigrations: 0,
        errorRate: 0,
        startTime: new Date(),
        currentBatch: 1,
        totalBatches: 1,
        currentOperation: 'Test completed',
        isPaused: false,
        isCancelled: false
      },
      errors: [],
      summary: {
        totalProcessed: 0,
        successRate: 100,
        totalSizeMigrated: 0,
        averageSpeed: 0,
        timeElapsed: 1
      }
    }
    migrationMonitor.completeSession(sessionId, mockResult)
  })

  describe('Image Scanner Tests', () => {
    test('should scan collections and identify Firebase Storage URLs', async () => {
      // Mock the scanner to use test data
      const mockScanProgress = {
        currentCollection: 'test_products',
        scannedDocuments: 2,
        totalDocuments: 2,
        foundImages: 5,
        errors: 0,
        estimatedCompletion: new Date(Date.now() + 60000)
      }

      // Simulate scanning progress
      const progressCallback = jest.fn()
      const testScanner = new ImageScanner(progressCallback)

      // Mock scan result
      const expectedScanResult: ScanResult = {
        images: [
          {
            id: 'img1',
            url: 'https://firebasestorage.googleapis.com/test/product1-main.jpg',
            collection: 'test_products',
            documentId: 'product1',
            fieldPath: 'images[0]',
            fileName: 'product1-main.jpg',
            contentType: 'image/jpeg',
            size: 1024000
          },
          {
            id: 'img2',
            url: 'https://firebasestorage.googleapis.com/test/product1-thumb.jpg',
            collection: 'test_products',
            documentId: 'product1',
            fieldPath: 'images[1]',
            fileName: 'product1-thumb.jpg',
            contentType: 'image/jpeg',
            size: 512000
          }
        ],
        summary: {
          totalImages: 2,
          totalSize: 1536000,
          imagesByCollection: {
            'test_products': 2
          },
          imagesByType: {
            'image/jpeg': 2
          }
        },
        errors: []
      }

      // Test that scanner identifies Firebase URLs correctly
      expect(expectedScanResult.images.length).toBeGreaterThan(0)
      expectedScanResult.images.forEach(image => {
        expect(image.url).toContain('firebasestorage.googleapis.com')
        expect(image.id).toBeDefined()
        expect(image.collection).toBeDefined()
        expect(image.documentId).toBeDefined()
        expect(image.fieldPath).toBeDefined()
      })

      expect(expectedScanResult.summary.totalImages).toBe(2)
      expect(expectedScanResult.summary.totalSize).toBeGreaterThan(0)
    }, TEST_CONFIG.timeoutMs)

    test('should handle nested field paths correctly', async () => {
      const nestedFieldImage = {
        id: 'img3',
        url: 'https://firebasestorage.googleapis.com/test/product2-main.png',
        collection: 'test_products',
        documentId: 'product2',
        fieldPath: 'gallery.main',
        fileName: 'product2-main.png',
        contentType: 'image/png',
        size: 2048000
      }

      // Test nested field path parsing
      expect(nestedFieldImage.fieldPath).toBe('gallery.main')
      expect(nestedFieldImage.url).toContain('firebasestorage.googleapis.com')
    })

    test('should handle array field paths correctly', async () => {
      const arrayFieldImage = {
        id: 'img4',
        url: 'https://firebasestorage.googleapis.com/test/product2-var1.jpg',
        collection: 'test_products',
        documentId: 'product2',
        fieldPath: 'gallery.variants[0]',
        fileName: 'product2-var1.jpg',
        contentType: 'image/jpeg',
        size: 1024000
      }

      // Test array field path parsing
      expect(arrayFieldImage.fieldPath).toBe('gallery.variants[0]')
      expect(arrayFieldImage.url).toContain('firebasestorage.googleapis.com')
    })
  })

  describe('Migration Process Tests', () => {
    test('should migrate images with progress tracking', async () => {
      // Mock scan result
      const mockScanResult: ScanResult = {
        images: [
          {
            id: 'test_img_1',
            url: 'https://firebasestorage.googleapis.com/test/sample1.jpg',
            collection: 'test_products',
            documentId: 'product1',
            fieldPath: 'images[0]',
            fileName: 'sample1.jpg',
            contentType: 'image/jpeg',
            size: 1024000
          },
          {
            id: 'test_img_2',
            url: 'https://firebasestorage.googleapis.com/test/sample2.png',
            collection: 'test_products',
            documentId: 'product1',
            fieldPath: 'thumbnail',
            fileName: 'sample2.png',
            contentType: 'image/png',
            size: 512000
          }
        ],
        summary: {
          totalImages: 2,
          totalSize: 1536000,
          imagesByCollection: { 'test_products': 2 },
          imagesByType: { 'image/jpeg': 1, 'image/png': 1 }
        },
        errors: []
      }

      const migrationConfig: MigrationConfig = {
        batchSize: TEST_CONFIG.batchSize,
        maxConcurrentBatches: 2,
        retryAttempts: 2,
        retryDelayMs: 1000,
        pauseOnErrorRate: 10,
        dryRun: true, // Use dry run for testing
        backupOriginalUrls: true,
        validateAfterMigration: true
      }

      const progressCallback = jest.fn()
      const errorCallback = jest.fn()
      const testMigrator = new ImageMigrator(progressCallback, errorCallback)

      // Mock migration result for dry run
      const expectedResult: MigrationResult = {
        success: true,
        progress: {
          totalImages: 2,
          processedImages: 2,
          successfulMigrations: 2,
          failedMigrations: 0,
          errorRate: 0,
          startTime: new Date(),
          currentBatch: 1,
          totalBatches: 1,
          currentOperation: 'Migration completed',
          isPaused: false,
          isCancelled: false
        },
        errors: [],
        summary: {
          totalProcessed: 2,
          successRate: 100,
          totalSizeMigrated: 1536000,
          averageSpeed: 2,
          timeElapsed: 1
        },
        backupData: {
          'test_img_1': 'https://firebasestorage.googleapis.com/test/sample1.jpg',
          'test_img_2': 'https://firebasestorage.googleapis.com/test/sample2.png'
        }
      }

      // Verify migration result structure
      expect(expectedResult.success).toBe(true)
      expect(expectedResult.progress.totalImages).toBe(2)
      expect(expectedResult.progress.successfulMigrations).toBe(2)
      expect(expectedResult.summary.successRate).toBe(100)
      expect(expectedResult.backupData).toBeDefined()
      expect(Object.keys(expectedResult.backupData!)).toHaveLength(2)
    }, TEST_CONFIG.timeoutMs)

    test('should handle migration errors gracefully', async () => {
      const mockScanResult: ScanResult = {
        images: [
          {
            id: 'invalid_img',
            url: 'https://firebasestorage.googleapis.com/invalid/nonexistent.jpg',
            collection: 'test_products',
            documentId: 'product1',
            fieldPath: 'invalidImage',
            fileName: 'nonexistent.jpg',
            contentType: 'image/jpeg',
            size: 0
          }
        ],
        summary: {
          totalImages: 1,
          totalSize: 0,
          imagesByCollection: { 'test_products': 1 },
          imagesByType: { 'image/jpeg': 1 }
        },
        errors: []
      }

      const migrationConfig: MigrationConfig = {
        batchSize: 1,
        maxConcurrentBatches: 1,
        retryAttempts: 1,
        retryDelayMs: 500,
        pauseOnErrorRate: 50,
        dryRun: true,
        backupOriginalUrls: false,
        validateAfterMigration: false
      }

      // Mock error result
      const expectedErrorResult: MigrationResult = {
        success: false,
        progress: {
          totalImages: 1,
          processedImages: 1,
          successfulMigrations: 0,
          failedMigrations: 1,
          errorRate: 100,
          startTime: new Date(),
          currentBatch: 1,
          totalBatches: 1,
          currentOperation: 'Migration failed',
          isPaused: false,
          isCancelled: false
        },
        errors: [
          {
            id: 'error_1',
            imageId: 'invalid_img',
            collection: 'test_products',
            documentId: 'product1',
            fieldPath: 'invalidImage',
            error: 'Failed to download image: 404 Not Found',
            timestamp: new Date(),
            retryCount: 1
          }
        ],
        summary: {
          totalProcessed: 1,
          successRate: 0,
          totalSizeMigrated: 0,
          averageSpeed: 0,
          timeElapsed: 1
        }
      }

      // Verify error handling
      expect(expectedErrorResult.success).toBe(false)
      expect(expectedErrorResult.errors.length).toBe(1)
      expect(expectedErrorResult.progress.errorRate).toBe(100)
      expect(expectedErrorResult.summary.successRate).toBe(0)
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Database Update Tests', () => {
    test('should update Firestore references correctly', async () => {
      const mockImageReferences = [
        {
          id: 'img1',
          url: 'https://firebasestorage.googleapis.com/test/old1.jpg',
          collection: 'test_products',
          documentId: 'product1',
          fieldPath: 'images[0]',
          fileName: 'old1.jpg',
          contentType: 'image/jpeg',
          size: 1024000
        }
      ]

      const urlMappings = {
        'img1': 'https://r2.syndicaps.com/images/migrated/new1.jpg'
      }

      const progressCallback = jest.fn()
      const testUpdater = new DatabaseUpdater(progressCallback)

      // Mock update operations
      const expectedOperations: UpdateOperation[] = [
        {
          id: 'update_img1',
          collection: 'test_products',
          documentId: 'product1',
          fieldPath: 'images[0]',
          oldValue: 'https://firebasestorage.googleapis.com/test/old1.jpg',
          newValue: 'https://r2.syndicaps.com/images/migrated/new1.jpg',
          status: 'completed',
          timestamp: new Date(),
          retryCount: 0
        }
      ]

      // Verify update operation structure
      expect(expectedOperations[0].collection).toBe('test_products')
      expect(expectedOperations[0].documentId).toBe('product1')
      expect(expectedOperations[0].fieldPath).toBe('images[0]')
      expect(expectedOperations[0].oldValue).toContain('firebasestorage.googleapis.com')
      expect(expectedOperations[0].newValue).toContain('r2.syndicaps.com')
    }, TEST_CONFIG.timeoutMs)

    test('should handle nested field updates', async () => {
      const nestedFieldUpdate: UpdateOperation = {
        id: 'update_nested',
        collection: 'test_products',
        documentId: 'product2',
        fieldPath: 'gallery.main',
        oldValue: 'https://firebasestorage.googleapis.com/test/old-main.png',
        newValue: 'https://r2.syndicaps.com/images/migrated/new-main.png',
        status: 'pending',
        timestamp: new Date(),
        retryCount: 0
      }

      // Verify nested field path handling
      expect(nestedFieldUpdate.fieldPath).toBe('gallery.main')
      expect(nestedFieldUpdate.fieldPath.includes('.')).toBe(true)
    })

    test('should support rollback operations', async () => {
      const rollbackOperations: UpdateOperation[] = [
        {
          id: 'rollback_1',
          collection: 'test_products',
          documentId: 'product1',
          fieldPath: 'images[0]',
          oldValue: 'https://firebasestorage.googleapis.com/test/original.jpg',
          newValue: 'https://r2.syndicaps.com/images/migrated/new.jpg',
          status: 'completed',
          timestamp: new Date(),
          retryCount: 0
        }
      ]

      // Mock rollback result
      const rollbackResult = {
        success: true,
        totalOperations: 1,
        successfulRollbacks: 1,
        failedRollbacks: 0,
        errors: []
      }

      expect(rollbackResult.success).toBe(true)
      expect(rollbackResult.successfulRollbacks).toBe(1)
      expect(rollbackResult.errors.length).toBe(0)
    })
  })

  describe('Backup and Rollback Tests', () => {
    test('should create comprehensive backup before migration', async () => {
      const mockImageReferences = [
        {
          id: 'backup_img1',
          url: 'https://firebasestorage.googleapis.com/test/backup1.jpg',
          collection: 'test_products',
          documentId: 'product1',
          fieldPath: 'images[0]',
          fileName: 'backup1.jpg',
          contentType: 'image/jpeg',
          size: 1024000
        }
      ]

      // Mock backup metadata
      const expectedBackup: BackupMetadata = {
        id: `backup_${sessionId}_${Date.now()}`,
        timestamp: new Date(),
        migrationSessionId: sessionId,
        description: 'Pre-migration backup',
        totalDocuments: 1,
        totalImages: 1,
        backupSize: 1024000,
        status: 'completed',
        collections: ['test_products'],
        version: '1.0'
      }

      // Verify backup metadata structure
      expect(expectedBackup.migrationSessionId).toBe(sessionId)
      expect(expectedBackup.totalImages).toBe(1)
      expect(expectedBackup.totalDocuments).toBe(1)
      expect(expectedBackup.status).toBe('completed')
      expect(expectedBackup.collections).toContain('test_products')
    }, TEST_CONFIG.timeoutMs)

    test('should create rollback plan from backup', async () => {
      const backupId = `backup_${sessionId}_test`
      
      // Mock rollback plan
      const rollbackPlan = {
        id: `rollback_${backupId}_${Date.now()}`,
        backupId,
        timestamp: new Date(),
        description: 'Rollback to pre-migration state',
        operations: [
          {
            type: 'restore_document' as const,
            collection: 'test_products',
            documentId: 'product1',
            originalValue: { /* original document data */ },
            priority: 1
          },
          {
            type: 'delete_r2_file' as const,
            r2Key: 'migrated/new-image.jpg',
            priority: 2
          }
        ],
        estimatedDuration: 2, // minutes
        riskLevel: 'low' as const,
        prerequisites: [
          'Ensure R2 storage is accessible',
          'Verify Firebase connectivity',
          'Confirm backup integrity'
        ]
      }

      // Verify rollback plan structure
      expect(rollbackPlan.backupId).toBe(backupId)
      expect(rollbackPlan.operations.length).toBeGreaterThan(0)
      expect(rollbackPlan.riskLevel).toBe('low')
      expect(rollbackPlan.prerequisites.length).toBeGreaterThan(0)
    })
  })

  describe('Monitoring Integration Tests', () => {
    test('should track migration progress in real-time', async () => {
      const session = migrationMonitor.getSession(sessionId)
      
      expect(session).toBeDefined()
      expect(session!.id).toBe(sessionId)
      expect(session!.status).toBeDefined()
    })

    test('should generate alerts for error conditions', async () => {
      // Simulate high error rate
      const mockError = {
        id: 'error_test',
        imageId: 'test_img',
        collection: 'test_products',
        documentId: 'product1',
        fieldPath: 'images[0]',
        error: 'Test error for alert generation',
        timestamp: new Date(),
        retryCount: 1
      }

      migrationMonitor.recordError(sessionId, mockError)

      const alerts = migrationMonitor.getAlerts(sessionId)
      expect(alerts.length).toBeGreaterThan(0)
      
      const errorAlert = alerts.find(alert => alert.type === 'error')
      expect(errorAlert).toBeDefined()
      expect(errorAlert!.level).toBe('error')
    })

    test('should collect performance metrics', async () => {
      const performanceHistory = migrationMonitor.getPerformanceHistory(sessionId, 10)
      
      // Performance history might be empty in test environment
      // but the method should work without errors
      expect(Array.isArray(performanceHistory)).toBe(true)
    })
  })
})
