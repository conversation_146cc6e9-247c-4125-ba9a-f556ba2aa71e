/**
 * Phase 1 Integration Tests
 * End-to-end testing of Cloudflare hybrid deployment Phase 1 components
 */

import { 
  hybridPerformanceMonitor,
  PerformanceMetric 
} from '../../lib/monitoring/hybridPerformanceMonitor'
import {
  shouldUseFeature,
  enableFeature,
  disableFeature,
  setFeaturePercentage
} from '../../lib/config/featureFlags'
import { CloudflareCacheManager } from '../../lib/cloudflare/cacheManager'
import { CloudflareCDNConfig } from '../../lib/cloudflare/cdnConfig'

// Mock fetch for API calls
global.fetch = jest.fn()

describe('Phase 1 Integration Tests', () => {
  let cacheManager: CloudflareCacheManager
  let cdnConfig: CloudflareCDNConfig
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>

  beforeEach(() => {
    cacheManager = new CloudflareCacheManager()
    cdnConfig = new CloudflareCDNConfig()
    jest.clearAllMocks()
    
    // Reset performance monitor
    hybridPerformanceMonitor.stopMonitoring()
  })

  afterEach(() => {
    hybridPerformanceMonitor.stopMonitoring()
  })

  describe('Feature Flag Integration', () => {
    it('should control CDN functionality based on feature flags', async () => {
      // Test CDN feature flag
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(true)
      
      // Disable CDN feature
      disableFeature('USE_CLOUDFLARE_CDN')
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(false)
      
      // Re-enable CDN feature
      enableFeature('USE_CLOUDFLARE_CDN')
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(true)
    })

    it('should control analytics based on feature flags', () => {
      // Test analytics feature flag
      expect(shouldUseFeature('USE_CLOUDFLARE_ANALYTICS')).toBe(true)
      
      // Disable analytics
      disableFeature('USE_CLOUDFLARE_ANALYTICS')
      expect(shouldUseFeature('USE_CLOUDFLARE_ANALYTICS')).toBe(false)
    })

    it('should support gradual rollout of features', () => {
      // Set 50% rollout for Workers
      setFeaturePercentage('USE_CLOUDFLARE_WORKERS', 50)
      
      // Mock Math.random for predictable testing
      const originalRandom = Math.random
      
      // Test enabled case
      Math.random = jest.fn().mockReturnValue(0.3)
      expect(shouldUseFeature('USE_CLOUDFLARE_WORKERS')).toBe(true)
      
      // Test disabled case
      Math.random = jest.fn().mockReturnValue(0.7)
      expect(shouldUseFeature('USE_CLOUDFLARE_WORKERS')).toBe(false)
      
      // Restore
      Math.random = originalRandom
    })
  })

  describe('Performance Monitoring Integration', () => {
    it('should collect and analyze performance metrics', async () => {
      // Start monitoring
      await hybridPerformanceMonitor.startMonitoring(0.1) // 0.1 minute for testing
      
      // Add sample metrics
      const metrics: PerformanceMetric[] = [
        {
          timestamp: new Date().toISOString(),
          metricType: 'response_time',
          value: 150,
          unit: 'ms',
          source: 'cloudflare',
          metadata: { endpoint: '/api/products' }
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'cache_hit_ratio',
          value: 85,
          unit: 'percentage',
          source: 'cloudflare'
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'error_rate',
          value: 0.5,
          unit: 'percentage',
          source: 'firebase'
        }
      ]

      metrics.forEach(metric => hybridPerformanceMonitor.addMetric(metric))
      
      // Generate report
      const report = hybridPerformanceMonitor.generateReport(1)
      
      expect(report.metrics.avgResponseTime).toBe(150)
      expect(report.metrics.cacheHitRatio).toBe(85)
      expect(report.metrics.errorRate).toBe(0.5)
      expect(report.recommendations).toBeDefined()
    })

    it('should trigger alerts based on performance thresholds', async () => {
      // Add metric that exceeds threshold
      const highErrorRate: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        metricType: 'error_rate',
        value: 10, // Exceeds 5% threshold
        unit: 'percentage',
        source: 'cloudflare'
      }

      hybridPerformanceMonitor.addMetric(highErrorRate)
      
      // Check alerts
      await hybridPerformanceMonitor.checkAlerts()
      
      const alerts = hybridPerformanceMonitor.getAlerts()
      expect(alerts).toHaveLength(1)
      expect(alerts[0].severity).toBe('critical')
      expect(alerts[0].ruleId).toBe('high_error_rate')
    })
  })

  describe('Cache Management Integration', () => {
    it('should manage cache with performance monitoring', async () => {
      // Mock successful cache analytics response
      const mockAnalyticsResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: {
            totals: {
              requests: { all: 1000 },
              bandwidth: { all: 5000000 },
              cache_hit_ratio: 85
            }
          }
        })
      }

      mockFetch.mockResolvedValueOnce(mockAnalyticsResponse as any)
      
      // Get cache analytics
      const analytics = await cacheManager.getCacheAnalytics()
      
      expect(analytics.totalRequests).toBe(1000)
      expect(analytics.hitRatio).toBe(85)
      
      // Performance monitor should track cache metrics
      const cacheMetric: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        metricType: 'cache_hit_ratio',
        value: analytics.hitRatio,
        unit: 'percentage',
        source: 'cloudflare'
      }

      hybridPerformanceMonitor.addMetric(cacheMetric)
      
      const report = hybridPerformanceMonitor.generateReport(1)
      expect(report.metrics.cacheHitRatio).toBe(85)
    })

    it('should purge cache and monitor performance impact', async () => {
      // Mock successful purge response
      const mockPurgeResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: { id: 'purge-123' }
        })
      }

      mockFetch.mockResolvedValueOnce(mockPurgeResponse as any)
      
      // Purge cache
      const purgeResult = await cacheManager.purgeCache(['https://example.com/api/products'])
      
      expect(purgeResult.success).toBe(true)
      expect(purgeResult.purgeId).toBe('purge-123')
      
      // Monitor performance after purge (simulate cache miss)
      const postPurgeMetric: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: 500, // Higher response time due to cache miss
        unit: 'ms',
        source: 'firebase',
        metadata: { cacheStatus: 'MISS', postPurge: true }
      }

      hybridPerformanceMonitor.addMetric(postPurgeMetric)
      
      const metrics = hybridPerformanceMonitor.getMetrics()
      const postPurgeMetrics = metrics.filter(m => m.metadata?.postPurge)
      expect(postPurgeMetrics).toHaveLength(1)
    })
  })

  describe('CDN Configuration Integration', () => {
    it('should configure CDN with feature flag control', async () => {
      // Ensure CDN feature is enabled
      enableFeature('USE_CLOUDFLARE_CDN')
      
      if (shouldUseFeature('USE_CLOUDFLARE_CDN')) {
        // Mock successful cache rule creation
        const mockRuleResponse = {
          ok: true,
          json: async () => ({
            success: true,
            result: {
              id: 'rule-123',
              expression: '(http.request.uri.path matches "^/api/")',
              action: 'cache'
            }
          })
        }

        mockFetch.mockResolvedValueOnce(mockRuleResponse as any)
        
        // Create cache rule
        const ruleResult = await cdnConfig.createCacheRule({
          name: 'API Cache Rule',
          expression: '(http.request.uri.path matches "^/api/")',
          ttl: 300,
          cacheLevel: 'standard'
        })

        expect(ruleResult.success).toBe(true)
        expect(ruleResult.ruleId).toBe('rule-123')
      }
    })

    it('should monitor CDN performance after configuration', async () => {
      // Mock CDN analytics response
      const mockAnalyticsResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: {
            totals: {
              requests: { all: 5000 },
              bandwidth: { all: 10000000 },
              threats: { all: 25 }
            },
            timeseries: [
              {
                since: '2023-01-01T00:00:00Z',
                until: '2023-01-01T01:00:00Z',
                requests: { all: 500 },
                bandwidth: { all: 1000000 }
              }
            ]
          }
        })
      }

      mockFetch.mockResolvedValueOnce(mockAnalyticsResponse as any)
      
      // Get CDN analytics
      const analyticsResult = await cdnConfig.getCDNAnalytics({
        since: '2023-01-01T00:00:00Z',
        until: '2023-01-02T00:00:00Z'
      })

      expect(analyticsResult.success).toBe(true)
      expect(analyticsResult.data?.totals.requests.all).toBe(5000)
      
      // Add CDN metrics to performance monitor
      const cdnMetrics: PerformanceMetric[] = [
        {
          timestamp: new Date().toISOString(),
          metricType: 'bandwidth',
          value: analyticsResult.data?.totals.bandwidth.all || 0,
          unit: 'bytes',
          source: 'cloudflare'
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'response_time',
          value: 75, // Fast CDN response
          unit: 'ms',
          source: 'cloudflare'
        }
      ]

      cdnMetrics.forEach(metric => hybridPerformanceMonitor.addMetric(metric))
      
      const report = hybridPerformanceMonitor.generateReport(1)
      expect(report.metrics.bandwidth).toBeGreaterThan(0)
      expect(report.metrics.avgResponseTime).toBe(75)
    })
  })

  describe('End-to-End Workflow', () => {
    it('should complete full Phase 1 workflow', async () => {
      // 1. Enable all Phase 1 features
      enableFeature('USE_CLOUDFLARE_CDN')
      enableFeature('USE_CLOUDFLARE_ANALYTICS')
      enableFeature('USE_HYBRID_CACHING')
      
      // 2. Start performance monitoring
      await hybridPerformanceMonitor.startMonitoring(0.1)
      
      // 3. Mock CDN configuration
      const mockConfigResponse = {
        ok: true,
        json: async () => ({ success: true, result: { id: 'config-123' } })
      }
      mockFetch.mockResolvedValue(mockConfigResponse as any)
      
      // 4. Configure CDN settings
      const perfResult = await cdnConfig.configurePerformanceSettings({
        minification: { css: true, js: true, html: true },
        compression: { brotli: true, gzip: true }
      })
      
      expect(perfResult.success).toBe(true)
      
      // 5. Add comprehensive metrics
      const workflowMetrics: PerformanceMetric[] = [
        {
          timestamp: new Date().toISOString(),
          metricType: 'response_time',
          value: 120,
          unit: 'ms',
          source: 'cloudflare'
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'cache_hit_ratio',
          value: 92,
          unit: 'percentage',
          source: 'cloudflare'
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'core_web_vitals',
          value: 1800,
          unit: 'ms',
          source: 'client',
          metadata: { metric: 'lcp' }
        }
      ]

      workflowMetrics.forEach(metric => hybridPerformanceMonitor.addMetric(metric))
      
      // 6. Generate final report
      const finalReport = hybridPerformanceMonitor.generateReport(1)
      
      expect(finalReport.metrics.avgResponseTime).toBe(120)
      expect(finalReport.metrics.cacheHitRatio).toBe(92)
      expect(finalReport.metrics.coreWebVitals.avgLCP).toBe(1800)
      expect(finalReport.recommendations).toContain('Performance metrics are within acceptable ranges')
      
      // 7. Verify all features are working
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(true)
      expect(shouldUseFeature('USE_CLOUDFLARE_ANALYTICS')).toBe(true)
      expect(shouldUseFeature('USE_HYBRID_CACHING')).toBe(true)
    })
  })
})
