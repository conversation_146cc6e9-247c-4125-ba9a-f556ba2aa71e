/**
 * R2 Storage Integration Tests
 * Comprehensive test suite for R2 storage operations, error scenarios, and performance benchmarks
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals'
import { R2StorageService } from '../../lib/cloudflare/r2StorageService'
import { r2PerformanceMonitor } from '../../lib/cloudflare/r2PerformanceMonitor'
import { featureFlags } from '../../lib/feature-flags/featureFlags'

// Test configuration
const TEST_CONFIG = {
  testBucket: 'test-images',
  backupBucket: 'test-backups',
  maxFileSize: 10 * 1024 * 1024, // 10MB
  timeoutMs: 30000, // 30 seconds
  performanceThresholds: {
    uploadTime: 5000, // 5 seconds
    downloadTime: 3000, // 3 seconds
    deleteTime: 1000 // 1 second
  }
}

// Test data
const TEST_IMAGES = {
  small: {
    name: 'test-small.jpg',
    size: 1024, // 1KB
    contentType: 'image/jpeg'
  },
  medium: {
    name: 'test-medium.png',
    size: 100 * 1024, // 100KB
    contentType: 'image/png'
  },
  large: {
    name: 'test-large.webp',
    size: 2 * 1024 * 1024, // 2MB
    contentType: 'image/webp'
  }
}

describe('R2 Storage Integration Tests', () => {
  let r2Service: R2StorageService
  let testFiles: Map<string, ArrayBuffer> = new Map()
  let uploadedKeys: string[] = []

  beforeAll(async () => {
    // Initialize R2 service
    r2Service = new R2StorageService()

    // Enable R2 storage feature flag for testing
    await featureFlags.setFlag('USE_R2_STORAGE', true)

    // Generate test image data
    Object.entries(TEST_IMAGES).forEach(([key, config]) => {
      const buffer = new ArrayBuffer(config.size)
      const view = new Uint8Array(buffer)
      
      // Fill with test pattern
      for (let i = 0; i < view.length; i++) {
        view[i] = i % 256
      }
      
      testFiles.set(key, buffer)
    })

    // Clear performance monitor
    r2PerformanceMonitor.clearMetrics()
  }, TEST_CONFIG.timeoutMs)

  afterAll(async () => {
    // Clean up uploaded test files
    for (const key of uploadedKeys) {
      try {
        await r2Service.delete({
          bucketType: 'images',
          key
        })
      } catch (error) {
        console.warn(`Failed to cleanup test file ${key}:`, error)
      }
    }

    // Reset feature flags
    await featureFlags.setFlag('USE_R2_STORAGE', false)
  }, TEST_CONFIG.timeoutMs)

  beforeEach(() => {
    // Reset performance monitor before each test
    r2PerformanceMonitor.clearMetrics()
  })

  afterEach(() => {
    // Log performance metrics after each test
    const summary = r2PerformanceMonitor.getPerformanceSummary(1)
    console.log('Test performance summary:', summary)
  })

  describe('Basic R2 Operations', () => {
    test('should upload small image successfully', async () => {
      const testData = testFiles.get('small')!
      const config = TEST_IMAGES.small
      const key = `test/small/${Date.now()}-${config.name}`

      const startTime = Date.now()
      const result = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType,
        metadata: {
          testType: 'small-upload',
          originalSize: config.size.toString()
        }
      })

      const uploadTime = Date.now() - startTime
      uploadedKeys.push(key)

      expect(result.success).toBe(true)
      expect(result.url).toContain(key)
      expect(result.size).toBe(config.size)
      expect(uploadTime).toBeLessThan(TEST_CONFIG.performanceThresholds.uploadTime)
    }, TEST_CONFIG.timeoutMs)

    test('should upload medium image successfully', async () => {
      const testData = testFiles.get('medium')!
      const config = TEST_IMAGES.medium
      const key = `test/medium/${Date.now()}-${config.name}`

      const result = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType,
        metadata: {
          testType: 'medium-upload',
          originalSize: config.size.toString()
        }
      })

      uploadedKeys.push(key)

      expect(result.success).toBe(true)
      expect(result.url).toContain(key)
      expect(result.size).toBe(config.size)
    }, TEST_CONFIG.timeoutMs)

    test('should upload large image successfully', async () => {
      const testData = testFiles.get('large')!
      const config = TEST_IMAGES.large
      const key = `test/large/${Date.now()}-${config.name}`

      const result = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType,
        metadata: {
          testType: 'large-upload',
          originalSize: config.size.toString()
        }
      })

      uploadedKeys.push(key)

      expect(result.success).toBe(true)
      expect(result.url).toContain(key)
      expect(result.size).toBe(config.size)
    }, TEST_CONFIG.timeoutMs)

    test('should download uploaded image successfully', async () => {
      // First upload a test image
      const testData = testFiles.get('small')!
      const config = TEST_IMAGES.small
      const key = `test/download/${Date.now()}-${config.name}`

      const uploadResult = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType
      })

      uploadedKeys.push(key)
      expect(uploadResult.success).toBe(true)

      // Now download it
      const startTime = Date.now()
      const downloadResult = await r2Service.download({
        bucketType: 'images',
        key
      })

      const downloadTime = Date.now() - startTime

      expect(downloadResult.success).toBe(true)
      expect(downloadResult.data).toBeDefined()
      expect(downloadResult.contentType).toBe(config.contentType)
      expect(downloadResult.size).toBe(config.size)
      expect(downloadTime).toBeLessThan(TEST_CONFIG.performanceThresholds.downloadTime)

      // Verify data integrity
      const downloadedBuffer = downloadResult.data as ArrayBuffer
      const originalView = new Uint8Array(testData)
      const downloadedView = new Uint8Array(downloadedBuffer)
      
      expect(downloadedView.length).toBe(originalView.length)
      for (let i = 0; i < Math.min(100, originalView.length); i++) {
        expect(downloadedView[i]).toBe(originalView[i])
      }
    }, TEST_CONFIG.timeoutMs)

    test('should get object metadata successfully', async () => {
      // Upload a test image with metadata
      const testData = testFiles.get('medium')!
      const config = TEST_IMAGES.medium
      const key = `test/metadata/${Date.now()}-${config.name}`
      const testMetadata = {
        testType: 'metadata-test',
        uploadTime: new Date().toISOString(),
        originalSize: config.size.toString()
      }

      const uploadResult = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType,
        metadata: testMetadata
      })

      uploadedKeys.push(key)
      expect(uploadResult.success).toBe(true)

      // Get metadata
      const metadataResult = await r2Service.getMetadata({
        bucketType: 'images',
        key
      })

      expect(metadataResult.success).toBe(true)
      expect(metadataResult.metadata).toBeDefined()
      expect(metadataResult.metadata!.contentType).toBe(config.contentType)
      expect(metadataResult.metadata!.size).toBe(config.size)
      expect(metadataResult.metadata!.customMetadata).toEqual(testMetadata)
    }, TEST_CONFIG.timeoutMs)

    test('should delete uploaded image successfully', async () => {
      // Upload a test image
      const testData = testFiles.get('small')!
      const config = TEST_IMAGES.small
      const key = `test/delete/${Date.now()}-${config.name}`

      const uploadResult = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType
      })

      expect(uploadResult.success).toBe(true)

      // Delete it
      const startTime = Date.now()
      const deleteResult = await r2Service.delete({
        bucketType: 'images',
        key
      })

      const deleteTime = Date.now() - startTime

      expect(deleteResult.success).toBe(true)
      expect(deleteTime).toBeLessThan(TEST_CONFIG.performanceThresholds.deleteTime)

      // Verify it's deleted by trying to download
      const downloadResult = await r2Service.download({
        bucketType: 'images',
        key
      })

      expect(downloadResult.success).toBe(false)
      expect(downloadResult.error).toContain('not found')
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Error Scenarios', () => {
    test('should handle upload to non-existent bucket', async () => {
      const testData = testFiles.get('small')!
      const config = TEST_IMAGES.small

      const result = await r2Service.upload({
        bucketType: 'non-existent' as any,
        key: `test/error/${Date.now()}-${config.name}`,
        body: testData,
        contentType: config.contentType
      })

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    }, TEST_CONFIG.timeoutMs)

    test('should handle download of non-existent file', async () => {
      const result = await r2Service.download({
        bucketType: 'images',
        key: `test/non-existent/${Date.now()}-fake.jpg`
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('not found')
    }, TEST_CONFIG.timeoutMs)

    test('should handle delete of non-existent file', async () => {
      const result = await r2Service.delete({
        bucketType: 'images',
        key: `test/non-existent/${Date.now()}-fake.jpg`
      })

      // Delete should succeed even if file doesn't exist (idempotent)
      expect(result.success).toBe(true)
    }, TEST_CONFIG.timeoutMs)

    test('should handle oversized file upload', async () => {
      // Create a buffer larger than the max size
      const oversizedBuffer = new ArrayBuffer(TEST_CONFIG.maxFileSize + 1024)
      
      const result = await r2Service.upload({
        bucketType: 'images',
        key: `test/oversized/${Date.now()}-huge.jpg`,
        body: oversizedBuffer,
        contentType: 'image/jpeg'
      })

      // This might succeed or fail depending on R2 limits
      // We're testing that it handles the scenario gracefully
      if (!result.success) {
        expect(result.error).toBeDefined()
      }
    }, TEST_CONFIG.timeoutMs)

    test('should handle invalid content type', async () => {
      const testData = testFiles.get('small')!

      const result = await r2Service.upload({
        bucketType: 'images',
        key: `test/invalid-type/${Date.now()}-test.jpg`,
        body: testData,
        contentType: 'invalid/content-type'
      })

      // Should either succeed (R2 accepts it) or fail gracefully
      if (!result.success) {
        expect(result.error).toBeDefined()
      }
    }, TEST_CONFIG.timeoutMs)

    test('should handle empty file upload', async () => {
      const emptyBuffer = new ArrayBuffer(0)

      const result = await r2Service.upload({
        bucketType: 'images',
        key: `test/empty/${Date.now()}-empty.jpg`,
        body: emptyBuffer,
        contentType: 'image/jpeg'
      })

      // Should handle empty files gracefully
      if (result.success) {
        uploadedKeys.push(`test/empty/${Date.now()}-empty.jpg`)
        expect(result.size).toBe(0)
      } else {
        expect(result.error).toBeDefined()
      }
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Performance Benchmarks', () => {
    test('should meet upload performance thresholds', async () => {
      const testData = testFiles.get('medium')!
      const config = TEST_IMAGES.medium
      const uploadTimes: number[] = []

      // Perform multiple uploads to get average performance
      for (let i = 0; i < 3; i++) {
        const key = `test/performance/upload-${i}-${Date.now()}-${config.name}`
        
        const startTime = Date.now()
        const result = await r2Service.upload({
          bucketType: 'images',
          key,
          body: testData,
          contentType: config.contentType
        })

        const uploadTime = Date.now() - startTime
        uploadTimes.push(uploadTime)
        uploadedKeys.push(key)

        expect(result.success).toBe(true)
      }

      const averageUploadTime = uploadTimes.reduce((a, b) => a + b, 0) / uploadTimes.length
      console.log(`Average upload time: ${averageUploadTime}ms`)
      
      expect(averageUploadTime).toBeLessThan(TEST_CONFIG.performanceThresholds.uploadTime)
    }, TEST_CONFIG.timeoutMs * 3)

    test('should meet download performance thresholds', async () => {
      // First upload a test file
      const testData = testFiles.get('medium')!
      const config = TEST_IMAGES.medium
      const key = `test/performance/download-${Date.now()}-${config.name}`

      const uploadResult = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType
      })

      uploadedKeys.push(key)
      expect(uploadResult.success).toBe(true)

      // Perform multiple downloads
      const downloadTimes: number[] = []

      for (let i = 0; i < 3; i++) {
        const startTime = Date.now()
        const result = await r2Service.download({
          bucketType: 'images',
          key
        })

        const downloadTime = Date.now() - startTime
        downloadTimes.push(downloadTime)

        expect(result.success).toBe(true)
      }

      const averageDownloadTime = downloadTimes.reduce((a, b) => a + b, 0) / downloadTimes.length
      console.log(`Average download time: ${averageDownloadTime}ms`)
      
      expect(averageDownloadTime).toBeLessThan(TEST_CONFIG.performanceThresholds.downloadTime)
    }, TEST_CONFIG.timeoutMs * 3)

    test('should track performance metrics correctly', async () => {
      // Clear metrics before test
      r2PerformanceMonitor.clearMetrics()

      // Perform some operations
      const testData = testFiles.get('small')!
      const config = TEST_IMAGES.small
      const key = `test/metrics/${Date.now()}-${config.name}`

      await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType
      })

      uploadedKeys.push(key)

      await r2Service.download({
        bucketType: 'images',
        key
      })

      await r2Service.getMetadata({
        bucketType: 'images',
        key
      })

      // Check performance metrics
      const summary = r2PerformanceMonitor.getPerformanceSummary(1)

      expect(summary.totalOperations).toBeGreaterThanOrEqual(3)
      expect(summary.successfulOperations).toBeGreaterThanOrEqual(3)
      expect(summary.averageLatency).toBeGreaterThan(0)
      expect(summary.throughputMBps).toBeGreaterThanOrEqual(0)
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Concurrent Operations', () => {
    test('should handle concurrent uploads', async () => {
      const testData = testFiles.get('small')!
      const config = TEST_IMAGES.small
      const concurrentUploads = 5

      const uploadPromises = Array.from({ length: concurrentUploads }, (_, i) => {
        const key = `test/concurrent/upload-${i}-${Date.now()}-${config.name}`
        uploadedKeys.push(key)
        
        return r2Service.upload({
          bucketType: 'images',
          key,
          body: testData,
          contentType: config.contentType,
          metadata: {
            testType: 'concurrent-upload',
            uploadIndex: i.toString()
          }
        })
      })

      const results = await Promise.all(uploadPromises)

      results.forEach((result, index) => {
        expect(result.success).toBe(true)
        expect(result.size).toBe(config.size)
      })

      // Check that all uploads were tracked
      const summary = r2PerformanceMonitor.getPerformanceSummary(1)
      expect(summary.totalOperations).toBeGreaterThanOrEqual(concurrentUploads)
    }, TEST_CONFIG.timeoutMs)

    test('should handle concurrent downloads', async () => {
      // First upload a test file
      const testData = testFiles.get('medium')!
      const config = TEST_IMAGES.medium
      const key = `test/concurrent/download-${Date.now()}-${config.name}`

      const uploadResult = await r2Service.upload({
        bucketType: 'images',
        key,
        body: testData,
        contentType: config.contentType
      })

      uploadedKeys.push(key)
      expect(uploadResult.success).toBe(true)

      // Perform concurrent downloads
      const concurrentDownloads = 3
      const downloadPromises = Array.from({ length: concurrentDownloads }, () => 
        r2Service.download({
          bucketType: 'images',
          key
        })
      )

      const results = await Promise.all(downloadPromises)

      results.forEach(result => {
        expect(result.success).toBe(true)
        expect(result.size).toBe(config.size)
        expect(result.contentType).toBe(config.contentType)
      })
    }, TEST_CONFIG.timeoutMs)
  })

  describe('Backup Bucket Operations', () => {
    test('should upload to backup bucket successfully', async () => {
      const testData = testFiles.get('small')!
      const config = TEST_IMAGES.small
      const key = `test/backup/${Date.now()}-${config.name}`

      const result = await r2Service.upload({
        bucketType: 'backups',
        key,
        body: testData,
        contentType: config.contentType,
        metadata: {
          testType: 'backup-upload',
          originalLocation: 'images/test/original.jpg'
        }
      })

      // Note: We don't add to uploadedKeys since this is in backup bucket
      expect(result.success).toBe(true)
      expect(result.url).toContain(key)
      expect(result.size).toBe(config.size)

      // Clean up backup file
      await r2Service.delete({
        bucketType: 'backups',
        key
      })
    }, TEST_CONFIG.timeoutMs)
  })
})
