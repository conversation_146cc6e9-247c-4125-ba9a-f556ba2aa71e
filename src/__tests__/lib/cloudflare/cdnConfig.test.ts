/**
 * Tests for CDN Configuration Service
 * Comprehensive test suite for Cloudflare CDN configuration
 */

import { CloudflareCDNConfig } from '../../../lib/cloudflare/cdnConfig'

// Mock fetch
global.fetch = jest.fn()

describe('CloudflareCDNConfig', () => {
  let cdnConfig: CloudflareCDNConfig
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>

  beforeEach(() => {
    cdnConfig = new CloudflareCDNConfig()
    jest.clearAllMocks()
  })

  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      expect(cdnConfig).toBeInstanceOf(CloudflareCDNConfig)
    })

    it('should have required environment variables', () => {
      // Mock environment variables
      process.env.CLOUDFLARE_API_TOKEN = 'test-token'
      process.env.CLOUDFLARE_ZONE_ID = 'test-zone-id'
      
      const newConfig = new CloudflareCDNConfig()
      expect(newConfig).toBeInstanceOf(CloudflareCDNConfig)
    })
  })

  describe('Cache Rules Management', () => {
    it('should create cache rule successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: {
            id: 'rule-123',
            expression: '(http.request.uri.path matches "^/api/")',
            action: 'cache',
            action_parameters: {
              cache: {
                cache_key: {
                  cache_by_device_type: false,
                  custom_key: {
                    query_string: { include: ['*'] }
                  }
                }
              }
            }
          }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.createCacheRule({
        name: 'API Cache Rule',
        expression: '(http.request.uri.path matches "^/api/")',
        ttl: 300,
        cacheLevel: 'standard'
      })

      expect(result.success).toBe(true)
      expect(result.ruleId).toBe('rule-123')
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/rulesets/'),
        expect.objectContaining({
          method: 'PUT',
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('Bearer'),
            'Content-Type': 'application/json'
          })
        })
      )
    })

    it('should handle cache rule creation failure', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        json: async () => ({
          success: false,
          errors: [{ message: 'Invalid expression' }]
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.createCacheRule({
        name: 'Invalid Rule',
        expression: 'invalid expression',
        ttl: 300,
        cacheLevel: 'standard'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid expression')
    })

    it('should update cache rule successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: {
            id: 'rule-123',
            expression: '(http.request.uri.path matches "^/api/v2/")',
            action: 'cache'
          }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.updateCacheRule('rule-123', {
        expression: '(http.request.uri.path matches "^/api/v2/")',
        ttl: 600
      })

      expect(result.success).toBe(true)
      expect(mockFetch).toHaveBeenCalled()
    })

    it('should delete cache rule successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: null
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.deleteCacheRule('rule-123')

      expect(result.success).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/rules/rule-123'),
        expect.objectContaining({
          method: 'DELETE'
        })
      )
    })
  })

  describe('Security Settings', () => {
    it('should configure security settings successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: {
            id: 'security-rule-123',
            action: 'challenge'
          }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.configureSecuritySettings({
        ddosProtection: true,
        botManagement: true,
        rateLimiting: {
          enabled: true,
          threshold: 100,
          period: 60
        },
        wafRules: ['managed-rules']
      })

      expect(result.success).toBe(true)
      expect(mockFetch).toHaveBeenCalled()
    })

    it('should handle security configuration failure', async () => {
      const mockResponse = {
        ok: false,
        status: 403,
        json: async () => ({
          success: false,
          errors: [{ message: 'Insufficient permissions' }]
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.configureSecuritySettings({
        ddosProtection: true,
        botManagement: true
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Insufficient permissions')
    })
  })

  describe('Performance Optimization', () => {
    it('should configure performance settings successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: {
            minify: { css: true, js: true, html: true },
            brotli: true,
            http2: true
          }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.configurePerformanceSettings({
        minification: {
          css: true,
          js: true,
          html: true
        },
        compression: {
          brotli: true,
          gzip: true
        },
        http2: true,
        http3: true
      })

      expect(result.success).toBe(true)
      expect(mockFetch).toHaveBeenCalled()
    })

    it('should handle performance configuration failure', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        json: async () => ({
          success: false,
          errors: [{ message: 'Internal server error' }]
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.configurePerformanceSettings({
        minification: { css: true, js: true, html: true }
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Internal server error')
    })
  })

  describe('Analytics and Monitoring', () => {
    it('should get CDN analytics successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: {
            totals: {
              requests: { all: 10000 },
              bandwidth: { all: 5000000 },
              threats: { all: 50 }
            },
            timeseries: [
              {
                since: '2023-01-01T00:00:00Z',
                until: '2023-01-01T01:00:00Z',
                requests: { all: 1000 },
                bandwidth: { all: 500000 }
              }
            ]
          }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.getCDNAnalytics({
        since: '2023-01-01T00:00:00Z',
        until: '2023-01-02T00:00:00Z',
        dimensions: ['datetime'],
        metrics: ['requests', 'bandwidth']
      })

      expect(result.success).toBe(true)
      expect(result.data?.totals.requests.all).toBe(10000)
      expect(result.data?.totals.bandwidth.all).toBe(5000000)
    })

    it('should handle analytics fetch failure', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        json: async () => ({
          success: false,
          errors: [{ message: 'Invalid date range' }]
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.getCDNAnalytics({
        since: 'invalid-date',
        until: 'invalid-date'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid date range')
    })
  })

  describe('Cache Purging', () => {
    it('should purge cache by URLs successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: { id: 'purge-123' }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.purgeCache({
        type: 'urls',
        targets: ['https://example.com/page1', 'https://example.com/page2']
      })

      expect(result.success).toBe(true)
      expect(result.purgeId).toBe('purge-123')
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/purge_cache'),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('https://example.com/page1')
        })
      )
    })

    it('should purge cache by tags successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: { id: 'purge-456' }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.purgeCache({
        type: 'tags',
        targets: ['product-123', 'category-456']
      })

      expect(result.success).toBe(true)
      expect(result.purgeId).toBe('purge-456')
    })

    it('should purge all cache successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          success: true,
          result: { id: 'purge-all' }
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.purgeCache({ type: 'everything' })

      expect(result.success).toBe(true)
      expect(result.purgeId).toBe('purge-all')
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await cdnConfig.createCacheRule({
        name: 'Test Rule',
        expression: '(http.request.uri.path matches "^/test/")',
        ttl: 300,
        cacheLevel: 'standard'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Network error')
    })

    it('should handle API rate limiting', async () => {
      const mockResponse = {
        ok: false,
        status: 429,
        json: async () => ({
          success: false,
          errors: [{ message: 'Rate limit exceeded' }]
        })
      }

      mockFetch.mockResolvedValueOnce(mockResponse as any)

      const result = await cdnConfig.getCDNAnalytics({
        since: '2023-01-01T00:00:00Z',
        until: '2023-01-02T00:00:00Z'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Rate limit exceeded')
    })

    it('should validate required parameters', async () => {
      const result = await cdnConfig.createCacheRule({
        name: '',
        expression: '',
        ttl: -1,
        cacheLevel: 'invalid' as any
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid')
    })
  })
})
