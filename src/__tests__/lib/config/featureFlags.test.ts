/**
 * Tests for Feature Flags System
 * Comprehensive test suite for feature flag functionality
 */

import {
  shouldUseFeature,
  getFeatureConfig,
  updateFeatureFlag,
  enableFeature,
  disableFeature,
  setFeaturePercentage,
  addUserToFeature,
  removeUserFromFeature,
  getFeatureMetrics,
  checkFeatureDependencies,
  emergencyDisableFeature,
  FeatureFlag,
  FeatureMetrics
} from '../../../lib/config/featureFlags'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('Feature Flags System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('Basic Feature Flag Operations', () => {
    it('should return correct feature flag status', () => {
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(true)
      expect(shouldUseFeature('USE_CLOUDFLARE_ANALYTICS')).toBe(true)
      expect(shouldUseFeature('USE_HYBRID_CACHING')).toBe(true)
      expect(shouldUseFeature('NONEXISTENT_FEATURE')).toBe(false)
    })

    it('should get feature configuration', () => {
      const config = getFeatureConfig('USE_CLOUDFLARE_CDN')
      
      expect(config).toBeDefined()
      expect(config?.enabled).toBe(true)
      expect(config?.name).toBe('USE_CLOUDFLARE_CDN')
      expect(config?.description).toContain('Cloudflare CDN')
    })

    it('should return null for non-existent feature', () => {
      const config = getFeatureConfig('NONEXISTENT_FEATURE')
      expect(config).toBeNull()
    })

    it('should enable feature correctly', () => {
      const result = enableFeature('USE_CLOUDFLARE_WORKERS')
      
      expect(result).toBe(true)
      expect(shouldUseFeature('USE_CLOUDFLARE_WORKERS')).toBe(true)
    })

    it('should disable feature correctly', () => {
      const result = disableFeature('USE_CLOUDFLARE_CDN')
      
      expect(result).toBe(true)
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(false)
    })

    it('should return false for non-existent feature operations', () => {
      expect(enableFeature('NONEXISTENT_FEATURE')).toBe(false)
      expect(disableFeature('NONEXISTENT_FEATURE')).toBe(false)
    })
  })

  describe('Percentage-based Rollout', () => {
    it('should set feature percentage correctly', () => {
      const result = setFeaturePercentage('USE_CLOUDFLARE_WORKERS', 50)
      
      expect(result).toBe(true)
      
      const config = getFeatureConfig('USE_CLOUDFLARE_WORKERS')
      expect(config?.rolloutPercentage).toBe(50)
    })

    it('should respect percentage rollout', () => {
      // Mock Math.random to return predictable values
      const originalRandom = Math.random
      
      // Test with 30% rollout
      setFeaturePercentage('USE_CLOUDFLARE_WORKERS', 30)
      
      // Should be enabled when random < 0.3
      Math.random = jest.fn().mockReturnValue(0.2)
      expect(shouldUseFeature('USE_CLOUDFLARE_WORKERS')).toBe(true)
      
      // Should be disabled when random >= 0.3
      Math.random = jest.fn().mockReturnValue(0.5)
      expect(shouldUseFeature('USE_CLOUDFLARE_WORKERS')).toBe(false)
      
      // Restore original Math.random
      Math.random = originalRandom
    })

    it('should validate percentage range', () => {
      expect(setFeaturePercentage('USE_CLOUDFLARE_WORKERS', -10)).toBe(false)
      expect(setFeaturePercentage('USE_CLOUDFLARE_WORKERS', 150)).toBe(false)
      expect(setFeaturePercentage('USE_CLOUDFLARE_WORKERS', 50)).toBe(true)
    })
  })

  describe('User-based Targeting', () => {
    it('should add user to feature correctly', () => {
      const result = addUserToFeature('USE_CLOUDFLARE_WORKERS', 'user123')
      
      expect(result).toBe(true)
      
      const config = getFeatureConfig('USE_CLOUDFLARE_WORKERS')
      expect(config?.targetUsers).toContain('user123')
    })

    it('should remove user from feature correctly', () => {
      addUserToFeature('USE_CLOUDFLARE_WORKERS', 'user123')
      const result = removeUserFromFeature('USE_CLOUDFLARE_WORKERS', 'user123')
      
      expect(result).toBe(true)
      
      const config = getFeatureConfig('USE_CLOUDFLARE_WORKERS')
      expect(config?.targetUsers).not.toContain('user123')
    })

    it('should enable feature for targeted users', () => {
      // Disable feature globally but target specific user
      disableFeature('USE_CLOUDFLARE_WORKERS')
      addUserToFeature('USE_CLOUDFLARE_WORKERS', 'user123')
      
      // Mock user context
      const originalLocation = window.location
      Object.defineProperty(window, 'location', {
        value: { search: '?userId=user123' },
        writable: true
      })
      
      expect(shouldUseFeature('USE_CLOUDFLARE_WORKERS')).toBe(true)
      
      // Restore
      Object.defineProperty(window, 'location', {
        value: originalLocation,
        writable: true
      })
    })
  })

  describe('Feature Dependencies', () => {
    it('should check dependencies correctly', () => {
      // USE_CLOUDFLARE_WORKERS depends on USE_CLOUDFLARE_CDN
      const dependencies = checkFeatureDependencies('USE_CLOUDFLARE_WORKERS')
      
      expect(dependencies.canEnable).toBe(true) // CDN is enabled by default
      expect(dependencies.missingDependencies).toHaveLength(0)
    })

    it('should prevent enabling feature with missing dependencies', () => {
      // Disable CDN first
      disableFeature('USE_CLOUDFLARE_CDN')
      
      const dependencies = checkFeatureDependencies('USE_CLOUDFLARE_WORKERS')
      
      expect(dependencies.canEnable).toBe(false)
      expect(dependencies.missingDependencies).toContain('USE_CLOUDFLARE_CDN')
    })

    it('should update feature flag with dependency validation', () => {
      // Disable CDN
      disableFeature('USE_CLOUDFLARE_CDN')
      
      const result = updateFeatureFlag('USE_CLOUDFLARE_WORKERS', {
        enabled: true,
        validateDependencies: true
      })
      
      expect(result).toBe(false) // Should fail due to missing dependency
    })
  })

  describe('Emergency Controls', () => {
    it('should emergency disable feature', () => {
      const result = emergencyDisableFeature('USE_CLOUDFLARE_CDN')
      
      expect(result).toBe(true)
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(false)
      
      const config = getFeatureConfig('USE_CLOUDFLARE_CDN')
      expect(config?.emergencyDisabled).toBe(true)
    })

    it('should prevent enabling emergency disabled feature', () => {
      emergencyDisableFeature('USE_CLOUDFLARE_CDN')
      
      const result = enableFeature('USE_CLOUDFLARE_CDN')
      
      expect(result).toBe(false) // Should fail due to emergency disable
      expect(shouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(false)
    })
  })

  describe('Feature Metrics', () => {
    it('should track feature usage metrics', () => {
      // Use feature multiple times
      shouldUseFeature('USE_CLOUDFLARE_CDN')
      shouldUseFeature('USE_CLOUDFLARE_CDN')
      shouldUseFeature('USE_CLOUDFLARE_CDN')
      
      const metrics = getFeatureMetrics('USE_CLOUDFLARE_CDN')
      
      expect(metrics).toBeDefined()
      expect(metrics?.usageCount).toBeGreaterThan(0)
      expect(metrics?.lastUsed).toBeDefined()
    })

    it('should return null metrics for non-existent feature', () => {
      const metrics = getFeatureMetrics('NONEXISTENT_FEATURE')
      expect(metrics).toBeNull()
    })
  })

  describe('Local Storage Integration', () => {
    it('should save feature flag changes to localStorage', () => {
      updateFeatureFlag('USE_CLOUDFLARE_CDN', { enabled: false })
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'featureFlags',
        expect.stringContaining('USE_CLOUDFLARE_CDN')
      )
    })

    it('should load feature flags from localStorage', () => {
      const mockFlags = {
        USE_CLOUDFLARE_CDN: { enabled: false, lastModified: new Date().toISOString() }
      }
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockFlags))
      
      // Re-import to trigger localStorage loading
      jest.resetModules()
      const { shouldUseFeature: newShouldUseFeature } = require('../../../lib/config/featureFlags')
      
      expect(newShouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(false)
    })
  })

  describe('Error Handling', () => {
    it('should handle localStorage errors gracefully', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      const result = updateFeatureFlag('USE_CLOUDFLARE_CDN', { enabled: false })
      
      expect(result).toBe(true) // Should still work despite storage error
      expect(consoleSpy).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })

    it('should handle invalid localStorage data', () => {
      localStorageMock.getItem.mockReturnValue('invalid json')
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      // Re-import to trigger localStorage loading
      jest.resetModules()
      const { shouldUseFeature: newShouldUseFeature } = require('../../../lib/config/featureFlags')
      
      expect(newShouldUseFeature('USE_CLOUDFLARE_CDN')).toBe(true) // Should fall back to defaults
      expect(consoleSpy).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })
  })
})
