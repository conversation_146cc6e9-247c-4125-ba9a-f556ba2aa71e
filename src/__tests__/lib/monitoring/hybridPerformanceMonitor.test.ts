/**
 * Tests for Hybrid Performance Monitor
 * Comprehensive test suite for performance monitoring functionality
 */

import { 
  HybridPerformanceMonitor,
  PerformanceMetric,
  AlertRule,
  Alert
} from '../../../lib/monitoring/hybridPerformanceMonitor'

// Mock feature flags
jest.mock('../../../lib/config/featureFlags', () => ({
  shouldUseFeature: jest.fn().mockReturnValue(true)
}))

describe('HybridPerformanceMonitor', () => {
  let monitor: HybridPerformanceMonitor
  
  beforeEach(() => {
    monitor = new HybridPerformanceMonitor()
    jest.clearAllMocks()
  })

  afterEach(() => {
    monitor.stopMonitoring()
  })

  describe('Initialization', () => {
    it('should initialize with default alert rules', () => {
      const rules = monitor.getAlertRules()
      expect(rules).toHaveLength(4)
      expect(rules.find(r => r.id === 'high_response_time')).toBeDefined()
      expect(rules.find(r => r.id === 'low_cache_hit_ratio')).toBeDefined()
      expect(rules.find(r => r.id === 'high_error_rate')).toBeDefined()
      expect(rules.find(r => r.id === 'poor_lcp')).toBeDefined()
    })

    it('should start with empty metrics and alerts', () => {
      expect(monitor.getMetrics()).toHaveLength(0)
      expect(monitor.getAlerts()).toHaveLength(0)
    })
  })

  describe('Metric Collection', () => {
    it('should add metrics correctly', () => {
      const metric: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: 500,
        unit: 'ms',
        source: 'cloudflare',
        metadata: { endpoint: '/api/test' }
      }

      monitor.addMetric(metric)
      const metrics = monitor.getMetrics()
      
      expect(metrics).toHaveLength(1)
      expect(metrics[0]).toEqual(metric)
    })

    it('should collect metrics when monitoring starts', async () => {
      const collectSpy = jest.spyOn(monitor as any, 'collectMetrics')
      
      await monitor.startMonitoring(0.1) // 0.1 minute interval for testing
      
      expect(collectSpy).toHaveBeenCalled()
      
      // Wait for interval to trigger
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(collectSpy).toHaveBeenCalledTimes(2) // Initial + interval
    })

    it('should not start monitoring if already running', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      await monitor.startMonitoring(1)
      await monitor.startMonitoring(1) // Try to start again
      
      expect(consoleSpy).toHaveBeenCalledWith('Performance monitoring is already running')
      
      consoleSpy.mockRestore()
    })

    it('should stop monitoring correctly', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      
      await monitor.startMonitoring(1)
      monitor.stopMonitoring()
      
      expect(consoleSpy).toHaveBeenCalledWith('Performance monitoring stopped')
      
      consoleSpy.mockRestore()
    })
  })

  describe('Alert System', () => {
    it('should trigger alert when threshold is exceeded', async () => {
      // Add metrics that exceed threshold
      const highResponseTime: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: 3000, // Exceeds 2000ms threshold
        unit: 'ms',
        source: 'cloudflare'
      }

      monitor.addMetric(highResponseTime)
      
      await monitor.checkAlerts()
      
      const alerts = monitor.getAlerts()
      expect(alerts).toHaveLength(1)
      expect(alerts[0].ruleId).toBe('high_response_time')
      expect(alerts[0].severity).toBe('high')
    })

    it('should not trigger duplicate alerts for same rule', async () => {
      // Add metric that triggers alert
      const metric: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        metricType: 'error_rate',
        value: 10, // Exceeds 5% threshold
        unit: 'percentage',
        source: 'cloudflare'
      }

      monitor.addMetric(metric)
      
      await monitor.checkAlerts()
      await monitor.checkAlerts() // Check again
      
      const alerts = monitor.getAlerts()
      expect(alerts).toHaveLength(1) // Should not duplicate
    })

    it('should resolve alerts correctly', () => {
      // Manually add an alert
      const alert: Alert = {
        id: 'test-alert',
        ruleId: 'test-rule',
        ruleName: 'Test Rule',
        severity: 'medium',
        message: 'Test alert',
        timestamp: new Date().toISOString(),
        resolved: false
      }

      monitor['alerts'].push(alert)
      
      const resolved = monitor.resolveAlert('test-alert')
      
      expect(resolved).toBe(true)
      expect(alert.resolved).toBe(true)
      expect(alert.resolvedAt).toBeDefined()
    })

    it('should update alert rules correctly', () => {
      const updated = monitor.updateAlertRule('high_response_time', {
        threshold: 1500,
        enabled: false
      })

      expect(updated).toBe(true)
      
      const rule = monitor.getAlertRules().find(r => r.id === 'high_response_time')
      expect(rule?.threshold).toBe(1500)
      expect(rule?.enabled).toBe(false)
    })
  })

  describe('Report Generation', () => {
    beforeEach(() => {
      // Add sample metrics
      const metrics: PerformanceMetric[] = [
        {
          timestamp: new Date().toISOString(),
          metricType: 'response_time',
          value: 500,
          unit: 'ms',
          source: 'cloudflare'
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'cache_hit_ratio',
          value: 85,
          unit: 'percentage',
          source: 'cloudflare'
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'error_rate',
          value: 1.5,
          unit: 'percentage',
          source: 'firebase'
        },
        {
          timestamp: new Date().toISOString(),
          metricType: 'core_web_vitals',
          value: 2000,
          unit: 'ms',
          source: 'client',
          metadata: { metric: 'lcp' }
        }
      ]

      metrics.forEach(metric => monitor.addMetric(metric))
    })

    it('should generate performance report correctly', () => {
      const report = monitor.generateReport(24)
      
      expect(report.period).toBe('24h')
      expect(report.startTime).toBeDefined()
      expect(report.endTime).toBeDefined()
      expect(report.metrics.avgResponseTime).toBe(500)
      expect(report.metrics.cacheHitRatio).toBe(85)
      expect(report.metrics.errorRate).toBe(1.5)
      expect(report.metrics.coreWebVitals.avgLCP).toBe(2000)
    })

    it('should generate recommendations based on metrics', () => {
      const report = monitor.generateReport(24)
      
      expect(report.recommendations).toContain('Performance metrics are within acceptable ranges')
    })

    it('should calculate trends correctly', () => {
      // Add more metrics to test trend calculation
      const olderMetrics: PerformanceMetric[] = [
        {
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          metricType: 'response_time',
          value: 600,
          unit: 'ms',
          source: 'cloudflare'
        },
        {
          timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
          metricType: 'response_time',
          value: 400,
          unit: 'ms',
          source: 'cloudflare'
        }
      ]

      olderMetrics.forEach(metric => monitor.addMetric(metric))
      
      const report = monitor.generateReport(24)
      
      expect(['improving', 'degrading', 'stable']).toContain(report.trends.responseTime)
    })
  })

  describe('Data Management', () => {
    it('should clean up old data correctly', () => {
      // Add old metric
      const oldMetric: PerformanceMetric = {
        timestamp: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(), // 8 days ago
        metricType: 'response_time',
        value: 500,
        unit: 'ms',
        source: 'cloudflare'
      }

      // Add recent metric
      const recentMetric: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: 400,
        unit: 'ms',
        source: 'cloudflare'
      }

      monitor.addMetric(oldMetric)
      monitor.addMetric(recentMetric)
      
      expect(monitor.getMetrics()).toHaveLength(2)
      
      // Trigger cleanup
      monitor['cleanupOldData']()
      
      const remainingMetrics = monitor.getMetrics()
      expect(remainingMetrics).toHaveLength(1)
      expect(remainingMetrics[0].timestamp).toBe(recentMetric.timestamp)
    })
  })

  describe('Legacy Compatibility', () => {
    it('should support legacy recordImageLoadTime method', () => {
      const { recordImageLoadTime } = require('../../../lib/monitoring/hybridPerformanceMonitor')
      
      recordImageLoadTime('/test-image.jpg', 300, 'cloudflare')
      
      const metrics = monitor.getMetrics()
      expect(metrics).toHaveLength(1)
      expect(metrics[0].metricType).toBe('response_time')
      expect(metrics[0].value).toBe(300)
      expect(metrics[0].source).toBe('cloudflare')
    })

    it('should support legacy recordApiResponseTime method', () => {
      const { recordApiResponseTime } = require('../../../lib/monitoring/hybridPerformanceMonitor')
      
      recordApiResponseTime('/api/test', 150, true)
      
      const metrics = monitor.getMetrics()
      expect(metrics).toHaveLength(1)
      expect(metrics[0].metricType).toBe('response_time')
      expect(metrics[0].value).toBe(150)
      expect(metrics[0].source).toBe('cloudflare')
    })

    it('should support legacy recordCacheEvent method', () => {
      const { recordCacheEvent } = require('../../../lib/monitoring/hybridPerformanceMonitor')
      
      recordCacheEvent('/test-url', 'HIT')
      
      const metrics = monitor.getMetrics()
      expect(metrics).toHaveLength(1)
      expect(metrics[0].metricType).toBe('cache_hit_ratio')
      expect(metrics[0].value).toBe(100)
      expect(metrics[0].source).toBe('cloudflare')
    })
  })
})
