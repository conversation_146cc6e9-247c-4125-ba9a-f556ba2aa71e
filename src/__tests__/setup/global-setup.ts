/**
 * Global Test Setup
 * Runs once before all tests
 */

export default async function globalSetup() {
  console.log('🚀 Starting Phase 1 Test Suite...')
  
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.JEST_WORKER_ID = '1'
  
  // Create test directories if they don't exist
  const fs = require('fs')
  const path = require('path')
  
  const testDirs = [
    './test-reports',
    './test-reports/phase1',
    './coverage',
    './coverage/phase1'
  ]
  
  testDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
  })
  
  // Log test configuration
  console.log('📋 Test Configuration:')
  console.log('  - Environment:', process.env.NODE_ENV)
  console.log('  - Test Timeout:', '10000ms')
  console.log('  - Coverage Threshold:', '80%')
  console.log('  - Test Reports:', './test-reports/phase1/')
  
  console.log('✅ Global setup complete')
}
