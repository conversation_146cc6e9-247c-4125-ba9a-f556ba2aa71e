/**
 * Global Test Teardown
 * Runs once after all tests
 */

export default async function globalTeardown() {
  console.log('🧹 Cleaning up after Phase 1 tests...')
  
  // Clean up any global resources
  // Reset environment variables
  delete process.env.JEST_WORKER_ID
  
  // Log test completion
  console.log('📊 Test Summary:')
  console.log('  - Phase 1 tests completed')
  console.log('  - Coverage reports generated')
  console.log('  - Test reports available in ./test-reports/phase1/')
  
  console.log('✅ Global teardown complete')
}
