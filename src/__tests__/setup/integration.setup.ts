/**
 * Integration Test Setup
 * Setup configuration for R2 storage and migration integration tests
 */

import { jest } from '@jest/globals'

// Extend Jest timeout for integration tests
jest.setTimeout(60000)

// Mock environment variables for testing
process.env.NODE_ENV = 'test'
process.env.CLOUDFLARE_ACCOUNT_ID = 'test-account-id'
process.env.CLOUDFLARE_R2_ACCESS_KEY_ID = 'test-access-key'
process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY = 'test-secret-key'
process.env.CLOUDFLARE_R2_IMAGES_BUCKET = 'test-images'
process.env.CLOUDFLARE_R2_BACKUPS_BUCKET = 'test-backups'
process.env.CLOUDFLARE_R2_ENDPOINT = 'https://test.r2.cloudflarestorage.com'

// Mock Firebase configuration
process.env.FIREBASE_PROJECT_ID = 'test-project'
process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>'
process.env.FIREBASE_PRIVATE_KEY = 'test-private-key'

// Global test utilities
global.testUtils = {
  // Generate test image data
  generateTestImageData: (size: number = 1024): ArrayBuffer => {
    const buffer = new ArrayBuffer(size)
    const view = new Uint8Array(buffer)
    
    // Fill with test pattern
    for (let i = 0; i < view.length; i++) {
      view[i] = i % 256
    }
    
    return buffer
  },

  // Generate test Firebase Storage URL
  generateFirebaseUrl: (path: string): string => {
    return `https://firebasestorage.googleapis.com/v0/b/test-project.appspot.com/o/${encodeURIComponent(path)}?alt=media&token=test-token`
  },

  // Generate test R2 URL
  generateR2Url: (key: string): string => {
    return `https://test.r2.cloudflarestorage.com/test-images/${key}`
  },

  // Wait for async operations
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // Generate unique test ID
  generateTestId: (): string => {
    return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// Mock console methods to reduce noise in tests
const originalConsole = { ...console }

beforeAll(() => {
  // Suppress console.log in tests unless explicitly needed
  console.log = jest.fn()
  console.info = jest.fn()
  
  // Keep error and warn for debugging
  console.error = originalConsole.error
  console.warn = originalConsole.warn
})

afterAll(() => {
  // Restore console methods
  Object.assign(console, originalConsole)
})

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Mock fetch for HTTP requests in tests
global.fetch = jest.fn()

// Mock WebSocket for real-time features
global.WebSocket = jest.fn().mockImplementation(() => ({
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1, // OPEN
}))

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByName: jest.fn(() => []),
  getEntriesByType: jest.fn(() => []),
  clearMarks: jest.fn(),
  clearMeasures: jest.fn(),
} as any

// Mock crypto API for Node.js environment
if (typeof global.crypto === 'undefined') {
  const crypto = require('crypto')
  global.crypto = {
    getRandomValues: (arr: any) => crypto.randomBytes(arr.length),
    randomUUID: () => crypto.randomUUID(),
  } as any
}

// Mock URL constructor for Node.js environment
if (typeof global.URL === 'undefined') {
  global.URL = require('url').URL
}

// Mock Blob for file operations
if (typeof global.Blob === 'undefined') {
  global.Blob = class MockBlob {
    constructor(public parts: any[], public options: any = {}) {}
    get size() { return this.parts.reduce((size, part) => size + (part.length || 0), 0) }
    get type() { return this.options.type || '' }
    slice() { return new MockBlob([], {}) }
    stream() { return new ReadableStream() }
    text() { return Promise.resolve('') }
    arrayBuffer() { return Promise.resolve(new ArrayBuffer(0)) }
  } as any
}

// Mock File for file upload tests
if (typeof global.File === 'undefined') {
  global.File = class MockFile extends (global.Blob as any) {
    constructor(parts: any[], public name: string, options: any = {}) {
      super(parts, options)
    }
    get lastModified() { return Date.now() }
  } as any
}

// Mock FormData for multipart uploads
if (typeof global.FormData === 'undefined') {
  global.FormData = class MockFormData {
    private data = new Map()
    
    append(key: string, value: any) {
      this.data.set(key, value)
    }
    
    get(key: string) {
      return this.data.get(key)
    }
    
    has(key: string) {
      return this.data.has(key)
    }
    
    delete(key: string) {
      this.data.delete(key)
    }
    
    entries() {
      return this.data.entries()
    }
  } as any
}

// Mock Headers for HTTP requests
if (typeof global.Headers === 'undefined') {
  global.Headers = class MockHeaders {
    private headers = new Map()
    
    constructor(init?: any) {
      if (init) {
        Object.entries(init).forEach(([key, value]) => {
          this.headers.set(key.toLowerCase(), value)
        })
      }
    }
    
    append(name: string, value: string) {
      this.headers.set(name.toLowerCase(), value)
    }
    
    delete(name: string) {
      this.headers.delete(name.toLowerCase())
    }
    
    get(name: string) {
      return this.headers.get(name.toLowerCase())
    }
    
    has(name: string) {
      return this.headers.has(name.toLowerCase())
    }
    
    set(name: string, value: string) {
      this.headers.set(name.toLowerCase(), value)
    }
    
    entries() {
      return this.headers.entries()
    }
  } as any
}

// Mock Request for HTTP requests
if (typeof global.Request === 'undefined') {
  global.Request = class MockRequest {
    constructor(
      public url: string,
      public init: any = {}
    ) {}
    
    get method() { return this.init.method || 'GET' }
    get headers() { return new (global.Headers as any)(this.init.headers) }
    get body() { return this.init.body }
    
    clone() {
      return new MockRequest(this.url, { ...this.init })
    }
    
    json() {
      return Promise.resolve(JSON.parse(this.init.body || '{}'))
    }
    
    text() {
      return Promise.resolve(this.init.body || '')
    }
    
    arrayBuffer() {
      return Promise.resolve(new ArrayBuffer(0))
    }
  } as any
}

// Mock Response for HTTP responses
if (typeof global.Response === 'undefined') {
  global.Response = class MockResponse {
    constructor(
      public body: any = null,
      public init: any = {}
    ) {}
    
    get status() { return this.init.status || 200 }
    get statusText() { return this.init.statusText || 'OK' }
    get ok() { return this.status >= 200 && this.status < 300 }
    get headers() { return new (global.Headers as any)(this.init.headers) }
    
    clone() {
      return new MockResponse(this.body, { ...this.init })
    }
    
    json() {
      return Promise.resolve(typeof this.body === 'string' ? JSON.parse(this.body) : this.body)
    }
    
    text() {
      return Promise.resolve(typeof this.body === 'string' ? this.body : JSON.stringify(this.body))
    }
    
    arrayBuffer() {
      return Promise.resolve(this.body instanceof ArrayBuffer ? this.body : new ArrayBuffer(0))
    }
    
    static json(data: any, init?: any) {
      return new MockResponse(data, {
        ...init,
        headers: {
          'content-type': 'application/json',
          ...init?.headers
        }
      })
    }
    
    static error() {
      return new MockResponse(null, { status: 500, statusText: 'Internal Server Error' })
    }
  } as any
}

// Export test utilities type
declare global {
  var testUtils: {
    generateTestImageData: (size?: number) => ArrayBuffer
    generateFirebaseUrl: (path: string) => string
    generateR2Url: (key: string) => string
    wait: (ms: number) => Promise<void>
    generateTestId: () => string
  }
}
