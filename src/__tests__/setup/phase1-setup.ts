/**
 * Phase 1 Test Setup
 * Configuration and mocks for Phase 1 testing
 */

import '@testing-library/jest-dom'

// Mock environment variables
process.env.CLOUDFLARE_API_TOKEN = 'test-token'
process.env.CLOUDFLARE_ZONE_ID = 'test-zone-id'
process.env.CLOUDFLARE_ACCOUNT_ID = 'test-account-id'
process.env.NEXT_PUBLIC_CLOUDFLARE_DOMAIN = 'test.example.com'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock
})

// Mock fetch globally
global.fetch = jest.fn()

// Mock console methods to reduce noise in tests
const originalConsole = { ...console }
beforeEach(() => {
  console.warn = jest.fn()
  console.error = jest.fn()
  console.log = jest.fn()
})

afterEach(() => {
  Object.assign(console, originalConsole)
})

// Mock performance observer for Core Web Vitals
global.PerformanceObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  takeRecords: jest.fn(() => [])
}))

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn(() => []),
    getEntriesByName: jest.fn(() => []),
    clearMarks: jest.fn(),
    clearMeasures: jest.fn()
  }
})

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: jest.fn(),
    replace: jest.fn(),
    reload: jest.fn()
  },
  writable: true
})

// Mock window.navigator
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Test Browser)',
    language: 'en-US',
    languages: ['en-US', 'en'],
    platform: 'Test Platform',
    cookieEnabled: true,
    onLine: true
  },
  writable: true
})

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'test-uuid-' + Math.random().toString(36).substr(2, 9)),
    getRandomValues: jest.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    })
  }
})

// Mock Date.now for consistent timestamps in tests
const originalDateNow = Date.now
beforeEach(() => {
  Date.now = jest.fn(() => 1640995200000) // 2022-01-01T00:00:00.000Z
})

afterEach(() => {
  Date.now = originalDateNow
})

// Mock setTimeout and setInterval for testing
jest.useFakeTimers()

// Global test utilities
global.testUtils = {
  // Helper to create mock performance metrics
  createMockMetric: (overrides = {}) => ({
    timestamp: new Date().toISOString(),
    metricType: 'response_time',
    value: 100,
    unit: 'ms',
    source: 'cloudflare',
    ...overrides
  }),
  
  // Helper to create mock alerts
  createMockAlert: (overrides = {}) => ({
    id: 'test-alert-' + Math.random().toString(36).substr(2, 9),
    ruleId: 'test-rule',
    ruleName: 'Test Rule',
    severity: 'medium',
    message: 'Test alert message',
    timestamp: new Date().toISOString(),
    resolved: false,
    ...overrides
  }),
  
  // Helper to create mock fetch responses
  createMockResponse: (data = {}, ok = true, status = 200) => ({
    ok,
    status,
    json: async () => data,
    text: async () => JSON.stringify(data),
    headers: new Headers(),
    statusText: ok ? 'OK' : 'Error'
  }),
  
  // Helper to wait for async operations
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to advance timers
  advanceTimers: (ms) => {
    jest.advanceTimersByTime(ms)
  }
}

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks()
  jest.clearAllTimers()
  localStorageMock.clear()
})

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Suppress specific warnings in tests
const originalWarn = console.warn
console.warn = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('React.createFactory') ||
     args[0].includes('componentWillReceiveProps') ||
     args[0].includes('componentWillUpdate'))
  ) {
    return
  }
  originalWarn.apply(console, args)
}
