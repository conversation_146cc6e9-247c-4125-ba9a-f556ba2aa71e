/**
 * Analytics Authentication Wrapper
 * 
 * Wrapper component that ensures proper admin authentication
 * before allowing access to analytics features.
 * 
 * Features:
 * - Admin role verification
 * - Loading state management
 * - Permission denied messaging
 * - Graceful fallback handling
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 1 Enhancement - Security Fix
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Shield, AlertTriangle, Loader2, Lock } from 'lucide-react'
import { AdminCard } from '../common'
import { useAdminAuth } from '../../hooks/useAdminAuth'

interface AnalyticsAuthWrapperProps {
  children: React.ReactNode
  requiredPermission?: 'admin' | 'superadmin'
  fallbackMessage?: string
}

/**
 * Analytics Authentication Wrapper Component
 */
const AnalyticsAuthWrapper: React.FC<AnalyticsAuthWrapperProps> = ({
  children,
  requiredPermission = 'admin',
  fallbackMessage = 'Advanced analytics features require admin access'
}) => {
  const { 
    isAdmin, 
    isSuperAdmin, 
    loading, 
    user, 
    hasAdminAccess 
  } = useAdminAuth()

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <Loader2 className="w-8 h-8 text-accent-400 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">Verifying admin access...</p>
        </motion.div>
      </div>
    )
  }

  // Check if user is authenticated
  if (!user) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="min-h-96"
      >
        <AdminCard title="Authentication Required" className="max-w-md mx-auto">
          <div className="text-center py-8">
            <Lock className="w-12 h-12 text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">
              Sign In Required
            </h3>
            <p className="text-gray-400 mb-6">
              Please sign in to access analytics features
            </p>
            <button
              onClick={() => window.location.href = '/admin/login'}
              className="px-6 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
            >
              Go to Login
            </button>
          </div>
        </AdminCard>
      </motion.div>
    )
  }

  // Check admin permissions
  const hasRequiredAccess = requiredPermission === 'superadmin' 
    ? isSuperAdmin 
    : hasAdminAccess()

  if (!hasRequiredAccess) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="min-h-96"
      >
        <AdminCard title="Access Denied" className="max-w-md mx-auto">
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-red-400" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">
              Insufficient Permissions
            </h3>
            <p className="text-gray-400 mb-6">
              {fallbackMessage}
            </p>
            <div className="space-y-2 text-sm text-gray-500">
              <p>Required: {requiredPermission} access</p>
              <p>Current role: {isAdmin ? 'Admin' : isSuperAdmin ? 'Super Admin' : 'User'}</p>
            </div>
          </div>
        </AdminCard>
      </motion.div>
    )
  }

  // User has proper access - render children with success indicator
  return (
    <div className="analytics-auth-wrapper">
      {/* Success indicator (optional) */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-4"
      >
        <div className="flex items-center justify-between bg-green-500/10 border border-green-500/20 rounded-lg px-4 py-2">
          <div className="flex items-center space-x-2">
            <Shield className="w-4 h-4 text-green-400" />
            <span className="text-green-400 text-sm font-medium">
              Authenticated as {requiredPermission}
            </span>
          </div>
          <span className="text-green-300 text-xs">
            Access verified
          </span>
        </div>
      </motion.div>

      {/* Render analytics content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        {children}
      </motion.div>
    </div>
  )
}

export default AnalyticsAuthWrapper