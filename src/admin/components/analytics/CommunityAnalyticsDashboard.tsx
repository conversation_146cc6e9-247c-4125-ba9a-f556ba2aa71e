/**
 * Community Analytics Dashboard Component
 *
 * Comprehensive analytics dashboard providing deep insights into community health,
 * engagement patterns, content performance, and user behavior across all community features.
 *
 * Features:
 * - Real-time community health metrics and KPIs
 * - User engagement and activity pattern analysis
 * - Content performance tracking across all types
 * - Growth trends and predictive analytics
 * - Interactive data visualizations and charts
 * - Customizable time ranges and filtering
 * - Exportable reports and insights
 * - Comparative analysis and benchmarking
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  MessageSquare,
  Upload,
  Trophy,
  Eye,
  Heart,
  Share2,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Target,
  Activity,
  Zap,
  Award
} from 'lucide-react';
import { AdminCard, AdminButton } from '../common';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { useCommunityAnalytics } from '../hooks/useCommunityAnalytics';
import { CommunityStatsCard } from '../community/shared/CommunityStatsCard';
import { AnalyticsChart } from './AnalyticsChart';
import { UserEngagementMatrix } from './UserEngagementMatrix';
import { ContentPerformanceGrid } from './ContentPerformanceGrid';
import type { 
  CommunityAnalyticsData,
  AnalyticsTimeRange,
  AnalyticsMetric
} from '../types';

interface CommunityAnalyticsDashboardProps {
  className?: string;
}

export const CommunityAnalyticsDashboard: React.FC<CommunityAnalyticsDashboardProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [timeRange, setTimeRange] = useState<AnalyticsTimeRange>('30d');
  const [selectedMetrics, setSelectedMetrics] = useState<AnalyticsMetric[]>([
    'user_growth',
    'content_creation',
    'engagement_rate',
    'community_health'
  ]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);

  // Data hooks
  const {
    analyticsData,
    loading,
    error,
    lastUpdated,
    refetch,
    exportData
  } = useCommunityAnalytics(timeRange, selectedMetrics);

  // Permission checks
  const canViewAnalytics = hasPermission('community_analytics', 'read');
  const canExportData = hasPermission('community_analytics', 'export');

  // Auto-refresh setup
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(refetch, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [refreshInterval, refetch]);

  // Handle export functionality
  const handleExport = useCallback(async (format: 'csv' | 'pdf' | 'json') => {
    if (!canExportData) return;
    
    try {
      await exportData(format, timeRange, selectedMetrics);
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, [canExportData, exportData, timeRange, selectedMetrics]);

  // Handle metric toggle
  const handleMetricToggle = useCallback((metric: AnalyticsMetric) => {
    setSelectedMetrics(prev => 
      prev.includes(metric)
        ? prev.filter(m => m !== metric)
        : [...prev, metric]
    );
  }, []);

  if (!canViewAnalytics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-400">Access denied</p>
          <p className="text-sm text-gray-500">You don't have permission to view analytics</p>
        </div>
      </div>
    );
  }

  if (loading && !analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 text-purple-400 mx-auto mb-4 animate-spin" />
          <p className="text-gray-400">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-400">Failed to load analytics</p>
          <p className="text-sm text-gray-500">{error}</p>
          <AdminButton
            variant="secondary"
            onClick={refetch}
            className="mt-4"
          >
            Retry
          </AdminButton>
        </div>
      </div>
    );
  }

  // Calculate key metrics
  const keyMetrics = analyticsData ? [
    {
      title: 'Total Users',
      value: analyticsData.userMetrics.totalUsers,
      icon: Users,
      color: 'blue' as const,
      trend: analyticsData.userMetrics.growthRate,
      subtitle: `+${analyticsData.userMetrics.newUsers} this ${timeRange}`
    },
    {
      title: 'Active Users',
      value: analyticsData.userMetrics.activeUsers,
      icon: Activity,
      color: 'green' as const,
      trend: analyticsData.userMetrics.activityTrend,
      subtitle: `${Math.round(analyticsData.userMetrics.activeUsers / analyticsData.userMetrics.totalUsers * 100)}% of total`
    },
    {
      title: 'Content Created',
      value: analyticsData.contentMetrics.totalContent,
      icon: Upload,
      color: 'purple' as const,
      trend: analyticsData.contentMetrics.creationTrend,
      subtitle: `${analyticsData.contentMetrics.dailyAverage}/day avg`
    },
    {
      title: 'Engagement Rate',
      value: `${Math.round(analyticsData.engagementMetrics.overallRate * 100)}%`,
      icon: Heart,
      color: 'red' as const,
      trend: analyticsData.engagementMetrics.rateTrend,
      subtitle: 'Across all content types'
    },
    {
      title: 'Community Health',
      value: `${Math.round(analyticsData.healthScore * 100)}%`,
      icon: Target,
      color: 'yellow' as const,
      trend: analyticsData.healthTrend,
      subtitle: 'Overall community wellness'
    },
    {
      title: 'Challenges Active',
      value: analyticsData.challengeMetrics.activeChallenges,
      icon: Trophy,
      color: 'orange' as const,
      trend: analyticsData.challengeMetrics.participationTrend,
      subtitle: `${analyticsData.challengeMetrics.totalParticipants} participants`
    }
  ] : [];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Community Analytics</h1>
          <p className="text-gray-400">
            Comprehensive insights into community health and engagement
            {lastUpdated && (
              <span className="ml-2 text-sm">
                • Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as AnalyticsTimeRange)}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          >
            Filters
          </AdminButton>
          
          <div className="flex items-center space-x-1 bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setRefreshInterval(refreshInterval === 30 ? null : 30)}
              className={`px-3 py-1 text-sm rounded ${
                refreshInterval === 30 ? 'bg-purple-600 text-white' : 'text-gray-400'
              }`}
            >
              Auto-refresh
            </button>
          </div>
          
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={refetch}
            disabled={loading}
            className={loading ? 'animate-spin' : ''}
          >
            Refresh
          </AdminButton>
          
          {canExportData && (
            <div className="relative group">
              <AdminButton
                variant="secondary"
                icon={Download}
              >
                Export
              </AdminButton>
              <div className="absolute right-0 top-full mt-2 w-32 bg-gray-800 border border-gray-700 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                <button
                  onClick={() => handleExport('csv')}
                  className="w-full px-3 py-2 text-left text-white hover:bg-gray-700 rounded-t-lg"
                >
                  CSV
                </button>
                <button
                  onClick={() => handleExport('pdf')}
                  className="w-full px-3 py-2 text-left text-white hover:bg-gray-700"
                >
                  PDF
                </button>
                <button
                  onClick={() => handleExport('json')}
                  className="w-full px-3 py-2 text-left text-white hover:bg-gray-700 rounded-b-lg"
                >
                  JSON
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <AdminCard className="p-4">
            <h3 className="text-lg font-semibold text-white mb-4">Advanced Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Metrics to Display
                </label>
                <div className="space-y-2">
                  {[
                    { id: 'user_growth', label: 'User Growth' },
                    { id: 'content_creation', label: 'Content Creation' },
                    { id: 'engagement_rate', label: 'Engagement Rate' },
                    { id: 'community_health', label: 'Community Health' }
                  ].map((metric) => (
                    <label key={metric.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedMetrics.includes(metric.id as AnalyticsMetric)}
                        onChange={() => handleMetricToggle(metric.id as AnalyticsMetric)}
                        className="rounded border-gray-600 text-purple-600 focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-300">{metric.label}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Content Types
                </label>
                <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                  <option value="all">All Types</option>
                  <option value="discussions">Discussions</option>
                  <option value="submissions">Submissions</option>
                  <option value="challenges">Challenges</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  User Segments
                </label>
                <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                  <option value="all">All Users</option>
                  <option value="new">New Users</option>
                  <option value="active">Active Users</option>
                  <option value="power">Power Users</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Comparison
                </label>
                <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                  <option value="none">No Comparison</option>
                  <option value="previous">Previous Period</option>
                  <option value="year_ago">Year Ago</option>
                </select>
              </div>
            </div>
          </AdminCard>
        </motion.div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {keyMetrics.map((metric, index) => (
          <CommunityStatsCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            color={metric.color}
            trend={metric.trend}
            subtitle={metric.subtitle}
          />
        ))}
      </div>

      {/* Main Analytics Content */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Primary Charts */}
        <div className="xl:col-span-2 space-y-6">
          {/* Growth Trends */}
          <AdminCard className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Growth Trends</h3>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-400">Users</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-gray-400">Content</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-400">Engagement</span>
                </div>
              </div>
            </div>
            <AnalyticsChart
              data={analyticsData?.chartData?.growthTrends || []}
              type="line"
              height={300}
            />
          </AdminCard>

          {/* Content Performance */}
          <AdminCard className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Content Performance</h3>
              <AdminButton variant="secondary" size="sm">
                View Details
              </AdminButton>
            </div>
            <ContentPerformanceGrid data={analyticsData?.contentPerformance || []} />
          </AdminCard>
        </div>

        {/* Sidebar Analytics */}
        <div className="space-y-6">
          {/* User Engagement Matrix */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">User Engagement</h3>
            <UserEngagementMatrix data={analyticsData?.userEngagement || {}} />
          </AdminCard>

          {/* Top Performers */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Top Performers</h3>
            <div className="space-y-4">
              {analyticsData?.topPerformers?.map((performer, index) => (
                <div key={performer.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <p className="text-white font-medium">{performer.name}</p>
                      <p className="text-sm text-gray-400">{performer.type}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">{performer.score}</p>
                    <p className="text-sm text-gray-400">{performer.metric}</p>
                  </div>
                </div>
              )) || (
                <div className="text-center py-4 text-gray-400">
                  <Award className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No performance data available</p>
                </div>
              )}
            </div>
          </AdminCard>

          {/* Quick Insights */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Quick Insights</h3>
            <div className="space-y-3">
              {analyticsData?.insights?.map((insight, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    insight.type === 'positive' ? 'bg-green-400' :
                    insight.type === 'negative' ? 'bg-red-400' :
                    'bg-yellow-400'
                  }`} />
                  <div>
                    <p className="text-white text-sm">{insight.title}</p>
                    <p className="text-gray-400 text-xs">{insight.description}</p>
                  </div>
                </div>
              )) || (
                <div className="text-center py-4 text-gray-400">
                  <Zap className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No insights available</p>
                </div>
              )}
            </div>
          </AdminCard>
        </div>
      </div>
    </div>
  );
};

export default CommunityAnalyticsDashboard;
