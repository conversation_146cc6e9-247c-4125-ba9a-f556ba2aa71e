/**
 * Content Performance Grid Component
 *
 * Interactive grid displaying content performance metrics across different content types.
 * Provides detailed analytics for discussions, submissions, challenges, and comments.
 *
 * Features:
 * - Performance metrics visualization with sortable columns
 * - Content type filtering and categorization
 * - Interactive performance indicators and trends
 * - Detailed hover tooltips with additional metrics
 * - Export functionality for performance data
 * - Responsive grid layout with mobile optimization
 * - Color-coded performance levels
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp,
  TrendingDown,
  Eye,
  Heart,
  MessageSquare,
  Share2,
  Trophy,
  Upload,
  Users,
  Clock,
  ArrowUpDown,
  Filter,
  ExternalLink
} from 'lucide-react';

interface ContentPerformanceItem {
  id: string;
  title: string;
  type: 'discussion' | 'submission' | 'challenge' | 'comment';
  views: number;
  engagement: number;
  score: number;
  author?: string;
  createdAt?: Date;
  likes?: number;
  comments?: number;
  shares?: number;
  trend?: number;
}

interface ContentPerformanceGridProps {
  data: ContentPerformanceItem[];
  className?: string;
}

type SortField = 'title' | 'type' | 'views' | 'engagement' | 'score' | 'createdAt';
type SortDirection = 'asc' | 'desc';

export const ContentPerformanceGrid: React.FC<ContentPerformanceGridProps> = ({
  data,
  className = ''
}) => {
  const [sortField, setSortField] = useState<SortField>('score');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filterType, setFilterType] = useState<string>('all');
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Sort and filter data
  const processedData = useMemo(() => {
    let filtered = data;
    
    // Apply type filter
    if (filterType !== 'all') {
      filtered = data.filter(item => item.type === filterType);
    }
    
    // Apply sorting
    return filtered.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];
      
      // Handle date sorting
      if (sortField === 'createdAt') {
        aValue = aValue?.getTime() || 0;
        bValue = bValue?.getTime() || 0;
      }
      
      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [data, sortField, sortDirection, filterType]);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Get content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'discussion': return MessageSquare;
      case 'submission': return Upload;
      case 'challenge': return Trophy;
      case 'comment': return MessageSquare;
      default: return MessageSquare;
    }
  };

  // Get content type color
  const getContentTypeColor = (type: string) => {
    switch (type) {
      case 'discussion': return 'bg-blue-500';
      case 'submission': return 'bg-purple-500';
      case 'challenge': return 'bg-yellow-500';
      case 'comment': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  // Get performance color based on score
  const getPerformanceColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    if (score >= 40) return 'text-orange-400';
    return 'text-red-400';
  };

  // Get engagement level
  const getEngagementLevel = (engagement: number) => {
    if (engagement >= 0.8) return { label: 'Excellent', color: 'text-green-400' };
    if (engagement >= 0.6) return { label: 'Good', color: 'text-blue-400' };
    if (engagement >= 0.4) return { label: 'Average', color: 'text-yellow-400' };
    if (engagement >= 0.2) return { label: 'Poor', color: 'text-orange-400' };
    return { label: 'Very Poor', color: 'text-red-400' };
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="w-3 h-3 text-gray-500" />;
    }
    return sortDirection === 'asc' ? 
      <TrendingUp className="w-3 h-3 text-purple-400" /> : 
      <TrendingDown className="w-3 h-3 text-purple-400" />;
  };

  if (!data.length) {
    return (
      <div className={`flex items-center justify-center h-64 bg-gray-800 rounded-lg ${className}`}>
        <div className="text-center">
          <div className="w-12 h-12 bg-gray-700 rounded-lg mx-auto mb-3 flex items-center justify-center">
            <Trophy className="w-6 h-6 text-gray-500" />
          </div>
          <p className="text-gray-400">No content performance data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-3 py-1 bg-gray-800 border border-gray-700 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Types</option>
            <option value="discussion">Discussions</option>
            <option value="submission">Submissions</option>
            <option value="challenge">Challenges</option>
            <option value="comment">Comments</option>
          </select>
        </div>
        
        <div className="text-sm text-gray-400">
          {processedData.length} items
        </div>
      </div>

      {/* Performance Grid */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        {/* Header */}
        <div className="grid grid-cols-12 gap-4 p-4 bg-gray-700 border-b border-gray-600">
          <button
            onClick={() => handleSort('title')}
            className="col-span-4 flex items-center space-x-2 text-left text-sm font-medium text-gray-300 hover:text-white"
          >
            <span>Content</span>
            {renderSortIcon('title')}
          </button>
          
          <button
            onClick={() => handleSort('type')}
            className="col-span-1 flex items-center space-x-2 text-left text-sm font-medium text-gray-300 hover:text-white"
          >
            <span>Type</span>
            {renderSortIcon('type')}
          </button>
          
          <button
            onClick={() => handleSort('views')}
            className="col-span-2 flex items-center space-x-2 text-center text-sm font-medium text-gray-300 hover:text-white"
          >
            <span>Views</span>
            {renderSortIcon('views')}
          </button>
          
          <button
            onClick={() => handleSort('engagement')}
            className="col-span-2 flex items-center space-x-2 text-center text-sm font-medium text-gray-300 hover:text-white"
          >
            <span>Engagement</span>
            {renderSortIcon('engagement')}
          </button>
          
          <button
            onClick={() => handleSort('score')}
            className="col-span-2 flex items-center space-x-2 text-center text-sm font-medium text-gray-300 hover:text-white"
          >
            <span>Score</span>
            {renderSortIcon('score')}
          </button>
          
          <div className="col-span-1 text-center text-sm font-medium text-gray-300">
            Actions
          </div>
        </div>

        {/* Content Rows */}
        <div className="divide-y divide-gray-700">
          {processedData.map((item, index) => {
            const ContentIcon = getContentTypeIcon(item.type);
            const engagementLevel = getEngagementLevel(item.engagement);
            
            return (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className={`grid grid-cols-12 gap-4 p-4 hover:bg-gray-700 transition-colors ${
                  hoveredItem === item.id ? 'bg-gray-700' : ''
                }`}
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                {/* Content Info */}
                <div className="col-span-4 flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getContentTypeColor(item.type)}`}>
                    <ContentIcon className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium truncate">{item.title}</p>
                    {item.author && (
                      <p className="text-xs text-gray-400">by {item.author}</p>
                    )}
                  </div>
                </div>

                {/* Type */}
                <div className="col-span-1 flex items-center">
                  <span className="text-sm text-gray-300 capitalize">{item.type}</span>
                </div>

                {/* Views */}
                <div className="col-span-2 flex items-center justify-center">
                  <div className="flex items-center space-x-1">
                    <Eye className="w-3 h-3 text-gray-400" />
                    <span className="text-sm text-white">{item.views.toLocaleString()}</span>
                  </div>
                </div>

                {/* Engagement */}
                <div className="col-span-2 flex items-center justify-center">
                  <div className="text-center">
                    <div className={`text-sm font-medium ${engagementLevel.color}`}>
                      {Math.round(item.engagement * 100)}%
                    </div>
                    <div className="text-xs text-gray-500">{engagementLevel.label}</div>
                  </div>
                </div>

                {/* Score */}
                <div className="col-span-2 flex items-center justify-center">
                  <div className="text-center">
                    <div className={`text-sm font-medium ${getPerformanceColor(item.score)}`}>
                      {item.score}
                    </div>
                    <div className="w-16 bg-gray-700 rounded-full h-1 mt-1">
                      <div
                        className={`h-1 rounded-full ${
                          item.score >= 80 ? 'bg-green-500' :
                          item.score >= 60 ? 'bg-yellow-500' :
                          item.score >= 40 ? 'bg-orange-500' :
                          'bg-red-500'
                        }`}
                        style={{ width: `${item.score}%` }}
                      />
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="col-span-1 flex items-center justify-center">
                  <button
                    className="p-1 text-gray-400 hover:text-white transition-colors"
                    title="View Details"
                  >
                    <ExternalLink className="w-3 h-3" />
                  </button>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Eye className="w-4 h-4 text-blue-400" />
            <span className="text-sm text-gray-400">Total Views</span>
          </div>
          <p className="text-xl font-bold text-white">
            {processedData.reduce((sum, item) => sum + item.views, 0).toLocaleString()}
          </p>
        </div>

        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Heart className="w-4 h-4 text-red-400" />
            <span className="text-sm text-gray-400">Avg Engagement</span>
          </div>
          <p className="text-xl font-bold text-white">
            {Math.round((processedData.reduce((sum, item) => sum + item.engagement, 0) / processedData.length) * 100)}%
          </p>
        </div>

        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Trophy className="w-4 h-4 text-yellow-400" />
            <span className="text-sm text-gray-400">Avg Score</span>
          </div>
          <p className="text-xl font-bold text-white">
            {Math.round(processedData.reduce((sum, item) => sum + item.score, 0) / processedData.length)}
          </p>
        </div>

        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <TrendingUp className="w-4 h-4 text-green-400" />
            <span className="text-sm text-gray-400">Top Performers</span>
          </div>
          <p className="text-xl font-bold text-white">
            {processedData.filter(item => item.score >= 80).length}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ContentPerformanceGrid;
