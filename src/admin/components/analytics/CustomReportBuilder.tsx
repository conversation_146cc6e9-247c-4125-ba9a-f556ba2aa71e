/**
 * Custom Report Builder Component
 * 
 * Provides a drag-and-drop interface for creating custom analytics reports
 * with configurable metrics, visualizations, filters, and scheduling.
 * 
 * Features:
 * - Metric selection and configuration
 * - Chart type selection
 * - Advanced filtering options
 * - Time range configuration
 * - Report scheduling
 * - Export options
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Plus, 
  X, 
  Bar<PERSON>hart3, 
  LineChart, 
  PieChart, 
  Calendar,
  Filter,
  Download,
  Save,
  Play,
  Settings
} from 'lucide-react'

interface Metric {
  id: string
  label: string
  description: string
  category: string
  dataType: 'number' | 'currency' | 'percentage' | 'count'
}

interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'doughnut'
  title: string
  metrics: string[]
  timeRange: string
  filters: FilterConfig[]
}

interface FilterConfig {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in'
  value: any
}

interface CustomReportBuilderProps {
  onSave: (report: any) => void
  onCancel: () => void
  existingReport?: any
}

export const CustomReportBuilder: React.FC<CustomReportBuilderProps> = ({
  onSave,
  onCancel,
  existingReport
}) => {
  const [reportName, setReportName] = useState(existingReport?.name || '')
  const [reportDescription, setReportDescription] = useState(existingReport?.description || '')
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(existingReport?.metrics || [])
  const [chartConfigs, setChartConfigs] = useState<ChartConfig[]>(existingReport?.charts || [])
  const [timeRange, setTimeRange] = useState(existingReport?.timeRange || '30d')
  const [filters, setFilters] = useState<FilterConfig[]>(existingReport?.filters || [])
  const [isScheduled, setIsScheduled] = useState(existingReport?.isScheduled || false)
  const [scheduleFrequency, setScheduleFrequency] = useState(existingReport?.scheduleFrequency || 'weekly')

  const availableMetrics: Metric[] = [
    { id: 'total_revenue', label: 'Total Revenue', description: 'Sum of all order values', category: 'Revenue', dataType: 'currency' },
    { id: 'total_orders', label: 'Total Orders', description: 'Number of completed orders', category: 'Orders', dataType: 'count' },
    { id: 'total_users', label: 'Total Users', description: 'Number of registered users', category: 'Users', dataType: 'count' },
    { id: 'avg_order_value', label: 'Average Order Value', description: 'Average value per order', category: 'Revenue', dataType: 'currency' },
    { id: 'conversion_rate', label: 'Conversion Rate', description: 'Percentage of visitors who purchase', category: 'Performance', dataType: 'percentage' },
    { id: 'cart_abandonment', label: 'Cart Abandonment', description: 'Percentage of abandoned carts', category: 'Performance', dataType: 'percentage' },
    { id: 'customer_lifetime_value', label: 'Customer LTV', description: 'Average customer lifetime value', category: 'Users', dataType: 'currency' },
    { id: 'product_views', label: 'Product Views', description: 'Number of product page views', category: 'Products', dataType: 'count' }
  ]

  const chartTypes = [
    { id: 'line', label: 'Line Chart', icon: LineChart, description: 'Best for trends over time' },
    { id: 'bar', label: 'Bar Chart', icon: BarChart3, description: 'Best for comparing categories' },
    { id: 'pie', label: 'Pie Chart', icon: PieChart, description: 'Best for showing proportions' },
    { id: 'area', label: 'Area Chart', icon: LineChart, description: 'Best for cumulative data' }
  ]

  const timeRanges = [
    { id: '1h', label: 'Last Hour' },
    { id: '24h', label: 'Last 24 Hours' },
    { id: '7d', label: 'Last 7 Days' },
    { id: '30d', label: 'Last 30 Days' },
    { id: '90d', label: 'Last 90 Days' },
    { id: '1y', label: 'Last Year' },
    { id: 'custom', label: 'Custom Range' }
  ]

  const addMetric = (metricId: string) => {
    if (!selectedMetrics.includes(metricId)) {
      setSelectedMetrics([...selectedMetrics, metricId])
    }
  }

  const removeMetric = (metricId: string) => {
    setSelectedMetrics(selectedMetrics.filter(id => id !== metricId))
  }

  const addChart = () => {
    const newChart: ChartConfig = {
      type: 'line',
      title: `Chart ${chartConfigs.length + 1}`,
      metrics: selectedMetrics.slice(0, 3), // Limit to 3 metrics per chart
      timeRange,
      filters: []
    }
    setChartConfigs([...chartConfigs, newChart])
  }

  const updateChart = (index: number, updates: Partial<ChartConfig>) => {
    const updatedCharts = chartConfigs.map((chart, i) => 
      i === index ? { ...chart, ...updates } : chart
    )
    setChartConfigs(updatedCharts)
  }

  const removeChart = (index: number) => {
    setChartConfigs(chartConfigs.filter((_, i) => i !== index))
  }

  const addFilter = () => {
    const newFilter: FilterConfig = {
      field: 'category',
      operator: 'equals',
      value: ''
    }
    setFilters([...filters, newFilter])
  }

  const updateFilter = (index: number, updates: Partial<FilterConfig>) => {
    const updatedFilters = filters.map((filter, i) => 
      i === index ? { ...filter, ...updates } : filter
    )
    setFilters(updatedFilters)
  }

  const removeFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index))
  }

  const handleSave = () => {
    const report = {
      name: reportName,
      description: reportDescription,
      metrics: selectedMetrics,
      charts: chartConfigs,
      timeRange,
      filters,
      isScheduled,
      scheduleFrequency: isScheduled ? scheduleFrequency : null,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    onSave(report)
  }

  const getMetricsByCategory = () => {
    const categories: Record<string, Metric[]> = {}
    availableMetrics.forEach(metric => {
      if (!categories[metric.category]) {
        categories[metric.category] = []
      }
      categories[metric.category].push(metric)
    })
    return categories
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-gray-800 p-6 rounded-lg">
        <h2 className="text-2xl font-bold text-white mb-4">Custom Report Builder</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Report Name</label>
            <input
              type="text"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              placeholder="Enter report name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
            <input
              type="text"
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none"
              placeholder="Enter report description"
            />
          </div>
        </div>
      </div>

      {/* Metrics Selection */}
      <div className="bg-gray-800 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-4">Select Metrics</h3>
        
        {/* Selected Metrics */}
        {selectedMetrics.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Selected Metrics</h4>
            <div className="flex flex-wrap gap-2">
              {selectedMetrics.map(metricId => {
                const metric = availableMetrics.find(m => m.id === metricId)
                return metric ? (
                  <span
                    key={metricId}
                    className="inline-flex items-center px-3 py-1 bg-purple-600 text-white text-sm rounded-full"
                  >
                    {metric.label}
                    <button
                      onClick={() => removeMetric(metricId)}
                      className="ml-2 text-purple-200 hover:text-white"
                    >
                      <X size={14} />
                    </button>
                  </span>
                ) : null
              })}
            </div>
          </div>
        )}

        {/* Available Metrics by Category */}
        <div className="space-y-4">
          {Object.entries(getMetricsByCategory()).map(([category, metrics]) => (
            <div key={category}>
              <h4 className="text-sm font-medium text-gray-300 mb-2">{category}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {metrics.map(metric => (
                  <button
                    key={metric.id}
                    onClick={() => addMetric(metric.id)}
                    disabled={selectedMetrics.includes(metric.id)}
                    className={`p-3 rounded-lg text-left transition-colors ${
                      selectedMetrics.includes(metric.id)
                        ? 'bg-purple-900/50 text-purple-300 cursor-not-allowed'
                        : 'bg-gray-700 hover:bg-gray-600 text-white'
                    }`}
                  >
                    <div className="font-medium">{metric.label}</div>
                    <div className="text-xs text-gray-400 mt-1">{metric.description}</div>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chart Configuration */}
      <div className="bg-gray-800 p-6 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Chart Configuration</h3>
          <button
            onClick={addChart}
            disabled={selectedMetrics.length === 0}
            className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Add Chart
          </button>
        </div>

        <div className="space-y-4">
          {chartConfigs.map((chart, index) => (
            <div key={index} className="bg-gray-700 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <input
                  type="text"
                  value={chart.title}
                  onChange={(e) => updateChart(index, { title: e.target.value })}
                  className="bg-gray-600 text-white px-3 py-1 rounded border border-gray-500 focus:border-purple-500 focus:outline-none"
                />
                <button
                  onClick={() => removeChart(index)}
                  className="text-red-400 hover:text-red-300"
                >
                  <X size={16} />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Chart Type</label>
                  <select
                    value={chart.type}
                    onChange={(e) => updateChart(index, { type: e.target.value as any })}
                    className="w-full px-3 py-2 bg-gray-600 text-white rounded border border-gray-500 focus:border-purple-500 focus:outline-none"
                  >
                    {chartTypes.map(type => (
                      <option key={type.id} value={type.id}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Time Range</label>
                  <select
                    value={chart.timeRange}
                    onChange={(e) => updateChart(index, { timeRange: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-600 text-white rounded border border-gray-500 focus:border-purple-500 focus:outline-none"
                  >
                    {timeRanges.map(range => (
                      <option key={range.id} value={range.id}>{range.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Metrics</label>
                  <div className="text-sm text-gray-400">
                    {chart.metrics.length} metric(s) selected
                  </div>
                </div>
              </div>
            </div>
          ))}

          {chartConfigs.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              <BarChart3 className="mx-auto h-12 w-12 mb-4" />
              <p>No charts configured. Add a chart to get started.</p>
            </div>
          )}
        </div>
      </div>

      {/* Scheduling */}
      <div className="bg-gray-800 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-4">Report Scheduling</h3>
        <div className="space-y-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={isScheduled}
              onChange={(e) => setIsScheduled(e.target.checked)}
              className="mr-3"
            />
            <span className="text-white">Schedule this report to run automatically</span>
          </label>

          {isScheduled && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Frequency</label>
              <select
                value={scheduleFrequency}
                onChange={(e) => setScheduleFrequency(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-purple-500 focus:outline-none"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={onCancel}
          className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          disabled={!reportName || selectedMetrics.length === 0}
          className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center"
        >
          <Save size={16} className="mr-2" />
          Save Report
        </button>
      </div>
    </div>
  )
}

export default CustomReportBuilder
