/**
 * Predictive Analytics Dashboard Component
 * 
 * Advanced dashboard for machine learning-powered predictions,
 * forecasting, and business intelligence insights.
 * 
 * Features:
 * - Churn prediction and risk assessment
 * - Lifetime value forecasting
 * - Engagement trend predictions
 * - Business metric forecasting
 * - Anomaly detection alerts
 * - Personalized recommendations
 * - Model performance monitoring
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 1 Enhancement
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Users,
  DollarSign,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Zap,
  BarChart3,
  LineChart,
  PieChart,
  ArrowUp,
  ArrowDown,
  Minus,
  RefreshCw,
  Settings,
  Download,
  Star,
  Shield,
  Lightbulb
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'
import PredictiveAnalyticsEngine, {
  ChurnPrediction,
  LifetimeValuePrediction,
  EngagementForecast,
  BusinessForecast,
  AnomalyDetection,
  RecommendationEngine,
  PredictionModel
} from '../../lib/analytics/PredictiveAnalyticsEngine'

interface PredictiveAnalyticsDashboardProps {
  timeframe?: '7d' | '30d' | '90d'
  autoRefresh?: boolean
}

/**
 * Predictive Analytics Dashboard Component
 */
const PredictiveAnalyticsDashboard: React.FC<PredictiveAnalyticsDashboardProps> = ({
  timeframe = '30d',
  autoRefresh = true
}) => {
  // ===== STATE =====
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'churn' | 'ltv' | 'forecasts' | 'anomalies' | 'models'>('overview')
  const [churnPredictions, setChurnPredictions] = useState<ChurnPrediction[]>([])
  const [ltvPredictions, setLtvPredictions] = useState<LifetimeValuePrediction[]>([])
  const [engagementForecast, setEngagementForecast] = useState<EngagementForecast | null>(null)
  const [businessForecasts, setBusinessForecasts] = useState<BusinessForecast[]>([])
  const [anomalies, setAnomalies] = useState<AnomalyDetection[]>([])
  const [modelPerformance, setModelPerformance] = useState<PredictionModel[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [recommendations, setRecommendations] = useState<RecommendationEngine | null>(null)

  const predictiveEngine = PredictiveAnalyticsEngine.getInstance()

  // ===== EFFECTS =====

  useEffect(() => {
    initializeEngine()
    loadPredictiveData()

    if (autoRefresh) {
      const interval = setInterval(loadPredictiveData, 5 * 60 * 1000) // Refresh every 5 minutes
      return () => clearInterval(interval)
    }
  }, [timeframe, autoRefresh])

  // ===== HANDLERS =====

  const initializeEngine = async () => {
    await predictiveEngine.initializeModels()
  }

  const loadPredictiveData = async () => {
    setLoading(true)
    try {
      const [
        churnData,
        ltvData,
        engagementData,
        businessData,
        anomalyData,
        modelData
      ] = await Promise.all([
        predictiveEngine.predictUserChurn(),
        predictiveEngine.predictLifetimeValue(),
        predictiveEngine.forecastEngagement('daily', 30),
        predictiveEngine.generateBusinessForecasts(),
        predictiveEngine.detectAnomalies(),
        Promise.resolve(predictiveEngine.getModelPerformance())
      ])

      setChurnPredictions(churnData)
      setLtvPredictions(ltvData)
      setEngagementForecast(engagementData)
      setBusinessForecasts(businessData)
      setAnomalies(anomalyData)
      setModelPerformance(modelData)
    } catch (error) {
      console.error('Error loading predictive data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRetrainModels = async () => {
    setLoading(true)
    try {
      await predictiveEngine.retrainModels()
      await loadPredictiveData()
    } catch (error) {
      console.error('Error retraining models:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUserSelect = async (userId: string) => {
    setSelectedUserId(userId)
    try {
      const userRecommendations = await predictiveEngine.generateRecommendations(userId)
      setRecommendations(userRecommendations)
    } catch (error) {
      console.error('Error loading user recommendations:', error)
    }
  }

  const handleExportPredictions = (format: 'csv' | 'json') => {
    console.log(`Exporting predictions in ${format} format`)
  }

  // ===== HELPER FUNCTIONS =====

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'text-red-400 bg-red-500/20 border-red-500/30'
      case 'high': return 'text-orange-400 bg-orange-500/20 border-orange-500/30'
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
      case 'low': return 'text-green-400 bg-green-500/20 border-green-500/30'
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30'
    }
  }

  const getValueSegmentColor = (segment: string) => {
    switch (segment) {
      case 'premium': return 'text-purple-400'
      case 'high': return 'text-blue-400'
      case 'medium': return 'text-green-400'
      case 'low': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  const getModelStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'training': return <Clock className="w-4 h-4 text-yellow-400" />
      case 'deprecated': return <XCircle className="w-4 h-4 text-red-400" />
      default: return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  // ===== RENDER =====

  return (
    <div className="predictive-analytics-dashboard space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h3 className="text-xl font-bold text-white mb-2 flex items-center space-x-2">
            <Brain className="w-6 h-6 text-purple-400" />
            <span>Predictive Analytics Intelligence</span>
          </h3>
          <p className="text-gray-400">
            AI-powered predictions, forecasting, and business optimization insights
          </p>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={loadPredictiveData}
            loading={loading}
          >
            Refresh
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={Zap}
            onClick={handleRetrainModels}
            loading={loading}
          >
            Retrain Models
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={Download}
            onClick={() => handleExportPredictions('csv')}
          >
            Export
          </AdminButton>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
            <span className="text-red-400 text-sm font-medium">Critical</span>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white mb-1">
              {churnPredictions.filter(p => p.riskLevel === 'critical').length}
            </h3>
            <p className="text-gray-400 text-sm">High Churn Risk Users</p>
          </div>
        </motion.div>

        <motion.div
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-400" />
            </div>
            <ArrowUp className="w-4 h-4 text-green-400" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white mb-1">
              {formatCurrency(ltvPredictions.reduce((sum, p) => sum + p.predictedLTV, 0) / ltvPredictions.length || 0)}
            </h3>
            <p className="text-gray-400 text-sm">Avg Predicted LTV</p>
          </div>
        </motion.div>

        <motion.div
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-blue-400" />
            </div>
            <span className="text-blue-400 text-sm font-medium">
              {engagementForecast?.trendDirection === 'increasing' ? '↗' : 
               engagementForecast?.trendDirection === 'decreasing' ? '↘' : '→'}
            </span>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white mb-1">
              {engagementForecast?.periods[0]?.predictedEngagement.toFixed(1) || '0.0'}%
            </h3>
            <p className="text-gray-400 text-sm">Next Period Engagement</p>
          </div>
        </motion.div>

        <motion.div
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-yellow-400" />
            </div>
            <span className="text-yellow-400 text-sm font-medium">Active</span>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white mb-1">
              {modelPerformance.filter(m => m.status === 'active').length}
            </h3>
            <p className="text-gray-400 text-sm">Active ML Models</p>
          </div>
        </motion.div>
      </div>

      {/* Anomaly Alerts */}
      {anomalies.filter(a => !a.isResolved && a.severity !== 'low').length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <AdminCard title="🚨 Anomaly Alerts" subtitle="Critical anomalies requiring attention">
            <div className="space-y-3">
              {anomalies
                .filter(a => !a.isResolved && a.severity !== 'low')
                .slice(0, 3)
                .map((anomaly) => (
                  <div
                    key={anomaly.id}
                    className={`p-4 rounded-lg border ${
                      anomaly.severity === 'critical' ? 'bg-red-500/10 border-red-500/30' :
                      anomaly.severity === 'high' ? 'bg-orange-500/10 border-orange-500/30' :
                      'bg-yellow-500/10 border-yellow-500/30'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <AlertTriangle className={`w-4 h-4 ${
                            anomaly.severity === 'critical' ? 'text-red-400' :
                            anomaly.severity === 'high' ? 'text-orange-400' :
                            'text-yellow-400'
                          }`} />
                          <h5 className="text-white font-medium">{anomaly.metric}</h5>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            anomaly.severity === 'critical' ? 'bg-red-500/20 text-red-400' :
                            anomaly.severity === 'high' ? 'bg-orange-500/20 text-orange-400' :
                            'bg-yellow-500/20 text-yellow-400'
                          }`}>
                            {anomaly.severity.toUpperCase()}
                          </span>
                        </div>
                        <p className="text-gray-400 text-sm mb-2">{anomaly.description}</p>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="text-gray-500">
                            Expected: {anomaly.expectedValue.toFixed(1)}
                          </span>
                          <span className="text-gray-500">
                            Actual: {anomaly.actualValue.toFixed(1)}
                          </span>
                          <span className="text-gray-500">
                            Deviation: {anomaly.deviationScore.toFixed(1)}σ
                          </span>
                        </div>
                      </div>
                      <div className="text-right text-sm text-gray-400">
                        {anomaly.timestamp.toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </AdminCard>
        </motion.div>
      )}

      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-1 bg-gray-800 p-1 rounded-lg">
        {[
          { id: 'overview', label: 'Overview', icon: Eye },
          { id: 'churn', label: 'Churn Risk', icon: AlertTriangle },
          { id: 'ltv', label: 'Lifetime Value', icon: DollarSign },
          { id: 'forecasts', label: 'Forecasts', icon: TrendingUp },
          { id: 'anomalies', label: 'Anomalies', icon: Shield },
          { id: 'models', label: 'Models', icon: Brain }
        ].map(tab => {
          const IconComponent = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as typeof activeTab)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <IconComponent className="w-4 h-4" />
              <span className="text-sm">{tab.label}</span>
            </button>
          )
        })}
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'churn' && (
          <motion.div
            key="churn"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <AdminCard title="🎯 Churn Risk Analysis" subtitle="Users at risk of churning with AI-powered predictions">
              <div className="space-y-4">
                {churnPredictions
                  .sort((a, b) => b.churnProbability - a.churnProbability)
                  .slice(0, 10)
                  .map((prediction) => (
                    <div
                      key={prediction.userId}
                      className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors cursor-pointer"
                      onClick={() => handleUserSelect(prediction.userId)}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className={`w-3 h-3 rounded-full ${
                            prediction.riskLevel === 'critical' ? 'bg-red-400' :
                            prediction.riskLevel === 'high' ? 'bg-orange-400' :
                            prediction.riskLevel === 'medium' ? 'bg-yellow-400' :
                            'bg-green-400'
                          }`} />
                        </div>
                        <div>
                          <h5 className="text-white font-medium">{prediction.userName}</h5>
                          <p className="text-gray-400 text-sm">
                            {formatPercentage(prediction.churnProbability)} churn probability
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-6 text-sm">
                        <div className="text-center">
                          <p className={`font-medium ${
                            prediction.riskLevel === 'critical' ? 'text-red-400' :
                            prediction.riskLevel === 'high' ? 'text-orange-400' :
                            prediction.riskLevel === 'medium' ? 'text-yellow-400' :
                            'text-green-400'
                          }`}>
                            {prediction.riskLevel.toUpperCase()}
                          </p>
                          <p className="text-gray-400">Risk Level</p>
                        </div>
                        <div className="text-center">
                          <p className="text-white font-medium">{prediction.timeToChurn}d</p>
                          <p className="text-gray-400">Time to Churn</p>
                        </div>
                        <div className="text-center">
                          <p className="text-blue-400 font-medium">{formatPercentage(prediction.confidence)}</p>
                          <p className="text-gray-400">Confidence</p>
                        </div>
                        <div className="text-center">
                          <p className="text-purple-400 font-medium">{prediction.recommendedActions.length}</p>
                          <p className="text-gray-400">Actions</p>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </AdminCard>
          </motion.div>
        )}

        {activeTab === 'ltv' && (
          <motion.div
            key="ltv"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
          >
            {ltvPredictions.map((prediction) => (
              <AdminCard
                key={prediction.userId}
                title={`💰 ${prediction.userName}`}
                subtitle="Lifetime Value Prediction"
              >
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-400 text-sm">Current LTV</p>
                      <p className="text-white text-xl font-bold">
                        {formatCurrency(prediction.currentLTV)}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Predicted LTV</p>
                      <p className={`text-xl font-bold ${getValueSegmentColor(prediction.valueSegment)}`}>
                        {formatCurrency(prediction.predictedLTV)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">6 Month</span>
                      <span className="text-white">{formatCurrency(prediction.ltv6Month)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">12 Month</span>
                      <span className="text-white">{formatCurrency(prediction.ltv12Month)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">24 Month</span>
                      <span className="text-white">{formatCurrency(prediction.ltv24Month)}</span>
                    </div>
                  </div>

                  <div className="pt-3 border-t border-gray-700">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-gray-400 text-sm">Growth Potential</span>
                      <span className="text-green-400 font-medium">{prediction.growthPotential}%</span>
                    </div>
                    <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-green-500 transition-all duration-500"
                        style={{ width: `${Math.min(100, prediction.growthPotential)}%` }}
                      />
                    </div>
                  </div>
                </div>
              </AdminCard>
            ))}
          </motion.div>
        )}

        {activeTab === 'models' && (
          <motion.div
            key="models"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <AdminCard title="🤖 Model Performance" subtitle="Machine learning model status and metrics">
              <div className="space-y-4">
                {modelPerformance.map((model) => (
                  <div
                    key={model.id}
                    className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg border border-gray-600"
                  >
                    <div className="flex items-center space-x-4">
                      {getModelStatusIcon(model.status)}
                      <div>
                        <h5 className="text-white font-medium">{model.name}</h5>
                        <p className="text-gray-400 text-sm">{model.description}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-8 text-sm">
                      <div className="text-center">
                        <p className="text-green-400 font-bold">{formatPercentage(model.accuracy)}</p>
                        <p className="text-gray-400">Accuracy</p>
                      </div>
                      <div className="text-center">
                        <p className="text-blue-400 font-bold">{formatPercentage(model.precision)}</p>
                        <p className="text-gray-400">Precision</p>
                      </div>
                      <div className="text-center">
                        <p className="text-purple-400 font-bold">{formatPercentage(model.f1Score)}</p>
                        <p className="text-gray-400">F1 Score</p>
                      </div>
                      <div className="text-center">
                        <p className="text-gray-300 text-xs">
                          {model.lastTrained.toLocaleDateString()}
                        </p>
                        <p className="text-gray-400">Last Trained</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>

      {/* User Recommendations Modal */}
      {selectedUserId && recommendations && (
        <motion.div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-bold text-white">AI Recommendations</h4>
              <button
                onClick={() => setSelectedUserId(null)}
                className="text-gray-400 hover:text-white"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-3">
              {recommendations.recommendations.map((rec, index) => (
                <div key={index} className="p-3 bg-gray-700/50 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <Lightbulb className="w-4 h-4 text-yellow-400 mt-0.5" />
                    <div>
                      <h6 className="text-white font-medium text-sm">{rec.title}</h6>
                      <p className="text-gray-400 text-xs">{rec.description}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs bg-accent-500/20 text-accent-400 px-2 py-1 rounded">
                          {rec.category}
                        </span>
                        <span className="text-xs text-gray-500">
                          Impact: +{rec.expectedImpact.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  )
}

export default PredictiveAnalyticsDashboard