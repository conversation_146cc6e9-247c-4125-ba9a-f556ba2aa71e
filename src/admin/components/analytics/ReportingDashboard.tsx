/**
 * Reporting Dashboard Component
 * 
 * Comprehensive reporting interface with template management,
 * scheduled reports, and automated distribution.
 * 
 * Features:
 * - Report template creation and management
 * - Scheduled report configuration
 * - Multi-format report generation
 * - Report sharing and distribution
 * - Real-time report status tracking
 * - Report history and analytics
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 1 Enhancement
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  FileText,
  Plus,
  Calendar,
  Download,
  Share2,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Settings,
  Filter,
  Search,
  Play,
  Pause,
  Eye,
  Mail,
  Copy,
  BarChart3,
  PieChart,
  Activity,
  TrendingUp
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'
import AutomatedReportingSystem, {
  ReportTemplate,
  ScheduledReport,
  GeneratedReport
} from '../../lib/analytics/AutomatedReportingSystem'

interface ReportingDashboardProps {
  onCreateTemplate?: () => void
  onScheduleReport?: () => void
}

/**
 * Reporting Dashboard Component
 */
const ReportingDashboard: React.FC<ReportingDashboardProps> = ({
  onCreateTemplate,
  onScheduleReport
}) => {
  // ===== STATE =====
  const [activeTab, setActiveTab] = useState<'templates' | 'scheduled' | 'generated' | 'analytics'>('templates')
  const [loading, setLoading] = useState(false)
  const [templates, setTemplates] = useState<ReportTemplate[]>([])
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([])
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null)

  const reportingSystem = AutomatedReportingSystem.getInstance()

  // ===== EFFECTS =====

  useEffect(() => {
    loadReportingData()
  }, [])

  // ===== HANDLERS =====

  const loadReportingData = async () => {
    setLoading(true)
    try {
      const [templatesData, scheduledData] = await Promise.all([
        reportingSystem.getReportTemplates(),
        reportingSystem.getScheduledReports()
      ])
      
      setTemplates(templatesData)
      setScheduledReports(scheduledData)
      setGeneratedReports(generateMockReports()) // Mock data for now
    } catch (error) {
      console.error('Error loading reporting data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateReport = async (templateId: string, format: 'pdf' | 'csv' | 'excel' | 'json' = 'pdf') => {
    try {
      const period = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        end: new Date()
      }
      
      const report = await reportingSystem.generateReport(templateId, period, {
        format,
        includeInsights: true,
        includeRecommendations: true
      })
      
      console.log('Report generated:', report)
      await loadReportingData() // Refresh data
    } catch (error) {
      console.error('Error generating report:', error)
    }
  }

  const handleScheduleToggle = async (scheduleId: string, isActive: boolean) => {
    try {
      // In real implementation, update schedule status
      console.log(`${isActive ? 'Activating' : 'Deactivating'} schedule ${scheduleId}`)
      await loadReportingData()
    } catch (error) {
      console.error('Error updating schedule:', error)
    }
  }

  const handleShareReport = async (reportId: string) => {
    try {
      await reportingSystem.shareReport(reportId, ['<EMAIL>'], 'download')
      console.log('Report shared successfully')
    } catch (error) {
      console.error('Error sharing report:', error)
    }
  }

  const handleExportReport = async (reportId: string, format: 'pdf' | 'csv' | 'excel' | 'json') => {
    try {
      const blob = await reportingSystem.exportReport(reportId, format)
      
      // Create download link
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `report_${reportId}.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting report:', error)
    }
  }

  // ===== HELPER FUNCTIONS =====

  const generateMockReports = (): GeneratedReport[] => {
    return [
      {
        id: 'report_1',
        templateId: 'exec_summary',
        title: 'Executive Summary - Dec 2024',
        period: {
          start: new Date('2024-12-01'),
          end: new Date('2024-12-31')
        },
        generatedAt: new Date(),
        generatedBy: 'system',
        format: 'pdf',
        fileSize: 245760,
        downloadUrl: '/api/reports/report_1/download',
        sharedWith: ['<EMAIL>', '<EMAIL>'],
        metrics: {
          totalUsers: 1247,
          activeUsers: 892,
          engagementRate: 73.2,
          conversionRate: 12.5,
          pointsAwarded: 156780,
          achievementsUnlocked: 3456
        },
        insights: [
          'User engagement increased by 15% this month',
          'Achievement completion rates are above average'
        ],
        recommendations: [
          'Consider expanding the rewards catalog',
          'Implement seasonal gamification events'
        ],
        status: 'completed'
      }
    ]
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'generating': return <Clock className="w-4 h-4 text-yellow-400" />
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-400" />
      default: return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'executive': return TrendingUp
      case 'operational': return BarChart3
      case 'marketing': return Users
      case 'technical': return Activity
      default: return FileText
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // ===== RENDER =====

  return (
    <div className="reporting-dashboard space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h3 className="text-xl font-bold text-white mb-2">Automated Reporting</h3>
          <p className="text-gray-400">Create, schedule, and manage automated reports</p>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            variant="primary"
            icon={Plus}
            onClick={() => setShowCreateModal(true)}
          >
            Create Template
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={Calendar}
            onClick={onScheduleReport}
          >
            Schedule Report
          </AdminButton>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search templates and reports..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-500"
          />
        </div>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
        >
          <option value="all">All Categories</option>
          <option value="executive">Executive</option>
          <option value="operational">Operational</option>
          <option value="marketing">Marketing</option>
          <option value="technical">Technical</option>
        </select>
      </div>

      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-1 bg-gray-800 p-1 rounded-lg">
        {[
          { id: 'templates', label: 'Templates', icon: FileText },
          { id: 'scheduled', label: 'Scheduled', icon: Calendar },
          { id: 'generated', label: 'Generated', icon: Download },
          { id: 'analytics', label: 'Analytics', icon: BarChart3 }
        ].map(tab => {
          const IconComponent = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as typeof activeTab)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <IconComponent className="w-4 h-4" />
              <span className="text-sm">{tab.label}</span>
            </button>
          )
        })}
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'templates' && (
          <motion.div
            key="templates"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => {
                const CategoryIcon = getCategoryIcon(template.category)
                return (
                  <motion.div
                    key={template.id}
                    className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          template.category === 'executive' ? 'bg-purple-500/20 text-purple-400' :
                          template.category === 'operational' ? 'bg-blue-500/20 text-blue-400' :
                          template.category === 'marketing' ? 'bg-green-500/20 text-green-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          <CategoryIcon className="w-4 h-4" />
                        </div>
                        <div>
                          <h4 className="text-white font-medium">{template.name}</h4>
                          <p className="text-gray-400 text-sm capitalize">{template.category}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => setSelectedTemplate(template)}
                          className="p-1 text-gray-400 hover:text-white transition-colors"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-white transition-colors">
                          <Edit className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <p className="text-gray-400 text-sm mb-4 line-clamp-2">
                      {template.description}
                    </p>

                    <div className="flex flex-wrap gap-1 mb-4">
                      {template.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-500 text-xs">
                        {template.sections.length} sections
                      </span>
                      <div className="flex items-center space-x-2">
                        <AdminButton
                          size="sm"
                          variant="secondary"
                          onClick={() => handleGenerateReport(template.id)}
                        >
                          Generate
                        </AdminButton>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        )}

        {activeTab === 'scheduled' && (
          <motion.div
            key="scheduled"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <AdminCard title="Scheduled Reports" subtitle="Automated report generation schedules">
              <div className="space-y-4">
                {scheduledReports.map((schedule) => (
                  <div
                    key={schedule.id}
                    className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg border border-gray-600"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`w-2 h-2 rounded-full ${
                        schedule.isActive ? 'bg-green-400' : 'bg-gray-400'
                      }`} />
                      <div>
                        <h5 className="text-white font-medium">{schedule.name}</h5>
                        <p className="text-gray-400 text-sm">
                          {schedule.schedule.frequency} at {schedule.schedule.time}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-gray-400 text-sm">Next Run</p>
                        <p className="text-white text-sm font-medium">
                          {schedule.nextRun.toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleScheduleToggle(schedule.id, !schedule.isActive)}
                          className={`p-2 rounded-lg transition-colors ${
                            schedule.isActive 
                              ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30' 
                              : 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30'
                          }`}
                        >
                          {schedule.isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                        <button className="p-2 bg-gray-600/50 text-gray-400 hover:text-white rounded-lg transition-colors">
                          <Settings className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>
          </motion.div>
        )}

        {activeTab === 'generated' && (
          <motion.div
            key="generated"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <AdminCard title="Generated Reports" subtitle="Recently generated reports and downloads">
              <div className="space-y-4">
                {generatedReports.map((report) => (
                  <div
                    key={report.id}
                    className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg border border-gray-600"
                  >
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(report.status)}
                      <div>
                        <h5 className="text-white font-medium">{report.title}</h5>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span>Generated: {report.generatedAt.toLocaleDateString()}</span>
                          <span>Size: {formatFileSize(report.fileSize)}</span>
                          <span className="capitalize">{report.format.toUpperCase()}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <AdminButton
                        size="sm"
                        variant="secondary"
                        icon={Download}
                        onClick={() => handleExportReport(report.id, report.format as any)}
                      >
                        Download
                      </AdminButton>
                      <AdminButton
                        size="sm"
                        variant="secondary"
                        icon={Share2}
                        onClick={() => handleShareReport(report.id)}
                      >
                        Share
                      </AdminButton>
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ReportingDashboard