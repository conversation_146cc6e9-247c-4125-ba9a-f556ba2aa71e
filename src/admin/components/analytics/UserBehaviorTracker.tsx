/**
 * User Behavior Tracker Component
 * 
 * Advanced user behavior analysis with heatmaps, journey mapping,
 * and real-time activity monitoring.
 * 
 * Features:
 * - Real-time user activity tracking
 * - Behavior pattern analysis
 * - User journey visualization
 * - Engagement scoring
 * - Churn risk identification
 * - Session replay analytics
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 1 Enhancement
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Activity,
  Eye,
  MousePointer,
  Clock,
  TrendingUp,
  TrendingDown,
  User,
  Users,
  Navigation,
  Target,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause,
  Filter,
  Download,
  Search,
  Calendar,
  Map,
  BarChart3,
  PieChart
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'

interface UserSession {
  id: string
  userId: string
  userName: string
  startTime: Date
  endTime?: Date
  duration: number
  pageViews: number
  actions: UserAction[]
  deviceType: 'desktop' | 'mobile' | 'tablet'
  location: string
  entryPoint: string
  exitPoint?: string
  engagementScore: number
  conversionEvents: string[]
}

interface UserAction {
  id: string
  type: 'click' | 'scroll' | 'hover' | 'form_submit' | 'navigation' | 'search' | 'purchase'
  timestamp: Date
  element: string
  page: string
  value?: string
  duration?: number
  coordinates?: { x: number; y: number }
}

interface BehaviorPattern {
  id: string
  name: string
  description: string
  frequency: number
  userCount: number
  avgEngagement: number
  conversionRate: number
  steps: string[]
  timeToComplete: number
}

interface UserJourney {
  userId: string
  userName: string
  journeySteps: {
    page: string
    timestamp: Date
    duration: number
    actions: number
    exitPoint: boolean
  }[]
  totalDuration: number
  conversionFunnel: {
    step: string
    reached: boolean
    timestamp?: Date
  }[]
  engagementScore: number
  churnRisk: number
}

interface BehaviorHeatmapData {
  page: string
  clicks: { x: number; y: number; intensity: number }[]
  scrollDepth: number[]
  timeSpent: number[]
  conversionAreas: { element: string; conversions: number }[]
}

/**
 * User Behavior Tracker Component
 */
const UserBehaviorTracker: React.FC = () => {
  // ===== STATE =====
  const [activeView, setActiveView] = useState<'realtime' | 'patterns' | 'journeys' | 'heatmaps'>('realtime')
  const [loading, setLoading] = useState(false)
  const [realTimeSessions, setRealTimeSessions] = useState<UserSession[]>([])
  const [behaviorPatterns, setBehaviorPatterns] = useState<BehaviorPattern[]>([])
  const [userJourneys, setUserJourneys] = useState<UserJourney[]>([])
  const [heatmapData, setHeatmapData] = useState<BehaviorHeatmapData[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h')
  const [isTracking, setIsTracking] = useState(true)

  // ===== EFFECTS =====

  useEffect(() => {
    loadBehaviorData()
    if (isTracking) {
      const interval = setInterval(loadRealTimeSessions, 5000)
      return () => clearInterval(interval)
    }
  }, [timeRange, isTracking])

  // ===== HANDLERS =====

  const loadBehaviorData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        loadRealTimeSessions(),
        loadBehaviorPatterns(),
        loadUserJourneys(),
        loadHeatmapData()
      ])
    } catch (error) {
      console.error('Error loading behavior data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadRealTimeSessions = useCallback(async () => {
    // Mock real-time sessions data
    const mockSessions: UserSession[] = [
      {
        id: 'session_1',
        userId: 'user_123',
        userName: 'ActiveUser1',
        startTime: new Date(Date.now() - 15 * 60 * 1000),
        duration: 15 * 60,
        pageViews: 8,
        actions: [],
        deviceType: 'desktop',
        location: 'San Francisco, CA',
        entryPoint: '/community/discover',
        engagementScore: 85,
        conversionEvents: ['profile_view', 'product_like']
      },
      {
        id: 'session_2',
        userId: 'user_456',
        userName: 'BrowsingUser2',
        startTime: new Date(Date.now() - 8 * 60 * 1000),
        duration: 8 * 60,
        pageViews: 12,
        actions: [],
        deviceType: 'mobile',
        location: 'New York, NY',
        entryPoint: '/products',
        engagementScore: 67,
        conversionEvents: ['search', 'filter_applied']
      },
      {
        id: 'session_3',
        userId: 'user_789',
        userName: 'PowerUser3',
        startTime: new Date(Date.now() - 25 * 60 * 1000),
        duration: 25 * 60,
        pageViews: 15,
        actions: [],
        deviceType: 'desktop',
        location: 'Austin, TX',
        entryPoint: '/admin',
        engagementScore: 95,
        conversionEvents: ['purchase', 'review_submitted', 'social_share']
      }
    ]
    setRealTimeSessions(mockSessions)
  }, [])

  const loadBehaviorPatterns = async () => {
    const mockPatterns: BehaviorPattern[] = [
      {
        id: 'pattern_1',
        name: 'Product Discovery Flow',
        description: 'Users who browse products after community engagement',
        frequency: 45.2,
        userCount: 234,
        avgEngagement: 78.5,
        conversionRate: 23.4,
        steps: ['Community Browse', 'Product View', 'Add to Cart', 'Purchase'],
        timeToComplete: 18.5
      },
      {
        id: 'pattern_2',
        name: 'Social Engagement Path',
        description: 'Users who engage heavily with community features',
        frequency: 32.8,
        userCount: 167,
        avgEngagement: 85.2,
        conversionRate: 15.7,
        steps: ['Profile Creation', 'Community Join', 'Post Creation', 'Social Interaction'],
        timeToComplete: 12.3
      },
      {
        id: 'pattern_3',
        name: 'Quick Purchase Journey',
        description: 'Users who purchase within first few visits',
        frequency: 18.6,
        userCount: 89,
        avgEngagement: 65.4,
        conversionRate: 67.8,
        steps: ['Search', 'Product View', 'Purchase'],
        timeToComplete: 5.2
      }
    ]
    setBehaviorPatterns(mockPatterns)
  }

  const loadUserJourneys = async () => {
    const mockJourneys: UserJourney[] = [
      {
        userId: 'user_123',
        userName: 'JourneyUser1',
        journeySteps: [
          {
            page: '/home',
            timestamp: new Date(Date.now() - 30 * 60 * 1000),
            duration: 120,
            actions: 3,
            exitPoint: false
          },
          {
            page: '/community/discover',
            timestamp: new Date(Date.now() - 28 * 60 * 1000),
            duration: 300,
            actions: 8,
            exitPoint: false
          },
          {
            page: '/products/keycap-set-123',
            timestamp: new Date(Date.now() - 23 * 60 * 1000),
            duration: 180,
            actions: 5,
            exitPoint: true
          }
        ],
        totalDuration: 600,
        conversionFunnel: [
          { step: 'Landing', reached: true, timestamp: new Date(Date.now() - 30 * 60 * 1000) },
          { step: 'Product View', reached: true, timestamp: new Date(Date.now() - 23 * 60 * 1000) },
          { step: 'Add to Cart', reached: false },
          { step: 'Purchase', reached: false }
        ],
        engagementScore: 72,
        churnRisk: 0.25
      }
    ]
    setUserJourneys(mockJourneys)
  }

  const loadHeatmapData = async () => {
    const mockHeatmap: BehaviorHeatmapData[] = [
      {
        page: '/community/discover',
        clicks: [
          { x: 250, y: 150, intensity: 85 },
          { x: 400, y: 200, intensity: 67 },
          { x: 180, y: 350, intensity: 92 }
        ],
        scrollDepth: [100, 85, 70, 45, 25],
        timeSpent: [30, 45, 60, 120, 90],
        conversionAreas: [
          { element: 'featured-product', conversions: 23 },
          { element: 'search-bar', conversions: 45 },
          { element: 'category-nav', conversions: 18 }
        ]
      }
    ]
    setHeatmapData(mockHeatmap)
  }

  const handleTrackingToggle = () => {
    setIsTracking(!isTracking)
  }

  const handleExportData = (format: 'csv' | 'json') => {
    console.log(`Exporting behavior data in ${format} format`)
  }

  // ===== HELPER FUNCTIONS =====

  const getEngagementColor = (score: number) => {
    if (score >= 80) return 'text-green-400'
    if (score >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile': return '📱'
      case 'tablet': return '📲'
      default: return '💻'
    }
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  // ===== RENDER =====

  return (
    <div className="user-behavior-tracker space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h3 className="text-xl font-bold text-white mb-2">User Behavior Analytics</h3>
          <p className="text-gray-400">Real-time tracking and analysis of user interactions</p>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            variant={isTracking ? 'success' : 'secondary'}
            icon={isTracking ? Pause : Play}
            onClick={handleTrackingToggle}
          >
            {isTracking ? 'Pause Tracking' : 'Start Tracking'}
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={Download}
            onClick={() => handleExportData('csv')}
          >
            Export
          </AdminButton>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as typeof timeRange)}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white text-sm focus:outline-none focus:border-accent-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      {/* View Tabs */}
      <div className="flex flex-wrap gap-1 bg-gray-800 p-1 rounded-lg">
        {[
          { id: 'realtime', label: 'Real-time Sessions', icon: Activity },
          { id: 'patterns', label: 'Behavior Patterns', icon: TrendingUp },
          { id: 'journeys', label: 'User Journeys', icon: Map },
          { id: 'heatmaps', label: 'Heatmaps', icon: Target }
        ].map(tab => {
          const IconComponent = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as typeof activeView)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <IconComponent className="w-4 h-4" />
              <span className="text-sm">{tab.label}</span>
            </button>
          )
        })}
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {activeView === 'realtime' && (
          <motion.div
            key="realtime"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <AdminCard title="Live User Sessions" subtitle="Currently active user sessions">
              {realTimeSessions.length > 0 ? (
                <div className="space-y-4">
                  {realTimeSessions.map((session) => (
                    <motion.div
                      key={session.id}
                      className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors cursor-pointer"
                      whileHover={{ scale: 1.01 }}
                      onClick={() => setSelectedUserId(session.userId)}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-accent-500/20 rounded-full flex items-center justify-center">
                            <User className="w-5 h-5 text-accent-400" />
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="text-white font-medium">{session.userName}</h4>
                            <span className="text-xl">{getDeviceIcon(session.deviceType)}</span>
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                          </div>
                          <p className="text-gray-400 text-sm">
                            {session.location} • Entry: {session.entryPoint}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-6 text-sm">
                        <div className="text-center">
                          <p className="text-white font-medium">{formatDuration(session.duration)}</p>
                          <p className="text-gray-400">Duration</p>
                        </div>
                        <div className="text-center">
                          <p className="text-blue-400 font-medium">{session.pageViews}</p>
                          <p className="text-gray-400">Pages</p>
                        </div>
                        <div className="text-center">
                          <p className={`font-medium ${getEngagementColor(session.engagementScore)}`}>
                            {session.engagementScore}
                          </p>
                          <p className="text-gray-400">Score</p>
                        </div>
                        <div className="text-center">
                          <p className="text-purple-400 font-medium">{session.conversionEvents.length}</p>
                          <p className="text-gray-400">Events</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400">No active sessions found</p>
                </div>
              )}
            </AdminCard>
          </motion.div>
        )}

        {activeView === 'patterns' && (
          <motion.div
            key="patterns"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
          >
            {behaviorPatterns.map((pattern) => (
              <AdminCard 
                key={pattern.id}
                title={pattern.name}
                subtitle={pattern.description}
              >
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-400">Frequency</p>
                      <p className="text-white font-bold">{pattern.frequency}%</p>
                    </div>
                    <div>
                      <p className="text-gray-400">Users</p>
                      <p className="text-blue-400 font-bold">{pattern.userCount}</p>
                    </div>
                    <div>
                      <p className="text-gray-400">Avg Engagement</p>
                      <p className={`font-bold ${getEngagementColor(pattern.avgEngagement)}`}>
                        {pattern.avgEngagement}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400">Conversion Rate</p>
                      <p className="text-green-400 font-bold">{pattern.conversionRate}%</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-gray-400 text-sm mb-2">Journey Steps</p>
                    <div className="flex items-center space-x-2">
                      {pattern.steps.map((step, index) => (
                        <React.Fragment key={index}>
                          <div className="bg-accent-500/20 text-accent-400 px-2 py-1 rounded text-xs">
                            {step}
                          </div>
                          {index < pattern.steps.length - 1 && (
                            <div className="w-2 h-2 bg-gray-500 rounded-full" />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t border-gray-700">
                    <span className="text-gray-400 text-sm">Avg Completion Time</span>
                    <span className="text-purple-400 font-medium">{pattern.timeToComplete}m</span>
                  </div>
                </div>
              </AdminCard>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default UserBehaviorTracker