/**
 * User Engagement Matrix Component
 *
 * Interactive matrix visualization for user engagement patterns and segmentation.
 * Provides insights into user behavior, activity levels, and engagement trends.
 *
 * Features:
 * - User segmentation visualization (new, active, power, inactive)
 * - Engagement heatmap with time-based patterns
 * - Interactive hover effects and detailed tooltips
 * - Responsive grid layout with mobile optimization
 * - Color-coded engagement levels
 * - Drill-down capabilities for detailed analysis
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Users,
  TrendingUp,
  Activity,
  Eye,
  MessageSquare,
  Heart,
  Share2,
  Clock
} from 'lucide-react';

interface UserEngagementData {
  daily: number[];
  weekly: number[];
  monthly: number[];
  segments: {
    new: number;
    active: number;
    power: number;
    inactive: number;
  };
}

interface UserEngagementMatrixProps {
  data: UserEngagementData;
  className?: string;
}

interface EngagementCell {
  day: string;
  hour: number;
  value: number;
  users: number;
}

export const UserEngagementMatrix: React.FC<UserEngagementMatrixProps> = ({
  data,
  className = ''
}) => {
  const [selectedCell, setSelectedCell] = useState<EngagementCell | null>(null);
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null);

  // Generate mock heatmap data for engagement patterns
  const generateHeatmapData = (): EngagementCell[][] => {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const hours = Array.from({ length: 24 }, (_, i) => i);
    
    return days.map(day => 
      hours.map(hour => ({
        day,
        hour,
        value: Math.random() * 100,
        users: Math.floor(Math.random() * 200) + 50
      }))
    );
  };

  const heatmapData = generateHeatmapData();

  // Get color intensity based on engagement value
  const getEngagementColor = (value: number) => {
    const intensity = Math.min(value / 100, 1);
    const opacity = Math.max(intensity, 0.1);
    return `rgba(139, 92, 246, ${opacity})`;
  };

  // Get segment color
  const getSegmentColor = (segment: string) => {
    switch (segment) {
      case 'new': return 'bg-blue-500';
      case 'active': return 'bg-green-500';
      case 'power': return 'bg-purple-500';
      case 'inactive': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  // Get segment icon
  const getSegmentIcon = (segment: string) => {
    switch (segment) {
      case 'new': return Users;
      case 'active': return Activity;
      case 'power': return TrendingUp;
      case 'inactive': return Clock;
      default: return Users;
    }
  };

  // Calculate total users
  const totalUsers = Object.values(data.segments).reduce((sum, count) => sum + count, 0);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* User Segments */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-300">User Segments</h4>
        <div className="grid grid-cols-2 gap-3">
          {Object.entries(data.segments).map(([segment, count]) => {
            const Icon = getSegmentIcon(segment);
            const percentage = (count / totalUsers) * 100;
            
            return (
              <motion.div
                key={segment}
                className={`p-3 rounded-lg border transition-all cursor-pointer ${
                  hoveredSegment === segment
                    ? 'border-purple-500 bg-purple-500/10'
                    : 'border-gray-700 bg-gray-800'
                }`}
                onMouseEnter={() => setHoveredSegment(segment)}
                onMouseLeave={() => setHoveredSegment(null)}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center space-x-2 mb-2">
                  <div className={`w-2 h-2 rounded-full ${getSegmentColor(segment)}`} />
                  <span className="text-xs text-gray-400 capitalize">{segment}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-lg font-bold text-white">{count.toLocaleString()}</p>
                    <p className="text-xs text-gray-500">{percentage.toFixed(1)}%</p>
                  </div>
                  <Icon className="w-4 h-4 text-gray-400" />
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Engagement Heatmap */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-300">Activity Heatmap</h4>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>Low</span>
            <div className="flex space-x-1">
              {[0.1, 0.3, 0.5, 0.7, 0.9].map((opacity) => (
                <div
                  key={opacity}
                  className="w-3 h-3 rounded-sm"
                  style={{ backgroundColor: `rgba(139, 92, 246, ${opacity})` }}
                />
              ))}
            </div>
            <span>High</span>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 overflow-x-auto">
          <div className="min-w-[600px]">
            {/* Hour labels */}
            <div className="flex mb-2">
              <div className="w-12" /> {/* Space for day labels */}
              {Array.from({ length: 24 }, (_, i) => (
                <div
                  key={i}
                  className="flex-1 text-center text-xs text-gray-500 min-w-[20px]"
                >
                  {i % 6 === 0 ? `${i}h` : ''}
                </div>
              ))}
            </div>
            
            {/* Heatmap grid */}
            {heatmapData.map((dayData, dayIndex) => (
              <div key={dayData[0].day} className="flex items-center mb-1">
                <div className="w-12 text-xs text-gray-400 text-right pr-2">
                  {dayData[0].day}
                </div>
                {dayData.map((cell, hourIndex) => (
                  <motion.div
                    key={`${dayIndex}-${hourIndex}`}
                    className="flex-1 h-4 rounded-sm cursor-pointer border border-gray-700 min-w-[20px]"
                    style={{ backgroundColor: getEngagementColor(cell.value) }}
                    whileHover={{ scale: 1.1, zIndex: 10 }}
                    onClick={() => setSelectedCell(cell)}
                    title={`${cell.day} ${cell.hour}:00 - ${Math.round(cell.value)}% engagement (${cell.users} users)`}
                  />
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Engagement Trends */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-300">Engagement Trends</h4>
        
        {/* Daily Trend */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-400">Daily (Last 7 days)</span>
            <div className="flex items-center space-x-1">
              <TrendingUp className="w-3 h-3 text-green-400" />
              <span className="text-xs text-green-400">****%</span>
            </div>
          </div>
          <div className="flex items-end space-x-1 h-12">
            {data.daily.map((value, index) => (
              <div
                key={index}
                className="flex-1 bg-purple-500 rounded-t-sm opacity-70 hover:opacity-100 transition-opacity"
                style={{ height: `${(value / Math.max(...data.daily)) * 100}%` }}
                title={`Day ${index + 1}: ${value}% engagement`}
              />
            ))}
          </div>
        </div>

        {/* Weekly Trend */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-400">Weekly (Last 4 weeks)</span>
            <div className="flex items-center space-x-1">
              <TrendingUp className="w-3 h-3 text-green-400" />
              <span className="text-xs text-green-400">+3.8%</span>
            </div>
          </div>
          <div className="flex items-end space-x-2 h-12">
            {data.weekly.map((value, index) => (
              <div
                key={index}
                className="flex-1 bg-blue-500 rounded-t-sm opacity-70 hover:opacity-100 transition-opacity"
                style={{ height: `${(value / Math.max(...data.weekly)) * 100}%` }}
                title={`Week ${index + 1}: ${value}% engagement`}
              />
            ))}
          </div>
        </div>

        {/* Monthly Trend */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-400">Monthly (Last 3 months)</span>
            <div className="flex items-center space-x-1">
              <TrendingUp className="w-3 h-3 text-green-400" />
              <span className="text-xs text-green-400">+8.1%</span>
            </div>
          </div>
          <div className="flex items-end space-x-3 h-12">
            {data.monthly.map((value, index) => (
              <div
                key={index}
                className="flex-1 bg-green-500 rounded-t-sm opacity-70 hover:opacity-100 transition-opacity"
                style={{ height: `${(value / Math.max(...data.monthly)) * 100}%` }}
                title={`Month ${index + 1}: ${value}% engagement`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Engagement Actions */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-300">Top Actions</h4>
        <div className="grid grid-cols-2 gap-3">
          {[
            { icon: Eye, label: 'Views', value: '12.4K', trend: '+8%' },
            { icon: Heart, label: 'Likes', value: '3.2K', trend: '+12%' },
            { icon: MessageSquare, label: 'Comments', value: '890', trend: '+5%' },
            { icon: Share2, label: 'Shares', value: '234', trend: '+15%' }
          ].map((action) => {
            const Icon = action.icon;
            return (
              <div key={action.label} className="bg-gray-800 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Icon className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-400">{action.label}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-white">{action.value}</span>
                  <span className="text-xs text-green-400">{action.trend}</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Selected Cell Details */}
      {selectedCell && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 border border-purple-500 rounded-lg p-4"
        >
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-white">
              {selectedCell.day} at {selectedCell.hour}:00
            </h4>
            <button
              onClick={() => setSelectedCell(null)}
              className="text-gray-400 hover:text-white"
            >
              ×
            </button>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Engagement:</span>
              <p className="text-white font-medium">{Math.round(selectedCell.value)}%</p>
            </div>
            <div>
              <span className="text-gray-400">Active Users:</span>
              <p className="text-white font-medium">{selectedCell.users}</p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default UserEngagementMatrix;
