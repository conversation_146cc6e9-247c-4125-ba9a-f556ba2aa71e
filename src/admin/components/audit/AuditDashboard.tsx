/**
 * Real-time Audit Dashboard Component
 * 
 * Provides comprehensive audit logging interface with real-time monitoring,
 * filtering capabilities, and security event tracking.
 * 
 * Features:
 * - Real-time audit log streaming
 * - Advanced filtering and search
 * - Security event monitoring
 * - Export capabilities
 * - Statistics and analytics
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useAuditLog, AuditLogFilters } from '../../hooks/useAuditLog'

interface AuditDashboardProps {
  className?: string
}

export const AuditDashboard: React.FC<AuditDashboardProps> = ({ className = '' }) => {
  const {
    logs,
    stats,
    loading,
    fetchLogs,
    fetchStats,
    exportLogs
  } = useAuditLog()

  const [filters, setFilters] = useState<AuditLogFilters>({})
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSeverity, setSelectedSeverity] = useState<string>('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchLogs(filters)
        fetchStats()
      }, 30000)

      return () => clearInterval(interval)
    }
  }, [autoRefresh, filters, fetchLogs, fetchStats])

  // Apply filters when they change
  useEffect(() => {
    const newFilters: AuditLogFilters = {}
    if (selectedCategory) newFilters.category = selectedCategory
    if (selectedSeverity) newFilters.severity = selectedSeverity
    if (searchTerm) newFilters.searchTerm = searchTerm

    setFilters(newFilters)
    fetchLogs(newFilters)
  }, [selectedCategory, selectedSeverity, searchTerm, fetchLogs])

  const handleExport = async (format: 'csv' | 'json' | 'pdf') => {
    try {
      await exportLogs(format, filters)
      console.log(`Exported audit logs as ${format}`)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-500 bg-red-50'
      case 'high': return 'text-orange-500 bg-orange-50'
      case 'medium': return 'text-yellow-500 bg-yellow-50'
      case 'low': return 'text-green-500 bg-green-50'
      default: return 'text-gray-500 bg-gray-50'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'security': return '🔒'
      case 'users': return '👥'
      case 'points': return '⭐'
      case 'achievements': return '🏆'
      case 'tiers': return '📊'
      case 'system': return '⚙️'
      default: return '📝'
    }
  }

  return (
    <div className={`audit-dashboard ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Audit Dashboard</h1>
          <p className="text-gray-400">Real-time monitoring and security oversight</p>
        </div>
        
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm text-gray-300">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            Auto-refresh
          </label>
          
          <div className="flex gap-2">
            <button
              onClick={() => handleExport('csv')}
              className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
            >
              Export CSV
            </button>
            <button
              onClick={() => handleExport('json')}
              className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
            >
              Export JSON
            </button>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-sm text-gray-400 mb-1">Total Entries</h3>
            <p className="text-2xl font-bold text-white">{stats.totalEntries.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-sm text-gray-400 mb-1">Last 24h</h3>
            <p className="text-2xl font-bold text-green-400">{stats.entriesLast24h}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-sm text-gray-400 mb-1">Last 7d</h3>
            <p className="text-2xl font-bold text-blue-400">{stats.entriesLast7d}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-sm text-gray-400 mb-1">Critical Events</h3>
            <p className="text-2xl font-bold text-red-400">
              {stats.severityBreakdown.find(s => s.severity === 'critical')?.count || 0}
            </p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-gray-800 p-4 rounded-lg mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Search</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search actions, admins, targets..."
              className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-purple-500"
            />
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-1">Category</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-purple-500"
            >
              <option value="">All Categories</option>
              <option value="security">🔒 Security</option>
              <option value="users">👥 Users</option>
              <option value="points">⭐ Points</option>
              <option value="achievements">🏆 Achievements</option>
              <option value="tiers">📊 Tiers</option>
              <option value="system">⚙️ System</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-1">Severity</label>
            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-purple-500"
            >
              <option value="">All Severities</option>
              <option value="critical">🔴 Critical</option>
              <option value="high">🟠 High</option>
              <option value="medium">🟡 Medium</option>
              <option value="low">🟢 Low</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('')
                setSelectedCategory('')
                setSelectedSeverity('')
              }}
              className="w-full px-3 py-2 bg-gray-600 text-white rounded hover:bg-gray-500"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Audit Logs Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Timestamp</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Admin</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Action</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Category</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Severity</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Target</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">IP Address</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                    Loading audit logs...
                  </td>
                </tr>
              ) : logs.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                    No audit logs found
                  </td>
                </tr>
              ) : (
                logs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-750">
                    <td className="px-4 py-3 text-sm text-gray-300">
                      {log.timestamp.toLocaleString()}
                    </td>
                    <td className="px-4 py-3 text-sm text-white">
                      <div>
                        <div className="font-medium">{log.adminName}</div>
                        <div className="text-xs text-gray-400">{log.adminEmail}</div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-white font-medium">
                      {log.action.replace(/_/g, ' ')}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <span className="flex items-center gap-1">
                        {getCategoryIcon(log.category)}
                        <span className="text-gray-300 capitalize">{log.category}</span>
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(log.severity)}`}>
                        {log.severity.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      <div>
                        <div className="font-medium">{log.targetName || 'N/A'}</div>
                        <div className="text-xs text-gray-400">{log.targetType}</div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300 font-mono">
                      {log.ipAddress || 'N/A'}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default AuditDashboard
