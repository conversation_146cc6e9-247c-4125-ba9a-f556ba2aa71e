/**
 * Audit Log Viewer Component
 * 
 * Enterprise-grade audit log viewing and filtering interface
 * Part of Phase 1 API Layer Expansion
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 1 Enhancement
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  Settings,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  RefreshCw
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'
import AuditService, { AuditLog, AuditFilters, AdminAction, AdminResource, AuditSeverity } from '../../lib/audit/AuditService'

interface AuditLogViewerProps {
  className?: string
}

interface FilterState extends AuditFilters {
  searchQuery: string
}

/**
 * Audit Log Viewer Component
 */
const AuditLogViewer: React.FC<AuditLogViewerProps> = ({ className = '' }) => {
  // ===== STATE =====
  const [logs, setLogs] = useState<AuditLog[]>([])
  const [loading, setLoading] = useState(false)
  const [filters, setFilters] = useState<FilterState>({
    searchQuery: '',
    limit: 50,
    offset: 0
  })
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null)
  const [exporting, setExporting] = useState(false)

  // ===== EFFECTS =====
  useEffect(() => {
    loadAuditLogs()
  }, [filters.limit, filters.offset, filters.adminId, filters.action, filters.resource, filters.severity, filters.dateFrom, filters.dateTo])

  // ===== HANDLERS =====
  const loadAuditLogs = async () => {
    setLoading(true)
    try {
      const result = await AuditService.getAuditLogs(filters)
      setLogs(result.logs)
      setTotalCount(result.totalCount)
      setCurrentPage(result.currentPage)
      setTotalPages(result.totalPages)
    } catch (error) {
      console.error('Failed to load audit logs:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      offset: key !== 'limit' ? 0 : prev.offset // Reset to first page when filtering
    }))
  }

  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * (filters.limit || 50)
    setFilters(prev => ({ ...prev, offset: newOffset }))
  }

  const handleExport = async (format: 'csv' | 'json' | 'pdf') => {
    setExporting(true)
    try {
      const exportData = await AuditService.exportAuditLogs({
        format,
        filters,
        includeMetadata: true
      })
      
      // Create and trigger download
      const blob = new Blob([exportData], { 
        type: format === 'json' ? 'application/json' : 'text/plain' 
      })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setExporting(false)
    }
  }

  const clearFilters = () => {
    setFilters({
      searchQuery: '',
      limit: 50,
      offset: 0
    })
  }

  // ===== HELPER FUNCTIONS =====
  const getSeverityIcon = (severity: AuditSeverity) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4 text-red-400" />
      case 'high': return <AlertTriangle className="w-4 h-4 text-orange-400" />
      case 'medium': return <Info className="w-4 h-4 text-yellow-400" />
      case 'low': return <CheckCircle className="w-4 h-4 text-green-400" />
      default: return <Info className="w-4 h-4 text-gray-400" />
    }
  }

  const getSeverityColor = (severity: AuditSeverity) => {
    switch (severity) {
      case 'critical': return 'bg-red-500/10 border-red-500/20 text-red-400'
      case 'high': return 'bg-orange-500/10 border-orange-500/20 text-orange-400'
      case 'medium': return 'bg-yellow-500/10 border-yellow-500/20 text-yellow-400'
      case 'low': return 'bg-green-500/10 border-green-500/20 text-green-400'
      default: return 'bg-gray-500/10 border-gray-500/20 text-gray-400'
    }
  }

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    }).format(timestamp)
  }

  // ===== RENDER =====
  return (
    <div className={`audit-log-viewer ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div className="flex items-center space-x-3">
          <Shield className="w-8 h-8 text-blue-400" />
          <div>
            <h2 className="text-2xl font-bold text-white">Audit Logs</h2>
            <p className="text-gray-400">Enterprise-grade audit trail and compliance logging</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={loadAuditLogs}
            loading={loading}
          >
            Refresh
          </AdminButton>
          
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-accent-600' : ''}
          >
            Filters
          </AdminButton>

          <AdminButton
            variant="secondary"
            icon={Download}
            onClick={() => handleExport('csv')}
            loading={exporting}
          >
            Export
          </AdminButton>
        </div>
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6"
          >
            <AdminCard title="Advanced Filters" className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Search Query */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Search
                  </label>
                  <div className="relative">
                    <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    <input
                      type="text"
                      value={filters.searchQuery}
                      onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                      placeholder="Search logs..."
                      className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                    />
                  </div>
                </div>

                {/* Action Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Action
                  </label>
                  <select
                    value={filters.action || ''}
                    onChange={(e) => handleFilterChange('action', e.target.value || undefined)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  >
                    <option value="">All Actions</option>
                    <option value="USER_CREATED">User Created</option>
                    <option value="USER_UPDATED">User Updated</option>
                    <option value="USER_DELETED">User Deleted</option>
                    <option value="PRODUCT_CREATED">Product Created</option>
                    <option value="PRODUCT_UPDATED">Product Updated</option>
                    <option value="ADMIN_LOGIN">Admin Login</option>
                    <option value="BULK_OPERATION">Bulk Operation</option>
                  </select>
                </div>

                {/* Resource Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Resource
                  </label>
                  <select
                    value={filters.resource || ''}
                    onChange={(e) => handleFilterChange('resource', e.target.value || undefined)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  >
                    <option value="">All Resources</option>
                    <option value="user">User</option>
                    <option value="product">Product</option>
                    <option value="order">Order</option>
                    <option value="raffle">Raffle</option>
                    <option value="admin">Admin</option>
                    <option value="system">System</option>
                  </select>
                </div>

                {/* Severity Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Severity
                  </label>
                  <select
                    value={filters.severity || ''}
                    onChange={(e) => handleFilterChange('severity', e.target.value || undefined)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  >
                    <option value="">All Severities</option>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>

                {/* Date From */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Date From
                  </label>
                  <input
                    type="datetime-local"
                    value={filters.dateFrom ? filters.dateFrom.toISOString().slice(0, 16) : ''}
                    onChange={(e) => handleFilterChange('dateFrom', e.target.value ? new Date(e.target.value) : undefined)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  />
                </div>

                {/* Date To */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Date To
                  </label>
                  <input
                    type="datetime-local"
                    value={filters.dateTo ? filters.dateTo.toISOString().slice(0, 16) : ''}
                    onChange={(e) => handleFilterChange('dateTo', e.target.value ? new Date(e.target.value) : undefined)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  />
                </div>

                {/* Admin ID */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Admin ID
                  </label>
                  <input
                    type="text"
                    value={filters.adminId || ''}
                    onChange={(e) => handleFilterChange('adminId', e.target.value || undefined)}
                    placeholder="Filter by admin..."
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  />
                </div>

                {/* Clear Filters */}
                <div className="flex items-end">
                  <AdminButton
                    variant="secondary"
                    onClick={clearFilters}
                    className="w-full"
                  >
                    Clear Filters
                  </AdminButton>
                </div>
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results Summary */}
      <div className="flex items-center justify-between mb-4">
        <p className="text-gray-400">
          Showing {logs.length} of {totalCount.toLocaleString()} audit logs
        </p>
        <div className="flex items-center space-x-4">
          <select
            value={filters.limit}
            onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
            className="px-3 py-1 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:border-accent-500"
          >
            <option value={25}>25 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>
        </div>
      </div>

      {/* Audit Logs Table */}
      <AdminCard className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700/50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Resource
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Admin
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  IP Address
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                    <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                    Loading audit logs...
                  </td>
                </tr>
              ) : logs.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                    No audit logs found
                  </td>
                </tr>
              ) : (
                logs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-700/30 transition-colors">
                    <td className="px-4 py-3 text-sm text-white">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span>{formatTimestamp(log.timestamp)}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full border ${getSeverityColor(log.severity)}`}>
                        {getSeverityIcon(log.severity)}
                        <span className="capitalize">{log.severity}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-white font-medium">
                      {log.action.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      <div>
                        <div className="font-medium">{log.resource}</div>
                        <div className="text-xs text-gray-400">{log.resourceId}</div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <span>{log.adminId}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-300">
                      {log.metadata.ipAddress}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <button
                        onClick={() => setSelectedLog(log)}
                        className="flex items-center space-x-1 text-accent-400 hover:text-accent-300 transition-colors"
                      >
                        <Eye className="w-4 h-4" />
                        <span>View</span>
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-4 py-3 border-t border-gray-700">
            <div className="text-sm text-gray-400">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </AdminCard>

      {/* Log Detail Modal */}
      <AnimatePresence>
        {selectedLog && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            onClick={() => setSelectedLog(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-800 rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-white">Audit Log Details</h3>
                  <button
                    onClick={() => setSelectedLog(null)}
                    className="text-gray-400 hover:text-white"
                  >
                    <XCircle className="w-6 h-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Timestamp</label>
                      <p className="text-white">{formatTimestamp(selectedLog.timestamp)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Severity</label>
                      <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full border ${getSeverityColor(selectedLog.severity)}`}>
                        {getSeverityIcon(selectedLog.severity)}
                        <span className="capitalize">{selectedLog.severity}</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Action</label>
                      <p className="text-white">{selectedLog.action}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Resource</label>
                      <p className="text-white">{selectedLog.resource}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Resource ID</label>
                      <p className="text-white font-mono">{selectedLog.resourceId}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Admin ID</label>
                      <p className="text-white">{selectedLog.adminId}</p>
                    </div>
                  </div>

                  {selectedLog.previousState && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Previous State</label>
                      <pre className="bg-gray-900 p-3 rounded text-green-400 text-xs overflow-auto">
                        {JSON.stringify(selectedLog.previousState, null, 2)}
                      </pre>
                    </div>
                  )}

                  {selectedLog.newState && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">New State</label>
                      <pre className="bg-gray-900 p-3 rounded text-blue-400 text-xs overflow-auto">
                        {JSON.stringify(selectedLog.newState, null, 2)}
                      </pre>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Metadata</label>
                    <pre className="bg-gray-900 p-3 rounded text-gray-300 text-xs overflow-auto">
                      {JSON.stringify(selectedLog.metadata, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default AuditLogViewer