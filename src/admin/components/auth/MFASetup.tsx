/**
 * MFA Setup Component for Admin Accounts
 * 
 * Provides comprehensive multi-factor authentication setup interface
 * for admin users including QR code generation, backup codes, and
 * verification process.
 * 
 * Features:
 * - QR code generation for authenticator apps
 * - Manual secret key entry option
 * - Backup code generation and display
 * - Verification process
 * - Recovery options
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import QRCode from 'qrcode'

interface MFASetupProps {
  adminId: string
  onComplete: () => void
  onCancel: () => void
}

interface MFASetupData {
  secret: string
  qrCodeUrl: string
  backupCodes: string[]
  setupToken: string
}

export const MFASetup: React.FC<MFASetupProps> = ({ adminId, onComplete, onCancel }) => {
  const [step, setStep] = useState<'setup' | 'verify' | 'backup' | 'complete'>('setup')
  const [setupData, setSetupData] = useState<MFASetupData | null>(null)
  const [qrCodeImage, setQrCodeImage] = useState<string>('')
  const [verificationCode, setVerificationCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showSecret, setShowSecret] = useState(false)

  // Initialize MFA setup
  useEffect(() => {
    initializeMFASetup()
  }, [])

  const initializeMFASetup = async () => {
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin/mfa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'setup' }),
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to initialize MFA setup')
      }

      const data: MFASetupData = await response.json()
      setSetupData(data)

      // Generate QR code image
      const qrImage = await QRCode.toDataURL(data.qrCodeUrl)
      setQrCodeImage(qrImage)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Setup failed')
    } finally {
      setLoading(false)
    }
  }

  const verifyMFACode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Please enter a 6-digit verification code')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin/mfa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'verify',
          token: verificationCode
        }),
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Invalid verification code')
      }

      setStep('backup')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Verification failed')
    } finally {
      setLoading(false)
    }
  }

  const completeMFASetup = () => {
    setStep('complete')
    setTimeout(() => {
      onComplete()
    }, 2000)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const downloadBackupCodes = () => {
    if (!setupData) return

    const content = `Syndicaps Admin Backup Codes\nGenerated: ${new Date().toISOString()}\n\n${setupData.backupCodes.join('\n')}\n\nKeep these codes safe and secure!`
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `syndicaps-admin-backup-codes-${Date.now()}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  if (loading && !setupData) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Initializing MFA setup...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto bg-gray-800 rounded-lg p-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Setup Two-Factor Authentication</h2>
        <p className="text-gray-400">Secure your admin account with MFA</p>
      </div>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {step === 'setup' && setupData && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-4">Scan QR Code</h3>
            <div className="bg-white p-4 rounded-lg inline-block">
              <img src={qrCodeImage} alt="MFA QR Code" className="w-48 h-48" />
            </div>
            <p className="text-sm text-gray-400 mt-2">
              Scan with Google Authenticator, Authy, or similar app
            </p>
          </div>

          <div className="border-t border-gray-700 pt-4">
            <button
              onClick={() => setShowSecret(!showSecret)}
              className="text-purple-400 hover:text-purple-300 text-sm mb-2"
            >
              {showSecret ? 'Hide' : 'Show'} manual entry key
            </button>
            {showSecret && (
              <div className="bg-gray-700 p-3 rounded">
                <p className="text-xs text-gray-400 mb-1">Manual entry key:</p>
                <code className="text-sm text-white font-mono break-all">{setupData.secret}</code>
                <button
                  onClick={() => copyToClipboard(setupData.secret)}
                  className="ml-2 text-purple-400 hover:text-purple-300 text-xs"
                >
                  Copy
                </button>
              </div>
            )}
          </div>

          <button
            onClick={() => setStep('verify')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
          >
            Continue to Verification
          </button>
        </div>
      )}

      {step === 'verify' && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-4">Verify Setup</h3>
            <p className="text-gray-400 mb-4">
              Enter the 6-digit code from your authenticator app
            </p>
          </div>

          <div>
            <input
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="000000"
              className="w-full px-4 py-3 bg-gray-700 text-white text-center text-2xl font-mono rounded border border-gray-600 focus:border-purple-500"
              maxLength={6}
            />
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => setStep('setup')}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-500"
            >
              Back
            </button>
            <button
              onClick={verifyMFACode}
              disabled={loading || verificationCode.length !== 6}
              className="flex-1 bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? 'Verifying...' : 'Verify'}
            </button>
          </div>
        </div>
      )}

      {step === 'backup' && setupData && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-4">Save Backup Codes</h3>
            <p className="text-gray-400 mb-4">
              Store these codes safely. You can use them to access your account if you lose your authenticator.
            </p>
          </div>

          <div className="bg-gray-700 p-4 rounded">
            <div className="grid grid-cols-2 gap-2 text-sm font-mono">
              {setupData.backupCodes.map((code, index) => (
                <div key={index} className="text-white bg-gray-800 p-2 rounded text-center">
                  {code}
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={downloadBackupCodes}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-500"
            >
              Download Codes
            </button>
            <button
              onClick={completeMFASetup}
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
            >
              Complete Setup
            </button>
          </div>
        </div>
      )}

      {step === 'complete' && (
        <div className="text-center space-y-4">
          <div className="text-green-400 text-6xl">✓</div>
          <h3 className="text-lg font-semibold text-white">MFA Setup Complete!</h3>
          <p className="text-gray-400">Your admin account is now secured with two-factor authentication.</p>
        </div>
      )}

      <div className="mt-6 pt-4 border-t border-gray-700">
        <button
          onClick={onCancel}
          className="w-full text-gray-400 hover:text-white text-sm"
        >
          Cancel Setup
        </button>
      </div>
    </div>
  )
}

export default MFASetup
