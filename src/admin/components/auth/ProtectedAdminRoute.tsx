'use client';

import { useUser } from '@/lib/useUser';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ProtectedAdminRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
}

export function ProtectedAdminRoute({ 
  children, 
  requiredPermissions = [] 
}: ProtectedAdminRouteProps) {
  const { user, profile, loading } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push('/admin/login');
        return;
      }

      if (!profile || !['admin', 'superadmin'].includes(profile.role)) {
        router.push('/admin/login');
        return;
      }

      // Check specific permissions if required
      if (requiredPermissions.length > 0) {
        const hasPermission = requiredPermissions.some(permission => 
          profile.permissions?.[permission] === true
        );
        
        if (!hasPermission) {
          router.push('/admin/dashboard');
          return;
        }
      }
    }
  }, [user, profile, loading, router, requiredPermissions]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-950">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
      </div>
    );
  }

  if (!user || !profile || !['admin', 'superadmin'].includes(profile.role)) {
    return null;
  }

  return <>{children}</>;
}

export default ProtectedAdminRoute;