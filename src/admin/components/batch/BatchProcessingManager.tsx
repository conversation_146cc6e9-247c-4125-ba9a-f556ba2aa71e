/**
 * Batch Processing Manager Component
 *
 * Comprehensive management interface for scheduled tasks and background processing.
 * Handles data cleanup, automated reports, maintenance tasks, and system optimization.
 *
 * Features:
 * - Scheduled task management and monitoring
 * - Background job processing and queue management
 * - Data cleanup and maintenance automation
 * - Automated report generation and distribution
 * - System optimization and performance tasks
 * - Task scheduling with cron-like expressions
 * - Job status tracking and error handling
 * - Resource usage monitoring and optimization
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Clock,
  Play,
  Pause,
  Square,
  RefreshCw,
  Calendar,
  Database,
  FileText,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  Activity,
  BarChart3
} from 'lucide-react';
import { AdminCard, AdminButton } from '../common';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { useBatchProcessing } from '../../hooks/useBatchProcessing';
import { CommunityStatsCard } from '../community/shared/CommunityStatsCard';

interface BatchProcessingManagerProps {
  className?: string;
}

interface BatchJob {
  id: string;
  name: string;
  type: 'cleanup' | 'report' | 'maintenance' | 'optimization' | 'backup';
  status: 'scheduled' | 'running' | 'completed' | 'failed' | 'paused';
  schedule: string; // cron expression
  lastRun?: Date;
  nextRun?: Date;
  duration?: number; // seconds
  progress?: number; // 0-100
  errorMessage?: string;
  config: Record<string, any>;
}

export const BatchProcessingManager: React.FC<BatchProcessingManagerProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [activeTab, setActiveTab] = useState<'jobs' | 'schedule' | 'history' | 'monitoring'>('jobs');
  const [selectedJob, setSelectedJob] = useState<BatchJob | null>(null);
  const [showJobEditor, setShowJobEditor] = useState(false);

  // Data hooks
  const {
    jobs,
    jobHistory,
    queueStats,
    systemMetrics,
    loading,
    error,
    refetch,
    runJob,
    pauseJob,
    resumeJob,
    stopJob,
    createJob,
    updateJob,
    deleteJob
  } = useBatchProcessing();

  // Permission checks
  const canManageJobs = hasPermission('system_batch', 'write');
  const canViewJobs = hasPermission('system_batch', 'read');

  // Handle job actions
  const handleRunJob = useCallback(async (jobId: string) => {
    if (!canManageJobs) return;
    
    try {
      await runJob(jobId);
      await refetch();
    } catch (error) {
      console.error('Failed to run job:', error);
    }
  }, [canManageJobs, runJob, refetch]);

  const handlePauseJob = useCallback(async (jobId: string) => {
    if (!canManageJobs) return;
    
    try {
      await pauseJob(jobId);
      await refetch();
    } catch (error) {
      console.error('Failed to pause job:', error);
    }
  }, [canManageJobs, pauseJob, refetch]);

  const handleStopJob = useCallback(async (jobId: string) => {
    if (!canManageJobs) return;
    
    try {
      await stopJob(jobId);
      await refetch();
    } catch (error) {
      console.error('Failed to stop job:', error);
    }
  }, [canManageJobs, stopJob, refetch]);

  // Get job type icon
  const getJobTypeIcon = (type: string) => {
    switch (type) {
      case 'cleanup': return Database;
      case 'report': return FileText;
      case 'maintenance': return Settings;
      case 'optimization': return Zap;
      case 'backup': return RefreshCw;
      default: return Clock;
    }
  };

  // Get job status color
  const getJobStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'text-blue-400 bg-blue-500/10';
      case 'running': return 'text-green-400 bg-green-500/10';
      case 'completed': return 'text-green-400 bg-green-500/10';
      case 'failed': return 'text-red-400 bg-red-500/10';
      case 'paused': return 'text-yellow-400 bg-yellow-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  if (!canViewJobs) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-400">Access denied</p>
          <p className="text-sm text-gray-500">You don't have permission to view batch processing</p>
        </div>
      </div>
    );
  }

  // Key metrics
  const keyMetrics = [
    {
      title: 'Active Jobs',
      value: jobs?.filter(j => j.status === 'running').length || 0,
      icon: Play,
      color: 'green' as const,
      trend: 0,
      subtitle: 'Currently running'
    },
    {
      title: 'Scheduled Jobs',
      value: jobs?.filter(j => j.status === 'scheduled').length || 0,
      icon: Calendar,
      color: 'blue' as const,
      trend: 0,
      subtitle: 'Waiting to run'
    },
    {
      title: 'Failed Jobs',
      value: jobs?.filter(j => j.status === 'failed').length || 0,
      icon: XCircle,
      color: 'red' as const,
      trend: 0,
      subtitle: 'Need attention'
    },
    {
      title: 'Queue Size',
      value: queueStats?.queueSize || 0,
      icon: Activity,
      color: 'purple' as const,
      trend: queueStats?.queueTrend || 0,
      subtitle: 'Jobs in queue'
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Batch Processing</h1>
          <p className="text-gray-400">Manage scheduled tasks, background jobs, and system automation</p>
        </div>
        <div className="flex items-center space-x-3">
          {canManageJobs && (
            <AdminButton
              variant="primary"
              icon={Plus}
              onClick={() => setShowJobEditor(true)}
            >
              Create Job
            </AdminButton>
          )}
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={refetch}
            disabled={loading}
            className={loading ? 'animate-spin' : ''}
          >
            Refresh
          </AdminButton>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('jobs')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'jobs' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Clock className="w-4 h-4" />
          <span>Jobs</span>
        </button>
        
        <button
          onClick={() => setActiveTab('schedule')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'schedule' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Calendar className="w-4 h-4" />
          <span>Schedule</span>
        </button>
        
        <button
          onClick={() => setActiveTab('history')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'history' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <FileText className="w-4 h-4" />
          <span>History</span>
        </button>
        
        <button
          onClick={() => setActiveTab('monitoring')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'monitoring' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <BarChart3 className="w-4 h-4" />
          <span>Monitoring</span>
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {keyMetrics.map((metric, index) => (
          <CommunityStatsCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            color={metric.color}
            trend={metric.trend}
            subtitle={metric.subtitle}
          />
        ))}
      </div>

      {/* Tab Content */}
      <div className="min-h-[500px]">
        {activeTab === 'jobs' && (
          <AdminCard className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Batch Jobs</h3>
              <div className="text-sm text-gray-400">
                {jobs?.length || 0} jobs configured
              </div>
            </div>
            
            <div className="space-y-3">
              {jobs?.map((job) => {
                const JobIcon = getJobTypeIcon(job.type);
                
                return (
                  <div key={job.id} className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
                        <JobIcon className="w-5 h-5 text-gray-400" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-white">{job.name}</h4>
                          <span className={`px-2 py-1 text-xs rounded-full ${getJobStatusColor(job.status)}`}>
                            {job.status}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span className="capitalize">{job.type}</span>
                          <span>•</span>
                          <span className="font-mono">{job.schedule}</span>
                          {job.lastRun && (
                            <>
                              <span>•</span>
                              <span>Last: {job.lastRun.toLocaleString()}</span>
                            </>
                          )}
                          {job.nextRun && (
                            <>
                              <span>•</span>
                              <span>Next: {job.nextRun.toLocaleString()}</span>
                            </>
                          )}
                        </div>
                        
                        {job.status === 'running' && job.progress !== undefined && (
                          <div className="mt-2">
                            <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
                              <span>Progress</span>
                              <span>{job.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-700 rounded-full h-1">
                              <div
                                className="bg-purple-500 h-1 rounded-full transition-all"
                                style={{ width: `${job.progress}%` }}
                              />
                            </div>
                          </div>
                        )}
                        
                        {job.status === 'failed' && job.errorMessage && (
                          <div className="mt-2 text-xs text-red-400">
                            Error: {job.errorMessage}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {canManageJobs && (
                        <>
                          {job.status === 'scheduled' || job.status === 'paused' ? (
                            <AdminButton
                              variant="secondary"
                              size="sm"
                              icon={Play}
                              onClick={() => handleRunJob(job.id)}
                            >
                              Run
                            </AdminButton>
                          ) : job.status === 'running' ? (
                            <>
                              <AdminButton
                                variant="secondary"
                                size="sm"
                                icon={Pause}
                                onClick={() => handlePauseJob(job.id)}
                              >
                                Pause
                              </AdminButton>
                              <AdminButton
                                variant="secondary"
                                size="sm"
                                icon={Square}
                                onClick={() => handleStopJob(job.id)}
                              >
                                Stop
                              </AdminButton>
                            </>
                          ) : null}
                          
                          <AdminButton
                            variant="secondary"
                            size="sm"
                            icon={Edit}
                            onClick={() => {
                              setSelectedJob(job);
                              setShowJobEditor(true);
                            }}
                          />
                          
                          <AdminButton
                            variant="secondary"
                            size="sm"
                            icon={Trash2}
                            onClick={() => deleteJob(job.id)}
                          />
                        </>
                      )}
                      
                      <AdminButton
                        variant="secondary"
                        size="sm"
                        icon={Eye}
                        onClick={() => setSelectedJob(job)}
                      />
                    </div>
                  </div>
                );
              }) || (
                <div className="text-center py-8 text-gray-400">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No batch jobs configured</p>
                  <p className="text-sm">Create your first batch job to automate tasks</p>
                </div>
              )}
            </div>
          </AdminCard>
        )}

        {activeTab === 'schedule' && (
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Job Schedule</h3>
            <div className="text-center py-8 text-gray-400">
              <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Schedule visualization</p>
              <p className="text-sm">Calendar view of scheduled jobs and their execution times</p>
            </div>
          </AdminCard>
        )}

        {activeTab === 'history' && (
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Execution History</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left text-gray-400 font-medium py-2">Job</th>
                    <th className="text-center text-gray-400 font-medium py-2">Status</th>
                    <th className="text-center text-gray-400 font-medium py-2">Started</th>
                    <th className="text-center text-gray-400 font-medium py-2">Duration</th>
                    <th className="text-center text-gray-400 font-medium py-2">Result</th>
                  </tr>
                </thead>
                <tbody>
                  {jobHistory?.map((execution, index) => (
                    <tr key={index} className="border-b border-gray-800">
                      <td className="py-3 text-white">{execution.jobName}</td>
                      <td className="text-center py-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${getJobStatusColor(execution.status)}`}>
                          {execution.status}
                        </span>
                      </td>
                      <td className="text-center py-3 text-gray-300">{execution.startTime.toLocaleString()}</td>
                      <td className="text-center py-3 text-gray-300">{execution.duration}s</td>
                      <td className="text-center py-3">
                        {execution.status === 'completed' ? (
                          <CheckCircle className="w-4 h-4 text-green-400 mx-auto" />
                        ) : execution.status === 'failed' ? (
                          <XCircle className="w-4 h-4 text-red-400 mx-auto" />
                        ) : (
                          <Clock className="w-4 h-4 text-yellow-400 mx-auto" />
                        )}
                      </td>
                    </tr>
                  )) || (
                    <tr>
                      <td colSpan={5} className="text-center py-8 text-gray-400">
                        No execution history available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </AdminCard>
        )}

        {activeTab === 'monitoring' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AdminCard className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">System Resources</h3>
              <div className="space-y-4">
                {systemMetrics?.map((metric, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-gray-400">{metric.name}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            metric.value > 80 ? 'bg-red-500' :
                            metric.value > 60 ? 'bg-yellow-500' :
                            'bg-green-500'
                          }`}
                          style={{ width: `${metric.value}%` }}
                        />
                      </div>
                      <span className="text-white font-medium w-12 text-right">{metric.value}%</span>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-4 text-gray-400">
                    No system metrics available
                  </div>
                )}
              </div>
            </AdminCard>

            <AdminCard className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Queue Statistics</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Queue Size</span>
                  <span className="text-white font-medium">{queueStats?.queueSize || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Processing Rate</span>
                  <span className="text-white font-medium">{queueStats?.processingRate || 0}/min</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Average Wait Time</span>
                  <span className="text-white font-medium">{queueStats?.averageWaitTime || 0}s</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Success Rate</span>
                  <span className="text-white font-medium">{queueStats?.successRate || 0}%</span>
                </div>
              </div>
            </AdminCard>
          </div>
        )}
      </div>
    </div>
  );
};

export default BatchProcessingManager;
