/**
 * Admin Error Boundary Component
 * 
 * Enhanced error boundary specifically for admin panel with detailed
 * error reporting and recovery options.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug, Co<PERSON>, CheckCircle } from 'lucide-react';
import AdminCard from './AdminCard';
import AdminButton from './AdminButton';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  copied: boolean;
}

class AdminErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      copied: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Log error details
    console.error('🚨 Admin Error Boundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString()
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Check if this is a JSON parsing error
    if (error.message.includes('Unexpected token') && error.message.includes('JSON')) {
      console.error('🔍 JSON Parsing Error Detected:', {
        message: error.message,
        possibleCauses: [
          'API endpoint returning HTML instead of JSON',
          'Authentication redirect intercepting API calls',
          'Middleware redirecting to error page',
          'Firebase configuration issues',
          'Network connectivity problems'
        ],
        troubleshooting: [
          'Check browser Network tab for failed requests',
          'Verify Firebase configuration in .env.local',
          'Check if user is properly authenticated',
          'Verify API routes are returning JSON responses',
          'Check middleware for unexpected redirects'
        ]
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      copied: false
    });
  };

  handleGoHome = () => {
    window.location.href = '/admin/dashboard';
  };

  handleCopyError = async () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
      this.setState({ copied: true });
      setTimeout(() => this.setState({ copied: false }), 2000);
    } catch (err) {
      console.error('Failed to copy error details:', err);
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const isJsonError = this.state.error?.message.includes('Unexpected token') && 
                         this.state.error?.message.includes('JSON');

      return (
        <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
          <AdminCard className="max-w-2xl w-full p-8">
            <div className="text-center mb-6">
              <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-white mb-2">
                {isJsonError ? 'Data Loading Error' : 'Something went wrong'}
              </h1>
              <p className="text-gray-400">
                {isJsonError 
                  ? 'There was an issue loading data from the server. This usually indicates a configuration or connectivity problem.'
                  : 'An unexpected error occurred in the admin panel. Our team has been notified.'
                }
              </p>
            </div>

            {/* Error Details */}
            <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-white">Error Details</h3>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-400">ID: {this.state.errorId}</span>
                  <AdminButton
                    variant="ghost"
                    size="sm"
                    icon={this.state.copied ? CheckCircle : Copy}
                    onClick={this.handleCopyError}
                  >
                    {this.state.copied ? 'Copied!' : 'Copy'}
                  </AdminButton>
                </div>
              </div>
              
              <div className="text-sm text-red-300 font-mono bg-red-900/20 p-3 rounded border border-red-600/30">
                {this.state.error?.message}
              </div>

              {isJsonError && (
                <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-600/30 rounded">
                  <h4 className="text-sm font-medium text-yellow-400 mb-2">Common Causes:</h4>
                  <ul className="text-xs text-yellow-300 space-y-1">
                    <li>• API endpoint returning HTML instead of JSON</li>
                    <li>• Authentication redirect intercepting requests</li>
                    <li>• Firebase configuration issues</li>
                    <li>• Network connectivity problems</li>
                  </ul>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <AdminButton
                variant="primary"
                icon={RefreshCw}
                onClick={this.handleRetry}
                className="flex-1"
              >
                Try Again
              </AdminButton>
              
              <AdminButton
                variant="secondary"
                icon={Home}
                onClick={this.handleGoHome}
                className="flex-1"
              >
                Go to Dashboard
              </AdminButton>
            </div>

            {/* Development Details */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6">
                <summary className="cursor-pointer text-sm text-gray-400 hover:text-gray-300 mb-3">
                  <Bug className="w-4 h-4 inline mr-2" />
                  Development Details
                </summary>
                
                <div className="space-y-4">
                  {/* Error Stack */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-2">Error Stack:</h4>
                    <pre className="text-xs text-gray-300 bg-gray-800 p-3 rounded overflow-auto max-h-40">
                      {this.state.error.stack}
                    </pre>
                  </div>

                  {/* Component Stack */}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <h4 className="text-sm font-medium text-white mb-2">Component Stack:</h4>
                      <pre className="text-xs text-gray-300 bg-gray-800 p-3 rounded overflow-auto max-h-40">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}

                  {/* Troubleshooting Tips */}
                  {isJsonError && (
                    <div>
                      <h4 className="text-sm font-medium text-white mb-2">Troubleshooting:</h4>
                      <ul className="text-xs text-gray-300 space-y-1">
                        <li>1. Check browser Network tab for failed requests</li>
                        <li>2. Verify Firebase configuration in .env.local</li>
                        <li>3. Check if user is properly authenticated</li>
                        <li>4. Verify API routes return JSON responses</li>
                        <li>5. Check middleware for unexpected redirects</li>
                      </ul>
                    </div>
                  )}
                </div>
              </details>
            )}

            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500">
                If this problem persists, please contact the development team with the error ID above.
              </p>
            </div>
          </AdminCard>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AdminErrorBoundary;
