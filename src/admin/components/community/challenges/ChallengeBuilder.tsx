/**
 * Challenge Builder Component
 *
 * Comprehensive modal interface for creating and editing challenges.
 * Provides step-by-step challenge creation with templates and customization.
 *
 * Features:
 * - Multi-step challenge creation wizard
 * - Challenge templates and presets
 * - Advanced configuration options
 * - Prize and reward management
 * - Judging criteria setup
 * - Team challenge configuration
 * - Preview and validation
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X,
  ChevronLeft,
  ChevronRight,
  Save,
  Eye,
  Trophy,
  Users,
  Target,
  Calendar,
  Award,
  Settings,
  Plus,
  Trash2,
  Copy
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import type { 
  ChallengeAdminInterface,
  ChallengePrize,
  JudgingCriteria
} from '../types';

interface ChallengeBuilderProps {
  challengeId?: string | null;
  onClose: () => void;
  onSave: (challengeData: Partial<ChallengeAdminInterface>) => Promise<void>;
  className?: string;
}

interface ChallengeTemplate {
  id: string;
  name: string;
  description: string;
  type: 'individual' | 'team' | 'community';
  icon: React.ComponentType<{ className?: string }>;
  defaultData: Partial<ChallengeAdminInterface>;
}

const challengeTemplates: ChallengeTemplate[] = [
  {
    id: 'design_contest',
    name: 'Design Contest',
    description: 'Individual keycap design competition',
    type: 'individual',
    icon: Trophy,
    defaultData: {
      type: 'individual',
      categories: ['Keycap Designs'],
      judgingCriteria: [
        { id: '1', name: 'Creativity', description: 'Originality and innovation', weight: 30, maxScore: 10 },
        { id: '2', name: 'Technical Quality', description: 'Design execution and quality', weight: 25, maxScore: 10 },
        { id: '3', name: 'Theme Adherence', description: 'How well it fits the theme', weight: 25, maxScore: 10 },
        { id: '4', name: 'Community Appeal', description: 'Broad community appeal', weight: 20, maxScore: 10 }
      ],
      rules: [
        'Original designs only',
        'Must be keycap-related',
        'One submission per participant',
        'Follow community guidelines'
      ]
    }
  },
  {
    id: 'team_build',
    name: 'Team Build Challenge',
    description: 'Collaborative keyboard building project',
    type: 'team',
    icon: Users,
    defaultData: {
      type: 'team',
      categories: ['Custom Builds'],
      maxParticipants: 20,
      judgingCriteria: [
        { id: '1', name: 'Teamwork', description: 'Collaboration and coordination', weight: 30, maxScore: 10 },
        { id: '2', name: 'Innovation', description: 'Creative solutions and ideas', weight: 25, maxScore: 10 },
        { id: '3', name: 'Execution', description: 'Quality of final build', weight: 25, maxScore: 10 },
        { id: '4', name: 'Documentation', description: 'Process documentation', weight: 20, maxScore: 10 }
      ]
    }
  },
  {
    id: 'community_vote',
    name: 'Community Choice',
    description: 'Community-voted challenge',
    type: 'community',
    icon: Target,
    defaultData: {
      type: 'community',
      categories: ['Community Events'],
      judgingCriteria: [
        { id: '1', name: 'Community Votes', description: 'Popular vote from community', weight: 100, maxScore: 10 }
      ]
    }
  }
];

export const ChallengeBuilder: React.FC<ChallengeBuilderProps> = ({
  challengeId,
  onClose,
  onSave,
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State
  const [currentStep, setCurrentStep] = useState(0);
  const [challengeData, setChallengeData] = useState<Partial<ChallengeAdminInterface>>({
    title: '',
    description: '',
    type: 'individual',
    status: 'draft',
    categories: [],
    prizes: [],
    judgingCriteria: [],
    judges: [],
    rules: [],
    startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    submissionDeadline: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000)
  });
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');

  const steps = [
    { id: 'template', title: 'Template', icon: Copy },
    { id: 'basic', title: 'Basic Info', icon: Settings },
    { id: 'timeline', title: 'Timeline', icon: Calendar },
    { id: 'prizes', title: 'Prizes', icon: Award },
    { id: 'judging', title: 'Judging', icon: Trophy },
    { id: 'preview', title: 'Preview', icon: Eye }
  ];

  // Load existing challenge data
  useEffect(() => {
    if (challengeId) {
      // In real implementation, fetch challenge data
      // For now, skip template selection for editing
      setCurrentStep(1);
    }
  }, [challengeId]);

  // Handle template selection
  const handleTemplateSelect = useCallback((templateId: string) => {
    const template = challengeTemplates.find(t => t.id === templateId);
    if (template) {
      setChallengeData(prev => ({
        ...prev,
        ...template.defaultData
      }));
      setSelectedTemplate(templateId);
    }
  }, []);

  // Handle form field changes
  const handleFieldChange = useCallback((field: string, value: any) => {
    setChallengeData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // Handle prize management
  const handleAddPrize = useCallback(() => {
    const newPrize: ChallengePrize = {
      position: (challengeData.prizes?.length || 0) + 1,
      title: '',
      description: '',
      value: 0,
      type: 'points'
    };
    
    setChallengeData(prev => ({
      ...prev,
      prizes: [...(prev.prizes || []), newPrize]
    }));
  }, [challengeData.prizes]);

  const handleUpdatePrize = useCallback((index: number, prize: ChallengePrize) => {
    setChallengeData(prev => ({
      ...prev,
      prizes: prev.prizes?.map((p, i) => i === index ? prize : p) || []
    }));
  }, []);

  const handleRemovePrize = useCallback((index: number) => {
    setChallengeData(prev => ({
      ...prev,
      prizes: prev.prizes?.filter((_, i) => i !== index) || []
    }));
  }, []);

  // Handle judging criteria management
  const handleAddCriteria = useCallback(() => {
    const newCriteria: JudgingCriteria = {
      id: Date.now().toString(),
      name: '',
      description: '',
      weight: 0,
      maxScore: 10
    };
    
    setChallengeData(prev => ({
      ...prev,
      judgingCriteria: [...(prev.judgingCriteria || []), newCriteria]
    }));
  }, []);

  const handleUpdateCriteria = useCallback((index: number, criteria: JudgingCriteria) => {
    setChallengeData(prev => ({
      ...prev,
      judgingCriteria: prev.judgingCriteria?.map((c, i) => i === index ? criteria : c) || []
    }));
  }, []);

  const handleRemoveCriteria = useCallback((index: number) => {
    setChallengeData(prev => ({
      ...prev,
      judgingCriteria: prev.judgingCriteria?.filter((_, i) => i !== index) || []
    }));
  }, []);

  // Handle save
  const handleSave = useCallback(async () => {
    try {
      setLoading(true);
      await onSave(challengeData);
    } catch (error) {
      console.error('Error saving challenge:', error);
    } finally {
      setLoading(false);
    }
  }, [challengeData, onSave]);

  // Navigation
  const canGoNext = currentStep < steps.length - 1;
  const canGoPrev = currentStep > 0;
  const canSave = challengeData.title && challengeData.description;

  // Render step content
  const renderStepContent = () => {
    switch (steps[currentStep].id) {
      case 'template':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Choose a Template</h3>
              <p className="text-gray-400 mb-4">Start with a pre-configured template or create from scratch</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {challengeTemplates.map((template) => {
                const Icon = template.icon;
                return (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template.id)}
                    className={`p-4 rounded-lg border-2 transition-colors text-left ${
                      selectedTemplate === template.id
                        ? 'border-purple-500 bg-purple-500/10'
                        : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <Icon className="w-6 h-6 text-purple-400" />
                      <h4 className="font-semibold text-white">{template.name}</h4>
                    </div>
                    <p className="text-sm text-gray-400">{template.description}</p>
                    <div className="mt-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        template.type === 'individual' ? 'bg-blue-600 text-white' :
                        template.type === 'team' ? 'bg-green-600 text-white' :
                        'bg-purple-600 text-white'
                      }`}>
                        {template.type}
                      </span>
                    </div>
                  </button>
                );
              })}
              
              <button
                onClick={() => setSelectedTemplate('custom')}
                className={`p-4 rounded-lg border-2 transition-colors text-left ${
                  selectedTemplate === 'custom'
                    ? 'border-purple-500 bg-purple-500/10'
                    : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                }`}
              >
                <div className="flex items-center space-x-3 mb-2">
                  <Settings className="w-6 h-6 text-gray-400" />
                  <h4 className="font-semibold text-white">Custom</h4>
                </div>
                <p className="text-sm text-gray-400">Start from scratch with full customization</p>
              </button>
            </div>
          </div>
        );

      case 'basic':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Basic Information</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Challenge Title <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={challengeData.title || ''}
                  onChange={(e) => handleFieldChange('title', e.target.value)}
                  placeholder="Enter challenge title..."
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description <span className="text-red-400">*</span>
                </label>
                <textarea
                  value={challengeData.description || ''}
                  onChange={(e) => handleFieldChange('description', e.target.value)}
                  placeholder="Describe the challenge..."
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Challenge Type
                </label>
                <select
                  value={challengeData.type || 'individual'}
                  onChange={(e) => handleFieldChange('type', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="individual">Individual</option>
                  <option value="team">Team</option>
                  <option value="community">Community</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Participants (Optional)
                </label>
                <input
                  type="number"
                  value={challengeData.maxParticipants || ''}
                  onChange={(e) => handleFieldChange('maxParticipants', e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="No limit"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>
          </div>
        );

      case 'timeline':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Timeline & Dates</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Start Date <span className="text-red-400">*</span>
                </label>
                <input
                  type="datetime-local"
                  value={challengeData.startDate?.toISOString().slice(0, 16) || ''}
                  onChange={(e) => handleFieldChange('startDate', new Date(e.target.value))}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  End Date <span className="text-red-400">*</span>
                </label>
                <input
                  type="datetime-local"
                  value={challengeData.endDate?.toISOString().slice(0, 16) || ''}
                  onChange={(e) => handleFieldChange('endDate', new Date(e.target.value))}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Submission Deadline <span className="text-red-400">*</span>
                </label>
                <input
                  type="datetime-local"
                  value={challengeData.submissionDeadline?.toISOString().slice(0, 16) || ''}
                  onChange={(e) => handleFieldChange('submissionDeadline', new Date(e.target.value))}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Judging Deadline (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={challengeData.judgingDeadline?.toISOString().slice(0, 16) || ''}
                  onChange={(e) => handleFieldChange('judgingDeadline', e.target.value ? new Date(e.target.value) : undefined)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>
          </div>
        );

      case 'prizes':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Prizes & Rewards</h3>
              <AdminButton
                variant="secondary"
                size="sm"
                icon={Plus}
                onClick={handleAddPrize}
              >
                Add Prize
              </AdminButton>
            </div>
            
            <div className="space-y-4">
              {challengeData.prizes?.map((prize, index) => (
                <AdminCard key={index} className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-white">Prize #{prize.position}</h4>
                    <AdminButton
                      variant="secondary"
                      size="sm"
                      icon={Trash2}
                      onClick={() => handleRemovePrize(index)}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Title</label>
                      <input
                        type="text"
                        value={prize.title}
                        onChange={(e) => handleUpdatePrize(index, { ...prize, title: e.target.value })}
                        placeholder="Prize title..."
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Type</label>
                      <select
                        value={prize.type}
                        onChange={(e) => handleUpdatePrize(index, { ...prize, type: e.target.value as any })}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <option value="points">Points</option>
                        <option value="badge">Badge</option>
                        <option value="physical">Physical</option>
                        <option value="digital">Digital</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Value</label>
                      <input
                        type="number"
                        value={prize.value}
                        onChange={(e) => handleUpdatePrize(index, { ...prize, value: parseInt(e.target.value) || 0 })}
                        placeholder="0"
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                    
                    <div className="md:col-span-3">
                      <label className="block text-sm font-medium text-gray-300 mb-1">Description</label>
                      <textarea
                        value={prize.description}
                        onChange={(e) => handleUpdatePrize(index, { ...prize, description: e.target.value })}
                        placeholder="Prize description..."
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                  </div>
                </AdminCard>
              )) || (
                <div className="text-center py-8 text-gray-400">
                  No prizes added yet. Click "Add Prize" to get started.
                </div>
              )}
            </div>
          </div>
        );

      case 'judging':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Judging Criteria</h3>
              <AdminButton
                variant="secondary"
                size="sm"
                icon={Plus}
                onClick={handleAddCriteria}
              >
                Add Criteria
              </AdminButton>
            </div>
            
            <div className="space-y-4">
              {challengeData.judgingCriteria?.map((criteria, index) => (
                <AdminCard key={criteria.id} className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-white">Criteria #{index + 1}</h4>
                    <AdminButton
                      variant="secondary"
                      size="sm"
                      icon={Trash2}
                      onClick={() => handleRemoveCriteria(index)}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Name</label>
                      <input
                        type="text"
                        value={criteria.name}
                        onChange={(e) => handleUpdateCriteria(index, { ...criteria, name: e.target.value })}
                        placeholder="Criteria name..."
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Weight (%)</label>
                      <input
                        type="number"
                        value={criteria.weight}
                        onChange={(e) => handleUpdateCriteria(index, { ...criteria, weight: parseInt(e.target.value) || 0 })}
                        placeholder="0"
                        min="0"
                        max="100"
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Max Score</label>
                      <input
                        type="number"
                        value={criteria.maxScore}
                        onChange={(e) => handleUpdateCriteria(index, { ...criteria, maxScore: parseInt(e.target.value) || 10 })}
                        placeholder="10"
                        min="1"
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                    
                    <div className="md:col-span-3">
                      <label className="block text-sm font-medium text-gray-300 mb-1">Description</label>
                      <textarea
                        value={criteria.description}
                        onChange={(e) => handleUpdateCriteria(index, { ...criteria, description: e.target.value })}
                        placeholder="Criteria description..."
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                  </div>
                </AdminCard>
              )) || (
                <div className="text-center py-8 text-gray-400">
                  No judging criteria added yet. Click "Add Criteria" to get started.
                </div>
              )}
            </div>
            
            {challengeData.judgingCriteria && challengeData.judgingCriteria.length > 0 && (
              <div className="bg-gray-800 rounded-lg p-4">
                <h4 className="font-medium text-white mb-2">Weight Summary</h4>
                <div className="text-sm text-gray-400">
                  Total Weight: {challengeData.judgingCriteria.reduce((sum, c) => sum + c.weight, 0)}%
                  {challengeData.judgingCriteria.reduce((sum, c) => sum + c.weight, 0) !== 100 && (
                    <span className="text-yellow-400 ml-2">⚠️ Should total 100%</span>
                  )}
                </div>
              </div>
            )}
          </div>
        );

      case 'preview':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Challenge Preview</h3>
            </div>
            
            <AdminCard className="p-6">
              <div className="space-y-4">
                <div>
                  <h4 className="text-xl font-bold text-white">{challengeData.title}</h4>
                  <p className="text-gray-400 mt-2">{challengeData.description}</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Type:</span>
                    <p className="text-white capitalize">{challengeData.type}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Start Date:</span>
                    <p className="text-white">{challengeData.startDate?.toLocaleDateString()}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">End Date:</span>
                    <p className="text-white">{challengeData.endDate?.toLocaleDateString()}</p>
                  </div>
                </div>
                
                {challengeData.prizes && challengeData.prizes.length > 0 && (
                  <div>
                    <h5 className="font-medium text-white mb-2">Prizes</h5>
                    <div className="space-y-2">
                      {challengeData.prizes.map((prize, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-800 rounded p-2">
                          <span className="text-white">{prize.position}. {prize.title}</span>
                          <span className="text-gray-400">{prize.value} {prize.type}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {challengeData.judgingCriteria && challengeData.judgingCriteria.length > 0 && (
                  <div>
                    <h5 className="font-medium text-white mb-2">Judging Criteria</h5>
                    <div className="space-y-2">
                      {challengeData.judgingCriteria.map((criteria, index) => (
                        <div key={criteria.id} className="flex items-center justify-between bg-gray-800 rounded p-2">
                          <span className="text-white">{criteria.name}</span>
                          <span className="text-gray-400">{criteria.weight}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </AdminCard>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          <AdminCard className="h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center space-x-3">
                <Trophy className="w-6 h-6 text-purple-400" />
                <h2 className="text-xl font-bold text-white">
                  {challengeId ? 'Edit Challenge' : 'Create Challenge'}
                </h2>
              </div>
              <AdminButton
                variant="secondary"
                size="sm"
                icon={X}
                onClick={onClose}
              />
            </div>

            {/* Progress Steps */}
            <div className="px-6 py-4 border-b border-gray-700">
              <div className="flex items-center space-x-4">
                {steps.map((step, index) => {
                  const Icon = step.icon;
                  const isActive = index === currentStep;
                  const isCompleted = index < currentStep;
                  
                  return (
                    <div key={step.id} className="flex items-center">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                        isActive ? 'bg-purple-600 text-white' :
                        isCompleted ? 'bg-green-600 text-white' :
                        'bg-gray-700 text-gray-400'
                      }`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <span className={`ml-2 text-sm ${
                        isActive ? 'text-white font-medium' :
                        isCompleted ? 'text-green-400' :
                        'text-gray-400'
                      }`}>
                        {step.title}
                      </span>
                      {index < steps.length - 1 && (
                        <div className="w-8 h-px bg-gray-700 mx-4" />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {renderStepContent()}
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-6 border-t border-gray-700">
              <div className="flex space-x-3">
                {canGoPrev && (
                  <AdminButton
                    variant="secondary"
                    icon={ChevronLeft}
                    onClick={() => setCurrentStep(prev => prev - 1)}
                  >
                    Previous
                  </AdminButton>
                )}
              </div>
              
              <div className="flex space-x-3">
                {canGoNext ? (
                  <AdminButton
                    variant="primary"
                    icon={ChevronRight}
                    onClick={() => setCurrentStep(prev => prev + 1)}
                    disabled={currentStep === 0 && !selectedTemplate}
                  >
                    Next
                  </AdminButton>
                ) : (
                  <AdminButton
                    variant="primary"
                    icon={Save}
                    onClick={handleSave}
                    disabled={!canSave || loading}
                    loading={loading}
                  >
                    Save Challenge
                  </AdminButton>
                )}
              </div>
            </div>
          </AdminCard>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ChallengeBuilder;
