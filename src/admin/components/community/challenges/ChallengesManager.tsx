/**
 * Challenges Manager Component
 *
 * Comprehensive admin interface for managing community challenges and contests.
 * Provides challenge creation, judging tools, team management, and analytics.
 *
 * Features:
 * - Challenge lifecycle management (draft, active, judging, completed)
 * - Advanced challenge builder with templates and customization
 * - Judging interface with criteria-based scoring
 * - Team challenge management and collaboration tools
 * - Prize distribution and winner selection
 * - Real-time analytics and participation metrics
 * - Bulk operations for efficient management
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Trophy, 
  Filter, 
  Search, 
  Plus,
  MoreHorizontal,
  Calendar,
  Users,
  Award,
  Clock,
  TrendingUp,
  Eye,
  Edit,
  Copy,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  BarChart3,
  Settings,
  Target,
  Star
} from 'lucide-react';
import { AdminCard, AdminButton, AdminTable } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import { useChallengesData } from '../hooks/useChallengesData';
import { ChallengeBuilder } from './ChallengeBuilder';
import { JudgingInterface } from './JudgingInterface';
import { TeamManagementTools } from './TeamManagementTools';
import { CommunityStatsCard } from '../shared/CommunityStatsCard';
import { CommunityFilters } from '../shared/CommunityFilters';
import type { 
  ChallengeAdminInterface,
  ChallengeFilters
} from '../types';

interface ChallengesManagerProps {
  className?: string;
}

export const ChallengesManager: React.FC<ChallengesManagerProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [filters, setFilters] = useState<ChallengeFilters>({
    status: 'all',
    type: 'all',
    dateRange: { 
      start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), 
      end: new Date() 
    },
    category: [],
    searchTerm: ''
  });
  
  const [selectedChallenges, setSelectedChallenges] = useState<string[]>([]);
  const [showChallengeBuilder, setShowChallengeBuilder] = useState(false);
  const [showJudgingInterface, setShowJudgingInterface] = useState(false);
  const [showTeamManagement, setShowTeamManagement] = useState(false);
  const [currentChallenge, setCurrentChallenge] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');

  // Data hooks
  const {
    challenges,
    loading,
    error,
    totalCount,
    analytics,
    refetch,
    createChallenge,
    updateChallenge,
    duplicateChallenge,
    manageJudging,
    selectWinners
  } = useChallengesData(filters, { page: currentPage, pageSize: 20 });

  // Permission checks
  const canCreate = hasPermission('community_challenges', 'write');
  const canModerate = hasPermission('community_challenges', 'moderate');
  const canDelete = hasPermission('community_challenges', 'delete');

  // Event handlers
  const handleCreateChallenge = useCallback(() => {
    setCurrentChallenge(null);
    setShowChallengeBuilder(true);
  }, []);

  const handleEditChallenge = useCallback((challengeId: string) => {
    setCurrentChallenge(challengeId);
    setShowChallengeBuilder(true);
  }, []);

  const handleDuplicateChallenge = useCallback(async (challengeId: string) => {
    try {
      await duplicateChallenge(challengeId);
      await refetch();
    } catch (error) {
      console.error('Failed to duplicate challenge:', error);
    }
  }, [duplicateChallenge, refetch]);

  const handleStartJudging = useCallback((challengeId: string) => {
    setCurrentChallenge(challengeId);
    setShowJudgingInterface(true);
  }, []);

  const handleManageTeams = useCallback((challengeId: string) => {
    setCurrentChallenge(challengeId);
    setShowTeamManagement(true);
  }, []);

  const handleFilterChange = useCallback((newFilters: Partial<ChallengeFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  const handleSearch = useCallback((searchTerm: string) => {
    setFilters(prev => ({ ...prev, searchTerm }));
    setCurrentPage(1);
  }, []);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-600';
      case 'active': return 'bg-green-600';
      case 'judging': return 'bg-yellow-600';
      case 'completed': return 'bg-blue-600';
      case 'cancelled': return 'bg-red-600';
      default: return 'bg-gray-600';
    }
  };

  // Get challenge type icon
  const getChallengeTypeIcon = (type: string) => {
    switch (type) {
      case 'individual': return Trophy;
      case 'team': return Users;
      case 'community': return Target;
      default: return Trophy;
    }
  };

  // Table configuration
  const tableColumns = [
    {
      key: 'challenge',
      label: 'Challenge',
      sortable: true,
      render: (challenge: ChallengeAdminInterface) => {
        const TypeIcon = getChallengeTypeIcon(challenge.type);
        return (
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                <TypeIcon className="w-6 h-6 text-purple-400" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <p className="text-white font-medium truncate">{challenge.title}</p>
                {challenge.prizes.length > 0 && (
                  <Star className="w-4 h-4 text-yellow-400" title="Has Prizes" />
                )}
              </div>
              <p className="text-gray-400 text-sm truncate">{challenge.description}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500 capitalize">{challenge.type}</span>
                {challenge.categories.length > 0 && (
                  <span className="text-xs text-gray-500">{challenge.categories[0]}</span>
                )}
                {challenge.maxParticipants && (
                  <span className="text-xs text-gray-500">Max: {challenge.maxParticipants}</span>
                )}
              </div>
            </div>
          </div>
        );
      }
    },
    {
      key: 'participation',
      label: 'Participation',
      sortable: true,
      render: (challenge: ChallengeAdminInterface) => (
        <div className="text-center">
          <div className="text-lg font-bold text-white">{challenge.participantCount}</div>
          <div className="text-xs text-gray-400">Participants</div>
          <div className="text-sm text-gray-300 mt-1">{challenge.submissionCount} submissions</div>
        </div>
      )
    },
    {
      key: 'timeline',
      label: 'Timeline',
      sortable: true,
      render: (challenge: ChallengeAdminInterface) => (
        <div className="text-sm">
          <div className="flex items-center space-x-1 mb-1">
            <Calendar className="w-3 h-3 text-gray-500" />
            <span className="text-white">Start: {challenge.startDate.toLocaleDateString()}</span>
          </div>
          <div className="flex items-center space-x-1 mb-1">
            <Clock className="w-3 h-3 text-gray-500" />
            <span className="text-gray-400">End: {challenge.endDate.toLocaleDateString()}</span>
          </div>
          {challenge.judgingDeadline && (
            <div className="text-xs text-gray-500">
              Judging: {challenge.judgingDeadline.toLocaleDateString()}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (challenge: ChallengeAdminInterface) => (
        <div className="flex flex-col space-y-1">
          <span className={`px-2 py-1 text-xs font-medium rounded-full text-white ${getStatusColor(challenge.status)}`}>
            {challenge.status}
          </span>
          {challenge.judges.length > 0 && (
            <span className="text-xs text-gray-400">
              {challenge.judges.length} judge{challenge.judges.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (challenge: ChallengeAdminInterface) => (
        <div className="flex items-center space-x-2">
          <AdminButton
            variant="secondary"
            size="sm"
            icon={Eye}
            onClick={() => handleEditChallenge(challenge.id)}
            title="View/Edit Challenge"
          >
            View
          </AdminButton>
          
          {challenge.status === 'active' && challenge.submissionCount > 0 && (
            <AdminButton
              variant="secondary"
              size="sm"
              icon={Award}
              onClick={() => handleStartJudging(challenge.id)}
              title="Start Judging"
            >
              Judge
            </AdminButton>
          )}
          
          {challenge.type === 'team' && (
            <AdminButton
              variant="secondary"
              size="sm"
              icon={Users}
              onClick={() => handleManageTeams(challenge.id)}
              title="Manage Teams"
            >
              Teams
            </AdminButton>
          )}
          
          {canModerate && (
            <AdminButton
              variant="secondary"
              size="sm"
              icon={MoreHorizontal}
              onClick={() => {
                // Show action menu
              }}
              title="More Actions"
            />
          )}
        </div>
      )
    }
  ];

  // Stats data
  const statsData = [
    {
      title: 'Total Challenges',
      value: totalCount,
      icon: Trophy,
      color: 'purple' as const,
      trend: analytics?.totalChallenges || 0
    },
    {
      title: 'Active Challenges',
      value: challenges.filter(c => c.status === 'active').length,
      icon: Play,
      color: 'green' as const,
      trend: 0
    },
    {
      title: 'In Judging',
      value: challenges.filter(c => c.status === 'judging').length,
      icon: Award,
      color: 'yellow' as const,
      trend: 0
    },
    {
      title: 'Total Participants',
      value: challenges.reduce((sum, c) => sum + c.participantCount, 0),
      icon: Users,
      color: 'blue' as const,
      trend: 0
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Challenges Management</h1>
          <p className="text-gray-400">Create and manage community challenges and contests</p>
        </div>
        <div className="flex items-center space-x-3">
          {canCreate && (
            <AdminButton
              variant="primary"
              icon={Plus}
              onClick={handleCreateChallenge}
            >
              Create Challenge
            </AdminButton>
          )}
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={BarChart3}
            onClick={() => {/* Show analytics modal */}}
          >
            Analytics
          </AdminButton>
          <div className="flex bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('table')}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === 'table' ? 'bg-purple-600 text-white' : 'text-gray-400'
              }`}
            >
              Table
            </button>
            <button
              onClick={() => setViewMode('cards')}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === 'cards' ? 'bg-purple-600 text-white' : 'text-gray-400'
              }`}
            >
              Cards
            </button>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search challenges..."
            value={filters.searchTerm || ''}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <CommunityFilters
            filters={filters as any}
            onFiltersChange={handleFilterChange}
            type="challenges"
          />
        </motion.div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsData.map((stat, index) => (
          <CommunityStatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
          />
        ))}
      </div>

      {/* Main Content */}
      <AdminCard>
        <AdminTable
          columns={tableColumns}
          data={challenges}
          loading={loading}
          error={error}
          selectable={canModerate}
          selectedRows={selectedChallenges}
          onSelectionChange={setSelectedChallenges}
          pagination={{
            currentPage,
            totalPages: Math.ceil(totalCount / 20),
            pageSize: 20,
            onPageChange: setCurrentPage
          }}
          emptyMessage="No challenges found"
        />
      </AdminCard>

      {/* Challenge Builder Modal */}
      {showChallengeBuilder && (
        <ChallengeBuilder
          challengeId={currentChallenge}
          onClose={() => {
            setShowChallengeBuilder(false);
            setCurrentChallenge(null);
          }}
          onSave={async (challengeData) => {
            try {
              if (currentChallenge) {
                await updateChallenge(currentChallenge, challengeData);
              } else {
                await createChallenge(challengeData);
              }
              await refetch();
              setShowChallengeBuilder(false);
              setCurrentChallenge(null);
            } catch (error) {
              console.error('Failed to save challenge:', error);
            }
          }}
        />
      )}

      {/* Judging Interface Modal */}
      {showJudgingInterface && currentChallenge && (
        <JudgingInterface
          challengeId={currentChallenge}
          onClose={() => {
            setShowJudgingInterface(false);
            setCurrentChallenge(null);
          }}
          onComplete={async (winners) => {
            try {
              await selectWinners(currentChallenge, winners);
              await refetch();
              setShowJudgingInterface(false);
              setCurrentChallenge(null);
            } catch (error) {
              console.error('Failed to complete judging:', error);
            }
          }}
        />
      )}

      {/* Team Management Modal */}
      {showTeamManagement && currentChallenge && (
        <TeamManagementTools
          challengeId={currentChallenge}
          onClose={() => {
            setShowTeamManagement(false);
            setCurrentChallenge(null);
          }}
        />
      )}
    </div>
  );
};

export default ChallengesManager;
