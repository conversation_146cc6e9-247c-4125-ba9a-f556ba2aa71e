/**
 * Bulk Discussion Actions Component
 *
 * Floating action panel for performing bulk moderation operations
 * on multiple selected discussions simultaneously.
 *
 * Features:
 * - Bulk approve, reject, hide, and delete operations
 * - Confirmation dialogs for destructive actions
 * - Progress tracking for bulk operations
 * - Reason and notes input for audit trail
 * - Responsive design with mobile optimization
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle,
  XCircle,
  EyeOff,
  Trash2,
  AlertTriangle,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import type { BulkModerationAction } from '../types';

interface BulkDiscussionActionsProps {
  selectedCount: number;
  onBulkAction: (action: BulkModerationAction, reason: string) => Promise<void>;
  onClearSelection: () => void;
  className?: string;
}

interface BulkActionOption {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'green' | 'red' | 'yellow' | 'gray';
  requiresConfirmation: boolean;
  destructive: boolean;
  requiredPermission: string;
}

export const BulkDiscussionActions: React.FC<BulkDiscussionActionsProps> = ({
  selectedCount,
  onBulkAction,
  onClearSelection,
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [reason, setReason] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [loading, setLoading] = useState(false);

  // Available bulk actions
  const bulkActions: BulkActionOption[] = [
    {
      id: 'approve',
      label: 'Approve All',
      icon: CheckCircle,
      color: 'green',
      requiresConfirmation: false,
      destructive: false,
      requiredPermission: 'moderate'
    },
    {
      id: 'reject',
      label: 'Reject All',
      icon: XCircle,
      color: 'red',
      requiresConfirmation: true,
      destructive: true,
      requiredPermission: 'moderate'
    },
    {
      id: 'hide',
      label: 'Hide All',
      icon: EyeOff,
      color: 'yellow',
      requiresConfirmation: true,
      destructive: true,
      requiredPermission: 'moderate'
    },
    {
      id: 'delete',
      label: 'Delete All',
      icon: Trash2,
      color: 'red',
      requiresConfirmation: true,
      destructive: true,
      requiredPermission: 'delete'
    }
  ];

  // Filter actions based on permissions
  const availableActions = bulkActions.filter(action => 
    hasPermission('community_discussions', action.requiredPermission as any)
  );

  // Handle action selection
  const handleActionSelect = useCallback((actionId: string) => {
    const action = availableActions.find(a => a.id === actionId);
    if (!action) return;

    setSelectedAction(actionId);
    
    if (action.requiresConfirmation) {
      setShowConfirmation(true);
    } else {
      handleExecuteAction(actionId);
    }
  }, [availableActions]);

  // Execute bulk action
  const handleExecuteAction = useCallback(async (actionId: string) => {
    if (!reason.trim() && selectedAction !== 'approve') {
      return;
    }

    try {
      setLoading(true);
      
      const action: BulkModerationAction = {
        type: actionId as any,
        status: getStatusFromAction(actionId),
        reason: reason.trim() || 'Bulk action performed'
      };

      await onBulkAction(action, reason.trim() || 'Bulk action performed');
      
      // Reset state
      setSelectedAction('');
      setReason('');
      setShowConfirmation(false);
      setIsExpanded(false);
    } catch (error) {
      console.error('Error executing bulk action:', error);
    } finally {
      setLoading(false);
    }
  }, [reason, selectedAction, onBulkAction]);

  // Helper function to get status from action
  const getStatusFromAction = (action: string) => {
    switch (action) {
      case 'approve': return 'approved';
      case 'reject': return 'rejected';
      case 'hide': return 'hidden';
      case 'delete': return 'deleted';
      default: return 'pending';
    }
  };

  // Get action color classes
  const getActionColorClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-green-600 hover:bg-green-700 text-white';
      case 'red':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'yellow':
        return 'bg-yellow-600 hover:bg-yellow-700 text-white';
      default:
        return 'bg-gray-600 hover:bg-gray-700 text-white';
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 100 }}
        className={`fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40 ${className}`}
      >
        <AdminCard className="shadow-2xl border-2 border-blue-500/20">
          {/* Main Action Bar */}
          <div className="flex items-center space-x-4 p-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-white font-medium">
                {selectedCount} discussion{selectedCount !== 1 ? 's' : ''} selected
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <AdminButton
                variant="secondary"
                size="sm"
                icon={isExpanded ? ChevronDown : ChevronUp}
                onClick={() => setIsExpanded(!isExpanded)}
              >
                Actions
              </AdminButton>
              
              <AdminButton
                variant="secondary"
                size="sm"
                icon={X}
                onClick={onClearSelection}
              >
                Clear
              </AdminButton>
            </div>
          </div>

          {/* Expanded Actions */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border-t border-gray-700"
              >
                <div className="p-4 space-y-4">
                  {/* Quick Actions */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {availableActions.map((action) => {
                      const Icon = action.icon;
                      return (
                        <button
                          key={action.id}
                          onClick={() => handleActionSelect(action.id)}
                          disabled={loading}
                          className={`flex items-center justify-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${getActionColorClasses(action.color)} disabled:opacity-50`}
                        >
                          <Icon className="w-4 h-4" />
                          <span className="hidden sm:inline">{action.label}</span>
                        </button>
                      );
                    })}
                  </div>

                  {/* Reason Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Reason for bulk action
                    </label>
                    <textarea
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      placeholder="Provide a reason for this bulk action..."
                      rows={2}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    />
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </AdminCard>

        {/* Confirmation Modal */}
        <AnimatePresence>
          {showConfirmation && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="w-full max-w-md mx-4"
              >
                <AdminCard className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <AlertTriangle className="w-6 h-6 text-yellow-400" />
                    <h3 className="text-lg font-semibold text-white">Confirm Bulk Action</h3>
                  </div>

                  <p className="text-gray-300 mb-4">
                    Are you sure you want to{' '}
                    <span className="font-semibold text-white">
                      {availableActions.find(a => a.id === selectedAction)?.label.toLowerCase()}
                    </span>{' '}
                    {selectedCount} discussion{selectedCount !== 1 ? 's' : ''}?
                  </p>

                  {!reason.trim() && selectedAction !== 'approve' && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Reason <span className="text-red-400">*</span>
                      </label>
                      <textarea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="This action requires a reason..."
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    </div>
                  )}

                  <div className="flex space-x-3">
                    <AdminButton
                      variant="primary"
                      onClick={() => handleExecuteAction(selectedAction)}
                      disabled={loading || (!reason.trim() && selectedAction !== 'approve')}
                      loading={loading}
                      className="flex-1"
                    >
                      Confirm
                    </AdminButton>
                    <AdminButton
                      variant="secondary"
                      onClick={() => {
                        setShowConfirmation(false);
                        setSelectedAction('');
                      }}
                      disabled={loading}
                      className="flex-1"
                    >
                      Cancel
                    </AdminButton>
                  </div>
                </AdminCard>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  );
};

export default BulkDiscussionActions;
