/**
 * Discussion Moderation Panel Component
 *
 * Modal interface for reviewing and moderating individual discussion threads.
 * Provides detailed view of discussion content with comprehensive moderation tools.
 *
 * Features:
 * - Full discussion thread display with replies
 * - Moderation action buttons (approve, reject, hide, escalate)
 * - Flag and report management
 * - Admin notes and reason tracking
 * - User profile and history integration
 * - Real-time updates and optimistic UI
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X,
  Flag,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MessageSquare,
  User,
  Calendar,
  ThumbsUp,
  ThumbsDown,
  Shield,
  FileText,
  Clock
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import type { 
  DiscussionTableRow, 
  ModerationAction,
  ModerationFlag 
} from '../types';

interface DiscussionModerationPanelProps {
  discussionId: string;
  onClose: () => void;
  onAction: (discussionId: string, action: ModerationAction, reason: string) => Promise<void>;
  className?: string;
}

export const DiscussionModerationPanel: React.FC<DiscussionModerationPanelProps> = ({
  discussionId,
  onClose,
  onAction,
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State
  const [discussion, setDiscussion] = useState<DiscussionTableRow | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [reason, setReason] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [showFullContent, setShowFullContent] = useState(false);

  // Permission checks
  const canModerate = hasPermission('community_discussions', 'moderate');
  const canDelete = hasPermission('community_discussions', 'delete');

  // Load discussion data
  useEffect(() => {
    const loadDiscussion = async () => {
      try {
        setLoading(true);
        // This would fetch the full discussion data including replies
        // For now, using mock data
        const mockDiscussion: DiscussionTableRow = {
          id: discussionId,
          title: 'Sample Discussion Title',
          content: 'This is the full content of the discussion thread. It contains detailed information about the topic being discussed and may include multiple paragraphs of text.',
          author: {
            id: 'user123',
            displayName: 'John Doe',
            email: '<EMAIL>'
          },
          category: 'General Discussion',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000),
          replyCount: 15,
          viewCount: 234,
          likeCount: 42,
          moderationStatus: 'pending',
          escalationLevel: 'medium',
          flags: [
            {
              id: 'flag1',
              type: 'inappropriate',
              reason: 'Contains inappropriate language',
              reportedBy: 'user456',
              reportedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
              severity: 'medium'
            }
          ],
          isLocked: false,
          isPinned: false,
          tags: ['discussion', 'community'],
          moderatedBy: undefined,
          moderatedAt: undefined,
          moderationReason: undefined,
          adminNotes: undefined
        };
        
        setDiscussion(mockDiscussion);
      } catch (error) {
        console.error('Error loading discussion:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDiscussion();
  }, [discussionId]);

  // Handle moderation action
  const handleModerationAction = useCallback(async () => {
    if (!selectedAction || !reason.trim() || !discussion) return;

    try {
      setActionLoading(true);
      
      const action: ModerationAction = {
        type: selectedAction as any,
        status: getStatusFromAction(selectedAction),
        previousStatus: discussion.moderationStatus,
        reason: reason.trim(),
        adminId: '', // This would be filled by the hook
        timestamp: new Date()
      };

      await onAction(discussionId, action, reason.trim());
      onClose();
    } catch (error) {
      console.error('Error performing moderation action:', error);
    } finally {
      setActionLoading(false);
    }
  }, [selectedAction, reason, discussion, discussionId, onAction, onClose]);

  // Helper function to get status from action
  const getStatusFromAction = (action: string) => {
    switch (action) {
      case 'approve': return 'approved';
      case 'reject': return 'rejected';
      case 'hide': return 'hidden';
      case 'escalate': return 'escalated';
      default: return 'pending';
    }
  };

  // Render flag severity badge
  const renderFlagSeverity = (severity: string) => {
    const colors = {
      low: 'bg-yellow-600',
      medium: 'bg-orange-600',
      high: 'bg-red-600'
    };
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full text-white ${colors[severity as keyof typeof colors] || 'bg-gray-600'}`}>
        {severity}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <AdminCard className="w-full max-w-4xl mx-4 p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-700 rounded w-1/3"></div>
            <div className="h-4 bg-gray-700 rounded w-full"></div>
            <div className="h-4 bg-gray-700 rounded w-2/3"></div>
          </div>
        </AdminCard>
      </div>
    );
  }

  if (!discussion) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <AdminCard className="w-full max-w-md mx-4 p-6 text-center">
          <p className="text-red-400">Failed to load discussion</p>
          <AdminButton onClick={onClose} className="mt-4">
            Close
          </AdminButton>
        </AdminCard>
      </div>
    );
  }

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          <AdminCard className="h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center space-x-3">
                <MessageSquare className="w-6 h-6 text-blue-400" />
                <h2 className="text-xl font-bold text-white">Discussion Moderation</h2>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  discussion.moderationStatus === 'pending' ? 'bg-yellow-600 text-white' :
                  discussion.moderationStatus === 'approved' ? 'bg-green-600 text-white' :
                  discussion.moderationStatus === 'flagged' ? 'bg-red-600 text-white' :
                  'bg-gray-600 text-white'
                }`}>
                  {discussion.moderationStatus}
                </span>
              </div>
              <AdminButton
                variant="secondary"
                size="sm"
                icon={X}
                onClick={onClose}
              />
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* Discussion Info */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">{discussion.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-400 mb-4">
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{discussion.author.displayName}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{discussion.createdAt.toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="w-4 h-4" />
                        <span>{discussion.viewCount} views</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="prose prose-invert max-w-none">
                      <p className="text-gray-300">
                        {showFullContent ? discussion.content : `${discussion.content.substring(0, 300)}...`}
                      </p>
                      {discussion.content.length > 300 && (
                        <button
                          onClick={() => setShowFullContent(!showFullContent)}
                          className="text-blue-400 hover:text-blue-300 text-sm mt-2"
                        >
                          {showFullContent ? 'Show less' : 'Show more'}
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Engagement Stats */}
                  <div className="flex items-center space-x-6 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <ThumbsUp className="w-4 h-4" />
                      <span>{discussion.likeCount} likes</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MessageSquare className="w-4 h-4" />
                      <span>{discussion.replyCount} replies</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>Last activity: {discussion.lastActivity.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Sidebar Info */}
                <div className="space-y-4">
                  {/* Flags */}
                  {discussion.flags.length > 0 && (
                    <AdminCard className="p-4">
                      <h4 className="font-semibold text-white mb-3 flex items-center">
                        <Flag className="w-4 h-4 mr-2 text-red-400" />
                        Flags ({discussion.flags.length})
                      </h4>
                      <div className="space-y-3">
                        {discussion.flags.map((flag) => (
                          <div key={flag.id} className="border-l-2 border-red-400 pl-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium text-white capitalize">{flag.type}</span>
                              {renderFlagSeverity(flag.severity)}
                            </div>
                            <p className="text-xs text-gray-400 mb-1">{flag.reason}</p>
                            <p className="text-xs text-gray-500">
                              Reported {flag.reportedAt.toLocaleDateString()}
                            </p>
                          </div>
                        ))}
                      </div>
                    </AdminCard>
                  )}

                  {/* Author Info */}
                  <AdminCard className="p-4">
                    <h4 className="font-semibold text-white mb-3">Author Information</h4>
                    <div className="space-y-2 text-sm">
                      <p className="text-gray-300">{discussion.author.displayName}</p>
                      <p className="text-gray-400">{discussion.author.email}</p>
                      <p className="text-gray-500">Member since: Jan 2024</p>
                      <p className="text-gray-500">Total posts: 45</p>
                    </div>
                  </AdminCard>

                  {/* Category & Tags */}
                  <AdminCard className="p-4">
                    <h4 className="font-semibold text-white mb-3">Classification</h4>
                    <div className="space-y-2">
                      <div>
                        <span className="text-xs text-gray-400">Category:</span>
                        <p className="text-sm text-white">{discussion.category}</p>
                      </div>
                      {discussion.tags.length > 0 && (
                        <div>
                          <span className="text-xs text-gray-400">Tags:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {discussion.tags.map(tag => (
                              <span key={tag} className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </AdminCard>
                </div>
              </div>
            </div>

            {/* Moderation Actions */}
            {canModerate && (
              <div className="border-t border-gray-700 p-6">
                <h4 className="font-semibold text-white mb-4">Moderation Actions</h4>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Action Selection */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Select Action
                      </label>
                      <select
                        value={selectedAction}
                        onChange={(e) => setSelectedAction(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Choose an action...</option>
                        <option value="approve">Approve Discussion</option>
                        <option value="reject">Reject Discussion</option>
                        <option value="hide">Hide Discussion</option>
                        <option value="escalate">Escalate to Higher Level</option>
                        {canDelete && <option value="delete">Delete Discussion</option>}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Reason <span className="text-red-400">*</span>
                      </label>
                      <textarea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Provide a reason for this action..."
                        rows={3}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Admin Notes */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Admin Notes (Optional)
                      </label>
                      <textarea
                        value={adminNotes}
                        onChange={(e) => setAdminNotes(e.target.value)}
                        placeholder="Internal notes for other admins..."
                        rows={3}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div className="flex space-x-3">
                      <AdminButton
                        variant="primary"
                        onClick={handleModerationAction}
                        disabled={!selectedAction || !reason.trim() || actionLoading}
                        loading={actionLoading}
                        className="flex-1"
                      >
                        Execute Action
                      </AdminButton>
                      <AdminButton
                        variant="secondary"
                        onClick={onClose}
                        disabled={actionLoading}
                      >
                        Cancel
                      </AdminButton>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </AdminCard>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default DiscussionModerationPanel;
