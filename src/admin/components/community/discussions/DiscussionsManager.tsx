/**
 * Discussions Manager Component
 *
 * Comprehensive admin interface for managing community discussions.
 * Provides moderation tools, bulk operations, analytics, and content oversight.
 *
 * Features:
 * - Discussion thread management with filtering and search
 * - Moderation actions (approve, reject, hide, escalate)
 * - Bulk operations for efficient moderation
 * - Real-time analytics and performance metrics
 * - Permission-based access control
 * - Responsive design with mobile optimization
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  MessageSquare, 
  Filter, 
  Search, 
  MoreHorizontal,
  Flag,
  Eye,
  Lock,
  Unlock,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Users,
  BarChart3
} from 'lucide-react';
import { AdminCard, AdminButton, AdminTable } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import { useDiscussionsData } from '../hooks/useDiscussionsData';
import { DiscussionModerationPanel } from './DiscussionModerationPanel';
import { BulkDiscussionActions } from './BulkDiscussionActions';
import { CommunityStatsCard } from '../shared/CommunityStatsCard';
import { CommunityFilters } from '../shared/CommunityFilters';
import type { 
  DiscussionFilters, 
  DiscussionTableRow, 
  ModerationAction,
  BulkModerationAction 
} from '../types';

interface DiscussionsManagerProps {
  className?: string;
}

export const DiscussionsManager: React.FC<DiscussionsManagerProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [filters, setFilters] = useState<DiscussionFilters>({
    status: 'all',
    category: [],
    dateRange: { 
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 
      end: new Date() 
    },
    escalationLevel: 'all',
    moderator: [],
    searchTerm: ''
  });
  
  const [selectedDiscussions, setSelectedDiscussions] = useState<string[]>([]);
  const [showModerationPanel, setShowModerationPanel] = useState(false);
  const [currentDiscussion, setCurrentDiscussion] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Data hooks
  const {
    discussions,
    loading,
    error,
    totalCount,
    analytics,
    refetch,
    moderateDiscussion,
    bulkModerateDiscussions
  } = useDiscussionsData(filters, { page: currentPage, pageSize: 20 });

  // Permission checks
  const canModerate = hasPermission('community_discussions', 'moderate');
  const canDelete = hasPermission('community_discussions', 'delete');
  const canWrite = hasPermission('community_discussions', 'write');

  // Event handlers
  const handleModerationAction = useCallback(async (
    discussionId: string,
    action: ModerationAction,
    reason: string
  ) => {
    try {
      await moderateDiscussion(discussionId, action, reason);
      await refetch();
      setShowModerationPanel(false);
      setCurrentDiscussion(null);
    } catch (error) {
      console.error('Moderation action failed:', error);
    }
  }, [moderateDiscussion, refetch]);

  const handleBulkAction = useCallback(async (
    action: BulkModerationAction,
    reason: string
  ) => {
    try {
      await bulkModerateDiscussions(selectedDiscussions, action, reason);
      await refetch();
      setSelectedDiscussions([]);
    } catch (error) {
      console.error('Bulk moderation failed:', error);
    }
  }, [bulkModerateDiscussions, selectedDiscussions, refetch]);

  const handleFilterChange = useCallback((newFilters: Partial<DiscussionFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  const handleSearch = useCallback((searchTerm: string) => {
    setFilters(prev => ({ ...prev, searchTerm }));
    setCurrentPage(1);
  }, []);

  // Table configuration
  const tableColumns = [
    {
      key: 'title',
      label: 'Discussion',
      sortable: true,
      render: (discussion: DiscussionTableRow) => (
        <div className="flex items-start space-x-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <p className="text-white font-medium truncate">{discussion.title}</p>
              {discussion.isPinned && (
                <span className="text-yellow-400" title="Pinned">📌</span>
              )}
              {discussion.isLocked && (
                <Lock size={14} className="text-red-400" title="Locked" />
              )}
            </div>
            <p className="text-gray-400 text-sm">by {discussion.author.displayName}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className="text-xs text-gray-500">{discussion.category}</span>
              {discussion.flags.length > 0 && (
                <span className="flex items-center text-xs text-red-400">
                  <Flag size={12} className="mr-1" />
                  {discussion.flags.length}
                </span>
              )}
              {discussion.tags.length > 0 && (
                <div className="flex space-x-1">
                  {discussion.tags.slice(0, 2).map(tag => (
                    <span key={tag} className="px-1 py-0.5 text-xs bg-gray-700 text-gray-300 rounded">
                      {tag}
                    </span>
                  ))}
                  {discussion.tags.length > 2 && (
                    <span className="text-xs text-gray-500">+{discussion.tags.length - 2}</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'activity',
      label: 'Activity',
      sortable: true,
      render: (discussion: DiscussionTableRow) => (
        <div className="text-sm">
          <p className="text-white">{discussion.replyCount} replies</p>
          <p className="text-gray-400">{discussion.viewCount} views</p>
          <p className="text-gray-400">{discussion.likeCount} likes</p>
          <p className="text-gray-500 text-xs">
            {discussion.lastActivity.toLocaleDateString()}
          </p>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (discussion: DiscussionTableRow) => (
        <div className="flex flex-col space-y-1">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            discussion.moderationStatus === 'approved' ? 'bg-green-600 text-white' :
            discussion.moderationStatus === 'pending' ? 'bg-yellow-600 text-white' :
            discussion.moderationStatus === 'flagged' ? 'bg-red-600 text-white' :
            discussion.moderationStatus === 'hidden' ? 'bg-gray-600 text-white' :
            'bg-blue-600 text-white'
          }`}>
            {discussion.moderationStatus}
          </span>
          {discussion.escalationLevel !== 'none' && (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              discussion.escalationLevel === 'critical' ? 'bg-red-500 text-white' :
              discussion.escalationLevel === 'high' ? 'bg-orange-500 text-white' :
              discussion.escalationLevel === 'medium' ? 'bg-yellow-500 text-white' :
              'bg-blue-500 text-white'
            }`}>
              {discussion.escalationLevel}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'moderator',
      label: 'Moderator',
      render: (discussion: DiscussionTableRow) => (
        <div className="text-sm">
          {discussion.moderatedBy ? (
            <div>
              <p className="text-white">{discussion.moderatedBy}</p>
              <p className="text-gray-500 text-xs">
                {discussion.moderatedAt?.toLocaleDateString()}
              </p>
            </div>
          ) : (
            <span className="text-gray-500">Unmoderated</span>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (discussion: DiscussionTableRow) => (
        <div className="flex items-center space-x-2">
          <AdminButton
            variant="secondary"
            size="sm"
            icon={Eye}
            onClick={() => {
              setCurrentDiscussion(discussion.id);
              setShowModerationPanel(true);
            }}
            title="Review Discussion"
          >
            Review
          </AdminButton>
          {canModerate && (
            <AdminButton
              variant="secondary"
              size="sm"
              icon={MoreHorizontal}
              onClick={() => {
                // Show quick action menu
              }}
              title="More Actions"
            />
          )}
        </div>
      )
    }
  ];

  // Stats data
  const statsData = [
    {
      title: 'Total Discussions',
      value: totalCount,
      icon: MessageSquare,
      color: 'blue' as const,
      trend: analytics?.trendData?.[0]?.totalDiscussions || 0
    },
    {
      title: 'Pending Review',
      value: discussions.filter(d => d.moderationStatus === 'pending').length,
      icon: Clock,
      color: 'yellow' as const,
      trend: 0
    },
    {
      title: 'Flagged Content',
      value: discussions.filter(d => d.flags.length > 0).length,
      icon: Flag,
      color: 'red' as const,
      trend: 0
    },
    {
      title: 'Active Today',
      value: discussions.filter(d => {
        const today = new Date();
        return d.lastActivity.toDateString() === today.toDateString();
      }).length,
      icon: TrendingUp,
      color: 'green' as const,
      trend: 0
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Discussions Management</h1>
          <p className="text-gray-400">Moderate community discussions and threads</p>
        </div>
        <div className="flex items-center space-x-3">
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={BarChart3}
            onClick={() => {/* Show analytics modal */}}
          >
            Analytics
          </AdminButton>
          {selectedDiscussions.length > 0 && (
            <AdminButton
              variant="primary"
              onClick={() => {/* Show bulk actions */}}
            >
              Bulk Actions ({selectedDiscussions.length})
            </AdminButton>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search discussions..."
            value={filters.searchTerm || ''}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <CommunityFilters
            filters={filters}
            onFiltersChange={handleFilterChange}
            type="discussions"
          />
        </motion.div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsData.map((stat, index) => (
          <CommunityStatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
          />
        ))}
      </div>

      {/* Main Table */}
      <AdminCard>
        <AdminTable
          columns={tableColumns}
          data={discussions}
          loading={loading}
          error={error}
          selectable={canModerate}
          selectedRows={selectedDiscussions}
          onSelectionChange={setSelectedDiscussions}
          pagination={{
            currentPage,
            totalPages: Math.ceil(totalCount / 20),
            pageSize: 20,
            onPageChange: setCurrentPage
          }}
          emptyMessage="No discussions found"
        />
      </AdminCard>

      {/* Moderation Panel */}
      {showModerationPanel && currentDiscussion && (
        <DiscussionModerationPanel
          discussionId={currentDiscussion}
          onClose={() => {
            setShowModerationPanel(false);
            setCurrentDiscussion(null);
          }}
          onAction={handleModerationAction}
        />
      )}

      {/* Bulk Actions */}
      {selectedDiscussions.length > 0 && canModerate && (
        <BulkDiscussionActions
          selectedCount={selectedDiscussions.length}
          onBulkAction={handleBulkAction}
          onClearSelection={() => setSelectedDiscussions([])}
        />
      )}
    </div>
  );
};

export default DiscussionsManager;
