/**
 * Challenges Data Hook
 *
 * Custom React hook for managing challenges data in admin interface.
 * Provides CRUD operations, judging tools, team management, and analytics.
 *
 * Features:
 * - Real-time challenges data with advanced filtering
 * - Challenge lifecycle management (draft, active, judging, completed)
 * - Judging interface with criteria-based scoring
 * - Team management and collaboration tools
 * - Prize distribution and winner selection
 * - Analytics and performance metrics
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  getDocs,
  doc,
  updateDoc,
  addDoc,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '../../../../lib/firebase';
import { COLLECTIONS } from '../../../../lib/firebase/collections';
import { COMMUNITY_ADMIN_COLLECTIONS } from '../../../../lib/firebase/communityAdminCollections';
import { useUser } from '../../../../lib/useUser';
import type { 
  ChallengeAdminInterface,
  ChallengeFilters,
  ChallengePrize,
  JudgingCriteria,
  PaginationParams,
  PaginatedResult
} from '../types';

interface ChallengeFilters {
  status: 'all' | 'draft' | 'active' | 'judging' | 'completed' | 'cancelled';
  type: 'all' | 'individual' | 'team' | 'community';
  dateRange: { start: Date; end: Date };
  category: string[];
  searchTerm?: string;
}

interface Winner {
  userId: string;
  position: number;
  prize: ChallengePrize;
  score?: number;
  feedback?: string;
}

interface ChallengeAnalytics {
  totalChallenges: number;
  activeChallenges: number;
  completedChallenges: number;
  totalParticipants: number;
  averageParticipation: number;
  completionRate: number;
}

interface UseChallengesDataOptions {
  page?: number;
  pageSize?: number;
  realtime?: boolean;
}

interface UseChallengesDataReturn {
  challenges: ChallengeAdminInterface[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  analytics: ChallengeAnalytics | null;
  refetch: () => Promise<void>;
  createChallenge: (challengeData: Partial<ChallengeAdminInterface>) => Promise<string>;
  updateChallenge: (challengeId: string, updates: Partial<ChallengeAdminInterface>) => Promise<void>;
  duplicateChallenge: (challengeId: string) => Promise<string>;
  manageJudging: (challengeId: string) => Promise<any>;
  selectWinners: (challengeId: string, winners: Winner[]) => Promise<void>;
}

export const useChallengesData = (
  filters: ChallengeFilters,
  options: UseChallengesDataOptions = {}
): UseChallengesDataReturn => {
  const { user } = useUser();
  const { page = 1, pageSize = 20, realtime = true } = options;

  // State
  const [challenges, setChallenges] = useState<ChallengeAdminInterface[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [analytics, setAnalytics] = useState<ChallengeAnalytics | null>(null);

  // Build Firestore query based on filters
  const buildQuery = useCallback(() => {
    let q = collection(db, COLLECTIONS.CHALLENGES);
    const constraints = [];

    // Status filter
    if (filters.status !== 'all') {
      constraints.push(where('status', '==', filters.status));
    }

    // Type filter
    if (filters.type !== 'all') {
      constraints.push(where('type', '==', filters.type));
    }

    // Category filter
    if (filters.category.length > 0) {
      constraints.push(where('categories', 'array-contains-any', filters.category));
    }

    // Date range filter
    constraints.push(where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)));
    constraints.push(where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end)));

    // Add ordering and pagination
    constraints.push(orderBy('createdAt', 'desc'));
    constraints.push(limit(pageSize));

    return query(q, ...constraints);
  }, [filters, page, pageSize]);

  // Fetch challenges data
  const fetchChallenges = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const q = buildQuery();
      const snapshot = await getDocs(q);
      
      const challengesData = snapshot.docs.map(docSnapshot => {
        const data = docSnapshot.data();
        
        return {
          id: docSnapshot.id,
          ...data,
          startDate: data.startDate?.toDate() || new Date(),
          endDate: data.endDate?.toDate() || new Date(),
          submissionDeadline: data.submissionDeadline?.toDate() || new Date(),
          judgingDeadline: data.judgingDeadline?.toDate(),
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          // Ensure required fields have defaults
          status: data.status || 'draft',
          type: data.type || 'individual',
          participantCount: data.participantCount || 0,
          submissionCount: data.submissionCount || 0,
          prizes: data.prizes || [],
          judgingCriteria: data.judgingCriteria || [],
          judges: data.judges || [],
          categories: data.categories || [],
          rules: data.rules || []
        } as ChallengeAdminInterface;
      });

      setChallenges(challengesData);
      
      // Get total count
      setTotalCount(await getTotalChallengesCount(filters));
      
      // Fetch analytics data
      const analyticsData = await fetchChallengeAnalytics(filters.dateRange);
      setAnalytics(analyticsData);

    } catch (err) {
      console.error('Error fetching challenges:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch challenges');
    } finally {
      setLoading(false);
    }
  }, [buildQuery, filters]);

  // Set up real-time listener
  useEffect(() => {
    if (!realtime) {
      fetchChallenges();
      return;
    }

    const q = buildQuery();
    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const challengesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          startDate: doc.data().startDate?.toDate() || new Date(),
          endDate: doc.data().endDate?.toDate() || new Date(),
          submissionDeadline: doc.data().submissionDeadline?.toDate() || new Date(),
          judgingDeadline: doc.data().judgingDeadline?.toDate(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date()
        })) as ChallengeAdminInterface[];

        setChallenges(challengesData);
        setLoading(false);
      },
      (err) => {
        console.error('Error in challenges listener:', err);
        setError(err.message);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [buildQuery, realtime, fetchChallenges]);

  // Create new challenge
  const createChallenge = useCallback(async (
    challengeData: Partial<ChallengeAdminInterface>
  ): Promise<string> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const challengeRef = await addDoc(collection(db, COLLECTIONS.CHALLENGES), {
        ...challengeData,
        createdBy: user.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: challengeData.status || 'draft',
        participantCount: 0,
        submissionCount: 0
      });

      // Add to admin audit log
      const auditRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG));
      await addDoc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG), {
        adminId: user.uid,
        action: 'create_challenge',
        resourceType: 'challenge',
        resourceId: challengeRef.id,
        details: { title: challengeData.title, type: challengeData.type },
        timestamp: serverTimestamp()
      });

      return challengeRef.id;
    } catch (error) {
      console.error('Error creating challenge:', error);
      throw error;
    }
  }, [user]);

  // Update existing challenge
  const updateChallenge = useCallback(async (
    challengeId: string,
    updates: Partial<ChallengeAdminInterface>
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const challengeRef = doc(db, COLLECTIONS.CHALLENGES, challengeId);
      await updateDoc(challengeRef, {
        ...updates,
        updatedAt: serverTimestamp(),
        updatedBy: user.uid
      });

      // Add to audit log
      await addDoc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG), {
        adminId: user.uid,
        action: 'update_challenge',
        resourceType: 'challenge',
        resourceId: challengeId,
        details: updates,
        timestamp: serverTimestamp()
      });

      // Optimistic update
      setChallenges(prev => prev.map(challenge => 
        challenge.id === challengeId 
          ? { ...challenge, ...updates, updatedAt: new Date() }
          : challenge
      ));
    } catch (error) {
      console.error('Error updating challenge:', error);
      throw error;
    }
  }, [user]);

  // Duplicate existing challenge
  const duplicateChallenge = useCallback(async (challengeId: string): Promise<string> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const originalChallenge = challenges.find(c => c.id === challengeId);
      if (!originalChallenge) throw new Error('Challenge not found');

      const duplicatedData = {
        ...originalChallenge,
        title: `${originalChallenge.title} (Copy)`,
        status: 'draft' as const,
        participantCount: 0,
        submissionCount: 0,
        // Reset dates to future
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        submissionDeadline: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000)
      };

      // Remove fields that shouldn't be duplicated
      delete (duplicatedData as any).id;
      delete (duplicatedData as any).createdAt;
      delete (duplicatedData as any).updatedAt;

      return await createChallenge(duplicatedData);
    } catch (error) {
      console.error('Error duplicating challenge:', error);
      throw error;
    }
  }, [challenges, createChallenge, user]);

  // Manage judging process
  const manageJudging = useCallback(async (challengeId: string) => {
    try {
      // This would return judging interface data
      // For now, return mock data
      return {
        challengeId,
        submissions: [],
        judges: [],
        criteria: [],
        scores: {}
      };
    } catch (error) {
      console.error('Error managing judging:', error);
      throw error;
    }
  }, []);

  // Select winners and complete challenge
  const selectWinners = useCallback(async (
    challengeId: string,
    winners: Winner[]
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const batch = writeBatch(db);
      
      // Update challenge status
      const challengeRef = doc(db, COLLECTIONS.CHALLENGES, challengeId);
      batch.update(challengeRef, {
        status: 'completed',
        winners: winners.map(w => ({
          userId: w.userId,
          position: w.position,
          prize: w.prize
        })),
        completedAt: serverTimestamp(),
        completedBy: user.uid
      });

      // Create winner records
      for (const winner of winners) {
        const winnerRef = doc(collection(db, 'challengeWinners'));
        batch.set(winnerRef, {
          challengeId,
          userId: winner.userId,
          position: winner.position,
          prize: winner.prize,
          score: winner.score,
          feedback: winner.feedback,
          awardedAt: serverTimestamp(),
          awardedBy: user.uid
        });
      }

      // Add to audit log
      const auditRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG));
      batch.set(auditRef, {
        adminId: user.uid,
        action: 'select_challenge_winners',
        resourceType: 'challenge',
        resourceId: challengeId,
        details: { winnersCount: winners.length, winners },
        timestamp: serverTimestamp()
      });

      await batch.commit();

      // Optimistic update
      setChallenges(prev => prev.map(challenge => 
        challenge.id === challengeId 
          ? { ...challenge, status: 'completed' as const }
          : challenge
      ));
    } catch (error) {
      console.error('Error selecting winners:', error);
      throw error;
    }
  }, [user]);

  // Helper functions
  const getTotalChallengesCount = async (filters: ChallengeFilters): Promise<number> => {
    // Mock count - in real implementation, use aggregation or count collection
    return 45;
  };

  const fetchChallengeAnalytics = async (dateRange: { start: Date; end: Date }): Promise<ChallengeAnalytics> => {
    // Mock analytics - in real implementation, fetch from analytics collection
    return {
      totalChallenges: 45,
      activeChallenges: 8,
      completedChallenges: 32,
      totalParticipants: 1250,
      averageParticipation: 28,
      completionRate: 0.85
    };
  };

  return {
    challenges,
    loading,
    error,
    totalCount,
    analytics,
    refetch: fetchChallenges,
    createChallenge,
    updateChallenge,
    duplicateChallenge,
    manageJudging,
    selectWinners
  };
};
