/**
 * Discussions Data Hook
 *
 * Custom React hook for managing discussions data in admin interface.
 * Provides CRUD operations, moderation actions, and real-time updates.
 *
 * Features:
 * - Real-time discussions data with filtering
 * - Moderation actions (approve, reject, hide, escalate)
 * - Bulk operations for efficient management
 * - Analytics and performance metrics
 * - Error handling and loading states
 * - Optimistic updates for better UX
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  getDocs,
  doc,
  updateDoc,
  addDoc,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '../../../../lib/firebase';
import { COLLECTIONS } from '../../../../lib/firebase/collections';
import { useUser } from '../../../../lib/useUser';
import type { 
  DiscussionFilters, 
  DiscussionTableRow, 
  DiscussionAnalytics,
  ModerationAction,
  BulkModerationAction,
  ModerationResult,
  BulkModerationResult,
  PaginationParams,
  PaginatedResult
} from '../types';

interface UseDiscussionsDataOptions {
  page?: number;
  pageSize?: number;
  realtime?: boolean;
}

interface UseDiscussionsDataReturn {
  discussions: DiscussionTableRow[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  analytics: DiscussionAnalytics | null;
  refetch: () => Promise<void>;
  moderateDiscussion: (
    discussionId: string, 
    action: ModerationAction, 
    reason: string
  ) => Promise<ModerationResult>;
  bulkModerateDiscussions: (
    discussionIds: string[], 
    action: BulkModerationAction, 
    reason: string
  ) => Promise<BulkModerationResult>;
}

export const useDiscussionsData = (
  filters: DiscussionFilters,
  options: UseDiscussionsDataOptions = {}
): UseDiscussionsDataReturn => {
  const { user } = useUser();
  const { page = 1, pageSize = 20, realtime = true } = options;

  // State
  const [discussions, setDiscussions] = useState<DiscussionTableRow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [analytics, setAnalytics] = useState<DiscussionAnalytics | null>(null);

  // Build Firestore query based on filters
  const buildQuery = useCallback(() => {
    let q = collection(db, COLLECTIONS.DISCUSSIONS);
    const constraints = [];

    // Status filter
    if (filters.status !== 'all') {
      constraints.push(where('moderationStatus', '==', filters.status));
    }

    // Category filter
    if (filters.category.length > 0) {
      constraints.push(where('category', 'in', filters.category));
    }

    // Escalation level filter
    if (filters.escalationLevel !== 'all') {
      constraints.push(where('escalationLevel', '==', filters.escalationLevel));
    }

    // Date range filter
    constraints.push(where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)));
    constraints.push(where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end)));

    // Moderator filter
    if (filters.moderator.length > 0) {
      constraints.push(where('moderatedBy', 'in', filters.moderator));
    }

    // Add ordering and pagination
    constraints.push(orderBy('createdAt', 'desc'));
    constraints.push(limit(pageSize));

    // Add pagination cursor if not first page
    if (page > 1) {
      // This would need to be implemented with proper cursor handling
      // For now, we'll use offset-based pagination (not ideal for large datasets)
      const offset = (page - 1) * pageSize;
      // Note: Firestore doesn't support offset directly, this is a simplified implementation
    }

    return query(q, ...constraints);
  }, [filters, page, pageSize]);

  // Fetch discussions data
  const fetchDiscussions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const q = buildQuery();
      const snapshot = await getDocs(q);
      
      const discussionsData = await Promise.all(
        snapshot.docs.map(async (docSnapshot) => {
          const data = docSnapshot.data();
          
          // Fetch author profile
          const authorProfile = await fetchUserProfile(data.authorId);
          
          // Convert Firestore timestamps to Date objects
          return {
            id: docSnapshot.id,
            ...data,
            author: authorProfile,
            createdAt: data.createdAt?.toDate() || new Date(),
            lastActivity: data.lastActivity?.toDate() || new Date(),
            moderatedAt: data.moderatedAt?.toDate(),
            // Ensure required fields have defaults
            moderationStatus: data.moderationStatus || 'pending',
            escalationLevel: data.escalationLevel || 'none',
            flags: data.flags || [],
            tags: data.tags || [],
            replyCount: data.replyCount || 0,
            viewCount: data.viewCount || 0,
            likeCount: data.likeCount || 0,
            isLocked: data.isLocked || false,
            isPinned: data.isPinned || false
          } as DiscussionTableRow;
        })
      );

      setDiscussions(discussionsData);
      
      // Get total count (this would typically be cached or computed separately)
      setTotalCount(await getTotalDiscussionsCount(filters));
      
      // Fetch analytics data
      const analyticsData = await fetchDiscussionAnalytics(filters.dateRange);
      setAnalytics(analyticsData);

    } catch (err) {
      console.error('Error fetching discussions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch discussions');
    } finally {
      setLoading(false);
    }
  }, [buildQuery, filters]);

  // Set up real-time listener
  useEffect(() => {
    if (!realtime) {
      fetchDiscussions();
      return;
    }

    const q = buildQuery();
    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const discussionsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          lastActivity: doc.data().lastActivity?.toDate() || new Date(),
          moderatedAt: doc.data().moderatedAt?.toDate()
        })) as DiscussionTableRow[];

        setDiscussions(discussionsData);
        setLoading(false);
      },
      (err) => {
        console.error('Error in discussions listener:', err);
        setError(err.message);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [buildQuery, realtime, fetchDiscussions]);

  // Moderate single discussion
  const moderateDiscussion = useCallback(async (
    discussionId: string,
    action: ModerationAction,
    reason: string
  ): Promise<ModerationResult> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const discussionRef = doc(db, COLLECTIONS.DISCUSSIONS, discussionId);
      const moderationRef = doc(collection(db, 'discussionModeration'));
      
      const batch = writeBatch(db);
      
      // Update discussion status
      batch.update(discussionRef, {
        moderationStatus: action.status,
        moderatedAt: serverTimestamp(),
        moderatedBy: user.uid,
        moderationReason: reason
      });
      
      // Create moderation record
      batch.set(moderationRef, {
        discussionId,
        action: action.type,
        reason,
        adminId: user.uid,
        timestamp: serverTimestamp(),
        previousStatus: action.previousStatus,
        newStatus: action.status
      });
      
      // Add to audit log
      const auditRef = doc(collection(db, 'adminAuditLog'));
      batch.set(auditRef, {
        adminId: user.uid,
        action: 'moderate_discussion',
        resourceType: 'discussion',
        resourceId: discussionId,
        details: { action: action.type, reason },
        timestamp: serverTimestamp()
      });
      
      await batch.commit();
      
      // Optimistic update
      setDiscussions(prev => prev.map(discussion => 
        discussion.id === discussionId 
          ? { 
              ...discussion, 
              moderationStatus: action.status,
              moderatedBy: user.uid,
              moderatedAt: new Date(),
              moderationReason: reason
            }
          : discussion
      ));
      
      return {
        success: true,
        discussionId,
        action: action.type,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error moderating discussion:', error);
      throw error;
    }
  }, [user]);

  // Bulk moderate discussions
  const bulkModerateDiscussions = useCallback(async (
    discussionIds: string[],
    action: BulkModerationAction,
    reason: string
  ): Promise<BulkModerationResult> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const batch = writeBatch(db);
      const results: ModerationResult[] = [];
      
      for (const discussionId of discussionIds) {
        const discussionRef = doc(db, COLLECTIONS.DISCUSSIONS, discussionId);
        const moderationRef = doc(collection(db, 'discussionModeration'));
        
        // Update discussion
        batch.update(discussionRef, {
          moderationStatus: action.status,
          moderatedAt: serverTimestamp(),
          moderatedBy: user.uid,
          moderationReason: reason
        });
        
        // Create moderation record
        batch.set(moderationRef, {
          discussionId,
          action: action.type,
          reason,
          adminId: user.uid,
          timestamp: serverTimestamp(),
          bulkOperation: true
        });
        
        results.push({
          success: true,
          discussionId,
          action: action.type,
          timestamp: new Date()
        });
      }
      
      // Add bulk audit log entry
      const auditRef = doc(collection(db, 'adminAuditLog'));
      batch.set(auditRef, {
        adminId: user.uid,
        action: 'bulk_moderate_discussions',
        resourceType: 'discussion',
        resourceIds: discussionIds,
        details: { action: action.type, reason, count: discussionIds.length },
        timestamp: serverTimestamp()
      });
      
      await batch.commit();
      
      // Optimistic update
      setDiscussions(prev => prev.map(discussion => 
        discussionIds.includes(discussion.id)
          ? { 
              ...discussion, 
              moderationStatus: action.status,
              moderatedBy: user.uid,
              moderatedAt: new Date(),
              moderationReason: reason
            }
          : discussion
      ));
      
      return {
        success: true,
        results,
        totalProcessed: discussionIds.length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error bulk moderating discussions:', error);
      throw error;
    }
  }, [user]);

  // Helper functions
  const fetchUserProfile = async (userId: string) => {
    // This would fetch user profile from the profiles collection
    // For now, return a mock profile
    return {
      id: userId,
      displayName: 'User Name',
      email: '<EMAIL>'
    };
  };

  const getTotalDiscussionsCount = async (filters: DiscussionFilters): Promise<number> => {
    // This would typically use a separate count collection or aggregation
    // For now, return a mock count
    return 150;
  };

  const fetchDiscussionAnalytics = async (dateRange: { start: Date; end: Date }): Promise<DiscussionAnalytics> => {
    // This would fetch analytics data from a separate collection
    // For now, return mock analytics
    return {
      totalDiscussions: 150,
      moderationActions: 45,
      escalations: 8,
      averageResponseTime: 2.5,
      trendData: [],
      categoryBreakdown: [],
      moderatorPerformance: [],
      engagementMetrics: {
        views: 1250,
        likes: 340,
        comments: 890,
        shares: 45,
        saves: 120,
        engagementRate: 0.68
      }
    };
  };

  return {
    discussions,
    loading,
    error,
    totalCount,
    analytics,
    refetch: fetchDiscussions,
    moderateDiscussion,
    bulkModerateDiscussions
  };
};
