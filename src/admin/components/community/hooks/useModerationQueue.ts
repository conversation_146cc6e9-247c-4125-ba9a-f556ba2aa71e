/**
 * Moderation Queue Hook
 *
 * Custom React hook for managing the unified moderation queue.
 * Provides queue management, assignment, processing, and analytics.
 *
 * Features:
 * - Real-time moderation queue with priority sorting
 * - Multi-content type management (discussions, submissions, challenges)
 * - Assignment management and workload distribution
 * - Automated moderation integration
 * - Queue analytics and performance metrics
 * - Bulk operations and workflow automation
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  getDocs,
  doc,
  updateDoc,
  addDoc,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '../../../../lib/firebase';
import { COMMUNITY_ADMIN_COLLECTIONS } from '../../../../lib/firebase/communityAdminCollections';
import { useUser } from '../../../../lib/useUser';
import type { 
  ModerationQueueItem,
  ModerationAnalytics,
  PaginationParams,
  PaginatedResult
} from '../types';

interface ModerationFilters {
  priority: 'all' | 'urgent' | 'high' | 'medium' | 'low';
  contentType: 'all' | 'discussion' | 'submission' | 'challenge' | 'comment' | 'reply';
  status: 'all' | 'pending' | 'in_review' | 'completed' | 'escalated';
  assignedTo: 'all' | 'me' | 'unassigned' | string;
  dateRange: { start: Date; end: Date };
}

interface QueueStats {
  totalItems: number;
  urgentItems: number;
  assignedItems: number;
  unassignedItems: number;
  averageWaitTime: number;
  queueTrend: number;
}

interface UseModerationQueueOptions {
  page?: number;
  pageSize?: number;
  realtime?: boolean;
}

interface UseModerationQueueReturn {
  queueItems: ModerationQueueItem[];
  queueStats: QueueStats | null;
  analytics: ModerationAnalytics | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  refetch: () => Promise<void>;
  assignToSelf: (itemId: string) => Promise<void>;
  bulkAssign: (itemIds: string[], adminId: string) => Promise<void>;
  processItem: (itemId: string, action: string, reason: string) => Promise<void>;
  escalateItem: (itemId: string, reason: string) => Promise<void>;
}

export const useModerationQueue = (
  filters: ModerationFilters,
  options: UseModerationQueueOptions = {}
): UseModerationQueueReturn => {
  const { user } = useUser();
  const { page = 1, pageSize = 20, realtime = true } = options;

  // State
  const [queueItems, setQueueItems] = useState<ModerationQueueItem[]>([]);
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [analytics, setAnalytics] = useState<ModerationAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Build Firestore query based on filters
  const buildQuery = useCallback(() => {
    let q = collection(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE);
    const constraints = [];

    // Priority filter
    if (filters.priority !== 'all') {
      constraints.push(where('priority', '==', filters.priority));
    }

    // Content type filter
    if (filters.contentType !== 'all') {
      constraints.push(where('contentType', '==', filters.contentType));
    }

    // Status filter
    if (filters.status !== 'all') {
      constraints.push(where('status', '==', filters.status));
    }

    // Assignment filter
    if (filters.assignedTo === 'me' && user) {
      constraints.push(where('assignedTo', '==', user.uid));
    } else if (filters.assignedTo === 'unassigned') {
      constraints.push(where('assignedTo', '==', null));
    } else if (filters.assignedTo !== 'all') {
      constraints.push(where('assignedTo', '==', filters.assignedTo));
    }

    // Date range filter
    constraints.push(where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)));
    constraints.push(where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end)));

    // Add ordering - priority first, then creation time
    constraints.push(orderBy('priority', 'desc'));
    constraints.push(orderBy('createdAt', 'asc'));
    constraints.push(limit(pageSize));

    return query(q, ...constraints);
  }, [filters, page, pageSize, user]);

  // Fetch queue data
  const fetchQueueData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const q = buildQuery();
      const snapshot = await getDocs(q);
      
      const queueData = await Promise.all(
        snapshot.docs.map(async (docSnapshot) => {
          const data = docSnapshot.data();
          
          // Fetch author profile
          const authorProfile = await fetchUserProfile(data.authorId);
          
          return {
            id: docSnapshot.id,
            ...data,
            author: authorProfile,
            submittedAt: data.createdAt?.toDate() || new Date(),
            assignedAt: data.assignedAt?.toDate(),
            // Ensure required fields have defaults
            priority: data.priority || 'medium',
            status: data.status || 'pending',
            flags: data.flags || [],
            estimatedReviewTime: data.estimatedReviewTime || 15
          } as ModerationQueueItem;
        })
      );

      setQueueItems(queueData);
      
      // Get total count and stats
      const stats = await fetchQueueStats(filters);
      setQueueStats(stats);
      setTotalCount(stats.totalItems);
      
      // Fetch analytics data
      const analyticsData = await fetchModerationAnalytics(filters.dateRange);
      setAnalytics(analyticsData);

    } catch (err) {
      console.error('Error fetching moderation queue:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch moderation queue');
    } finally {
      setLoading(false);
    }
  }, [buildQuery, filters]);

  // Set up real-time listener
  useEffect(() => {
    if (!realtime) {
      fetchQueueData();
      return;
    }

    const q = buildQuery();
    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const queueData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          submittedAt: doc.data().createdAt?.toDate() || new Date(),
          assignedAt: doc.data().assignedAt?.toDate()
        })) as ModerationQueueItem[];

        setQueueItems(queueData);
        setLoading(false);
      },
      (err) => {
        console.error('Error in moderation queue listener:', err);
        setError(err.message);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [buildQuery, realtime, fetchQueueData]);

  // Assign item to current user
  const assignToSelf = useCallback(async (itemId: string): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const itemRef = doc(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE, itemId);
      await updateDoc(itemRef, {
        assignedTo: user.uid,
        assignedAt: serverTimestamp(),
        status: 'in_review'
      });

      // Add to audit log
      await addDoc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG), {
        adminId: user.uid,
        action: 'assign_moderation_item',
        resourceType: 'moderation_queue',
        resourceId: itemId,
        details: { assignedTo: user.uid },
        timestamp: serverTimestamp()
      });

      // Optimistic update
      setQueueItems(prev => prev.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              assignedTo: user.uid,
              assignedAt: new Date(),
              status: 'in_review' as const
            }
          : item
      ));
    } catch (error) {
      console.error('Error assigning item:', error);
      throw error;
    }
  }, [user]);

  // Bulk assign items
  const bulkAssign = useCallback(async (
    itemIds: string[], 
    adminId: string
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const batch = writeBatch(db);
      
      for (const itemId of itemIds) {
        const itemRef = doc(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE, itemId);
        batch.update(itemRef, {
          assignedTo: adminId,
          assignedAt: serverTimestamp(),
          status: 'in_review'
        });
      }

      // Add bulk audit log entry
      const auditRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG));
      batch.set(auditRef, {
        adminId: user.uid,
        action: 'bulk_assign_moderation_items',
        resourceType: 'moderation_queue',
        resourceIds: itemIds,
        details: { assignedTo: adminId, count: itemIds.length },
        timestamp: serverTimestamp()
      });

      await batch.commit();

      // Optimistic update
      setQueueItems(prev => prev.map(item => 
        itemIds.includes(item.id)
          ? { 
              ...item, 
              assignedTo: adminId,
              assignedAt: new Date(),
              status: 'in_review' as const
            }
          : item
      ));
    } catch (error) {
      console.error('Error bulk assigning items:', error);
      throw error;
    }
  }, [user]);

  // Process moderation item
  const processItem = useCallback(async (
    itemId: string,
    action: string,
    reason: string
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const batch = writeBatch(db);
      
      // Update queue item
      const itemRef = doc(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE, itemId);
      batch.update(itemRef, {
        status: 'completed',
        completedAt: serverTimestamp(),
        completedBy: user.uid,
        action,
        reason
      });

      // Create moderation action record
      const actionRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_ACTIONS));
      batch.set(actionRef, {
        queueItemId: itemId,
        action,
        reason,
        adminId: user.uid,
        timestamp: serverTimestamp()
      });

      // Add to audit log
      const auditRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG));
      batch.set(auditRef, {
        adminId: user.uid,
        action: 'process_moderation_item',
        resourceType: 'moderation_queue',
        resourceId: itemId,
        details: { action, reason },
        timestamp: serverTimestamp()
      });

      await batch.commit();

      // Remove from queue (optimistic update)
      setQueueItems(prev => prev.filter(item => item.id !== itemId));
    } catch (error) {
      console.error('Error processing item:', error);
      throw error;
    }
  }, [user]);

  // Escalate item
  const escalateItem = useCallback(async (
    itemId: string,
    reason: string
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const itemRef = doc(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE, itemId);
      await updateDoc(itemRef, {
        status: 'escalated',
        escalatedAt: serverTimestamp(),
        escalatedBy: user.uid,
        escalationReason: reason,
        priority: 'urgent' // Escalated items become urgent
      });

      // Add to audit log
      await addDoc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG), {
        adminId: user.uid,
        action: 'escalate_moderation_item',
        resourceType: 'moderation_queue',
        resourceId: itemId,
        details: { reason },
        timestamp: serverTimestamp()
      });

      // Optimistic update
      setQueueItems(prev => prev.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              status: 'escalated' as const,
              priority: 'urgent' as const
            }
          : item
      ));
    } catch (error) {
      console.error('Error escalating item:', error);
      throw error;
    }
  }, [user]);

  // Helper functions
  const fetchUserProfile = async (userId: string) => {
    // Mock user profile - in real implementation, fetch from profiles collection
    return {
      id: userId,
      displayName: 'User Name',
      email: '<EMAIL>'
    };
  };

  const fetchQueueStats = async (filters: ModerationFilters): Promise<QueueStats> => {
    // Mock stats - in real implementation, use aggregation queries
    return {
      totalItems: 45,
      urgentItems: 8,
      assignedItems: 23,
      unassignedItems: 22,
      averageWaitTime: 2.5, // hours
      queueTrend: -5 // 5% decrease
    };
  };

  const fetchModerationAnalytics = async (dateRange: { start: Date; end: Date }): Promise<ModerationAnalytics> => {
    // Mock analytics - in real implementation, fetch from analytics collection
    return {
      queueSize: 45,
      averageProcessingTime: 15, // minutes
      actionBreakdown: [
        { action: 'approve', count: 120, percentage: 60 },
        { action: 'reject', count: 40, percentage: 20 },
        { action: 'escalate', count: 30, percentage: 15 },
        { action: 'hide', count: 10, percentage: 5 }
      ],
      escalationRate: 0.15,
      moderatorWorkload: [
        { adminId: 'admin1', adminName: 'Admin One', pendingItems: 8, completedToday: 12, averageTime: 12 },
        { adminId: 'admin2', adminName: 'Admin Two', pendingItems: 5, completedToday: 15, averageTime: 18 }
      ],
      responseTimeTrend: -8 // 8% improvement
    };
  };

  return {
    queueItems,
    queueStats,
    analytics,
    loading,
    error,
    totalCount,
    refetch: fetchQueueData,
    assignToSelf,
    bulkAssign,
    processItem,
    escalateItem
  };
};
