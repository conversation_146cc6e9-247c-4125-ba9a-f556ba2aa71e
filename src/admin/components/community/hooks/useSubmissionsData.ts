/**
 * Submissions Data Hook
 *
 * Custom React hook for managing submissions data in admin interface.
 * Provides CRUD operations, moderation actions, quality assessment, and analytics.
 *
 * Features:
 * - Real-time submissions data with advanced filtering
 * - Quality scoring and assessment tools
 * - Moderation actions (approve, reject, feature, request changes)
 * - Bulk operations for efficient management
 * - Analytics and performance metrics
 * - Content curation and featured content management
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  getDocs,
  doc,
  updateDoc,
  addDoc,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '../../../../lib/firebase';
import { COLLECTIONS } from '../../../../lib/firebase/collections';
import { COMMUNITY_ADMIN_COLLECTIONS } from '../../../../lib/firebase/communityAdminCollections';
import { useUser } from '../../../../lib/useUser';
import type { 
  SubmissionFilters, 
  SubmissionReviewItem, 
  SubmissionAnalytics,
  PaginationParams,
  PaginatedResult,
  QualityReport
} from '../types';

interface UseSubmissionsDataOptions {
  page?: number;
  pageSize?: number;
  realtime?: boolean;
}

interface UseSubmissionsDataReturn {
  submissions: SubmissionReviewItem[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  analytics: SubmissionAnalytics | null;
  refetch: () => Promise<void>;
  moderateSubmission: (
    submissionId: string, 
    action: string, 
    reason: string
  ) => Promise<void>;
  bulkModerateSubmissions: (
    submissionIds: string[], 
    action: string, 
    reason: string
  ) => Promise<void>;
  assessQuality: (submissionId: string) => Promise<QualityReport>;
  featureSubmission: (submissionId: string) => Promise<void>;
}

export const useSubmissionsData = (
  filters: SubmissionFilters,
  options: UseSubmissionsDataOptions = {}
): UseSubmissionsDataReturn => {
  const { user } = useUser();
  const { page = 1, pageSize = 20, realtime = true } = options;

  // State
  const [submissions, setSubmissions] = useState<SubmissionReviewItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [analytics, setAnalytics] = useState<SubmissionAnalytics | null>(null);

  // Build Firestore query based on filters
  const buildQuery = useCallback(() => {
    let q = collection(db, COLLECTIONS.SUBMISSIONS);
    const constraints = [];

    // Status filter
    if (filters.status !== 'all') {
      constraints.push(where('status', '==', filters.status));
    }

    // Category filter
    if (filters.category.length > 0) {
      constraints.push(where('category', 'in', filters.category));
    }

    // Content type filter
    if (filters.contentType !== 'all') {
      constraints.push(where('contentType', '==', filters.contentType));
    }

    // Priority filter
    if (filters.priority !== 'all') {
      constraints.push(where('priority', '==', filters.priority));
    }

    // Date range filter
    constraints.push(where('submittedAt', '>=', Timestamp.fromDate(filters.dateRange.start)));
    constraints.push(where('submittedAt', '<=', Timestamp.fromDate(filters.dateRange.end)));

    // Quality score filter
    if (filters.qualityScore.min > 0 || filters.qualityScore.max < 100) {
      constraints.push(where('qualityScore', '>=', filters.qualityScore.min));
      constraints.push(where('qualityScore', '<=', filters.qualityScore.max));
    }

    // Add ordering and pagination
    constraints.push(orderBy('submittedAt', 'desc'));
    constraints.push(limit(pageSize));

    return query(q, ...constraints);
  }, [filters, page, pageSize]);

  // Fetch submissions data
  const fetchSubmissions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const q = buildQuery();
      const snapshot = await getDocs(q);
      
      const submissionsData = await Promise.all(
        snapshot.docs.map(async (docSnapshot) => {
          const data = docSnapshot.data();
          
          // Fetch author profile
          const authorProfile = await fetchUserProfile(data.authorId);
          
          // Convert Firestore timestamps to Date objects
          return {
            id: docSnapshot.id,
            ...data,
            author: authorProfile,
            submittedAt: data.submittedAt?.toDate() || new Date(),
            reviewedAt: data.reviewedAt?.toDate(),
            // Ensure required fields have defaults
            status: data.status || 'pending',
            priority: data.priority || 'medium',
            contentType: data.contentType || 'mixed',
            qualityScore: data.qualityScore || 0,
            moderationFlags: data.moderationFlags || [],
            tags: data.tags || [],
            engagementMetrics: data.engagementMetrics || {
              views: 0,
              likes: 0,
              comments: 0,
              shares: 0,
              saves: 0,
              engagementRate: 0
            }
          } as SubmissionReviewItem;
        })
      );

      setSubmissions(submissionsData);
      
      // Get total count
      setTotalCount(await getTotalSubmissionsCount(filters));
      
      // Fetch analytics data
      const analyticsData = await fetchSubmissionAnalytics(filters.dateRange);
      setAnalytics(analyticsData);

    } catch (err) {
      console.error('Error fetching submissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch submissions');
    } finally {
      setLoading(false);
    }
  }, [buildQuery, filters]);

  // Set up real-time listener
  useEffect(() => {
    if (!realtime) {
      fetchSubmissions();
      return;
    }

    const q = buildQuery();
    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const submissionsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          submittedAt: doc.data().submittedAt?.toDate() || new Date(),
          reviewedAt: doc.data().reviewedAt?.toDate()
        })) as SubmissionReviewItem[];

        setSubmissions(submissionsData);
        setLoading(false);
      },
      (err) => {
        console.error('Error in submissions listener:', err);
        setError(err.message);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [buildQuery, realtime, fetchSubmissions]);

  // Moderate single submission
  const moderateSubmission = useCallback(async (
    submissionId: string,
    action: string,
    reason: string
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const submissionRef = doc(db, COLLECTIONS.SUBMISSIONS, submissionId);
      const moderationRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.SUBMISSION_MODERATION));
      
      const batch = writeBatch(db);
      
      // Update submission status
      const newStatus = getStatusFromAction(action);
      batch.update(submissionRef, {
        status: newStatus,
        reviewedAt: serverTimestamp(),
        reviewedBy: user.uid,
        reviewNotes: reason
      });
      
      // Create moderation record
      batch.set(moderationRef, {
        submissionId,
        action,
        reason,
        adminId: user.uid,
        timestamp: serverTimestamp(),
        newStatus,
        priority: 'medium' // This could be dynamic based on submission
      });
      
      // Add to audit log
      const auditRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG));
      batch.set(auditRef, {
        adminId: user.uid,
        action: 'moderate_submission',
        resourceType: 'submission',
        resourceId: submissionId,
        details: { action, reason },
        timestamp: serverTimestamp()
      });
      
      await batch.commit();
      
      // Optimistic update
      setSubmissions(prev => prev.map(submission => 
        submission.id === submissionId 
          ? { 
              ...submission, 
              status: newStatus as any,
              reviewedBy: user.uid,
              reviewedAt: new Date(),
              reviewNotes: reason
            }
          : submission
      ));
      
    } catch (error) {
      console.error('Error moderating submission:', error);
      throw error;
    }
  }, [user]);

  // Bulk moderate submissions
  const bulkModerateSubmissions = useCallback(async (
    submissionIds: string[],
    action: string,
    reason: string
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const batch = writeBatch(db);
      const newStatus = getStatusFromAction(action);
      
      for (const submissionId of submissionIds) {
        const submissionRef = doc(db, COLLECTIONS.SUBMISSIONS, submissionId);
        const moderationRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.SUBMISSION_MODERATION));
        
        // Update submission
        batch.update(submissionRef, {
          status: newStatus,
          reviewedAt: serverTimestamp(),
          reviewedBy: user.uid,
          reviewNotes: reason
        });
        
        // Create moderation record
        batch.set(moderationRef, {
          submissionId,
          action,
          reason,
          adminId: user.uid,
          timestamp: serverTimestamp(),
          newStatus,
          bulkOperation: true
        });
      }
      
      // Add bulk audit log entry
      const auditRef = doc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG));
      batch.set(auditRef, {
        adminId: user.uid,
        action: 'bulk_moderate_submissions',
        resourceType: 'submission',
        resourceIds: submissionIds,
        details: { action, reason, count: submissionIds.length },
        timestamp: serverTimestamp()
      });
      
      await batch.commit();
      
      // Optimistic update
      setSubmissions(prev => prev.map(submission => 
        submissionIds.includes(submission.id)
          ? { 
              ...submission, 
              status: newStatus as any,
              reviewedBy: user.uid,
              reviewedAt: new Date(),
              reviewNotes: reason
            }
          : submission
      ));
      
    } catch (error) {
      console.error('Error bulk moderating submissions:', error);
      throw error;
    }
  }, [user]);

  // Assess submission quality
  const assessQuality = useCallback(async (submissionId: string): Promise<QualityReport> => {
    try {
      // This would integrate with a quality assessment service
      // For now, return a mock quality report
      const mockReport: QualityReport = {
        overallScore: Math.floor(Math.random() * 40) + 60, // 60-100
        contentQuality: Math.floor(Math.random() * 30) + 70,
        technicalQuality: Math.floor(Math.random() * 30) + 70,
        communityFit: Math.floor(Math.random() * 30) + 70,
        originalityScore: Math.floor(Math.random() * 30) + 70,
        recommendations: [
          'Consider improving image resolution',
          'Add more descriptive tags',
          'Include usage examples'
        ],
        flags: []
      };

      // Update submission with quality score
      const submissionRef = doc(db, COLLECTIONS.SUBMISSIONS, submissionId);
      await updateDoc(submissionRef, {
        qualityScore: mockReport.overallScore,
        qualityAssessedAt: serverTimestamp(),
        qualityAssessedBy: user?.uid
      });

      return mockReport;
    } catch (error) {
      console.error('Error assessing quality:', error);
      throw error;
    }
  }, [user]);

  // Feature submission
  const featureSubmission = useCallback(async (submissionId: string): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const submissionRef = doc(db, COLLECTIONS.SUBMISSIONS, submissionId);
      await updateDoc(submissionRef, {
        status: 'featured',
        featuredAt: serverTimestamp(),
        featuredBy: user.uid
      });

      // Optimistic update
      setSubmissions(prev => prev.map(submission => 
        submission.id === submissionId 
          ? { ...submission, status: 'featured' }
          : submission
      ));
    } catch (error) {
      console.error('Error featuring submission:', error);
      throw error;
    }
  }, [user]);

  // Helper functions
  const getStatusFromAction = (action: string) => {
    switch (action) {
      case 'approve': return 'approved';
      case 'reject': return 'rejected';
      case 'feature': return 'featured';
      case 'request_changes': return 'pending';
      default: return 'pending';
    }
  };

  const fetchUserProfile = async (userId: string) => {
    // Mock user profile - in real implementation, fetch from profiles collection
    return {
      id: userId,
      displayName: 'User Name',
      email: '<EMAIL>'
    };
  };

  const getTotalSubmissionsCount = async (filters: SubmissionFilters): Promise<number> => {
    // Mock count - in real implementation, use aggregation or count collection
    return 250;
  };

  const fetchSubmissionAnalytics = async (dateRange: { start: Date; end: Date }): Promise<SubmissionAnalytics> => {
    // Mock analytics - in real implementation, fetch from analytics collection
    return {
      totalSubmissions: 250,
      approvalRate: 0.78,
      averageQualityScore: 72,
      featuredCount: 15,
      categoryBreakdown: [
        { category: 'Keycap Designs', count: 120, percentage: 48, trend: 'up' },
        { category: 'Artisan Keycaps', count: 80, percentage: 32, trend: 'stable' },
        { category: 'Custom Builds', count: 50, percentage: 20, trend: 'down' }
      ],
      qualityTrends: []
    };
  };

  return {
    submissions,
    loading,
    error,
    totalCount,
    analytics,
    refetch: fetchSubmissions,
    moderateSubmission,
    bulkModerateSubmissions,
    assessQuality,
    featureSubmission
  };
};
