/**
 * Community Admin Components
 *
 * Centralized exports for all community management admin components.
 * These components provide comprehensive administrative oversight for
 * community features including discussions, submissions, challenges,
 * moderation, social features, and real-time controls.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Discussions Management
export { default as DiscussionsManager } from './discussions/DiscussionsManager';
export { default as DiscussionModerationPanel } from './discussions/DiscussionModerationPanel';
export { default as DiscussionThreadTable } from './discussions/DiscussionThreadTable';
export { default as BulkDiscussionActions } from './discussions/BulkDiscussionActions';
export { default as DiscussionAnalytics } from './discussions/DiscussionAnalytics';

// Submissions Management
export { default as SubmissionsManager } from './submissions/SubmissionsManager';
export { default as SubmissionReviewQueue } from './submissions/SubmissionReviewQueue';
export { default as SubmissionDetailModal } from './submissions/SubmissionDetailModal';
export { default as ContentModerationTools } from './submissions/ContentModerationTools';
export { default as SubmissionAnalytics } from './submissions/SubmissionAnalytics';

// Challenges Management
export { default as ChallengesManager } from './challenges/ChallengesManager';
export { default as ChallengeBuilder } from './challenges/ChallengeBuilder';
export { default as JudgingInterface } from './challenges/JudgingInterface';
export { default as TeamManagementTools } from './challenges/TeamManagementTools';

// Moderation Dashboard
export { default as ModerationDashboard } from './moderation/ModerationDashboard';
export { default as ModerationQueue } from './moderation/ModerationQueue';
export { default as AutoModerationConfig } from './moderation/AutoModerationConfig';
export { default as ModerationAnalytics } from './moderation/ModerationAnalytics';

// Social Features Management
export { default as SocialFeaturesManager } from './social/SocialFeaturesManager';
export { default as UserConnectionsManager } from './social/UserConnectionsManager';
export { default as MentorshipManager } from './social/MentorshipManager';

// Real-time Controls
export { default as RealtimeControlsManager } from './realtime/RealtimeControlsManager';
export { default as PresenceManager } from './realtime/PresenceManager';
export { default as LiveChatModeration } from './realtime/LiveChatModeration';

// Shared Components
export { default as CommunityStatsCard } from './shared/CommunityStatsCard';
export { default as ModerationActionButton } from './shared/ModerationActionButton';
export { default as CommunityFilters } from './shared/CommunityFilters';
export { default as CommunitySearchBar } from './shared/CommunitySearchBar';

// Types
export type {
  DiscussionFilters,
  DiscussionTableRow,
  ModerationAction,
  BulkModerationAction,
  SubmissionReviewItem,
  SubmissionModerationActions,
  ChallengeAdminInterface,
  ModerationQueue as ModerationQueueType,
  CommunityAnalytics
} from './types';

// Hooks
export { useDiscussionsData } from './hooks/useDiscussionsData';
export { useSubmissionsData } from './hooks/useSubmissionsData';
export { useChallengesData } from './hooks/useChallengesData';
export { useModerationQueue } from './hooks/useModerationQueue';
export { useCommunityAnalytics } from './hooks/useCommunityAnalytics';
