/**
 * Auto Moderation Configuration Component
 *
 * Advanced configuration interface for automated moderation rules and AI-assisted decisions.
 * Provides rule management, threshold settings, and automated workflow configuration.
 *
 * Features:
 * - Automated moderation rule creation and management
 * - AI-assisted content analysis configuration
 * - Threshold settings for different content types
 * - Keyword and pattern-based filtering
 * - Escalation rules and automated actions
 * - Integration with external moderation services
 * - Real-time rule testing and validation
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X,
  Save,
  Plus,
  Trash2,
  Zap,
  Brain,
  Shield,
  AlertTriangle,
  CheckCircle,
  Settings,
  Target,
  Filter,
  Eye,
  Play,
  Pause
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';

interface AutoModerationRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  contentTypes: string[];
  conditions: ModerationCondition[];
  actions: ModerationAction[];
  priority: 'low' | 'medium' | 'high';
  createdAt: Date;
  updatedAt: Date;
}

interface ModerationCondition {
  type: 'keyword' | 'sentiment' | 'toxicity' | 'spam' | 'length' | 'pattern';
  operator: 'contains' | 'equals' | 'greater_than' | 'less_than' | 'matches';
  value: string | number;
  threshold?: number;
}

interface ModerationAction {
  type: 'auto_approve' | 'auto_reject' | 'flag' | 'escalate' | 'assign' | 'notify';
  parameters: Record<string, any>;
}

interface AutoModerationConfigProps {
  onClose?: () => void;
  onConfigUpdate: () => void;
  className?: string;
}

export const AutoModerationConfig: React.FC<AutoModerationConfigProps> = ({
  onClose,
  onConfigUpdate,
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State
  const [rules, setRules] = useState<AutoModerationRule[]>([]);
  const [selectedRule, setSelectedRule] = useState<AutoModerationRule | null>(null);
  const [showRuleEditor, setShowRuleEditor] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testMode, setTestMode] = useState(false);

  // Permission checks
  const canConfigure = hasPermission('community_moderation', 'configure');

  // Load existing rules
  useEffect(() => {
    loadModerationRules();
  }, []);

  const loadModerationRules = useCallback(async () => {
    try {
      setLoading(true);
      // Mock data - in real implementation, fetch from Firestore
      const mockRules: AutoModerationRule[] = [
        {
          id: '1',
          name: 'Spam Detection',
          description: 'Automatically detect and flag spam content',
          enabled: true,
          contentTypes: ['discussion', 'submission', 'comment'],
          conditions: [
            { type: 'keyword', operator: 'contains', value: 'spam,scam,fake', threshold: 0.8 },
            { type: 'length', operator: 'less_than', value: 10 }
          ],
          actions: [
            { type: 'flag', parameters: { reason: 'Potential spam detected' } },
            { type: 'escalate', parameters: { priority: 'medium' } }
          ],
          priority: 'high',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          name: 'Toxicity Filter',
          description: 'Filter toxic and inappropriate content',
          enabled: true,
          contentTypes: ['discussion', 'comment', 'reply'],
          conditions: [
            { type: 'toxicity', operator: 'greater_than', value: 0.7, threshold: 0.7 },
            { type: 'sentiment', operator: 'less_than', value: -0.5 }
          ],
          actions: [
            { type: 'auto_reject', parameters: { reason: 'Content violates community guidelines' } },
            { type: 'notify', parameters: { adminIds: ['admin1', 'admin2'] } }
          ],
          priority: 'high',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '3',
          name: 'Quality Submissions',
          description: 'Auto-approve high-quality submissions',
          enabled: false,
          contentTypes: ['submission'],
          conditions: [
            { type: 'length', operator: 'greater_than', value: 100 },
            { type: 'sentiment', operator: 'greater_than', value: 0.3 }
          ],
          actions: [
            { type: 'auto_approve', parameters: { reason: 'High-quality content detected' } }
          ],
          priority: 'medium',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      setRules(mockRules);
    } catch (error) {
      console.error('Error loading moderation rules:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle rule toggle
  const handleToggleRule = useCallback(async (ruleId: string, enabled: boolean) => {
    try {
      setRules(prev => prev.map(rule => 
        rule.id === ruleId ? { ...rule, enabled } : rule
      ));
      
      // In real implementation, update in Firestore
      onConfigUpdate();
    } catch (error) {
      console.error('Error toggling rule:', error);
    }
  }, [onConfigUpdate]);

  // Handle rule deletion
  const handleDeleteRule = useCallback(async (ruleId: string) => {
    try {
      setRules(prev => prev.filter(rule => rule.id !== ruleId));
      onConfigUpdate();
    } catch (error) {
      console.error('Error deleting rule:', error);
    }
  }, [onConfigUpdate]);

  // Handle rule creation/editing
  const handleSaveRule = useCallback(async (rule: Partial<AutoModerationRule>) => {
    try {
      if (selectedRule) {
        // Update existing rule
        setRules(prev => prev.map(r => 
          r.id === selectedRule.id ? { ...r, ...rule, updatedAt: new Date() } : r
        ));
      } else {
        // Create new rule
        const newRule: AutoModerationRule = {
          id: Date.now().toString(),
          name: rule.name || '',
          description: rule.description || '',
          enabled: rule.enabled || false,
          contentTypes: rule.contentTypes || [],
          conditions: rule.conditions || [],
          actions: rule.actions || [],
          priority: rule.priority || 'medium',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        setRules(prev => [...prev, newRule]);
      }
      
      setShowRuleEditor(false);
      setSelectedRule(null);
      onConfigUpdate();
    } catch (error) {
      console.error('Error saving rule:', error);
    }
  }, [selectedRule, onConfigUpdate]);

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400 bg-red-500/10';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10';
      case 'low': return 'text-blue-400 bg-blue-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  // Render condition summary
  const renderConditionSummary = (conditions: ModerationCondition[]) => {
    if (conditions.length === 0) return 'No conditions';
    if (conditions.length === 1) {
      const condition = conditions[0];
      return `${condition.type} ${condition.operator} ${condition.value}`;
    }
    return `${conditions.length} conditions`;
  };

  // Render action summary
  const renderActionSummary = (actions: ModerationAction[]) => {
    if (actions.length === 0) return 'No actions';
    if (actions.length === 1) {
      return actions[0].type.replace('_', ' ');
    }
    return `${actions.length} actions`;
  };

  const isModal = !!onClose;

  const content = (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Zap className="w-6 h-6 text-purple-400" />
          <div>
            <h2 className="text-2xl font-bold text-white">Auto Moderation</h2>
            <p className="text-gray-400">Configure automated moderation rules and AI assistance</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Test Mode</span>
            <button
              onClick={() => setTestMode(!testMode)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                testMode ? 'bg-purple-600' : 'bg-gray-600'
              }`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                testMode ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
          </div>
          {canConfigure && (
            <AdminButton
              variant="primary"
              icon={Plus}
              onClick={() => {
                setSelectedRule(null);
                setShowRuleEditor(true);
              }}
            >
              Add Rule
            </AdminButton>
          )}
          {onClose && (
            <AdminButton
              variant="secondary"
              icon={X}
              onClick={onClose}
            />
          )}
        </div>
      </div>

      {/* AI Configuration */}
      <AdminCard className="p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Brain className="w-5 h-5 text-blue-400" />
          <h3 className="text-lg font-semibold text-white">AI Configuration</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Toxicity Threshold
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              defaultValue="0.7"
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Lenient</span>
              <span>0.7</span>
              <span>Strict</span>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Spam Detection
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              defaultValue="0.8"
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Lenient</span>
              <span>0.8</span>
              <span>Strict</span>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Quality Score
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              defaultValue="0.6"
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Low</span>
              <span>0.6</span>
              <span>High</span>
            </div>
          </div>
        </div>
      </AdminCard>

      {/* Rules List */}
      <AdminCard className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Moderation Rules</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <span>{rules.filter(r => r.enabled).length} active</span>
            <span>•</span>
            <span>{rules.length} total</span>
          </div>
        </div>
        
        <div className="space-y-3">
          {rules.map((rule) => (
            <div key={rule.id} className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => handleToggleRule(rule.id, !rule.enabled)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    rule.enabled ? 'bg-green-600' : 'bg-gray-600'
                  }`}
                  disabled={!canConfigure}
                >
                  <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    rule.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </button>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-medium text-white">{rule.name}</h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(rule.priority)}`}>
                      {rule.priority}
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mb-2">{rule.description}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>Types: {rule.contentTypes.join(', ')}</span>
                    <span>•</span>
                    <span>Conditions: {renderConditionSummary(rule.conditions)}</span>
                    <span>•</span>
                    <span>Actions: {renderActionSummary(rule.actions)}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <AdminButton
                  variant="secondary"
                  size="sm"
                  icon={Eye}
                  onClick={() => {
                    setSelectedRule(rule);
                    setShowRuleEditor(true);
                  }}
                  disabled={!canConfigure}
                >
                  Edit
                </AdminButton>
                <AdminButton
                  variant="secondary"
                  size="sm"
                  icon={Trash2}
                  onClick={() => handleDeleteRule(rule.id)}
                  disabled={!canConfigure}
                />
              </div>
            </div>
          ))}
          
          {rules.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No moderation rules configured</p>
              <p className="text-sm">Create your first rule to get started</p>
            </div>
          )}
        </div>
      </AdminCard>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <AdminCard className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-400" />
            </div>
            <div>
              <p className="text-white font-medium">Auto Approved</p>
              <p className="text-2xl font-bold text-green-400">1,234</p>
              <p className="text-xs text-gray-400">Last 30 days</p>
            </div>
          </div>
        </AdminCard>

        <AdminCard className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-500/10 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-red-400" />
            </div>
            <div>
              <p className="text-white font-medium">Auto Rejected</p>
              <p className="text-2xl font-bold text-red-400">89</p>
              <p className="text-xs text-gray-400">Last 30 days</p>
            </div>
          </div>
        </AdminCard>

        <AdminCard className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-yellow-500/10 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
            </div>
            <div>
              <p className="text-white font-medium">Flagged</p>
              <p className="text-2xl font-bold text-yellow-400">156</p>
              <p className="text-xs text-gray-400">Last 30 days</p>
            </div>
          </div>
        </AdminCard>

        <AdminCard className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
              <Target className="w-5 h-5 text-blue-400" />
            </div>
            <div>
              <p className="text-white font-medium">Accuracy</p>
              <p className="text-2xl font-bold text-blue-400">94%</p>
              <p className="text-xs text-gray-400">AI predictions</p>
            </div>
          </div>
        </AdminCard>
      </div>
    </div>
  );

  if (isModal) {
    return (
      <AnimatePresence>
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="w-full max-w-6xl max-h-[90vh] overflow-hidden"
          >
            <AdminCard className="h-full overflow-y-auto">
              <div className="p-6">
                {content}
              </div>
            </AdminCard>
          </motion.div>
        </div>
      </AnimatePresence>
    );
  }

  return content;
};

export default AutoModerationConfig;
