/**
 * Moderation Analytics Component
 *
 * Comprehensive analytics dashboard for moderation performance and metrics.
 * Provides insights into queue efficiency, moderator performance, and trends.
 *
 * Features:
 * - Real-time moderation metrics and KPIs
 * - Moderator performance tracking and workload distribution
 * - Action breakdown and trend analysis
 * - Queue efficiency and response time metrics
 * - Visual charts and data visualization
 * - Exportable reports and insights
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  Target,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  Download,
  Filter
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import { CommunityStatsCard } from '../shared/CommunityStatsCard';
import type { ModerationAnalytics as ModerationAnalyticsType } from '../types';

interface ModerationAnalyticsProps {
  analytics: ModerationAnalyticsType | null;
  className?: string;
}

export const ModerationAnalytics: React.FC<ModerationAnalyticsProps> = ({
  analytics,
  className = ''
}) => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [selectedMetric, setSelectedMetric] = useState<'overview' | 'performance' | 'trends'>('overview');

  // Handle export functionality
  const handleExport = useCallback(() => {
    // Implementation for exporting analytics data
    console.log('Exporting analytics data...');
  }, []);

  if (!analytics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-400">No analytics data available</p>
        </div>
      </div>
    );
  }

  // Calculate trend indicators
  const getTrendIcon = (trend: number) => {
    if (trend > 0) return TrendingUp;
    if (trend < 0) return TrendingDown;
    return Target;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-400';
    if (trend < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  // Main metrics
  const mainMetrics = [
    {
      title: 'Queue Size',
      value: analytics.queueSize,
      icon: Clock,
      color: 'yellow' as const,
      trend: -12,
      description: 'Items pending review'
    },
    {
      title: 'Avg Processing Time',
      value: `${analytics.averageProcessingTime}m`,
      icon: TrendingUp,
      color: 'blue' as const,
      trend: analytics.responseTimeTrend || 0,
      description: 'Time to complete review'
    },
    {
      title: 'Escalation Rate',
      value: `${Math.round(analytics.escalationRate * 100)}%`,
      icon: AlertTriangle,
      color: 'red' as const,
      trend: -5,
      description: 'Items requiring escalation'
    },
    {
      title: 'Active Moderators',
      value: analytics.moderatorWorkload.length,
      icon: Users,
      color: 'green' as const,
      trend: 0,
      description: 'Currently active moderators'
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Moderation Analytics</h2>
          <p className="text-gray-400">Performance insights and trends</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <AdminButton
            variant="secondary"
            icon={Download}
            onClick={handleExport}
          >
            Export
          </AdminButton>
        </div>
      </div>

      {/* Metric Tabs */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setSelectedMetric('overview')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            selectedMetric === 'overview' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <BarChart3 className="w-4 h-4" />
          <span>Overview</span>
        </button>
        
        <button
          onClick={() => setSelectedMetric('performance')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            selectedMetric === 'performance' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Users className="w-4 h-4" />
          <span>Performance</span>
        </button>
        
        <button
          onClick={() => setSelectedMetric('trends')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            selectedMetric === 'trends' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <TrendingUp className="w-4 h-4" />
          <span>Trends</span>
        </button>
      </div>

      {/* Main Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {mainMetrics.map((metric, index) => (
          <CommunityStatsCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            color={metric.color}
            trend={metric.trend}
            subtitle={metric.description}
          />
        ))}
      </div>

      {/* Content based on selected metric */}
      {selectedMetric === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Action Breakdown */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Action Breakdown</h3>
            <div className="space-y-4">
              {analytics.actionBreakdown.map((action, index) => (
                <div key={action.action} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      action.action === 'approve' ? 'bg-green-500' :
                      action.action === 'reject' ? 'bg-red-500' :
                      action.action === 'escalate' ? 'bg-yellow-500' :
                      'bg-gray-500'
                    }`} />
                    <span className="text-white capitalize">{action.action}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-400">{action.count}</span>
                    <span className="text-sm text-gray-500">({action.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Visual representation */}
            <div className="mt-6">
              <div className="flex h-2 bg-gray-700 rounded-full overflow-hidden">
                {analytics.actionBreakdown.map((action, index) => (
                  <div
                    key={action.action}
                    className={`${
                      action.action === 'approve' ? 'bg-green-500' :
                      action.action === 'reject' ? 'bg-red-500' :
                      action.action === 'escalate' ? 'bg-yellow-500' :
                      'bg-gray-500'
                    }`}
                    style={{ width: `${action.percentage}%` }}
                  />
                ))}
              </div>
            </div>
          </AdminCard>

          {/* Queue Efficiency */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Queue Efficiency</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Average Response Time</span>
                <div className="flex items-center space-x-2">
                  <span className="text-white font-medium">{analytics.averageProcessingTime}m</span>
                  {React.createElement(getTrendIcon(analytics.responseTimeTrend || 0), {
                    className: `w-4 h-4 ${getTrendColor(analytics.responseTimeTrend || 0)}`
                  })}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Escalation Rate</span>
                <div className="flex items-center space-x-2">
                  <span className="text-white font-medium">{Math.round(analytics.escalationRate * 100)}%</span>
                  <TrendingDown className="w-4 h-4 text-green-400" />
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Queue Size</span>
                <div className="flex items-center space-x-2">
                  <span className="text-white font-medium">{analytics.queueSize}</span>
                  <TrendingDown className="w-4 h-4 text-green-400" />
                </div>
              </div>
            </div>
            
            {/* Efficiency Score */}
            <div className="mt-6 p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-400">Efficiency Score</span>
                <span className="text-2xl font-bold text-green-400">87%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '87%' }} />
              </div>
              <p className="text-xs text-gray-500 mt-2">Based on response time and resolution rate</p>
            </div>
          </AdminCard>
        </div>
      )}

      {selectedMetric === 'performance' && (
        <div className="space-y-6">
          {/* Moderator Workload */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Moderator Performance</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left text-gray-400 font-medium py-2">Moderator</th>
                    <th className="text-center text-gray-400 font-medium py-2">Pending</th>
                    <th className="text-center text-gray-400 font-medium py-2">Completed Today</th>
                    <th className="text-center text-gray-400 font-medium py-2">Avg Time</th>
                    <th className="text-center text-gray-400 font-medium py-2">Efficiency</th>
                  </tr>
                </thead>
                <tbody>
                  {analytics.moderatorWorkload.map((moderator, index) => (
                    <tr key={moderator.adminId} className="border-b border-gray-800">
                      <td className="py-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                              {moderator.adminName.charAt(0)}
                            </span>
                          </div>
                          <span className="text-white">{moderator.adminName}</span>
                        </div>
                      </td>
                      <td className="text-center py-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          moderator.pendingItems > 10 ? 'bg-red-600 text-white' :
                          moderator.pendingItems > 5 ? 'bg-yellow-600 text-white' :
                          'bg-green-600 text-white'
                        }`}>
                          {moderator.pendingItems}
                        </span>
                      </td>
                      <td className="text-center py-3 text-white">{moderator.completedToday}</td>
                      <td className="text-center py-3 text-white">{moderator.averageTime}m</td>
                      <td className="text-center py-3">
                        <div className="flex items-center justify-center">
                          <div className="w-16 bg-gray-700 rounded-full h-2 mr-2">
                            <div 
                              className="bg-green-500 h-2 rounded-full" 
                              style={{ width: `${Math.min(100, (moderator.completedToday / 20) * 100)}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-400">
                            {Math.round((moderator.completedToday / 20) * 100)}%
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </AdminCard>

          {/* Performance Insights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <AdminCard className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-white font-medium">Top Performer</p>
                  <p className="text-sm text-gray-400">
                    {analytics.moderatorWorkload.reduce((top, mod) => 
                      mod.completedToday > top.completedToday ? mod : top
                    ).adminName}
                  </p>
                </div>
              </div>
            </AdminCard>

            <AdminCard className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                  <Clock className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-white font-medium">Fastest Response</p>
                  <p className="text-sm text-gray-400">
                    {Math.min(...analytics.moderatorWorkload.map(m => m.averageTime))}m avg
                  </p>
                </div>
              </div>
            </AdminCard>

            <AdminCard className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-yellow-500/10 rounded-lg flex items-center justify-center">
                  <Target className="w-5 h-5 text-yellow-400" />
                </div>
                <div>
                  <p className="text-white font-medium">Team Efficiency</p>
                  <p className="text-sm text-gray-400">
                    {Math.round(analytics.moderatorWorkload.reduce((sum, mod) => 
                      sum + (mod.completedToday / 20), 0) / analytics.moderatorWorkload.length * 100)}%
                  </p>
                </div>
              </div>
            </AdminCard>
          </div>
        </div>
      )}

      {selectedMetric === 'trends' && (
        <div className="space-y-6">
          {/* Trend Analysis */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Trend Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-white font-medium mb-3">Response Time Trend</h4>
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingDown className="w-5 h-5 text-green-400" />
                  <span className="text-green-400 font-medium">8% improvement</span>
                  <span className="text-gray-400">vs last period</span>
                </div>
                <div className="h-32 bg-gray-800 rounded-lg flex items-end justify-center">
                  <div className="text-gray-500 text-sm">Chart visualization would go here</div>
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-3">Queue Size Trend</h4>
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingDown className="w-5 h-5 text-green-400" />
                  <span className="text-green-400 font-medium">12% decrease</span>
                  <span className="text-gray-400">vs last period</span>
                </div>
                <div className="h-32 bg-gray-800 rounded-lg flex items-end justify-center">
                  <div className="text-gray-500 text-sm">Chart visualization would go here</div>
                </div>
              </div>
            </div>
          </AdminCard>

          {/* Key Insights */}
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Key Insights</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2" />
                <div>
                  <p className="text-white font-medium">Response time improved by 8%</p>
                  <p className="text-gray-400 text-sm">
                    Average processing time decreased from 18m to 15m over the last 30 days
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2" />
                <div>
                  <p className="text-white font-medium">Queue size reduced significantly</p>
                  <p className="text-gray-400 text-sm">
                    12% decrease in pending items indicates improved efficiency
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2" />
                <div>
                  <p className="text-white font-medium">Escalation rate remains stable</p>
                  <p className="text-gray-400 text-sm">
                    15% escalation rate is within acceptable range for content quality
                  </p>
                </div>
              </div>
            </div>
          </AdminCard>
        </div>
      )}
    </div>
  );
};

export default ModerationAnalytics;
