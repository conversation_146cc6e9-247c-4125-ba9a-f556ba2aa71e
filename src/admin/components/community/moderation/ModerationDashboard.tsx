/**
 * Moderation Dashboard Component
 *
 * Unified moderation interface that brings together all community content management.
 * Provides centralized queue management, automated moderation, and comprehensive analytics.
 *
 * Features:
 * - Unified moderation queue with priority-based sorting
 * - Multi-content type management (discussions, submissions, challenges)
 * - Automated moderation rules and AI-assisted decisions
 * - Real-time queue updates and assignment management
 * - Comprehensive moderation analytics and performance metrics
 * - Bulk operations and workflow automation
 * - Escalation management and conflict resolution
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Filter, 
  Search, 
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Flag,
  Users,
  BarChart3,
  Settings,
  Zap,
  Target,
  TrendingUp,
  MessageSquare,
  Upload,
  Trophy,
  User,
  Calendar,
  Activity
} from 'lucide-react';
import { AdminCard, AdminButton, AdminTable } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import { useModerationQueue } from '../hooks/useModerationQueue';
import { ModerationQueue } from './ModerationQueue';
import { AutoModerationConfig } from './AutoModerationConfig';
import { ModerationAnalytics } from './ModerationAnalytics';
import { CommunityStatsCard } from '../shared/CommunityStatsCard';
import type { 
  ModerationQueueItem,
  ModerationFilters,
  ModerationAnalytics as ModerationAnalyticsType
} from '../types';

interface ModerationDashboardProps {
  className?: string;
}

export const ModerationDashboard: React.FC<ModerationDashboardProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [activeTab, setActiveTab] = useState<'queue' | 'analytics' | 'config'>('queue');
  const [filters, setFilters] = useState<ModerationFilters>({
    priority: 'all',
    contentType: 'all',
    status: 'pending',
    assignedTo: 'all',
    dateRange: { 
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), 
      end: new Date() 
    }
  });
  
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showAutoConfig, setShowAutoConfig] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Data hooks
  const {
    queueItems,
    queueStats,
    analytics,
    loading,
    error,
    totalCount,
    refetch,
    assignToSelf,
    bulkAssign,
    processItem,
    escalateItem
  } = useModerationQueue(filters, { page: currentPage, pageSize: 20 });

  // Permission checks
  const canModerate = hasPermission('community_moderation', 'execute');
  const canConfigure = hasPermission('community_moderation', 'configure');
  const canViewAnalytics = hasPermission('community_analytics', 'read');

  // Event handlers
  const handleFilterChange = useCallback((newFilters: Partial<ModerationFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  }, []);

  const handleAssignToSelf = useCallback(async (itemId: string) => {
    try {
      await assignToSelf(itemId);
      await refetch();
    } catch (error) {
      console.error('Failed to assign item:', error);
    }
  }, [assignToSelf, refetch]);

  const handleBulkAssign = useCallback(async (adminId: string) => {
    try {
      await bulkAssign(selectedItems, adminId);
      await refetch();
      setSelectedItems([]);
    } catch (error) {
      console.error('Failed to bulk assign:', error);
    }
  }, [bulkAssign, selectedItems, refetch]);

  const handleProcessItem = useCallback(async (
    itemId: string, 
    action: string, 
    reason: string
  ) => {
    try {
      await processItem(itemId, action, reason);
      await refetch();
    } catch (error) {
      console.error('Failed to process item:', error);
    }
  }, [processItem, refetch]);

  // Get content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'discussion': return MessageSquare;
      case 'submission': return Upload;
      case 'challenge': return Trophy;
      case 'comment': return MessageSquare;
      case 'reply': return MessageSquare;
      default: return Flag;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-400 bg-red-500/10';
      case 'high': return 'text-orange-400 bg-orange-500/10';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10';
      case 'low': return 'text-blue-400 bg-blue-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  // Table configuration for queue items
  const queueColumns = [
    {
      key: 'content',
      label: 'Content',
      sortable: true,
      render: (item: ModerationQueueItem) => {
        const ContentIcon = getContentTypeIcon(item.contentType);
        return (
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
                <ContentIcon className="w-5 h-5 text-gray-400" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-white font-medium truncate">{item.title}</p>
              <p className="text-gray-400 text-sm">by {item.author.displayName}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500 capitalize">{item.contentType}</span>
                {item.flags.length > 0 && (
                  <span className="flex items-center text-xs text-red-400">
                    <Flag size={10} className="mr-1" />
                    {item.flags.length}
                  </span>
                )}
              </div>
            </div>
          </div>
        );
      }
    },
    {
      key: 'priority',
      label: 'Priority',
      sortable: true,
      render: (item: ModerationQueueItem) => (
        <div className="text-center">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(item.priority)}`}>
            {item.priority}
          </span>
          {item.autoModerationScore && (
            <div className="text-xs text-gray-400 mt-1">
              AI: {Math.round(item.autoModerationScore * 100)}%
            </div>
          )}
        </div>
      )
    },
    {
      key: 'submitted',
      label: 'Submitted',
      sortable: true,
      render: (item: ModerationQueueItem) => (
        <div className="text-sm">
          <p className="text-white">{item.submittedAt.toLocaleDateString()}</p>
          <p className="text-gray-400">{item.submittedAt.toLocaleTimeString()}</p>
          {item.estimatedReviewTime && (
            <p className="text-gray-500 text-xs mt-1">
              Est: {item.estimatedReviewTime}m
            </p>
          )}
        </div>
      )
    },
    {
      key: 'assignment',
      label: 'Assignment',
      render: (item: ModerationQueueItem) => (
        <div className="text-sm">
          {item.assignedTo ? (
            <div>
              <p className="text-white">{item.assignedTo}</p>
              <p className="text-gray-400 text-xs">
                {item.assignedAt?.toLocaleTimeString()}
              </p>
            </div>
          ) : (
            <AdminButton
              variant="secondary"
              size="sm"
              onClick={() => handleAssignToSelf(item.id)}
              disabled={!canModerate}
            >
              Assign to Me
            </AdminButton>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (item: ModerationQueueItem) => (
        <div className="flex items-center space-x-2">
          <AdminButton
            variant="secondary"
            size="sm"
            icon={Eye}
            onClick={() => {
              // Open detailed review modal based on content type
            }}
            title="Review Content"
          >
            Review
          </AdminButton>
          
          {item.flags.length > 0 && (
            <AdminButton
              variant="secondary"
              size="sm"
              icon={AlertTriangle}
              onClick={() => handleProcessItem(item.id, 'escalate', 'Multiple flags detected')}
              title="Escalate"
            />
          )}
        </div>
      )
    }
  ];

  // Stats data
  const statsData = [
    {
      title: 'Queue Size',
      value: totalCount,
      icon: Clock,
      color: 'yellow' as const,
      trend: queueStats?.queueTrend || 0
    },
    {
      title: 'Urgent Items',
      value: queueItems.filter(item => item.priority === 'urgent').length,
      icon: AlertTriangle,
      color: 'red' as const,
      trend: 0
    },
    {
      title: 'Assigned Items',
      value: queueItems.filter(item => item.assignedTo).length,
      icon: Users,
      color: 'blue' as const,
      trend: 0
    },
    {
      title: 'Avg Response Time',
      value: `${analytics?.averageProcessingTime || 0}m`,
      icon: TrendingUp,
      color: 'green' as const,
      trend: analytics?.responseTimeTrend || 0
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Moderation Dashboard</h1>
          <p className="text-gray-400">Unified community content moderation and management</p>
        </div>
        <div className="flex items-center space-x-3">
          {canConfigure && (
            <AdminButton
              variant="secondary"
              icon={Settings}
              onClick={() => setShowAutoConfig(true)}
            >
              Auto Moderation
            </AdminButton>
          )}
          <AdminButton
            variant="secondary"
            icon={Activity}
            onClick={() => refetch()}
          >
            Refresh
          </AdminButton>
          {selectedItems.length > 0 && (
            <AdminButton
              variant="primary"
              onClick={() => {/* Show bulk actions */}}
            >
              Bulk Actions ({selectedItems.length})
            </AdminButton>
          )}
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('queue')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'queue' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Shield className="w-4 h-4" />
          <span>Moderation Queue</span>
        </button>
        
        {canViewAnalytics && (
          <button
            onClick={() => setActiveTab('analytics')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'analytics' 
                ? 'bg-purple-600 text-white' 
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <BarChart3 className="w-4 h-4" />
            <span>Analytics</span>
          </button>
        )}
        
        {canConfigure && (
          <button
            onClick={() => setActiveTab('config')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'config' 
                ? 'bg-purple-600 text-white' 
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Zap className="w-4 h-4" />
            <span>Configuration</span>
          </button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsData.map((stat, index) => (
          <CommunityStatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
          />
        ))}
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {activeTab === 'queue' && (
          <div className="space-y-6">
            {/* Search and Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search moderation queue..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div className="flex space-x-2">
                <select
                  value={filters.priority}
                  onChange={(e) => handleFilterChange({ priority: e.target.value as any })}
                  className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="all">All Priorities</option>
                  <option value="urgent">Urgent</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
                
                <select
                  value={filters.contentType}
                  onChange={(e) => handleFilterChange({ contentType: e.target.value as any })}
                  className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="all">All Types</option>
                  <option value="discussion">Discussions</option>
                  <option value="submission">Submissions</option>
                  <option value="challenge">Challenges</option>
                  <option value="comment">Comments</option>
                </select>
                
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange({ status: e.target.value as any })}
                  className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="pending">Pending</option>
                  <option value="in_review">In Review</option>
                  <option value="escalated">Escalated</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            </div>

            {/* Moderation Queue Table */}
            <AdminCard>
              <AdminTable
                columns={queueColumns}
                data={queueItems}
                loading={loading}
                error={error}
                selectable={canModerate}
                selectedRows={selectedItems}
                onSelectionChange={setSelectedItems}
                pagination={{
                  currentPage,
                  totalPages: Math.ceil(totalCount / 20),
                  pageSize: 20,
                  onPageChange: setCurrentPage
                }}
                emptyMessage="No items in moderation queue"
              />
            </AdminCard>
          </div>
        )}

        {activeTab === 'analytics' && canViewAnalytics && (
          <ModerationAnalytics analytics={analytics} />
        )}

        {activeTab === 'config' && canConfigure && (
          <AutoModerationConfig onConfigUpdate={refetch} />
        )}
      </div>

      {/* Auto Moderation Config Modal */}
      {showAutoConfig && (
        <AutoModerationConfig
          onClose={() => setShowAutoConfig(false)}
          onConfigUpdate={refetch}
        />
      )}
    </div>
  );
};

export default ModerationDashboard;
