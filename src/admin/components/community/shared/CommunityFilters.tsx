/**
 * Community Filters Component
 *
 * Comprehensive filtering interface for community admin features.
 * Supports discussions, submissions, challenges, and other community content.
 *
 * Features:
 * - Multi-select category filtering
 * - Date range selection with presets
 * - Status and escalation level filters
 * - Moderator assignment filters
 * - Real-time filter application
 * - Filter persistence and reset functionality
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar,
  Filter,
  X,
  ChevronDown,
  Check,
  RotateCcw
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import type { DiscussionFilters } from '../types';

interface CommunityFiltersProps {
  filters: DiscussionFilters | any; // Allow for different filter types
  onFiltersChange: (filters: Partial<DiscussionFilters> | any) => void;
  type: 'discussions' | 'submissions' | 'challenges';
  className?: string;
}

const statusOptions = {
  discussions: [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending Review' },
    { value: 'approved', label: 'Approved' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'flagged', label: 'Flagged' },
    { value: 'hidden', label: 'Hidden' },
    { value: 'escalated', label: 'Escalated' }
  ],
  submissions: [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending Review' },
    { value: 'approved', label: 'Approved' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'featured', label: 'Featured' }
  ],
  challenges: [
    { value: 'all', label: 'All Status' },
    { value: 'draft', label: 'Draft' },
    { value: 'active', label: 'Active' },
    { value: 'judging', label: 'Judging' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ]
};

const escalationLevels = [
  { value: 'all', label: 'All Levels' },
  { value: 'none', label: 'None' },
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'critical', label: 'Critical' }
];

const categoryOptions = [
  'General Discussion',
  'Keycap Designs',
  'Technical Help',
  'Community Events',
  'Feedback',
  'Announcements',
  'Off Topic'
];

const datePresets = [
  { label: 'Last 7 days', days: 7 },
  { label: 'Last 30 days', days: 30 },
  { label: 'Last 90 days', days: 90 },
  { label: 'Last 6 months', days: 180 },
  { label: 'Last year', days: 365 }
];

export const CommunityFilters: React.FC<CommunityFiltersProps> = ({
  filters,
  onFiltersChange,
  type,
  className = ''
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  const handleStatusChange = useCallback((status: string) => {
    onFiltersChange({ status: status as any });
  }, [onFiltersChange]);

  const handleEscalationChange = useCallback((level: string) => {
    onFiltersChange({ escalationLevel: level as any });
  }, [onFiltersChange]);

  const handleCategoryToggle = useCallback((category: string) => {
    const newCategories = filters.category.includes(category)
      ? filters.category.filter((c: string) => c !== category)
      : [...filters.category, category];
    onFiltersChange({ category: newCategories });
  }, [filters.category, onFiltersChange]);

  const handleDatePreset = useCallback((days: number) => {
    const end = new Date();
    const start = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    onFiltersChange({ dateRange: { start, end } });
    setShowDatePicker(false);
  }, [onFiltersChange]);

  const handleDateRangeChange = useCallback((start: Date, end: Date) => {
    onFiltersChange({ dateRange: { start, end } });
  }, [onFiltersChange]);

  const resetFilters = useCallback(() => {
    onFiltersChange({
      status: 'all',
      category: [],
      dateRange: { 
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 
        end: new Date() 
      },
      escalationLevel: 'all',
      moderator: [],
      searchTerm: ''
    });
  }, [onFiltersChange]);

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.status !== 'all') count++;
    if (filters.category.length > 0) count++;
    if (filters.escalationLevel !== 'all') count++;
    if (filters.moderator.length > 0) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <AdminCard className={`p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-white">Filters</h3>
          {activeFilterCount > 0 && (
            <span className="px-2 py-1 text-xs bg-blue-600 text-white rounded-full">
              {activeFilterCount}
            </span>
          )}
        </div>
        <AdminButton
          variant="secondary"
          size="sm"
          icon={RotateCcw}
          onClick={resetFilters}
          disabled={activeFilterCount === 0}
        >
          Reset
        </AdminButton>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Status
          </label>
          <select
            value={filters.status}
            onChange={(e) => handleStatusChange(e.target.value)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {statusOptions[type].map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Category Filter */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Categories
          </label>
          <button
            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white text-left focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-between"
          >
            <span>
              {filters.category.length === 0 
                ? 'All Categories' 
                : `${filters.category.length} selected`
              }
            </span>
            <ChevronDown className="w-4 h-4" />
          </button>

          {showCategoryDropdown && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute z-10 w-full mt-1 bg-gray-800 border border-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto"
            >
              {categoryOptions.map(category => (
                <label
                  key={category}
                  className="flex items-center px-3 py-2 hover:bg-gray-700 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={filters.category.includes(category)}
                    onChange={() => handleCategoryToggle(category)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 border border-gray-600 rounded mr-3 flex items-center justify-center ${
                    filters.category.includes(category) ? 'bg-blue-600 border-blue-600' : ''
                  }`}>
                    {filters.category.includes(category) && (
                      <Check className="w-3 h-3 text-white" />
                    )}
                  </div>
                  <span className="text-white text-sm">{category}</span>
                </label>
              ))}
            </motion.div>
          )}
        </div>

        {/* Escalation Level Filter (for discussions) */}
        {type === 'discussions' && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Escalation Level
            </label>
            <select
              value={filters.escalationLevel}
              onChange={(e) => handleEscalationChange(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {escalationLevels.map(level => (
                <option key={level.value} value={level.value}>
                  {level.label}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Date Range Filter */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Date Range
          </label>
          <button
            onClick={() => setShowDatePicker(!showDatePicker)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white text-left focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-between"
          >
            <span className="text-sm">
              {filters.dateRange.start.toLocaleDateString()} - {filters.dateRange.end.toLocaleDateString()}
            </span>
            <Calendar className="w-4 h-4" />
          </button>

          {showDatePicker && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute z-10 w-full mt-1 bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-3"
            >
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-300 mb-2">Quick Select</p>
                {datePresets.map(preset => (
                  <button
                    key={preset.days}
                    onClick={() => handleDatePreset(preset.days)}
                    className="block w-full text-left px-2 py-1 text-sm text-white hover:bg-gray-700 rounded"
                  >
                    {preset.label}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <p className="text-sm text-gray-400 mb-2">Active Filters:</p>
          <div className="flex flex-wrap gap-2">
            {filters.status !== 'all' && (
              <span className="inline-flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded-full">
                Status: {statusOptions[type].find(s => s.value === filters.status)?.label}
                <button
                  onClick={() => handleStatusChange('all')}
                  className="ml-1 hover:bg-blue-700 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            
            {filters.category.map((category: string) => (
              <span key={category} className="inline-flex items-center px-2 py-1 text-xs bg-green-600 text-white rounded-full">
                {category}
                <button
                  onClick={() => handleCategoryToggle(category)}
                  className="ml-1 hover:bg-green-700 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
            
            {filters.escalationLevel !== 'all' && (
              <span className="inline-flex items-center px-2 py-1 text-xs bg-red-600 text-white rounded-full">
                Level: {escalationLevels.find(l => l.value === filters.escalationLevel)?.label}
                <button
                  onClick={() => handleEscalationChange('all')}
                  className="ml-1 hover:bg-red-700 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </AdminCard>
  );
};

export default CommunityFilters;
