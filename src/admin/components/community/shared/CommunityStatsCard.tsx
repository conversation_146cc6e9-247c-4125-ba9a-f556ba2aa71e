/**
 * Community Stats Card Component
 *
 * Reusable statistics card component for community admin interfaces.
 * Displays key metrics with icons, trends, and visual indicators.
 *
 * Features:
 * - Customizable icon and color themes
 * - Trend indicators (up, down, stable)
 * - Loading and error states
 * - Responsive design
 * - Accessibility compliance
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { AdminCard } from '../../common';

interface CommunityStatsCardProps {
  title: string;
  value: number | string;
  icon: LucideIcon;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  trend?: number;
  trendLabel?: string;
  subtitle?: string;
  loading?: boolean;
  error?: string;
  onClick?: () => void;
  className?: string;
}

const colorVariants = {
  blue: {
    icon: 'text-blue-400',
    bg: 'bg-blue-500/10',
    border: 'border-blue-500/20',
    trend: 'text-blue-400'
  },
  green: {
    icon: 'text-green-400',
    bg: 'bg-green-500/10',
    border: 'border-green-500/20',
    trend: 'text-green-400'
  },
  yellow: {
    icon: 'text-yellow-400',
    bg: 'bg-yellow-500/10',
    border: 'border-yellow-500/20',
    trend: 'text-yellow-400'
  },
  red: {
    icon: 'text-red-400',
    bg: 'bg-red-500/10',
    border: 'border-red-500/20',
    trend: 'text-red-400'
  },
  purple: {
    icon: 'text-purple-400',
    bg: 'bg-purple-500/10',
    border: 'border-purple-500/20',
    trend: 'text-purple-400'
  },
  gray: {
    icon: 'text-gray-400',
    bg: 'bg-gray-500/10',
    border: 'border-gray-500/20',
    trend: 'text-gray-400'
  }
};

export const CommunityStatsCard: React.FC<CommunityStatsCardProps> = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  trend,
  trendLabel,
  subtitle,
  loading = false,
  error,
  onClick,
  className = ''
}) => {
  const colorClasses = colorVariants[color];

  const formatValue = (val: number | string): string => {
    if (typeof val === 'string') return val;
    
    if (val >= 1000000) {
      return `${(val / 1000000).toFixed(1)}M`;
    } else if (val >= 1000) {
      return `${(val / 1000).toFixed(1)}K`;
    }
    return val.toString();
  };

  const getTrendIcon = () => {
    if (!trend || trend === 0) return Minus;
    return trend > 0 ? TrendingUp : TrendingDown;
  };

  const getTrendColor = () => {
    if (!trend || trend === 0) return 'text-gray-400';
    return trend > 0 ? 'text-green-400' : 'text-red-400';
  };

  const getTrendText = () => {
    if (!trend || trend === 0) return 'No change';
    const percentage = Math.abs(trend);
    const direction = trend > 0 ? 'increase' : 'decrease';
    return `${percentage}% ${direction}`;
  };

  if (error) {
    return (
      <AdminCard className={`p-4 border-red-500/20 ${className}`}>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-sm">{title}</p>
            <p className="text-red-400 text-sm">Error loading data</p>
          </div>
          <div className={`p-2 rounded-lg bg-red-500/10 border border-red-500/20`}>
            <Icon className="w-6 h-6 text-red-400" />
          </div>
        </div>
      </AdminCard>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={onClick ? { scale: 1.02 } : undefined}
      className={className}
    >
      <AdminCard 
        className={`p-4 transition-all duration-200 ${
          onClick ? 'cursor-pointer hover:shadow-lg' : ''
        } ${colorClasses.border}`}
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <p className="text-gray-400 text-sm font-medium mb-1">{title}</p>
            
            {loading ? (
              <div className="animate-pulse">
                <div className="h-8 bg-gray-700 rounded w-16 mb-2"></div>
                {subtitle && <div className="h-4 bg-gray-700 rounded w-24"></div>}
              </div>
            ) : (
              <>
                <p className="text-2xl font-bold text-white mb-1">
                  {formatValue(value)}
                </p>
                
                {subtitle && (
                  <p className="text-gray-500 text-xs">{subtitle}</p>
                )}
                
                {trend !== undefined && (
                  <div className="flex items-center space-x-1 mt-2">
                    {React.createElement(getTrendIcon(), {
                      size: 14,
                      className: getTrendColor()
                    })}
                    <span className={`text-xs ${getTrendColor()}`}>
                      {trendLabel || getTrendText()}
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
          
          <div className={`p-3 rounded-lg ${colorClasses.bg} border ${colorClasses.border}`}>
            <Icon className={`w-6 h-6 ${colorClasses.icon}`} />
          </div>
        </div>
      </AdminCard>
    </motion.div>
  );
};

export default CommunityStatsCard;
