/**
 * Bulk Submission Actions Component
 *
 * Floating action panel for performing bulk moderation operations
 * on multiple selected submissions simultaneously.
 *
 * Features:
 * - Bulk approve, reject, feature, and delete operations
 * - Quality-based filtering for bulk actions
 * - Confirmation dialogs for destructive actions
 * - Progress tracking for bulk operations
 * - Reason and feedback input for audit trail
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle,
  XCircle,
  Star,
  Trash2,
  AlertTriangle,
  X,
  ChevronDown,
  ChevronUp,
  Filter,
  Award
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';

interface BulkSubmissionActionsProps {
  selectedCount: number;
  onBulkAction: (action: string, reason: string) => Promise<void>;
  onClearSelection: () => void;
  className?: string;
}

interface BulkActionOption {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'green' | 'red' | 'purple' | 'yellow' | 'gray';
  requiresConfirmation: boolean;
  destructive: boolean;
  requiredPermission: string;
  description: string;
}

export const BulkSubmissionActions: React.FC<BulkSubmissionActionsProps> = ({
  selectedCount,
  onBulkAction,
  onClearSelection,
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [reason, setReason] = useState('');
  const [feedback, setFeedback] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [qualityThreshold, setQualityThreshold] = useState(70);

  // Available bulk actions
  const bulkActions: BulkActionOption[] = [
    {
      id: 'approve',
      label: 'Approve All',
      icon: CheckCircle,
      color: 'green',
      requiresConfirmation: false,
      destructive: false,
      requiredPermission: 'moderate',
      description: 'Approve all selected submissions for publication'
    },
    {
      id: 'reject',
      label: 'Reject All',
      icon: XCircle,
      color: 'red',
      requiresConfirmation: true,
      destructive: true,
      requiredPermission: 'moderate',
      description: 'Reject all selected submissions with reason'
    },
    {
      id: 'feature',
      label: 'Feature All',
      icon: Star,
      color: 'purple',
      requiresConfirmation: true,
      destructive: false,
      requiredPermission: 'moderate',
      description: 'Feature all selected high-quality submissions'
    },
    {
      id: 'request_changes',
      label: 'Request Changes',
      icon: AlertTriangle,
      color: 'yellow',
      requiresConfirmation: false,
      destructive: false,
      requiredPermission: 'moderate',
      description: 'Request improvements from authors'
    },
    {
      id: 'delete',
      label: 'Delete All',
      icon: Trash2,
      color: 'red',
      requiresConfirmation: true,
      destructive: true,
      requiredPermission: 'delete',
      description: 'Permanently delete all selected submissions'
    }
  ];

  // Filter actions based on permissions
  const availableActions = bulkActions.filter(action => 
    hasPermission('community_submissions', action.requiredPermission as any)
  );

  // Handle action selection
  const handleActionSelect = useCallback((actionId: string) => {
    const action = availableActions.find(a => a.id === actionId);
    if (!action) return;

    setSelectedAction(actionId);
    
    if (action.requiresConfirmation) {
      setShowConfirmation(true);
    } else {
      handleExecuteAction(actionId);
    }
  }, [availableActions]);

  // Execute bulk action
  const handleExecuteAction = useCallback(async (actionId: string) => {
    if (!reason.trim() && selectedAction !== 'approve') {
      return;
    }

    try {
      setLoading(true);
      await onBulkAction(actionId, reason.trim() || 'Bulk action performed');
      
      // Reset state
      setSelectedAction('');
      setReason('');
      setFeedback('');
      setShowConfirmation(false);
      setIsExpanded(false);
    } catch (error) {
      console.error('Error executing bulk action:', error);
    } finally {
      setLoading(false);
    }
  }, [reason, selectedAction, onBulkAction]);

  // Get action color classes
  const getActionColorClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-green-600 hover:bg-green-700 text-white';
      case 'red':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'purple':
        return 'bg-purple-600 hover:bg-purple-700 text-white';
      case 'yellow':
        return 'bg-yellow-600 hover:bg-yellow-700 text-white';
      default:
        return 'bg-gray-600 hover:bg-gray-700 text-white';
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 100 }}
        className={`fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40 ${className}`}
      >
        <AdminCard className="shadow-2xl border-2 border-purple-500/20">
          {/* Main Action Bar */}
          <div className="flex items-center space-x-4 p-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
              <span className="text-white font-medium">
                {selectedCount} submission{selectedCount !== 1 ? 's' : ''} selected
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <AdminButton
                variant="secondary"
                size="sm"
                icon={isExpanded ? ChevronDown : ChevronUp}
                onClick={() => setIsExpanded(!isExpanded)}
              >
                Actions
              </AdminButton>
              
              <AdminButton
                variant="secondary"
                size="sm"
                icon={Filter}
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                title="Advanced Options"
              />
              
              <AdminButton
                variant="secondary"
                size="sm"
                icon={X}
                onClick={onClearSelection}
              >
                Clear
              </AdminButton>
            </div>
          </div>

          {/* Advanced Options */}
          <AnimatePresence>
            {showAdvancedOptions && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border-t border-gray-700 p-4"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Award className="w-4 h-4 text-yellow-400" />
                    <span className="text-sm text-gray-300">Quality Threshold:</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={qualityThreshold}
                      onChange={(e) => setQualityThreshold(Number(e.target.value))}
                      className="w-24"
                    />
                    <span className="text-sm text-white font-medium">{qualityThreshold}</span>
                  </div>
                  <span className="text-xs text-gray-400">
                    Only apply actions to submissions above this quality score
                  </span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Expanded Actions */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border-t border-gray-700"
              >
                <div className="p-4 space-y-4">
                  {/* Quick Actions Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-2">
                    {availableActions.map((action) => {
                      const Icon = action.icon;
                      return (
                        <button
                          key={action.id}
                          onClick={() => handleActionSelect(action.id)}
                          disabled={loading}
                          className={`flex flex-col items-center justify-center space-y-2 p-3 rounded-lg text-sm font-medium transition-colors ${getActionColorClasses(action.color)} disabled:opacity-50`}
                          title={action.description}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="text-xs text-center">{action.label}</span>
                        </button>
                      );
                    })}
                  </div>

                  {/* Reason and Feedback Inputs */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Reason for bulk action
                      </label>
                      <textarea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Provide a reason for this bulk action..."
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Feedback to authors (optional)
                      </label>
                      <textarea
                        value={feedback}
                        onChange={(e) => setFeedback(e.target.value)}
                        placeholder="Constructive feedback for authors..."
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                      />
                    </div>
                  </div>

                  {/* Action Summary */}
                  <div className="bg-gray-800 rounded-lg p-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">
                        {showAdvancedOptions && qualityThreshold > 0 
                          ? `Will apply to submissions with quality score ≥ ${qualityThreshold}`
                          : `Will apply to all ${selectedCount} selected submissions`
                        }
                      </span>
                      <span className="text-purple-400 font-medium">
                        Ready to execute
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </AdminCard>

        {/* Confirmation Modal */}
        <AnimatePresence>
          {showConfirmation && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="w-full max-w-md mx-4"
              >
                <AdminCard className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <AlertTriangle className="w-6 h-6 text-yellow-400" />
                    <h3 className="text-lg font-semibold text-white">Confirm Bulk Action</h3>
                  </div>

                  <div className="mb-4">
                    <p className="text-gray-300 mb-2">
                      Are you sure you want to{' '}
                      <span className="font-semibold text-white">
                        {availableActions.find(a => a.id === selectedAction)?.label.toLowerCase()}
                      </span>{' '}
                      {selectedCount} submission{selectedCount !== 1 ? 's' : ''}?
                    </p>
                    
                    {showAdvancedOptions && qualityThreshold > 0 && (
                      <p className="text-sm text-yellow-400">
                        ⚠️ Only submissions with quality score ≥ {qualityThreshold} will be affected
                      </p>
                    )}
                  </div>

                  {!reason.trim() && selectedAction !== 'approve' && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Reason <span className="text-red-400">*</span>
                      </label>
                      <textarea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="This action requires a reason..."
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                      />
                    </div>
                  )}

                  <div className="flex space-x-3">
                    <AdminButton
                      variant="primary"
                      onClick={() => handleExecuteAction(selectedAction)}
                      disabled={loading || (!reason.trim() && selectedAction !== 'approve')}
                      loading={loading}
                      className="flex-1"
                    >
                      Confirm Action
                    </AdminButton>
                    <AdminButton
                      variant="secondary"
                      onClick={() => {
                        setShowConfirmation(false);
                        setSelectedAction('');
                      }}
                      disabled={loading}
                      className="flex-1"
                    >
                      Cancel
                    </AdminButton>
                  </div>
                </AdminCard>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  );
};

export default BulkSubmissionActions;
