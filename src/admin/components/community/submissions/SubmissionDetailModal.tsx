/**
 * Submission Detail Modal Component
 *
 * Comprehensive modal interface for reviewing and moderating individual submissions.
 * Provides detailed content view, quality assessment, and moderation tools.
 *
 * Features:
 * - Full submission content display with media preview
 * - Quality scoring and assessment tools
 * - Moderation actions (approve, reject, feature, request changes)
 * - Author information and submission history
 * - Engagement metrics and analytics
 * - Admin notes and feedback system
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X,
  Star,
  Eye,
  ThumbsUp,
  MessageSquare,
  Share2,
  Bookmark,
  User,
  Calendar,
  Award,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Edit,
  Image,
  Video,
  FileText,
  Download,
  ExternalLink
} from 'lucide-react';
import { AdminCard, AdminButton } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import type { 
  SubmissionReviewItem,
  QualityReport
} from '../types';

interface SubmissionDetailModalProps {
  submissionId: string;
  onClose: () => void;
  onAction: (submissionId: string, action: string, reason: string) => Promise<void>;
  className?: string;
}

export const SubmissionDetailModal: React.FC<SubmissionDetailModalProps> = ({
  submissionId,
  onClose,
  onAction,
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State
  const [submission, setSubmission] = useState<SubmissionReviewItem | null>(null);
  const [qualityReport, setQualityReport] = useState<QualityReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [reason, setReason] = useState('');
  const [feedback, setFeedback] = useState('');
  const [showQualityAssessment, setShowQualityAssessment] = useState(false);

  // Permission checks
  const canModerate = hasPermission('community_submissions', 'moderate');
  const canDelete = hasPermission('community_submissions', 'delete');

  // Load submission data
  useEffect(() => {
    const loadSubmission = async () => {
      try {
        setLoading(true);
        // Mock submission data - in real implementation, fetch from Firestore
        const mockSubmission: SubmissionReviewItem = {
          id: submissionId,
          title: 'Custom Artisan Keycap Design - Cherry Blossom Theme',
          description: 'A beautiful cherry blossom themed artisan keycap design featuring delicate pink petals and traditional Japanese aesthetics. This design incorporates translucent resin with embedded sakura petals for a stunning visual effect.',
          author: {
            id: 'user123',
            displayName: 'ArtisanCrafter',
            email: '<EMAIL>'
          },
          category: 'Artisan Keycaps',
          submittedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          status: 'pending',
          priority: 'medium',
          contentType: 'image',
          contentUrl: 'https://example.com/keycap-image.jpg',
          thumbnailUrl: 'https://example.com/keycap-thumb.jpg',
          moderationFlags: [],
          qualityScore: 85,
          engagementMetrics: {
            views: 1250,
            likes: 89,
            comments: 23,
            shares: 12,
            saves: 45,
            engagementRate: 0.14
          },
          tags: ['artisan', 'cherry-blossom', 'resin', 'japanese', 'pink'],
          reviewedBy: undefined,
          reviewedAt: undefined,
          reviewNotes: undefined
        };
        
        setSubmission(mockSubmission);

        // Load quality assessment if available
        const mockQualityReport: QualityReport = {
          overallScore: 85,
          contentQuality: 90,
          technicalQuality: 82,
          communityFit: 88,
          originalityScore: 80,
          recommendations: [
            'Excellent craftsmanship and attention to detail',
            'Consider adding more process photos',
            'Great color scheme and theme execution'
          ],
          flags: []
        };
        setQualityReport(mockQualityReport);
      } catch (error) {
        console.error('Error loading submission:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSubmission();
  }, [submissionId]);

  // Handle moderation action
  const handleModerationAction = useCallback(async () => {
    if (!selectedAction || !reason.trim() || !submission) return;

    try {
      setActionLoading(true);
      await onAction(submissionId, selectedAction, reason.trim());
      onClose();
    } catch (error) {
      console.error('Error performing moderation action:', error);
    } finally {
      setActionLoading(false);
    }
  }, [selectedAction, reason, submission, submissionId, onAction, onClose]);

  // Get content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'image': return Image;
      case 'video': return Video;
      case 'text': return FileText;
      default: return FileText;
    }
  };

  // Render quality score with color
  const renderQualityScore = (score: number, label: string) => {
    const getColor = (score: number) => {
      if (score >= 80) return 'text-green-400';
      if (score >= 60) return 'text-yellow-400';
      if (score >= 40) return 'text-orange-400';
      return 'text-red-400';
    };

    return (
      <div className="text-center">
        <div className={`text-2xl font-bold ${getColor(score)}`}>{score}</div>
        <div className="text-xs text-gray-400">{label}</div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <AdminCard className="w-full max-w-4xl mx-4 p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-700 rounded w-1/3"></div>
            <div className="h-32 bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-700 rounded w-2/3"></div>
          </div>
        </AdminCard>
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <AdminCard className="w-full max-w-md mx-4 p-6 text-center">
          <p className="text-red-400">Failed to load submission</p>
          <AdminButton onClick={onClose} className="mt-4">
            Close
          </AdminButton>
        </AdminCard>
      </div>
    );
  }

  const ContentIcon = getContentTypeIcon(submission.contentType);

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="w-full max-w-7xl max-h-[90vh] overflow-hidden"
        >
          <AdminCard className="h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center space-x-3">
                <ContentIcon className="w-6 h-6 text-blue-400" />
                <h2 className="text-xl font-bold text-white">Submission Review</h2>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  submission.status === 'pending' ? 'bg-yellow-600 text-white' :
                  submission.status === 'approved' ? 'bg-green-600 text-white' :
                  submission.status === 'featured' ? 'bg-purple-600 text-white' :
                  'bg-red-600 text-white'
                }`}>
                  {submission.status}
                </span>
                {submission.status === 'featured' && (
                  <Star className="w-5 h-5 text-yellow-400" />
                )}
              </div>
              <AdminButton
                variant="secondary"
                size="sm"
                icon={X}
                onClick={onClose}
              />
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
                  {/* Submission Info */}
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-3">{submission.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-400 mb-4">
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{submission.author.displayName}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{submission.submittedAt.toLocaleDateString()}</span>
                      </div>
                      <span className="px-2 py-1 text-xs bg-gray-700 rounded">{submission.category}</span>
                    </div>
                  </div>

                  {/* Content Preview */}
                  <AdminCard className="p-4">
                    <h4 className="font-semibold text-white mb-3">Content Preview</h4>
                    {submission.contentType === 'image' && submission.contentUrl && (
                      <div className="relative">
                        <img 
                          src={submission.contentUrl} 
                          alt={submission.title}
                          className="w-full max-h-96 object-contain rounded-lg bg-gray-800"
                        />
                        <div className="absolute top-2 right-2 flex space-x-2">
                          <AdminButton
                            variant="secondary"
                            size="sm"
                            icon={ExternalLink}
                            onClick={() => window.open(submission.contentUrl, '_blank')}
                          />
                          <AdminButton
                            variant="secondary"
                            size="sm"
                            icon={Download}
                            onClick={() => {/* Download functionality */}}
                          />
                        </div>
                      </div>
                    )}
                    <div className="mt-4">
                      <p className="text-gray-300">{submission.description}</p>
                    </div>
                  </AdminCard>

                  {/* Tags */}
                  {submission.tags.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-white mb-2">Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {submission.tags.map(tag => (
                          <span key={tag} className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded-full">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Engagement Metrics */}
                  <AdminCard className="p-4">
                    <h4 className="font-semibold text-white mb-3">Engagement Metrics</h4>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      <div className="text-center">
                        <Eye className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                        <div className="text-lg font-bold text-white">{submission.engagementMetrics.views}</div>
                        <div className="text-xs text-gray-400">Views</div>
                      </div>
                      <div className="text-center">
                        <ThumbsUp className="w-5 h-5 text-green-400 mx-auto mb-1" />
                        <div className="text-lg font-bold text-white">{submission.engagementMetrics.likes}</div>
                        <div className="text-xs text-gray-400">Likes</div>
                      </div>
                      <div className="text-center">
                        <MessageSquare className="w-5 h-5 text-yellow-400 mx-auto mb-1" />
                        <div className="text-lg font-bold text-white">{submission.engagementMetrics.comments}</div>
                        <div className="text-xs text-gray-400">Comments</div>
                      </div>
                      <div className="text-center">
                        <Share2 className="w-5 h-5 text-purple-400 mx-auto mb-1" />
                        <div className="text-lg font-bold text-white">{submission.engagementMetrics.shares}</div>
                        <div className="text-xs text-gray-400">Shares</div>
                      </div>
                      <div className="text-center">
                        <Bookmark className="w-5 h-5 text-orange-400 mx-auto mb-1" />
                        <div className="text-lg font-bold text-white">{submission.engagementMetrics.saves}</div>
                        <div className="text-xs text-gray-400">Saves</div>
                      </div>
                    </div>
                  </AdminCard>
                </div>

                {/* Sidebar */}
                <div className="space-y-4">
                  {/* Quality Assessment */}
                  {qualityReport && (
                    <AdminCard className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-white flex items-center">
                          <Award className="w-4 h-4 mr-2 text-yellow-400" />
                          Quality Assessment
                        </h4>
                        <AdminButton
                          variant="secondary"
                          size="sm"
                          onClick={() => setShowQualityAssessment(!showQualityAssessment)}
                        >
                          {showQualityAssessment ? 'Hide' : 'Details'}
                        </AdminButton>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-3 mb-3">
                        {renderQualityScore(qualityReport.overallScore, 'Overall')}
                        {renderQualityScore(qualityReport.contentQuality, 'Content')}
                      </div>

                      {showQualityAssessment && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          className="space-y-3 pt-3 border-t border-gray-700"
                        >
                          <div className="grid grid-cols-2 gap-3">
                            {renderQualityScore(qualityReport.technicalQuality, 'Technical')}
                            {renderQualityScore(qualityReport.communityFit, 'Community Fit')}
                          </div>
                          <div className="text-center">
                            {renderQualityScore(qualityReport.originalityScore, 'Originality')}
                          </div>
                          
                          {qualityReport.recommendations.length > 0 && (
                            <div>
                              <h5 className="text-sm font-medium text-gray-300 mb-2">Recommendations:</h5>
                              <ul className="text-xs text-gray-400 space-y-1">
                                {qualityReport.recommendations.map((rec, index) => (
                                  <li key={index}>• {rec}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </motion.div>
                      )}
                    </AdminCard>
                  )}

                  {/* Author Info */}
                  <AdminCard className="p-4">
                    <h4 className="font-semibold text-white mb-3">Author Information</h4>
                    <div className="space-y-2 text-sm">
                      <p className="text-gray-300">{submission.author.displayName}</p>
                      <p className="text-gray-400">{submission.author.email}</p>
                      <p className="text-gray-500">Member since: Jan 2024</p>
                      <p className="text-gray-500">Total submissions: 12</p>
                      <p className="text-gray-500">Approval rate: 85%</p>
                    </div>
                  </AdminCard>

                  {/* Previous Reviews */}
                  {submission.reviewedBy && (
                    <AdminCard className="p-4">
                      <h4 className="font-semibold text-white mb-3">Review History</h4>
                      <div className="space-y-2 text-sm">
                        <p className="text-gray-300">Reviewed by: {submission.reviewedBy}</p>
                        <p className="text-gray-400">Date: {submission.reviewedAt?.toLocaleDateString()}</p>
                        {submission.reviewNotes && (
                          <p className="text-gray-400">Notes: {submission.reviewNotes}</p>
                        )}
                      </div>
                    </AdminCard>
                  )}
                </div>
              </div>
            </div>

            {/* Moderation Actions */}
            {canModerate && (
              <div className="border-t border-gray-700 p-6">
                <h4 className="font-semibold text-white mb-4">Moderation Actions</h4>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Action Selection */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Select Action
                      </label>
                      <select
                        value={selectedAction}
                        onChange={(e) => setSelectedAction(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Choose an action...</option>
                        <option value="approve">Approve Submission</option>
                        <option value="reject">Reject Submission</option>
                        <option value="feature">Feature Submission</option>
                        <option value="request_changes">Request Changes</option>
                        {canDelete && <option value="delete">Delete Submission</option>}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Reason <span className="text-red-400">*</span>
                      </label>
                      <textarea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Provide a reason for this action..."
                        rows={3}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Feedback */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Feedback to Author (Optional)
                      </label>
                      <textarea
                        value={feedback}
                        onChange={(e) => setFeedback(e.target.value)}
                        placeholder="Constructive feedback for the author..."
                        rows={3}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div className="flex space-x-3">
                      <AdminButton
                        variant="primary"
                        onClick={handleModerationAction}
                        disabled={!selectedAction || !reason.trim() || actionLoading}
                        loading={actionLoading}
                        className="flex-1"
                      >
                        Execute Action
                      </AdminButton>
                      <AdminButton
                        variant="secondary"
                        onClick={onClose}
                        disabled={actionLoading}
                      >
                        Cancel
                      </AdminButton>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </AdminCard>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default SubmissionDetailModal;
