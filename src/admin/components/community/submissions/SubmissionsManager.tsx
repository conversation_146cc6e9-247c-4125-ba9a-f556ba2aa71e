/**
 * Submissions Manager Component
 *
 * Comprehensive admin interface for managing community submissions.
 * Provides review queue, moderation tools, quality assessment, and content curation.
 *
 * Features:
 * - Submission review queue with priority sorting
 * - Quality scoring and assessment tools
 * - Content moderation and approval workflow
 * - Featured content curation system
 * - Bulk operations for efficient management
 * - Real-time analytics and performance metrics
 * - Advanced filtering and search capabilities
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Upload, 
  Filter, 
  Search, 
  MoreHorizontal,
  Star,
  Eye,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  TrendingUp,
  Image,
  Video,
  FileText,
  Award,
  BarChart3,
  Users
} from 'lucide-react';
import { AdminCard, AdminButton, AdminTable } from '../../common';
import { useAdminPermissions } from '../../../hooks/useAdminPermissions';
import { useSubmissionsData } from '../hooks/useSubmissionsData';
import { SubmissionDetailModal } from './SubmissionDetailModal';
import { BulkSubmissionActions } from './BulkSubmissionActions';
import { CommunityStatsCard } from '../shared/CommunityStatsCard';
import { CommunityFilters } from '../shared/CommunityFilters';
import type { 
  SubmissionFilters, 
  SubmissionReviewItem, 
  SubmissionModerationActions 
} from '../types';

interface SubmissionsManagerProps {
  className?: string;
}

export const SubmissionsManager: React.FC<SubmissionsManagerProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [filters, setFilters] = useState<SubmissionFilters>({
    status: 'all',
    category: [],
    contentType: 'all',
    dateRange: { 
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 
      end: new Date() 
    },
    priority: 'all',
    qualityScore: { min: 0, max: 100 }
  });
  
  const [selectedSubmissions, setSelectedSubmissions] = useState<string[]>([]);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [currentSubmission, setCurrentSubmission] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');

  // Data hooks
  const {
    submissions,
    loading,
    error,
    totalCount,
    analytics,
    refetch,
    moderateSubmission,
    bulkModerateSubmissions
  } = useSubmissionsData(filters, { page: currentPage, pageSize: 20 });

  // Permission checks
  const canModerate = hasPermission('community_submissions', 'moderate');
  const canDelete = hasPermission('community_submissions', 'delete');
  const canWrite = hasPermission('community_submissions', 'write');

  // Event handlers
  const handleModerationAction = useCallback(async (
    submissionId: string,
    action: string,
    reason: string
  ) => {
    try {
      await moderateSubmission(submissionId, action, reason);
      await refetch();
      setShowDetailModal(false);
      setCurrentSubmission(null);
    } catch (error) {
      console.error('Moderation action failed:', error);
    }
  }, [moderateSubmission, refetch]);

  const handleBulkAction = useCallback(async (
    action: string,
    reason: string
  ) => {
    try {
      await bulkModerateSubmissions(selectedSubmissions, action, reason);
      await refetch();
      setSelectedSubmissions([]);
    } catch (error) {
      console.error('Bulk moderation failed:', error);
    }
  }, [bulkModerateSubmissions, selectedSubmissions, refetch]);

  const handleFilterChange = useCallback((newFilters: Partial<SubmissionFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  const handleSearch = useCallback((searchTerm: string) => {
    // Add search functionality
    setCurrentPage(1);
  }, []);

  // Get content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'image': return Image;
      case 'video': return Video;
      case 'text': return FileText;
      default: return Upload;
    }
  };

  // Get quality score color
  const getQualityScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    if (score >= 40) return 'text-orange-400';
    return 'text-red-400';
  };

  // Table configuration
  const tableColumns = [
    {
      key: 'content',
      label: 'Submission',
      sortable: true,
      render: (submission: SubmissionReviewItem) => {
        const ContentIcon = getContentTypeIcon(submission.contentType);
        return (
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {submission.thumbnailUrl ? (
                <img 
                  src={submission.thumbnailUrl} 
                  alt={submission.title}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              ) : (
                <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                  <ContentIcon className="w-6 h-6 text-gray-400" />
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <p className="text-white font-medium truncate">{submission.title}</p>
                {submission.status === 'featured' && (
                  <Star className="w-4 h-4 text-yellow-400" title="Featured" />
                )}
              </div>
              <p className="text-gray-400 text-sm">by {submission.author.displayName}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500">{submission.category}</span>
                <span className="text-xs text-gray-500 capitalize">{submission.contentType}</span>
                {submission.tags.length > 0 && (
                  <div className="flex space-x-1">
                    {submission.tags.slice(0, 2).map(tag => (
                      <span key={tag} className="px-1 py-0.5 text-xs bg-gray-700 text-gray-300 rounded">
                        {tag}
                      </span>
                    ))}
                    {submission.tags.length > 2 && (
                      <span className="text-xs text-gray-500">+{submission.tags.length - 2}</span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      }
    },
    {
      key: 'quality',
      label: 'Quality',
      sortable: true,
      render: (submission: SubmissionReviewItem) => (
        <div className="text-center">
          <div className={`text-lg font-bold ${getQualityScoreColor(submission.qualityScore)}`}>
            {submission.qualityScore}
          </div>
          <div className="text-xs text-gray-400">Score</div>
          <div className="flex items-center justify-center space-x-1 mt-1">
            <Eye className="w-3 h-3 text-gray-500" />
            <span className="text-xs text-gray-500">{submission.engagementMetrics.views}</span>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (submission: SubmissionReviewItem) => (
        <div className="flex flex-col space-y-1">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            submission.status === 'approved' ? 'bg-green-600 text-white' :
            submission.status === 'pending' ? 'bg-yellow-600 text-white' :
            submission.status === 'rejected' ? 'bg-red-600 text-white' :
            submission.status === 'featured' ? 'bg-purple-600 text-white' :
            'bg-gray-600 text-white'
          }`}>
            {submission.status}
          </span>
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            submission.priority === 'high' ? 'bg-red-500 text-white' :
            submission.priority === 'medium' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
          }`}>
            {submission.priority}
          </span>
        </div>
      )
    },
    {
      key: 'submitted',
      label: 'Submitted',
      sortable: true,
      render: (submission: SubmissionReviewItem) => (
        <div className="text-sm">
          <p className="text-white">{submission.submittedAt.toLocaleDateString()}</p>
          <p className="text-gray-400">{submission.submittedAt.toLocaleTimeString()}</p>
          {submission.reviewedBy && (
            <p className="text-gray-500 text-xs mt-1">
              Reviewed by {submission.reviewedBy}
            </p>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (submission: SubmissionReviewItem) => (
        <div className="flex items-center space-x-2">
          <AdminButton
            variant="secondary"
            size="sm"
            icon={Eye}
            onClick={() => {
              setCurrentSubmission(submission.id);
              setShowDetailModal(true);
            }}
            title="Review Submission"
          >
            Review
          </AdminButton>
          {canModerate && (
            <AdminButton
              variant="secondary"
              size="sm"
              icon={MoreHorizontal}
              onClick={() => {
                // Show quick action menu
              }}
              title="More Actions"
            />
          )}
        </div>
      )
    }
  ];

  // Stats data
  const statsData = [
    {
      title: 'Total Submissions',
      value: totalCount,
      icon: Upload,
      color: 'blue',
      trend: analytics?.submissionCount || 0
    },
    {
      title: 'Pending Review',
      value: submissions.filter(s => s.status === 'pending').length,
      icon: Clock,
      color: 'yellow',
      trend: 0
    },
    {
      title: 'Featured Content',
      value: submissions.filter(s => s.status === 'featured').length,
      icon: Star,
      color: 'purple',
      trend: 0
    },
    {
      title: 'Avg Quality Score',
      value: analytics?.averageQualityScore || 0,
      icon: Award,
      color: 'green',
      trend: 0
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Submissions Management</h1>
          <p className="text-gray-400">Review and moderate community submissions</p>
        </div>
        <div className="flex items-center space-x-3">
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={BarChart3}
            onClick={() => {/* Show analytics modal */}}
          >
            Analytics
          </AdminButton>
          <div className="flex bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('table')}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === 'table' ? 'bg-blue-600 text-white' : 'text-gray-400'
              }`}
            >
              Table
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === 'grid' ? 'bg-blue-600 text-white' : 'text-gray-400'
              }`}
            >
              Grid
            </button>
          </div>
          {selectedSubmissions.length > 0 && (
            <AdminButton
              variant="primary"
              onClick={() => {/* Show bulk actions */}}
            >
              Bulk Actions ({selectedSubmissions.length})
            </AdminButton>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search submissions..."
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <CommunityFilters
            filters={filters as any}
            onFiltersChange={handleFilterChange}
            type="submissions"
          />
        </motion.div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsData.map((stat, index) => (
          <CommunityStatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
          />
        ))}
      </div>

      {/* Main Content */}
      <AdminCard>
        <AdminTable
          columns={tableColumns}
          data={submissions}
          loading={loading}
          error={error}
          selectable={canModerate}
          selectedRows={selectedSubmissions}
          onSelectionChange={setSelectedSubmissions}
          pagination={{
            currentPage,
            totalPages: Math.ceil(totalCount / 20),
            pageSize: 20,
            onPageChange: setCurrentPage
          }}
          emptyMessage="No submissions found"
        />
      </AdminCard>

      {/* Detail Modal */}
      {showDetailModal && currentSubmission && (
        <SubmissionDetailModal
          submissionId={currentSubmission}
          onClose={() => {
            setShowDetailModal(false);
            setCurrentSubmission(null);
          }}
          onAction={handleModerationAction}
        />
      )}

      {/* Bulk Actions */}
      {selectedSubmissions.length > 0 && canModerate && (
        <BulkSubmissionActions
          selectedCount={selectedSubmissions.length}
          onBulkAction={handleBulkAction}
          onClearSelection={() => setSelectedSubmissions([])}
        />
      )}
    </div>
  );
};

export default SubmissionsManager;
