/**
 * Community Admin Types
 *
 * TypeScript type definitions for community management admin interfaces.
 * Provides comprehensive type safety for all community admin operations.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Timestamp } from 'firebase/firestore';
import { UserProfile } from '../../../types/profile';

// ===== COMMON TYPES =====

export interface TimeRange {
  start: Date;
  end: Date;
}

export interface PaginationParams {
  pageSize: number;
  cursor?: any;
  page?: number;
}

export interface PaginatedResult<T> {
  data: T[];
  hasMore: boolean;
  nextCursor?: any;
  totalCount: number;
}

// ===== MODERATION TYPES =====

export type ModerationStatus = 'pending' | 'approved' | 'rejected' | 'flagged' | 'hidden' | 'escalated';
export type EscalationLevel = 'none' | 'low' | 'medium' | 'high' | 'critical';
export type ContentType = 'discussion' | 'submission' | 'comment' | 'reply';

export interface ModerationFlag {
  id: string;
  type: 'spam' | 'inappropriate' | 'harassment' | 'copyright' | 'other';
  reason: string;
  reportedBy: string;
  reportedAt: Date;
  severity: 'low' | 'medium' | 'high';
}

export interface ModerationAction {
  type: 'approve' | 'reject' | 'hide' | 'escalate' | 'warn' | 'ban';
  status: ModerationStatus;
  previousStatus?: ModerationStatus;
  reason: string;
  adminId: string;
  timestamp: Date;
}

export interface BulkModerationAction {
  type: 'approve' | 'reject' | 'hide' | 'delete';
  status: ModerationStatus;
  reason: string;
}

export interface ModerationResult {
  success: boolean;
  discussionId?: string;
  submissionId?: string;
  action: string;
  timestamp: Date;
  error?: string;
}

export interface BulkModerationResult {
  success: boolean;
  results: ModerationResult[];
  totalProcessed: number;
  timestamp: Date;
  errors?: string[];
}

// ===== DISCUSSIONS TYPES =====

export interface DiscussionFilters {
  status: 'all' | ModerationStatus;
  category: string[];
  dateRange: TimeRange;
  escalationLevel: 'all' | EscalationLevel;
  moderator: string[];
  searchTerm?: string;
}

export interface DiscussionTableRow {
  id: string;
  title: string;
  content: string;
  author: UserProfile;
  category: string;
  createdAt: Date;
  lastActivity: Date;
  replyCount: number;
  viewCount: number;
  likeCount: number;
  moderationStatus: ModerationStatus;
  escalationLevel: EscalationLevel;
  flags: ModerationFlag[];
  moderatedBy?: string;
  moderatedAt?: Date;
  moderationReason?: string;
  adminNotes?: string;
  isLocked: boolean;
  isPinned: boolean;
  tags: string[];
}

export interface DiscussionAnalytics {
  totalDiscussions: number;
  moderationActions: number;
  escalations: number;
  averageResponseTime: number;
  trendData: DailyDiscussionData[];
  categoryBreakdown: CategoryBreakdown[];
  moderatorPerformance: ModeratorPerformance[];
  engagementMetrics: EngagementMetrics;
}

export interface DailyDiscussionData {
  date: Date;
  totalDiscussions: number;
  moderationActions: number;
  escalations: number;
  averageResponseTime: number;
}

// ===== SUBMISSIONS TYPES =====

export interface SubmissionFilters {
  status: 'all' | 'pending' | 'approved' | 'rejected' | 'featured';
  category: string[];
  contentType: 'all' | 'image' | 'video' | 'text' | 'mixed';
  dateRange: TimeRange;
  priority: 'all' | 'low' | 'medium' | 'high';
  qualityScore: { min: number; max: number };
}

export interface SubmissionReviewItem {
  id: string;
  title: string;
  description: string;
  author: UserProfile;
  category: string;
  submittedAt: Date;
  status: 'pending' | 'approved' | 'rejected' | 'featured';
  priority: 'low' | 'medium' | 'high';
  contentType: 'image' | 'video' | 'text' | 'mixed';
  contentUrl?: string;
  thumbnailUrl?: string;
  moderationFlags: ModerationFlag[];
  qualityScore: number;
  engagementMetrics: EngagementMetrics;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
  tags: string[];
}

export interface SubmissionModerationActions {
  approve: (submissionId: string, adminId: string) => Promise<void>;
  reject: (submissionId: string, reason: string, adminId: string) => Promise<void>;
  feature: (submissionId: string, adminId: string) => Promise<void>;
  requestChanges: (submissionId: string, feedback: string, adminId: string) => Promise<void>;
  escalate: (submissionId: string, reason: string, adminId: string) => Promise<void>;
}

export interface QualityReport {
  overallScore: number; // 0-100
  contentQuality: number;
  technicalQuality: number;
  communityFit: number;
  originalityScore: number;
  recommendations: string[];
  flags: QualityFlag[];
}

export interface QualityFlag {
  type: 'low_quality' | 'duplicate' | 'off_topic' | 'technical_issues';
  severity: 'low' | 'medium' | 'high';
  description: string;
}

// ===== CHALLENGES TYPES =====

export interface ChallengeAdminInterface {
  id: string;
  title: string;
  description: string;
  type: 'individual' | 'team' | 'community';
  status: 'draft' | 'active' | 'judging' | 'completed' | 'cancelled';
  startDate: Date;
  endDate: Date;
  submissionDeadline: Date;
  judgingDeadline: Date;
  participantCount: number;
  submissionCount: number;
  maxParticipants?: number;
  prizes: ChallengePrize[];
  judgingCriteria: JudgingCriteria[];
  judges: string[];
  categories: string[];
  rules: string[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChallengePrize {
  position: number;
  title: string;
  description: string;
  value: number;
  type: 'points' | 'badge' | 'physical' | 'digital';
}

export interface JudgingCriteria {
  id: string;
  name: string;
  description: string;
  weight: number; // 0-100
  maxScore: number;
}

// ===== ANALYTICS TYPES =====

export interface CategoryBreakdown {
  category: string;
  count: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
}

export interface ModeratorPerformance {
  adminId: string;
  adminName: string;
  actionsCount: number;
  averageResponseTime: number;
  accuracyScore: number;
  escalationsHandled: number;
  period: TimeRange;
}

export interface EngagementMetrics {
  views: number;
  likes: number;
  comments: number;
  shares: number;
  saves: number;
  engagementRate: number;
}

export interface CommunityAnalytics {
  discussions: DiscussionAnalytics;
  submissions: SubmissionAnalytics;
  moderation: ModerationAnalytics;
  overall: OverallCommunityMetrics;
}

export interface SubmissionAnalytics {
  totalSubmissions: number;
  approvalRate: number;
  averageQualityScore: number;
  featuredCount: number;
  categoryBreakdown: CategoryBreakdown[];
  qualityTrends: QualityTrendData[];
}

export interface ModerationAnalytics {
  queueSize: number;
  averageProcessingTime: number;
  actionBreakdown: ActionBreakdown[];
  escalationRate: number;
  moderatorWorkload: ModeratorWorkload[];
}

export interface OverallCommunityMetrics {
  totalUsers: number;
  activeUsers: number;
  contentCreated: number;
  moderationActions: number;
  communityHealthScore: number;
  growthRate: number;
}

export interface ActionBreakdown {
  action: string;
  count: number;
  percentage: number;
}

export interface ModeratorWorkload {
  adminId: string;
  adminName: string;
  pendingItems: number;
  completedToday: number;
  averageTime: number;
}

export interface QualityTrendData {
  date: Date;
  averageScore: number;
  submissionCount: number;
  approvalRate: number;
}

// ===== MODERATION QUEUE TYPES =====

export interface ModerationQueue {
  urgent: ModerationQueueItem[];
  high: ModerationQueueItem[];
  medium: ModerationQueueItem[];
  low: ModerationQueueItem[];
}

export interface ModerationQueueItem {
  id: string;
  contentId: string;
  contentType: ContentType;
  title: string;
  author: UserProfile;
  priority: 'urgent' | 'high' | 'medium' | 'low';
  flags: ModerationFlag[];
  submittedAt: Date;
  estimatedReviewTime: number; // minutes
  assignedTo?: string;
  autoModerationScore?: number;
}
