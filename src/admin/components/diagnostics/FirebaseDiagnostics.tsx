/**
 * Firebase Diagnostics Component
 * 
 * Comprehensive diagnostic tool for Firebase connection, authentication,
 * and Firestore operations to help identify JSON parsing errors.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  Database,
  Shield,
  Cloud,
  Wifi,
  Settings
} from 'lucide-react';
import { auth, db } from '../../../lib/firebase';
import { useUser } from '../../../lib/useUser';
import { doc, getDoc, collection, getDocs, query, limit } from 'firebase/firestore';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';

interface DiagnosticResult {
  name: string;
  status: 'success' | 'warning' | 'error' | 'loading';
  message: string;
  details?: any;
  timestamp: Date;
}

const FirebaseDiagnostics: React.FC = () => {
  const { user, profile, loading } = useUser();
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addDiagnostic = (result: Omit<DiagnosticResult, 'timestamp'>) => {
    setDiagnostics(prev => [...prev, { ...result, timestamp: new Date() }]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setDiagnostics([]);

    try {
      // 1. Check Firebase Configuration
      addDiagnostic({
        name: 'Firebase Configuration',
        status: 'loading',
        message: 'Checking Firebase configuration...'
      });

      const config = {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
      };

      const missingKeys = Object.entries(config).filter(([, value]) => !value);
      
      if (missingKeys.length > 0) {
        addDiagnostic({
          name: 'Firebase Configuration',
          status: 'error',
          message: `Missing configuration keys: ${missingKeys.map(([key]) => key).join(', ')}`,
          details: { missingKeys, config }
        });
      } else {
        addDiagnostic({
          name: 'Firebase Configuration',
          status: 'success',
          message: 'All Firebase configuration keys are present',
          details: { projectId: config.projectId }
        });
      }

      // 2. Check Firebase Auth Connection
      addDiagnostic({
        name: 'Firebase Auth Connection',
        status: 'loading',
        message: 'Testing Firebase Auth connection...'
      });

      try {
        const authState = auth.currentUser;
        addDiagnostic({
          name: 'Firebase Auth Connection',
          status: 'success',
          message: authState ? `Connected as ${authState.email}` : 'Connected (no user)',
          details: { 
            uid: authState?.uid,
            email: authState?.email,
            emailVerified: authState?.emailVerified
          }
        });
      } catch (authError) {
        addDiagnostic({
          name: 'Firebase Auth Connection',
          status: 'error',
          message: `Auth connection failed: ${authError}`,
          details: { error: authError }
        });
      }

      // 3. Check Firestore Connection
      addDiagnostic({
        name: 'Firestore Connection',
        status: 'loading',
        message: 'Testing Firestore connection...'
      });

      try {
        // Try to read a simple document
        const testQuery = query(collection(db, 'profiles'), limit(1));
        const snapshot = await getDocs(testQuery);
        
        addDiagnostic({
          name: 'Firestore Connection',
          status: 'success',
          message: `Connected to Firestore (${snapshot.size} test documents)`,
          details: { 
            docsCount: snapshot.size,
            projectId: db.app.options.projectId
          }
        });
      } catch (firestoreError: any) {
        addDiagnostic({
          name: 'Firestore Connection',
          status: 'error',
          message: `Firestore connection failed: ${firestoreError.message}`,
          details: { 
            error: firestoreError,
            code: firestoreError.code,
            stack: firestoreError.stack
          }
        });
      }

      // 4. Check User Profile Access
      if (user) {
        addDiagnostic({
          name: 'User Profile Access',
          status: 'loading',
          message: 'Testing user profile access...'
        });

        try {
          const userDoc = await getDoc(doc(db, 'profiles', user.uid));
          
          if (userDoc.exists()) {
            const data = userDoc.data();
            addDiagnostic({
              name: 'User Profile Access',
              status: 'success',
              message: `Profile loaded successfully for ${data.email}`,
              details: { 
                role: data.role,
                points: data.points,
                hasProfile: true
              }
            });
          } else {
            addDiagnostic({
              name: 'User Profile Access',
              status: 'warning',
              message: 'User profile document does not exist',
              details: { userId: user.uid }
            });
          }
        } catch (profileError: any) {
          addDiagnostic({
            name: 'User Profile Access',
            status: 'error',
            message: `Profile access failed: ${profileError.message}`,
            details: { 
              error: profileError,
              userId: user.uid,
              code: profileError.code
            }
          });
        }
      }

      // 5. Check Network Connectivity
      addDiagnostic({
        name: 'Network Connectivity',
        status: 'loading',
        message: 'Testing network connectivity...'
      });

      try {
        const response = await fetch('https://www.google.com/favicon.ico', { 
          method: 'HEAD',
          mode: 'no-cors'
        });
        
        addDiagnostic({
          name: 'Network Connectivity',
          status: 'success',
          message: 'Network connectivity is working',
          details: { online: navigator.onLine }
        });
      } catch (networkError) {
        addDiagnostic({
          name: 'Network Connectivity',
          status: 'error',
          message: `Network connectivity failed: ${networkError}`,
          details: { 
            online: navigator.onLine,
            error: networkError
          }
        });
      }

      // 6. Check Browser Compatibility
      addDiagnostic({
        name: 'Browser Compatibility',
        status: 'loading',
        message: 'Checking browser compatibility...'
      });

      const hasIndexedDB = 'indexedDB' in window;
      const hasLocalStorage = 'localStorage' in window;
      const hasSessionStorage = 'sessionStorage' in window;
      const hasWebSocket = 'WebSocket' in window;

      if (hasIndexedDB && hasLocalStorage && hasSessionStorage && hasWebSocket) {
        addDiagnostic({
          name: 'Browser Compatibility',
          status: 'success',
          message: 'Browser supports all required features',
          details: {
            userAgent: navigator.userAgent,
            indexedDB: hasIndexedDB,
            localStorage: hasLocalStorage,
            sessionStorage: hasSessionStorage,
            webSocket: hasWebSocket
          }
        });
      } else {
        addDiagnostic({
          name: 'Browser Compatibility',
          status: 'warning',
          message: 'Some browser features may not be supported',
          details: {
            userAgent: navigator.userAgent,
            indexedDB: hasIndexedDB,
            localStorage: hasLocalStorage,
            sessionStorage: hasSessionStorage,
            webSocket: hasWebSocket
          }
        });
      }

    } catch (error) {
      addDiagnostic({
        name: 'Diagnostic Error',
        status: 'error',
        message: `Diagnostic process failed: ${error}`,
        details: { error }
      });
    } finally {
      setIsRunning(false);
    }
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-400" />;
      case 'loading':
        return <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-600/30 bg-green-600/10';
      case 'warning':
        return 'border-yellow-600/30 bg-yellow-600/10';
      case 'error':
        return 'border-red-600/30 bg-red-600/10';
      case 'loading':
        return 'border-blue-600/30 bg-blue-600/10';
    }
  };

  return (
    <AdminCard className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Database className="w-6 h-6 text-blue-400" />
          <div>
            <h3 className="text-lg font-semibold text-white">Firebase Diagnostics</h3>
            <p className="text-sm text-gray-400">System health and connectivity checks</p>
          </div>
        </div>
        
        <AdminButton
          variant="secondary"
          icon={RefreshCw}
          onClick={runDiagnostics}
          loading={isRunning}
        >
          Run Diagnostics
        </AdminButton>
      </div>

      <div className="space-y-4">
        {diagnostics.map((diagnostic, index) => (
          <div
            key={index}
            className={`p-4 rounded-lg border ${getStatusColor(diagnostic.status)}`}
          >
            <div className="flex items-start space-x-3">
              {getStatusIcon(diagnostic.status)}
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-white">
                    {diagnostic.name}
                  </h4>
                  <span className="text-xs text-gray-400">
                    {diagnostic.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                
                <p className="text-sm text-gray-300 mt-1">
                  {diagnostic.message}
                </p>
                
                {diagnostic.details && (
                  <details className="mt-2">
                    <summary className="text-xs text-gray-400 cursor-pointer hover:text-gray-300">
                      View Details
                    </summary>
                    <pre className="text-xs text-gray-500 mt-2 p-2 bg-gray-800/50 rounded overflow-auto">
                      {JSON.stringify(diagnostic.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {diagnostics.length === 0 && !isRunning && (
        <div className="text-center py-8">
          <Settings className="w-12 h-12 text-gray-600 mx-auto mb-3" />
          <p className="text-gray-400">Click "Run Diagnostics" to check system health</p>
        </div>
      )}
    </AdminCard>
  );
};

export default FirebaseDiagnostics;
