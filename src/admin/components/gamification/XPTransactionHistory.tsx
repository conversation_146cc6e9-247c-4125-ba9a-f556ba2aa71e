/**
 * XP Transaction History Component
 * 
 * Comprehensive XP transaction log component with advanced filtering, search,
 * and pagination for admin review of all XP activities across the platform.
 * 
 * Features:
 * - Advanced filtering by date range, user, XP source, and amount
 * - Real-time search with debounced input
 * - Sortable columns with pagination
 * - Export functionality for audit trails
 * - Admin action tracking and audit logs
 * - Visual indicators for different transaction types
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  Search,
  Filter,
  Download,
  Calendar,
  User,
  Zap,
  TrendingUp,
  TrendingDown,
  Eye,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  Clock,
  Award,
  ShoppingCart,
  Activity,
  Gift,
  Settings
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { AdminCard, AdminButton, AdminTable } from '@/src/admin/components/common'
import { LevelBadge } from '@/components/level'
import { useDebounce } from '@/src/hooks/useDebounce'

// ===== TYPES =====

export interface XPTransaction {
  id: string
  userId: string
  userName: string
  userLevel: number
  userTier: string
  amount: number
  source: 'purchase' | 'activity' | 'bonus' | 'event' | 'manual' | 'referral' | 'achievement'
  sourceDetails: string
  balanceBefore: number
  balanceAfter: number
  levelBefore: number
  levelAfter: number
  timestamp: Date
  adminId?: string
  adminName?: string
  reason?: string
  metadata?: Record<string, any>
  status: 'completed' | 'pending' | 'failed' | 'reversed'
}

interface TransactionFilters {
  search: string
  dateRange: {
    start: Date | null
    end: Date | null
  }
  source: string[]
  amountRange: {
    min: number | null
    max: number | null
  }
  status: string[]
  adminOnly: boolean
}

interface SortConfig {
  key: keyof XPTransaction
  direction: 'asc' | 'desc'
}

// ===== COMPONENT =====

interface XPTransactionHistoryProps {
  className?: string
}

export const XPTransactionHistory: React.FC<XPTransactionHistoryProps> = ({
  className = ''
}) => {
  // ===== STATE =====
  const [transactions, setTransactions] = useState<XPTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<TransactionFilters>({
    search: '',
    dateRange: { start: null, end: null },
    source: [],
    amountRange: { min: null, max: null },
    status: [],
    adminOnly: false
  })
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: 'timestamp',
    direction: 'desc'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(25)
  const [showFilters, setShowFilters] = useState(false)

  const debouncedSearch = useDebounce(filters.search, 300)

  // ===== EFFECTS =====
  useEffect(() => {
    loadTransactions()
  }, [debouncedSearch, filters, sortConfig, currentPage])

  // ===== HANDLERS =====
  const loadTransactions = async () => {
    try {
      setLoading(true)
      // TODO: Replace with real API call
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockTransactions: XPTransaction[] = [
        {
          id: 'txn_001',
          userId: 'user_123',
          userName: 'KeycapMaster',
          userLevel: 16,
          userTier: 'intermediate',
          amount: 150,
          source: 'purchase',
          sourceDetails: 'Order #ORD-2024-001 ($75.00)',
          balanceBefore: 225,
          balanceAfter: 375,
          levelBefore: 15,
          levelAfter: 16,
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          status: 'completed'
        },
        {
          id: 'txn_002',
          userId: 'user_456',
          userName: 'DesignGuru',
          userLevel: 12,
          userTier: 'intermediate',
          amount: 25,
          source: 'activity',
          sourceDetails: 'Daily login streak (7 days)',
          balanceBefore: 180,
          balanceAfter: 205,
          levelBefore: 12,
          levelAfter: 12,
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
          status: 'completed'
        },
        {
          id: 'txn_003',
          userId: 'user_789',
          userName: 'CommunityLead',
          userLevel: 22,
          userTier: 'advanced',
          amount: 500,
          source: 'manual',
          sourceDetails: 'Event participation bonus',
          balanceBefore: 890,
          balanceAfter: 1390,
          levelBefore: 22,
          levelAfter: 22,
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          adminId: 'admin_001',
          adminName: 'Admin User',
          reason: 'Community event participation reward',
          status: 'completed'
        }
      ]
      
      setTransactions(mockTransactions)
    } catch (error) {
      console.error('Error loading transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSort = (key: keyof XPTransaction) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  const handleFilterChange = (key: keyof TransactionFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1) // Reset to first page when filtering
  }

  const exportTransactions = () => {
    // TODO: Implement export functionality
    console.log('Exporting transactions...')
  }

  // ===== COMPUTED VALUES =====
  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      // Search filter
      if (debouncedSearch) {
        const searchLower = debouncedSearch.toLowerCase()
        const matchesSearch = 
          transaction.userName.toLowerCase().includes(searchLower) ||
          transaction.sourceDetails.toLowerCase().includes(searchLower) ||
          transaction.id.toLowerCase().includes(searchLower)
        if (!matchesSearch) return false
      }

      // Source filter
      if (filters.source.length > 0 && !filters.source.includes(transaction.source)) {
        return false
      }

      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(transaction.status)) {
        return false
      }

      // Admin only filter
      if (filters.adminOnly && !transaction.adminId) {
        return false
      }

      // Amount range filter
      if (filters.amountRange.min !== null && transaction.amount < filters.amountRange.min) {
        return false
      }
      if (filters.amountRange.max !== null && transaction.amount > filters.amountRange.max) {
        return false
      }

      // Date range filter
      if (filters.dateRange.start && transaction.timestamp < filters.dateRange.start) {
        return false
      }
      if (filters.dateRange.end && transaction.timestamp > filters.dateRange.end) {
        return false
      }

      return true
    })
  }, [transactions, debouncedSearch, filters])

  const sortedTransactions = useMemo(() => {
    return [...filteredTransactions].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]
      
      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1
      return 0
    })
  }, [filteredTransactions, sortConfig])

  const paginatedTransactions = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return sortedTransactions.slice(startIndex, startIndex + pageSize)
  }, [sortedTransactions, currentPage, pageSize])

  const totalPages = Math.ceil(sortedTransactions.length / pageSize)

  // ===== RENDER HELPERS =====
  const getSourceIcon = (source: XPTransaction['source']) => {
    switch (source) {
      case 'purchase': return <ShoppingCart className="w-4 h-4" />
      case 'activity': return <Activity className="w-4 h-4" />
      case 'bonus': return <Gift className="w-4 h-4" />
      case 'event': return <Award className="w-4 h-4" />
      case 'manual': return <Settings className="w-4 h-4" />
      case 'referral': return <User className="w-4 h-4" />
      case 'achievement': return <Award className="w-4 h-4" />
      default: return <Zap className="w-4 h-4" />
    }
  }

  const getSourceColor = (source: XPTransaction['source']) => {
    switch (source) {
      case 'purchase': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'activity': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'bonus': return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      case 'event': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'manual': return 'bg-orange-500/20 text-orange-400 border-orange-500/30'
      case 'referral': return 'bg-pink-500/20 text-pink-400 border-pink-500/30'
      case 'achievement': return 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusIcon = (status: XPTransaction['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'pending': return <Clock className="w-4 h-4 text-yellow-400" />
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-400" />
      case 'reversed': return <TrendingDown className="w-4 h-4 text-orange-400" />
      default: return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  // ===== TABLE COLUMNS =====
  const columns = [
    {
      key: 'timestamp',
      label: 'Date & Time',
      sortable: true,
      render: (transaction: XPTransaction) => (
        <div className="text-sm">
          <div className="text-white font-medium">
            {transaction.timestamp.toLocaleDateString()}
          </div>
          <div className="text-gray-400">
            {transaction.timestamp.toLocaleTimeString()}
          </div>
        </div>
      )
    },
    {
      key: 'userName',
      label: 'User',
      sortable: true,
      render: (transaction: XPTransaction) => (
        <div className="flex items-center gap-2">
          <LevelBadge
            level={transaction.userLevel}
            tier={transaction.userTier as any}
            size="xs"
          />
          <div>
            <div className="text-white font-medium">{transaction.userName}</div>
            <div className="text-xs text-gray-400">Level {transaction.userLevel}</div>
          </div>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'XP Amount',
      sortable: true,
      render: (transaction: XPTransaction) => (
        <div className="flex items-center gap-2">
          {transaction.amount > 0 ? (
            <TrendingUp className="w-4 h-4 text-green-400" />
          ) : (
            <TrendingDown className="w-4 h-4 text-red-400" />
          )}
          <span className={`font-bold ${
            transaction.amount > 0 ? 'text-green-400' : 'text-red-400'
          }`}>
            {transaction.amount > 0 ? '+' : ''}{transaction.amount.toLocaleString()} XP
          </span>
        </div>
      )
    },
    {
      key: 'source',
      label: 'Source',
      sortable: true,
      render: (transaction: XPTransaction) => (
        <div className="space-y-1">
          <Badge className={`${getSourceColor(transaction.source)} border`}>
            <div className="flex items-center gap-1">
              {getSourceIcon(transaction.source)}
              <span className="capitalize">{transaction.source}</span>
            </div>
          </Badge>
          <div className="text-xs text-gray-400 max-w-48 truncate">
            {transaction.sourceDetails}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (transaction: XPTransaction) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(transaction.status)}
          <span className="capitalize text-sm">{transaction.status}</span>
        </div>
      )
    }
  ]

  // ===== MAIN RENDER =====
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Search and Actions */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search transactions, users, or IDs..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10 bg-gray-800 border-gray-700 text-white"
            />
          </div>
        </div>
        
        <div className="flex flex-wrap gap-3">
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters {filteredTransactions.length !== transactions.length && `(${filteredTransactions.length})`}
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={loadTransactions}
            loading={loading}
          >
            Refresh
          </AdminButton>
          <AdminButton
            variant="secondary"
            icon={Download}
            onClick={exportTransactions}
          >
            Export
          </AdminButton>
        </div>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <AdminCard title="Advanced Filters" className="bg-gray-800/50">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Source Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  XP Source
                </label>
                {/* TODO: Implement multi-select for sources */}
                <div className="text-sm text-gray-400">Multi-select coming soon...</div>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Status
                </label>
                {/* TODO: Implement multi-select for status */}
                <div className="text-sm text-gray-400">Multi-select coming soon...</div>
              </div>

              {/* Amount Range */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  XP Amount Range
                </label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    className="bg-gray-800 border-gray-700 text-white text-sm"
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    className="bg-gray-800 border-gray-700 text-white text-sm"
                  />
                </div>
              </div>

              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Date Range
                </label>
                <div className="flex gap-2">
                  <Input
                    type="date"
                    className="bg-gray-800 border-gray-700 text-white text-sm"
                  />
                  <Input
                    type="date"
                    className="bg-gray-800 border-gray-700 text-white text-sm"
                  />
                </div>
              </div>
            </div>
          </AdminCard>
        </motion.div>
      )}

      {/* Transactions Table */}
      <AdminCard>
        <AdminTable
          columns={columns}
          data={paginatedTransactions}
          loading={loading}
          emptyMessage="No XP transactions found matching your criteria"
          sortConfig={sortConfig}
          onSort={handleSort}
        />
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-700">
            <div className="text-sm text-gray-400">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, sortedTransactions.length)} of {sortedTransactions.length} transactions
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </AdminCard>
    </div>
  )
}

export default XPTransactionHistory
