/**
 * Performance Metrics Hook
 *
 * Custom React hook for monitoring system performance metrics.
 * Provides real-time performance data, query metrics, and system health monitoring.
 *
 * Features:
 * - Real-time performance monitoring with auto-refresh
 * - Database query performance tracking
 * - Cache efficiency monitoring
 * - System resource usage tracking
 * - Performance alerts and notifications
 * - Historical performance data
 * - Export functionality for metrics
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useUser } from '../../../lib/useUser';

interface PerformanceData {
  averageResponseTime: number;
  averageQueryTime: number;
  cacheHitRate: number;
  systemLoad: number;
  memoryUsage: number;
  activeConnections: number;
  responseTimeTrend: number;
  queryTimeTrend: number;
  cacheHitTrend: number;
  systemLoadTrend: number;
  memoryTrend: number;
  connectionsTrend: number;
  lastUpdated: Date;
}

interface QueryMetric {
  name: string;
  avgTime: number;
  executions: number;
  slowestTime: number;
  fastestTime: number;
}

interface CacheMetric {
  name: string;
  hitRate: number;
  hits: number;
  misses: number;
  total: number;
  size: number;
}

interface SystemMetric {
  name: string;
  value: number;
  unit: string;
  threshold: number;
  status: 'healthy' | 'warning' | 'critical';
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  description: string;
  timestamp: Date;
  resolved: boolean;
}

interface UsePerformanceMetricsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // seconds
}

interface UsePerformanceMetricsReturn {
  performanceData: PerformanceData | null;
  queryMetrics: QueryMetric[] | null;
  cacheMetrics: CacheMetric[] | null;
  systemMetrics: SystemMetric[] | null;
  alerts: PerformanceAlert[] | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  exportMetrics: (format: 'csv' | 'json') => Promise<void>;
}

export const usePerformanceMetrics = (
  options: UsePerformanceMetricsOptions = {}
): UsePerformanceMetricsReturn => {
  const { user } = useUser();
  const { autoRefresh = false, refreshInterval = 30 } = options;

  // State
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [queryMetrics, setQueryMetrics] = useState<QueryMetric[] | null>(null);
  const [cacheMetrics, setCacheMetrics] = useState<CacheMetric[] | null>(null);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[] | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch performance metrics
  const fetchPerformanceMetrics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // In a real implementation, this would fetch from monitoring APIs
      // For now, we'll generate realistic mock data
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Mock performance data
      const mockPerformanceData: PerformanceData = {
        averageResponseTime: Math.floor(Math.random() * 100) + 50, // 50-150ms
        averageQueryTime: Math.floor(Math.random() * 50) + 10, // 10-60ms
        cacheHitRate: 0.85 + Math.random() * 0.1, // 85-95%
        systemLoad: Math.random() * 0.8, // 0-80%
        memoryUsage: 0.4 + Math.random() * 0.3, // 40-70%
        activeConnections: Math.floor(Math.random() * 50) + 20, // 20-70
        responseTimeTrend: (Math.random() - 0.5) * 20, // -10 to +10
        queryTimeTrend: (Math.random() - 0.5) * 15, // -7.5 to +7.5
        cacheHitTrend: (Math.random() - 0.5) * 10, // -5 to +5
        systemLoadTrend: (Math.random() - 0.5) * 25, // -12.5 to +12.5
        memoryTrend: (Math.random() - 0.5) * 20, // -10 to +10
        connectionsTrend: (Math.random() - 0.5) * 30, // -15 to +15
        lastUpdated: new Date()
      };

      // Mock query metrics
      const mockQueryMetrics: QueryMetric[] = [
        {
          name: 'SELECT discussions WHERE status = ?',
          avgTime: Math.floor(Math.random() * 50) + 20,
          executions: Math.floor(Math.random() * 1000) + 500,
          slowestTime: Math.floor(Math.random() * 200) + 100,
          fastestTime: Math.floor(Math.random() * 20) + 5
        },
        {
          name: 'SELECT submissions ORDER BY created_at',
          avgTime: Math.floor(Math.random() * 80) + 30,
          executions: Math.floor(Math.random() * 800) + 300,
          slowestTime: Math.floor(Math.random() * 300) + 150,
          fastestTime: Math.floor(Math.random() * 25) + 10
        },
        {
          name: 'UPDATE moderation_queue SET status = ?',
          avgTime: Math.floor(Math.random() * 40) + 15,
          executions: Math.floor(Math.random() * 600) + 200,
          slowestTime: Math.floor(Math.random() * 150) + 80,
          fastestTime: Math.floor(Math.random() * 15) + 5
        },
        {
          name: 'SELECT challenges WHERE active = true',
          avgTime: Math.floor(Math.random() * 60) + 25,
          executions: Math.floor(Math.random() * 400) + 100,
          slowestTime: Math.floor(Math.random() * 250) + 120,
          fastestTime: Math.floor(Math.random() * 20) + 8
        }
      ];

      // Mock cache metrics
      const mockCacheMetrics: CacheMetric[] = [
        {
          name: 'User Sessions',
          hitRate: 0.92 + Math.random() * 0.05,
          hits: Math.floor(Math.random() * 5000) + 8000,
          misses: Math.floor(Math.random() * 800) + 200,
          total: 0,
          size: Math.floor(Math.random() * 50) + 100 // MB
        },
        {
          name: 'Discussion Cache',
          hitRate: 0.88 + Math.random() * 0.08,
          hits: Math.floor(Math.random() * 3000) + 5000,
          misses: Math.floor(Math.random() * 600) + 300,
          total: 0,
          size: Math.floor(Math.random() * 30) + 80
        },
        {
          name: 'Analytics Cache',
          hitRate: 0.75 + Math.random() * 0.15,
          hits: Math.floor(Math.random() * 2000) + 3000,
          misses: Math.floor(Math.random() * 1000) + 500,
          total: 0,
          size: Math.floor(Math.random() * 20) + 40
        }
      ];

      // Calculate totals for cache metrics
      mockCacheMetrics.forEach(cache => {
        cache.total = cache.hits + cache.misses;
        cache.hitRate = cache.hits / cache.total;
      });

      // Mock system metrics
      const mockSystemMetrics: SystemMetric[] = [
        {
          name: 'CPU Usage',
          value: Math.random() * 80,
          unit: '%',
          threshold: 80,
          status: 'healthy'
        },
        {
          name: 'Memory Usage',
          value: 40 + Math.random() * 30,
          unit: '%',
          threshold: 85,
          status: 'healthy'
        },
        {
          name: 'Disk Usage',
          value: 20 + Math.random() * 40,
          unit: '%',
          threshold: 90,
          status: 'healthy'
        },
        {
          name: 'Network I/O',
          value: Math.random() * 100,
          unit: 'MB/s',
          threshold: 500,
          status: 'healthy'
        }
      ];

      // Update status based on thresholds
      mockSystemMetrics.forEach(metric => {
        if (metric.value > metric.threshold * 0.9) {
          metric.status = 'critical';
        } else if (metric.value > metric.threshold * 0.7) {
          metric.status = 'warning';
        }
      });

      // Mock alerts
      const mockAlerts: PerformanceAlert[] = [
        {
          id: '1',
          type: 'warning',
          title: 'High Query Response Time',
          description: 'Database queries are taking longer than usual. Consider optimizing slow queries.',
          timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          resolved: false
        },
        {
          id: '2',
          type: 'info',
          title: 'Cache Hit Rate Improved',
          description: 'Cache hit rate has improved by 5% over the last hour.',
          timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
          resolved: true
        }
      ];

      // Set all data
      setPerformanceData(mockPerformanceData);
      setQueryMetrics(mockQueryMetrics);
      setCacheMetrics(mockCacheMetrics);
      setSystemMetrics(mockSystemMetrics);
      setAlerts(mockAlerts);

    } catch (err) {
      console.error('Error fetching performance metrics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch performance metrics');
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-refresh setup
  useEffect(() => {
    fetchPerformanceMetrics();

    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchPerformanceMetrics, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [fetchPerformanceMetrics, autoRefresh, refreshInterval]);

  // Export metrics functionality
  const exportMetrics = useCallback(async (format: 'csv' | 'json') => {
    try {
      if (!performanceData || !queryMetrics || !cacheMetrics || !systemMetrics) {
        throw new Error('No metrics data available to export');
      }

      const exportData = {
        timestamp: new Date().toISOString(),
        performance: performanceData,
        queries: queryMetrics,
        cache: cacheMetrics,
        system: systemMetrics,
        alerts: alerts?.filter(a => !a.resolved) || []
      };

      if (format === 'json') {
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-metrics-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else if (format === 'csv') {
        // Convert to CSV format
        const csvData = [
          ['Metric', 'Value', 'Unit', 'Trend'],
          ['Average Response Time', performanceData.averageResponseTime.toString(), 'ms', performanceData.responseTimeTrend.toString()],
          ['Average Query Time', performanceData.averageQueryTime.toString(), 'ms', performanceData.queryTimeTrend.toString()],
          ['Cache Hit Rate', (performanceData.cacheHitRate * 100).toFixed(2), '%', performanceData.cacheHitTrend.toString()],
          ['System Load', (performanceData.systemLoad * 100).toFixed(2), '%', performanceData.systemLoadTrend.toString()],
          ['Memory Usage', (performanceData.memoryUsage * 100).toFixed(2), '%', performanceData.memoryTrend.toString()],
          ['Active Connections', performanceData.activeConnections.toString(), 'count', performanceData.connectionsTrend.toString()]
        ];

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-metrics-${Date.now()}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }

      console.log(`Performance metrics exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export failed:', error);
      throw error;
    }
  }, [performanceData, queryMetrics, cacheMetrics, systemMetrics, alerts]);

  return {
    performanceData,
    queryMetrics,
    cacheMetrics,
    systemMetrics,
    alerts,
    loading,
    error,
    refetch: fetchPerformanceMetrics,
    exportMetrics
  };
};
