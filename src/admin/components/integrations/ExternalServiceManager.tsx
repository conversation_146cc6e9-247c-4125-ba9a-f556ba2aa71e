/**
 * External Service Manager Component
 *
 * Comprehensive management interface for external service integrations.
 * Handles email notifications, webhooks, API endpoints, and third-party services.
 *
 * Features:
 * - Email notification service configuration
 * - Webhook management and testing
 * - API endpoint configuration and monitoring
 * - Third-party service integrations (Discord, Slack, etc.)
 * - Service health monitoring and status tracking
 * - Integration testing and validation
 * - Configuration backup and restore
 * - Service usage analytics and metrics
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Zap,
  Mail,
  Webhook,
  Globe,
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Play,
  Pause,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Eye,
  Copy,
  Download,
  Upload
} from 'lucide-react';
import { AdminCard, AdminButton } from '../common';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { useExternalServices } from '../../hooks/useExternalServices';

interface ExternalServiceManagerProps {
  className?: string;
}

interface ServiceIntegration {
  id: string;
  name: string;
  type: 'email' | 'webhook' | 'api' | 'discord' | 'slack' | 'analytics';
  status: 'active' | 'inactive' | 'error' | 'testing';
  endpoint?: string;
  lastUsed?: Date;
  usageCount: number;
  errorCount: number;
  config: Record<string, any>;
}

export const ExternalServiceManager: React.FC<ExternalServiceManagerProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [activeTab, setActiveTab] = useState<'services' | 'webhooks' | 'email' | 'analytics'>('services');
  const [selectedService, setSelectedService] = useState<ServiceIntegration | null>(null);
  const [showServiceEditor, setShowServiceEditor] = useState(false);
  const [testingService, setTestingService] = useState<string | null>(null);

  // Data hooks
  const {
    services,
    webhooks,
    emailConfig,
    analytics,
    loading,
    error,
    refetch,
    testService,
    updateService,
    createService,
    deleteService,
    exportConfig,
    importConfig
  } = useExternalServices();

  // Permission checks
  const canManageServices = hasPermission('system_integrations', 'write');
  const canViewServices = hasPermission('system_integrations', 'read');

  // Handle service test
  const handleTestService = useCallback(async (serviceId: string) => {
    if (!canManageServices) return;
    
    try {
      setTestingService(serviceId);
      await testService(serviceId);
    } catch (error) {
      console.error('Service test failed:', error);
    } finally {
      setTestingService(null);
    }
  }, [canManageServices, testService]);

  // Handle service toggle
  const handleToggleService = useCallback(async (serviceId: string, active: boolean) => {
    if (!canManageServices) return;
    
    try {
      await updateService(serviceId, { status: active ? 'active' : 'inactive' });
      await refetch();
    } catch (error) {
      console.error('Failed to toggle service:', error);
    }
  }, [canManageServices, updateService, refetch]);

  // Get service status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/10';
      case 'inactive': return 'text-gray-400 bg-gray-500/10';
      case 'error': return 'text-red-400 bg-red-500/10';
      case 'testing': return 'text-yellow-400 bg-yellow-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  // Get service type icon
  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'email': return Mail;
      case 'webhook': return Webhook;
      case 'api': return Globe;
      case 'discord': return Zap;
      case 'slack': return Zap;
      case 'analytics': return RefreshCw;
      default: return Settings;
    }
  };

  if (!canViewServices) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-400">Access denied</p>
          <p className="text-sm text-gray-500">You don't have permission to view service integrations</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">External Services</h1>
          <p className="text-gray-400">Manage integrations, webhooks, and external service connections</p>
        </div>
        <div className="flex items-center space-x-3">
          {canManageServices && (
            <>
              <AdminButton
                variant="secondary"
                icon={Upload}
                onClick={() => {/* Import config */}}
              >
                Import
              </AdminButton>
              <AdminButton
                variant="secondary"
                icon={Download}
                onClick={() => exportConfig()}
              >
                Export
              </AdminButton>
              <AdminButton
                variant="primary"
                icon={Plus}
                onClick={() => setShowServiceEditor(true)}
              >
                Add Service
              </AdminButton>
            </>
          )}
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={refetch}
            disabled={loading}
            className={loading ? 'animate-spin' : ''}
          >
            Refresh
          </AdminButton>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('services')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'services' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Settings className="w-4 h-4" />
          <span>Services</span>
        </button>
        
        <button
          onClick={() => setActiveTab('webhooks')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'webhooks' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Webhook className="w-4 h-4" />
          <span>Webhooks</span>
        </button>
        
        <button
          onClick={() => setActiveTab('email')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'email' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Mail className="w-4 h-4" />
          <span>Email</span>
        </button>
        
        <button
          onClick={() => setActiveTab('analytics')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'analytics' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <RefreshCw className="w-4 h-4" />
          <span>Analytics</span>
        </button>
      </div>

      {/* Tab Content */}
      <div className="min-h-[500px]">
        {activeTab === 'services' && (
          <div className="space-y-4">
            {/* Service Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <AdminCard className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Active Services</p>
                    <p className="text-2xl font-bold text-green-400">
                      {services?.filter(s => s.status === 'active').length || 0}
                    </p>
                  </div>
                </div>
              </AdminCard>

              <AdminCard className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-red-500/10 rounded-lg flex items-center justify-center">
                    <XCircle className="w-5 h-5 text-red-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Error Services</p>
                    <p className="text-2xl font-bold text-red-400">
                      {services?.filter(s => s.status === 'error').length || 0}
                    </p>
                  </div>
                </div>
              </AdminCard>

              <AdminCard className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                    <Globe className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Total Requests</p>
                    <p className="text-2xl font-bold text-blue-400">
                      {services?.reduce((sum, s) => sum + s.usageCount, 0).toLocaleString() || '0'}
                    </p>
                  </div>
                </div>
              </AdminCard>

              <AdminCard className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-yellow-500/10 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="w-5 h-5 text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Error Rate</p>
                    <p className="text-2xl font-bold text-yellow-400">
                      {services && services.length > 0 
                        ? `${Math.round((services.reduce((sum, s) => sum + s.errorCount, 0) / services.reduce((sum, s) => sum + s.usageCount, 1)) * 100)}%`
                        : '0%'
                      }
                    </p>
                  </div>
                </div>
              </AdminCard>
            </div>

            {/* Services List */}
            <AdminCard className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Service Integrations</h3>
                <div className="text-sm text-gray-400">
                  {services?.length || 0} services configured
                </div>
              </div>
              
              <div className="space-y-3">
                {services?.map((service) => {
                  const ServiceIcon = getServiceTypeIcon(service.type);
                  
                  return (
                    <div key={service.id} className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
                          <ServiceIcon className="w-5 h-5 text-gray-400" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-white">{service.name}</h4>
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(service.status)}`}>
                              {service.status}
                            </span>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-400">
                            <span className="capitalize">{service.type}</span>
                            {service.endpoint && (
                              <>
                                <span>•</span>
                                <span className="font-mono text-xs">{service.endpoint}</span>
                              </>
                            )}
                            <span>•</span>
                            <span>{service.usageCount} requests</span>
                            {service.errorCount > 0 && (
                              <>
                                <span>•</span>
                                <span className="text-red-400">{service.errorCount} errors</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {canManageServices && (
                          <>
                            <AdminButton
                              variant="secondary"
                              size="sm"
                              icon={testingService === service.id ? RefreshCw : Play}
                              onClick={() => handleTestService(service.id)}
                              disabled={testingService === service.id}
                              className={testingService === service.id ? 'animate-spin' : ''}
                            >
                              Test
                            </AdminButton>
                            
                            <button
                              onClick={() => handleToggleService(service.id, service.status !== 'active')}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                                service.status === 'active' ? 'bg-green-600' : 'bg-gray-600'
                              }`}
                            >
                              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                service.status === 'active' ? 'translate-x-6' : 'translate-x-1'
                              }`} />
                            </button>
                            
                            <AdminButton
                              variant="secondary"
                              size="sm"
                              icon={Edit}
                              onClick={() => {
                                setSelectedService(service);
                                setShowServiceEditor(true);
                              }}
                            />
                            
                            <AdminButton
                              variant="secondary"
                              size="sm"
                              icon={Trash2}
                              onClick={() => deleteService(service.id)}
                            />
                          </>
                        )}
                        
                        <AdminButton
                          variant="secondary"
                          size="sm"
                          icon={Eye}
                          onClick={() => setSelectedService(service)}
                        />
                      </div>
                    </div>
                  );
                }) || (
                  <div className="text-center py-8 text-gray-400">
                    <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No services configured</p>
                    <p className="text-sm">Add your first service integration to get started</p>
                  </div>
                )}
              </div>
            </AdminCard>
          </div>
        )}

        {activeTab === 'webhooks' && (
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Webhook Management</h3>
            <div className="text-center py-8 text-gray-400">
              <Webhook className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Webhook management interface</p>
              <p className="text-sm">Configure and monitor webhook endpoints</p>
            </div>
          </AdminCard>
        )}

        {activeTab === 'email' && (
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Email Configuration</h3>
            <div className="text-center py-8 text-gray-400">
              <Mail className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Email service configuration</p>
              <p className="text-sm">Configure SMTP settings and email templates</p>
            </div>
          </AdminCard>
        )}

        {activeTab === 'analytics' && (
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Service Analytics</h3>
            <div className="text-center py-8 text-gray-400">
              <RefreshCw className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Service usage analytics</p>
              <p className="text-sm">Monitor service performance and usage patterns</p>
            </div>
          </AdminCard>
        )}
      </div>

      {/* Service Details Modal */}
      {selectedService && !showServiceEditor && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="w-full max-w-2xl"
          >
            <AdminCard className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Service Details</h3>
                <AdminButton
                  variant="secondary"
                  size="sm"
                  icon={XCircle}
                  onClick={() => setSelectedService(null)}
                />
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Name</label>
                    <p className="text-white">{selectedService.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Type</label>
                    <p className="text-white capitalize">{selectedService.type}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Status</label>
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedService.status)}`}>
                      {selectedService.status}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Usage Count</label>
                    <p className="text-white">{selectedService.usageCount.toLocaleString()}</p>
                  </div>
                </div>
                
                {selectedService.endpoint && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Endpoint</label>
                    <div className="flex items-center space-x-2">
                      <code className="flex-1 px-3 py-2 bg-gray-800 rounded text-sm text-gray-300 font-mono">
                        {selectedService.endpoint}
                      </code>
                      <AdminButton
                        variant="secondary"
                        size="sm"
                        icon={Copy}
                        onClick={() => navigator.clipboard.writeText(selectedService.endpoint!)}
                      />
                    </div>
                  </div>
                )}
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Configuration</label>
                  <pre className="bg-gray-800 rounded p-3 text-sm text-gray-300 overflow-x-auto">
                    {JSON.stringify(selectedService.config, null, 2)}
                  </pre>
                </div>
              </div>
            </AdminCard>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default ExternalServiceManager;
