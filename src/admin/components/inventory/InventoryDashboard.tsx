/**
 * Inventory Management Dashboard
 * 
 * Comprehensive inventory tracking, alerts, and automation
 * Part of Phase 1 API Layer Expansion
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 1 Enhancement
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Eye,
  Settings,
  Bell,
  BarChart3,
  DollarSign,
  Truck,
  RefreshCw,
  Filter,
  Download,
  Plus,
  Minus,
  ShoppingCart,
  Warehouse,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'

interface InventoryItem {
  id: string
  name: string
  sku: string
  category: string
  currentStock: number
  minThreshold: number
  maxThreshold: number
  reorderPoint: number
  reorderQuantity: number
  costPrice: number
  salePrice: number
  supplier: string
  lastRestocked: Date
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'discontinued'
  reservedQuantity: number
  availableQuantity: number
  locationCode: string
  tags: string[]
}

interface StockAlert {
  id: string
  itemId: string
  itemName: string
  type: 'low_stock' | 'out_of_stock' | 'overstock' | 'expiring'
  severity: 'low' | 'medium' | 'high' | 'critical'
  currentStock: number
  threshold: number
  message: string
  createdAt: Date
  resolved: boolean
}

interface InventoryMetrics {
  totalItems: number
  totalValue: number
  lowStockItems: number
  outOfStockItems: number
  overstockItems: number
  averageTurnover: number
  totalReservations: number
  pendingReorders: number
}

/**
 * Inventory Management Dashboard Component
 */
const InventoryDashboard: React.FC = () => {
  // ===== STATE =====
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])
  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([])
  const [metrics, setMetrics] = useState<InventoryMetrics | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'items' | 'alerts' | 'analytics'>('overview')

  // ===== EFFECTS =====
  useEffect(() => {
    loadInventoryData()
  }, [])

  // ===== HANDLERS =====
  const loadInventoryData = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual API calls
      const mockItems = generateMockInventoryItems()
      const mockAlerts = generateMockStockAlerts(mockItems)
      const mockMetrics = calculateMetrics(mockItems)

      setInventoryItems(mockItems)
      setStockAlerts(mockAlerts)
      setMetrics(mockMetrics)
    } catch (error) {
      console.error('Failed to load inventory data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStockUpdate = async (itemId: string, newStock: number) => {
    // TODO: Implement stock update API call
    setInventoryItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, currentStock: newStock, availableQuantity: newStock - item.reservedQuantity }
        : item
    ))
  }

  const handleReorderTrigger = async (itemId: string) => {
    // TODO: Implement automatic reorder functionality
    console.log('Triggering reorder for item:', itemId)
  }

  const handleAlertResolve = async (alertId: string) => {
    setStockAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolved: true } : alert
    ))
  }

  // ===== HELPER FUNCTIONS =====
  const generateMockInventoryItems = (): InventoryItem[] => {
    const categories = ['Electronics', 'Accessories', 'Components', 'Tools', 'Materials']
    const suppliers = ['Supplier A', 'Supplier B', 'Supplier C', 'Supplier D']
    const statuses: InventoryItem['status'][] = ['in_stock', 'low_stock', 'out_of_stock', 'discontinued']

    return Array.from({ length: 50 }, (_, i) => {
      const currentStock = Math.floor(Math.random() * 200)
      const minThreshold = Math.floor(Math.random() * 20) + 5
      const reservedQuantity = Math.floor(Math.random() * (currentStock / 2))
      
      let status: InventoryItem['status'] = 'in_stock'
      if (currentStock === 0) status = 'out_of_stock'
      else if (currentStock <= minThreshold) status = 'low_stock'

      return {
        id: `item_${i + 1}`,
        name: `Product ${i + 1}`,
        sku: `SKU${String(i + 1).padStart(4, '0')}`,
        category: categories[Math.floor(Math.random() * categories.length)],
        currentStock,
        minThreshold,
        maxThreshold: minThreshold * 10,
        reorderPoint: minThreshold + 5,
        reorderQuantity: 50,
        costPrice: Math.round((Math.random() * 50 + 10) * 100) / 100,
        salePrice: Math.round((Math.random() * 100 + 20) * 100) / 100,
        supplier: suppliers[Math.floor(Math.random() * suppliers.length)],
        lastRestocked: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        status,
        reservedQuantity,
        availableQuantity: currentStock - reservedQuantity,
        locationCode: `A${Math.floor(Math.random() * 10)}-${Math.floor(Math.random() * 100)}`,
        tags: ['tag1', 'tag2'].slice(0, Math.floor(Math.random() * 3))
      }
    })
  }

  const generateMockStockAlerts = (items: InventoryItem[]): StockAlert[] => {
    const alerts: StockAlert[] = []
    
    items.forEach(item => {
      if (item.status === 'out_of_stock') {
        alerts.push({
          id: `alert_${item.id}_out`,
          itemId: item.id,
          itemName: item.name,
          type: 'out_of_stock',
          severity: 'critical',
          currentStock: item.currentStock,
          threshold: item.minThreshold,
          message: `${item.name} is out of stock`,
          createdAt: new Date(),
          resolved: false
        })
      } else if (item.status === 'low_stock') {
        alerts.push({
          id: `alert_${item.id}_low`,
          itemId: item.id,
          itemName: item.name,
          type: 'low_stock',
          severity: 'high',
          currentStock: item.currentStock,
          threshold: item.minThreshold,
          message: `${item.name} is running low (${item.currentStock} remaining)`,
          createdAt: new Date(),
          resolved: false
        })
      }
    })

    return alerts
  }

  const calculateMetrics = (items: InventoryItem[]): InventoryMetrics => {
    return {
      totalItems: items.length,
      totalValue: items.reduce((sum, item) => sum + (item.currentStock * item.costPrice), 0),
      lowStockItems: items.filter(item => item.status === 'low_stock').length,
      outOfStockItems: items.filter(item => item.status === 'out_of_stock').length,
      overstockItems: items.filter(item => item.currentStock > item.maxThreshold).length,
      averageTurnover: 4.2, // Mock value
      totalReservations: items.reduce((sum, item) => sum + item.reservedQuantity, 0),
      pendingReorders: items.filter(item => item.currentStock <= item.reorderPoint).length
    }
  }

  const getStatusColor = (status: InventoryItem['status']) => {
    switch (status) {
      case 'in_stock': return 'text-green-400 bg-green-500/10'
      case 'low_stock': return 'text-yellow-400 bg-yellow-500/10'
      case 'out_of_stock': return 'text-red-400 bg-red-500/10'
      case 'discontinued': return 'text-gray-400 bg-gray-500/10'
      default: return 'text-gray-400 bg-gray-500/10'
    }
  }

  const getAlertSeverityColor = (severity: StockAlert['severity']) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-500/10 border-red-500/20'
      case 'high': return 'text-orange-400 bg-orange-500/10 border-orange-500/20'
      case 'medium': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20'
      case 'low': return 'text-blue-400 bg-blue-500/10 border-blue-500/20'
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/20'
    }
  }

  const filteredItems = inventoryItems.filter(item => {
    if (selectedCategory !== 'all' && item.category !== selectedCategory) return false
    if (selectedStatus !== 'all' && item.status !== selectedStatus) return false
    if (searchQuery && !item.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !item.sku.toLowerCase().includes(searchQuery.toLowerCase())) return false
    return true
  })

  const unresolvedAlerts = stockAlerts.filter(alert => !alert.resolved)

  // ===== RENDER =====
  return (
    <div className="inventory-dashboard space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex items-center space-x-3">
          <Warehouse className="w-8 h-8 text-blue-400" />
          <div>
            <h2 className="text-2xl font-bold text-white">Inventory Management</h2>
            <p className="text-gray-400">Stock tracking, alerts, and automated reordering</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={loadInventoryData}
            loading={loading}
          >
            Refresh
          </AdminButton>
          
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-accent-600' : ''}
          >
            Filters
          </AdminButton>

          <AdminButton
            variant="secondary"
            icon={Download}
          >
            Export
          </AdminButton>
        </div>
      </div>

      {/* Metrics Overview */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Items</p>
                <p className="text-2xl font-bold text-white">{metrics.totalItems.toLocaleString()}</p>
                <p className="text-green-400 text-sm">+12 this week</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <Package className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Value</p>
                <p className="text-2xl font-bold text-white">${metrics.totalValue.toLocaleString()}</p>
                <p className="text-green-400 text-sm">+5.2% vs last month</p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Low Stock</p>
                <p className="text-2xl font-bold text-white">{metrics.lowStockItems}</p>
                <p className="text-yellow-400 text-sm">Needs attention</p>
              </div>
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Pending Reorders</p>
                <p className="text-2xl font-bold text-white">{metrics.pendingReorders}</p>
                <p className="text-orange-400 text-sm">Automated triggers</p>
              </div>
              <div className="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
                <Truck className="w-6 h-6 text-orange-400" />
              </div>
            </div>
          </AdminCard>
        </div>
      )}

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <AdminCard title="Inventory Filters" className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Search</label>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by name or SKU..."
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  >
                    <option value="all">All Categories</option>
                    <option value="Electronics">Electronics</option>
                    <option value="Accessories">Accessories</option>
                    <option value="Components">Components</option>
                    <option value="Tools">Tools</option>
                    <option value="Materials">Materials</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
                  >
                    <option value="all">All Statuses</option>
                    <option value="in_stock">In Stock</option>
                    <option value="low_stock">Low Stock</option>
                    <option value="out_of_stock">Out of Stock</option>
                    <option value="discontinued">Discontinued</option>
                  </select>
                </div>
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-1 bg-gray-800 p-1 rounded-lg">
        {[
          { id: 'overview', label: 'Overview', icon: BarChart3 },
          { id: 'items', label: 'Items', icon: Package },
          { id: 'alerts', label: 'Alerts', icon: Bell },
          { id: 'analytics', label: 'Analytics', icon: TrendingUp }
        ].map(tab => {
          const IconComponent = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as typeof activeTab)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <IconComponent className="w-4 h-4" />
              <span className="text-sm">{tab.label}</span>
              {tab.id === 'alerts' && unresolvedAlerts.length > 0 && (
                <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                  {unresolvedAlerts.length}
                </span>
              )}
            </button>
          )
        })}
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'overview' && (
          <motion.div
            key="overview"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
          >
            {/* Recent Alerts */}
            <AdminCard title="Recent Stock Alerts" subtitle="Urgent inventory notifications">
              <div className="space-y-3">
                {unresolvedAlerts.slice(0, 5).map((alert) => (
                  <div key={alert.id} className={`p-3 rounded-lg border ${getAlertSeverityColor(alert.severity)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{alert.itemName}</p>
                        <p className="text-sm opacity-80">{alert.message}</p>
                      </div>
                      <button
                        onClick={() => handleAlertResolve(alert.id)}
                        className="text-green-400 hover:text-green-300"
                      >
                        <CheckCircle className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                ))}
                {unresolvedAlerts.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    <CheckCircle className="w-12 h-12 mx-auto mb-2 text-green-400" />
                    <p>No active alerts</p>
                  </div>
                )}
              </div>
            </AdminCard>

            {/* Low Stock Items */}
            <AdminCard title="Low Stock Items" subtitle="Items requiring reorder">
              <div className="space-y-3">
                {filteredItems.filter(item => item.status === 'low_stock').slice(0, 5).map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium">{item.name}</p>
                      <p className="text-gray-400 text-sm">{item.sku} • Stock: {item.currentStock}</p>
                    </div>
                    <AdminButton
                      size="sm"
                      onClick={() => handleReorderTrigger(item.id)}
                    >
                      Reorder
                    </AdminButton>
                  </div>
                ))}
              </div>
            </AdminCard>
          </motion.div>
        )}

        {activeTab === 'items' && (
          <motion.div
            key="items"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <AdminCard title="Inventory Items" subtitle={`${filteredItems.length} items found`}>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-700/50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Item</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">SKU</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Category</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Stock</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Status</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {filteredItems.slice(0, 20).map((item) => (
                      <tr key={item.id} className="hover:bg-gray-700/30">
                        <td className="px-4 py-3">
                          <div>
                            <p className="text-white font-medium">{item.name}</p>
                            <p className="text-gray-400 text-sm">{item.supplier}</p>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-gray-300 font-mono">{item.sku}</td>
                        <td className="px-4 py-3 text-gray-300">{item.category}</td>
                        <td className="px-4 py-3">
                          <div>
                            <p className="text-white">{item.currentStock}</p>
                            <p className="text-gray-400 text-xs">Available: {item.availableQuantity}</p>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(item.status)}`}>
                            {item.status.replace('_', ' ')}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-2">
                            <button className="text-blue-400 hover:text-blue-300">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button className="text-green-400 hover:text-green-300">
                              <Plus className="w-4 h-4" />
                            </button>
                            <button className="text-red-400 hover:text-red-300">
                              <Minus className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </AdminCard>
          </motion.div>
        )}

        {activeTab === 'alerts' && (
          <motion.div
            key="alerts"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <AdminCard title="Stock Alerts" subtitle="Inventory notifications and warnings">
              <div className="space-y-4">
                {stockAlerts.map((alert) => (
                  <div key={alert.id} className={`p-4 rounded-lg border ${getAlertSeverityColor(alert.severity)} ${alert.resolved ? 'opacity-50' : ''}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="capitalize font-medium">{alert.type.replace('_', ' ')}</span>
                          <span className={`px-2 py-1 rounded text-xs ${getAlertSeverityColor(alert.severity)}`}>
                            {alert.severity}
                          </span>
                          {alert.resolved && (
                            <span className="px-2 py-1 bg-green-500/10 text-green-400 rounded text-xs">
                              Resolved
                            </span>
                          )}
                        </div>
                        <p className="text-white font-medium">{alert.message}</p>
                        <p className="text-gray-400 text-sm">
                          Current: {alert.currentStock} | Threshold: {alert.threshold}
                        </p>
                        <p className="text-gray-500 text-xs">
                          {alert.createdAt.toLocaleString()}
                        </p>
                      </div>
                      {!alert.resolved && (
                        <div className="flex space-x-2">
                          <AdminButton
                            size="sm"
                            variant="secondary"
                            onClick={() => handleAlertResolve(alert.id)}
                          >
                            Resolve
                          </AdminButton>
                          <AdminButton
                            size="sm"
                            onClick={() => handleReorderTrigger(alert.itemId)}
                          >
                            Reorder
                          </AdminButton>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>
          </motion.div>
        )}

        {activeTab === 'analytics' && (
          <motion.div
            key="analytics"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <AdminCard title="Inventory Analytics" subtitle="Coming Soon">
              <div className="text-center py-8">
                <BarChart3 className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                <p className="text-gray-400">Inventory analytics dashboard coming soon</p>
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default InventoryDashboard