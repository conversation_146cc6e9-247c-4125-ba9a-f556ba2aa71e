/**
 * Customer Segmentation Dashboard
 * 
 * Advanced customer segmentation and targeting interface
 * Part of Phase 2 Marketing Automation Suite
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Users,
  Target,
  TrendingUp,
  Zap,
  Filter,
  Search,
  Plus,
  Eye,
  Edit,
  Copy,
  Download,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings,
  Layers,
  Brain
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'
import CustomerSegmentationService, { 
  CustomerSegment, 
  SegmentType, 
  SegmentStatus,
  SegmentAnalytics,
  SegmentationInsight 
} from '../../lib/marketing/CustomerSegmentationService'

interface CustomerSegmentationDashboardProps {
  className?: string
}

/**
 * Customer Segmentation Dashboard Component
 */
const CustomerSegmentationDashboard: React.FC<CustomerSegmentationDashboardProps> = ({ 
  className = '' 
}) => {
  // ===== STATE =====
  const [segments, setSegments] = useState<CustomerSegment[]>([])
  const [analytics, setAnalytics] = useState<SegmentAnalytics | null>(null)
  const [insights, setInsights] = useState<SegmentationInsight[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedSegment, setSelectedSegment] = useState<CustomerSegment | null>(null)
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'inactive' | 'archived'>('all')
  const [showFilters, setShowFilters] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)

  const segmentationService = CustomerSegmentationService.getInstance()

  // ===== EFFECTS =====
  useEffect(() => {
    loadSegments()
    loadAnalytics()
    loadInsights()
  }, [activeTab])

  // ===== HANDLERS =====
  const loadSegments = async () => {
    setLoading(true)
    try {
      const filters: any = {
        limit: 50,
        sortBy: 'updated',
        sortOrder: 'desc'
      }

      if (activeTab !== 'all') {
        filters.status = [activeTab as SegmentStatus]
      }

      if (searchQuery) {
        filters.searchQuery = searchQuery
      }

      const result = await segmentationService.getSegments(filters)
      setSegments(result.segments)
    } catch (error) {
      console.error('Failed to load segments:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadAnalytics = async () => {
    try {
      const segmentAnalytics = await segmentationService.getSegmentationAnalytics()
      setAnalytics(segmentAnalytics)
    } catch (error) {
      console.error('Failed to load analytics:', error)
    }
  }

  const loadInsights = async () => {
    try {
      const segmentInsights = await segmentationService.getSegmentationInsights()
      setInsights(segmentInsights)
    } catch (error) {
      console.error('Failed to load insights:', error)
    }
  }

  const handleRefreshSegment = async (segmentId: string) => {
    try {
      await segmentationService.refreshSegment(segmentId)
      await loadSegments()
    } catch (error) {
      console.error('Failed to refresh segment:', error)
    }
  }

  const handleExportSegment = async (segment: CustomerSegment) => {
    try {
      const data = await segmentationService.exportSegment(segment.id, 'csv', {
        includeProfile: true
      })
      
      // Create and trigger download
      const blob = new Blob([data], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${segment.name.replace(/\s+/g, '_')}_segment.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export segment:', error)
    }
  }

  const getStatusColor = (status: SegmentStatus) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/10'
      case 'inactive': return 'text-yellow-400 bg-yellow-500/10'
      case 'archived': return 'text-gray-400 bg-gray-500/10'
      default: return 'text-gray-400 bg-gray-500/10'
    }
  }

  const getTypeColor = (type: SegmentType) => {
    switch (type) {
      case 'static': return 'text-blue-400'
      case 'dynamic': return 'text-purple-400'
      case 'behavioral': return 'text-green-400'
      case 'demographic': return 'text-yellow-400'
      case 'geographic': return 'text-orange-400'
      case 'psychographic': return 'text-pink-400'
      default: return 'text-gray-400'
    }
  }

  const getInsightIcon = (type: SegmentationInsight['type']) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="w-4 h-4 text-green-400" />
      case 'risk': return <AlertTriangle className="w-4 h-4 text-red-400" />
      case 'trend': return <Activity className="w-4 h-4 text-blue-400" />
      case 'anomaly': return <Zap className="w-4 h-4 text-yellow-400" />
      default: return <Brain className="w-4 h-4 text-purple-400" />
    }
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const formatPercentage = (num: number): string => {
    return `${(num * 100).toFixed(1)}%`
  }

  const getFilteredSegments = () => {
    let filtered = segments

    if (searchQuery) {
      filtered = filtered.filter(segment => 
        segment.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        segment.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }

  // ===== RENDER =====
  return (
    <div className={`customer-segmentation-dashboard ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div className="flex items-center space-x-3">
          <Target className="w-8 h-8 text-purple-400" />
          <div>
            <h2 className="text-2xl font-bold text-white">Customer Segmentation</h2>
            <p className="text-gray-400">Advanced targeting and customer insights</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            icon={Plus}
            onClick={() => setShowCreateModal(true)}
          >
            Create Segment
          </AdminButton>
          
          <AdminButton
            variant="secondary"
            icon={Brain}
          >
            AI Insights
          </AdminButton>
          
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-accent-600' : ''}
          >
            Filters
          </AdminButton>
        </div>
      </div>

      {/* Analytics Overview */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Segments</p>
                <p className="text-2xl font-bold text-white">{analytics.overview.totalSegments}</p>
                <p className="text-green-400 text-sm">+{analytics.overview.activeSegments} active</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <Layers className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Customers</p>
                <p className="text-2xl font-bold text-white">{formatNumber(analytics.overview.totalCustomers)}</p>
                <p className="text-blue-400 text-sm">Avg: {formatNumber(analytics.overview.averageSegmentSize)}/segment</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Top Performer</p>
                <p className="text-xl font-bold text-white truncate">{analytics.overview.topPerformingSegment}</p>
                <p className="text-green-400 text-sm">Highest conversion</p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">AI Insights</p>
                <p className="text-2xl font-bold text-white">{insights.length}</p>
                <p className="text-yellow-400 text-sm">Actionable recommendations</p>
              </div>
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <Brain className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </AdminCard>
        </div>
      )}

      {/* AI Insights Panel */}
      {insights.length > 0 && (
        <AdminCard title="🤖 AI-Powered Insights" subtitle="Automated segmentation recommendations" className="mb-6">
          <div className="space-y-4">
            {insights.slice(0, 3).map((insight) => (
              <div key={insight.id} className="p-4 bg-gray-700/30 rounded-lg border border-gray-600">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getInsightIcon(insight.type)}
                    <div className="flex-1">
                      <h4 className="text-white font-medium">{insight.title}</h4>
                      <p className="text-gray-400 text-sm mt-1">{insight.description}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className={`text-xs px-2 py-1 rounded ${
                          insight.impact === 'high' ? 'bg-red-500/20 text-red-400' :
                          insight.impact === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-green-500/20 text-green-400'
                        }`}>
                          {insight.impact} impact
                        </span>
                        <span className="text-xs text-gray-500">
                          {Math.round(insight.confidence * 100)}% confidence
                        </span>
                      </div>
                    </div>
                  </div>
                  <AdminButton size="sm" variant="secondary">
                    View Details
                  </AdminButton>
                </div>
              </div>
            ))}
          </div>
        </AdminCard>
      )}

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6"
          >
            <AdminCard title="Segmentation Filters" className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Search</label>
                  <div className="relative">
                    <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search segments..."
                      className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Segment Type</label>
                  <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500">
                    <option value="">All Types</option>
                    <option value="dynamic">Dynamic</option>
                    <option value="static">Static</option>
                    <option value="behavioral">Behavioral</option>
                    <option value="demographic">Demographic</option>
                    <option value="geographic">Geographic</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Size Range</label>
                  <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500">
                    <option value="">All Sizes</option>
                    <option value="small">Small (&lt; 1K)</option>
                    <option value="medium">Medium (1K - 10K)</option>
                    <option value="large">Large (10K - 100K)</option>
                    <option value="xlarge">X-Large (&gt; 100K)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Performance</label>
                  <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500">
                    <option value="">All Performance</option>
                    <option value="high">High Performers</option>
                    <option value="medium">Average Performers</option>
                    <option value="low">Low Performers</option>
                  </select>
                </div>
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg mb-6">
        {[
          { id: 'all', label: 'All Segments', count: segments.length },
          { id: 'active', label: 'Active', count: segments.filter(s => s.status === 'active').length },
          { id: 'inactive', label: 'Inactive', count: segments.filter(s => s.status === 'inactive').length },
          { id: 'archived', label: 'Archived', count: segments.filter(s => s.status === 'archived').length }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as typeof activeTab)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <span className="text-sm">{tab.label}</span>
            <span className="text-xs bg-gray-600 px-1.5 py-0.5 rounded">{tab.count}</span>
          </button>
        ))}
      </div>

      {/* Segments Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          Array.from({ length: 6 }).map((_, index) => (
            <AdminCard key={index} className="p-6 animate-pulse">
              <div className="space-y-4">
                <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                <div className="h-8 bg-gray-700 rounded"></div>
                <div className="flex space-x-2">
                  <div className="h-6 bg-gray-700 rounded w-16"></div>
                  <div className="h-6 bg-gray-700 rounded w-16"></div>
                </div>
              </div>
            </AdminCard>
          ))
        ) : getFilteredSegments().length === 0 ? (
          <div className="col-span-full">
            <AdminCard className="p-12 text-center">
              <Target className="w-16 h-16 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No segments found</h3>
              <p className="text-gray-400 mb-4">Create your first customer segment to get started</p>
              <AdminButton onClick={() => setShowCreateModal(true)}>
                Create Segment
              </AdminButton>
            </AdminCard>
          </div>
        ) : (
          getFilteredSegments().map((segment) => (
            <AdminCard key={segment.id} className="p-6 hover:border-purple-500/30 transition-colors">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: segment.color }}
                    />
                    <h3 className="text-white font-medium truncate">{segment.name}</h3>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className={`px-2 py-1 rounded text-xs ${getStatusColor(segment.status)}`}>
                      {segment.status}
                    </span>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-400 text-sm line-clamp-2">{segment.description}</p>

                {/* Metrics */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-gray-700/30 p-3 rounded">
                    <p className="text-gray-400 text-xs">Size</p>
                    <p className="text-white font-semibold">{formatNumber(segment.analytics.totalCustomers)}</p>
                  </div>
                  <div className="bg-gray-700/30 p-3 rounded">
                    <p className="text-gray-400 text-xs">Conversion</p>
                    <p className="text-white font-semibold">{formatPercentage(segment.analytics.conversionRate)}</p>
                  </div>
                  <div className="bg-gray-700/30 p-3 rounded">
                    <p className="text-gray-400 text-xs">AOV</p>
                    <p className="text-white font-semibold">${segment.analytics.averageOrderValue.toFixed(0)}</p>
                  </div>
                  <div className="bg-gray-700/30 p-3 rounded">
                    <p className="text-gray-400 text-xs">LTV</p>
                    <p className="text-white font-semibold">${formatNumber(segment.analytics.lifetimeValue)}</p>
                  </div>
                </div>

                {/* Type and Tags */}
                <div className="flex items-center justify-between">
                  <span className={`text-xs px-2 py-1 bg-gray-700 rounded capitalize ${getTypeColor(segment.type)}`}>
                    {segment.type}
                  </span>
                  {segment.metadata.tags.length > 0 && (
                    <div className="flex space-x-1">
                      {segment.metadata.tags.slice(0, 2).map((tag, index) => (
                        <span key={index} className="text-xs px-2 py-1 bg-gray-600 text-gray-300 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-700">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedSegment(segment)}
                      className="text-purple-400 hover:text-purple-300"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="text-blue-400 hover:text-blue-300">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleExportSegment(segment)}
                      className="text-green-400 hover:text-green-300"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleRefreshSegment(segment.id)}
                      className="text-gray-400 hover:text-white"
                    >
                      <RefreshCw className="w-4 h-4" />
                    </button>
                    <button className="text-gray-400 hover:text-white">
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </AdminCard>
          ))
        )}
      </div>

      {/* Segment Detail Modal */}
      <AnimatePresence>
        {selectedSegment && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            onClick={() => setSelectedSegment(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-800 rounded-lg max-w-4xl w-full mx-4 max-h-[80vh] overflow-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: selectedSegment.color }}
                    />
                    <div>
                      <h3 className="text-xl font-bold text-white">{selectedSegment.name}</h3>
                      <p className="text-gray-400">{selectedSegment.description}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedSegment(null)}
                    className="text-gray-400 hover:text-white"
                  >
                    <CheckCircle className="w-6 h-6" />
                  </button>
                </div>

                {/* Segment Analytics */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gray-700/30 p-4 rounded-lg">
                    <p className="text-gray-400 text-sm">Total Customers</p>
                    <p className="text-2xl font-bold text-white">{formatNumber(selectedSegment.analytics.totalCustomers)}</p>
                    <p className="text-blue-400 text-sm">{selectedSegment.analytics.activeCustomers} active</p>
                  </div>
                  <div className="bg-gray-700/30 p-4 rounded-lg">
                    <p className="text-gray-400 text-sm">Total Revenue</p>
                    <p className="text-2xl font-bold text-white">${formatNumber(selectedSegment.analytics.totalRevenue)}</p>
                    <p className="text-green-400 text-sm">AOV: ${selectedSegment.analytics.averageOrderValue.toFixed(0)}</p>
                  </div>
                  <div className="bg-gray-700/30 p-4 rounded-lg">
                    <p className="text-gray-400 text-sm">Conversion Rate</p>
                    <p className="text-2xl font-bold text-white">{formatPercentage(selectedSegment.analytics.conversionRate)}</p>
                    <p className="text-yellow-400 text-sm">Engagement: {selectedSegment.analytics.engagementScore.toFixed(1)}</p>
                  </div>
                  <div className="bg-gray-700/30 p-4 rounded-lg">
                    <p className="text-gray-400 text-sm">Lifetime Value</p>
                    <p className="text-2xl font-bold text-white">${formatNumber(selectedSegment.analytics.lifetimeValue)}</p>
                    <p className="text-red-400 text-sm">Churn Risk: {formatPercentage(selectedSegment.analytics.churnRisk)}</p>
                  </div>
                </div>

                {/* Additional segment details could go here */}
                <div className="bg-gray-700/20 p-4 rounded-lg">
                  <h4 className="text-white font-medium mb-2">Segment Details</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Type:</span>
                      <span className={`ml-2 capitalize ${getTypeColor(selectedSegment.type)}`}>
                        {selectedSegment.type}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Status:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${getStatusColor(selectedSegment.status)}`}>
                        {selectedSegment.status}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Created:</span>
                      <span className="ml-2 text-white">{selectedSegment.createdAt.toLocaleDateString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Last Updated:</span>
                      <span className="ml-2 text-white">{selectedSegment.updatedAt.toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default CustomerSegmentationDashboard