/**
 * Email Campaign Dashboard
 * 
 * Comprehensive email marketing campaign management interface
 * Part of Phase 2 Marketing Automation Suite
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Mail,
  Send,
  Users,
  TrendingUp,
  Eye,
  Play,
  Pause,
  Copy,
  Calendar,
  Filter,
  Search,
  Plus,
  MoreHorizontal,
  BarChart3,
  Clock,
  Target,
  Zap,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Settings,
  Edit,
  Trash2,
  Circle
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'
import EmailCampaignService, { 
  EmailCampaign, 
  CampaignStatus, 
  CampaignType,
  CampaignStats 
} from '../../lib/marketing/EmailCampaignService'

interface EmailCampaignDashboardProps {
  className?: string
}

/**
 * Email Campaign Dashboard Component
 */
const EmailCampaignDashboard: React.FC<EmailCampaignDashboardProps> = ({ className = '' }) => {
  // ===== STATE =====
  const [campaigns, setCampaigns] = useState<EmailCampaign[]>([])
  const [stats, setStats] = useState<CampaignStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedCampaign, setSelectedCampaign] = useState<EmailCampaign | null>(null)
  const [activeTab, setActiveTab] = useState<'all' | 'draft' | 'scheduled' | 'sent'>('all')
  const [showFilters, setShowFilters] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)

  const campaignService = EmailCampaignService.getInstance()

  // ===== EFFECTS =====
  useEffect(() => {
    loadCampaigns()
    loadStats()
  }, [activeTab])

  // ===== HANDLERS =====
  const loadCampaigns = async () => {
    setLoading(true)
    try {
      const filters: any = {
        limit: 50,
        sortBy: 'updated',
        sortOrder: 'desc'
      }

      if (activeTab !== 'all') {
        filters.status = [activeTab as CampaignStatus]
      }

      if (searchQuery) {
        filters.searchQuery = searchQuery
      }

      const result = await campaignService.getCampaigns(filters)
      setCampaigns(result.campaigns)
    } catch (error) {
      console.error('Failed to load campaigns:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const campaignStats = await campaignService.getCampaignStats()
      setStats(campaignStats)
    } catch (error) {
      console.error('Failed to load campaign stats:', error)
    }
  }

  const handleSendCampaign = async (campaignId: string) => {
    try {
      await campaignService.sendCampaign(campaignId)
      await loadCampaigns()
    } catch (error) {
      console.error('Failed to send campaign:', error)
      alert('Failed to send campaign: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  const handlePauseCampaign = async (campaignId: string) => {
    try {
      await campaignService.pauseCampaign(campaignId)
      await loadCampaigns()
    } catch (error) {
      console.error('Failed to pause campaign:', error)
    }
  }

  const handleDuplicateCampaign = async (campaign: EmailCampaign) => {
    try {
      await campaignService.duplicateCampaign(campaign.id, `${campaign.name} (Copy)`)
      await loadCampaigns()
    } catch (error) {
      console.error('Failed to duplicate campaign:', error)
    }
  }

  const getStatusColor = (status: CampaignStatus) => {
    switch (status) {
      case 'draft': return 'text-gray-400 bg-gray-500/10'
      case 'scheduled': return 'text-blue-400 bg-blue-500/10'
      case 'sending': return 'text-yellow-400 bg-yellow-500/10'
      case 'sent': return 'text-green-400 bg-green-500/10'
      case 'paused': return 'text-orange-400 bg-orange-500/10'
      case 'cancelled': return 'text-red-400 bg-red-500/10'
      case 'failed': return 'text-red-400 bg-red-500/10'
      default: return 'text-gray-400 bg-gray-500/10'
    }
  }

  const getStatusIcon = (status: CampaignStatus) => {
    switch (status) {
      case 'draft': return <Edit className="w-3 h-3" />
      case 'scheduled': return <Clock className="w-3 h-3" />
      case 'sending': return <Zap className="w-3 h-3 animate-pulse" />
      case 'sent': return <CheckCircle className="w-3 h-3" />
      case 'paused': return <Pause className="w-3 h-3" />
      case 'cancelled': return <XCircle className="w-3 h-3" />
      case 'failed': return <AlertTriangle className="w-3 h-3" />
      default: return <Circle className="w-3 h-3" />
    }
  }

  const getTypeColor = (type: CampaignType) => {
    switch (type) {
      case 'newsletter': return 'text-blue-400'
      case 'promotional': return 'text-green-400'
      case 'transactional': return 'text-purple-400'
      case 'welcome': return 'text-yellow-400'
      case 'abandoned_cart': return 'text-orange-400'
      case 'follow_up': return 'text-cyan-400'
      case 'survey': return 'text-pink-400'
      default: return 'text-gray-400'
    }
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const formatPercentage = (num: number): string => {
    return `${(num * 100).toFixed(1)}%`
  }

  const getFilteredCampaigns = () => {
    let filtered = campaigns

    if (searchQuery) {
      filtered = filtered.filter(campaign => 
        campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        campaign.subject.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }

  // ===== RENDER =====
  return (
    <div className={`email-campaign-dashboard ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div className="flex items-center space-x-3">
          <Mail className="w-8 h-8 text-purple-400" />
          <div>
            <h2 className="text-2xl font-bold text-white">Email Campaigns</h2>
            <p className="text-gray-400">Marketing automation and email management</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            icon={Plus}
            onClick={() => setShowCreateModal(true)}
          >
            Create Campaign
          </AdminButton>
          
          <AdminButton
            variant="secondary"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-accent-600' : ''}
          >
            Filters
          </AdminButton>

          <AdminButton
            variant="secondary"
            icon={BarChart3}
          >
            Analytics
          </AdminButton>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Campaigns</p>
                <p className="text-2xl font-bold text-white">{stats.totalCampaigns}</p>
                <p className="text-green-400 text-sm">+12 this month</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <Mail className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Sent</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalSent)}</p>
                <p className="text-green-400 text-sm">+28% vs last month</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <Send className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Avg Open Rate</p>
                <p className="text-2xl font-bold text-white">{formatPercentage(stats.averageOpenRate)}</p>
                <p className="text-green-400 text-sm">+2.3% improvement</p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <Eye className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </AdminCard>

          <AdminCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Revenue</p>
                <p className="text-2xl font-bold text-white">${formatNumber(stats.totalRevenue)}</p>
                <p className="text-green-400 text-sm">+15% this quarter</p>
              </div>
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </AdminCard>
        </div>
      )}

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6"
          >
            <AdminCard title="Campaign Filters" className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Search</label>
                  <div className="relative">
                    <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search campaigns..."
                      className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Campaign Type</label>
                  <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500">
                    <option value="">All Types</option>
                    <option value="newsletter">Newsletter</option>
                    <option value="promotional">Promotional</option>
                    <option value="transactional">Transactional</option>
                    <option value="welcome">Welcome</option>
                    <option value="abandoned_cart">Abandoned Cart</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
                  <select className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                  </select>
                </div>
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg mb-6">
        {[
          { id: 'all', label: 'All Campaigns', count: campaigns.length },
          { id: 'draft', label: 'Drafts', count: campaigns.filter(c => c.status === 'draft').length },
          { id: 'scheduled', label: 'Scheduled', count: campaigns.filter(c => c.status === 'scheduled').length },
          { id: 'sent', label: 'Sent', count: campaigns.filter(c => c.status === 'sent').length }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as typeof activeTab)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <span className="text-sm">{tab.label}</span>
            <span className="text-xs bg-gray-600 px-1.5 py-0.5 rounded">{tab.count}</span>
          </button>
        ))}
      </div>

      {/* Campaigns List */}
      <AdminCard>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700/50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Campaign</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Type</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Status</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Recipients</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Open Rate</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Click Rate</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Created</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={8} className="px-4 py-8 text-center text-gray-400">
                    <div className="flex items-center justify-center">
                      <div className="w-6 h-6 border-2 border-purple-400 border-t-transparent rounded-full animate-spin mr-2" />
                      Loading campaigns...
                    </div>
                  </td>
                </tr>
              ) : getFilteredCampaigns().length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-4 py-8 text-center text-gray-400">
                    <Mail className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>No campaigns found</p>
                  </td>
                </tr>
              ) : (
                getFilteredCampaigns().map((campaign) => (
                  <tr key={campaign.id} className="hover:bg-gray-700/30 transition-colors">
                    <td className="px-4 py-3">
                      <div>
                        <h4 className="text-white font-medium">{campaign.name}</h4>
                        <p className="text-gray-400 text-sm truncate max-w-xs">{campaign.subject}</p>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <span className={`capitalize text-sm ${getTypeColor(campaign.type)}`}>
                        {campaign.type.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${getStatusColor(campaign.status)}`}>
                        {getStatusIcon(campaign.status)}
                        <span className="capitalize">{campaign.status}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-gray-300">
                      {formatNumber(campaign.analytics.totalSent)}
                    </td>
                    <td className="px-4 py-3 text-gray-300">
                      {formatPercentage(campaign.analytics.openRate)}
                    </td>
                    <td className="px-4 py-3 text-gray-300">
                      {formatPercentage(campaign.analytics.clickRate)}
                    </td>
                    <td className="px-4 py-3 text-gray-300 text-sm">
                      {campaign.createdAt.toLocaleDateString()}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setSelectedCampaign(campaign)}
                          className="text-purple-400 hover:text-purple-300"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        
                        {campaign.status === 'draft' && (
                          <button
                            onClick={() => handleSendCampaign(campaign.id)}
                            className="text-green-400 hover:text-green-300"
                          >
                            <Send className="w-4 h-4" />
                          </button>
                        )}
                        
                        {campaign.status === 'sending' && (
                          <button
                            onClick={() => handlePauseCampaign(campaign.id)}
                            className="text-yellow-400 hover:text-yellow-300"
                          >
                            <Pause className="w-4 h-4" />
                          </button>
                        )}
                        
                        <button
                          onClick={() => handleDuplicateCampaign(campaign)}
                          className="text-blue-400 hover:text-blue-300"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                        
                        <button className="text-gray-400 hover:text-white">
                          <MoreHorizontal className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </AdminCard>

      {/* Campaign Detail Modal */}
      <AnimatePresence>
        {selectedCampaign && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            onClick={() => setSelectedCampaign(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-800 rounded-lg max-w-4xl w-full mx-4 max-h-[80vh] overflow-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-xl font-bold text-white">{selectedCampaign.name}</h3>
                    <p className="text-gray-400">{selectedCampaign.subject}</p>
                  </div>
                  <button
                    onClick={() => setSelectedCampaign(null)}
                    className="text-gray-400 hover:text-white"
                  >
                    <XCircle className="w-6 h-6" />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Campaign Analytics */}
                  <div className="md:col-span-2">
                    <h4 className="text-lg font-medium text-white mb-4">Performance Analytics</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="bg-gray-700/30 p-4 rounded-lg">
                        <p className="text-gray-400 text-sm">Sent</p>
                        <p className="text-xl font-bold text-white">{formatNumber(selectedCampaign.analytics.totalSent)}</p>
                      </div>
                      <div className="bg-gray-700/30 p-4 rounded-lg">
                        <p className="text-gray-400 text-sm">Opened</p>
                        <p className="text-xl font-bold text-white">{formatNumber(selectedCampaign.analytics.opened)}</p>
                        <p className="text-green-400 text-xs">{formatPercentage(selectedCampaign.analytics.openRate)}</p>
                      </div>
                      <div className="bg-gray-700/30 p-4 rounded-lg">
                        <p className="text-gray-400 text-sm">Clicked</p>
                        <p className="text-xl font-bold text-white">{formatNumber(selectedCampaign.analytics.clicked)}</p>
                        <p className="text-blue-400 text-xs">{formatPercentage(selectedCampaign.analytics.clickRate)}</p>
                      </div>
                      <div className="bg-gray-700/30 p-4 rounded-lg">
                        <p className="text-gray-400 text-sm">Revenue</p>
                        <p className="text-xl font-bold text-white">${formatNumber(selectedCampaign.analytics.revenue)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Campaign Details */}
                  <div>
                    <h4 className="text-lg font-medium text-white mb-4">Campaign Details</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm text-gray-400">Status</label>
                        <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${getStatusColor(selectedCampaign.status)}`}>
                          {getStatusIcon(selectedCampaign.status)}
                          <span className="capitalize">{selectedCampaign.status}</span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-gray-400">Type</label>
                        <p className={`capitalize ${getTypeColor(selectedCampaign.type)}`}>
                          {selectedCampaign.type.replace('_', ' ')}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm text-gray-400">Created</label>
                        <p className="text-white">{selectedCampaign.createdAt.toLocaleString()}</p>
                      </div>
                      {selectedCampaign.sentAt && (
                        <div>
                          <label className="text-sm text-gray-400">Sent</label>
                          <p className="text-white">{selectedCampaign.sentAt.toLocaleString()}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default EmailCampaignDashboard