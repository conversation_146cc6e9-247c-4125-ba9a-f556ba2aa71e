/**
 * Performance Monitor Component
 *
 * Real-time performance monitoring dashboard for the community admin system.
 * Tracks system metrics, query performance, cache efficiency, and resource usage.
 *
 * Features:
 * - Real-time system performance metrics
 * - Database query performance tracking
 * - Cache hit/miss ratio monitoring
 * - Memory and CPU usage tracking
 * - API response time analysis
 * - Performance alerts and notifications
 * - Historical performance trends
 * - Optimization recommendations
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Activity,
  Zap,
  Database,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Cpu,
  HardDrive,
  Wifi,
  RefreshCw,
  Settings,
  Download
} from 'lucide-react';
import { AdminCard, AdminButton } from '../common';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { usePerformanceMetrics } from '../hooks/usePerformanceMetrics';
import { CommunityStatsCard } from '../community/shared/CommunityStatsCard';

interface PerformanceMonitorProps {
  className?: string;
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  description: string;
  timestamp: Date;
  resolved: boolean;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className = ''
}) => {
  const { hasPermission } = useAdminPermissions();
  
  // State management
  const [activeTab, setActiveTab] = useState<'overview' | 'queries' | 'cache' | 'alerts'>('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Data hooks
  const {
    performanceData,
    queryMetrics,
    cacheMetrics,
    systemMetrics,
    alerts,
    loading,
    error,
    refetch,
    exportMetrics
  } = usePerformanceMetrics({ autoRefresh, refreshInterval });

  // Permission checks
  const canViewMetrics = hasPermission('system_monitoring', 'read');
  const canExportMetrics = hasPermission('system_monitoring', 'export');

  // Handle export
  const handleExport = useCallback(async (format: 'csv' | 'json') => {
    if (!canExportMetrics) return;
    
    try {
      await exportMetrics(format);
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, [canExportMetrics, exportMetrics]);

  if (!canViewMetrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-400">Access denied</p>
          <p className="text-sm text-gray-500">You don't have permission to view performance metrics</p>
        </div>
      </div>
    );
  }

  if (loading && !performanceData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 text-purple-400 mx-auto mb-4 animate-spin" />
          <p className="text-gray-400">Loading performance metrics...</p>
        </div>
      </div>
    );
  }

  // Key performance metrics
  const keyMetrics = performanceData ? [
    {
      title: 'Response Time',
      value: `${performanceData.averageResponseTime}ms`,
      icon: Clock,
      color: 'blue' as const,
      trend: performanceData.responseTimeTrend,
      subtitle: 'Average API response'
    },
    {
      title: 'Query Performance',
      value: `${performanceData.averageQueryTime}ms`,
      icon: Database,
      color: 'green' as const,
      trend: performanceData.queryTimeTrend,
      subtitle: 'Database queries'
    },
    {
      title: 'Cache Hit Rate',
      value: `${Math.round(performanceData.cacheHitRate * 100)}%`,
      icon: Zap,
      color: 'yellow' as const,
      trend: performanceData.cacheHitTrend,
      subtitle: 'Cache efficiency'
    },
    {
      title: 'System Load',
      value: `${Math.round(performanceData.systemLoad * 100)}%`,
      icon: Cpu,
      color: performanceData.systemLoad > 0.8 ? 'red' as const : 'purple' as const,
      trend: performanceData.systemLoadTrend,
      subtitle: 'CPU utilization'
    },
    {
      title: 'Memory Usage',
      value: `${Math.round(performanceData.memoryUsage * 100)}%`,
      icon: HardDrive,
      color: performanceData.memoryUsage > 0.8 ? 'red' as const : 'blue' as const,
      trend: performanceData.memoryTrend,
      subtitle: 'RAM utilization'
    },
    {
      title: 'Active Connections',
      value: performanceData.activeConnections,
      icon: Wifi,
      color: 'green' as const,
      trend: performanceData.connectionsTrend,
      subtitle: 'Database connections'
    }
  ] : [];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Performance Monitor</h1>
          <p className="text-gray-400">
            Real-time system performance and optimization insights
            {performanceData?.lastUpdated && (
              <span className="ml-2 text-sm">
                • Last updated: {performanceData.lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Auto-refresh</span>
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                autoRefresh ? 'bg-purple-600' : 'bg-gray-600'
              }`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                autoRefresh ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
          </div>
          
          <select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value={10}>10s</option>
            <option value={30}>30s</option>
            <option value={60}>1m</option>
            <option value={300}>5m</option>
          </select>
          
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={refetch}
            disabled={loading}
            className={loading ? 'animate-spin' : ''}
          >
            Refresh
          </AdminButton>
          
          {canExportMetrics && (
            <div className="relative group">
              <AdminButton
                variant="secondary"
                icon={Download}
              >
                Export
              </AdminButton>
              <div className="absolute right-0 top-full mt-2 w-24 bg-gray-800 border border-gray-700 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                <button
                  onClick={() => handleExport('csv')}
                  className="w-full px-3 py-2 text-left text-white hover:bg-gray-700 rounded-t-lg"
                >
                  CSV
                </button>
                <button
                  onClick={() => handleExport('json')}
                  className="w-full px-3 py-2 text-left text-white hover:bg-gray-700 rounded-b-lg"
                >
                  JSON
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('overview')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'overview' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Activity className="w-4 h-4" />
          <span>Overview</span>
        </button>
        
        <button
          onClick={() => setActiveTab('queries')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'queries' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Database className="w-4 h-4" />
          <span>Queries</span>
        </button>
        
        <button
          onClick={() => setActiveTab('cache')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'cache' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Zap className="w-4 h-4" />
          <span>Cache</span>
        </button>
        
        <button
          onClick={() => setActiveTab('alerts')}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'alerts' 
              ? 'bg-purple-600 text-white' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <AlertTriangle className="w-4 h-4" />
          <span>Alerts</span>
          {alerts && alerts.filter(a => !a.resolved).length > 0 && (
            <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
              {alerts.filter(a => !a.resolved).length}
            </span>
          )}
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {keyMetrics.map((metric, index) => (
          <CommunityStatsCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            color={metric.color}
            trend={metric.trend}
            subtitle={metric.subtitle}
          />
        ))}
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* System Health */}
            <AdminCard className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">System Health</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Overall Status</span>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Healthy</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Uptime</span>
                  <span className="text-white font-medium">99.9%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Error Rate</span>
                  <span className="text-white font-medium">0.1%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Throughput</span>
                  <span className="text-white font-medium">1,250 req/min</span>
                </div>
              </div>
            </AdminCard>

            {/* Performance Trends */}
            <AdminCard className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Performance Trends</h3>
              <div className="h-64 bg-gray-800 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <TrendingUp className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">Performance chart visualization</p>
                  <p className="text-xs">Would integrate with charting library</p>
                </div>
              </div>
            </AdminCard>
          </div>
        )}

        {activeTab === 'queries' && (
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Query Performance</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left text-gray-400 font-medium py-2">Query</th>
                    <th className="text-center text-gray-400 font-medium py-2">Avg Time</th>
                    <th className="text-center text-gray-400 font-medium py-2">Executions</th>
                    <th className="text-center text-gray-400 font-medium py-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {queryMetrics?.map((query, index) => (
                    <tr key={index} className="border-b border-gray-800">
                      <td className="py-3 text-white font-mono text-sm">{query.name}</td>
                      <td className="text-center py-3 text-white">{query.avgTime}ms</td>
                      <td className="text-center py-3 text-white">{query.executions}</td>
                      <td className="text-center py-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          query.avgTime < 100 ? 'bg-green-600 text-white' :
                          query.avgTime < 500 ? 'bg-yellow-600 text-white' :
                          'bg-red-600 text-white'
                        }`}>
                          {query.avgTime < 100 ? 'Fast' : query.avgTime < 500 ? 'Moderate' : 'Slow'}
                        </span>
                      </td>
                    </tr>
                  )) || (
                    <tr>
                      <td colSpan={4} className="text-center py-8 text-gray-400">
                        No query metrics available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </AdminCard>
        )}

        {activeTab === 'cache' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AdminCard className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Cache Statistics</h3>
              <div className="space-y-4">
                {cacheMetrics?.map((cache, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-gray-400">{cache.name}</span>
                    <div className="text-right">
                      <div className="text-white font-medium">{Math.round(cache.hitRate * 100)}%</div>
                      <div className="text-xs text-gray-500">{cache.hits}/{cache.total}</div>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-4 text-gray-400">
                    No cache metrics available
                  </div>
                )}
              </div>
            </AdminCard>

            <AdminCard className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Cache Performance</h3>
              <div className="h-48 bg-gray-800 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <Zap className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">Cache performance chart</p>
                </div>
              </div>
            </AdminCard>
          </div>
        )}

        {activeTab === 'alerts' && (
          <AdminCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Performance Alerts</h3>
            <div className="space-y-3">
              {alerts?.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border ${
                    alert.resolved 
                      ? 'border-gray-700 bg-gray-800' 
                      : alert.type === 'error' 
                        ? 'border-red-500 bg-red-500/10'
                        : alert.type === 'warning'
                          ? 'border-yellow-500 bg-yellow-500/10'
                          : 'border-blue-500 bg-blue-500/10'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {alert.type === 'error' ? (
                        <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5" />
                      ) : alert.type === 'warning' ? (
                        <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
                      ) : (
                        <CheckCircle className="w-5 h-5 text-blue-400 mt-0.5" />
                      )}
                      <div>
                        <h4 className="text-white font-medium">{alert.title}</h4>
                        <p className="text-gray-400 text-sm mt-1">{alert.description}</p>
                        <p className="text-gray-500 text-xs mt-2">
                          {alert.timestamp.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    {alert.resolved && (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    )}
                  </div>
                </div>
              )) || (
                <div className="text-center py-8 text-gray-400">
                  <CheckCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No active alerts</p>
                  <p className="text-sm">System is running smoothly</p>
                </div>
              )}
            </div>
          </AdminCard>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
