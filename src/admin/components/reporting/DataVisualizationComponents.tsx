/**
 * Data Visualization Components
 * 
 * Reusable chart and visualization components for reports
 * Part of Phase 2 Advanced Reporting System
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ding<PERSON>p,
  <PERSON><PERSON>ding<PERSON>own,
  Arrow<PERSON><PERSON>,
  ArrowDown,
  Minus,
  Eye,
  Download,
  Maximize2,
  Settings,
  RefreshCw,
  Filter,
  Calendar,
  Target,
  Activity
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'

// ===== TYPES =====
export interface ChartDataPoint {
  name: string
  value: number
  label?: string
  color?: string
  metadata?: Record<string, any>
}

export interface ChartConfig {
  colors?: string[]
  showLegend?: boolean
  showTooltip?: boolean
  showDataLabels?: boolean
  showGrid?: boolean
  animate?: boolean
  responsive?: boolean
  height?: number
  theme?: 'light' | 'dark'
}

export interface KPICardProps {
  title: string
  value: number
  previousValue?: number
  format?: 'currency' | 'percentage' | 'number'
  trend?: 'up' | 'down' | 'neutral'
  change?: number
  target?: number
  description?: string
  color?: string
  icon?: React.ComponentType<any>
  size?: 'sm' | 'md' | 'lg'
  showSparkline?: boolean
  sparklineData?: number[]
}

export interface ChartProps {
  data: ChartDataPoint[]
  config?: ChartConfig
  title?: string
  subtitle?: string
  loading?: boolean
  error?: string
  onDataPointClick?: (data: ChartDataPoint) => void
  className?: string
}

// ===== UTILITY FUNCTIONS =====
const formatValue = (value: number, format: KPICardProps['format'] = 'number'): string => {
  switch (format) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: value < 100 ? 2 : 0
      }).format(value)
    case 'percentage':
      return `${value.toFixed(1)}%`
    case 'number':
      if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
      if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
      return value.toLocaleString()
    default:
      return value.toString()
  }
}

const calculateChange = (current: number, previous: number): number => {
  if (previous === 0) return 0
  return ((current - previous) / previous) * 100
}

const getTrendColor = (trend: KPICardProps['trend']) => {
  switch (trend) {
    case 'up': return 'text-green-400'
    case 'down': return 'text-red-400'
    case 'neutral': return 'text-gray-400'
    default: return 'text-gray-400'
  }
}

const getTrendIcon = (trend: KPICardProps['trend']) => {
  switch (trend) {
    case 'up': return <ArrowUp className="w-3 h-3" />
    case 'down': return <ArrowDown className="w-3 h-3" />
    case 'neutral': return <Minus className="w-3 h-3" />
    default: return <Minus className="w-3 h-3" />
  }
}

// ===== KPI CARD COMPONENT =====
export const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  previousValue,
  format = 'number',
  trend,
  change,
  target,
  description,
  color = 'blue',
  icon: Icon,
  size = 'md',
  showSparkline = false,
  sparklineData = [],
  ...props
}) => {
  const calculatedChange = useMemo(() => {
    if (change !== undefined) return change
    if (previousValue !== undefined) return calculateChange(value, previousValue)
    return 0
  }, [value, previousValue, change])

  const calculatedTrend = useMemo(() => {
    if (trend) return trend
    if (calculatedChange > 0) return 'up'
    if (calculatedChange < 0) return 'down'
    return 'neutral'
  }, [trend, calculatedChange])

  const targetProgress = useMemo(() => {
    if (!target) return undefined
    return Math.min((value / target) * 100, 100)
  }, [value, target])

  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }

  const valueSize = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl'
  }

  return (
    <AdminCard className={`${sizeClasses[size]} hover:border-${color}-500/30 transition-colors`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* Header */}
          <div className="flex items-center space-x-2 mb-2">
            <p className="text-gray-400 text-sm font-medium">{title}</p>
            {calculatedChange !== 0 && (
              <div className={`flex items-center space-x-1 text-xs ${getTrendColor(calculatedTrend)}`}>
                {getTrendIcon(calculatedTrend)}
                <span>{Math.abs(calculatedChange).toFixed(1)}%</span>
              </div>
            )}
          </div>

          {/* Value */}
          <p className={`${valueSize[size]} font-bold text-white mb-1`}>
            {formatValue(value, format)}
          </p>

          {/* Additional Info */}
          <div className="space-y-1">
            {previousValue !== undefined && (
              <p className="text-gray-500 text-xs">
                Previous: {formatValue(previousValue, format)}
              </p>
            )}
            {description && (
              <p className="text-gray-400 text-xs">{description}</p>
            )}
          </div>

          {/* Target Progress */}
          {target && targetProgress !== undefined && (
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs mb-1">
                <span className="text-gray-400">Target Progress</span>
                <span className="text-gray-300">{targetProgress.toFixed(0)}%</span>
              </div>
              <div className="w-full h-1.5 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className={`h-full bg-${color}-500 transition-all duration-300`}
                  style={{ width: `${targetProgress}%` }}
                />
              </div>
            </div>
          )}

          {/* Sparkline */}
          {showSparkline && sparklineData.length > 0 && (
            <div className="mt-3">
              <div className="h-8 flex items-end space-x-1">
                {sparklineData.slice(-12).map((point, index) => (
                  <div
                    key={index}
                    className={`flex-1 bg-${color}-500/50 rounded-sm transition-all duration-200`}
                    style={{
                      height: `${(point / Math.max(...sparklineData)) * 100}%`,
                      minHeight: '2px'
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Icon */}
        {Icon && (
          <div className={`w-10 h-10 bg-${color}-500/20 rounded-lg flex items-center justify-center ml-4`}>
            <Icon className={`w-5 h-5 text-${color}-400`} />
          </div>
        )}
      </div>
    </AdminCard>
  )
}

// ===== SIMPLE BAR CHART COMPONENT =====
export const SimpleBarChart: React.FC<ChartProps> = ({
  data,
  config = {},
  title,
  subtitle,
  loading = false,
  error,
  onDataPointClick,
  className = ''
}) => {
  const {
    colors = ['#3B82F6', '#8B5CF6', '#EF4444', '#F59E0B', '#10B981'],
    showTooltip = true,
    animate = true,
    height = 300
  } = config

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)

  const maxValue = useMemo(() => Math.max(...data.map(d => d.value)), [data])

  if (loading) {
    return (
      <AdminCard title={title} subtitle={subtitle} className={className}>
        <div className="flex items-center justify-center" style={{ height }}>
          <div className="flex space-x-1">
            {[0, 1, 2].map(i => (
              <div
                key={i}
                className="w-4 bg-gray-600 rounded animate-pulse"
                style={{ 
                  height: Math.random() * 100 + 50,
                  animationDelay: `${i * 0.2}s`
                }}
              />
            ))}
          </div>
        </div>
      </AdminCard>
    )
  }

  if (error) {
    return (
      <AdminCard title={title} subtitle={subtitle} className={className}>
        <div className="flex items-center justify-center text-red-400" style={{ height }}>
          <p>{error}</p>
        </div>
      </AdminCard>
    )
  }

  return (
    <AdminCard title={title} subtitle={subtitle} className={className}>
      <div className="space-y-4">
        {/* Chart Actions */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            {data.length} data points
          </div>
          <div className="flex space-x-2">
            <button className="text-gray-400 hover:text-white">
              <Download className="w-4 h-4" />
            </button>
            <button className="text-gray-400 hover:text-white">
              <Maximize2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Chart */}
        <div className="relative" style={{ height }}>
          <div className="flex items-end justify-center space-x-2 h-full px-4">
            {data.map((item, index) => {
              const barHeight = (item.value / maxValue) * (height - 40)
              const color = item.color || colors[index % colors.length]
              
              return (
                <div
                  key={index}
                  className="flex-1 flex flex-col items-center group cursor-pointer"
                  onClick={() => onDataPointClick?.(item)}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  {/* Tooltip */}
                  {showTooltip && hoveredIndex === index && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg z-10"
                    >
                      {item.label || item.name}: {formatValue(item.value)}
                    </motion.div>
                  )}

                  {/* Bar */}
                  <motion.div
                    initial={animate ? { height: 0 } : undefined}
                    animate={{ height: barHeight }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="w-full rounded-t transition-all duration-200 group-hover:opacity-80"
                    style={{
                      backgroundColor: color,
                      minHeight: '4px'
                    }}
                  />

                  {/* Label */}
                  <div className="mt-2 text-xs text-gray-400 text-center truncate w-full">
                    {item.name}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </AdminCard>
  )
}

// ===== SIMPLE LINE CHART COMPONENT =====
export const SimpleLineChart: React.FC<ChartProps> = ({
  data,
  config = {},
  title,
  subtitle,
  loading = false,
  error,
  className = ''
}) => {
  const {
    colors = ['#3B82F6'],
    showTooltip = true,
    animate = true,
    height = 300,
    showGrid = true
  } = config

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)

  const maxValue = useMemo(() => Math.max(...data.map(d => d.value)), [data])
  const minValue = useMemo(() => Math.min(...data.map(d => d.value)), [data])

  if (loading) {
    return (
      <AdminCard title={title} subtitle={subtitle} className={className}>
        <div className="flex items-center justify-center" style={{ height }}>
          <div className="animate-pulse">
            <LineChart className="w-12 h-12 text-gray-600" />
          </div>
        </div>
      </AdminCard>
    )
  }

  if (error) {
    return (
      <AdminCard title={title} subtitle={subtitle} className={className}>
        <div className="flex items-center justify-center text-red-400" style={{ height }}>
          <p>{error}</p>
        </div>
      </AdminCard>
    )
  }

  const chartHeight = height - 60
  const chartWidth = 100 // percentage

  // Generate SVG path
  const pathData = data.map((item, index) => {
    const x = (index / (data.length - 1)) * chartWidth
    const y = chartHeight - ((item.value - minValue) / (maxValue - minValue)) * chartHeight
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`
  }).join(' ')

  return (
    <AdminCard title={title} subtitle={subtitle} className={className}>
      <div className="space-y-4">
        {/* Chart Actions */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Range: {formatValue(minValue)} - {formatValue(maxValue)}
          </div>
          <div className="flex space-x-2">
            <button className="text-gray-400 hover:text-white">
              <Download className="w-4 h-4" />
            </button>
            <button className="text-gray-400 hover:text-white">
              <Maximize2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Chart */}
        <div className="relative" style={{ height }}>
          <svg className="w-full h-full" viewBox={`0 0 ${chartWidth} ${chartHeight}`}>
            {/* Grid lines */}
            {showGrid && (
              <g className="opacity-20">
                {Array.from({ length: 5 }).map((_, index) => (
                  <line
                    key={index}
                    x1="0"
                    y1={(index / 4) * chartHeight}
                    x2={chartWidth}
                    y2={(index / 4) * chartHeight}
                    stroke="currentColor"
                    strokeWidth="0.5"
                  />
                ))}
              </g>
            )}

            {/* Line path */}
            <motion.path
              d={pathData}
              fill="none"
              stroke={colors[0]}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              initial={animate ? { pathLength: 0 } : undefined}
              animate={{ pathLength: 1 }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            />

            {/* Data points */}
            {data.map((item, index) => {
              const x = (index / (data.length - 1)) * chartWidth
              const y = chartHeight - ((item.value - minValue) / (maxValue - minValue)) * chartHeight
              
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r={hoveredIndex === index ? "4" : "3"}
                  fill={colors[0]}
                  className="cursor-pointer transition-all duration-200"
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                />
              )
            })}
          </svg>

          {/* Tooltip */}
          {showTooltip && hoveredIndex !== null && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="absolute bg-gray-800 text-white text-xs px-3 py-2 rounded shadow-lg z-10 pointer-events-none"
              style={{
                left: `${(hoveredIndex / (data.length - 1)) * 100}%`,
                top: `${100 - ((data[hoveredIndex].value - minValue) / (maxValue - minValue)) * 100}%`,
                transform: 'translate(-50%, -100%)'
              }}
            >
              <div className="text-center">
                <div className="font-medium">{data[hoveredIndex].name}</div>
                <div>{formatValue(data[hoveredIndex].value)}</div>
              </div>
            </motion.div>
          )}
        </div>

        {/* X-axis labels */}
        <div className="flex justify-between text-xs text-gray-400 px-2">
          {data.filter((_, index) => index % Math.ceil(data.length / 6) === 0).map((item, index) => (
            <span key={index}>{item.name}</span>
          ))}
        </div>
      </div>
    </AdminCard>
  )
}

// ===== SIMPLE PIE CHART COMPONENT =====
export const SimplePieChart: React.FC<ChartProps> = ({
  data,
  config = {},
  title,
  subtitle,
  loading = false,
  error,
  className = ''
}) => {
  const {
    colors = ['#3B82F6', '#8B5CF6', '#EF4444', '#F59E0B', '#10B981', '#F472B6'],
    showLegend = true,
    showTooltip = true,
    height = 300
  } = config

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)

  const total = useMemo(() => data.reduce((sum, item) => sum + item.value, 0), [data])

  if (loading) {
    return (
      <AdminCard title={title} subtitle={subtitle} className={className}>
        <div className="flex items-center justify-center" style={{ height }}>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500" />
        </div>
      </AdminCard>
    )
  }

  if (error) {
    return (
      <AdminCard title={title} subtitle={subtitle} className={className}>
        <div className="flex items-center justify-center text-red-400" style={{ height }}>
          <p>{error}</p>
        </div>
      </AdminCard>
    )
  }

  // Calculate angles
  let currentAngle = -90 // Start from top
  const segments = data.map((item, index) => {
    const percentage = (item.value / total) * 100
    const angle = (item.value / total) * 360
    const startAngle = currentAngle
    const endAngle = currentAngle + angle
    currentAngle += angle

    const radius = 80
    const largeArcFlag = angle > 180 ? 1 : 0
    
    const x1 = 100 + radius * Math.cos((startAngle * Math.PI) / 180)
    const y1 = 100 + radius * Math.sin((startAngle * Math.PI) / 180)
    const x2 = 100 + radius * Math.cos((endAngle * Math.PI) / 180)
    const y2 = 100 + radius * Math.sin((endAngle * Math.PI) / 180)

    const pathData = [
      `M 100 100`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ')

    return {
      ...item,
      pathData,
      percentage,
      color: item.color || colors[index % colors.length],
      index
    }
  })

  return (
    <AdminCard title={title} subtitle={subtitle} className={className}>
      <div className="space-y-4">
        {/* Chart Actions */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Total: {formatValue(total)}
          </div>
          <div className="flex space-x-2">
            <button className="text-gray-400 hover:text-white">
              <Download className="w-4 h-4" />
            </button>
            <button className="text-gray-400 hover:text-white">
              <Maximize2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="flex items-center justify-center space-x-8">
          {/* Chart */}
          <div className="relative">
            <svg width="200" height="200" viewBox="0 0 200 200">
              {segments.map((segment) => (
                <motion.path
                  key={segment.index}
                  d={segment.pathData}
                  fill={segment.color}
                  className="cursor-pointer transition-all duration-200"
                  style={{
                    opacity: hoveredIndex === segment.index ? 0.8 : 1,
                    transform: hoveredIndex === segment.index ? 'scale(1.05)' : 'scale(1)',
                    transformOrigin: '100px 100px'
                  }}
                  onMouseEnter={() => setHoveredIndex(segment.index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: segment.index * 0.1 }}
                />
              ))}
            </svg>

            {/* Center text */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{data.length}</div>
                <div className="text-xs text-gray-400">Categories</div>
              </div>
            </div>

            {/* Tooltip */}
            {showTooltip && hoveredIndex !== null && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute top-0 left-full ml-4 bg-gray-800 text-white text-xs px-3 py-2 rounded shadow-lg z-10"
              >
                <div>
                  <div className="font-medium">{segments[hoveredIndex].name}</div>
                  <div>{formatValue(segments[hoveredIndex].value)}</div>
                  <div className="text-gray-400">{segments[hoveredIndex].percentage.toFixed(1)}%</div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Legend */}
          {showLegend && (
            <div className="space-y-2">
              {segments.map((segment) => (
                <div
                  key={segment.index}
                  className="flex items-center space-x-2 cursor-pointer hover:opacity-80"
                  onMouseEnter={() => setHoveredIndex(segment.index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  <div
                    className="w-3 h-3 rounded"
                    style={{ backgroundColor: segment.color }}
                  />
                  <span className="text-sm text-gray-300">{segment.name}</span>
                  <span className="text-xs text-gray-500">
                    ({segment.percentage.toFixed(1)}%)
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </AdminCard>
  )
}

// ===== CHART GRID COMPONENT =====
export const ChartGrid: React.FC<{
  charts: Array<{
    id: string
    type: 'kpi' | 'bar' | 'line' | 'pie'
    props: any
    colSpan?: number
  }>
  columns?: number
  gap?: number
  className?: string
}> = ({
  charts,
  columns = 3,
  gap = 6,
  className = ''
}) => {
  return (
    <div className={`grid gap-${gap} ${className}`} style={{ gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))` }}>
      {charts.map((chart) => {
        const colSpan = chart.colSpan || 1
        const spanClass = colSpan > 1 ? `col-span-${colSpan}` : ''
        
        switch (chart.type) {
          case 'kpi':
            return (
              <div key={chart.id} className={spanClass}>
                <KPICard {...chart.props} />
              </div>
            )
          case 'bar':
            return (
              <div key={chart.id} className={spanClass}>
                <SimpleBarChart {...chart.props} />
              </div>
            )
          case 'line':
            return (
              <div key={chart.id} className={spanClass}>
                <SimpleLineChart {...chart.props} />
              </div>
            )
          case 'pie':
            return (
              <div key={chart.id} className={spanClass}>
                <SimplePieChart {...chart.props} />
              </div>
            )
          default:
            return null
        }
      })}
    </div>
  )
}

export default {
  KPICard,
  SimpleBarChart,
  SimpleLineChart,
  SimplePieChart,
  ChartGrid
}