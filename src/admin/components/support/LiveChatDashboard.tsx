/**
 * Live Chat Dashboard
 * 
 * Real-time customer support chat interface
 * Part of Phase 2 Customer Support Integration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MessageSquare,
  Users,
  Clock,
  Send,
  Paperclip,
  Smile,
  MoreHorizontal,
  User,
  Phone,
  Mail,
  Tag,
  AlertCircle,
  CheckCircle,
  Circle,
  RefreshCw,
  Filter,
  Search,
  Settings,
  ArrowRightLeft,
  UserPlus,
  Zap
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'
import LiveChatService, { 
  LiveChatSession, 
  ChatMessage, 
  ChatStatus, 
  ChatPriority,
  TypingIndicator 
} from '../../lib/support/LiveChatService'

interface LiveChatDashboardProps {
  className?: string
}

/**
 * Live Chat Dashboard Component
 */
const LiveChatDashboard: React.FC<LiveChatDashboardProps> = ({ className = '' }) => {
  // ===== STATE =====
  const [chatSessions, setChatSessions] = useState<LiveChatSession[]>([])
  const [selectedChat, setSelectedChat] = useState<LiveChatSession | null>(null)
  const [currentMessage, setCurrentMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const [isTyping, setIsTyping] = useState<TypingIndicator[]>([])
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'waiting' | 'closed'>('active')
  const [showFilters, setShowFilters] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [onlineStatus, setOnlineStatus] = useState(true)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const chatService = LiveChatService.getInstance()

  // ===== EFFECTS =====
  useEffect(() => {
    initializeChatService()
    loadChatSessions()

    return () => {
      chatService.disconnect()
    }
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [selectedChat?.messages])

  // ===== HANDLERS =====
  const initializeChatService = () => {
    // Set up WebSocket event listeners
    chatService.on('connected', () => {
      setOnlineStatus(true)
      console.log('Chat service connected')
    })

    chatService.on('disconnected', () => {
      setOnlineStatus(false)
      console.log('Chat service disconnected')
    })

    chatService.on('message', (message: ChatMessage) => {
      handleNewMessage(message)
    })

    chatService.on('typing', (typing: TypingIndicator) => {
      handleTypingIndicator(typing)
    })

    chatService.on('chatStatusUpdate', (update: any) => {
      handleChatStatusUpdate(update)
    })
  }

  const loadChatSessions = async () => {
    setLoading(true)
    try {
      const result = await chatService.getChatSessions({
        limit: 50,
        sortBy: 'updated',
        sortOrder: 'desc'
      })
      setChatSessions(result.chats)
    } catch (error) {
      console.error('Failed to load chat sessions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleNewMessage = (message: ChatMessage) => {
    if (selectedChat?.id === message.chatId) {
      setSelectedChat(prev => prev ? {
        ...prev,
        messages: [...prev.messages, message]
      } : null)
    }

    // Update chat session in list
    setChatSessions(prev => prev.map(chat => 
      chat.id === message.chatId
        ? { ...chat, messages: [...chat.messages, message], updatedAt: new Date() }
        : chat
    ))
  }

  const handleTypingIndicator = (typing: TypingIndicator) => {
    setIsTyping(prev => {
      const filtered = prev.filter(t => t.userId !== typing.userId || t.chatId !== typing.chatId)
      return typing.isTyping ? [...filtered, typing] : filtered
    })

    // Remove typing indicator after 3 seconds
    setTimeout(() => {
      setIsTyping(prev => prev.filter(t => 
        t.userId !== typing.userId || t.chatId !== typing.chatId || t.timestamp !== typing.timestamp
      ))
    }, 3000)
  }

  const handleChatStatusUpdate = (update: any) => {
    setChatSessions(prev => prev.map(chat => 
      chat.id === update.chatId
        ? { ...chat, ...update.changes }
        : chat
    ))
  }

  const selectChat = async (chat: LiveChatSession) => {
    if (selectedChat?.id) {
      chatService.leaveChat(selectedChat.id)
    }

    setSelectedChat(chat)
    chatService.joinChat(chat.id)

    // Mark messages as read
    const unreadMessages = chat.messages
      .filter(msg => !msg.readBy.some(r => r.userId === 'current_admin_id'))
      .map(msg => msg.id)
    
    if (unreadMessages.length > 0) {
      await chatService.markMessagesAsRead(chat.id, unreadMessages)
    }
  }

  const sendMessage = async () => {
    if (!selectedChat || !currentMessage.trim()) return

    try {
      const message = await chatService.sendMessage(selectedChat.id, {
        content: currentMessage,
        type: 'text'
      })

      setCurrentMessage('')
      
      // Stop typing indicator
      chatService.sendTypingIndicator(selectedChat.id, false)
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  const handleTyping = () => {
    if (selectedChat) {
      chatService.sendTypingIndicator(selectedChat.id, true)
      
      // Stop typing after 2 seconds of inactivity
      setTimeout(() => {
        chatService.sendTypingIndicator(selectedChat.id, false)
      }, 2000)
    }
  }

  const updateChatStatus = async (status: ChatStatus) => {
    if (!selectedChat) return

    try {
      await chatService.updateChatStatus(selectedChat.id, status)
      setSelectedChat(prev => prev ? { ...prev, status } : null)
    } catch (error) {
      console.error('Failed to update chat status:', error)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const getStatusColor = (status: ChatStatus) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/10'
      case 'waiting': return 'text-yellow-400 bg-yellow-500/10'
      case 'closed': return 'text-gray-400 bg-gray-500/10'
      case 'transferred': return 'text-blue-400 bg-blue-500/10'
      default: return 'text-gray-400 bg-gray-500/10'
    }
  }

  const getPriorityColor = (priority: ChatPriority) => {
    switch (priority) {
      case 'urgent': return 'text-red-400'
      case 'high': return 'text-orange-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getFilteredChats = () => {
    let filtered = chatSessions

    // Filter by tab
    if (activeTab !== 'all') {
      filtered = filtered.filter(chat => chat.status === activeTab)
    }

    // Filter by search
    if (searchQuery) {
      filtered = filtered.filter(chat => 
        chat.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.customerEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.subject.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const getTypingUsers = (chatId: string) => {
    return isTyping
      .filter(t => t.chatId === chatId && t.isTyping)
      .map(t => t.userName)
  }

  // ===== RENDER =====
  return (
    <div className={`live-chat-dashboard flex h-full ${className}`}>
      {/* Chat List Sidebar */}
      <div className="w-1/3 border-r border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="w-6 h-6 text-purple-400" />
              <h2 className="text-lg font-semibold text-white">Live Chat</h2>
              <div className={`w-2 h-2 rounded-full ${onlineStatus ? 'bg-green-400' : 'bg-red-400'}`} />
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <Filter className="w-4 h-4" />
              </button>
              <button
                onClick={loadChatSessions}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search chats..."
              className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:border-purple-500"
            />
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex bg-gray-800">
          {[
            { id: 'all', label: 'All', count: chatSessions.length },
            { id: 'active', label: 'Active', count: chatSessions.filter(c => c.status === 'active').length },
            { id: 'waiting', label: 'Waiting', count: chatSessions.filter(c => c.status === 'waiting').length },
            { id: 'closed', label: 'Closed', count: chatSessions.filter(c => c.status === 'closed').length }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as typeof activeTab)}
              className={`flex-1 px-3 py-2 text-sm transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        {/* Chat Sessions */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="w-6 h-6 animate-spin text-purple-400" />
            </div>
          ) : getFilteredChats().length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-gray-400">
              <MessageSquare className="w-8 h-8 mb-2 opacity-50" />
              <p>No chats found</p>
            </div>
          ) : (
            getFilteredChats().map((chat) => {
              const typingUsers = getTypingUsers(chat.id)
              const hasUnread = chat.messages.some(msg => 
                !msg.readBy.some(r => r.userId === 'current_admin_id') && 
                msg.senderRole === 'customer'
              )

              return (
                <div
                  key={chat.id}
                  onClick={() => selectChat(chat)}
                  className={`p-4 border-b border-gray-700 cursor-pointer transition-colors ${
                    selectedChat?.id === chat.id
                      ? 'bg-purple-600/20 border-purple-500/30'
                      : 'hover:bg-gray-700/30'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-white font-medium truncate">{chat.customerName}</h4>
                        {hasUnread && (
                          <div className="w-2 h-2 bg-purple-400 rounded-full" />
                        )}
                      </div>
                      <p className="text-gray-400 text-sm truncate">{chat.subject}</p>
                      {chat.messages.length > 0 && (
                        <p className="text-gray-500 text-xs truncate mt-1">
                          {chat.messages[chat.messages.length - 1].content}
                        </p>
                      )}
                      {typingUsers.length > 0 && (
                        <p className="text-purple-400 text-xs italic">
                          {typingUsers.join(', ')} typing...
                        </p>
                      )}
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      <span className={`px-2 py-1 rounded text-xs ${getStatusColor(chat.status)}`}>
                        {chat.status}
                      </span>
                      <span className="text-gray-500 text-xs">
                        {formatTime(chat.updatedAt)}
                      </span>
                      <div className={`w-1 h-1 rounded-full ${getPriorityColor(chat.priority)}`} />
                    </div>
                  </div>
                </div>
              )
            })
          )}
        </div>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 flex flex-col">
        {selectedChat ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-700 bg-gray-800/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <User className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium">{selectedChat.customerName}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span className="flex items-center space-x-1">
                        <Mail className="w-3 h-3" />
                        <span>{selectedChat.customerEmail}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>Wait: {Math.floor(selectedChat.waitTime / 60)}m</span>
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <select
                    value={selectedChat.status}
                    onChange={(e) => updateChatStatus(e.target.value as ChatStatus)}
                    className="px-3 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:border-purple-500"
                  >
                    <option value="active">Active</option>
                    <option value="waiting">Waiting</option>
                    <option value="closed">Closed</option>
                    <option value="transferred">Transferred</option>
                  </select>
                  
                  <AdminButton variant="secondary" size="sm" icon={ArrowRightLeft}>
                    Transfer
                  </AdminButton>
                  
                  <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {selectedChat.messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.senderRole === 'admin' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.senderRole === 'admin'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-700 text-white'
                  }`}>
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-xs opacity-75">{message.senderName}</span>
                      <span className="text-xs opacity-50">{formatTime(message.timestamp)}</span>
                    </div>
                    <p className="text-sm">{message.content}</p>
                  </div>
                </div>
              ))}
              
              {/* Typing indicators */}
              {getTypingUsers(selectedChat.id).map(user => (
                <div key={user} className="flex justify-start">
                  <div className="bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <p className="text-xs text-gray-400 italic">{user} is typing...</p>
                  </div>
                </div>
              ))}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-700">
              <div className="flex items-center space-x-3">
                <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                  <Paperclip className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                  <Smile className="w-4 h-4" />
                </button>
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={currentMessage}
                    onChange={(e) => {
                      setCurrentMessage(e.target.value)
                      handleTyping()
                    }}
                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                    placeholder="Type a message..."
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  />
                </div>
                <AdminButton
                  onClick={sendMessage}
                  disabled={!currentMessage.trim()}
                  icon={Send}
                  size="sm"
                >
                  Send
                </AdminButton>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <MessageSquare className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Select a chat to start</h3>
              <p>Choose a conversation from the sidebar to begin messaging</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default LiveChatDashboard