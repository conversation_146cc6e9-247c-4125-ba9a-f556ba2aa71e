/**
 * User Impersonation Panel
 * 
 * Secure user impersonation interface for customer support
 * Part of Phase 2 Customer Support Integration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  UserCog,
  Search,
  Shield,
  Clock,
  AlertTriangle,
  User,
  Play,
  Square,
  Eye,
  FileText,
  CheckCircle,
  XCircle,
  Timer,
  Lock,
  Unlock
} from 'lucide-react'
import { AdminCard, AdminButton } from '../common'
import UserImpersonationService, { 
  ImpersonationSession, 
  ImpersonationRequest 
} from '../../lib/support/UserImpersonationService'

interface UserImpersonationPanelProps {
  className?: string
  ticketId?: string
}

interface UserSearchResult {
  id: string
  email: string
  name: string
  tier: string
  lastActive: Date
  totalOrders: number
  canImpersonate: boolean
  restrictions?: string[]
}

/**
 * User Impersonation Panel Component
 */
const UserImpersonationPanel: React.FC<UserImpersonationPanelProps> = ({ 
  className = '',
  ticketId 
}) => {
  // ===== STATE =====
  const [currentSession, setCurrentSession] = useState<ImpersonationSession | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([])
  const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null)
  const [impersonationReason, setImpersonationReason] = useState('')
  const [duration, setDuration] = useState(30)
  const [loading, setLoading] = useState(false)
  const [searching, setSearching] = useState(false)
  const [showStartDialog, setShowStartDialog] = useState(false)
  const [sessions, setSessions] = useState<ImpersonationSession[]>([])
  const [activeTab, setActiveTab] = useState<'search' | 'active' | 'history'>('search')

  const impersonationService = UserImpersonationService.getInstance()

  // ===== EFFECTS =====
  useEffect(() => {
    checkCurrentSession()
    loadRecentSessions()
  }, [])

  useEffect(() => {
    if (searchQuery.length >= 3) {
      searchUsers()
    } else {
      setSearchResults([])
    }
  }, [searchQuery])

  // ===== HANDLERS =====
  const checkCurrentSession = async () => {
    try {
      const session = impersonationService.getCurrentSession()
      setCurrentSession(session)
    } catch (error) {
      console.error('Failed to check current session:', error)
    }
  }

  const loadRecentSessions = async () => {
    try {
      const result = await impersonationService.getImpersonationSessions({
        limit: 10,
        offset: 0
      })
      setSessions(result.sessions)
    } catch (error) {
      console.error('Failed to load recent sessions:', error)
    }
  }

  const searchUsers = async () => {
    setSearching(true)
    try {
      // Mock user search - replace with actual API call
      const mockResults: UserSearchResult[] = [
        {
          id: 'user_1',
          email: '<EMAIL>',
          name: 'John Doe',
          tier: 'Premium',
          lastActive: new Date(),
          totalOrders: 25,
          canImpersonate: true
        },
        {
          id: 'user_2',
          email: '<EMAIL>',
          name: 'Jane Smith',
          tier: 'Standard',
          lastActive: new Date(Date.now() - ********),
          totalOrders: 12,
          canImpersonate: true
        },
        {
          id: 'user_3',
          email: '<EMAIL>',
          name: 'Admin User',
          tier: 'Admin',
          lastActive: new Date(),
          totalOrders: 0,
          canImpersonate: false,
          restrictions: ['Admin accounts cannot be impersonated']
        }
      ].filter(user => 
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.name.toLowerCase().includes(searchQuery.toLowerCase())
      )

      setSearchResults(mockResults)
    } catch (error) {
      console.error('Failed to search users:', error)
    } finally {
      setSearching(false)
    }
  }

  const handleUserSelect = (user: UserSearchResult) => {
    setSelectedUser(user)
    setShowStartDialog(true)
    if (ticketId) {
      setImpersonationReason(`Customer support for ticket #${ticketId}`)
    }
  }

  const startImpersonation = async () => {
    if (!selectedUser || !impersonationReason.trim()) {
      return
    }

    setLoading(true)
    try {
      const request: ImpersonationRequest = {
        targetUserId: selectedUser.id,
        reason: impersonationReason,
        ticketId,
        duration
      }

      const session = await impersonationService.startImpersonation(request)
      setCurrentSession(session)
      setShowStartDialog(false)
      setSelectedUser(null)
      setImpersonationReason('')
      setSearchQuery('')
      setSearchResults([])
      
      // Refresh sessions list
      await loadRecentSessions()
    } catch (error) {
      console.error('Failed to start impersonation:', error)
      alert('Failed to start impersonation: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  const endImpersonation = async () => {
    if (!currentSession) return

    setLoading(true)
    try {
      await impersonationService.endImpersonation()
      setCurrentSession(null)
      await loadRecentSessions()
    } catch (error) {
      console.error('Failed to end impersonation:', error)
      alert('Failed to end impersonation: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}m`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const getSessionStatusColor = (status: ImpersonationSession['status']) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/10'
      case 'expired': return 'text-yellow-400 bg-yellow-500/10'
      case 'terminated': return 'text-gray-400 bg-gray-500/10'
      default: return 'text-gray-400 bg-gray-500/10'
    }
  }

  // ===== RENDER =====
  return (
    <div className={`user-impersonation-panel ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <UserCog className="w-6 h-6 text-purple-400" />
          <div>
            <h3 className="text-lg font-semibold text-white">User Impersonation</h3>
            <p className="text-gray-400 text-sm">Secure customer support impersonation</p>
          </div>
        </div>

        {currentSession && (
          <div className="flex items-center space-x-2">
            <div className={`px-3 py-1 rounded-full text-xs ${getSessionStatusColor(currentSession.status)}`}>
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                <span>Active Session</span>
              </span>
            </div>
            <AdminButton
              variant="secondary"
              size="sm"
              icon={Square}
              onClick={endImpersonation}
              loading={loading}
            >
              End Session
            </AdminButton>
          </div>
        )}
      </div>

      {/* Current Session Display */}
      {currentSession && (
        <AdminCard className="mb-6 bg-purple-500/10 border-purple-500/20">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <Shield className="w-6 h-6 text-purple-400" />
              </div>
              <div>
                <h4 className="text-white font-medium">
                  Impersonating: {currentSession.targetUserName}
                </h4>
                <p className="text-gray-400 text-sm">{currentSession.targetUserEmail}</p>
                <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                  <span className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>Started: {currentSession.startedAt.toLocaleTimeString()}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Timer className="w-3 h-3" />
                    <span>Expires: {currentSession.expiresAt.toLocaleTimeString()}</span>
                  </span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-white font-medium">Session Active</p>
              <p className="text-purple-400 text-sm">All actions logged</p>
            </div>
          </div>
        </AdminCard>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg mb-6">
        {[
          { id: 'search', label: 'Search Users', icon: Search },
          { id: 'active', label: 'Active Sessions', icon: Play },
          { id: 'history', label: 'Session History', icon: Clock }
        ].map(tab => {
          const IconComponent = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as typeof activeTab)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <IconComponent className="w-4 h-4" />
              <span className="text-sm">{tab.label}</span>
            </button>
          )
        })}
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'search' && (
          <motion.div
            key="search"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <AdminCard title="Search Users" subtitle="Find users to impersonate">
              <div className="space-y-4">
                {/* Search Input */}
                <div className="relative">
                  <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by email or name..."
                    className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  />
                  {searching && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
                    </div>
                  )}
                </div>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <div className="space-y-2">
                    {searchResults.map((user) => (
                      <div
                        key={user.id}
                        className={`p-4 rounded-lg border transition-colors ${
                          user.canImpersonate
                            ? 'border-gray-700 hover:border-purple-500/50 cursor-pointer'
                            : 'border-red-500/20 bg-red-500/5'
                        }`}
                        onClick={() => user.canImpersonate && handleUserSelect(user)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                              user.canImpersonate ? 'bg-purple-500/20' : 'bg-red-500/20'
                            }`}>
                              {user.canImpersonate ? (
                                <User className="w-5 h-5 text-purple-400" />
                              ) : (
                                <Lock className="w-5 h-5 text-red-400" />
                              )}
                            </div>
                            <div>
                              <h4 className="text-white font-medium">{user.name}</h4>
                              <p className="text-gray-400 text-sm">{user.email}</p>
                              <div className="flex items-center space-x-3 text-xs text-gray-500 mt-1">
                                <span>Tier: {user.tier}</span>
                                <span>Orders: {user.totalOrders}</span>
                                <span>Last active: {user.lastActive.toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {user.canImpersonate ? (
                              <div className="text-green-400 text-sm">
                                <Unlock className="w-4 h-4" />
                              </div>
                            ) : (
                              <div className="text-red-400 text-sm">
                                <Lock className="w-4 h-4" />
                              </div>
                            )}
                          </div>
                        </div>
                        {user.restrictions && (
                          <div className="mt-2 p-2 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-xs">
                            {user.restrictions.join(', ')}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {searchQuery.length >= 3 && searchResults.length === 0 && !searching && (
                  <div className="text-center py-8 text-gray-400">
                    <User className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>No users found</p>
                  </div>
                )}
              </div>
            </AdminCard>
          </motion.div>
        )}

        {activeTab === 'history' && (
          <motion.div
            key="history"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <AdminCard title="Session History" subtitle="Recent impersonation sessions">
              <div className="space-y-3">
                {sessions.map((session) => (
                  <div key={session.id} className="p-4 bg-gray-700/30 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-white font-medium">{session.targetUserName}</h4>
                        <p className="text-gray-400 text-sm">{session.targetUserEmail}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                          <span>Admin: {session.adminName}</span>
                          <span>Started: {session.startedAt.toLocaleString()}</span>
                          {session.endedAt && (
                            <span>Ended: {session.endedAt.toLocaleString()}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs ${getSessionStatusColor(session.status)}`}>
                          {session.status}
                        </span>
                        <button className="text-purple-400 hover:text-purple-300">
                          <Eye className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                {sessions.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    <Clock className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>No session history</p>
                  </div>
                )}
              </div>
            </AdminCard>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Start Impersonation Dialog */}
      <AnimatePresence>
        {showStartDialog && selectedUser && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            onClick={() => setShowStartDialog(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-800 rounded-lg max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-white">Start Impersonation</h3>
                  <button
                    onClick={() => setShowStartDialog(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    <XCircle className="w-6 h-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-white font-medium mb-2">Target User</h4>
                    <div className="p-3 bg-gray-700 rounded-lg">
                      <p className="text-white">{selectedUser.name}</p>
                      <p className="text-gray-400 text-sm">{selectedUser.email}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Reason for Impersonation *
                    </label>
                    <textarea
                      value={impersonationReason}
                      onChange={(e) => setImpersonationReason(e.target.value)}
                      placeholder="Provide a detailed reason for impersonation..."
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Minimum 10 characters required
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Session Duration
                    </label>
                    <select
                      value={duration}
                      onChange={(e) => setDuration(parseInt(e.target.value))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
                    >
                      <option value={15}>15 minutes</option>
                      <option value={30}>30 minutes</option>
                      <option value={60}>1 hour</option>
                      <option value={120}>2 hours</option>
                    </select>
                  </div>

                  <div className="flex space-x-3 pt-4">
                    <AdminButton
                      onClick={startImpersonation}
                      loading={loading}
                      disabled={impersonationReason.length < 10}
                      className="flex-1"
                    >
                      Start Impersonation
                    </AdminButton>
                    <AdminButton
                      variant="secondary"
                      onClick={() => setShowStartDialog(false)}
                      className="flex-1"
                    >
                      Cancel
                    </AdminButton>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default UserImpersonationPanel