/**
 * Navigation Context
 * 
 * React context for managing admin navigation state, tracking navigation
 * events, and providing navigation-related utilities.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useUser } from '../../lib/useUser';
import { navigationAuditService, NavigationInsights } from '../services/navigationAuditService';
import { usePermissionCheck, usePermissionFilteredNavigation } from '../hooks/usePermissionFilteredNavigation';
import { AdminRole, AdminPermission } from '../types/permissions';

interface NavigationState {
  currentPath: string;
  previousPath: string | null;
  navigationHistory: string[];
  isNavigating: boolean;
  lastNavigationTime: Date | null;
}

interface NavigationContextType extends NavigationState {
  navigateTo: (path: string, options?: { skipAudit?: boolean }) => Promise<boolean>;
  canNavigateTo: (path: string) => { canNavigate: boolean; reason?: string };
  getNavigationInsights: () => NavigationInsights;
  clearNavigationHistory: () => void;
  addToHistory: (path: string) => void;
  getRecentPaths: (limit?: number) => string[];
  isCurrentPath: (path: string) => boolean;
  getNavigationBreadcrumbs: () => { label: string; path: string }[];
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
  children: React.ReactNode;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const pathname = usePathname();
  const router = useRouter();
  const { user, profile } = useUser();
  const { hasAllPermissions, canPerformAction } = usePermissionCheck();
  const { hasAccessToPath } = usePermissionFilteredNavigation([]);

  const [state, setState] = useState<NavigationState>({
    currentPath: pathname || '',
    previousPath: null,
    navigationHistory: [],
    isNavigating: false,
    lastNavigationTime: null
  });

  // Track path changes
  useEffect(() => {
    if (pathname && pathname !== state.currentPath) {
      setState(prev => ({
        ...prev,
        previousPath: prev.currentPath,
        currentPath: pathname,
        navigationHistory: [pathname, ...prev.navigationHistory.slice(0, 49)], // Keep last 50
        lastNavigationTime: new Date()
      }));

      // Log navigation event if user is authenticated
      if (user && profile) {
        const adminRole = mapProfileRoleToAdminRole(profile.role, user.email);
        if (adminRole) {
          navigationAuditService.logNavigationEvent({
            adminId: user.uid,
            adminRole,
            fromPath: state.currentPath || 'unknown',
            toPath: pathname,
            permissions: [], // Will be populated by permission check
            accessGranted: true, // If we're here, access was granted
            sessionId: getSessionId(),
            userAgent: navigator.userAgent,
            ipAddress: 'unknown' // Would need to be passed from server
          });
        }
      }
    }
  }, [pathname, state.currentPath, user, profile]);

  /**
   * Navigate to a specific path with permission checking
   */
  const navigateTo = useCallback(async (
    path: string, 
    options: { skipAudit?: boolean } = {}
  ): Promise<boolean> => {
    if (!user || !profile) {
      console.warn('Cannot navigate: User not authenticated');
      return false;
    }

    setState(prev => ({ ...prev, isNavigating: true }));

    try {
      // Check permissions using path-based checking
      const hasAccess = hasAccessToPath(path);
      
      if (!hasAccess) {
        // Log permission denial
        const adminRole = mapProfileRoleToAdminRole(profile.role, user.email);
        if (adminRole && !options.skipAudit) {
          await navigationAuditService.logPermissionDenial(
            user.uid,
            adminRole,
            path,
            [], // Would need to determine required permissions
            'No permission found for path',
            getSessionId(),
            navigator.userAgent,
            'unknown'
          );
        }

        console.warn('Navigation denied: No permission found for', path);
        return false;
      }

      // Perform navigation
      router.push(path);
      return true;

    } catch (error) {
      console.error('Navigation error:', error);
      return false;
    } finally {
      setState(prev => ({ ...prev, isNavigating: false }));
    }
  }, [user, profile, hasAccessToPath, router]);

  /**
   * Check if user can navigate to a specific path
   */
  const canNavigateTo = useCallback((path: string): { canNavigate: boolean; reason?: string } => {
    if (!user || !profile) {
      return { canNavigate: false, reason: 'User not authenticated' };
    }

    const hasAccess = hasAccessToPath(path);
    return {
      canNavigate: hasAccess,
      reason: hasAccess ? undefined : 'No permission found for path'
    };
  }, [user, profile, hasAccessToPath]);

  /**
   * Get navigation insights
   */
  const getNavigationInsights = useCallback((): NavigationInsights => {
    return navigationAuditService.getNavigationInsights();
  }, []);

  /**
   * Clear navigation history
   */
  const clearNavigationHistory = useCallback(() => {
    setState(prev => ({
      ...prev,
      navigationHistory: [],
      previousPath: null
    }));
  }, []);

  /**
   * Add path to history manually
   */
  const addToHistory = useCallback((path: string) => {
    setState(prev => ({
      ...prev,
      navigationHistory: [path, ...prev.navigationHistory.filter(p => p !== path).slice(0, 49)]
    }));
  }, []);

  /**
   * Get recent paths
   */
  const getRecentPaths = useCallback((limit: number = 10): string[] => {
    return state.navigationHistory.slice(0, limit);
  }, [state.navigationHistory]);

  /**
   * Check if path is current
   */
  const isCurrentPath = useCallback((path: string): boolean => {
    return state.currentPath === path;
  }, [state.currentPath]);

  /**
   * Get navigation breadcrumbs
   */
  const getNavigationBreadcrumbs = useCallback((): { label: string; path: string }[] => {
    const pathSegments = state.currentPath.split('/').filter(Boolean);
    const breadcrumbs: { label: string; path: string }[] = [];

    let currentPath = '';
    pathSegments.forEach(segment => {
      currentPath += `/${segment}`;
      
      // Map path segments to readable labels
      const label = mapPathToLabel(segment);
      breadcrumbs.push({ label, path: currentPath });
    });

    return breadcrumbs;
  }, [state.currentPath]);

  const contextValue: NavigationContextType = {
    ...state,
    navigateTo,
    canNavigateTo,
    getNavigationInsights,
    clearNavigationHistory,
    addToHistory,
    getRecentPaths,
    isCurrentPath,
    getNavigationBreadcrumbs
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
};

/**
 * Hook to use navigation context
 */
export const useNavigation = (): NavigationContextType => {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

/**
 * Helper function to map profile role to admin role with email-based super admin detection
 */
function mapProfileRoleToAdminRole(role?: string, userEmail?: string): AdminRole | null {
  const roleMapping: Record<string, AdminRole> = {
    'superadmin': 'super_admin',
    'super_admin': 'super_admin',
    'admin': 'admin',
    'moderator': 'moderator',
    'analyst': 'analyst',
    'support': 'support'
  };

  // Default to admin role for any authenticated user
  let adminRole: AdminRole = 'admin';
  
  if (role) {
    adminRole = roleMapping[role.toLowerCase()] || 'admin';
  }

  // Check if user should be considered super admin based on specific email or role
  if (userEmail && (
    userEmail === '<EMAIL>' || 
    userEmail.includes('+superadmin') ||
    role?.toLowerCase().includes('super')
  )) {
    adminRole = 'super_admin';
  }
  // Check if user should be admin based on specific email
  else if (userEmail === '<EMAIL>') {
    adminRole = 'admin';
  }

  return adminRole;
}

/**
 * Helper function to get session ID
 */
function getSessionId(): string {
  // TODO: Implement proper session ID retrieval
  return `session_${Date.now()}`;
}

/**
 * Helper function to map path segments to readable labels
 */
function mapPathToLabel(segment: string): string {
  const labelMapping: Record<string, string> = {
    'admin': 'Admin',
    'dashboard': 'Dashboard',
    'analytics': 'Analytics',
    'products': 'Products',
    'inventory': 'Inventory',
    'orders': 'Orders',
    'raffles': 'Raffles',
    'reviews': 'Reviews',
    'users': 'Users',
    'segmentation': 'Segmentation',
    'bulk-operations': 'Bulk Operations',
    'gamification': 'Gamification',
    'support': 'Support',
    'tickets': 'Tickets',
    'chat': 'Live Chat',
    'impersonation': 'User Impersonation',
    'content': 'Content Management',
    'reports': 'Reports',
    'blog': 'Insights',
    'homepage': 'Homepage',
    'categories': 'Categories',
    'availability': 'Availability',
    'performance': 'Performance',
    'point-simulation': 'Point Simulation'
  };

  return labelMapping[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
}

export default NavigationProvider;
