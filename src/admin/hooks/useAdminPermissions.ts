import { useState, useEffect } from 'react';
import { useUser } from '@/lib/useUser';

export interface AdminPermissions {
  dashboard: boolean;
  users: boolean;
  products: boolean;
  raffles: boolean;
  analytics: boolean;
  content: boolean;
  settings: boolean;
  security: boolean;
  system: boolean;
  audit: boolean;
  database: boolean;
  admin_management: boolean;
}

export const useAdminPermissions = () => {
  const { user, profile } = useUser();
  const [permissions, setPermissions] = useState<AdminPermissions>({
    dashboard: false,
    users: false,
    products: false,
    raffles: false,
    analytics: false,
    content: false,
    settings: false,
    security: false,
    system: false,
    audit: false,
    database: false,
    admin_management: false,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user && profile) {
      if (profile.role === 'superadmin') {
        // Super admin has all permissions
        setPermissions({
          dashboard: true,
          users: true,
          products: true,
          raffles: true,
          analytics: true,
          content: true,
          settings: true,
          security: true,
          system: true,
          audit: true,
          database: true,
          admin_management: true,
        });
      } else if (profile.role === 'admin') {
        // Regular admin has limited permissions
        setPermissions({
          dashboard: true,
          users: true,
          products: true,
          raffles: true,
          analytics: true,
          content: true,
          settings: false,
          security: false,
          system: false,
          audit: false,
          database: false,
          admin_management: false,
        });
      } else {
        // No admin role, no permissions
        setPermissions({
          dashboard: false,
          users: false,
          products: false,
          raffles: false,
          analytics: false,
          content: false,
          settings: false,
          security: false,
          system: false,
          audit: false,
          database: false,
          admin_management: false,
        });
      }
      setLoading(false);
    } else if (user === null) {
      // User is not authenticated
      setLoading(false);
    }
  }, [user, profile]);

  const hasPermission = (permission: keyof AdminPermissions): boolean => {
    return permissions[permission];
  };

  const hasAnyPermission = (permissionList: (keyof AdminPermissions)[]): boolean => {
    return permissionList.some(permission => permissions[permission]);
  };

  const hasAllPermissions = (permissionList: (keyof AdminPermissions)[]): boolean => {
    return permissionList.every(permission => permissions[permission]);
  };

  const isSuperAdmin = profile?.role === 'superadmin';
  const isAdmin = profile?.role === 'admin' || profile?.role === 'superadmin';

  return {
    permissions,
    loading,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isSuperAdmin,
    isAdmin,
  };
};