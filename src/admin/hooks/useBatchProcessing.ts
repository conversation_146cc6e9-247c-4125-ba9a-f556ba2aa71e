import { useState, useCallback } from 'react';

export interface BatchOperation {
  id: string;
  type: 'delete' | 'update' | 'create';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  data: any;
  error?: string;
}

export const useBatchProcessing = () => {
  const [operations, setOperations] = useState<BatchOperation[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);

  const addOperation = useCallback((operation: Omit<BatchOperation, 'id' | 'status'>) => {
    const newOperation: BatchOperation = {
      ...operation,
      id: Date.now().toString(),
      status: 'pending',
    };
    setOperations(prev => [...prev, newOperation]);
    return newOperation.id;
  }, []);

  const updateOperation = useCallback((id: string, updates: Partial<BatchOperation>) => {
    setOperations(prev => prev.map(op => 
      op.id === id ? { ...op, ...updates } : op
    ));
  }, []);

  const processBatch = useCallback(async (processingFunction: (operation: BatchOperation) => Promise<any>) => {
    setIsProcessing(true);
    setProgress(0);
    
    const pendingOperations = operations.filter(op => op.status === 'pending');
    const total = pendingOperations.length;
    
    for (let i = 0; i < pendingOperations.length; i++) {
      const operation = pendingOperations[i];
      
      try {
        updateOperation(operation.id, { status: 'processing' });
        await processingFunction(operation);
        updateOperation(operation.id, { status: 'completed' });
      } catch (error) {
        updateOperation(operation.id, { 
          status: 'failed', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
      
      setProgress(((i + 1) / total) * 100);
    }
    
    setIsProcessing(false);
  }, [operations, updateOperation]);

  const clearOperations = useCallback(() => {
    setOperations([]);
    setProgress(0);
  }, []);

  const removeOperation = useCallback((id: string) => {
    setOperations(prev => prev.filter(op => op.id !== id));
  }, []);

  const getOperationStats = useCallback(() => {
    const stats = {
      total: operations.length,
      pending: operations.filter(op => op.status === 'pending').length,
      processing: operations.filter(op => op.status === 'processing').length,
      completed: operations.filter(op => op.status === 'completed').length,
      failed: operations.filter(op => op.status === 'failed').length,
    };
    return stats;
  }, [operations]);

  return {
    operations,
    isProcessing,
    progress,
    addOperation,
    updateOperation,
    processBatch,
    clearOperations,
    removeOperation,
    getOperationStats,
  };
};