import { useState } from 'react';

export interface ExternalService {
  id: string;
  name: string;
  status: 'connected' | 'disconnected' | 'error';
  lastSync?: Date;
}

export const useExternalServices = () => {
  const [services, setServices] = useState<ExternalService[]>([]);
  const [loading, setLoading] = useState(false);

  const connectService = async (serviceId: string) => {
    setLoading(true);
    try {
      // Simulate connection
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setServices(prev => prev.map(service => 
        service.id === serviceId 
          ? { ...service, status: 'connected' as const, lastSync: new Date() }
          : service
      ));
    } catch (error) {
      console.error('Failed to connect service:', error);
    } finally {
      setLoading(false);
    }
  };

  const disconnectService = async (serviceId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setServices(prev => prev.map(service => 
        service.id === serviceId 
          ? { ...service, status: 'disconnected' as const }
          : service
      ));
    } catch (error) {
      console.error('Failed to disconnect service:', error);
    } finally {
      setLoading(false);
    }
  };

  const syncService = async (serviceId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setServices(prev => prev.map(service => 
        service.id === serviceId 
          ? { ...service, lastSync: new Date() }
          : service
      ));
    } catch (error) {
      console.error('Failed to sync service:', error);
    } finally {
      setLoading(false);
    }
  };

  return {
    services,
    loading,
    connectService,
    disconnectService,
    syncService,
  };
};