/**
 * Permission-Filtered Navigation Hook
 * 
 * React hook for filtering navigation items based on admin permissions.
 * Provides real-time permission checking and navigation filtering.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useMemo, useCallback } from 'react';
import { useUser } from '../../lib/useUser';
import { permissionService } from '../services/permissionService';
import {
  AdminPermission,
  AdminRole,
  NavigationGroup,
  NavigationItem,
  ROLE_PERMISSIONS,
  NAVIGATION_PERMISSIONS
} from '../types/permissions';

interface UsePermissionFilteredNavigationResult {
  filteredNavigation: NavigationGroup[];
  hasAccessToPath: (path: string) => boolean;
  getPermissionStatus: (path: string) => {
    hasAccess: boolean;
    reason?: string;
    missingPermissions?: AdminPermission[];
    suggestedRole?: AdminRole;
  };
  adminPermissions: AdminPermission[];
  adminRole: AdminRole | null;
  isLoading: boolean;
}

/**
 * Hook for getting permission-filtered navigation
 */
export const usePermissionFilteredNavigation = (
  navigationGroups: NavigationGroup[]
): UsePermissionFilteredNavigationResult => {
  const { user, profile, loading } = useUser();

  // Get admin permissions based on role
  const adminPermissions = useMemo((): AdminPermission[] => {
    if (!profile?.role && !user) return [];
    
    // Map profile role to AdminRole type with more permissive fallbacks
    const roleMapping: Record<string, AdminRole> = {
      'superadmin': 'super_admin',
      'super_admin': 'super_admin',
      'admin': 'admin',
      'moderator': 'moderator',
      'analyst': 'analyst',
      'support': 'support'
    };

    // Default to admin role for any authenticated user in admin panel
    let adminRole: AdminRole = 'admin';
    
    if (profile?.role) {
      adminRole = roleMapping[profile.role.toLowerCase()] || 'admin';
    }

    // If user is authenticated but no profile role, still give admin permissions
    if (user && !profile?.role) {
      console.log('User authenticated but no profile role, defaulting to admin permissions');
      adminRole = 'admin';
    }

    // Check if user should be considered super admin based on specific email or role
    if (user?.email && (
      user.email === '<EMAIL>' || 
      user.email.includes('+superadmin') ||
      profile?.role?.toLowerCase().includes('super')
    )) {
      adminRole = 'super_admin';
      console.log('User elevated to super admin based on email or role');
    }
    // Check if user should be admin based on specific email
    else if (user?.email === '<EMAIL>') {
      adminRole = 'admin';
      console.log('User set to admin based on email');
    }

    const permissions = ROLE_PERMISSIONS[adminRole] || [];
    console.log('Admin permissions calculated:', { 
      profileRole: profile?.role, 
      mappedRole: adminRole, 
      permissionCount: permissions.length,
      hasInventory: permissions.some(p => p.resource === 'inventory')
    });
    
    return permissions;
  }, [profile?.role, user]);

  const adminRole = useMemo((): AdminRole | null => {
    if (!profile?.role && !user) return null;
    
    const roleMapping: Record<string, AdminRole> = {
      'superadmin': 'super_admin',
      'super_admin': 'super_admin',
      'admin': 'admin',
      'moderator': 'moderator',
      'analyst': 'analyst',
      'support': 'support'
    };

    // Default to admin role for any authenticated user in admin panel
    if (profile?.role) {
      return roleMapping[profile.role.toLowerCase()] || 'admin';
    }
    
    // If user is authenticated but no profile role, still return admin
    if (user) {
      return 'admin';
    }

    return null;
  }, [profile?.role, user]);

  // Filter navigation items based on permissions
  const filteredNavigation = useMemo((): NavigationGroup[] => {
    if (!adminPermissions.length) return [];

    return navigationGroups.map(group => {
      // Check if user has access to the group itself
      if (group.requiredPermissions && group.requiredPermissions.length > 0) {
        const groupCheck = permissionService.hasAllPermissions(
          adminPermissions,
          group.requiredPermissions
        );
        if (!groupCheck.hasPermission) {
          return { ...group, items: [] };
        }
      }

      // Filter items within the group
      const filteredItems = group.items.filter(item => {
        // Check item-specific permissions
        if (item.requiredPermissions && item.requiredPermissions.length > 0) {
          const itemCheck = permissionService.hasAllPermissions(
            adminPermissions,
            item.requiredPermissions
          );
          return itemCheck.hasPermission;
        }

        // Check path-based permissions
        const pathCheck = permissionService.canAccessPath(adminPermissions, item.path);
        return pathCheck.hasPermission;
      });

      return {
        ...group,
        items: filteredItems
      };
    }).filter(group => group.items.length > 0); // Remove empty groups
  }, [navigationGroups, adminPermissions]);

  // Check if user has access to a specific path
  const hasAccessToPath = useCallback((path: string): boolean => {
    if (!adminPermissions.length) return false;
    return permissionService.canAccessPath(adminPermissions, path).hasPermission;
  }, [adminPermissions]);

  // Get detailed permission status for a path
  const getPermissionStatus = useCallback((path: string) => {
    if (!adminPermissions.length) {
      return {
        hasAccess: false,
        reason: 'No admin permissions available',
        suggestedRole: 'admin' as AdminRole
      };
    }

    const result = permissionService.canAccessPath(adminPermissions, path);
    
    return {
      hasAccess: result.hasPermission,
      reason: result.reason,
      missingPermissions: result.missingPermissions,
      suggestedRole: result.missingPermissions 
        ? permissionService.getSuggestedRole(result.missingPermissions)
        : undefined
    };
  }, [adminPermissions]);

  return {
    filteredNavigation,
    hasAccessToPath,
    getPermissionStatus,
    adminPermissions,
    adminRole,
    isLoading: loading
  };
};

/**
 * Hook for checking specific permissions
 */
export const usePermissionCheck = () => {
  const { user, profile } = useUser();

  const adminPermissions = useMemo((): AdminPermission[] => {
    if (!profile?.role && !user) return [];
    
    const roleMapping: Record<string, AdminRole> = {
      'superadmin': 'super_admin',
      'super_admin': 'super_admin',
      'admin': 'admin',
      'moderator': 'moderator',
      'analyst': 'analyst',
      'support': 'support'
    };

    // Default to admin role for any authenticated user in admin panel
    let adminRole: AdminRole = 'admin';
    
    if (profile?.role) {
      adminRole = roleMapping[profile.role.toLowerCase()] || 'admin';
    }

    // If user is authenticated but no profile role, still give admin permissions
    if (user && !profile?.role) {
      adminRole = 'admin';
    }

    // Check if user should be considered super admin based on specific email or role
    if (user?.email && (
      user.email === '<EMAIL>' || 
      user.email.includes('+superadmin') ||
      profile?.role?.toLowerCase().includes('super')
    )) {
      adminRole = 'super_admin';
    }
    // Check if user should be admin based on specific email
    else if (user?.email === '<EMAIL>') {
      adminRole = 'admin';
    }

    return ROLE_PERMISSIONS[adminRole] || [];
  }, [profile?.role, user]);

  const hasPermission = useCallback((
    resource: string,
    action: string,
    scope?: string
  ): boolean => {
    if (!adminPermissions.length) return false;
    
    return permissionService.hasPermission(
      adminPermissions,
      resource as any,
      action as any,
      scope as any
    ).hasPermission;
  }, [adminPermissions]);

  const hasAllPermissions = useCallback((
    requiredPermissions: AdminPermission[]
  ): boolean => {
    if (!adminPermissions.length) return false;
    
    return permissionService.hasAllPermissions(
      adminPermissions,
      requiredPermissions
    ).hasPermission;
  }, [adminPermissions]);

  const canPerformAction = useCallback((
    resource: string,
    action: string
  ): { canPerform: boolean; reason?: string } => {
    if (!adminPermissions.length) {
      return {
        canPerform: false,
        reason: 'No admin permissions available'
      };
    }

    const result = permissionService.hasPermission(
      adminPermissions,
      resource as any,
      action as any
    );

    return {
      canPerform: result.hasPermission,
      reason: result.reason
    };
  }, [adminPermissions]);

  return {
    hasPermission,
    hasAllPermissions,
    canPerformAction,
    adminPermissions,
    adminRole: profile?.role || null
  };
};

/**
 * Hook for admin presence and activity
 */
export const useAdminPresence = () => {
  // TODO: Implement real-time admin presence tracking
  // This would connect to a WebSocket service for live updates
  
  return {
    activeAdmins: [],
    currentPageAdmins: [],
    adminActivity: [],
    isLoading: false
  };
};

export default usePermissionFilteredNavigation;
