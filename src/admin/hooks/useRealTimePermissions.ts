/**
 * Real-Time Permissions Hook
 * 
 * React hook for real-time permission checking and validation with
 * live updates and automatic re-validation.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser } from '../../lib/useUser';
import { realTimePermissionService, PermissionValidationEvent } from '../services/realTimePermissionService';
import { AdminPermission, AdminRole, PermissionCheckResult } from '../types/permissions';

interface UseRealTimePermissionsResult {
  hasPermission: (resource: string, action: string, scope?: string) => boolean;
  validatePermissions: (requiredPermissions: AdminPermission[]) => Promise<PermissionCheckResult>;
  batchCheckPermissions: (checks: { resource: string; action: string; scope?: string }[]) => Promise<Record<string, boolean>>;
  permissionEvents: PermissionValidationEvent[];
  isValidating: boolean;
  lastValidation: Date | null;
  validationStats: {
    totalChecks: number;
    successfulChecks: number;
    failedChecks: number;
    averageResponseTime: number;
  };
  refreshPermissions: () => Promise<void>;
  clearEvents: () => void;
}

/**
 * Hook for real-time permission checking
 */
export const useRealTimePermissions = (): UseRealTimePermissionsResult => {
  const { user, profile } = useUser();
  const [permissionEvents, setPermissionEvents] = useState<PermissionValidationEvent[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidation, setLastValidation] = useState<Date | null>(null);
  const [validationStats, setValidationStats] = useState({
    totalChecks: 0,
    successfulChecks: 0,
    failedChecks: 0,
    averageResponseTime: 0
  });

  const permissionCache = useRef(new Map<string, { result: boolean; timestamp: number }>());
  const responseTimeTracker = useRef<number[]>([]);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Map profile role to admin role
  const adminRole: AdminRole | null = profile?.role ? 
    (profile.role === 'superadmin' ? 'super_admin' : 
     profile.role === 'admin' ? 'admin' :
     profile.role === 'moderator' ? 'moderator' :
     profile.role === 'analyst' ? 'analyst' :
     profile.role === 'support' ? 'support' : null) : null;

  // Subscribe to permission events
  useEffect(() => {
    if (!user || !adminRole) return;

    const unsubscribe = realTimePermissionService.subscribe(
      user.uid,
      (event: PermissionValidationEvent) => {
        setPermissionEvents(prev => [event, ...prev.slice(0, 99)]); // Keep last 100 events
        
        // Update stats
        setValidationStats(prev => ({
          ...prev,
          totalChecks: prev.totalChecks + 1,
          successfulChecks: event.type === 'permission_granted' ? prev.successfulChecks + 1 : prev.successfulChecks,
          failedChecks: event.type === 'permission_denied' ? prev.failedChecks + 1 : prev.failedChecks
        }));

        // Clear cache on permission updates
        if (event.type === 'permission_updated' || event.type === 'session_expired') {
          permissionCache.current.clear();
        }
      }
    );

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [user, adminRole]);

  /**
   * Check if user has specific permission (with caching)
   */
  const hasPermission = useCallback((
    resource: string,
    action: string,
    scope?: string
  ): boolean => {
    if (!user || !adminRole) return false;

    const cacheKey = `${resource}:${action}:${scope || 'all'}`;
    const cached = permissionCache.current.get(cacheKey);
    
    // Return cached result if still valid (30 seconds)
    if (cached && (Date.now() - cached.timestamp) < 30000) {
      return cached.result;
    }

    // Perform async validation (result will be cached when complete)
    realTimePermissionService.hasPermission(user.uid, adminRole, resource, action, scope)
      .then(result => {
        permissionCache.current.set(cacheKey, {
          result,
          timestamp: Date.now()
        });
      })
      .catch(error => {
        console.error('Permission check error:', error);
      });

    // Return false for new checks (will be updated when validation completes)
    return false;
  }, [user, adminRole]);

  /**
   * Validate multiple permissions
   */
  const validatePermissions = useCallback(async (
    requiredPermissions: AdminPermission[]
  ): Promise<PermissionCheckResult> => {
    if (!user || !adminRole) {
      return { hasPermission: false, reason: 'User not authenticated' };
    }

    setIsValidating(true);
    const startTime = Date.now();

    try {
      const result = await realTimePermissionService.validatePermissions(
        user.uid,
        adminRole,
        requiredPermissions
      );

      const responseTime = Date.now() - startTime;
      responseTimeTracker.current.push(responseTime);
      
      // Keep only last 100 response times
      if (responseTimeTracker.current.length > 100) {
        responseTimeTracker.current = responseTimeTracker.current.slice(-100);
      }

      // Update average response time
      const avgResponseTime = responseTimeTracker.current.reduce((a, b) => a + b, 0) / 
                              responseTimeTracker.current.length;
      
      setValidationStats(prev => ({
        ...prev,
        averageResponseTime: avgResponseTime
      }));

      setLastValidation(new Date());
      return result;

    } catch (error) {
      console.error('Permission validation error:', error);
      return { hasPermission: false, reason: 'Validation failed' };
    } finally {
      setIsValidating(false);
    }
  }, [user, adminRole]);

  /**
   * Batch check multiple permissions
   */
  const batchCheckPermissions = useCallback(async (
    checks: { resource: string; action: string; scope?: string }[]
  ): Promise<Record<string, boolean>> => {
    if (!user || !adminRole) {
      return {};
    }

    setIsValidating(true);

    try {
      const results = await realTimePermissionService.batchValidatePermissions(
        user.uid,
        adminRole,
        checks
      );

      // Update cache with results
      Object.entries(results).forEach(([key, result]) => {
        permissionCache.current.set(key, {
          result,
          timestamp: Date.now()
        });
      });

      return results;

    } catch (error) {
      console.error('Batch permission check error:', error);
      return {};
    } finally {
      setIsValidating(false);
    }
  }, [user, adminRole]);

  /**
   * Refresh permissions (clear cache and re-validate)
   */
  const refreshPermissions = useCallback(async (): Promise<void> => {
    permissionCache.current.clear();
    setLastValidation(new Date());
    
    // TODO: Trigger re-validation of current permissions
    console.log('🔄 Permissions refreshed');
  }, []);

  /**
   * Clear permission events
   */
  const clearEvents = useCallback((): void => {
    setPermissionEvents([]);
    setValidationStats({
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      averageResponseTime: 0
    });
    responseTimeTracker.current = [];
  }, []);

  return {
    hasPermission,
    validatePermissions,
    batchCheckPermissions,
    permissionEvents,
    isValidating,
    lastValidation,
    validationStats,
    refreshPermissions,
    clearEvents
  };
};

/**
 * Hook for checking specific permission with real-time updates
 */
export const usePermissionCheck = (
  resource: string,
  action: string,
  scope?: string
): {
  hasPermission: boolean;
  isLoading: boolean;
  lastChecked: Date | null;
  error: string | null;
} => {
  const { hasPermission: checkPermission, isValidating } = useRealTimePermissions();
  const [hasPermission, setHasPermission] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      const result = checkPermission(resource, action, scope);
      setHasPermission(result);
      setLastChecked(new Date());
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Permission check failed');
      setHasPermission(false);
    }
  }, [checkPermission, resource, action, scope]);

  return {
    hasPermission,
    isLoading: isValidating,
    lastChecked,
    error
  };
};

/**
 * Hook for monitoring permission events
 */
export const usePermissionEvents = (
  eventTypes?: PermissionValidationEvent['type'][]
): {
  events: PermissionValidationEvent[];
  latestEvent: PermissionValidationEvent | null;
  eventCount: number;
  clearEvents: () => void;
} => {
  const { permissionEvents, clearEvents } = useRealTimePermissions();

  const filteredEvents = eventTypes ? 
    permissionEvents.filter(event => eventTypes.includes(event.type)) :
    permissionEvents;

  return {
    events: filteredEvents,
    latestEvent: filteredEvents[0] || null,
    eventCount: filteredEvents.length,
    clearEvents
  };
};

export default useRealTimePermissions;
