/**
 * Enterprise-Grade Audit Service
 * 
 * Comprehensive audit logging system for admin actions
 * Part of Phase 1 API Layer Expansion
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 1 Enhancement
 */

export type AdminAction = 
  | 'USER_CREATED' | 'USER_UPDATED' | 'USER_DELETED' | 'USER_ROLE_CHANGED'
  | 'PRODUCT_CREATED' | 'PRODUCT_UPDATED' | 'PRODUCT_DELETED'
  | 'ORDER_UPDATED' | 'ORDER_CANCELLED' | 'ORDER_REFUNDED'
  | 'RAFFLE_CREATED' | 'RAFFLE_UPDATED' | 'RAFFLE_DRAWN'
  | 'ADMIN_LOGIN' | 'ADMIN_LOGOUT' | 'ADMIN_ACCESS_DENIED'
  | 'BULK_OPERATION' | 'DATA_EXPORT' | 'SETTINGS_CHANGED'

export type AdminResource = 
  | 'user' | 'product' | 'order' | 'raffle' | 'category'
  | 'admin' | 'system' | 'analytics' | 'bulk_operation'

export type AuditSeverity = 'low' | 'medium' | 'high' | 'critical'

export interface AuditAction {
  action: AdminAction
  resource: AdminResource
  resourceId: string
  previousState?: Record<string, any>
  newState?: Record<string, any>
  severity?: AuditSeverity
  metadata?: Record<string, any>
}

export interface AuditLog {
  id: string
  userId: string
  adminId: string
  action: AdminAction
  resource: AdminResource
  resourceId: string
  previousState?: Record<string, any>
  newState?: Record<string, any>
  metadata: {
    ipAddress: string
    userAgent: string
    sessionId: string
    requestId: string
    [key: string]: any
  }
  timestamp: Date
  severity: AuditSeverity
}

export interface AuditFilters {
  adminId?: string
  action?: AdminAction
  resource?: AdminResource
  severity?: AuditSeverity
  dateFrom?: Date
  dateTo?: Date
  ipAddress?: string
  resourceId?: string
  limit?: number
  offset?: number
}

export interface ExportOptions {
  format: 'csv' | 'json' | 'pdf'
  filters?: AuditFilters
  includeMetadata?: boolean
  filename?: string
}

/**
 * Enterprise Audit Service for Admin Actions
 */
export class AuditService {
  private static instance: AuditService
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/admin/system/audit-logs'
  }

  static getInstance(): AuditService {
    if (!AuditService.instance) {
      AuditService.instance = new AuditService()
    }
    return AuditService.instance
  }

  /**
   * Log an admin action for audit trail
   */
  static async logAction(auditAction: AuditAction): Promise<void> {
    const instance = AuditService.getInstance()
    
    try {
      const response = await fetch(instance.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: auditAction.action,
          resource: auditAction.resource,
          resourceId: auditAction.resourceId,
          previousState: auditAction.previousState,
          newState: auditAction.newState,
          severity: auditAction.severity || 'medium',
          customMetadata: auditAction.metadata
        })
      })

      if (!response.ok) {
        throw new Error(`Audit logging failed: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to log audit action')
      }

      console.log('✅ Audit action logged:', auditAction.action, auditAction.resourceId)

    } catch (error) {
      console.error('❌ Failed to log audit action:', error)
      // Don't throw - audit logging should not break the main flow
      // In production, this might queue for retry or log to alternative system
    }
  }

  /**
   * Retrieve audit logs with filtering
   */
  static async getAuditLogs(filters: AuditFilters = {}): Promise<{
    logs: AuditLog[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    const instance = AuditService.getInstance()
    
    try {
      const params = new URLSearchParams()
      
      // Add filters to params
      if (filters.adminId) params.append('adminId', filters.adminId)
      if (filters.action) params.append('action', filters.action)
      if (filters.resource) params.append('resource', filters.resource)
      if (filters.severity) params.append('severity', filters.severity)
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.ipAddress) params.append('ipAddress', filters.ipAddress)
      if (filters.resourceId) params.append('resourceId', filters.resourceId)
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 50) + 1).toString())

      const response = await fetch(`${instance.baseUrl}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch audit logs: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve audit logs')
      }

      return {
        logs: result.data.map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp)
        })),
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }

    } catch (error) {
      console.error('Failed to retrieve audit logs:', error)
      throw error
    }
  }

  /**
   * Export audit logs in specified format
   */
  static async exportAuditLogs(options: ExportOptions): Promise<string> {
    try {
      const { logs } = await AuditService.getAuditLogs(options.filters)
      
      switch (options.format) {
        case 'csv':
          return AuditService.exportToCSV(logs, options.includeMetadata)
        case 'json':
          return AuditService.exportToJSON(logs, options.includeMetadata)
        case 'pdf':
          return AuditService.exportToPDF(logs, options.includeMetadata)
        default:
          throw new Error(`Unsupported export format: ${options.format}`)
      }
    } catch (error) {
      console.error('Failed to export audit logs:', error)
      throw error
    }
  }

  /**
   * Export audit logs to CSV format
   */
  private static exportToCSV(logs: AuditLog[], includeMetadata = false): string {
    const headers = [
      'Timestamp',
      'Admin ID',
      'Action',
      'Resource',
      'Resource ID',
      'Severity',
      'Previous State',
      'New State'
    ]

    if (includeMetadata) {
      headers.push('IP Address', 'User Agent', 'Session ID')
    }

    const rows = logs.map(log => {
      const row = [
        log.timestamp.toISOString(),
        log.adminId,
        log.action,
        log.resource,
        log.resourceId,
        log.severity,
        log.previousState ? JSON.stringify(log.previousState) : '',
        log.newState ? JSON.stringify(log.newState) : ''
      ]

      if (includeMetadata) {
        row.push(
          log.metadata.ipAddress,
          log.metadata.userAgent,
          log.metadata.sessionId
        )
      }

      return row.map(field => `"${field}"`).join(',')
    })

    return [headers.join(','), ...rows].join('\n')
  }

  /**
   * Export audit logs to JSON format
   */
  private static exportToJSON(logs: AuditLog[], includeMetadata = false): string {
    const exportData = logs.map(log => {
      const logData: any = {
        id: log.id,
        timestamp: log.timestamp.toISOString(),
        adminId: log.adminId,
        action: log.action,
        resource: log.resource,
        resourceId: log.resourceId,
        severity: log.severity,
        previousState: log.previousState,
        newState: log.newState
      }

      if (includeMetadata) {
        logData.metadata = log.metadata
      }

      return logData
    })

    return JSON.stringify({
      exportDate: new Date().toISOString(),
      totalLogs: logs.length,
      logs: exportData
    }, null, 2)
  }

  /**
   * Export audit logs to PDF format (simplified)
   */
  private static exportToPDF(logs: AuditLog[], includeMetadata = false): string {
    // This is a simplified text-based PDF export
    // In a real implementation, you'd use a PDF library like jsPDF
    let content = `SYNDICAPS AUDIT LOG REPORT\n`
    content += `Generated: ${new Date().toISOString()}\n`
    content += `Total Logs: ${logs.length}\n\n`
    content += `${'='.repeat(80)}\n\n`

    logs.forEach((log, index) => {
      content += `Log #${index + 1}\n`
      content += `Timestamp: ${log.timestamp.toISOString()}\n`
      content += `Admin ID: ${log.adminId}\n`
      content += `Action: ${log.action}\n`
      content += `Resource: ${log.resource} (${log.resourceId})\n`
      content += `Severity: ${log.severity.toUpperCase()}\n`
      
      if (log.previousState) {
        content += `Previous State: ${JSON.stringify(log.previousState)}\n`
      }
      
      if (log.newState) {
        content += `New State: ${JSON.stringify(log.newState)}\n`
      }

      if (includeMetadata) {
        content += `IP Address: ${log.metadata.ipAddress}\n`
        content += `User Agent: ${log.metadata.userAgent}\n`
        content += `Session ID: ${log.metadata.sessionId}\n`
      }

      content += `\n${'-'.repeat(40)}\n\n`
    })

    return content
  }

  /**
   * Convenience methods for common audit actions
   */
  static async logUserAction(action: 'USER_CREATED' | 'USER_UPDATED' | 'USER_DELETED' | 'USER_ROLE_CHANGED', userId: string, previousState?: any, newState?: any): Promise<void> {
    await AuditService.logAction({
      action,
      resource: 'user',
      resourceId: userId,
      previousState,
      newState,
      severity: action === 'USER_DELETED' ? 'high' : 'medium'
    })
  }

  static async logProductAction(action: 'PRODUCT_CREATED' | 'PRODUCT_UPDATED' | 'PRODUCT_DELETED', productId: string, previousState?: any, newState?: any): Promise<void> {
    await AuditService.logAction({
      action,
      resource: 'product',
      resourceId: productId,
      previousState,
      newState,
      severity: action === 'PRODUCT_DELETED' ? 'medium' : 'low'
    })
  }

  static async logOrderAction(action: 'ORDER_UPDATED' | 'ORDER_CANCELLED' | 'ORDER_REFUNDED', orderId: string, previousState?: any, newState?: any): Promise<void> {
    await AuditService.logAction({
      action,
      resource: 'order',
      resourceId: orderId,
      previousState,
      newState,
      severity: action === 'ORDER_REFUNDED' ? 'high' : 'medium'
    })
  }

  static async logAdminAccess(action: 'ADMIN_LOGIN' | 'ADMIN_LOGOUT' | 'ADMIN_ACCESS_DENIED', adminId: string): Promise<void> {
    await AuditService.logAction({
      action,
      resource: 'admin',
      resourceId: adminId,
      severity: action === 'ADMIN_ACCESS_DENIED' ? 'high' : 'low'
    })
  }

  static async logBulkOperation(operationId: string, operationType: string, affectedCount: number): Promise<void> {
    await AuditService.logAction({
      action: 'BULK_OPERATION',
      resource: 'bulk_operation',
      resourceId: operationId,
      newState: { operationType, affectedCount },
      severity: affectedCount > 100 ? 'high' : 'medium'
    })
  }

  static async logDataExport(exportType: string, recordCount: number): Promise<void> {
    await AuditService.logAction({
      action: 'DATA_EXPORT',
      resource: 'system',
      resourceId: `export_${Date.now()}`,
      newState: { exportType, recordCount },
      severity: recordCount > 1000 ? 'high' : 'medium'
    })
  }
}

export default AuditService