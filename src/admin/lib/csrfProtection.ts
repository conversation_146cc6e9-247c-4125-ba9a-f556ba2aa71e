/**
 * CSRF Protection Utilities for Admin Endpoints
 * 
 * Provides comprehensive Cross-Site Request Forgery protection
 * for admin endpoints using token-based validation.
 * 
 * Features:
 * - CSRF token generation and validation
 * - Double-submit cookie pattern
 * - Origin header validation
 * - Referer header validation
 * - Custom header requirement
 * 
 * <AUTHOR> Team
 */

import { NextRequest } from 'next/server'
import { createHash, randomBytes } from 'crypto'

export interface CSRFConfig {
  tokenLength: number
  cookieName: string
  headerName: string
  maxAge: number // in seconds
  sameSite: 'strict' | 'lax' | 'none'
  secure: boolean
}

export interface CSRFValidationResult {
  valid: boolean
  reason?: string
  token?: string
}

// Default CSRF configuration
export const DEFAULT_CSRF_CONFIG: CSRFConfig = {
  tokenLength: 32,
  cookieName: 'csrf-token',
  headerName: 'x-csrf-token',
  maxAge: 60 * 60 * 4, // 4 hours
  sameSite: 'strict',
  secure: process.env.NODE_ENV === 'production'
}

/**
 * Generate a cryptographically secure CSRF token
 */
export function generateCSRFToken(length: number = 32): string {
  return randomBytes(length).toString('hex')
}

/**
 * Create a hash of the token for double-submit cookie pattern
 */
export function hashToken(token: string): string {
  return createHash('sha256').update(token).digest('hex')
}

/**
 * Validate CSRF token using double-submit cookie pattern
 */
export function validateCSRFToken(
  headerToken: string | null,
  cookieToken: string | null
): boolean {
  if (!headerToken || !cookieToken) {
    return false
  }

  // Compare the header token with the hashed cookie token
  const hashedHeaderToken = hashToken(headerToken)
  return hashedHeaderToken === cookieToken
}

/**
 * Validate request origin
 */
export function validateOrigin(request: NextRequest, allowedOrigins: string[]): boolean {
  const origin = request.headers.get('origin')
  const referer = request.headers.get('referer')

  // For same-origin requests, origin might be null
  if (!origin && !referer) {
    return false
  }

  // Check origin header
  if (origin && !allowedOrigins.includes(origin)) {
    return false
  }

  // Check referer header as fallback
  if (!origin && referer) {
    const refererOrigin = new URL(referer).origin
    if (!allowedOrigins.includes(refererOrigin)) {
      return false
    }
  }

  return true
}

/**
 * Check if request has required custom header
 */
export function hasCustomHeader(request: NextRequest, headerName: string): boolean {
  return request.headers.has(headerName)
}

/**
 * Comprehensive CSRF validation
 */
export function validateCSRF(
  request: NextRequest,
  config: CSRFConfig = DEFAULT_CSRF_CONFIG
): CSRFValidationResult {
  // Skip CSRF validation for GET, HEAD, OPTIONS requests
  const method = request.method.toUpperCase()
  if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
    return { valid: true }
  }

  // Get tokens from header and cookie
  const headerToken = request.headers.get(config.headerName)
  const cookieToken = request.cookies.get(config.cookieName)?.value

  // Validate CSRF token
  if (!validateCSRFToken(headerToken, cookieToken)) {
    return {
      valid: false,
      reason: 'Invalid or missing CSRF token'
    }
  }

  // Validate origin (for admin endpoints, be strict about origins)
  const allowedOrigins = [
    process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    'https://syndicaps.com',
    'https://www.syndicaps.com'
  ]

  if (!validateOrigin(request, allowedOrigins)) {
    return {
      valid: false,
      reason: 'Invalid request origin'
    }
  }

  // Require custom header for admin endpoints (additional protection)
  if (!hasCustomHeader(request, 'x-requested-with')) {
    return {
      valid: false,
      reason: 'Missing required custom header'
    }
  }

  return { valid: true, token: headerToken || undefined }
}

/**
 * Generate CSRF token and cookie for response
 */
export function generateCSRFResponse(config: CSRFConfig = DEFAULT_CSRF_CONFIG): {
  token: string
  cookieValue: string
  cookieOptions: {
    httpOnly: boolean
    secure: boolean
    sameSite: 'strict' | 'lax' | 'none'
    maxAge: number
    path: string
  }
} {
  const token = generateCSRFToken(config.tokenLength)
  const cookieValue = hashToken(token)

  return {
    token,
    cookieValue,
    cookieOptions: {
      httpOnly: true,
      secure: config.secure,
      sameSite: config.sameSite,
      maxAge: config.maxAge,
      path: '/admin'
    }
  }
}

/**
 * CSRF protection middleware for Next.js API routes
 */
export function createCSRFMiddleware(config: CSRFConfig = DEFAULT_CSRF_CONFIG) {
  return async (request: NextRequest): Promise<Response | null> => {
    const validation = validateCSRF(request, config)

    if (!validation.valid) {
      return new Response(
        JSON.stringify({
          error: 'CSRF validation failed',
          message: validation.reason || 'Invalid CSRF token',
          code: 'CSRF_INVALID'
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    }

    return null // Continue to next middleware/handler
  }
}

/**
 * Add CSRF token to response headers
 */
export function addCSRFHeaders(
  response: Response,
  token: string
): Response {
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers
  })

  newResponse.headers.set('X-CSRF-Token', token)
  return newResponse
}

/**
 * Get CSRF token from request
 */
export function getCSRFToken(request: NextRequest, config: CSRFConfig = DEFAULT_CSRF_CONFIG): string | null {
  return request.headers.get(config.headerName)
}

/**
 * Validate CSRF for admin API routes
 */
export function validateAdminCSRF(request: NextRequest): CSRFValidationResult {
  // Enhanced validation for admin endpoints
  const adminConfig: CSRFConfig = {
    ...DEFAULT_CSRF_CONFIG,
    cookieName: 'admin-csrf-token',
    headerName: 'x-admin-csrf-token',
    sameSite: 'strict',
    secure: true // Always secure for admin
  }

  const result = validateCSRF(request, adminConfig)

  // Additional admin-specific validations
  if (result.valid) {
    // Check for admin session
    const adminAccess = request.cookies.get('admin-access')?.value
    if (adminAccess !== 'true') {
      return {
        valid: false,
        reason: 'Admin session required'
      }
    }

    // Check user agent consistency (basic bot detection)
    const userAgent = request.headers.get('user-agent')
    if (!userAgent || userAgent.length < 10) {
      return {
        valid: false,
        reason: 'Invalid user agent'
      }
    }
  }

  return result
}

/**
 * Generate admin CSRF token and response
 */
export function generateAdminCSRFResponse(): {
  token: string
  cookieValue: string
  cookieOptions: {
    httpOnly: boolean
    secure: boolean
    sameSite: 'strict' | 'lax' | 'none'
    maxAge: number
    path: string
  }
} {
  const adminConfig: CSRFConfig = {
    ...DEFAULT_CSRF_CONFIG,
    cookieName: 'admin-csrf-token',
    headerName: 'x-admin-csrf-token',
    sameSite: 'strict',
    secure: true
  }

  return generateCSRFResponse(adminConfig)
}

/**
 * CSRF token refresh endpoint helper
 */
export function createCSRFRefreshResponse(): Response {
  const { token, cookieValue, cookieOptions } = generateAdminCSRFResponse()

  const response = new Response(
    JSON.stringify({
      success: true,
      token,
      expiresAt: new Date(Date.now() + cookieOptions.maxAge * 1000)
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': token
      }
    }
  )

  // Set CSRF cookie
  const cookieString = `admin-csrf-token=${cookieValue}; HttpOnly; Secure; SameSite=Strict; Max-Age=${cookieOptions.maxAge}; Path=${cookieOptions.path}`
  response.headers.set('Set-Cookie', cookieString)

  return response
}
