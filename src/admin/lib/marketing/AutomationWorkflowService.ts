/**
 * Marketing Automation Workflow Service
 * 
 * Advanced workflow automation and customer journey management
 * Part of Phase 2 Marketing Automation Suite
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export type WorkflowStatus = 'draft' | 'active' | 'paused' | 'archived' | 'error'
export type WorkflowType = 'drip_campaign' | 'welcome_series' | 'abandoned_cart' | 'winback' | 'upsell' | 'lead_nurture' | 'onboarding' | 'retention'
export type TriggerType = 'event' | 'time_based' | 'conditional' | 'manual' | 'api' | 'webhook'
export type ActionType = 'send_email' | 'send_sms' | 'add_tag' | 'remove_tag' | 'update_field' | 'create_task' | 'webhook' | 'wait' | 'condition' | 'segment'
export type ConditionOperator = 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'exists' | 'not_exists' | 'in' | 'not_in'

export interface WorkflowTrigger {
  id: string
  type: TriggerType
  name: string
  config: {
    event?: string
    conditions?: Array<{
      field: string
      operator: ConditionOperator
      value: any
    }>
    schedule?: {
      type: 'immediate' | 'delay' | 'specific_time' | 'recurring'
      delay?: number // in minutes
      time?: string // HH:MM
      days?: string[] // ['monday', 'tuesday', ...]
      timezone?: string
    }
    webhook?: {
      url: string
      method: 'GET' | 'POST' | 'PUT' | 'DELETE'
      headers?: Record<string, string>
      payload?: Record<string, any>
    }
  }
}

export interface WorkflowAction {
  id: string
  type: ActionType
  name: string
  position: { x: number; y: number }
  config: {
    // Email action
    emailTemplate?: string
    emailSubject?: string
    emailContent?: string
    emailPersonalization?: Record<string, string>
    
    // SMS action
    smsTemplate?: string
    smsContent?: string
    
    // Tag/Field actions
    tagName?: string
    fieldName?: string
    fieldValue?: any
    
    // Task action
    taskTitle?: string
    taskDescription?: string
    assigneeId?: string
    dueDate?: Date
    
    // Wait action
    waitDuration?: number // in minutes
    waitUntil?: Date
    
    // Condition action
    conditions?: Array<{
      field: string
      operator: ConditionOperator
      value: any
    }>
    trueAction?: string // action ID
    falseAction?: string // action ID
    
    // Webhook action
    webhook?: {
      url: string
      method: 'GET' | 'POST' | 'PUT' | 'DELETE'
      headers?: Record<string, string>
      payload?: Record<string, any>
      retries?: number
      timeout?: number
    }
    
    // Segment action
    segmentId?: string
    segmentAction?: 'add' | 'remove'
  }
  connections: Array<{
    targetActionId: string
    condition?: 'success' | 'failure' | 'true' | 'false'
    delay?: number // in minutes
  }>
}

export interface AutomationWorkflow {
  id: string
  name: string
  description: string
  type: WorkflowType
  status: WorkflowStatus
  version: number
  isTemplate: boolean
  trigger: WorkflowTrigger
  actions: WorkflowAction[]
  settings: {
    timezone: string
    throttling: {
      enabled: boolean
      maxPerHour?: number
      maxPerDay?: number
    }
    errorHandling: {
      continueOnError: boolean
      retryCount: number
      retryDelay: number // in minutes
      fallbackAction?: string // action ID
    }
    analytics: {
      trackOpens: boolean
      trackClicks: boolean
      trackConversions: boolean
      conversionGoal?: string
    }
    constraints: {
      maxExecutions?: number
      startDate?: Date
      endDate?: Date
      daysOfWeek?: string[]
      timeRange?: {
        start: string // HH:MM
        end: string // HH:MM
      }
    }
  }
  analytics: {
    totalExecutions: number
    successfulExecutions: number
    failedExecutions: number
    averageExecutionTime: number
    conversionRate: number
    totalConversions: number
    revenue: number
    topPerformingAction?: string
    executionHistory: Array<{
      date: Date
      executions: number
      successes: number
      failures: number
      conversions: number
    }>
  }
  metadata: {
    tags: string[]
    category?: string
    author: string
    lastModifiedBy: string
    notes?: string
    estimatedDuration?: number // in minutes
    complexity: 'simple' | 'moderate' | 'complex'
  }
  createdAt: Date
  updatedAt: Date
  lastExecutedAt?: Date
  nextExecutionAt?: Date
}

export interface WorkflowExecution {
  id: string
  workflowId: string
  customerId: string
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  currentAction?: string
  startedAt: Date
  completedAt?: Date
  error?: {
    message: string
    actionId: string
    timestamp: Date
    retryCount: number
  }
  executionLog: Array<{
    actionId: string
    actionName: string
    status: 'started' | 'completed' | 'failed' | 'skipped'
    timestamp: Date
    duration?: number // in milliseconds
    input?: Record<string, any>
    output?: Record<string, any>
    error?: string
  }>
  metadata: {
    triggerData?: Record<string, any>
    customerData?: Record<string, any>
    conversions?: Array<{
      goal: string
      value: number
      timestamp: Date
    }>
  }
}

export interface WorkflowFilters {
  status?: WorkflowStatus[]
  type?: WorkflowType[]
  author?: string[]
  tags?: string[]
  dateFrom?: Date
  dateTo?: Date
  isTemplate?: boolean
  searchQuery?: string
  limit?: number
  offset?: number
  sortBy?: 'created' | 'updated' | 'name' | 'executions' | 'conversion_rate'
  sortOrder?: 'asc' | 'desc'
}

export interface WorkflowTemplate {
  id: string
  name: string
  description: string
  type: WorkflowType
  category: string
  thumbnail?: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedSetupTime: number // in minutes
  workflow: Omit<AutomationWorkflow, 'id' | 'createdAt' | 'updatedAt' | 'analytics'>
  usageCount: number
  rating: number
  tags: string[]
  createdAt: Date
}

export interface WorkflowStats {
  totalWorkflows: number
  activeWorkflows: number
  totalExecutions: number
  averageConversionRate: number
  totalRevenue: number
  topPerformingWorkflows: Array<{
    id: string
    name: string
    executions: number
    conversionRate: number
    revenue: number
  }>
  recentExecutions: Array<{
    workflowId: string
    workflowName: string
    customerId: string
    status: WorkflowExecution['status']
    completedAt: Date
  }>
  errorRate: number
  averageExecutionTime: number
}

/**
 * Marketing Automation Workflow Service
 */
export class AutomationWorkflowService {
  private static instance: AutomationWorkflowService
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/admin/marketing/workflows'
  }

  static getInstance(): AutomationWorkflowService {
    if (!AutomationWorkflowService.instance) {
      AutomationWorkflowService.instance = new AutomationWorkflowService()
    }
    return AutomationWorkflowService.instance
  }

  /**
   * Get workflows with filtering and pagination
   */
  async getWorkflows(filters: WorkflowFilters = {}): Promise<{
    workflows: AutomationWorkflow[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters.status) params.append('status', filters.status.join(','))
      if (filters.type) params.append('type', filters.type.join(','))
      if (filters.author) params.append('author', filters.author.join(','))
      if (filters.tags) params.append('tags', filters.tags.join(','))
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.isTemplate !== undefined) params.append('isTemplate', filters.isTemplate.toString())
      if (filters.searchQuery) params.append('query', filters.searchQuery)
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`${this.baseUrl}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch workflows: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve workflows')
      }

      return {
        workflows: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve workflows:', error)
      throw error
    }
  }

  /**
   * Get specific workflow
   */
  async getWorkflow(workflowId: string): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}/${workflowId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve workflow:', error)
      throw error
    }
  }

  /**
   * Create new workflow
   */
  async createWorkflow(workflowData: {
    name: string
    description: string
    type: WorkflowType
    trigger: WorkflowTrigger
    actions?: WorkflowAction[]
    settings?: Partial<AutomationWorkflow['settings']>
    tags?: string[]
  }): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      })

      if (!response.ok) {
        throw new Error(`Failed to create workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create workflow:', error)
      throw error
    }
  }

  /**
   * Update workflow
   */
  async updateWorkflow(workflowId: string, updates: Partial<AutomationWorkflow>): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}/${workflowId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error(`Failed to update workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update workflow:', error)
      throw error
    }
  }

  /**
   * Start workflow
   */
  async startWorkflow(workflowId: string): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}/${workflowId}/start`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error(`Failed to start workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to start workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to start workflow:', error)
      throw error
    }
  }

  /**
   * Pause workflow
   */
  async pauseWorkflow(workflowId: string): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}/${workflowId}/pause`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error(`Failed to pause workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to pause workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to pause workflow:', error)
      throw error
    }
  }

  /**
   * Test workflow
   */
  async testWorkflow(workflowId: string, testData?: {
    customerId?: string
    triggerData?: Record<string, any>
    dryRun?: boolean
  }): Promise<{
    success: boolean
    executionId?: string
    results: Array<{
      actionId: string
      status: 'success' | 'failure'
      output?: any
      error?: string
    }>
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/${workflowId}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData || {})
      })

      if (!response.ok) {
        throw new Error(`Failed to test workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to test workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to test workflow:', error)
      throw error
    }
  }

  /**
   * Get workflow executions
   */
  async getWorkflowExecutions(workflowId: string, options?: {
    status?: WorkflowExecution['status'][]
    limit?: number
    offset?: number
  }): Promise<{
    executions: WorkflowExecution[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (options?.status) params.append('status', options.status.join(','))
      if (options?.limit) params.append('limit', options.limit.toString())
      if (options?.offset) params.append('page', Math.floor((options.offset || 0) / (options.limit || 20) + 1).toString())

      const response = await fetch(`${this.baseUrl}/${workflowId}/executions?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch executions: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve executions')
      }

      return {
        executions: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve workflow executions:', error)
      throw error
    }
  }

  /**
   * Get workflow templates
   */
  async getWorkflowTemplates(category?: string): Promise<WorkflowTemplate[]> {
    try {
      const params = new URLSearchParams()
      if (category) params.append('category', category)

      const response = await fetch(`${this.baseUrl}/templates?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch templates: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve templates')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve workflow templates:', error)
      throw error
    }
  }

  /**
   * Create workflow from template
   */
  async createFromTemplate(templateId: string, customizations?: {
    name?: string
    description?: string
    settings?: Partial<AutomationWorkflow['settings']>
  }): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}/templates/${templateId}/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(customizations || {})
      })

      if (!response.ok) {
        throw new Error(`Failed to create from template: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create from template')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create workflow from template:', error)
      throw error
    }
  }

  /**
   * Duplicate workflow
   */
  async duplicateWorkflow(workflowId: string, newName: string): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}/${workflowId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name: newName })
      })

      if (!response.ok) {
        throw new Error(`Failed to duplicate workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to duplicate workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to duplicate workflow:', error)
      throw error
    }
  }

  /**
   * Get workflow statistics
   */
  async getWorkflowStats(dateRange?: {
    from: Date
    to: Date
  }): Promise<WorkflowStats> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/stats?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch stats: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve stats')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve workflow statistics:', error)
      throw error
    }
  }

  /**
   * Export workflow
   */
  async exportWorkflow(workflowId: string, format: 'json' | 'yaml'): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/${workflowId}/export?format=${format}`)
      
      if (!response.ok) {
        throw new Error(`Failed to export workflow: ${response.statusText}`)
      }

      return await response.text()
    } catch (error) {
      console.error('Failed to export workflow:', error)
      throw error
    }
  }

  /**
   * Import workflow
   */
  async importWorkflow(workflowData: string, format: 'json' | 'yaml'): Promise<AutomationWorkflow> {
    try {
      const response = await fetch(`${this.baseUrl}/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          data: workflowData,
          format
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to import workflow: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to import workflow')
      }

      return result.data
    } catch (error) {
      console.error('Failed to import workflow:', error)
      throw error
    }
  }
}

export default AutomationWorkflowService