/**
 * Customer Segmentation Service
 * 
 * Advanced customer segmentation and targeting engine
 * Part of Phase 2 Marketing Automation Suite
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export type SegmentType = 'static' | 'dynamic' | 'behavioral' | 'demographic' | 'geographic' | 'psychographic'
export type SegmentStatus = 'active' | 'inactive' | 'archived'
export type RuleOperator = 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'greater_equal' | 'less_equal' | 'in' | 'not_in' | 'between' | 'exists' | 'not_exists'
export type RuleLogic = 'and' | 'or'
export type CustomerTier = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
export type EngagementLevel = 'very_low' | 'low' | 'medium' | 'high' | 'very_high'

export interface SegmentRule {
  id: string
  field: string
  operator: RuleOperator
  value: any
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'array'
  weight?: number // for scoring-based segments
}

export interface SegmentRuleGroup {
  id: string
  logic: RuleLogic
  rules: SegmentRule[]
  subGroups?: SegmentRuleGroup[]
}

export interface CustomerSegment {
  id: string
  name: string
  description: string
  type: SegmentType
  status: SegmentStatus
  color: string
  icon?: string
  ruleGroups: SegmentRuleGroup[]
  analytics: {
    totalCustomers: number
    activeCustomers: number
    averageOrderValue: number
    totalRevenue: number
    conversionRate: number
    engagementScore: number
    churnRisk: number
    lifetimeValue: number
    lastUpdated: Date
  }
  targeting: {
    isTargetable: boolean
    campaignCount: number
    lastCampaignDate?: Date
    suppressionRules: SegmentRule[]
    frequencyCap?: {
      maxEmails: number
      timeWindow: number // in days
    }
  }
  metadata: {
    tags: string[]
    notes?: string
    createdBy: string
    lastModifiedBy: string
    refreshFrequency: 'realtime' | 'hourly' | 'daily' | 'weekly' | 'monthly'
    estimatedSize?: number
    sizeHistory: Array<{
      date: Date
      count: number
    }>
  }
  createdAt: Date
  updatedAt: Date
  lastRefreshAt?: Date
}

export interface CustomerProfile {
  id: string
  userId: string
  email: string
  name: string
  tier: CustomerTier
  demographics: {
    age?: number
    gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
    location?: {
      country: string
      region: string
      city: string
      timezone: string
    }
    language?: string
  }
  behavioral: {
    totalOrders: number
    totalSpent: number
    averageOrderValue: number
    lastOrderDate?: Date
    firstOrderDate?: Date
    daysSinceLastOrder?: number
    favoriteCategories: string[]
    preferredBrands: string[]
    shoppingFrequency: 'rarely' | 'occasionally' | 'regularly' | 'frequently'
    seasonalPatterns: Record<string, number>
  }
  engagement: {
    level: EngagementLevel
    score: number
    emailOpenRate: number
    emailClickRate: number
    websiteVisits: number
    lastActiveDate?: Date
    preferredChannels: string[]
    communicationPreferences: {
      email: boolean
      sms: boolean
      push: boolean
      frequency: 'minimal' | 'weekly' | 'daily'
    }
  }
  lifecycle: {
    stage: 'prospect' | 'new' | 'active' | 'at_risk' | 'dormant' | 'churned' | 'winback'
    acquisitionChannel: string
    acquisitionDate: Date
    churnRisk: number
    predictedLifetimeValue: number
    nextBestAction?: string
  }
  segmentMemberships: Array<{
    segmentId: string
    segmentName: string
    joinedAt: Date
    score?: number
  }>
  customAttributes: Record<string, any>
  updatedAt: Date
}

export interface SegmentFilters {
  type?: SegmentType[]
  status?: SegmentStatus[]
  tags?: string[]
  createdBy?: string[]
  dateFrom?: Date
  dateTo?: Date
  minSize?: number
  maxSize?: number
  searchQuery?: string
  limit?: number
  offset?: number
  sortBy?: 'created' | 'updated' | 'name' | 'size' | 'engagement'
  sortOrder?: 'asc' | 'desc'
}

export interface SegmentAnalytics {
  overview: {
    totalSegments: number
    totalCustomers: number
    activeSegments: number
    averageSegmentSize: number
    topPerformingSegment: string
  }
  performance: Array<{
    segmentId: string
    segmentName: string
    size: number
    growth: number
    conversionRate: number
    revenue: number
    engagementScore: number
  }>
  overlap: Array<{
    segment1: string
    segment2: string
    overlapCount: number
    overlapPercentage: number
  }>
  trends: Array<{
    date: Date
    totalCustomers: number
    newCustomers: number
    churnedCustomers: number
    reactivatedCustomers: number
  }>
}

export interface SegmentationInsight {
  id: string
  type: 'opportunity' | 'risk' | 'trend' | 'anomaly'
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  confidence: number
  recommendedActions: Array<{
    action: string
    priority: 'low' | 'medium' | 'high'
    estimatedImpact: string
  }>
  affectedSegments: string[]
  data: Record<string, any>
  createdAt: Date
}

/**
 * Customer Segmentation Service
 */
export class CustomerSegmentationService {
  private static instance: CustomerSegmentationService
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/admin/marketing/segmentation'
  }

  static getInstance(): CustomerSegmentationService {
    if (!CustomerSegmentationService.instance) {
      CustomerSegmentationService.instance = new CustomerSegmentationService()
    }
    return CustomerSegmentationService.instance
  }

  /**
   * Get segments with filtering and pagination
   */
  async getSegments(filters: SegmentFilters = {}): Promise<{
    segments: CustomerSegment[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters.type) params.append('type', filters.type.join(','))
      if (filters.status) params.append('status', filters.status.join(','))
      if (filters.tags) params.append('tags', filters.tags.join(','))
      if (filters.createdBy) params.append('createdBy', filters.createdBy.join(','))
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.minSize) params.append('minSize', filters.minSize.toString())
      if (filters.maxSize) params.append('maxSize', filters.maxSize.toString())
      if (filters.searchQuery) params.append('query', filters.searchQuery)
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`${this.baseUrl}/segments?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch segments: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve segments')
      }

      return {
        segments: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve segments:', error)
      throw error
    }
  }

  /**
   * Get specific segment
   */
  async getSegment(segmentId: string): Promise<CustomerSegment> {
    try {
      const response = await fetch(`${this.baseUrl}/segments/${segmentId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch segment: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve segment')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve segment:', error)
      throw error
    }
  }

  /**
   * Create new segment
   */
  async createSegment(segmentData: {
    name: string
    description: string
    type: SegmentType
    color: string
    ruleGroups: SegmentRuleGroup[]
    tags?: string[]
    refreshFrequency?: CustomerSegment['metadata']['refreshFrequency']
  }): Promise<CustomerSegment> {
    try {
      const response = await fetch(`${this.baseUrl}/segments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(segmentData)
      })

      if (!response.ok) {
        throw new Error(`Failed to create segment: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create segment')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create segment:', error)
      throw error
    }
  }

  /**
   * Update segment
   */
  async updateSegment(segmentId: string, updates: Partial<CustomerSegment>): Promise<CustomerSegment> {
    try {
      const response = await fetch(`${this.baseUrl}/segments/${segmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error(`Failed to update segment: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update segment')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update segment:', error)
      throw error
    }
  }

  /**
   * Delete segment
   */
  async deleteSegment(segmentId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/segments/${segmentId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error(`Failed to delete segment: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to delete segment')
      }
    } catch (error) {
      console.error('Failed to delete segment:', error)
      throw error
    }
  }

  /**
   * Get segment members
   */
  async getSegmentMembers(segmentId: string, options?: {
    limit?: number
    offset?: number
    includeProfile?: boolean
  }): Promise<{
    customers: CustomerProfile[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (options?.limit) params.append('limit', options.limit.toString())
      if (options?.offset) params.append('page', Math.floor((options.offset || 0) / (options.limit || 20) + 1).toString())
      if (options?.includeProfile) params.append('includeProfile', 'true')

      const response = await fetch(`${this.baseUrl}/segments/${segmentId}/members?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch segment members: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve segment members')
      }

      return {
        customers: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve segment members:', error)
      throw error
    }
  }

  /**
   * Refresh segment
   */
  async refreshSegment(segmentId: string): Promise<{
    success: boolean
    newSize: number
    previousSize: number
    changes: {
      added: number
      removed: number
    }
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/segments/${segmentId}/refresh`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error(`Failed to refresh segment: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to refresh segment')
      }

      return result.data
    } catch (error) {
      console.error('Failed to refresh segment:', error)
      throw error
    }
  }

  /**
   * Preview segment size
   */
  async previewSegment(ruleGroups: SegmentRuleGroup[]): Promise<{
    estimatedSize: number
    sampleMembers: CustomerProfile[]
    validationErrors: string[]
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/segments/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ruleGroups })
      })

      if (!response.ok) {
        throw new Error(`Failed to preview segment: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to preview segment')
      }

      return result.data
    } catch (error) {
      console.error('Failed to preview segment:', error)
      throw error
    }
  }

  /**
   * Get customer profile
   */
  async getCustomerProfile(customerId: string): Promise<CustomerProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/customers/${customerId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch customer profile: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve customer profile')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve customer profile:', error)
      throw error
    }
  }

  /**
   * Update customer profile
   */
  async updateCustomerProfile(customerId: string, updates: Partial<CustomerProfile>): Promise<CustomerProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/customers/${customerId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error(`Failed to update customer profile: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update customer profile')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update customer profile:', error)
      throw error
    }
  }

  /**
   * Get segmentation analytics
   */
  async getSegmentationAnalytics(dateRange?: {
    from: Date
    to: Date
  }): Promise<SegmentAnalytics> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/analytics?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve analytics')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve segmentation analytics:', error)
      throw error
    }
  }

  /**
   * Get AI-powered insights
   */
  async getSegmentationInsights(): Promise<SegmentationInsight[]> {
    try {
      const response = await fetch(`${this.baseUrl}/insights`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch insights: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve insights')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve segmentation insights:', error)
      throw error
    }
  }

  /**
   * Export segment data
   */
  async exportSegment(segmentId: string, format: 'csv' | 'xlsx' | 'json', options?: {
    includeProfile?: boolean
    fields?: string[]
  }): Promise<string> {
    try {
      const params = new URLSearchParams()
      params.append('format', format)
      
      if (options?.includeProfile) params.append('includeProfile', 'true')
      if (options?.fields) params.append('fields', options.fields.join(','))

      const response = await fetch(`${this.baseUrl}/segments/${segmentId}/export?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to export segment: ${response.statusText}`)
      }

      return await response.text()
    } catch (error) {
      console.error('Failed to export segment:', error)
      throw error
    }
  }

  /**
   * Get available segmentation fields
   */
  async getSegmentationFields(): Promise<Array<{
    field: string
    label: string
    type: 'string' | 'number' | 'date' | 'boolean' | 'array'
    category: 'demographic' | 'behavioral' | 'engagement' | 'transaction' | 'custom'
    operators: RuleOperator[]
    examples: any[]
  }>> {
    try {
      const response = await fetch(`${this.baseUrl}/fields`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch fields: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve fields')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve segmentation fields:', error)
      throw error
    }
  }

  /**
   * Create lookalike segment
   */
  async createLookalikeSegment(baseSegmentId: string, options: {
    name: string
    description: string
    similarity: number // 0-100
    maxSize?: number
  }): Promise<CustomerSegment> {
    try {
      const response = await fetch(`${this.baseUrl}/segments/${baseSegmentId}/lookalike`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(options)
      })

      if (!response.ok) {
        throw new Error(`Failed to create lookalike segment: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create lookalike segment')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create lookalike segment:', error)
      throw error
    }
  }
}

export default CustomerSegmentationService