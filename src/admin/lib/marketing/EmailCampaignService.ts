/**
 * Email Campaign Management Service
 * 
 * Comprehensive email marketing automation system
 * Part of Phase 2 Marketing Automation Suite
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export type CampaignStatus = 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused' | 'cancelled' | 'failed'
export type CampaignType = 'newsletter' | 'promotional' | 'transactional' | 'welcome' | 'abandoned_cart' | 'follow_up' | 'survey'
export type EmailTemplateType = 'modern' | 'classic' | 'minimal' | 'product_showcase' | 'announcement' | 'custom'
export type DeliveryStatus = 'pending' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'unsubscribed'

export interface EmailCampaign {
  id: string
  name: string
  subject: string
  previewText?: string
  status: CampaignStatus
  type: CampaignType
  template: EmailTemplateType
  content: {
    html: string
    text: string
    variables: Record<string, any>
    personalizations: Array<{
      field: string
      defaultValue: string
      required: boolean
    }>
  }
  design: {
    templateId: string
    customCSS?: string
    branding: {
      logoUrl?: string
      primaryColor: string
      secondaryColor: string
      fontFamily: string
    }
    layout: {
      headerEnabled: boolean
      footerEnabled: boolean
      socialLinksEnabled: boolean
      unsubscribeEnabled: boolean
    }
  }
  targeting: {
    segmentIds: string[]
    includeRules: Array<{
      field: string
      operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in'
      value: any
    }>
    excludeRules: Array<{
      field: string
      operator: string
      value: any
    }>
    estimatedAudience: number
  }
  scheduling: {
    sendImmediately: boolean
    scheduledAt?: Date
    timezone: string
    sendOptimization: boolean
    abTestConfig?: {
      enabled: boolean
      testType: 'subject' | 'content' | 'send_time'
      variants: Array<{
        id: string
        name: string
        percentage: number
        content?: any
      }>
      winnerCriteria: 'open_rate' | 'click_rate' | 'conversion_rate'
      testDuration: number // in hours
    }
  }
  analytics: {
    totalSent: number
    delivered: number
    opened: number
    clicked: number
    bounced: number
    complained: number
    unsubscribed: number
    conversions: number
    revenue: number
    openRate: number
    clickRate: number
    bounceRate: number
    unsubscribeRate: number
    conversionRate: number
    revenuePerEmail: number
  }
  metadata: {
    tags: string[]
    notes?: string
    createdBy: string
    approvedBy?: string
    approvedAt?: Date
    estimatedSendTime?: number // in minutes
    costEstimate?: number
  }
  createdAt: Date
  updatedAt: Date
  sentAt?: Date
  completedAt?: Date
}

export interface EmailRecipient {
  id: string
  campaignId: string
  userId: string
  email: string
  name: string
  personalizations: Record<string, any>
  status: DeliveryStatus
  sentAt?: Date
  deliveredAt?: Date
  openedAt?: Date
  clickedAt?: Date
  bouncedAt?: Date
  complainedAt?: Date
  unsubscribedAt?: Date
  errorMessage?: string
  trackingData: {
    ipAddress?: string
    userAgent?: string
    location?: {
      country: string
      region: string
      city: string
    }
    device?: {
      type: 'desktop' | 'mobile' | 'tablet'
      os: string
      browser: string
    }
  }
}

export interface CampaignFilters {
  status?: CampaignStatus[]
  type?: CampaignType[]
  createdBy?: string[]
  tags?: string[]
  dateFrom?: Date
  dateTo?: Date
  searchQuery?: string
  limit?: number
  offset?: number
  sortBy?: 'created' | 'updated' | 'sent' | 'open_rate' | 'click_rate' | 'name'
  sortOrder?: 'asc' | 'desc'
}

export interface CampaignStats {
  totalCampaigns: number
  totalSent: number
  totalRevenue: number
  averageOpenRate: number
  averageClickRate: number
  averageConversionRate: number
  byStatus: Record<CampaignStatus, number>
  byType: Record<CampaignType, number>
  topPerformingCampaigns: Array<{
    id: string
    name: string
    openRate: number
    clickRate: number
    revenue: number
  }>
  recentCampaigns: Array<{
    id: string
    name: string
    status: CampaignStatus
    sentAt?: Date
    recipients: number
  }>
  monthlyTrends: Array<{
    month: string
    sent: number
    openRate: number
    clickRate: number
    revenue: number
  }>
}

export interface EmailTemplate {
  id: string
  name: string
  description: string
  category: string
  type: EmailTemplateType
  thumbnail: string
  html: string
  css: string
  variables: Array<{
    name: string
    type: 'text' | 'image' | 'url' | 'color' | 'number'
    defaultValue: any
    required: boolean
  }>
  blocks: Array<{
    id: string
    type: 'header' | 'text' | 'image' | 'button' | 'product' | 'social' | 'footer'
    content: any
    editable: boolean
  }>
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * Email Campaign Management Service
 */
export class EmailCampaignService {
  private static instance: EmailCampaignService
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/admin/marketing/campaigns'
  }

  static getInstance(): EmailCampaignService {
    if (!EmailCampaignService.instance) {
      EmailCampaignService.instance = new EmailCampaignService()
    }
    return EmailCampaignService.instance
  }

  /**
   * Get campaigns with filtering and pagination
   */
  async getCampaigns(filters: CampaignFilters = {}): Promise<{
    campaigns: EmailCampaign[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters.status) params.append('status', filters.status.join(','))
      if (filters.type) params.append('type', filters.type.join(','))
      if (filters.createdBy) params.append('createdBy', filters.createdBy.join(','))
      if (filters.tags) params.append('tags', filters.tags.join(','))
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.searchQuery) params.append('query', filters.searchQuery)
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`${this.baseUrl}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch campaigns: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve campaigns')
      }

      return {
        campaigns: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve campaigns:', error)
      throw error
    }
  }

  /**
   * Get specific campaign
   */
  async getCampaign(campaignId: string): Promise<EmailCampaign> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve campaign:', error)
      throw error
    }
  }

  /**
   * Create new campaign
   */
  async createCampaign(campaignData: {
    name: string
    subject: string
    type: CampaignType
    template: EmailTemplate
    content?: {
      html?: string
      text?: string
      variables?: Record<string, any>
    }
    targeting?: {
      segmentIds?: string[]
      includeRules?: any[]
      excludeRules?: any[]
    }
    scheduling?: {
      sendImmediately?: boolean
      scheduledAt?: Date
      timezone?: string
    }
    tags?: string[]
  }): Promise<EmailCampaign> {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(campaignData)
      })

      if (!response.ok) {
        throw new Error(`Failed to create campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create campaign:', error)
      throw error
    }
  }

  /**
   * Update campaign
   */
  async updateCampaign(campaignId: string, updates: Partial<EmailCampaign>): Promise<EmailCampaign> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error(`Failed to update campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update campaign:', error)
      throw error
    }
  }

  /**
   * Send campaign
   */
  async sendCampaign(campaignId: string, options?: {
    testMode?: boolean
    testEmails?: string[]
    sendTime?: Date
  }): Promise<{
    success: boolean
    estimatedDelivery?: Date
    recipientCount: number
    messageId: string
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(options || {})
      })

      if (!response.ok) {
        throw new Error(`Failed to send campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to send campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to send campaign:', error)
      throw error
    }
  }

  /**
   * Pause/Resume campaign
   */
  async pauseCampaign(campaignId: string): Promise<EmailCampaign> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}/pause`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error(`Failed to pause campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to pause campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to pause campaign:', error)
      throw error
    }
  }

  async resumeCampaign(campaignId: string): Promise<EmailCampaign> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}/resume`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error(`Failed to resume campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to resume campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to resume campaign:', error)
      throw error
    }
  }

  /**
   * Get campaign recipients
   */
  async getCampaignRecipients(campaignId: string, filters?: {
    status?: DeliveryStatus[]
    limit?: number
    offset?: number
  }): Promise<{
    recipients: EmailRecipient[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters?.status) params.append('status', filters.status.join(','))
      if (filters?.limit) params.append('limit', filters.limit.toString())
      if (filters?.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())

      const response = await fetch(`${this.baseUrl}/${campaignId}/recipients?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch recipients: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve recipients')
      }

      return {
        recipients: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve campaign recipients:', error)
      throw error
    }
  }

  /**
   * Get campaign analytics
   */
  async getCampaignAnalytics(campaignId: string, dateRange?: {
    from: Date
    to: Date
  }): Promise<{
    overview: EmailCampaign['analytics']
    timeline: Array<{
      timestamp: Date
      event: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'unsubscribed'
      count: number
    }>
    geoLocation: Array<{
      country: string
      opens: number
      clicks: number
    }>
    devices: Array<{
      type: string
      opens: number
      clicks: number
    }>
    clickHeatmap: Array<{
      url: string
      clicks: number
      uniqueClicks: number
    }>
  }> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/${campaignId}/analytics?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve analytics')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve campaign analytics:', error)
      throw error
    }
  }

  /**
   * Duplicate campaign
   */
  async duplicateCampaign(campaignId: string, newName: string): Promise<EmailCampaign> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name: newName })
      })

      if (!response.ok) {
        throw new Error(`Failed to duplicate campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to duplicate campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to duplicate campaign:', error)
      throw error
    }
  }

  /**
   * Get email templates
   */
  async getEmailTemplates(category?: string): Promise<EmailTemplate[]> {
    try {
      const params = new URLSearchParams()
      if (category) params.append('category', category)

      const response = await fetch(`${this.baseUrl}/templates?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch templates: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve templates')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve email templates:', error)
      throw error
    }
  }

  /**
   * Preview campaign
   */
  async previewCampaign(campaignId: string, recipientData?: Record<string, any>): Promise<{
    html: string
    text: string
    subject: string
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ recipientData: recipientData || {} })
      })

      if (!response.ok) {
        throw new Error(`Failed to preview campaign: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to preview campaign')
      }

      return result.data
    } catch (error) {
      console.error('Failed to preview campaign:', error)
      throw error
    }
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats(dateRange?: {
    from: Date
    to: Date
  }): Promise<CampaignStats> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/stats?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch stats: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve stats')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve campaign statistics:', error)
      throw error
    }
  }

  /**
   * Export campaign data
   */
  async exportCampaignData(campaignId: string, format: 'csv' | 'xlsx' | 'json'): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}/export?format=${format}`)
      
      if (!response.ok) {
        throw new Error(`Failed to export campaign data: ${response.statusText}`)
      }

      return await response.text()
    } catch (error) {
      console.error('Failed to export campaign data:', error)
      throw error
    }
  }

  /**
   * A/B test campaign
   */
  async createABTest(campaignId: string, testConfig: {
    testType: 'subject' | 'content' | 'send_time'
    variants: Array<{
      name: string
      percentage: number
      content?: any
    }>
    winnerCriteria: 'open_rate' | 'click_rate' | 'conversion_rate'
    testDuration: number
  }): Promise<EmailCampaign> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignId}/ab-test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testConfig)
      })

      if (!response.ok) {
        throw new Error(`Failed to create A/B test: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create A/B test')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create A/B test:', error)
      throw error
    }
  }
}

export default EmailCampaignService