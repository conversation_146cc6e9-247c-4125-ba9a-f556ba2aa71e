/**
 * Rate Limiting Utilities for Admin Endpoints
 * 
 * Provides comprehensive rate limiting functionality to protect admin
 * endpoints from brute force attacks and abuse.
 * 
 * Features:
 * - IP-based rate limiting
 * - User-based rate limiting
 * - Sliding window algorithm
 * - Different limits for different endpoint types
 * - Automatic cleanup of expired entries
 * 
 * <AUTHOR> Team
 */

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  skipSuccessfulRequests?: boolean // Don't count successful requests
  skipFailedRequests?: boolean // Don't count failed requests
}

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: Date
  totalHits: number
}

// Rate limit configurations for different endpoint types
export const RATE_LIMIT_CONFIGS = {
  // Authentication endpoints - stricter limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    skipSuccessfulRequests: false
  },
  
  // MFA verification - very strict
  mfa: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 5, // 5 attempts per 5 minutes
    skipSuccessfulRequests: true
  },
  
  // General admin API - moderate limits
  admin: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
    skipSuccessfulRequests: true
  },
  
  // Bulk operations - stricter limits
  bulk: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 10, // 10 bulk operations per 5 minutes
    skipSuccessfulRequests: true
  },
  
  // Data export - very strict
  export: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5, // 5 exports per hour
    skipSuccessfulRequests: true
  }
} as const

// In-memory store for rate limiting (use Redis in production)
class RateLimitStore {
  private store = new Map<string, { count: number; resetTime: number; hits: { timestamp: number; success: boolean }[] }>()
  
  // Clean up expired entries every 5 minutes
  private cleanupInterval = setInterval(() => {
    this.cleanup()
  }, 5 * 60 * 1000)

  private cleanup(): void {
    const now = Date.now()
    for (const [key, data] of this.store.entries()) {
      if (data.resetTime < now) {
        this.store.delete(key)
      }
    }
  }

  get(key: string): { count: number; resetTime: number; hits: { timestamp: number; success: boolean }[] } | undefined {
    const data = this.store.get(key)
    if (data && data.resetTime < Date.now()) {
      this.store.delete(key)
      return undefined
    }
    return data
  }

  set(key: string, count: number, resetTime: number, hits: { timestamp: number; success: boolean }[]): void {
    this.store.set(key, { count, resetTime, hits })
  }

  increment(key: string, windowMs: number, success: boolean = false): { count: number; resetTime: number; hits: { timestamp: number; success: boolean }[] } {
    const now = Date.now()
    const resetTime = now + windowMs
    const existing = this.get(key)

    if (!existing) {
      const newData = { count: 1, resetTime, hits: [{ timestamp: now, success }] }
      this.set(key, 1, resetTime, newData.hits)
      return newData
    }

    // Filter hits within the current window
    const windowStart = now - windowMs
    const validHits = existing.hits.filter(hit => hit.timestamp > windowStart)
    validHits.push({ timestamp: now, success })

    const newData = { count: validHits.length, resetTime: existing.resetTime, hits: validHits }
    this.set(key, newData.count, newData.resetTime, newData.hits)
    return newData
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.store.clear()
  }
}

// Global rate limit store
const rateLimitStore = new RateLimitStore()

/**
 * Generate rate limit key
 */
export function generateRateLimitKey(identifier: string, endpoint: string): string {
  return `ratelimit:${endpoint}:${identifier}`
}

/**
 * Check rate limit for a request
 */
export function checkRateLimit(
  identifier: string,
  endpoint: string,
  config: RateLimitConfig,
  isSuccess: boolean = false
): RateLimitResult {
  const key = generateRateLimitKey(identifier, endpoint)
  
  // Skip counting if configured
  const shouldCount = !(
    (config.skipSuccessfulRequests && isSuccess) ||
    (config.skipFailedRequests && !isSuccess)
  )

  if (!shouldCount) {
    const existing = rateLimitStore.get(key)
    return {
      allowed: true,
      remaining: Math.max(0, config.maxRequests - (existing?.count || 0)),
      resetTime: existing ? new Date(existing.resetTime) : new Date(Date.now() + config.windowMs),
      totalHits: existing?.count || 0
    }
  }

  const data = rateLimitStore.increment(key, config.windowMs, isSuccess)
  const allowed = data.count <= config.maxRequests

  return {
    allowed,
    remaining: Math.max(0, config.maxRequests - data.count),
    resetTime: new Date(data.resetTime),
    totalHits: data.count
  }
}

/**
 * Rate limiting middleware for Next.js API routes
 */
export function createRateLimitMiddleware(
  endpointType: keyof typeof RATE_LIMIT_CONFIGS,
  getIdentifier?: (request: Request) => string
) {
  return async (request: Request): Promise<Response | null> => {
    const config = RATE_LIMIT_CONFIGS[endpointType]
    
    // Get identifier (IP address by default, or custom identifier)
    let identifier: string
    if (getIdentifier) {
      identifier = getIdentifier(request)
    } else {
      // Extract IP address from headers
      const forwarded = request.headers.get('x-forwarded-for')
      const realIP = request.headers.get('x-real-ip')
      identifier = forwarded?.split(',')[0].trim() || realIP || 'unknown'
    }

    // Check rate limit
    const result = checkRateLimit(identifier, endpointType, config)

    if (!result.allowed) {
      // Rate limit exceeded
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          message: `Too many requests. Try again after ${result.resetTime.toISOString()}`,
          retryAfter: Math.ceil((result.resetTime.getTime() - Date.now()) / 1000)
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': result.resetTime.getTime().toString(),
            'Retry-After': Math.ceil((result.resetTime.getTime() - Date.now()) / 1000).toString()
          }
        }
      )
    }

    // Add rate limit headers to successful responses
    return null // Continue to next middleware/handler
  }
}

/**
 * Add rate limit headers to response
 */
export function addRateLimitHeaders(
  response: Response,
  result: RateLimitResult,
  config: RateLimitConfig
): Response {
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers
  })

  newResponse.headers.set('X-RateLimit-Limit', config.maxRequests.toString())
  newResponse.headers.set('X-RateLimit-Remaining', result.remaining.toString())
  newResponse.headers.set('X-RateLimit-Reset', result.resetTime.getTime().toString())

  return newResponse
}

/**
 * Get rate limit status for an identifier
 */
export function getRateLimitStatus(
  identifier: string,
  endpoint: string,
  config: RateLimitConfig
): RateLimitResult {
  const key = generateRateLimitKey(identifier, endpoint)
  const existing = rateLimitStore.get(key)

  if (!existing) {
    return {
      allowed: true,
      remaining: config.maxRequests,
      resetTime: new Date(Date.now() + config.windowMs),
      totalHits: 0
    }
  }

  return {
    allowed: existing.count <= config.maxRequests,
    remaining: Math.max(0, config.maxRequests - existing.count),
    resetTime: new Date(existing.resetTime),
    totalHits: existing.count
  }
}

/**
 * Clear rate limit for an identifier (admin override)
 */
export function clearRateLimit(identifier: string, endpoint: string): boolean {
  const key = generateRateLimitKey(identifier, endpoint)
  return rateLimitStore.store.delete(key)
}

/**
 * Get all rate limit entries (for monitoring)
 */
export function getAllRateLimitEntries(): Array<{
  key: string
  count: number
  resetTime: Date
  endpoint: string
  identifier: string
}> {
  const entries: Array<{
    key: string
    count: number
    resetTime: Date
    endpoint: string
    identifier: string
  }> = []

  for (const [key, data] of rateLimitStore.store.entries()) {
    const [, endpoint, identifier] = key.split(':')
    entries.push({
      key,
      count: data.count,
      resetTime: new Date(data.resetTime),
      endpoint,
      identifier
    })
  }

  return entries.sort((a, b) => b.count - a.count)
}

// Cleanup on module unload
if (typeof process !== 'undefined') {
  process.on('exit', () => {
    rateLimitStore.destroy()
  })
}
