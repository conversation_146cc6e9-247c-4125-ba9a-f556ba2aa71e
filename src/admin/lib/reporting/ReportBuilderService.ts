/**
 * Advanced Report Builder Service
 * 
 * Custom report generation and analytics engine
 * Part of Phase 2 Advanced Reporting System
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export type ReportType = 'standard' | 'custom' | 'dashboard' | 'scheduled' | 'realtime'
export type ReportStatus = 'draft' | 'active' | 'scheduled' | 'archived' | 'processing' | 'failed'
export type DataSource = 'users' | 'orders' | 'products' | 'campaigns' | 'segments' | 'workflows' | 'support' | 'inventory' | 'custom'
export type VisualizationType = 'table' | 'line_chart' | 'bar_chart' | 'pie_chart' | 'donut_chart' | 'area_chart' | 'scatter_plot' | 'heatmap' | 'gauge' | 'kpi_card' | 'funnel' | 'treemap'
export type AggregationType = 'count' | 'sum' | 'average' | 'min' | 'max' | 'median' | 'distinct_count' | 'percentage' | 'growth_rate' | 'custom'
export type FilterOperator = 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'between' | 'in' | 'not_in' | 'is_null' | 'is_not_null'
export type ScheduleFrequency = 'hourly' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'custom'

export interface ReportField {
  id: string
  name: string
  displayName: string
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object'
  source: DataSource
  category: string
  description?: string
  isCalculated: boolean
  calculation?: {
    formula: string
    dependencies: string[]
  }
  formatting?: {
    type: 'currency' | 'percentage' | 'number' | 'date' | 'custom'
    decimals?: number
    prefix?: string
    suffix?: string
    dateFormat?: string
  }
}

export interface ReportFilter {
  id: string
  fieldId: string
  operator: FilterOperator
  value: any
  values?: any[]
  isRequired: boolean
  displayName: string
  defaultValue?: any
}

export interface ReportVisualization {
  id: string
  type: VisualizationType
  title: string
  description?: string
  position: { x: number; y: number; width: number; height: number }
  config: {
    // Data configuration
    dataSource: DataSource
    fields: Array<{
      fieldId: string
      role: 'dimension' | 'measure' | 'filter'
      aggregation?: AggregationType
      sorting?: 'asc' | 'desc'
      alias?: string
    }>
    
    // Chart-specific configuration
    chartConfig?: {
      colors?: string[]
      showLegend?: boolean
      showTooltip?: boolean
      showDataLabels?: boolean
      xAxisTitle?: string
      yAxisTitle?: string
      orientation?: 'horizontal' | 'vertical'
      stacked?: boolean
      showTrendLine?: boolean
      goalLine?: number
      thresholds?: Array<{
        value: number
        color: string
        label?: string
      }>
    }
    
    // Table-specific configuration
    tableConfig?: {
      pagination?: boolean
      pageSize?: number
      sortable?: boolean
      searchable?: boolean
      exportable?: boolean
      columnWidths?: Record<string, number>
      conditionalFormatting?: Array<{
        fieldId: string
        condition: {
          operator: FilterOperator
          value: any
        }
        style: {
          backgroundColor?: string
          textColor?: string
          fontWeight?: 'bold' | 'normal'
        }
      }>
    }
    
    // KPI-specific configuration
    kpiConfig?: {
      primaryMetric: string
      comparison?: {
        type: 'previous_period' | 'target' | 'custom'
        value?: number
        label?: string
      }
      trend?: {
        enabled: boolean
        period: 'day' | 'week' | 'month'
      }
      formatting?: {
        showAsPercentage?: boolean
        showGrowthIcon?: boolean
        invertColors?: boolean
      }
    }
  }
  filters: ReportFilter[]
}

export interface CustomReport {
  id: string
  name: string
  description: string
  type: ReportType
  status: ReportStatus
  category: string
  tags: string[]
  visualizations: ReportVisualization[]
  globalFilters: ReportFilter[]
  schedule?: {
    frequency: ScheduleFrequency
    time?: string // HH:MM
    timezone: string
    days?: string[] // for weekly
    dayOfMonth?: number // for monthly
    enabled: boolean
    recipients: Array<{
      email: string
      format: 'pdf' | 'excel' | 'csv' | 'png'
    }>
    lastRun?: Date
    nextRun?: Date
  }
  permissions: {
    viewers: string[]
    editors: string[]
    isPublic: boolean
    departments: string[]
  }
  analytics: {
    views: number
    lastViewed: Date
    averageViewTime: number
    exports: number
    shares: number
    ratings: Array<{
      userId: string
      rating: number
      comment?: string
      timestamp: Date
    }>
    averageRating: number
  }
  metadata: {
    author: string
    lastModifiedBy: string
    version: number
    changeLog: Array<{
      version: number
      changes: string
      author: string
      timestamp: Date
    }>
    complexity: 'simple' | 'moderate' | 'complex'
    estimatedRunTime: number // in seconds
    dataFreshness: Date
  }
  createdAt: Date
  updatedAt: Date
  lastRunAt?: Date
}

export interface ReportExecution {
  id: string
  reportId: string
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number // 0-100
  startedAt: Date
  completedAt?: Date
  duration?: number // in milliseconds
  error?: {
    message: string
    code: string
    details?: Record<string, any>
  }
  results?: {
    data: Record<string, any>[]
    metadata: {
      totalRows: number
      executionTime: number
      dataFreshness: Date
      queryPlan?: string
    }
  }
  triggeredBy: {
    type: 'manual' | 'scheduled' | 'api'
    userId?: string
    source?: string
  }
}

export interface ReportTemplate {
  id: string
  name: string
  description: string
  category: string
  thumbnail?: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedSetupTime: number // in minutes
  useCases: string[]
  report: Omit<CustomReport, 'id' | 'createdAt' | 'updatedAt' | 'analytics'>
  usageCount: number
  rating: number
  tags: string[]
  createdAt: Date
}

export interface ReportFilters {
  type?: ReportType[]
  status?: ReportStatus[]
  category?: string[]
  author?: string[]
  tags?: string[]
  dateFrom?: Date
  dateTo?: Date
  searchQuery?: string
  limit?: number
  offset?: number
  sortBy?: 'created' | 'updated' | 'name' | 'views' | 'rating'
  sortOrder?: 'asc' | 'desc'
}

export interface ReportingStats {
  totalReports: number
  activeReports: number
  scheduledReports: number
  totalViews: number
  averageRating: number
  topReports: Array<{
    id: string
    name: string
    views: number
    rating: number
  }>
  recentActivity: Array<{
    type: 'created' | 'viewed' | 'exported' | 'scheduled'
    reportId: string
    reportName: string
    userId: string
    timestamp: Date
  }>
  categoryBreakdown: Record<string, number>
  usageMetrics: {
    dailyViews: Array<{
      date: Date
      views: number
      exports: number
    }>
    popularVisualizations: Record<VisualizationType, number>
    averageReportComplexity: string
  }
}

/**
 * Advanced Report Builder Service
 */
export class ReportBuilderService {
  private static instance: ReportBuilderService
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/admin/reporting'
  }

  static getInstance(): ReportBuilderService {
    if (!ReportBuilderService.instance) {
      ReportBuilderService.instance = new ReportBuilderService()
    }
    return ReportBuilderService.instance
  }

  /**
   * Get available data sources and fields
   */
  async getDataSources(): Promise<Array<{
    source: DataSource
    name: string
    description: string
    fields: ReportField[]
    sampleData?: Record<string, any>[]
  }>> {
    try {
      const response = await fetch(`${this.baseUrl}/data-sources`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch data sources: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve data sources')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve data sources:', error)
      throw error
    }
  }

  /**
   * Get reports with filtering and pagination
   */
  async getReports(filters: ReportFilters = {}): Promise<{
    reports: CustomReport[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters.type) params.append('type', filters.type.join(','))
      if (filters.status) params.append('status', filters.status.join(','))
      if (filters.category) params.append('category', filters.category.join(','))
      if (filters.author) params.append('author', filters.author.join(','))
      if (filters.tags) params.append('tags', filters.tags.join(','))
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.searchQuery) params.append('query', filters.searchQuery)
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`${this.baseUrl}/reports?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch reports: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve reports')
      }

      return {
        reports: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve reports:', error)
      throw error
    }
  }

  /**
   * Get specific report
   */
  async getReport(reportId: string): Promise<CustomReport> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/${reportId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch report: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve report')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve report:', error)
      throw error
    }
  }

  /**
   * Create new report
   */
  async createReport(reportData: {
    name: string
    description: string
    type: ReportType
    category: string
    visualizations?: ReportVisualization[]
    globalFilters?: ReportFilter[]
    tags?: string[]
  }): Promise<CustomReport> {
    try {
      const response = await fetch(`${this.baseUrl}/reports`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reportData)
      })

      if (!response.ok) {
        throw new Error(`Failed to create report: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create report')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create report:', error)
      throw error
    }
  }

  /**
   * Update report
   */
  async updateReport(reportId: string, updates: Partial<CustomReport>): Promise<CustomReport> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/${reportId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error(`Failed to update report: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update report')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update report:', error)
      throw error
    }
  }

  /**
   * Execute report
   */
  async executeReport(reportId: string, parameters?: Record<string, any>): Promise<ReportExecution> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/${reportId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ parameters: parameters || {} })
      })

      if (!response.ok) {
        throw new Error(`Failed to execute report: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to execute report')
      }

      return result.data
    } catch (error) {
      console.error('Failed to execute report:', error)
      throw error
    }
  }

  /**
   * Get report execution status
   */
  async getExecutionStatus(executionId: string): Promise<ReportExecution> {
    try {
      const response = await fetch(`${this.baseUrl}/executions/${executionId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch execution status: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve execution status')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve execution status:', error)
      throw error
    }
  }

  /**
   * Preview report data
   */
  async previewReportData(config: {
    dataSource: DataSource
    fields: Array<{
      fieldId: string
      aggregation?: AggregationType
    }>
    filters?: ReportFilter[]
    limit?: number
  }): Promise<{
    data: Record<string, any>[]
    totalRows: number
    executionTime: number
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      if (!response.ok) {
        throw new Error(`Failed to preview data: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to preview data')
      }

      return result.data
    } catch (error) {
      console.error('Failed to preview report data:', error)
      throw error
    }
  }

  /**
   * Export report
   */
  async exportReport(reportId: string, format: 'pdf' | 'excel' | 'csv' | 'png', options?: {
    parameters?: Record<string, any>
    includeCharts?: boolean
    pageSize?: 'A4' | 'letter' | 'legal'
    orientation?: 'portrait' | 'landscape'
  }): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/${reportId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          format,
          options: options || {}
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to export report: ${response.statusText}`)
      }

      return await response.text()
    } catch (error) {
      console.error('Failed to export report:', error)
      throw error
    }
  }

  /**
   * Get report templates
   */
  async getReportTemplates(category?: string): Promise<ReportTemplate[]> {
    try {
      const params = new URLSearchParams()
      if (category) params.append('category', category)

      const response = await fetch(`${this.baseUrl}/templates?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch templates: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve templates')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve report templates:', error)
      throw error
    }
  }

  /**
   * Create report from template
   */
  async createFromTemplate(templateId: string, customizations?: {
    name?: string
    description?: string
    filters?: ReportFilter[]
  }): Promise<CustomReport> {
    try {
      const response = await fetch(`${this.baseUrl}/templates/${templateId}/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(customizations || {})
      })

      if (!response.ok) {
        throw new Error(`Failed to create from template: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create from template')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create report from template:', error)
      throw error
    }
  }

  /**
   * Schedule report
   */
  async scheduleReport(reportId: string, schedule: CustomReport['schedule']): Promise<CustomReport> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/${reportId}/schedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(schedule)
      })

      if (!response.ok) {
        throw new Error(`Failed to schedule report: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to schedule report')
      }

      return result.data
    } catch (error) {
      console.error('Failed to schedule report:', error)
      throw error
    }
  }

  /**
   * Get reporting statistics
   */
  async getReportingStats(dateRange?: {
    from: Date
    to: Date
  }): Promise<ReportingStats> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/stats?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch stats: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve stats')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve reporting statistics:', error)
      throw error
    }
  }

  /**
   * Duplicate report
   */
  async duplicateReport(reportId: string, newName: string): Promise<CustomReport> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/${reportId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name: newName })
      })

      if (!response.ok) {
        throw new Error(`Failed to duplicate report: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to duplicate report')
      }

      return result.data
    } catch (error) {
      console.error('Failed to duplicate report:', error)
      throw error
    }
  }

  /**
   * Share report
   */
  async shareReport(reportId: string, shareConfig: {
    recipients: string[]
    message?: string
    permissions: 'view' | 'edit'
    expiresAt?: Date
  }): Promise<{
    shareId: string
    shareUrl: string
    expiresAt?: Date
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/${reportId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(shareConfig)
      })

      if (!response.ok) {
        throw new Error(`Failed to share report: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to share report')
      }

      return result.data
    } catch (error) {
      console.error('Failed to share report:', error)
      throw error
    }
  }
}

export default ReportBuilderService