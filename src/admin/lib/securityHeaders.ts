/**
 * Security Headers Utilities for Admin API Routes
 * 
 * Provides comprehensive security header management for admin
 * API endpoints to protect against various security threats.
 * 
 * Features:
 * - Content Security Policy (CSP)
 * - HTTP Strict Transport Security (HSTS)
 * - X-Frame-Options protection
 * - Content type sniffing protection
 * - XSS protection
 * - Referrer policy
 * - Permissions policy
 * 
 * <AUTHOR> Team
 */

export interface SecurityHeadersConfig {
  enableCSP: boolean
  enableHSTS: boolean
  enableFrameOptions: boolean
  enableContentTypeOptions: boolean
  enableXSSProtection: boolean
  enableReferrerPolicy: boolean
  enablePermissionsPolicy: boolean
  customHeaders?: Record<string, string>
}

export const DEFAULT_SECURITY_CONFIG: SecurityHeadersConfig = {
  enableCSP: true,
  enableHSTS: true,
  enableFrameOptions: true,
  enableContentTypeOptions: true,
  enableXSSProtection: true,
  enableReferrerPolicy: true,
  enablePermissionsPolicy: true
}

/**
 * Generate Content Security Policy header value
 */
export function generateCSP(isAdmin: boolean = false): string {
  const baseDirectives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "font-src 'self' data:",
    "img-src 'self' data: https: blob:",
    "connect-src 'self'",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ]

  if (isAdmin) {
    // Stricter CSP for admin routes
    return [
      "default-src 'self'",
      "script-src 'self'", // No unsafe-inline for admin
      "style-src 'self' 'unsafe-inline'", // Allow inline styles for admin UI
      "font-src 'self'",
      "img-src 'self' data:",
      "connect-src 'self'",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "object-src 'none'",
      "media-src 'none'",
      "child-src 'none'",
      "worker-src 'none'",
      "manifest-src 'none'"
    ].join('; ')
  }

  return baseDirectives.join('; ')
}

/**
 * Generate Permissions Policy header value
 */
export function generatePermissionsPolicy(isAdmin: boolean = false): string {
  const basePermissions = [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
    'ambient-light-sensor=()',
    'autoplay=()',
    'encrypted-media=()',
    'fullscreen=(self)',
    'picture-in-picture=()'
  ]

  if (isAdmin) {
    // Even stricter permissions for admin
    return [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()',
      'magnetometer=()',
      'gyroscope=()',
      'accelerometer=()',
      'ambient-light-sensor=()',
      'autoplay=()',
      'encrypted-media=()',
      'fullscreen=()',
      'picture-in-picture=()',
      'web-share=()',
      'clipboard-read=()',
      'clipboard-write=(self)'
    ].join(', ')
  }

  return basePermissions.join(', ')
}

/**
 * Apply security headers to a Response object
 */
export function applySecurityHeaders(
  response: Response,
  config: SecurityHeadersConfig = DEFAULT_SECURITY_CONFIG,
  isAdmin: boolean = false
): Response {
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers
  })

  // X-Frame-Options
  if (config.enableFrameOptions) {
    newResponse.headers.set('X-Frame-Options', 'DENY')
  }

  // X-Content-Type-Options
  if (config.enableContentTypeOptions) {
    newResponse.headers.set('X-Content-Type-Options', 'nosniff')
  }

  // X-XSS-Protection
  if (config.enableXSSProtection) {
    newResponse.headers.set('X-XSS-Protection', '1; mode=block')
  }

  // Referrer Policy
  if (config.enableReferrerPolicy) {
    newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  }

  // Content Security Policy
  if (config.enableCSP) {
    newResponse.headers.set('Content-Security-Policy', generateCSP(isAdmin))
  }

  // Permissions Policy
  if (config.enablePermissionsPolicy) {
    newResponse.headers.set('Permissions-Policy', generatePermissionsPolicy(isAdmin))
  }

  // HTTP Strict Transport Security (only in production)
  if (config.enableHSTS && process.env.NODE_ENV === 'production') {
    newResponse.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  }

  // Admin-specific headers
  if (isAdmin) {
    newResponse.headers.set('X-Admin-Response', 'true')
    newResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    newResponse.headers.set('Pragma', 'no-cache')
    newResponse.headers.set('Expires', '0')
    newResponse.headers.set('Surrogate-Control', 'no-store')
    
    // Additional security headers for admin
    newResponse.headers.set('X-Permitted-Cross-Domain-Policies', 'none')
    newResponse.headers.set('Cross-Origin-Embedder-Policy', 'require-corp')
    newResponse.headers.set('Cross-Origin-Opener-Policy', 'same-origin')
    newResponse.headers.set('Cross-Origin-Resource-Policy', 'same-origin')
  }

  // Custom headers
  if (config.customHeaders) {
    for (const [key, value] of Object.entries(config.customHeaders)) {
      newResponse.headers.set(key, value)
    }
  }

  return newResponse
}

/**
 * Create security headers middleware for Next.js API routes
 */
export function createSecurityHeadersMiddleware(
  config: SecurityHeadersConfig = DEFAULT_SECURITY_CONFIG,
  isAdmin: boolean = false
) {
  return (response: Response): Response => {
    return applySecurityHeaders(response, config, isAdmin)
  }
}

/**
 * Security headers for admin API responses
 */
export function addAdminSecurityHeaders(response: Response): Response {
  return applySecurityHeaders(response, DEFAULT_SECURITY_CONFIG, true)
}

/**
 * Validate security headers in request
 */
export function validateRequestSecurityHeaders(request: Request): {
  valid: boolean
  issues: string[]
} {
  const issues: string[] = []

  // Check for required headers in admin requests
  const userAgent = request.headers.get('user-agent')
  if (!userAgent || userAgent.length < 10) {
    issues.push('Invalid or missing User-Agent header')
  }

  // Check for suspicious headers
  const suspiciousHeaders = [
    'x-forwarded-host',
    'x-original-host',
    'x-rewrite-url'
  ]

  for (const header of suspiciousHeaders) {
    if (request.headers.has(header)) {
      issues.push(`Suspicious header detected: ${header}`)
    }
  }

  // Check Content-Type for POST/PUT requests
  const method = request.method.toUpperCase()
  if (['POST', 'PUT', 'PATCH'].includes(method)) {
    const contentType = request.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      issues.push('Invalid or missing Content-Type header for data requests')
    }
  }

  return {
    valid: issues.length === 0,
    issues
  }
}

/**
 * Generate security report for monitoring
 */
export function generateSecurityReport(request: Request): {
  timestamp: Date
  method: string
  url: string
  userAgent: string
  origin?: string
  referer?: string
  securityHeaders: Record<string, string>
  securityScore: number
} {
  const securityHeaders: Record<string, string> = {}
  const securityHeaderNames = [
    'x-frame-options',
    'x-content-type-options',
    'x-xss-protection',
    'strict-transport-security',
    'content-security-policy',
    'referrer-policy',
    'permissions-policy'
  ]

  for (const headerName of securityHeaderNames) {
    const value = request.headers.get(headerName)
    if (value) {
      securityHeaders[headerName] = value
    }
  }

  // Calculate security score (0-100)
  const maxScore = securityHeaderNames.length
  const presentHeaders = Object.keys(securityHeaders).length
  const securityScore = Math.round((presentHeaders / maxScore) * 100)

  return {
    timestamp: new Date(),
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent') || 'unknown',
    origin: request.headers.get('origin') || undefined,
    referer: request.headers.get('referer') || undefined,
    securityHeaders,
    securityScore
  }
}

/**
 * Check if request is from a secure context
 */
export function isSecureContext(request: Request): boolean {
  const url = new URL(request.url)
  const isHTTPS = url.protocol === 'https:'
  const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1'
  
  // Secure if HTTPS or localhost (for development)
  return isHTTPS || isLocalhost
}

/**
 * Enforce HTTPS for admin routes
 */
export function enforceHTTPS(request: Request): Response | null {
  if (!isSecureContext(request) && process.env.NODE_ENV === 'production') {
    const url = new URL(request.url)
    url.protocol = 'https:'
    
    return new Response(null, {
      status: 301,
      headers: {
        'Location': url.toString(),
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
      }
    })
  }
  
  return null
}
