/**
 * Session Management Utilities for Admin Accounts
 * 
 * Provides comprehensive session management functionality including
 * automatic expiration, concurrent session limits, and security monitoring.
 * 
 * Features:
 * - Session creation and validation
 * - Automatic session expiration
 * - Concurrent session limits
 * - Session activity tracking
 * - Security monitoring
 * - Session cleanup
 * 
 * <AUTHOR> Team
 */

export interface AdminSession {
  sessionId: string
  adminId: string
  adminEmail: string
  adminRole: 'admin' | 'superadmin'
  createdAt: Date
  lastActivity: Date
  expiresAt: Date
  ipAddress: string
  userAgent: string
  isActive: boolean
  mfaVerified: boolean
  mfaVerifiedAt?: Date
}

export interface SessionConfig {
  maxAge: number // Session duration in milliseconds
  maxConcurrentSessions: number // Maximum concurrent sessions per admin
  inactivityTimeout: number // Inactivity timeout in milliseconds
  mfaTimeout: number // MFA verification timeout in milliseconds
  cleanupInterval: number // Cleanup interval in milliseconds
}

export const DEFAULT_SESSION_CONFIG: SessionConfig = {
  maxAge: 4 * 60 * 60 * 1000, // 4 hours
  maxConcurrentSessions: 3, // 3 concurrent sessions
  inactivityTimeout: 30 * 60 * 1000, // 30 minutes inactivity
  mfaTimeout: 4 * 60 * 60 * 1000, // 4 hours MFA validity
  cleanupInterval: 15 * 60 * 1000 // 15 minutes cleanup
}

// In-memory session store (use Redis in production)
class SessionStore {
  private sessions = new Map<string, AdminSession>()
  private adminSessions = new Map<string, Set<string>>() // adminId -> sessionIds
  private cleanupInterval: NodeJS.Timeout

  constructor(config: SessionConfig = DEFAULT_SESSION_CONFIG) {
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, config.cleanupInterval)
  }

  /**
   * Create a new admin session
   */
  createSession(
    adminId: string,
    adminEmail: string,
    adminRole: 'admin' | 'superadmin',
    ipAddress: string,
    userAgent: string,
    config: SessionConfig = DEFAULT_SESSION_CONFIG
  ): AdminSession {
    const now = new Date()
    const sessionId = `admin_session_${adminId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    
    const session: AdminSession = {
      sessionId,
      adminId,
      adminEmail,
      adminRole,
      createdAt: now,
      lastActivity: now,
      expiresAt: new Date(now.getTime() + config.maxAge),
      ipAddress,
      userAgent,
      isActive: true,
      mfaVerified: false
    }

    // Check concurrent session limit
    this.enforceConcurrentSessionLimit(adminId, config.maxConcurrentSessions)

    // Store session
    this.sessions.set(sessionId, session)
    
    // Track admin sessions
    if (!this.adminSessions.has(adminId)) {
      this.adminSessions.set(adminId, new Set())
    }
    this.adminSessions.get(adminId)!.add(sessionId)

    console.log(`🔐 New admin session created: ${sessionId} for ${adminEmail}`)
    return session
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): AdminSession | null {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    // Check if session is expired
    if (this.isSessionExpired(session)) {
      this.removeSession(sessionId)
      return null
    }

    return session
  }

  /**
   * Update session activity
   */
  updateActivity(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session || !session.isActive) return false

    session.lastActivity = new Date()
    console.log(`📱 Session activity updated: ${sessionId}`)
    return true
  }

  /**
   * Set MFA verification status
   */
  setMFAVerified(sessionId: string, verified: boolean): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    session.mfaVerified = verified
    session.mfaVerifiedAt = verified ? new Date() : undefined
    
    console.log(`🔐 MFA verification ${verified ? 'set' : 'cleared'} for session: ${sessionId}`)
    return true
  }

  /**
   * Check if session is expired
   */
  isSessionExpired(session: AdminSession, config: SessionConfig = DEFAULT_SESSION_CONFIG): boolean {
    const now = new Date()
    
    // Check absolute expiration
    if (now > session.expiresAt) {
      return true
    }

    // Check inactivity timeout
    const inactivityTime = now.getTime() - session.lastActivity.getTime()
    if (inactivityTime > config.inactivityTimeout) {
      return true
    }

    // Check MFA timeout
    if (session.mfaVerified && session.mfaVerifiedAt) {
      const mfaTime = now.getTime() - session.mfaVerifiedAt.getTime()
      if (mfaTime > config.mfaTimeout) {
        return true
      }
    }

    return false
  }

  /**
   * Remove session
   */
  removeSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    // Remove from sessions
    this.sessions.delete(sessionId)

    // Remove from admin sessions tracking
    const adminSessions = this.adminSessions.get(session.adminId)
    if (adminSessions) {
      adminSessions.delete(sessionId)
      if (adminSessions.size === 0) {
        this.adminSessions.delete(session.adminId)
      }
    }

    console.log(`🗑️ Session removed: ${sessionId}`)
    return true
  }

  /**
   * Get all sessions for an admin
   */
  getAdminSessions(adminId: string): AdminSession[] {
    const sessionIds = this.adminSessions.get(adminId)
    if (!sessionIds) return []

    const sessions: AdminSession[] = []
    for (const sessionId of sessionIds) {
      const session = this.getSession(sessionId)
      if (session) {
        sessions.push(session)
      }
    }

    return sessions
  }

  /**
   * Enforce concurrent session limit
   */
  private enforceConcurrentSessionLimit(adminId: string, maxSessions: number): void {
    const sessions = this.getAdminSessions(adminId)
    
    if (sessions.length >= maxSessions) {
      // Remove oldest sessions
      const sortedSessions = sessions.sort((a, b) => a.lastActivity.getTime() - b.lastActivity.getTime())
      const sessionsToRemove = sortedSessions.slice(0, sessions.length - maxSessions + 1)
      
      for (const session of sessionsToRemove) {
        this.removeSession(session.sessionId)
        console.log(`⚠️ Session removed due to concurrent limit: ${session.sessionId}`)
      }
    }
  }

  /**
   * Cleanup expired sessions
   */
  private cleanup(): void {
    let cleanedCount = 0
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (this.isSessionExpired(session)) {
        this.removeSession(sessionId)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired sessions`)
    }
  }

  /**
   * Get session statistics
   */
  getStats(): {
    totalSessions: number
    activeSessions: number
    uniqueAdmins: number
    averageSessionAge: number
    mfaVerifiedSessions: number
  } {
    const now = new Date()
    let activeSessions = 0
    let totalSessionAge = 0
    let mfaVerifiedSessions = 0

    for (const session of this.sessions.values()) {
      if (session.isActive && !this.isSessionExpired(session)) {
        activeSessions++
        totalSessionAge += now.getTime() - session.createdAt.getTime()
        
        if (session.mfaVerified) {
          mfaVerifiedSessions++
        }
      }
    }

    return {
      totalSessions: this.sessions.size,
      activeSessions,
      uniqueAdmins: this.adminSessions.size,
      averageSessionAge: activeSessions > 0 ? totalSessionAge / activeSessions : 0,
      mfaVerifiedSessions
    }
  }

  /**
   * Add a session directly (for development/testing)
   */
  addSession(session: AdminSession): void {
    this.sessions.set(session.sessionId, session)

    // Add to admin sessions map
    if (!this.adminSessions.has(session.adminId)) {
      this.adminSessions.set(session.adminId, new Set())
    }
    this.adminSessions.get(session.adminId)!.add(session.sessionId)
  }

  /**
   * Destroy all sessions for an admin
   */
  destroyAdminSessions(adminId: string): number {
    const sessionIds = this.adminSessions.get(adminId)
    if (!sessionIds) return 0

    let destroyedCount = 0
    for (const sessionId of Array.from(sessionIds)) {
      if (this.removeSession(sessionId)) {
        destroyedCount++
      }
    }

    console.log(`🗑️ Destroyed ${destroyedCount} sessions for admin: ${adminId}`)
    return destroyedCount
  }

  /**
   * Destroy the session store
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.sessions.clear()
    this.adminSessions.clear()
  }
}

// Global session store
const sessionStore = new SessionStore()

/**
 * Create a new admin session
 */
export function createAdminSession(
  adminId: string,
  adminEmail: string,
  adminRole: 'admin' | 'superadmin',
  ipAddress: string,
  userAgent: string,
  config?: SessionConfig
): AdminSession {
  return sessionStore.createSession(adminId, adminEmail, adminRole, ipAddress, userAgent, config)
}

/**
 * Validate admin session with fallback for development
 */
export function validateAdminSession(sessionId: string): AdminSession | null {
  const session = sessionStore.getSession(sessionId)
  if (session) {
    sessionStore.updateActivity(sessionId)
    return session
  }

  // In development, if session is not found but sessionId looks valid,
  // create a temporary session to avoid constant redirects
  if (process.env.NODE_ENV === 'development' && sessionId && sessionId.startsWith('admin_session_')) {
    console.log('🔧 Development mode: Creating temporary session for', sessionId)

    // Extract admin ID from session ID format: admin_session_{adminId}_{timestamp}_{random}
    const parts = sessionId.split('_')
    if (parts.length >= 3) {
      const adminId = parts[2]

      // Create a temporary session
      const tempSession: AdminSession = {
        sessionId,
        adminId,
        adminEmail: '<EMAIL>',
        adminRole: 'superadmin',
        createdAt: new Date(),
        lastActivity: new Date(),
        expiresAt: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours
        ipAddress: 'development',
        userAgent: 'development',
        isActive: true,
        mfaVerified: true,
        mfaVerifiedAt: new Date()
      }

      // Store the temporary session
      sessionStore.addSession(tempSession)
      console.log('✅ Temporary development session created:', sessionId)

      return tempSession
    }
  }

  return null
}

/**
 * Update session activity
 */
export function updateSessionActivity(sessionId: string): boolean {
  return sessionStore.updateActivity(sessionId)
}

/**
 * Set MFA verification for session
 */
export function setSessionMFAVerified(sessionId: string, verified: boolean): boolean {
  return sessionStore.setMFAVerified(sessionId, verified)
}

/**
 * Remove admin session
 */
export function removeAdminSession(sessionId: string): boolean {
  return sessionStore.removeSession(sessionId)
}

/**
 * Get all sessions for admin
 */
export function getAdminSessions(adminId: string): AdminSession[] {
  return sessionStore.getAdminSessions(adminId)
}

/**
 * Get session statistics
 */
export function getSessionStats() {
  return sessionStore.getStats()
}

/**
 * Destroy all sessions for admin
 */
export function destroyAdminSessions(adminId: string): number {
  return sessionStore.destroyAdminSessions(adminId)
}

// Cleanup on module unload (only in Node.js environment, not Edge Runtime)
if (typeof process !== 'undefined' && process.on && typeof process.on === 'function') {
  try {
    process.on('exit', () => {
      sessionStore.destroy()
    })
  } catch (error) {
    // Silently fail in Edge Runtime environments
    console.warn('Process cleanup handlers not available in Edge Runtime')
  }
}
