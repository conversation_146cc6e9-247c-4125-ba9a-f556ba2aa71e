/**
 * Knowledge Base Service
 * 
 * Comprehensive knowledge base management for customer support
 * Part of Phase 2 Customer Support Integration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export type ArticleStatus = 'draft' | 'published' | 'archived' | 'under_review'
export type ArticleType = 'faq' | 'tutorial' | 'troubleshooting' | 'policy' | 'announcement'
export type ContentFormat = 'markdown' | 'html' | 'plain_text'

export interface KnowledgeBaseArticle {
  id: string
  title: string
  slug: string
  content: string
  contentFormat: ContentFormat
  excerpt: string
  status: ArticleStatus
  type: ArticleType
  category: string
  subcategory?: string
  tags: string[]
  author: {
    id: string
    name: string
    email: string
  }
  reviewer?: {
    id: string
    name: string
    email: string
    reviewedAt: Date
  }
  metadata: {
    difficulty?: 'beginner' | 'intermediate' | 'advanced'
    estimatedReadTime?: number // in minutes
    lastUpdated?: Date
    version?: string
    relatedArticles?: string[]
    attachments?: Array<{
      id: string
      filename: string
      url: string
      size: number
      type: string
    }>
  }
  seo: {
    metaTitle?: string
    metaDescription?: string
    keywords?: string[]
    canonicalUrl?: string
  }
  analytics: {
    views: number
    uniqueViews: number
    helpfulVotes: number
    unhelpfulVotes: number
    averageRating: number
    totalRatings: number
    lastViewed?: Date
    popularSearchTerms?: string[]
  }
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface KnowledgeBaseCategory {
  id: string
  name: string
  slug: string
  description: string
  icon?: string
  color?: string
  parentId?: string
  subcategories: KnowledgeBaseCategory[]
  articleCount: number
  isVisible: boolean
  sortOrder: number
  createdAt: Date
  updatedAt: Date
}

export interface ArticleFilters {
  status?: ArticleStatus[]
  type?: ArticleType[]
  category?: string[]
  author?: string[]
  tags?: string[]
  dateFrom?: Date
  dateTo?: Date
  difficulty?: ('beginner' | 'intermediate' | 'advanced')[]
  query?: string
  minRating?: number
  limit?: number
  offset?: number
  sortBy?: 'created' | 'updated' | 'views' | 'rating' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface KnowledgeBaseStats {
  totalArticles: number
  publishedArticles: number
  draftArticles: number
  archivedArticles: number
  totalViews: number
  totalCategories: number
  averageRating: number
  topArticles: Array<{
    id: string
    title: string
    views: number
    rating: number
  }>
  topCategories: Array<{
    id: string
    name: string
    articleCount: number
    views: number
  }>
  recentActivity: Array<{
    type: 'created' | 'updated' | 'published' | 'viewed'
    articleId: string
    articleTitle: string
    timestamp: Date
    author?: string
  }>
}

export interface SearchResult {
  article: KnowledgeBaseArticle
  relevanceScore: number
  matchedFields: string[]
  highlightedContent: string
}

export interface ArticleTemplate {
  id: string
  name: string
  description: string
  type: ArticleType
  template: string
  placeholders: Array<{
    key: string
    label: string
    type: 'text' | 'textarea' | 'select' | 'multiselect'
    options?: string[]
    required: boolean
  }>
  isActive: boolean
  createdAt: Date
}

/**
 * Knowledge Base Service
 * Handles comprehensive knowledge base management
 */
export class KnowledgeBaseService {
  private static instance: KnowledgeBaseService
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/admin/support/knowledge-base'
  }

  static getInstance(): KnowledgeBaseService {
    if (!KnowledgeBaseService.instance) {
      KnowledgeBaseService.instance = new KnowledgeBaseService()
    }
    return KnowledgeBaseService.instance
  }

  /**
   * Get articles with filtering and pagination
   */
  async getArticles(filters: ArticleFilters = {}): Promise<{
    articles: KnowledgeBaseArticle[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters.status) params.append('status', filters.status.join(','))
      if (filters.type) params.append('type', filters.type.join(','))
      if (filters.category) params.append('category', filters.category.join(','))
      if (filters.author) params.append('author', filters.author.join(','))
      if (filters.tags) params.append('tags', filters.tags.join(','))
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.difficulty) params.append('difficulty', filters.difficulty.join(','))
      if (filters.query) params.append('query', filters.query)
      if (filters.minRating) params.append('minRating', filters.minRating.toString())
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`${this.baseUrl}/articles?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch articles: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve articles')
      }

      return {
        articles: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve articles:', error)
      throw error
    }
  }

  /**
   * Get specific article
   */
  async getArticle(articleId: string): Promise<KnowledgeBaseArticle> {
    try {
      const response = await fetch(`${this.baseUrl}/articles/${articleId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch article: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve article')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve article:', error)
      throw error
    }
  }

  /**
   * Create new article
   */
  async createArticle(articleData: {
    title: string
    content: string
    contentFormat?: ContentFormat
    excerpt?: string
    type: ArticleType
    category: string
    subcategory?: string
    tags?: string[]
    status?: ArticleStatus
    difficulty?: 'beginner' | 'intermediate' | 'advanced'
    attachments?: File[]
    seo?: {
      metaTitle?: string
      metaDescription?: string
      keywords?: string[]
    }
  }): Promise<KnowledgeBaseArticle> {
    try {
      const formData = new FormData()
      formData.append('title', articleData.title)
      formData.append('content', articleData.content)
      formData.append('contentFormat', articleData.contentFormat || 'markdown')
      formData.append('type', articleData.type)
      formData.append('category', articleData.category)
      formData.append('status', articleData.status || 'draft')
      
      if (articleData.excerpt) formData.append('excerpt', articleData.excerpt)
      if (articleData.subcategory) formData.append('subcategory', articleData.subcategory)
      if (articleData.tags) formData.append('tags', JSON.stringify(articleData.tags))
      if (articleData.difficulty) formData.append('difficulty', articleData.difficulty)
      if (articleData.seo) formData.append('seo', JSON.stringify(articleData.seo))
      
      // Add attachments
      if (articleData.attachments) {
        articleData.attachments.forEach((file, index) => {
          formData.append(`attachment_${index}`, file)
        })
      }

      const response = await fetch(`${this.baseUrl}/articles`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`Failed to create article: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create article')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create article:', error)
      throw error
    }
  }

  /**
   * Update article
   */
  async updateArticle(articleId: string, updates: Partial<KnowledgeBaseArticle>): Promise<KnowledgeBaseArticle> {
    try {
      const response = await fetch(`${this.baseUrl}/articles/${articleId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error(`Failed to update article: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update article')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update article:', error)
      throw error
    }
  }

  /**
   * Delete article
   */
  async deleteArticle(articleId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/articles/${articleId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error(`Failed to delete article: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to delete article')
      }
    } catch (error) {
      console.error('Failed to delete article:', error)
      throw error
    }
  }

  /**
   * Search articles
   */
  async searchArticles(query: string, filters?: ArticleFilters): Promise<SearchResult[]> {
    try {
      const params = new URLSearchParams()
      params.append('q', query)
      
      if (filters?.status) params.append('status', filters.status.join(','))
      if (filters?.type) params.append('type', filters.type.join(','))
      if (filters?.category) params.append('category', filters.category.join(','))
      if (filters?.limit) params.append('limit', filters.limit.toString())

      const response = await fetch(`${this.baseUrl}/articles/search?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to search articles: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to search articles')
      }

      return result.data
    } catch (error) {
      console.error('Failed to search articles:', error)
      throw error
    }
  }

  /**
   * Get categories
   */
  async getCategories(): Promise<KnowledgeBaseCategory[]> {
    try {
      const response = await fetch(`${this.baseUrl}/categories`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve categories')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve categories:', error)
      throw error
    }
  }

  /**
   * Create category
   */
  async createCategory(categoryData: {
    name: string
    description: string
    icon?: string
    color?: string
    parentId?: string
  }): Promise<KnowledgeBaseCategory> {
    try {
      const response = await fetch(`${this.baseUrl}/categories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(categoryData)
      })

      if (!response.ok) {
        throw new Error(`Failed to create category: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create category')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create category:', error)
      throw error
    }
  }

  /**
   * Get knowledge base statistics
   */
  async getKnowledgeBaseStats(dateRange?: {
    from: Date
    to: Date
  }): Promise<KnowledgeBaseStats> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/stats?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch stats: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve stats')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve knowledge base statistics:', error)
      throw error
    }
  }

  /**
   * Rate article
   */
  async rateArticle(articleId: string, rating: number, feedback?: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/articles/${articleId}/rate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ rating, feedback })
      })

      if (!response.ok) {
        throw new Error(`Failed to rate article: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to rate article')
      }
    } catch (error) {
      console.error('Failed to rate article:', error)
      throw error
    }
  }

  /**
   * Get article templates
   */
  async getArticleTemplates(): Promise<ArticleTemplate[]> {
    try {
      const response = await fetch(`${this.baseUrl}/templates`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch templates: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve templates')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve article templates:', error)
      throw error
    }
  }

  /**
   * Bulk operations on articles
   */
  async bulkUpdateArticles(articleIds: string[], updates: {
    status?: ArticleStatus
    category?: string
    tags?: string[]
    author?: string
  }): Promise<{ updated: number; errors: any[] }> {
    try {
      const response = await fetch(`${this.baseUrl}/articles/bulk`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          articleIds,
          updates
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to bulk update articles: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to bulk update articles')
      }

      return result.data
    } catch (error) {
      console.error('Failed to bulk update articles:', error)
      throw error
    }
  }

  /**
   * Export articles
   */
  async exportArticles(format: 'json' | 'csv' | 'xml', filters?: ArticleFilters): Promise<string> {
    try {
      const params = new URLSearchParams()
      params.append('format', format)
      
      if (filters?.status) params.append('status', filters.status.join(','))
      if (filters?.type) params.append('type', filters.type.join(','))
      if (filters?.category) params.append('category', filters.category.join(','))

      const response = await fetch(`${this.baseUrl}/articles/export?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to export articles: ${response.statusText}`)
      }

      return await response.text()
    } catch (error) {
      console.error('Failed to export articles:', error)
      throw error
    }
  }

  /**
   * Get related articles
   */
  async getRelatedArticles(articleId: string, limit: number = 5): Promise<KnowledgeBaseArticle[]> {
    try {
      const response = await fetch(`${this.baseUrl}/articles/${articleId}/related?limit=${limit}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch related articles: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve related articles')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve related articles:', error)
      throw error
    }
  }
}

export default KnowledgeBaseService