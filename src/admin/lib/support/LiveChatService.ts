/**
 * Live Chat Service
 * 
 * Real-time customer support chat system
 * Part of Phase 2 Customer Support Integration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export type ChatMessageType = 'text' | 'image' | 'file' | 'system' | 'typing' | 'emoji'
export type ChatParticipantRole = 'customer' | 'admin' | 'system' | 'bot'
export type ChatStatus = 'active' | 'waiting' | 'closed' | 'transferred'
export type ChatPriority = 'low' | 'medium' | 'high' | 'urgent'

export interface ChatMessage {
  id: string
  chatId: string
  senderId: string
  senderName: string
  senderRole: ChatParticipantRole
  type: ChatMessageType
  content: string
  metadata?: {
    fileName?: string
    fileSize?: number
    fileType?: string
    imageUrl?: string
    isEncrypted?: boolean
    mentions?: string[]
    reactions?: Array<{
      emoji: string
      userId: string
      timestamp: Date
    }>
  }
  timestamp: Date
  readBy: Array<{
    userId: string
    readAt: Date
  }>
  editedAt?: Date
  deletedAt?: Date
}

export interface ChatParticipant {
  id: string
  name: string
  email: string
  role: ChatParticipantRole
  avatar?: string
  isOnline: boolean
  lastSeen: Date
  joinedAt: Date
  leftAt?: Date
}

export interface LiveChatSession {
  id: string
  customerId: string
  customerName: string
  customerEmail: string
  assignedAdminId?: string
  assignedAdminName?: string
  status: ChatStatus
  priority: ChatPriority
  subject: string
  department: string
  tags: string[]
  participants: ChatParticipant[]
  messages: ChatMessage[]
  metadata: {
    userAgent?: string
    ipAddress?: string
    referrerUrl?: string
    sessionId?: string
    customerContext?: {
      lastOrderId?: string
      totalOrders?: number
      lifetimeValue?: number
      tier?: string
    }
  }
  waitTime: number // in seconds
  responseTime?: number // in seconds
  firstResponseAt?: Date
  resolvedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface ChatFilters {
  status?: ChatStatus[]
  priority?: ChatPriority[]
  department?: string[]
  assignedAdmin?: string[]
  customer?: string
  dateFrom?: Date
  dateTo?: Date
  tags?: string[]
  unread?: boolean
  limit?: number
  offset?: number
  sortBy?: 'created' | 'updated' | 'priority' | 'waitTime'
  sortOrder?: 'asc' | 'desc'
}

export interface ChatStats {
  total: number
  active: number
  waiting: number
  byStatus: Record<ChatStatus, number>
  byPriority: Record<ChatPriority, number>
  avgWaitTime: number
  avgResponseTime: number
  avgResolutionTime: number
  customerSatisfaction: number
  adminWorkload: Array<{
    adminId: string
    name: string
    activeChatsSt: number
    avgResponseTime: number
  }>
}

export interface TypingIndicator {
  chatId: string
  userId: string
  userName: string
  isTyping: boolean
  timestamp: Date
}

export interface ChatNotification {
  id: string
  type: 'new_chat' | 'new_message' | 'chat_assigned' | 'chat_transferred' | 'chat_escalated'
  chatId: string
  title: string
  message: string
  timestamp: Date
  read: boolean
}

/**
 * Live Chat Service
 * Handles real-time customer support chat functionality
 */
export class LiveChatService {
  private static instance: LiveChatService
  private baseUrl: string
  private websocket: WebSocket | null = null
  private eventHandlers: Map<string, Function[]> = new Map()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  constructor() {
    this.baseUrl = '/api/admin/support/chat'
    this.initializeWebSocket()
  }

  static getInstance(): LiveChatService {
    if (!LiveChatService.instance) {
      LiveChatService.instance = new LiveChatService()
    }
    return LiveChatService.instance
  }

  /**
   * Initialize WebSocket connection for real-time chat
   */
  private initializeWebSocket(): void {
    try {
      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/chat`
      this.websocket = new WebSocket(wsUrl)

      this.websocket.onopen = () => {
        console.log('Chat WebSocket connected')
        this.reconnectAttempts = 0
        this.emit('connected')
      }

      this.websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleWebSocketMessage(data)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      this.websocket.onclose = () => {
        console.log('Chat WebSocket disconnected')
        this.emit('disconnected')
        this.attemptReconnect()
      }

      this.websocket.onerror = (error) => {
        console.error('Chat WebSocket error:', error)
        this.emit('error', error)
      }
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error)
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleWebSocketMessage(data: any): void {
    switch (data.type) {
      case 'new_message':
        this.emit('message', data.payload)
        break
      case 'typing_indicator':
        this.emit('typing', data.payload)
        break
      case 'chat_status_update':
        this.emit('chatStatusUpdate', data.payload)
        break
      case 'participant_update':
        this.emit('participantUpdate', data.payload)
        break
      case 'notification':
        this.emit('notification', data.payload)
        break
      default:
        console.log('Unknown WebSocket message type:', data.type)
    }
  }

  /**
   * Attempt to reconnect WebSocket
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
        this.initializeWebSocket()
      }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts))
    } else {
      console.error('Max reconnection attempts reached')
    }
  }

  /**
   * Event handler management
   */
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
  }

  off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  private emit(event: string, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => handler(...args))
    }
  }

  /**
   * Get chat sessions with filtering
   */
  async getChatSessions(filters: ChatFilters = {}): Promise<{
    chats: LiveChatSession[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters.status) params.append('status', filters.status.join(','))
      if (filters.priority) params.append('priority', filters.priority.join(','))
      if (filters.department) params.append('department', filters.department.join(','))
      if (filters.assignedAdmin) params.append('assignedAdmin', filters.assignedAdmin.join(','))
      if (filters.customer) params.append('customer', filters.customer)
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.tags) params.append('tags', filters.tags.join(','))
      if (filters.unread !== undefined) params.append('unread', filters.unread.toString())
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`${this.baseUrl}/sessions?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch chat sessions: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve chat sessions')
      }

      return {
        chats: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve chat sessions:', error)
      throw error
    }
  }

  /**
   * Get specific chat session
   */
  async getChatSession(chatId: string): Promise<LiveChatSession> {
    try {
      const response = await fetch(`${this.baseUrl}/sessions/${chatId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch chat session: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve chat session')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve chat session:', error)
      throw error
    }
  }

  /**
   * Send message in chat
   */
  async sendMessage(chatId: string, messageData: {
    content: string
    type?: ChatMessageType
    attachments?: File[]
    mentions?: string[]
  }): Promise<ChatMessage> {
    try {
      const formData = new FormData()
      formData.append('content', messageData.content)
      formData.append('type', messageData.type || 'text')
      
      if (messageData.mentions) {
        formData.append('mentions', JSON.stringify(messageData.mentions))
      }
      
      if (messageData.attachments) {
        messageData.attachments.forEach((file, index) => {
          formData.append(`attachment_${index}`, file)
        })
      }

      const response = await fetch(`${this.baseUrl}/sessions/${chatId}/messages`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to send message')
      }

      return result.data
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }

  /**
   * Assign chat to admin
   */
  async assignChat(chatId: string, adminId: string): Promise<LiveChatSession> {
    try {
      const response = await fetch(`${this.baseUrl}/sessions/${chatId}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adminId })
      })

      if (!response.ok) {
        throw new Error(`Failed to assign chat: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to assign chat')
      }

      return result.data
    } catch (error) {
      console.error('Failed to assign chat:', error)
      throw error
    }
  }

  /**
   * Update chat status
   */
  async updateChatStatus(chatId: string, status: ChatStatus): Promise<LiveChatSession> {
    try {
      const response = await fetch(`${this.baseUrl}/sessions/${chatId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      })

      if (!response.ok) {
        throw new Error(`Failed to update chat status: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update chat status')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update chat status:', error)
      throw error
    }
  }

  /**
   * Send typing indicator
   */
  sendTypingIndicator(chatId: string, isTyping: boolean): void {
    if (this.websocket?.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'typing_indicator',
        payload: {
          chatId,
          isTyping,
          timestamp: new Date()
        }
      }))
    }
  }

  /**
   * Join chat session
   */
  joinChat(chatId: string): void {
    if (this.websocket?.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'join_chat',
        payload: { chatId }
      }))
    }
  }

  /**
   * Leave chat session
   */
  leaveChat(chatId: string): void {
    if (this.websocket?.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'leave_chat',
        payload: { chatId }
      }))
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(chatId: string, messageIds: string[]): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/sessions/${chatId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ messageIds })
      })

      if (!response.ok) {
        throw new Error(`Failed to mark messages as read: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to mark messages as read')
      }
    } catch (error) {
      console.error('Failed to mark messages as read:', error)
      throw error
    }
  }

  /**
   * Get chat statistics
   */
  async getChatStats(dateRange?: {
    from: Date
    to: Date
  }): Promise<ChatStats> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/stats?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch chat stats: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve chat stats')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve chat statistics:', error)
      throw error
    }
  }

  /**
   * Create canned response
   */
  async createCannedResponse(responseData: {
    title: string
    content: string
    category: string
    tags: string[]
    isActive: boolean
  }): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/canned-responses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(responseData)
      })

      if (!response.ok) {
        throw new Error(`Failed to create canned response: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create canned response')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create canned response:', error)
      throw error
    }
  }

  /**
   * Transfer chat to another admin
   */
  async transferChat(chatId: string, targetAdminId: string, reason: string): Promise<LiveChatSession> {
    try {
      const response = await fetch(`${this.baseUrl}/sessions/${chatId}/transfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          targetAdminId,
          reason
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to transfer chat: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to transfer chat')
      }

      return result.data
    } catch (error) {
      console.error('Failed to transfer chat:', error)
      throw error
    }
  }

  /**
   * Cleanup WebSocket connection
   */
  disconnect(): void {
    if (this.websocket) {
      this.websocket.close()
      this.websocket = null
    }
    this.eventHandlers.clear()
  }
}

export default LiveChatService