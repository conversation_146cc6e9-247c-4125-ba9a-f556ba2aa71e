/**
 * Support Ticket Management Service
 * 
 * Comprehensive customer support ticket system
 * Part of Phase 2 Customer Support Integration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export type TicketStatus = 'open' | 'in_progress' | 'pending_customer' | 'resolved' | 'closed'
export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent' | 'critical'
export type TicketCategory = 'technical' | 'billing' | 'product' | 'shipping' | 'account' | 'other'
export type TicketSource = 'email' | 'chat' | 'phone' | 'form' | 'social' | 'admin_created'

export interface SupportTicket {
  id: string
  ticketNumber: string
  subject: string
  description: string
  status: TicketStatus
  priority: TicketPriority
  category: TicketCategory
  source: TicketSource
  customer: {
    userId: string
    email: string
    name: string
    tier: string
    totalOrders: number
    lifetimeValue: number
  }
  assignee?: {
    adminId: string
    name: string
    email: string
  }
  tags: string[]
  attachments: TicketAttachment[]
  messages: TicketMessage[]
  metadata: {
    ipAddress?: string
    userAgent?: string
    referrer?: string
    sessionId?: string
  }
  sla: {
    responseTime: number // in minutes
    resolutionTime: number // in minutes
    breached: boolean
  }
  satisfaction?: {
    rating: number // 1-5
    comment?: string
    submittedAt: Date
  }
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
  closedAt?: Date
}

export interface TicketMessage {
  id: string
  ticketId: string
  content: string
  type: 'customer' | 'admin' | 'system' | 'automated'
  author: {
    id: string
    name: string
    email: string
    role: 'customer' | 'admin' | 'system'
  }
  attachments: TicketAttachment[]
  isInternal: boolean
  readBy: Array<{
    userId: string
    readAt: Date
  }>
  createdAt: Date
  updatedAt: Date
}

export interface TicketAttachment {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  uploadedBy: string
  uploadedAt: Date
}

export interface TicketFilters {
  status?: TicketStatus[]
  priority?: TicketPriority[]
  category?: TicketCategory[]
  assignee?: string[]
  customer?: string
  dateFrom?: Date
  dateTo?: Date
  tags?: string[]
  slaBreached?: boolean
  unread?: boolean
  limit?: number
  offset?: number
  sortBy?: 'created' | 'updated' | 'priority' | 'sla'
  sortOrder?: 'asc' | 'desc'
}

export interface TicketStats {
  total: number
  byStatus: Record<TicketStatus, number>
  byPriority: Record<TicketPriority, number>
  byCategory: Record<TicketCategory, number>
  avgResponseTime: number
  avgResolutionTime: number
  slaBreachRate: number
  customerSatisfaction: number
  assigneeWorkload: Array<{
    adminId: string
    name: string
    openTickets: number
    avgResponseTime: number
  }>
}

/**
 * Support Ticket Management Service
 */
export class SupportTicketService {
  private static instance: SupportTicketService
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/admin/support'
  }

  static getInstance(): SupportTicketService {
    if (!SupportTicketService.instance) {
      SupportTicketService.instance = new SupportTicketService()
    }
    return SupportTicketService.instance
  }

  /**
   * Create a new support ticket
   */
  async createTicket(ticketData: {
    subject: string
    description: string
    priority: TicketPriority
    category: TicketCategory
    customerId?: string
    customerEmail?: string
    source?: TicketSource
    tags?: string[]
    attachments?: File[]
  }): Promise<SupportTicket> {
    try {
      const formData = new FormData()
      formData.append('subject', ticketData.subject)
      formData.append('description', ticketData.description)
      formData.append('priority', ticketData.priority)
      formData.append('category', ticketData.category)
      formData.append('source', ticketData.source || 'admin_created')
      
      if (ticketData.customerId) {
        formData.append('customerId', ticketData.customerId)
      }
      if (ticketData.customerEmail) {
        formData.append('customerEmail', ticketData.customerEmail)
      }
      if (ticketData.tags) {
        formData.append('tags', JSON.stringify(ticketData.tags))
      }
      
      // Add attachments
      if (ticketData.attachments) {
        ticketData.attachments.forEach((file, index) => {
          formData.append(`attachment_${index}`, file)
        })
      }

      const response = await fetch(`${this.baseUrl}/tickets`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`Failed to create ticket: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create ticket')
      }

      return result.data
    } catch (error) {
      console.error('Failed to create support ticket:', error)
      throw error
    }
  }

  /**
   * Get support tickets with filtering and pagination
   */
  async getTickets(filters: TicketFilters = {}): Promise<{
    tickets: SupportTicket[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      // Add filters to params
      if (filters.status) params.append('status', filters.status.join(','))
      if (filters.priority) params.append('priority', filters.priority.join(','))
      if (filters.category) params.append('category', filters.category.join(','))
      if (filters.assignee) params.append('assignee', filters.assignee.join(','))
      if (filters.customer) params.append('customer', filters.customer)
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters.tags) params.append('tags', filters.tags.join(','))
      if (filters.slaBreached !== undefined) params.append('slaBreached', filters.slaBreached.toString())
      if (filters.unread !== undefined) params.append('unread', filters.unread.toString())
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`${this.baseUrl}/tickets?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch tickets: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve tickets')
      }

      return {
        tickets: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve support tickets:', error)
      throw error
    }
  }

  /**
   * Get a specific ticket by ID
   */
  async getTicket(ticketId: string): Promise<SupportTicket> {
    try {
      const response = await fetch(`${this.baseUrl}/tickets/${ticketId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ticket: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve ticket')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve support ticket:', error)
      throw error
    }
  }

  /**
   * Update a support ticket
   */
  async updateTicket(ticketId: string, updates: {
    status?: TicketStatus
    priority?: TicketPriority
    category?: TicketCategory
    assigneeId?: string
    tags?: string[]
    subject?: string
    description?: string
  }): Promise<SupportTicket> {
    try {
      const response = await fetch(`${this.baseUrl}/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error(`Failed to update ticket: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update ticket')
      }

      return result.data
    } catch (error) {
      console.error('Failed to update support ticket:', error)
      throw error
    }
  }

  /**
   * Add a message to a ticket
   */
  async addMessage(ticketId: string, messageData: {
    content: string
    type?: 'customer' | 'admin' | 'system'
    isInternal?: boolean
    attachments?: File[]
  }): Promise<TicketMessage> {
    try {
      const formData = new FormData()
      formData.append('content', messageData.content)
      formData.append('type', messageData.type || 'admin')
      formData.append('isInternal', (messageData.isInternal || false).toString())
      
      // Add attachments
      if (messageData.attachments) {
        messageData.attachments.forEach((file, index) => {
          formData.append(`attachment_${index}`, file)
        })
      }

      const response = await fetch(`${this.baseUrl}/tickets/${ticketId}/messages`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`Failed to add message: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to add message')
      }

      return result.data
    } catch (error) {
      console.error('Failed to add ticket message:', error)
      throw error
    }
  }

  /**
   * Assign ticket to admin
   */
  async assignTicket(ticketId: string, adminId: string): Promise<SupportTicket> {
    return this.updateTicket(ticketId, { assigneeId: adminId })
  }

  /**
   * Close a ticket with optional satisfaction survey
   */
  async closeTicket(ticketId: string, satisfaction?: {
    rating: number
    comment?: string
  }): Promise<SupportTicket> {
    try {
      const response = await fetch(`${this.baseUrl}/tickets/${ticketId}/close`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ satisfaction })
      })

      if (!response.ok) {
        throw new Error(`Failed to close ticket: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to close ticket')
      }

      return result.data
    } catch (error) {
      console.error('Failed to close support ticket:', error)
      throw error
    }
  }

  /**
   * Get support ticket statistics
   */
  async getTicketStats(dateRange?: {
    from: Date
    to: Date
  }): Promise<TicketStats> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/tickets/stats?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ticket stats: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve ticket stats')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve ticket statistics:', error)
      throw error
    }
  }

  /**
   * Search tickets by content
   */
  async searchTickets(query: string, filters?: TicketFilters): Promise<SupportTicket[]> {
    try {
      const params = new URLSearchParams()
      params.append('q', query)
      
      // Add additional filters if provided
      if (filters?.status) params.append('status', filters.status.join(','))
      if (filters?.priority) params.append('priority', filters.priority.join(','))
      if (filters?.category) params.append('category', filters.category.join(','))

      const response = await fetch(`${this.baseUrl}/tickets/search?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to search tickets: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to search tickets')
      }

      return result.data
    } catch (error) {
      console.error('Failed to search support tickets:', error)
      throw error
    }
  }

  /**
   * Bulk update tickets
   */
  async bulkUpdateTickets(ticketIds: string[], updates: {
    status?: TicketStatus
    priority?: TicketPriority
    assigneeId?: string
    tags?: string[]
  }): Promise<{ updated: number; errors: any[] }> {
    try {
      const response = await fetch(`${this.baseUrl}/tickets/bulk`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ticketIds,
          updates
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to bulk update tickets: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to bulk update tickets')
      }

      return result.data
    } catch (error) {
      console.error('Failed to bulk update tickets:', error)
      throw error
    }
  }

  /**
   * Get SLA breach predictions
   */
  async getSLABreachPredictions(): Promise<Array<{
    ticketId: string
    ticketNumber: string
    customer: string
    timeToBreach: number // minutes
    probability: number // 0-1
  }>> {
    try {
      const response = await fetch(`${this.baseUrl}/tickets/sla-predictions`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch SLA predictions: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve SLA predictions')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve SLA predictions:', error)
      throw error
    }
  }

  /**
   * Export tickets to various formats
   */
  async exportTickets(format: 'csv' | 'excel' | 'pdf', filters?: TicketFilters): Promise<string> {
    try {
      const params = new URLSearchParams()
      params.append('format', format)
      
      // Add filters
      if (filters?.status) params.append('status', filters.status.join(','))
      if (filters?.priority) params.append('priority', filters.priority.join(','))
      if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters?.dateTo) params.append('dateTo', filters.dateTo.toISOString())

      const response = await fetch(`${this.baseUrl}/tickets/export?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to export tickets: ${response.statusText}`)
      }

      return await response.text()
    } catch (error) {
      console.error('Failed to export tickets:', error)
      throw error
    }
  }
}

export default SupportTicketService