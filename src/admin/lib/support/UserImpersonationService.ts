/**
 * User Impersonation Service
 * 
 * Secure admin impersonation capabilities for customer support
 * Part of Phase 2 Customer Support Integration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since Phase 2 Enhancement
 */

export interface ImpersonationSession {
  id: string
  adminId: string
  adminName: string
  targetUserId: string
  targetUserEmail: string
  targetUserName: string
  reason: string
  startedAt: Date
  expiresAt: Date
  endedAt?: Date
  status: 'active' | 'expired' | 'terminated'
  ipAddress: string
  userAgent: string
  ticketId?: string
}

export interface ImpersonationRequest {
  targetUserId: string
  reason: string
  ticketId?: string
  duration?: number // in minutes, default 30
}

export interface ImpersonationAuditLog {
  id: string
  sessionId: string
  adminId: string
  action: string
  resource: string
  timestamp: Date
  metadata: Record<string, any>
}

export interface ImpersonationRestrictions {
  maxDuration: number // minutes
  allowedActions: string[]
  blockedEndpoints: string[]
  requiresApproval: boolean
  approverRoles: string[]
}

/**
 * User Impersonation Service
 * Handles secure admin impersonation with comprehensive audit trail
 */
export class UserImpersonationService {
  private static instance: UserImpersonationService
  private baseUrl: string
  private currentSession: ImpersonationSession | null = null

  constructor() {
    this.baseUrl = '/api/admin/support/impersonation'
  }

  static getInstance(): UserImpersonationService {
    if (!UserImpersonationService.instance) {
      UserImpersonationService.instance = new UserImpersonationService()
    }
    return UserImpersonationService.instance
  }

  /**
   * Start impersonation session
   */
  async startImpersonation(request: ImpersonationRequest): Promise<ImpersonationSession> {
    try {
      // Validate request
      if (!request.targetUserId || !request.reason.trim()) {
        throw new Error('Target user ID and reason are required')
      }

      if (request.reason.length < 10) {
        throw new Error('Reason must be at least 10 characters long')
      }

      const response = await fetch(`${this.baseUrl}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          targetUserId: request.targetUserId,
          reason: request.reason,
          ticketId: request.ticketId,
          duration: request.duration || 30
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error?.message || 'Failed to start impersonation')
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to start impersonation')
      }

      this.currentSession = result.data
      
      // Set up automatic session cleanup
      this.setupSessionCleanup(this.currentSession!)
      
      return this.currentSession!
    } catch (error) {
      console.error('Failed to start impersonation:', error)
      throw error
    }
  }

  /**
   * End impersonation session
   */
  async endImpersonation(sessionId?: string): Promise<void> {
    try {
      const targetSessionId = sessionId || this.currentSession?.id
      
      if (!targetSessionId) {
        throw new Error('No active impersonation session')
      }

      const response = await fetch(`${this.baseUrl}/end`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sessionId: targetSessionId })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error?.message || 'Failed to end impersonation')
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to end impersonation')
      }

      // Clear current session if it matches
      if (this.currentSession?.id === targetSessionId) {
        this.currentSession = null
      }
    } catch (error) {
      console.error('Failed to end impersonation:', error)
      throw error
    }
  }

  /**
   * Get current session status
   */
  getCurrentSession(): ImpersonationSession | null {
    return this.currentSession
  }

  /**
   * Check if currently impersonating
   */
  isImpersonating(): boolean {
    return this.currentSession !== null && this.currentSession.status === 'active'
  }

  /**
   * Get all impersonation sessions
   */
  async getImpersonationSessions(filters?: {
    adminId?: string
    targetUserId?: string
    status?: ImpersonationSession['status']
    dateFrom?: Date
    dateTo?: Date
    limit?: number
    offset?: number
  }): Promise<{
    sessions: ImpersonationSession[]
    totalCount: number
    currentPage: number
    totalPages: number
  }> {
    try {
      const params = new URLSearchParams()
      
      if (filters?.adminId) params.append('adminId', filters.adminId)
      if (filters?.targetUserId) params.append('targetUserId', filters.targetUserId)
      if (filters?.status) params.append('status', filters.status)
      if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString())
      if (filters?.dateTo) params.append('dateTo', filters.dateTo.toISOString())
      if (filters?.limit) params.append('limit', filters.limit.toString())
      if (filters?.offset) params.append('page', Math.floor((filters.offset || 0) / (filters.limit || 20) + 1).toString())

      const response = await fetch(`${this.baseUrl}/sessions?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch sessions: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve sessions')
      }

      return {
        sessions: result.data,
        totalCount: result.meta.pagination.totalItems,
        currentPage: result.meta.pagination.currentPage,
        totalPages: result.meta.pagination.totalPages
      }
    } catch (error) {
      console.error('Failed to retrieve impersonation sessions:', error)
      throw error
    }
  }

  /**
   * Get session audit logs
   */
  async getSessionAuditLogs(sessionId: string): Promise<ImpersonationAuditLog[]> {
    try {
      const response = await fetch(`${this.baseUrl}/sessions/${sessionId}/audit`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch audit logs: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve audit logs')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve session audit logs:', error)
      throw error
    }
  }

  /**
   * Validate impersonation permissions
   */
  async validateImpersonationPermissions(targetUserId: string): Promise<{
    allowed: boolean
    restrictions: ImpersonationRestrictions
    reasons?: string[]
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/validate/${targetUserId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to validate permissions: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to validate permissions')
      }

      return result.data
    } catch (error) {
      console.error('Failed to validate impersonation permissions:', error)
      throw error
    }
  }

  /**
   * Request impersonation approval
   */
  async requestApproval(request: ImpersonationRequest): Promise<{
    approvalId: string
    status: 'pending' | 'approved' | 'denied'
    approvers: string[]
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/request-approval`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error?.message || 'Failed to request approval')
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to request approval')
      }

      return result.data
    } catch (error) {
      console.error('Failed to request impersonation approval:', error)
      throw error
    }
  }

  /**
   * Get impersonation statistics
   */
  async getImpersonationStats(dateRange?: {
    from: Date
    to: Date
  }): Promise<{
    totalSessions: number
    activeSessions: number
    averageDuration: number
    topAdmins: Array<{
      adminId: string
      adminName: string
      sessionCount: number
    }>
    topTargetUsers: Array<{
      userId: string
      userName: string
      sessionCount: number
    }>
    sessionsByReason: Record<string, number>
  }> {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('dateFrom', dateRange.from.toISOString())
        params.append('dateTo', dateRange.to.toISOString())
      }

      const response = await fetch(`${this.baseUrl}/stats?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch stats: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to retrieve stats')
      }

      return result.data
    } catch (error) {
      console.error('Failed to retrieve impersonation statistics:', error)
      throw error
    }
  }

  /**
   * Setup automatic session cleanup
   */
  private setupSessionCleanup(session: ImpersonationSession): void {
    const timeUntilExpiry = session.expiresAt.getTime() - Date.now()
    
    if (timeUntilExpiry > 0) {
      setTimeout(() => {
        if (this.currentSession?.id === session.id) {
          this.endImpersonation(session.id).catch(console.error)
        }
      }, timeUntilExpiry)
    }
  }

  /**
   * Check session validity
   */
  private isSessionValid(session: ImpersonationSession): boolean {
    return session.status === 'active' && session.expiresAt > new Date()
  }

  /**
   * Log impersonation action
   */
  async logImpersonationAction(action: string, resource: string, metadata: Record<string, any> = {}): Promise<void> {
    if (!this.currentSession) {
      return // No active session, nothing to log
    }

    try {
      await fetch(`${this.baseUrl}/audit-log`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: this.currentSession.id,
          action,
          resource,
          metadata
        })
      })
    } catch (error) {
      console.error('Failed to log impersonation action:', error)
      // Don't throw here to avoid disrupting the main flow
    }
  }

  /**
   * Generate impersonation token for API requests
   */
  async generateImpersonationToken(): Promise<string> {
    if (!this.currentSession) {
      throw new Error('No active impersonation session')
    }

    try {
      const response = await fetch(`${this.baseUrl}/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: this.currentSession.id
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate impersonation token')
      }

      const result = await response.json()
      return result.data.token
    } catch (error) {
      console.error('Failed to generate impersonation token:', error)
      throw error
    }
  }
}

export default UserImpersonationService