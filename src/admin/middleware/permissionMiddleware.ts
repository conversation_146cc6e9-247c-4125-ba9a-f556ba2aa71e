/**
 * Admin Permission Middleware
 * 
 * Enhanced middleware for admin route protection with granular permission
 * checking, audit logging, and real-time permission validation.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { NextRequest, NextResponse } from 'next/server';
import { permissionService } from '../services/permissionService';
import { navigationAuditService } from '../services/navigationAuditService';
import { ROLE_PERMISSIONS, NAVIGATION_PERMISSIONS, AdminRole, AdminPermission } from '../types/permissions';

export interface PermissionMiddlewareConfig {
  requireMFA?: boolean;
  logAccess?: boolean;
  strictMode?: boolean;
  allowedRoles?: AdminRole[];
  requiredPermissions?: AdminPermission[];
}

export interface AdminAuthInfo {
  adminId: string;
  adminRole: AdminRole;
  permissions: AdminPermission[];
  sessionId: string;
  mfaVerified: boolean;
  ipAddress: string;
  userAgent: string;
}

/**
 * Enhanced admin permission middleware
 */
export async function adminPermissionMiddleware(
  request: NextRequest,
  config: PermissionMiddlewareConfig = {}
): Promise<NextResponse | null> {
  const {
    requireMFA = false,
    logAccess = true,
    strictMode = true,
    allowedRoles,
    requiredPermissions
  } = config;

  try {
    // Extract admin authentication info
    const authInfo = extractAdminAuthInfo(request);
    
    if (!authInfo) {
      if (logAccess) {
        await logPermissionDenial(request, 'No authentication info', 'AUTHENTICATION_FAILED');
      }
      return createUnauthorizedResponse('Authentication required');
    }

    // Check MFA if required
    if (requireMFA && !authInfo.mfaVerified) {
      if (logAccess) {
        await logPermissionDenial(request, 'MFA verification required', 'MFA_REQUIRED', authInfo);
      }
      return createUnauthorizedResponse('MFA verification required');
    }

    // Check role-based access
    if (allowedRoles && !allowedRoles.includes(authInfo.adminRole)) {
      if (logAccess) {
        await logPermissionDenial(
          request, 
          `Role ${authInfo.adminRole} not in allowed roles: ${allowedRoles.join(', ')}`,
          'ROLE_DENIED',
          authInfo
        );
      }
      return createForbiddenResponse('Insufficient role permissions');
    }

    // Check specific permissions
    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasPermissions = permissionService.hasAllPermissions(
        authInfo.permissions,
        requiredPermissions
      );

      if (!hasPermissions.hasPermission) {
        if (logAccess) {
          await logPermissionDenial(
            request,
            `Missing required permissions: ${JSON.stringify(hasPermissions.missingPermissions)}`,
            'PERMISSION_DENIED',
            authInfo
          );
        }
        return createForbiddenResponse('Insufficient permissions');
      }
    }

    // Check path-specific permissions
    const pathname = request.nextUrl.pathname;
    const pathPermissions = NAVIGATION_PERMISSIONS[pathname];
    
    if (pathPermissions && pathPermissions.length > 0) {
      const hasPathAccess = permissionService.hasAllPermissions(
        authInfo.permissions,
        pathPermissions
      );

      if (!hasPathAccess.hasPermission) {
        if (logAccess) {
          await logPermissionDenial(
            request,
            `No access to path ${pathname}: ${hasPathAccess.reason}`,
            'PATH_DENIED',
            authInfo
          );
        }
        return createForbiddenResponse('Path access denied');
      }
    }

    // Log successful access
    if (logAccess) {
      await logSuccessfulAccess(request, authInfo);
    }

    // Add admin info to request headers for downstream use
    const response = NextResponse.next();
    response.headers.set('x-admin-id', authInfo.adminId);
    response.headers.set('x-admin-role', authInfo.adminRole);
    response.headers.set('x-admin-session', authInfo.sessionId);
    
    return response;

  } catch (error) {
    console.error('Permission middleware error:', error);
    
    if (logAccess) {
      await logPermissionDenial(request, `Middleware error: ${error}`, 'SYSTEM_ERROR');
    }
    
    return createServerErrorResponse('Permission check failed');
  }
}

/**
 * Extract admin authentication info from request
 */
function extractAdminAuthInfo(request: NextRequest): AdminAuthInfo | null {
  const userRole = request.cookies.get('user-role')?.value;
  const adminId = request.cookies.get('admin-id')?.value || 
                  request.headers.get('x-admin-id') ||
                  'unknown';
  const sessionId = request.cookies.get('admin-session')?.value ||
                    request.headers.get('x-session-id') ||
                    'unknown';
  const mfaVerified = request.cookies.get('mfa-verified')?.value === 'true';
  
  // Map user role to admin role
  const roleMapping: Record<string, AdminRole> = {
    'superadmin': 'super_admin',
    'admin': 'admin',
    'moderator': 'moderator',
    'analyst': 'analyst',
    'support': 'support'
  };

  const adminRole = userRole ? roleMapping[userRole] : null;
  
  if (!adminRole) {
    return null;
  }

  // Get permissions for role
  const permissions = ROLE_PERMISSIONS[adminRole] || [];

  // Extract IP and User Agent
  const ipAddress = request.headers.get('x-forwarded-for') ||
                    request.headers.get('x-real-ip') ||
                    request.ip ||
                    'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';

  return {
    adminId,
    adminRole,
    permissions,
    sessionId,
    mfaVerified,
    ipAddress,
    userAgent
  };
}

/**
 * Log permission denial
 */
async function logPermissionDenial(
  request: NextRequest,
  reason: string,
  denialType: string,
  authInfo?: AdminAuthInfo
): Promise<void> {
  try {
    const pathname = request.nextUrl.pathname;
    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (authInfo) {
      await navigationAuditService.logPermissionDenial(
        authInfo.adminId,
        authInfo.adminRole,
        pathname,
        authInfo.permissions,
        reason,
        authInfo.sessionId,
        userAgent,
        ipAddress
      );
    }

    console.warn(`🚫 Permission Denied [${denialType}]:`, {
      path: pathname,
      reason,
      adminId: authInfo?.adminId || 'unknown',
      role: authInfo?.adminRole || 'unknown',
      ip: ipAddress
    });

  } catch (error) {
    console.error('Failed to log permission denial:', error);
  }
}

/**
 * Log successful access
 */
async function logSuccessfulAccess(
  request: NextRequest,
  authInfo: AdminAuthInfo
): Promise<void> {
  try {
    const pathname = request.nextUrl.pathname;
    const referer = request.headers.get('referer') || 'direct';

    await navigationAuditService.logNavigationEvent({
      adminId: authInfo.adminId,
      adminRole: authInfo.adminRole,
      fromPath: referer.includes(request.nextUrl.origin) ? 
                 new URL(referer).pathname : 'external',
      toPath: pathname,
      permissions: authInfo.permissions,
      accessGranted: true,
      sessionId: authInfo.sessionId,
      userAgent: authInfo.userAgent,
      ipAddress: authInfo.ipAddress
    });

  } catch (error) {
    console.error('Failed to log successful access:', error);
  }
}

/**
 * Create unauthorized response
 */
function createUnauthorizedResponse(message: string): NextResponse {
  return new NextResponse(
    JSON.stringify({ error: message, code: 'UNAUTHORIZED' }),
    {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'WWW-Authenticate': 'Bearer'
      }
    }
  );
}

/**
 * Create forbidden response
 */
function createForbiddenResponse(message: string): NextResponse {
  return new NextResponse(
    JSON.stringify({ error: message, code: 'FORBIDDEN' }),
    {
      status: 403,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * Create server error response
 */
function createServerErrorResponse(message: string): NextResponse {
  return new NextResponse(
    JSON.stringify({ error: message, code: 'INTERNAL_ERROR' }),
    {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * Utility function to create permission middleware for specific routes
 */
export function createPermissionMiddleware(config: PermissionMiddlewareConfig) {
  return (request: NextRequest) => adminPermissionMiddleware(request, config);
}

/**
 * Pre-configured middleware for common admin routes
 */
export const adminDashboardMiddleware = createPermissionMiddleware({
  requiredPermissions: [{ resource: 'dashboard', actions: ['read'] }],
  logAccess: true
});

export const adminUsersMiddleware = createPermissionMiddleware({
  requiredPermissions: [{ resource: 'users', actions: ['read'] }],
  logAccess: true
});

export const adminSystemMiddleware = createPermissionMiddleware({
  allowedRoles: ['admin', 'super_admin'],
  requiredPermissions: [{ resource: 'system', actions: ['read'] }],
  requireMFA: true,
  logAccess: true,
  strictMode: true
});
