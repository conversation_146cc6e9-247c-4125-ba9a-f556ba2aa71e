/**
 * Navigation Audit Service
 * 
 * Comprehensive service for tracking admin navigation patterns, permission
 * usage, and providing insights for navigation optimization.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { AdminPermission, AdminRole } from '../types/permissions';

export interface NavigationEvent {
  id: string;
  adminId: string;
  adminRole: AdminRole;
  fromPath: string;
  toPath: string;
  timestamp: Date;
  permissions: AdminPermission[];
  accessGranted: boolean;
  denialReason?: string;
  sessionId: string;
  userAgent: string;
  ipAddress: string;
}

export interface NavigationPattern {
  path: string;
  accessCount: number;
  uniqueAdmins: number;
  averageTimeSpent: number;
  mostCommonNextPaths: string[];
  permissionDenials: number;
  popularityScore: number;
}

export interface NavigationInsights {
  totalNavigations: number;
  uniqueAdmins: number;
  mostAccessedPaths: NavigationPattern[];
  leastAccessedPaths: NavigationPattern[];
  permissionDenialRate: number;
  averageSessionDuration: number;
  peakUsageHours: number[];
  roleBasedUsage: Record<AdminRole, NavigationPattern[]>;
}

class NavigationAuditService {
  private events: NavigationEvent[] = [];
  private readonly MAX_EVENTS = 10000; // Keep last 10k events in memory

  /**
   * Log a navigation event
   */
  async logNavigationEvent(event: Omit<NavigationEvent, 'id' | 'timestamp'>): Promise<void> {
    const navigationEvent: NavigationEvent = {
      ...event,
      id: `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    // Add to in-memory store
    this.events.push(navigationEvent);

    // Keep only recent events
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(-this.MAX_EVENTS);
    }

    // TODO: Persist to database
    console.log('🧭 Navigation Event:', {
      admin: event.adminId,
      from: event.fromPath,
      to: event.toPath,
      granted: event.accessGranted,
      reason: event.denialReason
    });

    // Send to audit service if available
    try {
      await this.sendToAuditService(navigationEvent);
    } catch (error) {
      console.error('Failed to send navigation event to audit service:', error);
    }
  }

  /**
   * Log permission denial
   */
  async logPermissionDenial(
    adminId: string,
    adminRole: AdminRole,
    attemptedPath: string,
    requiredPermissions: AdminPermission[],
    reason: string,
    sessionId: string,
    userAgent: string,
    ipAddress: string
  ): Promise<void> {
    await this.logNavigationEvent({
      adminId,
      adminRole,
      fromPath: 'unknown',
      toPath: attemptedPath,
      permissions: requiredPermissions,
      accessGranted: false,
      denialReason: reason,
      sessionId,
      userAgent,
      ipAddress
    });
  }

  /**
   * Get navigation insights for a specific time period
   */
  getNavigationInsights(
    startDate: Date = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    endDate: Date = new Date()
  ): NavigationInsights {
    const filteredEvents = this.events.filter(
      event => event.timestamp >= startDate && event.timestamp <= endDate
    );

    const pathStats = new Map<string, {
      accessCount: number;
      uniqueAdmins: Set<string>;
      totalTime: number;
      nextPaths: string[];
      denials: number;
    }>();

    const adminSessions = new Map<string, Date[]>();
    const roleUsage = new Map<AdminRole, NavigationEvent[]>();

    // Process events
    filteredEvents.forEach(event => {
      // Path statistics
      if (!pathStats.has(event.toPath)) {
        pathStats.set(event.toPath, {
          accessCount: 0,
          uniqueAdmins: new Set(),
          totalTime: 0,
          nextPaths: [],
          denials: 0
        });
      }

      const pathStat = pathStats.get(event.toPath)!;
      
      if (event.accessGranted) {
        pathStat.accessCount++;
        pathStat.uniqueAdmins.add(event.adminId);
      } else {
        pathStat.denials++;
      }

      // Admin sessions
      if (!adminSessions.has(event.adminId)) {
        adminSessions.set(event.adminId, []);
      }
      adminSessions.get(event.adminId)!.push(event.timestamp);

      // Role usage
      if (!roleUsage.has(event.adminRole)) {
        roleUsage.set(event.adminRole, []);
      }
      roleUsage.get(event.adminRole)!.push(event);
    });

    // Convert to NavigationPattern objects
    const patterns: NavigationPattern[] = Array.from(pathStats.entries()).map(([path, stats]) => ({
      path,
      accessCount: stats.accessCount,
      uniqueAdmins: stats.uniqueAdmins.size,
      averageTimeSpent: stats.totalTime / stats.accessCount || 0,
      mostCommonNextPaths: this.getMostCommonNextPaths(path, filteredEvents),
      permissionDenials: stats.denials,
      popularityScore: this.calculatePopularityScore(stats.accessCount, stats.uniqueAdmins.size, stats.denials)
    }));

    // Sort patterns by popularity
    patterns.sort((a, b) => b.popularityScore - a.popularityScore);

    // Calculate role-based usage
    const roleBasedUsage: Record<AdminRole, NavigationPattern[]> = {} as any;
    roleUsage.forEach((events, role) => {
      const rolePathStats = new Map<string, { count: number; denials: number }>();
      
      events.forEach(event => {
        if (!rolePathStats.has(event.toPath)) {
          rolePathStats.set(event.toPath, { count: 0, denials: 0 });
        }
        
        const stat = rolePathStats.get(event.toPath)!;
        if (event.accessGranted) {
          stat.count++;
        } else {
          stat.denials++;
        }
      });

      roleBasedUsage[role] = Array.from(rolePathStats.entries()).map(([path, stats]) => ({
        path,
        accessCount: stats.count,
        uniqueAdmins: 1, // Simplified for role-based view
        averageTimeSpent: 0,
        mostCommonNextPaths: [],
        permissionDenials: stats.denials,
        popularityScore: stats.count
      }));
    });

    return {
      totalNavigations: filteredEvents.filter(e => e.accessGranted).length,
      uniqueAdmins: new Set(filteredEvents.map(e => e.adminId)).size,
      mostAccessedPaths: patterns.slice(0, 10),
      leastAccessedPaths: patterns.slice(-10).reverse(),
      permissionDenialRate: filteredEvents.filter(e => !e.accessGranted).length / filteredEvents.length,
      averageSessionDuration: this.calculateAverageSessionDuration(adminSessions),
      peakUsageHours: this.calculatePeakUsageHours(filteredEvents),
      roleBasedUsage
    };
  }

  /**
   * Get most common next paths for a given path
   */
  private getMostCommonNextPaths(path: string, events: NavigationEvent[]): string[] {
    const nextPaths = new Map<string, number>();
    
    for (let i = 0; i < events.length - 1; i++) {
      if (events[i].toPath === path && events[i + 1].accessGranted) {
        const nextPath = events[i + 1].toPath;
        nextPaths.set(nextPath, (nextPaths.get(nextPath) || 0) + 1);
      }
    }

    return Array.from(nextPaths.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([path]) => path);
  }

  /**
   * Calculate popularity score for a path
   */
  private calculatePopularityScore(accessCount: number, uniqueAdmins: number, denials: number): number {
    const baseScore = accessCount * 0.7 + uniqueAdmins * 0.3;
    const denialPenalty = denials * 0.1;
    return Math.max(0, baseScore - denialPenalty);
  }

  /**
   * Calculate average session duration
   */
  private calculateAverageSessionDuration(adminSessions: Map<string, Date[]>): number {
    let totalDuration = 0;
    let sessionCount = 0;

    adminSessions.forEach(timestamps => {
      if (timestamps.length > 1) {
        const sessionStart = Math.min(...timestamps.map(t => t.getTime()));
        const sessionEnd = Math.max(...timestamps.map(t => t.getTime()));
        totalDuration += sessionEnd - sessionStart;
        sessionCount++;
      }
    });

    return sessionCount > 0 ? totalDuration / sessionCount / 1000 / 60 : 0; // Return in minutes
  }

  /**
   * Calculate peak usage hours
   */
  private calculatePeakUsageHours(events: NavigationEvent[]): number[] {
    const hourCounts = new Array(24).fill(0);
    
    events.forEach(event => {
      const hour = event.timestamp.getHours();
      hourCounts[hour]++;
    });

    // Return top 3 peak hours
    return hourCounts
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour);
  }

  /**
   * Send navigation event to audit service
   */
  private async sendToAuditService(event: NavigationEvent): Promise<void> {
    // TODO: Implement actual audit service integration
    // This would send the event to the main audit logging system
  }

  /**
   * Clear old events (for memory management)
   */
  clearOldEvents(olderThan: Date): void {
    this.events = this.events.filter(event => event.timestamp > olderThan);
  }

  /**
   * Export navigation data for analysis
   */
  exportNavigationData(startDate?: Date, endDate?: Date): NavigationEvent[] {
    if (!startDate && !endDate) {
      return [...this.events];
    }

    return this.events.filter(event => {
      if (startDate && event.timestamp < startDate) return false;
      if (endDate && event.timestamp > endDate) return false;
      return true;
    });
  }
}

// Export singleton instance
export const navigationAuditService = new NavigationAuditService();

// Export class for testing
export { NavigationAuditService };
