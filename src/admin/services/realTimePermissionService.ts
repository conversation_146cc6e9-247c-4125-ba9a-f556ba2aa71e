/**
 * Real-Time Permission Validation Service
 * 
 * Service for real-time permission checking, validation, and updates
 * with WebSocket support for live permission changes.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { AdminPermission, AdminRole, PermissionCheckResult } from '../types/permissions';
import { permissionService } from './permissionService';

export interface PermissionUpdate {
  adminId: string;
  permissions: AdminPermission[];
  timestamp: Date;
  reason: string;
  updatedBy: string;
}

export interface PermissionValidationEvent {
  type: 'permission_granted' | 'permission_denied' | 'permission_updated' | 'session_expired';
  adminId: string;
  resource?: string;
  action?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

class RealTimePermissionService {
  private subscribers = new Map<string, (event: PermissionValidationEvent) => void>();
  private permissionCache = new Map<string, { permissions: AdminPermission[]; timestamp: number }>();
  private validationQueue: PermissionValidationEvent[] = [];
  private isProcessing = false;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly VALIDATION_INTERVAL = 30 * 1000; // 30 seconds

  constructor() {
    // Start periodic validation
    this.startPeriodicValidation();
  }

  /**
   * Subscribe to permission validation events
   */
  subscribe(adminId: string, callback: (event: PermissionValidationEvent) => void): () => void {
    this.subscribers.set(adminId, callback);
    
    return () => {
      this.subscribers.delete(adminId);
    };
  }

  /**
   * Validate permissions in real-time
   */
  async validatePermissions(
    adminId: string,
    adminRole: AdminRole,
    requiredPermissions: AdminPermission[]
  ): Promise<PermissionCheckResult> {
    try {
      // Get current permissions (with caching)
      const currentPermissions = await this.getCurrentPermissions(adminId, adminRole);
      
      // Perform permission check
      const result = permissionService.hasAllPermissions(currentPermissions, requiredPermissions);
      
      // Emit validation event
      const event: PermissionValidationEvent = {
        type: result.hasPermission ? 'permission_granted' : 'permission_denied',
        adminId,
        timestamp: new Date(),
        metadata: {
          requiredPermissions,
          currentPermissions,
          result
        }
      };
      
      this.emitEvent(adminId, event);
      
      return result;
      
    } catch (error) {
      console.error('Real-time permission validation error:', error);
      
      return {
        hasPermission: false,
        reason: 'Validation service error'
      };
    }
  }

  /**
   * Update admin permissions in real-time
   */
  async updatePermissions(update: PermissionUpdate): Promise<void> {
    try {
      // Update cache
      this.permissionCache.set(update.adminId, {
        permissions: update.permissions,
        timestamp: Date.now()
      });

      // Emit update event
      const event: PermissionValidationEvent = {
        type: 'permission_updated',
        adminId: update.adminId,
        timestamp: update.timestamp,
        metadata: {
          newPermissions: update.permissions,
          reason: update.reason,
          updatedBy: update.updatedBy
        }
      };

      this.emitEvent(update.adminId, event);

      // TODO: Persist to database
      console.log('🔄 Permissions updated for admin:', update.adminId);

    } catch (error) {
      console.error('Failed to update permissions:', error);
    }
  }

  /**
   * Invalidate admin session
   */
  async invalidateSession(adminId: string, reason: string): Promise<void> {
    try {
      // Clear cache
      this.permissionCache.delete(adminId);

      // Emit session expired event
      const event: PermissionValidationEvent = {
        type: 'session_expired',
        adminId,
        timestamp: new Date(),
        metadata: { reason }
      };

      this.emitEvent(adminId, event);

      console.log('🚫 Session invalidated for admin:', adminId, 'Reason:', reason);

    } catch (error) {
      console.error('Failed to invalidate session:', error);
    }
  }

  /**
   * Check if admin has permission for specific resource/action
   */
  async hasPermission(
    adminId: string,
    adminRole: AdminRole,
    resource: string,
    action: string,
    scope?: string
  ): Promise<boolean> {
    const requiredPermissions: AdminPermission[] = [{
      resource: resource as any,
      actions: [action as any],
      scope: scope as any
    }];

    const result = await this.validatePermissions(adminId, adminRole, requiredPermissions);
    return result.hasPermission;
  }

  /**
   * Batch validate multiple permissions
   */
  async batchValidatePermissions(
    adminId: string,
    adminRole: AdminRole,
    permissionChecks: { resource: string; action: string; scope?: string }[]
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const check of permissionChecks) {
      const key = `${check.resource}:${check.action}:${check.scope || 'all'}`;
      results[key] = await this.hasPermission(
        adminId,
        adminRole,
        check.resource,
        check.action,
        check.scope
      );
    }

    return results;
  }

  /**
   * Get current permissions for admin
   */
  private async getCurrentPermissions(adminId: string, adminRole: AdminRole): Promise<AdminPermission[]> {
    // Check cache first
    const cached = this.permissionCache.get(adminId);
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
      return cached.permissions;
    }

    // TODO: Fetch from database/API
    // For now, use role-based permissions
    const permissions = permissionService.getPermissionsForRole(adminRole);
    
    // Update cache
    this.permissionCache.set(adminId, {
      permissions,
      timestamp: Date.now()
    });

    return permissions;
  }

  /**
   * Emit event to subscribers
   */
  private emitEvent(adminId: string, event: PermissionValidationEvent): void {
    const callback = this.subscribers.get(adminId);
    if (callback) {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in permission event callback:', error);
      }
    }

    // Add to validation queue for processing
    this.validationQueue.push(event);
  }

  /**
   * Start periodic validation
   */
  private startPeriodicValidation(): void {
    setInterval(() => {
      this.processValidationQueue();
      this.cleanupExpiredCache();
    }, this.VALIDATION_INTERVAL);
  }

  /**
   * Process validation queue
   */
  private async processValidationQueue(): Promise<void> {
    if (this.isProcessing || this.validationQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const events = this.validationQueue.splice(0, 100); // Process up to 100 events
      
      // TODO: Send events to audit service or analytics
      console.log(`📊 Processing ${events.length} permission validation events`);

      // Group events by type for analytics
      const eventsByType = events.reduce((acc, event) => {
        acc[event.type] = (acc[event.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log('Permission event summary:', eventsByType);

    } catch (error) {
      console.error('Error processing validation queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.permissionCache.forEach((value, key) => {
      if ((now - value.timestamp) > this.CACHE_TTL) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.permissionCache.delete(key);
    });

    if (expiredKeys.length > 0) {
      console.log(`🧹 Cleaned up ${expiredKeys.length} expired permission cache entries`);
    }
  }

  /**
   * Get validation statistics
   */
  getValidationStats(): {
    cacheSize: number;
    subscriberCount: number;
    queueSize: number;
    cacheHitRate: number;
  } {
    return {
      cacheSize: this.permissionCache.size,
      subscriberCount: this.subscribers.size,
      queueSize: this.validationQueue.length,
      cacheHitRate: 0 // TODO: Implement cache hit rate tracking
    };
  }

  /**
   * Clear all caches and reset service
   */
  reset(): void {
    this.permissionCache.clear();
    this.validationQueue.length = 0;
    console.log('🔄 Real-time permission service reset');
  }
}

// Export singleton instance
export const realTimePermissionService = new RealTimePermissionService();

// Export class for testing
export { RealTimePermissionService };
