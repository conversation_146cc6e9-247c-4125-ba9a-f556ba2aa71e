/**
 * Admin Permission System Types
 * 
 * Comprehensive type definitions for granular admin permission system
 * supporting multi-admin environments with role-based access control.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

export type AdminRole = 'super_admin' | 'admin' | 'moderator' | 'analyst' | 'support';

export type AdminResource =
  | 'dashboard'
  | 'analytics'
  | 'products'
  | 'inventory'
  | 'orders'
  | 'raffles'
  | 'reviews'
  | 'users'
  | 'user_segmentation'
  | 'bulk_operations'
  | 'gamification'
  | 'support'
  | 'tickets'
  | 'chat'
  | 'impersonation'
  | 'content'
  | 'reports'
  | 'blog'
  | 'homepage'
  | 'categories'
  | 'availability'
  | 'performance'
  | 'point_simulation'
  | 'system'
  | 'admin_management'
  | 'security'
  | 'logs'
  | 'backups'
  | 'settings'
  | 'integrations'
  | 'workflows'
  | 'marketing'
  | 'crm'
  | 'analytics_platform'
  | 'backup'
  | 'events'
  | 'monitoring'
  | 'ai_analytics'
  | 'security_compliance'
  | 'multi_tenant'
  | 'business_intelligence'
  | 'mobile_api'
  | 'customization'
  | 'api_management'
  | 'database'
  | 'cache'
  | 'deployments'
  | 'feature_flags'
  // Community Admin Resources
  | 'community_discussions'
  | 'community_submissions'
  | 'community_challenges'
  | 'community_moderation'
  | 'community_social'
  | 'community_realtime'
  | 'community_analytics';

export type AdminAction = 'read' | 'write' | 'delete' | 'admin' | 'execute' | 'root' | 'configure' | 'deploy' | 'backup' | 'restore';

export type AdminScope = 'own' | 'team' | 'all';

export interface AdminPermission {
  resource: AdminResource;
  actions: AdminAction[];
  scope?: AdminScope;
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  value: any;
}

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: AdminRole;
  permissions: AdminPermission[];
  avatar?: string;
  lastLogin?: Date;
  loginCount: number;
  isActive: boolean;
  isMfaEnabled: boolean;
  teamId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface NavigationItem {
  path: string;
  icon: any; // LucideIcon type
  label: string;
  requiredPermissions: AdminPermission[];
  adminLevel?: AdminRole[];
  scope?: AdminScope;
  badge?: string;
  isNew?: boolean;
  isDeprecated?: boolean;
}

export interface NavigationGroup {
  name: string;
  emoji: string;
  color: string;
  description: string;
  items: NavigationItem[];
  requiredPermissions?: AdminPermission[];
  minimumRole?: AdminRole;
}

export interface PermissionCheckResult {
  hasPermission: boolean;
  reason?: string;
  missingPermissions?: AdminPermission[];
  suggestedRole?: AdminRole;
}

export interface AdminPresence {
  adminId: string;
  adminName: string;
  adminEmail: string;
  currentPage: string;
  lastActivity: Date;
  activeOperations: string[];
  status: 'active' | 'idle' | 'away';
}

export interface AdminActivity {
  id: string;
  adminId: string;
  adminName: string;
  action: string;
  resource: AdminResource;
  resourceId?: string;
  timestamp: Date;
  status: 'in-progress' | 'completed' | 'failed';
  metadata?: Record<string, any>;
}

export interface AdminConflict {
  id: string;
  resource: AdminResource;
  resourceId: string;
  conflictType: 'simultaneous_edit' | 'permission_conflict' | 'data_conflict';
  admins: {
    adminId: string;
    adminName: string;
    changes: Record<string, any>;
    timestamp: Date;
  }[];
  status: 'detected' | 'resolving' | 'resolved';
  resolution?: ConflictResolution;
}

export interface ConflictResolution {
  action: 'merge' | 'override' | 'manual';
  strategy: 'auto' | 'yours' | 'theirs' | 'custom';
  resolvedBy: string;
  resolvedAt: Date;
  finalState?: Record<string, any>;
}

// Role-based permission presets
export const ROLE_PERMISSIONS: Record<AdminRole, AdminPermission[]> = {
  super_admin: [
    // Core Business Operations - Full Control
    { resource: 'dashboard', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'analytics', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'products', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'inventory', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'orders', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'raffles', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'reviews', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    
    // User & Community Management - Full Control
    { resource: 'users', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'user_segmentation', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'bulk_operations', actions: ['read', 'write', 'execute', 'admin', 'root'], scope: 'all' },
    { resource: 'gamification', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    
    // Support & Communication - Full Control
    { resource: 'support', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'tickets', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'chat', actions: ['read', 'write', 'admin', 'root'], scope: 'all' },
    { resource: 'impersonation', actions: ['read', 'write', 'execute', 'admin', 'root'], scope: 'all' },
    
    // Content & Marketing - Full Control
    { resource: 'content', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'reports', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'blog', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    { resource: 'homepage', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'categories', actions: ['read', 'write', 'delete', 'admin', 'root'], scope: 'all' },
    
    // System Administration - Full Control
    { resource: 'availability', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'performance', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'point_simulation', actions: ['read', 'write', 'execute', 'admin', 'root'], scope: 'all' },
    { resource: 'system', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    
    // Super Admin Exclusive Resources - Root Level Control
    { resource: 'admin_management', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'security', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'logs', actions: ['read', 'write', 'admin', 'root'], scope: 'all' },
    { resource: 'backups', actions: ['read', 'write', 'execute', 'admin', 'root', 'backup', 'restore'], scope: 'all' },
    { resource: 'settings', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'integrations', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'workflows', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'marketing', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'crm', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'analytics_platform', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'backup', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'events', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'monitoring', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'ai_analytics', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'security_compliance', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'multi_tenant', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'business_intelligence', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'mobile_api', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'customization', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'api_management', actions: ['read', 'write', 'delete', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'database', actions: ['read', 'write', 'admin', 'root', 'backup', 'restore'], scope: 'all' },
    { resource: 'cache', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' },
    { resource: 'deployments', actions: ['read', 'write', 'execute', 'admin', 'root', 'deploy'], scope: 'all' },
    { resource: 'feature_flags', actions: ['read', 'write', 'admin', 'root', 'configure'], scope: 'all' }
  ],
  admin: [
    { resource: 'dashboard', actions: ['read', 'write'], scope: 'all' },
    { resource: 'analytics', actions: ['read'], scope: 'all' },
    { resource: 'products', actions: ['read', 'write', 'delete'], scope: 'all' },
    { resource: 'inventory', actions: ['read', 'write'], scope: 'all' },
    { resource: 'orders', actions: ['read', 'write'], scope: 'all' },
    { resource: 'raffles', actions: ['read', 'write'], scope: 'all' },
    { resource: 'reviews', actions: ['read', 'write', 'delete'], scope: 'all' },
    { resource: 'users', actions: ['read', 'write'], scope: 'all' },
    { resource: 'user_segmentation', actions: ['read', 'write'], scope: 'all' },
    { resource: 'bulk_operations', actions: ['read', 'execute'], scope: 'all' },
    { resource: 'gamification', actions: ['read', 'write'], scope: 'all' },
    { resource: 'support', actions: ['read', 'write'], scope: 'all' },
    { resource: 'tickets', actions: ['read', 'write'], scope: 'all' },
    { resource: 'chat', actions: ['read', 'write'], scope: 'all' },
    { resource: 'content', actions: ['read', 'write'], scope: 'all' },
    { resource: 'reports', actions: ['read'], scope: 'all' },
    { resource: 'blog', actions: ['read', 'write'], scope: 'all' },
    { resource: 'homepage', actions: ['read', 'write'], scope: 'all' },
    { resource: 'categories', actions: ['read', 'write'], scope: 'all' },
    { resource: 'workflows', actions: ['read', 'write'], scope: 'all' },
    { resource: 'marketing', actions: ['read', 'write'], scope: 'all' },
    { resource: 'crm', actions: ['read', 'write'], scope: 'all' },
    { resource: 'analytics_platform', actions: ['read'], scope: 'all' },
    { resource: 'backup', actions: ['read'], scope: 'all' },
    { resource: 'events', actions: ['read', 'write'], scope: 'all' },
    { resource: 'monitoring', actions: ['read'], scope: 'all' },
    { resource: 'ai_analytics', actions: ['read'], scope: 'all' },
    { resource: 'security_compliance', actions: ['read'], scope: 'all' },
    { resource: 'multi_tenant', actions: ['read'], scope: 'all' },
    { resource: 'business_intelligence', actions: ['read', 'write'], scope: 'all' },
    { resource: 'mobile_api', actions: ['read'], scope: 'all' },
    { resource: 'customization', actions: ['read', 'write'], scope: 'all' },
    { resource: 'integrations', actions: ['read'], scope: 'all' },

    // Community Management - Admin Level
    { resource: 'community_discussions', actions: ['read', 'write', 'moderate', 'delete'], scope: 'all' },
    { resource: 'community_submissions', actions: ['read', 'write', 'moderate', 'delete'], scope: 'all' },
    { resource: 'community_challenges', actions: ['read', 'write', 'moderate'], scope: 'all' },
    { resource: 'community_moderation', actions: ['read', 'write', 'execute', 'configure'], scope: 'all' },
    { resource: 'community_social', actions: ['read', 'write', 'moderate'], scope: 'all' },
    { resource: 'community_analytics', actions: ['read'], scope: 'all' }
  ],
  moderator: [
    { resource: 'dashboard', actions: ['read'], scope: 'all' },
    { resource: 'products', actions: ['read'], scope: 'all' },
    { resource: 'orders', actions: ['read'], scope: 'all' },
    { resource: 'reviews', actions: ['read', 'write', 'delete'], scope: 'all' },
    { resource: 'users', actions: ['read', 'write'], scope: 'all' },
    { resource: 'support', actions: ['read', 'write'], scope: 'all' },
    { resource: 'tickets', actions: ['read', 'write'], scope: 'all' },
    { resource: 'chat', actions: ['read', 'write'], scope: 'all' },
    { resource: 'content', actions: ['read', 'write'], scope: 'all' },

    // Community Management - Moderator Level
    { resource: 'community_discussions', actions: ['read', 'moderate'], scope: 'all' },
    { resource: 'community_submissions', actions: ['read', 'moderate'], scope: 'all' },
    { resource: 'community_moderation', actions: ['read', 'execute'], scope: 'all' }
  ],
  analyst: [
    { resource: 'dashboard', actions: ['read'], scope: 'all' },
    { resource: 'analytics', actions: ['read'], scope: 'all' },
    { resource: 'products', actions: ['read'], scope: 'all' },
    { resource: 'orders', actions: ['read'], scope: 'all' },
    { resource: 'users', actions: ['read'], scope: 'all' },
    { resource: 'reports', actions: ['read'], scope: 'all' },
    { resource: 'performance', actions: ['read'], scope: 'all' }
  ],
  support: [
    { resource: 'dashboard', actions: ['read'], scope: 'all' },
    { resource: 'users', actions: ['read'], scope: 'all' },
    { resource: 'orders', actions: ['read'], scope: 'all' },
    { resource: 'support', actions: ['read', 'write'], scope: 'all' },
    { resource: 'tickets', actions: ['read', 'write'], scope: 'all' },
    { resource: 'chat', actions: ['read', 'write'], scope: 'all' },
    { resource: 'impersonation', actions: ['read', 'execute'], scope: 'all' }
  ]
};

// Navigation item permission requirements
export const NAVIGATION_PERMISSIONS: Record<string, AdminPermission[]> = {
  // Core Business Operations
  '/admin/dashboard': [{ resource: 'dashboard', actions: ['read'] }],
  '/admin/analytics': [{ resource: 'analytics', actions: ['read'] }],
  '/admin/products': [{ resource: 'products', actions: ['read'] }],
  '/admin/inventory': [{ resource: 'inventory', actions: ['read'] }],
  '/admin/orders': [{ resource: 'orders', actions: ['read'] }],
  '/admin/raffles': [{ resource: 'raffles', actions: ['read'] }],
  '/admin/reviews': [{ resource: 'reviews', actions: ['read'] }],
  
  // User & Community Management
  '/admin/users': [{ resource: 'users', actions: ['read'] }],
  '/admin/users/segmentation': [{ resource: 'user_segmentation', actions: ['read'] }],
  '/admin/bulk-operations': [{ resource: 'bulk_operations', actions: ['read'] }],
  '/admin/gamification': [{ resource: 'gamification', actions: ['read'] }],
  
  // Support & Communication
  '/admin/support': [{ resource: 'support', actions: ['read'] }],
  '/admin/support/tickets': [{ resource: 'tickets', actions: ['read'] }],
  '/admin/support/chat': [{ resource: 'chat', actions: ['read'] }],
  '/admin/support/impersonation': [{ resource: 'impersonation', actions: ['read'] }],
  
  // Content & Marketing
  '/admin/content': [{ resource: 'content', actions: ['read'] }],
  '/admin/reports': [{ resource: 'reports', actions: ['read'] }],
  '/admin/blog': [{ resource: 'blog', actions: ['read'] }],
  '/admin/homepage': [{ resource: 'homepage', actions: ['read'] }],
  '/admin/categories': [{ resource: 'categories', actions: ['read'] }],
  
  // System Administration
  '/admin/availability': [{ resource: 'availability', actions: ['read'] }],
  '/admin/performance': [{ resource: 'performance', actions: ['read'] }],
  '/admin/point-simulation': [{ resource: 'point_simulation', actions: ['read'] }],
  '/admin/system': [{ resource: 'system', actions: ['read'] }],
  
  // Super Admin Exclusive Routes
  '/admin/admin-management': [{ resource: 'admin_management', actions: ['read'] }],
  '/admin/security': [{ resource: 'security', actions: ['read'] }],
  '/admin/logs': [{ resource: 'logs', actions: ['read'] }],
  '/admin/backups': [{ resource: 'backups', actions: ['read'] }],
  '/admin/settings': [{ resource: 'settings', actions: ['read'] }],
  '/admin/integrations': [{ resource: 'integrations', actions: ['read'] }],
  '/admin/workflows': [{ resource: 'workflows', actions: ['read'] }],
  '/admin/marketing': [{ resource: 'marketing', actions: ['read'] }],
  '/admin/crm': [{ resource: 'crm', actions: ['read'] }],
  '/admin/analytics-platform': [{ resource: 'analytics_platform', actions: ['read'] }],
  '/admin/backup': [{ resource: 'backup', actions: ['read'] }],
  '/admin/events': [{ resource: 'events', actions: ['read'] }],
  '/admin/monitoring': [{ resource: 'monitoring', actions: ['read'] }],
  '/admin/ai-analytics': [{ resource: 'ai_analytics', actions: ['read'] }],
  '/admin/security-compliance': [{ resource: 'security_compliance', actions: ['read'] }],
  '/admin/multi-tenant': [{ resource: 'multi_tenant', actions: ['read'] }],
  '/admin/business-intelligence': [{ resource: 'business_intelligence', actions: ['read'] }],
  '/admin/mobile-api': [{ resource: 'mobile_api', actions: ['read'] }],
  '/admin/customization': [{ resource: 'customization', actions: ['read'] }],
  '/admin/api-management': [{ resource: 'api_management', actions: ['read'] }],
  '/admin/database': [{ resource: 'database', actions: ['read'] }],
  '/admin/cache': [{ resource: 'cache', actions: ['read'] }],
  '/admin/deployments': [{ resource: 'deployments', actions: ['read'] }],
  '/admin/feature-flags': [{ resource: 'feature_flags', actions: ['read'] }],
  '/admin/debug/permissions': [{ resource: 'admin_management', actions: ['read'] }],

  // Community Management Routes
  '/admin/community/discussions': [{ resource: 'community_discussions', actions: ['read'] }],
  '/admin/community/submissions': [{ resource: 'community_submissions', actions: ['read'] }],
  '/admin/community/challenges': [{ resource: 'community_challenges', actions: ['read'] }],
  '/admin/community/moderation': [{ resource: 'community_moderation', actions: ['read'] }],
  '/admin/community/social': [{ resource: 'community_social', actions: ['read'] }],
  '/admin/community/realtime': [{ resource: 'community_realtime', actions: ['read'] }],
  '/admin/community/analytics': [{ resource: 'community_analytics', actions: ['read'] }]
};
