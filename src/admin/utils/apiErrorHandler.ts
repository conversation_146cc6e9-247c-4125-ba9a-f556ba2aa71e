/**
 * API Error Handler Utility
 * 
 * Comprehensive error handling for API calls with detailed logging
 * and user-friendly error messages.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

export interface ApiError {
  message: string;
  code: string;
  status: number;
  details?: any;
  timestamp: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  timestamp?: string;
}

/**
 * Enhanced fetch wrapper with error handling
 */
export async function apiCall<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    console.log(`🌐 API Call: ${options.method || 'GET'} ${url}`);
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    // Log response details
    console.log(`📡 Response: ${response.status} ${response.statusText}`);

    // Check if response is HTML (common error case)
    const contentType = response.headers.get('content-type');
    if (contentType && !contentType.includes('application/json')) {
      console.error(`❌ Expected JSON but received: ${contentType}`);
      
      // Try to get the HTML content for debugging
      const htmlContent = await response.text();
      console.error('HTML Response:', htmlContent.substring(0, 500));
      
      throw new Error(`API returned HTML instead of JSON. Status: ${response.status}. Content-Type: ${contentType}`);
    }

    // Parse JSON response
    let data: ApiResponse<T>;
    try {
      data = await response.json();
    } catch (parseError) {
      console.error('❌ JSON Parse Error:', parseError);
      throw new Error(`Failed to parse JSON response from ${url}`);
    }

    // Handle API-level errors
    if (!response.ok) {
      const apiError: ApiError = {
        message: data.error?.message || `HTTP ${response.status}: ${response.statusText}`,
        code: data.error?.code || 'HTTP_ERROR',
        status: response.status,
        details: data.error?.details,
        timestamp: new Date()
      };

      console.error('❌ API Error:', apiError);
      throw apiError;
    }

    // Handle application-level errors
    if (!data.success && data.error) {
      const apiError: ApiError = {
        message: data.error.message,
        code: data.error.code || 'API_ERROR',
        status: response.status,
        details: data.error.details,
        timestamp: new Date()
      };

      console.error('❌ Application Error:', apiError);
      throw apiError;
    }

    console.log('✅ API Success:', url);
    return data.data || data;

  } catch (error) {
    // Enhanced error logging
    if (error instanceof Error) {
      console.error(`❌ API Call Failed: ${url}`, {
        message: error.message,
        stack: error.stack,
        url,
        options
      });
    }

    // Re-throw the error for handling by the caller
    throw error;
  }
}

/**
 * Specific API call functions with error handling
 */
export const adminApi = {
  /**
   * Get admin dashboard data
   */
  async getDashboardData(): Promise<any> {
    return apiCall('/api/admin/dashboard');
  },

  /**
   * Get admin analytics
   */
  async getAnalytics(params?: Record<string, string>): Promise<any> {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiCall(`/api/admin/analytics${searchParams}`);
  },

  /**
   * Get user data
   */
  async getUsers(params?: Record<string, string>): Promise<any> {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiCall(`/api/admin/users${searchParams}`);
  },

  /**
   * Verify admin authentication
   */
  async verifyAuth(): Promise<any> {
    return apiCall('/api/admin/auth/verify');
  },

  /**
   * Get admin permissions
   */
  async getPermissions(): Promise<any> {
    return apiCall('/api/admin/permissions');
  }
};

/**
 * Error boundary for API calls
 */
export function withApiErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | null> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.error('API Error Boundary:', error);
      
      // You can add toast notifications here
      // toast.error(getErrorMessage(error));
      
      return null;
    }
  };
}

/**
 * Get user-friendly error message
 */
export function getErrorMessage(error: any): string {
  if (error && typeof error === 'object') {
    if (error.message) {
      return error.message;
    }
    if (error.code) {
      return `Error: ${error.code}`;
    }
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unexpected error occurred';
}

/**
 * Check if error is authentication related
 */
export function isAuthError(error: any): boolean {
  if (!error) return false;
  
  const authCodes = ['UNAUTHORIZED', 'FORBIDDEN', 'TOKEN_EXPIRED', 'INVALID_TOKEN'];
  const authStatuses = [401, 403];
  
  return (
    authCodes.includes(error.code) ||
    authStatuses.includes(error.status) ||
    (error.message && error.message.toLowerCase().includes('auth'))
  );
}

/**
 * Check if error is network related
 */
export function isNetworkError(error: any): boolean {
  if (!error) return false;
  
  return (
    error.message?.includes('fetch') ||
    error.message?.includes('network') ||
    error.message?.includes('connection') ||
    error.code === 'NETWORK_ERROR'
  );
}

/**
 * Retry API call with exponential backoff
 */
export async function retryApiCall<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on authentication errors
      if (isAuthError(error)) {
        throw error;
      }
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`🔄 Retrying API call in ${delay}ms (attempt ${attempt + 1}/${maxRetries})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Debug API calls in development
 */
export function debugApiCall(url: string, options: RequestInit, response: Response) {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔍 API Debug: ${options.method || 'GET'} ${url}`);
    console.log('Request Options:', options);
    console.log('Response Status:', response.status, response.statusText);
    console.log('Response Headers:', Object.fromEntries(response.headers.entries()));
    console.groupEnd();
  }
}
