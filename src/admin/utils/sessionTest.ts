/**
 * Session Testing Utility
 * 
 * Utility functions for testing session management and validation
 * in development environment.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { validateAdminSession, createAdminSession } from '../lib/sessionManager';

/**
 * Test session validation with various scenarios
 */
export function testSessionValidation() {
  console.log('🧪 Testing session validation...');

  // Test 1: Valid session creation and validation
  console.log('\n1. Testing valid session creation...');
  const session = createAdminSession(
    'test-admin-123',
    '<EMAIL>',
    'superadmin',
    '127.0.0.1',
    'test-user-agent'
  );
  console.log('✅ Session created:', session.sessionId);

  const validatedSession = validateAdminSession(session.sessionId);
  console.log('✅ Session validated:', validatedSession ? 'SUCCESS' : 'FAILED');

  // Test 2: Invalid session ID
  console.log('\n2. Testing invalid session ID...');
  const invalidSession = validateAdminSession('invalid-session-id');
  console.log('❌ Invalid session result:', invalidSession ? 'UNEXPECTED SUCCESS' : 'CORRECTLY FAILED');

  // Test 3: Development fallback session
  console.log('\n3. Testing development fallback...');
  const devSessionId = 'admin_session_test123_1234567890_abcdef';
  const devSession = validateAdminSession(devSessionId);
  console.log('🔧 Development session result:', devSession ? 'SUCCESS (fallback created)' : 'FAILED');

  // Test 4: Malformed session ID
  console.log('\n4. Testing malformed session ID...');
  const malformedSession = validateAdminSession('malformed_session');
  console.log('❌ Malformed session result:', malformedSession ? 'UNEXPECTED SUCCESS' : 'CORRECTLY FAILED');

  console.log('\n🧪 Session validation tests completed!');
}

/**
 * Test middleware authentication flow
 */
export function testMiddlewareFlow() {
  console.log('🧪 Testing middleware authentication flow...');

  // Simulate middleware request cookies
  const mockCookies = {
    'firebase-auth-token': 'mock-firebase-token',
    'user-role': 'superadmin',
    'admin-access': 'true',
    'user-id': 'test-user-123',
    'admin-session': 'admin_session_test123_1234567890_abcdef'
  };

  console.log('🍪 Mock cookies:', mockCookies);

  // Test session validation
  const sessionValid = validateAdminSession(mockCookies['admin-session']);
  console.log('✅ Session validation result:', sessionValid ? 'VALID' : 'INVALID');

  if (sessionValid) {
    console.log('📋 Session details:', {
      sessionId: sessionValid.sessionId,
      adminId: sessionValid.adminId,
      adminEmail: sessionValid.adminEmail,
      adminRole: sessionValid.adminRole,
      isActive: sessionValid.isActive,
      mfaVerified: sessionValid.mfaVerified
    });
  }

  console.log('🧪 Middleware flow test completed!');
}

/**
 * Run all session tests
 */
export function runAllSessionTests() {
  console.log('🚀 Starting comprehensive session tests...\n');
  
  testSessionValidation();
  console.log('\n' + '='.repeat(50) + '\n');
  testMiddlewareFlow();
  
  console.log('\n🎉 All session tests completed!');
}

// Export for use in development console
if (typeof window !== 'undefined') {
  (window as any).sessionTest = {
    testSessionValidation,
    testMiddlewareFlow,
    runAllSessionTests
  };
  console.log('🔧 Session test utilities available at window.sessionTest');
}
