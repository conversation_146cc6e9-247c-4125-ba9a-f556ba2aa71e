/**
 * Achievement Highlights Component
 * 
 * Displays a summary of recent achievements, progress, and highlights
 * for use in dashboard contexts.
 * 
 * Features:
 * - Recent achievement unlocks
 * - Achievement progress overview
 * - Quick stats and highlights
 * - Responsive design
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Trophy,
  Star,
  Award,
  Target,
  Zap,
  ChevronRight,
  Sparkles,
  Crown,
  Gift
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { UserProfile } from '@/types/profile'

interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  points: number
  category: 'purchase' | 'social' | 'engagement' | 'milestone' | 'special'
  isUnlocked: boolean
  unlockedAt?: Date
  progress?: number
  maxProgress?: number
}

interface AchievementHighlightsProps {
  profile: UserProfile | null
  className?: string
}

/**
 * Mock achievements data for demonstration
 */
const mockAchievements: Achievement[] = [
  {
    id: '1',
    name: 'First Purchase',
    description: 'Made your first purchase',
    icon: '🛒',
    rarity: 'common',
    points: 100,
    category: 'purchase',
    isUnlocked: true,
    unlockedAt: new Date(Date.now() - 86400000) // 1 day ago
  },
  {
    id: '2',
    name: 'Profile Complete',
    description: 'Completed your profile',
    icon: '✅',
    rarity: 'common',
    points: 50,
    category: 'social',
    isUnlocked: true,
    unlockedAt: new Date(Date.now() - 172800000) // 2 days ago
  },
  {
    id: '3',
    name: 'Big Spender',
    description: 'Spend over $100 in a single order',
    icon: '💰',
    rarity: 'rare',
    points: 250,
    category: 'purchase',
    isUnlocked: false,
    progress: 75,
    maxProgress: 100
  },
  {
    id: '4',
    name: 'Community Star',
    description: 'Get 10 likes on your reviews',
    icon: '⭐',
    rarity: 'epic',
    points: 500,
    category: 'social',
    isUnlocked: false,
    progress: 3,
    maxProgress: 10
  }
]

const getRarityColor = (rarity: string) => {
  switch (rarity) {
    case 'legendary': return 'text-yellow-400 border-yellow-400'
    case 'epic': return 'text-purple-400 border-purple-400'
    case 'rare': return 'text-blue-400 border-blue-400'
    default: return 'text-gray-400 border-gray-400'
  }
}

const getRarityBg = (rarity: string) => {
  switch (rarity) {
    case 'legendary': return 'bg-yellow-400/10'
    case 'epic': return 'bg-purple-400/10'
    case 'rare': return 'bg-blue-400/10'
    default: return 'bg-gray-400/10'
  }
}

export default function AchievementHighlights({ profile, className = '' }: AchievementHighlightsProps) {
  const [achievements] = useState<Achievement[]>(mockAchievements)
  
  const recentUnlocks = achievements
    .filter(a => a.isUnlocked && a.unlockedAt)
    .sort((a, b) => (b.unlockedAt?.getTime() || 0) - (a.unlockedAt?.getTime() || 0))
    .slice(0, 3)
  
  const inProgress = achievements
    .filter(a => !a.isUnlocked && a.progress !== undefined)
    .sort((a, b) => ((b.progress || 0) / (b.maxProgress || 1)) - ((a.progress || 0) / (a.maxProgress || 1)))
    .slice(0, 2)
  
  const totalUnlocked = achievements.filter(a => a.isUnlocked).length
  const totalPoints = achievements.filter(a => a.isUnlocked).reduce((sum, a) => sum + a.points, 0)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Trophy className="w-6 h-6 text-accent-400" />
          <h2 className="text-xl font-semibold text-white">Achievement Highlights</h2>
        </div>
        <Link href="/profile/achievements">
          <Button variant="ghost" size="sm" className="text-accent-400 hover:text-accent-300">
            View All
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </Link>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 gap-4">
        <Card className="bg-gray-900/50 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Award className="w-8 h-8 text-accent-400" />
              <div>
                <div className="text-2xl font-bold text-white">{totalUnlocked}</div>
                <div className="text-sm text-gray-400">Achievements</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gray-900/50 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Star className="w-8 h-8 text-yellow-400" />
              <div>
                <div className="text-2xl font-bold text-white">{totalPoints}</div>
                <div className="text-sm text-gray-400">Points Earned</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Unlocks */}
      {recentUnlocks.length > 0 && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-white text-lg">
              <Sparkles className="w-5 h-5 mr-2 text-accent-400" />
              Recent Unlocks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {recentUnlocks.map((achievement) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg"
              >
                <div className="text-2xl">{achievement.icon}</div>
                <div className="flex-1">
                  <div className="font-medium text-white">{achievement.name}</div>
                  <div className="text-sm text-gray-400">{achievement.description}</div>
                </div>
                <div className="text-right">
                  <Badge className={`${getRarityColor(achievement.rarity)} ${getRarityBg(achievement.rarity)}`}>
                    +{achievement.points}
                  </Badge>
                </div>
              </motion.div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Progress Tracking */}
      {inProgress.length > 0 && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-white text-lg">
              <Target className="w-5 h-5 mr-2 text-accent-400" />
              In Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {inProgress.map((achievement) => {
              const progressPercent = ((achievement.progress || 0) / (achievement.maxProgress || 1)) * 100
              return (
                <div key={achievement.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{achievement.icon}</span>
                      <span className="font-medium text-white">{achievement.name}</span>
                    </div>
                    <Badge className={`${getRarityColor(achievement.rarity)} ${getRarityBg(achievement.rarity)}`}>
                      {achievement.progress}/{achievement.maxProgress}
                    </Badge>
                  </div>
                  <Progress value={progressPercent} className="h-2" />
                </div>
              )
            })}
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {recentUnlocks.length === 0 && inProgress.length === 0 && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardContent className="p-8 text-center">
            <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-400 mb-2">Start Your Achievement Journey</h3>
            <p className="text-gray-500 mb-4">Make purchases, engage with the community, and unlock achievements!</p>
            <Link href="/shop">
              <Button className="bg-accent-600 hover:bg-accent-700 text-white">
                <Gift className="w-4 h-4 mr-2" />
                Start Shopping
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
