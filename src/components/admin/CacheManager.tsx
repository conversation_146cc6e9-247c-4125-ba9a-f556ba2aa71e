/**
 * Cache Manager Component for Admin Dashboard
 * Provides cache management interface for Cloudflare hybrid deployment
 */

import React, { useState, useEffect } from 'react'
import { CacheAnalytics, CacheStatus } from '../../lib/cloudflare/cacheManager'

interface CacheManagerProps {
  className?: string
}

interface PurgeOptions {
  urls: string
  tags: string
  everything: boolean
  reason: string
}

export default function CacheManager({ className = '' }: CacheManagerProps) {
  const [analytics, setAnalytics] = useState<CacheAnalytics | null>(null)
  const [loading, setLoading] = useState(false)
  const [purgeOptions, setPurgeOptions] = useState<PurgeOptions>({
    urls: '',
    tags: '',
    everything: false,
    reason: '',
  })
  const [statusUrl, setStatusUrl] = useState('')
  const [cacheStatus, setCacheStatus] = useState<CacheStatus | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  // Load analytics on component mount
  useEffect(() => {
    loadAnalytics()
  }, [])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/cache/analytics?period=24h')
      const data = await response.json()
      
      if (data.success) {
        setAnalytics(data.data)
      } else {
        setMessage({ type: 'error', text: data.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to load cache analytics' })
    } finally {
      setLoading(false)
    }
  }

  const handlePurgeCache = async () => {
    try {
      setLoading(true)
      setMessage(null)

      const body: any = {
        reason: purgeOptions.reason || 'Manual purge from admin dashboard',
      }

      if (purgeOptions.everything) {
        body.everything = true
      } else {
        if (purgeOptions.urls) {
          body.urls = purgeOptions.urls.split('\n').filter(url => url.trim())
        }
        if (purgeOptions.tags) {
          body.tags = purgeOptions.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        }
      }

      const response = await fetch('/api/cache/purge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      const data = await response.json()
      
      if (data.success) {
        setMessage({ type: 'success', text: 'Cache purged successfully' })
        // Reload analytics after purge
        setTimeout(loadAnalytics, 2000)
      } else {
        setMessage({ type: 'error', text: data.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to purge cache' })
    } finally {
      setLoading(false)
    }
  }

  const handleCheckStatus = async () => {
    if (!statusUrl) return

    try {
      setLoading(true)
      setCacheStatus(null)

      const response = await fetch(`/api/cache/status?url=${encodeURIComponent(statusUrl)}`)
      const data = await response.json()
      
      if (data.success) {
        setCacheStatus(data.data)
      } else {
        setMessage({ type: 'error', text: data.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to check cache status' })
    } finally {
      setLoading(false)
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  return (
    <div className={`bg-gray-900 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">Cache Management</h2>
        <button
          onClick={loadAnalytics}
          disabled={loading}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {message && (
        <div className={`mb-4 p-3 rounded-lg ${
          message.type === 'success' ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
        }`}>
          {message.text}
        </div>
      )}

      {/* Analytics Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-white mb-4">Cache Analytics (24h)</h3>
        {analytics ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-sm text-gray-400">Cache Hit Ratio</div>
              <div className="text-2xl font-bold text-green-400">
                {analytics.cacheHitRatio.toFixed(1)}%
              </div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-sm text-gray-400">Bandwidth</div>
              <div className="text-2xl font-bold text-blue-400">
                {formatBytes(analytics.bandwidth)}
              </div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-sm text-gray-400">Requests</div>
              <div className="text-2xl font-bold text-purple-400">
                {formatNumber(analytics.requests)}
              </div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-sm text-gray-400">Errors</div>
              <div className="text-2xl font-bold text-red-400">
                {formatNumber(analytics.errors)}
              </div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-sm text-gray-400">Avg Response Time</div>
              <div className="text-2xl font-bold text-yellow-400">
                {analytics.avgResponseTime}ms
              </div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-sm text-gray-400">P95 Response Time</div>
              <div className="text-2xl font-bold text-orange-400">
                {analytics.p95ResponseTime}ms
              </div>
            </div>
          </div>
        ) : (
          <div className="text-gray-400">Loading analytics...</div>
        )}
      </div>

      {/* Cache Purge Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-white mb-4">Cache Purge</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Purge Everything
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={purgeOptions.everything}
                onChange={(e) => setPurgeOptions(prev => ({ ...prev, everything: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-gray-300">Purge entire cache</span>
            </label>
          </div>

          {!purgeOptions.everything && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  URLs (one per line)
                </label>
                <textarea
                  value={purgeOptions.urls}
                  onChange={(e) => setPurgeOptions(prev => ({ ...prev, urls: e.target.value }))}
                  placeholder="https://syndicaps.com/page1&#10;https://syndicaps.com/page2"
                  className="w-full p-3 bg-gray-800 text-white rounded-lg border border-gray-700 focus:border-purple-500"
                  rows={4}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  value={purgeOptions.tags}
                  onChange={(e) => setPurgeOptions(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="static, api, images"
                  className="w-full p-3 bg-gray-800 text-white rounded-lg border border-gray-700 focus:border-purple-500"
                />
              </div>
            </>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Reason (optional)
            </label>
            <input
              type="text"
              value={purgeOptions.reason}
              onChange={(e) => setPurgeOptions(prev => ({ ...prev, reason: e.target.value }))}
              placeholder="Deployment, bug fix, etc."
              className="w-full p-3 bg-gray-800 text-white rounded-lg border border-gray-700 focus:border-purple-500"
            />
          </div>

          <button
            onClick={handlePurgeCache}
            disabled={loading}
            className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
          >
            {loading ? 'Purging...' : 'Purge Cache'}
          </button>
        </div>
      </div>

      {/* Cache Status Check Section */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Cache Status Check</h3>
        <div className="space-y-4">
          <div className="flex gap-4">
            <input
              type="url"
              value={statusUrl}
              onChange={(e) => setStatusUrl(e.target.value)}
              placeholder="https://syndicaps.com/page"
              className="flex-1 p-3 bg-gray-800 text-white rounded-lg border border-gray-700 focus:border-purple-500"
            />
            <button
              onClick={handleCheckStatus}
              disabled={loading || !statusUrl}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              Check Status
            </button>
          </div>

          {cacheStatus && (
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-400">Status</div>
                  <div className={`text-lg font-semibold ${
                    cacheStatus.status === 'HIT' ? 'text-green-400' :
                    cacheStatus.status === 'MISS' ? 'text-yellow-400' :
                    cacheStatus.status === 'BYPASS' ? 'text-red-400' :
                    'text-gray-400'
                  }`}>
                    {cacheStatus.status}
                  </div>
                </div>
                {cacheStatus.age && (
                  <div>
                    <div className="text-sm text-gray-400">Age</div>
                    <div className="text-lg font-semibold text-white">{cacheStatus.age}s</div>
                  </div>
                )}
                {cacheStatus.ttl && (
                  <div>
                    <div className="text-sm text-gray-400">TTL</div>
                    <div className="text-lg font-semibold text-white">{cacheStatus.ttl}s</div>
                  </div>
                )}
                {cacheStatus.etag && (
                  <div>
                    <div className="text-sm text-gray-400">ETag</div>
                    <div className="text-sm font-mono text-white truncate">{cacheStatus.etag}</div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
