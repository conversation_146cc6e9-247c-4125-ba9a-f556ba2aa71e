/**
 * Cache Statistics Component
 * Displays detailed cache performance metrics and statistics
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Database, 
  TrendingUp, 
  TrendingDown, 
  Zap, 
  Clock,
  BarChart3,
  RefreshCw,
  Activity
} from 'lucide-react'
import { CloudflareCacheManager } from '../../lib/cloudflare/cacheManager'

interface CacheStats {
  hitRatio: number
  totalRequests: number
  cacheHits: number
  cacheMisses: number
  bandwidth: number
  avgResponseTime: number
  topCachedUrls: Array<{
    url: string
    hits: number
    size: string
  }>
  cacheRules: Array<{
    pattern: string
    ttl: number
    hitRatio: number
  }>
}

interface CacheStatisticsProps {
  className?: string
}

export default function CacheStatistics({ className = '' }: CacheStatisticsProps) {
  const [stats, setStats] = useState<CacheStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [timeframe, setTimeframe] = useState('24h')
  const cacheManager = new CloudflareCacheManager()

  useEffect(() => {
    loadCacheStats()
    const interval = setInterval(loadCacheStats, 60000) // Update every minute
    return () => clearInterval(interval)
  }, [timeframe])

  const loadCacheStats = async () => {
    try {
      setLoading(true)
      
      // Get cache analytics
      const analytics = await cacheManager.getCacheAnalytics()
      
      // Mock additional statistics for demonstration
      const mockStats: CacheStats = {
        hitRatio: analytics.hitRatio,
        totalRequests: analytics.totalRequests,
        cacheHits: Math.floor(analytics.totalRequests * (analytics.hitRatio / 100)),
        cacheMisses: Math.floor(analytics.totalRequests * (1 - analytics.hitRatio / 100)),
        bandwidth: Math.random() * 1000000000 + 500000000, // Random bandwidth
        avgResponseTime: Math.random() * 200 + 100,
        topCachedUrls: [
          { url: '/api/products', hits: 1250, size: '2.3 MB' },
          { url: '/images/hero.jpg', hits: 980, size: '1.8 MB' },
          { url: '/api/categories', hits: 750, size: '850 KB' },
          { url: '/css/main.css', hits: 650, size: '120 KB' },
          { url: '/js/app.js', hits: 500, size: '450 KB' },
        ],
        cacheRules: [
          { pattern: '*.css', ttl: 86400, hitRatio: 95.2 },
          { pattern: '*.js', ttl: 86400, hitRatio: 92.8 },
          { pattern: '*.jpg', ttl: 604800, hitRatio: 88.5 },
          { pattern: '/api/*', ttl: 300, hitRatio: 75.3 },
          { pattern: '*.png', ttl: 604800, hitRatio: 90.1 },
        ]
      }
      
      setStats(mockStats)
    } catch (error) {
      console.error('Error loading cache statistics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getHitRatioColor = (ratio: number) => {
    if (ratio >= 90) return 'text-green-400'
    if (ratio >= 75) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getTrendIcon = (ratio: number) => {
    if (ratio >= 85) return <TrendingUp className="w-4 h-4 text-green-400" />
    return <TrendingDown className="w-4 h-4 text-red-400" />
  }

  return (
    <div className={`bg-gray-900 rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Database className="w-6 h-6 text-purple-400" />
          <h2 className="text-xl font-bold text-white">Cache Statistics</h2>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={loadCacheStats}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className="bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      {stats ? (
        <>
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Hit Ratio</h3>
                {getTrendIcon(stats.hitRatio)}
              </div>
              <div className={`text-2xl font-bold ${getHitRatioColor(stats.hitRatio)}`}>
                {stats.hitRatio.toFixed(1)}%
              </div>
              <p className="text-xs text-gray-400 mt-1">
                {stats.cacheHits.toLocaleString()} hits / {stats.totalRequests.toLocaleString()} total
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Total Requests</h3>
                <BarChart3 className="w-4 h-4 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {stats.totalRequests.toLocaleString()}
              </div>
              <p className="text-xs text-gray-400 mt-1">
                {stats.cacheMisses.toLocaleString()} misses
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Bandwidth Saved</h3>
                <Zap className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {formatBytes(stats.bandwidth * (stats.hitRatio / 100))}
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Total: {formatBytes(stats.bandwidth)}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Avg Response</h3>
                <Clock className="w-4 h-4 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {stats.avgResponseTime.toFixed(0)}ms
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Cache enabled
              </p>
            </motion.div>
          </div>

          {/* Top Cached URLs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-gray-800 rounded-lg p-6 mb-6"
          >
            <div className="flex items-center space-x-2 mb-4">
              <Activity className="w-5 h-5 text-green-400" />
              <h3 className="text-lg font-semibold text-white">Top Cached URLs</h3>
            </div>
            
            <div className="space-y-3">
              {stats.topCachedUrls.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <div className="flex-1">
                    <div className="text-white font-medium truncate">{item.url}</div>
                    <div className="text-gray-400 text-sm">{item.size}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-green-400 font-semibold">{item.hits.toLocaleString()}</div>
                    <div className="text-gray-400 text-sm">hits</div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Cache Rules Performance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="bg-gray-800 rounded-lg p-6"
          >
            <div className="flex items-center space-x-2 mb-4">
              <BarChart3 className="w-5 h-5 text-purple-400" />
              <h3 className="text-lg font-semibold text-white">Cache Rules Performance</h3>
            </div>
            
            <div className="space-y-3">
              {stats.cacheRules.map((rule, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <div className="flex-1">
                    <div className="text-white font-medium">{rule.pattern}</div>
                    <div className="text-gray-400 text-sm">TTL: {rule.ttl}s</div>
                  </div>
                  <div className="text-right">
                    <div className={`font-semibold ${getHitRatioColor(rule.hitRatio)}`}>
                      {rule.hitRatio.toFixed(1)}%
                    </div>
                    <div className="text-gray-400 text-sm">hit ratio</div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </>
      ) : (
        <div className="text-center py-8">
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
              <p className="text-gray-400">Loading cache statistics...</p>
            </>
          ) : (
            <p className="text-gray-400">No cache data available</p>
          )}
        </div>
      )}
    </div>
  )
}
