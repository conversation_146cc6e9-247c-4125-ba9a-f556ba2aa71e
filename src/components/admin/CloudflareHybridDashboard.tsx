/**
 * Cloudflare Hybrid Dashboard Component
 * 
 * Comprehensive admin dashboard for monitoring and managing
 * Cloudflare hybrid deployment performance and configuration.
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { 
  Activity, 
  Zap, 
  Globe, 
  Database, 
  Image, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings
} from 'lucide-react'

interface DashboardData {
  performance: {
    imageLoadTime: { firebase: number; cloudflare: number }
    apiResponseTime: { firebase: number; cloudflare: number }
    cacheHitRate: number
    coreWebVitals: { lcp: number; fid: number; cls: number }
  }
  usage: {
    r2Storage: { used: number; total: number }
    bandwidth: { firebase: number; cloudflare: number }
    requests: { total: number; cached: number }
  }
  featureFlags: {
    r2Storage: { enabled: boolean; rollout: number }
    workers: { enabled: boolean; rollout: number }
    images: { enabled: boolean; rollout: number }
  }
  alerts: Array<{
    id: string
    level: 'info' | 'warning' | 'critical'
    message: string
    timestamp: number
  }>
  optimizations: Array<{
    id: string
    action: string
    impact: 'low' | 'medium' | 'high'
    improvement: number
    timestamp: number
  }>
}

export default function CloudflareHybridDashboard() {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [autoRefresh, setAutoRefresh] = useState(true)

  useEffect(() => {
    fetchDashboardData()
    
    if (autoRefresh) {
      const interval = setInterval(fetchDashboardData, 30000) // 30 seconds
      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/admin/cloudflare-dashboard')
      const dashboardData = await response.json()
      setData(dashboardData)
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFeatureFlagToggle = async (flag: string, enabled: boolean) => {
    try {
      await fetch('/api/admin/feature-flags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ flag, enabled })
      })
      fetchDashboardData()
    } catch (error) {
      console.error('Failed to update feature flag:', error)
    }
  }

  const handleOptimizationTrigger = async () => {
    try {
      await fetch('/api/admin/trigger-optimization', { method: 'POST' })
      fetchDashboardData()
    } catch (error) {
      console.error('Failed to trigger optimization:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  if (!data) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load dashboard data. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    )
  }

  const performanceData = [
    { name: 'Image Load Time', firebase: data.performance.imageLoadTime.firebase, cloudflare: data.performance.imageLoadTime.cloudflare },
    { name: 'API Response Time', firebase: data.performance.apiResponseTime.firebase, cloudflare: data.performance.apiResponseTime.cloudflare }
  ]

  const coreWebVitalsData = [
    { name: 'LCP', value: data.performance.coreWebVitals.lcp, threshold: 2500 },
    { name: 'FID', value: data.performance.coreWebVitals.fid, threshold: 100 },
    { name: 'CLS', value: data.performance.coreWebVitals.cls, threshold: 0.1 }
  ]

  const usageData = [
    { name: 'Firebase', value: data.usage.bandwidth.firebase, color: '#FF6B35' },
    { name: 'Cloudflare', value: data.usage.bandwidth.cloudflare, color: '#F7931E' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Cloudflare Hybrid Dashboard</h1>
          <p className="text-gray-400">Monitor and manage hybrid deployment performance</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button
            variant={autoRefresh ? "default" : "outline"}
            onClick={() => setAutoRefresh(!autoRefresh)}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Activity className="h-4 w-4 mr-2" />
            Auto Refresh
          </Button>
          <Button onClick={fetchDashboardData} variant="outline">
            Refresh Now
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {data.alerts.length > 0 && (
        <div className="space-y-2">
          {data.alerts.slice(0, 3).map((alert) => (
            <Alert key={alert.id} className={`border-l-4 ${
              alert.level === 'critical' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
              alert.level === 'warning' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
              'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            }`}>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{alert.message}</AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-gray-800">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-300">Cache Hit Rate</CardTitle>
                <Zap className="h-4 w-4 text-purple-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{data.performance.cacheHitRate}%</div>
                <Progress value={data.performance.cacheHitRate} className="mt-2" />
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-300">R2 Storage</CardTitle>
                <Database className="h-4 w-4 text-purple-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">
                  {Math.round((data.usage.r2Storage.used / data.usage.r2Storage.total) * 100)}%
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {(data.usage.r2Storage.used / 1024 / 1024 / 1024).toFixed(2)} GB used
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-300">Total Requests</CardTitle>
                <Globe className="h-4 w-4 text-purple-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">
                  {(data.usage.requests.total / 1000).toFixed(1)}K
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {Math.round((data.usage.requests.cached / data.usage.requests.total) * 100)}% cached
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-300">Optimizations</CardTitle>
                <TrendingUp className="h-4 w-4 text-purple-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{data.optimizations.length}</div>
                <p className="text-xs text-gray-400 mt-1">Last 24 hours</p>
              </CardContent>
            </Card>
          </div>

          {/* Performance Chart */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="name" stroke="#9CA3AF" />
                  <YAxis stroke="#9CA3AF" />
                  <Tooltip 
                    contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151' }}
                    labelStyle={{ color: '#F3F4F6' }}
                  />
                  <Bar dataKey="firebase" fill="#FF6B35" name="Firebase" />
                  <Bar dataKey="cloudflare" fill="#F7931E" name="Cloudflare" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Core Web Vitals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {coreWebVitalsData.map((metric) => (
                    <div key={metric.name} className="flex items-center justify-between">
                      <span className="text-gray-300">{metric.name}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-white font-mono">
                          {metric.name === 'CLS' ? metric.value.toFixed(3) : Math.round(metric.value)}
                          {metric.name !== 'CLS' && 'ms'}
                        </span>
                        <Badge variant={metric.value <= metric.threshold ? "default" : "destructive"}>
                          {metric.value <= metric.threshold ? 'Good' : 'Poor'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Bandwidth Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={usageData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {usageData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Usage Tab */}
        <TabsContent value="usage" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">R2 Storage Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Used</span>
                    <span className="text-white">
                      {(data.usage.r2Storage.used / 1024 / 1024 / 1024).toFixed(2)} GB
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Total</span>
                    <span className="text-white">
                      {(data.usage.r2Storage.total / 1024 / 1024 / 1024).toFixed(2)} GB
                    </span>
                  </div>
                  <Progress 
                    value={(data.usage.r2Storage.used / data.usage.r2Storage.total) * 100} 
                    className="mt-2" 
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Request Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Total Requests</span>
                    <span className="text-white">{data.usage.requests.total.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Cached</span>
                    <span className="text-white">{data.usage.requests.cached.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Cache Rate</span>
                    <span className="text-white">
                      {Math.round((data.usage.requests.cached / data.usage.requests.total) * 100)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Bandwidth Savings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Firebase</span>
                    <span className="text-white">
                      {(data.usage.bandwidth.firebase / 1024 / 1024).toFixed(1)} MB
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Cloudflare</span>
                    <span className="text-white">
                      {(data.usage.bandwidth.cloudflare / 1024 / 1024).toFixed(1)} MB
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Savings</span>
                    <span className="text-green-400">
                      {Math.round((data.usage.bandwidth.cloudflare / (data.usage.bandwidth.firebase + data.usage.bandwidth.cloudflare)) * 100)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Features Tab */}
        <TabsContent value="features" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(data.featureFlags).map(([key, flag]) => (
              <Card key={key} className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white capitalize">{key.replace(/([A-Z])/g, ' $1')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Status</span>
                      <Badge variant={flag.enabled ? "default" : "secondary"}>
                        {flag.enabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-300">Rollout</span>
                        <span className="text-white">{flag.rollout}%</span>
                      </div>
                      <Progress value={flag.rollout} className="h-2" />
                    </div>
                    <Button
                      size="sm"
                      variant={flag.enabled ? "destructive" : "default"}
                      onClick={() => handleFeatureFlagToggle(key, !flag.enabled)}
                      className="w-full"
                    >
                      {flag.enabled ? 'Disable' : 'Enable'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Optimization Tab */}
        <TabsContent value="optimization" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-white">Performance Optimizations</h3>
            <Button onClick={handleOptimizationTrigger} className="bg-purple-600 hover:bg-purple-700">
              <Settings className="h-4 w-4 mr-2" />
              Trigger Optimization
            </Button>
          </div>

          <div className="space-y-4">
            {data.optimizations.map((optimization) => (
              <Card key={optimization.id} className="bg-gray-800 border-gray-700">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        optimization.impact === 'high' ? 'bg-green-500' :
                        optimization.impact === 'medium' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`} />
                      <span className="text-white font-medium">{optimization.action}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge variant={
                        optimization.impact === 'high' ? 'default' :
                        optimization.impact === 'medium' ? 'secondary' :
                        'outline'
                      }>
                        {optimization.impact} impact
                      </Badge>
                      <span className="text-green-400 font-mono">
                        +{optimization.improvement.toFixed(1)}%
                      </span>
                      <span className="text-gray-400 text-sm">
                        {new Date(optimization.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
