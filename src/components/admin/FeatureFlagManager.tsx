/**
 * Feature Flag Management Component for Admin Dashboard
 * 
 * Provides real-time management of Cloudflare hybrid deployment feature flags
 * with emergency controls, rollout management, and analytics.
 */

'use client'

import React, { useState, useEffect } from 'react'
import { 
  getFeatureFlagAnalytics, 
  adjustRolloutPercentage, 
  emergencyRollback,
  type FeatureFlags 
} from '@/lib/config/featureFlags'

interface FeatureFlagData {
  enabled: boolean
  rolloutPercentage: number
  description: string
  dependencies: string[]
  emergencyKillSwitch: boolean
  userTargeting: any
  metrics: any
  healthStatus: 'healthy' | 'warning' | 'critical'
}

export default function FeatureFlagManager() {
  const [flags, setFlags] = useState<Record<string, FeatureFlagData>>({})
  const [loading, setLoading] = useState(true)
  const [selectedFlag, setSelectedFlag] = useState<string | null>(null)

  useEffect(() => {
    loadFeatureFlags()
  }, [])

  const loadFeatureFlags = () => {
    setLoading(true)
    try {
      const flagData = getFeatureFlagAnalytics()
      setFlags(flagData)
    } catch (error) {
      console.error('Failed to load feature flags:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRolloutChange = async (flagName: string, newPercentage: number) => {
    try {
      const success = adjustRolloutPercentage(
        flagName as keyof FeatureFlags, 
        newPercentage,
        'Admin dashboard adjustment'
      )
      
      if (success) {
        loadFeatureFlags()
      }
    } catch (error) {
      console.error('Failed to adjust rollout:', error)
    }
  }

  const handleEmergencyRollback = async (flagName: string) => {
    if (confirm(`Are you sure you want to trigger emergency rollback for ${flagName}?`)) {
      try {
        emergencyRollback(
          flagName as keyof FeatureFlags,
          'Manual emergency rollback from admin dashboard'
        )
        loadFeatureFlags()
      } catch (error) {
        console.error('Failed to trigger emergency rollback:', error)
      }
    }
  }

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'critical': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return '✅'
      case 'warning': return '⚠️'
      case 'critical': return '🚨'
      default: return '❓'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-2 text-gray-600">Loading feature flags...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Feature Flag Management</h2>
          <p className="text-gray-600">Manage Cloudflare hybrid deployment feature flags</p>
        </div>
        <button
          onClick={loadFeatureFlags}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Refresh
        </button>
      </div>

      {/* Feature Flags Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Object.entries(flags).map(([flagName, flag]) => (
          <div
            key={flagName}
            className="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
          >
            {/* Flag Header */}
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="font-semibold text-gray-900 text-sm">
                  {flagName.replace(/_/g, ' ')}
                </h3>
                <p className="text-xs text-gray-600 mt-1">{flag.description}</p>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getHealthStatusColor(flag.healthStatus)}`}>
                {getHealthStatusIcon(flag.healthStatus)} {flag.healthStatus}
              </div>
            </div>

            {/* Status Indicators */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Enabled:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  flag.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {flag.enabled ? 'Yes' : 'No'}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Rollout:</span>
                <span className="text-sm font-medium">{flag.rolloutPercentage}%</span>
              </div>

              {flag.metrics && (
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Success Rate:</span>
                    <span className="text-sm font-medium text-green-600">
                      {flag.metrics.successRate?.toFixed(1) || 0}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Error Rate:</span>
                    <span className="text-sm font-medium text-red-600">
                      {flag.metrics.errorRate?.toFixed(1) || 0}%
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Rollout Control */}
            {flag.enabled && !flag.emergencyKillSwitch && (
              <div className="mt-4">
                <label className="block text-sm text-gray-600 mb-2">
                  Rollout Percentage:
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={flag.rolloutPercentage}
                  onChange={(e) => handleRolloutChange(flagName, parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0%</span>
                  <span>50%</span>
                  <span>100%</span>
                </div>
              </div>
            )}

            {/* Dependencies */}
            {flag.dependencies && flag.dependencies.length > 0 && (
              <div className="mt-4">
                <span className="text-sm text-gray-600">Dependencies:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {flag.dependencies.map((dep) => (
                    <span
                      key={dep}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                    >
                      {dep.replace(/_/g, ' ')}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Emergency Controls */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              {flag.emergencyKillSwitch ? (
                <div className="text-center">
                  <span className="text-red-600 font-medium text-sm">🚨 Emergency Disabled</span>
                </div>
              ) : (
                <button
                  onClick={() => handleEmergencyRollback(flagName)}
                  className="w-full px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
                >
                  Emergency Rollback
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Legend */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">Health Status Legend</h3>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="flex items-center">
            <span className="text-green-600">✅</span>
            <span className="ml-2">Healthy: Error rate &lt; 10%</span>
          </div>
          <div className="flex items-center">
            <span className="text-yellow-600">⚠️</span>
            <span className="ml-2">Warning: Error rate 10-25%</span>
          </div>
          <div className="flex items-center">
            <span className="text-red-600">🚨</span>
            <span className="ml-2">Critical: Error rate &gt; 25%</span>
          </div>
        </div>
      </div>
    </div>
  )
}
