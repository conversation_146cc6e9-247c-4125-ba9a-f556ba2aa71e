/**
 * Segmentation and A/B Testing Dashboard
 * 
 * Comprehensive admin dashboard for user segmentation, A/B testing,
 * and personalization management with real-time analytics.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  useSegmentation, 
  useABTesting, 
  useSegmentationInsights,
  useSegmentBuilder,
  TestCreationData
} from '../../hooks/useSegmentation'
import { 
  UserSegment, 
  ABTest, 
  SegmentCriteria, 
  TestVariant, 
  TestMetric,
  TestResults
} from '../../lib/api/gamification/segmentationEngine'
import { useNotifications } from '../../lib/notifications/NotificationSystem'

// ===== TYPES =====

interface TabConfig {
  id: string
  label: string
  icon: string
}

// ===== MAIN DASHBOARD COMPONENT =====

export function SegmentationDashboard() {
  const [activeTab, setActiveTab] = useState('segments')
  const notifications = useNotifications()

  const tabs: TabConfig[] = [
    { id: 'segments', label: 'User Segments', icon: '👥' },
    { id: 'tests', label: 'A/B Tests', icon: '🧪' },
    { id: 'insights', label: 'Insights', icon: '📊' },
    { id: 'builder', label: 'Segment Builder', icon: '🔧' }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Segmentation & A/B Testing
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage user segments, run experiments, and optimize engagement
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-8 bg-gray-200 dark:bg-gray-800 rounded-lg p-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-700 text-accent-600 dark:text-accent-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <span className="text-lg">{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'segments' && <SegmentsTab />}
            {activeTab === 'tests' && <ABTestsTab />}
            {activeTab === 'insights' && <InsightsTab />}
            {activeTab === 'builder' && <SegmentBuilderTab />}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  )
}

// ===== SEGMENTS TAB =====

function SegmentsTab() {
  const { segments, isLoading, error, createSegment, getSegmentUsers } = useSegmentation()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedSegment, setSelectedSegment] = useState<UserSegment | null>(null)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 text-lg mb-2">Failed to load segments</div>
        <div className="text-gray-600 dark:text-gray-400">{error}</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            User Segments ({segments.length})
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Organize users into targeted groups for personalized experiences
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
        >
          <span>➕</span>
          <span>Create Segment</span>
        </button>
      </div>

      {/* Segments Grid */}
      {segments.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {segments.map((segment) => (
            <SegmentCard
              key={segment.id}
              segment={segment}
              onClick={() => setSelectedSegment(segment)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">👥</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No segments created yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Create your first user segment to start personalizing experiences
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-accent-600 hover:bg-accent-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Create First Segment
          </button>
        </div>
      )}

      {/* Create Segment Modal */}
      {showCreateModal && (
        <CreateSegmentModal
          onClose={() => setShowCreateModal(false)}
          onCreated={() => {
            setShowCreateModal(false)
            // Segments will refresh automatically
          }}
        />
      )}

      {/* Segment Details Modal */}
      {selectedSegment && (
        <SegmentDetailsModal
          segment={selectedSegment}
          onClose={() => setSelectedSegment(null)}
        />
      )}
    </div>
  )
}

// ===== SEGMENT CARD COMPONENT =====

interface SegmentCardProps {
  segment: UserSegment
  onClick: () => void
}

function SegmentCard({ segment, onClick }: SegmentCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 cursor-pointer hover:shadow-md transition-all duration-200"
    >
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
            {segment.name}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
            {segment.description}
          </p>
        </div>
        
        <div className={`px-2 py-1 rounded text-xs font-medium ${
          segment.isActive 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
        }`}>
          {segment.isActive ? 'Active' : 'Inactive'}
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400">Users</span>
          <span className="font-semibold text-gray-900 dark:text-white">
            {segment.userCount.toLocaleString()}
          </span>
        </div>

        {/* Criteria Summary */}
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <div>Criteria:</div>
          <ul className="list-disc list-inside mt-1 space-y-1">
            {segment.criteria.pointsRange && (
              <li>
                Points: {segment.criteria.pointsRange.min || 0}
                {segment.criteria.pointsRange.max ? `-${segment.criteria.pointsRange.max}` : '+'}
              </li>
            )}
            {segment.criteria.tierIds && segment.criteria.tierIds.length > 0 && (
              <li>Tiers: {segment.criteria.tierIds.length} selected</li>
            )}
            {segment.criteria.activityLevel && (
              <li>Activity: {segment.criteria.activityLevel}</li>
            )}
          </ul>
        </div>
      </div>
    </motion.div>
  )
}

// ===== A/B TESTS TAB =====

function ABTestsTab() {
  const { tests, isLoading, error, createTest, startTest, getTestResults } = useABTesting()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedTest, setSelectedTest] = useState<ABTest | null>(null)
  const [testResults, setTestResults] = useState<Record<string, TestResults>>({})

  // Load test results for completed tests
  useEffect(() => {
    const loadResults = async () => {
      const completedTests = tests.filter(test => test.status === 'completed')
      const results: Record<string, TestResults> = {}
      
      for (const test of completedTests) {
        try {
          const result = await getTestResults(test.id)
          if (result) {
            results[test.id] = result
          }
        } catch (error) {
          console.error(`Failed to load results for test ${test.id}:`, error)
        }
      }
      
      setTestResults(results)
    }

    if (tests.length > 0) {
      loadResults()
    }
  }, [tests, getTestResults])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            A/B Tests ({tests.length})
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Run experiments to optimize gamification features
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
        >
          <span>🧪</span>
          <span>Create Test</span>
        </button>
      </div>

      {/* Tests List */}
      {tests.length > 0 ? (
        <div className="space-y-4">
          {tests.map((test) => (
            <ABTestCard
              key={test.id}
              test={test}
              results={testResults[test.id]}
              onStart={() => startTest(test.id)}
              onClick={() => setSelectedTest(test)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🧪</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No A/B tests created yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Create your first A/B test to start optimizing user experience
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-accent-600 hover:bg-accent-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Create First Test
          </button>
        </div>
      )}

      {/* Create Test Modal */}
      {showCreateModal && (
        <CreateTestModal
          onClose={() => setShowCreateModal(false)}
          onCreated={() => {
            setShowCreateModal(false)
            // Tests will refresh automatically
          }}
        />
      )}
    </div>
  )
}

// ===== A/B TEST CARD COMPONENT =====

interface ABTestCardProps {
  test: ABTest
  results?: TestResults
  onStart: () => void
  onClick: () => void
}

function ABTestCard({ test, results, onStart, onClick }: ABTestCardProps) {
  const statusColors = {
    draft: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    running: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    paused: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
            {test.name}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
            {test.description}
          </p>
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
            <span>Feature: {test.feature}</span>
            <span>Traffic: {test.trafficAllocation}%</span>
            <span>Variants: {test.variants.length}</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className={`px-2 py-1 rounded text-xs font-medium ${statusColors[test.status]}`}>
            {test.status.charAt(0).toUpperCase() + test.status.slice(1)}
          </div>
          
          {test.status === 'draft' && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                onStart()
              }}
              className="bg-accent-600 hover:bg-accent-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
            >
              Start Test
            </button>
          )}
        </div>
      </div>

      {/* Test Results Preview */}
      {results && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mt-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Results Summary</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-600 dark:text-gray-400">Significance</div>
              <div className="font-semibold text-gray-900 dark:text-white">
                {(results.statisticalSignificance * 100).toFixed(1)}%
              </div>
            </div>
            <div>
              <div className="text-gray-600 dark:text-gray-400">Recommendation</div>
              <div className="font-semibold text-gray-900 dark:text-white capitalize">
                {results.recommendedAction.replace('_', ' ')}
              </div>
            </div>
            {results.winningVariant && (
              <div>
                <div className="text-gray-600 dark:text-gray-400">Winner</div>
                <div className="font-semibold text-green-600 dark:text-green-400">
                  {results.winningVariant}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <button
        onClick={onClick}
        className="mt-4 text-accent-600 hover:text-accent-700 text-sm font-medium transition-colors"
      >
        View Details →
      </button>
    </div>
  )
}

// ===== INSIGHTS TAB =====

function InsightsTab() {
  const { insights, isLoading, error } = useSegmentationInsights()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  if (error || !insights) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 text-lg mb-2">Failed to load insights</div>
        <div className="text-gray-600 dark:text-gray-400">{error}</div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Segmentation Insights
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Key metrics and opportunities from your user segments
        </p>
      </div>

      {/* Top Performing Segments */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Top Performing Segments
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {insights.topSegments.map(({ segment, metrics }, index) => (
            <div 
              key={segment.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {segment.name}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {segment.userCount.toLocaleString()} users
                  </p>
                </div>
                <div className="text-2xl">#{index + 1}</div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-gray-600 dark:text-gray-400">Avg Points</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {metrics.averagePoints.toLocaleString()}
                  </div>
                </div>
                <div>
                  <div className="text-gray-600 dark:text-gray-400">Achievement Rate</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {metrics.achievementRate.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className="text-gray-600 dark:text-gray-400">Retention</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {metrics.retentionRate.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className="text-gray-600 dark:text-gray-400">Engagement</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {metrics.engagementScore.toFixed(1)}/10
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Growth Opportunities */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Growth Opportunities
        </h3>
        <div className="space-y-4">
          {insights.growthOpportunities.map((opportunity, index) => (
            <div 
              key={index}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-gray-900 dark:text-white">
                  {opportunity.segment}
                </h4>
                <div className="text-sm font-medium text-accent-600 dark:text-accent-400">
                  {opportunity.potential}% potential growth
                </div>
              </div>
              
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Recommendations:
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300">
                  {opportunity.recommendations.map((rec, recIndex) => (
                    <li key={recIndex}>{rec}</li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Segments */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          At-Risk Segments
        </h3>
        <div className="space-y-4">
          {insights.riskSegments.map((risk, index) => (
            <div 
              key={index}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-gray-900 dark:text-white">
                  {risk.segment}
                </h4>
                <div className={`px-2 py-1 rounded text-xs font-medium ${
                  risk.riskLevel === 'high' 
                    ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    : risk.riskLevel === 'medium'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                }`}>
                  {risk.riskLevel} risk
                </div>
              </div>
              
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Risk indicators:
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300">
                  {risk.indicators.map((indicator, indicatorIndex) => (
                    <li key={indicatorIndex}>{indicator}</li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// ===== SEGMENT BUILDER TAB =====

function SegmentBuilderTab() {
  const { criteria, estimatedSize, isCalculating, updateCriteria, resetCriteria } = useSegmentBuilder()
  const { createSegment } = useSegmentation()
  const [segmentName, setSegmentName] = useState('')
  const [segmentDescription, setSegmentDescription] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const notifications = useNotifications()

  const handleCreateSegment = async () => {
    if (!segmentName.trim()) {
      notifications.addNotification({ title: 'Validation Error', message: 'Please enter a segment name', type: 'error' })
      return
    }

    if (!segmentDescription.trim()) {
      notifications.addNotification({ title: 'Validation Error', message: 'Please enter a segment description', type: 'error' })
      return
    }

    if (Object.keys(criteria).length === 0) {
      notifications.addNotification({ title: 'Validation Error', message: 'Please add at least one criteria', type: 'error' })
      return
    }

    setIsCreating(true)
    try {
      const segmentId = await createSegment(segmentName, segmentDescription, criteria)
      if (segmentId) {
        notifications.addNotification({ title: 'Success', message: `"${segmentName}" has been created successfully`, type: 'success' })
        setSegmentName('')
        setSegmentDescription('')
        resetCriteria()
      }
    } catch (error) {
      notifications.addNotification({ title: 'Error', message: 'Unable to create segment. Please try again.', type: 'error' })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Segment Builder
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Create custom user segments with advanced criteria
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Criteria Builder */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">
              Segment Information
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Segment Name
                </label>
                <input
                  type="text"
                  value={segmentName}
                  onChange={(e) => setSegmentName(e.target.value)}
                  placeholder="e.g., High Value Users"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={segmentDescription}
                  onChange={(e) => setSegmentDescription(e.target.value)}
                  placeholder="Describe this segment and its intended use"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
          </div>

          {/* Points Range */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">
              Points Range
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Minimum Points
                </label>
                <input
                  type="number"
                  value={criteria.pointsRange?.min || ''}
                  onChange={(e) => updateCriteria({
                    pointsRange: {
                      ...criteria.pointsRange,
                      min: e.target.value ? parseInt(e.target.value) : undefined
                    }
                  })}
                  placeholder="0"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Points
                </label>
                <input
                  type="number"
                  value={criteria.pointsRange?.max || ''}
                  onChange={(e) => updateCriteria({
                    pointsRange: {
                      ...criteria.pointsRange,
                      max: e.target.value ? parseInt(e.target.value) : undefined
                    }
                  })}
                  placeholder="No limit"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
          </div>

          {/* Activity Level */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">
              Activity Level
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {['inactive', 'low', 'medium', 'high'].map((level) => (
                <button
                  key={level}
                  onClick={() => updateCriteria({
                    activityLevel: criteria.activityLevel === level ? undefined : level as any
                  })}
                  className={`px-3 py-2 rounded-lg border text-sm font-medium transition-colors ${
                    criteria.activityLevel === level
                      ? 'bg-accent-600 border-accent-600 text-white'
                      : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                  }`}
                >
                  {level.charAt(0).toUpperCase() + level.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          {/* Size Estimate */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">
              Estimated Size
            </h3>
            
            {isCalculating ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-accent-500"></div>
                <span className="text-gray-600 dark:text-gray-400">Calculating...</span>
              </div>
            ) : estimatedSize !== null ? (
              <div>
                <div className="text-3xl font-bold text-accent-600 dark:text-accent-400">
                  {estimatedSize.toLocaleString()}
                </div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">
                  users match this criteria
                </div>
              </div>
            ) : (
              <div className="text-gray-600 dark:text-gray-400">
                Add criteria to see size estimate
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="space-y-3">
            <button
              onClick={handleCreateSegment}
              disabled={isCreating || !segmentName.trim() || Object.keys(criteria).length === 0}
              className="w-full bg-accent-600 hover:bg-accent-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-3 rounded-lg font-medium transition-colors"
            >
              {isCreating ? 'Creating...' : 'Create Segment'}
            </button>
            
            <button
              onClick={resetCriteria}
              className="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Reset Criteria
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// ===== PLACEHOLDER MODALS =====

function CreateSegmentModal({ onClose, onCreated }: { onClose: () => void; onCreated: () => void }) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Create Segment
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Use the Segment Builder tab for a more comprehensive creation experience.
        </p>
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

function SegmentDetailsModal({ segment, onClose }: { segment: UserSegment; onClose: () => void }) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full p-6 max-h-96 overflow-y-auto">
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {segment.name}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-400">{segment.description}</p>
          
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Criteria</h4>
            <pre className="bg-gray-100 dark:bg-gray-700 p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(segment.criteria, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

function CreateTestModal({ onClose, onCreated }: { onClose: () => void; onCreated: () => void }) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Create A/B Test
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          A/B test creation functionality would be implemented here with a comprehensive form.
        </p>
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default SegmentationDashboard