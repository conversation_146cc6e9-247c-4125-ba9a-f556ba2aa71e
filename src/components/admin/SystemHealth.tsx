/**
 * System Health Component
 * Displays overall system health status and service monitoring
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Clock,
  Activity,
  Server,
  Database,
  Zap,
  Globe,
  RefreshCw
} from 'lucide-react'

interface ServiceStatus {
  name: string
  status: 'healthy' | 'warning' | 'critical' | 'unknown'
  responseTime: number
  uptime: number
  lastCheck: string
  endpoint?: string
}

interface SystemHealthData {
  overallStatus: 'healthy' | 'warning' | 'critical'
  services: ServiceStatus[]
  metrics: {
    totalUptime: number
    avgResponseTime: number
    errorRate: number
    activeConnections: number
  }
}

interface SystemHealthProps {
  className?: string
}

export default function SystemHealth({ className = '' }: SystemHealthProps) {
  const [healthData, setHealthData] = useState<SystemHealthData | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadHealthData()
    const interval = setInterval(loadHealthData, 30000) // Update every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const loadHealthData = async () => {
    try {
      setLoading(true)
      
      // Mock system health data
      const mockData: SystemHealthData = {
        overallStatus: 'healthy',
        services: [
          {
            name: 'Cloudflare CDN',
            status: 'healthy',
            responseTime: 45,
            uptime: 99.98,
            lastCheck: new Date().toISOString(),
            endpoint: 'https://api.cloudflare.com/client/v4'
          },
          {
            name: 'Firebase Auth',
            status: 'healthy',
            responseTime: 120,
            uptime: 99.95,
            lastCheck: new Date().toISOString(),
            endpoint: 'https://identitytoolkit.googleapis.com'
          },
          {
            name: 'Firebase Firestore',
            status: 'warning',
            responseTime: 250,
            uptime: 99.85,
            lastCheck: new Date().toISOString(),
            endpoint: 'https://firestore.googleapis.com'
          },
          {
            name: 'R2 Storage',
            status: 'healthy',
            responseTime: 80,
            uptime: 99.99,
            lastCheck: new Date().toISOString(),
            endpoint: 'https://r2.cloudflarestorage.com'
          },
          {
            name: 'Workers',
            status: 'healthy',
            responseTime: 35,
            uptime: 99.97,
            lastCheck: new Date().toISOString(),
            endpoint: 'https://workers.cloudflare.com'
          },
          {
            name: 'KV Storage',
            status: 'healthy',
            responseTime: 25,
            uptime: 99.99,
            lastCheck: new Date().toISOString(),
            endpoint: 'https://kv.cloudflare.com'
          }
        ],
        metrics: {
          totalUptime: 99.92,
          avgResponseTime: 92,
          errorRate: 0.08,
          activeConnections: 1247
        }
      }
      
      setHealthData(mockData)
    } catch (error) {
      console.error('Error loading health data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-400" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'healthy':
        return 'border-green-500 bg-green-900/20'
      case 'warning':
        return 'border-yellow-500 bg-yellow-900/20'
      case 'critical':
        return 'border-red-500 bg-red-900/20'
      default:
        return 'border-gray-500 bg-gray-900/20'
    }
  }

  const getOverallStatusColor = (status: SystemHealthData['overallStatus']) => {
    switch (status) {
      case 'healthy':
        return 'text-green-400'
      case 'warning':
        return 'text-yellow-400'
      case 'critical':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getServiceIcon = (serviceName: string) => {
    if (serviceName.includes('CDN') || serviceName.includes('Cloudflare')) {
      return <Globe className="w-4 h-4" />
    }
    if (serviceName.includes('Auth')) {
      return <Shield className="w-4 h-4" />
    }
    if (serviceName.includes('Storage') || serviceName.includes('Firestore')) {
      return <Database className="w-4 h-4" />
    }
    if (serviceName.includes('Workers')) {
      return <Zap className="w-4 h-4" />
    }
    return <Server className="w-4 h-4" />
  }

  return (
    <div className={`bg-gray-900 rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Activity className="w-6 h-6 text-green-400" />
          <h2 className="text-xl font-bold text-white">System Health</h2>
          {healthData && (
            <span className={`text-sm font-medium ${getOverallStatusColor(healthData.overallStatus)}`}>
              ({healthData.overallStatus.toUpperCase()})
            </span>
          )}
        </div>
        
        <button
          onClick={loadHealthData}
          disabled={loading}
          className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {healthData ? (
        <>
          {/* System Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Overall Uptime</h3>
                <Activity className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-green-400">
                {healthData.metrics.totalUptime.toFixed(2)}%
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Avg Response</h3>
                <Clock className="w-4 h-4 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {healthData.metrics.avgResponseTime}ms
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Error Rate</h3>
                <AlertTriangle className="w-4 h-4 text-yellow-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {healthData.metrics.errorRate.toFixed(2)}%
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-gray-800 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Active Connections</h3>
                <Server className="w-4 h-4 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {healthData.metrics.activeConnections.toLocaleString()}
              </div>
            </motion.div>
          </div>

          {/* Service Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-gray-800 rounded-lg p-6"
          >
            <div className="flex items-center space-x-2 mb-4">
              <Server className="w-5 h-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Service Status</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {healthData.services.map((service, index) => (
                <motion.div
                  key={service.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  className={`p-4 rounded-lg border ${getStatusColor(service.status)}`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getServiceIcon(service.name)}
                      <h4 className="font-medium text-white">{service.name}</h4>
                    </div>
                    {getStatusIcon(service.status)}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-400">Response Time</p>
                      <p className="text-white font-medium">{service.responseTime}ms</p>
                    </div>
                    <div>
                      <p className="text-gray-400">Uptime</p>
                      <p className="text-white font-medium">{service.uptime.toFixed(2)}%</p>
                    </div>
                  </div>
                  
                  <div className="mt-2 text-xs text-gray-400">
                    Last check: {new Date(service.lastCheck).toLocaleTimeString()}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </>
      ) : (
        <div className="text-center py-8">
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
              <p className="text-gray-400">Loading system health data...</p>
            </>
          ) : (
            <p className="text-gray-400">No health data available</p>
          )}
        </div>
      )}
    </div>
  )
}
