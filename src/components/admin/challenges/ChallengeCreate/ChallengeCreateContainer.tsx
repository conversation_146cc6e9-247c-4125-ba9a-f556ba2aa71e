/**
 * Challenge Create Container Component
 * 
 * Main orchestrator for the refactored challenge creation system
 * Manages state, navigation, and renders appropriate step components
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, X, Save, Send } from 'lucide-react'
import { toast } from 'react-hot-toast'

// Hooks
import { useChallengeForm, useStepNavigation } from './hooks/useChallengeForm'
import { useChallengeSubmission, useChallengeDraft, useChallengeValidation } from './hooks/useChallengeSubmission'

// Types
import { 
  ChallengeCreateContainerProps,
  STEP_TITLES,
  CHALLENGE_STEPS
} from './types/challengeTypes'

// Step Components
import BasicInfoStep from './steps/BasicInfoStep'
import ScheduleStep from './steps/ScheduleStep'
import RequirementsStep from './steps/RequirementsStep'
import RewardsStep from './steps/RewardsStep'
import MediaStep from './steps/MediaStep'

/**
 * Main challenge create container component
 */
export const ChallengeCreateContainer: React.FC<ChallengeCreateContainerProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  // Form management
  const { formState, updateFormState, resetForm, validateCurrentStep, validateAllSteps } = useChallengeForm()
  const { submitChallenge, isSubmitting, submissionError } = useChallengeSubmission()
  const { saveDraft, isDraftSaving } = useChallengeDraft()
  const { validateForSubmission } = useChallengeValidation()
  
  // Step navigation
  const { nextStep, prevStep, getStepProgress } = useStepNavigation(
    formState.currentStep,
    updateFormState,
    validateCurrentStep
  )

  /**
   * Handle next step navigation
   */
  const handleNext = useCallback(() => {
    const success = nextStep()
    if (success) {
      console.log('[ChallengeCreate] Moved to next step:', formState.currentStep)
    }
  }, [nextStep, formState.currentStep])

  /**
   * Handle previous step navigation
   */
  const handlePrev = useCallback(() => {
    const success = prevStep()
    if (success) {
      console.log('[ChallengeCreate] Moved to previous step:', formState.currentStep)
    }
  }, [prevStep, formState.currentStep])

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async () => {
    console.log('[ChallengeCreate] Starting submission process')
    
    // Validate all steps
    const allStepsValidation = validateAllSteps()
    if (!allStepsValidation.isValid) {
      updateFormState({ errors: allStepsValidation.errors })
      toast.error('Please fix validation errors before submitting')
      return
    }

    // Final validation for submission
    const submissionValidation = validateForSubmission(formState.formData)
    if (!submissionValidation.isValid) {
      toast.error('Validation failed: ' + submissionValidation.errors.join(', '))
      return
    }

    // Submit challenge
    const success = await submitChallenge(formState.formData)
    if (success) {
      resetForm()
      onSuccess()
      onClose()
    }
  }, [formState.formData, validateAllSteps, validateForSubmission, submitChallenge, updateFormState, resetForm, onSuccess, onClose])

  /**
   * Handle save as draft
   */
  const handleSaveDraft = useCallback(async () => {
    console.log('[ChallengeCreate] Saving as draft')
    
    const draftData = { ...formState.formData, status: 'draft' as const }
    const success = await saveDraft(draftData)
    
    if (success) {
      toast.success('Draft saved successfully!')
    }
  }, [formState.formData, saveDraft])

  /**
   * Handle close with confirmation
   */
  const handleClose = useCallback(() => {
    if (formState.isDirty) {
      const confirmed = window.confirm('You have unsaved changes. Are you sure you want to close?')
      if (!confirmed) return
    }
    
    resetForm()
    onClose()
  }, [formState.isDirty, resetForm, onClose])

  /**
   * Render current step component
   */
  const renderCurrentStep = () => {
    const stepProps = {
      formState,
      onUpdateFormState: updateFormState,
      onNext: handleNext,
      onPrev: handlePrev
    }

    switch (formState.currentStep) {
      case 'basic':
        return <BasicInfoStep {...stepProps} />
      case 'schedule':
        return <ScheduleStep {...stepProps} />
      case 'requirements':
        return <RequirementsStep {...stepProps} />
      case 'rewards':
        return <RewardsStep {...stepProps} />
      case 'media':
        return <MediaStep {...stepProps} />
      default:
        return null
    }
  }

  /**
   * Get progress information
   */
  const progress = getStepProgress()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-6xl w-full max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-white">
                Create New Challenge
              </h2>
              <div className="flex items-center gap-4 mt-2">
                {/* Progress Bar */}
                <div className="flex items-center gap-2">
                  <div className="w-48 bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress.percentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-400">
                    {progress.current} of {progress.total}
                  </span>
                </div>
                
                {/* Current Step */}
                <span className="text-sm text-purple-400 font-medium">
                  {STEP_TITLES[formState.currentStep]}
                </span>
              </div>
            </div>
            
            <button
              onClick={handleClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Step Navigation */}
        <div className="px-6 py-3 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between">
            {CHALLENGE_STEPS.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center ${index < CHALLENGE_STEPS.length - 1 ? 'flex-1' : ''}`}
              >
                <div className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                  formState.currentStep === step.id
                    ? 'bg-purple-600 text-white'
                    : progress.current > index + 1
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-700 text-gray-400'
                }`}>
                  <span className="text-sm font-medium">{index + 1}</span>
                  <span className="text-sm hidden md:block">{step.title}</span>
                </div>
                {index < CHALLENGE_STEPS.length - 1 && (
                  <div className="flex-1 h-0.5 bg-gray-700 mx-2" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6 max-h-[calc(95vh-250px)] overflow-y-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={formState.currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {renderCurrentStep()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-800 border-t border-gray-700">
          <div className="flex items-center justify-between">
            {/* Left Side - Previous Button */}
            <div>
              {!progress.isFirst ? (
                <button
                  onClick={handlePrev}
                  className="flex items-center gap-2 px-4 py-2 text-gray-300 hover:text-white transition-colors"
                >
                  <ChevronLeft size={20} />
                  Previous
                </button>
              ) : (
                <div />
              )}
            </div>

            {/* Center - Save Draft */}
            <button
              onClick={handleSaveDraft}
              disabled={isDraftSaving}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              {isDraftSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save size={16} />
                  Save Draft
                </>
              )}
            </button>

            {/* Right Side - Next/Submit Button */}
            <div>
              {progress.isLast ? (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex items-center gap-2 px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Send size={16} />
                      Create Challenge
                    </>
                  )}
                </button>
              ) : (
                <button
                  onClick={handleNext}
                  className="flex items-center gap-2 px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  Next Step
                  <ChevronRight size={20} />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Submission Error */}
        {submissionError && (
          <div className="px-6 py-3 bg-red-500/20 border-t border-red-500">
            <p className="text-red-400 text-sm">{submissionError}</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ChallengeCreateContainer
