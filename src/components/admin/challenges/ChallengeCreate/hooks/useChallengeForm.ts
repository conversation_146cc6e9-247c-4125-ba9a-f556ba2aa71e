/**
 * Challenge Form Management Hook
 * 
 * Centralized state management and validation for challenge creation form
 * 
 * <AUTHOR> Team
 */

'use client'

import { useState, useCallback } from 'react'
import {
  ChallengeFormState,
  ChallengeFormData,
  ChallengeFormErrors,
  ChallengeFormStep,
  ValidationResult,
  UseChallengeFormReturn,
  INITIAL_CHALLENGE_FORM_STATE,
  STEP_ORDER
} from '../types/challengeTypes'

/**
 * Challenge form management hook
 */
export const useChallengeForm = (): UseChallengeFormReturn => {
  const [formState, setFormState] = useState<ChallengeFormState>(INITIAL_CHALLENGE_FORM_STATE)

  /**
   * Update form state
   */
  const updateFormState = useCallback((updates: Partial<ChallengeFormState>) => {
    setFormState(prev => ({
      ...prev,
      ...updates,
      isDirty: true
    }))
  }, [])

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    setFormState(INITIAL_CHALLENGE_FORM_STATE)
  }, [])

  /**
   * Validate basic information step
   */
  const validateBasicStep = (formData: ChallengeFormData): ChallengeFormErrors => {
    const errors: ChallengeFormErrors = {}

    if (!formData.title.trim()) {
      errors.title = 'Challenge title is required'
    } else if (formData.title.length < 5) {
      errors.title = 'Title must be at least 5 characters long'
    } else if (formData.title.length > 100) {
      errors.title = 'Title must be less than 100 characters'
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required'
    } else if (formData.description.length < 20) {
      errors.description = 'Description must be at least 20 characters long'
    }

    if (!formData.shortDescription.trim()) {
      errors.shortDescription = 'Short description is required'
    } else if (formData.shortDescription.length > 200) {
      errors.shortDescription = 'Short description must be less than 200 characters'
    }

    if (!formData.theme.trim()) {
      errors.theme = 'Theme is required'
    }

    if (!formData.category.trim()) {
      errors.category = 'Category is required'
    }

    return errors
  }

  /**
   * Validate schedule step
   */
  const validateScheduleStep = (formData: ChallengeFormData): ChallengeFormErrors => {
    const errors: ChallengeFormErrors = {}

    if (!formData.startDate) {
      errors.startDate = 'Start date is required'
    }

    if (!formData.endDate) {
      errors.endDate = 'End date is required'
    }

    if (!formData.votingStartDate) {
      errors.votingStartDate = 'Voting start date is required'
    }

    if (!formData.votingEndDate) {
      errors.votingEndDate = 'Voting end date is required'
    }

    // Date validation logic
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate)
      const endDate = new Date(formData.endDate)
      
      if (startDate >= endDate) {
        errors.endDate = 'End date must be after start date'
      }
    }

    if (formData.votingStartDate && formData.votingEndDate) {
      const votingStart = new Date(formData.votingStartDate)
      const votingEnd = new Date(formData.votingEndDate)
      
      if (votingStart >= votingEnd) {
        errors.votingEndDate = 'Voting end date must be after voting start date'
      }
    }

    if (formData.endDate && formData.votingStartDate) {
      const endDate = new Date(formData.endDate)
      const votingStart = new Date(formData.votingStartDate)
      
      if (votingStart < endDate) {
        errors.votingStartDate = 'Voting should start after challenge ends'
      }
    }

    return errors
  }

  /**
   * Validate requirements step
   */
  const validateRequirementsStep = (formData: ChallengeFormData): ChallengeFormErrors => {
    const errors: ChallengeFormErrors = {}

    if (formData.rules.length === 0) {
      errors.rules = 'At least one rule is required'
    }

    if (formData.requirements.maxSubmissions < 1) {
      errors.requirements = 'Maximum submissions must be at least 1'
    }

    if (formData.requirements.allowedFileTypes.length === 0) {
      errors.requirements = 'At least one file type must be allowed'
    }

    if (formData.requirements.maxFileSize < 1) {
      errors.requirements = 'Maximum file size must be at least 1 MB'
    }

    return errors
  }

  /**
   * Validate rewards step
   */
  const validateRewardsStep = (formData: ChallengeFormData): ChallengeFormErrors => {
    const errors: ChallengeFormErrors = {}

    if (formData.rewards.winner.points < 1) {
      errors.rewards = 'Winner points must be at least 1'
    }

    if (formData.rewards.runnerUp.points < 1) {
      errors.rewards = 'Runner-up points must be at least 1'
    }

    if (formData.rewards.participation.points < 1) {
      errors.rewards = 'Participation points must be at least 1'
    }

    if (formData.rewards.winner.points <= formData.rewards.runnerUp.points) {
      errors.rewards = 'Winner points should be higher than runner-up points'
    }

    return errors
  }

  /**
   * Validate media step
   */
  const validateMediaStep = (formData: ChallengeFormData): ChallengeFormErrors => {
    const errors: ChallengeFormErrors = {}

    if (!formData.media.bannerImage.trim()) {
      errors.media = 'Banner image is required'
    }

    if (!formData.media.thumbnailImage.trim()) {
      errors.media = 'Thumbnail image is required'
    }

    if (formData.tags.length === 0) {
      errors.tags = 'At least one tag is required'
    }

    return errors
  }

  /**
   * Validate current step
   */
  const validateCurrentStep = useCallback((): ValidationResult => {
    const { currentStep, formData } = formState
    let errors: ChallengeFormErrors = {}

    switch (currentStep) {
      case 'basic':
        errors = validateBasicStep(formData)
        break
      case 'schedule':
        errors = validateScheduleStep(formData)
        break
      case 'requirements':
        errors = validateRequirementsStep(formData)
        break
      case 'rewards':
        errors = validateRewardsStep(formData)
        break
      case 'media':
        errors = validateMediaStep(formData)
        break
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  }, [formState])

  /**
   * Validate all steps
   */
  const validateAllSteps = useCallback((): ValidationResult => {
    const { formData } = formState
    
    const allErrors = {
      ...validateBasicStep(formData),
      ...validateScheduleStep(formData),
      ...validateRequirementsStep(formData),
      ...validateRewardsStep(formData),
      ...validateMediaStep(formData)
    }

    return {
      isValid: Object.keys(allErrors).length === 0,
      errors: allErrors
    }
  }, [formState])

  return {
    formState,
    updateFormState,
    resetForm,
    validateCurrentStep,
    validateAllSteps
  }
}

/**
 * Step navigation hook
 */
export const useStepNavigation = (
  currentStep: ChallengeFormStep,
  updateFormState: (updates: Partial<ChallengeFormState>) => void,
  validateCurrentStep: () => ValidationResult
) => {
  /**
   * Go to next step
   */
  const nextStep = useCallback(() => {
    const validation = validateCurrentStep()
    
    if (!validation.isValid) {
      updateFormState({ errors: validation.errors })
      return false
    }

    const currentIndex = STEP_ORDER.indexOf(currentStep)
    if (currentIndex < STEP_ORDER.length - 1) {
      const nextStepId = STEP_ORDER[currentIndex + 1]
      updateFormState({ 
        currentStep: nextStepId,
        errors: {} // Clear errors when moving to next step
      })
      return true
    }
    
    return false
  }, [currentStep, updateFormState, validateCurrentStep])

  /**
   * Go to previous step
   */
  const prevStep = useCallback(() => {
    const currentIndex = STEP_ORDER.indexOf(currentStep)
    if (currentIndex > 0) {
      const prevStepId = STEP_ORDER[currentIndex - 1]
      updateFormState({ 
        currentStep: prevStepId,
        errors: {} // Clear errors when moving to previous step
      })
      return true
    }
    
    return false
  }, [currentStep, updateFormState])

  /**
   * Get step progress information
   */
  const getStepProgress = useCallback(() => {
    const currentIndex = STEP_ORDER.indexOf(currentStep)
    return {
      current: currentIndex + 1,
      total: STEP_ORDER.length,
      percentage: ((currentIndex + 1) / STEP_ORDER.length) * 100,
      isFirst: currentIndex === 0,
      isLast: currentIndex === STEP_ORDER.length - 1
    }
  }, [currentStep])

  return {
    nextStep,
    prevStep,
    getStepProgress
  }
}
