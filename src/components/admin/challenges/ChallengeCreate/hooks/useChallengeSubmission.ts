/**
 * Challenge Submission Hook
 * 
 * Handles challenge creation API calls and submission logic
 * 
 * <AUTHOR> Team
 */

'use client'

import { useState, useCallback } from 'react'
import { toast } from 'react-hot-toast'
import { Timestamp } from 'firebase/firestore'
import { createChallenge } from '@/lib/api/gamification'
import {
  ChallengeFormData,
  UseChallengeSubmissionReturn
} from '../types/challengeTypes'

/**
 * Challenge submission hook
 */
export const useChallengeSubmission = (): UseChallengeSubmissionReturn => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submissionError, setSubmissionError] = useState<string | null>(null)

  /**
   * Submit challenge to API
   */
  const submitChallenge = useCallback(async (formData: ChallengeFormData): Promise<boolean> => {
    setIsSubmitting(true)
    setSubmissionError(null)

    try {
      // Transform form data for API
      const challengeData = {
        ...formData,
        // Convert date strings to Firestore Timestamps
        startDate: Timestamp.fromDate(new Date(formData.startDate)),
        endDate: Timestamp.fromDate(new Date(formData.endDate)),
        votingStartDate: Timestamp.fromDate(new Date(formData.votingStartDate)),
        votingEndDate: Timestamp.fromDate(new Date(formData.votingEndDate)),
        // Add metadata
        createdBy: 'admin', // TODO: Get from auth context
        moderators: ['admin'], // TODO: Get from form or defaults
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        // Initialize counters
        submissionCount: 0,
        participantCount: 0,
        viewCount: 0,
        // Initialize arrays
        submissions: [],
        participants: [],
        winners: []
      }

      console.log('[ChallengeSubmission] Submitting challenge:', challengeData)

      // Call API
      const result = await createChallenge(challengeData)
      
      if (result) {
        console.log('[ChallengeSubmission] Challenge created successfully:', result)
        toast.success('Challenge created successfully!')
        return true
      } else {
        throw new Error('Failed to create challenge - no result returned')
      }

    } catch (error: any) {
      console.error('[ChallengeSubmission] Error creating challenge:', error)
      
      const errorMessage = error.message || 'Failed to create challenge'
      setSubmissionError(errorMessage)
      toast.error(`Failed to create challenge: ${errorMessage}`)
      
      return false
    } finally {
      setIsSubmitting(false)
    }
  }, [])

  return {
    submitChallenge,
    isSubmitting,
    submissionError
  }
}

/**
 * Challenge validation hook for submission
 */
export const useChallengeValidation = () => {
  /**
   * Validate challenge data before submission
   */
  const validateForSubmission = useCallback((formData: ChallengeFormData): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    // Basic validation
    if (!formData.title.trim()) {
      errors.push('Title is required')
    }

    if (!formData.description.trim()) {
      errors.push('Description is required')
    }

    if (!formData.theme.trim()) {
      errors.push('Theme is required')
    }

    if (!formData.category.trim()) {
      errors.push('Category is required')
    }

    // Date validation
    if (!formData.startDate) {
      errors.push('Start date is required')
    }

    if (!formData.endDate) {
      errors.push('End date is required')
    }

    if (!formData.votingStartDate) {
      errors.push('Voting start date is required')
    }

    if (!formData.votingEndDate) {
      errors.push('Voting end date is required')
    }

    // Date logic validation
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate)
      const endDate = new Date(formData.endDate)
      
      if (startDate >= endDate) {
        errors.push('End date must be after start date')
      }

      // Check if dates are in the future (for non-draft challenges)
      if (formData.status !== 'draft') {
        const now = new Date()
        if (startDate < now) {
          errors.push('Start date must be in the future for non-draft challenges')
        }
      }
    }

    if (formData.votingStartDate && formData.votingEndDate) {
      const votingStart = new Date(formData.votingStartDate)
      const votingEnd = new Date(formData.votingEndDate)
      
      if (votingStart >= votingEnd) {
        errors.push('Voting end date must be after voting start date')
      }
    }

    if (formData.endDate && formData.votingStartDate) {
      const endDate = new Date(formData.endDate)
      const votingStart = new Date(formData.votingStartDate)
      
      if (votingStart < endDate) {
        errors.push('Voting should start after challenge ends')
      }
    }

    // Rules validation
    if (formData.rules.length === 0) {
      errors.push('At least one rule is required')
    }

    // Requirements validation
    if (formData.requirements.maxSubmissions < 1) {
      errors.push('Maximum submissions must be at least 1')
    }

    if (formData.requirements.allowedFileTypes.length === 0) {
      errors.push('At least one file type must be allowed')
    }

    if (formData.requirements.maxFileSize < 1) {
      errors.push('Maximum file size must be at least 1 MB')
    }

    // Rewards validation
    if (formData.rewards.winner.points < 1) {
      errors.push('Winner points must be at least 1')
    }

    if (formData.rewards.runnerUp.points < 1) {
      errors.push('Runner-up points must be at least 1')
    }

    if (formData.rewards.participation.points < 1) {
      errors.push('Participation points must be at least 1')
    }

    if (formData.rewards.winner.points <= formData.rewards.runnerUp.points) {
      errors.push('Winner points should be higher than runner-up points')
    }

    // Media validation
    if (!formData.media.bannerImage.trim()) {
      errors.push('Banner image is required')
    }

    if (!formData.media.thumbnailImage.trim()) {
      errors.push('Thumbnail image is required')
    }

    // Tags validation
    if (formData.tags.length === 0) {
      errors.push('At least one tag is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }, [])

  return {
    validateForSubmission
  }
}

/**
 * Challenge draft management hook
 */
export const useChallengeDraft = () => {
  const [isDraftSaving, setIsDraftSaving] = useState(false)

  /**
   * Save challenge as draft
   */
  const saveDraft = useCallback(async (formData: ChallengeFormData): Promise<boolean> => {
    setIsDraftSaving(true)

    try {
      // Create draft version with minimal validation
      const draftData = {
        ...formData,
        status: 'draft' as const,
        // Convert dates if they exist
        startDate: formData.startDate ? Timestamp.fromDate(new Date(formData.startDate)) : null,
        endDate: formData.endDate ? Timestamp.fromDate(new Date(formData.endDate)) : null,
        votingStartDate: formData.votingStartDate ? Timestamp.fromDate(new Date(formData.votingStartDate)) : null,
        votingEndDate: formData.votingEndDate ? Timestamp.fromDate(new Date(formData.votingEndDate)) : null,
        // Add metadata
        createdBy: 'admin',
        moderators: ['admin'],
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        isDraft: true
      }

      console.log('[ChallengeDraft] Saving draft:', draftData)

      // Save draft (could be a different API endpoint)
      const result = await createChallenge(draftData)
      
      if (result) {
        toast.success('Draft saved successfully!')
        return true
      } else {
        throw new Error('Failed to save draft')
      }

    } catch (error: any) {
      console.error('[ChallengeDraft] Error saving draft:', error)
      toast.error(`Failed to save draft: ${error.message}`)
      return false
    } finally {
      setIsDraftSaving(false)
    }
  }, [])

  return {
    saveDraft,
    isDraftSaving
  }
}
