/**
 * Challenge Create Module Exports
 * 
 * Centralized exports for the refactored challenge creation system
 * 
 * <AUTHOR> Team
 */

// Main Container Component
export { default as ChallengeCreateContainer } from './ChallengeCreateContainer'

// Step Components
export { default as BasicInfoStep } from './steps/BasicInfoStep'
export { default as ScheduleStep } from './steps/ScheduleStep'
export { default as RequirementsStep } from './steps/RequirementsStep'
export { default as RewardsStep } from './steps/RewardsStep'
export { default as MediaStep } from './steps/MediaStep'

// Hooks
export { useChallengeForm, useStepNavigation } from './hooks/useChallengeForm'
export { 
  useChallengeSubmission, 
  useChallengeValidation,
  useChallengeDraft 
} from './hooks/useChallengeSubmission'

// Types
export type {
  ChallengeFormStep,
  ChallengeType,
  ChallengeDifficulty,
  ChallengeStatus,
  UserTier,
  ChallengeRequirements,
  ChallengeRewards,
  ChallengeMedia,
  ChallengeFormData,
  ChallengeFormErrors,
  ChallengeFormState,
  ValidationResult,
  ChallengeStepProps,
  ChallengeCreateContainerProps,
  UseChallengeFormReturn,
  UseChallengeSubmissionReturn
} from './types/challengeTypes'

// Constants
export {
  INITIAL_CHALLENGE_FORM_STATE,
  CHALLENGE_STEPS,
  CHALLENGE_TYPES,
  DIFFICULTY_LEVELS,
  STATUS_OPTIONS,
  USER_TIERS,
  FILE_TYPES,
  STEP_TITLES,
  STEP_ORDER
} from './types/challengeTypes'
