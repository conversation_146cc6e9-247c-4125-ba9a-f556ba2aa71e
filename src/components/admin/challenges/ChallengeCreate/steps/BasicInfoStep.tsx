/**
 * Basic Information Step Component
 * 
 * First step of challenge creation - Basic information and settings
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { FileText, Info, Star } from 'lucide-react'
import {
  ChallengeStepProps,
  CHALLENGE_TYPES,
  DIFFICULTY_LEVELS,
  STATUS_OPTIONS
} from '../types/challengeTypes'

/**
 * Basic information step component
 */
export const BasicInfoStep: React.FC<ChallengeStepProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { formData, errors } = formState

  /**
   * Handle input change
   */
  const handleInputChange = (field: string, value: any) => {
    const updatedFormData = { ...formData }
    
    // Handle nested field updates
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      updatedFormData[parent as keyof typeof updatedFormData] = {
        ...(updatedFormData[parent as keyof typeof updatedFormData] as any),
        [child]: value
      }
    } else {
      (updatedFormData as any)[field] = value
    }

    onUpdateFormState({
      formData: updatedFormData,
      isDirty: true
    })
  }

  /**
   * Input field component
   */
  const InputField: React.FC<{
    label: string
    field: string
    type?: string
    placeholder?: string
    required?: boolean
    maxLength?: number
    rows?: number
  }> = ({ label, field, type = 'text', placeholder, required = false, maxLength, rows }) => {
    const value = field.includes('.') 
      ? field.split('.').reduce((obj, key) => obj?.[key], formData as any) || ''
      : (formData as any)[field] || ''

    const error = errors[field as keyof typeof errors]

    const Component = rows ? 'textarea' : 'input'

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-300">
          {label} {required && <span className="text-red-400">*</span>}
        </label>
        <Component
          type={rows ? undefined : type}
          rows={rows}
          value={value}
          onChange={(e) => handleInputChange(field, e.target.value)}
          placeholder={placeholder}
          maxLength={maxLength}
          className={`w-full px-4 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 transition-colors resize-none ${
            error
              ? 'border-red-500 focus:ring-red-500'
              : 'border-gray-700 focus:ring-purple-500'
          }`}
        />
        {maxLength && (
          <div className="text-xs text-gray-500 text-right">
            {value.length}/{maxLength}
          </div>
        )}
        {error && (
          <p className="text-red-400 text-sm">{error}</p>
        )}
      </div>
    )
  }

  /**
   * Select field component
   */
  const SelectField: React.FC<{
    label: string
    field: string
    options: readonly any[]
    required?: boolean
    valueKey?: string
    labelKey?: string
  }> = ({ label, field, options, required = false, valueKey = 'value', labelKey = 'label' }) => {
    const value = (formData as any)[field] || ''
    const error = errors[field as keyof typeof errors]

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-300">
          {label} {required && <span className="text-red-400">*</span>}
        </label>
        <select
          value={value}
          onChange={(e) => handleInputChange(field, e.target.value)}
          className={`w-full px-4 py-2 bg-gray-800 border rounded-lg text-white focus:outline-none focus:ring-2 transition-colors ${
            error
              ? 'border-red-500 focus:ring-red-500'
              : 'border-gray-700 focus:ring-purple-500'
          }`}
        >
          <option value="">Select {label.toLowerCase()}</option>
          {options.map((option) => (
            <option key={option[valueKey]} value={option[valueKey]}>
              {option[labelKey]}
            </option>
          ))}
        </select>
        {error && (
          <p className="text-red-400 text-sm">{error}</p>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-600 rounded-lg">
          <FileText size={20} className="text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Basic Information</h3>
          <p className="text-gray-400 text-sm">Set up the fundamental details of your challenge</p>
        </div>
      </div>

      {/* Challenge Title */}
      <InputField
        label="Challenge Title"
        field="title"
        placeholder="Enter a compelling challenge title"
        required
        maxLength={100}
      />

      {/* Short Description */}
      <InputField
        label="Short Description"
        field="shortDescription"
        placeholder="Brief description for preview cards"
        required
        maxLength={200}
        rows={2}
      />

      {/* Full Description */}
      <InputField
        label="Full Description"
        field="description"
        placeholder="Detailed description of the challenge, goals, and expectations"
        required
        rows={4}
      />

      {/* Theme and Category Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          label="Theme"
          field="theme"
          placeholder="e.g., Cyberpunk, Nature, Minimalism"
          required
        />
        
        <InputField
          label="Category"
          field="category"
          placeholder="e.g., Keycaps, Keyboards, Accessories"
          required
        />
      </div>

      {/* Challenge Type */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-300">
          Challenge Type <span className="text-red-400">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {CHALLENGE_TYPES.map(type => (
            <motion.label
              key={type.value}
              className={`flex items-center p-3 rounded-lg border cursor-pointer transition-all ${
                formData.type === type.value
                  ? 'bg-purple-600/20 border-purple-500'
                  : 'bg-gray-800 border-gray-700 hover:border-gray-600'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <input
                type="radio"
                name="type"
                value={type.value}
                checked={formData.type === type.value}
                onChange={(e) => handleInputChange('type', e.target.value)}
                className="sr-only"
              />
              <span className="text-2xl mr-3">{type.icon}</span>
              <div>
                <div className="text-white font-medium">{type.label}</div>
                <div className="text-gray-400 text-sm">{type.description}</div>
              </div>
            </motion.label>
          ))}
        </div>
        {errors.type && (
          <p className="text-red-400 text-sm">{errors.type}</p>
        )}
      </div>

      {/* Difficulty and Status Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectField
          label="Difficulty Level"
          field="difficulty"
          options={DIFFICULTY_LEVELS}
          required
        />
        
        <SelectField
          label="Status"
          field="status"
          options={STATUS_OPTIONS}
          required
        />
      </div>

      {/* Featured Toggle */}
      <div className="flex items-center gap-3 p-4 bg-gray-800 rounded-lg border border-gray-700">
        <label className="flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={formData.featured}
            onChange={(e) => handleInputChange('featured', e.target.checked)}
            className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
          />
          <Star className="w-5 h-5 text-yellow-400 ml-3 mr-2" />
          <span className="text-white font-medium">Feature this challenge</span>
        </label>
        <div className="flex items-center gap-1 text-gray-400">
          <Info size={14} />
          <span className="text-xs">Featured challenges appear prominently on the community page</span>
        </div>
      </div>

      {/* Help Text */}
      <div className="p-4 bg-blue-500/20 border border-blue-500 rounded-lg">
        <h4 className="text-blue-400 font-medium mb-2">💡 Tips for a Great Challenge</h4>
        <ul className="text-blue-300 text-sm space-y-1">
          <li>• Choose a clear, engaging title that describes the challenge goal</li>
          <li>• Write a detailed description that explains expectations and judging criteria</li>
          <li>• Select an appropriate difficulty level for your target audience</li>
          <li>• Use relevant themes and categories to help users discover your challenge</li>
        </ul>
      </div>
    </div>
  )
}

export default BasicInfoStep
