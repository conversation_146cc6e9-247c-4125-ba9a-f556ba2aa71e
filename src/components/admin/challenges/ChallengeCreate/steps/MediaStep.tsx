/**
 * Media Step Component
 * 
 * Fifth step of challenge creation - Media, tags, and categorization
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Palette, Upload, Tag, Plus, X, Image, Video, Eye } from 'lucide-react'
import { ChallengeStepProps } from '../types/challengeTypes'

/**
 * Media step component
 */
export const MediaStep: React.FC<ChallengeStepProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { formData, errors } = formState
  const [newTag, setNewTag] = useState('')

  /**
   * Handle input change
   */
  const handleInputChange = (field: string, value: any) => {
    const updatedFormData = { ...formData }
    
    if (field.startsWith('media.')) {
      const mediaField = field.split('.')[1]
      updatedFormData.media = {
        ...updatedFormData.media,
        [mediaField]: value
      }
    } else {
      (updatedFormData as any)[field] = value
    }

    onUpdateFormState({
      formData: updatedFormData,
      isDirty: true
    })
  }

  /**
   * Add new tag
   */
  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim().toLowerCase())) {
      const updatedTags = [...formData.tags, newTag.trim().toLowerCase()]
      handleInputChange('tags', updatedTags)
      setNewTag('')
    }
  }

  /**
   * Remove tag
   */
  const removeTag = (tagToRemove: string) => {
    const updatedTags = formData.tags.filter(tag => tag !== tagToRemove)
    handleInputChange('tags', updatedTags)
  }

  /**
   * Add inspiration image
   */
  const addInspirationImage = () => {
    const currentImages = formData.media.inspirationImages || []
    const updatedImages = [...currentImages, '']
    handleInputChange('media.inspirationImages', updatedImages)
  }

  /**
   * Update inspiration image
   */
  const updateInspirationImage = (index: number, url: string) => {
    const currentImages = formData.media.inspirationImages || []
    const updatedImages = [...currentImages]
    updatedImages[index] = url
    handleInputChange('media.inspirationImages', updatedImages)
  }

  /**
   * Remove inspiration image
   */
  const removeInspirationImage = (index: number) => {
    const currentImages = formData.media.inspirationImages || []
    const updatedImages = currentImages.filter((_, i) => i !== index)
    handleInputChange('media.inspirationImages', updatedImages)
  }

  /**
   * Image upload component
   */
  const ImageUpload: React.FC<{
    label: string
    field: string
    required?: boolean
    description?: string
  }> = ({ label, field, required = false, description }) => {
    const value = field.includes('.') 
      ? field.split('.').reduce((obj, key) => obj?.[key], formData as any) || ''
      : (formData as any)[field] || ''

    const error = errors[field.split('.')[0] as keyof typeof errors]

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-300">
          {label} {required && <span className="text-red-400">*</span>}
        </label>
        {description && (
          <p className="text-xs text-gray-500">{description}</p>
        )}
        
        <div className="space-y-3">
          {/* URL Input */}
          <input
            type="url"
            value={value}
            onChange={(e) => handleInputChange(field, e.target.value)}
            placeholder="Enter image URL or upload file"
            className={`w-full px-4 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 transition-colors ${
              error
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-700 focus:ring-purple-500'
            }`}
          />
          
          {/* Upload Button */}
          <button
            type="button"
            className="flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            <Upload size={16} />
            Upload Image
          </button>
          
          {/* Preview */}
          {value && (
            <div className="relative w-full h-32 bg-gray-700 rounded-lg overflow-hidden">
              <img
                src={value}
                alt={label}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none'
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                <Eye size={24} className="text-white" />
              </div>
            </div>
          )}
        </div>
        
        {error && (
          <p className="text-red-400 text-sm">{error}</p>
        )}
      </div>
    )
  }

  /**
   * Suggested tags
   */
  const suggestedTags = [
    'design', 'keycaps', 'artisan', 'photography', 'build', 'custom',
    'mechanical', 'rgb', 'minimalist', 'colorful', 'vintage', 'modern',
    'gaming', 'productivity', 'creative', 'beginner-friendly'
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-600 rounded-lg">
          <Palette size={20} className="text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Media & Tags</h3>
          <p className="text-gray-400 text-sm">Add visual content and categorize your challenge</p>
        </div>
      </div>

      {/* Required Images */}
      <div className="space-y-6">
        <h4 className="text-white font-medium flex items-center gap-2">
          <Image size={16} />
          Required Images
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ImageUpload
            label="Banner Image"
            field="media.bannerImage"
            required
            description="Main challenge banner (recommended: 1200x400px)"
          />
          
          <ImageUpload
            label="Thumbnail Image"
            field="media.thumbnailImage"
            required
            description="Challenge card thumbnail (recommended: 400x300px)"
          />
        </div>
      </div>

      {/* Optional Media */}
      <div className="space-y-4">
        <h4 className="text-white font-medium flex items-center gap-2">
          <Video size={16} />
          Optional Media
        </h4>

        {/* Video URL */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300">
            Video URL
          </label>
          <input
            type="url"
            value={formData.media.videoUrl || ''}
            onChange={(e) => handleInputChange('media.videoUrl', e.target.value)}
            placeholder="YouTube, Vimeo, or direct video URL"
            className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        {/* Inspiration Images */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-300">
              Inspiration Images
            </label>
            <button
              type="button"
              onClick={addInspirationImage}
              className="flex items-center gap-2 px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm"
            >
              <Plus size={14} />
              Add Image
            </button>
          </div>
          
          {formData.media.inspirationImages?.map((imageUrl, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex gap-2"
            >
              <input
                type="url"
                value={imageUrl}
                onChange={(e) => updateInspirationImage(index, e.target.value)}
                placeholder="Enter inspiration image URL"
                className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <button
                type="button"
                onClick={() => removeInspirationImage(index)}
                className="p-2 text-gray-400 hover:text-red-400 transition-colors"
              >
                <X size={16} />
              </button>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Tags */}
      <div className="space-y-4">
        <h4 className="text-white font-medium flex items-center gap-2">
          <Tag size={16} />
          Tags <span className="text-red-400">*</span>
        </h4>

        {/* Current Tags */}
        <div className="flex flex-wrap gap-2">
          {formData.tags.map(tag => (
            <motion.span
              key={tag}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center gap-2 px-3 py-1 bg-purple-600/20 border border-purple-500 rounded-full text-purple-400 text-sm"
            >
              {tag}
              <button
                type="button"
                onClick={() => removeTag(tag)}
                className="text-purple-400 hover:text-red-400 transition-colors"
              >
                <X size={12} />
              </button>
            </motion.span>
          ))}
        </div>

        {/* Add New Tag */}
        <div className="flex gap-2">
          <input
            type="text"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder="Enter a tag..."
            className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
            onKeyPress={(e) => e.key === 'Enter' && addTag()}
          />
          <button
            type="button"
            onClick={addTag}
            disabled={!newTag.trim()}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            Add
          </button>
        </div>

        {/* Suggested Tags */}
        <div className="space-y-2">
          <p className="text-sm text-gray-400">Suggested tags:</p>
          <div className="flex flex-wrap gap-2">
            {suggestedTags
              .filter(tag => !formData.tags.includes(tag))
              .slice(0, 8)
              .map(tag => (
                <button
                  key={tag}
                  type="button"
                  onClick={() => {
                    setNewTag(tag)
                    const updatedTags = [...formData.tags, tag]
                    handleInputChange('tags', updatedTags)
                  }}
                  className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-full text-sm transition-colors"
                >
                  {tag}
                </button>
              ))}
          </div>
        </div>

        {errors.tags && (
          <p className="text-red-400 text-sm">{errors.tags}</p>
        )}
      </div>

      {/* Media Summary */}
      <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
        <h4 className="text-white font-medium mb-3">Media Summary</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Banner image:</span>
            <span className="text-white">{formData.media.bannerImage ? '✓ Set' : '✗ Missing'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Thumbnail image:</span>
            <span className="text-white">{formData.media.thumbnailImage ? '✓ Set' : '✗ Missing'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Video:</span>
            <span className="text-white">{formData.media.videoUrl ? '✓ Added' : 'None'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Inspiration images:</span>
            <span className="text-white">{formData.media.inspirationImages?.length || 0}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Tags:</span>
            <span className="text-white">{formData.tags.length}</span>
          </div>
        </div>
      </div>

      {/* Guidelines */}
      <div className="p-4 bg-blue-500/20 border border-blue-500 rounded-lg">
        <h4 className="text-blue-400 font-medium mb-2">📸 Media Guidelines</h4>
        <ul className="text-blue-300 text-sm space-y-1">
          <li>• Use high-quality images that represent your challenge theme</li>
          <li>• Banner images should be landscape orientation (3:1 ratio)</li>
          <li>• Thumbnail images should be clear and eye-catching</li>
          <li>• Add relevant tags to help users discover your challenge</li>
          <li>• Inspiration images help participants understand the vision</li>
        </ul>
      </div>
    </div>
  )
}

export default MediaStep
