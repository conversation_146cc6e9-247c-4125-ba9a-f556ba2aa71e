/**
 * Requirements Step Component
 * 
 * Third step of challenge creation - Rules and requirements configuration
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Settings, Plus, X, Users, Shield, FileType, HardDrive } from 'lucide-react'
import {
  ChallengeStepProps,
  USER_TIERS,
  FILE_TYPES
} from '../types/challengeTypes'

/**
 * Requirements step component
 */
export const RequirementsStep: React.FC<ChallengeStepProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { formData, errors } = formState
  const [newRule, setNewRule] = useState('')

  /**
   * Handle input change
   */
  const handleInputChange = (field: string, value: any) => {
    const updatedFormData = { ...formData }
    
    if (field.startsWith('requirements.')) {
      const requirementField = field.split('.')[1]
      updatedFormData.requirements = {
        ...updatedFormData.requirements,
        [requirementField]: value
      }
    } else {
      (updatedFormData as any)[field] = value
    }

    onUpdateFormState({
      formData: updatedFormData,
      isDirty: true
    })
  }

  /**
   * Add new rule
   */
  const addRule = () => {
    if (newRule.trim()) {
      const updatedRules = [...formData.rules, newRule.trim()]
      handleInputChange('rules', updatedRules)
      setNewRule('')
    }
  }

  /**
   * Remove rule
   */
  const removeRule = (index: number) => {
    const updatedRules = formData.rules.filter((_, i) => i !== index)
    handleInputChange('rules', updatedRules)
  }

  /**
   * Toggle file type
   */
  const toggleFileType = (fileType: string) => {
    const currentTypes = formData.requirements.allowedFileTypes
    const updatedTypes = currentTypes.includes(fileType)
      ? currentTypes.filter(type => type !== fileType)
      : [...currentTypes, fileType]
    
    handleInputChange('requirements.allowedFileTypes', updatedTypes)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-600 rounded-lg">
          <Settings size={20} className="text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Rules & Requirements</h3>
          <p className="text-gray-400 text-sm">Define the rules and technical requirements for participation</p>
        </div>
      </div>

      {/* Challenge Rules */}
      <div className="space-y-4">
        <h4 className="text-white font-medium flex items-center gap-2">
          <Shield size={16} />
          Challenge Rules
        </h4>
        
        {/* Existing Rules */}
        <div className="space-y-2">
          {formData.rules.map((rule, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg border border-gray-700"
            >
              <span className="text-gray-400 text-sm font-mono">#{index + 1}</span>
              <span className="flex-1 text-white text-sm">{rule}</span>
              <button
                onClick={() => removeRule(index)}
                className="p-1 text-gray-400 hover:text-red-400 transition-colors"
              >
                <X size={16} />
              </button>
            </motion.div>
          ))}
        </div>

        {/* Add New Rule */}
        <div className="flex gap-2">
          <input
            type="text"
            value={newRule}
            onChange={(e) => setNewRule(e.target.value)}
            placeholder="Enter a new rule..."
            className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
            onKeyPress={(e) => e.key === 'Enter' && addRule()}
          />
          <button
            onClick={addRule}
            disabled={!newRule.trim()}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
          >
            <Plus size={16} />
            Add Rule
          </button>
        </div>

        {errors.rules && (
          <p className="text-red-400 text-sm">{errors.rules}</p>
        )}
      </div>

      {/* Participation Requirements */}
      <div className="space-y-4">
        <h4 className="text-white font-medium flex items-center gap-2">
          <Users size={16} />
          Participation Requirements
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Minimum Tier */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              Minimum User Tier
            </label>
            <select
              value={formData.requirements.minTier || ''}
              onChange={(e) => handleInputChange('requirements.minTier', e.target.value || undefined)}
              className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">No minimum tier</option>
              {USER_TIERS.map(tier => (
                <option key={tier.value} value={tier.value}>
                  {tier.label}
                </option>
              ))}
            </select>
          </div>

          {/* Max Submissions */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              Max Submissions per User <span className="text-red-400">*</span>
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={formData.requirements.maxSubmissions}
              onChange={(e) => handleInputChange('requirements.maxSubmissions', parseInt(e.target.value) || 1)}
              className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
        </div>

        {/* Team and Approval Settings */}
        <div className="space-y-3">
          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.requirements.allowTeams}
              onChange={(e) => handleInputChange('requirements.allowTeams', e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
            />
            <span className="text-white">Allow team submissions</span>
          </label>

          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.requirements.requiresApproval}
              onChange={(e) => handleInputChange('requirements.requiresApproval', e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
            />
            <span className="text-white">Require moderator approval for submissions</span>
          </label>
        </div>
      </div>

      {/* File Requirements */}
      <div className="space-y-4">
        <h4 className="text-white font-medium flex items-center gap-2">
          <FileType size={16} />
          File Requirements
        </h4>

        {/* Allowed File Types */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-300">
            Allowed File Types <span className="text-red-400">*</span>
          </label>
          <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
            {FILE_TYPES.map(fileType => (
              <motion.label
                key={fileType.value}
                className={`flex items-center justify-center p-2 rounded-lg border cursor-pointer transition-all ${
                  formData.requirements.allowedFileTypes.includes(fileType.value)
                    ? 'bg-purple-600/20 border-purple-500 text-purple-400'
                    : 'bg-gray-800 border-gray-700 text-gray-400 hover:border-gray-600'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <input
                  type="checkbox"
                  checked={formData.requirements.allowedFileTypes.includes(fileType.value)}
                  onChange={() => toggleFileType(fileType.value)}
                  className="sr-only"
                />
                <span className="text-xs font-medium">{fileType.label}</span>
              </motion.label>
            ))}
          </div>
          {formData.requirements.allowedFileTypes.length > 0 && (
            <p className="text-xs text-gray-500">
              Selected: {formData.requirements.allowedFileTypes.join(', ')}
            </p>
          )}
        </div>

        {/* Max File Size */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300 flex items-center gap-2">
            <HardDrive size={16} />
            Maximum File Size (MB) <span className="text-red-400">*</span>
          </label>
          <div className="flex items-center gap-4">
            <input
              type="range"
              min="1"
              max="100"
              value={formData.requirements.maxFileSize}
              onChange={(e) => handleInputChange('requirements.maxFileSize', parseInt(e.target.value))}
              className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <span className="text-white font-medium w-16 text-center">
              {formData.requirements.maxFileSize} MB
            </span>
          </div>
          <div className="flex justify-between text-xs text-gray-500">
            <span>1 MB</span>
            <span>100 MB</span>
          </div>
        </div>

        {errors.requirements && (
          <p className="text-red-400 text-sm">{errors.requirements}</p>
        )}
      </div>

      {/* Requirements Summary */}
      <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
        <h4 className="text-white font-medium mb-3">Requirements Summary</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Rules defined:</span>
            <span className="text-white">{formData.rules.length}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Min tier required:</span>
            <span className="text-white">{formData.requirements.minTier || 'None'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Max submissions:</span>
            <span className="text-white">{formData.requirements.maxSubmissions}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Team submissions:</span>
            <span className="text-white">{formData.requirements.allowTeams ? 'Allowed' : 'Not allowed'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Approval required:</span>
            <span className="text-white">{formData.requirements.requiresApproval ? 'Yes' : 'No'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">File types:</span>
            <span className="text-white">{formData.requirements.allowedFileTypes.length} types</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Max file size:</span>
            <span className="text-white">{formData.requirements.maxFileSize} MB</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RequirementsStep
