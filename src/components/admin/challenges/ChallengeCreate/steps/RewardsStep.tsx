/**
 * Rewards Step Component
 * 
 * Fourth step of challenge creation - Rewards and recognition configuration
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Trophy, Medal, Star, Gift, Zap } from 'lucide-react'
import { ChallengeStepProps } from '../types/challengeTypes'

/**
 * Rewards step component
 */
export const RewardsStep: React.FC<ChallengeStepProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { formData, errors } = formState

  /**
   * Handle input change
   */
  const handleInputChange = (field: string, value: any) => {
    const updatedFormData = { ...formData }
    
    // Handle nested rewards field updates
    if (field.startsWith('rewards.')) {
      const [category, property] = field.split('.').slice(1) // Remove 'rewards' prefix
      updatedFormData.rewards = {
        ...updatedFormData.rewards,
        [category]: {
          ...updatedFormData.rewards[category as keyof typeof updatedFormData.rewards],
          [property]: value
        }
      }
    } else {
      (updatedFormData as any)[field] = value
    }

    onUpdateFormState({
      formData: updatedFormData,
      isDirty: true
    })
  }

  /**
   * Reward category component
   */
  const RewardCategory: React.FC<{
    title: string
    icon: React.ReactNode
    category: 'winner' | 'runnerUp' | 'participation' | 'featured'
    color: string
    description: string
  }> = ({ title, icon, category, color, description }) => {
    const rewards = formData.rewards[category]
    const hasError = errors.rewards && category === 'winner' // Simplified error handling

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`p-4 rounded-lg border ${hasError ? 'border-red-500' : 'border-gray-700'} bg-gray-800`}
      >
        <div className="flex items-center gap-3 mb-4">
          <div className={`p-2 rounded-lg ${color}`}>
            {icon}
          </div>
          <div>
            <h4 className="text-white font-medium">{title}</h4>
            <p className="text-gray-400 text-sm">{description}</p>
          </div>
        </div>

        <div className="space-y-3">
          {/* Points */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              Points <span className="text-red-400">*</span>
            </label>
            <input
              type="number"
              min="1"
              max="10000"
              value={rewards.points}
              onChange={(e) => handleInputChange(`rewards.${category}.points`, parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          {/* Badge (for winner and runner-up) */}
          {(category === 'winner' || category === 'runnerUp') && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                Badge Name
              </label>
              <input
                type="text"
                value={rewards.badge || ''}
                onChange={(e) => handleInputChange(`rewards.${category}.badge`, e.target.value)}
                placeholder="e.g., Design Master, Creative Genius"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          )}

          {/* Special Reward (for winner only) */}
          {category === 'winner' && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                Special Reward
              </label>
              <input
                type="text"
                value={rewards.specialReward || ''}
                onChange={(e) => handleInputChange(`rewards.${category}.specialReward`, e.target.value)}
                placeholder="e.g., Custom keycap set, Featured showcase"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          )}
        </div>
      </motion.div>
    )
  }

  /**
   * Calculate total points budget
   */
  const calculateTotalBudget = () => {
    const { winner, runnerUp, participation, featured } = formData.rewards
    // Estimate based on typical participation
    const estimatedParticipants = 20
    const estimatedFeatured = 3
    
    return (
      winner.points + 
      runnerUp.points + 
      (participation.points * estimatedParticipants) + 
      (featured.points * estimatedFeatured)
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-600 rounded-lg">
          <Trophy size={20} className="text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Rewards & Recognition</h3>
          <p className="text-gray-400 text-sm">Configure points, badges, and special rewards for participants</p>
        </div>
      </div>

      {/* Reward Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <RewardCategory
          title="Winner"
          icon={<Trophy size={20} className="text-white" />}
          category="winner"
          color="bg-yellow-600"
          description="First place winner rewards"
        />

        <RewardCategory
          title="Runner-up"
          icon={<Medal size={20} className="text-white" />}
          category="runnerUp"
          color="bg-gray-600"
          description="Second place rewards"
        />

        <RewardCategory
          title="Participation"
          icon={<Star size={20} className="text-white" />}
          category="participation"
          color="bg-blue-600"
          description="All participants receive"
        />

        <RewardCategory
          title="Featured"
          icon={<Zap size={20} className="text-white" />}
          category="featured"
          color="bg-purple-600"
          description="Featured submissions bonus"
        />
      </div>

      {/* Points Budget Overview */}
      <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
        <h4 className="text-white font-medium mb-4 flex items-center gap-2">
          <Gift size={16} />
          Points Budget Overview
        </h4>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">{formData.rewards.winner.points}</div>
            <div className="text-xs text-gray-400">Winner</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-400">{formData.rewards.runnerUp.points}</div>
            <div className="text-xs text-gray-400">Runner-up</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{formData.rewards.participation.points}</div>
            <div className="text-xs text-gray-400">Participation</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">{formData.rewards.featured.points}</div>
            <div className="text-xs text-gray-400">Featured</div>
          </div>
        </div>

        <div className="pt-4 border-t border-gray-700">
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Estimated Total Budget:</span>
            <span className="text-white font-bold text-lg">{calculateTotalBudget().toLocaleString()} points</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Based on ~20 participants, 3 featured submissions
          </p>
        </div>
      </div>

      {/* Validation Errors */}
      {errors.rewards && (
        <div className="p-3 bg-red-500/20 border border-red-500 rounded-lg">
          <p className="text-red-400 text-sm">{errors.rewards}</p>
        </div>
      )}

      {/* Reward Guidelines */}
      <div className="p-4 bg-blue-500/20 border border-blue-500 rounded-lg">
        <h4 className="text-blue-400 font-medium mb-2">💡 Reward Guidelines</h4>
        <ul className="text-blue-300 text-sm space-y-1">
          <li>• Winner points should be significantly higher than runner-up</li>
          <li>• Participation points encourage engagement (recommended: 25-100)</li>
          <li>• Featured submission bonus motivates quality (recommended: 50-200)</li>
          <li>• Consider your community's point economy when setting values</li>
          <li>• Special rewards create excitement and prestige</li>
        </ul>
      </div>

      {/* Badge Preview */}
      {(formData.rewards.winner.badge || formData.rewards.runnerUp.badge) && (
        <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
          <h4 className="text-white font-medium mb-3">Badge Preview</h4>
          <div className="flex gap-4">
            {formData.rewards.winner.badge && (
              <div className="flex items-center gap-2 px-3 py-2 bg-yellow-600/20 border border-yellow-600 rounded-lg">
                <Trophy size={16} className="text-yellow-400" />
                <span className="text-yellow-400 text-sm font-medium">{formData.rewards.winner.badge}</span>
              </div>
            )}
            {formData.rewards.runnerUp.badge && (
              <div className="flex items-center gap-2 px-3 py-2 bg-gray-600/20 border border-gray-600 rounded-lg">
                <Medal size={16} className="text-gray-400" />
                <span className="text-gray-400 text-sm font-medium">{formData.rewards.runnerUp.badge}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Points Comparison */}
      <div className="p-4 bg-green-500/20 border border-green-500 rounded-lg">
        <h4 className="text-green-400 font-medium mb-2">✅ Points Validation</h4>
        <div className="space-y-1 text-green-300 text-sm">
          {formData.rewards.winner.points > formData.rewards.runnerUp.points ? (
            <p>✓ Winner points are higher than runner-up points</p>
          ) : (
            <p className="text-red-400">⚠ Winner points should be higher than runner-up points</p>
          )}
          {formData.rewards.participation.points >= 25 ? (
            <p>✓ Participation points are in recommended range</p>
          ) : (
            <p className="text-yellow-400">⚠ Consider increasing participation points (recommended: 25+)</p>
          )}
          {formData.rewards.featured.points >= 50 ? (
            <p>✓ Featured bonus is meaningful</p>
          ) : (
            <p className="text-yellow-400">⚠ Consider increasing featured bonus (recommended: 50+)</p>
          )}
        </div>
      </div>
    </div>
  )
}

export default RewardsStep
