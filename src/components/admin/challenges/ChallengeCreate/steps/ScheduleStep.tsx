/**
 * Schedule Step Component
 * 
 * Second step of challenge creation - Schedule and timing configuration
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Calendar, Clock, AlertCircle, Info } from 'lucide-react'
import { ChallengeStepProps } from '../types/challengeTypes'

/**
 * Schedule step component
 */
export const ScheduleStep: React.FC<ChallengeStepProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { formData, errors } = formState

  /**
   * Handle input change
   */
  const handleInputChange = (field: string, value: string) => {
    const updatedFormData = { ...formData, [field]: value }
    
    onUpdateFormState({
      formData: updatedFormData,
      isDirty: true
    })
  }

  /**
   * Get minimum date (today)
   */
  const getMinDate = () => {
    return new Date().toISOString().split('T')[0]
  }

  /**
   * Get minimum datetime (now)
   */
  const getMinDateTime = () => {
    return new Date().toISOString().slice(0, 16)
  }

  /**
   * Format date for display
   */
  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  /**
   * Calculate duration between dates
   */
  const calculateDuration = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return ''
    
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return '1 day'
    if (diffDays < 7) return `${diffDays} days`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} week${Math.floor(diffDays / 7) !== 1 ? 's' : ''}`
    return `${Math.floor(diffDays / 30)} month${Math.floor(diffDays / 30) !== 1 ? 's' : ''}`
  }

  /**
   * Date input field component
   */
  const DateTimeField: React.FC<{
    label: string
    field: string
    required?: boolean
    minDate?: string
    description?: string
  }> = ({ label, field, required = false, minDate, description }) => {
    const value = (formData as any)[field] || ''
    const error = errors[field as keyof typeof errors]

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-300">
          {label} {required && <span className="text-red-400">*</span>}
        </label>
        {description && (
          <p className="text-xs text-gray-500">{description}</p>
        )}
        <input
          type="datetime-local"
          value={value}
          onChange={(e) => handleInputChange(field, e.target.value)}
          min={minDate}
          className={`w-full px-4 py-2 bg-gray-800 border rounded-lg text-white focus:outline-none focus:ring-2 transition-colors ${
            error
              ? 'border-red-500 focus:ring-red-500'
              : 'border-gray-700 focus:ring-purple-500'
          }`}
        />
        {value && (
          <p className="text-xs text-gray-400">
            {formatDateForDisplay(value)}
          </p>
        )}
        {error && (
          <p className="text-red-400 text-sm">{error}</p>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-600 rounded-lg">
          <Calendar size={20} className="text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Schedule & Timing</h3>
          <p className="text-gray-400 text-sm">Configure when your challenge runs and when voting occurs</p>
        </div>
      </div>

      {/* Challenge Period */}
      <div className="space-y-4">
        <h4 className="text-white font-medium flex items-center gap-2">
          <Clock size={16} />
          Challenge Period
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DateTimeField
            label="Start Date & Time"
            field="startDate"
            required
            minDate={getMinDateTime()}
            description="When participants can start submitting"
          />
          
          <DateTimeField
            label="End Date & Time"
            field="endDate"
            required
            minDate={formData.startDate || getMinDateTime()}
            description="Submission deadline"
          />
        </div>

        {/* Duration Display */}
        {formData.startDate && formData.endDate && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-3 bg-green-500/20 border border-green-500 rounded-lg"
          >
            <p className="text-green-400 text-sm">
              <strong>Challenge Duration:</strong> {calculateDuration(formData.startDate, formData.endDate)}
            </p>
          </motion.div>
        )}
      </div>

      {/* Voting Period */}
      <div className="space-y-4">
        <h4 className="text-white font-medium flex items-center gap-2">
          <Clock size={16} />
          Voting Period
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DateTimeField
            label="Voting Start Date & Time"
            field="votingStartDate"
            required
            minDate={formData.endDate || getMinDateTime()}
            description="When community voting begins"
          />
          
          <DateTimeField
            label="Voting End Date & Time"
            field="votingEndDate"
            required
            minDate={formData.votingStartDate || getMinDateTime()}
            description="When voting closes and winners are determined"
          />
        </div>

        {/* Voting Duration Display */}
        {formData.votingStartDate && formData.votingEndDate && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-3 bg-blue-500/20 border border-blue-500 rounded-lg"
          >
            <p className="text-blue-400 text-sm">
              <strong>Voting Duration:</strong> {calculateDuration(formData.votingStartDate, formData.votingEndDate)}
            </p>
          </motion.div>
        )}
      </div>

      {/* Timeline Visualization */}
      {formData.startDate && formData.endDate && formData.votingStartDate && formData.votingEndDate && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-gray-800 rounded-lg border border-gray-700"
        >
          <h4 className="text-white font-medium mb-4">Challenge Timeline</h4>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <p className="text-white text-sm font-medium">Challenge Starts</p>
                <p className="text-gray-400 text-xs">{formatDateForDisplay(formData.startDate)}</p>
              </div>
            </div>
            
            <div className="ml-1.5 w-0.5 h-6 bg-gray-600"></div>
            
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div>
                <p className="text-white text-sm font-medium">Submissions Close</p>
                <p className="text-gray-400 text-xs">{formatDateForDisplay(formData.endDate)}</p>
              </div>
            </div>
            
            <div className="ml-1.5 w-0.5 h-6 bg-gray-600"></div>
            
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <div>
                <p className="text-white text-sm font-medium">Voting Starts</p>
                <p className="text-gray-400 text-xs">{formatDateForDisplay(formData.votingStartDate)}</p>
              </div>
            </div>
            
            <div className="ml-1.5 w-0.5 h-6 bg-gray-600"></div>
            
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <div>
                <p className="text-white text-sm font-medium">Winners Announced</p>
                <p className="text-gray-400 text-xs">{formatDateForDisplay(formData.votingEndDate)}</p>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Important Notes */}
      <div className="p-4 bg-yellow-500/20 border border-yellow-500 rounded-lg">
        <div className="flex items-start gap-3">
          <AlertCircle size={20} className="text-yellow-400 mt-0.5" />
          <div>
            <h4 className="text-yellow-400 font-medium mb-2">Important Scheduling Notes</h4>
            <ul className="text-yellow-300 text-sm space-y-1">
              <li>• Voting should start after the submission period ends</li>
              <li>• Allow sufficient time for participants to create quality submissions</li>
              <li>• Consider time zones when setting deadlines</li>
              <li>• Longer voting periods typically result in more community engagement</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Recommended Durations */}
      <div className="p-4 bg-blue-500/20 border border-blue-500 rounded-lg">
        <div className="flex items-start gap-3">
          <Info size={20} className="text-blue-400 mt-0.5" />
          <div>
            <h4 className="text-blue-400 font-medium mb-2">Recommended Durations</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-300 text-sm">
              <div>
                <p className="font-medium">Challenge Period:</p>
                <ul className="space-y-1 mt-1">
                  <li>• Design challenges: 1-2 weeks</li>
                  <li>• Build projects: 2-4 weeks</li>
                  <li>• Photo challenges: 3-7 days</li>
                </ul>
              </div>
              <div>
                <p className="font-medium">Voting Period:</p>
                <ul className="space-y-1 mt-1">
                  <li>• Community voting: 3-7 days</li>
                  <li>• Expert judging: 1-2 weeks</li>
                  <li>• Mixed voting: 1 week</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ScheduleStep
