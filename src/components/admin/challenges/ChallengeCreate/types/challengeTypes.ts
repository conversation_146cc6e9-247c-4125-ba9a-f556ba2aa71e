/**
 * Challenge Creation Types and Interfaces
 * 
 * Comprehensive type definitions for the refactored challenge creation system
 * 
 * <AUTHOR> Team
 */

/**
 * Challenge form steps
 */
export type ChallengeFormStep = 'basic' | 'schedule' | 'requirements' | 'rewards' | 'media'

/**
 * Challenge types
 */
export type ChallengeType = 'design' | 'photo' | 'build' | 'creative' | 'collaboration'

/**
 * Challenge difficulty levels
 */
export type ChallengeDifficulty = 'beginner' | 'intermediate' | 'advanced' | 'expert'

/**
 * Challenge status
 */
export type ChallengeStatus = 'draft' | 'upcoming' | 'active'

/**
 * User tier levels
 */
export type UserTier = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'

/**
 * Challenge requirements interface
 */
export interface ChallengeRequirements {
  minTier?: UserTier
  maxSubmissions: number
  allowTeams: boolean
  requiresApproval: boolean
  allowedFileTypes: string[]
  maxFileSize: number
}

/**
 * Challenge rewards interface
 */
export interface ChallengeRewards {
  winner: { points: number; badge?: string; specialReward?: string }
  runnerUp: { points: number; badge?: string }
  participation: { points: number }
  featured: { points: number }
}

/**
 * Challenge media interface
 */
export interface ChallengeMedia {
  bannerImage: string
  thumbnailImage: string
  inspirationImages?: string[]
  videoUrl?: string
}

/**
 * Main challenge form data interface
 */
export interface ChallengeFormData {
  title: string
  description: string
  shortDescription: string
  theme: string
  type: ChallengeType
  difficulty: ChallengeDifficulty
  status: ChallengeStatus
  startDate: string
  endDate: string
  votingStartDate: string
  votingEndDate: string
  rules: string[]
  requirements: ChallengeRequirements
  rewards: ChallengeRewards
  media: ChallengeMedia
  tags: string[]
  category: string
  featured: boolean
}

/**
 * Form validation errors
 */
export interface ChallengeFormErrors {
  title?: string
  description?: string
  shortDescription?: string
  theme?: string
  category?: string
  startDate?: string
  endDate?: string
  votingStartDate?: string
  votingEndDate?: string
  rules?: string
  requirements?: string
  rewards?: string
  media?: string
  tags?: string
  general?: string
}

/**
 * Challenge form state
 */
export interface ChallengeFormState {
  currentStep: ChallengeFormStep
  formData: ChallengeFormData
  errors: ChallengeFormErrors
  isValidating: boolean
  isDirty: boolean
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean
  errors: ChallengeFormErrors
}

/**
 * Step component props interface
 */
export interface ChallengeStepProps {
  formState: ChallengeFormState
  onUpdateFormState: (updates: Partial<ChallengeFormState>) => void
  onNext: () => void
  onPrev: () => void
}

/**
 * Container component props
 */
export interface ChallengeCreateContainerProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

/**
 * Form hook return type
 */
export interface UseChallengeFormReturn {
  formState: ChallengeFormState
  updateFormState: (updates: Partial<ChallengeFormState>) => void
  resetForm: () => void
  validateCurrentStep: () => ValidationResult
  validateAllSteps: () => ValidationResult
}

/**
 * Submission hook return type
 */
export interface UseChallengeSubmissionReturn {
  submitChallenge: (formData: ChallengeFormData) => Promise<boolean>
  isSubmitting: boolean
  submissionError: string | null
}

/**
 * Initial form state
 */
export const INITIAL_CHALLENGE_FORM_STATE: ChallengeFormState = {
  currentStep: 'basic',
  formData: {
    title: '',
    description: '',
    shortDescription: '',
    theme: '',
    type: 'design',
    difficulty: 'intermediate',
    status: 'draft',
    startDate: '',
    endDate: '',
    votingStartDate: '',
    votingEndDate: '',
    rules: [],
    requirements: {
      maxSubmissions: 3,
      allowTeams: false,
      requiresApproval: false,
      allowedFileTypes: ['png', 'jpg', 'jpeg'],
      maxFileSize: 10
    },
    rewards: {
      winner: { points: 500, badge: '', specialReward: '' },
      runnerUp: { points: 300, badge: '' },
      participation: { points: 50 },
      featured: { points: 100 }
    },
    media: {
      bannerImage: '',
      thumbnailImage: '',
      inspirationImages: [],
      videoUrl: ''
    },
    tags: [],
    category: '',
    featured: false
  },
  errors: {},
  isValidating: false,
  isDirty: false
}

/**
 * Step configuration
 */
export const CHALLENGE_STEPS = [
  { id: 'basic', title: 'Basic Info', icon: 'FileText', description: 'Title, description, and basic settings' },
  { id: 'schedule', title: 'Schedule', icon: 'Calendar', description: 'Start, end, and voting dates' },
  { id: 'requirements', title: 'Rules & Requirements', icon: 'Settings', description: 'Rules, requirements, and restrictions' },
  { id: 'rewards', title: 'Rewards', icon: 'Trophy', description: 'Points, badges, and special rewards' },
  { id: 'media', title: 'Media & Tags', icon: 'Palette', description: 'Images, videos, tags, and categorization' }
] as const

/**
 * Challenge type options
 */
export const CHALLENGE_TYPES = [
  { value: 'design', label: 'Design Challenge', icon: '🎨', description: 'Create visual designs and artwork' },
  { value: 'photo', label: 'Photography', icon: '📸', description: 'Capture and share photographs' },
  { value: 'build', label: 'Build Project', icon: '🔧', description: 'Build physical or digital projects' },
  { value: 'creative', label: 'Creative Writing', icon: '✍️', description: 'Write stories, poems, or articles' },
  { value: 'collaboration', label: 'Collaboration', icon: '🤝', description: 'Work together on team projects' }
] as const

/**
 * Difficulty level options
 */
export const DIFFICULTY_LEVELS = [
  { value: 'beginner', label: 'Beginner', color: 'green', description: 'Perfect for newcomers' },
  { value: 'intermediate', label: 'Intermediate', color: 'blue', description: 'Some experience required' },
  { value: 'advanced', label: 'Advanced', color: 'orange', description: 'Significant experience needed' },
  { value: 'expert', label: 'Expert', color: 'red', description: 'For seasoned professionals' }
] as const

/**
 * Status options
 */
export const STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', description: 'Not visible to users, can be edited freely' },
  { value: 'upcoming', label: 'Upcoming', description: 'Visible but not yet started' },
  { value: 'active', label: 'Active', description: 'Currently running and accepting submissions' }
] as const

/**
 * User tier options
 */
export const USER_TIERS = [
  { value: 'bronze', label: 'Bronze', color: '#CD7F32' },
  { value: 'silver', label: 'Silver', color: '#C0C0C0' },
  { value: 'gold', label: 'Gold', color: '#FFD700' },
  { value: 'platinum', label: 'Platinum', color: '#E5E4E2' },
  { value: 'diamond', label: 'Diamond', color: '#B9F2FF' }
] as const

/**
 * File type options
 */
export const FILE_TYPES = [
  { value: 'png', label: 'PNG', description: 'Portable Network Graphics' },
  { value: 'jpg', label: 'JPG', description: 'JPEG Image' },
  { value: 'jpeg', label: 'JPEG', description: 'JPEG Image' },
  { value: 'gif', label: 'GIF', description: 'Graphics Interchange Format' },
  { value: 'webp', label: 'WebP', description: 'Modern image format' },
  { value: 'svg', label: 'SVG', description: 'Scalable Vector Graphics' },
  { value: 'pdf', label: 'PDF', description: 'Portable Document Format' },
  { value: 'mp4', label: 'MP4', description: 'Video file' },
  { value: 'mov', label: 'MOV', description: 'QuickTime video' }
] as const

/**
 * Step titles for display
 */
export const STEP_TITLES: Record<ChallengeFormStep, string> = {
  basic: 'Basic Information',
  schedule: 'Schedule & Timing',
  requirements: 'Rules & Requirements',
  rewards: 'Rewards & Recognition',
  media: 'Media & Tags'
}

/**
 * Step order for navigation
 */
export const STEP_ORDER: ChallengeFormStep[] = ['basic', 'schedule', 'requirements', 'rewards', 'media']
