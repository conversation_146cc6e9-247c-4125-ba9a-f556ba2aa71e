/**
 * Challenge Creation Modal Component
 * 
 * Comprehensive form for creating new community challenges with all required fields,
 * validation, and proper error handling.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, Plus, Trash2, Upload, Calendar, Trophy, Users, 
  FileText, Settings, Tag, Palette, Clock, Star,
  AlertCircle, CheckCircle, Save
} from 'lucide-react'
import { createChallenge } from '@/lib/api/gamification'
import { Timestamp } from 'firebase/firestore'
import { toast } from 'react-hot-toast'

interface ChallengeCreateModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface ChallengeFormData {
  title: string
  description: string
  shortDescription: string
  theme: string
  type: 'design' | 'photo' | 'build' | 'creative' | 'collaboration'
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  status: 'draft' | 'upcoming' | 'active'
  startDate: string
  endDate: string
  votingStartDate: string
  votingEndDate: string
  rules: string[]
  requirements: {
    minTier?: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
    maxSubmissions: number
    allowTeams: boolean
    requiresApproval: boolean
    allowedFileTypes: string[]
    maxFileSize: number
  }
  rewards: {
    winner: { points: number, badge?: string, specialReward?: string }
    runnerUp: { points: number, badge?: string }
    participation: { points: number }
    featured: { points: number }
  }
  media: {
    bannerImage: string
    thumbnailImage: string
    inspirationImages?: string[]
    videoUrl?: string
  }
  tags: string[]
  category: string
  featured: boolean
}

const ChallengeCreateModal: React.FC<ChallengeCreateModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [newRule, setNewRule] = useState('')
  const [newTag, setNewTag] = useState('')
  const [newFileType, setNewFileType] = useState('')
  const [newInspirationImage, setNewInspirationImage] = useState('')

  const [formData, setFormData] = useState<ChallengeFormData>({
    title: '',
    description: '',
    shortDescription: '',
    theme: '',
    type: 'design',
    difficulty: 'intermediate',
    status: 'draft',
    startDate: '',
    endDate: '',
    votingStartDate: '',
    votingEndDate: '',
    rules: [],
    requirements: {
      maxSubmissions: 3,
      allowTeams: false,
      requiresApproval: false,
      allowedFileTypes: ['png', 'jpg', 'jpeg'],
      maxFileSize: 10
    },
    rewards: {
      winner: { points: 500, badge: '', specialReward: '' },
      runnerUp: { points: 300, badge: '' },
      participation: { points: 50 },
      featured: { points: 100 }
    },
    media: {
      bannerImage: '',
      thumbnailImage: '',
      inspirationImages: [],
      videoUrl: ''
    },
    tags: [],
    category: '',
    featured: false
  })

  const steps = [
    { number: 1, title: 'Basic Info', icon: FileText },
    { number: 2, title: 'Schedule', icon: Calendar },
    { number: 3, title: 'Rules & Requirements', icon: Settings },
    { number: 4, title: 'Rewards', icon: Trophy },
    { number: 5, title: 'Media & Tags', icon: Palette }
  ]

  const challengeTypes = [
    { value: 'design', label: 'Design Challenge', icon: '🎨' },
    { value: 'photo', label: 'Photography', icon: '📸' },
    { value: 'build', label: 'Build Project', icon: '🔧' },
    { value: 'creative', label: 'Creative Writing', icon: '✍️' },
    { value: 'collaboration', label: 'Collaboration', icon: '🤝' }
  ]

  const difficultyLevels = [
    { value: 'beginner', label: 'Beginner', color: 'text-green-400' },
    { value: 'intermediate', label: 'Intermediate', color: 'text-yellow-400' },
    { value: 'advanced', label: 'Advanced', color: 'text-orange-400' },
    { value: 'expert', label: 'Expert', color: 'text-red-400' }
  ]

  const tierLevels = [
    { value: 'bronze', label: 'Bronze' },
    { value: 'silver', label: 'Silver' },
    { value: 'gold', label: 'Gold' },
    { value: 'platinum', label: 'Platinum' },
    { value: 'diamond', label: 'Diamond' }
  ]

  const categories = [
    'Design', 'Photography', 'Build', 'Art', 'Technology', 
    'Community', 'Educational', 'Entertainment', 'Other'
  ]

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof ChallengeFormData] as any,
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const addRule = () => {
    if (newRule.trim()) {
      setFormData(prev => ({
        ...prev,
        rules: [...prev.rules, newRule.trim()]
      }))
      setNewRule('')
    }
  }

  const removeRule = (index: number) => {
    setFormData(prev => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index)
    }))
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }))
  }

  const addFileType = () => {
    if (newFileType.trim() && !formData.requirements.allowedFileTypes.includes(newFileType.trim())) {
      setFormData(prev => ({
        ...prev,
        requirements: {
          ...prev.requirements,
          allowedFileTypes: [...prev.requirements.allowedFileTypes, newFileType.trim()]
        }
      }))
      setNewFileType('')
    }
  }

  const removeFileType = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requirements: {
        ...prev.requirements,
        allowedFileTypes: prev.requirements.allowedFileTypes.filter((_, i) => i !== index)
      }
    }))
  }

  const addInspirationImage = () => {
    if (newInspirationImage.trim()) {
      setFormData(prev => ({
        ...prev,
        media: {
          ...prev.media,
          inspirationImages: [...(prev.media.inspirationImages || []), newInspirationImage.trim()]
        }
      }))
      setNewInspirationImage('')
    }
  }

  const removeInspirationImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      media: {
        ...prev.media,
        inspirationImages: prev.media.inspirationImages?.filter((_, i) => i !== index) || []
      }
    }))
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.title && formData.description && formData.shortDescription && 
                 formData.theme && formData.type && formData.difficulty && formData.category)
      case 2:
        return !!(formData.startDate && formData.endDate && 
                 formData.votingStartDate && formData.votingEndDate)
      case 3:
        return formData.rules.length > 0 && formData.requirements.allowedFileTypes.length > 0
      case 4:
        return formData.rewards.winner.points > 0 && formData.rewards.participation.points > 0
      case 5:
        return !!(formData.media.bannerImage && formData.media.thumbnailImage && formData.tags.length > 0)
      default:
        return true
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 5))
    } else {
      toast.error('Please fill in all required fields before proceeding')
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(5)) {
      toast.error('Please complete all required fields')
      return
    }

    setLoading(true)
    try {
      const challengeData = {
        ...formData,
        startDate: Timestamp.fromDate(new Date(formData.startDate)),
        endDate: Timestamp.fromDate(new Date(formData.endDate)),
        votingStartDate: Timestamp.fromDate(new Date(formData.votingStartDate)),
        votingEndDate: Timestamp.fromDate(new Date(formData.votingEndDate)),
        createdBy: 'admin',
        moderators: ['admin']
      }

      const challengeId = await createChallenge(challengeData)
      toast.success(`Challenge created successfully! ID: ${challengeId}`)
      onSuccess()
      onClose()
      
      // Reset form
      setCurrentStep(1)
      setFormData({
        title: '',
        description: '',
        shortDescription: '',
        theme: '',
        type: 'design',
        difficulty: 'intermediate',
        status: 'draft',
        startDate: '',
        endDate: '',
        votingStartDate: '',
        votingEndDate: '',
        rules: [],
        requirements: {
          maxSubmissions: 3,
          allowTeams: false,
          requiresApproval: false,
          allowedFileTypes: ['png', 'jpg', 'jpeg'],
          maxFileSize: 10
        },
        rewards: {
          winner: { points: 500, badge: '', specialReward: '' },
          runnerUp: { points: 300, badge: '' },
          participation: { points: 50 },
          featured: { points: 100 }
        },
        media: {
          bannerImage: '',
          thumbnailImage: '',
          inspirationImages: [],
          videoUrl: ''
        },
        tags: [],
        category: '',
        featured: false
      })
    } catch (error: any) {
      console.error('Error creating challenge:', error)
      toast.error(`Failed to create challenge: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">Basic Information</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Challenge Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                placeholder="Enter challenge title"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Short Description *
              </label>
              <input
                type="text"
                value={formData.shortDescription}
                onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                placeholder="Brief description for cards and previews"
                maxLength={100}
              />
              <p className="text-xs text-gray-500 mt-1">{formData.shortDescription.length}/100 characters</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Full Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                placeholder="Detailed challenge description, objectives, and what participants should achieve"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Theme *
                </label>
                <input
                  type="text"
                  value={formData.theme}
                  onChange={(e) => handleInputChange('theme', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  placeholder="e.g., Cyberpunk, Nature, Minimalism"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                >
                  <option value="">Select category</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Challenge Type *
                </label>
                <div className="space-y-2">
                  {challengeTypes.map(type => (
                    <label key={type.value} className="flex items-center">
                      <input
                        type="radio"
                        name="type"
                        value={type.value}
                        checked={formData.type === type.value}
                        onChange={(e) => handleInputChange('type', e.target.value)}
                        className="mr-3 text-purple-500"
                      />
                      <span className="mr-2">{type.icon}</span>
                      <span className="text-gray-300">{type.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Difficulty Level *
                </label>
                <div className="space-y-2">
                  {difficultyLevels.map(level => (
                    <label key={level.value} className="flex items-center">
                      <input
                        type="radio"
                        name="difficulty"
                        value={level.value}
                        checked={formData.difficulty === level.value}
                        onChange={(e) => handleInputChange('difficulty', e.target.value)}
                        className="mr-3 text-purple-500"
                      />
                      <span className={`${level.color} capitalize font-medium`}>{level.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Status *
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
              >
                <option value="draft">Draft</option>
                <option value="upcoming">Upcoming</option>
                <option value="active">Active</option>
              </select>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">Schedule & Timing</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Start Date *
                </label>
                <input
                  type="datetime-local"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  End Date *
                </label>
                <input
                  type="datetime-local"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Voting Start Date *
                </label>
                <input
                  type="datetime-local"
                  value={formData.votingStartDate}
                  onChange={(e) => handleInputChange('votingStartDate', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Voting End Date *
                </label>
                <input
                  type="datetime-local"
                  value={formData.votingEndDate}
                  onChange={(e) => handleInputChange('votingEndDate', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>

            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5" />
                <div>
                  <h4 className="text-blue-400 font-medium">Scheduling Guidelines</h4>
                  <ul className="text-sm text-blue-300 mt-2 space-y-1">
                    <li>• Challenge should run for at least 7 days</li>
                    <li>• Voting period should be 3-7 days after challenge ends</li>
                    <li>• Ensure dates don't overlap with other major challenges</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">Rules & Requirements</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Challenge Rules *
              </label>
              <div className="space-y-2">
                {formData.rules.map((rule, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span className="text-gray-400 text-sm">{index + 1}.</span>
                    <span className="flex-1 text-gray-300">{rule}</span>
                    <button
                      onClick={() => removeRule(index)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex space-x-2 mt-3">
                <input
                  type="text"
                  value={newRule}
                  onChange={(e) => setNewRule(e.target.value)}
                  placeholder="Add a new rule"
                  className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  onKeyPress={(e) => e.key === 'Enter' && addRule()}
                />
                <button
                  onClick={addRule}
                  disabled={!newRule.trim()}
                  className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Maximum Submissions
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.requirements.maxSubmissions}
                  onChange={(e) => handleInputChange('requirements.maxSubmissions', parseInt(e.target.value))}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Maximum File Size (MB)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={formData.requirements.maxFileSize}
                  onChange={(e) => handleInputChange('requirements.maxFileSize', parseInt(e.target.value))}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Minimum Tier Requirement
              </label>
              <select
                value={formData.requirements.minTier || ''}
                onChange={(e) => handleInputChange('requirements.minTier', e.target.value || undefined)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
              >
                <option value="">No minimum tier</option>
                {tierLevels.map(tier => (
                  <option key={tier.value} value={tier.value}>{tier.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Allowed File Types *
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.requirements.allowedFileTypes.map((type, index) => (
                  <span key={index} className="inline-flex items-center space-x-1 px-3 py-1 bg-gray-700 rounded-full text-sm">
                    <span>{type}</span>
                    <button
                      onClick={() => removeFileType(index)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newFileType}
                  onChange={(e) => setNewFileType(e.target.value.toLowerCase())}
                  placeholder="e.g., png, jpg, pdf"
                  className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  onKeyPress={(e) => e.key === 'Enter' && addFileType()}
                />
                <button
                  onClick={addFileType}
                  disabled={!newFileType.trim()}
                  className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.requirements.allowTeams}
                  onChange={(e) => handleInputChange('requirements.allowTeams', e.target.checked)}
                  className="mr-3 rounded text-purple-500"
                />
                <span className="text-gray-300">Allow team submissions</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.requirements.requiresApproval}
                  onChange={(e) => handleInputChange('requirements.requiresApproval', e.target.checked)}
                  className="mr-3 rounded text-purple-500"
                />
                <span className="text-gray-300">Require submission approval</span>
              </label>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">Rewards & Incentives</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="text-md font-medium text-white">Winner Rewards</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Points *
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.rewards.winner.points}
                    onChange={(e) => handleInputChange('rewards.winner.points', parseInt(e.target.value))}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Badge
                  </label>
                  <input
                    type="text"
                    value={formData.rewards.winner.badge || ''}
                    onChange={(e) => handleInputChange('rewards.winner.badge', e.target.value)}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                    placeholder="e.g., Design Master"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Special Reward
                  </label>
                  <input
                    type="text"
                    value={formData.rewards.winner.specialReward || ''}
                    onChange={(e) => handleInputChange('rewards.winner.specialReward', e.target.value)}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                    placeholder="e.g., 1 month premium access"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-md font-medium text-white">Runner-up Rewards</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Points *
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.rewards.runnerUp.points}
                    onChange={(e) => handleInputChange('rewards.runnerUp.points', parseInt(e.target.value))}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Badge
                  </label>
                  <input
                    type="text"
                    value={formData.rewards.runnerUp.badge || ''}
                    onChange={(e) => handleInputChange('rewards.runnerUp.badge', e.target.value)}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                    placeholder="e.g., Design Expert"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Participation Points *
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.rewards.participation.points}
                  onChange={(e) => handleInputChange('rewards.participation.points', parseInt(e.target.value))}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
                <p className="text-xs text-gray-500 mt-1">Points for all participants</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Featured Submission Points *
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.rewards.featured.points}
                  onChange={(e) => handleInputChange('rewards.featured.points', parseInt(e.target.value))}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
                <p className="text-xs text-gray-500 mt-1">Points for featured submissions</p>
              </div>
            </div>

            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Trophy className="w-5 h-5 text-yellow-400 mt-0.5" />
                <div>
                  <h4 className="text-yellow-400 font-medium">Reward Guidelines</h4>
                  <ul className="text-sm text-yellow-300 mt-2 space-y-1">
                    <li>• Winner rewards should be significantly higher than participation</li>
                    <li>• Consider the challenge difficulty when setting point values</li>
                    <li>• Special rewards can include premium features, merchandise, or recognition</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">Media & Tags</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Banner Image URL *
                </label>
                <input
                  type="url"
                  value={formData.media.bannerImage}
                  onChange={(e) => handleInputChange('media.bannerImage', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  placeholder="https://example.com/banner.jpg"
                />
                <p className="text-xs text-gray-500 mt-1">Main banner image (recommended: 1200x400px)</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Thumbnail Image URL *
                </label>
                <input
                  type="url"
                  value={formData.media.thumbnailImage}
                  onChange={(e) => handleInputChange('media.thumbnailImage', e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  placeholder="https://example.com/thumbnail.jpg"
                />
                <p className="text-xs text-gray-500 mt-1">Card thumbnail (recommended: 400x300px)</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Video URL (Optional)
              </label>
              <input
                type="url"
                value={formData.media.videoUrl || ''}
                onChange={(e) => handleInputChange('media.videoUrl', e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                placeholder="https://youtube.com/watch?v=..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Inspiration Images
              </label>
              <div className="space-y-2 mb-3">
                {formData.media.inspirationImages?.map((image, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span className="flex-1 text-gray-300 truncate">{image}</span>
                    <button
                      onClick={() => removeInspirationImage(index)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="url"
                  value={newInspirationImage}
                  onChange={(e) => setNewInspirationImage(e.target.value)}
                  placeholder="https://example.com/inspiration.jpg"
                  className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  onKeyPress={(e) => e.key === 'Enter' && addInspirationImage()}
                />
                <button
                  onClick={addInspirationImage}
                  disabled={!newInspirationImage.trim()}
                  className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Tags *
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags.map((tag, index) => (
                  <span key={index} className="inline-flex items-center space-x-1 px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">
                    <Tag className="w-3 h-3" />
                    <span>{tag}</span>
                    <button
                      onClick={() => removeTag(index)}
                      className="text-purple-400 hover:text-purple-300"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value.toLowerCase())}
                  placeholder="Add a tag"
                  className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <button
                  onClick={addTag}
                  disabled={!newTag.trim()}
                  className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.featured}
                  onChange={(e) => handleInputChange('featured', e.target.checked)}
                  className="mr-3 rounded text-purple-500"
                />
                <Star className="w-4 h-4 text-yellow-400 mr-2" />
                <span className="text-gray-300">Feature this challenge</span>
              </label>
              <p className="text-xs text-gray-500 ml-9 mt-1">Featured challenges appear prominently on the community page</p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-gray-900 rounded-xl border border-gray-800 w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-800">
            <div>
              <h2 className="text-xl font-bold text-white">Create New Challenge</h2>
              <p className="text-gray-400 text-sm">Step {currentStep} of 5</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Progress Bar */}
          <div className="px-6 py-4 border-b border-gray-800">
            <div className="flex items-center justify-between mb-2">
              {steps.map((step) => (
                <div key={step.number} className="flex items-center">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${currentStep >= step.number
                      ? 'bg-purple-500 text-white'
                      : 'bg-gray-700 text-gray-400'
                    }
                  `}>
                    {currentStep > step.number ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      <step.icon className="w-4 h-4" />
                    )}
                  </div>
                  {step.number < 5 && (
                    <div className={`
                      w-16 h-0.5 mx-2
                      ${currentStep > step.number ? 'bg-purple-500' : 'bg-gray-700'}
                    `} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between text-xs text-gray-400">
              {steps.map((step) => (
                <span key={step.number} className={currentStep >= step.number ? 'text-purple-400' : ''}>
                  {step.title}
                </span>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {renderStepContent()}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-800">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="px-4 py-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Previous
            </button>

            <div className="flex space-x-3">
              {currentStep < 5 ? (
                <button
                  onClick={nextStep}
                  className="px-6 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
                >
                  Next
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={loading}
                  className="flex items-center space-x-2 px-6 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Creating...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      <span>Create Challenge</span>
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default ChallengeCreateModal