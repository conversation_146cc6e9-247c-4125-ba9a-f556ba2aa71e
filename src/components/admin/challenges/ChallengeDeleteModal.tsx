/**
 * Challenge Delete Confirmation Modal
 * 
 * Modal for confirming challenge deletion with safety checks
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Trash2, Alert<PERSON>riangle, Trophy } from 'lucide-react'
import { Challenge } from '@/lib/api/gamification'
import { toast } from 'react-hot-toast'

interface ChallengeDeleteModalProps {
  isOpen: boolean
  challenge: Challenge | null
  onClose: () => void
  onConfirm: (challengeId: string) => void
}

const ChallengeDeleteModal: React.FC<ChallengeDeleteModalProps> = ({
  isOpen,
  challenge,
  onClose,
  onConfirm
}) => {
  const [confirmText, setConfirmText] = useState('')
  const [loading, setLoading] = useState(false)

  const handleDelete = async () => {
    if (!challenge?.id) {
      toast.error('No challenge selected for deletion')
      return
    }

    if (confirmText !== challenge.title) {
      toast.error('Please type the exact challenge title to confirm deletion')
      return
    }

    setLoading(true)
    try {
      console.log('🗑️ Delete modal: Starting deletion for challenge ID:', challenge.id)
      await onConfirm(challenge.id)
      toast.success('Challenge deleted successfully!')
      onClose()
      setConfirmText('')
    } catch (error: any) {
      console.error('❌ Delete modal: Error deleting challenge:', error)
      toast.error(`Failed to delete challenge: ${error.message || 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setConfirmText('')
    onClose()
  }

  if (!isOpen || !challenge) return null

  const isActiveChallenge = challenge.status === 'active'
  const hasParticipants = (challenge.stats?.participants || 0) > 0

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4"
        onClick={(e) => e.target === e.currentTarget && handleClose()}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-gray-900 rounded-xl border border-gray-800 w-full max-w-md shadow-2xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-800">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center">
                <Trash2 className="w-5 h-5 text-red-400" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-white">Delete Challenge</h2>
                <p className="text-gray-400 text-sm">This action cannot be undone</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="space-y-4">
              {/* Challenge Info */}
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <Trophy className="w-5 h-5 text-purple-400" />
                  <div>
                    <h3 className="font-medium text-white">{challenge.title}</h3>
                    <p className="text-sm text-gray-400">{challenge.shortDescription}</p>
                  </div>
                </div>
              </div>

              {/* Warnings */}
              {(isActiveChallenge || hasParticipants) && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="text-red-400 font-medium mb-2">Warning</h4>
                      <ul className="text-sm text-red-300 space-y-1">
                        {isActiveChallenge && (
                          <li>• This challenge is currently active and accepting submissions</li>
                        )}
                        {hasParticipants && (
                          <li>• {challenge.stats?.participants || 0} participants will lose their progress</li>
                        )}
                        <li>• All submissions and votes will be permanently deleted</li>
                        <li>• This action cannot be undone</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* Confirmation Input */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Type the challenge title to confirm:
                </label>
                <input
                  type="text"
                  value={confirmText}
                  onChange={(e) => setConfirmText(e.target.value)}
                  placeholder={challenge.title}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:outline-none"
                />
              </div>

              {/* Additional Safety Info */}
              <div className="bg-gray-800 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-300 mb-2">What will be deleted:</h4>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>• Challenge details and configuration</li>
                  <li>• All participant submissions</li>
                  <li>• All votes and ratings</li>
                  <li>• Challenge statistics and analytics</li>
                  <li>• Media files and attachments</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-800">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>

            <button
              onClick={handleDelete}
              disabled={loading || confirmText !== challenge.title}
              className="flex items-center space-x-2 px-6 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Deleting...</span>
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4" />
                  <span>Delete Challenge</span>
                </>
              )}
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default ChallengeDeleteModal