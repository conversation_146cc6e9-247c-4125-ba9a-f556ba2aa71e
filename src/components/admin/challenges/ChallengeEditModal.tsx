/**
 * Challenge Edit Modal Component
 * 
 * Modal for editing existing challenges with all the same fields as create
 * but pre-populated with existing data.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, Plus, Trash2, Upload, Calendar, Trophy, Users, 
  FileText, Settings, Tag, Palette, Clock, Star,
  AlertCircle, CheckCircle, Save
} from 'lucide-react'
import { updateChallenge, Challenge } from '@/lib/api/gamification'
import { Timestamp } from 'firebase/firestore'
import { toast } from 'react-hot-toast'

interface ChallengeEditModalProps {
  isOpen: boolean
  challenge: Challenge | null
  onClose: () => void
  onSuccess: () => void
}

interface ChallengeFormData {
  title: string
  description: string
  shortDescription: string
  theme: string
  type: 'design' | 'photo' | 'build' | 'creative' | 'collaboration'
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  status: 'draft' | 'upcoming' | 'active' | 'voting' | 'completed' | 'cancelled'
  startDate: string
  endDate: string
  votingStartDate: string
  votingEndDate: string
  rules: string[]
  requirements: {
    minTier?: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
    maxSubmissions: number
    allowTeams: boolean
    requiresApproval: boolean
    allowedFileTypes: string[]
    maxFileSize: number
  }
  rewards: {
    winner: { points: number, badge?: string, specialReward?: string }
    runnerUp: { points: number, badge?: string }
    participation: { points: number }
    featured: { points: number }
  }
  media: {
    bannerImage: string
    thumbnailImage: string
    inspirationImages?: string[]
    videoUrl?: string
  }
  tags: string[]
  category: string
  featured: boolean
}

const ChallengeEditModal: React.FC<ChallengeEditModalProps> = ({
  isOpen,
  challenge,
  onClose,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<ChallengeFormData>({
    title: '',
    description: '',
    shortDescription: '',
    theme: '',
    type: 'design',
    difficulty: 'intermediate',
    status: 'draft',
    startDate: '',
    endDate: '',
    votingStartDate: '',
    votingEndDate: '',
    rules: [],
    requirements: {
      maxSubmissions: 3,
      allowTeams: false,
      requiresApproval: false,
      allowedFileTypes: ['png', 'jpg', 'jpeg'],
      maxFileSize: 10
    },
    rewards: {
      winner: { points: 500, badge: '', specialReward: '' },
      runnerUp: { points: 300, badge: '' },
      participation: { points: 50 },
      featured: { points: 100 }
    },
    media: {
      bannerImage: '',
      thumbnailImage: '',
      inspirationImages: [],
      videoUrl: ''
    },
    tags: [],
    category: '',
    featured: false
  })

  // Populate form with challenge data when challenge changes
  useEffect(() => {
    if (challenge) {
      const formatTimestampToInput = (timestamp: any) => {
        if (timestamp && timestamp.toDate) {
          return timestamp.toDate().toISOString().slice(0, 16)
        }
        if (timestamp instanceof Date) {
          return timestamp.toISOString().slice(0, 16)
        }
        return ''
      }

      setFormData({
        title: challenge.title || '',
        description: challenge.description || '',
        shortDescription: challenge.shortDescription || '',
        theme: challenge.theme || '',
        type: challenge.type || 'design',
        difficulty: challenge.difficulty || 'intermediate',
        status: challenge.status || 'draft',
        startDate: formatTimestampToInput(challenge.startDate),
        endDate: formatTimestampToInput(challenge.endDate),
        votingStartDate: formatTimestampToInput(challenge.votingStartDate),
        votingEndDate: formatTimestampToInput(challenge.votingEndDate),
        rules: challenge.rules || [],
        requirements: challenge.requirements || {
          maxSubmissions: 3,
          allowTeams: false,
          requiresApproval: false,
          allowedFileTypes: ['png', 'jpg', 'jpeg'],
          maxFileSize: 10
        },
        rewards: challenge.rewards || {
          winner: { points: 500, badge: '', specialReward: '' },
          runnerUp: { points: 300, badge: '' },
          participation: { points: 50 },
          featured: { points: 100 }
        },
        media: challenge.media || {
          bannerImage: '',
          thumbnailImage: '',
          inspirationImages: [],
          videoUrl: ''
        },
        tags: challenge.tags || [],
        category: challenge.category || '',
        featured: challenge.featured || false
      })
    }
  }, [challenge])

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof ChallengeFormData] as any,
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleSubmit = async () => {
    if (!challenge?.id) return

    setLoading(true)
    try {
      const updateData = {
        ...formData,
        startDate: formData.startDate ? Timestamp.fromDate(new Date(formData.startDate)) : challenge.startDate,
        endDate: formData.endDate ? Timestamp.fromDate(new Date(formData.endDate)) : challenge.endDate,
        votingStartDate: formData.votingStartDate ? Timestamp.fromDate(new Date(formData.votingStartDate)) : challenge.votingStartDate,
        votingEndDate: formData.votingEndDate ? Timestamp.fromDate(new Date(formData.votingEndDate)) : challenge.votingEndDate,
      }

      await updateChallenge(challenge.id, updateData)
      toast.success('Challenge updated successfully!')
      onSuccess()
      onClose()
    } catch (error: any) {
      console.error('Error updating challenge:', error)
      toast.error(`Failed to update challenge: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen || !challenge) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-gray-900 rounded-xl border border-gray-800 w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-800">
            <div>
              <h2 className="text-xl font-bold text-white">Edit Challenge</h2>
              <p className="text-gray-400 text-sm">{challenge.title}</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Challenge Title
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  >
                    <option value="draft">Draft</option>
                    <option value="upcoming">Upcoming</option>
                    <option value="active">Active</option>
                    <option value="voting">Voting</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                />
              </div>

              {/* Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Start Date
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    End Date
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              {/* Rewards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Winner Points
                  </label>
                  <input
                    type="number"
                    value={formData.rewards.winner.points}
                    onChange={(e) => handleInputChange('rewards.winner.points', parseInt(e.target.value))}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Runner-up Points
                  </label>
                  <input
                    type="number"
                    value={formData.rewards.runnerUp.points}
                    onChange={(e) => handleInputChange('rewards.runnerUp.points', parseInt(e.target.value))}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Participation Points
                  </label>
                  <input
                    type="number"
                    value={formData.rewards.participation.points}
                    onChange={(e) => handleInputChange('rewards.participation.points', parseInt(e.target.value))}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                  />
                </div>
              </div>

              {/* Featured */}
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="mr-3 rounded text-purple-500"
                  />
                  <Star className="w-4 h-4 text-yellow-400 mr-2" />
                  <span className="text-gray-300">Feature this challenge</span>
                </label>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-800">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>

            <button
              onClick={handleSubmit}
              disabled={loading}
              className="flex items-center space-x-2 px-6 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Updating...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>Update Challenge</span>
                </>
              )}
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default ChallengeEditModal