/**
 * Challenge View Modal Component
 * 
 * Modal for viewing challenge details in a read-only format
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, Calendar, Trophy, Users, Tag, Clock, Star,
  CheckCircle, AlertCircle, FileText, Settings,
  Target, Award, Gift, Eye, Share2
} from 'lucide-react'
import { Challenge } from '@/lib/api/gamification'
import { formatDistanceToNow } from 'date-fns'

interface ChallengeViewModalProps {
  isOpen: boolean
  challenge: Challenge | null
  onClose: () => void
  onEdit?: (challenge: Challenge) => void
}

const ChallengeViewModal: React.FC<ChallengeViewModalProps> = ({
  isOpen,
  challenge,
  onClose,
  onEdit
}) => {
  if (!isOpen || !challenge) return null

  const formatTimestamp = (timestamp: any) => {
    if (timestamp && timestamp.toDate) {
      return timestamp.toDate().toLocaleDateString() + ' ' + timestamp.toDate().toLocaleTimeString()
    }
    if (timestamp instanceof Date) {
      return timestamp.toLocaleDateString() + ' ' + timestamp.toLocaleTimeString()
    }
    return 'Not set'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'upcoming':
        return 'bg-blue-500/10 text-blue-400 border-blue-500/20'
      case 'completed':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'voting':
        return 'bg-purple-500/10 text-purple-400 border-purple-500/20'
      case 'draft':
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
      case 'cancelled':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4" />
      case 'upcoming':
        return <Clock className="w-4 h-4" />
      case 'completed':
        return <Trophy className="w-4 h-4" />
      case 'voting':
        return <Star className="w-4 h-4" />
      case 'draft':
        return <FileText className="w-4 h-4" />
      case 'cancelled':
        return <AlertCircle className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'text-green-400'
      case 'intermediate':
        return 'text-yellow-400'
      case 'advanced':
        return 'text-orange-400'
      case 'expert':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-gray-900 rounded-xl border border-gray-800 w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-800">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                <Trophy className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">{challenge.title}</h2>
                <div className="flex items-center space-x-2 mt-1">
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs border ${getStatusColor(challenge.status)}`}>
                    {getStatusIcon(challenge.status)}
                    <span className="capitalize">{challenge.status}</span>
                  </span>
                  {challenge.featured && (
                    <span className="inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs bg-yellow-500/10 text-yellow-400 border border-yellow-500/20">
                      <Star className="w-3 h-3" />
                      <span>Featured</span>
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {onEdit && (
                <button
                  onClick={() => onEdit(challenge)}
                  className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors text-sm"
                >
                  Edit
                </button>
              )}
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[70vh] overflow-y-auto">
            <div className="space-y-8">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Challenge Details</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm text-gray-400">Description:</span>
                      <p className="text-gray-300 mt-1">{challenge.description}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-400">Short Description:</span>
                      <p className="text-gray-300 mt-1">{challenge.shortDescription}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-400">Theme:</span>
                      <p className="text-gray-300 mt-1">{challenge.theme}</p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div>
                        <span className="text-sm text-gray-400">Type:</span>
                        <p className="text-gray-300 capitalize">{challenge.type}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">Difficulty:</span>
                        <p className={`capitalize font-medium ${getDifficultyColor(challenge.difficulty)}`}>
                          {challenge.difficulty}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Statistics</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-800 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <Users className="w-5 h-5 text-blue-400" />
                        <span className="text-sm text-gray-400">Participants</span>
                      </div>
                      <p className="text-2xl font-bold text-white mt-2">{challenge.stats?.participants || 0}</p>
                    </div>
                    <div className="bg-gray-800 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <FileText className="w-5 h-5 text-green-400" />
                        <span className="text-sm text-gray-400">Submissions</span>
                      </div>
                      <p className="text-2xl font-bold text-white mt-2">{challenge.stats?.submissions || 0}</p>
                    </div>
                    <div className="bg-gray-800 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <Star className="w-5 h-5 text-yellow-400" />
                        <span className="text-sm text-gray-400">Votes</span>
                      </div>
                      <p className="text-2xl font-bold text-white mt-2">{challenge.stats?.totalVotes || 0}</p>
                    </div>
                    <div className="bg-gray-800 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <Eye className="w-5 h-5 text-purple-400" />
                        <span className="text-sm text-gray-400">Views</span>
                      </div>
                      <p className="text-2xl font-bold text-white mt-2">{challenge.stats?.views || 0}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Timeline</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Calendar className="w-5 h-5 text-blue-400" />
                      <span className="text-sm font-medium text-gray-300">Challenge Period</span>
                    </div>
                    <p className="text-sm text-gray-400">Start: {formatTimestamp(challenge.startDate)}</p>
                    <p className="text-sm text-gray-400">End: {formatTimestamp(challenge.endDate)}</p>
                  </div>
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Target className="w-5 h-5 text-purple-400" />
                      <span className="text-sm font-medium text-gray-300">Voting Period</span>
                    </div>
                    <p className="text-sm text-gray-400">Start: {formatTimestamp(challenge.votingStartDate)}</p>
                    <p className="text-sm text-gray-400">End: {formatTimestamp(challenge.votingEndDate)}</p>
                  </div>
                </div>
              </div>

              {/* Rules */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Rules</h3>
                <div className="bg-gray-800 rounded-lg p-4">
                  {challenge.rules && challenge.rules.length > 0 ? (
                    <ol className="space-y-2">
                      {challenge.rules.map((rule, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-purple-400 font-medium">{index + 1}.</span>
                          <span className="text-gray-300">{rule}</span>
                        </li>
                      ))}
                    </ol>
                  ) : (
                    <p className="text-gray-400">No rules specified</p>
                  )}
                </div>
              </div>

              {/* Requirements */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Requirements</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Max Submissions:</span>
                        <span className="text-white">{challenge.requirements?.maxSubmissions || 'Not set'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Max File Size:</span>
                        <span className="text-white">{challenge.requirements?.maxFileSize || 'Not set'} MB</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Teams Allowed:</span>
                        <span className="text-white">{challenge.requirements?.allowTeams ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Requires Approval:</span>
                        <span className="text-white">{challenge.requirements?.requiresApproval ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="mb-2">
                      <span className="text-sm text-gray-400">Allowed File Types:</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {challenge.requirements?.allowedFileTypes?.map((type, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-700 rounded text-xs text-gray-300">
                          {type}
                        </span>
                      )) || <span className="text-gray-400">None specified</span>}
                    </div>
                  </div>
                </div>
              </div>

              {/* Rewards */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Rewards</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Trophy className="w-5 h-5 text-yellow-400" />
                      <span className="text-sm font-medium text-yellow-300">Winner</span>
                    </div>
                    <p className="text-lg font-bold text-white">{challenge.rewards?.winner?.points || 0} pts</p>
                    {challenge.rewards?.winner?.badge && (
                      <p className="text-xs text-yellow-300 mt-1">{challenge.rewards.winner.badge}</p>
                    )}
                  </div>
                  <div className="bg-gradient-to-br from-gray-500/10 to-gray-600/10 border border-gray-500/20 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Award className="w-5 h-5 text-gray-400" />
                      <span className="text-sm font-medium text-gray-300">Runner-up</span>
                    </div>
                    <p className="text-lg font-bold text-white">{challenge.rewards?.runnerUp?.points || 0} pts</p>
                    {challenge.rewards?.runnerUp?.badge && (
                      <p className="text-xs text-gray-300 mt-1">{challenge.rewards.runnerUp.badge}</p>
                    )}
                  </div>
                  <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="w-5 h-5 text-blue-400" />
                      <span className="text-sm font-medium text-blue-300">Participation</span>
                    </div>
                    <p className="text-lg font-bold text-white">{challenge.rewards?.participation?.points || 0} pts</p>
                  </div>
                  <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Star className="w-5 h-5 text-purple-400" />
                      <span className="text-sm font-medium text-purple-300">Featured</span>
                    </div>
                    <p className="text-lg font-bold text-white">{challenge.rewards?.featured?.points || 0} pts</p>
                  </div>
                </div>
              </div>

              {/* Tags */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {challenge.tags && challenge.tags.length > 0 ? (
                    challenge.tags.map((tag, index) => (
                      <span key={index} className="inline-flex items-center space-x-1 px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">
                        <Tag className="w-3 h-3" />
                        <span>{tag}</span>
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-400">No tags specified</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default ChallengeViewModal