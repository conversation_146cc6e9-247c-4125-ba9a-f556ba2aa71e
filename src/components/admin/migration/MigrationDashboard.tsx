'use client'

/**
 * Migration Dashboard Component
 * Real-time monitoring interface for image migration
 */

import React, { useState, useEffect } from 'react'
import { 
  migrationMonitor, 
  MigrationSession, 
  MigrationAlert, 
  PerformanceMetrics 
} from '../../../lib/migration/migrationMonitor'

interface MigrationDashboardProps {
  sessionId?: string
}

export default function MigrationDashboard({ sessionId }: MigrationDashboardProps) {
  const [sessions, setSessions] = useState<MigrationSession[]>([])
  const [currentSession, setCurrentSession] = useState<MigrationSession | null>(null)
  const [alerts, setAlerts] = useState<MigrationAlert[]>([])
  const [performance, setPerformance] = useState<PerformanceMetrics[]>([])
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    // Initialize data
    setSessions(migrationMonitor.getAllSessions())
    setAlerts(migrationMonitor.getAlerts(sessionId))
    setPerformance(migrationMonitor.getPerformanceHistory(sessionId, 50))

    if (sessionId) {
      setCurrentSession(migrationMonitor.getSession(sessionId) || null)
    }

    // Set up event listeners
    const handleSessionUpdate = (data: any) => {
      setSessions(migrationMonitor.getAllSessions())
      if (data.sessionId === sessionId) {
        setCurrentSession(migrationMonitor.getSession(sessionId) || null)
      }
    }

    const handleAlertUpdate = (alert: MigrationAlert) => {
      if (!sessionId || alert.sessionId === sessionId) {
        setAlerts(migrationMonitor.getAlerts(sessionId))
      }
    }

    const handlePerformanceUpdate = (data: any) => {
      if (!sessionId || data.sessionId === sessionId) {
        setPerformance(migrationMonitor.getPerformanceHistory(sessionId, 50))
      }
    }

    migrationMonitor.on('session:started', handleSessionUpdate)
    migrationMonitor.on('session:completed', handleSessionUpdate)
    migrationMonitor.on('migration:progress', handleSessionUpdate)
    migrationMonitor.on('scan:progress', handleSessionUpdate)
    migrationMonitor.on('database:progress', handleSessionUpdate)
    migrationMonitor.on('alert:created', handleAlertUpdate)
    migrationMonitor.on('alert:acknowledged', handleAlertUpdate)
    migrationMonitor.on('performance:metrics', handlePerformanceUpdate)

    setIsConnected(true)

    return () => {
      migrationMonitor.off('session:started', handleSessionUpdate)
      migrationMonitor.off('session:completed', handleSessionUpdate)
      migrationMonitor.off('migration:progress', handleSessionUpdate)
      migrationMonitor.off('scan:progress', handleSessionUpdate)
      migrationMonitor.off('database:progress', handleSessionUpdate)
      migrationMonitor.off('alert:created', handleAlertUpdate)
      migrationMonitor.off('alert:acknowledged', handleAlertUpdate)
      migrationMonitor.off('performance:metrics', handlePerformanceUpdate)
    }
  }, [sessionId])

  const handleAcknowledgeAlert = (alertId: string) => {
    migrationMonitor.acknowledgeAlert(alertId)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400'
      case 'failed': return 'text-red-400'
      case 'scanning': case 'migrating': case 'updating': return 'text-blue-400'
      case 'cancelled': return 'text-yellow-400'
      default: return 'text-gray-400'
    }
  }

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'critical': return 'border-red-500 bg-red-900/20'
      case 'error': return 'border-red-400 bg-red-900/10'
      case 'warning': return 'border-yellow-400 bg-yellow-900/10'
      case 'info': return 'border-blue-400 bg-blue-900/10'
      default: return 'border-gray-400 bg-gray-900/10'
    }
  }

  const formatDuration = (startTime: Date, endTime?: Date) => {
    const end = endTime || new Date()
    const duration = end.getTime() - startTime.getTime()
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Migration Dashboard</h1>
            <p className="text-gray-400 mt-2">
              Real-time monitoring for image migration to Cloudflare R2
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${isConnected ? 'text-green-400' : 'text-red-400'}`}>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
              <span className="text-sm">{isConnected ? 'Connected' : 'Disconnected'}</span>
            </div>
          </div>
        </div>

        {/* Current Session Overview */}
        {currentSession && (
          <div className="bg-gray-800 rounded-lg p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Current Session: {currentSession.id}</h2>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(currentSession.status)}`}>
                {currentSession.status.toUpperCase()}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-400">{currentSession.processedImages}</div>
                <div className="text-sm text-gray-400">Processed Images</div>
                <div className="text-xs text-gray-500">of {currentSession.totalImages} total</div>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-400">{currentSession.successRate.toFixed(1)}%</div>
                <div className="text-sm text-gray-400">Success Rate</div>
                <div className="text-xs text-gray-500">Error rate: {currentSession.errorRate.toFixed(1)}%</div>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-400">{currentSession.phase.toUpperCase()}</div>
                <div className="text-sm text-gray-400">Current Phase</div>
                <div className="text-xs text-gray-500">{formatDuration(currentSession.startTime, currentSession.endTime)}</div>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-yellow-400">
                  {currentSession.estimatedCompletion 
                    ? new Date(currentSession.estimatedCompletion).toLocaleTimeString()
                    : 'N/A'
                  }
                </div>
                <div className="text-sm text-gray-400">Est. Completion</div>
                <div className="text-xs text-gray-500">{currentSession.currentOperation}</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-6">
              <div className="flex justify-between text-sm text-gray-400 mb-2">
                <span>Progress</span>
                <span>{currentSession.totalImages > 0 ? ((currentSession.processedImages / currentSession.totalImages) * 100).toFixed(1) : 0}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: currentSession.totalImages > 0 
                      ? `${(currentSession.processedImages / currentSession.totalImages) * 100}%` 
                      : '0%' 
                  }}
                ></div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Alerts Panel */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Active Alerts</h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {alerts.filter(alert => !alert.acknowledged).length === 0 ? (
                <div className="text-gray-400 text-center py-8">
                  <div className="text-4xl mb-2">✅</div>
                  <div>No active alerts</div>
                </div>
              ) : (
                alerts
                  .filter(alert => !alert.acknowledged)
                  .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
                  .map(alert => (
                    <div key={alert.id} className={`border rounded-lg p-4 ${getAlertColor(alert.level)}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium">{alert.title}</span>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              alert.level === 'critical' ? 'bg-red-500 text-white' :
                              alert.level === 'error' ? 'bg-red-400 text-white' :
                              alert.level === 'warning' ? 'bg-yellow-400 text-black' :
                              'bg-blue-400 text-white'
                            }`}>
                              {alert.level.toUpperCase()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-300 mb-2">{alert.message}</div>
                          <div className="text-xs text-gray-500">
                            {alert.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                        <button
                          onClick={() => handleAcknowledgeAlert(alert.id)}
                          className="ml-4 px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-sm transition-colors"
                        >
                          Acknowledge
                        </button>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
            {performance.length > 0 ? (
              <div className="space-y-4">
                {/* Latest Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-lg font-bold text-blue-400">
                      {performance[performance.length - 1]?.imagesPerSecond.toFixed(2) || '0.00'}
                    </div>
                    <div className="text-sm text-gray-400">Images/sec</div>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-lg font-bold text-green-400">
                      {performance[performance.length - 1]?.averageUploadTime.toFixed(0) || '0'}ms
                    </div>
                    <div className="text-sm text-gray-400">Avg Upload Time</div>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-lg font-bold text-purple-400">
                      {performance[performance.length - 1]?.networkThroughput.toFixed(2) || '0.00'} MB/s
                    </div>
                    <div className="text-sm text-gray-400">Throughput</div>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-lg font-bold text-yellow-400">
                      {performance[performance.length - 1]?.memoryUsage.toFixed(1) || '0.0'}%
                    </div>
                    <div className="text-sm text-gray-400">Memory Usage</div>
                  </div>
                </div>

                {/* Performance Chart Placeholder */}
                <div className="bg-gray-700 rounded-lg p-4 h-32 flex items-center justify-center">
                  <div className="text-gray-400 text-center">
                    <div className="text-2xl mb-2">📊</div>
                    <div className="text-sm">Performance Chart</div>
                    <div className="text-xs">(Chart implementation needed)</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-400 text-center py-8">
                <div className="text-4xl mb-2">📊</div>
                <div>No performance data available</div>
              </div>
            )}
          </div>
        </div>

        {/* All Sessions */}
        <div className="bg-gray-800 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold mb-4">Migration Sessions</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-3 px-4">Session ID</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Phase</th>
                  <th className="text-left py-3 px-4">Progress</th>
                  <th className="text-left py-3 px-4">Success Rate</th>
                  <th className="text-left py-3 px-4">Duration</th>
                  <th className="text-left py-3 px-4">Started</th>
                </tr>
              </thead>
              <tbody>
                {sessions.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center py-8 text-gray-400">
                      No migration sessions found
                    </td>
                  </tr>
                ) : (
                  sessions
                    .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
                    .map(session => (
                      <tr key={session.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                        <td className="py-3 px-4 font-mono text-xs">{session.id}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(session.status)}`}>
                            {session.status}
                          </span>
                        </td>
                        <td className="py-3 px-4">{session.phase}</td>
                        <td className="py-3 px-4">
                          {session.totalImages > 0 
                            ? `${session.processedImages}/${session.totalImages} (${((session.processedImages / session.totalImages) * 100).toFixed(1)}%)`
                            : 'N/A'
                          }
                        </td>
                        <td className="py-3 px-4">{session.successRate.toFixed(1)}%</td>
                        <td className="py-3 px-4">{formatDuration(session.startTime, session.endTime)}</td>
                        <td className="py-3 px-4">{session.startTime.toLocaleString()}</td>
                      </tr>
                    ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
