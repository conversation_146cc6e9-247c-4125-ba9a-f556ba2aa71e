/**
 * Performance Monitoring Dashboard
 * 
 * Real-time performance metrics and monitoring for Phase 2 refactored components
 * Tracks animation performance, bundle sizes, and optimization opportunities
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { 
  Activity, 
  Zap, 
  Package, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Gauge
} from 'lucide-react'
import { animationMonitor, useAnimationMonitoring } from '../../../lib/performance/animationMonitor'
import { getBundleAnalyzer } from '../../../lib/performance/bundleAnalyzer'
import { getPhase2BundleReport } from '../../../lib/performance/phase3CodeSplitting'

/**
 * Performance metrics interface
 */
interface PerformanceMetrics {
  animations: {
    totalAnimations: number
    averageFPS: number
    averageDuration: number
    budgetViolations: number
  }
  bundles: {
    totalSize: number
    criticalPathSize: number
    lazyLoadedSize: number
    componentCount: number
  }
  vitals: {
    fcp: number
    lcp: number
    cls: number
    fid: number
  }
  recommendations: string[]
}

/**
 * Performance dashboard component
 */
export const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'animations' | 'bundles' | 'vitals'>('overview')
  
  const { getSummary, getMetrics } = useAnimationMonitoring()

  /**
   * Load performance metrics
   */
  useEffect(() => {
    const loadMetrics = async () => {
      setIsLoading(true)
      
      try {
        // Get animation metrics
        const animationSummary = getSummary()
        
        // Get bundle metrics
        const bundleAnalyzer = getBundleAnalyzer()
        const bundleAnalysis = await bundleAnalyzer.analyzeBundles()
        const phase2Report = getPhase2BundleReport()
        
        // Get web vitals (mock data for now)
        const vitals = {
          fcp: 1200, // First Contentful Paint
          lcp: 2100, // Largest Contentful Paint
          cls: 0.05, // Cumulative Layout Shift
          fid: 45    // First Input Delay
        }
        
        // Combine all metrics
        const combinedMetrics: PerformanceMetrics = {
          animations: animationSummary,
          bundles: {
            totalSize: phase2Report.totalEstimatedSize,
            criticalPathSize: phase2Report.criticalPathSize,
            lazyLoadedSize: phase2Report.lazyLoadedSize,
            componentCount: phase2Report.componentCount
          },
          vitals,
          recommendations: [
            ...animationSummary.recommendations,
            ...phase2Report.recommendations,
            ...bundleAnalysis.recommendations.map(r => r.description)
          ]
        }
        
        setMetrics(combinedMetrics)
      } catch (error) {
        console.error('Failed to load performance metrics:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadMetrics()
    
    // Refresh metrics every 30 seconds
    const interval = setInterval(loadMetrics, 30000)
    return () => clearInterval(interval)
  }, [getSummary])

  /**
   * Get performance score
   */
  const getPerformanceScore = (metrics: PerformanceMetrics): number => {
    let score = 100
    
    // Animation performance
    if (metrics.animations.averageFPS < 60) score -= 15
    if (metrics.animations.budgetViolations > 0) score -= 10
    
    // Bundle performance
    if (metrics.bundles.criticalPathSize > 100000) score -= 15
    if (metrics.bundles.totalSize > 500000) score -= 10
    
    // Web vitals
    if (metrics.vitals.lcp > 2500) score -= 15
    if (metrics.vitals.cls > 0.1) score -= 10
    if (metrics.vitals.fid > 100) score -= 10
    
    return Math.max(0, score)
  }

  /**
   * Metric card component
   */
  const MetricCard: React.FC<{
    title: string
    value: string | number
    unit?: string
    trend?: 'up' | 'down' | 'stable'
    status?: 'good' | 'warning' | 'error'
    icon: React.ReactNode
  }> = ({ title, value, unit, trend, status, icon }) => {
    const getStatusColor = () => {
      switch (status) {
        case 'good': return 'text-green-400 border-green-500'
        case 'warning': return 'text-yellow-400 border-yellow-500'
        case 'error': return 'text-red-400 border-red-500'
        default: return 'text-gray-400 border-gray-600'
      }
    }
    
    const getTrendIcon = () => {
      switch (trend) {
        case 'up': return <TrendingUp size={16} className="text-green-400" />
        case 'down': return <TrendingDown size={16} className="text-red-400" />
        default: return null
      }
    }
    
    return (
      <div className={`p-4 bg-gray-800 rounded-lg border ${getStatusColor()}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-sm text-gray-400">{title}</span>
          </div>
          {getTrendIcon()}
        </div>
        <div className="text-2xl font-bold text-white">
          {typeof value === 'number' ? value.toLocaleString() : value}
          {unit && <span className="text-sm text-gray-400 ml-1">{unit}</span>}
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-8 bg-gray-900 rounded-lg">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-3 text-gray-400">Loading performance metrics...</span>
        </div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="p-8 bg-gray-900 rounded-lg">
        <div className="text-center">
          <AlertTriangle size={48} className="text-yellow-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Failed to Load Metrics</h3>
          <p className="text-gray-400">Unable to load performance data. Please try again.</p>
        </div>
      </div>
    )
  }

  const performanceScore = getPerformanceScore(metrics)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Performance Dashboard</h2>
          <p className="text-gray-400">Real-time monitoring of Phase 2 refactored components</p>
        </div>
        
        {/* Performance Score */}
        <div className="flex items-center gap-4">
          <div className="text-center">
            <div className={`text-3xl font-bold ${
              performanceScore >= 80 ? 'text-green-400' :
              performanceScore >= 60 ? 'text-yellow-400' :
              'text-red-400'
            }`}>
              {performanceScore}
            </div>
            <div className="text-sm text-gray-400">Performance Score</div>
          </div>
          <Gauge size={32} className={
            performanceScore >= 80 ? 'text-green-400' :
            performanceScore >= 60 ? 'text-yellow-400' :
            'text-red-400'
          } />
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'animations', label: 'Animations', icon: Zap },
            { id: 'bundles', label: 'Bundles', icon: Package },
            { id: 'vitals', label: 'Web Vitals', icon: Activity }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <tab.icon size={16} />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Average FPS"
              value={metrics.animations.averageFPS}
              unit="fps"
              status={metrics.animations.averageFPS >= 60 ? 'good' : 'warning'}
              icon={<Zap size={16} />}
            />
            
            <MetricCard
              title="Bundle Size"
              value={Math.round(metrics.bundles.totalSize / 1024)}
              unit="KB"
              status={metrics.bundles.totalSize < 300000 ? 'good' : 'warning'}
              icon={<Package size={16} />}
            />
            
            <MetricCard
              title="LCP"
              value={metrics.vitals.lcp}
              unit="ms"
              status={metrics.vitals.lcp < 2500 ? 'good' : 'warning'}
              icon={<Clock size={16} />}
            />
            
            <MetricCard
              title="Components"
              value={metrics.bundles.componentCount}
              status="good"
              icon={<Activity size={16} />}
            />
          </div>

          {/* Recommendations */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <AlertTriangle size={20} className="text-yellow-400" />
              Optimization Recommendations
            </h3>
            
            {metrics.recommendations.length > 0 ? (
              <div className="space-y-2">
                {metrics.recommendations.slice(0, 5).map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-gray-700 rounded">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300 text-sm">{recommendation}</span>
                  </div>
                ))}
                {metrics.recommendations.length > 5 && (
                  <div className="text-sm text-gray-500 mt-2">
                    +{metrics.recommendations.length - 5} more recommendations
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-2 text-green-400">
                <CheckCircle size={16} />
                <span>No optimization recommendations at this time</span>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'animations' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <MetricCard
              title="Total Animations"
              value={metrics.animations.totalAnimations}
              icon={<Zap size={16} />}
            />
            
            <MetricCard
              title="Average Duration"
              value={Math.round(metrics.animations.averageDuration)}
              unit="ms"
              status={metrics.animations.averageDuration < 300 ? 'good' : 'warning'}
              icon={<Clock size={16} />}
            />
            
            <MetricCard
              title="Budget Violations"
              value={metrics.animations.budgetViolations}
              status={metrics.animations.budgetViolations === 0 ? 'good' : 'error'}
              icon={<AlertTriangle size={16} />}
            />
          </div>
          
          {/* Animation Details */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Animation Performance</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Target FPS:</span>
                <span className="text-white">60 fps</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Current Average:</span>
                <span className={`font-medium ${
                  metrics.animations.averageFPS >= 60 ? 'text-green-400' : 'text-yellow-400'
                }`}>
                  {metrics.animations.averageFPS} fps
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    metrics.animations.averageFPS >= 60 ? 'bg-green-500' : 'bg-yellow-500'
                  }`}
                  style={{ width: `${Math.min((metrics.animations.averageFPS / 60) * 100, 100)}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Additional tab content would be implemented here */}
    </div>
  )
}

export default PerformanceDashboard
