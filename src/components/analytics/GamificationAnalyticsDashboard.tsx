/**
 * Gamification Analytics Dashboard
 * 
 * Comprehensive real-time analytics dashboard for gamification system
 * with interactive charts, user engagement metrics, and performance monitoring.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  useGamificationAnalytics,
  useUserEngagement,
  useChallengeAnalytics,
  useAnalyticsCharts,
  useRealtimeAnalytics
} from '../../hooks/useGamificationAnalytics'
import { AnalyticsFilter } from '../../lib/api/gamification/analyticsEngine'

// ===== MAIN DASHBOARD COMPONENT =====

export default function GamificationAnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'challenges' | 'charts'>('overview')
  const [filters, setFilters] = useState<AnalyticsFilter | null>(null)
  const [showFilters, setShowFilters] = useState(false)

  const {
    metrics,
    loading: metricsLoading,
    error: metricsError,
    lastUpdated,
    refresh: refreshMetrics,
    generateReport,
    enableRealTime,
    setEnableRealTime
  } = useGamificationAnalytics(filters || undefined, true)

  const {
    isConnected,
    connectionStatus,
    updateCount,
    lastUpdate,
    subscribe,
    unsubscribe
  } = useRealtimeAnalytics()

  // Toggle real-time updates
  const handleRealTimeToggle = (enabled: boolean) => {
    setEnableRealTime(enabled)
    if (enabled) {
      subscribe('metrics')
      subscribe('users')
      subscribe('challenges')
    } else {
      unsubscribe('metrics')
      unsubscribe('users')
      unsubscribe('challenges')
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'users', label: 'User Engagement', icon: '👥' },
    { id: 'challenges', label: 'Challenge Analytics', icon: '🏆' },
    { id: 'charts', label: 'Charts & Visualizations', icon: '📈' }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Gamification Analytics
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Real-time insights into your gamification system performance
              </p>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-4">
              {/* Real-time toggle */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Real-time
                </span>
                <button
                  onClick={() => handleRealTimeToggle(!enableRealTime)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    enableRealTime
                      ? 'bg-green-600'
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      enableRealTime ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
                {enableRealTime && (
                  <div className="flex items-center space-x-1 text-xs text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span>Live ({updateCount} updates)</span>
                  </div>
                )}
              </div>

              {/* Filters button */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                🔍 Filters
              </button>

              {/* Refresh button */}
              <button
                onClick={refreshMetrics}
                disabled={metricsLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-accent-600 hover:bg-accent-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-500 disabled:opacity-50"
              >
                {metricsLoading ? '🔄' : '↻'} Refresh
              </button>

              {/* Generate report */}
              <div className="relative">
                <button
                  onClick={() => generateReport('summary')}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  📋 Generate Report
                </button>
              </div>
            </div>
          </div>

          {/* Status bar */}
          {lastUpdated && (
            <div className="pb-4">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Last updated: {lastUpdated.toLocaleString()}
                {enableRealTime && lastUpdate && (
                  <span className="ml-4">
                    Last real-time update: {lastUpdate.toLocaleString()}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Navigation tabs */}
          <div className="border-t border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-accent-500 text-accent-600 dark:text-accent-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Error display */}
      {metricsError && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Error loading analytics data
                </h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  {metricsError}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters panel */}
      {showFilters && (
        <FilterPanel
          filters={filters}
          onFiltersChange={setFilters}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <OverviewTab
            metrics={metrics}
            loading={metricsLoading}
            filters={filters}
          />
        )}
        {activeTab === 'users' && (
          <UserEngagementTab filters={filters} />
        )}
        {activeTab === 'challenges' && (
          <ChallengeAnalyticsTab />
        )}
        {activeTab === 'charts' && (
          <ChartsTab />
        )}
      </div>
    </div>
  )
}

// ===== OVERVIEW TAB =====

function OverviewTab({ 
  metrics, 
  loading, 
  filters 
}: { 
  metrics: any
  loading: boolean
  filters: AnalyticsFilter | null 
}) {
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">No data available</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Key metrics cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Users"
          value={metrics.totalUsers?.toLocaleString() || '0'}
          change="+12%"
          icon="👥"
          color="blue"
        />
        <MetricCard
          title="Active Users (Monthly)"
          value={metrics.activeUsers?.monthly?.toLocaleString() || '0'}
          change="+8%"
          icon="🔥"
          color="green"
        />
        <MetricCard
          title="Total Points Earned"
          value={metrics.pointsDistribution?.totalPoints?.toLocaleString() || '0'}
          change="+23%"
          icon="💰"
          color="yellow"
        />
        <MetricCard
          title="Active Challenges"
          value={metrics.challengeStats?.activeChallenges?.toString() || '0'}
          change="+5%"
          icon="🏆"
          color="purple"
        />
      </div>

      {/* Engagement overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Engagement Overview
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Achievement Completion Rate</span>
              <span className="font-medium">{metrics.achievementStats?.completionRate?.toFixed(1) || '0'}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Challenge Completion Rate</span>
              <span className="font-medium">{metrics.challengeStats?.completionRate?.toFixed(1) || '0'}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Reward Redemption Rate</span>
              <span className="font-medium">{metrics.rewardStats?.redemptionRate?.toFixed(1) || '0'}%</span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            System Health
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Cache Hit Rate</span>
              <span className="font-medium text-green-600">{metrics.systemHealth?.cacheHitRate?.toFixed(1) || '0'}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Response Time</span>
              <span className="font-medium">{metrics.systemHealth?.responseTime || '0'}ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Error Rate</span>
              <span className={`font-medium ${
                (metrics.systemHealth?.errorRate || 0) < 1 ? 'text-green-600' : 'text-red-600'
              }`}>
                {metrics.systemHealth?.errorRate?.toFixed(2) || '0'}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Tier distribution */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Tier Distribution
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {metrics.tierDistribution?.map((tier: any) => (
            <div key={tier.tier} className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {tier.userCount}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {tier.tier} ({tier.percentage}%)
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// ===== USER ENGAGEMENT TAB =====

function UserEngagementTab({ filters }: { filters: AnalyticsFilter | null }) {
  const {
    users,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    exportData,
    sortBy,
    sortOrder,
    setSorting
  } = useUserEngagement(50, filters || undefined)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          User Engagement Analytics
        </h2>
        <div className="flex space-x-4">
          <button
            onClick={exportData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            📥 Export CSV
          </button>
          <button
            onClick={refresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-accent-600 hover:bg-accent-700 disabled:opacity-50"
          >
            ↻ Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-500"></div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <UserEngagementTable
            users={users}
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSort={setSorting}
          />
          {hasMore && (
            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={loadMore}
                className="w-full text-center py-2 text-accent-600 hover:text-accent-500"
              >
                Load More Users
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// ===== CHALLENGE ANALYTICS TAB =====

function ChallengeAnalyticsTab() {
  const {
    challenges,
    selectedChallenge,
    loading,
    error,
    selectChallenge,
    refresh,
    compareMode,
    setCompareMode
  } = useChallengeAnalytics()

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Challenge Analytics
        </h2>
        <div className="flex space-x-4">
          <button
            onClick={() => setCompareMode(!compareMode)}
            className={`inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium ${
              compareMode
                ? 'border-accent-600 text-accent-600 bg-accent-50 dark:bg-accent-900/20'
                : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800'
            }`}
          >
            📊 Compare Mode
          </button>
          <button
            onClick={refresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-accent-600 hover:bg-accent-700 disabled:opacity-50"
          >
            ↻ Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-500"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Challenge list */}
          <div className="lg:col-span-1">
            <ChallengeList
              challenges={challenges}
              selectedChallenge={selectedChallenge}
              onSelectChallenge={selectChallenge}
              compareMode={compareMode}
            />
          </div>

          {/* Challenge details */}
          <div className="lg:col-span-2">
            {selectedChallenge ? (
              <ChallengeDetails challenge={selectedChallenge} />
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  Select a challenge to view detailed analytics
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// ===== CHARTS TAB =====

function ChartsTab() {
  const {
    chartData,
    timeRange,
    setTimeRange,
    loading,
    error,
    refresh
  } = useAnalyticsCharts()

  const timeRangeOptions = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' }
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Charts & Visualizations
        </h2>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="rounded-md border-gray-300 dark:border-gray-600 shadow-sm text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            {timeRangeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <button
            onClick={refresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-accent-600 hover:bg-accent-700 disabled:opacity-50"
          >
            ↻ Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-500"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Placeholder for charts - would integrate with charting library */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              User Activity Trend
            </h3>
            <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
              <p className="text-gray-500 dark:text-gray-400">
                Chart visualization would go here
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Points Distribution
            </h3>
            <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
              <p className="text-gray-500 dark:text-gray-400">
                Chart visualization would go here
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Tier Distribution
            </h3>
            <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
              <p className="text-gray-500 dark:text-gray-400">
                Chart visualization would go here
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Challenge Performance
            </h3>
            <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
              <p className="text-gray-500 dark:text-gray-400">
                Chart visualization would go here
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// ===== HELPER COMPONENTS =====

function MetricCard({ 
  title, 
  value, 
  change, 
  icon, 
  color 
}: { 
  title: string
  value: string
  change: string
  icon: string
  color: string 
}) {
  const colorClasses = {
    blue: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
    green: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400',
    yellow: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400',
    purple: 'bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
    >
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${colorClasses[color]}`}>
          <span className="text-2xl">{icon}</span>
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </p>
          <p className="text-2xl font-semibold text-gray-900 dark:text-white">
            {value}
          </p>
          <p className="text-sm text-green-600">
            {change} from last month
          </p>
        </div>
      </div>
    </motion.div>
  )
}

function FilterPanel({ 
  filters, 
  onFiltersChange, 
  onClose 
}: { 
  filters: AnalyticsFilter | null
  onFiltersChange: (filters: AnalyticsFilter | null) => void
  onClose: () => void 
}) {
  // Filter panel implementation
  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Filters
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ✕
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date Range
            </label>
            <input
              type="date"
              className="w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              User Segment
            </label>
            <select className="w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="all">All Users</option>
              <option value="new">New Users</option>
              <option value="active">Active Users</option>
              <option value="returning">Returning Users</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tier
            </label>
            <select className="w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="">All Tiers</option>
              <option value="bronze">Bronze</option>
              <option value="silver">Silver</option>
              <option value="gold">Gold</option>
              <option value="platinum">Platinum</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}

function UserEngagementTable({ 
  users, 
  sortBy, 
  sortOrder, 
  onSort 
}: { 
  users: any[]
  sortBy: string
  sortOrder: 'asc' | 'desc'
  onSort: (field: any, order: 'asc' | 'desc') => void 
}) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              User
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Engagement Score
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Total Points
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Current Tier
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Churn Risk
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          {users.map((user, index) => (
            <tr key={user.userId} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {user.username || `User ${index + 1}`}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {user.userId}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {user.engagementScore}/100
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {user.totalPoints?.toLocaleString()}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {user.currentTier}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  user.churnRisk === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  user.churnRisk === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {user.churnRisk}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function ChallengeList({ 
  challenges, 
  selectedChallenge, 
  onSelectChallenge, 
  compareMode 
}: { 
  challenges: any[]
  selectedChallenge: any
  onSelectChallenge: (id: string | null) => void
  compareMode: boolean 
}) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Challenges
        </h3>
      </div>
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {challenges.map((challenge) => (
          <button
            key={challenge.challengeId}
            onClick={() => onSelectChallenge(challenge.challengeId)}
            className={`w-full text-left p-4 hover:bg-gray-50 dark:hover:bg-gray-700 ${
              selectedChallenge?.challengeId === challenge.challengeId
                ? 'bg-accent-50 dark:bg-accent-900/20'
                : ''
            }`}
          >
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {challenge.challengeTitle}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {challenge.totalParticipants} participants
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {challenge.completionRate}% completion rate
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

function ChallengeDetails({ challenge }: { challenge: any }) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        {challenge.challengeTitle}
      </h3>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Total Participants</div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {challenge.totalParticipants}
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Completion Rate</div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {challenge.completionRate}%
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Average Progress</div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {challenge.averageProgress}%
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Engagement Score</div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {challenge.engagementScore}/100
          </div>
        </div>
      </div>
    </div>
  )
}