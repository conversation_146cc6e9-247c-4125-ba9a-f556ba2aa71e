/**
 * Animated Components
 * 
 * Pre-built animated components with micro-interactions
 * and gamification-specific animations.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { forwardRef, useRef, useImperativeHandle } from 'react'
import { motion, AnimatePresence, MotionProps } from 'framer-motion'
import { useAnimations, useGamificationAnimations } from '../../hooks/useAnimations'

// ===== TYPES =====

interface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  isLoading?: boolean
  children: React.ReactNode
  className?: string
}

interface AnimatedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined'
  hover?: boolean
  flip?: boolean
  children: React.ReactNode
  className?: string
}

interface AnimatedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean
  success?: boolean
  className?: string
}

interface AnimatedProgressBarProps {
  progress: number
  max?: number
  animated?: boolean
  showValue?: boolean
  color?: 'primary' | 'success' | 'warning' | 'error'
  className?: string
}

interface AnimatedNotificationProps {
  type?: 'info' | 'success' | 'warning' | 'error'
  title: string
  message?: string
  icon?: React.ReactNode
  onClose?: () => void
  duration?: number
  className?: string
}

interface AnimatedModalProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  className?: string
}

interface AnimatedListProps {
  children: React.ReactNode
  stagger?: boolean
  className?: string
}

// ===== ANIMATED BUTTON =====

const AnimatedButton = forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ variant = 'primary', size = 'md', isLoading, children, className = '', ...props }, ref) => {
    const { variants, microInteractions } = useAnimations()

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg'
    }

    const variantClasses = {
      primary: 'bg-accent-600 hover:bg-accent-700 text-white',
      secondary: 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100',
      ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
    }

    // Extract props that might conflict with Framer Motion
    const { onDrag, onDragStart, onDragEnd, onAnimationStart, onAnimationEnd, onAnimationIteration, ...motionProps } = props

    return (
      <motion.button
        ref={ref}
        variants={variants.fade}
        initial="hidden"
        animate="visible"
        whileHover="hover"
        whileTap="tap"
        disabled={isLoading || props.disabled}
        className={`
          inline-flex items-center justify-center
          font-medium rounded-lg
          transition-all duration-200
          focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2
          disabled:opacity-50 disabled:cursor-not-allowed
          ${sizeClasses[size]}
          ${variantClasses[variant]}
          ${className}
        `}
        {...motionProps}
      >
        <AnimatePresence mode="wait">
          {isLoading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                className="w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"
              />
              Loading...
            </motion.div>
          ) : (
            <motion.span
              key="content"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              {children}
            </motion.span>
          )}
        </AnimatePresence>
      </motion.button>
    )
  }
)

AnimatedButton.displayName = 'AnimatedButton'

// ===== ANIMATED CARD =====

const AnimatedCard = forwardRef<HTMLDivElement, AnimatedCardProps>(
  ({ variant = 'default', hover = true, flip = false, children, className = '', ...props }, ref) => {
    const { variants } = useAnimations()

    const variantClasses = {
      default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
      elevated: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg',
      outlined: 'bg-transparent border-2 border-gray-300 dark:border-gray-600'
    }

    // Extract props that might conflict with Framer Motion
    const { onDrag, onDragStart, onDragEnd, onAnimationStart, onAnimationEnd, onAnimationIteration, ...motionProps } = props

    return (
      <motion.div
        ref={ref}
        variants={variants.scale}
        initial="idle"
        animate="idle"
        whileHover={hover ? "hover" : undefined}
        whileTap="tap"
        className={`
          rounded-lg p-4
          transition-all duration-200
          ${variantClasses[variant]}
          ${className}
        `}
        {...motionProps}
      >
        <motion.div
          animate={flip ? { rotateY: 180 } : { rotateY: 0 }}
          transition={{ duration: 0.6 }}
          style={{ transformStyle: 'preserve-3d' }}
        >
          {children}
        </motion.div>
      </motion.div>
    )
  }
)

AnimatedCard.displayName = 'AnimatedCard'

// ===== ANIMATED INPUT =====

const AnimatedInput = forwardRef<HTMLInputElement, AnimatedInputProps>(
  ({ error, success, className = '', ...props }, ref) => {
    const { variants } = useAnimations()

    const getState = () => {
      if (error) return 'error'
      if (success) return 'success'
      return 'idle'
    }

    // Extract props that might conflict with Framer Motion
    const { onDrag, onDragStart, onDragEnd, onAnimationStart, onAnimationEnd, onAnimationIteration, ...motionProps } = props

    return (
      <motion.input
        ref={ref}
        variants={variants.fade}
        initial="idle"
        animate={getState()}
        whileFocus="focus"
        className={`
          block w-full px-3 py-2
          border rounded-lg
          bg-white dark:bg-gray-800
          text-gray-900 dark:text-gray-100
          focus:outline-none focus:ring-2 focus:ring-accent-500
          transition-all duration-200
          ${error ? 'border-red-500' : success ? 'border-green-500' : 'border-gray-300 dark:border-gray-600'}
          ${className}
        `}
        {...motionProps}
      />
    )
  }
)

AnimatedInput.displayName = 'AnimatedInput'

// ===== ANIMATED PROGRESS BAR =====

const AnimatedProgressBar: React.FC<AnimatedProgressBarProps> = ({
  progress,
  max = 100,
  animated = true,
  showValue = false,
  color = 'primary',
  className = ''
}) => {
  const percentage = Math.min((progress / max) * 100, 100)

  const colorClasses = {
    primary: 'bg-accent-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500'
  }

  return (
    <div className={`w-full ${className}`}>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
        <motion.div
          className={`h-full ${colorClasses[color]} relative`}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{
            duration: animated ? 0.6 : 0,
            ease: 'easeOut'
          }}
          data-progress-bar
        >
          {percentage === 100 && (
            <motion.div
              className="absolute inset-0 bg-white opacity-30"
              animate={{
                x: ['-100%', '100%']
              }}
              transition={{
                duration: 0.8,
                ease: 'easeInOut'
              }}
            />
          )}
        </motion.div>
      </div>
      {showValue && (
        <motion.div
          className="text-sm text-gray-600 dark:text-gray-400 mt-1 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          {Math.round(percentage)}%
        </motion.div>
      )}
    </div>
  )
}

// ===== ANIMATED NOTIFICATION =====

const AnimatedNotification: React.FC<AnimatedNotificationProps> = ({
  type = 'info',
  title,
  message,
  icon,
  onClose,
  duration = 5000,
  className = ''
}) => {
  const { variants } = useAnimations()

  const typeClasses = {
    info: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200',
    success: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200',
    warning: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200',
    error: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200'
  }

  const defaultIcons = {
    info: '💡',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  }

  React.useEffect(() => {
    if (duration > 0 && onClose) {
      const timer = setTimeout(onClose, duration)
      return () => clearTimeout(timer)
    }
  }, [duration, onClose])

  return (
    <motion.div
      variants={variants.notification}
      initial="hidden"
      animate="visible"
      exit="exit"
      className={`
        max-w-sm w-full border rounded-lg shadow-lg p-4
        ${typeClasses[type]}
        ${className}
      `}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {icon || <span className="text-lg">{defaultIcons[type]}</span>}
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium">{title}</h3>
          {message && (
            <p className="mt-1 text-sm opacity-90">{message}</p>
          )}
        </div>
        {onClose && (
          <motion.button
            onClick={onClose}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="ml-4 inline-flex text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <span className="sr-only">Close</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </motion.button>
        )}
      </div>
    </motion.div>
  )
}

// ===== ANIMATED MODAL =====

const AnimatedModal: React.FC<AnimatedModalProps> = ({
  isOpen,
  onClose,
  children,
  className = ''
}) => {
  const { variants } = useAnimations()

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            variants={variants.backdrop}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            variants={variants.modal}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={`
              relative w-full max-w-md
              bg-white dark:bg-gray-800
              rounded-xl shadow-xl
              ${className}
            `}
            onClick={(e) => e.stopPropagation()}
          >
            {children}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

// ===== ANIMATED LIST =====

const AnimatedList: React.FC<AnimatedListProps> = ({
  children,
  stagger = true,
  className = ''
}) => {
  const { variants } = useAnimations()

  return (
    <motion.div
      variants={stagger ? variants.list : variants.fade}
      initial="hidden"
      animate="visible"
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={stagger ? variants.listItem : variants.fade}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  )
}

// ===== GAMIFICATION COMPONENTS =====

interface AnimatedAchievementProps {
  achievement: {
    id: string
    title: string
    description: string
    icon: string
    rarity: string
    isUnlocked: boolean
  }
  onUnlock?: () => void
  className?: string
}

const AnimatedAchievement: React.FC<AnimatedAchievementProps> = ({
  achievement,
  onUnlock,
  className = ''
}) => {
  const { variants } = useAnimations()
  const gamificationAnimations = useGamificationAnimations()
  const cardRef = useRef<HTMLDivElement>(null)

  const handleUnlock = async () => {
    if (cardRef.current) {
      await gamificationAnimations.achievementUnlock(cardRef.current)
      onUnlock?.()
    }
  }

  React.useEffect(() => {
    if (achievement.isUnlocked) {
      handleUnlock()
    }
  }, [achievement.isUnlocked])

  return (
    <motion.div
      ref={cardRef}
      variants={variants.achievement}
      initial={achievement.isUnlocked ? "unlocked" : "locked"}
      animate={achievement.isUnlocked ? "unlocked" : "locked"}
      whileHover={achievement.isUnlocked ? "hover" : undefined}
      className={`
        relative p-4 rounded-lg border-2
        transition-all duration-200
        ${achievement.isUnlocked 
          ? 'border-accent-500 bg-accent-50 dark:bg-accent-900/20' 
          : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800'
        }
        ${className}
      `}
    >
      <div className="flex items-center space-x-3">
        <div className="text-3xl">{achievement.icon}</div>
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
            {achievement.title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {achievement.description}
          </p>
          <span className={`
            inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2
            ${achievement.rarity === 'common' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
              achievement.rarity === 'rare' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300' :
              achievement.rarity === 'epic' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
              'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
            }
          `}>
            {achievement.rarity}
          </span>
        </div>
      </div>
    </motion.div>
  )
}

interface AnimatedPointsDisplayProps {
  points: number
  change?: number
  onPointsChange?: (change: number) => void
  className?: string
}

const AnimatedPointsDisplay: React.FC<AnimatedPointsDisplayProps> = ({
  points,
  change,
  onPointsChange,
  className = ''
}) => {
  const { variants } = useAnimations()
  const gamificationAnimations = useGamificationAnimations()
  const pointsRef = useRef<HTMLDivElement>(null)
  const prevPoints = useRef(points)

  React.useEffect(() => {
    if (change !== undefined && pointsRef.current) {
      if (change > 0) {
        gamificationAnimations.pointsGain(change, pointsRef.current)
      } else if (change < 0) {
        gamificationAnimations.pointsLoss(Math.abs(change), pointsRef.current)
      }
      onPointsChange?.(change)
    }
    prevPoints.current = points
  }, [change, points])

  return (
    <motion.div
      ref={pointsRef}
      variants={variants.points}
      initial="idle"
      className={`
        inline-flex items-center font-semibold
        ${change && change > 0 ? 'text-green-600 dark:text-green-400' :
          change && change < 0 ? 'text-red-600 dark:text-red-400' :
          'text-gray-900 dark:text-gray-100'
        }
        ${className}
      `}
    >
      <span className="mr-1">💰</span>
      {points.toLocaleString()}
    </motion.div>
  )
}

export {
  AnimatedButton,
  AnimatedCard,
  AnimatedInput,
  AnimatedProgressBar,
  AnimatedNotification,
  AnimatedModal,
  AnimatedList,
  AnimatedAchievement,
  AnimatedPointsDisplay
}