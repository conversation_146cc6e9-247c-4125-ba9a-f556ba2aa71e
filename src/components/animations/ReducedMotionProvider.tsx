/**
 * Reduced Motion Provider
 * 
 * Context provider for managing reduced motion preferences and
 * providing optimized animation configurations throughout the app.
 * 
 * Phase 2 Performance Optimization - Community System Audit
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { Variants, TargetAndTransition } from 'framer-motion'

interface ReducedMotionContextType {
  prefersReducedMotion: boolean
  animationsEnabled: boolean
  performanceMode: 'high' | 'balanced' | 'low'
  isMobile: boolean
  
  // Utility functions
  getOptimizedVariants: (variants: Variants) => Variants
  getOptimizedTransition: (transition: any) => any
  shouldUseAnimation: (animationType: 'essential' | 'decorative' | 'complex') => boolean
  
  // Settings
  toggleAnimations: () => void
  setPerformanceMode: (mode: 'high' | 'balanced' | 'low') => void
}

const ReducedMotionContext = createContext<ReducedMotionContextType | undefined>(undefined)

interface ReducedMotionProviderProps {
  children: ReactNode
  defaultAnimationsEnabled?: boolean
  defaultPerformanceMode?: 'high' | 'balanced' | 'low'
}

export function ReducedMotionProvider({
  children,
  defaultAnimationsEnabled = true,
  defaultPerformanceMode = 'balanced'
}: ReducedMotionProviderProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)
  const [animationsEnabled, setAnimationsEnabled] = useState(defaultAnimationsEnabled)
  const [performanceMode, setPerformanceMode] = useState(defaultPerformanceMode)
  const [isMobile, setIsMobile] = useState(false)

  // Detect user preferences and device capabilities
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Check reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)
    
    // Check if mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))
    }
    
    checkMobile()
    
    // Listen for changes
    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
      if (e.matches) {
        setAnimationsEnabled(false)
      }
    }
    
    const handleResize = () => {
      checkMobile()
    }
    
    mediaQuery.addEventListener('change', handleReducedMotionChange)
    window.addEventListener('resize', handleResize)
    
    // Auto-adjust performance mode based on device
    if (isMobile && performanceMode === 'high') {
      setPerformanceMode('balanced')
    }
    
    // Check for low-end device indicators
    const isLowEnd = (navigator as any).hardwareConcurrency <= 2 || 
                     (navigator as any).deviceMemory <= 2
    
    if (isLowEnd && performanceMode !== 'low') {
      setPerformanceMode('low')
    }
    
    return () => {
      mediaQuery.removeEventListener('change', handleReducedMotionChange)
      window.removeEventListener('resize', handleResize)
    }
  }, [performanceMode, isMobile])

  // Get optimized variants based on current settings
  const getOptimizedVariants = (variants: Variants): Variants => {
    if (prefersReducedMotion || !animationsEnabled) {
      // Return instant variants
      const instantVariants: Variants = {}
      Object.keys(variants).forEach(key => {
        const variant = variants[key]
        if (typeof variant === 'object' && variant !== null) {
          instantVariants[key] = {
            ...variant,
            transition: { duration: 0.01 }
          }
        }
      })
      return instantVariants
    }

    // Optimize based on performance mode
    const optimizedVariants: Variants = {}
    Object.keys(variants).forEach(key => {
      const variant = variants[key]
      if (typeof variant === 'object' && variant !== null) {
        let optimizedVariant = { ...variant }
        
        if (performanceMode === 'low') {
          // Reduce duration and simplify easing
          optimizedVariant = {
            ...optimizedVariant,
            transition: {
              ...optimizedVariant.transition,
              duration: Math.min((optimizedVariant.transition?.duration || 0.3) * 0.5, 0.15),
              ease: 'linear'
            }
          }
        } else if (performanceMode === 'balanced') {
          // Moderate optimization
          optimizedVariant = {
            ...optimizedVariant,
            transition: {
              ...optimizedVariant.transition,
              duration: (optimizedVariant.transition?.duration || 0.3) * 0.75
            }
          }
        }
        
        optimizedVariants[key] = optimizedVariant
      }
    })
    
    return optimizedVariants
  }

  // Get optimized transition
  const getOptimizedTransition = (transition: any) => {
    if (prefersReducedMotion || !animationsEnabled) {
      return { duration: 0.01 }
    }
    
    if (performanceMode === 'low') {
      return {
        ...transition,
        duration: Math.min((transition?.duration || 0.3) * 0.5, 0.15),
        ease: 'linear'
      }
    }
    
    if (performanceMode === 'balanced') {
      return {
        ...transition,
        duration: (transition?.duration || 0.3) * 0.75
      }
    }
    
    return transition
  }

  // Determine if animation should be used based on type
  const shouldUseAnimation = (animationType: 'essential' | 'decorative' | 'complex'): boolean => {
    if (prefersReducedMotion) {
      // Only allow essential animations with reduced motion
      return animationType === 'essential'
    }
    
    if (!animationsEnabled) {
      return false
    }
    
    if (performanceMode === 'low') {
      // Only essential animations on low-end devices
      return animationType === 'essential'
    }
    
    if (performanceMode === 'balanced' && animationType === 'complex') {
      // Reduce complex animations on balanced mode
      return false
    }
    
    return true
  }

  // Toggle animations
  const toggleAnimations = () => {
    setAnimationsEnabled(prev => !prev)
  }

  const contextValue: ReducedMotionContextType = {
    prefersReducedMotion,
    animationsEnabled,
    performanceMode,
    isMobile,
    getOptimizedVariants,
    getOptimizedTransition,
    shouldUseAnimation,
    toggleAnimations,
    setPerformanceMode
  }

  return (
    <ReducedMotionContext.Provider value={contextValue}>
      {children}
    </ReducedMotionContext.Provider>
  )
}

// Hook to use reduced motion context
export function useReducedMotion() {
  const context = useContext(ReducedMotionContext)
  if (context === undefined) {
    throw new Error('useReducedMotion must be used within a ReducedMotionProvider')
  }
  return context
}

// Utility component for conditional animations
interface ConditionalAnimationProps {
  children: ReactNode
  animationType?: 'essential' | 'decorative' | 'complex'
  fallback?: ReactNode
}

export function ConditionalAnimation({
  children,
  animationType = 'decorative',
  fallback = null
}: ConditionalAnimationProps) {
  const { shouldUseAnimation } = useReducedMotion()
  
  if (shouldUseAnimation(animationType)) {
    return <>{children}</>
  }
  
  return <>{fallback}</>
}

// HOC for wrapping components with reduced motion support
export function withReducedMotion<P extends object>(
  Component: React.ComponentType<P>,
  animationType: 'essential' | 'decorative' | 'complex' = 'decorative'
) {
  return function ReducedMotionWrapper(props: P) {
    return (
      <ConditionalAnimation animationType={animationType}>
        <Component {...props} />
      </ConditionalAnimation>
    )
  }
}

// Preset animation variants optimized for different performance modes
export const optimizedPresets = {
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  },
  
  slideUp: {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  },
  
  slideDown: {
    hidden: { opacity: 0, y: -20 },
    visible: { opacity: 1, y: 0 }
  },
  
  scaleIn: {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1 }
  },
  
  staggerContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  },
  
  staggerItem: {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }
}

export default ReducedMotionProvider
