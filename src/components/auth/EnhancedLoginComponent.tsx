/**
 * Enhanced Login Component with MFA Support
 * 
 * Handles user authentication including Multi-Factor Authentication verification.
 * Provides seamless login experience with security features.
 * 
 * Features:
 * - Email/password authentication
 * - Google OAuth integration
 * - MFA verification flow
 * - Trusted device management
 * - Security logging
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Mail, Lock, Eye, EyeOff, Shield, Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { signIn, signInWithGoogle } from '@/lib/auth'
import { MFAService } from '@/lib/security/mfaService'
import MFAVerificationModal from '@/components/security/MFAVerificationModal'
import { UserProfile } from '@/types/profile'
import toast from 'react-hot-toast'

interface LoginFormData {
  email: string
  password: string
}

interface LoginState {
  step: 'login' | 'mfa-verification'
  user: any | null
  profile: UserProfile | null
  requiresMFA: boolean
}

export default function EnhancedLoginComponent() {
  const router = useRouter()
  const [form, setForm] = useState<LoginFormData>({
    email: '',
    password: ''
  })
  const [loginState, setLoginState] = useState<LoginState>({
    step: 'login',
    user: null,
    profile: null,
    requiresMFA: false
  })
  const [error, setError] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }))
    setError(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!form.email || !form.password) {
      setError({ message: 'Please fill in all fields' })
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await signIn(form.email, form.password)
      
      if (result.error) {
        setError(result.error)
        return
      }

      if (result.user) {
        if (result.requiresMFA) {
          // User needs MFA verification
          setLoginState({
            step: 'mfa-verification',
            user: result.user,
            profile: result.profile ?? null,
            requiresMFA: true
          })
        } else {
          // Login successful, redirect
          toast.success('Login successful!')
          router.push('/profile/account')
        }
      }
    } catch (err: any) {
      setError(err)
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await signInWithGoogle()
      
      if (result.error) {
        setError(result.error)
        return
      }

      if (result.user) {
        toast.success('Google sign-in successful!')
        router.push('/profile/account')
      }
    } catch (err: any) {
      setError(err)
    } finally {
      setLoading(false)
    }
  }

  const handleMFAVerificationSuccess = (trustDevice?: boolean) => {
    toast.success('Login successful!')
    
    // If user chose to trust device, this would be handled by the MFA service
    if (trustDevice) {
      toast.success('Device added to trusted devices')
    }
    
    router.push('/profile/account')
  }

  const handleMFACancel = () => {
    // Reset login state and sign out the user
    setLoginState({
      step: 'login',
      user: null,
      profile: null,
      requiresMFA: false
    })
    setForm({ email: '', password: '' })
    toast.info('Login cancelled')
  }

  const handleBackupCodeUsed = () => {
    toast.warning('Backup code used. Consider regenerating your backup codes.')
  }

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full space-y-8"
      >
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-3xl font-extrabold text-white">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-gray-400">
            Welcome back to Syndicaps
          </p>
        </div>

        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-red-900/20 border border-red-500/30 rounded-lg p-4"
          >
            <p className="text-red-400 text-sm">{error.message}</p>
          </motion.div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-200 mb-2">
                Email address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={form.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="input-gaming pl-10 pr-3"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-200 mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={form.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="input-gaming pl-10 pr-10"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-white" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-white" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm">
              <Link href="/auth/forgot-password" className="text-blue-400 hover:text-blue-300">
                Forgot your password?
              </Link>
            </div>
          </div>

          <div className="space-y-4">
            <button
              type="submit"
              disabled={loading}
              className="btn-auth w-full touch-target"
            >
              {loading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                'Sign in'
              )}
            </button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-600" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gray-900 text-gray-400">Or continue with</span>
              </div>
            </div>

            <button
              type="button"
              onClick={handleGoogleSignIn}
              disabled={loading}
              className="btn-outline w-full touch-target flex justify-center items-center"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Continue with Google
            </button>
          </div>

          <div className="text-center">
            <span className="text-gray-400 text-sm">
              Don't have an account?{' '}
              <Link href="/auth/register" className="text-blue-400 hover:text-blue-300 font-medium">
                Sign up
              </Link>
            </span>
          </div>
        </form>
      </motion.div>

      {/* MFA Verification Modal */}
      {loginState.step === 'mfa-verification' && loginState.user && loginState.profile && (
        <MFAVerificationModal
          isOpen={true}
          userId={loginState.user.uid}
          userEmail={loginState.profile.email}
          onVerificationSuccess={handleMFAVerificationSuccess}
          onCancel={handleMFACancel}
          onBackupCodeUsed={handleBackupCodeUsed}
        />
      )}
    </div>
  )
}
