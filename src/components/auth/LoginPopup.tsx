'use client';

import * as React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Loader2, X, CheckCircle } from 'lucide-react';
import { signIn, signInWithGoogle } from '@/lib/auth';

interface LoginPopupProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function LoginPopup({ open, onOpenChange }: LoginPopupProps) {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [error, setError] = React.useState<string | null>(null);
  const [isEmailSigningIn, setIsEmailSigningIn] = React.useState(false);
  const [isGoogleSigningIn, setIsGoogleSigningIn] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);
  const [authSuccess, setAuthSuccess] = React.useState(false);

  // Reset states when modal is closed
  React.useEffect(() => {
    if (!open) {
      setAuthSuccess(false);
      setError(null);
      setIsEmailSigningIn(false);
      setIsGoogleSigningIn(false);
    }
  }, [open]);

  const handleSignIn = async () => {
    setError(null);
    if (!email || !password) {
      setError('Please enter both email and password.');
      return;
    }
    setIsEmailSigningIn(true);
    try {
      const { user, error: signInError } = await signIn(email, password);
      if (user) {
        setAuthSuccess(true);
        // Show success animation briefly before closing
        setTimeout(() => {
          onOpenChange(false); // Close popup on success
        }, 1000);
      }
      if (signInError) {
        console.error('Sign-In Error:', signInError);
        // Provide more user-friendly error messages
        switch (signInError.code) {
          case 'auth/user-not-found':
          case 'auth/wrong-password':
          case 'auth/invalid-credential':
            setError('Invalid email or password. Please try again.');
            break;
          default:
            setError('An unexpected error occurred. Please try again.');
            break;
        }
      }
    } catch (err) {
      console.error('An unexpected error occurred during Sign-In:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsEmailSigningIn(false);
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEmailSigningIn && !isGoogleSigningIn) {
      handleSignIn();
    }
  };

  const handleGoogleSignIn = async () => {
    setError(null);
    setIsGoogleSigningIn(true);
    try {
      const { user, error: googleError } = await signInWithGoogle();
      if (user) {
        setAuthSuccess(true);
        // Show success animation briefly before closing
        setTimeout(() => {
          onOpenChange(false); // Close the popup on successful login
        }, 1000);
      }
      if (googleError) {
        // You can add more robust error handling here, like showing a toast notification
        console.error('Google Sign-In Error:', googleError);
        setError('An error occurred during Google Sign-In. Please try again.');
      }
    } catch (err) {
      console.error('An unexpected error occurred during Google Sign-In:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsGoogleSigningIn(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-800 text-white max-w-sm rounded-lg p-0">
        <AnimatePresence mode="wait">
          {authSuccess ? (
            <motion.div
              key="success"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3 }}
              className="p-8 text-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
                className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <CheckCircle className="h-8 w-8 text-white" />
              </motion.div>
              <motion.h3
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-xl font-bold text-green-400 mb-2"
              >
                Welcome to Syndicaps!
              </motion.h3>
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-gray-200"
              >
                Sign-in successful
              </motion.p>
            </motion.div>
          ) : (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <DialogHeader className="p-6 pb-0">
                <DialogTitle className="text-2xl font-bold text-center">
                  LOGIN
                </DialogTitle>
              </DialogHeader>
        <form onSubmit={handleFormSubmit}>
          <div className="p-6 pt-4 space-y-4">
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="bg-red-900/20 border border-red-500/30 rounded-lg p-3"
                  role="alert"
                  aria-live="polite"
                >
                  <p id="error-message" className="text-center text-sm text-red-400">{error}</p>
                </motion.div>
              )}
            </AnimatePresence>
            <div className="space-y-2">
              <div className="relative">
                <input
                  id="email"
                  type="email"
                  placeholder="Email Address"
                  className="input-gaming pr-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoComplete="email"
                  aria-describedby={error ? "error-message" : undefined}
                  required
                />
                {email && (
                  <button
                    type="button"
                    onClick={() => setEmail('')}
                    className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400 hover:text-white transition-colors"
                    aria-label="Clear email"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <div className="relative space-y-2">
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Password"
                  className="input-gaming pr-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  autoComplete="current-password"
                  aria-describedby={error ? "error-message" : undefined}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400 hover:text-white"
                    aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember-me"
                  className="border-gray-600 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                />
                <Label
                  htmlFor="remember-me"
                  className="text-sm font-medium text-gray-200"
                >
                  Remember me
                </Label>
              </div>
              <a href="#" className="text-sm text-blue-500 hover:underline">
                Forgot Password
              </a>
            </div>
            <button
              type="submit"
              className="btn-auth w-full touch-target"
              disabled={isEmailSigningIn || isGoogleSigningIn}
              aria-describedby={error ? "error-message" : undefined}
            >
              {isEmailSigningIn ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : null}
              {isEmailSigningIn ? 'Signing In...' : 'SIGN IN'}
            </button>
            <button
              type="button"
              className="btn-outline w-full touch-target"
              onClick={handleGoogleSignIn}
              disabled={isEmailSigningIn || isGoogleSigningIn}
              aria-label="Sign in with Google"
            >
              {isGoogleSigningIn ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : (
                <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24">
                  <path
                    fill="#4285F4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="#34A853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="#EA4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
              )}
              {isGoogleSigningIn ? 'Please wait...' : 'Continue with Google'}
            </button>
            <p className="text-center text-sm text-gray-400">
              Join us{' '}
              <a href="#" className="font-semibold text-white hover:underline">
                SIGN UP
              </a>
            </p>
            <p className="px-8 text-center text-xs text-gray-500">
              By signing in, you agree to the Syndicaps{' '}
              <a href="#" className="underline hover:text-gray-400">
                Terms of Use
              </a>{' '}
              and{' '}
              <a href="#" className="underline hover:text-gray-400">
                Privacy Policy
              </a>
              .
            </p>
          </div>
        </form>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}