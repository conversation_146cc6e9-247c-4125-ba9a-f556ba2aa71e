/**
 * Register Component
 * 
 * User registration form with validation and authentication.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import {
  Mail,
  Lock,
  User,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  Loader2,
  Shield,
  Sparkles,
  Infinity
} from 'lucide-react';
import { useAuth } from '@/lib/hooks/useAuth';
import { useRouter } from 'next/navigation';

// S-Infinity Logo Component
const SInfinityLogo = ({ className = "w-8 h-8" }: { className?: string }) => (
  <motion.div
    animate={{ rotate: 360 }}
    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
    className={className}
  >
    <Infinity className="text-accent-400" />
  </motion.div>
);

// Enhanced Loading Spinner
const TechLoadingSpinner = ({ size = "md" }: { size?: "sm" | "md" | "lg" }) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8"
  };

  return (
    <div className="relative">
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        className={`${sizeClasses[size]} border-2 border-accent-600 border-t-transparent rounded-full`}
      />
      <motion.div
        animate={{ rotate: -360 }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
        className={`absolute inset-1 border border-accent-400 border-b-transparent rounded-full`}
      />
    </div>
  );
};

export default function RegisterComponent() {
  const router = useRouter();
  const { signUp, signInWithGoogle } = useAuth();
  const [form, setForm] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<{
    name?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
  }>({});

  // Form validation
  const validateForm = () => {
    const errors: typeof formErrors = {};

    if (!form.name.trim()) {
      errors.name = 'Full name is required';
    } else if (form.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    }

    if (!form.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(form.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!form.password) {
      errors.password = 'Password is required';
    } else if (form.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (!form.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (form.password !== form.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);
    setFormErrors({});

    try {
      const { user, error } = await signUp(form.email, form.password);

      if (error) {
        setError(error);
        return;
      }

      if (user) {
        router.push('/profile/account');
      }
    } catch (err: any) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setGoogleLoading(true);
    setError(null);

    try {
      const { user, error } = await signInWithGoogle();

      if (error) {
        setError(error);
        return;
      }

      if (user) {
        router.push('/profile/account');
      }
    } catch (err: any) {
      setError(err);
    } finally {
      setGoogleLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Enhanced Background with Tech Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-accent-900/20 to-gray-900" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.1),transparent_70%)]" />

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
          className="absolute -top-40 -right-40 w-80 h-80 border border-accent-500/20 rounded-full"
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 35, repeat: Infinity, ease: "linear" }}
          className="absolute -bottom-40 -left-40 w-96 h-96 border border-accent-400/10 rounded-full"
        />
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative max-w-md w-full space-y-8 bg-gray-800/90 backdrop-blur-xl border border-gray-700 rounded-2xl p-8 shadow-2xl"
      >
        {/* Header with S-Infinity Logo */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-6">
            <SInfinityLogo className="w-12 h-12 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-white">Syndicaps</h1>
            </div>
          </div>

          <motion.h2
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-3xl font-extrabold text-white"
          >
            Join the Community
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-2 text-sm text-gray-400"
          >
            Create your Syndicaps account and start your journey
          </motion.p>
        </div>

        {/* Error Message */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              className="bg-red-900/20 border border-red-500/30 rounded-xl p-4 backdrop-blur-sm"
            >
              <div className="flex items-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                >
                  <AlertCircle className="h-5 w-5 text-red-400 mr-3" />
                </motion.div>
                <div>
                  <p className="text-red-400 text-sm font-medium">Registration Error</p>
                  <p className="text-red-300 text-sm mt-1">{error.message}</p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-6">
            {/* Name Field */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <label htmlFor="name" className="block text-sm font-medium text-gray-200 mb-3">
                Full Name
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                  <User className={`h-5 w-5 transition-colors duration-200 ${
                    formErrors.name ? 'text-red-400' : 'text-accent-400'
                  }`} />
                </div>
                <input
                  id="name"
                  name="name"
                  type="text"
                  required
                  value={form.name}
                  onChange={(e) => {
                    setForm({ ...form, name: e.target.value });
                    if (formErrors.name) {
                      setFormErrors({ ...formErrors, name: undefined });
                    }
                  }}
                  className={`input-gaming pl-14 pr-14 ${
                    formErrors.name ? 'error' : ''
                  }`}
                  placeholder="Enter your full name"
                  aria-describedby={formErrors.name ? "name-error" : undefined}
                  aria-invalid={formErrors.name ? "true" : "false"}
                />
                {formErrors.name && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center z-10"
                  >
                    <AlertCircle className="h-5 w-5 text-red-400" />
                  </motion.div>
                )}
              </div>
              {formErrors.name && (
                <motion.p
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  id="name-error"
                  className="mt-2 text-sm text-red-400 flex items-center"
                  role="alert"
                >
                  <AlertCircle className="h-4 w-4 mr-1 flex-shrink-0" />
                  {formErrors.name}
                </motion.p>
              )}
            </motion.div>

            {/* Email Field */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
            >
              <label htmlFor="email" className="block text-sm font-medium text-gray-200 mb-3">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                  <Mail className={`h-5 w-5 transition-colors duration-200 ${
                    formErrors.email ? 'text-red-400' : 'text-accent-400'
                  }`} />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={form.email}
                  onChange={(e) => {
                    setForm({ ...form, email: e.target.value });
                    if (formErrors.email) {
                      setFormErrors({ ...formErrors, email: undefined });
                    }
                  }}
                  className={`input-gaming pl-14 pr-14 ${
                    formErrors.email ? 'error' : ''
                  }`}
                  placeholder="Enter your email address"
                  aria-describedby={formErrors.email ? "email-error" : undefined}
                  aria-invalid={formErrors.email ? "true" : "false"}
                />
                {formErrors.email && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center z-10"
                  >
                    <AlertCircle className="h-5 w-5 text-red-400" />
                  </motion.div>
                )}
              </div>
              {formErrors.email && (
                <motion.p
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  id="email-error"
                  className="mt-2 text-sm text-red-400 flex items-center"
                  role="alert"
                >
                  <AlertCircle className="h-4 w-4 mr-1 flex-shrink-0" />
                  {formErrors.email}
                </motion.p>
              )}
            </motion.div>

            {/* Password Field */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
            >
              <label htmlFor="password" className="block text-sm font-medium text-gray-200 mb-3">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                  <Lock className={`h-5 w-5 transition-colors duration-200 ${
                    formErrors.password ? 'text-red-400' : 'text-accent-400'
                  }`} />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={form.password}
                  onChange={(e) => {
                    setForm({ ...form, password: e.target.value });
                    if (formErrors.password) {
                      setFormErrors({ ...formErrors, password: undefined });
                    }
                  }}
                  className={`input-gaming pl-14 pr-20 ${
                    formErrors.password ? 'error' : ''
                  }`}
                  placeholder="Create a secure password"
                  aria-describedby={formErrors.password ? "password-error" : undefined}
                  aria-invalid={formErrors.password ? "true" : "false"}
                />
                <div className="absolute inset-y-0 right-0 flex items-center z-10">
                  {formErrors.password && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="pr-2"
                    >
                      <AlertCircle className="h-5 w-5 text-red-400" />
                    </motion.div>
                  )}
                  <button
                    type="button"
                    className="pr-4 flex items-center justify-center touch-target-sm rounded-md hover:bg-accent-500/10 transition-colors duration-200"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                    tabIndex={0}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400 hover:text-accent-400 transition-colors duration-200" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400 hover:text-accent-400 transition-colors duration-200" />
                    )}
                  </button>
                </div>
              </div>
              {formErrors.password && (
                <motion.p
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  id="password-error"
                  className="mt-2 text-sm text-red-400 flex items-center"
                  role="alert"
                >
                  <AlertCircle className="h-4 w-4 mr-1 flex-shrink-0" />
                  {formErrors.password}
                </motion.p>
              )}
            </motion.div>

            {/* Confirm Password Field */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7 }}
            >
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-200 mb-3">
                Confirm Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                  <Lock className={`h-5 w-5 transition-colors duration-200 ${
                    formErrors.confirmPassword ? 'text-red-400' : 'text-accent-400'
                  }`} />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={form.confirmPassword}
                  onChange={(e) => {
                    setForm({ ...form, confirmPassword: e.target.value });
                    if (formErrors.confirmPassword) {
                      setFormErrors({ ...formErrors, confirmPassword: undefined });
                    }
                  }}
                  className={`input-gaming pl-14 pr-20 ${
                    formErrors.confirmPassword ? 'error' : ''
                  }`}
                  placeholder="Confirm your password"
                  aria-describedby={formErrors.confirmPassword ? "confirm-password-error" : undefined}
                  aria-invalid={formErrors.confirmPassword ? "true" : "false"}
                />
                <div className="absolute inset-y-0 right-0 flex items-center z-10">
                  {formErrors.confirmPassword && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="pr-2"
                    >
                      <AlertCircle className="h-5 w-5 text-red-400" />
                    </motion.div>
                  )}
                  <button
                    type="button"
                    className="pr-4 flex items-center justify-center touch-target-sm rounded-md hover:bg-accent-500/10 transition-colors duration-200"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    aria-label={showConfirmPassword ? "Hide confirm password" : "Show confirm password"}
                    tabIndex={0}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400 hover:text-accent-400 transition-colors duration-200" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400 hover:text-accent-400 transition-colors duration-200" />
                    )}
                  </button>
                </div>
              </div>
              {formErrors.confirmPassword && (
                <motion.p
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  id="confirm-password-error"
                  className="mt-2 text-sm text-red-400 flex items-center"
                  role="alert"
                >
                  <AlertCircle className="h-4 w-4 mr-1 flex-shrink-0" />
                  {formErrors.confirmPassword}
                </motion.p>
              )}
            </motion.div>
          </div>

          {/* Create Account Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <button
              type="submit"
              disabled={loading || Object.keys(formErrors).length > 0}
              className="btn-auth w-full relative overflow-hidden group touch-target"
              aria-label="Create your account"
            >
              <div className="flex items-center justify-center space-x-2">
                {loading ? (
                  <>
                    <TechLoadingSpinner size="sm" />
                    <span>Creating account...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-5 w-5" />
                    <span>Create Account</span>
                  </>
                )}
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
            </button>
          </motion.div>

          {/* Divider */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
            className="relative"
          >
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-accent-500/20" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gray-800/90 text-gray-400 backdrop-blur-sm">
                Or continue with
              </span>
            </div>
          </motion.div>

          {/* Google OAuth Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
          >
            <button
              type="button"
              onClick={handleGoogleSignUp}
              disabled={googleLoading}
              className="btn-outline w-full flex items-center justify-center space-x-3 group relative overflow-hidden touch-target"
              aria-label="Sign up with Google"
            >
              <div className="flex items-center space-x-3">
                {googleLoading ? (
                  <TechLoadingSpinner size="sm" />
                ) : (
                  <svg className="w-5 h-5 transition-transform group-hover:scale-110" viewBox="0 0 24 24">
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                )}
                <span className="font-medium">
                  {googleLoading ? 'Signing up...' : 'Continue with Google'}
                </span>
              </div>

              {/* Hover effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </button>
          </motion.div>

          {/* Sign In Link */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.1 }}
            className="text-center"
          >
            <p className="text-sm text-gray-400">
              Already have an account?{' '}
              <Link
                href="/auth"
                className="font-medium text-accent-400 hover:text-accent-300 transition-colors relative group"
              >
                <span className="relative z-10">Sign in here</span>
                <span className="absolute inset-x-0 bottom-0 h-0.5 bg-accent-400 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
              </Link>
            </p>
          </motion.div>
        </form>
      </motion.div>
    </div>
  );
}
