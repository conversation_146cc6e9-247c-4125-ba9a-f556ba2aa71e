/**
 * Community Error Boundary Component
 * 
 * Specialized error boundary for community-specific errors
 * with fallback UI and retry mechanisms.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { Component, ReactNode } from 'react'
import { AlertTriangle, RefreshCw, Home, MessageCircle } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorId: string
  retryCount: number
}

class CommunityErrorBoundary extends Component<Props, State> {
  private maxRetries = 3

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Community Error Boundary caught an error:', error, errorInfo)
    
    // Log to error reporting service
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Log to console with additional context
    console.group('🚨 Community Error Details')
    console.error('Error:', error.message)
    console.error('Stack:', error.stack)
    console.error('Component Stack:', errorInfo.componentStack)
    console.error('Error ID:', this.state.errorId)
    console.groupEnd()
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        retryCount: prevState.retryCount + 1
      }))
    } else {
      // Max retries reached, could redirect or show different UI
      console.warn('Max retries reached for community error')
    }
  }

  handleReloadPage = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  handleReportIssue = () => {
    const errorDetails = {
      error: this.state.error?.message || 'Unknown error',
      stack: this.state.error?.stack || 'No stack trace',
      errorId: this.state.errorId,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    }

    // Copy error details to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('Error details copied to clipboard. Please share this with our support team.')
      })
      .catch(() => {
        // Fallback: open email client
        const subject = encodeURIComponent('Community Error Report')
        const body = encodeURIComponent(`Error Details:\n${JSON.stringify(errorDetails, null, 2)}`)
        window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)
      })
  }

  getErrorType = (error: Error | null): string => {
    if (!error) return 'unknown'
    
    const message = error.message.toLowerCase()
    
    if (message.includes('firebase') || message.includes('firestore')) {
      return 'firebase'
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'network'
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
      return 'auth'
    }
    if (message.includes('permission') || message.includes('forbidden')) {
      return 'permission'
    }
    
    return 'application'
  }

  getErrorMessage = (errorType: string): { title: string; description: string } => {
    switch (errorType) {
      case 'firebase':
        return {
          title: 'Database Connection Issue',
          description: 'We\'re having trouble connecting to our servers. This is usually temporary.'
        }
      case 'network':
        return {
          title: 'Network Connection Issue',
          description: 'Please check your internet connection and try again.'
        }
      case 'auth':
        return {
          title: 'Authentication Required',
          description: 'You need to log in to access this community feature.'
        }
      case 'permission':
        return {
          title: 'Access Denied',
          description: 'You don\'t have permission to access this community feature.'
        }
      default:
        return {
          title: 'Something Went Wrong',
          description: 'An unexpected error occurred while loading the community content.'
        }
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      const errorType = this.getErrorType(this.state.error)
      const { title, description } = this.getErrorMessage(errorType)
      const canRetry = this.state.retryCount < this.maxRetries

      return (
        <div className="min-h-[400px] flex items-center justify-center p-6">
          <div className="max-w-md w-full text-center">
            <div className="mb-6">
              <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
              <p className="text-gray-400 mb-4">{description}</p>
              
              {/* Error ID for support */}
              <div className="bg-gray-800 rounded-lg p-3 mb-6">
                <p className="text-xs text-gray-500 mb-1">Error ID</p>
                <code className="text-xs text-gray-300 font-mono">{this.state.errorId}</code>
              </div>

              {/* Retry count indicator */}
              {this.state.retryCount > 0 && (
                <p className="text-sm text-yellow-400 mb-4">
                  Retry attempt {this.state.retryCount} of {this.maxRetries}
                </p>
              )}
            </div>

            <div className="space-y-3">
              {/* Primary action - Retry */}
              {canRetry && (
                <button
                  onClick={this.handleRetry}
                  className="w-full bg-accent-600 hover:bg-accent-700 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Try Again</span>
                </button>
              )}

              {/* Secondary actions */}
              <div className="flex space-x-3">
                <button
                  onClick={this.handleReloadPage}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Reload</span>
                </button>
                
                <button
                  onClick={this.handleGoHome}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <Home className="w-4 h-4" />
                  <span>Home</span>
                </button>
              </div>

              {/* Report issue */}
              <button
                onClick={this.handleReportIssue}
                className="w-full bg-transparent border border-gray-600 text-gray-300 py-2 px-4 rounded-lg font-medium transition-colors hover:bg-gray-800 flex items-center justify-center space-x-2"
              >
                <MessageCircle className="w-4 h-4" />
                <span>Report Issue</span>
              </button>
            </div>

            {/* Additional help text */}
            <div className="mt-6 text-xs text-gray-500">
              <p>If this problem persists, please contact our support team.</p>
              <p className="mt-1">
                You can also try refreshing the page or checking your internet connection.
              </p>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default CommunityErrorBoundary