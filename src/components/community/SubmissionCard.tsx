/**
 * Submission Card Component
 * 
 * Displays user submissions with category, author info,
 * engagement metrics, and interaction buttons.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Heart,
  Eye,
  Share2,
  MessageSquare,
  Bookmark,
  MoreHorizontal,
  ExternalLink,
  Download,
  Flag
} from 'lucide-react'
import { motion } from 'framer-motion'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { useImageLazyLoad } from '@/hooks/useVirtualization'

interface SubmissionCardProps {
  category: string
  title: string
  author: string
  authorAvatar?: string
  image: string
  likes: number
  views: number
  comments?: number
  submittedAt: Date
  isLiked?: boolean
  isBookmarked?: boolean
  className?: string
}

export default function SubmissionCard({
  category,
  title,
  author,
  authorAvatar,
  image,
  likes,
  views,
  comments = 0,
  submittedAt,
  isLiked = false,
  isBookmarked = false,
  className = ''
}: SubmissionCardProps) {
  
  const [liked, setLiked] = useState(isLiked)
  const [bookmarked, setBookmarked] = useState(isBookmarked)
  const [likeCount, setLikeCount] = useState(likes)

  // Lazy load image
  const { imageRef, isLoaded, isError, shouldLoad } = useImageLazyLoad(image, {
    threshold: 0.1,
    rootMargin: '50px'
  })

  const handleLike = () => {
    setLiked(!liked)
    setLikeCount(prev => liked ? prev - 1 : prev + 1)
  }

  const handleBookmark = () => {
    setBookmarked(!bookmarked)
  }

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'cyberpunk':
        return 'text-cyan-400 bg-cyan-400/20'
      case 'nature':
        return 'text-green-400 bg-green-400/20'
      case 'monster':
        return 'text-red-400 bg-red-400/20'
      case 'other':
        return 'text-gray-400 bg-gray-400/20'
      default:
        return 'text-purple-400 bg-purple-400/20'
    }
  }

  const getCategoryEmoji = (category: string) => {
    switch (category.toLowerCase()) {
      case 'cyberpunk':
        return '🤖'
      case 'nature':
        return '🌿'
      case 'monster':
        return '👹'
      case 'other':
        return '✨'
      default:
        return '🎨'
    }
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={className}
    >
      <Card className="bg-gray-900/50 border-gray-700 hover:border-gray-600 transition-all duration-300 overflow-hidden">
        {/* Image */}
        <div className="relative aspect-square bg-gray-800 overflow-hidden group" ref={imageRef}>
          {/* Loading placeholder */}
          {!shouldLoad && (
            <div className="w-full h-full bg-gray-800 animate-pulse flex items-center justify-center">
              <div className="w-12 h-12 border-2 border-gray-600 border-t-accent-400 rounded-full animate-spin"></div>
            </div>
          )}
          
          {/* Error state */}
          {isError && (
            <div className="w-full h-full bg-gray-800 flex items-center justify-center">
              <div className="text-gray-500 text-center">
                <Flag className="w-8 h-8 mx-auto mb-2" />
                <p className="text-sm">Image failed to load</p>
              </div>
            </div>
          )}
          
          {/* Actual image */}
          {shouldLoad && !isError && (
            <img 
              src={image} 
              alt={title}
              className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
                isLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={() => {}} // Image loading handled by hook
              onError={() => {}} // Error handling handled by hook
            />
          )}
          
          {/* Loading overlay for image transition */}
          {shouldLoad && !isLoaded && !isError && (
            <div className="absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-gray-600 border-t-accent-400 rounded-full animate-spin"></div>
            </div>
          )}
          
          {/* Overlay on hover - only show when image is loaded */}
          {isLoaded && (
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
          )}
          
          {/* Category Badge - only show when image is loaded */}
          {isLoaded && (
            <div className="absolute top-3 left-3">
              <Badge className={getCategoryColor(category)}>
                <span className="mr-1">{getCategoryEmoji(category)}</span>
                {category}
              </Badge>
            </div>
          )}

          {/* Quick Actions - only show when image is loaded */}
          {isLoaded && (
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="flex items-center space-x-2">
                <Button
                  size="default"
                  variant="ghost"
                  className="bg-black/50 hover:bg-black/70 text-white p-2 min-h-[44px] min-w-[44px]"
                  onClick={handleBookmark}
                >
                  <Bookmark className={`w-4 h-4 ${bookmarked ? 'fill-current text-yellow-400' : ''}`} />
                </Button>
                <Button
                  size="default"
                  variant="ghost"
                  className="bg-black/50 hover:bg-black/70 text-white p-2 min-h-[44px] min-w-[44px]"
                >
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}

          {/* View Count Overlay - only show when image is loaded */}
          {isLoaded && (
            <div className="absolute bottom-3 right-3 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
              <div className="flex items-center space-x-1 text-white text-xs">
                <Eye className="w-3 h-3" />
                <span>{views.toLocaleString()}</span>
              </div>
            </div>
          )}
        </div>

        <CardContent className="p-4 space-y-3">
          {/* Title */}
          <h3 className="font-semibold text-white line-clamp-2 leading-tight">
            {title}
          </h3>

          {/* Author */}
          <div className="flex items-center space-x-2">
            <Avatar className="w-6 h-6">
              <AvatarImage src={authorAvatar} alt={author} />
              <AvatarFallback className="bg-gray-700 text-white text-xs">
                {author.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-gray-400">{author}</span>
          </div>

          {/* Engagement Stats */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleLike}
                className={`flex items-center space-x-1 text-sm transition-colors min-h-[44px] min-w-[44px] justify-center ${
                  liked ? 'text-red-400' : 'text-gray-400 hover:text-red-400'
                }`}
              >
                <Heart className={`w-4 h-4 ${liked ? 'fill-current' : ''}`} />
                <span>{likeCount.toLocaleString()}</span>
              </button>
              
              {comments > 0 && (
                <div className="flex items-center space-x-1 text-sm text-gray-400">
                  <MessageSquare className="w-4 h-4" />
                  <span>{comments}</span>
                </div>
              )}
            </div>

            <div className="text-xs text-gray-500">
              {formatDistanceToNow(submittedAt, { addSuffix: true, locale: id })}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2 pt-2">
            <Button
              size="default"
              className="flex-1 bg-accent-600 hover:bg-accent-700 text-white"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              View Details
            </Button>
            
            <Button
              size="default"
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              <Download className="w-4 h-4" />
            </Button>
            
            <Button
              size="default"
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
