'use client'

/**
 * Submission Card Component
 * 
 * Displays submission information in a card format with interaction
 * buttons, user information, and media preview.
 * 
 * Features:
 * - Multiple display variants (grid, list, masonry)
 * - Image/video preview with lazy loading
 * - Interaction buttons (like, save, share, report)
 * - User profile integration
 * - Challenge association display
 * - Mobile touch optimization
 * - Accessibility compliance
 * 
 * <AUTHOR> Team
 */

import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import {
  Heart,
  Bookmark,
  Share2,
  Eye,
  MessageCircle,
  Flag,
  User,
  Trophy,
  Star,
  Play,
  MoreHorizontal,
  ExternalLink
} from 'lucide-react'
import { Submission } from '../../../lib/api/gamification'
import { useSubmissionInteractions } from '../../../hooks/useSubmissions'
import { useUser } from '../../../lib/useUser'
import { useRealTimeSubmissions, useRealTimeListener } from '../../../hooks/useRealTimeCommunity'

interface SubmissionCardProps {
  submission: Submission
  variant?: 'grid' | 'list' | 'masonry'
  showChallenge?: boolean
  showUser?: boolean
  onUserClick?: (userId: string) => void
  onChallengeClick?: (challengeId: string) => void
  className?: string
}

/**
 * Format number for display (1.2k, 1.2M, etc.)
 */
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

/**
 * Get status color based on submission status
 */
const getStatusColor = (status: Submission['status']) => {
  switch (status) {
    case 'approved':
      return 'bg-green-500'
    case 'featured':
      return 'bg-yellow-500'
    case 'submitted':
      return 'bg-blue-500'
    case 'draft':
      return 'bg-gray-500'
    case 'rejected':
      return 'bg-red-500'
    default:
      return 'bg-gray-400'
  }
}

/**
 * Submission Card Component
 */
const SubmissionCard: React.FC<SubmissionCardProps> = ({
  submission,
  variant = 'grid',
  showChallenge = true,
  showUser = true,
  onUserClick,
  onChallengeClick,
  className = ''
}) => {
  const { user } = useUser()
  const { like, save, share, report, liking, saving, reporting } = useSubmissionInteractions(submission.id || '')
  const { sendInteractionUpdate } = useRealTimeSubmissions()
  const [imageError, setImageError] = useState(false)
  const [showReportModal, setShowReportModal] = useState(false)
  const [liveInteractions, setLiveInteractions] = useState(submission.interactions)

  // Check if user has interacted with this submission
  const isLiked = submission.userInteractions?.likedBy?.includes(user?.uid || '') || false
  const isSaved = submission.userInteractions?.savedBy?.includes(user?.uid || '') || false
  const isOwner = submission.userId === user?.uid

  // Listen for real-time interaction updates
  useRealTimeListener('submission_interaction', (data: any) => {
    if (data.submissionId === submission.id) {
      setLiveInteractions(prev => ({
        ...prev,
        likes: data.action === 'like' ? (data.isActive ? (prev?.likes || 0) + 1 : Math.max((prev?.likes || 0) - 1, 0)) : prev?.likes || 0,
        saves: data.action === 'save' ? (data.isActive ? (prev?.saves || 0) + 1 : Math.max((prev?.saves || 0) - 1, 0)) : prev?.saves || 0,
        shares: data.action === 'share' ? (prev?.shares || 0) + 1 : prev?.shares || 0
      }))
    }
  }, [submission.id])

  const handleLike = useCallback(async () => {
    if (!user || !submission.id) return
    const wasLiked = await like()
    sendInteractionUpdate(submission.id, 'like', wasLiked)
  }, [like, user, submission.id, sendInteractionUpdate])

  const handleSave = useCallback(async () => {
    if (!user || !submission.id) return
    const wasSaved = await save()
    sendInteractionUpdate(submission.id, 'save', wasSaved)
  }, [save, user, submission.id, sendInteractionUpdate])

  const handleShare = useCallback(async () => {
    if (!submission.id) return
    await share()
    sendInteractionUpdate(submission.id, 'share', true)
  }, [share, submission.id, sendInteractionUpdate])

  const handleReport = useCallback(async (reason: string) => {
    if (!user) return
    const success = await report(reason)
    if (success) {
      setShowReportModal(false)
    }
  }, [report, user])

  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    hover: { y: -5, transition: { duration: 0.2 } }
  }

  const baseClasses = `
    bg-gray-800 rounded-lg border border-gray-700 overflow-hidden
    hover:border-accent-500 transition-all duration-300
    ${className}
  `

  const gridLayout = variant === 'grid'
  const listLayout = variant === 'list'
  const masonryLayout = variant === 'masonry'

  // Get primary image
  const primaryImage = submission.content?.images?.[0]

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      className={baseClasses}
    >
      {/* Media Preview */}
      <div className={`relative ${gridLayout ? 'h-48' : listLayout ? 'h-32' : 'h-auto'}`}>
        {primaryImage && !imageError ? (
          <Link href={`/community/submissions/${submission.id}`}>
            <div className="relative w-full h-full cursor-pointer group">
              <Image
                src={primaryImage.url}
                alt={submission.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                onError={() => setImageError(true)}
              />
              
              {/* Overlay on hover */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                <Eye className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </div>
          </Link>
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
            <Image className="w-12 h-12 text-gray-500" />
          </div>
        )}
        
        {/* Status Badge */}
        <div className="absolute top-3 left-3">
          <span className={`
            px-2 py-1 rounded-full text-xs font-medium text-white
            ${getStatusColor(submission.status)}
          `}>
            {submission.status === 'approved' ? 'Published' : 
             submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
          </span>
        </div>

        {/* Featured Badge */}
        {submission.status === 'featured' && (
          <div className="absolute top-3 right-3">
            <Star className="w-6 h-6 text-yellow-400" />
          </div>
        )}

        {/* Media Count */}
        {submission.content?.images && submission.content.images.length > 1 && (
          <div className="absolute bottom-3 right-3 bg-black/70 text-white px-2 py-1 rounded text-xs">
            +{submission.content.images.length - 1} more
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Title and Description */}
        <div className="mb-3">
          <Link href={`/community/submissions/${submission.id}`}>
            <h3 className="text-lg font-bold text-white mb-1 line-clamp-2 hover:text-accent-400 transition-colors cursor-pointer">
              {submission.title}
            </h3>
          </Link>
          <p className="text-gray-400 text-sm line-clamp-2">
            {submission.description}
          </p>
        </div>

        {/* Challenge Association */}
        {showChallenge && submission.challengeId && (
          <div className="mb-3">
            <button
              onClick={() => onChallengeClick?.(submission.challengeId!)}
              className="inline-flex items-center space-x-2 text-accent-400 hover:text-accent-300 transition-colors text-sm"
            >
              <Trophy className="w-4 h-4" />
              <span>Challenge Entry</span>
            </button>
          </div>
        )}

        {/* User Info */}
        {showUser && (
          <div className="flex items-center space-x-3 mb-3">
            <button
              onClick={() => onUserClick?.(submission.userId)}
              className="flex items-center space-x-2 hover:text-accent-400 transition-colors"
            >
              <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-gray-400" />
              </div>
              <span className="text-sm text-gray-300">
                {submission.userId.slice(0, 8)}...
              </span>
            </button>
          </div>
        )}

        {/* Interaction Stats */}
        <div className="flex items-center justify-between text-sm text-gray-400 mb-3">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Heart className="w-4 h-4" />
              <span>{formatNumber(liveInteractions?.likes || 0)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Eye className="w-4 h-4" />
              <span>{formatNumber(liveInteractions?.views || 0)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Bookmark className="w-4 h-4" />
              <span>{formatNumber(liveInteractions?.saves || 0)}</span>
            </div>
          </div>
          
          {/* Type Badge */}
          <span className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs">
            {submission.type.charAt(0).toUpperCase() + submission.type.slice(1)}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Like Button */}
            <button
              onClick={handleLike}
              disabled={!user || liking}
              className={`
                p-2 rounded-lg transition-all duration-200 flex items-center space-x-1
                ${isLiked 
                  ? 'bg-red-600 text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }
                ${!user ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
              {liking && <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />}
            </button>

            {/* Save Button */}
            <button
              onClick={handleSave}
              disabled={!user || saving}
              className={`
                p-2 rounded-lg transition-all duration-200 flex items-center space-x-1
                ${isSaved 
                  ? 'bg-accent-600 text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }
                ${!user ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <Bookmark className={`w-4 h-4 ${isSaved ? 'fill-current' : ''}`} />
              {saving && <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />}
            </button>

            {/* Share Button */}
            <button
              onClick={handleShare}
              className="p-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <Share2 className="w-4 h-4" />
            </button>
          </div>

          {/* More Actions */}
          <div className="flex items-center space-x-2">
            {!isOwner && user && (
              <button
                onClick={() => setShowReportModal(true)}
                className="p-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors"
              >
                <Flag className="w-4 h-4" />
              </button>
            )}
            
            <Link
              href={`/community/submissions/${submission.id}`}
              className="p-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Tags */}
        {submission.tags && submission.tags.length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-700">
            <div className="flex flex-wrap gap-1">
              {submission.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs"
                >
                  #{tag}
                </span>
              ))}
              {submission.tags.length > 3 && (
                <span className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs">
                  +{submission.tags.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Report Modal */}
      {showReportModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-white mb-4">Report Submission</h3>
            <p className="text-gray-400 mb-4">
              Why are you reporting this submission?
            </p>
            <div className="space-y-2 mb-6">
              {[
                'Inappropriate content',
                'Spam or misleading',
                'Copyright violation',
                'Harassment or abuse',
                'Other'
              ].map((reason) => (
                <button
                  key={reason}
                  onClick={() => handleReport(reason)}
                  disabled={reporting}
                  className="w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-white"
                >
                  {reason}
                </button>
              ))}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowReportModal(false)}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  )
}

export default SubmissionCard
