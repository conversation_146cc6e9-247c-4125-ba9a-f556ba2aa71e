/**
 * Submission Upload Component
 * 
 * Complete form for uploading community submissions including
 * images, metadata, and validation.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Plus, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  Tag,
  FileText,
  Camera,
  Trash2
} from 'lucide-react'
import { useUser } from '@/lib/useUser'
import { submissionUploadService } from '@/lib/firebase/community'

interface UploadedImage {
  file: File
  preview: string
  id: string
}

const SubmissionUpload: React.FC = () => {
  const router = useRouter()
  const { user } = useUser()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: [] as string[],
    challengeId: ''
  })

  const [images, setImages] = useState<UploadedImage[]>([])
  const [currentTag, setCurrentTag] = useState('')
  const [uploading, setUploading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [success, setSuccess] = useState(false)

  // Get available categories
  const categories = submissionUploadService.getSubmissionCategories()

  // Handle form field changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setErrors([]) // Clear errors when user types
  }

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return

    const newImages: UploadedImage[] = []
    const maxFiles = 10 - images.length

    Array.from(files).slice(0, maxFiles).forEach((file) => {
      if (file.type.startsWith('image/')) {
        const preview = URL.createObjectURL(file)
        newImages.push({
          file,
          preview,
          id: Math.random().toString(36).substr(2, 9)
        })
      }
    })

    setImages(prev => [...prev, ...newImages])
  }, [images.length])

  // Handle drag and drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    handleFileSelect(e.dataTransfer.files)
  }, [handleFileSelect])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
  }, [])

  // Remove image
  const removeImage = (id: string) => {
    setImages(prev => {
      const filtered = prev.filter(img => img.id !== id)
      // Revoke URL to prevent memory leaks
      const toRemove = prev.find(img => img.id === id)
      if (toRemove) {
        URL.revokeObjectURL(toRemove.preview)
      }
      return filtered
    })
  }

  // Add tag
  const addTag = () => {
    const tag = currentTag.trim().toLowerCase()
    if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
      setCurrentTag('')
    }
  }

  // Remove tag
  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  // Handle tag input key press
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      addTag()
    }
  }

  // Validate and submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      setErrors(['You must be logged in to upload submissions'])
      return
    }

    // Validate form data
    const validationErrors = submissionUploadService.validateSubmissionData({
      title: formData.title,
      description: formData.description,
      category: formData.category,
      tags: formData.tags,
      images: images.map(img => img.file)
    })

    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }

    setUploading(true)
    setErrors([])

    try {
      const submissionId = await submissionUploadService.createSubmission({
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags,
        images: images.map(img => img.file),
        challengeId: formData.challengeId || undefined,
        authorId: user.uid,
        author: {
          id: user.uid,
          name: user.displayName || user.email || 'Anonymous',
          avatar: user.photoURL || undefined,
          level: 1 // Would be fetched from user profile in real implementation
        }
      })

      setSuccess(true)
      
      // Redirect after a delay
      setTimeout(() => {
        router.push(`/community/submissions/${submissionId}`)
      }, 2000)

    } catch (error) {
      console.error('Error uploading submission:', error)
      setErrors([error instanceof Error ? error.message : 'Failed to upload submission'])
    } finally {
      setUploading(false)
    }
  }

  // Cleanup URLs on unmount
  React.useEffect(() => {
    return () => {
      images.forEach(img => URL.revokeObjectURL(img.preview))
    }
  }, [])

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-950 pt-24 pb-20">
        <div className="container mx-auto px-4 max-w-2xl">
          <div className="text-center py-12">
            <Upload className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-4">Login Required</h1>
            <p className="text-gray-400 mb-6">
              You need to be logged in to upload submissions to the community.
            </p>
            <button
              onClick={() => router.push('/auth')}
              className="bg-accent-600 hover:bg-accent-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Login / Register
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-950 pt-24 pb-20">
        <div className="container mx-auto px-4 max-w-2xl">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-4">Submission Uploaded!</h1>
            <p className="text-gray-400 mb-6">
              Your submission has been uploaded successfully and is pending review.
              You'll be redirected to your submission page shortly.
            </p>
            <div className="flex items-center justify-center">
              <Loader2 className="w-5 h-5 text-accent-400 animate-spin" />
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-950 pt-24 pb-20">
      <div className="container mx-auto px-4 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
            <Upload className="w-8 h-8 mr-3 text-accent-400" />
            Upload Submission
          </h1>
          <p className="text-gray-400">
            Share your creative work with the community
          </p>
        </motion.div>

        {/* Error Messages */}
        <AnimatePresence>
          {errors.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mb-6 p-4 bg-red-900/20 border border-red-700 rounded-lg"
            >
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="text-red-400 font-medium mb-1">Please fix the following errors:</h3>
                  <ul className="text-red-300 text-sm space-y-1">
                    {errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Image Upload */}
          <div className="bg-gray-900/50 rounded-lg p-6">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Camera className="w-5 h-5 mr-2 text-accent-400" />
              Images ({images.length}/10)
            </h2>

            {/* Upload Area */}
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => fileInputRef.current?.click()}
              className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-accent-400 transition-colors"
            >
              <ImageIcon className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-300 mb-2">Click to select images or drag and drop</p>
              <p className="text-sm text-gray-500">PNG, JPG, GIF up to 15MB each (max 10 images)</p>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
            />

            {/* Image Previews */}
            {images.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                {images.map((image) => (
                  <div key={image.id} className="relative group">
                    <img
                      src={image.preview}
                      alt="Preview"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(image.id)}
                      className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Form Fields */}
          <div className="bg-gray-900/50 rounded-lg p-6 space-y-6">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-accent-400" />
              Details
            </h2>

            {/* Title */}
            <div>
              <label className="block text-white font-medium mb-2">
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Give your submission a catchy title"
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-400 focus:ring-1 focus:ring-accent-400"
                maxLength={100}
              />
              <p className="text-xs text-gray-500 mt-1">{formData.title.length}/100 characters</p>
            </div>

            {/* Description */}
            <div>
              <label className="block text-white font-medium mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your submission, process, inspiration, etc."
                rows={4}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-400 focus:ring-1 focus:ring-accent-400 resize-none"
                maxLength={2000}
              />
              <p className="text-xs text-gray-500 mt-1">{formData.description.length}/2000 characters</p>
            </div>

            {/* Category */}
            <div>
              <label className="block text-white font-medium mb-2">
                Category *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-400 focus:ring-1 focus:ring-accent-400"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-white font-medium mb-2 flex items-center">
                <Tag className="w-4 h-4 mr-2" />
                Tags * ({formData.tags.length}/10)
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags.map((tag) => (
                  <span
                    key={tag}
                    className="bg-accent-600/20 text-accent-400 px-3 py-1 rounded-full text-sm flex items-center space-x-2"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="hover:text-accent-300"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={currentTag}
                  onChange={(e) => setCurrentTag(e.target.value)}
                  onKeyPress={handleTagKeyPress}
                  placeholder="Add tags (press Enter or comma to add)"
                  className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-400 focus:ring-1 focus:ring-accent-400"
                />
                <button
                  type="button"
                  onClick={addTag}
                  disabled={!currentTag.trim() || formData.tags.length >= 10}
                  className="bg-accent-600 hover:bg-accent-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-1">Use tags to help others find your submission</p>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploading}
              className="bg-accent-600 hover:bg-accent-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white px-8 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              {uploading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <>
                  <Upload className="w-5 h-5" />
                  <span>Upload Submission</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default SubmissionUpload