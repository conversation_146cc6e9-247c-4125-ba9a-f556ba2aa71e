/**
 * Activity Tab Component
 * 
 * Enhanced activity tab with the existing ActivityFeed component
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import ActivityFeed from '@/components/community/ActivityFeed'

const ActivityTab: React.FC = () => {

  const containerVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  }

  const sectionVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="activity-tab space-y-8"
    >
      {/* Activity Content */}
      <motion.section
        variants={sectionVariants}
        transition={{ delay: 0.05 }}
      >
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-2">Community Activity</h2>
            <p className="text-gray-400">
              Stay updated with real-time community activity and interactions
            </p>
          </div>
          
          <ActivityFeed 
            limit={20}
            sort="recent"
            showRefresh={true}
            className="max-w-4xl mx-auto"
          />
        </div>
      </motion.section>
    </motion.div>
  )
}

export default ActivityTab