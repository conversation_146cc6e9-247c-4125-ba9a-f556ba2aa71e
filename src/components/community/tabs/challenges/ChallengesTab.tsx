/**
 * Challenges Tab Component
 * 
 * Enhanced challenges tab with active challenges, progress tracking,
 * achievement showcase, and filtering capabilities.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useUser } from '@/lib/useUser'
import ChallengeGrid from './ChallengeGrid'
import UserProgressDashboard from './UserProgressDashboard'
import AchievementShowcase from './AchievementShowcase'
import ChallengeFilters from './ChallengeFilters'
import { ChallengeFilters as ChallengeFiltersType } from '@/types/community-tabs'

const ChallengesTab: React.FC = () => {
  const { user } = useUser()
  const [filters, setFilters] = useState<ChallengeFiltersType>({
    difficulty: 'all',
    category: 'all',
    status: 'active',
    timeRemaining: 'all',
    sortBy: 'newest'
  })

  const containerVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  }

  const sectionVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="challenges-tab space-y-8"
    >
      {/* User Progress Dashboard - Only show for authenticated users */}
      {user && (
        <motion.section
          variants={sectionVariants}
          transition={{ delay: 0.1 }}
        >
          <h2 className="text-2xl font-bold text-white mb-6">My Challenge Progress</h2>
          <UserProgressDashboard userId={user.uid} />
        </motion.section>
      )}

      {/* Active Challenges */}
      <motion.section
        variants={sectionVariants}
        transition={{ delay: 0.2 }}
      >
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 space-y-4 lg:space-y-0">
          <h2 className="text-2xl font-bold text-white">Active Challenges</h2>
          <ChallengeFilters filters={filters} onChange={setFilters} />
        </div>
        <ChallengeGrid filters={filters} />
      </motion.section>

      {/* Achievement Showcase */}
      <motion.section
        variants={sectionVariants}
        transition={{ delay: 0.3 }}
      >
        <h2 className="text-2xl font-bold text-white mb-6">Challenge Achievements</h2>
        <AchievementShowcase />
      </motion.section>
    </motion.div>
  )
}

export default ChallengesTab