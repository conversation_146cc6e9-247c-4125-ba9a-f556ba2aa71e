/**
 * User Progress Dashboard Component
 * 
 * Displays personal challenge progress, active challenges,
 * and upcoming deadlines for authenticated users.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { UserChallengeProgress, Challenge } from '@/types/community-tabs'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Clock, 
  Trophy, 
  Target, 
  Calendar, 
  CheckCircle, 
  AlertCircle,
  Upload,
  TrendingUp
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface UserProgressDashboardProps {
  userId: string
}

const UserProgressDashboard: React.FC<UserProgressDashboardProps> = ({ userId }) => {
  const [userProgress, setUserProgress] = useState<UserChallengeProgress[]>([])
  const [challenges, setChallenges] = useState<Challenge[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalActive: 0,
    totalCompleted: 0,
    averageProgress: 0,
    nearingDeadline: 0
  })

  useEffect(() => {
    const loadUserProgress = async () => {
      try {
        // Mock data - replace with actual API call
        const mockProgress: UserChallengeProgress[] = [
          {
            challengeId: '1',
            userId,
            status: 'in-progress',
            progress: 75,
            joinedAt: new Date('2024-01-10'),
            lastActivity: new Date('2024-01-20'),
            submission: {
              id: 'sub1',
              content: 'Working on final touches...',
              submittedAt: new Date('2024-01-20'),
              status: 'pending'
            }
          },
          {
            challengeId: '2',
            userId,
            status: 'completed',
            progress: 100,
            joinedAt: new Date('2024-01-05'),
            lastActivity: new Date('2024-01-15'),
            submission: {
              id: 'sub2',
              content: 'Submitted winter-themed keycap design',
              submittedAt: new Date('2024-01-15'),
              status: 'approved'
            }
          },
          {
            challengeId: '3',
            userId,
            status: 'joined',
            progress: 25,
            joinedAt: new Date('2024-01-18'),
            lastActivity: new Date('2024-01-19')
          }
        ]

        const mockChallenges: Challenge[] = [
          {
            id: '1',
            title: 'Cyberpunk Keycap Design',
            description: 'Create a cyberpunk-themed artisan keycap',
            difficulty: 'medium',
            category: 'design',
            reward: '$300 + Featured',
            participants: 45,
            startDate: new Date('2024-01-01'),
            endDate: new Date('2024-01-25'),
            status: 'active',
            requirements: ['Original design', 'High resolution render'],
            tags: ['design', 'cyberpunk', 'artisan'],
            createdBy: { id: '1', name: 'Admin' }
          },
          {
            id: '2',
            title: 'Winter Keycap Collection',
            description: 'Design a winter-themed keycap collection',
            difficulty: 'hard',
            category: 'design',
            reward: '$500 + Featured',
            participants: 32,
            startDate: new Date('2024-01-01'),
            endDate: new Date('2024-01-15'),
            status: 'completed',
            requirements: ['Collection of 5+ keycaps', 'Consistent theme'],
            tags: ['design', 'winter', 'collection'],
            createdBy: { id: '1', name: 'Admin' }
          },
          {
            id: '3',
            title: 'Minimalist Layout Challenge',
            description: 'Design the most efficient minimalist layout',
            difficulty: 'easy',
            category: 'design',
            reward: '$200 + Recognition',
            participants: 67,
            startDate: new Date('2024-01-15'),
            endDate: new Date('2024-02-01'),
            status: 'active',
            requirements: ['60% or smaller', 'Functional layout'],
            tags: ['design', 'minimalist', 'layout'],
            createdBy: { id: '1', name: 'Admin' }
          }
        ]

        setUserProgress(mockProgress)
        setChallenges(mockChallenges)
        
        // Calculate stats
        const active = mockProgress.filter(p => p.status === 'in-progress' || p.status === 'joined').length
        const completed = mockProgress.filter(p => p.status === 'completed').length
        const avgProgress = mockProgress.reduce((sum, p) => sum + p.progress, 0) / mockProgress.length
        const nearDeadline = mockProgress.filter(p => {
          const challenge = mockChallenges.find(c => c.id === p.challengeId)
          return challenge && new Date(challenge.endDate).getTime() - Date.now() < 3 * 24 * 60 * 60 * 1000
        }).length

        setStats({
          totalActive: active,
          totalCompleted: completed,
          averageProgress: Math.round(avgProgress),
          nearingDeadline: nearDeadline
        })

        setLoading(false)
      } catch (error) {
        console.error('Error loading user progress:', error)
        setLoading(false)
      }
    }

    loadUserProgress()
  }, [userId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/20'
      case 'in-progress':
        return 'text-blue-400 bg-blue-400/20'
      case 'submitted':
        return 'text-yellow-400 bg-yellow-400/20'
      case 'joined':
        return 'text-purple-400 bg-purple-400/20'
      default:
        return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'in-progress':
        return <Target className="w-4 h-4" />
      case 'submitted':
        return <Upload className="w-4 h-4" />
      case 'joined':
        return <Clock className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="bg-gray-800/50 rounded-lg p-6 animate-pulse">
            <div className="h-4 bg-gray-700 rounded w-3/4 mb-4"></div>
            <div className="h-8 bg-gray-700 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-700 rounded w-full"></div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-blue-400/10 rounded-lg p-6"
        >
          <div className="flex items-center justify-between mb-2">
            <Target className="w-6 h-6 text-blue-400" />
            <span className="text-2xl font-bold text-blue-400">{stats.totalActive}</span>
          </div>
          <p className="text-sm text-gray-400">Active Challenges</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-green-400/10 rounded-lg p-6"
        >
          <div className="flex items-center justify-between mb-2">
            <Trophy className="w-6 h-6 text-green-400" />
            <span className="text-2xl font-bold text-green-400">{stats.totalCompleted}</span>
          </div>
          <p className="text-sm text-gray-400">Completed</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-accent-400/10 rounded-lg p-6"
        >
          <div className="flex items-center justify-between mb-2">
            <TrendingUp className="w-6 h-6 text-accent-400" />
            <span className="text-2xl font-bold text-accent-400">{stats.averageProgress}%</span>
          </div>
          <p className="text-sm text-gray-400">Average Progress</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-red-400/10 rounded-lg p-6"
        >
          <div className="flex items-center justify-between mb-2">
            <AlertCircle className="w-6 h-6 text-red-400" />
            <span className="text-2xl font-bold text-red-400">{stats.nearingDeadline}</span>
          </div>
          <p className="text-sm text-gray-400">Nearing Deadline</p>
        </motion.div>
      </div>

      {/* Active Challenges */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white">Your Active Challenges</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {userProgress
            .filter(progress => progress.status === 'in-progress' || progress.status === 'joined')
            .map((progress, index) => {
              const challenge = challenges.find(c => c.id === progress.challengeId)
              if (!challenge) return null

              const timeLeft = formatDistanceToNow(challenge.endDate, { addSuffix: true })
              const isNearDeadline = new Date(challenge.endDate).getTime() - Date.now() < 3 * 24 * 60 * 60 * 1000

              return (
                <motion.div
                  key={progress.challengeId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 + index * 0.1 }}
                  className={`bg-gray-800/50 rounded-lg p-6 border ${
                    isNearDeadline ? 'border-red-400/50' : 'border-gray-700'
                  }`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h4 className="font-semibold text-white mb-1">{challenge.title}</h4>
                      <p className="text-sm text-gray-400 mb-2">{challenge.description}</p>
                      <div className="flex items-center space-x-2 mb-3">
                        <Badge className={getStatusColor(progress.status)}>
                          {getStatusIcon(progress.status)}
                          <span className="ml-1 capitalize">{progress.status.replace('-', ' ')}</span>
                        </Badge>
                        <Badge variant="outline" className="text-gray-400 border-gray-600">
                          {challenge.difficulty}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-white">{progress.progress}%</div>
                      <div className="text-xs text-gray-400">Complete</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm text-gray-400">Progress</span>
                        <span className="text-sm text-gray-300">{progress.progress}%</span>
                      </div>
                      <Progress value={progress.progress} className="h-2" />
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-1 text-gray-400">
                        <Calendar className="w-4 h-4" />
                        <span>Ends {timeLeft}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-accent-400">
                        <span>{challenge.reward}</span>
                      </div>
                    </div>

                    {progress.submission && (
                      <div className="bg-gray-700/50 rounded p-3 text-sm">
                        <div className="flex items-center space-x-2 mb-1">
                          <Upload className="w-4 h-4 text-blue-400" />
                          <span className="text-blue-400">Latest Submission</span>
                          <Badge className={
                            progress.submission.status === 'approved' ? 'bg-green-400/20 text-green-400' :
                            progress.submission.status === 'rejected' ? 'bg-red-400/20 text-red-400' :
                            'bg-yellow-400/20 text-yellow-400'
                          }>
                            {progress.submission.status}
                          </Badge>
                        </div>
                        <p className="text-gray-300">{progress.submission.content}</p>
                      </div>
                    )}

                    <div className="flex space-x-2">
                      <Button size="sm" className="bg-accent-600 hover:bg-accent-700">
                        Continue Challenge
                      </Button>
                      {!progress.submission && (
                        <Button size="sm" variant="outline" className="border-gray-600">
                          Submit Work
                        </Button>
                      )}
                    </div>
                  </div>
                </motion.div>
              )
            })}
        </div>
      </div>
    </div>
  )
}

export default UserProgressDashboard