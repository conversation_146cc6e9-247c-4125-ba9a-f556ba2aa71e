/**
 * Contests Tab Component
 * 
 * Community contests tab showing active contests, voting opportunities,
 * and contest participation features.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { 
  Trophy, 
  Calendar, 
  Users, 
  Star, 
  Filter,
  Search,
  Plus,
  ArrowRight,
  Clock,
  Eye,
  Award,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import { useContests } from '@/hooks/useContests'
import { ContestStatusCard } from '@/components/contests'
import type { ContestStatus } from '@/types/contests'

interface ContestsTabProps {
  className?: string
}

const ContestsTab: React.FC<ContestsTabProps> = ({ className }) => {
  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Community Contests</h1>
            <p className="text-gray-400">
              Participate in design contests and showcase your creativity
            </p>
          </div>

          <div className="mt-4 lg:mt-0">
            <Link
              href="/community/contests"
              className="inline-flex items-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>View All Contests</span>
            </Link>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-gray-800/50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <Zap className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">0</p>
                <p className="text-sm text-gray-400">Active</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Eye className="w-5 h-5 text-yellow-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">0</p>
                <p className="text-sm text-gray-400">Voting</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Clock className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">0</p>
                <p className="text-sm text-gray-400">Upcoming</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Award className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">0</p>
                <p className="text-sm text-gray-400">Completed</p>
              </div>
            </div>
          </div>
        </div>

        {/* Empty State */}
        <div className="text-center py-12">
          <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No contests available</h3>
          <p className="text-gray-400">Check back soon for new contests and competitions!</p>
        </div>
      </div>
    </div>
  )

}



export default ContestsTab
