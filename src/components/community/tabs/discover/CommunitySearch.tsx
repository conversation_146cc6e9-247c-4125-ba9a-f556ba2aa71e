/**
 * Community Search Component
 * 
 * Provides search functionality across all community content types
 * including submissions, discussions, challenges, and members.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, X, Filter, Loader2, Users, MessageSquare, Target, Award, TrendingUp } from 'lucide-react'
import { communitySearchService, Submission, Discussion, Challenge, CommunityUser } from '@/lib/firebase/community'
import { useDebounce } from '@/hooks/useDebounce'

interface SearchResults {
  submissions: Submission[]
  discussions: Discussion[]
  challenges: Challenge[]
  members: CommunityUser[]
}

const CommunitySearch: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [results, setResults] = useState<SearchResults>({
    submissions: [],
    discussions: [],
    challenges: [],
    members: []
  })
  const [loading, setLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [selectedTypes, setSelectedTypes] = useState<('submissions' | 'discussions' | 'challenges' | 'members')[]>([
    'submissions', 'discussions', 'challenges', 'members'
  ])
  const [trendingSearches, setTrendingSearches] = useState<string[]>([])

  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Load trending searches on mount
  useEffect(() => {
    const loadTrendingSearches = async () => {
      try {
        const trending = await communitySearchService.getTrendingSearches()
        setTrendingSearches(trending)
      } catch (error) {
        console.error('Error loading trending searches:', error)
      }
    }
    loadTrendingSearches()
  }, [])

  // Perform search when debounced query changes
  useEffect(() => {
    if (debouncedSearchQuery.trim().length >= 2) {
      performSearch(debouncedSearchQuery)
    } else {
      setResults({ submissions: [], discussions: [], challenges: [], members: [] })
      setShowResults(false)
    }
  }, [debouncedSearchQuery, selectedTypes])

  const performSearch = useCallback(async (query: string) => {
    setLoading(true)
    try {
      const searchResults = await communitySearchService.searchCommunityContent(query, {
        types: selectedTypes,
        limit: 10
      })
      setResults(searchResults)
      setShowResults(true)
    } catch (error) {
      console.error('Error performing search:', error)
    } finally {
      setLoading(false)
    }
  }, [selectedTypes])

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim().length >= 2) {
      performSearch(searchQuery)
    }
  }

  const clearSearch = () => {
    setSearchQuery('')
    setResults({ submissions: [], discussions: [], challenges: [], members: [] })
    setShowResults(false)
  }

  const handleTrendingClick = (term: string) => {
    setSearchQuery(term)
    performSearch(term)
  }

  const toggleContentType = (type: 'submissions' | 'discussions' | 'challenges' | 'members') => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  const totalResults = results.submissions.length + results.discussions.length + results.challenges.length + results.members.length

  const resultSections = [
    {
      key: 'submissions',
      title: 'Submissions',
      icon: Award,
      data: results.submissions,
      color: 'text-yellow-400'
    },
    {
      key: 'discussions',
      title: 'Discussions',
      icon: MessageSquare,
      data: results.discussions,
      color: 'text-blue-400'
    },
    {
      key: 'challenges',
      title: 'Challenges',
      icon: Target,
      data: results.challenges,
      color: 'text-accent-400'
    },
    {
      key: 'members',
      title: 'Members',
      icon: Users,
      data: results.members,
      color: 'text-green-400'
    }
  ]

  return (
    <div className="community-search relative">
      {/* Search Form */}
      <form onSubmit={handleSearchSubmit} className="relative mb-6">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search community content..."
            className="w-full pl-12 pr-12 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-400 focus:ring-1 focus:ring-accent-400"
          />
          {searchQuery && (
            <button
              type="button"
              onClick={clearSearch}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          )}
          {loading && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
              <Loader2 className="w-5 h-5 text-accent-400 animate-spin" />
            </div>
          )}
        </div>

        {/* Content Type Filters */}
        <div className="flex items-center space-x-4 mt-4">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-400">Filter by:</span>
          </div>
          {['submissions', 'discussions', 'challenges', 'members'].map((type) => (
            <button
              key={type}
              type="button"
              onClick={() => toggleContentType(type as any)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedTypes.includes(type as any)
                  ? 'bg-accent-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </button>
          ))}
        </div>
      </form>

      {/* Trending Searches */}
      {!showResults && trendingSearches.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center space-x-2 mb-3">
            <TrendingUp className="w-4 h-4 text-accent-400" />
            <span className="text-sm font-medium text-gray-300">Trending Searches</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {trendingSearches.map((term, index) => (
              <motion.button
                key={term}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => handleTrendingClick(term)}
                className="px-3 py-1 bg-gray-800/50 border border-gray-600 rounded-full text-sm text-gray-300 hover:border-accent-400 hover:text-accent-400 transition-colors"
              >
                {term}
              </motion.button>
            ))}
          </div>
        </motion.div>
      )}

      {/* Search Results */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="search-results"
          >
            {totalResults === 0 ? (
              <div className="text-center py-8">
                <Search className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-400">No results found for "{searchQuery}"</p>
                <p className="text-sm text-gray-500 mt-2">Try different keywords or check your spelling</p>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white">
                    Search Results ({totalResults})
                  </h3>
                  <span className="text-sm text-gray-400">for "{searchQuery}"</span>
                </div>

                {resultSections.map((section) => {
                  if (section.data.length === 0) return null

                  return (
                    <motion.div
                      key={section.key}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="result-section"
                    >
                      <div className="flex items-center space-x-2 mb-4">
                        <section.icon className={`w-5 h-5 ${section.color}`} />
                        <h4 className="font-semibold text-white">
                          {section.title} ({section.data.length})
                        </h4>
                      </div>

                      <div className="space-y-3">
                        {section.data.slice(0, 3).map((item: any, index) => (
                          <motion.div
                            key={item.id}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors cursor-pointer"
                          >
                            <h5 className="font-medium text-white mb-1 line-clamp-2">
                              {item.title}
                            </h5>
                            {item.description && (
                              <p className="text-sm text-gray-400 mb-2 line-clamp-2">
                                {item.description}
                              </p>
                            )}
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>
                                {section.key === 'members' ? 
                                  `Level ${item.level}` : 
                                  `by ${item.author?.name || item.createdBy?.name || 'Unknown'}`
                                }
                              </span>
                              {item.likes && (
                                <span>❤️ {item.likes}</span>
                              )}
                              {item.replies && (
                                <span>💬 {item.replies}</span>
                              )}
                              {item.participants && (
                                <span>👥 {item.participants}</span>
                              )}
                            </div>
                          </motion.div>
                        ))}
                      </div>

                      {section.data.length > 3 && (
                        <button className="mt-3 text-sm text-accent-400 hover:text-accent-300 transition-colors">
                          View all {section.data.length} {section.title.toLowerCase()}
                        </button>
                      )}
                    </motion.div>
                  )
                })}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default CommunitySearch