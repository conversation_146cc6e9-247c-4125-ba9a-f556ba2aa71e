/**
 * Discussions Tab Component
 * 
 * Community discussions tab showing forums, topics, and conversation threads.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  MessageCircle,
  Users,
  Clock,
  Pin,
  Flame,
  Search,
  Plus,
  ArrowRight,
  Eye,
  ThumbsUp,
  MessageSquare,
  User,
  Calendar,
  Tag
} from 'lucide-react'
import Link from 'next/link'
import { useUser } from '@/lib/useUser'

interface DiscussionsTabProps {
  className?: string
}

// Mock data - replace with actual data fetching
const mockDiscussions = [
  {
    id: '1',
    title: 'Best keycap profiles for gaming?',
    author: { id: '1', name: 'GamerPro', avatar: null, level: 12 },
    category: 'General Discussion',
    replies: 23,
    views: 456,
    lastActivity: new Date('2024-01-15T10:30:00'),
    createdAt: new Date('2024-01-14T09:00:00'),
    isPinned: false,
    isHot: true,
    tags: ['gaming', 'keycaps', 'profiles'],
    lastReply: {
      author: 'KeyboardExpert',
      timestamp: new Date('2024-01-15T10:30:00')
    }
  },
  {
    id: '2',
    title: 'Group buy organization tips',
    author: { id: '2', name: 'OrganizeGuru', avatar: null, level: 18 },
    category: 'Group Buys',
    replies: 45,
    views: 1200,
    lastActivity: new Date('2024-01-15T08:15:00'),
    createdAt: new Date('2024-01-10T14:20:00'),
    isPinned: true,
    isHot: false,
    tags: ['group-buy', 'organization', 'tips'],
    lastReply: {
      author: 'CommunityMod',
      timestamp: new Date('2024-01-15T08:15:00')
    }
  },
  {
    id: '3',
    title: 'Show off your latest build!',
    author: { id: '3', name: 'BuildMaster', avatar: null, level: 15 },
    category: 'Showcase',
    replies: 67,
    views: 2100,
    lastActivity: new Date('2024-01-15T07:45:00'),
    createdAt: new Date('2024-01-12T16:30:00'),
    isPinned: false,
    isHot: true,
    tags: ['showcase', 'builds', 'photos'],
    lastReply: {
      author: 'KeyboardLover',
      timestamp: new Date('2024-01-15T07:45:00')
    }
  }
]

const categories = [
  'All Categories',
  'General Discussion',
  'Group Buys',
  'Showcase',
  'Technical Help',
  'Reviews',
  'Trading'
]

const DiscussionsTab: React.FC<DiscussionsTabProps> = ({ className }) => {
  const { user } = useUser()
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('All Categories')
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'replies'>('recent')

  // Filter and sort discussions
  const filteredDiscussions = useMemo(() => {
    let filtered = mockDiscussions.filter(discussion => {
      const matchesSearch = discussion.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           discussion.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           discussion.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      const matchesCategory = categoryFilter === 'All Categories' || discussion.category === categoryFilter
      
      return matchesSearch && matchesCategory
    })

    // Sort discussions
    switch (sortBy) {
      case 'popular':
        filtered.sort((a, b) => b.views - a.views)
        break
      case 'replies':
        filtered.sort((a, b) => b.replies - a.replies)
        break
      case 'recent':
      default:
        filtered.sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime())
        break
    }

    return filtered
  }, [searchQuery, categoryFilter, sortBy])

  const pinnedDiscussions = filteredDiscussions.filter(d => d.isPinned)
  const hotDiscussions = filteredDiscussions.filter(d => d.isHot && !d.isPinned)
  const regularDiscussions = filteredDiscussions.filter(d => !d.isPinned && !d.isHot)

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Community Discussions</h1>
          <p className="text-gray-400">
            Join conversations and connect with fellow enthusiasts
          </p>
        </div>
        
        <div className="mt-4 lg:mt-0">
          <Link
            href="/community/discussions/new"
            className="inline-flex items-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Start Discussion</span>
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <MessageCircle className="w-5 h-5 text-blue-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{mockDiscussions.length}</p>
              <p className="text-sm text-gray-400">Active Topics</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <MessageSquare className="w-5 h-5 text-green-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">
                {mockDiscussions.reduce((sum, d) => sum + d.replies, 0)}
              </p>
              <p className="text-sm text-gray-400">Total Replies</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <Eye className="w-5 h-5 text-purple-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">
                {mockDiscussions.reduce((sum, d) => sum + d.views, 0)}
              </p>
              <p className="text-sm text-gray-400">Total Views</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-500/20 rounded-lg">
              <Flame className="w-5 h-5 text-orange-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{hotDiscussions.length}</p>
              <p className="text-sm text-gray-400">Hot Topics</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search discussions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-500 w-full sm:w-64"
            />
          </div>

          {/* Category Filter */}
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
          >
            <option value="recent">Most Recent</option>
            <option value="popular">Most Popular</option>
            <option value="replies">Most Replies</option>
          </select>
        </div>
      </div>

      {/* Pinned Discussions */}
      {pinnedDiscussions.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
            <Pin className="w-5 h-5 text-accent-400" />
            <span>Pinned Discussions</span>
          </h2>
          <div className="space-y-3">
            {pinnedDiscussions.map((discussion) => (
              <DiscussionCard key={discussion.id} discussion={discussion} />
            ))}
          </div>
        </div>
      )}

      {/* Hot Discussions */}
      {hotDiscussions.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
            <Flame className="w-5 h-5 text-orange-400" />
            <span>Hot Topics</span>
          </h2>
          <div className="space-y-3">
            {hotDiscussions.map((discussion) => (
              <DiscussionCard key={discussion.id} discussion={discussion} />
            ))}
          </div>
        </div>
      )}

      {/* Regular Discussions */}
      <div>
        <h2 className="text-xl font-bold text-white mb-4">
          All Discussions ({regularDiscussions.length})
        </h2>
        
        {regularDiscussions.length === 0 ? (
          <div className="text-center py-12">
            <MessageCircle className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">No discussions found</h3>
            <p className="text-gray-400">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className="space-y-3">
            {regularDiscussions.map((discussion) => (
              <DiscussionCard key={discussion.id} discussion={discussion} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Discussion Card Component
const DiscussionCard: React.FC<{ discussion: any }> = ({ discussion }) => {
  const getTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    return `${Math.floor(diffInHours / 24)}d ago`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-accent-500/30 transition-all duration-300 p-4"
    >
      <Link href={`/community/discussions/${discussion.id}`}>
        <div className="flex items-start space-x-4">
          {/* Avatar */}
          <div className="w-10 h-10 bg-gray-600 rounded-full flex-shrink-0 flex items-center justify-center">
            <User className="w-5 h-5 text-gray-400" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  {discussion.isPinned && (
                    <Pin className="w-4 h-4 text-accent-400" />
                  )}
                  {discussion.isHot && (
                    <Flame className="w-4 h-4 text-orange-400" />
                  )}
                  <h3 className="text-lg font-semibold text-white hover:text-accent-400 transition-colors truncate">
                    {discussion.title}
                  </h3>
                </div>
                
                <div className="flex items-center space-x-3 text-sm text-gray-400 mb-2">
                  <span>by {discussion.author.name}</span>
                  <span>•</span>
                  <span>{discussion.category}</span>
                  <span>•</span>
                  <span>{getTimeAgo(discussion.createdAt)}</span>
                </div>

                {/* Tags */}
                <div className="flex items-center space-x-2 mb-3">
                  {discussion.tags.map((tag: string) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* Stats */}
              <div className="flex flex-col items-end space-y-1 text-sm text-gray-400 ml-4">
                <div className="flex items-center space-x-4">
                  <span className="flex items-center space-x-1">
                    <MessageSquare className="w-4 h-4" />
                    <span>{discussion.replies}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>{discussion.views}</span>
                  </span>
                </div>
                <div className="text-xs">
                  Last reply by {discussion.lastReply.author}
                </div>
                <div className="text-xs">
                  {getTimeAgo(discussion.lastActivity)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}

export default DiscussionsTab
