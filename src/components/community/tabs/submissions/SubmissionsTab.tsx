/**
 * Submissions Tab Component
 * 
 * Community submissions tab showing user submissions, galleries,
 * and submission management features.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { 
  Image, 
  Heart, 
  Eye, 
  MessageCircle, 
  Filter,
  Search,
  Plus,
  Grid,
  List,
  Star,
  Calendar,
  User,
  Award,
  TrendingUp
} from 'lucide-react'
import Link from 'next/link'
import { useUser } from '@/lib/useUser'

interface SubmissionsTabProps {
  className?: string
}

// Mock data - replace with actual data fetching
const mockSubmissions = [
  {
    id: '1',
    title: 'Neon Dreams Keycap Set',
    author: { id: '1', name: 'DesignMaster', avatar: null, level: 15 },
    image: '/images/keycaps/neon-dreams.jpg',
    category: 'Keycap Design',
    likes: 124,
    views: 1250,
    comments: 23,
    submittedAt: new Date('2024-01-15'),
    featured: true,
    tags: ['neon', 'cyberpunk', 'artisan']
  },
  {
    id: '2',
    title: 'Minimalist Wood Theme',
    author: { id: '2', name: '<PERSON><PERSON><PERSON><PERSON>', avatar: null, level: 12 },
    image: '/images/keycaps/wood-theme.jpg',
    category: 'Keycap Design',
    likes: 89,
    views: 890,
    comments: 15,
    submittedAt: new Date('2024-01-14'),
    featured: false,
    tags: ['minimalist', 'wood', 'natural']
  },
  {
    id: '3',
    title: 'Galaxy Artisan Collection',
    author: { id: '3', name: 'SpaceDesigner', avatar: null, level: 18 },
    image: '/images/keycaps/galaxy.jpg',
    category: 'Artisan',
    likes: 156,
    views: 2100,
    comments: 31,
    submittedAt: new Date('2024-01-13'),
    featured: true,
    tags: ['galaxy', 'space', 'artisan', 'resin']
  }
]

const SubmissionsTab: React.FC<SubmissionsTabProps> = ({ className }) => {
  const { user } = useUser()
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'trending'>('recent')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // Filter and sort submissions
  const filteredSubmissions = useMemo(() => {
    let filtered = mockSubmissions.filter(submission => {
      const matchesSearch = submission.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           submission.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           submission.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      const matchesCategory = categoryFilter === 'all' || submission.category === categoryFilter
      
      return matchesSearch && matchesCategory
    })

    // Sort submissions
    switch (sortBy) {
      case 'popular':
        filtered.sort((a, b) => b.likes - a.likes)
        break
      case 'trending':
        filtered.sort((a, b) => (b.likes + b.views) - (a.likes + a.views))
        break
      case 'recent':
      default:
        filtered.sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime())
        break
    }

    return filtered
  }, [searchQuery, categoryFilter, sortBy])

  const featuredSubmissions = filteredSubmissions.filter(s => s.featured)
  const categories = ['all', 'Keycap Design', 'Artisan', 'Layout', 'Photography']

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Community Submissions</h1>
          <p className="text-gray-400">
            Explore amazing creations from our community members
          </p>
        </div>
        
        <div className="mt-4 lg:mt-0">
          <Link
            href="/submit"
            className="inline-flex items-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Submit Your Work</span>
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <Image className="w-5 h-5 text-blue-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{mockSubmissions.length}</p>
              <p className="text-sm text-gray-400">Total Submissions</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-500/20 rounded-lg">
              <Heart className="w-5 h-5 text-red-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">
                {mockSubmissions.reduce((sum, s) => sum + s.likes, 0)}
              </p>
              <p className="text-sm text-gray-400">Total Likes</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <Eye className="w-5 h-5 text-green-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">
                {mockSubmissions.reduce((sum, s) => sum + s.views, 0)}
              </p>
              <p className="text-sm text-gray-400">Total Views</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-500/20 rounded-lg">
              <Star className="w-5 h-5 text-yellow-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{featuredSubmissions.length}</p>
              <p className="text-sm text-gray-400">Featured</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search submissions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-500 w-full sm:w-64"
            />
          </div>

          {/* Category Filter */}
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
          >
            <option value="recent">Most Recent</option>
            <option value="popular">Most Popular</option>
            <option value="trending">Trending</option>
          </select>
        </div>

        {/* View Mode */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid' 
                ? 'bg-accent-600 text-white' 
                : 'bg-gray-800 text-gray-400 hover:text-white'
            }`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list' 
                ? 'bg-accent-600 text-white' 
                : 'bg-gray-800 text-gray-400 hover:text-white'
            }`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Featured Submissions */}
      {featuredSubmissions.length > 0 && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-6">Featured Submissions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredSubmissions.map((submission) => (
              <SubmissionCard key={submission.id} submission={submission} featured />
            ))}
          </div>
        </div>
      )}

      {/* All Submissions */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">
          All Submissions ({filteredSubmissions.length})
        </h2>
        
        {filteredSubmissions.length === 0 ? (
          <div className="text-center py-12">
            <Image className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">No submissions found</h3>
            <p className="text-gray-400">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className={viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" 
            : "space-y-4"
          }>
            {filteredSubmissions.map((submission) => (
              <SubmissionCard 
                key={submission.id} 
                submission={submission} 
                viewMode={viewMode}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Submission Card Component
const SubmissionCard: React.FC<{ 
  submission: any; 
  featured?: boolean;
  viewMode?: 'grid' | 'list';
}> = ({ submission, featured = false, viewMode = 'grid' }) => {
  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-accent-500/30 transition-all duration-300 p-4"
      >
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-700 rounded-lg flex-shrink-0"></div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-bold text-white truncate">{submission.title}</h3>
            <p className="text-gray-400 text-sm">by {submission.author.name}</p>
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-400">
              <span className="flex items-center space-x-1">
                <Heart className="w-4 h-4" />
                <span>{submission.likes}</span>
              </span>
              <span className="flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span>{submission.views}</span>
              </span>
              <span className="flex items-center space-x-1">
                <MessageCircle className="w-4 h-4" />
                <span>{submission.comments}</span>
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      className={`bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-accent-500/30 transition-all duration-300 overflow-hidden group ${
        featured ? 'ring-2 ring-accent-500/30' : ''
      }`}
    >
      <div className="aspect-video bg-gray-700 relative">
        {featured && (
          <div className="absolute top-2 left-2 px-2 py-1 bg-accent-500 text-white text-xs font-medium rounded">
            Featured
          </div>
        )}
      </div>
      
      <div className="p-4">
        <h3 className="text-lg font-bold text-white group-hover:text-accent-400 transition-colors truncate mb-2">
          {submission.title}
        </h3>
        
        <div className="flex items-center space-x-2 mb-3">
          <div className="w-6 h-6 bg-gray-600 rounded-full"></div>
          <span className="text-gray-400 text-sm">{submission.author.name}</span>
          <span className="text-accent-400 text-xs">Lv.{submission.author.level}</span>
        </div>

        <div className="flex items-center justify-between text-sm text-gray-400">
          <div className="flex items-center space-x-3">
            <span className="flex items-center space-x-1">
              <Heart className="w-4 h-4" />
              <span>{submission.likes}</span>
            </span>
            <span className="flex items-center space-x-1">
              <Eye className="w-4 h-4" />
              <span>{submission.views}</span>
            </span>
          </div>
          <span>{submission.submittedAt.toLocaleDateString()}</span>
        </div>
      </div>
    </motion.div>
  )
}

export default SubmissionsTab
