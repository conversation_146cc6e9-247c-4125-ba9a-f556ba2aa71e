/**
 * Contest Status Card Component
 *
 * Displays contest lifecycle information including current phase,
 * progress, time remaining, and next phase transitions.
 *
 * Features:
 * - Real-time phase tracking
 * - Progress visualization
 * - Countdown timers
 * - Phase transition indicators
 * - Gaming-inspired design
 *
 * @component
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar, 
  Clock, 
  Trophy, 
  Users, 
  Vote,
  CheckCircle,
  AlertCircle,
  Play,
  Pause
} from 'lucide-react'
import { getContestPhaseInfo, isContestInPhase, getTimeRemainingInPhase } from '../../lib/contestLifecycle'
import type { Contest } from '../../types/contests'

interface ContestStatusCardProps {
  contest: Contest
  showDetails?: boolean
  compact?: boolean
  className?: string
}

export const ContestStatusCard: React.FC<ContestStatusCardProps> = ({
  contest,
  showDetails = true,
  compact = false,
  className = ''
}) => {
  const phaseInfo = getContestPhaseInfo(contest)
  const timeRemaining = getTimeRemainingInPhase(contest)
  const [timeDisplay, setTimeDisplay] = useState<string>('')

  // Update countdown display
  useEffect(() => {
    if (!timeRemaining) {
      setTimeDisplay('')
      return
    }

    const updateDisplay = () => {
      const remaining = timeRemaining - (Date.now() - Date.now()) // This needs proper calculation
      if (remaining <= 0) {
        setTimeDisplay('Time expired')
        return
      }

      const days = Math.floor(remaining / (1000 * 60 * 60 * 24))
      const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))

      if (days > 0) {
        setTimeDisplay(`${days}d ${hours}h ${minutes}m`)
      } else if (hours > 0) {
        setTimeDisplay(`${hours}h ${minutes}m`)
      } else {
        setTimeDisplay(`${minutes}m`)
      }
    }

    updateDisplay()
    const interval = setInterval(updateDisplay, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [timeRemaining])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'upcoming':
        return 'text-blue-400 bg-blue-500/20 border-blue-500/30'
      case 'accepting submissions':
      case 'active':
        return 'text-green-400 bg-green-500/20 border-green-500/30'
      case 'voting period':
      case 'voting':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
      case 'completed':
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30'
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30'
    }
  }

  const getPhaseIcon = (phase: string) => {
    switch (phase.toLowerCase()) {
      case 'upcoming':
        return <Calendar className="w-4 h-4" />
      case 'accepting submissions':
      case 'active':
        return <Play className="w-4 h-4" />
      case 'voting period':
      case 'voting':
        return <Vote className="w-4 h-4" />
      case 'completed':
        return <Trophy className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  if (compact) {
    return (
      <div className={`inline-flex items-center space-x-2 ${className}`}>
        <div className={`
          flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border
          ${getStatusColor(phaseInfo?.currentPhase || contest.status)}
        `}>
          {getPhaseIcon(phaseInfo?.currentPhase || contest.status)}
          <span>{phaseInfo?.currentPhase || contest.status}</span>
        </div>
        {timeDisplay && (
          <span className="text-xs text-gray-400">
            {timeDisplay} left
          </span>
        )}
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        p-4 bg-gray-800/50 rounded-lg border border-gray-700/50
        hover:border-accent-500/30 transition-colors
        ${className}
      `}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className={`
            flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium border
            ${getStatusColor(phaseInfo?.currentPhase || contest.status)}
          `}>
            {getPhaseIcon(phaseInfo?.currentPhase || contest.status)}
            <span>{phaseInfo?.currentPhase || contest.status}</span>
          </div>
          {contest.featured && (
            <span className="px-2 py-1 text-xs font-medium rounded-full bg-accent-500/20 text-accent-400 border border-accent-500/30">
              Featured
            </span>
          )}
        </div>
        
        {timeDisplay && (
          <div className="text-right">
            <p className="text-sm text-gray-400">Time Remaining</p>
            <p className="text-lg font-semibold text-white">{timeDisplay}</p>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {phaseInfo && phaseInfo.progress > 0 && (
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
            <span>Progress</span>
            <span>{Math.round(phaseInfo.progress)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-gradient-to-r from-accent-500 to-accent-400 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${phaseInfo.progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>
      )}

      {/* Next Phase */}
      {phaseInfo?.nextPhase && (
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <AlertCircle className="w-4 h-4" />
          <span>Next: {phaseInfo.nextPhase}</span>
        </div>
      )}

      {/* Contest Stats */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-700/50">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-lg font-semibold text-white">{contest.stats.submissions}</p>
              <p className="text-xs text-gray-400">Submissions</p>
            </div>
            <div>
              <p className="text-lg font-semibold text-white">{contest.stats.participants}</p>
              <p className="text-xs text-gray-400">Participants</p>
            </div>
            <div>
              <p className="text-lg font-semibold text-white">{contest.stats.totalVotes}</p>
              <p className="text-xs text-gray-400">Votes</p>
            </div>
          </div>
        </div>
      )}

      {/* Timeline Preview */}
      {showDetails && !compact && (
        <div className="mt-4 pt-4 border-t border-gray-700/50">
          <h4 className="text-sm font-medium text-gray-300 mb-3">Timeline</h4>
          <div className="space-y-2">
            <TimelineItem
              icon={<Calendar className="w-3 h-3" />}
              label="Submissions Open"
              date={contest.submissionStart.toDate()}
              active={phaseInfo?.currentPhase === 'Accepting Submissions'}
              completed={phaseInfo?.currentPhase === 'Voting Period' || phaseInfo?.currentPhase === 'Completed'}
            />
            <TimelineItem
              icon={<Vote className="w-3 h-3" />}
              label="Voting Begins"
              date={contest.votingStart.toDate()}
              active={phaseInfo?.currentPhase === 'Voting Period'}
              completed={phaseInfo?.currentPhase === 'Completed'}
            />
            <TimelineItem
              icon={<Trophy className="w-3 h-3" />}
              label="Results Announced"
              date={contest.resultsDate.toDate()}
              active={false}
              completed={phaseInfo?.currentPhase === 'Completed'}
            />
          </div>
        </div>
      )}
    </motion.div>
  )
}

/**
 * Timeline Item Component
 */
interface TimelineItemProps {
  icon: React.ReactNode
  label: string
  date: Date
  active: boolean
  completed: boolean
}

const TimelineItem: React.FC<TimelineItemProps> = ({
  icon,
  label,
  date,
  active,
  completed
}) => {
  return (
    <div className="flex items-center space-x-3">
      <div className={`
        flex items-center justify-center w-6 h-6 rounded-full border-2
        ${completed 
          ? 'bg-green-500 border-green-500 text-white' 
          : active 
            ? 'bg-accent-500 border-accent-500 text-white'
            : 'bg-gray-700 border-gray-600 text-gray-400'
        }
      `}>
        {completed ? <CheckCircle className="w-3 h-3" /> : icon}
      </div>
      <div className="flex-1">
        <p className={`text-sm font-medium ${
          completed ? 'text-green-400' : active ? 'text-accent-400' : 'text-gray-400'
        }`}>
          {label}
        </p>
        <p className="text-xs text-gray-500">
          {date.toLocaleDateString()} at {date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </p>
      </div>
    </div>
  )
}

export default ContestStatusCard
