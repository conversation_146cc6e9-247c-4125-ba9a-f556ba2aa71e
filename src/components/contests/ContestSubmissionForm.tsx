/**
 * Contest Submission Form Component
 *
 * User-facing form for submitting entries to contests with file upload,
 * validation, and submission management.
 *
 * Features:
 * - Multi-file image upload with drag & drop
 * - Real-time validation and preview
 * - Contest rule compliance checking
 * - Team submission support
 * - Draft saving functionality
 * - Progress tracking
 *
 * @component
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Upload,
  X,
  Save,
  Send,
  AlertCircle
} from 'lucide-react'
import { Timestamp } from 'firebase/firestore'
import { useUser } from '../../lib/useUser'
import { useContestSubmission } from '../../hooks/useContests'
import { ContestStatusCard } from './ContestStatusCard'
import {
  uploadSubmissionImages,
  validateFiles,
  validateImageDimensions,
  type UploadProgress
} from '../../lib/api/contestUploads'
import type { Contest, ContestSubmissionCreateInput, ContestTeam } from '../../types/contests'

interface ContestSubmissionFormProps {
  contest: Contest
  existingSubmission?: any // ContestSubmission type
  onSubmit?: (submissionId: string) => void
  onCancel?: () => void
  className?: string
}

interface FormData {
  title: string
  description: string
  category: string
  tags: string[]
  images: File[]
  team?: ContestTeam
}

interface ValidationError {
  field: string
  message: string
}

export const ContestSubmissionForm: React.FC<ContestSubmissionFormProps> = ({
  contest,
  existingSubmission,
  onSubmit,
  onCancel,
  className = ''
}) => {
  const { user } = useUser()
  const { submit, submitting, error } = useContestSubmission(existingSubmission?.id)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [dragActive, setDragActive] = useState(false)
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [previewImages, setPreviewImages] = useState<string[]>([])
  const [isDraft, setIsDraft] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([])
  const [uploading, setUploading] = useState(false)

  const [formData, setFormData] = useState<FormData>({
    title: existingSubmission?.title || '',
    description: existingSubmission?.description || '',
    category: existingSubmission?.category || contest.category,
    tags: existingSubmission?.tags || [],
    images: [],
    team: existingSubmission?.team || undefined
  })

  // Load existing images if editing
  useEffect(() => {
    if (existingSubmission?.images) {
      setPreviewImages(existingSubmission.images)
    }
  }, [existingSubmission])

  /**
   * Update form data
   */
  const updateFormData = useCallback((field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear validation errors for this field
    setValidationErrors(prev => prev.filter(error => error.field !== field))
  }, [])

  /**
   * Validate form data
   */
  const validateForm = useCallback((): boolean => {
    const errors: ValidationError[] = []

    // Title validation
    if (!formData.title.trim()) {
      errors.push({ field: 'title', message: 'Title is required' })
    } else if (formData.title.length < 3) {
      errors.push({ field: 'title', message: 'Title must be at least 3 characters' })
    } else if (formData.title.length > 100) {
      errors.push({ field: 'title', message: 'Title must be less than 100 characters' })
    }

    // Description validation
    if (!formData.description.trim()) {
      errors.push({ field: 'description', message: 'Description is required' })
    } else if (formData.description.length < 10) {
      errors.push({ field: 'description', message: 'Description must be at least 10 characters' })
    } else if (formData.description.length > 1000) {
      errors.push({ field: 'description', message: 'Description must be less than 1000 characters' })
    }

    // Image validation
    const totalImages = formData.images.length + previewImages.length
    if (totalImages === 0) {
      errors.push({ field: 'images', message: 'At least one image is required' })
    }

    // File type validation
    const allowedTypes = contest.requirements.fileTypes.map(type => `image/${type}`)
    const invalidFiles = formData.images.filter(file => !allowedTypes.includes(file.type))
    if (invalidFiles.length > 0) {
      errors.push({ 
        field: 'images', 
        message: `Invalid file types. Allowed: ${contest.requirements.fileTypes.join(', ')}` 
      })
    }

    // File size validation
    const oversizedFiles = formData.images.filter(file => file.size > contest.requirements.maxFileSize)
    if (oversizedFiles.length > 0) {
      const maxSizeMB = contest.requirements.maxFileSize / (1024 * 1024)
      errors.push({ 
        field: 'images', 
        message: `Some files are too large. Maximum size: ${maxSizeMB}MB` 
      })
    }

    setValidationErrors(errors)
    return errors.length === 0
  }, [formData, previewImages, contest.requirements])

  /**
   * Handle file selection
   */
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files) return

    const newFiles = Array.from(files)
    const currentImageCount = formData.images.length + previewImages.length
    const maxImages = 5 // Reasonable limit

    if (currentImageCount + newFiles.length > maxImages) {
      setValidationErrors(prev => [...prev, {
        field: 'images',
        message: `Maximum ${maxImages} images allowed`
      }])
      return
    }

    // Validate files
    const validation = validateFiles(newFiles, contest.requirements)
    if (!validation.valid) {
      setValidationErrors(prev => [
        ...prev.filter(e => e.field !== 'images'),
        ...validation.errors.map(error => ({ field: 'images', message: error }))
      ])
      return
    }

    // Show warnings if any
    if (validation.warnings.length > 0) {
      console.warn('File upload warnings:', validation.warnings)
    }

    // Validate image dimensions for each file
    const validFiles: File[] = []
    const newPreviews: string[] = []

    for (const file of newFiles) {
      try {
        const dimensionCheck = await validateImageDimensions(file, contest.requirements.minImageDimensions)
        if (dimensionCheck.valid) {
          validFiles.push(file)
          newPreviews.push(URL.createObjectURL(file))
        } else {
          setValidationErrors(prev => [...prev, {
            field: 'images',
            message: `${file.name}: ${dimensionCheck.error}`
          }])
        }
      } catch (error) {
        setValidationErrors(prev => [...prev, {
          field: 'images',
          message: `${file.name}: Failed to validate image`
        }])
      }
    }

    if (validFiles.length > 0) {
      updateFormData('images', [...formData.images, ...validFiles])
      setPreviewImages(prev => [...prev, ...newPreviews])
    }
  }, [formData.images, previewImages.length, updateFormData, contest.requirements])

  /**
   * Handle drag and drop
   */
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files)
    }
  }, [handleFileSelect])

  /**
   * Remove image
   */
  const removeImage = useCallback((index: number) => {
    const isNewImage = index >= previewImages.length - formData.images.length
    
    if (isNewImage) {
      // Remove from new images
      const newImageIndex = index - (previewImages.length - formData.images.length)
      const newImages = [...formData.images]
      newImages.splice(newImageIndex, 1)
      updateFormData('images', newImages)
    }
    
    // Remove preview
    const newPreviews = [...previewImages]
    URL.revokeObjectURL(newPreviews[index]) // Clean up memory
    newPreviews.splice(index, 1)
    setPreviewImages(newPreviews)
  }, [formData.images, previewImages, updateFormData])

  /**
   * Add tag
   */
  const addTag = useCallback((tag: string) => {
    const trimmedTag = tag.trim().toLowerCase()
    if (trimmedTag && !formData.tags.includes(trimmedTag)) {
      updateFormData('tags', [...formData.tags, trimmedTag])
    }
  }, [formData.tags, updateFormData])

  /**
   * Remove tag
   */
  const removeTag = useCallback((tagToRemove: string) => {
    updateFormData('tags', formData.tags.filter(tag => tag !== tagToRemove))
  }, [formData.tags, updateFormData])

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async (asDraft = false) => {
    if (!user) {
      setValidationErrors([{ field: 'auth', message: 'You must be logged in to submit' }])
      return
    }

    if (!asDraft && !validateForm()) {
      return
    }

    setIsDraft(asDraft)
    setUploading(true)

    try {
      let imageUrls: string[] = []
      let thumbnailUrls: string[] = []

      // Upload images if there are new files
      if (formData.images.length > 0) {
        const uploadResults = await uploadSubmissionImages(
          formData.images,
          contest.id,
          user.uid,
          setUploadProgress
        )

        imageUrls = uploadResults.map(result => result.url)
        thumbnailUrls = uploadResults.map(result => result.thumbnail)
      }

      // Include existing images if editing
      if (existingSubmission?.images) {
        imageUrls = [...existingSubmission.images, ...imageUrls]
        thumbnailUrls = [...(existingSubmission.thumbnails || []), ...thumbnailUrls]
      }

      const submissionData: ContestSubmissionCreateInput = {
        contestId: contest.id,
        userId: user.uid,
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags,
        images: imageUrls,
        thumbnails: thumbnailUrls,
        lastModified: Timestamp.now(),
        status: asDraft ? 'draft' : 'submitted'
      }

      const submissionId = await submit(submissionData)

      if (submissionId) {
        onSubmit?.(submissionId)
      }
    } catch (err) {
      console.error('Submission failed:', err)
      setValidationErrors([{ field: 'submit', message: 'Failed to submit entry. Please try again.' }])
    } finally {
      setUploading(false)
      setUploadProgress([])
    }
  }, [user, validateForm, formData, contest.id, submit, onSubmit, existingSubmission])

  /**
   * Get error for field
   */
  const getFieldError = useCallback((field: string) => {
    return validationErrors.find(error => error.field === field)?.message
  }, [validationErrors])

  if (!user) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">Login Required</h3>
        <p className="text-gray-400">
          You must be logged in to submit to this contest
        </p>
      </div>
    )
  }

  return (
    <div className={`max-w-4xl mx-auto space-y-6 ${className}`}>
      {/* Contest Info */}
      <ContestStatusCard contest={contest} compact={true} />

      {/* Form */}
      <div className="bg-gray-900 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-white">Submit Your Entry</h2>
            <p className="text-gray-400 mt-1">
              Share your creative work with the community
            </p>
          </div>
          {existingSubmission && (
            <span className="px-3 py-1 text-sm bg-blue-500/20 text-blue-400 rounded-full">
              Editing Submission
            </span>
          )}
        </div>

        <div className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => updateFormData('title', e.target.value)}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-500 focus:outline-none"
              placeholder="Give your submission a catchy title..."
              maxLength={100}
            />
            {getFieldError('title') && (
              <p className="text-sm text-red-400 mt-1">{getFieldError('title')}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              {formData.title.length}/100 characters
            </p>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => updateFormData('description', e.target.value)}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-500 focus:outline-none"
              rows={4}
              placeholder="Describe your submission, inspiration, and creative process..."
              maxLength={1000}
            />
            {getFieldError('description') && (
              <p className="text-sm text-red-400 mt-1">{getFieldError('description')}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              {formData.description.length}/1000 characters
            </p>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Images * (Max 5)
            </label>
            
            {/* Upload Area */}
            <div
              className={`
                border-2 border-dashed rounded-lg p-6 text-center transition-colors
                ${dragActive 
                  ? 'border-accent-500 bg-accent-500/10' 
                  : 'border-gray-600 hover:border-gray-500'
                }
              `}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-400 mb-2">
                Drag and drop images here, or click to select
              </p>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors"
              >
                Choose Files
              </button>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={contest.requirements.fileTypes.map(type => `image/${type}`).join(',')}
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
              />
            </div>

            {/* File Requirements */}
            <div className="mt-2 text-xs text-gray-500">
              <p>Allowed formats: {contest.requirements.fileTypes.join(', ').toUpperCase()}</p>
              <p>Maximum size: {(contest.requirements.maxFileSize / (1024 * 1024)).toFixed(1)}MB per file</p>
              {contest.requirements.minImageDimensions && (
                <p>
                  Minimum dimensions: {contest.requirements.minImageDimensions.width}×{contest.requirements.minImageDimensions.height}px
                </p>
              )}
            </div>

            {getFieldError('images') && (
              <p className="text-sm text-red-400 mt-1">{getFieldError('images')}</p>
            )}
          </div>

          {/* Image Previews */}
          {previewImages.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {previewImages.map((preview, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="relative group"
                >
                  <img
                    src={preview}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-32 object-cover rounded-lg border border-gray-700"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-2 right-2 p-1 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </motion.div>
              ))}
            </div>
          )}

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Tags (Optional)
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-sm"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-2 text-gray-400 hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
            <input
              type="text"
              placeholder="Add tags (press Enter)"
              className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-500 focus:outline-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addTag(e.currentTarget.value)
                  e.currentTarget.value = ''
                }
              }}
            />
            <p className="text-xs text-gray-500 mt-1">
              Press Enter to add tags. Use relevant keywords to help others discover your work.
            </p>
          </div>

          {/* Upload Progress */}
          {uploading && uploadProgress.length > 0 && (
            <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-3">Uploading Images...</h4>
              <div className="space-y-3">
                {uploadProgress.map((progress) => (
                  <div key={progress.fileIndex} className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400 truncate">{progress.fileName}</span>
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          progress.status === 'complete' ? 'bg-green-500/20 text-green-400' :
                          progress.status === 'error' ? 'bg-red-500/20 text-red-400' :
                          progress.status === 'uploading' ? 'bg-blue-500/20 text-blue-400' :
                          progress.status === 'processing' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {progress.status === 'complete' ? 'Complete' :
                           progress.status === 'error' ? 'Error' :
                           progress.status === 'uploading' ? 'Uploading' :
                           progress.status === 'processing' ? 'Processing' :
                           'Pending'}
                        </span>
                        <span className="text-white font-medium">{Math.round(progress.progress)}%</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          progress.status === 'complete' ? 'bg-green-500' :
                          progress.status === 'error' ? 'bg-red-500' :
                          'bg-blue-500'
                        }`}
                        style={{ width: `${progress.progress}%` }}
                      />
                    </div>
                    {progress.error && (
                      <p className="text-xs text-red-400">{progress.error}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-red-400" />
                <span className="text-sm text-red-400">{error}</span>
              </div>
            </div>
          )}

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertCircle className="w-4 h-4 text-red-400 mt-0.5" />
                <div className="space-y-1">
                  {validationErrors.map((error, index) => (
                    <p key={index} className="text-sm text-red-400">{error.message}</p>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-700">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => handleSubmit(true)}
                disabled={submitting}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4 inline mr-2" />
                Save Draft
              </button>
              <button
                type="button"
                onClick={() => handleSubmit(false)}
                disabled={submitting || uploading}
                className="px-6 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {submitting || uploading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {uploading ? 'Uploading...' : isDraft ? 'Saving...' : 'Submitting...'}
                  </div>
                ) : (
                  <>
                    <Send className="w-4 h-4 inline mr-2" />
                    Submit Entry
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContestSubmissionForm
