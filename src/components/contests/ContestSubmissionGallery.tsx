/**
 * Contest Submission Gallery Component
 *
 * Public gallery displaying contest submissions with filtering,
 * sorting, and detailed view capabilities.
 *
 * Features:
 * - Grid layout with responsive design
 * - Submission filtering and sorting
 * - Detailed submission modal
 * - Voting integration
 * - User interaction tracking
 * - Performance optimization
 *
 * @component
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useMemo, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Timestamp } from 'firebase/firestore'
import {
  Eye,
  Heart,
  MessageCircle,
  Star,
  Filter,
  Grid,
  List,
  Search,
  Trophy,
  Calendar,
  User,
  Tag,
  X,
  ChevronLeft,
  ChevronRight,
  ExternalLink
} from 'lucide-react'
import { useContestSubmissions, useContestVoting } from '../../hooks/useContests'
import { ContestStatusCard } from './ContestStatusCard'
import type { Contest, ContestSubmission } from '../../types/contests'

interface ContestSubmissionGalleryProps {
  contest: Contest
  className?: string
}

type SortOption = 'newest' | 'oldest' | 'popular' | 'rating' | 'random'
type ViewMode = 'grid' | 'list'

export const ContestSubmissionGallery: React.FC<ContestSubmissionGalleryProps> = ({
  contest,
  className = ''
}) => {
  const { submissions, loading } = useContestSubmissions(contest.id)
  const [selectedSubmission, setSelectedSubmission] = useState<ContestSubmission | null>(null)
  const [sortBy, setSortBy] = useState<SortOption>('newest')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  // Filter and sort submissions
  const filteredSubmissions = useMemo(() => {
    let filtered = submissions.filter(submission => 
      submission.status === 'approved' // Only show approved submissions
    )

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(submission =>
        submission.title.toLowerCase().includes(query) ||
        submission.description.toLowerCase().includes(query) ||
        submission.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    // Apply tag filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(submission =>
        selectedTags.some(tag => submission.tags.includes(tag))
      )
    }

    // Sort submissions
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => b.submittedAt.toMillis() - a.submittedAt.toMillis())
        break
      case 'oldest':
        filtered.sort((a, b) => a.submittedAt.toMillis() - b.submittedAt.toMillis())
        break
      case 'popular':
        filtered.sort((a, b) => (b.views + b.likes) - (a.views + a.likes))
        break
      case 'rating':
        filtered.sort((a, b) => b.scores.final - a.scores.final)
        break
      case 'random':
        filtered.sort(() => Math.random() - 0.5)
        break
    }

    return filtered
  }, [submissions, searchQuery, selectedTags, sortBy])

  // Get all unique tags
  const allTags = useMemo(() => {
    const tags = new Set<string>()
    submissions.forEach(submission => {
      submission.tags.forEach(tag => tags.add(tag))
    })
    return Array.from(tags).sort()
  }, [submissions])

  /**
   * Toggle tag filter
   */
  const toggleTag = useCallback((tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }, [])

  /**
   * Open submission modal
   */
  const openSubmission = useCallback((submission: ContestSubmission) => {
    setSelectedSubmission(submission)
    // Track view
    // TODO: Implement view tracking
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Contest Status */}
      <ContestStatusCard contest={contest} showDetails={true} />

      {/* Gallery Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h2 className="text-xl font-bold text-white">
            Submissions ({filteredSubmissions.length})
          </h2>
          <p className="text-gray-400 mt-1">
            Browse creative entries from the community
          </p>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search submissions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-500 focus:outline-none"
            />
          </div>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortOption)}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-accent-500 focus:outline-none"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="popular">Most Popular</option>
            <option value="rating">Highest Rated</option>
            <option value="random">Random</option>
          </select>

          {/* View Mode */}
          <div className="flex bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' ? 'bg-accent-500 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' ? 'bg-accent-500 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>

          {/* Filters */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters ? 'bg-accent-500 text-white' : 'bg-gray-800 text-gray-400 hover:text-white'
            }`}
          >
            <Filter className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
          >
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-2">Filter by Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {allTags.map((tag) => (
                    <button
                      key={tag}
                      onClick={() => toggleTag(tag)}
                      className={`px-3 py-1 text-sm rounded-full transition-colors ${
                        selectedTags.includes(tag)
                          ? 'bg-accent-500 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>

              {selectedTags.length > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-400">Active filters:</span>
                  {selectedTags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 bg-accent-500/20 text-accent-400 rounded-full text-xs"
                    >
                      {tag}
                      <button
                        onClick={() => toggleTag(tag)}
                        className="ml-1 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                  <button
                    onClick={() => setSelectedTags([])}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear all
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Submissions Grid/List */}
      {filteredSubmissions.length === 0 ? (
        <div className="text-center py-12">
          <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No submissions found</h3>
          <p className="text-gray-400">
            {searchQuery || selectedTags.length > 0 
              ? 'Try adjusting your search or filters'
              : 'Be the first to submit to this contest!'
            }
          </p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {filteredSubmissions.map((submission) => (
            <SubmissionCard
              key={submission.id}
              submission={submission}
              contest={contest}
              viewMode={viewMode}
              onClick={() => openSubmission(submission)}
            />
          ))}
        </div>
      )}

      {/* Submission Detail Modal */}
      {selectedSubmission && (
        <SubmissionModal
          submission={selectedSubmission}
          contest={contest}
          onClose={() => setSelectedSubmission(null)}
        />
      )}
    </div>
  )
}

/**
 * Individual Submission Card Component
 */
interface SubmissionCardProps {
  submission: ContestSubmission
  contest: Contest
  viewMode: ViewMode
  onClick: () => void
}

const SubmissionCard: React.FC<SubmissionCardProps> = ({
  submission,
  contest,
  viewMode,
  onClick
}) => {
  const { userVote, canVote, vote } = useContestVoting(contest.id, submission.id)

  const handleVote = useCallback(async (rating: number) => {
    if (canVote) {
      await vote({ type: 'community', rating, lastModified: Timestamp.now() })
    }
  }, [canVote, vote])

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-accent-500/30 transition-colors cursor-pointer"
        onClick={onClick}
      >
        {/* Thumbnail */}
        <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-700 flex-shrink-0">
          {submission.thumbnails?.[0] ? (
            <img
              src={submission.thumbnails[0]}
              alt={submission.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Trophy className="w-8 h-8 text-gray-500" />
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-white truncate">{submission.title}</h3>
          <p className="text-gray-400 text-sm line-clamp-2 mt-1">{submission.description}</p>
          
          {/* Stats */}
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
            <span className="flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              {submission.views}
            </span>
            <span className="flex items-center">
              <Heart className="w-4 h-4 mr-1" />
              {submission.likes}
            </span>
            <span className="flex items-center">
              <Star className="w-4 h-4 mr-1" />
              {submission.scores.final.toFixed(1)}
            </span>
          </div>
        </div>

        {/* Quick Vote */}
        {canVote && contest.status === 'voting' && (
          <div className="flex items-center space-x-1">
            {[1, 2, 3, 4, 5].map((rating) => (
              <button
                key={rating}
                onClick={(e) => {
                  e.stopPropagation()
                  handleVote(rating)
                }}
                className={`p-1 rounded transition-colors ${
                  userVote && userVote.rating >= rating
                    ? 'text-yellow-400'
                    : 'text-gray-600 hover:text-yellow-400'
                }`}
              >
                <Star className="w-4 h-4" />
              </button>
            ))}
          </div>
        )}
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-accent-500/30 transition-colors cursor-pointer overflow-hidden group"
      onClick={onClick}
    >
      {/* Image */}
      <div className="aspect-square bg-gray-700 relative overflow-hidden">
        {submission.thumbnails?.[0] ? (
          <img
            src={submission.thumbnails[0]}
            alt={submission.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Trophy className="w-12 h-12 text-gray-500" />
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors" />
        
        {/* Quick Actions */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex items-center space-x-1 bg-black/50 rounded-lg p-1">
            <span className="text-white text-xs px-2">{submission.scores.final.toFixed(1)}</span>
            <Star className="w-3 h-3 text-yellow-400" />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className="text-white font-medium truncate">{submission.title}</h3>
        <p className="text-gray-400 text-sm line-clamp-2 mt-1">{submission.description}</p>
        
        {/* Tags */}
        {submission.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {submission.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded-full"
              >
                {tag}
              </span>
            ))}
            {submission.tags.length > 3 && (
              <span className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded-full">
                +{submission.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Stats */}
        <div className="flex items-center justify-between mt-3 text-sm text-gray-500">
          <div className="flex items-center space-x-3">
            <span className="flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              {submission.views}
            </span>
            <span className="flex items-center">
              <Heart className="w-4 h-4 mr-1" />
              {submission.likes}
            </span>
          </div>
          <span className="text-xs">
            {submission.submittedAt.toDate().toLocaleDateString()}
          </span>
        </div>
      </div>
    </motion.div>
  )
}

/**
 * Submission Detail Modal Component
 */
interface SubmissionModalProps {
  submission: ContestSubmission
  contest: Contest
  onClose: () => void
}

const SubmissionModal: React.FC<SubmissionModalProps> = ({
  submission,
  contest,
  onClose
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const { userVote, canVote, vote, voteStats } = useContestVoting(contest.id, submission.id)

  const handleVote = useCallback(async (rating: number) => {
    if (canVote) {
      await vote({ type: 'community', rating, lastModified: Timestamp.now() })
    }
  }, [canVote, vote])

  const nextImage = useCallback(() => {
    setCurrentImageIndex(prev => 
      prev < submission.images.length - 1 ? prev + 1 : 0
    )
  }, [submission.images.length])

  const prevImage = useCallback(() => {
    setCurrentImageIndex(prev => 
      prev > 0 ? prev - 1 : submission.images.length - 1
    )
  }, [submission.images.length])

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-white">{submission.title}</h2>
            <p className="text-gray-400 text-sm mt-1">
              Submitted {submission.submittedAt.toDate().toLocaleDateString()}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Image Gallery */}
          <div className="relative">
            <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
              <img
                src={submission.images[currentImageIndex]}
                alt={`${submission.title} - Image ${currentImageIndex + 1}`}
                className="w-full h-full object-contain"
              />
            </div>
            
            {submission.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
                
                {/* Image indicators */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {submission.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </>
            )}
          </div>

          {/* Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Description */}
            <div className="lg:col-span-2 space-y-4">
              <div>
                <h3 className="text-lg font-medium text-white mb-2">Description</h3>
                <p className="text-gray-300 leading-relaxed">{submission.description}</p>
              </div>

              {/* Tags */}
              {submission.tags.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-white mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {submission.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-4">
              {/* Stats */}
              <div className="bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-white mb-3">Statistics</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Views</span>
                    <span className="text-white">{submission.views}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Likes</span>
                    <span className="text-white">{submission.likes}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Score</span>
                    <span className="text-white">{submission.scores.final.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Votes</span>
                    <span className="text-white">{voteStats.total}</span>
                  </div>
                </div>
              </div>

              {/* Voting */}
              {contest.status === 'voting' && (
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-3">Rate This Submission</h3>
                  {canVote ? (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-1">
                        {[1, 2, 3, 4, 5].map((rating) => (
                          <button
                            key={rating}
                            onClick={() => handleVote(rating)}
                            className={`p-1 rounded transition-colors ${
                              userVote && userVote.rating >= rating
                                ? 'text-yellow-400'
                                : 'text-gray-600 hover:text-yellow-400'
                            }`}
                          >
                            <Star className="w-6 h-6" />
                          </button>
                        ))}
                      </div>
                      {userVote && (
                        <p className="text-sm text-green-400">
                          You rated this {userVote.rating} star{userVote.rating !== 1 ? 's' : ''}
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-400">
                      {userVote ? 'You have already voted' : 'Login to vote'}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default ContestSubmissionGallery
