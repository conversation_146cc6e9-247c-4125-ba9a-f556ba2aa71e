/**
 * User Submission Manager Component
 *
 * Interface for users to manage their contest submissions including
 * viewing, editing, deleting, and tracking submission status.
 *
 * Features:
 * - Personal submission dashboard
 * - Edit/delete submission controls
 * - Status tracking and notifications
 * - Draft management
 * - Submission analytics
 * - Contest deadline reminders
 *
 * @component
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Edit, 
  Trash2, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Plus,
  Calendar,
  Trophy,
  Star,
  MessageSquare,
  BarChart3,
  FileText,
  Upload
} from 'lucide-react'
import { useUser } from '../../lib/useUser'
import { useContestSubmission } from '../../hooks/useContests'
import { ContestSubmissionForm } from './ContestSubmissionForm'
import { ContestStatusCard } from './ContestStatusCard'
import type { Contest, ContestSubmission } from '../../types/contests'

interface UserSubmissionManagerProps {
  contest: Contest
  userSubmissions: ContestSubmission[]
  onSubmissionUpdate?: () => void
  className?: string
}

export const UserSubmissionManager: React.FC<UserSubmissionManagerProps> = ({
  contest,
  userSubmissions,
  onSubmissionUpdate,
  className = ''
}) => {
  const { user } = useUser()
  const [showSubmissionForm, setShowSubmissionForm] = useState(false)
  const [editingSubmission, setEditingSubmission] = useState<ContestSubmission | null>(null)
  const [selectedSubmission, setSelectedSubmission] = useState<ContestSubmission | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null)

  // Check if user can submit more entries
  const canSubmitMore = useMemo(() => {
    if (!user) return false
    const userSubmissionCount = userSubmissions.filter(s => s.status !== 'rejected').length
    return userSubmissionCount < contest.requirements.maxSubmissions && 
           contest.status === 'active'
  }, [user, userSubmissions, contest])

  // Get submission statistics
  const submissionStats = useMemo(() => {
    const stats = {
      total: userSubmissions.length,
      draft: 0,
      submitted: 0,
      approved: 0,
      rejected: 0,
      totalViews: 0,
      totalLikes: 0,
      averageScore: 0
    }

    userSubmissions.forEach(submission => {
      switch (submission.status) {
        case 'draft':
          stats.draft++
          break
        case 'submitted':
        case 'under_review':
          stats.submitted++
          break
        case 'approved':
          stats.approved++
          stats.totalViews += submission.views
          stats.totalLikes += submission.likes
          break
        case 'rejected':
          stats.rejected++
          break
      }
    })

    const approvedSubmissions = userSubmissions.filter(s => s.status === 'approved')
    if (approvedSubmissions.length > 0) {
      stats.averageScore = approvedSubmissions.reduce((sum, s) => sum + s.scores.final, 0) / approvedSubmissions.length
    }

    return stats
  }, [userSubmissions])

  /**
   * Handle submission deletion
   */
  const handleDeleteSubmission = useCallback(async (submissionId: string) => {
    try {
      // Import API function dynamically
      const { deleteContestSubmission } = await import('../../lib/api/contests')
      await deleteContestSubmission(submissionId)
      
      onSubmissionUpdate?.()
      setShowDeleteConfirm(null)
    } catch (error) {
      console.error('Failed to delete submission:', error)
    }
  }, [onSubmissionUpdate])

  /**
   * Handle submission form completion
   */
  const handleSubmissionComplete = useCallback((submissionId: string) => {
    setShowSubmissionForm(false)
    setEditingSubmission(null)
    onSubmissionUpdate?.()
  }, [onSubmissionUpdate])

  /**
   * Get status color and icon
   */
  const getStatusDisplay = (status: ContestSubmission['status']) => {
    switch (status) {
      case 'draft':
        return { color: 'text-gray-400 bg-gray-500/20', icon: FileText, label: 'Draft' }
      case 'submitted':
      case 'under_review':
        return { color: 'text-yellow-400 bg-yellow-500/20', icon: Clock, label: 'Under Review' }
      case 'approved':
        return { color: 'text-green-400 bg-green-500/20', icon: CheckCircle, label: 'Approved' }
      case 'rejected':
        return { color: 'text-red-400 bg-red-500/20', icon: XCircle, label: 'Rejected' }
      default:
        return { color: 'text-gray-400 bg-gray-500/20', icon: FileText, label: 'Unknown' }
    }
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">Login Required</h3>
        <p className="text-gray-400">
          You must be logged in to manage your submissions
        </p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Contest Status */}
      <ContestStatusCard contest={contest} showDetails={true} />

      {/* User Stats */}
      <div className="bg-gray-900 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-medium text-white mb-4">Your Submission Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-white">{submissionStats.total}</p>
            <p className="text-sm text-gray-400">Total Submissions</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-400">{submissionStats.approved}</p>
            <p className="text-sm text-gray-400">Approved</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-400">{submissionStats.totalViews}</p>
            <p className="text-sm text-gray-400">Total Views</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-accent-400">
              {submissionStats.averageScore.toFixed(1)}
            </p>
            <p className="text-sm text-gray-400">Avg Score</p>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-white">Your Submissions</h2>
          <p className="text-gray-400 mt-1">
            {userSubmissions.length} of {contest.requirements.maxSubmissions} submissions used
          </p>
        </div>
        
        {canSubmitMore && (
          <button
            onClick={() => setShowSubmissionForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>New Submission</span>
          </button>
        )}
      </div>

      {/* Submissions List */}
      {userSubmissions.length === 0 ? (
        <div className="text-center py-12 bg-gray-900 rounded-lg border border-gray-700">
          <Upload className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No submissions yet</h3>
          <p className="text-gray-400 mb-6">
            Create your first submission to participate in this contest
          </p>
          {canSubmitMore && (
            <button
              onClick={() => setShowSubmissionForm(true)}
              className="px-6 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors"
            >
              Create Submission
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {userSubmissions.map((submission) => {
            const statusDisplay = getStatusDisplay(submission.status)
            const StatusIcon = statusDisplay.icon

            return (
              <motion.div
                key={submission.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-900 rounded-lg border border-gray-700 p-6 hover:border-accent-500/30 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-white truncate">
                        {submission.title}
                      </h3>
                      <span className={`
                        inline-flex items-center px-2 py-1 text-xs font-medium rounded-full
                        ${statusDisplay.color}
                      `}>
                        <StatusIcon className="w-3 h-3 mr-1" />
                        {statusDisplay.label}
                      </span>
                    </div>

                    {/* Description */}
                    <p className="text-gray-400 text-sm line-clamp-2 mb-3">
                      {submission.description}
                    </p>

                    {/* Meta Info */}
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {submission.submittedAt.toDate().toLocaleDateString()}
                      </span>
                      {submission.status === 'approved' && (
                        <>
                          <span className="flex items-center">
                            <Eye className="w-4 h-4 mr-1" />
                            {submission.views} views
                          </span>
                          <span className="flex items-center">
                            <Star className="w-4 h-4 mr-1" />
                            {submission.scores.final.toFixed(1)} score
                          </span>
                        </>
                      )}
                    </div>

                    {/* Moderation Notes */}
                    {submission.moderationNotes && (
                      <div className="mt-3 p-2 bg-yellow-900/20 border border-yellow-500/30 rounded">
                        <p className="text-sm text-yellow-400">
                          <MessageSquare className="w-4 h-4 inline mr-1" />
                          {submission.moderationNotes}
                        </p>
                      </div>
                    )}

                    {/* Tags */}
                    {submission.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3">
                        {submission.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => setSelectedSubmission(submission)}
                      className="p-2 text-gray-400 hover:text-white transition-colors"
                      title="View Details"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    
                    {(submission.status === 'draft' || submission.status === 'rejected') && (
                      <button
                        onClick={() => setEditingSubmission(submission)}
                        className="p-2 text-gray-400 hover:text-accent-400 transition-colors"
                        title="Edit Submission"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                    )}
                    
                    {submission.status === 'draft' && (
                      <button
                        onClick={() => setShowDeleteConfirm(submission.id)}
                        className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                        title="Delete Submission"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      )}

      {/* Submission Form Modal */}
      {(showSubmissionForm || editingSubmission) && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <ContestSubmissionForm
              contest={contest}
              existingSubmission={editingSubmission}
              onSubmit={handleSubmissionComplete}
              onCancel={() => {
                setShowSubmissionForm(false)
                setEditingSubmission(null)
              }}
              className="p-6"
            />
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gray-900 rounded-lg p-6 max-w-md w-full"
          >
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-400" />
              <h3 className="text-lg font-medium text-white">Delete Submission</h3>
            </div>
            <p className="text-gray-400 mb-6">
              Are you sure you want to delete this submission? This action cannot be undone.
            </p>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteSubmission(showDeleteConfirm)}
                className="flex-1 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
              >
                Delete
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Submission Detail Modal */}
      {selectedSubmission && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gray-900 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-white">{selectedSubmission.title}</h2>
                <button
                  onClick={() => setSelectedSubmission(null)}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              {/* Images */}
              {selectedSubmission.images.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {selectedSubmission.images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`${selectedSubmission.title} - Image ${index + 1}`}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                  ))}
                </div>
              )}

              {/* Details */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium text-white mb-2">Description</h3>
                  <p className="text-gray-300">{selectedSubmission.description}</p>
                </div>

                {selectedSubmission.tags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedSubmission.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-sm"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {selectedSubmission.status === 'approved' && (
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Performance</h3>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-lg font-semibold text-white">{selectedSubmission.views}</p>
                        <p className="text-sm text-gray-400">Views</p>
                      </div>
                      <div>
                        <p className="text-lg font-semibold text-white">{selectedSubmission.likes}</p>
                        <p className="text-sm text-gray-400">Likes</p>
                      </div>
                      <div>
                        <p className="text-lg font-semibold text-white">
                          {selectedSubmission.scores.final.toFixed(1)}
                        </p>
                        <p className="text-sm text-gray-400">Score</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default UserSubmissionManager
