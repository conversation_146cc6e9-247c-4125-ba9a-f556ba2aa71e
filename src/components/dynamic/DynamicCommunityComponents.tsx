/**
 * Dynamic Community Components
 * 
 * Dynamically loaded community components to optimize bundle size.
 * These components are loaded only when community features are accessed.
 * 
 * Phase 2 Performance Optimization - Community System Audit
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { ComponentType } from 'react'
import dynamic from 'next/dynamic'
import { motion } from 'framer-motion'
import { Users, Trophy, Heart, MessageCircle } from 'lucide-react'

// Community Loading Fallback Component
const CommunityLoadingFallback: React.FC<{ title: string }> = ({ title }) => (
  <div className="flex items-center justify-center min-h-[400px] p-8">
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-4"
    >
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        className="w-12 h-12 mx-auto"
      >
        <Users className="w-full h-full text-accent-400" />
      </motion.div>
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-white">{title}</h3>
        <p className="text-sm text-gray-400">Initializing community features...</p>
      </div>
      <div className="flex justify-center space-x-1">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity, delay: i * 0.2 }}
            className="w-2 h-2 bg-accent-400 rounded-full"
          />
        ))}
      </div>
    </motion.div>
  </div>
)

// ===== CORE COMMUNITY COMPONENTS =====

export const DynamicCommunityAchievementSystem = dynamic(
  () => import('@/components/achievements/CommunityAchievementSystem'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Achievement System" />,
    ssr: false
  }
);

export const DynamicLeaderboardTable = dynamic(
  () => import('@/components/community/LeaderboardTable'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Leaderboard" />,
    ssr: false
  }
);

export const DynamicCommunityProfile = dynamic(
  () => import('@/components/community/CommunityProfile'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Community Profile" />,
    ssr: false
  }
);

export const DynamicCommunityFeed = dynamic(
  () => import('@/components/community/CommunityFeed'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Community Feed" />,
    ssr: false
  }
);

export const DynamicDiscussionThread = dynamic(
  () => import('@/components/community/DiscussionThread'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Discussion" />,
    ssr: false
  }
);

export const DynamicUserSubmissions = dynamic(
  () => import('@/components/community/UserSubmissions'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Submissions" />,
    ssr: false
  }
);

// ===== COMMUNITY FEATURES =====

export const DynamicCommunityVoting = dynamic(
  () => import('@/components/community/voting/CommunityVoting'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Voting System" />,
    ssr: false
  }
);

export const DynamicCommunityChallenge = dynamic(
  () => import('@/components/community/challenges/CommunityChallenge'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Challenge System" />,
    ssr: false
  }
);

export const DynamicCommunitySpotlight = dynamic(
  () => import('@/components/community/spotlight/CommunitySpotlight'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Community Spotlight" />,
    ssr: false
  }
);

export const DynamicCommunityAnalytics = dynamic(
  () => import('@/components/community/analytics/CommunityAnalytics'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Community Analytics" />,
    ssr: false
  }
);

// ===== COMMUNITY TABS =====

export const DynamicCommunityTabs = dynamic(
  () => import('@/components/community/tabs/CommunityTabs'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Community Navigation" />,
    ssr: false
  }
);

export const DynamicCommunityDiscussions = dynamic(
  () => import('@/components/community/tabs/CommunityDiscussions'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Discussions" />,
    ssr: false
  }
);

export const DynamicCommunitySubmissions = dynamic(
  () => import('@/components/community/tabs/CommunitySubmissions'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Submissions" />,
    ssr: false
  }
);

export const DynamicCommunityChallenges = dynamic(
  () => import('@/components/community/tabs/CommunityChallenges'),
  {
    loading: () => <CommunityLoadingFallback title="Loading Challenges" />,
    ssr: false
  }
);

// ===== UTILITY FUNCTIONS =====

// Export type for better TypeScript support
export type DynamicCommunityComponent = ComponentType<any>;

// Helper function to create dynamic community components
export function createDynamicCommunityComponent(
  importFn: () => Promise<{ default: ComponentType<any> }>,
  loadingTitle: string = 'Loading Community Feature'
) {
  return dynamic(importFn, {
    loading: () => <CommunityLoadingFallback title={loadingTitle} />,
    ssr: false
  });
}

// Preload functions for better UX
export const preloadCommunityComponents = {
  achievements: () => import('@/components/achievements/CommunityAchievementSystem'),
  leaderboard: () => import('@/components/community/LeaderboardTable'),
  profile: () => import('@/components/community/CommunityProfile'),
  feed: () => import('@/components/community/CommunityFeed'),
  discussions: () => import('@/components/community/DiscussionThread'),
  submissions: () => import('@/components/community/UserSubmissions'),
  voting: () => import('@/components/community/voting/CommunityVoting'),
  challenges: () => import('@/components/community/challenges/CommunityChallenge'),
  spotlight: () => import('@/components/community/spotlight/CommunitySpotlight'),
  analytics: () => import('@/components/community/analytics/CommunityAnalytics'),
  tabs: () => import('@/components/community/tabs/CommunityTabs'),
};

// Preload community components on user interaction
export function preloadCommunityComponent(componentName: keyof typeof preloadCommunityComponents) {
  if (typeof window !== 'undefined') {
    preloadCommunityComponents[componentName]().catch(() => {
      // Silently fail if preload fails
    });
  }
}

// Conditional loading based on user engagement
export function shouldLoadCommunityFeatures(user: any): boolean {
  if (!user) return false;
  
  // Load community features if user has community activity
  return (
    user.communityLevel > 0 ||
    user.achievements?.length > 0 ||
    user.communityPoints > 0 ||
    user.hasPostedInCommunity
  );
}

// Preload community components based on user behavior
export function preloadBasedOnUserActivity(user: any) {
  if (!shouldLoadCommunityFeatures(user)) return;
  
  // Preload core community features
  setTimeout(() => {
    preloadCommunityComponent('achievements');
    preloadCommunityComponent('leaderboard');
  }, 2000);
  
  // Preload additional features based on user activity
  if (user.communityLevel > 5) {
    setTimeout(() => {
      preloadCommunityComponent('challenges');
      preloadCommunityComponent('voting');
    }, 4000);
  }
}

// Bundle splitting helper
export const CommunityFeatureBundle = {
  Core: [
    'achievements',
    'leaderboard', 
    'profile'
  ] as const,
  
  Social: [
    'feed',
    'discussions',
    'submissions'
  ] as const,
  
  Advanced: [
    'voting',
    'challenges',
    'spotlight',
    'analytics'
  ] as const
};

// Load community bundle based on feature set
export function loadCommunityBundle(bundleType: keyof typeof CommunityFeatureBundle) {
  const features = CommunityFeatureBundle[bundleType];
  features.forEach(feature => {
    preloadCommunityComponent(feature);
  });
}

export default {
  DynamicCommunityAchievementSystem,
  DynamicLeaderboardTable,
  DynamicCommunityProfile,
  DynamicCommunityFeed,
  DynamicDiscussionThread,
  DynamicUserSubmissions,
  DynamicCommunityVoting,
  DynamicCommunityChallenge,
  DynamicCommunitySpotlight,
  DynamicCommunityAnalytics,
  DynamicCommunityTabs,
  DynamicCommunityDiscussions,
  DynamicCommunitySubmissions,
  DynamicCommunityChallenges,
  preloadCommunityComponents,
  preloadCommunityComponent,
  shouldLoadCommunityFeatures,
  preloadBasedOnUserActivity,
  loadCommunityBundle
};
