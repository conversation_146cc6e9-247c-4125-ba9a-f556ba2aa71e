/**
 * Seasonal Events Container Component
 * 
 * Main orchestrator for the refactored seasonal events system
 * Manages tabs, data, and renders appropriate components
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar, 
  Trophy, 
  Users, 
  Target,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react'
import { toast } from 'react-hot-toast'

// Hooks
import { useSeasonalEvents } from './hooks/useSeasonalEvents'
import { useEventFilters } from './hooks/useEventFilters'
import { useEventActions } from './hooks/useEventActions'

// Components
import EventStatsOverview from './components/EventStatsOverview'
import EventGrid from './components/EventGrid'
import CampaignGrid from './components/CampaignGrid'
import EventCalendar from './components/EventCalendar'
import AchievementsView from './components/AchievementsView'

// Types
import { 
  SeasonalEventsContainerProps,
  EVENT_THEMES,
  EVENT_STATUSES
} from './types/seasonalEventTypes'

/**
 * Main seasonal events container component
 */
export const SeasonalEventsContainer: React.FC<SeasonalEventsContainerProps> = ({
  initialTab = 'events'
}) => {
  const [activeTab, setActiveTab] = useState(initialTab)
  const [selectedDate, setSelectedDate] = useState(new Date())

  // Data management
  const { events, campaigns, userStats, loading, error, refreshData } = useSeasonalEvents()
  const { filters, updateFilters, filteredEvents, resetFilters } = useEventFilters(events)
  const { joinEvent, joinCampaign, isJoining } = useEventActions()

  /**
   * Tab configuration
   */
  const tabs = [
    { id: 'events', label: 'Events', icon: Calendar, count: events.length },
    { id: 'campaigns', label: 'Campaigns', icon: Users, count: campaigns.length },
    { id: 'calendar', label: 'Calendar', icon: Target, count: null },
    { id: 'achievements', label: 'Achievements', icon: Trophy, count: userStats ? Object.values(userStats.achievements).filter(Boolean).length : 0 }
  ]

  /**
   * Handle tab change
   */
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId)
    console.log('[SeasonalEvents] Tab changed to:', tabId)
  }

  /**
   * Handle join event
   */
  const handleJoinEvent = async (eventId: string) => {
    const success = await joinEvent(eventId)
    if (success) {
      await refreshData() // Refresh data to show updated participation
    }
  }

  /**
   * Handle join campaign
   */
  const handleJoinCampaign = async (campaignId: string) => {
    const success = await joinCampaign(campaignId)
    if (success) {
      await refreshData() // Refresh data to show updated participation
    }
  }

  /**
   * Handle refresh
   */
  const handleRefresh = async () => {
    await refreshData()
    toast.success('Data refreshed successfully!')
  }

  /**
   * Render tab content
   */
  const renderTabContent = () => {
    switch (activeTab) {
      case 'events':
        return (
          <div className="space-y-6">
            {/* Filters */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div className="relative">
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search events..."
                    value={filters.searchQuery}
                    onChange={(e) => updateFilters({ searchQuery: e.target.value })}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>

                {/* Theme Filter */}
                <select
                  value={filters.selectedTheme}
                  onChange={(e) => updateFilters({ selectedTheme: e.target.value })}
                  className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {EVENT_THEMES.map(theme => (
                    <option key={theme.value} value={theme.value}>
                      {theme.icon} {theme.label}
                    </option>
                  ))}
                </select>

                {/* Status Filter */}
                <select
                  value={filters.selectedStatus}
                  onChange={(e) => updateFilters({ selectedStatus: e.target.value })}
                  className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {EVENT_STATUSES.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>

                {/* Participating Only */}
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.showParticipatingOnly}
                    onChange={(e) => updateFilters({ showParticipatingOnly: e.target.checked })}
                    className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                  />
                  <span className="text-white text-sm">My Events Only</span>
                </label>
              </div>

              {/* Filter Actions */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-400">
                  Showing {filteredEvents.length} of {events.length} events
                </div>
                <button
                  onClick={resetFilters}
                  className="flex items-center gap-2 px-3 py-1 text-gray-400 hover:text-white transition-colors"
                >
                  <Filter size={14} />
                  Clear Filters
                </button>
              </div>
            </div>

            {/* Events Grid */}
            <EventGrid
              events={filteredEvents}
              loading={loading}
              onJoinEvent={handleJoinEvent}
            />
          </div>
        )

      case 'campaigns':
        return (
          <CampaignGrid
            campaigns={campaigns}
            loading={loading}
            onJoinCampaign={handleJoinCampaign}
          />
        )

      case 'calendar':
        return (
          <EventCalendar
            events={events}
            campaigns={campaigns}
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
        )

      case 'achievements':
        return userStats ? (
          <AchievementsView
            stats={userStats}
            events={events}
            campaigns={campaigns}
          />
        ) : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📊</div>
            <p className="text-gray-400">Loading achievements...</p>
          </div>
        )

      default:
        return null
    }
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">⚠️</div>
        <h3 className="text-xl font-semibold text-white mb-2">Something went wrong</h3>
        <p className="text-gray-400 mb-6">{error}</p>
        <button
          onClick={handleRefresh}
          className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Seasonal Events</h1>
          <p className="text-gray-400">Join community events and campaigns</p>
        </div>
        
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 text-white rounded-lg transition-colors"
        >
          <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
          Refresh
        </button>
      </div>

      {/* Stats Overview */}
      {userStats && (
        <EventStatsOverview stats={userStats} />
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <tab.icon size={16} />
              {tab.label}
              {tab.count !== null && (
                <span className={`px-2 py-1 rounded-full text-xs ${
                  activeTab === tab.id
                    ? 'bg-purple-500/20 text-purple-400'
                    : 'bg-gray-700 text-gray-400'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {renderTabContent()}
      </motion.div>
    </div>
  )
}

export default SeasonalEventsContainer
