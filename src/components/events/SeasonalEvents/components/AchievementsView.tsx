/**
 * Achievements View Component
 * 
 * Displays user achievements, progress, and milestones for seasonal events
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Trophy, 
  Star, 
  Award, 
  Target, 
  Flame,
  Calendar,
  Users,
  TrendingUp,
  Lock,
  CheckCircle
} from 'lucide-react'
import { AchievementsViewProps, EVENT_THEMES } from '../types/seasonalEventTypes'

/**
 * Achievements view component
 */
export const AchievementsView: React.FC<AchievementsViewProps> = ({
  stats,
  events,
  campaigns
}) => {
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'events' | 'campaigns' | 'social'>('all')

  /**
   * Achievement categories
   */
  const achievementCategories = [
    { id: 'all', label: 'All Achievements', icon: Trophy },
    { id: 'events', label: 'Event Achievements', icon: Calendar },
    { id: 'campaigns', label: 'Campaign Achievements', icon: Users },
    { id: 'social', label: 'Social Achievements', icon: Star }
  ]

  /**
   * All available achievements
   */
  const allAchievements = [
    // Event Achievements
    {
      id: 'first_event',
      title: 'First Steps',
      description: 'Join your first seasonal event',
      category: 'events',
      icon: Calendar,
      earned: stats.participatedEvents > 0,
      progress: Math.min(stats.participatedEvents, 1),
      target: 1,
      points: 50,
      rarity: 'common'
    },
    {
      id: 'event_master',
      title: 'Event Master',
      description: 'Complete 10 seasonal events',
      category: 'events',
      icon: Trophy,
      earned: stats.achievements.eventMaster,
      progress: stats.completedEvents,
      target: 10,
      points: 500,
      rarity: 'epic'
    },
    {
      id: 'seasonal_champion',
      title: 'Seasonal Champion',
      description: 'Win a seasonal contest',
      category: 'events',
      icon: Award,
      earned: stats.achievements.seasonalChampion,
      progress: stats.achievements.seasonalChampion ? 1 : 0,
      target: 1,
      points: 1000,
      rarity: 'legendary'
    },
    {
      id: 'streak_master',
      title: 'Streak Master',
      description: 'Maintain a 7-day event streak',
      category: 'events',
      icon: Flame,
      earned: stats.longestStreak >= 7,
      progress: stats.currentStreak,
      target: 7,
      points: 300,
      rarity: 'rare'
    },
    
    // Campaign Achievements
    {
      id: 'campaign_hero',
      title: 'Campaign Hero',
      description: 'Lead a community campaign',
      category: 'campaigns',
      icon: Users,
      earned: stats.achievements.campaignHero,
      progress: stats.achievements.campaignHero ? 1 : 0,
      target: 1,
      points: 750,
      rarity: 'epic'
    },
    {
      id: 'community_builder',
      title: 'Community Builder',
      description: 'Help 50+ community members',
      category: 'campaigns',
      icon: Star,
      earned: stats.achievements.communityBuilder,
      progress: stats.achievements.communityBuilder ? 50 : 25, // Mock progress
      target: 50,
      points: 400,
      rarity: 'rare'
    },
    
    // Social Achievements
    {
      id: 'social_butterfly',
      title: 'Social Butterfly',
      description: 'Share 5 events on social media',
      category: 'social',
      icon: Star,
      earned: false, // Mock
      progress: 2, // Mock
      target: 5,
      points: 200,
      rarity: 'common'
    },
    {
      id: 'influencer',
      title: 'Community Influencer',
      description: 'Get 100 likes on event posts',
      category: 'social',
      icon: TrendingUp,
      earned: false, // Mock
      progress: 45, // Mock
      target: 100,
      points: 600,
      rarity: 'epic'
    }
  ]

  /**
   * Filter achievements by category
   */
  const filteredAchievements = selectedCategory === 'all' 
    ? allAchievements 
    : allAchievements.filter(achievement => achievement.category === selectedCategory)

  /**
   * Get rarity color
   */
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400 border-gray-500'
      case 'rare': return 'text-blue-400 border-blue-500'
      case 'epic': return 'text-purple-400 border-purple-500'
      case 'legendary': return 'text-yellow-400 border-yellow-500'
      default: return 'text-gray-400 border-gray-500'
    }
  }

  /**
   * Achievement card component
   */
  const AchievementCard: React.FC<{
    achievement: any
    index: number
  }> = ({ achievement, index }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className={`p-4 rounded-lg border transition-all ${
        achievement.earned
          ? `bg-gradient-to-br from-${achievement.rarity === 'legendary' ? 'yellow' : achievement.rarity === 'epic' ? 'purple' : achievement.rarity === 'rare' ? 'blue' : 'gray'}-500/20 to-transparent ${getRarityColor(achievement.rarity)}`
          : 'bg-gray-800 border-gray-700'
      }`}
    >
      <div className="flex items-start gap-4">
        {/* Icon */}
        <div className={`p-3 rounded-lg ${
          achievement.earned
            ? achievement.rarity === 'legendary' ? 'bg-yellow-500/20' :
              achievement.rarity === 'epic' ? 'bg-purple-500/20' :
              achievement.rarity === 'rare' ? 'bg-blue-500/20' :
              'bg-gray-500/20'
            : 'bg-gray-700'
        }`}>
          {achievement.earned ? (
            <achievement.icon size={24} className={
              achievement.rarity === 'legendary' ? 'text-yellow-400' :
              achievement.rarity === 'epic' ? 'text-purple-400' :
              achievement.rarity === 'rare' ? 'text-blue-400' :
              'text-gray-400'
            } />
          ) : (
            <Lock size={24} className="text-gray-500" />
          )}
        </div>

        {/* Content */}
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h3 className={`font-semibold ${
              achievement.earned ? 'text-white' : 'text-gray-400'
            }`}>
              {achievement.title}
            </h3>
            {achievement.earned && (
              <CheckCircle size={16} className="text-green-400" />
            )}
          </div>
          
          <p className={`text-sm mb-3 ${
            achievement.earned ? 'text-gray-300' : 'text-gray-500'
          }`}>
            {achievement.description}
          </p>

          {/* Progress Bar */}
          <div className="mb-2">
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-400">Progress</span>
              <span className="text-gray-400">
                {achievement.progress} / {achievement.target}
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 ${
                  achievement.earned
                    ? achievement.rarity === 'legendary' ? 'bg-yellow-500' :
                      achievement.rarity === 'epic' ? 'bg-purple-500' :
                      achievement.rarity === 'rare' ? 'bg-blue-500' :
                      'bg-gray-500'
                    : 'bg-gray-600'
                }`}
                style={{ width: `${Math.min((achievement.progress / achievement.target) * 100, 100)}%` }}
              />
            </div>
          </div>

          {/* Reward */}
          <div className="flex items-center justify-between">
            <span className={`text-xs px-2 py-1 rounded ${getRarityColor(achievement.rarity)} bg-opacity-20`}>
              {achievement.rarity.toUpperCase()}
            </span>
            <span className="text-sm font-medium text-yellow-400">
              +{achievement.points} pts
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  )

  /**
   * Calculate achievement stats
   */
  const earnedCount = allAchievements.filter(a => a.earned).length
  const totalCount = allAchievements.length
  const completionRate = Math.round((earnedCount / totalCount) * 100)

  return (
    <div className="space-y-6">
      {/* Achievement Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
          <div className="flex items-center gap-3">
            <Trophy size={20} className="text-yellow-400" />
            <div>
              <div className="text-2xl font-bold text-white">{earnedCount}</div>
              <div className="text-sm text-gray-400">Earned</div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
          <div className="flex items-center gap-3">
            <Target size={20} className="text-blue-400" />
            <div>
              <div className="text-2xl font-bold text-white">{totalCount}</div>
              <div className="text-sm text-gray-400">Total</div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
          <div className="flex items-center gap-3">
            <Star size={20} className="text-purple-400" />
            <div>
              <div className="text-2xl font-bold text-white">{completionRate}%</div>
              <div className="text-sm text-gray-400">Complete</div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
          <div className="flex items-center gap-3">
            <Flame size={20} className="text-orange-400" />
            <div>
              <div className="text-2xl font-bold text-white">{stats.currentStreak}</div>
              <div className="text-sm text-gray-400">Streak</div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {achievementCategories.map(category => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id as any)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
              selectedCategory === category.id
                ? 'bg-purple-600 text-white'
                : 'bg-gray-800 text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <category.icon size={16} />
            {category.label}
          </button>
        ))}
      </div>

      {/* Achievements Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredAchievements.map((achievement, index) => (
          <AchievementCard
            key={achievement.id}
            achievement={achievement}
            index={index}
          />
        ))}
      </div>

      {/* Achievement Tips */}
      <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg border border-purple-500/50 p-6">
        <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
          <Star size={20} className="text-yellow-400" />
          Achievement Tips
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="text-purple-400 font-medium mb-2">Quick Wins:</h4>
            <ul className="text-gray-300 space-y-1">
              <li>• Join your first event for instant points</li>
              <li>• Share events on social media</li>
              <li>• Complete daily activities</li>
              <li>• Participate in community discussions</li>
            </ul>
          </div>
          <div>
            <h4 className="text-blue-400 font-medium mb-2">Long-term Goals:</h4>
            <ul className="text-gray-300 space-y-1">
              <li>• Maintain consistent event participation</li>
              <li>• Build your community reputation</li>
              <li>• Lead or organize campaigns</li>
              <li>• Achieve legendary status</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AchievementsView
