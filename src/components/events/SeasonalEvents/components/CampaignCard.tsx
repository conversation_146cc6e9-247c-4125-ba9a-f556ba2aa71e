/**
 * Campaign Card Component
 * 
 * Individual campaign card with progress, impact metrics, and join functionality
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  Target, 
  TrendingUp, 
  Calendar,
  Heart,
  ChevronRight,
  Loader2
} from 'lucide-react'
import { CampaignCardProps, CAMPAIGN_TYPES } from '../types/seasonalEventTypes'

/**
 * Campaign card component
 */
export const CampaignCard: React.FC<CampaignCardProps> = ({
  campaign,
  onJoinCampaign,
  index
}) => {
  const [isJoining, setIsJoining] = useState(false)

  /**
   * Get campaign type data
   */
  const typeData = CAMPAIGN_TYPES.find(t => t.value === campaign.type)
  const typeIcon = typeData?.icon || '📢'

  /**
   * Get status color
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20 border-green-500'
      case 'planning': return 'text-blue-400 bg-blue-500/20 border-blue-500'
      case 'completed': return 'text-gray-400 bg-gray-500/20 border-gray-500'
      case 'paused': return 'text-orange-400 bg-orange-500/20 border-orange-500'
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500'
    }
  }

  /**
   * Calculate progress percentage
   */
  const progressPercentage = Math.min((campaign.goal.current / campaign.goal.target) * 100, 100)

  /**
   * Format date
   */
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  /**
   * Calculate days remaining
   */
  const getDaysRemaining = () => {
    const now = new Date()
    const diffTime = campaign.endDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, diffDays)
  }

  /**
   * Handle join campaign
   */
  const handleJoinCampaign = async () => {
    setIsJoining(true)
    try {
      await onJoinCampaign(campaign.id)
    } finally {
      setIsJoining(false)
    }
  }

  /**
   * Check if user can join
   */
  const canJoin = campaign.status === 'active'

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6 hover:border-gray-600 transition-all group"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-600/20 rounded-lg">
            <span className="text-xl">{typeIcon}</span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white group-hover:text-purple-400 transition-colors">
              {campaign.title}
            </h3>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(campaign.status)}`}>
              {campaign.status.toUpperCase()}
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      <p className="text-gray-400 text-sm mb-4 line-clamp-2">
        {campaign.description}
      </p>

      {/* Campaign Details */}
      <div className="space-y-3 mb-4">
        {/* Duration */}
        <div className="flex items-center gap-2 text-sm text-gray-400">
          <Calendar size={14} />
          <span>{formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}</span>
        </div>

        {/* Supporters */}
        <div className="flex items-center gap-2 text-sm text-gray-400">
          <Users size={14} />
          <span>{campaign.supporters.length} supporters</span>
        </div>

        {/* Days Remaining */}
        {campaign.status === 'active' && (
          <div className="flex items-center gap-2 text-sm text-orange-400">
            <Target size={14} />
            <span>{getDaysRemaining()} days remaining</span>
          </div>
        )}
      </div>

      {/* Goal Progress */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-white">Campaign Goal</span>
          <span className="text-sm text-gray-400">
            {campaign.goal.current.toLocaleString()} / {campaign.goal.target.toLocaleString()} {campaign.goal.unit}
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-3">
          <div
            className="bg-gradient-to-r from-purple-600 to-blue-600 h-3 rounded-full transition-all duration-500"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {progressPercentage.toFixed(1)}% complete
        </div>
      </div>

      {/* Impact Metrics */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-white mb-2 flex items-center gap-2">
          <TrendingUp size={14} />
          Impact Metrics
        </h4>
        <div className="grid grid-cols-2 gap-3">
          {campaign.impact.metrics.slice(0, 4).map((metric, idx) => (
            <div key={idx} className="text-center p-2 bg-gray-700/50 rounded">
              <div className="text-lg font-bold text-purple-400">
                {metric.value.toLocaleString()}
              </div>
              <div className="text-xs text-gray-400">
                {metric.label}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Activities Preview */}
      {campaign.activities.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-white mb-2">Available Activities</h4>
          <div className="space-y-1">
            {campaign.activities.slice(0, 2).map(activity => (
              <div key={activity.id} className="flex items-center justify-between text-sm">
                <span className="text-gray-400">{activity.title}</span>
                <span className="text-purple-400 font-medium">+{activity.reward.points} pts</span>
              </div>
            ))}
            {campaign.activities.length > 2 && (
              <div className="text-xs text-gray-500">
                +{campaign.activities.length - 2} more activities
              </div>
            )}
          </div>
        </div>
      )}

      {/* Recent Updates */}
      {campaign.updates.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-white mb-2">Latest Update</h4>
          <div className="p-3 bg-gray-700/50 rounded">
            <div className="text-sm text-white font-medium mb-1">
              {campaign.updates[0].title}
            </div>
            <div className="text-xs text-gray-400 line-clamp-2">
              {campaign.updates[0].content}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {formatDate(campaign.updates[0].publishedAt)}
            </div>
          </div>
        </div>
      )}

      {/* Supporter Levels */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-white mb-2">Top Supporters</h4>
        <div className="flex items-center gap-2">
          {campaign.supporters.slice(0, 3).map(supporter => (
            <div
              key={supporter.userId}
              className="flex items-center gap-1 px-2 py-1 bg-gray-700 rounded text-xs"
            >
              <div className="w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center">
                <span className="text-xs">👤</span>
              </div>
              <span className="text-gray-300">{supporter.userName.split(' ')[0]}</span>
              {supporter.level === 'champion' && <Heart size={10} className="text-red-400" />}
            </div>
          ))}
          {campaign.supporters.length > 3 && (
            <span className="text-xs text-gray-500">
              +{campaign.supporters.length - 3} more
            </span>
          )}
        </div>
      </div>

      {/* Action Button */}
      <button
        onClick={handleJoinCampaign}
        disabled={!canJoin || isJoining}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-all flex items-center justify-center gap-2 ${
          !canJoin
            ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
            : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white'
        }`}
      >
        {isJoining ? (
          <>
            <Loader2 size={16} className="animate-spin" />
            Joining...
          </>
        ) : campaign.status === 'completed' ? (
          'View Results'
        ) : campaign.status === 'paused' ? (
          'Campaign Paused'
        ) : (
          <>
            Join Campaign
            <ChevronRight size={16} />
          </>
        )}
      </button>
    </motion.div>
  )
}

export default CampaignCard
