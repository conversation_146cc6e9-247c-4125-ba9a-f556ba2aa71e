/**
 * Campaign Grid Component
 * 
 * Displays community campaigns in a responsive grid layout
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { CampaignGridProps } from '../types/seasonalEventTypes'
import CampaignCard from './CampaignCard'

/**
 * Campaign grid skeleton
 */
const CampaignGridSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    {Array.from({ length: 4 }, (_, index) => (
      <motion.div
        key={index}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
        className="bg-gray-800 rounded-lg border border-gray-700 p-6"
      >
        <div className="space-y-4">
          <div className="h-6 bg-gray-700 rounded animate-pulse" />
          <div className="space-y-2">
            <div className="h-4 bg-gray-700 rounded animate-pulse" />
            <div className="h-4 bg-gray-700 rounded w-3/4 animate-pulse" />
          </div>
          <div className="h-4 bg-gray-700 rounded w-1/2 animate-pulse" />
          <div className="h-2 bg-gray-700 rounded animate-pulse" />
          <div className="h-10 bg-gray-700 rounded animate-pulse" />
        </div>
      </motion.div>
    ))}
  </div>
)

/**
 * Campaign grid component
 */
export const CampaignGrid: React.FC<CampaignGridProps> = ({
  campaigns,
  loading,
  onJoinCampaign
}) => {
  if (loading) {
    return <CampaignGridSkeleton />
  }

  if (campaigns.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <div className="text-6xl mb-4">📢</div>
        <h3 className="text-xl font-semibold text-white mb-2">No Active Campaigns</h3>
        <p className="text-gray-400 mb-6">
          There are currently no active community campaigns. Check back soon for new initiatives!
        </p>
        <div className="text-sm text-gray-500">
          💡 Tip: Follow our social media for campaign announcements
        </div>
      </motion.div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {campaigns.map((campaign, index) => (
        <CampaignCard
          key={campaign.id}
          campaign={campaign}
          onJoinCampaign={onJoinCampaign}
          index={index}
        />
      ))}
    </div>
  )
}

export default CampaignGrid
