/**
 * Event Calendar Component
 * 
 * Calendar view for seasonal events and campaigns with date selection
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from 'lucide-react'
import { EventCalendarProps, EVENT_THEMES } from '../types/seasonalEventTypes'

/**
 * Event calendar component
 */
export const EventCalendar: React.FC<EventCalendarProps> = ({
  events,
  campaigns,
  selectedDate,
  onDateSelect
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date())

  /**
   * Get calendar data for current month
   */
  const calendarData = useMemo(() => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()
    
    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()
    
    // Create calendar grid
    const days = []
    
    // Add empty cells for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day)
      
      // Find events and campaigns for this date
      const dayEvents = events.filter(event => {
        const eventStart = new Date(event.startDate.getFullYear(), event.startDate.getMonth(), event.startDate.getDate())
        const eventEnd = new Date(event.endDate.getFullYear(), event.endDate.getMonth(), event.endDate.getDate())
        const currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
        
        return currentDate >= eventStart && currentDate <= eventEnd
      })
      
      const dayCampaigns = campaigns.filter(campaign => {
        const campaignStart = new Date(campaign.startDate.getFullYear(), campaign.startDate.getMonth(), campaign.startDate.getDate())
        const campaignEnd = new Date(campaign.endDate.getFullYear(), campaign.endDate.getMonth(), campaign.endDate.getDate())
        const currentDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
        
        return currentDate >= campaignStart && currentDate <= campaignEnd
      })
      
      days.push({
        date,
        day,
        events: dayEvents,
        campaigns: dayCampaigns,
        isToday: date.toDateString() === new Date().toDateString(),
        isSelected: date.toDateString() === selectedDate.toDateString()
      })
    }
    
    return days
  }, [currentMonth, events, campaigns, selectedDate])

  /**
   * Navigate to previous month
   */
  const previousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))
  }

  /**
   * Navigate to next month
   */
  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))
  }

  /**
   * Get events for selected date
   */
  const selectedDateEvents = useMemo(() => {
    const dayEvents = events.filter(event => {
      const eventStart = new Date(event.startDate.getFullYear(), event.startDate.getMonth(), event.startDate.getDate())
      const eventEnd = new Date(event.endDate.getFullYear(), event.endDate.getMonth(), event.endDate.getDate())
      const currentDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())
      
      return currentDate >= eventStart && currentDate <= eventEnd
    })
    
    const dayCampaigns = campaigns.filter(campaign => {
      const campaignStart = new Date(campaign.startDate.getFullYear(), campaign.startDate.getMonth(), campaign.startDate.getDate())
      const campaignEnd = new Date(campaign.endDate.getFullYear(), campaign.endDate.getMonth(), campaign.endDate.getDate())
      const currentDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())
      
      return currentDate >= campaignStart && currentDate <= campaignEnd
    })
    
    return { events: dayEvents, campaigns: dayCampaigns }
  }, [selectedDate, events, campaigns])

  /**
   * Get theme icon
   */
  const getThemeIcon = (theme: string) => {
    const themeData = EVENT_THEMES.find(t => t.value === theme)
    return themeData?.icon || '🌟'
  }

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Calendar Grid */}
      <div className="lg:col-span-2">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          {/* Calendar Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">
              {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
            </h3>
            <div className="flex items-center gap-2">
              <button
                onClick={previousMonth}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ChevronLeft size={20} />
              </button>
              <button
                onClick={nextMonth}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ChevronRight size={20} />
              </button>
            </div>
          </div>

          {/* Day Headers */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {dayNames.map(day => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-400">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div className="grid grid-cols-7 gap-1">
            {calendarData.map((dayData, index) => (
              <motion.div
                key={index}
                className={`relative p-2 min-h-[80px] border border-gray-700 rounded cursor-pointer transition-all ${
                  dayData
                    ? dayData.isSelected
                      ? 'bg-purple-600/20 border-purple-500'
                      : dayData.isToday
                      ? 'bg-blue-600/20 border-blue-500'
                      : 'hover:bg-gray-700'
                    : ''
                }`}
                onClick={() => dayData && onDateSelect(dayData.date)}
                whileHover={dayData ? { scale: 1.02 } : {}}
                whileTap={dayData ? { scale: 0.98 } : {}}
              >
                {dayData && (
                  <>
                    {/* Day Number */}
                    <div className={`text-sm font-medium mb-1 ${
                      dayData.isToday ? 'text-blue-400' :
                      dayData.isSelected ? 'text-purple-400' :
                      'text-white'
                    }`}>
                      {dayData.day}
                    </div>

                    {/* Event Indicators */}
                    <div className="space-y-1">
                      {dayData.events.slice(0, 2).map(event => (
                        <div
                          key={event.id}
                          className="text-xs px-1 py-0.5 bg-purple-600/30 text-purple-300 rounded truncate"
                          title={event.title}
                        >
                          {getThemeIcon(event.theme)} {event.title.slice(0, 10)}...
                        </div>
                      ))}
                      
                      {dayData.campaigns.slice(0, 1).map(campaign => (
                        <div
                          key={campaign.id}
                          className="text-xs px-1 py-0.5 bg-blue-600/30 text-blue-300 rounded truncate"
                          title={campaign.title}
                        >
                          📢 {campaign.title.slice(0, 8)}...
                        </div>
                      ))}
                      
                      {(dayData.events.length + dayData.campaigns.length) > 3 && (
                        <div className="text-xs text-gray-500">
                          +{(dayData.events.length + dayData.campaigns.length) - 3} more
                        </div>
                      )}
                    </div>
                  </>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Date Details */}
      <div className="space-y-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <CalendarIcon size={20} />
            {selectedDate.toLocaleDateString('en-US', {
              weekday: 'long',
              month: 'long',
              day: 'numeric',
              year: 'numeric'
            })}
          </h3>

          {/* Events for Selected Date */}
          {selectedDateEvents.events.length > 0 && (
            <div className="mb-4">
              <h4 className="text-white font-medium mb-2">Events</h4>
              <div className="space-y-2">
                {selectedDateEvents.events.map(event => (
                  <div key={event.id} className="p-3 bg-purple-600/20 border border-purple-500 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <span>{getThemeIcon(event.theme)}</span>
                      <span className="text-white font-medium text-sm">{event.title}</span>
                    </div>
                    <div className="text-xs text-gray-400">
                      {event.startDate.toLocaleDateString()} - {event.endDate.toLocaleDateString()}
                    </div>
                    <div className="text-xs text-purple-300 mt-1">
                      {event.currentParticipants} participants
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Campaigns for Selected Date */}
          {selectedDateEvents.campaigns.length > 0 && (
            <div className="mb-4">
              <h4 className="text-white font-medium mb-2">Campaigns</h4>
              <div className="space-y-2">
                {selectedDateEvents.campaigns.map(campaign => (
                  <div key={campaign.id} className="p-3 bg-blue-600/20 border border-blue-500 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <span>📢</span>
                      <span className="text-white font-medium text-sm">{campaign.title}</span>
                    </div>
                    <div className="text-xs text-gray-400">
                      {campaign.startDate.toLocaleDateString()} - {campaign.endDate.toLocaleDateString()}
                    </div>
                    <div className="text-xs text-blue-300 mt-1">
                      {campaign.supporters.length} supporters
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Events Message */}
          {selectedDateEvents.events.length === 0 && selectedDateEvents.campaigns.length === 0 && (
            <div className="text-center py-8">
              <div className="text-4xl mb-2">📅</div>
              <p className="text-gray-400 text-sm">No events or campaigns on this date</p>
            </div>
          )}
        </div>

        {/* Calendar Legend */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
          <h4 className="text-white font-medium mb-3">Legend</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-600 rounded"></div>
              <span className="text-gray-400">Events</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-600 rounded"></div>
              <span className="text-gray-400">Campaigns</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 border-2 border-blue-500 rounded"></div>
              <span className="text-gray-400">Today</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 border-2 border-purple-500 rounded"></div>
              <span className="text-gray-400">Selected</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EventCalendar
