/**
 * Event Card Component (Performance Optimized)
 *
 * Individual event card with details, progress, and join functionality
 * Optimized with CSS animations for better performance
 *
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useCallback } from 'react'
import {
  Calendar,
  Users,
  Trophy,
  Clock,
  Star,
  ChevronRight,
  Loader2
} from 'lucide-react'
import { EventCardProps, EVENT_THEMES } from '../types/seasonalEventTypes'
import { buildAnimationClasses, CSS_ANIMATIONS } from '../../../../lib/animations/cssAnimations'
import { useAnimationMonitoring } from '../../../../lib/performance/animationMonitor'

/**
 * Event card component (Performance Optimized)
 */
export const EventCard: React.FC<EventCardProps> = ({
  event,
  onJoinEvent,
  index
}) => {
  const [isJoining, setIsJoining] = useState(false)
  const { trackStart, trackEnd } = useAnimationMonitoring()

  // Performance monitoring
  React.useEffect(() => {
    trackStart(`event-card-${event.id}`)
    return () => {
      trackEnd(`event-card-${event.id}`)
    }
  }, [event.id, trackStart, trackEnd])

  /**
   * Get theme data
   */
  const themeData = EVENT_THEMES.find(t => t.value === event.theme)
  const themeIcon = themeData?.icon || '🌟'

  /**
   * Get status color
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20 border-green-500'
      case 'upcoming': return 'text-blue-400 bg-blue-500/20 border-blue-500'
      case 'ending_soon': return 'text-orange-400 bg-orange-500/20 border-orange-500'
      case 'completed': return 'text-gray-400 bg-gray-500/20 border-gray-500'
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500'
    }
  }

  /**
   * Get difficulty color
   */
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'hard': return 'text-orange-400'
      case 'expert': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  /**
   * Format date
   */
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  /**
   * Calculate days remaining
   */
  const getDaysRemaining = () => {
    const now = new Date()
    const diffTime = event.endDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, diffDays)
  }

  /**
   * Handle join event (Optimized with useCallback)
   */
  const handleJoinEvent = useCallback(async () => {
    setIsJoining(true)
    try {
      await onJoinEvent(event.id)
    } finally {
      setIsJoining(false)
    }
  }, [event.id, onJoinEvent])

  /**
   * Check if user can join
   */
  const canJoin = event.status === 'active' || event.status === 'upcoming'
  const isFull = event.maxParticipants && event.currentParticipants >= event.maxParticipants

  // Build optimized animation classes
  const cardClasses = buildAnimationClasses(
    ['bg-gray-800', 'rounded-lg', 'border', 'border-gray-700', 'overflow-hidden', 'group'],
    [CSS_ANIMATIONS.fadeInUp, CSS_ANIMATIONS.cardHover],
    {
      [CSS_ANIMATIONS.stagger1]: index === 0,
      [CSS_ANIMATIONS.stagger2]: index === 1,
      [CSS_ANIMATIONS.stagger3]: index === 2,
      [CSS_ANIMATIONS.stagger4]: index === 3
    }
  )

  return (
    <div className={cardClasses}>
      {/* Event Image */}
      <div className="relative h-48 bg-gradient-to-br from-purple-600/20 to-blue-600/20">
        {event.media.banner ? (
          <img
            src={event.media.banner}
            alt={event.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <span className="text-6xl">{themeIcon}</span>
          </div>
        )}
        
        {/* Status Badge */}
        <div className={`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(event.status)}`}>
          {event.status.replace('_', ' ').toUpperCase()}
        </div>

        {/* Featured Badge */}
        {event.metadata.featured && (
          <div className="absolute top-3 right-3 p-1 bg-yellow-500/20 border border-yellow-500 rounded-full">
            <Star size={12} className="text-yellow-400" />
          </div>
        )}

        {/* Difficulty */}
        <div className="absolute bottom-3 left-3 flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${getDifficultyColor(event.metadata.difficulty)}`} />
          <span className={`text-xs font-medium ${getDifficultyColor(event.metadata.difficulty)}`}>
            {event.metadata.difficulty.toUpperCase()}
          </span>
        </div>
      </div>

      {/* Event Content */}
      <div className="p-4">
        {/* Title and Theme */}
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-white group-hover:text-purple-400 transition-colors line-clamp-2">
            {event.title}
          </h3>
          <span className="text-xl ml-2">{themeIcon}</span>
        </div>

        {/* Description */}
        <p className="text-gray-400 text-sm mb-4 line-clamp-2">
          {event.description}
        </p>

        {/* Event Details */}
        <div className="space-y-2 mb-4">
          {/* Date Range */}
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Calendar size={14} />
            <span>{formatDate(event.startDate)} - {formatDate(event.endDate)}</span>
          </div>

          {/* Participants */}
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Users size={14} />
            <span>
              {event.currentParticipants.toLocaleString()}
              {event.maxParticipants && ` / ${event.maxParticipants.toLocaleString()}`} participants
            </span>
          </div>

          {/* Time Remaining */}
          {event.status === 'active' && (
            <div className="flex items-center gap-2 text-sm text-orange-400">
              <Clock size={14} />
              <span>{getDaysRemaining()} days remaining</span>
            </div>
          )}

          {/* Rewards Preview */}
          <div className="flex items-center gap-2 text-sm text-yellow-400">
            <Trophy size={14} />
            <span>Up to {event.rewards.leaderboard[0]?.points || 0} points</span>
          </div>
        </div>

        {/* Progress Bar (if user is participating) */}
        {event.progress.individual && (
          <div className="mb-4">
            <div className="flex justify-between text-xs text-gray-400 mb-1">
              <span>Your Progress</span>
              <span>{event.progress.individual.current} / {event.progress.individual.target}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${event.progress.individual.percentage}%` }}
              />
            </div>
          </div>
        )}

        {/* Community Progress */}
        <div className="mb-4">
          <div className="flex justify-between text-xs text-gray-400 mb-1">
            <span>Community Progress</span>
            <span>{event.progress.community.current.toLocaleString()} / {event.progress.community.target.toLocaleString()}</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${event.progress.community.percentage}%` }}
            />
          </div>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {event.metadata.tags.slice(0, 3).map(tag => (
            <span
              key={tag}
              className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded"
            >
              {tag}
            </span>
          ))}
          {event.metadata.tags.length > 3 && (
            <span className="px-2 py-1 bg-gray-700 text-gray-400 text-xs rounded">
              +{event.metadata.tags.length - 3}
            </span>
          )}
        </div>

        {/* Action Button */}
        <button
          onClick={handleJoinEvent}
          disabled={!canJoin || isFull || isJoining}
          className={`w-full py-2 px-4 rounded-lg font-medium transition-all flex items-center justify-center gap-2 ${
            !canJoin || isFull
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-purple-600 hover:bg-purple-700 text-white'
          }`}
        >
          {isJoining ? (
            <>
              <Loader2 size={16} className="animate-spin" />
              Joining...
            </>
          ) : isFull ? (
            'Event Full'
          ) : event.status === 'completed' ? (
            'View Results'
          ) : event.status === 'upcoming' ? (
            <>
              Register Now
              <ChevronRight size={16} />
            </>
          ) : (
            <>
              Join Event
              <ChevronRight size={16} />
            </>
          )}
        </button>
      </div>
    </div>
  )
}

export default EventCard
