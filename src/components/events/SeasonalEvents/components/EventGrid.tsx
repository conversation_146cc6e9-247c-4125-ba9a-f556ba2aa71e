/**
 * Event Grid Component
 * 
 * Displays seasonal events in a responsive grid layout with filtering
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { EventGridProps } from '../types/seasonalEventTypes'
import EventCard from './EventCard'
import EventGridSkeleton from './EventGridSkeleton'

/**
 * Event grid component
 */
export const EventGrid: React.FC<EventGridProps> = ({
  events,
  loading,
  onJoinEvent
}) => {
  if (loading) {
    return <EventGridSkeleton />
  }

  if (events.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <div className="text-6xl mb-4">🎭</div>
        <h3 className="text-xl font-semibold text-white mb-2">No Events Found</h3>
        <p className="text-gray-400 mb-6">
          No seasonal events match your current filters. Try adjusting your search criteria.
        </p>
        <div className="text-sm text-gray-500">
          💡 Tip: Clear filters or check back later for new events
        </div>
      </motion.div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {events.map((event, index) => (
        <EventCard
          key={event.id}
          event={event}
          onJoinEvent={onJoinEvent}
          index={index}
        />
      ))}
    </div>
  )
}

export default EventGrid
