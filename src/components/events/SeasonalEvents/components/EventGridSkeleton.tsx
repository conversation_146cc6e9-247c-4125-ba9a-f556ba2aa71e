/**
 * Event Grid Skeleton Component
 * 
 * Loading skeleton for the event grid
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'

/**
 * Event card skeleton component
 */
const EventCardSkeleton: React.FC<{ index: number }> = ({ index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3, delay: index * 0.1 }}
    className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden"
  >
    {/* Image Skeleton */}
    <div className="h-48 bg-gray-700 animate-pulse" />
    
    {/* Content Skeleton */}
    <div className="p-4 space-y-4">
      {/* Title */}
      <div className="h-6 bg-gray-700 rounded animate-pulse" />
      
      {/* Description */}
      <div className="space-y-2">
        <div className="h-4 bg-gray-700 rounded animate-pulse" />
        <div className="h-4 bg-gray-700 rounded w-3/4 animate-pulse" />
      </div>
      
      {/* Details */}
      <div className="space-y-2">
        <div className="h-4 bg-gray-700 rounded w-1/2 animate-pulse" />
        <div className="h-4 bg-gray-700 rounded w-2/3 animate-pulse" />
        <div className="h-4 bg-gray-700 rounded w-1/3 animate-pulse" />
      </div>
      
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="h-3 bg-gray-700 rounded w-1/4 animate-pulse" />
        <div className="h-2 bg-gray-700 rounded animate-pulse" />
      </div>
      
      {/* Tags */}
      <div className="flex gap-2">
        <div className="h-6 bg-gray-700 rounded w-16 animate-pulse" />
        <div className="h-6 bg-gray-700 rounded w-20 animate-pulse" />
        <div className="h-6 bg-gray-700 rounded w-12 animate-pulse" />
      </div>
      
      {/* Button */}
      <div className="h-10 bg-gray-700 rounded animate-pulse" />
    </div>
  </motion.div>
)

/**
 * Event grid skeleton component
 */
export const EventGridSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }, (_, index) => (
        <EventCardSkeleton key={index} index={index} />
      ))}
    </div>
  )
}

export default EventGridSkeleton
