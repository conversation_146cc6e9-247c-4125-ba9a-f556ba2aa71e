/**
 * Event Stats Overview Component
 * 
 * Displays user statistics and achievements for seasonal events
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Trophy, 
  Calendar, 
  Star, 
  Flame, 
  Target, 
  Award,
  TrendingUp,
  Users
} from 'lucide-react'
import { EventStatsOverviewProps, EVENT_THEMES } from '../types/seasonalEventTypes'

/**
 * Event stats overview component
 */
export const EventStatsOverview: React.FC<EventStatsOverviewProps> = ({ stats }) => {
  /**
   * Get theme icon
   */
  const getThemeIcon = (theme: string) => {
    const themeData = EVENT_THEMES.find(t => t.value === theme)
    return themeData?.icon || '🌟'
  }

  /**
   * Calculate participation rate
   */
  const participationRate = stats.totalEvents > 0 
    ? Math.round((stats.participatedEvents / stats.totalEvents) * 100)
    : 0

  /**
   * Calculate completion rate
   */
  const completionRate = stats.participatedEvents > 0
    ? Math.round((stats.completedEvents / stats.participatedEvents) * 100)
    : 0

  /**
   * Stat card component
   */
  const StatCard: React.FC<{
    title: string
    value: string | number
    subtitle?: string
    icon: React.ReactNode
    color: string
    trend?: 'up' | 'down' | 'stable'
    index: number
  }> = ({ title, value, subtitle, icon, color, trend, index }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className={`p-4 rounded-lg border ${color} bg-opacity-10`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className={`p-2 rounded-lg ${color} bg-opacity-20`}>
          {icon}
        </div>
        {trend && (
          <div className={`flex items-center gap-1 text-xs ${
            trend === 'up' ? 'text-green-400' :
            trend === 'down' ? 'text-red-400' :
            'text-gray-400'
          }`}>
            <TrendingUp size={12} className={trend === 'down' ? 'rotate-180' : ''} />
          </div>
        )}
      </div>
      
      <div className="text-2xl font-bold text-white mb-1">
        {typeof value === 'number' ? value.toLocaleString() : value}
      </div>
      
      <div className="text-sm text-gray-400">{title}</div>
      
      {subtitle && (
        <div className="text-xs text-gray-500 mt-1">{subtitle}</div>
      )}
    </motion.div>
  )

  /**
   * Achievement badge component
   */
  const AchievementBadge: React.FC<{
    title: string
    description: string
    icon: React.ReactNode
    earned: boolean
    index: number
  }> = ({ title, description, icon, earned, index }) => (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className={`p-3 rounded-lg border transition-all ${
        earned
          ? 'bg-yellow-500/20 border-yellow-500 text-yellow-400'
          : 'bg-gray-800 border-gray-700 text-gray-500'
      }`}
    >
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg ${
          earned ? 'bg-yellow-500/20' : 'bg-gray-700'
        }`}>
          {icon}
        </div>
        <div>
          <div className={`font-medium ${earned ? 'text-yellow-400' : 'text-gray-400'}`}>
            {title}
          </div>
          <div className="text-xs text-gray-500">{description}</div>
        </div>
      </div>
    </motion.div>
  )

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <StatCard
          title="Total Events"
          value={stats.totalEvents}
          subtitle="Available events"
          icon={<Calendar size={20} className="text-blue-400" />}
          color="border-blue-500"
          index={0}
        />
        
        <StatCard
          title="Participated"
          value={stats.participatedEvents}
          subtitle={`${participationRate}% participation rate`}
          icon={<Users size={20} className="text-green-400" />}
          color="border-green-500"
          trend="up"
          index={1}
        />
        
        <StatCard
          title="Completed"
          value={stats.completedEvents}
          subtitle={`${completionRate}% completion rate`}
          icon={<Trophy size={20} className="text-yellow-400" />}
          color="border-yellow-500"
          trend="up"
          index={2}
        />
        
        <StatCard
          title="Total Rewards"
          value={stats.totalRewards}
          subtitle="Points earned"
          icon={<Star size={20} className="text-purple-400" />}
          color="border-purple-500"
          trend="up"
          index={3}
        />
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          title="Current Streak"
          value={stats.currentStreak}
          subtitle="Consecutive events"
          icon={<Flame size={20} className="text-orange-400" />}
          color="border-orange-500"
          index={4}
        />
        
        <StatCard
          title="Longest Streak"
          value={stats.longestStreak}
          subtitle="Personal best"
          icon={<Target size={20} className="text-red-400" />}
          color="border-red-500"
          index={5}
        />
        
        <StatCard
          title="Upcoming Events"
          value={stats.upcomingEvents}
          subtitle="Events to join"
          icon={<Calendar size={20} className="text-cyan-400" />}
          color="border-cyan-500"
          index={6}
        />
      </div>

      {/* Favorite Theme */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.7 }}
        className="p-4 bg-gray-800 rounded-lg border border-gray-700"
      >
        <h3 className="text-white font-medium mb-3 flex items-center gap-2">
          <Star size={16} className="text-yellow-400" />
          Favorite Theme
        </h3>
        
        <div className="flex items-center gap-3">
          <span className="text-2xl">{getThemeIcon(stats.favoriteTheme)}</span>
          <div>
            <div className="text-white font-medium capitalize">
              {stats.favoriteTheme.replace('_', ' ')}
            </div>
            <div className="text-gray-400 text-sm">
              Most participated theme
            </div>
          </div>
        </div>
      </motion.div>

      {/* Achievements */}
      <div className="space-y-4">
        <h3 className="text-white font-medium flex items-center gap-2">
          <Award size={16} className="text-yellow-400" />
          Achievements
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <AchievementBadge
            title="Event Master"
            description="Complete 10+ events"
            icon={<Trophy size={16} />}
            earned={stats.achievements.eventMaster}
            index={0}
          />
          
          <AchievementBadge
            title="Seasonal Champion"
            description="Win a seasonal contest"
            icon={<Award size={16} />}
            earned={stats.achievements.seasonalChampion}
            index={1}
          />
          
          <AchievementBadge
            title="Campaign Hero"
            description="Lead a community campaign"
            icon={<Users size={16} />}
            earned={stats.achievements.campaignHero}
            index={2}
          />
          
          <AchievementBadge
            title="Community Builder"
            description="Help 50+ community members"
            icon={<Star size={16} />}
            earned={stats.achievements.communityBuilder}
            index={3}
          />
        </div>
      </div>

      {/* Progress Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.8 }}
        className="p-4 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg border border-purple-500/50"
      >
        <h3 className="text-white font-medium mb-3 flex items-center gap-2">
          <TrendingUp size={16} className="text-purple-400" />
          Progress Insights
        </h3>
        
        <div className="space-y-2 text-sm">
          {participationRate >= 80 && (
            <div className="text-green-400">
              🎉 Excellent participation! You're very active in the community.
            </div>
          )}
          
          {completionRate >= 90 && (
            <div className="text-blue-400">
              💪 Outstanding completion rate! You finish what you start.
            </div>
          )}
          
          {stats.currentStreak >= 5 && (
            <div className="text-orange-400">
              🔥 Amazing streak! Keep the momentum going.
            </div>
          )}
          
          {stats.upcomingEvents > 0 && (
            <div className="text-cyan-400">
              📅 {stats.upcomingEvents} upcoming event{stats.upcomingEvents > 1 ? 's' : ''} waiting for you!
            </div>
          )}
          
          {Object.values(stats.achievements).filter(Boolean).length >= 3 && (
            <div className="text-yellow-400">
              🏆 Achievement collector! You've earned most available badges.
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

export default EventStatsOverview
