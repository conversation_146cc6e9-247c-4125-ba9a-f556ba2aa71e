/**
 * Event Filters Hook
 * 
 * Manages filtering and searching functionality for seasonal events
 * 
 * <AUTHOR> Team
 */

'use client'

import { useState, useMemo, useCallback } from 'react'
import {
  SeasonalEvent,
  EventFilters,
  UseEventFiltersReturn,
  DEFAULT_EVENT_FILTERS
} from '../types/seasonalEventTypes'

/**
 * Event filters management hook
 */
export const useEventFilters = (events: SeasonalEvent[]): UseEventFiltersReturn => {
  const [filters, setFilters] = useState<EventFilters>(DEFAULT_EVENT_FILTERS)

  /**
   * Update filters
   */
  const updateFilters = useCallback((updates: Partial<EventFilters>) => {
    setFilters(prev => ({ ...prev, ...updates }))
  }, [])

  /**
   * Reset filters to default
   */
  const resetFilters = useCallback(() => {
    setFilters(DEFAULT_EVENT_FILTERS)
  }, [])

  /**
   * Filter events based on current filters
   */
  const filteredEvents = useMemo(() => {
    let filtered = [...events]

    // Search query filter
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase().trim()
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(query) ||
        event.description.toLowerCase().includes(query) ||
        event.metadata.tags.some(tag => tag.toLowerCase().includes(query)) ||
        event.metadata.organizer.toLowerCase().includes(query)
      )
    }

    // Theme filter
    if (filters.selectedTheme !== 'all') {
      filtered = filtered.filter(event => event.theme === filters.selectedTheme)
    }

    // Status filter
    if (filters.selectedStatus !== 'all') {
      filtered = filtered.filter(event => event.status === filters.selectedStatus)
    }

    // Participating only filter
    if (filters.showParticipatingOnly) {
      // In a real app, this would check if the user is participating
      // For now, we'll filter based on some criteria
      filtered = filtered.filter(event => 
        event.status === 'active' || event.currentParticipants > 0
      )
    }

    return filtered
  }, [events, filters])

  return {
    filters,
    updateFilters,
    filteredEvents,
    resetFilters
  }
}

/**
 * Event search and sorting utilities
 */
export const useEventSearch = (events: SeasonalEvent[]) => {
  /**
   * Search events by multiple criteria
   */
  const searchEvents = useCallback((
    query: string,
    searchFields: ('title' | 'description' | 'tags' | 'organizer')[] = ['title', 'description', 'tags']
  ) => {
    if (!query.trim()) return events

    const searchTerm = query.toLowerCase().trim()
    
    return events.filter(event => {
      return searchFields.some(field => {
        switch (field) {
          case 'title':
            return event.title.toLowerCase().includes(searchTerm)
          case 'description':
            return event.description.toLowerCase().includes(searchTerm)
          case 'tags':
            return event.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm))
          case 'organizer':
            return event.metadata.organizer.toLowerCase().includes(searchTerm)
          default:
            return false
        }
      })
    })
  }, [events])

  /**
   * Sort events by different criteria
   */
  const sortEvents = useCallback((
    events: SeasonalEvent[],
    sortBy: 'date' | 'popularity' | 'difficulty' | 'alphabetical' = 'date',
    order: 'asc' | 'desc' = 'asc'
  ) => {
    const sorted = [...events].sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'date':
          comparison = a.startDate.getTime() - b.startDate.getTime()
          break
        case 'popularity':
          comparison = a.currentParticipants - b.currentParticipants
          break
        case 'difficulty':
          const difficultyOrder = { easy: 1, medium: 2, hard: 3, expert: 4 }
          comparison = difficultyOrder[a.metadata.difficulty] - difficultyOrder[b.metadata.difficulty]
          break
        case 'alphabetical':
          comparison = a.title.localeCompare(b.title)
          break
      }

      return order === 'desc' ? -comparison : comparison
    })

    return sorted
  }, [])

  /**
   * Filter events by date range
   */
  const filterByDateRange = useCallback((
    events: SeasonalEvent[],
    startDate?: Date,
    endDate?: Date
  ) => {
    return events.filter(event => {
      if (startDate && event.endDate < startDate) return false
      if (endDate && event.startDate > endDate) return false
      return true
    })
  }, [])

  /**
   * Filter events by difficulty
   */
  const filterByDifficulty = useCallback((
    events: SeasonalEvent[],
    difficulties: ('easy' | 'medium' | 'hard' | 'expert')[]
  ) => {
    if (difficulties.length === 0) return events
    return events.filter(event => difficulties.includes(event.metadata.difficulty))
  }, [])

  /**
   * Filter events by user eligibility
   */
  const filterByEligibility = useCallback((
    events: SeasonalEvent[],
    userLevel: number,
    userTier: string
  ) => {
    return events.filter(event => {
      // Check level requirement
      if (event.requirements.minLevel > userLevel) return false
      
      // Check tier requirement
      if (!event.requirements.eligibleTiers.includes(userTier)) return false
      
      return true
    })
  }, [])

  return {
    searchEvents,
    sortEvents,
    filterByDateRange,
    filterByDifficulty,
    filterByEligibility
  }
}

/**
 * Advanced filtering utilities
 */
export const useAdvancedFilters = (events: SeasonalEvent[]) => {
  /**
   * Get unique values for filter options
   */
  const getFilterOptions = useMemo(() => {
    const themes = [...new Set(events.map(event => event.theme))]
    const statuses = [...new Set(events.map(event => event.status))]
    const difficulties = [...new Set(events.map(event => event.metadata.difficulty))]
    const organizers = [...new Set(events.map(event => event.metadata.organizer))]
    const tags = [...new Set(events.flatMap(event => event.metadata.tags))]

    return {
      themes,
      statuses,
      difficulties,
      organizers,
      tags
    }
  }, [events])

  /**
   * Get events statistics for filters
   */
  const getFilterStats = useMemo(() => {
    const total = events.length
    const active = events.filter(e => e.status === 'active').length
    const upcoming = events.filter(e => e.status === 'upcoming').length
    const completed = events.filter(e => e.status === 'completed').length
    const featured = events.filter(e => e.metadata.featured).length

    return {
      total,
      active,
      upcoming,
      completed,
      featured
    }
  }, [events])

  /**
   * Get recommended events based on user activity
   */
  const getRecommendedEvents = useCallback((
    userFavoriteTheme?: string,
    userCompletedEvents?: string[],
    userLevel?: number
  ) => {
    let recommended = [...events]

    // Filter by user level eligibility
    if (userLevel) {
      recommended = recommended.filter(event => event.requirements.minLevel <= userLevel)
    }

    // Prioritize favorite theme
    if (userFavoriteTheme) {
      recommended.sort((a, b) => {
        const aMatchesTheme = a.theme === userFavoriteTheme ? 1 : 0
        const bMatchesTheme = b.theme === userFavoriteTheme ? 1 : 0
        return bMatchesTheme - aMatchesTheme
      })
    }

    // Filter out completed events
    if (userCompletedEvents) {
      recommended = recommended.filter(event => !userCompletedEvents.includes(event.id))
    }

    // Prioritize active and upcoming events
    recommended.sort((a, b) => {
      const statusPriority = { active: 3, upcoming: 2, ending_soon: 1, completed: 0, cancelled: 0 }
      return statusPriority[b.status] - statusPriority[a.status]
    })

    return recommended.slice(0, 6) // Return top 6 recommendations
  }, [events])

  return {
    getFilterOptions,
    getFilterStats,
    getRecommendedEvents
  }
}
