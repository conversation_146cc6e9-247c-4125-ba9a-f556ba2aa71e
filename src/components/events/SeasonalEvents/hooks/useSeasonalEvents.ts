/**
 * Seasonal Events Data Management Hook
 * 
 * Centralized data fetching and state management for seasonal events
 * 
 * <AUTHOR> Team
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'react-hot-toast'
import {
  SeasonalEvent,
  Campaign,
  EventStats,
  UseSeasonalEventsReturn
} from '../types/seasonalEventTypes'

/**
 * Mock data for development
 */
const getMockEvents = (): SeasonalEvent[] => [
  {
    id: '1',
    title: 'Winter Wonderland Keycap Design Contest',
    description: 'Create magical winter-themed keycap designs and win exclusive seasonal rewards',
    theme: 'winter',
    type: 'contest',
    status: 'active',
    startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
    registrationDeadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    maxParticipants: 500,
    currentParticipants: 234,
    requirements: {
      minLevel: 3,
      eligibleTiers: ['bronze', 'silver', 'gold', 'platinum', 'diamond'],
      prerequisites: ['design_basics']
    },
    rewards: {
      participation: { points: 100, badges: ['Winter Participant'] },
      milestones: [
        { threshold: 1, reward: 'First submission bonus', points: 50 },
        { threshold: 3, reward: 'Creative explorer badge', points: 100 },
        { threshold: 5, reward: 'Design master recognition', points: 200 }
      ],
      leaderboard: [
        { position: 1, reward: 'Winter Champion Crown + Exclusive Keycap Set', points: 1000 },
        { position: 2, reward: 'Silver Snowflake Badge + Premium Keycaps', points: 750 },
        { position: 3, reward: 'Bronze Crystal Badge + Special Keycaps', points: 500 }
      ],
      completion: { points: 300, badges: ['Winter Designer'], exclusiveItems: ['winter_theme_pack'] }
    },
    progress: {
      individual: { current: 2, target: 5, percentage: 40 },
      community: { current: 1250, target: 2000, percentage: 62.5 },
      milestones: [
        { value: 500, description: 'Unlock winter color palette', completed: true, completedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) },
        { value: 1000, description: 'Unlock snow effect templates', completed: true, completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) },
        { value: 1500, description: 'Unlock exclusive winter gallery', completed: false },
        { value: 2000, description: 'Unlock legendary winter collection', completed: false }
      ]
    },
    activities: [
      {
        id: 'a1',
        title: 'Daily Design Challenge',
        description: 'Submit one winter-themed keycap design',
        type: 'daily',
        requirements: 'Create and submit a design with winter theme',
        reward: { points: 25 },
        isCompleted: true,
        completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        participantCount: 156
      },
      {
        id: 'a2',
        title: 'Community Voting',
        description: 'Vote on your favorite community designs',
        type: 'weekly',
        requirements: 'Vote on at least 10 community designs',
        reward: { points: 50, items: ['voter_badge'] },
        deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        isCompleted: false,
        participantCount: 89
      }
    ],
    leaderboard: [
      {
        userId: 'user1',
        userName: 'Alex Rodriguez',
        userAvatar: '/avatars/user1.jpg',
        score: 1250,
        rank: 1,
        achievements: ['Winter Master', 'Design Guru'],
        lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        userId: 'user2',
        userName: 'Sarah Chen',
        userAvatar: '/avatars/user2.jpg',
        score: 980,
        rank: 2,
        achievements: ['Creative Explorer'],
        lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000)
      }
    ],
    media: {
      banner: '/events/winter-contest-banner.jpg',
      icon: '❄️',
      gallery: ['/events/winter1.jpg', '/events/winter2.jpg', '/events/winter3.jpg']
    },
    metadata: {
      featured: true,
      difficulty: 'medium',
      estimatedTime: 20,
      tags: ['design', 'contest', 'winter', 'seasonal'],
      organizer: 'Syndicaps Team'
    }
  },
  {
    id: '2',
    title: 'Spring Awakening Community Challenge',
    description: 'Welcome spring with fresh designs and collaborative projects',
    theme: 'spring',
    type: 'challenge',
    status: 'upcoming',
    startDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
    currentParticipants: 67,
    requirements: {
      minLevel: 1,
      eligibleTiers: ['bronze', 'silver', 'gold', 'platinum', 'diamond'],
      teamRequired: true
    },
    rewards: {
      participation: { points: 75, badges: ['Spring Participant'] },
      milestones: [
        { threshold: 1, reward: 'Team formation bonus', points: 25 },
        { threshold: 3, reward: 'Collaboration badge', points: 75 }
      ],
      leaderboard: [
        { position: 1, reward: 'Spring Champion Title + Team Rewards', points: 800 },
        { position: 2, reward: 'Bloom Badge + Premium Access', points: 600 },
        { position: 3, reward: 'Growth Badge + Special Recognition', points: 400 }
      ],
      completion: { points: 200, badges: ['Spring Collaborator'] }
    },
    progress: {
      community: { current: 0, target: 1000, percentage: 0 },
      milestones: [
        { value: 250, description: 'Unlock spring color palette', completed: false },
        { value: 500, description: 'Unlock nature-inspired templates', completed: false },
        { value: 750, description: 'Unlock collaborative workspace', completed: false },
        { value: 1000, description: 'Unlock exclusive spring collection', completed: false }
      ]
    },
    activities: [],
    leaderboard: [],
    media: {
      banner: '/events/spring-challenge-banner.jpg',
      icon: '🌸',
      gallery: []
    },
    metadata: {
      featured: false,
      difficulty: 'easy',
      estimatedTime: 15,
      tags: ['collaboration', 'spring', 'team', 'community'],
      organizer: 'Community Team'
    }
  }
]

const getMockCampaigns = (): Campaign[] => [
  {
    id: '1',
    title: 'Accessibility Awareness Campaign',
    description: 'Promoting inclusive design and accessibility in the mechanical keyboard community',
    type: 'awareness',
    status: 'active',
    startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000),
    goal: {
      type: 'participation',
      target: 1000,
      current: 567,
      unit: 'participants'
    },
    impact: {
      description: 'Raising awareness about accessibility needs in keyboard design',
      metrics: [
        { label: 'Stories shared', value: 89, unit: 'stories' },
        { label: 'Guides created', value: 12, unit: 'guides' },
        { label: 'Community reach', value: 2500, unit: 'people' }
      ]
    },
    activities: [
      {
        id: 'ca1',
        title: 'Share Accessibility Story',
        description: 'Share your experience or story about accessibility needs',
        type: 'sharing',
        reward: { points: 100, recognition: 'Accessibility Advocate' },
        completedBy: 89,
        isUserCompleted: false
      },
      {
        id: 'ca2',
        title: 'Create Accessibility Guide',
        description: 'Write a guide about accessible keyboard design',
        type: 'creation',
        reward: { points: 250, recognition: 'Guide Creator' },
        completedBy: 12,
        isUserCompleted: false
      }
    ],
    supporters: [
      {
        userId: 'user1',
        userName: 'Alex Rodriguez',
        userAvatar: '/avatars/user1.jpg',
        contribution: 350,
        contributionType: 'points',
        joinedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
        level: 'champion'
      }
    ],
    updates: [
      {
        id: 'u1',
        title: 'Campaign Milestone Reached!',
        content: 'We have reached 500 participants! Thank you for your amazing support.',
        author: 'Campaign Team',
        publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      }
    ]
  }
]

const getMockUserStats = (): EventStats => ({
  totalEvents: 15,
  participatedEvents: 8,
  completedEvents: 5,
  totalRewards: 2750,
  favoriteTheme: 'winter',
  currentStreak: 3,
  longestStreak: 7,
  upcomingEvents: 2,
  achievements: {
    eventMaster: false,
    seasonalChampion: true,
    campaignHero: false,
    communityBuilder: true
  }
})

/**
 * Seasonal events data management hook
 */
export const useSeasonalEvents = (): UseSeasonalEventsReturn => {
  const [events, setEvents] = useState<SeasonalEvent[]>([])
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [userStats, setUserStats] = useState<EventStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  /**
   * Load events data
   */
  const loadEvents = useCallback(async () => {
    try {
      // In production, this would be an API call
      // const response = await fetch('/api/events/seasonal')
      // const data = await response.json()
      
      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay
      setEvents(getMockEvents())
    } catch (err) {
      console.error('Failed to load events:', err)
      setError('Failed to load events')
      toast.error('Failed to load events')
    }
  }, [])

  /**
   * Load campaigns data
   */
  const loadCampaigns = useCallback(async () => {
    try {
      // In production, this would be an API call
      // const response = await fetch('/api/campaigns')
      // const data = await response.json()
      
      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 800)) // Simulate API delay
      setCampaigns(getMockCampaigns())
    } catch (err) {
      console.error('Failed to load campaigns:', err)
      setError('Failed to load campaigns')
      toast.error('Failed to load campaigns')
    }
  }, [])

  /**
   * Load user stats
   */
  const loadUserStats = useCallback(async () => {
    try {
      // In production, this would be an API call
      // const response = await fetch('/api/user/event-stats')
      // const data = await response.json()
      
      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 600)) // Simulate API delay
      setUserStats(getMockUserStats())
    } catch (err) {
      console.error('Failed to load user stats:', err)
      setError('Failed to load user stats')
      toast.error('Failed to load user statistics')
    }
  }, [])

  /**
   * Refresh all data
   */
  const refreshData = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      await Promise.all([
        loadEvents(),
        loadCampaigns(),
        loadUserStats()
      ])
    } catch (err) {
      console.error('Failed to refresh data:', err)
      setError('Failed to refresh data')
    } finally {
      setLoading(false)
    }
  }, [loadEvents, loadCampaigns, loadUserStats])

  /**
   * Initial data load
   */
  useEffect(() => {
    refreshData()
  }, [refreshData])

  return {
    events,
    campaigns,
    userStats,
    loading,
    error,
    refreshData
  }
}
