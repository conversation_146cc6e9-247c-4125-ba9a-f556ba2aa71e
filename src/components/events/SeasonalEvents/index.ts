/**
 * Seasonal Events Module Exports
 * 
 * Centralized exports for the refactored seasonal events system
 * 
 * <AUTHOR> Team
 */

// Main Container Component
export { default as SeasonalEventsContainer } from './SeasonalEventsContainer'

// Individual Components
export { default as EventStatsOverview } from './components/EventStatsOverview'
export { default as EventGrid } from './components/EventGrid'
export { default as EventCard } from './components/EventCard'
export { default as EventGridSkeleton } from './components/EventGridSkeleton'
export { default as CampaignGrid } from './components/CampaignGrid'
export { default as CampaignCard } from './components/CampaignCard'
export { default as EventCalendar } from './components/EventCalendar'
export { default as AchievementsView } from './components/AchievementsView'

// Hooks
export { useSeasonalEvents } from './hooks/useSeasonalEvents'
export { useEventFilters, useEventSearch, useAdvancedFilters } from './hooks/useEventFilters'
export { useEventActions, useEventActivity, useEventSharing } from './hooks/useEventActions'

// Types
export type {
  EventTheme,
  EventType,
  EventStatus,
  CampaignType,
  CampaignStatus,
  ActivityType,
  EventActivity,
  EventLeaderboardEntry,
  EventRequirements,
  EventRewards,
  EventProgress,
  EventMedia,
  EventMetadata,
  SeasonalEvent,
  CampaignActivity,
  CampaignSupporter,
  CampaignUpdate,
  CampaignGoal,
  CampaignImpact,
  Campaign,
  EventStats,
  EventFilters,
  EventStatsOverviewProps,
  EventFiltersProps,
  EventGridProps,
  EventCardProps,
  CampaignGridProps,
  CampaignCardProps,
  EventCalendarProps,
  AchievementsViewProps,
  SeasonalEventsContainerProps,
  UseSeasonalEventsReturn,
  UseEventFiltersReturn,
  UseEventActionsReturn
} from './types/seasonalEventTypes'

// Constants
export {
  EVENT_THEMES,
  EVENT_STATUSES,
  CAMPAIGN_TYPES,
  DEFAULT_EVENT_FILTERS
} from './types/seasonalEventTypes'
