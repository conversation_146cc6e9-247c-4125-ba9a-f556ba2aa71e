/**
 * Seasonal Events System Types and Interfaces
 * 
 * Comprehensive type definitions for the refactored seasonal events system
 * 
 * <AUTHOR> Team
 */

/**
 * Event themes
 */
export type EventTheme = 'spring' | 'summer' | 'autumn' | 'winter' | 'holiday' | 'anniversary' | 'special'

/**
 * Event types
 */
export type EventType = 'challenge' | 'campaign' | 'contest' | 'celebration' | 'milestone' | 'collaboration'

/**
 * Event status
 */
export type EventStatus = 'upcoming' | 'active' | 'ending_soon' | 'completed' | 'cancelled'

/**
 * Campaign types
 */
export type CampaignType = 'awareness' | 'fundraising' | 'community_building' | 'education' | 'celebration'

/**
 * Campaign status
 */
export type CampaignStatus = 'planning' | 'active' | 'completed' | 'paused'

/**
 * Activity types
 */
export type ActivityType = 'daily' | 'weekly' | 'milestone' | 'sharing' | 'creation'

/**
 * Event activity interface
 */
export interface EventActivity {
  id: string
  title: string
  description: string
  type: ActivityType
  requirements: string
  reward: { points: number; items?: string[] }
  deadline?: Date
  isCompleted: boolean
  completedAt?: Date
  participantCount: number
}

/**
 * Event leaderboard entry
 */
export interface EventLeaderboardEntry {
  userId: string
  userName: string
  userAvatar: string
  score: number
  rank: number
  achievements: string[]
  lastActivity: Date
}

/**
 * Event requirements
 */
export interface EventRequirements {
  minLevel: number
  eligibleTiers: string[]
  prerequisites?: string[]
  teamRequired?: boolean
}

/**
 * Event rewards structure
 */
export interface EventRewards {
  participation: { points: number; badges: string[]; items?: string[] }
  milestones: { threshold: number; reward: string; points: number }[]
  leaderboard: { position: number; reward: string; points: number }[]
  completion: { points: number; badges: string[]; exclusiveItems?: string[] }
}

/**
 * Event progress tracking
 */
export interface EventProgress {
  individual?: { current: number; target: number; percentage: number }
  community: { current: number; target: number; percentage: number }
  milestones: { value: number; description: string; completed: boolean; completedAt?: Date }[]
}

/**
 * Event media
 */
export interface EventMedia {
  banner: string
  icon: string
  gallery: string[]
}

/**
 * Event metadata
 */
export interface EventMetadata {
  featured: boolean
  difficulty: 'easy' | 'medium' | 'hard' | 'expert'
  estimatedTime: number
  tags: string[]
  organizer: string
}

/**
 * Main seasonal event interface
 */
export interface SeasonalEvent {
  id: string
  title: string
  description: string
  theme: EventTheme
  type: EventType
  status: EventStatus
  startDate: Date
  endDate: Date
  registrationDeadline?: Date
  maxParticipants?: number
  currentParticipants: number
  requirements: EventRequirements
  rewards: EventRewards
  progress: EventProgress
  activities: EventActivity[]
  leaderboard: EventLeaderboardEntry[]
  media: EventMedia
  metadata: EventMetadata
}

/**
 * Campaign activity interface
 */
export interface CampaignActivity {
  id: string
  title: string
  description: string
  type: ActivityType
  reward: { points: number; recognition?: string }
  completedBy: number
  isUserCompleted: boolean
}

/**
 * Campaign supporter interface
 */
export interface CampaignSupporter {
  userId: string
  userName: string
  userAvatar: string
  contribution: number
  contributionType: string
  joinedAt: Date
  level: 'supporter' | 'advocate' | 'champion'
}

/**
 * Campaign update interface
 */
export interface CampaignUpdate {
  id: string
  title: string
  content: string
  author: string
  publishedAt: Date
  media?: string[]
}

/**
 * Campaign goal interface
 */
export interface CampaignGoal {
  type: 'participation' | 'contribution' | 'engagement' | 'creation' | 'sharing'
  target: number
  current: number
  unit: string
}

/**
 * Campaign impact interface
 */
export interface CampaignImpact {
  description: string
  metrics: { label: string; value: number; unit: string }[]
  beneficiaries?: string
}

/**
 * Main campaign interface
 */
export interface Campaign {
  id: string
  title: string
  description: string
  type: CampaignType
  status: CampaignStatus
  startDate: Date
  endDate: Date
  goal: CampaignGoal
  impact: CampaignImpact
  activities: CampaignActivity[]
  supporters: CampaignSupporter[]
  updates: CampaignUpdate[]
}

/**
 * User event statistics
 */
export interface EventStats {
  totalEvents: number
  participatedEvents: number
  completedEvents: number
  totalRewards: number
  favoriteTheme: EventTheme
  currentStreak: number
  longestStreak: number
  upcomingEvents: number
  achievements: {
    eventMaster: boolean
    seasonalChampion: boolean
    campaignHero: boolean
    communityBuilder: boolean
  }
}

/**
 * Event filters interface
 */
export interface EventFilters {
  searchQuery: string
  selectedTheme: string
  selectedStatus: string
  showParticipatingOnly: boolean
}

/**
 * Component props interfaces
 */
export interface EventStatsOverviewProps {
  stats: EventStats
}

export interface EventFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  selectedTheme: string
  onThemeChange: (theme: string) => void
  selectedStatus: string
  onStatusChange: (status: string) => void
  showParticipatingOnly: boolean
  onShowParticipatingChange: (show: boolean) => void
}

export interface EventGridProps {
  events: SeasonalEvent[]
  loading: boolean
  onJoinEvent: (eventId: string) => Promise<void> | void
}

export interface EventCardProps {
  event: SeasonalEvent
  onJoinEvent: (eventId: string) => Promise<void> | void
  index: number
}

export interface CampaignGridProps {
  campaigns: Campaign[]
  loading: boolean
  onJoinCampaign: (campaignId: string) => void
}

export interface CampaignCardProps {
  campaign: Campaign
  onJoinCampaign: (campaignId: string) => void
  index: number
}

export interface EventCalendarProps {
  events: SeasonalEvent[]
  campaigns: Campaign[]
  selectedDate: Date
  onDateSelect: (date: Date) => void
}

export interface AchievementsViewProps {
  stats: EventStats
  events: SeasonalEvent[]
  campaigns: Campaign[]
}

/**
 * Main container props
 */
export interface SeasonalEventsContainerProps {
  initialTab?: 'events' | 'campaigns' | 'calendar' | 'achievements'
}

/**
 * Hook return types
 */
export interface UseSeasonalEventsReturn {
  events: SeasonalEvent[]
  campaigns: Campaign[]
  userStats: EventStats | null
  loading: boolean
  error: string | null
  refreshData: () => Promise<void>
}

export interface UseEventFiltersReturn {
  filters: EventFilters
  updateFilters: (updates: Partial<EventFilters>) => void
  filteredEvents: SeasonalEvent[]
  resetFilters: () => void
}

export interface UseEventActionsReturn {
  joinEvent: (eventId: string) => Promise<boolean>
  leaveEvent: (eventId: string) => Promise<boolean>
  joinCampaign: (campaignId: string) => Promise<boolean>
  leaveCampaign: (campaignId: string) => Promise<boolean>
  isJoining: boolean
  isLeaving: boolean
}

/**
 * Constants
 */
export const EVENT_THEMES = [
  { value: 'all', label: 'All Themes', icon: '🌟' },
  { value: 'spring', label: 'Spring', icon: '🌸' },
  { value: 'summer', label: 'Summer', icon: '☀️' },
  { value: 'autumn', label: 'Autumn', icon: '🍂' },
  { value: 'winter', label: 'Winter', icon: '❄️' },
  { value: 'holiday', label: 'Holiday', icon: '🎄' },
  { value: 'anniversary', label: 'Anniversary', icon: '🎂' },
  { value: 'special', label: 'Special', icon: '✨' }
] as const

export const EVENT_STATUSES = [
  { value: 'all', label: 'All Status' },
  { value: 'upcoming', label: 'Upcoming' },
  { value: 'active', label: 'Active' },
  { value: 'ending_soon', label: 'Ending Soon' },
  { value: 'completed', label: 'Completed' }
] as const

export const CAMPAIGN_TYPES = [
  { value: 'awareness', label: 'Awareness', icon: '📢' },
  { value: 'fundraising', label: 'Fundraising', icon: '💰' },
  { value: 'community_building', label: 'Community Building', icon: '🤝' },
  { value: 'education', label: 'Education', icon: '📚' },
  { value: 'celebration', label: 'Celebration', icon: '🎉' }
] as const

/**
 * Default values
 */
export const DEFAULT_EVENT_FILTERS: EventFilters = {
  searchQuery: '',
  selectedTheme: 'all',
  selectedStatus: 'all',
  showParticipatingOnly: false
}
