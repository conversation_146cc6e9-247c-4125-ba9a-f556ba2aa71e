/**
 * Secure Error Boundary for Gamification Components
 * 
 * Enhanced error boundary with security-aware error handling,
 * automatic recovery, and comprehensive error reporting.
 * 
 * <AUTHOR> Team - Phase 1 Security Improvements
 * @version 2.0.0
 */

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw, Shield, Bug, Network, Database } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { AuditLogger } from '../../../lib/api/gamification/secureTransactions'

// ===== TYPES =====

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
  retryCount: number
  isRecovering: boolean
  errorType: ErrorType
  securityLevel: SecurityLevel
}

interface SecureErrorBoundaryProps {
  children: ReactNode
  fallback?: ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void
  maxRetries?: number
  autoRecovery?: boolean
  securityMode?: SecurityLevel
  componentName?: string
  userId?: string
}

interface ErrorFallbackProps {
  error: Error
  errorInfo: ErrorInfo | null
  retry: () => void
  errorId: string
  retryCount: number
  maxRetries: number
  isRecovering: boolean
  securityLevel: SecurityLevel
  componentName?: string
}

type ErrorType = 
  | 'render_error'
  | 'security_violation'
  | 'network_error'
  | 'data_corruption'
  | 'permission_denied'
  | 'rate_limit_exceeded'
  | 'unknown_error'

type SecurityLevel = 'low' | 'medium' | 'high' | 'critical'

// ===== ERROR CLASSIFICATION =====

class ErrorClassifier {
  static classifyError(error: Error): { type: ErrorType; securityLevel: SecurityLevel } {
    const message = error.message.toLowerCase()
    const stack = error.stack?.toLowerCase() || ''

    // Security-related errors
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return { type: 'permission_denied', securityLevel: 'high' }
    }

    if (message.includes('xss') || message.includes('injection') || message.includes('malicious')) {
      return { type: 'security_violation', securityLevel: 'critical' }
    }

    // Network and API errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return { type: 'network_error', securityLevel: 'low' }
    }

    if (message.includes('rate limit') || message.includes('too many requests')) {
      return { type: 'rate_limit_exceeded', securityLevel: 'medium' }
    }

    // Data integrity errors
    if (message.includes('corruption') || message.includes('invalid data') || message.includes('checksum')) {
      return { type: 'data_corruption', securityLevel: 'high' }
    }

    // Render errors
    if (stack.includes('render') || message.includes('component') || message.includes('jsx')) {
      return { type: 'render_error', securityLevel: 'low' }
    }

    return { type: 'unknown_error', securityLevel: 'medium' }
  }

  static shouldAutoRecover(errorType: ErrorType, securityLevel: SecurityLevel): boolean {
    // Never auto-recover from security violations
    if (securityLevel === 'critical' || errorType === 'security_violation') {
      return false
    }

    // Auto-recover from low-impact errors
    if (securityLevel === 'low' && ['render_error', 'network_error'].includes(errorType)) {
      return true
    }

    return false
  }

  static getMaxRetries(errorType: ErrorType, securityLevel: SecurityLevel): number {
    if (securityLevel === 'critical') return 0
    if (securityLevel === 'high') return 1
    if (errorType === 'network_error') return 3
    return 2
  }
}

// ===== SECURE ERROR REPORTING =====

class SecureErrorReporter {
  static async reportError(
    error: Error,
    errorInfo: ErrorInfo,
    componentName: string,
    userId?: string,
    securityLevel: SecurityLevel = 'medium'
  ): Promise<string> {
    try {
      const errorId = this.generateErrorId()
      
      // Sanitize error data for security
      const sanitizedError = this.sanitizeErrorData(error, errorInfo, securityLevel)
      
      // Log to audit system
      const auditId = await AuditLogger.logOperation('error_occurred', userId || 'anonymous', {
        errorId,
        errorType: error.name,
        errorMessage: sanitizedError.message,
        componentName,
        securityLevel,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        ...sanitizedError.metadata
      })

      // Report to external monitoring (if configured)
      if (process.env.NODE_ENV === 'production' && securityLevel !== 'critical') {
        await this.reportToExternalMonitoring(sanitizedError, errorId, componentName)
      }

      return errorId
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
      return 'error-reporting-failed'
    }
  }

  private static generateErrorId(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 8)
    return `err_${timestamp}_${random}`
  }

  private static sanitizeErrorData(
    error: Error, 
    errorInfo: ErrorInfo, 
    securityLevel: SecurityLevel
  ) {
    // Remove sensitive information based on security level
    let sanitizedMessage = error.message
    let sanitizedStack = error.stack

    if (securityLevel === 'critical' || securityLevel === 'high') {
      // Remove file paths, user data, and internal details
      sanitizedMessage = error.message.replace(/\/[^\s]+/g, '[PATH_REDACTED]')
      sanitizedStack = '[STACK_REDACTED_FOR_SECURITY]'
    } else {
      // Basic sanitization - remove sensitive patterns
      sanitizedMessage = error.message.replace(/\b\d{4}-\d{4}-\d{4}-\d{4}\b/g, '[CARD_REDACTED]')
      sanitizedMessage = sanitizedMessage.replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REDACTED]')
      
      if (sanitizedStack) {
        sanitizedStack = sanitizedStack.replace(/\/[^\s]+node_modules[^\s]+/g, '[NODE_MODULE_PATH]')
        sanitizedStack = sanitizedStack.replace(/webpack:\/\/[^\s]+/g, '[WEBPACK_PATH]')
      }
    }

    return {
      message: sanitizedMessage,
      stack: sanitizedStack,
      metadata: {
        errorName: error.name,
        componentStack: errorInfo.componentStack ? '[COMPONENT_STACK_AVAILABLE]' : null,
        securityLevel
      }
    }
  }

  private static async reportToExternalMonitoring(
    sanitizedError: any,
    errorId: string,
    componentName: string
  ): Promise<void> {
    // Placeholder for external monitoring integration
    // Could integrate with Sentry, DataDog, etc.
    if (typeof window !== 'undefined' && (window as any).errorReporter) {
      (window as any).errorReporter.captureException(sanitizedError, {
        tags: {
          component: componentName,
          errorId,
          source: 'gamification'
        }
      })
    }
  }
}

// ===== SECURE ERROR BOUNDARY COMPONENT =====

export class SecureErrorBoundary extends Component<SecureErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeout: NodeJS.Timeout | null = null

  constructor(props: SecureErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      isRecovering: false,
      errorType: 'unknown_error',
      securityLevel: props.securityMode || 'medium'
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const classification = ErrorClassifier.classifyError(error)
    
    return {
      hasError: true,
      error,
      errorType: classification.type,
      securityLevel: classification.securityLevel
    }
  }

  async componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, componentName = 'GamificationComponent', userId } = this.props
    
    // Report error securely
    const errorId = await SecureErrorReporter.reportError(
      error,
      errorInfo,
      componentName,
      userId,
      this.state.securityLevel
    )

    this.setState({
      errorInfo,
      errorId
    })

    // Call custom error handler
    if (onError) {
      onError(error, errorInfo, errorId)
    }

    // Attempt auto-recovery if safe
    if (this.shouldAutoRecover()) {
      this.scheduleAutoRecovery()
    }
  }

  private shouldAutoRecover(): boolean {
    const { autoRecovery = true, maxRetries = 3 } = this.props
    const { errorType, securityLevel, retryCount } = this.state
    
    if (!autoRecovery || retryCount >= maxRetries) {
      return false
    }

    return ErrorClassifier.shouldAutoRecover(errorType, securityLevel)
  }

  private scheduleAutoRecovery(): void {
    const delay = Math.min(1000 * Math.pow(2, this.state.retryCount), 10000) // Exponential backoff
    
    this.setState({ isRecovering: true })
    
    this.retryTimeout = setTimeout(() => {
      this.handleRetry()
    }, delay)
  }

  private handleRetry = (): void => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: prevState.retryCount + 1,
      isRecovering: false
    }))
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout)
    }
  }

  render() {
    const { hasError, error, errorInfo, errorId, retryCount, isRecovering, securityLevel } = this.state
    const { children, fallback: CustomFallback, maxRetries = 3 } = this.props

    if (hasError && error) {
      const FallbackComponent = CustomFallback || DefaultErrorFallback

      return (
        <FallbackComponent
          error={error}
          errorInfo={errorInfo}
          retry={this.handleRetry}
          errorId={errorId || 'unknown'}
          retryCount={retryCount}
          maxRetries={maxRetries}
          isRecovering={isRecovering}
          securityLevel={securityLevel}
          componentName={this.props.componentName}
        />
      )
    }

    return children
  }
}

// ===== DEFAULT ERROR FALLBACK COMPONENT =====

const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  retry,
  errorId,
  retryCount,
  maxRetries,
  isRecovering,
  securityLevel,
  componentName
}) => {
  const getErrorIcon = () => {
    switch (securityLevel) {
      case 'critical':
        return <Shield className="w-8 h-8 text-red-500" />
      case 'high':
        return <AlertTriangle className="w-8 h-8 text-orange-500" />
      case 'medium':
        return <Bug className="w-8 h-8 text-yellow-500" />
      default:
        return <AlertTriangle className="w-8 h-8 text-gray-500" />
    }
  }

  const getErrorTitle = () => {
    switch (securityLevel) {
      case 'critical':
        return 'Security Error Detected'
      case 'high':
        return 'High Priority Error'
      default:
        return 'Something went wrong'
    }
  }

  const getErrorMessage = () => {
    if (securityLevel === 'critical') {
      return 'A security issue has been detected. Please contact support immediately.'
    }
    
    if (securityLevel === 'high') {
      return 'An important system error occurred. Our team has been notified.'
    }
    
    return 'We encountered an unexpected error. Please try again or contact support if the problem persists.'
  }

  const canRetry = retryCount < maxRetries && securityLevel !== 'critical'

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={`
          min-h-[300px] flex items-center justify-center p-8 rounded-lg border-2
          ${securityLevel === 'critical' 
            ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800' 
            : securityLevel === 'high'
            ? 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800'
            : 'bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-800'
          }
        `}
      >
        <div className="text-center max-w-md">
          <div className="flex justify-center mb-4">
            {getErrorIcon()}
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {getErrorTitle()}
          </h3>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {getErrorMessage()}
          </p>

          {process.env.NODE_ENV === 'development' && (
            <details className="mb-6 text-left">
              <summary className="cursor-pointer text-sm text-gray-500 mb-2">
                Technical Details (Development Mode)
              </summary>
              <div className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 p-3 rounded border overflow-auto max-h-32">
                <div><strong>Component:</strong> {componentName}</div>
                <div><strong>Error ID:</strong> {errorId}</div>
                <div><strong>Error:</strong> {error.message}</div>
                <div><strong>Retry Count:</strong> {retryCount}/{maxRetries}</div>
                <div><strong>Security Level:</strong> {securityLevel}</div>
              </div>
            </details>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {canRetry && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={retry}
                disabled={isRecovering}
                className={`
                  flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors
                  ${isRecovering
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }
                `}
              >
                {isRecovering ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Recovering...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again ({maxRetries - retryCount} left)
                  </>
                )}
              </motion.button>
            )}

            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Reload Page
            </button>
          </div>

          <p className="text-xs text-gray-500 mt-4">
            Error ID: {errorId}
          </p>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

// ===== SPECIALIZED ERROR BOUNDARIES =====

export const NetworkErrorBoundary: React.FC<{ children: ReactNode; onRetry?: () => void }> = ({ 
  children, 
  onRetry 
}) => (
  <SecureErrorBoundary
    securityMode="low"
    autoRecovery={true}
    maxRetries={3}
    componentName="NetworkComponent"
    fallback={(props) => (
      <div className="flex items-center justify-center p-8 bg-yellow-50 rounded-lg border border-yellow-200">
        <div className="text-center">
          <Network className="w-8 h-8 text-yellow-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Connection Error</h3>
          <p className="text-yellow-700 mb-4">Please check your internet connection and try again.</p>
          <button
            onClick={onRetry || props.retry}
            className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2 inline" />
            Retry
          </button>
        </div>
      </div>
    )}
  >
    {children}
  </SecureErrorBoundary>
)

export const DataErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <SecureErrorBoundary
    securityMode="high"
    autoRecovery={false}
    maxRetries={1}
    componentName="DataComponent"
    fallback={(props) => (
      <div className="flex items-center justify-center p-8 bg-red-50 rounded-lg border border-red-200">
        <div className="text-center">
          <Database className="w-8 h-8 text-red-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">Data Error</h3>
          <p className="text-red-700 mb-4">There was an issue with the data. Please refresh the page.</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Reload Page
          </button>
        </div>
      </div>
    )}
  >
    {children}
  </SecureErrorBoundary>
)

export default SecureErrorBoundary