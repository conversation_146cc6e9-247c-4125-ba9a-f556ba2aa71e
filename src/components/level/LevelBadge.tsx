/**
 * Level Badge Component
 * 
 * Displays user level with tier-appropriate styling, animations, and hover effects.
 * Supports multiple sizes and variants for different use cases.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Crown, Star, Zap, Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'
import { LevelTier, getTierStyling } from '@/lib/levelSystem'

// ===== TYPES =====

export interface LevelBadgeProps {
  /** Current user level */
  level: number
  /** Level name (e.g., "Switch Novice", "Keycap Enthusiast") */
  levelName: string
  /** Level tier for styling */
  tier: LevelTier
  /** Badge size variant */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /** Display variant */
  variant?: 'solid' | 'outline' | 'glass' | 'minimal'
  /** Whether to show level number */
  showLevel?: boolean
  /** Whether to show level name */
  showName?: boolean
  /** Whether to show tier icon */
  showIcon?: boolean
  /** Whether to animate on hover */
  animated?: boolean
  /** Whether to show glow effect */
  glow?: boolean
  /** Custom CSS classes */
  className?: string
  /** Click handler */
  onClick?: () => void
}

// ===== SIZE CONFIGURATIONS =====

const sizeConfigs = {
  xs: {
    container: 'px-2 py-1 text-xs',
    icon: 'w-3 h-3',
    level: 'text-xs font-medium',
    name: 'text-xs'
  },
  sm: {
    container: 'px-3 py-1.5 text-sm',
    icon: 'w-4 h-4',
    level: 'text-sm font-semibold',
    name: 'text-xs'
  },
  md: {
    container: 'px-4 py-2 text-base',
    icon: 'w-5 h-5',
    level: 'text-base font-bold',
    name: 'text-sm'
  },
  lg: {
    container: 'px-6 py-3 text-lg',
    icon: 'w-6 h-6',
    level: 'text-lg font-bold',
    name: 'text-base'
  },
  xl: {
    container: 'px-8 py-4 text-xl',
    icon: 'w-8 h-8',
    level: 'text-xl font-bold',
    name: 'text-lg'
  }
}

// ===== TIER ICONS =====

const getTierIcon = (tier: LevelTier) => {
  switch (tier) {
    case 'novice':
      return Sparkles
    case 'intermediate':
      return Zap
    case 'advanced':
      return Star
    case 'expert':
      return Crown
    default:
      return Sparkles
  }
}

// ===== COMPONENT =====

export const LevelBadge: React.FC<LevelBadgeProps> = ({
  level,
  levelName,
  tier,
  size = 'md',
  variant = 'solid',
  showLevel = true,
  showName = false,
  showIcon = true,
  animated = true,
  glow = false,
  className = '',
  onClick
}) => {
  const tierStyling = getTierStyling(tier)
  const sizeConfig = sizeConfigs[size]
  const TierIcon = getTierIcon(tier)

  // Variant styling
  const getVariantStyling = () => {
    switch (variant) {
      case 'solid':
        return `${tierStyling.bgColor} ${tierStyling.color} border-2 ${tierStyling.borderColor}`
      case 'outline':
        return `bg-transparent ${tierStyling.color} border-2 ${tierStyling.borderColor}`
      case 'glass':
        return `${tierStyling.bgColor} ${tierStyling.color} border ${tierStyling.borderColor} backdrop-blur-sm`
      case 'minimal':
        return `bg-gray-800/50 ${tierStyling.color} border border-gray-700`
      default:
        return `${tierStyling.bgColor} ${tierStyling.color} border-2 ${tierStyling.borderColor}`
    }
  }

  // Animation variants
  const badgeVariants = {
    initial: { scale: 1, rotate: 0 },
    hover: { 
      scale: animated ? 1.05 : 1, 
      rotate: animated ? [0, -1, 1, 0] : 0,
      transition: { 
        duration: 0.3,
        rotate: { duration: 0.6, ease: "easeInOut" }
      }
    },
    tap: { scale: 0.95 }
  }

  const glowVariants = {
    initial: { opacity: 0 },
    hover: { 
      opacity: glow ? 0.6 : 0,
      transition: { duration: 0.3 }
    }
  }

  return (
    <motion.div
      className={cn(
        'relative inline-flex items-center gap-2 rounded-full font-medium',
        'transition-all duration-300 cursor-pointer select-none',
        sizeConfig.container,
        getVariantStyling(),
        glow && 'shadow-lg',
        onClick && 'hover:shadow-xl',
        className
      )}
      variants={badgeVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      onClick={onClick}
    >
      {/* Glow effect */}
      {glow && (
        <motion.div
          className={cn(
            'absolute inset-0 rounded-full blur-md -z-10',
            tierStyling.bgColor
          )}
          variants={glowVariants}
        />
      )}

      {/* Tier icon */}
      {showIcon && (
        <TierIcon className={cn(sizeConfig.icon, 'flex-shrink-0')} />
      )}

      {/* Level number */}
      {showLevel && (
        <span className={cn(sizeConfig.level, 'flex-shrink-0')}>
          {level}
        </span>
      )}

      {/* Level name */}
      {showName && (
        <span className={cn(sizeConfig.name, 'truncate max-w-32')}>
          {levelName}
        </span>
      )}

      {/* Sparkle animation for expert tier */}
      {tier === 'expert' && animated && (
        <motion.div
          className="absolute -top-1 -right-1"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Sparkles className="w-3 h-3 text-yellow-300" />
        </motion.div>
      )}
    </motion.div>
  )
}

// ===== PRESET VARIANTS =====

export const CompactLevelBadge: React.FC<Omit<LevelBadgeProps, 'size' | 'showName'>> = (props) => (
  <LevelBadge {...props} size="sm" showName={false} />
)

export const DetailedLevelBadge: React.FC<Omit<LevelBadgeProps, 'size' | 'showName'>> = (props) => (
  <LevelBadge {...props} size="lg" showName={true} />
)

export const MinimalLevelBadge: React.FC<Omit<LevelBadgeProps, 'variant' | 'showIcon'>> = (props) => (
  <LevelBadge {...props} variant="minimal" showIcon={false} />
)

export const GlowingLevelBadge: React.FC<Omit<LevelBadgeProps, 'glow' | 'animated'>> = (props) => (
  <LevelBadge {...props} glow={true} animated={true} />
)

export default LevelBadge
