/**
 * Level Progress Bar Component
 * 
 * Displays XP progress to next level with smooth animations and tier-appropriate styling.
 * Supports multiple layouts and interactive features.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useEffect, useState } from 'react'
import { Zap } from 'lucide-react'
import { cn } from '@/lib/utils'
import { LevelTier, getTierStyling } from '@/lib/levelSystem'

// ===== TYPES =====

export interface LevelProgressBarProps {
  /** Current XP in current level */
  currentXP: number
  /** XP needed for next level */
  nextLevelXP: number
  /** Progress percentage (0-100) */
  progressPercentage: number
  /** Current level */
  currentLevel: number
  /** Next level */
  nextLevel?: number
  /** Level tier for styling */
  tier: LevelTier
  /** Progress bar size */
  size?: 'sm' | 'md' | 'lg'
  /** Layout variant */
  variant?: 'linear' | 'circular' | 'compact'
  /** Whether to show XP numbers */
  showXP?: boolean
  /** Whether to show percentage */
  showPercentage?: boolean
  /** Whether to animate progress changes */
  animated?: boolean
  /** Whether to show glow effect */
  glow?: boolean
  /** Custom CSS classes */
  className?: string
}

// ===== SIZE CONFIGURATIONS =====

const sizeConfigs = {
  sm: {
    height: 'h-2',
    text: 'text-xs',
    padding: 'px-2 py-1',
    circular: 'w-16 h-16',
    strokeWidth: 4
  },
  md: {
    height: 'h-3',
    text: 'text-sm',
    padding: 'px-3 py-2',
    circular: 'w-24 h-24',
    strokeWidth: 6
  },
  lg: {
    height: 'h-4',
    text: 'text-base',
    padding: 'px-4 py-3',
    circular: 'w-32 h-32',
    strokeWidth: 8
  }
}

// ===== COMPONENT =====

export const LevelProgressBar: React.FC<LevelProgressBarProps> = ({
  currentXP,
  nextLevelXP,
  progressPercentage,
  currentLevel,
  nextLevel,
  tier,
  size = 'md',
  variant = 'linear',
  showXP = true,
  showPercentage = false,
  animated = true,
  glow = false,
  className = ''
}) => {
  const [displayProgress, setDisplayProgress] = useState(progressPercentage || 0)
  const tierStyling = getTierStyling(tier)
  const sizeConfig = sizeConfigs[size]

  // Note: Animation is now handled directly in motion.div animate props

  // Update progress with animation
  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setDisplayProgress(progressPercentage)
      }, 50) // Reduced delay for more responsive animation
      return () => clearTimeout(timer)
    } else {
      setDisplayProgress(progressPercentage)
    }
  }, [progressPercentage, animated])

  // Linear Progress Bar
  const LinearProgressBar = () => (
    <div className={cn('w-full space-y-2', className)}>
      {/* Header with level info */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Zap className={cn('w-4 h-4', tierStyling.color)} />
          <span className={cn('font-medium', tierStyling.color, sizeConfig.text)}>
            Level {currentLevel}
            {nextLevel && ` → ${nextLevel}`}
          </span>
        </div>
        {showPercentage && (
          <span className={cn('font-medium text-gray-400', sizeConfig.text)}>
            {Math.round(progressPercentage)}%
          </span>
        )}
      </div>

      {/* Progress bar container */}
      <div className={cn(
        'relative bg-gray-800 rounded-full overflow-hidden',
        sizeConfig.height,
        glow && 'shadow-lg'
      )}>
        {/* Background glow */}
        {glow && (
          <div className={cn(
            'absolute inset-0 rounded-full blur-sm opacity-50',
            tierStyling.bgColor
          )} />
        )}

        {/* Progress fill */}
        <div
          className={cn(
            'h-full rounded-full relative overflow-hidden transition-all duration-700 ease-out',
            `bg-gradient-to-r ${tierStyling.gradientColors}`
          )}
          style={{
            width: `${displayProgress}%`
          }}
        >
          {/* Shimmer effect */}
          {animated && (
            <div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"
              style={{
                animation: 'shimmer 2s ease-in-out infinite'
              }}
            />
          )}
          <style jsx>{`
            @keyframes shimmer {
              0% { transform: translateX(-100%); }
              100% { transform: translateX(100%); }
            }
          `}</style>
        </div>

        {/* Progress indicator dot */}
        <div
          className={cn(
            'absolute top-1/2 w-2 h-2 rounded-full transform -translate-y-1/2 transition-all duration-700 ease-out',
            tierStyling.bgColor,
            'shadow-lg'
          )}
          style={{
            left: `${displayProgress}%`,
            transform: 'translateX(-50%)'
          }}
        />
      </div>

      {/* XP info */}
      {showXP && (
        <div className="flex items-center justify-between">
          <span className={cn('text-gray-400', sizeConfig.text)}>
            {currentXP.toLocaleString()} XP
          </span>
          <span className={cn('text-gray-400', sizeConfig.text)}>
            {nextLevelXP.toLocaleString()} XP
          </span>
        </div>
      )}
    </div>
  )

  // Circular Progress Bar
  const CircularProgressBar = () => {
    const radius = size === 'sm' ? 28 : size === 'md' ? 44 : 60
    const circumference = 2 * Math.PI * radius
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (displayProgress / 100) * circumference

    return (
      <div className={cn('relative flex items-center justify-center', sizeConfig.circular, className)}>
        <svg
          className="transform -rotate-90"
          width="100%"
          height="100%"
          viewBox={`0 0 ${radius * 2 + 20} ${radius * 2 + 20}`}
        >
          {/* Background circle */}
          <circle
            cx={radius + 10}
            cy={radius + 10}
            r={radius}
            stroke="rgb(31 41 55)" // gray-800
            strokeWidth={sizeConfig.strokeWidth}
            fill="transparent"
          />
          
          {/* Progress circle */}
          <circle
            cx={radius + 10}
            cy={radius + 10}
            r={radius}
            stroke={`url(#gradient-${tier})`}
            strokeWidth={sizeConfig.strokeWidth}
            fill="transparent"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            style={{
              transition: 'stroke-dashoffset 0.7s ease-out'
            }}
          />

          {/* Gradient definition */}
          <defs>
            <linearGradient id={`gradient-${tier}`} x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor={tierStyling.color.replace('text-', '')} />
              <stop offset="100%" stopColor={tierStyling.color.replace('text-', '')} stopOpacity="0.6" />
            </linearGradient>
          </defs>
        </svg>

        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className={cn('font-bold', tierStyling.color, sizeConfig.text)}>
            {currentLevel}
          </span>
          {showPercentage && (
            <span className={cn('text-gray-400 text-xs')}>
              {Math.round(progressPercentage)}%
            </span>
          )}
        </div>
      </div>
    )
  }

  // Compact Progress Bar
  const CompactProgressBar = () => (
    <div className={cn('flex items-center gap-3', className)}>
      <span className={cn('font-medium', tierStyling.color, sizeConfig.text)}>
        Lv.{currentLevel}
      </span>
      
      <div className={cn('flex-1 bg-gray-800 rounded-full', sizeConfig.height)}>
        <div
          className={cn(
            'h-full rounded-full transition-all duration-700 ease-out',
            `bg-gradient-to-r ${tierStyling.gradientColors}`
          )}
          style={{
            width: `${displayProgress}%`
          }}
        />
      </div>

      {showXP && (
        <span className={cn('text-gray-400 text-xs font-medium')}>
          {currentXP}/{nextLevelXP}
        </span>
      )}
    </div>
  )

  // Render based on variant
  switch (variant) {
    case 'circular':
      return <CircularProgressBar />
    case 'compact':
      return <CompactProgressBar />
    default:
      return <LinearProgressBar />
  }
}

// ===== PRESET VARIANTS =====

export const CompactLevelProgress: React.FC<Omit<LevelProgressBarProps, 'variant' | 'size'>> = (props) => (
  <LevelProgressBar {...props} variant="compact" size="sm" />
)

export const CircularLevelProgress: React.FC<Omit<LevelProgressBarProps, 'variant'>> = (props) => (
  <LevelProgressBar {...props} variant="circular" />
)

export const DetailedLevelProgress: React.FC<Omit<LevelProgressBarProps, 'showXP' | 'showPercentage'>> = (props) => (
  <LevelProgressBar {...props} showXP={true} showPercentage={true} />
)

export default LevelProgressBar
