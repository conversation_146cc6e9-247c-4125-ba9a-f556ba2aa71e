/**
 * Level Rewards Panel Component
 * 
 * Displays available and claimed level rewards with claiming functionality.
 * Features filtering, sorting, and interactive reward cards.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Gift, 
  Trophy, 
  Zap, 
  Crown, 
  Star, 
  Sparkles,
  Check,
  Lock,
  Filter,
  SortAsc,
  Calendar,
  Award
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { LevelTier, getTierStyling } from '@/lib/levelSystem'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

// ===== TYPES =====

export interface LevelReward {
  id: string
  level: number
  type: 'badge' | 'points' | 'discount' | 'keycap' | 'access' | 'title'
  name: string
  description: string
  value: any
  isExclusive: boolean
  canClaim: boolean
  alreadyClaimed: boolean
  claimDeadline?: Date
  tier: LevelTier
}

export interface LevelRewardsPanelProps {
  /** User's current level */
  currentLevel: number
  /** Available rewards */
  rewards: LevelReward[]
  /** Whether rewards are loading */
  loading?: boolean
  /** Callback when reward is claimed */
  onClaimReward: (rewardId: string) => Promise<void>
  /** Custom CSS classes */
  className?: string
}

// ===== CONFIGURATIONS =====

const rewardTypeConfigs = {
  badge: {
    icon: Trophy,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20',
    borderColor: 'border-yellow-400/30',
    label: 'Badge'
  },
  points: {
    icon: Zap,
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    borderColor: 'border-green-400/30',
    label: 'Points'
  },
  discount: {
    icon: Gift,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20',
    borderColor: 'border-blue-400/30',
    label: 'Discount'
  },
  keycap: {
    icon: Crown,
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/20',
    borderColor: 'border-purple-400/30',
    label: 'Keycap'
  },
  access: {
    icon: Star,
    color: 'text-orange-400',
    bgColor: 'bg-orange-500/20',
    borderColor: 'border-orange-400/30',
    label: 'Access'
  },
  title: {
    icon: Sparkles,
    color: 'text-pink-400',
    bgColor: 'bg-pink-500/20',
    borderColor: 'border-pink-400/30',
    label: 'Title'
  }
}

// ===== REWARD CARD COMPONENT =====

const RewardCard: React.FC<{
  reward: LevelReward
  onClaim: (rewardId: string) => Promise<void>
  index: number
}> = ({ reward, onClaim, index }) => {
  const [claiming, setClaiming] = useState(false)
  const typeConfig = rewardTypeConfigs[reward.type]
  const tierStyling = getTierStyling(reward.tier)
  const TypeIcon = typeConfig.icon

  const handleClaim = async () => {
    if (!reward.canClaim || reward.alreadyClaimed || claiming) return

    setClaiming(true)
    try {
      await onClaim(reward.id)
    } catch (error) {
      console.error('Failed to claim reward:', error)
    } finally {
      setClaiming(false)
    }
  }

  const getStatusIcon = () => {
    if (reward.alreadyClaimed) return Check
    if (!reward.canClaim) return Lock
    return null
  }

  const StatusIcon = getStatusIcon()

  return (
    <motion.div
      className={cn(
        'relative bg-gray-800/50 rounded-lg border p-4',
        'hover:bg-gray-800/70 transition-all duration-300',
        reward.alreadyClaimed ? 'border-green-500/30' : 
        reward.canClaim ? typeConfig.borderColor : 'border-gray-700',
        reward.isExclusive && 'ring-1 ring-purple-500/30'
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05 }}
      whileHover={{ scale: 1.02 }}
    >
      {/* Exclusive badge */}
      {reward.isExclusive && (
        <div className="absolute -top-2 -right-2">
          <Badge className="bg-purple-500 text-white text-xs">
            Exclusive
          </Badge>
        </div>
      )}

      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className={cn(
            'p-2 rounded-lg',
            typeConfig.bgColor
          )}>
            <TypeIcon className={cn('w-5 h-5', typeConfig.color)} />
          </div>
          
          <div>
            <h3 className="font-semibold text-white text-sm">
              {reward.name}
            </h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className={cn('text-xs', tierStyling.color)}>
                Level {reward.level}
              </Badge>
              <Badge variant="outline" className={cn('text-xs', typeConfig.color)}>
                {typeConfig.label}
              </Badge>
            </div>
          </div>
        </div>

        {/* Status indicator */}
        {StatusIcon && (
          <div className={cn(
            'p-1 rounded-full',
            reward.alreadyClaimed ? 'bg-green-500/20' : 'bg-gray-700/50'
          )}>
            <StatusIcon className={cn(
              'w-4 h-4',
              reward.alreadyClaimed ? 'text-green-400' : 'text-gray-500'
            )} />
          </div>
        )}
      </div>

      {/* Description */}
      <p className="text-gray-400 text-sm mb-3">
        {reward.description}
      </p>

      {/* Value display */}
      {reward.type === 'points' && (
        <div className="mb-3">
          <span className="inline-block px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">
            +{reward.value.toLocaleString()} points
          </span>
        </div>
      )}

      {reward.type === 'discount' && (
        <div className="mb-3">
          <span className="inline-block px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded">
            {reward.value}% discount
          </span>
        </div>
      )}

      {/* Claim deadline */}
      {reward.claimDeadline && !reward.alreadyClaimed && (
        <div className="flex items-center gap-1 text-orange-400 text-xs mb-3">
          <Calendar className="w-3 h-3" />
          <span>
            Expires {reward.claimDeadline.toLocaleDateString()}
          </span>
        </div>
      )}

      {/* Action button */}
      <div className="flex justify-end">
        {reward.alreadyClaimed ? (
          <Badge className="bg-green-500/20 text-green-400">
            <Check className="w-3 h-3 mr-1" />
            Claimed
          </Badge>
        ) : reward.canClaim ? (
          <Button
            size="sm"
            onClick={handleClaim}
            disabled={claiming}
            className={cn(
              'bg-gradient-to-r',
              tierStyling.gradientColors,
              'hover:opacity-90'
            )}
          >
            {claiming ? 'Claiming...' : 'Claim'}
          </Button>
        ) : (
          <Badge variant="outline" className="text-gray-500">
            <Lock className="w-3 h-3 mr-1" />
            Locked
          </Badge>
        )}
      </div>
    </motion.div>
  )
}

// ===== MAIN COMPONENT =====

export const LevelRewardsPanel: React.FC<LevelRewardsPanelProps> = ({
  currentLevel,
  rewards,
  loading = false,
  onClaimReward,
  className = ''
}) => {
  const [filterType, setFilterType] = useState<'all' | 'available' | 'claimed' | 'locked'>('all')
  const [sortBy, setSortBy] = useState<'level' | 'type' | 'status'>('level')

  // Filter and sort rewards
  const filteredAndSortedRewards = useMemo(() => {
    let filtered = rewards

    // Apply filter
    switch (filterType) {
      case 'available':
        filtered = rewards.filter(r => r.canClaim && !r.alreadyClaimed)
        break
      case 'claimed':
        filtered = rewards.filter(r => r.alreadyClaimed)
        break
      case 'locked':
        filtered = rewards.filter(r => !r.canClaim)
        break
      default:
        filtered = rewards
    }

    // Apply sort
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'level':
          return a.level - b.level
        case 'type':
          return a.type.localeCompare(b.type)
        case 'status':
          if (a.alreadyClaimed !== b.alreadyClaimed) {
            return a.alreadyClaimed ? 1 : -1
          }
          if (a.canClaim !== b.canClaim) {
            return a.canClaim ? -1 : 1
          }
          return 0
        default:
          return 0
      }
    })
  }, [rewards, filterType, sortBy])

  // Statistics
  const stats = useMemo(() => {
    const total = rewards.length
    const claimed = rewards.filter(r => r.alreadyClaimed).length
    const available = rewards.filter(r => r.canClaim && !r.alreadyClaimed).length
    const locked = rewards.filter(r => !r.canClaim).length

    return { total, claimed, available, locked }
  }, [rewards])

  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        {Array.from({ length: 6 }, (_, i) => (
          <div key={i} className="bg-gray-800/50 rounded-lg p-4 animate-pulse">
            <div className="h-4 bg-gray-700 rounded w-3/4 mb-2" />
            <div className="h-3 bg-gray-700 rounded w-1/2" />
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            <Award className="w-6 h-6 text-purple-400" />
            Level Rewards
          </h2>
          <p className="text-gray-400 text-sm mt-1">
            Claim rewards as you level up
          </p>
        </div>

        {/* Stats */}
        <div className="flex gap-4 text-sm">
          <div className="text-center">
            <div className="text-white font-semibold">{stats.claimed}</div>
            <div className="text-gray-400">Claimed</div>
          </div>
          <div className="text-center">
            <div className="text-green-400 font-semibold">{stats.available}</div>
            <div className="text-gray-400">Available</div>
          </div>
          <div className="text-center">
            <div className="text-gray-500 font-semibold">{stats.locked}</div>
            <div className="text-gray-400">Locked</div>
          </div>
        </div>
      </div>

      {/* Filters and sorting */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="bg-gray-800 border border-gray-700 rounded px-3 py-1 text-sm text-white"
          >
            <option value="all">All Rewards</option>
            <option value="available">Available</option>
            <option value="claimed">Claimed</option>
            <option value="locked">Locked</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <SortAsc className="w-4 h-4 text-gray-400" />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="bg-gray-800 border border-gray-700 rounded px-3 py-1 text-sm text-white"
          >
            <option value="level">Sort by Level</option>
            <option value="type">Sort by Type</option>
            <option value="status">Sort by Status</option>
          </select>
        </div>
      </div>

      {/* Rewards grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence mode="popLayout">
          {filteredAndSortedRewards.map((reward, index) => (
            <RewardCard
              key={reward.id}
              reward={reward}
              onClaim={onClaimReward}
              index={index}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Empty state */}
      {filteredAndSortedRewards.length === 0 && (
        <div className="text-center py-12">
          <Gift className="w-12 h-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-400 mb-2">
            No rewards found
          </h3>
          <p className="text-gray-500">
            {filterType === 'all' 
              ? 'No rewards available yet'
              : `No ${filterType} rewards found`
            }
          </p>
        </div>
      )}
    </div>
  )
}

export default LevelRewardsPanel
