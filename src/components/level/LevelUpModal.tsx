/**
 * Level Up Modal Component
 * 
 * Celebration modal for level up events with rewards display and animations.
 * Features confetti effects, reward showcase, and smooth transitions.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Crown, 
  Star, 
  Gift, 
  Zap, 
  Trophy,
  Sparkles,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { LevelTier, getTierStyling } from '@/lib/levelSystem'
import { LevelBadge } from './LevelBadge'
import { Button } from '@/components/ui/button'

// ===== TYPES =====

export interface LevelUpReward {
  id: string
  type: 'badge' | 'points' | 'discount' | 'keycap' | 'access' | 'title'
  name: string
  description: string
  value: any
  icon?: string
}

export interface LevelUpModalProps {
  /** Whether the modal is open */
  isOpen: boolean
  /** Callback to close the modal */
  onClose: () => void
  /** Old level */
  oldLevel: number
  /** New level */
  newLevel: number
  /** New level name */
  newLevelName: string
  /** Level tier */
  tier: LevelTier
  /** XP gained that caused level up */
  xpGained: number
  /** Rewards earned from level up */
  rewards?: LevelUpReward[]
  /** Whether to show confetti animation */
  showConfetti?: boolean
  /** Custom celebration message */
  customMessage?: string
}

// ===== CONFETTI COMPONENT =====

const ConfettiParticle: React.FC<{ delay: number; color: string }> = ({ delay, color }) => (
  <motion.div
    className={cn('absolute w-2 h-2 rounded-full', color)}
    initial={{ 
      opacity: 0, 
      scale: 0, 
      x: 0, 
      y: 0,
      rotate: 0 
    }}
    animate={{
      opacity: [0, 1, 1, 0],
      scale: [0, 1, 1, 0],
      x: Math.random() * 400 - 200,
      y: Math.random() * 300 - 150,
      rotate: Math.random() * 360
    }}
    transition={{
      duration: 3,
      delay,
      ease: "easeOut"
    }}
  />
)

const Confetti: React.FC<{ show: boolean }> = ({ show }) => {
  const colors = [
    'bg-yellow-400', 'bg-purple-400', 'bg-blue-400', 
    'bg-green-400', 'bg-red-400', 'bg-pink-400'
  ]

  if (!show) return null

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {Array.from({ length: 50 }, (_, i) => (
        <ConfettiParticle
          key={i}
          delay={i * 0.1}
          color={colors[i % colors.length]}
        />
      ))}
    </div>
  )
}

// ===== REWARD CARD COMPONENT =====

const RewardCard: React.FC<{ reward: LevelUpReward; index: number }> = ({ reward, index }) => {
  const getRewardIcon = () => {
    switch (reward.type) {
      case 'badge': return Trophy
      case 'points': return Zap
      case 'discount': return Gift
      case 'keycap': return Crown
      case 'access': return Star
      case 'title': return Sparkles
      default: return Gift
    }
  }

  const RewardIcon = getRewardIcon()

  return (
    <motion.div
      className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1 + index * 0.1 }}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 p-2 bg-purple-500/20 rounded-lg">
          <RewardIcon className="w-5 h-5 text-purple-400" />
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-white text-sm">
            {reward.name}
          </h4>
          <p className="text-gray-400 text-xs mt-1">
            {reward.description}
          </p>
          {reward.type === 'points' && (
            <span className="inline-block mt-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">
              +{reward.value} points
            </span>
          )}
          {reward.type === 'discount' && (
            <span className="inline-block mt-2 px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded">
              {reward.value}% off
            </span>
          )}
        </div>
      </div>
    </motion.div>
  )
}

// ===== MAIN COMPONENT =====

export const LevelUpModal: React.FC<LevelUpModalProps> = ({
  isOpen,
  onClose,
  oldLevel,
  newLevel,
  newLevelName,
  tier,
  xpGained,
  rewards = [],
  showConfetti = true,
  customMessage
}) => {
  const [showRewards, setShowRewards] = useState(false)
  const tierStyling = getTierStyling(tier)

  // Show rewards after initial animation
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        setShowRewards(true)
      }, 1500)
      return () => clearTimeout(timer)
    } else {
      setShowRewards(false)
    }
  }, [isOpen])

  // Modal animation variants
  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 50
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      y: 50,
      transition: { duration: 0.2 }
    }
  }

  const levelBadgeVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: [0, 1.2, 1],
      rotate: [180, 0, 0],
      transition: {
        duration: 0.8,
        times: [0, 0.6, 1],
        ease: "easeOut"
      }
    }
  }

  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { delay: 0.5, duration: 0.5 }
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            className="relative bg-gray-900 rounded-2xl border border-gray-700 shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Confetti */}
            <Confetti show={showConfetti} />

            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 p-2 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors"
            >
              <X className="w-5 h-5 text-gray-400" />
            </button>

            {/* Header */}
            <div className="relative px-6 pt-8 pb-6 text-center">
              {/* Background glow */}
              <div className={cn(
                'absolute inset-0 opacity-20 blur-3xl',
                tierStyling.bgColor
              )} />

              {/* Level up badge */}
              <motion.div
                className="flex justify-center mb-4"
                variants={levelBadgeVariants}
                initial="hidden"
                animate="visible"
              >
                <LevelBadge
                  level={newLevel}
                  levelName={newLevelName}
                  tier={tier}
                  size="xl"
                  showName={true}
                  glow={true}
                  animated={true}
                />
              </motion.div>

              {/* Level up text */}
              <motion.div variants={textVariants} initial="hidden" animate="visible">
                <h2 className="text-2xl font-bold text-white mb-2">
                  Level Up!
                </h2>
                <p className="text-gray-300 mb-1">
                  {customMessage || `Congratulations! You've reached level ${newLevel}`}
                </p>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
                  <span>Level {oldLevel}</span>
                  <ChevronRight className="w-4 h-4" />
                  <span className={tierStyling.color}>Level {newLevel}</span>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  +{xpGained.toLocaleString()} XP earned
                </p>
              </motion.div>
            </div>

            {/* Rewards section */}
            <AnimatePresence>
              {showRewards && rewards.length > 0 && (
                <motion.div
                  className="px-6 pb-6"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="border-t border-gray-700 pt-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                      <Gift className="w-5 h-5 text-purple-400" />
                      Rewards Unlocked
                    </h3>
                    
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {rewards.map((reward, index) => (
                        <RewardCard key={reward.id} reward={reward} index={index} />
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Footer */}
            <div className="px-6 pb-6">
              <Button
                onClick={onClose}
                className={cn(
                  'w-full',
                  `bg-gradient-to-r ${tierStyling.gradientColors}`,
                  'hover:opacity-90 transition-opacity'
                )}
              >
                Continue
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

// ===== HOOKS =====

export const useLevelUpModal = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [levelUpData, setLevelUpData] = useState<Omit<LevelUpModalProps, 'isOpen' | 'onClose'> | null>(null)

  const showLevelUp = (data: Omit<LevelUpModalProps, 'isOpen' | 'onClose'>) => {
    setLevelUpData(data)
    setIsOpen(true)
  }

  const closeLevelUp = () => {
    setIsOpen(false)
    // Clear data after animation completes
    setTimeout(() => setLevelUpData(null), 300)
  }

  return {
    isOpen,
    levelUpData,
    showLevelUp,
    closeLevelUp
  }
}

export default LevelUpModal
