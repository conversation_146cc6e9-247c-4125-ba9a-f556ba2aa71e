/**
 * XP Gain Notification Component
 * 
 * Displays animated notifications for XP gains with smooth entrance/exit animations.
 * Supports different XP sources and customizable styling.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Zap, 
  ShoppingCart, 
  MessageCircle, 
  Trophy, 
  Gift, 
  Star,
  TrendingUp,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'

// ===== TYPES =====

export interface XPGainNotificationProps {
  /** Amount of XP gained */
  xpAmount: number
  /** Source of XP gain */
  source: 'purchase' | 'activity' | 'bonus' | 'event' | 'manual'
  /** Description of the XP gain */
  description: string
  /** Whether the notification is visible */
  isVisible: boolean
  /** Callback when notification should be dismissed */
  onDismiss: () => void
  /** Auto-dismiss duration in milliseconds */
  duration?: number
  /** Position on screen */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center'
  /** Size variant */
  size?: 'sm' | 'md' | 'lg'
  /** Whether to show close button */
  showCloseButton?: boolean
  /** Custom CSS classes */
  className?: string
}

export interface XPNotificationQueueItem extends Omit<XPGainNotificationProps, 'isVisible' | 'onDismiss'> {
  id: string
  timestamp: number
}

// ===== CONFIGURATIONS =====

const sourceConfigs = {
  purchase: {
    icon: ShoppingCart,
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    borderColor: 'border-green-400/30',
    label: 'Purchase'
  },
  activity: {
    icon: MessageCircle,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20',
    borderColor: 'border-blue-400/30',
    label: 'Activity'
  },
  bonus: {
    icon: Gift,
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/20',
    borderColor: 'border-purple-400/30',
    label: 'Bonus'
  },
  event: {
    icon: Star,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20',
    borderColor: 'border-yellow-400/30',
    label: 'Event'
  },
  manual: {
    icon: Trophy,
    color: 'text-orange-400',
    bgColor: 'bg-orange-500/20',
    borderColor: 'border-orange-400/30',
    label: 'Reward'
  }
}

const sizeConfigs = {
  sm: {
    container: 'px-3 py-2 text-sm',
    icon: 'w-4 h-4',
    xp: 'text-lg font-bold',
    close: 'w-4 h-4'
  },
  md: {
    container: 'px-4 py-3 text-base',
    icon: 'w-5 h-5',
    xp: 'text-xl font-bold',
    close: 'w-5 h-5'
  },
  lg: {
    container: 'px-6 py-4 text-lg',
    icon: 'w-6 h-6',
    xp: 'text-2xl font-bold',
    close: 'w-6 h-6'
  }
}

const positionConfigs = {
  'top-right': 'top-4 right-4',
  'top-left': 'top-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'center': 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
}

// ===== COMPONENT =====

export const XPGainNotification: React.FC<XPGainNotificationProps> = ({
  xpAmount,
  source,
  description,
  isVisible,
  onDismiss,
  duration = 4000,
  position = 'top-right',
  size = 'md',
  showCloseButton = true,
  className = ''
}) => {
  const sourceConfig = sourceConfigs[source]
  const sizeConfig = sizeConfigs[size]
  const SourceIcon = sourceConfig.icon

  // Auto-dismiss timer
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        onDismiss()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [isVisible, duration, onDismiss])

  // Animation variants
  const notificationVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
      y: position.includes('top') ? -50 : 50,
      x: position.includes('right') ? 50 : position.includes('left') ? -50 : 0
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: position.includes('top') ? -50 : 50,
      x: position.includes('right') ? 50 : position.includes('left') ? -50 : 0,
      transition: {
        duration: 0.2
      }
    }
  }

  const xpCountVariants = {
    hidden: { scale: 0 },
    visible: {
      scale: [0, 1.2, 1],
      transition: {
        duration: 0.6,
        times: [0, 0.6, 1],
        ease: "easeOut"
      }
    }
  }

  const sparkleVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: [0, 1, 0],
      scale: [0, 1, 0],
      rotate: [0, 180, 360],
      transition: {
        duration: 1.5,
        times: [0, 0.5, 1],
        ease: "easeOut"
      }
    }
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={cn(
            'fixed z-50 pointer-events-auto',
            positionConfigs[position]
          )}
          variants={notificationVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          <div className={cn(
            'relative bg-gray-900/95 backdrop-blur-sm rounded-lg border shadow-xl',
            'flex items-center gap-3 min-w-64 max-w-sm',
            sourceConfig.bgColor,
            sourceConfig.borderColor,
            sizeConfig.container,
            className
          )}>
            {/* Source icon */}
            <div className={cn(
              'flex-shrink-0 p-2 rounded-full',
              sourceConfig.bgColor
            )}>
              <SourceIcon className={cn(sizeConfig.icon, sourceConfig.color)} />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <Zap className={cn('w-4 h-4', sourceConfig.color)} />
                <motion.span
                  className={cn(sizeConfig.xp, sourceConfig.color)}
                  variants={xpCountVariants}
                  initial="hidden"
                  animate="visible"
                >
                  +{xpAmount.toLocaleString()}
                </motion.span>
                <span className={cn('text-xs font-medium', sourceConfig.color)}>
                  XP
                </span>
              </div>
              
              <p className="text-gray-300 text-sm truncate mt-1">
                {description}
              </p>
              
              <span className={cn('text-xs', sourceConfig.color)}>
                {sourceConfig.label}
              </span>
            </div>

            {/* Close button */}
            {showCloseButton && (
              <button
                onClick={onDismiss}
                className={cn(
                  'flex-shrink-0 p-1 rounded-full hover:bg-gray-700/50',
                  'transition-colors duration-200'
                )}
              >
                <X className={cn(sizeConfig.close, 'text-gray-400 hover:text-white')} />
              </button>
            )}

            {/* Sparkle effects */}
            <motion.div
              className="absolute -top-2 -right-2"
              variants={sparkleVariants}
              initial="hidden"
              animate="visible"
            >
              <Star className={cn('w-4 h-4', sourceConfig.color)} />
            </motion.div>

            <motion.div
              className="absolute -bottom-1 -left-1"
              variants={sparkleVariants}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.3 }}
            >
              <TrendingUp className={cn('w-3 h-3', sourceConfig.color)} />
            </motion.div>

            {/* Progress bar for auto-dismiss */}
            {duration > 0 && (
              <motion.div
                className={cn(
                  'absolute bottom-0 left-0 h-1 rounded-b-lg',
                  sourceConfig.bgColor
                )}
                initial={{ width: '100%' }}
                animate={{ width: '0%' }}
                transition={{ duration: duration / 1000, ease: "linear" }}
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// ===== NOTIFICATION QUEUE MANAGER =====

export const useXPNotificationQueue = () => {
  const [notifications, setNotifications] = useState<XPNotificationQueueItem[]>([])
  const [currentNotification, setCurrentNotification] = useState<XPNotificationQueueItem | null>(null)

  // Add notification to queue
  const addNotification = (notification: Omit<XPNotificationQueueItem, 'id' | 'timestamp'>) => {
    const newNotification: XPNotificationQueueItem = {
      ...notification,
      id: `xp-${Date.now()}-${Math.random()}`,
      timestamp: Date.now()
    }

    setNotifications(prev => [...prev, newNotification])
  }

  // Process queue
  useEffect(() => {
    if (!currentNotification && notifications.length > 0) {
      const [next, ...rest] = notifications
      setCurrentNotification(next)
      setNotifications(rest)
    }
  }, [notifications, currentNotification])

  // Dismiss current notification
  const dismissCurrent = () => {
    setCurrentNotification(null)
  }

  return {
    currentNotification,
    addNotification,
    dismissCurrent,
    queueLength: notifications.length
  }
}

// ===== PRESET VARIANTS =====

export const PurchaseXPNotification: React.FC<Omit<XPGainNotificationProps, 'source'>> = (props) => (
  <XPGainNotification {...props} source="purchase" />
)

export const ActivityXPNotification: React.FC<Omit<XPGainNotificationProps, 'source'>> = (props) => (
  <XPGainNotification {...props} source="activity" />
)

export const BonusXPNotification: React.FC<Omit<XPGainNotificationProps, 'source'>> = (props) => (
  <XPGainNotification {...props} source="bonus" />
)

export default XPGainNotification
