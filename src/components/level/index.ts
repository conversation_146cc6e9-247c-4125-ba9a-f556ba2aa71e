/**
 * Level System Components Index
 * 
 * Centralized exports for all level system components.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Core Components
export { 
  LevelBadge,
  CompactLevelBadge,
  DetailedLevelBadge,
  MinimalLevelBadge,
  GlowingLevelBadge
} from './LevelBadge'

export {
  LevelProgressBar,
  CompactLevelProgress,
  CircularLevelProgress,
  DetailedLevelProgress
} from './LevelProgressBar'

export {
  XPGainNotification,
  PurchaseXPNotification,
  ActivityXPNotification,
  BonusXPNotification,
  useXPNotificationQueue
} from './XPGainNotification'

export {
  LevelUpModal,
  useLevelUpModal
} from './LevelUpModal'

export {
  LevelRewardsPanel
} from './LevelRewardsPanel'

// Profile Integration
// Note: EnhancedUserProfileWithLevel removed in Phase 2 cleanup

// Types
export type {
  LevelBadgeProps
} from './LevelBadge'

export type {
  LevelProgressBarProps
} from './LevelProgressBar'

export type {
  XPGainNotificationProps,
  XPNotificationQueueItem
} from './XPGainNotification'

export type {
  LevelUpModalProps,
  LevelUpReward
} from './LevelUpModal'

export type {
  LevelRewardsPanelProps,
  LevelReward
} from './LevelRewardsPanel'
