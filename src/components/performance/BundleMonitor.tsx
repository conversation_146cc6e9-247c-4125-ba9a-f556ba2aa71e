/**
 * Bundle Monitor Component
 * 
 * Real-time bundle performance monitoring for development and production.
 * Tracks chunk loading times, sizes, and provides optimization insights.
 * 
 * Phase 2 Performance Optimization - Community System Audit
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Activity, AlertTriangle, CheckCircle, Clock, Download, Zap } from 'lucide-react'
import { getBundleAnalyzer, type BundleAnalysis } from '@/lib/performance/bundleAnalyzer'

interface BundleMonitorProps {
  enabled?: boolean
  showInProduction?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  minimized?: boolean
}

interface ChunkLoadEvent {
  name: string
  size: number
  loadTime: number
  timestamp: number
  cached: boolean
}

export default function BundleMonitor({
  enabled = process.env.NODE_ENV === 'development',
  showInProduction = false,
  position = 'bottom-right',
  minimized = true
}: BundleMonitorProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isMinimized, setIsMinimized] = useState(minimized)
  const [analysis, setAnalysis] = useState<BundleAnalysis | null>(null)
  const [recentChunks, setRecentChunks] = useState<ChunkLoadEvent[]>([])
  const [healthScore, setHealthScore] = useState(100)

  // Show monitor only in development or when explicitly enabled for production
  const shouldShow = enabled && (process.env.NODE_ENV === 'development' || showInProduction)

  useEffect(() => {
    if (!shouldShow) return

    setIsVisible(true)
    
    const analyzer = getBundleAnalyzer()
    
    // Initial analysis
    const performAnalysis = async () => {
      try {
        const bundleAnalysis = await analyzer.analyzeBundles()
        setAnalysis(bundleAnalysis)
        setHealthScore(analyzer.getBundleHealthScore(bundleAnalysis))
      } catch (error) {
        console.warn('Bundle analysis failed:', error)
      }
    }

    // Perform analysis after initial load
    setTimeout(performAnalysis, 2000)

    // Monitor new chunk loads
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          
          if (resourceEntry.name.includes('/_next/static/chunks/')) {
            const chunkName = extractChunkName(resourceEntry.name)
            const chunkEvent: ChunkLoadEvent = {
              name: chunkName,
              size: resourceEntry.transferSize || 0,
              loadTime: resourceEntry.duration,
              timestamp: Date.now(),
              cached: resourceEntry.transferSize === 0 && resourceEntry.decodedBodySize > 0
            }
            
            setRecentChunks(prev => [chunkEvent, ...prev.slice(0, 9)]) // Keep last 10
          }
        }
      }
    })

    observer.observe({ entryTypes: ['resource'] })

    return () => {
      observer.disconnect()
      analyzer.destroy()
    }
  }, [shouldShow])

  const extractChunkName = (url: string): string => {
    const match = url.match(/chunks\/([^\/]+)\.js/)
    return match ? match[1] : 'unknown'
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'top-right':
        return 'top-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
      default:
        return 'bottom-4 right-4'
    }
  }

  const getHealthColor = (score: number) => {
    if (score >= 80) return 'text-green-400'
    if (score >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getHealthIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="w-4 h-4" />
    if (score >= 60) return <Clock className="w-4 h-4" />
    return <AlertTriangle className="w-4 h-4" />
  }

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatTime = (ms: number) => {
    return ms < 1000 ? `${ms.toFixed(0)}ms` : `${(ms / 1000).toFixed(1)}s`
  }

  if (!shouldShow || !isVisible) return null

  return (
    <div className={`fixed ${getPositionClasses()} z-50 font-mono text-xs`}>
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-lg"
        >
          {/* Header */}
          <div 
            className="flex items-center justify-between p-3 cursor-pointer border-b border-gray-700"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-blue-400" />
              <span className="text-white font-semibold">Bundle Monitor</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className={`flex items-center space-x-1 ${getHealthColor(healthScore)}`}>
                {getHealthIcon(healthScore)}
                <span>{healthScore}</span>
              </div>
              <motion.div
                animate={{ rotate: isMinimized ? 0 : 180 }}
                className="text-gray-400"
              >
                ▼
              </motion.div>
            </div>
          </div>

          {/* Content */}
          <AnimatePresence>
            {!isMinimized && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="overflow-hidden"
              >
                <div className="p-3 space-y-3 max-w-sm">
                  {/* Bundle Overview */}
                  {analysis && (
                    <div className="space-y-2">
                      <h4 className="text-white font-semibold">Bundle Overview</h4>
                      <div className="grid grid-cols-2 gap-2 text-gray-300">
                        <div>
                          <div className="text-gray-400">Total Size</div>
                          <div>{formatSize(analysis.totalGzipSize)}</div>
                        </div>
                        <div>
                          <div className="text-gray-400">Chunks</div>
                          <div>{analysis.chunks.length}</div>
                        </div>
                        <div>
                          <div className="text-gray-400">Load Time</div>
                          <div>{formatTime(analysis.performance.loadTime)}</div>
                        </div>
                        <div>
                          <div className="text-gray-400">Parse Time</div>
                          <div>{formatTime(analysis.performance.parseTime)}</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Recent Chunks */}
                  {recentChunks.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-white font-semibold">Recent Chunks</h4>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {recentChunks.slice(0, 5).map((chunk, index) => (
                          <div 
                            key={`${chunk.name}-${chunk.timestamp}`}
                            className="flex items-center justify-between text-gray-300 p-1 rounded bg-gray-800/50"
                          >
                            <div className="flex items-center space-x-2 min-w-0">
                              <div className="flex items-center space-x-1">
                                {chunk.cached ? (
                                  <Zap className="w-3 h-3 text-green-400" />
                                ) : (
                                  <Download className="w-3 h-3 text-blue-400" />
                                )}
                              </div>
                              <span className="truncate text-xs">
                                {chunk.name.length > 15 ? `${chunk.name.slice(0, 15)}...` : chunk.name}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2 text-xs">
                              <span className="text-gray-400">{formatSize(chunk.size)}</span>
                              <span className={chunk.loadTime > 1000 ? 'text-red-400' : 'text-gray-400'}>
                                {formatTime(chunk.loadTime)}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Recommendations */}
                  {analysis?.recommendations && analysis.recommendations.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-white font-semibold">Recommendations</h4>
                      <div className="space-y-1 max-h-24 overflow-y-auto">
                        {analysis.recommendations.slice(0, 3).map((rec, index) => (
                          <div 
                            key={index}
                            className={`text-xs p-2 rounded ${
                              rec.priority === 'high' ? 'bg-red-900/30 text-red-300' :
                              rec.priority === 'medium' ? 'bg-yellow-900/30 text-yellow-300' :
                              'bg-blue-900/30 text-blue-300'
                            }`}
                          >
                            <div className="font-semibold">{rec.type.toUpperCase()}</div>
                            <div className="text-xs opacity-90">{rec.description}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </AnimatePresence>
    </div>
  )
}

// Export for use in development
export { BundleMonitor }
