/**
 * Privacy Level Presets Component
 * 
 * Provides easy-to-understand privacy level presets (Basic/Moderate/Strict) that
 * automatically configure multiple privacy settings. Includes impact visualization
 * and guided setup for new users.
 * 
 * Features:
 * - 3 privacy levels with clear explanations
 * - Visual impact preview of each level
 * - One-click preset application
 * - Custom level creation and modification
 * - Privacy score calculation
 * - Impact visualization with icons and descriptions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  Eye,
  Users,
  Globe,
  Lock,
  AlertTriangle,
  CheckCircle,
  Info,
  Settings,
  Zap,
  UserCheck,
  EyeOff
} from 'lucide-react'
import { PrivacySettings } from '@/types/profile'
import { DEFAULT_PRIVACY_SETTINGS } from '@/lib/privacy/privacyService'

// Privacy Level Definitions
export type PrivacyLevel = 'basic' | 'moderate' | 'strict' | 'custom'

interface PrivacyPreset {
  id: PrivacyLevel
  name: string
  description: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  color: string
  score: number
  settings: Partial<PrivacySettings>
  impacts: {
    visibility: 'high' | 'medium' | 'low'
    functionality: 'full' | 'limited' | 'restricted'
    personalization: 'full' | 'partial' | 'minimal'
    social: 'open' | 'friends' | 'private'
  }
  benefits: string[]
  tradeoffs: string[]
}

interface PrivacyLevelPresetsProps {
  currentSettings: PrivacySettings
  onPresetSelect: (preset: PrivacyPreset) => void
  onCustomize: () => void
  className?: string
}

/**
 * Privacy preset configurations
 */
const PRIVACY_PRESETS: PrivacyPreset[] = [
  {
    id: 'basic',
    name: 'Basic Privacy',
    description: 'Balanced privacy with full functionality and social features',
    icon: Globe,
    color: 'green',
    score: 40,
    settings: {
      profileVisibility: 'public',
      showEmail: false,
      showPhone: false,
      showLocation: true,
      showBio: true,
      showSocialLinks: true,
      showWebsite: true,
      showActivityStatus: true,
      showLastSeen: true,
      showOnlineStatus: true,
      showAchievements: true,
      showPoints: true,
      showOrderHistory: false,
      showWishlist: true,
      allowDirectMessages: 'everyone',
      allowFriendRequests: true,
      allowMentions: true,
      allowTagging: true,
      allowDataAnalytics: true,
      allowPersonalization: true,
      allowMarketingAnalytics: true,
      allowThirdPartySharing: false,
      allowCookieTracking: true,
      showNotificationPreviews: true,
      allowPushNotifications: true,
      allowEmailDigests: true,
      allowSearchEngineIndexing: true,
      allowProfileDiscovery: true,
      showInMemberDirectory: true,
      showInLeaderboards: true
    },
    impacts: {
      visibility: 'high',
      functionality: 'full',
      personalization: 'full',
      social: 'open'
    },
    benefits: [
      'Full social features and discoverability',
      'Personalized recommendations and content',
      'Complete platform functionality',
      'Easy for others to find and connect with you'
    ],
    tradeoffs: [
      'Higher visibility of your activity',
      'More data collection for analytics',
      'Profile appears in search results'
    ]
  },
  {
    id: 'moderate',
    name: 'Moderate Privacy',
    description: 'Balanced approach with selective sharing and enhanced privacy',
    icon: UserCheck,
    color: 'blue',
    score: 65,
    settings: {
      profileVisibility: 'friends',
      showEmail: false,
      showPhone: false,
      showLocation: false,
      showBio: true,
      showSocialLinks: true,
      showWebsite: false,
      showActivityStatus: true,
      showLastSeen: false,
      showOnlineStatus: true,
      showAchievements: true,
      showPoints: false,
      showOrderHistory: false,
      showWishlist: false,
      allowDirectMessages: 'friends',
      allowFriendRequests: true,
      allowMentions: true,
      allowTagging: false,
      allowDataAnalytics: true,
      allowPersonalization: true,
      allowMarketingAnalytics: false,
      allowThirdPartySharing: false,
      allowCookieTracking: true,
      showNotificationPreviews: false,
      allowPushNotifications: true,
      allowEmailDigests: false,
      allowSearchEngineIndexing: false,
      allowProfileDiscovery: true,
      showInMemberDirectory: false,
      showInLeaderboards: false
    },
    impacts: {
      visibility: 'medium',
      functionality: 'limited',
      personalization: 'partial',
      social: 'friends'
    },
    benefits: [
      'Profile visible only to friends',
      'Reduced marketing and tracking',
      'Selective information sharing',
      'Maintained core functionality'
    ],
    tradeoffs: [
      'Limited discoverability by new people',
      'Reduced personalization features',
      'Some social features restricted'
    ]
  },
  {
    id: 'strict',
    name: 'Strict Privacy',
    description: 'Maximum privacy protection with minimal data sharing',
    icon: EyeOff,
    color: 'red',
    score: 90,
    settings: {
      profileVisibility: 'private',
      showEmail: false,
      showPhone: false,
      showLocation: false,
      showBio: false,
      showSocialLinks: false,
      showWebsite: false,
      showActivityStatus: false,
      showLastSeen: false,
      showOnlineStatus: false,
      showAchievements: false,
      showPoints: false,
      showOrderHistory: false,
      showWishlist: false,
      allowDirectMessages: 'nobody',
      allowFriendRequests: false,
      allowMentions: false,
      allowTagging: false,
      allowDataAnalytics: false,
      allowPersonalization: false,
      allowMarketingAnalytics: false,
      allowThirdPartySharing: false,
      allowCookieTracking: false,
      showNotificationPreviews: false,
      allowPushNotifications: false,
      allowEmailDigests: false,
      allowSearchEngineIndexing: false,
      allowProfileDiscovery: false,
      showInMemberDirectory: false,
      showInLeaderboards: false
    },
    impacts: {
      visibility: 'low',
      functionality: 'restricted',
      personalization: 'minimal',
      social: 'private'
    },
    benefits: [
      'Maximum privacy protection',
      'Minimal data collection',
      'No third-party sharing',
      'Complete anonymity'
    ],
    tradeoffs: [
      'Significantly reduced functionality',
      'No social features or connections',
      'Limited personalization',
      'Harder for legitimate contacts to find you'
    ]
  }
]

/**
 * Calculate privacy score based on current settings
 */
const calculatePrivacyScore = (settings: PrivacySettings): number => {
  let score = 0
  const maxScore = 100

  // Profile visibility (20 points)
  if (settings.profileVisibility === 'private') score += 20
  else if (settings.profileVisibility === 'friends') score += 10
  else score += 0

  // Personal information visibility (15 points)
  if (!settings.showEmail) score += 3
  if (!settings.showPhone) score += 3
  if (!settings.showLocation) score += 3
  if (!settings.showBio) score += 3
  if (!settings.showSocialLinks) score += 3

  // Activity visibility (15 points)
  if (!settings.showActivityStatus) score += 3
  if (!settings.showLastSeen) score += 3
  if (!settings.showOnlineStatus) score += 3
  if (!settings.showAchievements) score += 3
  if (!settings.showPoints) score += 3

  // Communication restrictions (15 points)
  if (settings.allowDirectMessages === 'nobody') score += 8
  else if (settings.allowDirectMessages === 'friends') score += 4
  if (!settings.allowFriendRequests) score += 4
  if (!settings.allowMentions) score += 3

  // Data sharing restrictions (20 points)
  if (!settings.allowDataAnalytics) score += 5
  if (!settings.allowPersonalization) score += 3
  if (!settings.allowMarketingAnalytics) score += 4
  if (!settings.allowThirdPartySharing) score += 4
  if (!settings.allowCookieTracking) score += 4

  // Search and discovery restrictions (15 points)
  if (!settings.allowSearchEngineIndexing) score += 4
  if (!settings.allowProfileDiscovery) score += 4
  if (!settings.showInMemberDirectory) score += 4
  if (!settings.showInLeaderboards) score += 3

  return Math.min(score, maxScore)
}

/**
 * Determine current privacy level based on settings
 */
const getCurrentPrivacyLevel = (settings: PrivacySettings): PrivacyLevel => {
  const score = calculatePrivacyScore(settings)
  
  if (score >= 80) return 'strict'
  if (score >= 50) return 'moderate'
  if (score >= 30) return 'basic'
  return 'custom'
}

/**
 * Privacy Impact Visualization Component
 */
const PrivacyImpactVisualization: React.FC<{
  preset: PrivacyPreset
  isSelected: boolean
}> = ({ preset, isSelected }) => {
  const getImpactColor = (level: string) => {
    switch (level) {
      case 'high': case 'full': case 'open': return 'text-green-400'
      case 'medium': case 'limited': case 'partial': case 'friends': return 'text-yellow-400'
      case 'low': case 'restricted': case 'minimal': case 'private': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getImpactIcon = (category: string, level: string) => {
    switch (category) {
      case 'visibility': return level === 'high' ? Eye : level === 'medium' ? Users : EyeOff
      case 'functionality': return level === 'full' ? Zap : level === 'limited' ? Settings : Lock
      case 'personalization': return level === 'full' ? CheckCircle : level === 'partial' ? Info : AlertTriangle
      case 'social': return level === 'open' ? Users : level === 'friends' ? UserCheck : Lock
      default: return Info
    }
  }

  return (
    <div className="grid grid-cols-2 gap-3 mt-4">
      {Object.entries(preset.impacts).map(([category, level]) => {
        const IconComponent = getImpactIcon(category, level)
        return (
          <div key={category} className="flex items-center space-x-2">
            <IconComponent size={16} className={getImpactColor(level)} />
            <div>
              <div className="text-sm font-medium text-white capitalize">{category}</div>
              <div className={`text-xs capitalize ${getImpactColor(level)}`}>{level}</div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

/**
 * Privacy Preset Card Component
 */
const PrivacyPresetCard: React.FC<{
  preset: PrivacyPreset
  isSelected: boolean
  isCurrent: boolean
  onSelect: () => void
}> = ({ preset, isSelected, isCurrent, onSelect }) => {
  const colorClasses = {
    green: 'border-green-500 bg-green-500/10',
    blue: 'border-blue-500 bg-blue-500/10',
    red: 'border-red-500 bg-red-500/10'
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`
        relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-200
        ${isSelected || isCurrent 
          ? colorClasses[preset.color as keyof typeof colorClasses]
          : 'border-gray-600 bg-gray-800 hover:border-gray-500'
        }
      `}
      onClick={onSelect}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <preset.icon size={24} className={isSelected || isCurrent ? `text-${preset.color}-400` : 'text-gray-400'} />
          <div>
            <h3 className="text-lg font-semibold text-white">{preset.name}</h3>
            <div className="flex items-center space-x-2">
              <Shield size={14} className="text-gray-400" />
              <span className="text-sm text-gray-400">Privacy Score: {preset.score}%</span>
            </div>
          </div>
        </div>
        {isCurrent && (
          <div className="bg-accent-500 text-white text-xs px-2 py-1 rounded-full">
            Current
          </div>
        )}
      </div>

      {/* Description */}
      <p className="text-gray-300 text-sm mb-4">{preset.description}</p>

      {/* Impact Visualization */}
      <PrivacyImpactVisualization preset={preset} isSelected={isSelected || isCurrent} />

      {/* Benefits and Tradeoffs */}
      <div className="mt-4 space-y-3">
        <div>
          <h4 className="text-sm font-medium text-green-400 mb-2">Benefits:</h4>
          <ul className="text-xs text-gray-300 space-y-1">
            {preset.benefits.slice(0, 2).map((benefit, index) => (
              <li key={index} className="flex items-start space-x-2">
                <CheckCircle size={12} className="text-green-400 mt-0.5 flex-shrink-0" />
                <span>{benefit}</span>
              </li>
            ))}
          </ul>
        </div>
        
        {preset.tradeoffs.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-yellow-400 mb-2">Considerations:</h4>
            <ul className="text-xs text-gray-300 space-y-1">
              {preset.tradeoffs.slice(0, 2).map((tradeoff, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <AlertTriangle size={12} className="text-yellow-400 mt-0.5 flex-shrink-0" />
                  <span>{tradeoff}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </motion.div>
  )
}

/**
 * Main Privacy Level Presets Component
 */
const PrivacyLevelPresets: React.FC<PrivacyLevelPresetsProps> = ({
  currentSettings,
  onPresetSelect,
  onCustomize,
  className = ''
}) => {
  const [selectedPreset, setSelectedPreset] = useState<PrivacyLevel | null>(null)
  
  const currentLevel = useMemo(() => getCurrentPrivacyLevel(currentSettings), [currentSettings])
  const currentScore = useMemo(() => calculatePrivacyScore(currentSettings), [currentSettings])

  const handlePresetSelect = (preset: PrivacyPreset) => {
    setSelectedPreset(preset.id)
    onPresetSelect(preset)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Choose Your Privacy Level</h2>
        <p className="text-gray-400">
          Select a preset that matches your privacy preferences, or customize your own settings.
        </p>
        <div className="mt-4 inline-flex items-center space-x-2 bg-gray-800 px-4 py-2 rounded-lg">
          <Shield size={16} className="text-accent-400" />
          <span className="text-white font-medium">Current Privacy Score: {currentScore}%</span>
        </div>
      </div>

      {/* Privacy Presets */}
      <div className="grid md:grid-cols-3 gap-6">
        {PRIVACY_PRESETS.map((preset) => (
          <PrivacyPresetCard
            key={preset.id}
            preset={preset}
            isSelected={selectedPreset === preset.id}
            isCurrent={currentLevel === preset.id}
            onSelect={() => handlePresetSelect(preset)}
          />
        ))}
      </div>

      {/* Custom Option */}
      <div className="text-center">
        <button
          onClick={onCustomize}
          className="inline-flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
        >
          <Settings size={16} />
          <span>Customize Individual Settings</span>
        </button>
        <p className="text-sm text-gray-400 mt-2">
          Fine-tune your privacy settings with granular control over each option.
        </p>
      </div>
    </div>
  )
}

export default PrivacyLevelPresets
export type { PrivacyLevel, PrivacyPreset, PrivacyLevelPresetsProps }
export { PRIVACY_PRESETS, calculatePrivacyScore, getCurrentPrivacyLevel }
