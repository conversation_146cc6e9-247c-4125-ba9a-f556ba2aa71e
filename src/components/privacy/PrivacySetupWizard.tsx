/**
 * Privacy Setup Wizard Component
 * 
 * Guided privacy setup wizard for new users that simplifies the complex privacy
 * configuration process into easy-to-understand steps with visual feedback.
 * 
 * Features:
 * - Multi-step wizard with progress tracking
 * - Visual privacy impact preview
 * - Contextual help and explanations
 * - Skip option for advanced users
 * - Integration with privacy presets
 * - Onboarding completion tracking
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  Eye,
  Users,
  Globe,
  Lock,
  ChevronRight,
  ChevronLeft,
  Check,
  X,
  Info,
  AlertTriangle,
  Sparkles,
  UserPlus,
  MessageCircle,
  BarChart3
} from 'lucide-react'
import { PrivacySettings } from '@/types/profile'
import { PrivacyLevel, PRIVACY_PRESETS, PrivacyPreset } from './PrivacyLevelPresets'

interface PrivacySetupWizardProps {
  isOpen: boolean
  onClose: () => void
  onComplete: (settings: Partial<PrivacySettings>) => void
  onSkip: () => void
  currentSettings?: PrivacySettings
  className?: string
}

interface WizardStep {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  component: React.ComponentType<any>
}

/**
 * Step 1: Welcome and Introduction
 */
const WelcomeStep: React.FC<{
  onNext: () => void
  onSkip: () => void
}> = ({ onNext, onSkip }) => (
  <div className="text-center space-y-6">
    <div className="mx-auto w-20 h-20 bg-accent-500/20 rounded-full flex items-center justify-center">
      <Shield size={40} className="text-accent-400" />
    </div>
    
    <div>
      <h2 className="text-2xl font-bold text-white mb-3">Welcome to Syndicaps!</h2>
      <p className="text-gray-300 text-lg mb-4">
        Let's set up your privacy preferences to ensure you're comfortable with how your information is shared.
      </p>
      <p className="text-gray-400 text-sm">
        This will only take 2-3 minutes and you can change these settings anytime.
      </p>
    </div>

    <div className="bg-gray-800 rounded-lg p-4 space-y-3">
      <h3 className="text-white font-medium flex items-center">
        <Info size={16} className="mr-2 text-blue-400" />
        What we'll cover:
      </h3>
      <ul className="text-sm text-gray-300 space-y-2">
        <li className="flex items-center">
          <Eye size={14} className="mr-2 text-gray-400" />
          Who can see your profile and activity
        </li>
        <li className="flex items-center">
          <Users size={14} className="mr-2 text-gray-400" />
          How others can interact with you
        </li>
        <li className="flex items-center">
          <BarChart3 size={14} className="mr-2 text-gray-400" />
          Data usage and personalization
        </li>
      </ul>
    </div>

    <div className="flex space-x-4">
      <button
        onClick={onSkip}
        className="flex-1 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
      >
        Skip Setup
      </button>
      <button
        onClick={onNext}
        className="flex-1 px-6 py-3 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors flex items-center justify-center"
      >
        Get Started
        <ChevronRight size={16} className="ml-2" />
      </button>
    </div>
  </div>
)

/**
 * Step 2: Privacy Level Selection
 */
const PrivacyLevelStep: React.FC<{
  selectedLevel: PrivacyLevel | null
  onLevelSelect: (level: PrivacyLevel) => void
  onNext: () => void
  onBack: () => void
}> = ({ selectedLevel, onLevelSelect, onNext, onBack }) => {
  const presets = PRIVACY_PRESETS.slice(0, 3) // Basic, Moderate, Strict

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-3">Choose Your Privacy Level</h2>
        <p className="text-gray-300">
          Select the level that best matches your comfort with sharing information.
        </p>
      </div>

      <div className="space-y-4">
        {presets.map((preset) => (
          <motion.div
            key={preset.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`
              p-4 rounded-lg border-2 cursor-pointer transition-all duration-200
              ${selectedLevel === preset.id
                ? `border-${preset.color}-500 bg-${preset.color}-500/10`
                : 'border-gray-600 bg-gray-800 hover:border-gray-500'
              }
            `}
            onClick={() => onLevelSelect(preset.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <preset.icon 
                  size={24} 
                  className={selectedLevel === preset.id ? `text-${preset.color}-400` : 'text-gray-400'} 
                />
                <div>
                  <h3 className="text-lg font-semibold text-white">{preset.name}</h3>
                  <p className="text-gray-300 text-sm">{preset.description}</p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                    <span>Privacy Score: {preset.score}%</span>
                    <span>•</span>
                    <span>Social: {preset.impacts.social}</span>
                    <span>•</span>
                    <span>Functionality: {preset.impacts.functionality}</span>
                  </div>
                </div>
              </div>
              {selectedLevel === preset.id && (
                <Check size={20} className={`text-${preset.color}-400`} />
              )}
            </div>
          </motion.div>
        ))}
      </div>

      <div className="flex space-x-4">
        <button
          onClick={onBack}
          className="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors flex items-center"
        >
          <ChevronLeft size={16} className="mr-2" />
          Back
        </button>
        <button
          onClick={onNext}
          disabled={!selectedLevel}
          className="flex-1 px-6 py-3 bg-accent-500 hover:bg-accent-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center justify-center"
        >
          Continue
          <ChevronRight size={16} className="ml-2" />
        </button>
      </div>
    </div>
  )
}

/**
 * Step 3: Impact Preview and Confirmation
 */
const ConfirmationStep: React.FC<{
  selectedPreset: PrivacyPreset | null
  onConfirm: () => void
  onBack: () => void
  onCustomize: () => void
}> = ({ selectedPreset, onConfirm, onBack, onCustomize }) => {
  if (!selectedPreset) return null

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-3">Review Your Privacy Settings</h2>
        <p className="text-gray-300">
          Here's what your <span className="text-accent-400 font-medium">{selectedPreset.name}</span> settings will do:
        </p>
      </div>

      {/* Selected Preset Summary */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center space-x-4 mb-4">
          <selectedPreset.icon size={32} className={`text-${selectedPreset.color}-400`} />
          <div>
            <h3 className="text-xl font-semibold text-white">{selectedPreset.name}</h3>
            <div className="flex items-center space-x-2">
              <Shield size={16} className="text-gray-400" />
              <span className="text-gray-400">Privacy Score: {selectedPreset.score}%</span>
            </div>
          </div>
        </div>

        {/* Benefits */}
        <div className="mb-4">
          <h4 className="text-green-400 font-medium mb-2 flex items-center">
            <Check size={16} className="mr-2" />
            What you'll get:
          </h4>
          <ul className="space-y-2">
            {selectedPreset.benefits.map((benefit, index) => (
              <li key={index} className="text-gray-300 text-sm flex items-start">
                <Check size={14} className="text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        {/* Tradeoffs */}
        {selectedPreset.tradeoffs.length > 0 && (
          <div>
            <h4 className="text-yellow-400 font-medium mb-2 flex items-center">
              <AlertTriangle size={16} className="mr-2" />
              Keep in mind:
            </h4>
            <ul className="space-y-2">
              {selectedPreset.tradeoffs.map((tradeoff, index) => (
                <li key={index} className="text-gray-300 text-sm flex items-start">
                  <AlertTriangle size={14} className="text-yellow-400 mr-2 mt-0.5 flex-shrink-0" />
                  {tradeoff}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Customization Option */}
      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info size={20} className="text-blue-400 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="text-blue-400 font-medium mb-1">Want more control?</h4>
            <p className="text-gray-300 text-sm mb-3">
              You can customize individual settings after setup or choose to fine-tune them now.
            </p>
            <button
              onClick={onCustomize}
              className="text-blue-400 hover:text-blue-300 text-sm font-medium"
            >
              Customize Individual Settings →
            </button>
          </div>
        </div>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={onBack}
          className="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors flex items-center"
        >
          <ChevronLeft size={16} className="mr-2" />
          Back
        </button>
        <button
          onClick={onConfirm}
          className="flex-1 px-6 py-3 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors flex items-center justify-center"
        >
          <Sparkles size={16} className="mr-2" />
          Apply Settings
        </button>
      </div>
    </div>
  )
}

/**
 * Main Privacy Setup Wizard Component
 */
const PrivacySetupWizard: React.FC<PrivacySetupWizardProps> = ({
  isOpen,
  onClose,
  onComplete,
  onSkip,
  currentSettings,
  className = ''
}) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedLevel, setSelectedLevel] = useState<PrivacyLevel | null>(null)
  const [selectedPreset, setSelectedPreset] = useState<PrivacyPreset | null>(null)

  const steps = [
    { component: WelcomeStep },
    { component: PrivacyLevelStep },
    { component: ConfirmationStep }
  ]

  const handleNext = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }, [currentStep, steps.length])

  const handleBack = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }, [currentStep])

  const handleLevelSelect = useCallback((level: PrivacyLevel) => {
    setSelectedLevel(level)
    const preset = PRIVACY_PRESETS.find(p => p.id === level)
    setSelectedPreset(preset || null)
  }, [])

  const handleConfirm = useCallback(() => {
    if (selectedPreset) {
      onComplete(selectedPreset.settings)
    }
  }, [selectedPreset, onComplete])

  const handleSkip = useCallback(() => {
    onSkip()
  }, [onSkip])

  const handleCustomize = useCallback(() => {
    onClose()
    // Navigate to detailed privacy settings
    window.location.href = '/profile/privacy'
  }, [onClose])

  if (!isOpen) return null

  const CurrentStepComponent = steps[currentStep].component

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className={`bg-gray-900 rounded-xl shadow-2xl border border-gray-700 w-full max-w-2xl max-h-[90vh] overflow-y-auto ${className}`}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700">
            <div className="flex items-center space-x-3">
              <Shield size={24} className="text-accent-400" />
              <div>
                <h1 className="text-xl font-bold text-white">Privacy Setup</h1>
                <p className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          {/* Progress Bar */}
          <div className="px-6 py-4">
            <div className="w-full bg-gray-700 rounded-full h-2">
              <motion.div
                className="bg-accent-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>

          {/* Step Content */}
          <div className="p-6">
            <CurrentStepComponent
              onNext={handleNext}
              onBack={handleBack}
              onSkip={handleSkip}
              onConfirm={handleConfirm}
              onCustomize={handleCustomize}
              selectedLevel={selectedLevel}
              onLevelSelect={handleLevelSelect}
              selectedPreset={selectedPreset}
            />
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default PrivacySetupWizard
export type { PrivacySetupWizardProps }
