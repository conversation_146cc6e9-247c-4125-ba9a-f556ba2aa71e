/**
 * ProductCard Component
 *
 * Displays a product in a card format with image, name, price, and action buttons.
 * Handles both regular products and raffle products with different UI states.
 * Includes hover animations and responsive design.
 *
 * Features:
 * - Product image with hover zoom effect
 * - Add to cart functionality for regular products
 * - Join raffle button for raffle products
 * - Sold out and limited edition badges
 * - Responsive design with motion animations
 */

import React, { useState, useEffect, memo } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useCartStore } from '../../store/cartStore';
import { useWishlistStore } from '../../store/wishlistStore';
import { Product } from '../../lib/firestore';
import { Timer, Heart, Loader2, Share2, Star, Eye, ShoppingCart, Gift } from 'lucide-react';
import { db } from '../../lib/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import Image from 'next/image';
import RewardProductBadge from '../shop/RewardProductBadge';
import { useUser } from '../../lib/useUser';
import { usePointHistory } from '../../hooks/usePointHistory';
import { useRewardCartStore } from '../../store/rewardCartStore';
import { useOptimisticCart, useOptimisticWishlist } from '../../hooks/useOptimisticUpdate';
import SocialShare, { useProductShare } from '../social/SocialShare';
import { Reward } from '../../lib/firestore';
import toast from 'react-hot-toast';
import ProductBadge, { getProductBadges } from '../ui/ProductBadge';
import { cn } from '@/lib/utils';

/**
 * Props for ProductCard component
 */
interface ProductCardProps {
  /** Product data to display */
  product: Product;
  /** Card display variant */
  variant?: 'grid' | 'list' | 'featured';
  /** Card size */
  size?: 'sm' | 'md' | 'lg';
  /** Show quick actions overlay */
  showQuickActions?: boolean;
  /** Enable hover effects */
  enableHover?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Quick view callback */
  onQuickView?: (product: Product) => void;
  /** Enhanced mode with advanced features */
  enhanced?: boolean;
}

/**
 * ProductCard component for displaying individual products
 *
 * @param props - Component props
 * @param props.product - Product data to display
 * @returns JSX.Element - Rendered product card
 */
const ProductCard: React.FC<ProductCardProps> = ({
  product,
  variant = 'grid',
  size = 'md',
  showQuickActions = false,
  enableHover = true,
  className = '',
  onQuickView,
  enhanced = false
}) => {
  const addItem = useCartStore(state => state.addItem);
  const addReward = useRewardCartStore(state => state.addReward);
  const { toggleWishlist, isInWishlist } = useWishlistStore();
  const { addItemOptimistically, isItemPending } = useOptimisticCart();
  const { toggleWishlistOptimistically, isTogglePending } = useOptimisticWishlist();
  const { createProductShareContent } = useProductShare();
  const { user } = useUser();
  const { currentBalance } = usePointHistory(user?.uid || null);
  const [raffleStatus, setRaffleStatus] = useState<'upcoming' | 'active' | 'ended' | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);

  // Enhanced features state
  const [imageIndex, setImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [showQuickView, setShowQuickView] = useState(false);

  // Enhanced card styling variants
  const cardVariants = {
    grid: 'flex flex-col',
    list: 'flex flex-row gap-4 p-4',
    featured: 'flex flex-col relative'
  };

  const sizeClasses = {
    sm: 'max-w-xs',
    md: 'max-w-sm',
    lg: 'max-w-md'
  };

  /**
   * Handles adding product to cart with optimistic update
   * Prevents event propagation to avoid navigation when clicking add to cart
   *
   * @param e - Mouse click event
   */
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();

    try {
      const optimisticUpdate = addItemOptimistically(
        product.id,
        async () => {
          addItem(product);
        }
      );

      await optimisticUpdate.execute();

      // Show success notification
      toast.success(`${product.name} added to cart!`, {
        duration: 3000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add item to cart. Please try again.', {
        duration: 4000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      });
    }
  };

  /**
   * Handles adding reward to reward cart
   * Prevents event propagation to avoid navigation when clicking add to reward cart
   *
   * @param e - Mouse click event
   */
  const handleAddToRewardCart = (e: React.MouseEvent) => {
    e.preventDefault();

    try {
      if (isRewardProduct && product.pointsCost) {
        // Convert product to reward format
        const reward: Reward = {
          id: product.id,
          name: product.name,
          description: product.description,
          pointsCost: product.pointsCost,
          type: 'product',
          value: product.price,
          isActive: !product.soldOut,
          stock: product.stock,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt
        };
        addReward(reward);

        // Show success notification
        toast.success(`${product.name} added to reward cart!`, {
          duration: 3000,
          style: {
            background: '#1f2937',
            color: '#fff',
            border: '1px solid #374151'
          }
        });
      }
    } catch (error) {
      console.error('Error adding to reward cart:', error);
      toast.error('Failed to add item to reward cart. Please try again.', {
        duration: 4000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      });
    }
  };

  // Fetch raffle data if product is a raffle
  useEffect(() => {
    const fetchRaffleStatus = async () => {
      if (product.isRaffle) {
        try {
          const raffleQuery = query(
            collection(db, 'raffles'),
            where('productId', '==', product.id)
          );
          const raffleSnapshot = await getDocs(raffleQuery);

          if (!raffleSnapshot.empty) {
            const raffle = raffleSnapshot.docs[0].data();

            // Determine raffle status based on current time
            const now = new Date();
            const startDate = raffle.startDate?.toDate();
            const endDate = raffle.endDate?.toDate();

            if (startDate && endDate) {
              if (now < startDate) {
                setRaffleStatus('upcoming');
              } else if (now >= startDate && now <= endDate) {
                setRaffleStatus('active');
              } else {
                setRaffleStatus('ended');
              }
            }
          }
        } catch (error) {
          console.error('Error fetching raffle status:', error);
        }
      }
    };

    fetchRaffleStatus();
  }, [product.id, product.isRaffle]);

  /** Check if raffle is currently active */
  const isRaffleActive = product.isRaffle && raffleStatus === 'active';

  /** Check if this is a reward product */
  const isRewardProduct = product.pointsCost && product.pointsCost > 0;

  /** Check if user can afford this reward */
  const canAffordReward = isRewardProduct ? (currentBalance || 0) >= (product.pointsCost || 0) : true;

  /** Check if product is in wishlist */
  const inWishlist = isInWishlist(product.id);

  /** Check if operations are pending */
  const isAddingToCart = isItemPending(product.id);
  const isTogglingWishlist = isTogglePending(product.id);

  /**
   * Handle wishlist toggle with optimistic update
   */
  const handleWishlistToggle = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const optimisticUpdate = toggleWishlistOptimistically(
      product.id,
      inWishlist,
      async () => {
        toggleWishlist(product);
      }
    );

    await optimisticUpdate.execute();
  };

  // Enhanced features handlers
  const handleQuickView = () => {
    if (onQuickView) {
      onQuickView(product);
    } else {
      setShowQuickView(true);
    }
  };

  const handleImageHover = (index: number) => {
    if (enhanced && product.gallery && product.gallery.length > 1) {
      setImageIndex(index);
    }
  };

  // Get product badges for enhanced mode
  const productBadges = enhanced ? getProductBadges(product) : [];

  return (
    <motion.article
      whileHover={enableHover ? { y: -5 } : {}}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "bg-gray-900 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 relative",
        cardVariants[variant],
        sizeClasses[size],
        className
      )}
      role="article"
      aria-labelledby={`product-title-${product.id}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/shop/${product.id}`} className="block relative overflow-hidden" aria-label={`View details for ${product.name}`}>
        <figure className="aspect-w-1 aspect-h-1 relative overflow-hidden bg-gray-800 h-64 sm:h-72 lg:h-80">
          <Image
            src={product.image}
            alt={`${product.name} - Artisan keycap product image`}
            width={400}
            height={400}
            className="w-full h-full object-cover transform transition-transform duration-500 hover:scale-110"
            loading="lazy"
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
          />
          {product.soldOut && !product.isRaffle && (
            <div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center" role="status" aria-label="Product sold out">
              <span className="text-white font-medium text-lg">Sold Out</span>
            </div>
          )}

          {product.isRaffle && (
            <div className="absolute top-4 right-4 bg-neon-purple text-white text-xs px-2 py-1 rounded-full flex items-center animate-pulse-neon" role="status" aria-label="Raffle product">
              <Timer size={12} className="mr-1" aria-hidden="true" />
              Raffle
            </div>
          )}
          {isRewardProduct && (
            <div className="absolute top-4 right-4 z-10">
              <RewardProductBadge
                pointsCost={product.pointsCost || 0}
                rewardType="product"
                isAvailable={!product.soldOut}
                canAfford={canAffordReward}
                size="sm"
              />
            </div>
          )}

          {/* Wishlist Button */}
          <button
            onClick={handleWishlistToggle}
            disabled={isTogglingWishlist}
            className={`absolute top-4 left-4 p-2 rounded-full transition-all duration-200 z-10 ${
              inWishlist
                ? 'bg-red-500 text-white shadow-lg'
                : 'bg-black/50 text-white hover:bg-black/70'
            } ${isTogglingWishlist ? 'opacity-75 cursor-not-allowed' : ''}`}
            aria-label={inWishlist ? `Remove ${product.name} from wishlist` : `Add ${product.name} to wishlist`}
          >
            {isTogglingWishlist ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <Heart
                size={16}
                className={`transition-transform duration-200 ${inWishlist ? 'fill-current scale-110' : ''}`}
              />
            )}
          </button>

          {/* Enhanced Quick Actions Overlay */}
          {enhanced && showQuickActions && isHovered && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/60 flex items-center justify-center gap-2 z-20"
            >
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleQuickView();
                }}
                className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-all"
                aria-label={`Quick view ${product.name}`}
              >
                <Eye size={20} />
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleAddToCart(e);
                }}
                className="p-3 bg-accent-500 rounded-full text-white hover:bg-accent-600 transition-all"
                aria-label={`Add ${product.name} to cart`}
              >
                <ShoppingCart size={20} />
              </button>
            </motion.div>
          )}

          {/* Enhanced Product Badges */}
          {enhanced && productBadges.length > 0 && (
            <div className="absolute top-2 right-2 flex flex-col gap-1 z-10">
              {productBadges.slice(0, 2).map((badge, index) => (
                <ProductBadge key={index} type={badge} />
              ))}
            </div>
          )}
        </figure>
      </Link>

      {/* Product Info Overlay - Bottom positioned like reference */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-3 sm:p-4">
        <div className="flex justify-between items-end gap-2 sm:gap-4">
          {/* Left side: Product name, artist, and price */}
          <div className="flex-1 min-w-0">
            <Link href={`/shop/${product.id}`} className="block">
              <h3 id={`product-title-${product.id}`} className="text-white font-medium text-base sm:text-lg leading-tight mb-1 lg:mb-2 lg:px-1 hover:text-accent-400 transition-colors">
                {product.name}
              </h3>
            </Link>

            {/* Artist/Brand name - using "Sirius" as example or product category */}
            <p className="text-gray-400 text-xs sm:text-sm mb-2 lg:px-1">
              {product.category || 'Artisan'}
            </p>

            {/* Enhanced Rating Display */}
            {enhanced && (
              <div className="flex items-center gap-2 mb-2 lg:px-1">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      size={12}
                      className={cn(
                        i < 4
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-600'
                      )}
                    />
                  ))}
                </div>
                <span className="text-gray-400 text-xs">(24)</span>
              </div>
            )}

            {/* Price */}
            <div className="flex items-center space-x-2">
              {isRewardProduct ? (
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 line-through text-sm">${product.price.toFixed(2)}</span>
                  <RewardProductBadge
                    pointsCost={product.pointsCost || 0}
                    rewardType="product"
                    isAvailable={!product.soldOut}
                    canAfford={canAffordReward}
                    size="sm"
                  />
                </div>
              ) : (
                <span className="text-green-400 font-bold text-lg sm:text-xl" aria-label={`Price: $${product.price.toFixed(2)}`}>
                  ${product.price.toFixed(2)}
                </span>
              )}
            </div>
          </div>

          {/* Right side: Action button */}
          <div className="flex-shrink-0">
            {product.isRaffle ? (
              <Link
                href={`/shop/${product.id}`}
                className={`px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-300 min-h-[44px] flex items-center justify-center whitespace-nowrap ${
                  raffleStatus === 'active'
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : raffleStatus === 'upcoming'
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-600 text-gray-300 cursor-not-allowed'
                }`}
                aria-label={`${
                  raffleStatus === 'upcoming' ? 'Get notified when raffle starts for' :
                  raffleStatus === 'active' ? 'Join raffle for' :
                  'Raffle has ended for'
                } ${product.name}`}
                role="button"
              >
                {raffleStatus === 'upcoming' ? 'NOTIFY ME' :
                 raffleStatus === 'active' ? 'JOIN RAFFLE' :
                 'RAFFLE ENDED'}
              </Link>
            ) : isRewardProduct ? (
              <button
                onClick={handleAddToRewardCart}
                disabled={product.soldOut || !canAffordReward}
                className={`px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors min-h-[44px] touch-manipulation whitespace-nowrap ${
                  product.soldOut || !canAffordReward
                    ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                    : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white'
                }`}
                aria-label={
                  product.soldOut
                    ? `${product.name} is sold out`
                    : !canAffordReward
                    ? `Insufficient points for ${product.name}`
                    : `Add ${product.name} to reward cart`
                }
              >
                <span className="hidden sm:inline">{product.soldOut ? 'OUT OF STOCK' : !canAffordReward ? 'INSUFFICIENT POINTS' : 'ADD TO REWARD CART'}</span>
                <span className="sm:hidden">{product.soldOut ? 'OUT OF STOCK' : !canAffordReward ? 'NO POINTS' : 'ADD TO CART'}</span>
              </button>
            ) : (
              <button
                onClick={handleAddToCart}
                disabled={product.soldOut || isAddingToCart}
                className={`px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors min-h-[44px] touch-manipulation flex items-center justify-center space-x-1 sm:space-x-2 whitespace-nowrap ${
                  product.soldOut || isAddingToCart
                    ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                    : 'bg-accent-500 text-white hover:bg-accent-600'
                }`}
                aria-label={product.soldOut ? `${product.name} is sold out` : `Add ${product.name} to cart`}
              >
                {isAddingToCart && <Loader2 size={16} className="animate-spin" />}
                <span>{product.soldOut ? 'OUT OF STOCK' : isAddingToCart ? 'ADDING...' : 'ADD TO CART'}</span>
              </button>
            )}
          </div>
        </div>

        {/* Share Button - positioned in top right of overlay */}
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setShowShareModal(true);
          }}
          className="absolute top-4 right-4 text-gray-400 hover:text-accent-400 transition-colors p-2 bg-black/50 rounded-full"
          aria-label={`Share ${product.name}`}
        >
          <Share2 size={16} />
        </button>
      </div>

      {/* Social Share Modal */}
      {showShareModal && (
        <SocialShare
          content={createProductShareContent(product)}
          modal={true}
          trigger={null}
          onShare={(platform) => {
            console.log(`Shared ${product.name} on ${platform}`);
            setShowShareModal(false);
          }}
        />
      )}
    </motion.article>
  );
};

// Memoize ProductCard for performance optimization
export default memo(ProductCard);