/**
 * AddressForm Component
 * Comprehensive form for adding and editing user addresses
 */

import React, { useState, useEffect } from 'react'
import { UserAddress, AddressType } from '@/types/profile'
import { validateAddress } from '@/lib/addressManagement'
import {
  MapPin,
  User,
  Phone,
  Globe,
  Home,
  Building,
  Star,
  AlertCircle,
  Check,
  X
} from 'lucide-react'

interface AddressFormProps {
  address?: UserAddress | null
  onSave: (addressData: Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

const ADDRESS_TYPES: { value: AddressType; label: string; description: string }[] = [
  { value: 'shipping', label: 'Shipping Only', description: 'Use for deliveries only' },
  { value: 'billing', label: 'Billing Only', description: 'Use for payment processing only' },
  { value: 'both', label: 'Shipping & Billing', description: 'Use for both deliveries and billing' }
]

const COMMON_LABELS = [
  { value: 'Home', icon: Home },
  { value: 'Work', icon: Building },
  { value: 'Other', icon: MapPin }
]

const COUNTRIES = [
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'JP', name: 'Japan' },
  { code: 'KR', name: 'South Korea' },
  { code: 'SG', name: 'Singapore' },
  { code: 'NL', name: 'Netherlands' }
]

export default function AddressForm({ address, onSave, onCancel, loading = false }: AddressFormProps) {
  const [formData, setFormData] = useState({
    type: 'both' as AddressType,
    label: 'Home',
    fullName: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'US',
    phone: '',
    isDefault: false
  })

  const [errors, setErrors] = useState<string[]>([])
  const [customLabel, setCustomLabel] = useState('')
  const [showCustomLabel, setShowCustomLabel] = useState(false)

  // Initialize form data when address prop changes
  useEffect(() => {
    if (address) {
      setFormData({
        type: address.type,
        label: address.label,
        fullName: address.fullName,
        addressLine1: address.addressLine1,
        addressLine2: address.addressLine2 || '',
        city: address.city,
        state: address.state,
        postalCode: address.postalCode,
        country: address.country,
        phone: address.phone || '',
        isDefault: address.isDefault
      })
      
      // Check if label is custom
      const isCommonLabel = COMMON_LABELS.some(l => l.value === address.label)
      if (!isCommonLabel) {
        setCustomLabel(address.label)
        setShowCustomLabel(true)
      }
    }
  }, [address])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([])
    }
  }

  const handleLabelChange = (label: string) => {
    if (label === 'Other') {
      setShowCustomLabel(true)
      setFormData(prev => ({ ...prev, label: customLabel || 'Other' }))
    } else {
      setShowCustomLabel(false)
      setFormData(prev => ({ ...prev, label }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const addressData = {
      ...formData,
      label: showCustomLabel ? customLabel : formData.label
    }
    
    // Validate the address
    const validationErrors = validateAddress(addressData)
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }
    
    try {
      await onSave(addressData)
    } catch (error) {
      setErrors(['Failed to save address. Please try again.'])
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <MapPin className="w-5 h-5" />
          {address ? 'Edit Address' : 'Add New Address'}
        </h3>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-700"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {errors.length > 0 && (
        <div className="mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-red-400 font-medium mb-1">Please fix the following errors:</h4>
              <ul className="text-red-300 text-sm space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Address Type */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-3">
            Address Type
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {ADDRESS_TYPES.map(({ value, label, description }) => (
              <button
                key={value}
                type="button"
                onClick={() => handleInputChange('type', value)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  formData.type === value
                    ? 'border-purple-500 bg-purple-900/30 text-white'
                    : 'border-gray-600 bg-gray-700 text-gray-300 hover:border-gray-500'
                }`}
              >
                <div className="font-medium text-sm">{label}</div>
                <div className="text-xs text-gray-400 mt-1">{description}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Address Label */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-3">
            Address Label
          </label>
          <div className="grid grid-cols-3 gap-3 mb-3">
            {COMMON_LABELS.map(({ value, icon: Icon }) => (
              <button
                key={value}
                type="button"
                onClick={() => handleLabelChange(value)}
                className={`p-3 rounded-lg border text-center transition-all ${
                  formData.label === value && !showCustomLabel
                    ? 'border-purple-500 bg-purple-900/30 text-white'
                    : 'border-gray-600 bg-gray-700 text-gray-300 hover:border-gray-500'
                }`}
              >
                <Icon className="w-5 h-5 mx-auto mb-1" />
                <div className="text-sm font-medium">{value}</div>
              </button>
            ))}
          </div>
          
          {showCustomLabel && (
            <input
              type="text"
              value={customLabel}
              onChange={(e) => setCustomLabel(e.target.value)}
              placeholder="Enter custom label"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          )}
        </div>

        {/* Full Name */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <User className="w-4 h-4 inline mr-1" />
            Full Name
          </label>
          <input
            type="text"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            placeholder="Enter full name for this address"
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>

        {/* Address Lines */}
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Address Line 1
            </label>
            <input
              type="text"
              value={formData.addressLine1}
              onChange={(e) => handleInputChange('addressLine1', e.target.value)}
              placeholder="Street address, P.O. box, company name"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Address Line 2 (Optional)
            </label>
            <input
              type="text"
              value={formData.addressLine2}
              onChange={(e) => handleInputChange('addressLine2', e.target.value)}
              placeholder="Apartment, suite, unit, building, floor, etc."
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* City, State, Postal Code */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              City
            </label>
            <input
              type="text"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              placeholder="City"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              State/Province
            </label>
            <input
              type="text"
              value={formData.state}
              onChange={(e) => handleInputChange('state', e.target.value)}
              placeholder="State/Province"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Postal/ZIP Code
            </label>
            <input
              type="text"
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              placeholder="Postal/ZIP Code"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Country */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Globe className="w-4 h-4 inline mr-1" />
            Country
          </label>
          <select
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            {COUNTRIES.map(({ code, name }) => (
              <option key={code} value={code}>
                {name}
              </option>
            ))}
          </select>
        </div>

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Phone className="w-4 h-4 inline mr-1" />
            Phone Number (Optional)
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            placeholder="Phone number for delivery"
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>

        {/* Set as Default */}
        <div className="flex items-center gap-3 p-4 bg-gray-700 rounded-lg">
          <button
            type="button"
            onClick={() => handleInputChange('isDefault', !formData.isDefault)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.isDefault ? 'bg-purple-500' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.isDefault ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
          <div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-400" />
              <span className="text-white font-medium">Set as default address</span>
            </div>
            <p className="text-gray-400 text-sm">
              This address will be pre-selected for checkout and shipping
            </p>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="flex-1 px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {loading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Check className="w-4 h-4" />
                {address ? 'Update Address' : 'Save Address'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
