/**
 * Completion Celebration Component
 * Provides positive feedback and rewards when users complete profile sections
 * Part of Phase 3: Enhanced User Experience Implementation
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Trophy,
  Star,
  Gift,
  Zap,
  CheckCircle,
  Sparkles,
  Award,
  Target,
  Crown,
  Gem
} from 'lucide-react'

interface CelebrationReward {
  type: 'points' | 'badge' | 'unlock' | 'milestone'
  title: string
  description: string
  value?: number
  icon: React.ComponentType<any>
  color: string
}

interface MilestoneReward {
  percentage: number
  title: string
  description: string
  unlocks: string[]
  icon: React.ComponentType<any>
  color: string
}

interface CompletionCelebrationProps {
  isVisible: boolean
  onClose: () => void
  completedSection: {
    id: string
    title: string
    weight: number
    category: 'essential' | 'recommended' | 'optional'
  }
  newCompletionPercentage: number
  rewards?: CelebrationReward[]
  milestone?: MilestoneReward
}

const CompletionCelebration: React.FC<CompletionCelebrationProps> = ({
  isVisible,
  onClose,
  completedSection,
  newCompletionPercentage,
  rewards = [],
  milestone
}) => {
  const [showRewards, setShowRewards] = useState(false)
  const [showMilestone, setShowMilestone] = useState(false)
  const [animationPhase, setAnimationPhase] = useState<'celebration' | 'rewards' | 'milestone' | 'complete'>('celebration')

  // Default rewards based on section completion
  const defaultRewards: CelebrationReward[] = [
    {
      type: 'points',
      title: 'Profile Progress',
      description: `+${completedSection.weight}% completion`,
      value: completedSection.weight,
      icon: Target,
      color: 'text-purple-400'
    }
  ]

  // Add category-specific rewards
  if (completedSection.category === 'essential') {
    defaultRewards.push({
      type: 'badge',
      title: 'Essential Step',
      description: 'Completed an essential profile section',
      icon: Star,
      color: 'text-yellow-400'
    })
  }

  const allRewards = [...defaultRewards, ...rewards]

  // Milestone rewards for major completion percentages
  const getMilestoneReward = (percentage: number): MilestoneReward | null => {
    if (percentage >= 100) {
      return {
        percentage: 100,
        title: 'Profile Master!',
        description: 'You\'ve completed your entire profile!',
        unlocks: ['Premium features', 'Community recognition', 'Priority support'],
        icon: Crown,
        color: 'text-yellow-400'
      }
    } else if (percentage >= 75) {
      return {
        percentage: 75,
        title: 'Profile Expert',
        description: 'Your profile is looking fantastic!',
        unlocks: ['Advanced features', 'Community badges', 'Special offers'],
        icon: Award,
        color: 'text-blue-400'
      }
    } else if (percentage >= 50) {
      return {
        percentage: 50,
        title: 'Halfway Hero',
        description: 'You\'re halfway to a complete profile!',
        unlocks: ['Enhanced visibility', 'Community features', 'Profile insights'],
        icon: Trophy,
        color: 'text-purple-400'
      }
    } else if (percentage >= 25) {
      return {
        percentage: 25,
        title: 'Getting Started',
        description: 'Great progress on your profile!',
        unlocks: ['Basic features', 'Community access', 'Profile tips'],
        icon: Zap,
        color: 'text-green-400'
      }
    }
    return null
  }

  const milestoneReward: MilestoneReward | null = milestone || getMilestoneReward(newCompletionPercentage)

  // Animation sequence
  useEffect(() => {
    if (!isVisible) {
      console.log('CompletionCelebration: Not visible, skipping animation sequence')
      return
    }

    console.log('CompletionCelebration: Starting animation sequence')
    let timeoutIds: NodeJS.Timeout[] = []
    let isCancelled = false

    const sequence = async () => {
      try {
        // Phase 1: Celebration (2 seconds)
        console.log('CompletionCelebration: Phase 1 - Celebration')
        setAnimationPhase('celebration')
        await new Promise(resolve => {
          const id = setTimeout(resolve, 2000)
          timeoutIds.push(id)
        })
        if (isCancelled) {
          console.log('CompletionCelebration: Sequence cancelled after phase 1')
          return
        }

        // Phase 2: Show rewards (3 seconds)
        if (allRewards.length > 0) {
          console.log('CompletionCelebration: Phase 2 - Rewards')
          setAnimationPhase('rewards')
          setShowRewards(true)
          await new Promise(resolve => {
            const id = setTimeout(resolve, 3000)
            timeoutIds.push(id)
          })
          if (isCancelled) {
            console.log('CompletionCelebration: Sequence cancelled after phase 2')
            return
          }
        }

        // Phase 3: Show milestone if applicable (3 seconds)
        if (milestoneReward) {
          console.log('CompletionCelebration: Phase 3 - Milestone')
          setAnimationPhase('milestone')
          setShowMilestone(true)
          await new Promise(resolve => {
            const id = setTimeout(resolve, 3000)
            timeoutIds.push(id)
          })
          if (isCancelled) {
            console.log('CompletionCelebration: Sequence cancelled after phase 3')
            return
          }
        }

        // Phase 4: Complete
        console.log('CompletionCelebration: Phase 4 - Complete')
        setAnimationPhase('complete')

        // Auto-close after 3 seconds in complete phase
        console.log('CompletionCelebration: Starting auto-close timer (3 seconds)')
        await new Promise(resolve => {
          const id = setTimeout(resolve, 3000)
          timeoutIds.push(id)
        })
        if (isCancelled) {
          console.log('CompletionCelebration: Auto-close cancelled')
          return
        }

        console.log('CompletionCelebration: Auto-closing modal')
        onClose()
      } catch (error) {
        // Handle any errors in the sequence
        console.error('Celebration sequence error:', error)
      }
    }

    sequence()

    // Cleanup function to cancel all timeouts
    return () => {
      console.log('CompletionCelebration: Cleaning up animation sequence')
      isCancelled = true
      timeoutIds.forEach(id => clearTimeout(id))
    }
  }, [isVisible, allRewards.length, milestoneReward, onClose])

  // Confetti animation
  const confettiPieces = Array.from({ length: 50 }, (_, i) => ({
    id: i,
    delay: Math.random() * 2,
    duration: 3 + Math.random() * 2,
    x: Math.random() * 100,
    rotation: Math.random() * 360,
    color: ['text-purple-400', 'text-blue-400', 'text-yellow-400', 'text-green-400', 'text-pink-400'][Math.floor(Math.random() * 5)]
  }))

  if (!isVisible) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        {/* Confetti */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {confettiPieces.map((piece) => (
            <motion.div
              key={piece.id}
              initial={{ 
                y: -20, 
                x: `${piece.x}%`, 
                rotate: 0,
                opacity: 0 
              }}
              animate={{ 
                y: '100vh', 
                rotate: piece.rotation,
                opacity: [0, 1, 1, 0] 
              }}
              transition={{ 
                duration: piece.duration,
                delay: piece.delay,
                ease: "easeOut"
              }}
              className={`absolute w-3 h-3 ${piece.color}`}
            >
              <Sparkles className="w-full h-full" />
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          className="bg-gray-800 rounded-lg border border-purple-500 max-w-md w-full mx-4 overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Celebration Phase */}
          <AnimatePresence mode="wait">
            {animationPhase === 'celebration' && (
              <motion.div
                key="celebration"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="p-8 text-center"
              >
                <motion.div
                  animate={{ 
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{ 
                    duration: 1,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                  className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6"
                >
                  <CheckCircle className="w-10 h-10 text-white" />
                </motion.div>
                
                <motion.h2
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-2xl font-bold text-white mb-2"
                >
                  Awesome Work!
                </motion.h2>
                
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7 }}
                  className="text-gray-400"
                >
                  You completed: <span className="text-white font-medium">{completedSection.title}</span>
                </motion.p>
              </motion.div>
            )}

            {/* Rewards Phase */}
            {animationPhase === 'rewards' && showRewards && (
              <motion.div
                key="rewards"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="p-8"
              >
                <div className="text-center mb-6">
                  <Gift className="w-12 h-12 text-purple-400 mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-white mb-2">Rewards Earned!</h3>
                </div>

                <div className="space-y-3">
                  {allRewards.map((reward, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600"
                    >
                      <div className={`p-2 rounded-lg bg-gray-600/50 ${reward.color}`}>
                        <reward.icon className="w-5 h-5" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-white font-medium">{reward.title}</h4>
                        <p className="text-gray-400 text-sm">{reward.description}</p>
                      </div>
                      {reward.value && (
                        <div className={`font-bold ${reward.color}`}>
                          +{reward.value}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Milestone Phase */}
            {animationPhase === 'milestone' && showMilestone && milestoneReward && (
              <motion.div
                key="milestone"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="p-8"
              >
                <div className="text-center mb-6">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className={`w-16 h-16 mx-auto mb-4 ${milestoneReward.color}`}
                  >
                    <milestoneReward.icon className="w-full h-full" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white mb-2">{milestoneReward.title}</h3>
                  <p className="text-gray-400 mb-4">{milestoneReward.description}</p>
                  
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-purple-400 mb-2">
                      {newCompletionPercentage}%
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-3">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${newCompletionPercentage}%` }}
                        transition={{ duration: 1.5, ease: "easeOut" }}
                        className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-white font-medium mb-3">🎉 You've unlocked:</h4>
                  {milestoneReward.unlocks.map((unlock, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="flex items-center gap-2 text-green-400"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">{unlock}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Complete Phase */}
            {animationPhase === 'complete' && (
              <motion.div
                key="complete"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-8 text-center"
              >
                <Gem className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Keep Going!</h3>
                <p className="text-gray-400 mb-6">
                  Your profile is {newCompletionPercentage}% complete. Every step makes your experience better!
                </p>
                
                <button
                  onClick={onClose}
                  className="w-full px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium"
                >
                  Continue Building Profile
                </button>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default CompletionCelebration
