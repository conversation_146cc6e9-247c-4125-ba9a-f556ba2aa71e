/**
 * Guided Workflow Manager Component
 * Manages step-by-step guided profile completion experience
 * Part of Phase 3: Enhanced User Experience Implementation
 */

import React, { useState, useCallback, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  ArrowRight,
  X,
  CheckCircle,
  Lightbulb,
  Clock,
  Star,
  Gift
} from 'lucide-react'
import { UserProfile } from '@/types/profile'

interface GuidedStep {
  id: string
  title: string
  description: string
  instructions: string[]
  tips: string[]
  estimatedTime: string
  benefits: string[]
  tabId?: string
  fieldFocus?: string
}

interface GuidedWorkflowManagerProps {
  isOpen: boolean
  onClose: () => void
  profile: UserProfile
  onStepComplete: (stepId: string) => void
  onOpenEditor: (tabId?: string, fieldFocus?: string) => void
}

const GuidedWorkflowManager: React.FC<GuidedWorkflowManagerProps> = ({
  isOpen,
  onClose,
  profile,
  onStepComplete,
  onOpenEditor
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set())
  const [showTips, setShowTips] = useState(false)

  // Define guided steps based on profile completion status
  const guidedSteps: GuidedStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Profile Setup!',
      description: "Let's get your profile set up in just a few minutes",
      instructions: [
        'We\'ll guide you through each step',
        'You can skip steps and come back later',
        'Each step shows estimated time and benefits'
      ],
      tips: [
        'Complete essential steps first for the best experience',
        'Your profile helps others connect with you in the community'
      ],
      estimatedTime: '5-10 minutes total',
      benefits: ['Better community connections', 'Personalized experience', 'Faster checkout']
    },
    {
      id: 'basic_info',
      title: 'Add Your Display Name & Bio',
      description: 'Help others recognize and connect with you',
      instructions: [
        'Choose a display name that represents you',
        'Write a brief bio (2-3 sentences)',
        'Share your interests or what brings you to Syndicaps'
      ],
      tips: [
        'Your display name appears throughout the community',
        'A good bio helps others understand your interests',
        'You can always update this information later'
      ],
      estimatedTime: '2 minutes',
      benefits: ['Increased profile visibility', 'Better community connections'],
      tabId: 'basic',
      fieldFocus: 'displayName'
    },
    {
      id: 'profile_photo',
      title: 'Upload Your Profile Photo',
      description: 'Add a personal touch to your profile',
      instructions: [
        'Click the camera icon on your profile picture',
        'Choose a clear, recent photo of yourself',
        'Make sure the image is well-lit and shows your face'
      ],
      tips: [
        'Profile photos help build trust in the community',
        'Use a photo that represents you professionally',
        'Square images work best for profile pictures'
      ],
      estimatedTime: '1 minute',
      benefits: ['Personal branding', 'Trust building', 'Recognition'],
      tabId: 'basic',
      fieldFocus: 'photo'
    },
    {
      id: 'contact_info',
      title: 'Complete Contact Information',
      description: 'Add your contact details for account security',
      instructions: [
        'Add your first and last name',
        'Enter your phone number for verification',
        'Verify your email address if not already done'
      ],
      tips: [
        'Contact info helps with account recovery',
        'Phone verification adds security to your account',
        'We never share your personal information'
      ],
      estimatedTime: '3 minutes',
      benefits: ['Account security', 'Order notifications', 'Account recovery'],
      tabId: 'contact',
      fieldFocus: 'firstName'
    },
    {
      id: 'addresses',
      title: 'Add Your Shipping Address',
      description: 'Set up your default shipping information',
      instructions: [
        'Click "Add Address" to create your first address',
        'Enter your complete shipping address',
        'Set it as your default for faster checkout'
      ],
      tips: [
        'You can add multiple addresses for different locations',
        'Default addresses speed up the checkout process',
        'All address information is securely encrypted'
      ],
      estimatedTime: '3 minutes',
      benefits: ['Faster checkout', 'Order tracking', 'Multiple shipping options'],
      tabId: 'addresses',
      fieldFocus: 'addAddress'
    },
    {
      id: 'completion',
      title: 'Profile Setup Complete!',
      description: 'Congratulations! Your profile is now set up',
      instructions: [
        'You\'ve completed the essential profile setup',
        'You can always add more information later',
        'Explore the community and start connecting!'
      ],
      tips: [
        'Consider adding social links to connect with others',
        'Check out the community features and discussions',
        'Your profile completion helps unlock features'
      ],
      estimatedTime: 'All done!',
      benefits: ['Full platform access', 'Community participation', 'Personalized experience']
    }
  ]

  const currentStep = guidedSteps[currentStepIndex]
  const isLastStep = currentStepIndex === guidedSteps.length - 1
  const isFirstStep = currentStepIndex === 0

  // Check if current step is completed based on profile data
  const isCurrentStepCompleted = useCallback(() => {
    if (!currentStep) return false
    
    switch (currentStep.id) {
      case 'welcome':
        return true // Welcome step is always "completed"
      case 'basic_info':
        return !!(profile?.displayName && profile?.bio && profile.bio.length > 10)
      case 'profile_photo':
        return !!(profile?.avatar && profile.avatar !== '')
      case 'contact_info':
        return !!(profile?.phone && profile?.firstName && profile?.lastName)
      case 'addresses':
        return !!(profile?.addresses && profile.addresses.length > 0)
      case 'completion':
        return true
      default:
        return false
    }
  }, [currentStep, profile])

  // Auto-advance when step is completed
  useEffect(() => {
    if (isCurrentStepCompleted() && !completedSteps.has(currentStep.id)) {
      setCompletedSteps(prev => new Set([...prev, currentStep.id]))
      onStepComplete(currentStep.id)
    }
  }, [isCurrentStepCompleted, currentStep.id, completedSteps, onStepComplete])

  const handleNext = useCallback(() => {
    if (isLastStep) {
      onClose()
    } else {
      setCurrentStepIndex(prev => prev + 1)
    }
  }, [isLastStep, onClose])

  const handlePrevious = useCallback(() => {
    if (!isFirstStep) {
      setCurrentStepIndex(prev => prev - 1)
    }
  }, [isFirstStep])

  const handleStartStep = useCallback(() => {
    if (currentStep.tabId) {
      onOpenEditor(currentStep.tabId, currentStep.fieldFocus)
    }
  }, [currentStep, onOpenEditor])

  const handleSkipStep = useCallback(() => {
    handleNext()
  }, [handleNext])

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-gray-800 rounded-lg border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700">
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-400">
                Step {currentStepIndex + 1} of {guidedSteps.length}
              </div>
              <div className="flex gap-1">
                {guidedSteps.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full ${
                      index <= currentStepIndex ? 'bg-purple-500' : 'bg-gray-600'
                    }`}
                  />
                ))}
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <motion.div
              key={currentStep.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* Step Header */}
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">{currentStep.title}</h2>
                <p className="text-gray-400 text-lg">{currentStep.description}</p>
                <div className="flex items-center gap-4 mt-3">
                  <div className="flex items-center gap-1 text-sm text-purple-400">
                    <Clock className="w-4 h-4" />
                    {currentStep.estimatedTime}
                  </div>
                  {isCurrentStepCompleted() && (
                    <div className="flex items-center gap-1 text-sm text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      Completed
                    </div>
                  )}
                </div>
              </div>

              {/* Instructions */}
              <div className="mb-6">
                <h3 className="text-white font-medium mb-3">Instructions:</h3>
                <ul className="space-y-2">
                  {currentStep.instructions.map((instruction, index) => (
                    <li key={index} className="flex items-start gap-2 text-gray-300">
                      <div className="w-5 h-5 rounded-full bg-purple-500/20 flex items-center justify-center mt-0.5 flex-shrink-0">
                        <span className="text-purple-400 text-xs font-medium">{index + 1}</span>
                      </div>
                      {instruction}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Benefits */}
              <div className="mb-6">
                <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                  <Gift className="w-4 h-4 text-purple-400" />
                  Benefits:
                </h3>
                <div className="flex flex-wrap gap-2">
                  {currentStep.benefits.map((benefit, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-purple-900/30 text-purple-400 rounded-full text-sm border border-purple-500/30"
                    >
                      {benefit}
                    </span>
                  ))}
                </div>
              </div>

              {/* Tips */}
              {currentStep.tips.length > 0 && (
                <div className="mb-6">
                  <button
                    onClick={() => setShowTips(!showTips)}
                    className="flex items-center gap-2 text-blue-400 hover:text-blue-300 transition-colors mb-3"
                  >
                    <Lightbulb className="w-4 h-4" />
                    {showTips ? 'Hide Tips' : 'Show Tips'}
                  </button>
                  <AnimatePresence>
                    {showTips && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="bg-blue-900/20 rounded-lg p-4 border border-blue-500/30"
                      >
                        <ul className="space-y-2">
                          {currentStep.tips.map((tip, index) => (
                            <li key={index} className="flex items-start gap-2 text-blue-300 text-sm">
                              <Star className="w-3 h-3 mt-1 flex-shrink-0" />
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </motion.div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-700">
            <button
              onClick={handlePrevious}
              disabled={isFirstStep}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                isFirstStep
                  ? 'text-gray-500 cursor-not-allowed'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <ArrowLeft className="w-4 h-4" />
              Previous
            </button>

            <div className="flex items-center gap-3">
              {!isLastStep && currentStep.id !== 'welcome' && (
                <button
                  onClick={handleSkipStep}
                  className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
                >
                  Skip for now
                </button>
              )}
              
              {currentStep.tabId && !isCurrentStepCompleted() && (
                <button
                  onClick={handleStartStep}
                  className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  Start This Step
                </button>
              )}

              <button
                onClick={handleNext}
                className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                {isLastStep ? 'Finish' : 'Next'}
                {!isLastStep && <ArrowRight className="w-4 h-4" />}
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default GuidedWorkflowManager
