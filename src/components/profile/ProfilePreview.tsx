/**
 * Profile Preview Component
 * 
 * Provides a real-time preview of how the user's profile appears to others
 * based on their current privacy settings and profile information.
 * 
 * Features:
 * - Real-time profile preview with privacy filtering
 * - Multiple view modes (Public, Friends, Private)
 * - Profile completion progress tracking
 * - Customization suggestions and recommendations
 * - Social media style profile cards
 * - Export/share profile functionality
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Eye,
  EyeOff,
  Users,
  Globe,
  Lock,
  Share2,
  Download,
  Edit,
  Star,
  Trophy,
  Heart,
  MapPin,
  Calendar,
  Mail,
  Phone,
  Link as LinkIcon,
  Award,
  TrendingUp,
  Zap,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { calculatePrivacyScore } from '@/components/privacy/PrivacyLevelPresets'
import RoleBadge from '@/components/ui/RoleBadge'
import { isValidRoleId } from '@/constants/roles'
import { filterProfileForViewMode, type ViewMode } from '@/utils/profilePrivacy'
import { ProfileCompletionManager } from '@/lib/profileCompletion'

interface ProfilePreviewProps {
  profile: UserProfile | null
  viewMode?: ViewMode
  showCompletionProgress?: boolean
  showCustomizationSuggestions?: boolean
  onEdit?: (section: string) => void
  onShare?: () => void
  onExport?: () => void
  className?: string
}

interface ProfileCompletionItem {
  id: string
  label: string
  completed: boolean
  importance: 'high' | 'medium' | 'low'
  points: number
  action: string
}

/**
 * Calculate profile completion status using standardized ProfileCompletionManager
 */
const calculateProfileCompletion = (profile: UserProfile | null): {
  percentage: number
  items: ProfileCompletionItem[]
  totalPoints: number
  earnedPoints: number
} => {
  if (!profile) {
    return { percentage: 0, items: [], totalPoints: 0, earnedPoints: 0 }
  }

  // Use standardized completion calculation
  const completion = ProfileCompletionManager.calculateCompletion(profile)

  // Convert to legacy format for compatibility
  const items: ProfileCompletionItem[] = [
    {
      id: 'basic_info',
      label: 'Basic Information',
      completed: !!(profile.displayName && profile.bio && profile.bio.length > 10),
      importance: 'high',
      points: 20,
      action: 'edit-basic'
    },
    {
      id: 'avatar',
      label: 'Profile Picture',
      completed: !!profile.avatar,
      importance: 'high',
      points: 20,
      action: 'upload-avatar'
    },
    {
      id: 'contact_personal',
      label: 'Contact & Personal',
      completed: !!(profile.firstName && profile.lastName && profile.phone && profile.emailVerified && profile.dateOfBirth),
      importance: 'high',
      points: 35,
      action: 'edit-contact'
    },
    {
      id: 'location',
      label: 'Location',
      completed: !!profile.location,
      importance: 'medium',
      points: 15,
      action: 'edit-location'
    },
    {
      id: 'social_links',
      label: 'Social Links',
      completed: !!(profile.socialLinks && Object.values(profile.socialLinks).some(link => link)) || !!profile.website,
      importance: 'medium',
      points: 25,
      action: 'edit-social'
    },
    {
      id: 'location',
      label: 'Location',
      completed: !!profile.location,
      importance: 'low',
      points: 5,
      action: 'edit-location'
    },
    {
      id: 'website',
      label: 'Website',
      completed: !!profile.website,
      importance: 'low',
      points: 5,
      action: 'edit-website'
    },
    {
      id: 'privacy',
      label: 'Privacy Settings',
      completed: !!profile.privacy && calculatePrivacyScore(profile.privacy) > 50,
      importance: 'high',
      points: 15,
      action: 'edit-privacy'
    },
    {
      id: 'security',
      label: 'Security Setup',
      completed: !!profile.mfaEnabled,
      importance: 'high',
      points: 20,
      action: 'setup-security'
    }
  ]

  const totalPoints = items.reduce((sum, item) => sum + item.points, 0)
  const earnedPoints = items.filter(item => item.completed).reduce((sum, item) => sum + item.points, 0)

  // Use standardized percentage calculation
  const percentage = completion.percentage

  return { percentage, items, totalPoints, earnedPoints }
}

/**
 * Filter profile data based on privacy settings and view mode
 * Uses centralized privacy utility for consistent filtering across components
 */
const getFilteredProfile = (profile: UserProfile | null, viewMode: ViewMode): UserProfile | null => {
  return filterProfileForViewMode(profile, viewMode)
}

/**
 * Profile Card Component
 */
const ProfileCard: React.FC<{
  profile: UserProfile
  viewMode: ViewMode
  onEdit?: (section: string) => void
}> = ({ profile, viewMode, onEdit }) => {
  const filteredProfile = getFilteredProfile(profile, viewMode)

  if (!filteredProfile) {
    return (
      <div className="bg-gray-800 rounded-lg p-8 text-center border border-gray-700">
        <Lock size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">Profile Not Visible</h3>
        <p className="text-gray-400">
          This profile is not visible to {viewMode === 'public' ? 'the public' : 'friends'} due to privacy settings.
        </p>
        {onEdit && (
          <button
            onClick={() => onEdit('privacy')}
            className="mt-4 px-4 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors"
          >
            Adjust Privacy Settings
          </button>
        )}
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
      {/* Header */}
      <div className="relative h-32 bg-gradient-to-r from-accent-500 to-purple-600">
        <div className="absolute inset-0 bg-black/20" />
        {onEdit && (
          <button
            onClick={() => onEdit('cover')}
            className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 text-white rounded-lg transition-colors"
          >
            <Edit size={16} />
          </button>
        )}
      </div>

      {/* Profile Info */}
      <div className="relative px-6 pb-6">
        {/* Avatar */}
        <div className="relative -mt-16 mb-4">
          <div className="w-24 h-24 bg-gray-700 rounded-full border-4 border-gray-800 flex items-center justify-center">
            {filteredProfile.avatar ? (
              <img
                src={filteredProfile.avatar}
                alt={filteredProfile.displayName || 'Profile'}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <div className="text-2xl font-bold text-gray-400">
                {(filteredProfile.displayName || filteredProfile.email || 'U')[0].toUpperCase()}
              </div>
            )}
          </div>
          {onEdit && (
            <button
              onClick={() => onEdit('avatar')}
              className="absolute bottom-0 right-0 p-1 bg-accent-500 hover:bg-accent-600 text-white rounded-full transition-colors"
            >
              <Edit size={12} />
            </button>
          )}
        </div>

        {/* Name and Title */}
        <div className="mb-4">
          <h2 className="text-xl font-bold text-white mb-1">
            {filteredProfile.displayName || 'Anonymous User'}
          </h2>
          {filteredProfile.role && isValidRoleId(filteredProfile.role) && (
            <RoleBadge
              role={filteredProfile.role}
              size="sm"
              variant="subtle"
            />
          )}
        </div>

        {/* Bio */}
        {filteredProfile.bio && (
          <div className="mb-4">
            <p className="text-gray-300 text-sm leading-relaxed">{filteredProfile.bio}</p>
            {onEdit && (
              <button
                onClick={() => onEdit('bio')}
                className="mt-2 text-accent-400 hover:text-accent-300 text-xs"
              >
                Edit bio
              </button>
            )}
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          {filteredProfile.points !== undefined && (
            <div className="text-center">
              <div className="text-lg font-bold text-accent-400">{filteredProfile.points.toLocaleString()}</div>
              <div className="text-xs text-gray-400">Points</div>
            </div>
          )}
          {filteredProfile.achievements && (
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-400">{filteredProfile.achievements.length}</div>
              <div className="text-xs text-gray-400">Achievements</div>
            </div>
          )}
          <div className="text-center">
            <div className="text-lg font-bold text-green-400">
              {filteredProfile.createdAt ? Math.floor((Date.now() - (filteredProfile.createdAt instanceof Date ? filteredProfile.createdAt.getTime() : filteredProfile.createdAt.toDate().getTime())) / (1000 * 60 * 60 * 24)) : 0}
            </div>
            <div className="text-xs text-gray-400">Days</div>
          </div>
        </div>

        {/* Contact Info */}
        <div className="space-y-2 mb-4">
          {filteredProfile.email && (
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <Mail size={14} className="text-gray-400" />
              <span>{filteredProfile.email}</span>
            </div>
          )}
          {filteredProfile.phone && (
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <Phone size={14} className="text-gray-400" />
              <span>{filteredProfile.phone}</span>
            </div>
          )}
          {filteredProfile.location && (
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <MapPin size={14} className="text-gray-400" />
              <span>{filteredProfile.location}</span>
            </div>
          )}
          {filteredProfile.website && (
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <LinkIcon size={14} className="text-gray-400" />
              <a href={filteredProfile.website} target="_blank" rel="noopener noreferrer" className="text-accent-400 hover:text-accent-300">
                {filteredProfile.website}
              </a>
            </div>
          )}
        </div>

        {/* Social Links */}
        {filteredProfile.socialLinks && Object.keys(filteredProfile.socialLinks).length > 0 && (
          <div className="flex space-x-2">
            {Object.entries(filteredProfile.socialLinks).map(([platform, url]) => (
              <a
                key={platform}
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-colors"
              >
                <LinkIcon size={16} />
              </a>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * Profile Completion Progress Component
 */
const ProfileCompletionProgress: React.FC<{
  completion: ReturnType<typeof calculateProfileCompletion>
  onActionClick: (action: string) => void
}> = ({ completion, onActionClick }) => {
  const { percentage, items, earnedPoints, totalPoints } = completion

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Profile Completion</h3>
        <div className="text-right">
          <div className="text-2xl font-bold text-accent-400">{percentage}%</div>
          <div className="text-sm text-gray-400">{earnedPoints}/{totalPoints} points</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-3 mb-6">
        <motion.div
          className="bg-gradient-to-r from-accent-500 to-green-500 h-3 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 1, delay: 0.2 }}
        />
      </div>

      {/* Completion Items */}
      <div className="space-y-3">
        {items.map((item) => (
          <div
            key={item.id}
            className={`flex items-center justify-between p-3 rounded-lg ${
              item.completed ? 'bg-green-900/20 border border-green-500/30' : 'bg-gray-700/50'
            }`}
          >
            <div className="flex items-center space-x-3">
              {item.completed ? (
                <CheckCircle size={20} className="text-green-400" />
              ) : (
                <AlertCircle size={20} className="text-yellow-400" />
              )}
              <div>
                <div className="text-white font-medium">{item.label}</div>
                <div className="text-sm text-gray-400">
                  {item.points} points • {item.importance} priority
                </div>
              </div>
            </div>
            {!item.completed && (
              <button
                onClick={() => onActionClick(item.action)}
                className="px-3 py-1 bg-accent-500 hover:bg-accent-600 text-white text-sm rounded-lg transition-colors"
              >
                Complete
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

/**
 * Main Profile Preview Component
 */
const ProfilePreview: React.FC<ProfilePreviewProps> = ({
  profile,
  viewMode = 'public',
  showCompletionProgress = true,
  showCustomizationSuggestions = true,
  onEdit,
  onShare,
  onExport,
  className = ''
}) => {
  const [selectedViewMode, setSelectedViewMode] = useState(viewMode)
  const completion = useMemo(() => calculateProfileCompletion(profile), [profile])

  const viewModes = [
    { id: 'public', label: 'Public View', icon: Globe, description: 'How strangers see your profile' },
    { id: 'friends', label: 'Friends View', icon: Users, description: 'How friends see your profile' },
    { id: 'private', label: 'Your View', icon: Eye, description: 'Your complete profile' }
  ]

  const handleActionClick = (action: string) => {
    switch (action) {
      case 'upload-avatar':
        onEdit?.('avatar')
        break
      case 'edit-bio':
        onEdit?.('bio')
        break
      case 'edit-contact':
        onEdit?.('contact')
        break
      case 'edit-social':
        onEdit?.('social')
        break
      case 'edit-location':
        onEdit?.('location')
        break
      case 'edit-website':
        onEdit?.('website')
        break
      case 'edit-privacy':
        onEdit?.('privacy')
        break
      case 'setup-security':
        onEdit?.('security')
        break
      default:
        console.log('Unknown action:', action)
    }
  }

  if (!profile) {
    return (
      <div className={`bg-gray-800 rounded-lg p-8 text-center border border-gray-700 ${className}`}>
        <Info size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Profile Data</h3>
        <p className="text-gray-400">Please log in to preview your profile.</p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Profile Preview</h2>
        <div className="flex items-center space-x-2">
          {onShare && (
            <button
              onClick={onShare}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <Share2 size={16} />
              <span>Share</span>
            </button>
          )}
          {onExport && (
            <button
              onClick={onExport}
              className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <Download size={16} />
              <span>Export</span>
            </button>
          )}
        </div>
      </div>

      {/* View Mode Selector */}
      <div className="flex space-x-1 bg-gray-700 rounded-lg p-1">
        {viewModes.map((mode) => (
          <button
            key={mode.id}
            onClick={() => setSelectedViewMode(mode.id as ViewMode)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-colors ${
              selectedViewMode === mode.id
                ? 'bg-accent-500 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-600'
            }`}
          >
            <mode.icon size={16} />
            <span className="font-medium">{mode.label}</span>
          </button>
        ))}
      </div>

      {/* View Mode Description */}
      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info size={20} className="text-blue-400 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="text-blue-400 font-medium mb-1">
              {viewModes.find(m => m.id === selectedViewMode)?.label}
            </h4>
            <p className="text-gray-300 text-sm">
              {viewModes.find(m => m.id === selectedViewMode)?.description}
            </p>
          </div>
        </div>
      </div>

      {/* Profile Card and Completion Progress */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Profile Card */}
        <ProfileCard
          profile={profile}
          viewMode={selectedViewMode}
          onEdit={onEdit}
        />

        {/* Profile Completion Progress */}
        {showCompletionProgress && (
          <ProfileCompletionProgress
            completion={completion}
            onActionClick={handleActionClick}
          />
        )}
      </div>
    </div>
  )
}

export default ProfilePreview
export type { ProfilePreviewProps }
