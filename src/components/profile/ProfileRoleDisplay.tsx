/**
 * Profile Role Display Component
 * 
 * Specialized component for displaying user roles within the profile system.
 * Provides consistent role visualization with admin-specific styling and
 * enhanced features for profile pages.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Shield, Crown, Settings, Users, Eye, EyeOff } from 'lucide-react'
import RoleBadge, { AdminRoleBadge, UserRoleIndicator } from '@/components/ui/RoleBadge'
import { 
  ROLE_IDS, 
  getRoleDisplayName, 
  getRoleDescription,
  isAdminRole,
  isSuperAdminRole,
  hasRolePermission,
  type RoleId 
} from '@/constants/roles'
import { UserProfile } from '@/types/profile'

interface ProfileRoleDisplayProps {
  /** User profile data */
  profile: UserProfile | null
  /** Display variant */
  variant?: 'compact' | 'detailed' | 'card'
  /** Whether to show role description */
  showDescription?: boolean
  /** Whether to show admin privileges indicator */
  showPrivileges?: boolean
  /** Whether to show role change history */
  showHistory?: boolean
  /** Custom CSS classes */
  className?: string
}

/**
 * Admin Privileges Indicator
 */
const AdminPrivilegesIndicator: React.FC<{ role: RoleId }> = ({ role }) => {
  if (!isAdminRole(role)) return null

  const isSuperAdmin = isSuperAdminRole(role)
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`
        flex items-center space-x-2 px-3 py-2 rounded-lg border
        ${isSuperAdmin 
          ? 'bg-red-500/10 border-red-500/30 text-red-400' 
          : 'bg-orange-500/10 border-orange-500/30 text-orange-400'
        }
      `}
    >
      {isSuperAdmin ? (
        <Crown size={16} className="flex-shrink-0" />
      ) : (
        <Shield size={16} className="flex-shrink-0" />
      )}
      <div className="text-sm">
        <div className="font-medium">
          {isSuperAdmin ? 'Root Access' : 'Administrative Access'}
        </div>
        <div className="text-xs opacity-75">
          {isSuperAdmin 
            ? 'Full system control and user management'
            : 'Most admin panel features and user management'
          }
        </div>
      </div>
    </motion.div>
  )
}

/**
 * Role Change History (placeholder for future implementation)
 */
const RoleChangeHistory: React.FC<{ profile: UserProfile }> = ({ profile }) => {
  // This would connect to actual role change history data
  const hasHistory = false // Placeholder
  
  if (!hasHistory) return null
  
  return (
    <div className="mt-4 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
      <h4 className="text-sm font-medium text-white mb-2 flex items-center">
        <Settings size={14} className="mr-2" />
        Role History
      </h4>
      <div className="text-xs text-gray-400">
        No role changes recorded
      </div>
    </div>
  )
}

/**
 * Compact Role Display
 */
const CompactRoleDisplay: React.FC<{ profile: UserProfile }> = ({ profile }) => {
  if (!profile.role) return null
  
  return (
    <div className="flex items-center space-x-2">
      <RoleBadge 
        role={profile.role as RoleId} 
        size="sm" 
        variant="default"
        showTooltip={true}
      />
      {isAdminRole(profile.role as RoleId) && (
        <div className="flex items-center text-xs text-gray-400">
          <Eye size={12} className="mr-1" />
          <span>Admin Access</span>
        </div>
      )}
    </div>
  )
}

/**
 * Detailed Role Display
 */
const DetailedRoleDisplay: React.FC<{ 
  profile: UserProfile
  showDescription: boolean
  showPrivileges: boolean
}> = ({ profile, showDescription, showPrivileges }) => {
  if (!profile.role) return null
  
  const role = profile.role as RoleId
  const displayName = getRoleDisplayName(role)
  const description = getRoleDescription(role)
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <RoleBadge 
            role={role} 
            size="md" 
            variant="default"
            showTooltip={false}
          />
          <div>
            <h3 className="text-lg font-semibold text-white">{displayName}</h3>
            {showDescription && (
              <p className="text-sm text-gray-400 mt-1">{description}</p>
            )}
          </div>
        </div>
      </div>
      
      {showPrivileges && (
        <AdminPrivilegesIndicator role={role} />
      )}
    </div>
  )
}

/**
 * Card Role Display
 */
const CardRoleDisplay: React.FC<{ 
  profile: UserProfile
  showDescription: boolean
  showPrivileges: boolean
  showHistory: boolean
}> = ({ profile, showDescription, showPrivileges, showHistory }) => {
  if (!profile.role) return null
  
  const role = profile.role as RoleId
  const displayName = getRoleDisplayName(role)
  const description = getRoleDescription(role)
  const isAdmin = isAdminRole(role)
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`
            p-3 rounded-lg
            ${isAdmin 
              ? 'bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-500/30' 
              : 'bg-gray-700/50 border border-gray-600'
            }
          `}>
            {isSuperAdminRole(role) ? (
              <Crown size={24} className="text-red-400" />
            ) : isAdmin ? (
              <Shield size={24} className="text-orange-400" />
            ) : (
              <Users size={24} className="text-gray-400" />
            )}
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">{displayName}</h3>
            {showDescription && (
              <p className="text-gray-400 mt-1">{description}</p>
            )}
          </div>
        </div>
        
        <RoleBadge 
          role={role} 
          size="lg" 
          variant="outline"
          showTooltip={false}
        />
      </div>
      
      {showPrivileges && (
        <AdminPrivilegesIndicator role={role} />
      )}
      
      {showHistory && (
        <RoleChangeHistory profile={profile} />
      )}
    </motion.div>
  )
}

/**
 * Main Profile Role Display Component
 */
const ProfileRoleDisplay: React.FC<ProfileRoleDisplayProps> = ({
  profile,
  variant = 'compact',
  showDescription = false,
  showPrivileges = false,
  showHistory = false,
  className = ''
}) => {
  if (!profile || !profile.role) {
    return (
      <div className={`text-gray-400 text-sm ${className}`}>
        No role assigned
      </div>
    )
  }

  const content = (() => {
    switch (variant) {
      case 'compact':
        return <CompactRoleDisplay profile={profile} />
      case 'detailed':
        return (
          <DetailedRoleDisplay 
            profile={profile}
            showDescription={showDescription}
            showPrivileges={showPrivileges}
          />
        )
      case 'card':
        return (
          <CardRoleDisplay 
            profile={profile}
            showDescription={showDescription}
            showPrivileges={showPrivileges}
            showHistory={showHistory}
          />
        )
      default:
        return <CompactRoleDisplay profile={profile} />
    }
  })()

  return (
    <div className={className}>
      {content}
    </div>
  )
}

export default ProfileRoleDisplay
export type { ProfileRoleDisplayProps }
