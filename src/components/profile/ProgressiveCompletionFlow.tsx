 /**
 * Progressive Profile Completion Flow Component
 * Implements guided workflow with smart next-step recommendations
 * Part of Phase 3: Enhanced User Experience Implementation
 */

import React, { useState, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  CheckCircle,
  Circle,
  ArrowRight,
  Star,
  Trophy,
  Gift,
  Zap,
  Target,
  ChevronRight,
  Sparkles
} from 'lucide-react'
import { UserProfile } from '@/types/profile'

interface CompletionStep {
  id: string
  title: string
  description: string
  weight: number
  completed: boolean
  priority: 'high' | 'medium' | 'low'
  category: 'essential' | 'recommended' | 'optional'
  estimatedTime: string
  benefits: string[]
  icon: React.ComponentType<any>
}

interface ProgressiveCompletionFlowProps {
  profile: UserProfile
  onStepClick: (stepId: string) => void
  onStartGuided: () => void
  showCelebration?: boolean
  onCelebrationComplete?: () => void
}

const ProgressiveCompletionFlow: React.FC<ProgressiveCompletionFlowProps> = ({
  profile,
  onStepClick,
  onStartGuided,
  showCelebration = false,
  onCelebrationComplete
}) => {
  const [showAllSteps, setShowAllSteps] = useState(false)
  const [guidedMode, setGuidedMode] = useState(false)

  // Calculate completion steps based on profile data
  const completionSteps: CompletionStep[] = useMemo(() => [
    {
      id: 'basic_info',
      title: 'Add Your Display Name & Bio',
      description: 'Help others recognize and connect with you',
      weight: 25,
      completed: !!(profile?.displayName && profile?.bio && profile.bio.length > 10),
      priority: 'high',
      category: 'essential',
      estimatedTime: '2 min',
      benefits: ['Increased profile visibility', 'Better community connections'],
      icon: Target
    },
    {
      id: 'profile_photo',
      title: 'Upload Profile Photo',
      description: 'Add a personal touch to your profile',
      weight: 15,
      completed: !!(profile?.photoURL && profile.photoURL !== ''),
      priority: 'high',
      category: 'essential',
      estimatedTime: '1 min',
      benefits: ['Personal branding', 'Trust building'],
      icon: Star
    },
    {
      id: 'contact_info',
      title: 'Complete Contact Information',
      description: 'Add phone number and verify your details',
      weight: 20,
      completed: !!(profile?.phone && profile?.firstName && profile?.lastName),
      priority: 'medium',
      category: 'recommended',
      estimatedTime: '3 min',
      benefits: ['Account security', 'Order notifications'],
      icon: CheckCircle
    },
    {
      id: 'social_links',
      title: 'Connect Social Profiles',
      description: 'Link your social media and website',
      weight: 15,
      completed: !!(profile?.website || profile?.socialLinks?.twitter || profile?.socialLinks?.instagram),
      priority: 'low',
      category: 'optional',
      estimatedTime: '2 min',
      benefits: ['Community engagement', 'Professional networking'],
      icon: Zap
    },
    {
      id: 'addresses',
      title: 'Add Shipping Address',
      description: 'Set up your default shipping information',
      weight: 20,
      completed: !!(profile?.addresses && profile.addresses.length > 0),
      priority: 'medium',
      category: 'recommended',
      estimatedTime: '3 min',
      benefits: ['Faster checkout', 'Order tracking'],
      icon: Gift
    },
    {
      id: 'privacy_settings',
      title: 'Configure Privacy Settings',
      description: 'Control who can see your profile information',
      weight: 5,
      completed: true, // Privacy has defaults
      priority: 'low',
      category: 'optional',
      estimatedTime: '2 min',
      benefits: ['Data control', 'Privacy protection'],
      icon: Trophy
    }
  ], [profile])

  // Calculate completion statistics
  const completionStats = useMemo(() => {
    const completed = completionSteps.filter(step => step.completed)
    const totalWeight = completionSteps.reduce((sum, step) => sum + step.weight, 0)
    const completedWeight = completed.reduce((sum, step) => sum + step.weight, 0)
    
    return {
      percentage: Math.round((completedWeight / totalWeight) * 100),
      completedSteps: completed.length,
      totalSteps: completionSteps.length,
      nextStep: completionSteps.find(step => !step.completed),
      essentialCompleted: completionSteps.filter(s => s.category === 'essential' && s.completed).length,
      essentialTotal: completionSteps.filter(s => s.category === 'essential').length
    }
  }, [completionSteps])

  const handleStartGuided = useCallback(() => {
    setGuidedMode(true)
    onStartGuided()
  }, [onStartGuided])

  const handleStepClick = useCallback((stepId: string) => {
    onStepClick(stepId)
  }, [onStepClick])

  // Get priority steps to show by default
  const prioritySteps = useMemo(() => {
    const incomplete = completionSteps.filter(step => !step.completed)
    const highPriority = incomplete.filter(step => step.priority === 'high')
    const mediumPriority = incomplete.filter(step => step.priority === 'medium')
    
    return [...highPriority, ...mediumPriority.slice(0, 2)]
  }, [completionSteps])

  const stepsToShow = showAllSteps ? completionSteps : prioritySteps

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      {/* Header with Progress */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-400" />
            Complete Your Profile
          </h3>
          <p className="text-gray-400 text-sm mt-1">
            {completionStats.percentage}% complete • {completionStats.completedSteps} of {completionStats.totalSteps} steps
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-purple-400">
            {completionStats.percentage}%
          </div>
          <div className="w-20 bg-gray-700 rounded-full h-2 mt-1">
            <motion.div
              className="bg-purple-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${completionStats.percentage}%` }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            />
          </div>
        </div>
      </div>

      {/* Next Step Highlight */}
      {completionStats.nextStep && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 p-4 bg-gradient-to-r from-purple-900/30 to-blue-900/30 rounded-lg border border-purple-500/30"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <completionStats.nextStep.icon className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <h4 className="text-white font-medium">Next: {completionStats.nextStep.title}</h4>
                <p className="text-gray-400 text-sm">{completionStats.nextStep.description}</p>
                <div className="flex items-center gap-4 mt-1">
                  <span className="text-purple-400 text-sm font-medium">
                    +{completionStats.nextStep.weight}% completion
                  </span>
                  <span className="text-gray-500 text-sm">
                    ~{completionStats.nextStep.estimatedTime}
                  </span>
                </div>
              </div>
            </div>
            <button
              onClick={() => handleStepClick(completionStats.nextStep!.id)}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Start
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      )}

      {/* Guided Mode CTA */}
      {!guidedMode && completionStats.percentage < 80 && (
        <div className="mb-6 p-4 bg-gray-900/50 rounded-lg border border-gray-600">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Want guided help?</h4>
              <p className="text-gray-400 text-sm">
                Let us walk you through completing your profile step by step
              </p>
            </div>
            <button
              onClick={handleStartGuided}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
            >
              Start Guide
            </button>
          </div>
        </div>
      )}

      {/* Completion Steps */}
      <div className="space-y-3">
        <AnimatePresence>
          {stepsToShow.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ delay: index * 0.1 }}
              className={`p-4 rounded-lg border transition-all cursor-pointer ${
                step.completed
                  ? 'bg-green-900/20 border-green-500/30 hover:bg-green-900/30'
                  : 'bg-gray-700/50 border-gray-600 hover:bg-gray-700 hover:border-purple-500/50'
              }`}
              onClick={() => handleStepClick(step.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    step.completed ? 'bg-green-500/20' : 'bg-gray-600/50'
                  }`}>
                    {step.completed ? (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    ) : (
                      <step.icon className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                  <div>
                    <h5 className={`font-medium ${step.completed ? 'text-green-400' : 'text-white'}`}>
                      {step.title}
                    </h5>
                    <p className="text-gray-400 text-sm">{step.description}</p>
                    {!step.completed && (
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-purple-400 text-xs font-medium">
                          +{step.weight}%
                        </span>
                        <span className="text-gray-500 text-xs">
                          {step.estimatedTime}
                        </span>
                        <span className={`px-2 py-0.5 rounded-full text-xs ${
                          step.category === 'essential' ? 'bg-red-900/30 text-red-400' :
                          step.category === 'recommended' ? 'bg-yellow-900/30 text-yellow-400' :
                          'bg-gray-900/30 text-gray-400'
                        }`}>
                          {step.category}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <ChevronRight className={`w-5 h-5 ${
                  step.completed ? 'text-green-400' : 'text-gray-400'
                }`} />
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Show More/Less Toggle */}
      {prioritySteps.length < completionSteps.length && (
        <button
          onClick={() => setShowAllSteps(!showAllSteps)}
          className="w-full mt-4 py-2 text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors"
        >
          {showAllSteps ? 'Show Less' : `Show All ${completionSteps.length} Steps`}
        </button>
      )}

      {/* Completion Celebration */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            onClick={onCelebrationComplete}
          >
            <motion.div
              initial={{ y: 50 }}
              animate={{ y: 0 }}
              className="bg-gray-800 rounded-lg border border-purple-500 p-8 text-center max-w-md mx-4"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <Trophy className="w-8 h-8 text-white" />
              </motion.div>
              <h3 className="text-xl font-bold text-white mb-2">Congratulations!</h3>
              <p className="text-gray-400 mb-4">
                You've completed another step in your profile journey!
              </p>
              <button
                onClick={onCelebrationComplete}
                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Continue
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ProgressiveCompletionFlow
