/**
 * Settings Backup & Restore Component
 * 
 * Provides functionality to backup, restore, and sync profile settings
 * across devices and sessions. Includes export/import capabilities and
 * automatic backup scheduling.
 * 
 * Features:
 * - Automatic settings backup
 * - Manual backup creation and restoration
 * - Settings export/import (JSON format)
 * - Cross-device synchronization
 * - Backup history and versioning
 * - Selective restore options
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Download,
  Upload,
  Save,
  RotateCcw,
  Clock,
  Shield,
  Smartphone,
  Monitor,
  Cloud,
  CheckCircle,
  AlertTriangle,
  Info,
  FileText,
  Settings,
  Trash2,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react'
import { UserProfile, PrivacySettings } from '@/types/profile'
import toast from 'react-hot-toast'

interface SettingsBackupRestoreProps {
  profile: UserProfile | null
  onRestore?: (settings: Partial<UserProfile>) => Promise<void>
  onBackup?: (settings: Partial<UserProfile>) => Promise<void>
  className?: string
}

interface BackupData {
  id: string
  timestamp: Date
  deviceInfo: {
    type: 'mobile' | 'desktop' | 'tablet'
    browser: string
    os: string
  }
  settings: {
    privacy?: PrivacySettings
    preferences?: any
    notifications?: any
    security?: any
  }
  metadata: {
    version: string
    size: number
    checksum: string
  }
}

interface ExportOptions {
  includePrivacy: boolean
  includePreferences: boolean
  includeNotifications: boolean
  includeSecurity: boolean
  format: 'json' | 'csv'
  encrypted: boolean
}

/**
 * Generate backup data from profile
 */
const generateBackupData = (profile: UserProfile | null): BackupData | null => {
  if (!profile) return null

  const deviceInfo = {
    type: (window.innerWidth < 768 ? 'mobile' : window.innerWidth < 1024 ? 'tablet' : 'desktop') as 'mobile' | 'desktop' | 'tablet',
    browser: navigator.userAgent.includes('Chrome') ? 'Chrome' : 
             navigator.userAgent.includes('Firefox') ? 'Firefox' : 
             navigator.userAgent.includes('Safari') ? 'Safari' : 'Unknown',
    os: navigator.platform
  }

  const settings = {
    privacy: profile.privacy,
    preferences: profile.preferences,
    notifications: profile.notifications,
    security: {
      mfaEnabled: profile.mfaEnabled,
      trustedDevices: profile.trustedDevices?.length || 0,
      lastPasswordChange: profile.lastPasswordChange
    }
  }

  const settingsString = JSON.stringify(settings)
  const checksum = btoa(settingsString).slice(0, 8) // Simple checksum

  return {
    id: `backup_${Date.now()}`,
    timestamp: new Date(),
    deviceInfo,
    settings,
    metadata: {
      version: '1.0.0',
      size: settingsString.length,
      checksum
    }
  }
}

/**
 * Backup History Component
 */
const BackupHistory: React.FC<{
  backups: BackupData[]
  onRestore: (backup: BackupData) => void
  onDelete: (id: string) => void
  onView: (backup: BackupData) => void
}> = ({ backups, onRestore, onDelete, onView }) => {
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'mobile': return Smartphone
      case 'tablet': return Smartphone
      case 'desktop': return Monitor
      default: return Monitor
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }

  return (
    <div className="space-y-3">
      {backups.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          <Clock size={48} className="mx-auto mb-4 opacity-50" />
          <p>No backups found</p>
        </div>
      ) : (
        backups.map((backup) => {
          const DeviceIcon = getDeviceIcon(backup.deviceInfo.type)
          return (
            <div
              key={backup.id}
              className="bg-gray-700 rounded-lg p-4 flex items-center justify-between"
            >
              <div className="flex items-center space-x-4">
                <DeviceIcon size={20} className="text-gray-400" />
                <div>
                  <div className="text-white font-medium">
                    {backup.timestamp.toLocaleDateString()} at {backup.timestamp.toLocaleTimeString()}
                  </div>
                  <div className="text-sm text-gray-400">
                    {backup.deviceInfo.browser} on {backup.deviceInfo.os} • {formatFileSize(backup.metadata.size)}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onView(backup)}
                  className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors"
                  title="View backup details"
                >
                  <Eye size={16} />
                </button>
                <button
                  onClick={() => onRestore(backup)}
                  className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors text-sm"
                >
                  Restore
                </button>
                <button
                  onClick={() => onDelete(backup.id)}
                  className="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-600 rounded transition-colors"
                  title="Delete backup"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          )
        })
      )}
    </div>
  )
}

/**
 * Export Settings Component
 */
const ExportSettings: React.FC<{
  profile: UserProfile | null
  onExport: (data: string, filename: string) => void
}> = ({ profile, onExport }) => {
  const [options, setOptions] = useState<ExportOptions>({
    includePrivacy: true,
    includePreferences: true,
    includeNotifications: true,
    includeSecurity: false,
    format: 'json',
    encrypted: false
  })

  const handleExport = useCallback(() => {
    if (!profile) return

    const exportData: any = {}
    
    if (options.includePrivacy && profile.privacy) {
      exportData.privacy = profile.privacy
    }
    if (options.includePreferences && profile.preferences) {
      exportData.preferences = profile.preferences
    }
    if (options.includeNotifications && profile.notifications) {
      exportData.notifications = profile.notifications
    }
    if (options.includeSecurity) {
      exportData.security = {
        mfaEnabled: profile.mfaEnabled,
        trustedDevicesCount: profile.trustedDevices?.length || 0
      }
    }

    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `syndicaps-settings-${timestamp}.${options.format}`
    
    let data: string
    if (options.format === 'json') {
      data = JSON.stringify(exportData, null, 2)
    } else {
      // Simple CSV export
      const rows = Object.entries(exportData).flatMap(([section, settings]) =>
        Object.entries(settings as any).map(([key, value]) => 
          `${section},${key},${value}`
        )
      )
      data = 'Section,Setting,Value\n' + rows.join('\n')
    }

    if (options.encrypted) {
      // Simple base64 encoding (in production, use proper encryption)
      data = btoa(data)
    }

    onExport(data, filename)
  }, [profile, options, onExport])

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white">Export Options</h3>
      
      {/* Include Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-300">Include in Export:</h4>
        <div className="grid grid-cols-2 gap-3">
          {[
            { key: 'includePrivacy', label: 'Privacy Settings', icon: Shield },
            { key: 'includePreferences', label: 'Preferences', icon: Settings },
            { key: 'includeNotifications', label: 'Notifications', icon: Info },
            { key: 'includeSecurity', label: 'Security Settings', icon: Shield }
          ].map(({ key, label, icon: Icon }) => (
            <label key={key} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={options[key as keyof ExportOptions] as boolean}
                onChange={(e) => setOptions(prev => ({ ...prev, [key]: e.target.checked }))}
                className="rounded border-gray-600 bg-gray-700 text-accent-500 focus:ring-accent-500"
              />
              <Icon size={16} className="text-gray-400" />
              <span className="text-sm text-gray-300">{label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Format Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-300">Format:</h4>
        <div className="flex space-x-4">
          {[
            { value: 'json', label: 'JSON' },
            { value: 'csv', label: 'CSV' }
          ].map(({ value, label }) => (
            <label key={value} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="format"
                value={value}
                checked={options.format === value}
                onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'json' | 'csv' }))}
                className="border-gray-600 bg-gray-700 text-accent-500 focus:ring-accent-500"
              />
              <span className="text-sm text-gray-300">{label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Security Options */}
      <div className="space-y-3">
        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={options.encrypted}
            onChange={(e) => setOptions(prev => ({ ...prev, encrypted: e.target.checked }))}
            className="rounded border-gray-600 bg-gray-700 text-accent-500 focus:ring-accent-500"
          />
          <Shield size={16} className="text-gray-400" />
          <span className="text-sm text-gray-300">Encrypt export (Base64)</span>
        </label>
      </div>

      <button
        onClick={handleExport}
        disabled={!profile}
        className="w-full px-4 py-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
      >
        <Download size={16} />
        <span>Export Settings</span>
      </button>
    </div>
  )
}

/**
 * Import Settings Component
 */
const ImportSettings: React.FC<{
  onImport: (settings: any) => void
}> = ({ onImport }) => {
  const [dragActive, setDragActive] = useState(false)

  const handleFile = useCallback((file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        let data: any

        if (file.name.endsWith('.json')) {
          // Try to decode if it's base64 encoded
          try {
            const decoded = atob(content)
            data = JSON.parse(decoded)
          } catch {
            data = JSON.parse(content)
          }
        } else if (file.name.endsWith('.csv')) {
          // Simple CSV parsing
          const lines = content.split('\n').slice(1) // Skip header
          data = {}
          lines.forEach(line => {
            const [section, key, value] = line.split(',')
            if (!data[section]) data[section] = {}
            data[section][key] = value
          })
        }

        onImport(data)
        toast.success('Settings imported successfully')
      } catch (error) {
        console.error('Import error:', error)
        toast.error('Failed to import settings. Please check the file format.')
      }
    }
    reader.readAsText(file)
  }, [onImport])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFile(files[0])
    }
  }, [handleFile])

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      handleFile(files[0])
    }
  }, [handleFile])

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white">Import Settings</h3>
      
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive 
            ? 'border-accent-500 bg-accent-500/10' 
            : 'border-gray-600 hover:border-gray-500'
        }`}
        onDragOver={(e) => {
          e.preventDefault()
          setDragActive(true)
        }}
        onDragLeave={() => setDragActive(false)}
        onDrop={handleDrop}
      >
        <Upload size={48} className="mx-auto text-gray-400 mb-4" />
        <h4 className="text-white font-medium mb-2">
          Drop your settings file here
        </h4>
        <p className="text-gray-400 text-sm mb-4">
          Supports JSON and CSV files exported from Syndicaps
        </p>
        
        <label className="inline-block px-4 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg cursor-pointer transition-colors">
          <span>Choose File</span>
          <input
            type="file"
            accept=".json,.csv"
            onChange={handleFileInput}
            className="hidden"
          />
        </label>
      </div>

      <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle size={20} className="text-yellow-400 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="text-yellow-400 font-medium mb-1">Import Warning</h4>
            <p className="text-gray-300 text-sm">
              Importing settings will overwrite your current configuration. 
              Consider creating a backup before proceeding.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Main Settings Backup & Restore Component
 */
const SettingsBackupRestore: React.FC<SettingsBackupRestoreProps> = ({
  profile,
  onRestore,
  onBackup,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'backup' | 'export' | 'import'>('backup')
  const [backups, setBackups] = useState<BackupData[]>([])
  const [selectedBackup, setSelectedBackup] = useState<BackupData | null>(null)

  const handleCreateBackup = useCallback(async () => {
    const backupData = generateBackupData(profile)
    if (!backupData) return

    try {
      await onBackup?.(backupData.settings)
      setBackups(prev => [backupData, ...prev])
      toast.success('Backup created successfully')
    } catch (error) {
      console.error('Backup error:', error)
      toast.error('Failed to create backup')
    }
  }, [profile, onBackup])

  const handleRestoreBackup = useCallback(async (backup: BackupData) => {
    try {
      await onRestore?.(backup.settings)
      toast.success('Settings restored successfully')
    } catch (error) {
      console.error('Restore error:', error)
      toast.error('Failed to restore settings')
    }
  }, [onRestore])

  const handleExport = useCallback((data: string, filename: string) => {
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Settings exported successfully')
  }, [])

  const handleImport = useCallback(async (settings: any) => {
    try {
      await onRestore?.(settings)
    } catch (error) {
      console.error('Import error:', error)
      throw error
    }
  }, [onRestore])

  const tabs = [
    { id: 'backup', label: 'Backup & Restore', icon: Save },
    { id: 'export', label: 'Export', icon: Download },
    { id: 'import', label: 'Import', icon: Upload }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-2">Settings Backup & Restore</h2>
        <p className="text-gray-400">
          Backup, restore, and sync your profile settings across devices.
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-700 rounded-lg p-1">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-colors ${
              activeTab === tab.id
                ? 'bg-accent-500 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-600'
            }`}
          >
            <tab.icon size={16} />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        {activeTab === 'backup' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Backup History</h3>
              <button
                onClick={handleCreateBackup}
                disabled={!profile}
                className="px-4 py-2 bg-accent-500 hover:bg-accent-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"
              >
                <Save size={16} />
                <span>Create Backup</span>
              </button>
            </div>
            
            <BackupHistory
              backups={backups}
              onRestore={handleRestoreBackup}
              onDelete={(id) => setBackups(prev => prev.filter(b => b.id !== id))}
              onView={setSelectedBackup}
            />
          </div>
        )}

        {activeTab === 'export' && (
          <ExportSettings profile={profile} onExport={handleExport} />
        )}

        {activeTab === 'import' && (
          <ImportSettings onImport={handleImport} />
        )}
      </div>
    </div>
  )
}

export default SettingsBackupRestore
export type { SettingsBackupRestoreProps, BackupData, ExportOptions }
