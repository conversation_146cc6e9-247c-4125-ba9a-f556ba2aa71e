/**
 * Smart Recommendations Component
 * 
 * Provides AI-powered recommendations for profile improvements, settings
 * optimization, and personalized suggestions based on user behavior and
 * profile analysis.
 * 
 * Features:
 * - Profile improvement recommendations
 * - Privacy settings optimization suggestions
 * - Security enhancement recommendations
 * - Personalization suggestions based on usage patterns
 * - Smart defaults and auto-configuration
 * - Learning from user preferences
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Lightbulb,
  TrendingUp,
  Shield,
  Eye,
  Users,
  Star,
  Zap,
  Target,
  Award,
  CheckCircle,
  X,
  ArrowRight,
  Brain,
  Sparkles,
  Settings,
  Lock,
  Heart,
  MessageSquare,
  Bell,
  Palette,
  Globe
} from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { calculatePrivacyScore } from '@/components/privacy/PrivacyLevelPresets'
import { getRoleDisplayName, isAdminRole } from '@/constants/roles'

interface SmartRecommendationsProps {
  profile: UserProfile | null
  userBehavior?: UserBehaviorData
  onApplyRecommendation?: (recommendation: Recommendation) => void
  onDismissRecommendation?: (id: string) => void
  className?: string
}

interface UserBehaviorData {
  pageViews: Record<string, number>
  timeSpent: Record<string, number>
  actionsPerformed: string[]
  lastActive: Date
  deviceType: 'mobile' | 'desktop' | 'tablet'
  preferredTheme: 'dark' | 'light' | 'auto'
}

interface Recommendation {
  id: string
  type: 'profile' | 'privacy' | 'security' | 'personalization' | 'engagement'
  priority: 'high' | 'medium' | 'low'
  title: string
  description: string
  impact: string
  effort: 'easy' | 'medium' | 'hard'
  estimatedTime: string
  action: string
  actionLabel: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  category: string
  benefits: string[]
  dismissed?: boolean
  applied?: boolean
}

/**
 * Generate smart recommendations based on profile and behavior
 */
const generateRecommendations = (
  profile: UserProfile | null,
  behavior?: UserBehaviorData
): Recommendation[] => {
  if (!profile) return []

  const recommendations: Recommendation[] = []

  // Profile Completion Recommendations
  if (!profile.avatar) {
    recommendations.push({
      id: 'add-profile-picture',
      type: 'profile',
      priority: 'high',
      title: 'Add a Profile Picture',
      description: 'Profiles with pictures get 40% more engagement and appear more trustworthy.',
      impact: 'High engagement boost',
      effort: 'easy',
      estimatedTime: '2 minutes',
      action: 'upload-avatar',
      actionLabel: 'Upload Photo',
      icon: Users,
      category: 'Profile Completion',
      benefits: [
        'Increased profile views',
        'Better first impressions',
        'Higher trust score'
      ]
    })
  }

  if (!profile.bio || profile.bio.length < 50) {
    recommendations.push({
      id: 'improve-bio',
      type: 'profile',
      priority: 'medium',
      title: 'Write a Compelling Bio',
      description: 'A good bio helps others understand who you are and what you\'re passionate about.',
      impact: 'Better connections',
      effort: 'easy',
      estimatedTime: '5 minutes',
      action: 'edit-bio',
      actionLabel: 'Write Bio',
      icon: MessageSquare,
      category: 'Profile Completion',
      benefits: [
        'More meaningful connections',
        'Better search visibility',
        'Personal branding'
      ]
    })
  }

  // Privacy Recommendations
  const privacyScore = profile.privacy ? calculatePrivacyScore(profile.privacy) : 0
  if (privacyScore < 60) {
    recommendations.push({
      id: 'improve-privacy',
      type: 'privacy',
      priority: 'high',
      title: 'Strengthen Your Privacy',
      description: 'Your privacy score is below recommended levels. Review your settings to protect your data.',
      impact: 'Better data protection',
      effort: 'medium',
      estimatedTime: '10 minutes',
      action: 'review-privacy',
      actionLabel: 'Review Settings',
      icon: Eye,
      category: 'Privacy & Security',
      benefits: [
        'Enhanced data protection',
        'Control over information sharing',
        'Peace of mind'
      ]
    })
  }

  // Security Recommendations
  if (!profile.mfaEnabled) {
    recommendations.push({
      id: 'enable-mfa',
      type: 'security',
      priority: 'high',
      title: 'Enable Two-Factor Authentication',
      description: 'Protect your account with an extra layer of security. MFA reduces breach risk by 99.9%.',
      impact: 'Maximum security',
      effort: 'easy',
      estimatedTime: '3 minutes',
      action: 'setup-mfa',
      actionLabel: 'Enable MFA',
      icon: Shield,
      category: 'Privacy & Security',
      benefits: [
        '99.9% breach protection',
        'Secure account recovery',
        'Peace of mind'
      ]
    })
  }

  // Engagement Recommendations
  if (profile.points && profile.points < 100) {
    recommendations.push({
      id: 'earn-points',
      type: 'engagement',
      priority: 'medium',
      title: 'Start Earning Points',
      description: 'Complete your profile and engage with the community to earn points and unlock rewards.',
      impact: 'Unlock rewards',
      effort: 'easy',
      estimatedTime: 'Ongoing',
      action: 'view-rewards',
      actionLabel: 'View Rewards',
      icon: Star,
      category: 'Engagement',
      benefits: [
        'Unlock exclusive rewards',
        'Level up your profile',
        'Community recognition'
      ]
    })
  }

  // Personalization Recommendations based on behavior
  if (behavior) {
    if (behavior.deviceType === 'mobile' && !profile.preferences?.mobileOptimized) {
      recommendations.push({
        id: 'mobile-optimization',
        type: 'personalization',
        priority: 'medium',
        title: 'Optimize for Mobile',
        description: 'We noticed you primarily use mobile. Enable mobile optimizations for a better experience.',
        impact: 'Better mobile experience',
        effort: 'easy',
        estimatedTime: '1 minute',
        action: 'enable-mobile-optimization',
        actionLabel: 'Optimize',
        icon: Zap,
        category: 'Personalization',
        benefits: [
          'Faster loading times',
          'Touch-friendly interface',
          'Better battery life'
        ]
      })
    }

    if (behavior.preferredTheme === 'dark' && profile.preferences?.theme !== 'dark') {
      recommendations.push({
        id: 'dark-theme',
        type: 'personalization',
        priority: 'low',
        title: 'Switch to Dark Theme',
        description: 'Based on your system preferences, you might prefer our dark theme.',
        impact: 'Better visual comfort',
        effort: 'easy',
        estimatedTime: '30 seconds',
        action: 'set-dark-theme',
        actionLabel: 'Apply Theme',
        icon: Palette,
        category: 'Personalization',
        benefits: [
          'Reduced eye strain',
          'Better battery life',
          'Modern appearance'
        ]
      })
    }
  }

  // Social Recommendations
  if (!profile.socialLinks || Object.keys(profile.socialLinks).length === 0) {
    recommendations.push({
      id: 'add-social-links',
      type: 'profile',
      priority: 'low',
      title: 'Connect Your Social Accounts',
      description: 'Link your social media accounts to help others find and connect with you.',
      impact: 'Better discoverability',
      effort: 'easy',
      estimatedTime: '5 minutes',
      action: 'add-social-links',
      actionLabel: 'Add Links',
      icon: Globe,
      category: 'Profile Completion',
      benefits: [
        'Increased discoverability',
        'Cross-platform connections',
        'Professional networking'
      ]
    })
  }

  // Notification Recommendations
  if (!profile.preferences?.emailNotifications) {
    recommendations.push({
      id: 'enable-notifications',
      type: 'engagement',
      priority: 'low',
      title: 'Stay Updated with Notifications',
      description: 'Enable notifications to stay informed about important updates and community activity.',
      impact: 'Stay connected',
      effort: 'easy',
      estimatedTime: '2 minutes',
      action: 'setup-notifications',
      actionLabel: 'Enable',
      icon: Bell,
      category: 'Engagement',
      benefits: [
        'Never miss important updates',
        'Stay engaged with community',
        'Timely security alerts'
      ]
    })
  }

  // Role-specific recommendations
  if (profile.role && isAdminRole(profile.role)) {
    const roleDisplayName = getRoleDisplayName(profile.role)
    recommendations.push({
      id: 'admin-profile-optimization',
      type: 'profile',
      priority: 'medium',
      title: `Optimize Your ${roleDisplayName} Profile`,
      description: `As a ${roleDisplayName}, consider adding professional information to help users identify your administrative role.`,
      impact: 'Better user trust',
      effort: 'easy',
      estimatedTime: '5 minutes',
      action: 'optimize-admin-profile',
      actionLabel: 'Optimize Profile',
      icon: Shield,
      category: 'Profile Completion',
      benefits: [
        'Increased user trust',
        'Clear role identification',
        'Professional appearance'
      ]
    })
  }

  // Sort by priority and return
  return recommendations.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 }
    return priorityOrder[b.priority] - priorityOrder[a.priority]
  })
}

/**
 * Recommendation Card Component
 */
const RecommendationCard: React.FC<{
  recommendation: Recommendation
  onApply: () => void
  onDismiss: () => void
}> = ({ recommendation, onApply, onDismiss }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const priorityColors = {
    high: 'border-red-500 bg-red-500/10',
    medium: 'border-yellow-500 bg-yellow-500/10',
    low: 'border-blue-500 bg-blue-500/10'
  }

  const effortColors = {
    easy: 'text-green-400',
    medium: 'text-yellow-400',
    hard: 'text-red-400'
  }

  return (
    <motion.div
      layout
      className={`border-2 rounded-lg p-4 ${priorityColors[recommendation.priority]}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <recommendation.icon size={24} className="text-accent-400 flex-shrink-0 mt-1" />
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-white font-semibold">{recommendation.title}</h3>
              <span className={`text-xs px-2 py-1 rounded-full ${
                recommendation.priority === 'high' ? 'bg-red-500/20 text-red-400' :
                recommendation.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-blue-500/20 text-blue-400'
              }`}>
                {recommendation.priority}
              </span>
            </div>
            
            <p className="text-gray-300 text-sm mb-3">{recommendation.description}</p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-400 mb-3">
              <span>Impact: {recommendation.impact}</span>
              <span>•</span>
              <span className={effortColors[recommendation.effort]}>
                {recommendation.effort} effort
              </span>
              <span>•</span>
              <span>{recommendation.estimatedTime}</span>
            </div>

            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mb-3"
                >
                  <h4 className="text-white font-medium mb-2">Benefits:</h4>
                  <ul className="space-y-1">
                    {recommendation.benefits.map((benefit, index) => (
                      <li key={index} className="text-gray-300 text-sm flex items-center">
                        <CheckCircle size={12} className="text-green-400 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="flex items-center space-x-3">
              <button
                onClick={onApply}
                className="px-4 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors flex items-center space-x-2 text-sm"
              >
                <span>{recommendation.actionLabel}</span>
                <ArrowRight size={14} />
              </button>
              
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-gray-400 hover:text-white text-sm"
              >
                {isExpanded ? 'Show less' : 'Learn more'}
              </button>
            </div>
          </div>
        </div>

        <button
          onClick={onDismiss}
          className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <X size={16} />
        </button>
      </div>
    </motion.div>
  )
}

/**
 * Main Smart Recommendations Component
 */
const SmartRecommendations: React.FC<SmartRecommendationsProps> = ({
  profile,
  userBehavior,
  onApplyRecommendation,
  onDismissRecommendation,
  className = ''
}) => {
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set())
  
  const recommendations = useMemo(() => {
    return generateRecommendations(profile, userBehavior)
      .filter(rec => !dismissedIds.has(rec.id))
  }, [profile, userBehavior, dismissedIds])

  const handleApply = (recommendation: Recommendation) => {
    onApplyRecommendation?.(recommendation)
  }

  const handleDismiss = (id: string) => {
    setDismissedIds(prev => new Set([...prev, id]))
    onDismissRecommendation?.(id)
  }

  const categorizedRecommendations = recommendations.reduce((acc, rec) => {
    if (!acc[rec.category]) acc[rec.category] = []
    acc[rec.category].push(rec)
    return acc
  }, {} as Record<string, Recommendation[]>)

  if (recommendations.length === 0) {
    return (
      <div className={`bg-gray-800 rounded-lg p-8 text-center border border-gray-700 ${className}`}>
        <Sparkles size={48} className="mx-auto text-green-400 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">All Caught Up!</h3>
        <p className="text-gray-400">
          Your profile is well-optimized. We'll let you know when we have new suggestions.
        </p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Brain size={24} className="text-accent-400" />
        <div>
          <h2 className="text-2xl font-bold text-white">Smart Recommendations</h2>
          <p className="text-gray-400">
            Personalized suggestions to improve your profile and experience
          </p>
        </div>
      </div>

      {/* Recommendations by Category */}
      {Object.entries(categorizedRecommendations).map(([category, recs]) => (
        <div key={category}>
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Target size={20} className="mr-2 text-accent-400" />
            {category}
            <span className="ml-2 text-sm text-gray-400">({recs.length})</span>
          </h3>
          
          <div className="space-y-4">
            {recs.map((recommendation) => (
              <RecommendationCard
                key={recommendation.id}
                recommendation={recommendation}
                onApply={() => handleApply(recommendation)}
                onDismiss={() => handleDismiss(recommendation.id)}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

export default SmartRecommendations
export type { SmartRecommendationsProps, Recommendation, UserBehaviorData }
