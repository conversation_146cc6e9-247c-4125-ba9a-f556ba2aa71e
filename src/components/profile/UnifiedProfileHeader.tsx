'use client'

/**
 * Unified Profile Header Component
 * 
 * Consolidates profile information display with completion status
 * and provides clear visual hierarchy for better user experience.
 * 
 * Features:
 * - Integrated user information and completion status
 * - Clear primary and secondary CTAs
 * - Mobile-responsive design
 * - Syndicaps design system compliance
 * - Accessibility support
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import React from 'react'
import { motion } from 'framer-motion'
import {
  User,
  Edit3,
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Globe,
  CheckCircle,
  Shield,
  Settings,
  Trophy
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import UserAvatar from '@/components/ui/UserAvatar'
import { UserProfile } from '@/types/profile'
import { User as FirebaseUser } from 'firebase/auth'

interface UnifiedProfileHeaderProps {
  user: FirebaseUser
  profile: UserProfile
  completionPercentage: number
  securityScore: number
  totalPoints: number
  onEditClick: () => void
  onSecurityClick?: () => void
  showBackButton?: boolean
  className?: string
}

interface ProfileStatProps {
  label: string
  value: string | number
  color: string
  icon: React.ComponentType<any>
}

const ProfileStat: React.FC<ProfileStatProps> = ({ label, value, color, icon: Icon }) => (
  <div className="text-center">
    <div className={`text-2xl font-bold ${color} flex items-center justify-center space-x-1`}>
      <Icon className="w-5 h-5" />
      <span>{value}</span>
    </div>
    <div className="text-gray-400 text-sm">{label}</div>
  </div>
)

const getCompletionColor = (percentage: number): string => {
  if (percentage >= 80) return 'text-green-400'
  if (percentage >= 60) return 'text-blue-400'
  if (percentage >= 40) return 'text-yellow-400'
  return 'text-red-400'
}

const getSecurityColor = (score: number): string => {
  if (score >= 80) return 'text-green-400'
  if (score >= 60) return 'text-yellow-400'
  return 'text-red-400'
}

export default function UnifiedProfileHeader({
  user,
  profile,
  completionPercentage,
  securityScore,
  totalPoints,
  onEditClick,
  onSecurityClick,
  showBackButton = true,
  className = ''
}: UnifiedProfileHeaderProps) {
  const router = useRouter()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-gray-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden ${className}`}
    >
      {/* Header Section */}
      <div className="p-4 sm:p-6 border-b border-gray-700">
        <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-3">
            {showBackButton && (
              <button
                onClick={() => router.back()}
                className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-700 min-h-[44px] min-w-[44px] flex items-center justify-center"
                aria-label="Go back"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
            )}
            <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
              <User className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-white">Profile Management</h1>
              <p className="text-gray-400 text-sm sm:text-base hidden sm:block">Complete your profile and manage settings</p>
            </div>
          </div>

          {/* Primary CTA */}
          <button
            onClick={onEditClick}
            className="w-full sm:w-auto bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-purple-500/25 transform hover:scale-105 min-h-[44px]"
          >
            <Edit3 className="w-5 h-5" />
            <span>Edit Profile</span>
          </button>
        </div>
      </div>

      {/* Profile Information Section */}
      <div className="p-4 sm:p-6">
        <div className="flex flex-col space-y-6">
          {/* User Info */}
          <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="flex items-center space-x-4 sm:flex-col sm:space-x-0 sm:space-y-2">
              <div className="relative">
                <UserAvatar
                  user={user}
                  profile={profile}
                  size="xl"
                  className="ring-4 ring-purple-500/20"
                />
                {profile.emailVerified && (
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>

              {/* Mobile Stats - Show on mobile next to avatar */}
              <div className="grid grid-cols-3 gap-2 sm:hidden">
                <ProfileStat
                  label="Complete"
                  value={`${completionPercentage}%`}
                  color={getCompletionColor(completionPercentage)}
                  icon={User}
                />
                <ProfileStat
                  label="Security"
                  value={`${securityScore}%`}
                  color={getSecurityColor(securityScore)}
                  icon={Shield}
                />
                <ProfileStat
                  label="Points"
                  value={totalPoints}
                  color="text-purple-400"
                  icon={Trophy}
                />
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2">
                  <h2 className="text-lg sm:text-xl font-bold text-white truncate">
                    {profile.displayName || 'Complete Your Profile'}
                  </h2>
                  {profile.role && (
                    <span className="px-2 py-1 bg-purple-600 text-white text-xs rounded-full self-start">
                      {profile.role}
                    </span>
                  )}
                </div>
              </div>

              {profile.username && (
                <p className="text-gray-400 mb-2 text-sm">@{profile.username}</p>
              )}

              {profile.bio && (
                <p className="text-gray-300 text-sm mb-3 line-clamp-2">{profile.bio}</p>
              )}

              {/* Contact Information - Responsive Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                <div className="flex items-center space-x-2 text-gray-400 min-w-0">
                  <Mail className="w-4 h-4 flex-shrink-0" />
                  <span className="flex items-center space-x-1 min-w-0">
                    <span className="truncate">{profile.email}</span>
                    {profile.emailVerified && (
                      <CheckCircle className="w-3 h-3 text-green-400 flex-shrink-0" />
                    )}
                  </span>
                </div>

                {profile.phone && (
                  <div className="flex items-center space-x-2 text-gray-400 min-w-0">
                    <Phone className="w-4 h-4 flex-shrink-0" />
                    <span className="flex items-center space-x-1 min-w-0">
                      <span className="truncate">{profile.phone}</span>
                      {profile.phoneVerified && (
                        <CheckCircle className="w-3 h-3 text-green-400 flex-shrink-0" />
                      )}
                    </span>
                  </div>
                )}

                {profile.location && (
                  <div className="flex items-center space-x-2 text-gray-400 min-w-0">
                    <MapPin className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">{profile.location}</span>
                  </div>
                )}

                {profile.website && (
                  <div className="flex items-center space-x-2 text-gray-400 min-w-0">
                    <Globe className="w-4 h-4 flex-shrink-0" />
                    <a
                      href={profile.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-purple-400 transition-colors truncate"
                    >
                      Website
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Desktop Stats & Actions */}
          <div className="hidden sm:flex sm:items-center sm:justify-between">
            <div className="grid grid-cols-3 gap-6">
              <ProfileStat
                label="Complete"
                value={`${completionPercentage}%`}
                color={getCompletionColor(completionPercentage)}
                icon={User}
              />
              <ProfileStat
                label="Security"
                value={`${securityScore}%`}
                color={getSecurityColor(securityScore)}
                icon={Shield}
              />
              <ProfileStat
                label="Points"
                value={totalPoints}
                color="text-purple-400"
                icon={Trophy}
              />
            </div>

            {/* Secondary Actions */}
            <div className="flex space-x-2">
              {onSecurityClick && (
                <button
                  onClick={onSecurityClick}
                  className="flex items-center space-x-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm min-h-[44px]"
                >
                  <Shield className="w-4 h-4" />
                  <span>Security</span>
                </button>
              )}
              <button
                onClick={() => router.push('/profile/settings')}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm min-h-[44px]"
              >
                <Settings className="w-4 h-4" />
                <span>Settings</span>
              </button>
            </div>
          </div>

          {/* Mobile Secondary Actions */}
          <div className="flex sm:hidden space-x-2">
            {onSecurityClick && (
              <button
                onClick={onSecurityClick}
                className="flex-1 flex items-center justify-center space-x-2 px-3 py-3 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm min-h-[44px]"
              >
                <Shield className="w-4 h-4" />
                <span>Security</span>
              </button>
            )}
            <button
              onClick={() => router.push('/profile/settings')}
              className="flex-1 flex items-center justify-center space-x-2 px-3 py-3 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm min-h-[44px]"
            >
              <Settings className="w-4 h-4" />
              <span>Settings</span>
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-300">Profile Completion</span>
            <span className={`text-sm font-bold ${getCompletionColor(completionPercentage)}`}>
              {completionPercentage}%
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <motion.div
              className={`h-2 rounded-full transition-all duration-500 ${
                completionPercentage >= 80 ? 'bg-green-500' :
                completionPercentage >= 60 ? 'bg-blue-500' :
                completionPercentage >= 40 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              initial={{ width: 0 }}
              animate={{ width: `${completionPercentage}%` }}
              transition={{ duration: 1, ease: 'easeOut' }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  )
}
