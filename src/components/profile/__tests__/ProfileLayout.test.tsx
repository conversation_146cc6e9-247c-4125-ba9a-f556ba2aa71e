/**
 * ProfileLayout Component Tests
 * 
 * Comprehensive test suite for the ProfileLayout component covering
 * rendering, navigation, responsive behavior, and error handling.
 * 
 * <AUTHOR> Team
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import ProfileLayout from '../ProfileLayout'
import { useUser } from '@/lib/useUser'
import { useWishlistStore } from '@/store/wishlistStore'

// Mock dependencies
jest.mock('@/lib/useUser')
jest.mock('@/store/wishlistStore')
jest.mock('../gamification/error/GamificationErrorBoundary', () => {
  return function MockGamificationErrorBoundary({ children }: { children: React.ReactNode }) {
    return <div data-testid="gamification-error-boundary">{children}</div>
  }
})

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>
const mockUseWishlistStore = useWishlistStore as jest.MockedFunction<typeof useWishlistStore>

// Mock user data
const mockUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User'
}

const mockProfile = {
  id: 'test-profile-id',
  firstName: 'Test',
  lastName: 'User',
  displayName: 'Test User',
  email: '<EMAIL>',
  createdAt: new Date('2024-01-01'),
  profileCompletion: 75
}

describe('ProfileLayout', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()
    
    // Default mock implementations
    mockUseUser.mockReturnValue({
      user: mockUser,
      profile: mockProfile,
      loading: false,
      isAdmin: false,
      isModerator: false
    })
    
    mockUseWishlistStore.mockReturnValue({
      items: [],
      itemCount: 0,
      addItem: jest.fn(),
      removeItem: jest.fn(),
      clearWishlist: jest.fn()
    })
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn()
      },
      writable: true
    })
  })

  describe('Rendering', () => {
    it('renders children correctly', () => {
      render(
        <ProfileLayout>
          <div data-testid="test-content">Test Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('test-content')).toBeInTheDocument()
    })

    it('renders with default props', () => {
      render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByRole('main')).toBeInTheDocument()
    })

    it('applies custom className', () => {
      render(
        <ProfileLayout className="custom-class">
          <div>Content</div>
        </ProfileLayout>
      )
      
      const layout = screen.getByRole('main')
      expect(layout).toHaveClass('custom-class')
    })
  })

  describe('Navigation Variants', () => {
    it('renders smart navigation by default', () => {
      render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      // Smart navigation should be rendered
      expect(screen.getByTestId('smart-navigation')).toBeInTheDocument()
    })

    it('renders simple navigation when specified', () => {
      render(
        <ProfileLayout navigation="simple">
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('profile-navigation')).toBeInTheDocument()
    })

    it('renders bottom navigation for mobile', () => {
      render(
        <ProfileLayout navigation="bottom">
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('profile-bottom-nav')).toBeInTheDocument()
    })
  })

  describe('Layout Variants', () => {
    it('renders full variant with all features', () => {
      render(
        <ProfileLayout variant="full">
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('smart-navigation')).toBeInTheDocument()
      expect(screen.getByRole('main')).toHaveClass('full-layout')
    })

    it('renders simple variant with minimal features', () => {
      render(
        <ProfileLayout variant="simple">
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('profile-navigation')).toBeInTheDocument()
      expect(screen.getByRole('main')).toHaveClass('simple-layout')
    })

    it('renders mobile variant with bottom navigation', () => {
      render(
        <ProfileLayout variant="mobile">
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('profile-bottom-nav')).toBeInTheDocument()
      expect(screen.getByRole('main')).toHaveClass('mobile-layout')
    })
  })

  describe('Welcome Modal', () => {
    it('shows welcome modal for new users', async () => {
      // Mock new user (profile created recently)
      const newProfile = {
        ...mockProfile,
        createdAt: new Date() // Created now
      }
      
      mockUseUser.mockReturnValue({
        user: mockUser,
        profile: newProfile,
        loading: false,
        isAdmin: false,
        isModerator: false
      })
      
      // Mock localStorage to not have welcome shown
      const mockGetItem = jest.fn().mockReturnValue(null)
      Object.defineProperty(window, 'localStorage', {
        value: { ...window.localStorage, getItem: mockGetItem },
        writable: true
      })
      
      render(
        <ProfileLayout showWelcome={true}>
          <div>Content</div>
        </ProfileLayout>
      )
      
      await waitFor(() => {
        expect(screen.getByTestId('enhanced-welcome-modal')).toBeInTheDocument()
      })
    })

    it('does not show welcome modal when disabled', () => {
      render(
        <ProfileLayout showWelcome={false}>
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.queryByTestId('enhanced-welcome-modal')).not.toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    it('shows skeleton when loading and enabled', () => {
      mockUseUser.mockReturnValue({
        user: null,
        profile: null,
        loading: true,
        isAdmin: false,
        isModerator: false
      })
      
      render(
        <ProfileLayout showSkeleton={true}>
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('profile-skeleton')).toBeInTheDocument()
    })

    it('does not show skeleton when disabled', () => {
      mockUseUser.mockReturnValue({
        user: null,
        profile: null,
        loading: true,
        isAdmin: false,
        isModerator: false
      })
      
      render(
        <ProfileLayout showSkeleton={false}>
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.queryByTestId('profile-skeleton')).not.toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('renders error boundary', () => {
      render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByTestId('gamification-error-boundary')).toBeInTheDocument()
    })

    it('handles missing user gracefully', () => {
      mockUseUser.mockReturnValue({
        user: null,
        profile: null,
        loading: false,
        isAdmin: false,
        isModerator: false
      })
      
      render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      expect(screen.getByRole('main')).toBeInTheDocument()
    })
  })

  describe('Responsive Behavior', () => {
    it('switches to mobile navigation on small screens', () => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500
      })
      
      render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      // Should automatically switch to bottom navigation
      expect(screen.getByTestId('profile-bottom-nav')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      const main = screen.getByRole('main')
      expect(main).toHaveAttribute('aria-label', 'Profile content')
    })

    it('supports keyboard navigation', () => {
      render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      const navigation = screen.getByTestId('smart-navigation')
      expect(navigation).toHaveAttribute('tabIndex', '0')
    })
  })

  describe('Performance', () => {
    it('memoizes expensive calculations', () => {
      const { rerender } = render(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      // Re-render with same props should not trigger recalculation
      rerender(
        <ProfileLayout>
          <div>Content</div>
        </ProfileLayout>
      )
      
      // Component should render without issues
      expect(screen.getByRole('main')).toBeInTheDocument()
    })
  })
})
