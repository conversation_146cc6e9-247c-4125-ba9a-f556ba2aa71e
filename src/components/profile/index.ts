/**
 * Profile Components Barrel Export
 * 
 * Centralized exports for all profile-related components to improve
 * tree shaking and provide cleaner import statements.
 * 
 * <AUTHOR> Team
 */

// Core Profile Components
export { default as ProfileLayout } from './ProfileLayout'
export { default as EnhancedProfileEditor } from './EnhancedProfileEditor'
export { default as ProfileCompletionTracker } from './ProfileCompletionTracker'
export { default as ProfilePhotoUpload } from './ProfilePhotoUpload'
export { default as RecommendationEngine } from './RecommendationEngine'
export { default as MemberTierDisplay } from './MemberTierDisplay'

// Layout Components
export { default as SmartNavigation } from './layout/SmartNavigation'
export { default as ProfileNavigation } from './layout/ProfileNavigation'
export { default as ProfileBottomNav } from './layout/ProfileBottomNav'
export { default as ProgressiveDashboard } from './layout/ProgressiveDashboard'
export { default as MobileOptimizations } from './layout/MobileOptimizations'

// Social Components
export { default as SocialProfileHeader } from './social/SocialProfileHeader'
export { default as SocialActivityTimeline } from './social/SocialActivityTimeline'
export { default as CommunityConnections } from './social/CommunityConnections'
export { default as SocialSharing } from './social/SocialSharing'
export { default as ProfileVisibilitySettings } from './social/ProfileVisibilitySettings'
export { default as SocialStats } from './social/SocialStats'

// Dropdown Components
export { 
  OptimizedUserProfileDropdown,
  ProfileDropdownButton,
  ProfileDropdownHeader,
  ProfileDropdownTabs
} from './dropdown'

// Search Components
export { default as AdvancedSearchDashboard } from './search/AdvancedSearchDashboard'

// Recommendation Components
export { default as RecommendationsDashboard } from './recommendations/RecommendationsDashboard'

// Onboarding Components
export { default as OnboardingWizard } from './onboarding/OnboardingWizard'

// Error Boundary
export { default as ProfileErrorBoundary } from './ProfileErrorBoundary'

// Types (re-export for convenience)
export type { UserProfile, ProfileFormData } from '@/types/profile'
