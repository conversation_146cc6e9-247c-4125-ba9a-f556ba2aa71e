/**
 * Breadcrumb Navigation Component
 * 
 * Provides breadcrumb navigation for complex profile flows to improve user orientation.
 * Automatically generates breadcrumbs based on current route and navigation structure.
 * 
 * Features:
 * - Automatic breadcrumb generation from route
 * - Click-to-navigate functionality
 * - Mobile-responsive design
 * - Accessibility compliant (WCAG 2.1 AA)
 * - Integration with UnifiedNavigation structure
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { ChevronRight, Home } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface BreadcrumbItem {
  label: string
  href: string
  icon?: React.ComponentType<{ size?: number; className?: string }>
  isActive?: boolean
}

interface BreadcrumbNavigationProps {
  className?: string
  showHome?: boolean
  maxItems?: number
  customItems?: BreadcrumbItem[]
}

/**
 * Route to breadcrumb mapping
 */
const ROUTE_BREADCRUMBS: Record<string, BreadcrumbItem[]> = {
  '/profile': [
    { label: 'Profile', href: '/profile/account' }
  ],
  '/profile/account': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Account Details', href: '/profile/account' }
  ],
  '/profile/contact': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Account & Personal', href: '/profile/account' },
    { label: 'Contact Information', href: '/profile/contact' }
  ],
  '/profile/edit': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Account & Personal', href: '/profile/account' },
    { label: 'Edit Profile', href: '/profile/edit' }
  ],
  '/profile/privacy': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Account & Personal', href: '/profile/account' },
    { label: 'Privacy Settings', href: '/profile/privacy' }
  ],
  '/profile/security': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Account & Personal', href: '/profile/account' },
    { label: 'Security', href: '/profile/security' }
  ],
  '/profile/wishlist': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Orders & Activity', href: '/profile/orders' },
    { label: 'Wishlist', href: '/profile/wishlist' }
  ],
  '/profile/orders': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Orders & Activity', href: '/profile/orders' },
    { label: 'Orders', href: '/profile/orders' }
  ],
  '/profile/raffle-entries': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Orders & Activity', href: '/profile/orders' },
    { label: 'Raffle Entries', href: '/profile/raffle-entries' }
  ],
  '/profile/activity': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Orders & Activity', href: '/profile/orders' },
    { label: 'Activity History', href: '/profile/activity' }
  ],
  '/profile/points': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Rewards & Social', href: '/profile/points' },
    { label: 'Point History', href: '/profile/points' }
  ],
  '/profile/achievements': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Rewards & Social', href: '/profile/points' },
    { label: 'Achievements', href: '/profile/achievements' }
  ],
  '/profile/social': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Rewards & Social', href: '/profile/points' },
    { label: 'Social Profile', href: '/profile/social' }
  ],
  '/profile/gamification': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Rewards & Social', href: '/profile/points' },
    { label: 'Gamification', href: '/profile/gamification' }
  ],
  '/profile/analytics': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Analytics & Settings', href: '/profile/analytics' },
    { label: 'Analytics', href: '/profile/analytics' }
  ],
  '/profile/notifications': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Analytics & Settings', href: '/profile/analytics' },
    { label: 'Notifications', href: '/profile/notifications' }
  ],
  '/profile/preferences': [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Analytics & Settings', href: '/profile/analytics' },
    { label: 'Preferences', href: '/profile/preferences' }
  ]
}

/**
 * Generate breadcrumbs from current path
 */
const generateBreadcrumbs = (pathname: string, customItems?: BreadcrumbItem[]): BreadcrumbItem[] => {
  if (customItems) {
    return customItems.map((item, index, array) => ({
      ...item,
      isActive: index === array.length - 1
    }))
  }

  const breadcrumbs = ROUTE_BREADCRUMBS[pathname] || [
    { label: 'Profile', href: '/profile/account' },
    { label: 'Unknown Page', href: pathname }
  ]

  return breadcrumbs.map((item, index, array) => ({
    ...item,
    isActive: index === array.length - 1
  }))
}

/**
 * Individual breadcrumb item component
 */
const BreadcrumbItem: React.FC<{
  item: BreadcrumbItem
  isLast: boolean
  showSeparator: boolean
}> = ({ item, isLast, showSeparator }) => {
  const content = (
    <span className={`
      flex items-center space-x-1 text-sm transition-colors
      ${item.isActive 
        ? 'text-accent-400 font-medium' 
        : 'text-gray-400 hover:text-white'
      }
    `}>
      {item.icon && <item.icon size={14} />}
      <span className="truncate max-w-[120px] sm:max-w-none">{item.label}</span>
    </span>
  )

  return (
    <li className="flex items-center">
      {item.isActive ? (
        content
      ) : (
        <Link href={item.href} className="focus:outline-none focus:ring-2 focus:ring-accent-500 rounded">
          {content}
        </Link>
      )}
      
      {showSeparator && (
        <ChevronRight 
          size={14} 
          className="mx-2 text-gray-500 flex-shrink-0" 
          aria-hidden="true"
        />
      )}
    </li>
  )
}

/**
 * Main breadcrumb navigation component
 */
const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  className = '',
  showHome = true,
  maxItems = 4,
  customItems
}) => {
  const pathname = usePathname()
  
  // Don't show breadcrumbs on the main profile page
  if (pathname === '/profile' || pathname === '/profile/account') {
    return null
  }

  const breadcrumbs = generateBreadcrumbs(pathname, customItems)
  
  // Add home breadcrumb if requested
  const allBreadcrumbs = showHome 
    ? [{ label: 'Home', href: '/', icon: Home }, ...breadcrumbs]
    : breadcrumbs

  // Truncate breadcrumbs if too many
  const displayBreadcrumbs = allBreadcrumbs.length > maxItems
    ? [
        allBreadcrumbs[0],
        { label: '...', href: '#', isActive: false },
        ...allBreadcrumbs.slice(-2)
      ]
    : allBreadcrumbs

  return (
    <motion.nav
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-gray-800/50 rounded-lg px-4 py-2 mb-4 ${className}`}
      aria-label="Breadcrumb navigation"
    >
      <ol className="flex items-center space-x-1 overflow-x-auto">
        {displayBreadcrumbs.map((item, index) => (
          <BreadcrumbItem
            key={`${item.href}-${index}`}
            item={item}
            isLast={index === displayBreadcrumbs.length - 1}
            showSeparator={index < displayBreadcrumbs.length - 1}
          />
        ))}
      </ol>
    </motion.nav>
  )
}

/**
 * Hook for breadcrumb management
 */
export const useBreadcrumbs = () => {
  const pathname = usePathname()
  
  return {
    currentPath: pathname,
    breadcrumbs: generateBreadcrumbs(pathname),
    shouldShow: pathname !== '/profile' && pathname !== '/profile/account'
  }
}

export default BreadcrumbNavigation
export type { BreadcrumbNavigationProps, BreadcrumbItem }
