/**
 * Global Keyboard Shortcuts for Profile System
 * 
 * Provides global keyboard shortcuts for profile navigation and actions.
 * Implements common shortcuts found in modern applications like Discord, Slack, etc.
 * 
 * Shortcuts:
 * - Ctrl+, (Cmd+,): Open profile settings
 * - Ctrl+K (Cmd+K): Open search
 * - Ctrl+Shift+P: Open preferences
 * - Ctrl+Shift+S: Open security settings
 * - Ctrl+Shift+N: Open notifications
 * - Escape: Close modals/search
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/lib/useUser'
import toast from 'react-hot-toast'

interface KeyboardShortcutsProps {
  onToggleSearch?: () => void
  onToggleQuickSettings?: () => void
  enabled?: boolean
}

/**
 * Keyboard shortcut definitions
 */
const SHORTCUTS = {
  OPEN_PROFILE: { key: ',', ctrl: true, action: '/profile/account', description: 'Open profile settings' },
  OPEN_SEARCH: { key: 'k', ctrl: true, action: 'search', description: 'Search settings' },
  OPEN_PREFERENCES: { key: 'p', ctrl: true, shift: true, action: '/profile/preferences', description: 'Open preferences' },
  OPEN_SECURITY: { key: 's', ctrl: true, shift: true, action: '/profile/security', description: 'Open security settings' },
  OPEN_NOTIFICATIONS: { key: 'n', ctrl: true, shift: true, action: '/profile/notifications', description: 'Open notifications' },
  OPEN_PRIVACY: { key: 'y', ctrl: true, shift: true, action: '/profile/privacy', description: 'Open privacy settings' },
  CLOSE_MODAL: { key: 'Escape', action: 'close', description: 'Close modal or search' },
  HELP: { key: '?', shift: true, action: 'help', description: 'Show keyboard shortcuts' }
} as const

/**
 * Global keyboard shortcuts component
 */
const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
  onToggleSearch,
  onToggleQuickSettings,
  enabled = true
}) => {
  const router = useRouter()
  const { user } = useUser()

  /**
   * Handle keyboard shortcut
   */
  const handleShortcut = useCallback((shortcut: typeof SHORTCUTS[keyof typeof SHORTCUTS]) => {
    if (!enabled) return

    switch (shortcut.action) {
      case 'search':
        onToggleSearch?.()
        break
      case 'close':
        onToggleSearch?.() // Close search if open
        break
      case 'help':
        showKeyboardHelp()
        break
      default:
        if (shortcut.action.startsWith('/')) {
          router.push(shortcut.action)
          toast.success(`Navigated to ${shortcut.description.toLowerCase()}`)
        }
        break
    }
  }, [enabled, onToggleSearch, router])

  /**
   * Show keyboard shortcuts help
   */
  const showKeyboardHelp = useCallback(() => {
    const shortcuts = Object.entries(SHORTCUTS).map(([key, shortcut]) => {
      const keys = []
      if (shortcut.ctrl) keys.push(navigator.platform.includes('Mac') ? 'Cmd' : 'Ctrl')
      if (shortcut.shift) keys.push('Shift')
      keys.push(shortcut.key === ',' ? 'Comma' : shortcut.key.toUpperCase())
      
      return `${keys.join('+')} - ${shortcut.description}`
    }).join('\n')

    toast.success(
      `Keyboard Shortcuts:\n\n${shortcuts}`,
      {
        duration: 8000,
        style: {
          whiteSpace: 'pre-line',
          textAlign: 'left',
          maxWidth: '400px'
        }
      }
    )
  }, [])

  /**
   * Handle keydown events
   */
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement ||
      (event.target as HTMLElement)?.contentEditable === 'true'
    ) {
      // Allow Escape to work in inputs
      if (event.key === 'Escape') {
        handleShortcut(SHORTCUTS.CLOSE_MODAL)
      }
      return
    }

    // Check each shortcut
    for (const shortcut of Object.values(SHORTCUTS)) {
      const ctrlPressed = event.ctrlKey || event.metaKey
      const shiftPressed = event.shiftKey
      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase()

      if (
        keyMatches &&
        (shortcut.ctrl ? ctrlPressed : !ctrlPressed) &&
        (shortcut.shift ? shiftPressed : !shiftPressed)
      ) {
        event.preventDefault()
        handleShortcut(shortcut)
        return
      }
    }
  }, [handleShortcut])

  /**
   * Set up keyboard event listeners
   */
  useEffect(() => {
    if (!enabled || !user) return

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [enabled, user, handleKeyDown])

  // This component doesn't render anything
  return null
}

/**
 * Hook for keyboard shortcuts
 */
export const useKeyboardShortcuts = (options: Omit<KeyboardShortcutsProps, 'enabled'> = {}) => {
  const { user } = useUser()
  
  return {
    enabled: !!user,
    shortcuts: SHORTCUTS,
    showHelp: () => {
      const shortcuts = Object.entries(SHORTCUTS).map(([key, shortcut]) => {
        const keys = []
        if (shortcut.ctrl) keys.push(navigator.platform.includes('Mac') ? 'Cmd' : 'Ctrl')
        if (shortcut.shift) keys.push('Shift')
        keys.push(shortcut.key === ',' ? 'Comma' : shortcut.key.toUpperCase())
        
        return `${keys.join('+')} - ${shortcut.description}`
      }).join('\n')

      toast.success(
        `Keyboard Shortcuts:\n\n${shortcuts}`,
        {
          duration: 8000,
          style: {
            whiteSpace: 'pre-line',
            textAlign: 'left',
            maxWidth: '400px'
          }
        }
      )
    }
  }
}

/**
 * Keyboard shortcuts indicator component
 */
export const KeyboardShortcutsIndicator: React.FC<{
  shortcut: keyof typeof SHORTCUTS
  className?: string
}> = ({ shortcut, className = '' }) => {
  const shortcutDef = SHORTCUTS[shortcut]
  const isMac = typeof navigator !== 'undefined' && navigator.platform.includes('Mac')
  
  const keys = []
  if (shortcutDef.ctrl) keys.push(isMac ? '⌘' : 'Ctrl')
  if (shortcutDef.shift) keys.push('⇧')
  keys.push(shortcutDef.key === ',' ? ',' : shortcutDef.key.toUpperCase())

  return (
    <span className={`inline-flex items-center space-x-1 text-xs text-gray-500 ${className}`}>
      {keys.map((key, index) => (
        <span key={index}>
          <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-xs font-mono">
            {key}
          </kbd>
          {index < keys.length - 1 && <span className="mx-0.5">+</span>}
        </span>
      ))}
    </span>
  )
}

export default KeyboardShortcuts
export type { KeyboardShortcutsProps }
