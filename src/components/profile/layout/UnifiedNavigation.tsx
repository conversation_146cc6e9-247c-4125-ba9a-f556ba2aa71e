/**
 * Unified Profile Navigation Component
 * 
 * Consolidates SmartNavigation, ProfileNavigation, and ProfileBottomNav into a single,
 * responsive navigation system that adapts to device capabilities and user preferences.
 * 
 * Features:
 * - Responsive design: Desktop sidebar → Mobile bottom nav
 * - 4-category structure aligned across all breakpoints
 * - Keyboard shortcuts (Ctrl+, to open settings)
 * - Search functionality with keyboard navigation
 * - Quick settings panel integration
 * - Breadcrumb navigation for complex flows
 * - WCAG 2.1 AA accessibility compliance
 * - Haptic feedback for mobile interactions
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  User,
  Package,
  Trophy,
  BarChart3,
  Search,
  Settings,
  Keyboard,
  ChevronRight,
  Home,
  Heart,
  Shield,
  Bell,
  Edit,
  Activity,
  Star,
  Users,
  Lock,
  Eye,
  Calendar,
  CreditCard,
  Mail
} from 'lucide-react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { UserProfile } from '@/types/profile'
import BreadcrumbNavigation from './BreadcrumbNavigation'

// Types
interface NavigationItem {
  id: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  label: string
  href: string
  description: string
  badge?: number | string
  badgeType?: 'notification' | 'count' | 'status' | 'new'
  keywords?: string[]
  category: string
  priority?: number
  quickAction?: boolean
}

interface NavigationCategory {
  id: string
  label: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  description: string
  items: NavigationItem[]
  priority: 'high' | 'medium' | 'low'
  quickSettings?: string[] // IDs of items that should appear in quick settings
}

interface UnifiedNavigationProps {
  profile: UserProfile | null
  wishlistItemCount: number
  loading: boolean
  variant?: 'desktop' | 'mobile' | 'auto'
  showSearch?: boolean
  showQuickSettings?: boolean
  showBreadcrumbs?: boolean
  className?: string
  onNavigate?: (href: string) => void
}

interface NavigationState {
  searchQuery: string
  showSearch: boolean
  activeCategory: string
  recentlyVisited: string[]
  keyboardNavIndex: number
}

/**
 * Get contextual badges for navigation items
 */
const getContextualBadges = (profile: UserProfile | null, wishlistItemCount: number) => {
  const badges: Record<string, { badge?: number | string; badgeType?: string }> = {}

  // Profile completion badge
  if (profile && profile.profileCompletion?.percentage < 100) {
    badges['account'] = {
      badge: Math.floor((100 - profile.profileCompletion.percentage) / 20),
      badgeType: 'info'
    }
  }

  // Wishlist badge
  if (wishlistItemCount > 0) {
    badges['wishlist'] = {
      badge: wishlistItemCount,
      badgeType: 'count'
    }
  }

  // Points badge
  if (profile?.points) {
    badges['points'] = {
      badge: profile.points.toLocaleString(),
      badgeType: 'status'
    }
  }

  // New achievements badge (placeholder)
  // TODO: Implement actual achievement tracking
  badges['achievements'] = {
    badge: 'new',
    badgeType: 'new'
  }

  return badges
}

/**
 * Unified 4-Category Navigation Configuration
 * Consistent across desktop sidebar and mobile bottom nav
 */
const getNavigationCategories = (
  profile: UserProfile | null, 
  wishlistItemCount: number
): NavigationCategory[] => {
  const badges = getContextualBadges(profile, wishlistItemCount)

  return [
    {
      id: 'account',
      label: 'Account & Personal',
      icon: User,
      description: 'Personal information and account management',
      priority: 'high',
      quickSettings: ['privacy-toggle', 'notifications-toggle'],
      items: [
        {
          id: 'account',
          icon: User,
          label: 'Account Overview',
          href: '/profile/account',
          description: 'Account summary and activity dashboard',
          keywords: ['profile', 'account', 'overview', 'dashboard', 'summary'],
          category: 'account',
          priority: 10,
          quickAction: true,
          ...badges['account']
        },
        {
          id: 'edit-profile',
          icon: Edit,
          label: 'Edit Profile',
          href: '/profile/edit',
          description: 'Manage profile information, contact details, addresses, and privacy settings',
          keywords: ['edit', 'profile', 'contact', 'address', 'privacy', 'personal', 'phone', 'email', 'update', 'modify', 'change'],
          category: 'account',
          priority: 9,
          quickAction: true
        },
        {
          id: 'privacy',
          icon: Eye,
          label: 'Privacy Settings',
          href: '/profile/privacy',
          description: 'Control your privacy and visibility',
          keywords: ['privacy', 'visibility', 'security', 'data'],
          category: 'account',
          priority: 9,
          quickAction: true
        },
        {
          id: 'security',
          icon: Shield,
          label: 'Security',
          href: '/profile/security',
          description: 'Password, MFA, and security settings',
          keywords: ['security', 'password', 'mfa', 'authentication'],
          category: 'account',
          priority: 9,
          quickAction: true
        }
      ]
    },
    {
      id: 'orders',
      label: 'Orders & Activity',
      icon: Package,
      description: 'Shopping history and user activity',
      priority: 'high',
      quickSettings: ['order-notifications'],
      items: [
        {
          id: 'wishlist',
          icon: Heart,
          label: 'Wishlist',
          href: '/profile/wishlist',
          description: 'View your saved products',
          keywords: ['wishlist', 'saved', 'favorites', 'products'],
          category: 'orders',
          priority: 9,
          quickAction: true,
          ...badges['wishlist']
        },
        {
          id: 'orders',
          icon: Package,
          label: 'Orders',
          href: '/profile/orders',
          description: 'Track your orders and history',
          keywords: ['orders', 'purchases', 'history', 'tracking'],
          category: 'orders',
          priority: 10
        },
        {
          id: 'raffle-entries',
          icon: Trophy,
          label: 'Raffle Entries',
          href: '/profile/raffle-entries',
          description: 'View your raffle entries and wins',
          keywords: ['raffle', 'entries', 'wins', 'contests'],
          category: 'orders',
          priority: 7
        },
        {
          id: 'activity',
          icon: Activity,
          label: 'Activity History',
          href: '/profile/activity',
          description: 'Your recent activity and interactions',
          keywords: ['activity', 'history', 'interactions', 'timeline'],
          category: 'orders',
          priority: 6
        }
      ]
    },
    {
      id: 'rewards',
      label: 'Rewards & Social',
      icon: Trophy,
      description: 'Rewards, achievements, and social features',
      priority: 'medium',
      quickSettings: ['social-visibility'],
      items: [
        {
          id: 'points',
          icon: Star,
          label: 'Point History',
          href: '/profile/points',
          description: 'Track your points and rewards',
          keywords: ['points', 'rewards', 'history', 'balance'],
          category: 'rewards',
          priority: 9,
          ...badges['points']
        },
        {
          id: 'achievements',
          icon: Trophy,
          label: 'Achievements',
          href: '/profile/achievements',
          description: 'View your achievements and progress',
          keywords: ['achievements', 'badges', 'progress', 'milestones'],
          category: 'rewards',
          priority: 8,
          ...badges['achievements']
        },
        {
          id: 'social',
          icon: Users,
          label: 'Social Profile',
          href: '/profile/social',
          description: 'Your social presence and connections',
          keywords: ['social', 'community', 'connections', 'sharing'],
          category: 'rewards',
          priority: 7
        },
        {
          id: 'gamification',
          icon: Trophy,
          label: 'Gamification',
          href: '/profile/gamification',
          description: 'Level progression and game features',
          keywords: ['gamification', 'levels', 'progression', 'games'],
          category: 'rewards',
          priority: 6
        }
      ]
    },
    {
      id: 'analytics',
      label: 'Analytics & Settings',
      icon: BarChart3,
      description: 'Data insights and system preferences',
      priority: 'low',
      quickSettings: ['theme-toggle', 'language-toggle'],
      items: [
        {
          id: 'analytics',
          icon: BarChart3,
          label: 'Analytics',
          href: '/profile/analytics',
          description: 'Personal analytics and insights',
          keywords: ['analytics', 'insights', 'data', 'statistics'],
          category: 'analytics',
          priority: 7
        },
        {
          id: 'notifications',
          icon: Bell,
          label: 'Notifications',
          href: '/profile/notifications',
          description: 'Manage your notification preferences',
          keywords: ['notifications', 'alerts', 'preferences', 'settings'],
          category: 'analytics',
          priority: 8,
          quickAction: true
        },
        {
          id: 'preferences',
          icon: Settings,
          label: 'Preferences',
          href: '/profile/preferences',
          description: 'App preferences and settings',
          keywords: ['preferences', 'settings', 'configuration', 'options'],
          category: 'analytics',
          priority: 6
        }
      ]
    }
  ]
}

/**
 * Hook for keyboard shortcuts
 */
const useKeyboardShortcuts = (onToggleSearch: () => void) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+, or Cmd+, to open profile settings
      if ((event.ctrlKey || event.metaKey) && event.key === ',') {
        event.preventDefault()
        window.location.href = '/profile/account'
        return
      }

      // Ctrl+K or Cmd+K to open search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        onToggleSearch()
        return
      }

      // Escape to close search
      if (event.key === 'Escape') {
        onToggleSearch()
        return
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onToggleSearch])
}

/**
 * Hook for mobile detection and responsive behavior
 */
const useResponsiveNavigation = () => {
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth
      setIsMobile(width < 768)
      setIsTablet(width >= 768 && width < 1024)
    }

    checkDevice()
    window.addEventListener('resize', checkDevice)
    return () => window.removeEventListener('resize', checkDevice)
  }, [])

  return { isMobile, isTablet }
}

/**
 * Navigation Search Component
 */
const NavigationSearch: React.FC<{
  searchQuery: string
  onSearchChange: (query: string) => void
  onClose: () => void
  items: NavigationItem[]
}> = ({ searchQuery, onSearchChange, onClose, items }) => {
  const [filteredItems, setFilteredItems] = useState<NavigationItem[]>([])

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredItems([])
      return
    }

    const query = searchQuery.toLowerCase()
    const filtered = items.filter(item =>
      item.label.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query) ||
      item.keywords?.some(keyword => keyword.toLowerCase().includes(query))
    ).slice(0, 5) // Limit to 5 results

    setFilteredItems(filtered)
  }, [searchQuery, items])

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="bg-gray-800 border border-gray-600 rounded-lg p-4 mb-4"
    >
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
        <input
          type="text"
          placeholder="Search settings... (Ctrl+K)"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500"
          autoFocus
        />
      </div>

      {filteredItems.length > 0 && (
        <div className="mt-3 space-y-1">
          {filteredItems.map((item) => (
            <Link
              key={item.id}
              href={item.href}
              onClick={onClose}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <item.icon size={16} className="text-gray-400" />
              <div className="flex-1">
                <div className="text-sm font-medium text-white">{item.label}</div>
                <div className="text-xs text-gray-400">{item.description}</div>
              </div>
              <ChevronRight size={14} className="text-gray-500" />
            </Link>
          ))}
        </div>
      )}

      {searchQuery.trim() && filteredItems.length === 0 && (
        <div className="mt-3 text-center text-gray-400 text-sm">
          No settings found for "{searchQuery}"
        </div>
      )}
    </motion.div>
  )
}

/**
 * Quick Settings Panel Component
 */
const QuickSettingsPanel: React.FC<{
  profile: UserProfile | null
  onToggle: (setting: string, value: boolean) => void
}> = ({ profile, onToggle }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 border border-gray-600 rounded-lg p-4 mb-4"
    >
      <h3 className="text-sm font-semibold text-gray-300 mb-3 flex items-center">
        <Settings size={16} className="mr-2" />
        Quick Settings
      </h3>

      <div className="space-y-3">
        {/* Privacy Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Eye size={14} className="text-gray-400" />
            <span className="text-sm text-gray-300">Profile Visibility</span>
          </div>
          <button
            onClick={() => onToggle('profileVisibility', !profile?.privacy?.profileVisibility)}
            className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
              profile?.privacy?.profileVisibility ? 'bg-accent-500' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                profile?.privacy?.profileVisibility ? 'translate-x-5' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Notifications Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell size={14} className="text-gray-400" />
            <span className="text-sm text-gray-300">Notifications</span>
          </div>
          <button
            onClick={() => onToggle('notifications', !profile?.preferences?.emailNotifications)}
            className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
              profile?.preferences?.emailNotifications ? 'bg-accent-500' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                profile?.preferences?.emailNotifications ? 'translate-x-5' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      <div className="mt-3 pt-3 border-t border-gray-700">
        <Link
          href="/profile/preferences"
          className="text-xs text-accent-400 hover:text-accent-300 flex items-center"
        >
          View all settings
          <ChevronRight size={12} className="ml-1" />
        </Link>
      </div>
    </motion.div>
  )
}

/**
 * Navigation Item Component
 */
const NavigationItem: React.FC<{
  item: NavigationItem
  isActive: boolean
  isMobile: boolean
  onClick: () => void
}> = ({ item, isActive, isMobile, onClick }) => {
  const handleClick = () => {
    // Haptic feedback for mobile
    if (isMobile && 'vibrate' in navigator) {
      navigator.vibrate(10)
    }
    onClick()
  }

  return (
    <Link
      href={item.href}
      onClick={handleClick}
      className={`
        flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200
        min-h-[44px] touch-target-lg focus:outline-none focus:ring-2 focus:ring-accent-500
        ${isActive
          ? 'bg-accent-900/20 text-accent-400 border-l-2 border-accent-500'
          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
        }
      `}
    >
      <item.icon
        size={isMobile ? 20 : 18}
        className={isActive ? 'text-accent-500' : 'text-gray-400'}
      />
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <span className={`font-medium truncate ${isMobile ? 'text-sm' : 'text-sm'}`}>
            {item.label}
          </span>
          {item.badge && (
            <span className={`
              px-2 py-1 text-xs rounded-full flex-shrink-0 ml-2
              ${item.badgeType === 'notification' ? 'bg-red-500 text-white' :
                item.badgeType === 'count' ? 'bg-accent-500 text-white' :
                item.badgeType === 'new' ? 'bg-green-500 text-white' :
                'bg-gray-600 text-gray-300'}
            `}>
              {item.badge}
            </span>
          )}
        </div>
        {!isMobile && (
          <p className="text-xs text-gray-400 mt-1 truncate">
            {item.description}
          </p>
        )}
      </div>
    </Link>
  )
}

/**
 * Main Unified Navigation Component
 */
const UnifiedNavigation: React.FC<UnifiedNavigationProps> = ({
  profile,
  wishlistItemCount,
  loading,
  variant = 'auto',
  showSearch = true,
  showQuickSettings = true,
  showBreadcrumbs = false,
  className = '',
  onNavigate
}) => {
  const pathname = usePathname()
  const router = useRouter()
  const { isMobile, isTablet } = useResponsiveNavigation()

  // Navigation state
  const [state, setState] = useState<NavigationState>({
    searchQuery: '',
    showSearch: false,
    activeCategory: '',
    recentlyVisited: [],
    keyboardNavIndex: -1
  })

  // Get navigation data
  const categories = useMemo(() =>
    getNavigationCategories(profile, wishlistItemCount),
    [profile, wishlistItemCount]
  )

  const allItems = useMemo(() =>
    categories.flatMap(category => category.items),
    [categories]
  )

  // Determine navigation variant
  const effectiveVariant = variant === 'auto'
    ? (isMobile ? 'mobile' : 'desktop')
    : variant

  // Keyboard shortcuts
  useKeyboardShortcuts(() => {
    setState(prev => ({ ...prev, showSearch: !prev.showSearch }))
  })

  // Handle navigation
  const handleNavigate = useCallback((href: string) => {
    onNavigate?.(href)
    router.push(href)
  }, [onNavigate, router])

  // Handle quick settings toggle
  const handleQuickSettingsToggle = useCallback((setting: string, value: boolean) => {
    // TODO: Implement actual settings update
    console.log('Toggle setting:', setting, value)
  }, [])

  // Check if item is active
  const isActive = useCallback((href: string) => pathname === href, [pathname])

  // Mobile bottom navigation
  if (effectiveVariant === 'mobile') {
    return (
      <motion.nav
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className={`
          fixed bottom-0 left-0 right-0 z-50
          bg-gray-900/95 backdrop-blur-sm border-t border-gray-700
          safe-area-pb ${className}
        `}
      >
        <div className="flex items-center justify-around px-2 py-2">
          {categories.map((category) => {
            const isActiveCategory = category.items.some(item => isActive(item.href))
            const categoryBadge = category.items.find(item => item.badge)?.badge

            return (
              <button
                key={category.id}
                onClick={() => handleNavigate(category.items[0].href)}
                className={`
                  flex flex-col items-center justify-center p-2 rounded-lg
                  min-h-[60px] min-w-[60px] transition-all duration-200
                  ${isActiveCategory
                    ? 'text-accent-400 bg-accent-900/20'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800'
                  }
                `}
              >
                <div className="relative">
                  <category.icon size={24} />
                  {categoryBadge && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {typeof categoryBadge === 'number' && categoryBadge > 9 ? '9+' : categoryBadge}
                    </span>
                  )}
                </div>
                <span className="text-xs mt-1 font-medium">{category.label.split(' ')[0]}</span>
              </button>
            )
          })}
        </div>
        <div className="h-safe-area-inset-bottom bg-gray-900/95" />
      </motion.nav>
    )
  }

  // Desktop sidebar navigation
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className={`bg-gray-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden ${className}`}
    >
      {/* Header with keyboard shortcut hint */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-white">Profile Settings</h2>
          <div className="flex items-center space-x-2">
            {showSearch && (
              <button
                onClick={() => setState(prev => ({ ...prev, showSearch: !prev.showSearch }))}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                title="Search settings (Ctrl+K)"
              >
                <Search size={16} />
              </button>
            )}
            <div className="hidden lg:flex items-center space-x-1 text-xs text-gray-500">
              <Keyboard size={12} />
              <span>Ctrl+,</span>
            </div>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Breadcrumb Navigation */}
        {showBreadcrumbs && !state.showSearch && (
          <BreadcrumbNavigation showHome={false} maxItems={3} />
        )}

        {/* Search */}
        <AnimatePresence>
          {state.showSearch && (
            <NavigationSearch
              searchQuery={state.searchQuery}
              onSearchChange={(query) => setState(prev => ({ ...prev, searchQuery: query }))}
              onClose={() => setState(prev => ({ ...prev, showSearch: false, searchQuery: '' }))}
              items={allItems}
            />
          )}
        </AnimatePresence>

        {/* Quick Settings */}
        {showQuickSettings && !state.showSearch && (
          <QuickSettingsPanel
            profile={profile}
            onToggle={handleQuickSettingsToggle}
          />
        )}

        {/* Navigation Categories */}
        {!state.searchQuery && (
          <nav className="space-y-6">
            {categories.map((category) => (
              <div key={category.id}>
                <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 flex items-center">
                  <category.icon size={14} className="mr-2" />
                  {category.label}
                </h3>
                <div className="space-y-1">
                  {category.items.map((item) => (
                    <NavigationItem
                      key={item.id}
                      item={item}
                      isActive={isActive(item.href)}
                      isMobile={false}
                      onClick={() => handleNavigate(item.href)}
                    />
                  ))}
                </div>
              </div>
            ))}
          </nav>
        )}
      </div>
    </motion.div>
  )
}

export default UnifiedNavigation
export type { UnifiedNavigationProps, NavigationItem, NavigationCategory }
