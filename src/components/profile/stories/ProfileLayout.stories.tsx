/**
 * ProfileLayout Storybook Stories
 * 
 * Interactive documentation and testing for ProfileLayout component
 * with various configurations and use cases.
 * 
 * <AUTHOR> Team
 */

import type { Meta, StoryObj } from '@storybook/react'
import ProfileLayout from '../ProfileLayout'
import { useUser } from '@/lib/useUser'
import { useWishlistStore } from '@/store/wishlistStore'

// Mock the hooks for Storybook
const mockUseUser = {
  user: {
    uid: 'story-user-id',
    email: '<EMAIL>',
    displayName: 'Story User'
  },
  profile: {
    id: 'story-profile-id',
    firstName: 'Story',
    lastName: 'User',
    displayName: 'Story User',
    email: '<EMAIL>',
    createdAt: new Date('2024-01-01'),
    profileCompletion: 75,
    photoURL: 'https://via.placeholder.com/150'
  },
  loading: false,
  isAdmin: false,
  isModerator: false
}

const mockUseWishlistStore = {
  items: [],
  itemCount: 3,
  addItem: () => {},
  removeItem: () => {},
  clearWishlist: () => {}
}

// Mock the hooks
jest.mock('@/lib/useUser', () => ({
  useUser: () => mockUseUser
}))

jest.mock('@/store/wishlistStore', () => ({
  useWishlistStore: () => mockUseWishlistStore
}))

const meta: Meta<typeof ProfileLayout> = {
  title: 'Profile/Layout/ProfileLayout',
  component: ProfileLayout,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
ProfileLayout is the main wrapper component for all profile pages. It provides:

- Responsive navigation (smart, simple, or bottom)
- Layout variants (full, simple, mobile)
- Welcome modal for new users
- Loading skeleton states
- Error boundaries
- Mobile optimizations

## Features

- **Smart Navigation**: Advanced navigation with search and categories
- **Responsive Design**: Automatically adapts to screen size
- **Performance Optimized**: Lazy loading and efficient rendering
- **Accessibility**: Full keyboard navigation and screen reader support
        `
      }
    }
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['full', 'simple', 'mobile'],
      description: 'Layout variant that determines the overall structure'
    },
    navigation: {
      control: 'select',
      options: ['smart', 'simple', 'bottom'],
      description: 'Navigation type to display'
    },
    showHeader: {
      control: 'boolean',
      description: 'Whether to show the header section'
    },
    showWelcome: {
      control: 'boolean',
      description: 'Whether to show welcome modal for new users'
    },
    showSkeleton: {
      control: 'boolean',
      description: 'Whether to show loading skeleton when loading'
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply'
    }
  },
  decorators: [
    (Story) => (
      <div style={{ height: '100vh', background: '#0f0f0f' }}>
        <Story />
      </div>
    )
  ]
}

export default meta
type Story = StoryObj<typeof ProfileLayout>

// Sample content component
const SampleContent = ({ title = 'Profile Content' }: { title?: string }) => (
  <div className="p-6 space-y-6">
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <h2 className="text-2xl font-bold text-white mb-4">{title}</h2>
      <p className="text-gray-300 mb-4">
        This is sample content to demonstrate the ProfileLayout component.
        The layout adapts to different screen sizes and navigation preferences.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">Card {i}</h3>
            <p className="text-gray-400 text-sm">
              Sample card content with some text to show how the layout handles content.
            </p>
          </div>
        ))}
      </div>
    </div>
  </div>
)

// Default story
export const Default: Story = {
  args: {
    variant: 'full',
    navigation: 'smart',
    showHeader: true,
    showWelcome: true,
    showSkeleton: true
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent />
    </ProfileLayout>
  )
}

// Full layout with smart navigation
export const FullLayout: Story = {
  args: {
    variant: 'full',
    navigation: 'smart',
    showHeader: true,
    showWelcome: false,
    showSkeleton: false
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent title="Full Layout with Smart Navigation" />
    </ProfileLayout>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Full layout variant with smart navigation, ideal for desktop users with advanced features.'
      }
    }
  }
}

// Simple layout
export const SimpleLayout: Story = {
  args: {
    variant: 'simple',
    navigation: 'simple',
    showHeader: true,
    showWelcome: false,
    showSkeleton: false
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent title="Simple Layout" />
    </ProfileLayout>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Simplified layout with basic navigation, perfect for focused user experiences.'
      }
    }
  }
}

// Mobile layout
export const MobileLayout: Story = {
  args: {
    variant: 'mobile',
    navigation: 'bottom',
    showHeader: false,
    showWelcome: false,
    showSkeleton: false
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent title="Mobile Layout" />
    </ProfileLayout>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    },
    docs: {
      description: {
        story: 'Mobile-optimized layout with bottom navigation for touch interfaces.'
      }
    }
  }
}

// Loading state
export const LoadingState: Story = {
  args: {
    variant: 'full',
    navigation: 'smart',
    showHeader: true,
    showWelcome: false,
    showSkeleton: true
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent title="Loading State" />
    </ProfileLayout>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Layout with loading skeleton displayed while content is being fetched.'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock loading state
      const originalUseUser = mockUseUser
      mockUseUser.loading = true
      mockUseUser.user = null
      mockUseUser.profile = null
      
      return (
        <div style={{ height: '100vh', background: '#0f0f0f' }}>
          <Story />
        </div>
      )
    }
  ]
}

// New user with welcome modal
export const NewUserWelcome: Story = {
  args: {
    variant: 'full',
    navigation: 'smart',
    showHeader: true,
    showWelcome: true,
    showSkeleton: false
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent title="New User Experience" />
    </ProfileLayout>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Layout for new users showing the welcome modal with onboarding information.'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock new user
      mockUseUser.profile.createdAt = new Date() // Just created
      
      // Mock localStorage to not have welcome shown
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: () => null,
          setItem: () => {},
          removeItem: () => {},
          clear: () => {}
        },
        writable: true
      })
      
      return (
        <div style={{ height: '100vh', background: '#0f0f0f' }}>
          <Story />
        </div>
      )
    }
  ]
}

// Custom styling
export const CustomStyling: Story = {
  args: {
    variant: 'full',
    navigation: 'smart',
    showHeader: true,
    showWelcome: false,
    showSkeleton: false,
    className: 'custom-profile-layout'
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent title="Custom Styled Layout" />
    </ProfileLayout>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Layout with custom CSS classes applied for specialized styling.'
      }
    }
  },
  decorators: [
    (Story) => (
      <div style={{ height: '100vh', background: '#0f0f0f' }}>
        <style>{`
          .custom-profile-layout {
            border: 2px solid #8b5cf6;
            border-radius: 12px;
            margin: 16px;
            overflow: hidden;
          }
        `}</style>
        <Story />
      </div>
    )
  ]
}

// Responsive behavior
export const ResponsiveBehavior: Story = {
  args: {
    variant: 'full',
    navigation: 'smart',
    showHeader: true,
    showWelcome: false,
    showSkeleton: false
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <SampleContent title="Responsive Layout" />
    </ProfileLayout>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates how the layout adapts to different screen sizes. Try resizing the viewport.'
      }
    }
  }
}

// Error state
export const ErrorState: Story = {
  args: {
    variant: 'full',
    navigation: 'smart',
    showHeader: true,
    showWelcome: false,
    showSkeleton: false
  },
  render: (args) => (
    <ProfileLayout {...args}>
      <div className="p-6">
        <div className="bg-red-900/20 border border-red-500 rounded-lg p-6 text-center">
          <h2 className="text-xl font-bold text-red-400 mb-2">Error State</h2>
          <p className="text-red-300">
            This demonstrates how the layout handles error states gracefully.
          </p>
        </div>
      </div>
    </ProfileLayout>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Layout handling error states with appropriate error boundaries and fallback UI.'
      }
    }
  }
}
