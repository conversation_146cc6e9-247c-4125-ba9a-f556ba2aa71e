/**
 * Membership & Rewards Tab Component
 * 
 * Consolidated view of tier benefits, points history, achievements,
 * and membership perks without redundancy.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Crown,
  Star,
  Trophy,
  Gift,
  Percent,
  Truck,
  Clock,
  Zap,
  TrendingUp,
  Award,
  Calendar,
  Target,
  Users,
  Shield
} from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { useAccountData, AccountData } from '@/hooks/useAccountData'
import { getTierInfo, type MemberTier } from '@/lib/memberTiers'

interface MembershipTabProps {
  profile: UserProfile
  className?: string
}

/**
 * Current Tier Status Component
 */
const CurrentTierStatus: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const { tier, points } = accountData

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center ${tier.color}`}>
            <Crown size={24} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">{tier.current}</h2>
            <p className="text-gray-400">Level {tier.level} • {points.current.toLocaleString()} points</p>
          </div>
        </div>
        
        {tier.nextTier && (
          <div className="text-right">
            <div className="text-sm text-gray-400 mb-1">Next: {tier.nextTier}</div>
            <div className="text-lg font-bold text-accent-400">
              {points.pointsToNextTier?.toLocaleString()} points to go
            </div>
          </div>
        )}
      </div>

      {/* Progress to Next Tier */}
      {tier.nextTier && (
        <div className="mb-6">
          <div className="flex justify-between text-sm mb-2">
            <span className="text-gray-400">Progress to {tier.nextTier}</span>
            <span className="text-gray-400">{tier.progress}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3">
            <motion.div
              className="h-3 rounded-full bg-gradient-to-r from-accent-500 to-accent-400"
              initial={{ width: 0 }}
              animate={{ width: `${tier.progress}%` }}
              transition={{ duration: 1, delay: 0.3 }}
            />
          </div>
        </div>
      )}

      {/* Active Benefits */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {tier.discount > 0 && (
          <div className="flex items-center space-x-2 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <Percent size={16} className="text-green-400" />
            <span className="text-green-400 font-medium">{tier.discount}% Discount</span>
          </div>
        )}
        
        {tier.freeShipping && (
          <div className="flex items-center space-x-2 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <Truck size={16} className="text-blue-400" />
            <span className="text-blue-400 font-medium">Free Shipping</span>
          </div>
        )}
        
        {tier.earlyAccessHours > 0 && (
          <div className="flex items-center space-x-2 p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
            <Clock size={16} className="text-purple-400" />
            <span className="text-purple-400 font-medium">{tier.earlyAccessHours}h Early Access</span>
          </div>
        )}
      </div>
    </motion.div>
  )
}

/**
 * Tier Comparison Component
 */
const TierComparison: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const tierOrder: MemberTier[] = ['bronze', 'silver', 'gold', 'platinum', 'diamond']
  const currentTierIndex = tierOrder.findIndex(t => t === accountData.tier.current.toLowerCase())

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
        <Crown size={20} className="mr-2 text-accent-400" />
        Membership Tiers
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        {tierOrder.map((tierKey, index) => {
          const tierDef = getTierInfo(tierKey)
          const isCurrent = tierDef.name.toLowerCase() === accountData.tier.current.toLowerCase()
          const isUnlocked = index <= currentTierIndex
          
          return (
            <motion.div
              key={tierDef.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 + index * 0.1 }}
              className={`p-4 rounded-lg border-2 transition-all ${
                isCurrent
                  ? 'border-accent-500 bg-accent-500/10'
                  : isUnlocked
                    ? 'border-gray-600 bg-gray-700/50'
                    : 'border-gray-700 bg-gray-800/50 opacity-60'
              }`}
            >
              <div className="text-center mb-4">
                <div className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center mb-2 ${tierDef.color}`}>
                  <Crown size={20} className="text-white" />
                </div>
                <h4 className={`font-bold ${isCurrent ? 'text-accent-400' : 'text-white'}`}>
                  {tierDef.name}
                </h4>
                <p className="text-gray-400 text-sm">{tierDef.minPoints.toLocaleString()} points</p>
              </div>
              
              <div className="space-y-2">
                {tierDef.benefits.slice(0, 3).map((benefit, benefitIndex) => (
                  <div key={benefitIndex} className="flex items-center space-x-2 text-sm">
                    <Star size={12} className={isCurrent ? 'text-accent-400' : 'text-gray-400'} />
                    <span className="text-gray-300">{benefit}</span>
                  </div>
                ))}
                
                {tierDef.benefits.length > 3 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{tierDef.benefits.length - 3} more benefits
                  </div>
                )}
              </div>
              
              {isCurrent && (
                <div className="mt-3 text-center">
                  <span className="px-2 py-1 bg-accent-500 text-white text-xs rounded-full">
                    Current Tier
                  </span>
                </div>
              )}
            </motion.div>
          )
        })}
      </div>
    </motion.div>
  )
}

/**
 * Points Summary Component
 */
const PointsSummary: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const { points } = accountData

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
        <Star size={20} className="mr-2 text-accent-400" />
        Points Overview
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-4 bg-gray-700/50 rounded-lg">
          <div className="text-2xl font-bold text-accent-400">{points.current.toLocaleString()}</div>
          <div className="text-gray-400 text-sm">Current Points</div>
        </div>
        
        <div className="text-center p-4 bg-gray-700/50 rounded-lg">
          <div className="text-2xl font-bold text-blue-400">{points.lifetime.toLocaleString()}</div>
          <div className="text-gray-400 text-sm">Lifetime Earned</div>
        </div>
        
        <div className="text-center p-4 bg-gray-700/50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-400">{points.pending.toLocaleString()}</div>
          <div className="text-gray-400 text-sm">Pending</div>
        </div>
        
        <div className="text-center p-4 bg-gray-700/50 rounded-lg">
          <div className="text-2xl font-bold text-green-400">
            {points.nextTierThreshold ? (points.nextTierThreshold - points.current).toLocaleString() : '—'}
          </div>
          <div className="text-gray-400 text-sm">To Next Tier</div>
        </div>
      </div>
    </motion.div>
  )
}

/**
 * Achievements Showcase Component
 */
const AchievementsShowcase: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const mockAchievements = [
    {
      id: 'first-purchase',
      title: 'First Purchase',
      description: 'Made your first purchase',
      icon: '🛒',
      points: 100,
      unlocked: true,
      unlockedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
    },
    {
      id: 'profile-complete',
      title: 'Profile Complete',
      description: 'Completed your profile',
      icon: '✅',
      points: 50,
      unlocked: true,
      unlockedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'big-spender',
      title: 'Big Spender',
      description: 'Spend $500 in total',
      icon: '💰',
      points: 200,
      unlocked: false,
      progress: 75
    },
    {
      id: 'community-star',
      title: 'Community Star',
      description: 'Get 10 community votes',
      icon: '⭐',
      points: 150,
      unlocked: false,
      progress: 30
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <Trophy size={20} className="mr-2 text-accent-400" />
          Achievements
        </h3>
        
        <div className="text-sm text-gray-400">
          {mockAchievements.filter(a => a.unlocked).length} of {mockAchievements.length} unlocked
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {mockAchievements.map((achievement, index) => (
          <motion.div
            key={achievement.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 + index * 0.1 }}
            className={`p-4 rounded-lg border ${
              achievement.unlocked 
                ? 'border-green-500/30 bg-green-500/10' 
                : 'border-gray-600 bg-gray-700/50'
            }`}
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="text-2xl">{achievement.icon}</div>
              <div>
                <h4 className={`font-medium ${achievement.unlocked ? 'text-green-400' : 'text-white'}`}>
                  {achievement.title}
                </h4>
                <p className="text-gray-400 text-sm">{achievement.description}</p>
              </div>
              <div className="text-right">
                <div className={`font-bold ${achievement.unlocked ? 'text-green-400' : 'text-gray-400'}`}>
                  +{achievement.points}
                </div>
              </div>
            </div>
            
            {achievement.unlocked ? (
              <div className="flex items-center space-x-2 text-sm text-green-400">
                <Trophy size={14} />
                <span>Unlocked {achievement.unlockedAt?.toLocaleDateString()}</span>
              </div>
            ) : (
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">Progress</span>
                  <span className="text-gray-400">{achievement.progress}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="h-2 rounded-full bg-gradient-to-r from-accent-500 to-accent-400"
                    style={{ width: `${achievement.progress}%` }}
                  />
                </div>
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

/**
 * Main Membership Tab Component
 */
const MembershipTab: React.FC<MembershipTabProps> = ({ profile, className = '' }) => {
  const accountData = useAccountData(profile)

  if (!accountData) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading membership data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Current Tier Status */}
      <CurrentTierStatus accountData={accountData} />
      
      {/* Points Summary */}
      <PointsSummary accountData={accountData} />
      
      {/* Tier Comparison */}
      <TierComparison accountData={accountData} />
      
      {/* Achievements */}
      <AchievementsShowcase accountData={accountData} />
    </div>
  )
}

export default MembershipTab
