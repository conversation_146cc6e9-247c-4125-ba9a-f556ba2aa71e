/**
 * Overview Tab Component
 * 
 * Consolidated dashboard view showing key account information
 * without redundancy. Single source of truth for user data.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  User,
  Crown,
  TrendingUp,
  Award,
  Trophy,
  Calendar,
  Activity,
  Eye,
  Target,
  Zap,
  Clock
} from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { useAccountData, AccountData } from '@/hooks/useAccountData'
import RoleBadge from '@/components/ui/RoleBadge'

interface OverviewTabProps {
  profile: UserProfile
  className?: string
}

/**
 * User Info Card Component
 */
const UserInfoCard: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center space-x-4">
        {/* Avatar */}
        <div className="relative">
          <div className="w-16 h-16 bg-gray-700 rounded-full border-2 border-gray-600 flex items-center justify-center overflow-hidden">
            {accountData.user.avatar ? (
              <img
                src={accountData.user.avatar}
                alt={accountData.user.displayName}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="text-xl font-bold text-gray-400">
                {accountData.user.displayName[0]?.toUpperCase() || 'U'}
              </div>
            )}
          </div>
          
          {/* Online Status */}
          <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-gray-800 rounded-full"></div>
        </div>

        {/* User Details */}
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h2 className="text-xl font-bold text-white">{accountData.user.displayName}</h2>
            <RoleBadge role={accountData.user.role} />
          </div>
          
          <p className="text-gray-400 mb-2">{accountData.user.email}</p>
          
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Calendar size={14} />
              <span>{accountData.stats.daysActive} days active</span>
            </div>
            
            {accountData.activity.lastLogin && (
              <div className="flex items-center space-x-1">
                <Clock size={14} />
                <span>Last seen {accountData.activity.lastLogin.toLocaleDateString()}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

/**
 * Points & Tier Summary Component
 */
const PointsTierCard: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${accountData.tier.color}`}>
            <Crown size={20} className="text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{accountData.tier.current} Member</h3>
            <p className="text-gray-400 text-sm">Level {accountData.tier.level}</p>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-2xl font-bold text-accent-400">{accountData.points.current.toLocaleString()}</div>
          <div className="text-gray-400 text-sm">points</div>
        </div>
      </div>

      {/* Tier Progress */}
      {accountData.points.nextTierThreshold && (
        <div>
          <div className="flex justify-between text-sm mb-2">
            <span className="text-gray-400">Progress to {accountData.tier.nextTier}</span>
            <span className="text-gray-400">{accountData.points.pointsToNextTier} points to go</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <motion.div
              className="h-2 rounded-full bg-gradient-to-r from-accent-500 to-accent-400"
              initial={{ width: 0 }}
              animate={{ width: `${accountData.tier.progress}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </div>
      )}

      {/* Key Benefits */}
      <div className="mt-4 pt-4 border-t border-gray-700">
        <div className="grid grid-cols-2 gap-4 text-sm">
          {accountData.tier.discount > 0 && (
            <div className="flex items-center space-x-2 text-green-400">
              <TrendingUp size={14} />
              <span>{accountData.tier.discount}% Discount</span>
            </div>
          )}
          
          {accountData.tier.freeShipping && (
            <div className="flex items-center space-x-2 text-blue-400">
              <Zap size={14} />
              <span>Free Shipping</span>
            </div>
          )}
          
          {accountData.tier.earlyAccessHours > 0 && (
            <div className="flex items-center space-x-2 text-purple-400">
              <Clock size={14} />
              <span>{accountData.tier.earlyAccessHours}h Early Access</span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

/**
 * Quick Stats Component
 */
const QuickStatsCard: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const stats = [
    {
      label: 'Orders',
      value: accountData.stats.orders,
      icon: Award,
      color: 'text-green-400'
    },
    {
      label: 'Achievements',
      value: accountData.stats.achievements,
      icon: Trophy,
      color: 'text-yellow-400'
    },
    {
      label: 'Activity Score',
      value: accountData.stats.activityScore,
      icon: Activity,
      color: 'text-blue-400'
    },
    {
      label: 'Profile Views',
      value: accountData.stats.profileViews,
      icon: Eye,
      color: 'text-purple-400'
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Target size={20} className="mr-2 text-accent-400" />
        Quick Stats
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 + index * 0.1 }}
            className="text-center"
          >
            <div className="flex items-center justify-center mb-2">
              <stat.icon size={20} className={stat.color} />
            </div>
            <div className={`text-xl font-bold ${stat.color}`}>
              {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
            </div>
            <div className="text-gray-400 text-sm">{stat.label}</div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

/**
 * Recent Activity Component
 */
const RecentActivityCard: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Activity size={20} className="mr-2 text-accent-400" />
        Recent Activity
      </h3>
      
      <div className="space-y-4">
        {accountData.activity.recentActions.slice(0, 3).map((action, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 + index * 0.1 }}
            className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg"
          >
            <div className="w-8 h-8 bg-accent-500/20 rounded-full flex items-center justify-center">
              <Activity size={16} className="text-accent-400" />
            </div>
            
            <div className="flex-1">
              <h4 className="text-white font-medium capitalize">{action.type}</h4>
              <p className="text-gray-400 text-sm">{action.description}</p>
            </div>
            
            <div className="text-right">
              {action.points && action.points > 0 && (
                <div className="text-accent-400 font-medium">+{action.points}</div>
              )}
              <div className="text-gray-500 text-xs">
                {action.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

/**
 * Main Overview Tab Component
 */
const OverviewTab: React.FC<OverviewTabProps> = ({ profile, className = '' }) => {
  const accountData = useAccountData(profile)

  if (!accountData) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading account overview...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* User Info */}
      <UserInfoCard accountData={accountData} />
      
      {/* Points & Tier */}
      <PointsTierCard accountData={accountData} />
      
      {/* Quick Stats */}
      <QuickStatsCard accountData={accountData} />
      
      {/* Recent Activity */}
      <RecentActivityCard accountData={accountData} />
    </div>
  )
}

export default OverviewTab
