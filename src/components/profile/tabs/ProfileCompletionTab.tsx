/**
 * Profile Completion Tab Component
 * 
 * Unified interface for profile completion combining ProfilePreview,
 * SmartRecommendations, and OnboardingWizard functionality.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import {
  Target,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Star,
  Zap,
  Trophy,
  Clock,
  User,
  Camera,
  Phone,
  MapPin,
  Link as LinkIcon,
  Shield,
  Settings
} from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { useAccountData, AccountData } from '@/hooks/useAccountData'

interface ProfileCompletionTabProps {
  profile: UserProfile
  className?: string
}

/**
 * Completion Progress Header
 */
const CompletionProgressHeader: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const { completion } = accountData
  
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-400'
    if (percentage >= 60) return 'text-blue-400'
    if (percentage >= 40) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getProgressMessage = (percentage: number) => {
    if (percentage >= 90) return 'Your profile is almost perfect!'
    if (percentage >= 70) return 'Great progress! Just a few more steps.'
    if (percentage >= 50) return 'You\'re halfway there! Keep going.'
    if (percentage >= 25) return 'Good start! Complete more sections for better visibility.'
    return 'Let\'s get your profile set up!'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
            <Target className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">Profile Completion</h2>
            <p className="text-gray-400">{getProgressMessage(completion.percentage)}</p>
          </div>
        </div>
        
        <div className="text-right">
          <div className={`text-3xl font-bold ${getProgressColor(completion.percentage)}`}>
            {completion.percentage}%
          </div>
          <div className="text-gray-400 text-sm">
            {completion.pointsEarned}/{completion.pointsPossible} points
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-3 mb-4">
        <motion.div
          className="h-3 rounded-full bg-gradient-to-r from-purple-500 to-accent-500"
          initial={{ width: 0 }}
          animate={{ width: `${completion.percentage}%` }}
          transition={{ duration: 1, delay: 0.3 }}
        />
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-4 text-center">
        <div>
          <div className="text-lg font-bold text-white">{completion.completedSections}</div>
          <div className="text-gray-400 text-sm">Completed</div>
        </div>
        <div>
          <div className="text-lg font-bold text-yellow-400">{completion.totalSections - completion.completedSections}</div>
          <div className="text-gray-400 text-sm">Remaining</div>
        </div>
        <div>
          <div className="text-lg font-bold text-green-400">+{completion.pointsEarned}</div>
          <div className="text-gray-400 text-sm">Points Earned</div>
        </div>
      </div>
    </motion.div>
  )
}

/**
 * Navigation handler for completion actions
 */
const useCompletionNavigation = () => {
  const router = useRouter()

  const navigateToCompletion = (actionId: string) => {
    switch (actionId) {
      case 'basic_info':
        // Navigate to profile edit with basic tab
        router.push('/profile/edit?tab=basic')
        break
      case 'contact_personal':
        // Navigate to profile edit with contact tab
        router.push('/profile/edit?tab=contact')
        break
      case 'avatar':
      case 'profile_picture':
        // Navigate to social profile for photo upload
        router.push('/profile/social')
        break
      case 'social_links':
        // Navigate to profile edit with social tab
        router.push('/profile/edit?tab=social')
        break
      case 'location':
        // Location is in contact tab
        router.push('/profile/edit?tab=contact')
        break
      case 'preferences':
        // Navigate to preferences page
        router.push('/profile/preferences')
        break
      case 'notifications':
        // Navigate to notifications page
        router.push('/profile/notifications')
        break
      case 'newsletter':
        // Navigate to preferences page with newsletter focus
        router.push('/profile/preferences?section=newsletter')
        break
      default:
        // Default to profile edit page
        router.push('/profile/edit')
    }
  }

  return { navigateToCompletion }
}

/**
 * Priority Actions Component
 */
const PriorityActions: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const { navigateToCompletion } = useCompletionNavigation()
  const highPriorityActions = accountData.completion.nextActions.filter(action => action.priority === 'high')

  if (highPriorityActions.length === 0) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <AlertCircle size={20} className="text-red-400" />
        <h3 className="text-lg font-semibold text-white">High Priority Actions</h3>
        <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">
          {highPriorityActions.length} remaining
        </span>
      </div>
      
      <div className="space-y-3">
        {highPriorityActions.map((action, index) => (
          <motion.div
            key={action.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 + index * 0.1 }}
            className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/20 rounded-lg hover:bg-red-500/20 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                <Star size={16} className="text-red-400" />
              </div>
              <div>
                <h4 className="text-white font-medium">{action.title}</h4>
                <p className="text-gray-400 text-sm">{action.description}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-green-400 text-xs">+{action.points} points</span>
                  <span className="text-gray-500">•</span>
                  <span className="text-red-400 text-xs">high priority</span>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => navigateToCompletion(action.id)}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <span>Complete</span>
              <ArrowRight size={16} />
            </button>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

/**
 * All Actions List Component
 */
const AllActionsList: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const { navigateToCompletion } = useCompletionNavigation()
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all')
  
  const filteredActions = accountData.completion.nextActions.filter(action => 
    filter === 'all' || action.priority === filter
  )

  const getActionIcon = (actionId: string) => {
    switch (actionId) {
      case 'basic-info': return User
      case 'profile-picture': return Camera
      case 'contact-info': return Phone
      case 'location': return MapPin
      case 'social-links': return LinkIcon
      case 'privacy-settings': return Shield
      case 'security-setup': return Settings
      default: return Target
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400 bg-red-500/20 border-red-500/20'
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/20'
      case 'low': return 'text-blue-400 bg-blue-500/20 border-blue-500/20'
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/20'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      {/* Header with Filters */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">All Completion Tasks</h3>
        
        <div className="flex space-x-2">
          {['all', 'high', 'medium', 'low'].map((filterOption) => (
            <button
              key={filterOption}
              onClick={() => setFilter(filterOption as any)}
              className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                filter === filterOption
                  ? 'bg-accent-500 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Actions List */}
      <div className="space-y-3">
        <AnimatePresence>
          {filteredActions.map((action, index) => {
            const Icon = getActionIcon(action.id)
            
            return (
              <motion.div
                key={action.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
                className={`flex items-center justify-between p-4 border rounded-lg hover:bg-gray-700/50 transition-colors ${getPriorityColor(action.priority)}`}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                    <Icon size={20} className="text-gray-300" />
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{action.title}</h4>
                    <p className="text-gray-400 text-sm">{action.description}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-green-400 text-xs">+{action.points} points</span>
                      <span className="text-gray-500">•</span>
                      <span className={`text-xs ${action.priority === 'high' ? 'text-red-400' : action.priority === 'medium' ? 'text-yellow-400' : 'text-blue-400'}`}>
                        {action.priority} priority
                      </span>
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => navigateToCompletion(action.id)}
                  className="px-4 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors flex items-center space-x-2"
                >
                  <span>Complete</span>
                  <ArrowRight size={16} />
                </button>
              </motion.div>
            )
          })}
        </AnimatePresence>
      </div>

      {filteredActions.length === 0 && (
        <div className="text-center py-8">
          <CheckCircle size={48} className="mx-auto text-green-400 mb-4" />
          <h4 className="text-lg font-semibold text-white mb-2">All {filter !== 'all' ? filter + ' priority ' : ''}tasks completed!</h4>
          <p className="text-gray-400">Great job on completing your profile setup.</p>
        </div>
      )}
    </motion.div>
  )
}

/**
 * Main Profile Completion Tab Component
 */
const ProfileCompletionTab: React.FC<ProfileCompletionTabProps> = ({ profile, className = '' }) => {
  const accountData = useAccountData(profile)

  if (!accountData) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading completion data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Header */}
      <CompletionProgressHeader accountData={accountData} />
      
      {/* Priority Actions */}
      <PriorityActions accountData={accountData} />
      
      {/* All Actions */}
      <AllActionsList accountData={accountData} />
    </div>
  )
}

export default ProfileCompletionTab
