/**
 * Settings & Security Tab Component
 * 
 * Consolidated view of privacy settings, security recommendations,
 * account settings, and role information.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Lock,
  Eye,
  EyeOff,
  Settings,
  Bell,
  Palette,
  Globe,
  Smartphone,
  Key,
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  User,
  Crown,
  Info
} from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { useAccountData, AccountData } from '@/hooks/useAccountData'
import RoleBadge from '@/components/ui/RoleBadge'

interface SettingsTabProps {
  profile: UserProfile
  className?: string
}

/**
 * Role & Permissions Component
 */
const RolePermissions: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const { user } = accountData

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <Crown size={20} className="text-accent-400" />
        <h3 className="text-lg font-semibold text-white">Role & Permissions</h3>
      </div>
      
      <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-accent-500/20 rounded-full flex items-center justify-center">
            <User size={20} className="text-accent-400" />
          </div>
          <div>
            <h4 className="text-white font-medium">{user.roleDisplayName}</h4>
            <p className="text-gray-400 text-sm">
              {user.isAdmin ? 'Administrative privileges enabled' : 'Standard user permissions'}
            </p>
          </div>
        </div>
        
        <RoleBadge role={user.role} />
      </div>
      
      {user.isAdmin && (
        <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={16} className="text-yellow-400" />
            <span className="text-yellow-400 text-sm font-medium">Administrative Access</span>
          </div>
          <p className="text-gray-400 text-sm mt-1">
            You have elevated permissions. Use responsibly and follow security best practices.
          </p>
        </div>
      )}
    </motion.div>
  )
}

/**
 * Security Status Component
 */
const SecurityStatus: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const securityItems = [
    {
      id: 'password',
      title: 'Strong Password',
      description: 'Password meets security requirements',
      status: 'good',
      action: 'Change Password'
    },
    {
      id: 'mfa',
      title: 'Two-Factor Authentication',
      description: 'Add an extra layer of security',
      status: 'warning',
      action: 'Enable MFA'
    },
    {
      id: 'sessions',
      title: 'Active Sessions',
      description: '2 active sessions detected',
      status: 'info',
      action: 'Manage Sessions'
    },
    {
      id: 'recovery',
      title: 'Account Recovery',
      description: 'Recovery email not verified',
      status: 'warning',
      action: 'Verify Email'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle size={16} className="text-green-400" />
      case 'warning': return <AlertTriangle size={16} className="text-yellow-400" />
      case 'error': return <AlertTriangle size={16} className="text-red-400" />
      default: return <Info size={16} className="text-blue-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'border-green-500/20 bg-green-500/10'
      case 'warning': return 'border-yellow-500/20 bg-yellow-500/10'
      case 'error': return 'border-red-500/20 bg-red-500/10'
      default: return 'border-blue-500/20 bg-blue-500/10'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <Shield size={20} className="text-accent-400" />
        <h3 className="text-lg font-semibold text-white">Security Status</h3>
      </div>
      
      <div className="space-y-4">
        {securityItems.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 + index * 0.1 }}
            className={`flex items-center justify-between p-4 border rounded-lg ${getStatusColor(item.status)}`}
          >
            <div className="flex items-center space-x-3">
              {getStatusIcon(item.status)}
              <div>
                <h4 className="text-white font-medium">{item.title}</h4>
                <p className="text-gray-400 text-sm">{item.description}</p>
              </div>
            </div>
            
            <button className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors flex items-center space-x-2">
              <span>{item.action}</span>
              <ArrowRight size={14} />
            </button>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

/**
 * Privacy Settings Component
 */
const PrivacySettings: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const [settings, setSettings] = useState({
    profileVisibility: 'public',
    showEmail: false,
    showActivity: true,
    allowMessages: true,
    dataSharing: false
  })

  const toggleSetting = (key: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }))
  }

  const privacyItems = [
    {
      id: 'profileVisibility',
      title: 'Profile Visibility',
      description: 'Who can see your profile',
      type: 'select',
      value: settings.profileVisibility,
      options: [
        { value: 'public', label: 'Public' },
        { value: 'friends', label: 'Friends Only' },
        { value: 'private', label: 'Private' }
      ]
    },
    {
      id: 'showEmail',
      title: 'Show Email Address',
      description: 'Display email on your public profile',
      type: 'toggle',
      value: settings.showEmail
    },
    {
      id: 'showActivity',
      title: 'Show Activity Status',
      description: 'Let others see when you\'re online',
      type: 'toggle',
      value: settings.showActivity
    },
    {
      id: 'allowMessages',
      title: 'Allow Direct Messages',
      description: 'Receive messages from other users',
      type: 'toggle',
      value: settings.allowMessages
    },
    {
      id: 'dataSharing',
      title: 'Analytics Data Sharing',
      description: 'Share usage data for product improvement',
      type: 'toggle',
      value: settings.dataSharing
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <Eye size={20} className="text-accent-400" />
        <h3 className="text-lg font-semibold text-white">Privacy Settings</h3>
      </div>
      
      <div className="space-y-4">
        {privacyItems.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 + index * 0.1 }}
            className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg"
          >
            <div>
              <h4 className="text-white font-medium">{item.title}</h4>
              <p className="text-gray-400 text-sm">{item.description}</p>
            </div>
            
            {item.type === 'toggle' ? (
              <button
                onClick={() => toggleSetting(item.id)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  item.value ? 'bg-accent-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    item.value ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            ) : (
              <select
                value={item.value}
                onChange={(e) => setSettings(prev => ({ ...prev, [item.id]: e.target.value }))}
                className="bg-gray-600 text-white px-3 py-1 rounded-lg border border-gray-500 focus:border-accent-500 focus:outline-none"
              >
                {item.options?.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            )}
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

/**
 * App Preferences Component
 */
const AppPreferences: React.FC<{ accountData: AccountData }> = ({ accountData }) => {
  const [preferences, setPreferences] = useState({
    theme: 'dark',
    language: 'en',
    notifications: true,
    emailUpdates: false,
    autoSave: true
  })

  const togglePreference = (key: string) => {
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }))
  }

  const preferenceItems = [
    {
      id: 'theme',
      title: 'Theme',
      description: 'Choose your preferred color scheme',
      type: 'select',
      icon: Palette,
      value: preferences.theme,
      options: [
        { value: 'dark', label: 'Dark' },
        { value: 'light', label: 'Light' },
        { value: 'auto', label: 'Auto' }
      ]
    },
    {
      id: 'language',
      title: 'Language',
      description: 'Select your preferred language',
      type: 'select',
      icon: Globe,
      value: preferences.language,
      options: [
        { value: 'en', label: 'English' },
        { value: 'es', label: 'Español' },
        { value: 'fr', label: 'Français' }
      ]
    },
    {
      id: 'notifications',
      title: 'Push Notifications',
      description: 'Receive notifications on this device',
      type: 'toggle',
      icon: Bell,
      value: preferences.notifications
    },
    {
      id: 'emailUpdates',
      title: 'Email Updates',
      description: 'Receive product updates via email',
      type: 'toggle',
      icon: Bell,
      value: preferences.emailUpdates
    },
    {
      id: 'autoSave',
      title: 'Auto-save Changes',
      description: 'Automatically save form changes',
      type: 'toggle',
      icon: Settings,
      value: preferences.autoSave
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="bg-gray-800 rounded-lg border border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <Settings size={20} className="text-accent-400" />
        <h3 className="text-lg font-semibold text-white">App Preferences</h3>
      </div>
      
      <div className="space-y-4">
        {preferenceItems.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 + index * 0.1 }}
            className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg"
          >
            <div className="flex items-center space-x-3">
              <item.icon size={20} className="text-gray-400" />
              <div>
                <h4 className="text-white font-medium">{item.title}</h4>
                <p className="text-gray-400 text-sm">{item.description}</p>
              </div>
            </div>
            
            {item.type === 'toggle' ? (
              <button
                onClick={() => togglePreference(item.id)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  item.value ? 'bg-accent-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    item.value ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            ) : (
              <select
                value={item.value}
                onChange={(e) => setPreferences(prev => ({ ...prev, [item.id]: e.target.value }))}
                className="bg-gray-600 text-white px-3 py-1 rounded-lg border border-gray-500 focus:border-accent-500 focus:outline-none"
              >
                {item.options?.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            )}
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

/**
 * Main Settings Tab Component
 */
const SettingsTab: React.FC<SettingsTabProps> = ({ profile, className = '' }) => {
  const accountData = useAccountData(profile)

  if (!accountData) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Role & Permissions */}
      <RolePermissions accountData={accountData} />
      
      {/* Security Status */}
      <SecurityStatus accountData={accountData} />
      
      {/* Privacy Settings */}
      <PrivacySettings accountData={accountData} />
      
      {/* App Preferences */}
      <AppPreferences accountData={accountData} />
    </div>
  )
}

export default SettingsTab
