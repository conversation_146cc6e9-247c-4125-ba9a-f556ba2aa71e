/**
 * Lazy Loading Components for Profile System
 * 
 * Dynamic imports for heavy profile components to improve initial bundle size
 * and loading performance. Components are loaded on-demand when needed.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { Suspense, lazy } from 'react'
import { Loader2 } from 'lucide-react'

// Loading fallback component
const LoadingFallback = ({ message = 'Loading...' }: { message?: string }) => (
  <div className="flex items-center justify-center p-8">
    <div className="flex items-center space-x-2 text-gray-400">
      <Loader2 className="w-5 h-5 animate-spin" />
      <span>{message}</span>
    </div>
  </div>
)

// Lazy loaded components
const LazyEnhancedProfileEditor = lazy(() => import('../EnhancedProfileEditor'))
const LazyRecommendationsDashboard = lazy(() => import('../recommendations/RecommendationsDashboard'))
const LazyAdvancedSearchDashboard = lazy(() => import('../search/AdvancedSearchDashboard'))
const LazyOnboardingWizard = lazy(() => import('../onboarding/OnboardingWizard'))
const LazyMobileOptimizations = lazy(() => import('../layout/MobileOptimizations'))

// Wrapped components with Suspense
export const EnhancedProfileEditor = (props: any) => (
  <Suspense fallback={<LoadingFallback message="Loading profile editor..." />}>
    <LazyEnhancedProfileEditor {...props} />
  </Suspense>
)

export const RecommendationsDashboard = (props: any) => (
  <Suspense fallback={<LoadingFallback message="Loading recommendations..." />}>
    <LazyRecommendationsDashboard {...props} />
  </Suspense>
)

export const AdvancedSearchDashboard = (props: any) => (
  <Suspense fallback={<LoadingFallback message="Loading search..." />}>
    <LazyAdvancedSearchDashboard {...props} />
  </Suspense>
)

export const OnboardingWizard = (props: any) => (
  <Suspense fallback={<LoadingFallback message="Loading onboarding..." />}>
    <LazyOnboardingWizard {...props} />
  </Suspense>
)

export const MobileOptimizations = (props: any) => (
  <Suspense fallback={<LoadingFallback message="Loading mobile features..." />}>
    <LazyMobileOptimizations {...props} />
  </Suspense>
)

// Preload functions for better UX
export const preloadProfileEditor = () => {
  import('../EnhancedProfileEditor')
}

export const preloadRecommendations = () => {
  import('../recommendations/RecommendationsDashboard')
}

export const preloadSearch = () => {
  import('../search/AdvancedSearchDashboard')
}

export const preloadOnboarding = () => {
  import('../onboarding/OnboardingWizard')
}

export const preloadMobileOptimizations = () => {
  import('../layout/MobileOptimizations')
}

// Preload all heavy components (use sparingly)
export const preloadAllProfileComponents = () => {
  preloadProfileEditor()
  preloadRecommendations()
  preloadSearch()
  preloadOnboarding()
  preloadMobileOptimizations()
}

/**
 * Hook for preloading components based on user interaction
 */
export const useProfileComponentPreloader = () => {
  const preloadOnHover = {
    profileEditor: preloadProfileEditor,
    recommendations: preloadRecommendations,
    search: preloadSearch,
    onboarding: preloadOnboarding,
    mobile: preloadMobileOptimizations
  }

  const preloadOnFocus = {
    profileEditor: preloadProfileEditor,
    recommendations: preloadRecommendations,
    search: preloadSearch
  }

  return {
    preloadOnHover,
    preloadOnFocus,
    preloadAll: preloadAllProfileComponents
  }
}
