import React from 'react'

/**
 * Profile System Performance Monitor
 * 
 * Tracks performance metrics for profile components and provides
 * insights for optimization opportunities.
 * 
 * <AUTHOR> Team
 */

interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  metadata?: Record<string, any>
}

interface ComponentMetrics {
  renderTime: number
  mountTime: number
  updateCount: number
  lastUpdate: number
  memoryUsage?: number
}

class ProfilePerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map()
  private componentMetrics: Map<string, ComponentMetrics> = new Map()
  private isEnabled: boolean = process.env.NODE_ENV === 'development'

  /**
   * Start timing a performance metric
   */
  startTiming(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return

    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata
    })
  }

  /**
   * End timing a performance metric
   */
  endTiming(name: string): number | null {
    if (!this.isEnabled) return null

    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance metric "${name}" was not started`)
      return null
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration

    // Log slow operations
    if (duration > 100) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`)
    }

    return duration
  }

  /**
   * Track component render performance
   */
  trackComponentRender(componentName: string, renderTime: number): void {
    if (!this.isEnabled) return

    const existing = this.componentMetrics.get(componentName)
    if (existing) {
      existing.renderTime = renderTime
      existing.updateCount += 1
      existing.lastUpdate = Date.now()
    } else {
      this.componentMetrics.set(componentName, {
        renderTime,
        mountTime: Date.now(),
        updateCount: 1,
        lastUpdate: Date.now()
      })
    }
  }

  /**
   * Track component mount time
   */
  trackComponentMount(componentName: string): void {
    if (!this.isEnabled) return

    const existing = this.componentMetrics.get(componentName)
    if (existing) {
      existing.mountTime = Date.now()
    } else {
      this.componentMetrics.set(componentName, {
        renderTime: 0,
        mountTime: Date.now(),
        updateCount: 0,
        lastUpdate: Date.now()
      })
    }
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    metrics: PerformanceMetric[]
    components: Array<{ name: string; metrics: ComponentMetrics }>
    slowOperations: PerformanceMetric[]
  } {
    const metrics = Array.from(this.metrics.values())
    const components = Array.from(this.componentMetrics.entries()).map(([name, metrics]) => ({
      name,
      metrics
    }))
    const slowOperations = metrics.filter(m => m.duration && m.duration > 100)

    return {
      metrics,
      components,
      slowOperations
    }
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.clear()
    this.componentMetrics.clear()
  }

  /**
   * Log performance summary to console
   */
  logSummary(): void {
    if (!this.isEnabled) return

    const summary = this.getPerformanceSummary()
    
    console.group('🚀 Profile Performance Summary')
    
    if (summary.slowOperations.length > 0) {
      console.warn('⚠️ Slow Operations:')
      summary.slowOperations.forEach(op => {
        console.warn(`  ${op.name}: ${op.duration?.toFixed(2)}ms`)
      })
    }

    console.log('📊 Component Metrics:')
    summary.components.forEach(({ name, metrics }) => {
      console.log(`  ${name}:`, {
        renderTime: `${metrics.renderTime.toFixed(2)}ms`,
        updates: metrics.updateCount,
        mountTime: new Date(metrics.mountTime).toLocaleTimeString()
      })
    })

    console.groupEnd()
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * Check if monitoring is enabled
   */
  isMonitoringEnabled(): boolean {
    return this.isEnabled
  }
}

// Singleton instance
export const profilePerformanceMonitor = new ProfilePerformanceMonitor()

/**
 * React hook for component performance tracking
 */
export const useProfilePerformanceTracking = (componentName: string) => {
  const startRender = () => {
    profilePerformanceMonitor.startTiming(`${componentName}-render`)
  }

  const endRender = () => {
    const duration = profilePerformanceMonitor.endTiming(`${componentName}-render`)
    if (duration !== null) {
      profilePerformanceMonitor.trackComponentRender(componentName, duration)
    }
  }

  const trackMount = () => {
    profilePerformanceMonitor.trackComponentMount(componentName)
  }

  return {
    startRender,
    endRender,
    trackMount,
    startTiming: (name: string) => profilePerformanceMonitor.startTiming(`${componentName}-${name}`),
    endTiming: (name: string) => profilePerformanceMonitor.endTiming(`${componentName}-${name}`)
  }
}

/**
 * Higher-order component for automatic performance tracking
 */
export const withPerformanceTracking = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  return React.memo((props: P) => {
    const { startRender, endRender, trackMount } = useProfilePerformanceTracking(componentName)

    React.useEffect(() => {
      trackMount()
    }, [trackMount])

    React.useLayoutEffect(() => {
      startRender()
      return () => {
        endRender()
      }
    })

    return <WrappedComponent {...props} />
  })
}

// Development utilities
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // @ts-ignore
  (window as any).profilePerformanceMonitor = profilePerformanceMonitor
}
}