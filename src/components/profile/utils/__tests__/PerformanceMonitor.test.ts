/**
 * PerformanceMonitor Tests
 * 
 * Test suite for the profile performance monitoring system.
 * 
 * <AUTHOR> Team
 */

import { jest } from '@jest/globals'
import { profilePerformanceMonitor, useProfilePerformanceTracking } from '../PerformanceMonitor'
import { renderHook, act } from '@testing-library/react'

// Mock performance.now()
const mockPerformanceNow = jest.fn()
Object.defineProperty(global, 'performance', {
  value: {
    now: mockPerformanceNow
  },
  writable: true
})

// Mock console methods
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation()
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation()
const mockConsoleGroup = jest.spyOn(console, 'group').mockImplementation()
const mockConsoleGroupEnd = jest.spyOn(console, 'groupEnd').mockImplementation()

describe('ProfilePerformanceMonitor', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    profilePerformanceMonitor.clearMetrics()
    profilePerformanceMonitor.setEnabled(true)
    mockPerformanceNow.mockReturnValue(1000)
  })

  afterEach(() => {
    profilePerformanceMonitor.setEnabled(false)
  })

  describe('Basic Timing', () => {
    it('starts and ends timing correctly', () => {
      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1100)
      
      profilePerformanceMonitor.startTiming('test-operation')
      const duration = profilePerformanceMonitor.endTiming('test-operation')
      
      expect(duration).toBe(100)
    })

    it('returns null for non-existent timing', () => {
      const duration = profilePerformanceMonitor.endTiming('non-existent')
      
      expect(duration).toBeNull()
      expect(mockConsoleWarn).toHaveBeenCalledWith(
        'Performance metric "non-existent" was not started'
      )
    })

    it('warns about slow operations', () => {
      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1200) // 200ms
      
      profilePerformanceMonitor.startTiming('slow-operation')
      profilePerformanceMonitor.endTiming('slow-operation')
      
      expect(mockConsoleWarn).toHaveBeenCalledWith(
        'Slow operation detected: slow-operation took 200.00ms'
      )
    })

    it('stores metadata with timing', () => {
      const metadata = { component: 'TestComponent', props: { id: 1 } }
      
      profilePerformanceMonitor.startTiming('test-with-metadata', metadata)
      profilePerformanceMonitor.endTiming('test-with-metadata')
      
      const summary = profilePerformanceMonitor.getPerformanceSummary()
      const metric = summary.metrics.find(m => m.name === 'test-with-metadata')
      
      expect(metric?.metadata).toEqual(metadata)
    })
  })

  describe('Component Tracking', () => {
    it('tracks component render time', () => {
      profilePerformanceMonitor.trackComponentRender('TestComponent', 50)
      
      const summary = profilePerformanceMonitor.getPerformanceSummary()
      const component = summary.components.find(c => c.name === 'TestComponent')
      
      expect(component?.metrics.renderTime).toBe(50)
      expect(component?.metrics.updateCount).toBe(1)
    })

    it('updates existing component metrics', () => {
      profilePerformanceMonitor.trackComponentRender('TestComponent', 50)
      profilePerformanceMonitor.trackComponentRender('TestComponent', 75)
      
      const summary = profilePerformanceMonitor.getPerformanceSummary()
      const component = summary.components.find(c => c.name === 'TestComponent')
      
      expect(component?.metrics.renderTime).toBe(75)
      expect(component?.metrics.updateCount).toBe(2)
    })

    it('tracks component mount time', () => {
      const mockDate = 1640995200000 // 2022-01-01
      jest.spyOn(Date, 'now').mockReturnValue(mockDate)
      
      profilePerformanceMonitor.trackComponentMount('TestComponent')
      
      const summary = profilePerformanceMonitor.getPerformanceSummary()
      const component = summary.components.find(c => c.name === 'TestComponent')
      
      expect(component?.metrics.mountTime).toBe(mockDate)
    })
  })

  describe('Performance Summary', () => {
    it('returns comprehensive summary', () => {
      // Add some metrics
      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1050)
      profilePerformanceMonitor.startTiming('fast-operation')
      profilePerformanceMonitor.endTiming('fast-operation')
      
      mockPerformanceNow.mockReturnValueOnce(2000).mockReturnValueOnce(2150)
      profilePerformanceMonitor.startTiming('slow-operation')
      profilePerformanceMonitor.endTiming('slow-operation')
      
      profilePerformanceMonitor.trackComponentRender('TestComponent', 25)
      
      const summary = profilePerformanceMonitor.getPerformanceSummary()
      
      expect(summary.metrics).toHaveLength(2)
      expect(summary.components).toHaveLength(1)
      expect(summary.slowOperations).toHaveLength(1) // Only slow-operation (150ms > 100ms)
    })

    it('identifies slow operations correctly', () => {
      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1200) // 200ms
      profilePerformanceMonitor.startTiming('slow-operation')
      profilePerformanceMonitor.endTiming('slow-operation')
      
      mockPerformanceNow.mockReturnValueOnce(2000).mockReturnValueOnce(2050) // 50ms
      profilePerformanceMonitor.startTiming('fast-operation')
      profilePerformanceMonitor.endTiming('fast-operation')
      
      const summary = profilePerformanceMonitor.getPerformanceSummary()
      
      expect(summary.slowOperations).toHaveLength(1)
      expect(summary.slowOperations[0].name).toBe('slow-operation')
    })
  })

  describe('Logging', () => {
    it('logs performance summary', () => {
      profilePerformanceMonitor.trackComponentRender('TestComponent', 25)
      
      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1200)
      profilePerformanceMonitor.startTiming('slow-operation')
      profilePerformanceMonitor.endTiming('slow-operation')
      
      profilePerformanceMonitor.logSummary()
      
      expect(mockConsoleGroup).toHaveBeenCalledWith('🚀 Profile Performance Summary')
      expect(mockConsoleWarn).toHaveBeenCalledWith('⚠️ Slow Operations:')
      expect(mockConsoleLog).toHaveBeenCalledWith('📊 Component Metrics:')
      expect(mockConsoleGroupEnd).toHaveBeenCalled()
    })

    it('does not log when disabled', () => {
      profilePerformanceMonitor.setEnabled(false)
      profilePerformanceMonitor.logSummary()
      
      expect(mockConsoleGroup).not.toHaveBeenCalled()
    })
  })

  describe('Enable/Disable', () => {
    it('can be enabled and disabled', () => {
      expect(profilePerformanceMonitor.isMonitoringEnabled()).toBe(true)
      
      profilePerformanceMonitor.setEnabled(false)
      expect(profilePerformanceMonitor.isMonitoringEnabled()).toBe(false)
      
      profilePerformanceMonitor.setEnabled(true)
      expect(profilePerformanceMonitor.isMonitoringEnabled()).toBe(true)
    })

    it('does not track when disabled', () => {
      profilePerformanceMonitor.setEnabled(false)
      
      profilePerformanceMonitor.startTiming('test-operation')
      const duration = profilePerformanceMonitor.endTiming('test-operation')
      
      expect(duration).toBeNull()
    })
  })

  describe('Clear Metrics', () => {
    it('clears all metrics', () => {
      profilePerformanceMonitor.startTiming('test-operation')
      profilePerformanceMonitor.trackComponentRender('TestComponent', 25)
      
      let summary = profilePerformanceMonitor.getPerformanceSummary()
      expect(summary.metrics).toHaveLength(1)
      expect(summary.components).toHaveLength(1)
      
      profilePerformanceMonitor.clearMetrics()
      
      summary = profilePerformanceMonitor.getPerformanceSummary()
      expect(summary.metrics).toHaveLength(0)
      expect(summary.components).toHaveLength(0)
    })
  })
})

describe('useProfilePerformanceTracking', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    profilePerformanceMonitor.clearMetrics()
    profilePerformanceMonitor.setEnabled(true)
  })

  it('provides tracking functions', () => {
    const { result } = renderHook(() => 
      useProfilePerformanceTracking('TestComponent')
    )
    
    expect(result.current.startRender).toBeInstanceOf(Function)
    expect(result.current.endRender).toBeInstanceOf(Function)
    expect(result.current.trackMount).toBeInstanceOf(Function)
    expect(result.current.startTiming).toBeInstanceOf(Function)
    expect(result.current.endTiming).toBeInstanceOf(Function)
  })

  it('tracks render performance', () => {
    mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1050)
    
    const { result } = renderHook(() => 
      useProfilePerformanceTracking('TestComponent')
    )
    
    act(() => {
      result.current.startRender()
    })
    
    act(() => {
      result.current.endRender()
    })
    
    const summary = profilePerformanceMonitor.getPerformanceSummary()
    const component = summary.components.find(c => c.name === 'TestComponent')
    
    expect(component?.metrics.renderTime).toBe(50)
  })

  it('tracks mount', () => {
    const mockDate = 1640995200000
    jest.spyOn(Date, 'now').mockReturnValue(mockDate)
    
    const { result } = renderHook(() => 
      useProfilePerformanceTracking('TestComponent')
    )
    
    act(() => {
      result.current.trackMount()
    })
    
    const summary = profilePerformanceMonitor.getPerformanceSummary()
    const component = summary.components.find(c => c.name === 'TestComponent')
    
    expect(component?.metrics.mountTime).toBe(mockDate)
  })

  it('provides scoped timing functions', () => {
    mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1100)
    
    const { result } = renderHook(() => 
      useProfilePerformanceTracking('TestComponent')
    )
    
    act(() => {
      result.current.startTiming('custom-operation')
    })
    
    const duration = act(() => {
      return result.current.endTiming('custom-operation')
    })
    
    expect(duration).toBe(100)
    
    const summary = profilePerformanceMonitor.getPerformanceSummary()
    const metric = summary.metrics.find(m => m.name === 'TestComponent-custom-operation')
    
    expect(metric).toBeDefined()
    expect(metric?.duration).toBe(100)
  })
})
