/**
 * PWA Components
 * 
 * UI components for Progressive Web App functionality including
 * install prompts, offline indicators, and cache management.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useInstallPrompt, usePWA, useOffline, useCache } from '../../hooks/usePWA'
import { useNotifications } from '../../lib/notifications/NotificationSystem'

// ===== INSTALL PROMPT COMPONENT =====

interface InstallPromptProps {
  showDelay?: number
  position?: 'top' | 'bottom'
  variant?: 'banner' | 'modal' | 'card'
  onInstall?: () => void
  onDismiss?: () => void
}

function InstallPrompt({
  showDelay = 5000,
  position = 'bottom',
  variant = 'banner',
  onInstall,
  onDismiss
}: InstallPromptProps) {
  const { isInstallable, isInstalled, install, dismiss } = useInstallPrompt()
  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)
  const { addNotification } = useNotifications()

  // Show prompt after delay
  React.useEffect(() => {
    if (isInstallable && !isDismissed) {
      const timer = setTimeout(() => {
        setIsVisible(true)
      }, showDelay)

      return () => clearTimeout(timer)
    }
  }, [isInstallable, isDismissed, showDelay])

  // Hide if already installed
  React.useEffect(() => {
    if (isInstalled) {
      setIsVisible(false)
    }
  }, [isInstalled])

  const handleInstall = async () => {
    try {
      const result = await install()
      
      if (result === 'accepted') {
        addNotification('App Installed!', 'Syndicaps has been added to your device', 'success')
        onInstall?.()
      }
      
      setIsVisible(false)
    } catch (error) {
      addNotification('Installation Failed', 'Unable to install the app. Please try again.', 'error')
      console.error('Install failed:', error)
    }
  }

  const handleDismiss = () => {
    dismiss()
    setIsDismissed(true)
    setIsVisible(false)
    onDismiss?.()
  }

  if (!isInstallable || !isVisible || isInstalled) {
    return null
  }

  if (variant === 'modal') {
    return (
      <AnimatePresence>
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6"
          >
            <div className="text-center">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Install Syndicaps
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Get the full app experience with offline support, faster loading, and quick access from your home screen.
              </p>
              
              <div className="flex space-x-3">
                <button
                  onClick={handleInstall}
                  className="flex-1 bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Install App
                </button>
                <button
                  onClick={handleDismiss}
                  className="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Not Now
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </AnimatePresence>
    )
  }

  if (variant === 'card') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 m-4"
      >
        <div className="flex items-start space-x-3">
          <div className="text-2xl">📱</div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 dark:text-white">
              Install Syndicaps App
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Get the app for a better experience with offline support
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleInstall}
              className="bg-accent-600 hover:bg-accent-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
            >
              Install
            </button>
            <button
              onClick={handleDismiss}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-sm"
            >
              ×
            </button>
          </div>
        </div>
      </motion.div>
    )
  }

  // Banner variant (default)
  const positionClasses = position === 'top' 
    ? 'top-0' 
    : 'bottom-0'

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: position === 'top' ? -100 : 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: position === 'top' ? -100 : 100 }}
        className={`fixed left-0 right-0 ${positionClasses} z-50 bg-accent-600 text-white`}
      >
        <div className="max-w-7xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-xl">📱</span>
              <div>
                <p className="font-medium">Install Syndicaps App</p>
                <p className="text-xs opacity-90">Get offline access and faster loading</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handleInstall}
                className="bg-white text-accent-600 px-3 py-1 rounded font-medium text-sm hover:bg-gray-100 transition-colors"
              >
                Install
              </button>
              <button
                onClick={handleDismiss}
                className="text-white hover:text-gray-200 text-xl leading-none"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

// ===== OFFLINE INDICATOR =====

export function OfflineIndicator() {
  const { isOnline, queueStatus } = useOffline()

  if (isOnline) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className="fixed top-0 left-0 right-0 z-40 bg-yellow-500 text-white"
    >
      <div className="max-w-7xl mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            <span className="text-sm font-medium">
              You're offline
            </span>
            {queueStatus.pending > 0 && (
              <span className="text-xs opacity-90">
                ({queueStatus.pending} items queued for sync)
              </span>
            )}
          </div>
          
          <div className="text-xs opacity-90">
            Changes will sync when you're back online
          </div>
        </div>
      </div>
    </motion.div>
  )
}

// ===== UPDATE AVAILABLE NOTIFICATION =====

export function UpdateNotification() {
  const { status, updateApp } = usePWA()
  const [isUpdating, setIsUpdating] = useState(false)

  const handleUpdate = async () => {
    setIsUpdating(true)
    try {
      await updateApp()
    } catch (error) {
      console.error('Update failed:', error)
      setIsUpdating(false)
    }
  }

  if (!status.hasUpdate) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed top-0 left-0 right-0 z-40 bg-blue-600 text-white"
    >
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-xl">🔄</span>
            <div>
              <p className="font-medium">App Update Available</p>
              <p className="text-xs opacity-90">A new version is ready to install</p>
            </div>
          </div>
          
          <button
            onClick={handleUpdate}
            disabled={isUpdating}
            className="bg-white text-blue-600 px-4 py-2 rounded font-medium text-sm hover:bg-gray-100 transition-colors disabled:opacity-50"
          >
            {isUpdating ? 'Updating...' : 'Update Now'}
          </button>
        </div>
      </div>
    </motion.div>
  )
}

// ===== CACHE MANAGEMENT PANEL =====

interface CacheManagementProps {
  isOpen: boolean
  onClose: () => void
}

export function CacheManagement({ isOpen, onClose }: CacheManagementProps) {
  const { cacheStatus, isLoading, getCacheStatus, clearCache, clearAllCaches } = useCache()
  const [isClearing, setIsClearing] = useState<string | null>(null)
  const { success: notifySuccess, error: notifyError } = useNotifications()

  const handleClearCache = async (cacheName: string) => {
    setIsClearing(cacheName)
    try {
      await clearCache(cacheName)
      addNotification('Cache Cleared', `${cacheName} cache has been cleared`, 'success')
    } catch (error) {
      addNotification('Clear Failed', `Failed to clear ${cacheName} cache`, 'error')
    } finally {
      setIsClearing(null)
    }
  }

  const handleClearAll = async () => {
    setIsClearing('all')
    try {
      await clearAllCaches()
      addNotification('All Caches Cleared', 'All cached data has been removed', 'success')
    } catch (error) {
      addNotification('Clear Failed', 'Failed to clear caches', 'error')
    } finally {
      setIsClearing(null)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full max-h-96 overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Cache Management
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto"></div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Loading cache status...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Cache status */}
              {Object.entries(cacheStatus).map(([name, stats]) => (
                <div key={name} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white capitalize">
                      {name} Cache
                    </h4>
                    <button
                      onClick={() => handleClearCache(name)}
                      disabled={isClearing === name}
                      className="text-sm text-red-600 hover:text-red-700 disabled:opacity-50"
                    >
                      {isClearing === name ? 'Clearing...' : 'Clear'}
                    </button>
                  </div>
                  
                  <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <div className="flex justify-between">
                      <span>Size:</span>
                      <span>{stats.size} / {stats.maxSize} items</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Usage:</span>
                      <span>{stats.usage.toFixed(1)}%</span>
                    </div>
                  </div>
                  
                  {/* Progress bar */}
                  <div className="mt-2 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-accent-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(stats.usage, 100)}%` }}
                    />
                  </div>
                </div>
              ))}

              {/* Clear all button */}
              <button
                onClick={handleClearAll}
                disabled={isClearing === 'all'}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-medium disabled:opacity-50 transition-colors"
              >
                {isClearing === 'all' ? 'Clearing All...' : 'Clear All Caches'}
              </button>

              {/* Refresh button */}
              <button
                onClick={getCacheStatus}
                className="w-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 py-2 px-4 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Refresh Status
              </button>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

// ===== SYNC STATUS INDICATOR =====

export function SyncStatusIndicator() {
  const { queueStatus, isOnline } = useOffline()

  if (queueStatus.pending === 0 && queueStatus.failed === 0) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 100 }}
      className="fixed bottom-4 right-4 z-30 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3"
    >
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          queueStatus.processing ? 'bg-blue-500 animate-pulse' :
          queueStatus.failed > 0 ? 'bg-red-500' :
          'bg-yellow-500'
        }`} />
        
        <div className="text-sm">
          <div className="font-medium text-gray-900 dark:text-white">
            {queueStatus.processing ? 'Syncing...' : 
             queueStatus.failed > 0 ? 'Sync Issues' :
             'Queued for Sync'}
          </div>
          
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {queueStatus.pending} pending
            {queueStatus.failed > 0 && `, ${queueStatus.failed} failed`}
          </div>
        </div>
        
        {!isOnline && (
          <div className="text-xs text-yellow-600 dark:text-yellow-400">
            Offline
          </div>
        )}
      </div>
    </motion.div>
  )
}

export {
  InstallPrompt,
  OfflineIndicator,
  UpdateNotification,
  CacheManagement,
  SyncStatusIndicator
}