/**
 * Raffle Form Hook
 * 
 * Manages form state and validation for the raffle entry process
 * 
 * <AUTHOR> Team
 */

import { useState, useCallback } from 'react'
import { 
  RaffleFormState, 
  ValidationResult, 
  UseRaffleFormReturn,
  INITIAL_FORM_STATE,
  FormStep,
  STEP_ORDER
} from '../types/raffleTypes'

/**
 * Custom hook for managing raffle form state and validation
 */
export const useRaffleForm = (): UseRaffleFormReturn => {
  const [formState, setFormState] = useState<RaffleFormState>(INITIAL_FORM_STATE)

  /**
   * Update form state with partial updates
   */
  const updateFormState = useCallback((updates: Partial<RaffleFormState>) => {
    setFormState(prev => ({
      ...prev,
      ...updates,
      // Clear field errors when updating form state
      fieldErrors: updates.fieldErrors || {}
    }))
  }, [])

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    setFormState(INITIAL_FORM_STATE)
  }, [])

  /**
   * Validate current step
   */
  const validateCurrentStep = useCallback((): ValidationResult => {
    const errors: Record<string, string> = {}

    switch (formState.currentStep) {
      case 'requirements':
        // Check if at least one requirement is met
        const hasRequiredRequirements = Object.values(formState.raffleRequirements).some(Boolean)
        if (!hasRequiredRequirements) {
          errors.requirements = 'Please complete at least one requirement to continue'
        }
        break

      case 'products':
        if (formState.selectedVariants.length === 0) {
          errors.variants = 'Please select at least one product variant'
        }
        break

      case 'address':
        const { newAddress } = formState
        if (!newAddress.name.trim()) {
          errors.name = 'Name is required'
        }
        if (!newAddress.address.trim()) {
          errors.address = 'Address is required'
        }
        if (!newAddress.city.trim()) {
          errors.city = 'City is required'
        }
        if (!newAddress.state.trim()) {
          errors.state = 'State is required'
        }
        if (!newAddress.zipCode.trim()) {
          errors.zipCode = 'ZIP code is required'
        }
        if (!newAddress.phone.trim()) {
          errors.phone = 'Phone number is required'
        }
        // Basic phone validation
        if (newAddress.phone && !/^\+?[\d\s\-\(\)]+$/.test(newAddress.phone)) {
          errors.phone = 'Please enter a valid phone number'
        }
        break

      case 'information':
        if (!formState.selectedAddress) {
          errors.address = 'Please select a shipping address'
        }
        if (!formState.shippingMethod) {
          errors.shipping = 'Please select a shipping method'
        }
        break

      case 'review':
        // Final validation - ensure all required data is present
        if (formState.selectedVariants.length === 0) {
          errors.variants = 'No products selected'
        }
        if (!formState.selectedAddress && !formState.newAddress.name) {
          errors.address = 'No shipping address provided'
        }
        if (!formState.shippingMethod) {
          errors.shipping = 'No shipping method selected'
        }
        break

      case 'success':
        // No validation needed for success step
        break

      default:
        break
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  }, [formState])

  return {
    formState,
    updateFormState,
    resetForm,
    validateCurrentStep
  }
}

/**
 * Hook for step navigation logic
 */
export const useStepNavigation = (
  currentStep: FormStep,
  updateFormState: (updates: Partial<RaffleFormState>) => void,
  validateCurrentStep: () => ValidationResult,
  hasAddresses: boolean = false
) => {
  /**
   * Navigate to next step with validation
   */
  const nextStep = useCallback(() => {
    updateFormState({ isValidating: true })
    const validation = validateCurrentStep()

    if (!validation.isValid) {
      updateFormState({ 
        fieldErrors: validation.errors,
        isValidating: false 
      })
      return false
    }

    // Clear errors and proceed to next step
    updateFormState({ fieldErrors: {}, isValidating: false })

    let nextStepName: FormStep
    switch (currentStep) {
      case 'requirements':
        nextStepName = 'products'
        break
      case 'products':
        // Skip address step if user has existing addresses
        nextStepName = hasAddresses ? 'information' : 'address'
        break
      case 'address':
        nextStepName = 'information'
        break
      case 'information':
        nextStepName = 'review'
        break
      case 'review':
        nextStepName = 'success'
        break
      default:
        return false
    }

    updateFormState({ currentStep: nextStepName })
    return true
  }, [currentStep, updateFormState, validateCurrentStep, hasAddresses])

  /**
   * Navigate to previous step
   */
  const prevStep = useCallback(() => {
    let prevStepName: FormStep
    switch (currentStep) {
      case 'products':
        prevStepName = 'requirements'
        break
      case 'address':
        prevStepName = 'products'
        break
      case 'information':
        // Go back to address step if user has no existing addresses, otherwise products
        prevStepName = hasAddresses ? 'products' : 'address'
        break
      case 'review':
        prevStepName = hasAddresses ? 'information' : 'address'
        break
      default:
        return false
    }

    updateFormState({ currentStep: prevStepName })
    return true
  }, [currentStep, updateFormState, hasAddresses])

  /**
   * Get step progress information
   */
  const getStepProgress = useCallback(() => {
    const currentIndex = STEP_ORDER.indexOf(currentStep)
    const totalSteps = hasAddresses ? 5 : 6 // Skip address step if user has addresses
    const adjustedIndex = currentStep === 'information' && hasAddresses ? 2 : currentIndex
    
    return {
      current: adjustedIndex + 1,
      total: totalSteps,
      percentage: ((adjustedIndex + 1) / totalSteps) * 100
    }
  }, [currentStep, hasAddresses])

  return {
    nextStep,
    prevStep,
    getStepProgress
  }
}
