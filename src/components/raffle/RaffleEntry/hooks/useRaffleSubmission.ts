/**
 * Raffle Submission Hook
 * 
 * Handles raffle entry submission logic and API calls
 * 
 * <AUTHOR> Team
 */

import { useState, useCallback } from 'react'
import { toast } from 'react-hot-toast'
import { 
  createRaffleEntry,
  createShippingAddress,
  getUserShippingAddresses
} from '@/lib/firestore'
import { 
  RaffleEntrySubmission, 
  UseRaffleSubmissionReturn,
  ShippingAddress,
  NewAddressForm
} from '../types/raffleTypes'

/**
 * Custom hook for handling raffle entry submission
 */
export const useRaffleSubmission = (): UseRaffleSubmissionReturn => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submissionError, setSubmissionError] = useState<string | null>(null)

  /**
   * Submit raffle entry
   */
  const submitEntry = useCallback(async (data: RaffleEntrySubmission): Promise<boolean> => {
    setIsSubmitting(true)
    setSubmissionError(null)

    try {
      let finalShippingAddress: ShippingAddress

      // If using new address, create it first
      if ('name' in data.shippingAddress && !('id' in data.shippingAddress)) {
        const newAddressData = data.shippingAddress as NewAddressForm
        
        try {
          const addressId = await createShippingAddress(data.userId, {
            ...newAddressData,
            isDefault: false
          })

          finalShippingAddress = {
            id: addressId,
            ...newAddressData,
            isDefault: false
          }

          toast.success('Shipping address saved successfully')
        } catch (addressError) {
          console.error('Error creating shipping address:', addressError)
          throw new Error('Failed to save shipping address')
        }
      } else {
        // Use existing address
        finalShippingAddress = data.shippingAddress as ShippingAddress
      }

      // Create raffle entry
      const entryData = {
        userId: data.userId,
        raffleId: data.raffleId,
        selectedVariants: data.selectedVariants,
        shippingAddressId: finalShippingAddress.id,
        shippingMethod: data.shippingMethod,
        requirements: data.requirements,
        submittedAt: data.submittedAt,
        status: 'pending' as const
      }

      await createRaffleEntry(entryData)

      toast.success('Raffle entry submitted successfully!')
      return true

    } catch (error) {
      console.error('Error submitting raffle entry:', error)
      
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Failed to submit raffle entry. Please try again.'
      
      setSubmissionError(errorMessage)
      toast.error(errorMessage)
      return false

    } finally {
      setIsSubmitting(false)
    }
  }, [])

  return {
    submitEntry,
    isSubmitting,
    submissionError
  }
}

/**
 * Hook for managing shipping addresses
 */
export const useShippingAddresses = (userId: string | undefined) => {
  const [addresses, setAddresses] = useState<ShippingAddress[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Load user's shipping addresses
   */
  const loadAddresses = useCallback(async () => {
    if (!userId) return

    setIsLoading(true)
    setError(null)

    try {
      const userAddresses = await getUserShippingAddresses(userId)
      setAddresses(userAddresses)
    } catch (err) {
      console.error('Error loading shipping addresses:', err)
      setError('Failed to load shipping addresses')
    } finally {
      setIsLoading(false)
    }
  }, [userId])

  /**
   * Add new address to the list
   */
  const addAddress = useCallback((newAddress: ShippingAddress) => {
    setAddresses(prev => [...prev, newAddress])
  }, [])

  /**
   * Update existing address in the list
   */
  const updateAddress = useCallback((addressId: string, updates: Partial<ShippingAddress>) => {
    setAddresses(prev => 
      prev.map(addr => 
        addr.id === addressId 
          ? { ...addr, ...updates }
          : addr
      )
    )
  }, [])

  /**
   * Remove address from the list
   */
  const removeAddress = useCallback((addressId: string) => {
    setAddresses(prev => prev.filter(addr => addr.id !== addressId))
  }, [])

  return {
    addresses,
    isLoading,
    error,
    loadAddresses,
    addAddress,
    updateAddress,
    removeAddress
  }
}

/**
 * Hook for raffle eligibility checking
 */
export const useRaffleEligibility = () => {
  const [isChecking, setIsChecking] = useState(false)
  const [eligibilityError, setEligibilityError] = useState<string | null>(null)

  /**
   * Check if user is eligible for raffle entry
   */
  const checkEligibility = useCallback(async (
    userId: string, 
    raffleId: string
  ): Promise<boolean> => {
    setIsChecking(true)
    setEligibilityError(null)

    try {
      // Import the function dynamically to avoid circular dependencies
      const { checkRaffleEntryEligibility } = await import('@/lib/firestore')
      
      const isEligible = await checkRaffleEntryEligibility(userId, raffleId)
      
      if (!isEligible) {
        setEligibilityError('You are not eligible for this raffle or have already entered')
        toast.error('You are not eligible for this raffle')
      }

      return isEligible

    } catch (error) {
      console.error('Error checking raffle eligibility:', error)
      setEligibilityError('Failed to check eligibility')
      toast.error('Failed to check raffle eligibility')
      return false

    } finally {
      setIsChecking(false)
    }
  }, [])

  return {
    checkEligibility,
    isChecking,
    eligibilityError
  }
}
