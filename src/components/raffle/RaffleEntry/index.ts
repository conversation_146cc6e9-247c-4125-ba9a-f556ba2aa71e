/**
 * Raffle Entry Module Exports
 * 
 * Centralized exports for the refactored raffle entry system
 * 
 * <AUTHOR> Team
 */

// Main Container Component
export { default as RaffleEntryContainer } from './RaffleEntryContainer'

// Step Components
export { default as RequirementsStep } from './steps/RequirementsStep'
export { default as ProductsStep } from './steps/ProductsStep'
export { default as AddressStep } from './steps/AddressStep'
export { default as InformationStep } from './steps/InformationStep'
export { default as ReviewStep } from './steps/ReviewStep'
export { default as SuccessStep } from './steps/SuccessStep'

// Hooks
export { useRaffleForm, useStepNavigation } from './hooks/useRaffleForm'
export { 
  useRaffleSubmission, 
  useShippingAddresses, 
  useRaffleEligibility 
} from './hooks/useRaffleSubmission'

// Types
export type {
  FormStep,
  Product,
  ProductVariant,
  ShippingAddress,
  ShippingMethod,
  RaffleRequirements,
  NewAddressForm,
  ValidationResult,
  RaffleFormState,
  StepComponentProps,
  RaffleEntryContainerProps,
  ExtendedProductVariant,
  RaffleEntrySubmission,
  UseRaffleFormReturn,
  UseRaffleSubmissionReturn
} from './types/raffleTypes'

// Constants
export {
  INITIAL_FORM_STATE,
  DEFAULT_SHIPPING_METHODS,
  STEP_TITLES,
  STEP_ORDER
} from './types/raffleTypes'
