/**
 * Address Step Component
 * 
 * Third step of raffle entry - Add new shipping address (shown when user has no saved addresses)
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { MapPin, User, Phone, Globe } from 'lucide-react'
import { StepComponentProps } from '../types/raffleTypes'

/**
 * Address step component
 */
export const AddressStep: React.FC<StepComponentProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { newAddress, fieldErrors } = formState

  /**
   * Handle input change
   */
  const handleInputChange = (field: keyof typeof newAddress, value: string) => {
    onUpdateFormState({
      newAddress: {
        ...newAddress,
        [field]: value
      }
    })
  }

  /**
   * Input field component
   */
  const InputField: React.FC<{
    label: string
    field: keyof typeof newAddress
    type?: string
    placeholder?: string
    icon?: React.ReactNode
    required?: boolean
  }> = ({ label, field, type = 'text', placeholder, icon, required = true }) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-300">
        {label} {required && <span className="text-red-400">*</span>}
      </label>
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        <input
          type={type}
          value={newAddress[field]}
          onChange={(e) => handleInputChange(field, e.target.value)}
          placeholder={placeholder}
          className={`w-full ${icon ? 'pl-10' : 'pl-3'} pr-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 transition-colors ${
            fieldErrors[field]
              ? 'border-red-500 focus:ring-red-500'
              : 'border-gray-700 focus:ring-accent-500'
          }`}
        />
      </div>
      {fieldErrors[field] && (
        <p className="text-red-400 text-sm">{fieldErrors[field]}</p>
      )}
    </div>
  )

  /**
   * Country options
   */
  const countries = [
    { code: 'US', name: 'United States' },
    { code: 'CA', name: 'Canada' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'AU', name: 'Australia' },
    { code: 'DE', name: 'Germany' },
    { code: 'FR', name: 'France' },
    { code: 'JP', name: 'Japan' },
    { code: 'KR', name: 'South Korea' },
    { code: 'SG', name: 'Singapore' },
    { code: 'NL', name: 'Netherlands' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <p className="text-gray-300 mb-4">
          Add a shipping address to continue with your raffle entry.
        </p>
        <p className="text-sm text-gray-400">
          This address will be saved to your account for future use.
        </p>
      </div>

      {/* Form */}
      <div className="space-y-6">
        {/* Full Name */}
        <InputField
          label="Full Name"
          field="name"
          placeholder="Enter your full name"
          icon={<User size={18} />}
        />

        {/* Address */}
        <InputField
          label="Street Address"
          field="address"
          placeholder="Enter your street address"
          icon={<MapPin size={18} />}
        />

        {/* City and State Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            label="City"
            field="city"
            placeholder="Enter city"
          />
          <InputField
            label="State/Province"
            field="state"
            placeholder="Enter state or province"
          />
        </div>

        {/* ZIP and Country Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            label="ZIP/Postal Code"
            field="zipCode"
            placeholder="Enter ZIP or postal code"
          />
          
          {/* Country Dropdown */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              Country <span className="text-red-400">*</span>
            </label>
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <Globe size={18} />
              </div>
              <select
                value={newAddress.country}
                onChange={(e) => handleInputChange('country', e.target.value)}
                className={`w-full pl-10 pr-3 py-2 bg-gray-800 border rounded-lg text-white focus:outline-none focus:ring-2 transition-colors appearance-none ${
                  fieldErrors.country
                    ? 'border-red-500 focus:ring-red-500'
                    : 'border-gray-700 focus:ring-accent-500'
                }`}
              >
                {countries.map(country => (
                  <option key={country.code} value={country.code}>
                    {country.name}
                  </option>
                ))}
              </select>
            </div>
            {fieldErrors.country && (
              <p className="text-red-400 text-sm">{fieldErrors.country}</p>
            )}
          </div>
        </div>

        {/* Phone Number */}
        <InputField
          label="Phone Number"
          field="phone"
          type="tel"
          placeholder="Enter your phone number"
          icon={<Phone size={18} />}
        />
      </div>

      {/* Address Preview */}
      {(newAddress.name || newAddress.address || newAddress.city) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-gray-800 rounded-lg border border-gray-700"
        >
          <h4 className="text-white font-medium mb-3">Address Preview:</h4>
          <div className="text-gray-300 text-sm space-y-1">
            {newAddress.name && <div>{newAddress.name}</div>}
            {newAddress.address && <div>{newAddress.address}</div>}
            {(newAddress.city || newAddress.state || newAddress.zipCode) && (
              <div>
                {[newAddress.city, newAddress.state, newAddress.zipCode]
                  .filter(Boolean)
                  .join(', ')}
              </div>
            )}
            {newAddress.country && (
              <div>{countries.find(c => c.code === newAddress.country)?.name}</div>
            )}
            {newAddress.phone && <div>{newAddress.phone}</div>}
          </div>
        </motion.div>
      )}

      {/* Security Note */}
      <div className="p-3 bg-blue-500/20 border border-blue-500 rounded-lg">
        <p className="text-blue-400 text-sm">
          🔒 Your address information is encrypted and securely stored. We only use it for shipping purposes.
        </p>
      </div>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Make sure your address is accurate to ensure successful delivery if you win.
        </p>
      </div>
    </div>
  )
}

export default AddressStep
