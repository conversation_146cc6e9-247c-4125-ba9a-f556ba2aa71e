/**
 * Information Step Component
 * 
 * Third step of raffle entry - Select existing address and shipping method (for users with saved addresses)
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { MapPin, Truck, Check, Clock, DollarSign } from 'lucide-react'
import { StepComponentProps } from '../types/raffleTypes'

/**
 * Information step component
 */
export const InformationStep: React.FC<StepComponentProps> = ({
  formState,
  onUpdateFormState,
  addresses,
  shippingMethods
}) => {
  const { selectedAddress, shippingMethod, fieldErrors } = formState

  /**
   * Handle address selection
   */
  const handleAddressSelect = (addressId: string) => {
    onUpdateFormState({ selectedAddress: addressId })
  }

  /**
   * Handle shipping method selection
   */
  const handleShippingMethodSelect = (methodId: string) => {
    onUpdateFormState({ shippingMethod: methodId })
  }

  /**
   * Address card component
   */
  const AddressCard: React.FC<{ address: any }> = ({ address }) => {
    const isSelected = selectedAddress === address.id

    return (
      <motion.div
        className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
          isSelected
            ? 'bg-accent-600/20 border-accent-500'
            : 'bg-gray-800 border-gray-700 hover:border-gray-600'
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => handleAddressSelect(address.id)}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <div className={`p-2 rounded-lg ${isSelected ? 'bg-accent-600' : 'bg-gray-700'}`}>
              <MapPin size={18} className="text-white" />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-white font-medium">{address.name}</h3>
                {address.isDefault && (
                  <span className="px-2 py-0.5 bg-accent-600 text-white text-xs rounded">
                    Default
                  </span>
                )}
              </div>
              
              <div className="text-gray-300 text-sm space-y-0.5">
                <div>{address.address}</div>
                <div>{address.city}, {address.state} {address.zipCode}</div>
                <div>{address.country}</div>
                {address.phone && <div>{address.phone}</div>}
              </div>
            </div>
          </div>
          
          {isSelected && (
            <div className="w-6 h-6 bg-accent-600 rounded-full flex items-center justify-center">
              <Check size={14} className="text-white" />
            </div>
          )}
        </div>
      </motion.div>
    )
  }

  /**
   * Shipping method card component
   */
  const ShippingMethodCard: React.FC<{ method: any }> = ({ method }) => {
    const isSelected = shippingMethod === method.id

    return (
      <motion.div
        className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
          isSelected
            ? 'bg-accent-600/20 border-accent-500'
            : 'bg-gray-800 border-gray-700 hover:border-gray-600'
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => handleShippingMethodSelect(method.id)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${isSelected ? 'bg-accent-600' : 'bg-gray-700'}`}>
              <Truck size={18} className="text-white" />
            </div>
            
            <div>
              <h3 className="text-white font-medium">{method.name}</h3>
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Clock size={14} />
                <span>{method.description}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="flex items-center gap-1 text-accent-400 font-bold">
                <DollarSign size={16} />
                {method.price}
              </div>
            </div>
            
            {isSelected && (
              <div className="w-6 h-6 bg-accent-600 rounded-full flex items-center justify-center">
                <Check size={14} className="text-white" />
              </div>
            )}
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <p className="text-gray-300 mb-4">
          Select your shipping address and preferred delivery method.
        </p>
      </div>

      {/* Shipping Address Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-white font-medium">Select Shipping Address</h3>
          <button className="text-accent-400 hover:text-accent-300 text-sm">
            + Add New Address
          </button>
        </div>

        {fieldErrors.address && (
          <div className="p-3 bg-red-500/20 border border-red-500 rounded-lg">
            <p className="text-red-400 text-sm">{fieldErrors.address}</p>
          </div>
        )}

        <div className="space-y-3">
          {addresses.map(address => (
            <AddressCard key={address.id} address={address} />
          ))}
        </div>
      </div>

      {/* Shipping Method Selection */}
      <div className="space-y-4">
        <h3 className="text-white font-medium">Select Shipping Method</h3>

        {fieldErrors.shipping && (
          <div className="p-3 bg-red-500/20 border border-red-500 rounded-lg">
            <p className="text-red-400 text-sm">{fieldErrors.shipping}</p>
          </div>
        )}

        <div className="space-y-3">
          {shippingMethods.map(method => (
            <ShippingMethodCard key={method.id} method={method} />
          ))}
        </div>
      </div>

      {/* Selection Summary */}
      {(selectedAddress || shippingMethod) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-gray-800 rounded-lg border border-gray-700"
        >
          <h4 className="text-white font-medium mb-3">Selection Summary:</h4>
          
          {selectedAddress && (
            <div className="mb-3">
              <p className="text-sm text-gray-400 mb-1">Shipping Address:</p>
              {(() => {
                const address = addresses.find(a => a.id === selectedAddress)
                return address ? (
                  <div className="text-gray-300 text-sm">
                    <div>{address.name}</div>
                    <div>{address.address}, {address.city}, {address.state} {address.zipCode}</div>
                  </div>
                ) : null
              })()}
            </div>
          )}
          
          {shippingMethod && (
            <div>
              <p className="text-sm text-gray-400 mb-1">Shipping Method:</p>
              {(() => {
                const method = shippingMethods.find(m => m.id === shippingMethod)
                return method ? (
                  <div className="flex items-center justify-between">
                    <div className="text-gray-300 text-sm">
                      <div>{method.name}</div>
                      <div className="text-gray-400">{method.description}</div>
                    </div>
                    <div className="text-accent-400 font-bold">${method.price}</div>
                  </div>
                ) : null
              })()}
            </div>
          )}
        </motion.div>
      )}

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          You can manage your saved addresses in your{' '}
          <a href="/profile" className="text-accent-400 hover:text-accent-300">
            profile settings
          </a>.
        </p>
      </div>
    </div>
  )
}

export default InformationStep
