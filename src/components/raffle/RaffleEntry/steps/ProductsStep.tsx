/**
 * Products Step Component
 * 
 * Second step of raffle entry - Product variant selection
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Check, Package } from 'lucide-react'
import Image from 'next/image'
import { StepComponentProps, ExtendedProductVariant } from '../types/raffleTypes'

/**
 * Products step component
 */
export const ProductsStep: React.FC<StepComponentProps> = ({
  formState,
  onUpdateFormState,
  products
}) => {
  const { selectedVariants, fieldErrors } = formState

  /**
   * Get all variants from products with product names
   */
  const getAllVariants = (): ExtendedProductVariant[] => {
    const allVariants: ExtendedProductVariant[] = []

    products.forEach(product => {
      if (product.variants && product.variants.length > 0) {
        product.variants.forEach(variant => {
          allVariants.push({
            ...variant,
            productName: product.name
          })
        })
      }
    })

    return allVariants
  }

  const allVariants = getAllVariants()

  /**
   * Handle variant selection toggle
   */
  const handleVariantToggle = (variantId: string) => {
    const newSelectedVariants = selectedVariants.includes(variantId)
      ? selectedVariants.filter(id => id !== variantId)
      : [...selectedVariants, variantId]

    onUpdateFormState({
      selectedVariants: newSelectedVariants
    })
  }

  /**
   * Variant card component
   */
  const VariantCard: React.FC<{ variant: ExtendedProductVariant }> = ({ variant }) => {
    const isSelected = selectedVariants.includes(variant.id)
    const isOutOfStock = variant.stock === 0

    return (
      <motion.div
        className={`relative p-4 rounded-lg border transition-all duration-200 cursor-pointer ${
          isOutOfStock
            ? 'bg-gray-900 border-gray-800 opacity-50 cursor-not-allowed'
            : isSelected
            ? 'bg-accent-600/20 border-accent-500'
            : 'bg-gray-800 border-gray-700 hover:border-gray-600'
        }`}
        whileHover={!isOutOfStock ? { scale: 1.02 } : {}}
        whileTap={!isOutOfStock ? { scale: 0.98 } : {}}
        onClick={() => !isOutOfStock && handleVariantToggle(variant.id)}
      >
        {/* Selection Indicator */}
        {isSelected && (
          <div className="absolute top-2 right-2 w-6 h-6 bg-accent-600 rounded-full flex items-center justify-center">
            <Check size={14} className="text-white" />
          </div>
        )}

        {/* Out of Stock Badge */}
        {isOutOfStock && (
          <div className="absolute top-2 left-2 px-2 py-1 bg-red-600 text-white text-xs rounded">
            Out of Stock
          </div>
        )}

        {/* Product Image */}
        <div className="relative w-full h-48 mb-4 rounded-lg overflow-hidden bg-gray-700">
          {variant.image ? (
            <Image
              src={variant.image}
              alt={variant.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Package size={48} className="text-gray-500" />
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-2">
          <h3 className="text-white font-medium text-sm">{variant.productName}</h3>
          <p className="text-gray-300 font-semibold">{variant.name}</p>
          
          {variant.description && (
            <p className="text-gray-400 text-sm">{variant.description}</p>
          )}

          <div className="flex items-center justify-between">
            <span className="text-accent-400 font-bold">${variant.price}</span>
            <span className="text-gray-500 text-sm">
              {isOutOfStock ? 'Out of Stock' : `${variant.stock} available`}
            </span>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <p className="text-gray-300 mb-4">
          Select the product variants you'd like to enter the raffle for.
        </p>
        <p className="text-sm text-gray-400">
          You can select multiple variants to increase your chances.
        </p>
      </div>

      {/* Error Message */}
      {fieldErrors.variants && (
        <div className="p-3 bg-red-500/20 border border-red-500 rounded-lg">
          <p className="text-red-400 text-sm">{fieldErrors.variants}</p>
        </div>
      )}

      {/* Selected Count */}
      {selectedVariants.length > 0 && (
        <div className="p-3 bg-accent-600/20 border border-accent-500 rounded-lg">
          <p className="text-accent-400 text-sm">
            {selectedVariants.length} variant{selectedVariants.length !== 1 ? 's' : ''} selected
          </p>
        </div>
      )}

      {/* Products Grid */}
      {allVariants.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {allVariants.map(variant => (
            <VariantCard key={variant.id} variant={variant} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Package size={48} className="text-gray-500 mx-auto mb-4" />
          <p className="text-gray-400">No product variants available for this raffle.</p>
        </div>
      )}

      {/* Selection Summary */}
      {selectedVariants.length > 0 && (
        <div className="mt-8 p-4 bg-gray-800 rounded-lg">
          <h4 className="text-white font-medium mb-3">Selected Variants:</h4>
          <div className="space-y-2">
            {selectedVariants.map(variantId => {
              const variant = allVariants.find(v => v.id === variantId)
              if (!variant) return null

              return (
                <div key={variantId} className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">
                    {variant.productName} - {variant.name}
                  </span>
                  <span className="text-accent-400">${variant.price}</span>
                </div>
              )
            })}
          </div>
          
          <div className="mt-3 pt-3 border-t border-gray-700">
            <div className="flex items-center justify-between font-medium">
              <span className="text-white">Total Value:</span>
              <span className="text-accent-400">
                ${selectedVariants.reduce((total, variantId) => {
                  const variant = allVariants.find(v => v.id === variantId)
                  return total + (variant?.price || 0)
                }, 0)}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Selecting multiple variants increases your entry count but doesn't guarantee winning multiple items.
        </p>
      </div>
    </div>
  )
}

export default ProductsStep
