/**
 * Requirements Step Component
 * 
 * First step of raffle entry - Discord verification and requirements checking
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Check, ExternalLink, MessageCircle, Instagram, Send, Mail } from 'lucide-react'
import { StepComponentProps } from '../types/raffleTypes'

/**
 * Requirements step component
 */
export const RequirementsStep: React.FC<StepComponentProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { raffleRequirements, fieldErrors } = formState

  /**
   * Handle requirement toggle
   */
  const handleRequirementToggle = (requirement: keyof typeof raffleRequirements) => {
    onUpdateFormState({
      raffleRequirements: {
        ...raffleRequirements,
        [requirement]: !raffleRequirements[requirement]
      }
    })
  }

  /**
   * Requirement item component
   */
  const RequirementItem: React.FC<{
    id: keyof typeof raffleRequirements
    title: string
    description: string
    icon: React.ReactNode
    actionText: string
    actionUrl?: string
    isCompleted: boolean
  }> = ({ id, title, description, icon, actionText, actionUrl, isCompleted }) => (
    <motion.div
      className={`p-4 rounded-lg border transition-all duration-200 ${
        isCompleted
          ? 'bg-accent-600/20 border-accent-500'
          : 'bg-gray-800 border-gray-700 hover:border-gray-600'
      }`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="flex items-start gap-4">
        <div className={`p-2 rounded-lg ${isCompleted ? 'bg-accent-600' : 'bg-gray-700'}`}>
          {icon}
        </div>
        
        <div className="flex-1">
          <h3 className="text-white font-medium mb-1">{title}</h3>
          <p className="text-gray-400 text-sm mb-3">{description}</p>
          
          <div className="flex items-center gap-3">
            {actionUrl ? (
              <a
                href={actionUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 px-3 py-1.5 bg-accent-600 hover:bg-accent-700 text-white text-sm rounded-md transition-colors"
              >
                {actionText}
                <ExternalLink size={14} />
              </a>
            ) : (
              <button
                onClick={() => handleRequirementToggle(id)}
                className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                  isCompleted
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-gray-600 hover:bg-gray-700 text-white'
                }`}
              >
                {isCompleted ? 'Completed' : actionText}
              </button>
            )}
            
            <button
              onClick={() => handleRequirementToggle(id)}
              className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-colors ${
                isCompleted
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-gray-600 hover:bg-gray-700 text-white'
              }`}
            >
              {isCompleted ? (
                <>
                  <Check size={14} />
                  Mark as Done
                </>
              ) : (
                'Mark as Done'
              )}
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <p className="text-gray-300 mb-4">
          Complete the following requirements to enter this raffle.
        </p>
        <p className="text-sm text-gray-400">
          You need to complete at least one requirement to continue.
        </p>
      </div>

      {/* Error Message */}
      {fieldErrors.requirements && (
        <div className="p-3 bg-red-500/20 border border-red-500 rounded-lg">
          <p className="text-red-400 text-sm">{fieldErrors.requirements}</p>
        </div>
      )}

      {/* Requirements List */}
      <div className="space-y-4">
        <RequirementItem
          id="discordLinked"
          title="Link Discord Account"
          description="Connect your Discord account to verify your identity and join our community."
          icon={<MessageCircle size={20} className="text-white" />}
          actionText="Link Discord"
          actionUrl="/auth/discord"
          isCompleted={raffleRequirements.discordLinked}
        />

        <RequirementItem
          id="followInstagram"
          title="Follow on Instagram"
          description="Follow @syndicaps on Instagram for the latest updates and behind-the-scenes content."
          icon={<Instagram size={20} className="text-white" />}
          actionText="Follow @syndicaps"
          actionUrl="https://instagram.com/syndicaps"
          isCompleted={raffleRequirements.followInstagram}
        />

        <RequirementItem
          id="joinTelegram"
          title="Join Telegram Channel"
          description="Join our Telegram channel for exclusive announcements and community discussions."
          icon={<Send size={20} className="text-white" />}
          actionText="Join Telegram"
          actionUrl="https://t.me/syndicaps"
          isCompleted={raffleRequirements.joinTelegram}
        />

        <RequirementItem
          id="subscribeNewsletter"
          title="Subscribe to Newsletter"
          description="Get the latest news, product launches, and exclusive offers delivered to your inbox."
          icon={<Mail size={20} className="text-white" />}
          actionText="Subscribe"
          actionUrl="/newsletter"
          isCompleted={raffleRequirements.subscribeNewsletter}
        />
      </div>

      {/* Progress Indicator */}
      <div className="mt-8 p-4 bg-gray-800 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-400">Requirements Completed</span>
          <span className="text-sm text-white">
            {Object.values(raffleRequirements).filter(Boolean).length} / {Object.keys(raffleRequirements).length}
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-accent-600 h-2 rounded-full transition-all duration-300"
            style={{
              width: `${(Object.values(raffleRequirements).filter(Boolean).length / Object.keys(raffleRequirements).length) * 100}%`
            }}
          />
        </div>
      </div>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Having trouble with any requirements? Contact our{' '}
          <a href="/support" className="text-accent-400 hover:text-accent-300">
            support team
          </a>{' '}
          for assistance.
        </p>
      </div>
    </div>
  )
}

export default RequirementsStep
