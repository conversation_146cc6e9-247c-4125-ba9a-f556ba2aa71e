/**
 * Review Step Component
 * 
 * Fourth step of raffle entry - Review and submit entry
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Check, Package, MapPin, Truck, User, AlertCircle } from 'lucide-react'
import Image from 'next/image'
import { StepComponentProps, ExtendedProductVariant } from '../types/raffleTypes'

/**
 * Review step component
 */
export const ReviewStep: React.FC<StepComponentProps> = ({
  formState,
  products,
  addresses,
  shippingMethods
}) => {
  const { selectedVariants, selectedAddress, shippingMethod, newAddress, raffleRequirements } = formState

  /**
   * Get selected address data
   */
  const getAddressData = () => {
    if (addresses.length > 0 && selectedAddress) {
      return addresses.find(addr => addr.id === selectedAddress)
    }
    return newAddress
  }

  /**
   * Get selected shipping method data
   */
  const getShippingMethodData = () => {
    return shippingMethods.find(method => method.id === shippingMethod)
  }

  /**
   * Get all variants with product names
   */
  const getAllVariants = (): ExtendedProductVariant[] => {
    const allVariants: ExtendedProductVariant[] = []

    products.forEach(product => {
      if (product.variants && product.variants.length > 0) {
        product.variants.forEach(variant => {
          allVariants.push({
            ...variant,
            productName: product.name
          })
        })
      }
    })

    return allVariants
  }

  const allVariants = getAllVariants()
  const selectedVariantData = selectedVariants.map(id => 
    allVariants.find(variant => variant.id === id)
  ).filter(Boolean)

  const addressData = getAddressData()
  const shippingData = getShippingMethodData()

  /**
   * Calculate total value
   */
  const totalValue = selectedVariantData.reduce((sum, variant) => sum + (variant?.price || 0), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <p className="text-gray-300 mb-4">
          Please review your raffle entry details before submitting.
        </p>
        <p className="text-sm text-gray-400">
          Make sure all information is correct as entries cannot be modified after submission.
        </p>
      </div>

      {/* Requirements Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-4 bg-gray-800 rounded-lg border border-gray-700"
      >
        <div className="flex items-center gap-3 mb-3">
          <div className="p-2 bg-accent-600 rounded-lg">
            <User size={18} className="text-white" />
          </div>
          <h3 className="text-white font-medium">Entry Requirements</h3>
        </div>
        
        <div className="space-y-2">
          {Object.entries(raffleRequirements).map(([key, completed]) => {
            const labels = {
              discordLinked: 'Discord Account Linked',
              followInstagram: 'Following on Instagram',
              joinTelegram: 'Joined Telegram Channel',
              subscribeNewsletter: 'Subscribed to Newsletter'
            }
            
            return (
              <div key={key} className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full flex items-center justify-center ${
                  completed ? 'bg-green-600' : 'bg-gray-600'
                }`}>
                  {completed && <Check size={10} className="text-white" />}
                </div>
                <span className={`text-sm ${completed ? 'text-green-400' : 'text-gray-400'}`}>
                  {labels[key as keyof typeof labels]}
                </span>
              </div>
            )
          })}
        </div>
      </motion.div>

      {/* Selected Products */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="p-4 bg-gray-800 rounded-lg border border-gray-700"
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-accent-600 rounded-lg">
            <Package size={18} className="text-white" />
          </div>
          <h3 className="text-white font-medium">Selected Products</h3>
        </div>
        
        <div className="space-y-3">
          {selectedVariantData.map(variant => (
            <div key={variant?.id} className="flex items-center gap-3 p-3 bg-gray-700 rounded-lg">
              <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-600">
                {variant?.image ? (
                  <Image
                    src={variant.image}
                    alt={variant.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Package size={20} className="text-gray-400" />
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <p className="text-white font-medium text-sm">{variant?.productName}</p>
                <p className="text-gray-300 text-sm">{variant?.name}</p>
              </div>
              
              <div className="text-accent-400 font-bold">${variant?.price}</div>
            </div>
          ))}
          
          <div className="flex items-center justify-between pt-3 border-t border-gray-600">
            <span className="text-white font-medium">Total Value:</span>
            <span className="text-accent-400 font-bold text-lg">${totalValue}</span>
          </div>
        </div>
      </motion.div>

      {/* Shipping Information */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="p-4 bg-gray-800 rounded-lg border border-gray-700"
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-accent-600 rounded-lg">
            <MapPin size={18} className="text-white" />
          </div>
          <h3 className="text-white font-medium">Shipping Information</h3>
        </div>
        
        {addressData && (
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-400 mb-2">Shipping Address:</p>
              <div className="text-gray-300 text-sm space-y-0.5">
                <div>{addressData.name}</div>
                <div>{addressData.address}</div>
                <div>{addressData.city}, {addressData.state} {addressData.zipCode}</div>
                <div>{addressData.country}</div>
                {addressData.phone && <div>{addressData.phone}</div>}
              </div>
            </div>
            
            {shippingData && (
              <div>
                <p className="text-sm text-gray-400 mb-2">Shipping Method:</p>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-300 text-sm font-medium">{shippingData.name}</p>
                    <p className="text-gray-400 text-sm">{shippingData.description}</p>
                  </div>
                  <div className="text-accent-400 font-bold">${shippingData.price}</div>
                </div>
              </div>
            )}
          </div>
        )}
      </motion.div>

      {/* Important Notice */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="p-4 bg-yellow-500/20 border border-yellow-500 rounded-lg"
      >
        <div className="flex items-start gap-3">
          <AlertCircle size={20} className="text-yellow-400 mt-0.5" />
          <div>
            <h4 className="text-yellow-400 font-medium mb-2">Important Notice</h4>
            <ul className="text-yellow-300 text-sm space-y-1">
              <li>• Raffle entries cannot be modified or cancelled after submission</li>
              <li>• Winners will be selected randomly and notified via email</li>
              <li>• Shipping costs are separate and will be charged if you win</li>
              <li>• Please ensure your contact information is accurate</li>
            </ul>
          </div>
        </div>
      </motion.div>

      {/* Terms and Conditions */}
      <div className="text-center">
        <label className="flex items-center justify-center gap-2 text-sm text-gray-400">
          <input
            type="checkbox"
            className="w-4 h-4 text-accent-600 bg-gray-800 border-gray-600 rounded focus:ring-accent-500"
            required
          />
          I agree to the{' '}
          <a href="/terms" className="text-accent-400 hover:text-accent-300">
            Terms and Conditions
          </a>{' '}
          and{' '}
          <a href="/privacy" className="text-accent-400 hover:text-accent-300">
            Privacy Policy
          </a>
        </label>
      </div>
    </div>
  )
}

export default ReviewStep
