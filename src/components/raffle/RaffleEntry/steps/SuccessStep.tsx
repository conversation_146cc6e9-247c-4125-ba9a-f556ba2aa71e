/**
 * Success Step Component
 * 
 * Final step of raffle entry - Success confirmation
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Check, Gift, Mail, Calendar, Share2, Home } from 'lucide-react'
import { StepComponentProps } from '../types/raffleTypes'

/**
 * Success step component
 */
export const SuccessStep: React.FC<StepComponentProps> = ({
  formState,
  onUpdateFormState
}) => {
  const { selectedVariants } = formState

  /**
   * Handle share entry
   */
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'I just entered a Syndicaps raffle!',
        text: 'Check out this awesome keycap raffle from Syndicaps',
        url: window.location.origin + '/raffles'
      })
    } else {
      // Fallback to copying to clipboard
      navigator.clipboard.writeText(window.location.origin + '/raffles')
      // Could show a toast here
    }
  }

  /**
   * <PERSON>le return to raffles
   */
  const handleReturnToRaffles = () => {
    window.location.href = '/raffles'
  }

  /**
   * Handle go to home
   */
  const handleGoHome = () => {
    window.location.href = '/'
  }

  return (
    <div className="text-center space-y-8">
      {/* Success Animation */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ 
          type: "spring",
          stiffness: 260,
          damping: 20,
          delay: 0.2 
        }}
        className="w-24 h-24 bg-accent-600 rounded-full flex items-center justify-center mx-auto"
      >
        <Check size={48} className="text-white" />
      </motion.div>

      {/* Success Message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-4"
      >
        <h2 className="text-3xl font-bold text-white">Entry Submitted Successfully!</h2>
        <p className="text-gray-300 text-lg">
          Your raffle entry has been received and confirmed.
        </p>
      </motion.div>

      {/* Entry Details */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="p-6 bg-gray-800 rounded-lg border border-gray-700 max-w-md mx-auto"
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-accent-600 rounded-lg">
            <Gift size={20} className="text-white" />
          </div>
          <h3 className="text-white font-medium">Entry Summary</h3>
        </div>
        
        <div className="space-y-3 text-left">
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Products Entered:</span>
            <span className="text-white font-medium">{selectedVariants.length}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Entry Date:</span>
            <span className="text-white font-medium">
              {new Date().toLocaleDateString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Status:</span>
            <span className="text-green-400 font-medium">Confirmed</span>
          </div>
        </div>
      </motion.div>

      {/* What's Next */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="space-y-6"
      >
        <h3 className="text-xl font-semibold text-white">What happens next?</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
          <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-2 bg-blue-600 rounded-lg w-fit mx-auto mb-3">
              <Calendar size={20} className="text-white" />
            </div>
            <h4 className="text-white font-medium mb-2">Wait for Draw</h4>
            <p className="text-gray-400 text-sm">
              The raffle will end on the scheduled date and winners will be drawn randomly.
            </p>
          </div>
          
          <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-2 bg-green-600 rounded-lg w-fit mx-auto mb-3">
              <Mail size={20} className="text-white" />
            </div>
            <h4 className="text-white font-medium mb-2">Get Notified</h4>
            <p className="text-gray-400 text-sm">
              Winners will be notified via email within 24 hours of the draw.
            </p>
          </div>
          
          <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-2 bg-purple-600 rounded-lg w-fit mx-auto mb-3">
              <Gift size={20} className="text-white" />
            </div>
            <h4 className="text-white font-medium mb-2">Claim Prize</h4>
            <p className="text-gray-400 text-sm">
              If you win, follow the instructions in your notification email.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
        className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto"
      >
        <button
          onClick={handleShare}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
        >
          <Share2 size={18} />
          Share Entry
        </button>
        
        <button
          onClick={handleReturnToRaffles}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
        >
          <Gift size={18} />
          View More Raffles
        </button>
        
        <button
          onClick={handleGoHome}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
        >
          <Home size={18} />
          Go Home
        </button>
      </motion.div>

      {/* Additional Info */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
        className="space-y-4"
      >
        <div className="p-4 bg-blue-500/20 border border-blue-500 rounded-lg max-w-lg mx-auto">
          <p className="text-blue-400 text-sm">
            📧 A confirmation email has been sent to your registered email address with your entry details.
          </p>
        </div>
        
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Questions about your entry? Contact our{' '}
            <a href="/support" className="text-accent-400 hover:text-accent-300">
              support team
            </a>{' '}
            for assistance.
          </p>
        </div>
      </motion.div>

      {/* Social Media Follow */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.4 }}
        className="pt-8 border-t border-gray-700"
      >
        <p className="text-gray-400 text-sm mb-4">
          Stay updated on future raffles and product launches:
        </p>
        
        <div className="flex justify-center gap-4">
          <a
            href="https://instagram.com/syndicaps"
            target="_blank"
            rel="noopener noreferrer"
            className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:opacity-90 transition-opacity text-sm"
          >
            Follow on Instagram
          </a>
          
          <a
            href="https://t.me/syndicaps"
            target="_blank"
            rel="noopener noreferrer"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
          >
            Join Telegram
          </a>
        </div>
      </motion.div>
    </div>
  )
}

export default SuccessStep
