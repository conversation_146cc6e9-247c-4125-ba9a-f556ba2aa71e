/**
 * Raffle Entry Types and Interfaces
 * 
 * Shared types for the refactored raffle entry system
 * 
 * <AUTHOR> Team
 */

/**
 * Form step type
 */
export type FormStep = 'requirements' | 'products' | 'address' | 'information' | 'review' | 'success'

/**
 * Product variant interface
 */
export interface ProductVariant {
  id: string
  name: string
  price: number
  image: string
  stock: number
  description?: string
}

/**
 * Product interface
 */
export interface Product {
  id: string
  name: string
  price: number
  image: string
  category: string
  isRaffle: boolean
  soldOut: boolean
  variants?: ProductVariant[]
}

/**
 * Shipping address interface
 */
export interface ShippingAddress {
  id: string
  name: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  phone: string
  isDefault: boolean
}

/**
 * Shipping method interface
 */
export interface ShippingMethod {
  id: string
  name: string
  price: number
  description: string
}

/**
 * Raffle requirements interface
 */
export interface RaffleRequirements {
  discordLinked: boolean
  followInstagram: boolean
  joinTelegram: boolean
  subscribeNewsletter: boolean
}

/**
 * New address form data
 */
export interface NewAddressForm {
  name: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  phone: string
}

/**
 * Form validation result
 */
export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

/**
 * Raffle form state
 */
export interface RaffleFormState {
  currentStep: FormStep
  selectedVariants: string[]
  selectedAddress: string
  shippingMethod: string
  newAddress: NewAddressForm
  raffleRequirements: RaffleRequirements
  isSubmitting: boolean
  isValidating: boolean
  fieldErrors: Record<string, string>
}

/**
 * Step component props base interface
 */
export interface StepComponentProps {
  formState: RaffleFormState
  onUpdateFormState: (updates: Partial<RaffleFormState>) => void
  onNext: () => void
  onPrev: () => void
  products: Product[]
  addresses: ShippingAddress[]
  shippingMethods: ShippingMethod[]
}

/**
 * Raffle entry container props
 */
export interface RaffleEntryContainerProps {
  onClose: () => void
  preSelectedProductId?: string
}

/**
 * Extended product variant with product name
 */
export interface ExtendedProductVariant extends ProductVariant {
  productName: string
}

/**
 * Raffle entry submission data
 */
export interface RaffleEntrySubmission {
  userId: string
  raffleId: string
  selectedVariants: string[]
  shippingAddress: ShippingAddress | NewAddressForm
  shippingMethod: string
  requirements: RaffleRequirements
  submittedAt: Date
}

/**
 * Raffle entry hooks return types
 */
export interface UseRaffleFormReturn {
  formState: RaffleFormState
  updateFormState: (updates: Partial<RaffleFormState>) => void
  resetForm: () => void
  validateCurrentStep: () => ValidationResult
}

export interface UseRaffleSubmissionReturn {
  submitEntry: (data: RaffleEntrySubmission) => Promise<boolean>
  isSubmitting: boolean
  submissionError: string | null
}

/**
 * Constants
 */
export const INITIAL_FORM_STATE: RaffleFormState = {
  currentStep: 'requirements',
  selectedVariants: [],
  selectedAddress: '',
  shippingMethod: 'express',
  newAddress: {
    name: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
    phone: ''
  },
  raffleRequirements: {
    discordLinked: false,
    followInstagram: false,
    joinTelegram: false,
    subscribeNewsletter: false
  },
  isSubmitting: false,
  isValidating: false,
  fieldErrors: {}
}

/**
 * Default shipping methods
 */
export const DEFAULT_SHIPPING_METHODS: ShippingMethod[] = [
  {
    id: 'express',
    name: 'Express Shipping',
    price: 15,
    description: '2-3 business days'
  },
  {
    id: 'standard',
    name: 'Standard Shipping',
    price: 8,
    description: '5-7 business days'
  },
  {
    id: 'economy',
    name: 'Economy Shipping',
    price: 5,
    description: '7-14 business days'
  }
]

/**
 * Step titles mapping
 */
export const STEP_TITLES: Record<FormStep, string> = {
  requirements: 'STEP 1. COMPLETE ENTRY REQUIREMENTS',
  products: 'STEP 2. SELECT YOUR PRODUCTS',
  address: 'STEP 3. ADD SHIPPING ADDRESS',
  information: 'STEP 3. SHIPPING INFORMATION',
  review: 'STEP 4. REVIEW & SUBMIT',
  success: 'ENTRY SUBMITTED SUCCESSFULLY'
}

/**
 * Step order for navigation
 */
export const STEP_ORDER: FormStep[] = ['requirements', 'products', 'address', 'information', 'review', 'success']
