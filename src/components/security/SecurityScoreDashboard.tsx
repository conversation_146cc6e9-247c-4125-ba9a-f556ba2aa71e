/**
 * Security Score Dashboard Component
 * 
 * Provides a comprehensive security score dashboard that evaluates user's account
 * security posture and provides actionable recommendations for improvement.
 * 
 * Features:
 * - Overall security score calculation
 * - Category-based security assessment
 * - Actionable improvement recommendations
 * - Visual progress indicators
 * - Quick action buttons for common security tasks
 * - Security best practices education
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  ShieldCheck,
  ShieldAlert,
  ShieldX,
  Key,
  Smartphone,
  Eye,
  EyeOff,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  Lock,
  Unlock,
  Zap,
  Target,
  Award,
  ArrowRight
} from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { getTimestamp, isWithinDays, isOlderThan, TIME_CONSTANTS } from '@/utils/dateUtils'
import Link from 'next/link'

interface SecurityScoreDashboardProps {
  profile: UserProfile | null
  className?: string
  onActionClick?: (action: string) => void
}

interface SecurityCategory {
  id: string
  name: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  score: number
  maxScore: number
  status: 'excellent' | 'good' | 'fair' | 'poor'
  description: string
  recommendations: SecurityRecommendation[]
}

interface SecurityRecommendation {
  id: string
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  difficulty: 'easy' | 'medium' | 'hard'
  action: string
  href?: string
  completed: boolean
}

/**
 * Calculate security score based on user profile
 */
const calculateSecurityScore = (profile: UserProfile | null): {
  overall: number
  categories: SecurityCategory[]
} => {
  if (!profile) {
    return {
      overall: 0,
      categories: []
    }
  }

  const categories: SecurityCategory[] = [
    {
      id: 'authentication',
      name: 'Authentication',
      icon: Key,
      score: 0,
      maxScore: 100,
      status: 'poor',
      description: 'Password strength and multi-factor authentication',
      recommendations: []
    },
    {
      id: 'devices',
      name: 'Device Security',
      icon: Smartphone,
      score: 0,
      maxScore: 100,
      status: 'poor',
      description: 'Trusted devices and session management',
      recommendations: []
    },
    {
      id: 'privacy',
      name: 'Privacy Controls',
      icon: Eye,
      score: 0,
      maxScore: 100,
      status: 'poor',
      description: 'Data visibility and sharing settings',
      recommendations: []
    },
    {
      id: 'activity',
      name: 'Account Activity',
      icon: Clock,
      score: 0,
      maxScore: 100,
      status: 'poor',
      description: 'Login monitoring and suspicious activity detection',
      recommendations: []
    }
  ]

  // Authentication Score (0-100)
  let authScore = 0
  const authRecommendations: SecurityRecommendation[] = []

  // MFA enabled (+40 points)
  if (profile.mfaEnabled) {
    authScore += 40
  } else {
    authRecommendations.push({
      id: 'enable-mfa',
      title: 'Enable Two-Factor Authentication',
      description: 'Add an extra layer of security to your account',
      impact: 'high',
      difficulty: 'easy',
      action: 'setup-mfa',
      href: '/profile/security#mfa',
      completed: false
    })
  }

  // Email verified (+20 points)
  if (profile.emailVerified) {
    authScore += 20
  } else {
    authRecommendations.push({
      id: 'verify-email',
      title: 'Verify Your Email Address',
      description: 'Confirm your email for account recovery',
      impact: 'medium',
      difficulty: 'easy',
      action: 'verify-email',
      completed: false
    })
  }

  // Phone verified (+15 points)
  if (profile.phoneVerified) {
    authScore += 15
  } else {
    authRecommendations.push({
      id: 'verify-phone',
      title: 'Add Phone Number',
      description: 'Enable SMS recovery and notifications',
      impact: 'medium',
      difficulty: 'easy',
      action: 'verify-phone',
      href: '/profile/contact',
      completed: false
    })
  }

  // Recent password change (+15 points)
  const lastPasswordChange = profile.lastPasswordChange
  if (lastPasswordChange && isWithinDays(lastPasswordChange, 90)) {
    authScore += 15
  } else {
    authRecommendations.push({
      id: 'update-password',
      title: 'Update Your Password',
      description: 'Use a strong, unique password',
      impact: 'high',
      difficulty: 'easy',
      action: 'update-password',
      href: '/profile/security#password',
      completed: false
    })
  }

  // Backup codes (+10 points)
  if (profile.backupCodes && profile.backupCodes.length > 0) {
    authScore += 10
  } else if (profile.mfaEnabled) {
    authRecommendations.push({
      id: 'backup-codes',
      title: 'Generate Backup Codes',
      description: 'Create recovery codes for account access',
      impact: 'medium',
      difficulty: 'easy',
      action: 'generate-backup-codes',
      href: '/profile/security#backup',
      completed: false
    })
  }

  categories[0].score = authScore
  categories[0].recommendations = authRecommendations
  categories[0].status = authScore >= 80 ? 'excellent' : authScore >= 60 ? 'good' : authScore >= 40 ? 'fair' : 'poor'

  // Device Security Score (0-100)
  let deviceScore = 0
  const deviceRecommendations: SecurityRecommendation[] = []

  // Trusted devices management (+30 points)
  if (profile.trustedDevices && profile.trustedDevices.length > 0) {
    deviceScore += 30
  }

  // Session timeout configured (+25 points)
  if (profile.privacy?.sessionTimeout && profile.privacy.sessionTimeout <= 60) {
    deviceScore += 25
  } else {
    deviceRecommendations.push({
      id: 'session-timeout',
      title: 'Set Session Timeout',
      description: 'Automatically log out after inactivity',
      impact: 'medium',
      difficulty: 'easy',
      action: 'set-session-timeout',
      href: '/profile/security#session',
      completed: false
    })
  }

  // Multiple device login control (+25 points)
  if (profile.privacy?.allowMultipleDeviceLogin === false) {
    deviceScore += 25
  } else {
    deviceRecommendations.push({
      id: 'limit-devices',
      title: 'Limit Device Access',
      description: 'Restrict simultaneous logins for better security',
      impact: 'medium',
      difficulty: 'easy',
      action: 'limit-devices',
      href: '/profile/security#devices',
      completed: false
    })
  }

  // Remember device disabled (+20 points)
  if (profile.privacy?.allowRememberDevice === false) {
    deviceScore += 20
  } else {
    deviceRecommendations.push({
      id: 'disable-remember',
      title: 'Disable Remember Device',
      description: 'Require authentication on each login',
      impact: 'low',
      difficulty: 'easy',
      action: 'disable-remember',
      href: '/profile/security#devices',
      completed: false
    })
  }

  categories[1].score = deviceScore
  categories[1].recommendations = deviceRecommendations
  categories[1].status = deviceScore >= 80 ? 'excellent' : deviceScore >= 60 ? 'good' : deviceScore >= 40 ? 'fair' : 'poor'

  // Privacy Controls Score (0-100)
  let privacyScore = 0
  const privacyRecommendations: SecurityRecommendation[] = []

  // Profile visibility set (+20 points)
  if (profile.privacy?.profileVisibility === 'private') {
    privacyScore += 20
  } else if (profile.privacy?.profileVisibility === 'friends') {
    privacyScore += 15
  } else {
    privacyRecommendations.push({
      id: 'profile-visibility',
      title: 'Review Profile Visibility',
      description: 'Control who can see your profile information',
      impact: 'medium',
      difficulty: 'easy',
      action: 'set-profile-visibility',
      href: '/profile/privacy',
      completed: false
    })
  }

  // Email/phone hidden (+15 points each)
  if (!profile.privacy?.showEmail) privacyScore += 15
  if (!profile.privacy?.showPhone) privacyScore += 15

  // Data sharing disabled (+25 points)
  if (!profile.privacy?.allowThirdPartySharing) {
    privacyScore += 25
  } else {
    privacyRecommendations.push({
      id: 'disable-sharing',
      title: 'Disable Third-Party Sharing',
      description: 'Prevent sharing your data with external services',
      impact: 'high',
      difficulty: 'easy',
      action: 'disable-sharing',
      href: '/profile/privacy#sharing',
      completed: false
    })
  }

  // Marketing analytics disabled (+15 points)
  if (!profile.privacy?.allowMarketingAnalytics) {
    privacyScore += 15
  }

  // Search engine indexing disabled (+10 points)
  if (!profile.privacy?.allowSearchEngineIndexing) {
    privacyScore += 10
  }

  categories[2].score = privacyScore
  categories[2].recommendations = privacyRecommendations
  categories[2].status = privacyScore >= 80 ? 'excellent' : privacyScore >= 60 ? 'good' : privacyScore >= 40 ? 'fair' : 'poor'

  // Account Activity Score (0-100)
  let activityScore = 50 // Base score for having an account
  const activityRecommendations: SecurityRecommendation[] = []

  // Recent login activity (+25 points)
  if (profile.lastLoginAt && isWithinDays(profile.lastLoginAt, 7)) {
    activityScore += 25
  }

  // Account age (+25 points for accounts older than 30 days)
  if (profile.createdAt && isOlderThan(profile.createdAt, 30)) {
    activityScore += 25
  }

  // Always recommend security review
  activityRecommendations.push({
    id: 'security-review',
    title: 'Review Security Log',
    description: 'Check recent account activity for suspicious behavior',
    impact: 'medium',
    difficulty: 'easy',
    action: 'review-activity',
    href: '/profile/security#activity',
    completed: false
  })

  categories[3].score = activityScore
  categories[3].recommendations = activityRecommendations
  categories[3].status = activityScore >= 80 ? 'excellent' : activityScore >= 60 ? 'good' : activityScore >= 40 ? 'fair' : 'poor'

  // Calculate overall score
  const overall = Math.round(categories.reduce((sum, cat) => sum + cat.score, 0) / categories.length)

  return { overall, categories }
}

/**
 * Security Score Indicator Component
 */
const SecurityScoreIndicator: React.FC<{
  score: number
  size?: 'sm' | 'md' | 'lg'
}> = ({ score, size = 'md' }) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400'
    if (score >= 60) return 'text-yellow-400'
    if (score >= 40) return 'text-orange-400'
    return 'text-red-400'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return ShieldCheck
    if (score >= 60) return Shield
    if (score >= 40) return ShieldAlert
    return ShieldX
  }

  const IconComponent = getScoreIcon(score)
  const sizeClasses = {
    sm: 'text-2xl',
    md: 'text-4xl',
    lg: 'text-6xl'
  }

  return (
    <div className="flex items-center space-x-3">
      <IconComponent size={size === 'lg' ? 48 : size === 'md' ? 32 : 24} className={getScoreColor(score)} />
      <div>
        <div className={`font-bold ${getScoreColor(score)} ${sizeClasses[size]}`}>
          {score}
        </div>
        <div className="text-gray-400 text-sm">Security Score</div>
      </div>
    </div>
  )
}

/**
 * Security Category Card Component
 */
const SecurityCategoryCard: React.FC<{
  category: SecurityCategory
  onActionClick: (action: string) => void
}> = ({ category, onActionClick }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-400 border-green-500'
      case 'good': return 'text-blue-400 border-blue-500'
      case 'fair': return 'text-yellow-400 border-yellow-500'
      case 'poor': return 'text-red-400 border-red-500'
      default: return 'text-gray-400 border-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return CheckCircle
      case 'good': return Shield
      case 'fair': return AlertTriangle
      case 'poor': return XCircle
      default: return Shield
    }
  }

  const StatusIcon = getStatusIcon(category.status)
  const highPriorityRecs = category.recommendations.filter(r => r.impact === 'high').slice(0, 2)

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`bg-gray-800 rounded-lg border-2 p-6 ${getStatusColor(category.status)}`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <category.icon size={24} className="text-gray-400" />
          <div>
            <h3 className="text-lg font-semibold text-white">{category.name}</h3>
            <p className="text-gray-400 text-sm">{category.description}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="flex items-center space-x-2">
            <StatusIcon size={20} className={getStatusColor(category.status).split(' ')[0]} />
            <span className="text-2xl font-bold text-white">{category.score}</span>
          </div>
          <div className="text-gray-400 text-sm">/ {category.maxScore}</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
        <motion.div
          className={`h-2 rounded-full ${getStatusColor(category.status).includes('green') ? 'bg-green-500' :
            getStatusColor(category.status).includes('blue') ? 'bg-blue-500' :
            getStatusColor(category.status).includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'
            }`}
          initial={{ width: 0 }}
          animate={{ width: `${(category.score / category.maxScore) * 100}%` }}
          transition={{ duration: 1, delay: 0.2 }}
        />
      </div>

      {/* Top Recommendations */}
      {highPriorityRecs.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-300">Quick Actions:</h4>
          {highPriorityRecs.map((rec) => (
            <div key={rec.id} className="flex items-center justify-between bg-gray-700/50 rounded-lg p-3">
              <div className="flex-1">
                <div className="text-sm font-medium text-white">{rec.title}</div>
                <div className="text-xs text-gray-400">{rec.description}</div>
              </div>
              {rec.href ? (
                <Link
                  href={rec.href}
                  className="ml-3 px-3 py-1 bg-accent-500 hover:bg-accent-600 text-white text-xs rounded-lg transition-colors flex items-center"
                >
                  Fix
                  <ArrowRight size={12} className="ml-1" />
                </Link>
              ) : (
                <button
                  onClick={() => onActionClick(rec.action)}
                  className="ml-3 px-3 py-1 bg-accent-500 hover:bg-accent-600 text-white text-xs rounded-lg transition-colors flex items-center"
                >
                  Fix
                  <ArrowRight size={12} className="ml-1" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}
    </motion.div>
  )
}

/**
 * Main Security Score Dashboard Component
 */
const SecurityScoreDashboard: React.FC<SecurityScoreDashboardProps> = ({
  profile,
  className = '',
  onActionClick = () => { }
}) => {
  const { overall, categories } = useMemo(() => calculateSecurityScore(profile), [profile])

  if (!profile) {
    return (
      <div className={`bg-gray-800 rounded-lg p-6 text-center ${className}`}>
        <Shield size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">Security Dashboard</h3>
        <p className="text-gray-400">Please log in to view your security score.</p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Score Header */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Security Dashboard</h2>
            <p className="text-gray-400">
              Your account security score based on current settings and activity.
            </p>
          </div>
          <SecurityScoreIndicator score={overall} size="lg" />
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          {categories.map((category) => (
            <div key={category.id} className="text-center">
              <category.icon size={24} className="mx-auto text-gray-400 mb-2" />
              <div className="text-lg font-semibold text-white">{category.score}%</div>
              <div className="text-xs text-gray-400">{category.name}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Security Categories */}
      <div className="grid md:grid-cols-2 gap-6">
        {categories.map((category) => (
          <SecurityCategoryCard
            key={category.id}
            category={category}
            onActionClick={onActionClick}
          />
        ))}
      </div>

      {/* Security Tips */}
      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Target size={24} className="text-blue-400 flex-shrink-0 mt-1" />
          <div>
            <h3 className="text-lg font-semibold text-blue-400 mb-2">Security Best Practices</h3>
            <ul className="text-gray-300 text-sm space-y-2">
              <li>• Use a unique, strong password for your Syndicaps account</li>
              <li>• Enable two-factor authentication for maximum security</li>
              <li>• Regularly review your account activity and trusted devices</li>
              <li>• Keep your contact information up to date for account recovery</li>
              <li>• Review and adjust your privacy settings periodically</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SecurityScoreDashboard
export type { SecurityScoreDashboardProps, SecurityCategory, SecurityRecommendation }
