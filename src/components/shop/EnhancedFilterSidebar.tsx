/**
 * Enhanced Filter Sidebar Component
 * 
 * Modern, intuitive filtering interface following 2024 UX best practices.
 * Designed to complement the Enhanced Shop Header with consistent visual language.
 * 
 * Features:
 * - Real-time filtering with result counts
 * - Visual filter elements (color swatches, icons)
 * - Smart filter presets and recommendations
 * - Mobile-optimized off-canvas design
 * - Accessibility-first implementation
 * - Smooth animations and micro-interactions
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 */

'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Sliders,
  Star,
  Crown,
  Timer,
  ShieldCheck,
  Palette,
  Package,
  DollarSign,
  Filter,
  RotateCcw,
  Sparkles,
  Trophy,
  Zap,
  Tag,
  Check,
  ArrowRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSearchTracking } from '@/hooks/useAnalytics'

export interface FilterOptions {
  categories: string[]
  availability: string[]
  priceRange: [number, number]
  colors: string[]
  materials: string[]
  compatibility: string[]
  features: string[]
  sortBy: string
}

export interface FilterPreset {
  id: string
  name: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  description: string
  filters: Partial<FilterOptions>
  popular?: boolean
  badge?: string
}

interface EnhancedFilterSidebarProps {
  isOpen: boolean
  onClose: () => void
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
  productCount: number
  isLoading?: boolean
  className?: string
}

const defaultFilters: FilterOptions = {
  categories: [],
  availability: [],
  priceRange: [0, 500],
  colors: [],
  materials: [],
  compatibility: [],
  features: [],
  sortBy: 'newest'
}

const filterPresets: FilterPreset[] = [
  {
    id: 'new-arrivals',
    name: 'New Arrivals',
    icon: Sparkles,
    description: 'Latest artisan creations',
    filters: { availability: ['new'], sortBy: 'newest' },
    popular: true,
    badge: 'Hot'
  },
  {
    id: 'limited-edition',
    name: 'Limited Edition',
    icon: Crown,
    description: 'Exclusive & rare finds',
    filters: { availability: ['limited'], features: ['limited-edition'] },
    popular: true,
    badge: 'Rare'
  },
  {
    id: 'featured-picks',
    name: 'Featured Picks',
    icon: Trophy,
    description: 'Editor\'s choice',
    filters: { features: ['featured'] },
    popular: true
  },
  {
    id: 'sale-items',
    name: 'Sale Items',
    icon: Zap,
    description: 'Best deals available',
    filters: { availability: ['sale'] },
    badge: 'Save'
  }
]

const categoryOptions = [
  { value: 'artisan', label: 'Artisan Keycaps', count: 156, icon: Crown },
  { value: 'resin', label: 'Resin Crafted', count: 89, icon: Sparkles },
  { value: 'sculpted', label: 'Sculpted Design', count: 67, icon: Package },
  { value: 'accessories', label: 'Accessories', count: 34, icon: Tag },
  { value: 'sets', label: 'Complete Sets', count: 23, icon: Package },
  { value: 'limited-edition', label: 'Limited Edition', count: 12, icon: Trophy }
]

const availabilityOptions = [
  { value: 'in-stock', label: 'In Stock', count: 234, color: 'text-green-400', bgColor: 'bg-green-500/10' },
  { value: 'new', label: 'New Arrivals', count: 45, color: 'text-blue-400', bgColor: 'bg-blue-500/10' },
  { value: 'limited', label: 'Limited Stock', count: 23, color: 'text-orange-400', bgColor: 'bg-orange-500/10' },
  { value: 'sale', label: 'On Sale', count: 67, color: 'text-red-400', bgColor: 'bg-red-500/10' },
  { value: 'pre-order', label: 'Pre-Order', count: 12, color: 'text-purple-400', bgColor: 'bg-purple-500/10' }
]

const colorOptions = [
  { value: 'red', label: 'Red', hex: '#EF4444', count: 45 },
  { value: 'blue', label: 'Blue', hex: '#3B82F6', count: 67 },
  { value: 'green', label: 'Green', hex: '#10B981', count: 34 },
  { value: 'purple', label: 'Purple', hex: '#8B5CF6', count: 56 },
  { value: 'black', label: 'Black', hex: '#1F2937', count: 89 },
  { value: 'white', label: 'White', hex: '#F9FAFB', count: 78 },
  { value: 'gold', label: 'Gold', hex: '#F59E0B', count: 23 },
  { value: 'silver', label: 'Silver', hex: '#6B7280', count: 34 }
]

export const EnhancedFilterSidebar: React.FC<EnhancedFilterSidebarProps> = ({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  productCount,
  isLoading = false,
  className = ''
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedSections, setExpandedSections] = useState({
    presets: true,
    categories: true,
    availability: true,
    colors: false,
    price: false,
    materials: false
  })
  const [showAllCategories, setShowAllCategories] = useState(false)
  const { trackSearch } = useSearchTracking()

  const sidebarVariants = {
    closed: {
      x: '-100%',
      opacity: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    open: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
        staggerChildren: 0.05
      }
    }
  }

  const itemVariants = {
    closed: { opacity: 0, x: -20 },
    open: { opacity: 1, x: 0 }
  }

  const handleFilterChange = useCallback((filterType: keyof FilterOptions, value: any) => {
    const newFilters = { ...filters, [filterType]: value }
    onFiltersChange(newFilters)
  }, [filters, onFiltersChange])

  const toggleArrayFilter = useCallback((filterType: keyof FilterOptions, value: string) => {
    const currentArray = filters[filterType] as string[]
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    handleFilterChange(filterType, newArray)
  }, [filters, handleFilterChange])

  const applyPreset = useCallback((preset: FilterPreset) => {
    const newFilters = { ...defaultFilters, ...preset.filters }
    onFiltersChange(newFilters)
  }, [onFiltersChange])

  const clearAllFilters = useCallback(() => {
    onFiltersChange(defaultFilters)
    setSearchQuery('')
  }, [onFiltersChange])

  const activeFilterCount = useMemo(() => {
    return Object.entries(filters).reduce((count, [key, value]) => {
      if (key === 'priceRange') {
        const [min, max] = value as [number, number]
        return count + (min > 0 || max < 500 ? 1 : 0)
      }
      return count + (Array.isArray(value) ? value.length : 0)
    }, 0)
  }, [filters])

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }))
  }

  const filteredCategories = categoryOptions.filter(cat =>
    cat.label.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const displayedCategories = showAllCategories 
    ? filteredCategories 
    : filteredCategories.slice(0, 6)

  return (
    <>
      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        variants={sidebarVariants}
        initial="closed"
        animate={isOpen ? "open" : "closed"}
        className={cn(
          'fixed top-0 left-0 h-full w-80 bg-gray-900/95 backdrop-blur-md',
          'border-r border-gray-800/50 z-50 overflow-y-auto',
          'lg:relative lg:w-full lg:h-auto lg:border-r-0',
          className
        )}
      >
        {/* Header */}
        <motion.div
          variants={itemVariants}
          className="sticky top-0 z-10 bg-gray-900/95 backdrop-blur-md border-b border-gray-800/50 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-accent-500/10 rounded-xl border border-accent-500/20">
                <Filter size={20} className="text-accent-400" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-white">Filters</h2>
                <p className="text-xs text-gray-400">
                  {productCount} products {activeFilterCount > 0 && `• ${activeFilterCount} active`}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="lg:hidden p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              <X size={18} />
            </button>
          </div>

          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search filters..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-800/50 text-white pl-10 pr-4 py-2.5 rounded-lg border border-gray-700/50 focus:border-accent-500/50 focus:outline-none text-sm"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                <X size={14} />
              </button>
            )}
          </div>
        </motion.div>

        {/* Content */}
        <div className="p-6 space-y-6">
          
          {/* Active Filters Summary */}
          {activeFilterCount > 0 && (
            <motion.div
              variants={itemVariants}
              className="bg-accent-500/10 border border-accent-500/20 rounded-xl p-4"
            >
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-accent-400">Active Filters</span>
                <button
                  onClick={clearAllFilters}
                  className="text-xs text-gray-400 hover:text-white flex items-center gap-1"
                >
                  <RotateCcw size={12} />
                  Clear All
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {/* Show active filter tags */}
                {filters.categories.map(cat => (
                  <span key={cat} className="text-xs bg-accent-500/20 text-accent-300 px-2 py-1 rounded-full">
                    {cat}
                  </span>
                ))}
                {filters.availability.map(avail => (
                  <span key={avail} className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">
                    {avail}
                  </span>
                ))}
              </div>
            </motion.div>
          )}

          {/* Quick Presets */}
          <motion.section variants={itemVariants}>
            <button
              onClick={() => toggleSection('presets')}
              className="w-full flex items-center justify-between mb-4 group"
            >
              <h3 className="text-white font-medium">Quick Filters</h3>
              <ChevronDown
                size={16}
                className={cn(
                  'text-gray-400 transition-transform',
                  expandedSections.presets && 'rotate-180'
                )}
              />
            </button>
            
            <AnimatePresence>
              {expandedSections.presets && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="grid grid-cols-2 gap-3 mb-6"
                >
                  {filterPresets.map((preset) => {
                    const Icon = preset.icon
                    return (
                      <motion.button
                        key={preset.id}
                        onClick={() => applyPreset(preset)}
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        className="relative p-3 bg-gray-800/50 hover:bg-gray-800 rounded-xl border border-gray-700/50 hover:border-accent-500/30 transition-all text-left group"
                      >
                        {preset.badge && (
                          <div className="absolute -top-1 -right-1 bg-accent-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                            {preset.badge}
                          </div>
                        )}
                        <div className="flex items-center gap-2 mb-2">
                          <Icon size={16} className="text-accent-400" />
                          <span className="text-sm font-medium text-white truncate">
                            {preset.name}
                          </span>
                        </div>
                        <p className="text-xs text-gray-400 line-clamp-2">
                          {preset.description}
                        </p>
                      </motion.button>
                    )
                  })}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.section>

          {/* Categories */}
          <motion.section variants={itemVariants}>
            <button
              onClick={() => toggleSection('categories')}
              className="w-full flex items-center justify-between mb-4"
            >
              <h3 className="text-white font-medium">Categories</h3>
              <ChevronDown
                size={16}
                className={cn(
                  'text-gray-400 transition-transform',
                  expandedSections.categories && 'rotate-180'
                )}
              />
            </button>
            
            <AnimatePresence>
              {expandedSections.categories && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="space-y-2 mb-6"
                >
                  {displayedCategories.map((category) => {
                    const Icon = category.icon
                    const isSelected = filters.categories.includes(category.value)
                    return (
                      <motion.label
                        key={category.value}
                        whileHover={{ x: 4 }}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-800/50 cursor-pointer group"
                      >
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <input
                              type="checkbox"
                              checked={isSelected}
                              onChange={() => toggleArrayFilter('categories', category.value)}
                              className="w-4 h-4 rounded border-gray-600 bg-gray-800 text-accent-500 focus:ring-accent-500 focus:ring-offset-gray-900"
                            />
                            {isSelected && (
                              <Check size={12} className="absolute inset-0 m-auto text-white pointer-events-none" />
                            )}
                          </div>
                          <Icon size={16} className="text-gray-400 group-hover:text-accent-400 transition-colors" />
                          <span className="text-sm text-gray-300 group-hover:text-white transition-colors">
                            {category.label}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded-full">
                          {category.count}
                        </span>
                      </motion.label>
                    )
                  })}
                  
                  {filteredCategories.length > 6 && (
                    <button
                      onClick={() => setShowAllCategories(!showAllCategories)}
                      className="w-full p-2 text-sm text-accent-400 hover:text-accent-300 flex items-center justify-center gap-1"
                    >
                      {showAllCategories ? 'Show Less' : `Show ${filteredCategories.length - 6} More`}
                      <ArrowRight size={12} className={cn(
                        'transition-transform',
                        showAllCategories && 'rotate-90'
                      )} />
                    </button>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.section>

          {/* Availability */}
          <motion.section variants={itemVariants}>
            <button
              onClick={() => toggleSection('availability')}
              className="w-full flex items-center justify-between mb-4"
            >
              <h3 className="text-white font-medium">Availability</h3>
              <ChevronDown
                size={16}
                className={cn(
                  'text-gray-400 transition-transform',
                  expandedSections.availability && 'rotate-180'
                )}
              />
            </button>
            
            <AnimatePresence>
              {expandedSections.availability && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="space-y-2 mb-6"
                >
                  {availabilityOptions.map((option) => {
                    const isSelected = filters.availability.includes(option.value)
                    return (
                      <motion.label
                        key={option.value}
                        whileHover={{ x: 4 }}
                        className={cn(
                          'flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all',
                          'hover:bg-gray-800/50',
                          isSelected && option.bgColor
                        )}
                      >
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <input
                              type="checkbox"
                              checked={isSelected}
                              onChange={() => toggleArrayFilter('availability', option.value)}
                              className="w-4 h-4 rounded border-gray-600 bg-gray-800 text-accent-500 focus:ring-accent-500"
                            />
                            {isSelected && (
                              <Check size={12} className="absolute inset-0 m-auto text-white pointer-events-none" />
                            )}
                          </div>
                          <div className={cn('w-2 h-2 rounded-full', option.bgColor)} />
                          <span className={cn(
                            'text-sm transition-colors',
                            isSelected ? option.color : 'text-gray-300 hover:text-white'
                          )}>
                            {option.label}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded-full">
                          {option.count}
                        </span>
                      </motion.label>
                    )
                  })}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.section>

          {/* Colors */}
          <motion.section variants={itemVariants}>
            <button
              onClick={() => toggleSection('colors')}
              className="w-full flex items-center justify-between mb-4"
            >
              <h3 className="text-white font-medium">Colors</h3>
              <ChevronDown
                size={16}
                className={cn(
                  'text-gray-400 transition-transform',
                  expandedSections.colors && 'rotate-180'
                )}
              />
            </button>
            
            <AnimatePresence>
              {expandedSections.colors && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="mb-6"
                >
                  <div className="grid grid-cols-4 gap-3">
                    {colorOptions.map((color) => {
                      const isSelected = filters.colors.includes(color.value)
                      return (
                        <motion.button
                          key={color.value}
                          onClick={() => toggleArrayFilter('colors', color.value)}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          className="relative group"
                          title={`${color.label} (${color.count})`}
                        >
                          <div
                            className={cn(
                              'w-12 h-12 rounded-lg border-2 transition-all',
                              isSelected 
                                ? 'border-accent-500 ring-2 ring-accent-500/30'
                                : 'border-gray-600 hover:border-gray-400'
                            )}
                            style={{ backgroundColor: color.hex }}
                          />
                          {isSelected && (
                            <Check size={16} className="absolute inset-0 m-auto text-white drop-shadow-lg" />
                          )}
                          <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            {color.label}
                          </span>
                        </motion.button>
                      )
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.section>

          {/* Price Range */}
          <motion.section variants={itemVariants}>
            <button
              onClick={() => toggleSection('price')}
              className="w-full flex items-center justify-between mb-4"
            >
              <h3 className="text-white font-medium">Price Range</h3>
              <ChevronDown
                size={16}
                className={cn(
                  'text-gray-400 transition-transform',
                  expandedSections.price && 'rotate-180'
                )}
              />
            </button>
            
            <AnimatePresence>
              {expandedSections.price && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="mb-6"
                >
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <input
                        type="number"
                        placeholder="Min"
                        value={filters.priceRange[0]}
                        onChange={(e) => handleFilterChange('priceRange', [+e.target.value, filters.priceRange[1]])}
                        className="flex-1 bg-gray-800 text-white px-3 py-2 rounded-lg border border-gray-700 focus:border-accent-500 focus:outline-none text-sm"
                      />
                      <span className="text-gray-400">to</span>
                      <input
                        type="number"
                        placeholder="Max"
                        value={filters.priceRange[1]}
                        onChange={(e) => handleFilterChange('priceRange', [filters.priceRange[0], +e.target.value])}
                        className="flex-1 bg-gray-800 text-white px-3 py-2 rounded-lg border border-gray-700 focus:border-accent-500 focus:outline-none text-sm"
                      />
                    </div>
                    
                    {/* Quick price ranges */}
                    <div className="grid grid-cols-3 gap-2">
                      {[
                        { label: 'Under $50', range: [0, 50] },
                        { label: '$50-$100', range: [50, 100] },
                        { label: '$100+', range: [100, 500] }
                      ].map(({ label, range }) => (
                        <button
                          key={label}
                          onClick={() => handleFilterChange('priceRange', range)}
                          className="text-xs bg-gray-800 hover:bg-accent-500/20 text-gray-300 hover:text-accent-400 px-3 py-2 rounded-lg transition-colors"
                        >
                          {label}
                        </button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.section>
        </div>

        {/* Footer */}
        <motion.div
          variants={itemVariants}
          className="sticky bottom-0 bg-gray-900/95 backdrop-blur-md border-t border-gray-800/50 p-6"
        >
          <div className="flex gap-3">
            <button
              onClick={clearAllFilters}
              disabled={activeFilterCount === 0}
              className="flex-1 py-3 px-4 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
            >
              Clear Filters
            </button>
            <button
              onClick={onClose}
              className="flex-2 py-3 px-6 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors text-sm font-medium lg:hidden"
            >
              Apply ({productCount})
            </button>
          </div>
        </motion.div>
      </motion.aside>
    </>
  )
}

export default EnhancedFilterSidebar