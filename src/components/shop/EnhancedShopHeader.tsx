/**
 * Enhanced Shop Header Component
 * 
 * Modern, engaging shop header following 2024 UX best practices.
 * Features dynamic stats, improved visual hierarchy, and better mobile experience.
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  Star, 
  TrendingUp, 
  Users, 
  Sparkles,
  ShoppingBag,
  Trophy,
  Zap
} from 'lucide-react'
import { ViewportReveal } from '../ui/ScrollAnimations'
import { cn } from '@/lib/utils'

interface ShopStats {
  totalProducts: number
  featuredProducts: number
  newArrivals: number
  onSale: number
}

interface EnhancedShopHeaderProps {
  stats: ShopStats
  isUserLoggedIn?: boolean
  className?: string
}

export const EnhancedShopHeader: React.FC<EnhancedShopHeaderProps> = ({
  stats,
  isUserLoggedIn = false,
  className = ''
}) => {
  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const statsCards = [
    {
      icon: Package,
      value: stats.totalProducts,
      label: 'Products',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/20',
      animation: { rotate: [0, 360] },
      animationConfig: { duration: 8, repeat: Infinity, ease: "linear" }
    },
    {
      icon: Sparkles,
      value: stats.newArrivals,
      label: 'New Arrivals',
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/20',
      animation: { scale: [1, 1.2, 1], rotate: [0, 5, -5, 0] },
      animationConfig: { duration: 3, repeat: Infinity, repeatDelay: 1 }
    },
    {
      icon: Trophy,
      value: stats.featuredProducts,
      label: 'Featured',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/10',
      borderColor: 'border-yellow-500/20',
      animation: { y: [0, -5, 0] },
      animationConfig: { duration: 2, repeat: Infinity, repeatDelay: 2 }
    },
    {
      icon: Zap,
      value: stats.onSale,
      label: 'On Sale',
      color: 'text-red-400',
      bgColor: 'bg-red-500/10',
      borderColor: 'border-red-500/20',
      animation: { scale: [1, 1.1, 1] },
      animationConfig: { duration: 2.5, repeat: Infinity, repeatDelay: 1.5 }
    }
  ]

  return (
    <motion.header
      variants={headerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        'relative overflow-hidden bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800',
        'border-b border-gray-800/50 backdrop-blur-sm',
        className
      )}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_49%,rgba(139,92,246,0.05)_50%,transparent_51%)] bg-[length:20px_20px]" />
      </div>

      <div className="relative container-custom py-12 lg:py-16">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
          
          {/* Main Title Section - 6 columns on large screens */}
          <motion.div 
            variants={itemVariants}
            className="lg:col-span-6 text-center lg:text-left"
          >
            <div className="space-y-4">
              {/* Brand Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center gap-2 bg-accent-500/10 border border-accent-500/20 rounded-full px-4 py-2 text-accent-400 text-sm font-medium"
              >
                <ShoppingBag size={16} />
                <span>Premium Artisan Collection</span>
              </motion.div>

              {/* Main Heading */}
              <div className="space-y-2">
                <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold">
                  <span className="text-white">Syndicaps</span>
                  <motion.span 
                    className="text-transparent bg-gradient-to-r from-accent-400 via-purple-400 to-pink-400 bg-clip-text block lg:inline lg:ml-3"
                    animate={{
                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                    }}
                    transition={{ 
                      duration: 3, 
                      repeat: Infinity, 
                      ease: "easeInOut" 
                    }}
                    style={{
                      backgroundSize: '200% 200%'
                    }}
                  >
                    Shop
                  </motion.span>
                </h1>
                
                {/* Tagline */}
                <motion.p 
                  className="text-lg lg:text-xl text-gray-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed"
                  variants={itemVariants}
                >
                  Discover premium artisan keycaps, mechanical keyboards, and custom accessories crafted by master artisans
                </motion.p>
              </div>

              {/* Trust Indicators */}
              <motion.div 
                variants={itemVariants}
                className="flex items-center justify-center lg:justify-start gap-6 text-sm text-gray-400"
              >
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>Premium Quality</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  <span>Worldwide Shipping</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                  <span>Expert Crafted</span>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Stats Grid - 6 columns on large screens */}
          <motion.div 
            variants={itemVariants}
            className="lg:col-span-6"
          >
            <div className="grid grid-cols-2 lg:grid-cols-2 gap-4 lg:gap-6">
              {statsCards.map((stat, index) => {
                const Icon = stat.icon
                return (
                  <motion.div
                    key={stat.label}
                    variants={itemVariants}
                    whileHover={{ 
                      scale: 1.05, 
                      y: -5,
                      transition: { type: "spring", stiffness: 300 }
                    }}
                    className={cn(
                      'relative group rounded-2xl border backdrop-blur-sm transition-all duration-300',
                      'hover:shadow-xl hover:shadow-accent-500/10',
                      stat.bgColor,
                      stat.borderColor,
                      'p-6'
                    )}
                  >
                    {/* Hover glow effect */}
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    <div className="relative space-y-3">
                      {/* Icon */}
                      <div className="flex items-center justify-between">
                        <motion.div
                          animate={stat.animation}
                          transition={stat.animationConfig}
                          className={cn(
                            'p-3 rounded-xl',
                            stat.bgColor,
                            'border',
                            stat.borderColor
                          )}
                        >
                          <Icon size={24} className={stat.color} />
                        </motion.div>
                        
                        {/* Trend indicator */}
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                          className="opacity-60"
                        >
                          <TrendingUp size={16} className="text-green-400" />
                        </motion.div>
                      </div>

                      {/* Value */}
                      <div className="space-y-1">
                        <motion.div 
                          className="text-2xl lg:text-3xl font-bold text-white"
                          initial={{ opacity: 0, scale: 0.5 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.3 + index * 0.1 }}
                        >
                          {stat.value}
                        </motion.div>
                        <div className="text-sm text-gray-400 font-medium">
                          {stat.label}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        </div>

        {/* User Status & Quick Info */}
        <motion.div 
          variants={itemVariants}
          className="mt-8 flex flex-col sm:flex-row items-center justify-between gap-4 text-sm"
        >
          <div className="flex items-center gap-6">
            <motion.div 
              className="flex items-center gap-2 text-gray-400"
              whileHover={{ scale: 1.05, color: '#10b981' }}
              transition={{ duration: 0.2 }}
            >
              <Users size={16} />
              <span>{isUserLoggedIn ? 'Welcome back, Member!' : 'Shopping as Guest'}</span>
            </motion.div>
            
            <motion.div 
              className="flex items-center gap-2 text-gray-400"
              whileHover={{ scale: 1.05, color: '#3b82f6' }}
              transition={{ duration: 0.2 }}
            >
              <TrendingUp size={16} />
              <span>Updated 2 hours ago</span>
            </motion.div>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center gap-3">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="text-accent-400 hover:text-accent-300 transition-colors"
            >
              View All Categories
            </motion.button>
            <span className="text-gray-600">•</span>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="text-accent-400 hover:text-accent-300 transition-colors"
            >
              New Arrivals
            </motion.button>
          </div>
        </motion.div>
      </div>
    </motion.header>
  )
}

export default EnhancedShopHeader