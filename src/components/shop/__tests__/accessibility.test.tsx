/**
 * Accessibility Tests for Shop Components
 * 
 * Comprehensive accessibility audit for shop components ensuring WCAG 2.1 AA compliance
 * and proper screen reader support.
 * 
 * <AUTHOR> Team
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { axe, toHaveNoViolations } from 'jest-axe'
import '@testing-library/jest-dom'
import { Product } from '../../../lib/firestore'

// Extend Jest matchers
expect.extend(toHaveNoViolations)

// Mock heavy dependencies to isolate component functionality
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    article: ({ children, ...props }: any) => <article {...props}>{children}</article>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>
  },
  useMotionValue: () => ({ set: jest.fn() }),
  useSpring: () => ({ set: jest.fn() }),
  AnimatePresence: ({ children }: any) => children
}))

// Mock Next.js Image component
jest.mock('next/image', () => {
  const MockImage = ({ src, alt, width, height, className, ...props }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      {...props}
    />
  )
  MockImage.displayName = 'Image'
  return MockImage
})

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Timer: ({ size, className }: any) => <div data-testid="timer-icon" className={className} style={{ width: size, height: size }}>Timer</div>,
  Heart: ({ size, className }: any) => <div data-testid="heart-icon" className={className} style={{ width: size, height: size }}>Heart</div>,
  Loader2: ({ size, className }: any) => <div data-testid="loader-icon" className={className} style={{ width: size, height: size }}>Loader</div>,
  Share2: ({ size, className }: any) => <div data-testid="share-icon" className={className} style={{ width: size, height: size }}>Share</div>,
  Star: ({ size, className }: any) => <div data-testid="star-icon" className={className} style={{ width: size, height: size }}>Star</div>,
  Eye: ({ size, className }: any) => <div data-testid="eye-icon" className={className} style={{ width: size, height: size }}>Eye</div>,
  ShoppingCart: ({ size, className }: any) => <div data-testid="cart-icon" className={className} style={{ width: size, height: size }}>Cart</div>,
  Gift: ({ size, className }: any) => <div data-testid="gift-icon" className={className} style={{ width: size, height: size }}>Gift</div>,
  ChevronDown: ({ size, className }: any) => <div data-testid="chevron-down-icon" className={className} style={{ width: size, height: size }}>ChevronDown</div>,
  ChevronUp: ({ size, className }: any) => <div data-testid="chevron-up-icon" className={className} style={{ width: size, height: size }}>ChevronUp</div>,
  Filter: ({ size, className }: any) => <div data-testid="filter-icon" className={className} style={{ width: size, height: size }}>Filter</div>,
  X: ({ size, className }: any) => <div data-testid="x-icon" className={className} style={{ width: size, height: size }}>X</div>,
  Search: ({ size, className }: any) => <div data-testid="search-icon" className={className} style={{ width: size, height: size }}>Search</div>,
  Sliders: ({ size, className }: any) => <div data-testid="sliders-icon" className={className} style={{ width: size, height: size }}>Sliders</div>,
  Crown: ({ size, className }: any) => <div data-testid="crown-icon" className={className} style={{ width: size, height: size }}>Crown</div>,
  ShieldCheck: ({ size, className }: any) => <div data-testid="shield-check-icon" className={className} style={{ width: size, height: size }}>ShieldCheck</div>,
  Palette: ({ size, className }: any) => <div data-testid="palette-icon" className={className} style={{ width: size, height: size }}>Palette</div>,
  Package: ({ size, className }: any) => <div data-testid="package-icon" className={className} style={{ width: size, height: size }}>Package</div>,
  DollarSign: ({ size, className }: any) => <div data-testid="dollar-sign-icon" className={className} style={{ width: size, height: size }}>DollarSign</div>,
  RotateCcw: ({ size, className }: any) => <div data-testid="rotate-ccw-icon" className={className} style={{ width: size, height: size }}>RotateCcw</div>,
  Sparkles: ({ size, className }: any) => <div data-testid="sparkles-icon" className={className} style={{ width: size, height: size }}>Sparkles</div>,
  Trophy: ({ size, className }: any) => <div data-testid="trophy-icon" className={className} style={{ width: size, height: size }}>Trophy</div>,
  Zap: ({ size, className }: any) => <div data-testid="zap-icon" className={className} style={{ width: size, height: size }}>Zap</div>,
  Tag: ({ size, className }: any) => <div data-testid="tag-icon" className={className} style={{ width: size, height: size }}>Tag</div>,
  Check: ({ size, className }: any) => <div data-testid="check-icon" className={className} style={{ width: size, height: size }}>Check</div>,
  ArrowRight: ({ size, className }: any) => <div data-testid="arrow-right-icon" className={className} style={{ width: size, height: size }}>ArrowRight</div>
}))

// Mock analytics to prevent external calls during tests
jest.mock('@/hooks/useAnalytics', () => ({
  useProductActionTracking: () => ({ trackAction: jest.fn() }),
  useFunnelTracking: () => ({ trackFunnelStep: jest.fn() }),
  useSearchTracking: () => ({ trackSearch: jest.fn() })
}))

// Mock utils
jest.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' ')
}))

// Mock the EnhancedFilterSidebar component for accessibility testing
jest.mock('../EnhancedFilterSidebar', () => ({
  __esModule: true,
  default: ({ isOpen, children, ...props }: any) => (
    <div data-testid="enhanced-filter-sidebar" data-open={isOpen} {...props}>
      <h2>Filters</h2>
      {children}
      <div>Mock Enhanced Filter Sidebar</div>
    </div>
  )
}))

// Import components after mocking dependencies
import ProductCard from '../../../components/products/ProductCard'
import EnhancedFilterSidebar from '../EnhancedFilterSidebar'
import {
  MagneticButton,
  RippleEffect,
  FloatingActionButton,
  PulseNotification
} from '../../../components/ui/MicroInteractions'

// Mock product data
const mockProduct: Product = {
  id: 'a11y-test-product',
  name: 'Accessible Artisan Keycap',
  description: 'A beautifully crafted keycap with excellent accessibility features',
  price: 49.99,
  image: 'https://example.com/keycap.jpg',
  category: 'Artisan',
  stock: 10,
  soldOut: false,
  featured: true,
  isRaffle: false,
  tags: ['premium', 'accessible'],
  createdAt: { toDate: () => new Date() } as any,
  updatedAt: { toDate: () => new Date() } as any
}

const defaultFilters = {
  categories: [],
  availability: [],
  priceRange: [0, 500] as [number, number],
  colors: [],
  materials: [],
  compatibility: [],
  features: [],
  sortBy: 'newest' as const
}

describe('Accessibility Tests', () => {
  describe('ProductCard Accessibility', () => {
    it('should not have any accessibility violations', async () => {
      const { container } = render(<ProductCard product={mockProduct} />)
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('has proper semantic structure', () => {
      render(<ProductCard product={mockProduct} />)
      
      // Should have article element for semantic meaning
      expect(screen.getByRole('article')).toBeInTheDocument()
      
      // Should have proper heading
      const productTitle = screen.getByRole('heading', { level: 3 })
      expect(productTitle).toBeInTheDocument()
      expect(productTitle).toHaveTextContent('Accessible Artisan Keycap')
    })

    it('has descriptive link labels', () => {
      render(<ProductCard product={mockProduct} />)
      
      const productLink = screen.getByLabelText('View details for Accessible Artisan Keycap')
      expect(productLink).toBeInTheDocument()
      expect(productLink).toHaveAttribute('href', '/shop/a11y-test-product')
    })

    it('has accessible button labels', () => {
      render(<ProductCard product={mockProduct} />)
      
      const addToCartButton = screen.getByRole('button', { name: /add to cart/i })
      expect(addToCartButton).toBeInTheDocument()
      expect(addToCartButton).not.toBeDisabled()
    })

    it('provides alternative text for images', () => {
      render(<ProductCard product={mockProduct} />)
      
      const productImage = screen.getByRole('img')
      expect(productImage).toHaveAttribute('alt', expect.stringContaining('Accessible Artisan Keycap'))
    })

    it('has proper focus management', async () => {
      const user = userEvent.setup()
      render(<ProductCard product={mockProduct} />)
      
      // Tab through focusable elements
      await user.tab()
      
      // First focusable element should be the product link
      const productLink = screen.getByLabelText('View details for Accessible Artisan Keycap')
      expect(productLink).toHaveFocus()
      
      // Continue tabbing to button
      await user.tab()
      const button = screen.getByRole('button', { name: /add to cart/i })
      expect(button).toHaveFocus()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      render(<ProductCard product={mockProduct} />)
      
      const button = screen.getByRole('button', { name: /add to cart/i })
      button.focus()
      
      // Should be activatable with Enter
      await user.keyboard('{Enter}')
      expect(button).toBeInTheDocument()
      
      // Should be activatable with Space
      await user.keyboard(' ')
      expect(button).toBeInTheDocument()
    })

    it('has proper color contrast', () => {
      const { container } = render(<ProductCard product={mockProduct} />)
      
      // Check that text elements have sufficient contrast
      const productTitle = screen.getByText('Accessible Artisan Keycap')
      const computedStyle = window.getComputedStyle(productTitle)
      
      // Should have light text on dark background (high contrast)
      expect(computedStyle.color).toBeDefined()
    })

    it('handles disabled states accessibly', () => {
      const soldOutProduct = { ...mockProduct, soldOut: true }
      render(<ProductCard product={soldOutProduct} />)
      
      const button = screen.getByRole('button', { name: /out of stock/i })
      expect(button).toBeDisabled()
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })

    it('provides status information for screen readers', () => {
      render(<ProductCard product={mockProduct} />)
      
      // Stock information should be available to screen readers
      const stockInfo = screen.getByText('Only 10 left')
      expect(stockInfo).toBeInTheDocument()
    })
  })

  describe('EnhancedFilterSidebar Accessibility', () => {
    const mockProps = {
      isOpen: true,
      onClose: jest.fn(),
      filters: defaultFilters,
      onFiltersChange: jest.fn(),
      productCount: 42,
      isLoading: false
    }

    it('should not have any accessibility violations', async () => {
      const { container } = render(<EnhancedFilterSidebar {...mockProps} />)
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('has proper heading structure', () => {
      render(<EnhancedFilterSidebar {...mockProps} />)
      
      // Main heading
      expect(screen.getByText('Filters')).toBeInTheDocument()
      
      // Section headings
      expect(screen.getByText('Quick Filters')).toBeInTheDocument()
      expect(screen.getByText('Categories')).toBeInTheDocument()
      expect(screen.getByText('Availability')).toBeInTheDocument()
    })

    it('has accessible form controls', () => {
      render(<EnhancedFilterSidebar {...mockProps} />)
      
      // Search input should have proper label
      const searchInput = screen.getByRole('searchbox')
      expect(searchInput).toBeInTheDocument()
      expect(searchInput).toHaveAttribute('placeholder', 'Search filters...')
      
      // Checkboxes should have proper labels
      const artisanCheckbox = screen.getByLabelText('Artisan Keycaps (156)')
      expect(artisanCheckbox).toBeInTheDocument()
      expect(artisanCheckbox).toHaveAttribute('type', 'checkbox')
    })

    it('supports keyboard navigation through filters', async () => {
      const user = userEvent.setup()
      render(<EnhancedFilterSidebar {...mockProps} />)
      
      // Tab to search input
      await user.tab()
      const searchInput = screen.getByRole('searchbox')
      expect(searchInput).toHaveFocus()
      
      // Continue tabbing through interactive elements
      await user.tab()
      // Should focus on next interactive element
    })

    it('has proper ARIA labels for complex controls', () => {
      render(<EnhancedFilterSidebar {...mockProps} />)
      
      // Price range sliders should have labels
      const sliders = screen.getAllByRole('slider')
      sliders.forEach(slider => {
        expect(slider).toHaveAccessibleName()
      })
    })

    it('provides feedback for filter changes', async () => {
      const user = userEvent.setup()
      const onFiltersChange = jest.fn()
      
      render(
        <EnhancedFilterSidebar 
          {...mockProps} 
          onFiltersChange={onFiltersChange}
        />
      )
      
      const checkbox = screen.getByLabelText('Artisan Keycaps (156)')
      await user.click(checkbox)
      
      expect(onFiltersChange).toHaveBeenCalled()
    })

    it('has accessible close button', () => {
      render(<EnhancedFilterSidebar {...mockProps} />)
      
      const closeButton = screen.getByTitle('Clear all filters')
      expect(closeButton).toBeInTheDocument()
      expect(closeButton).toHaveAccessibleName()
    })

    it('manages focus when opening and closing', async () => {
      const user = userEvent.setup()
      const onClose = jest.fn()
      
      const { rerender } = render(
        <EnhancedFilterSidebar {...mockProps} onClose={onClose} />
      )
      
      // Focus should be managed when sidebar opens
      const searchInput = screen.getByRole('searchbox')
      expect(searchInput).toBeInTheDocument()
      
      // Close sidebar
      rerender(
        <EnhancedFilterSidebar {...mockProps} isOpen={false} onClose={onClose} />
      )
      
      // Should not have focus issues
    })
  })

  describe('Micro-Interactions Accessibility', () => {
    it('MagneticButton maintains accessibility', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      const { container } = render(
        <MagneticButton onClick={handleClick}>
          Accessible Magnetic Button
        </MagneticButton>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
      
      const button = screen.getByRole('button')
      expect(button).toHaveAccessibleName('Accessible Magnetic Button')
      
      // Should work with keyboard
      await user.click(button)
      expect(handleClick).toHaveBeenCalled()
    })

    it('RippleEffect maintains button semantics', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      const { container } = render(
        <RippleEffect onClick={handleClick}>
          Ripple Button
        </RippleEffect>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
      
      const button = screen.getByRole('button')
      await user.click(button)
      expect(handleClick).toHaveBeenCalled()
    })

    it('FloatingActionButton has proper tooltip accessibility', async () => {
      const user = userEvent.setup()
      
      const { container } = render(
        <FloatingActionButton tooltip="Accessible tooltip">
          <span>FAB</span>
        </FloatingActionButton>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
      
      const button = screen.getByRole('button')
      await user.hover(button)
      
      // Tooltip should be accessible
      expect(screen.getByText('Accessible tooltip')).toBeInTheDocument()
    })

    it('PulseNotification provides count information accessibly', () => {
      const { container } = render(
        <PulseNotification count={5}>
          <button aria-label="Notifications">Bell</button>
        </PulseNotification>
      )
      
      expect(screen.getByLabelText('Notifications')).toBeInTheDocument()
      expect(screen.getByText('5')).toBeInTheDocument()
    })
  })

  describe('Screen Reader Support', () => {
    it('provides meaningful content for screen readers', () => {
      render(<ProductCard product={mockProduct} />)
      
      // Product information should be readable by screen readers
      expect(screen.getByText('Accessible Artisan Keycap')).toBeInTheDocument()
      expect(screen.getByText('$49.99')).toBeInTheDocument()
      expect(screen.getByText('Artisan')).toBeInTheDocument()
    })

    it('has proper landmark regions', () => {
      render(<ProductCard product={mockProduct} />)
      
      // Product card should be in an article landmark
      const article = screen.getByRole('article')
      expect(article).toBeInTheDocument()
    })

    it('provides status updates for dynamic content', () => {
      const defaultProps = {
        isOpen: true,
        onClose: jest.fn(),
        filters: defaultFilters,
        onFiltersChange: jest.fn(),
        productCount: 42,
        isLoading: false
      }
      
      const { rerender } = render(
        <EnhancedFilterSidebar {...defaultProps} />
      )
      
      expect(screen.getByText('(42 items)')).toBeInTheDocument()
      
      // Update product count
      rerender(
        <EnhancedFilterSidebar {...defaultProps} productCount={15} />
      )
      
      expect(screen.getByText('(15 items)')).toBeInTheDocument()
    })
  })

  describe('High Contrast Mode Support', () => {
    it('works in high contrast mode', () => {
      // Simulate high contrast mode
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-contrast: high)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      })
      
      render(<ProductCard product={mockProduct} />)
      
      // Should render properly in high contrast mode
      expect(screen.getByText('Accessible Artisan Keycap')).toBeInTheDocument()
    })
  })

  describe('Reduced Motion Support', () => {
    it('respects prefers-reduced-motion', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      })
      
      render(<ProductCard product={mockProduct} enableHover={true} />)
      
      // Should still be functional with reduced motion
      expect(screen.getByText('Accessible Artisan Keycap')).toBeInTheDocument()
    })
  })

  describe('Touch Device Accessibility', () => {
    it('has appropriate touch targets', () => {
      render(<ProductCard product={mockProduct} />)
      
      const button = screen.getByRole('button', { name: /add to cart/i })
      const computedStyle = window.getComputedStyle(button)
      
      // Button should have minimum touch target size (44px)
      expect(parseInt(computedStyle.minHeight || '0')).toBeGreaterThanOrEqual(44)
    })

    it('supports touch interactions', async () => {
      const user = userEvent.setup()
      render(<ProductCard product={mockProduct} />)
      
      const button = screen.getByRole('button', { name: /add to cart/i })
      
      // Should work with touch events
      await user.click(button)
      expect(button).toBeInTheDocument()
    })
  })

  describe('Error States Accessibility', () => {
    it('communicates errors accessibly', () => {
      const errorProduct = { ...mockProduct, soldOut: true }
      render(<ProductCard product={errorProduct} />)
      
      // Error state should be communicated clearly
      expect(screen.getByText('OUT OF STOCK')).toBeInTheDocument()
      
      const button = screen.getByRole('button', { name: /out of stock/i })
      expect(button).toBeDisabled()
    })

    it('provides loading state information', () => {
      const mockProps = {
        isOpen: true,
        onClose: jest.fn(),
        filters: defaultFilters,
        onFiltersChange: jest.fn(),
        productCount: 42,
        isLoading: true
      }
      
      render(<EnhancedFilterSidebar {...mockProps} />)
      
      expect(screen.getByText('Applying...')).toBeInTheDocument()
    })
  })
})