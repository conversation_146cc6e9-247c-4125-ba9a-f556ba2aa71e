/**
 * Bundle Size Analysis Tool
 * 
 * Analyzes bundle size and performance implications of shop components.
 * Helps identify optimization opportunities and track size regressions.
 * 
 * <AUTHOR> Team
 */

const fs = require('fs')
const path = require('path')

// Component size analysis
const analyzeComponentSize = (componentPath) => {
  try {
    const fullPath = path.resolve(__dirname, '..', componentPath)
    const stats = fs.statSync(fullPath)
    const content = fs.readFileSync(fullPath, 'utf8')
    
    // Count lines, imports, and complexity
    const lines = content.split('\n').length
    const imports = (content.match(/^import/gm) || []).length
    const exports = (content.match(/^export/gm) || []).length
    const components = (content.match(/const \w+: React\.FC|function \w+\(/g) || []).length
    
    return {
      file: componentPath,
      sizeBytes: stats.size,
      sizeKB: (stats.size / 1024).toFixed(2),
      lines,
      imports,
      exports,
      components,
      lastModified: stats.mtime
    }
  } catch (error) {
    console.error(`Error analyzing ${componentPath}:`, error.message)
    return null
  }
}

// Analyze all shop components
const analyzeShopComponents = () => {
  const components = [
    '../ShopComponent.tsx',
    '../AdvancedFilterSidebar.tsx',
    '../../products/EnhancedProductCard.tsx',
    '../../ui/MicroInteractions.tsx',
    '../../ui/LoadingAnimations.tsx',
    '../../ui/ScrollAnimations.tsx',
    '../../ui/ProductBadge.tsx',
    '../../ui/ProductImageGallery.tsx',
    '../../ui/QuickActions.tsx'
  ]

  console.log('🔍 Shop Components Bundle Analysis')
  console.log('=' * 50)

  const results = components
    .map(analyzeComponentSize)
    .filter(Boolean)
    .sort((a, b) => b.sizeBytes - a.sizeBytes)

  let totalSize = 0
  let totalLines = 0

  results.forEach(result => {
    totalSize += result.sizeBytes
    totalLines += result.lines
    
    console.log(`📄 ${result.file}`)
    console.log(`   Size: ${result.sizeKB} KB (${result.sizeBytes} bytes)`)
    console.log(`   Lines: ${result.lines}`)
    console.log(`   Imports: ${result.imports}`)
    console.log(`   Components: ${result.components}`)
    console.log(`   Modified: ${result.lastModified.toLocaleDateString()}`)
    console.log('')
  })

  console.log('📊 Summary')
  console.log(`Total Size: ${(totalSize / 1024).toFixed(2)} KB`)
  console.log(`Total Lines: ${totalLines}`)
  console.log(`Average Size: ${(totalSize / results.length / 1024).toFixed(2)} KB per component`)
  console.log('')

  // Check for potential optimization opportunities
  console.log('⚡ Optimization Opportunities')
  
  results.forEach(result => {
    const suggestions = []
    
    if (result.sizeKB > 50) {
      suggestions.push('Consider splitting into smaller components')
    }
    
    if (result.lines > 500) {
      suggestions.push('High line count - consider refactoring')
    }
    
    if (result.imports > 20) {
      suggestions.push('High import count - check for unnecessary dependencies')
    }
    
    if (suggestions.length > 0) {
      console.log(`🔧 ${result.file}:`)
      suggestions.forEach(suggestion => console.log(`   - ${suggestion}`))
      console.log('')
    }
  })

  return results
}

// Dependency analysis
const analyzeDependencies = () => {
  console.log('📦 Dependency Analysis')
  console.log('=' * 30)

  try {
    const packagePath = path.resolve(__dirname, '../../../../package.json')
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    
    // Check for heavy dependencies that might affect shop performance
    const heavyDeps = [
      'framer-motion',
      '@testing-library/react',
      'react-hot-toast',
      'lucide-react'
    ]

    console.log('Heavy dependencies used in shop components:')
    heavyDeps.forEach(dep => {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        const version = packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]
        console.log(`   ${dep}: ${version}`)
      }
    })
    
    console.log('')
  } catch (error) {
    console.error('Error analyzing dependencies:', error.message)
  }
}

// Performance recommendations
const generateRecommendations = (results) => {
  console.log('🚀 Performance Recommendations')
  console.log('=' * 35)

  // Calculate metrics
  const largestComponent = results[0]
  const totalSize = results.reduce((sum, r) => sum + r.sizeBytes, 0)
  const avgSize = totalSize / results.length

  console.log('General recommendations:')
  
  if (totalSize > 200 * 1024) { // 200KB
    console.log('   - Consider code splitting for shop components')
  }
  
  if (largestComponent.sizeKB > 30) {
    console.log(`   - ${largestComponent.file} is quite large (${largestComponent.sizeKB}KB)`)
    console.log('     Consider breaking it into smaller, focused components')
  }
  
  console.log('   - Implement lazy loading for non-critical components')
  console.log('   - Use React.memo() for components with stable props')
  console.log('   - Consider using dynamic imports for heavy animations')
  console.log('   - Optimize images with next/image for better performance')
  console.log('')
  
  console.log('Bundle optimization:')
  console.log('   - Tree-shake unused exports')
  console.log('   - Use babel-plugin-transform-imports for selective imports')
  console.log('   - Consider replacing heavy dependencies with lighter alternatives')
  console.log('   - Enable gzip compression on production builds')
  console.log('')
}

// Run analysis
if (require.main === module) {
  console.log('🏪 Syndicaps Shop Performance Analysis')
  console.log('Started at:', new Date().toISOString())
  console.log('')
  
  const results = analyzeShopComponents()
  analyzeDependencies()
  generateRecommendations(results)
  
  console.log('Analysis completed at:', new Date().toISOString())
}

module.exports = {
  analyzeComponentSize,
  analyzeShopComponents,
  analyzeDependencies,
  generateRecommendations
}