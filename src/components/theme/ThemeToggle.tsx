/**
 * Theme Toggle Component
 * 
 * Interactive theme switcher with mode and accessibility options.
 * Supports light/dark/system modes and high contrast settings.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useTheme, useThemeMode, useThemeAccessibility } from '../../lib/theme/ThemeProvider'
import { ThemeMode, ColorScheme } from '../../lib/theme/themeConfig'

// ===== TYPES =====

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown' | 'modal'
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  showAccessibilityOptions?: boolean
  className?: string
}

// ===== MAIN COMPONENT =====

export default function ThemeToggle({
  variant = 'button',
  size = 'md',
  showLabel = false,
  showAccessibilityOptions = false,
  className = ''
}: ThemeToggleProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  if (variant === 'button') {
    return (
      <ThemeToggleButton
        size={size}
        showLabel={showLabel}
        className={className}
      />
    )
  }
  
  if (variant === 'dropdown') {
    return (
      <ThemeDropdown
        isOpen={isOpen}
        onToggle={() => setIsOpen(!isOpen)}
        onClose={() => setIsOpen(false)}
        size={size}
        showLabel={showLabel}
        showAccessibilityOptions={showAccessibilityOptions}
        className={className}
      />
    )
  }

  if (variant === 'modal') {
    return (
      <ThemeModal
        isOpen={isOpen}
        onToggle={() => setIsOpen(!isOpen)}
        onClose={() => setIsOpen(false)}
        className={className}
      />
    )
  }

  return null
}

// ===== SIMPLE TOGGLE BUTTON =====

function ThemeToggleButton({
  size,
  showLabel,
  className
}: {
  size: 'sm' | 'md' | 'lg'
  showLabel: boolean
  className: string
}) {
  const { mode, toggleMode, isDark } = useThemeMode()
  const { colors } = useTheme()

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  }

  const getIcon = () => {
    if (mode === 'system') return '🌓'
    return isDark ? '🌙' : '☀️'
  }

  const getLabel = () => {
    if (mode === 'system') return 'System'
    return isDark ? 'Dark' : 'Light'
  }

  return (
    <motion.button
      onClick={toggleMode}
      className={`
        ${sizeClasses[size]}
        inline-flex items-center justify-center
        rounded-lg border border-gray-300 dark:border-gray-600
        bg-white dark:bg-gray-800
        text-gray-900 dark:text-gray-100
        shadow-sm hover:shadow-md
        transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2
        ${className}
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
      title={`Current: ${getLabel()}`}
    >
      <motion.span
        key={mode}
        initial={{ rotate: -90, opacity: 0 }}
        animate={{ rotate: 0, opacity: 1 }}
        exit={{ rotate: 90, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        {getIcon()}
      </motion.span>
      {showLabel && (
        <span className="ml-2 text-sm font-medium">
          {getLabel()}
        </span>
      )}
    </motion.button>
  )
}

// ===== DROPDOWN COMPONENT =====

function ThemeDropdown({
  isOpen,
  onToggle,
  onClose,
  size,
  showLabel,
  showAccessibilityOptions,
  className
}: {
  isOpen: boolean
  onToggle: () => void
  onClose: () => void
  size: 'sm' | 'md' | 'lg'
  showLabel: boolean
  showAccessibilityOptions: boolean
  className: string
}) {
  const { mode, setMode, isDark } = useThemeMode()
  const { colorScheme, setColorScheme } = useThemeAccessibility()

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  }

  const modes: { value: ThemeMode; label: string; icon: string }[] = [
    { value: 'light', label: 'Light', icon: '☀️' },
    { value: 'dark', label: 'Dark', icon: '🌙' },
    { value: 'system', label: 'System', icon: '🌓' }
  ]

  const colorSchemes: { value: ColorScheme; label: string; description: string }[] = [
    { value: 'default', label: 'Default', description: 'Standard colors' },
    { value: 'high-contrast', label: 'High Contrast', description: 'Better accessibility' },
    { value: 'colorblind-friendly', label: 'Colorblind Friendly', description: 'Optimized palette' }
  ]

  const handleModeChange = (newMode: ThemeMode) => {
    setMode(newMode)
    if (!showAccessibilityOptions) {
      onClose()
    }
  }

  const handleColorSchemeChange = (newScheme: ColorScheme) => {
    setColorScheme(newScheme)
    onClose()
  }

  // Close dropdown when clicking outside
  React.useEffect(() => {
    if (!isOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-theme-dropdown]')) {
        onClose()
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isOpen, onClose])

  return (
    <div className={`relative ${className}`} data-theme-dropdown>
      <motion.button
        onClick={onToggle}
        className={`
          ${sizeClasses[size]}
          inline-flex items-center justify-center
          rounded-lg border border-gray-300 dark:border-gray-600
          bg-white dark:bg-gray-800
          text-gray-900 dark:text-gray-100
          shadow-sm hover:shadow-md
          transition-all duration-200
          focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2
        `}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label="Theme options"
        aria-expanded={isOpen}
        aria-haspopup="menu"
      >
        <span>{isDark ? '🌙' : '☀️'}</span>
        {showLabel && (
          <span className="ml-2 text-sm font-medium">
            Theme
          </span>
        )}
        <svg 
          className={`ml-1 w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
            role="menu"
            aria-orientation="vertical"
          >
            <div className="p-4">
              {/* Theme Mode Selection */}
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Theme Mode
                </h3>
                <div className="space-y-1">
                  {modes.map((modeOption) => (
                    <button
                      key={modeOption.value}
                      onClick={() => handleModeChange(modeOption.value)}
                      className={`
                        w-full flex items-center px-3 py-2 text-sm rounded-md
                        transition-colors duration-150
                        ${mode === modeOption.value
                          ? 'bg-accent-100 dark:bg-accent-900/30 text-accent-700 dark:text-accent-300'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                        }
                      `}
                      role="menuitem"
                    >
                      <span className="mr-3">{modeOption.icon}</span>
                      <span className="font-medium">{modeOption.label}</span>
                      {mode === modeOption.value && (
                        <span className="ml-auto text-accent-500">✓</span>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Accessibility Options */}
              {showAccessibilityOptions && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Accessibility
                  </h3>
                  <div className="space-y-1">
                    {colorSchemes.map((schemeOption) => (
                      <button
                        key={schemeOption.value}
                        onClick={() => handleColorSchemeChange(schemeOption.value)}
                        className={`
                          w-full text-left px-3 py-2 text-sm rounded-md
                          transition-colors duration-150
                          ${colorScheme === schemeOption.value
                            ? 'bg-accent-100 dark:bg-accent-900/30 text-accent-700 dark:text-accent-300'
                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                          }
                        `}
                        role="menuitem"
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{schemeOption.label}</span>
                          {colorScheme === schemeOption.value && (
                            <span className="text-accent-500">✓</span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {schemeOption.description}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// ===== MODAL COMPONENT =====

function ThemeModal({
  isOpen,
  onToggle,
  onClose,
  className
}: {
  isOpen: boolean
  onToggle: () => void
  onClose: () => void
  className: string
}) {
  const { mode, setMode, isDark } = useThemeMode()
  const { colorScheme, setColorScheme, prefersReducedMotion } = useThemeAccessibility()
  const { resetToDefaults } = useTheme()

  const modes: { value: ThemeMode; label: string; icon: string; description: string }[] = [
    { value: 'light', label: 'Light', icon: '☀️', description: 'Always use light theme' },
    { value: 'dark', label: 'Dark', icon: '🌙', description: 'Always use dark theme' },
    { value: 'system', label: 'System', icon: '🌓', description: 'Follow system preference' }
  ]

  const colorSchemes: { value: ColorScheme; label: string; icon: string; description: string }[] = [
    { value: 'default', label: 'Default', icon: '🎨', description: 'Standard color palette' },
    { value: 'high-contrast', label: 'High Contrast', icon: '⚡', description: 'Enhanced contrast for better readability' },
    { value: 'colorblind-friendly', label: 'Colorblind Friendly', icon: '👁️', description: 'Optimized for color vision differences' }
  ]

  return (
    <>
      {/* Trigger Button */}
      <motion.button
        onClick={onToggle}
        className={`
          inline-flex items-center px-4 py-2 text-sm font-medium
          rounded-lg border border-gray-300 dark:border-gray-600
          bg-white dark:bg-gray-800
          text-gray-900 dark:text-gray-100
          shadow-sm hover:shadow-md
          transition-all duration-200
          focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2
          ${className}
        `}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <span className="mr-2">{isDark ? '🌙' : '☀️'}</span>
        Theme Settings
      </motion.button>

      {/* Modal */}
      <AnimatePresence>
        {isOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black bg-opacity-50"
              onClick={onClose}
            />

            {/* Modal Content */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: prefersReducedMotion ? 0 : 0.2 }}
              className="relative w-full max-w-md bg-white dark:bg-gray-800 rounded-xl shadow-xl"
              role="dialog"
              aria-modal="true"
              aria-labelledby="theme-modal-title"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 id="theme-modal-title" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Theme Settings
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  aria-label="Close theme settings"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Content */}
              <div className="p-6 space-y-6">
                {/* Theme Mode */}
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Appearance
                  </h3>
                  <div className="grid grid-cols-3 gap-3">
                    {modes.map((modeOption) => (
                      <button
                        key={modeOption.value}
                        onClick={() => setMode(modeOption.value)}
                        className={`
                          p-4 text-center rounded-lg border-2 transition-all duration-200
                          ${mode === modeOption.value
                            ? 'border-accent-500 bg-accent-50 dark:bg-accent-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          }
                        `}
                      >
                        <div className="text-2xl mb-2">{modeOption.icon}</div>
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {modeOption.label}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {modeOption.description}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Color Scheme */}
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Accessibility
                  </h3>
                  <div className="space-y-2">
                    {colorSchemes.map((schemeOption) => (
                      <button
                        key={schemeOption.value}
                        onClick={() => setColorScheme(schemeOption.value)}
                        className={`
                          w-full p-4 text-left rounded-lg border-2 transition-all duration-200
                          ${colorScheme === schemeOption.value
                            ? 'border-accent-500 bg-accent-50 dark:bg-accent-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          }
                        `}
                      >
                        <div className="flex items-start">
                          <span className="text-xl mr-3 mt-1">{schemeOption.icon}</span>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {schemeOption.label}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {schemeOption.description}
                            </div>
                          </div>
                          {colorScheme === schemeOption.value && (
                            <span className="text-accent-500 mt-1">✓</span>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={resetToDefaults}
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                >
                  Reset to Defaults
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-white bg-accent-600 hover:bg-accent-700 rounded-lg transition-colors duration-200"
                >
                  Done
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </>
  )
}

export { ThemeToggleButton, ThemeDropdown, ThemeModal }