/**
 * Loading Animations Components
 * 
 * Collection of sophisticated loading animations and transitions
 * for better user feedback during async operations.
 * 
 * Features:
 * - Skeleton loaders with shimmer effects
 * - Spinner variations with different styles
 * - Progress indicators
 * - Breathing animations
 * - Wave loading effects
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

// Shimmer Skeleton Loader
interface ShimmerSkeletonProps {
  className?: string
  variant?: 'text' | 'circular' | 'rectangular'
  lines?: number
  animate?: boolean
}

export const ShimmerSkeleton: React.FC<ShimmerSkeletonProps> = ({
  className = '',
  variant = 'rectangular',
  lines = 1,
  animate = true
}) => {
  const shimmerVariants = {
    initial: { x: '-100%' },
    animate: {
      x: '100%',
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'linear'
      }
    }
  }

  const baseClasses = 'bg-gray-800 relative overflow-hidden'
  const variantClasses = {
    text: 'h-4 rounded',
    circular: 'rounded-full aspect-square',
    rectangular: 'rounded-lg'
  }

  const SkeletonElement = ({ className: elementClass = '' }: { className?: string }) => (
    <div className={cn(baseClasses, variantClasses[variant], elementClass)}>
      {animate && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-700 to-transparent"
          variants={shimmerVariants}
          initial="initial"
          animate="animate"
        />
      )}
    </div>
  )

  if (variant === 'text' && lines > 1) {
    return (
      <div className={cn('space-y-2', className)}>
        {Array.from({ length: lines }).map((_, index) => (
          <SkeletonElement
            key={index}
            className={index === lines - 1 ? 'w-3/4' : 'w-full'}
          />
        ))}
      </div>
    )
  }

  return <SkeletonElement className={className} />
}

// Breathing Dot Loader
interface BreathingDotsProps {
  className?: string
  dotCount?: number
  color?: string
  size?: 'sm' | 'md' | 'lg'
}

export const BreathingDots: React.FC<BreathingDotsProps> = ({
  className = '',
  dotCount = 3,
  color = 'bg-accent-500',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  }

  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {Array.from({ length: dotCount }).map((_, index) => (
        <motion.div
          key={index}
          className={cn('rounded-full', color, sizeClasses[size])}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: index * 0.2,
            ease: 'easeInOut'
          }}
        />
      ))}
    </div>
  )
}

// Wave Loading Animation
interface WaveLoaderProps {
  className?: string
  barCount?: number
  color?: string
  height?: string
}

export const WaveLoader: React.FC<WaveLoaderProps> = ({
  className = '',
  barCount = 5,
  color = 'bg-accent-500',
  height = 'h-8'
}) => {
  return (
    <div className={cn('flex items-end space-x-1', className)}>
      {Array.from({ length: barCount }).map((_, index) => (
        <motion.div
          key={index}
          className={cn('w-1 rounded-full', color, height)}
          animate={{
            scaleY: [0.3, 1, 0.3],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: index * 0.1,
            ease: 'easeInOut'
          }}
        />
      ))}
    </div>
  )
}

// Circular Progress Indicator
interface CircularProgressProps {
  progress: number
  size?: number
  strokeWidth?: number
  className?: string
  showPercentage?: boolean
  color?: string
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  progress,
  size = 60,
  strokeWidth = 4,
  className = '',
  showPercentage = false,
  color = 'stroke-accent-500'
}) => {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <div className={cn('relative inline-block', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-700"
        />
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={color}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
        />
      </svg>
      {showPercentage && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-medium text-white">
            {Math.round(progress)}%
          </span>
        </div>
      )}
    </div>
  )
}

// Spinner with customizable styles
interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'dots' | 'pulse' | 'bars'
  className?: string
  color?: string
}

export const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className = '',
  color = 'text-accent-500'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  if (variant === 'dots') {
    return <BreathingDots className={className} size={size} />
  }

  if (variant === 'bars') {
    return <WaveLoader className={className} />
  }

  if (variant === 'pulse') {
    return (
      <motion.div
        className={cn(
          'rounded-full bg-current',
          sizeClasses[size],
          color,
          className
        )}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [1, 0.5, 1]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />
    )
  }

  // Default spinner
  return (
    <motion.div
      className={cn(
        'border-2 border-current border-t-transparent rounded-full',
        sizeClasses[size],
        color,
        className
      )}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      }}
    />
  )
}

// Loading Overlay
interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
  spinner?: React.ReactNode
  className?: string
  blur?: boolean
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = 'Loading...',
  spinner,
  className = '',
  blur = true
}) => {
  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center',
        blur ? 'backdrop-blur-sm' : '',
        'bg-black/50',
        className
      )}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="bg-gray-900 rounded-lg p-6 shadow-xl max-w-sm mx-4"
      >
        <div className="flex items-center space-x-3">
          {spinner || <Spinner size="md" />}
          <span className="text-white font-medium">{message}</span>
        </div>
      </motion.div>
    </motion.div>
  )
}

// Product Card Skeleton specifically for shop
export const ProductCardSkeleton: React.FC<{ className?: string }> = ({
  className = ''
}) => {
  return (
    <div className={cn('bg-gray-900/50 border border-gray-800 rounded-xl overflow-hidden', className)}>
      {/* Image skeleton */}
      <ShimmerSkeleton className="aspect-square w-full" />
      
      {/* Content skeleton */}
      <div className="p-4 space-y-3">
        {/* Title */}
        <ShimmerSkeleton variant="text" className="h-5" />
        
        {/* Category */}
        <ShimmerSkeleton variant="text" className="h-3 w-1/2" />
        
        {/* Rating */}
        <div className="flex items-center space-x-1">
          {Array.from({ length: 5 }).map((_, i) => (
            <ShimmerSkeleton key={i} variant="circular" className="w-3 h-3" />
          ))}
        </div>
        
        {/* Price */}
        <ShimmerSkeleton variant="text" className="h-6 w-1/3" />
        
        {/* Button */}
        <ShimmerSkeleton className="h-10 w-full rounded-lg" />
      </div>
    </div>
  )
}