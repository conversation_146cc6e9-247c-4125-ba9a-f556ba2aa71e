/**
 * Micro-Interactions Components
 * 
 * Collection of reusable micro-interaction components for enhanced UX.
 * Provides subtle animations and feedback for user actions.
 * 
 * Features:
 * - Magnetic buttons with cursor following
 * - Ripple effects for clicks
 * - Floating action buttons
 * - Pulse animations for notifications
 * - Smooth transitions and springs
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { motion, useMotionValue, useSpring, useTransform, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

// Magnetic Button Component
interface MagneticButtonProps {
  children: React.ReactNode
  className?: string
  strength?: number
  disabled?: boolean
  onClick?: () => void
}

export const MagneticButton: React.FC<MagneticButtonProps> = ({
  children,
  className = '',
  strength = 0.3,
  disabled = false,
  onClick
}) => {
  const ref = useRef<HTMLButtonElement>(null)
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const springX = useSpring(x, { stiffness: 300, damping: 20 })
  const springY = useSpring(y, { stiffness: 300, damping: 20 })

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!ref.current || disabled) return

    const rect = ref.current.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    const deltaX = (e.clientX - centerX) * strength
    const deltaY = (e.clientY - centerY) * strength

    x.set(deltaX)
    y.set(deltaY)
  }, [x, y, strength, disabled])

  const handleMouseLeave = useCallback(() => {
    if (disabled) return
    x.set(0)
    y.set(0)
  }, [x, y, disabled])

  return (
    <motion.button
      ref={ref}
      style={{ x: springX, y: springY }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      disabled={disabled}
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      className={cn(
        'relative overflow-hidden transition-all duration-200',
        disabled && 'cursor-not-allowed opacity-50',
        className
      )}
    >
      {children}
    </motion.button>
  )
}

// Ripple Effect Component
interface RippleEffectProps {
  children: React.ReactNode
  className?: string
  rippleColor?: string
  disabled?: boolean
  onClick?: () => void
}

export const RippleEffect: React.FC<RippleEffectProps> = ({
  children,
  className = '',
  rippleColor = 'rgba(255, 255, 255, 0.3)',
  disabled = false,
  onClick
}) => {
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([])
  const nextRippleId = useRef(0)

  const handleClick = useCallback((e: React.MouseEvent) => {
    if (disabled) return

    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const newRipple = {
      id: nextRippleId.current++,
      x,
      y
    }

    setRipples(prev => [...prev, newRipple])
    onClick?.()

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, 600)
  }, [disabled, onClick])

  return (
    <button
      className={cn('relative overflow-hidden', className)}
      onClick={handleClick}
      disabled={disabled}
    >
      {children}
      <AnimatePresence>
        {ripples.map(ripple => (
          <motion.span
            key={ripple.id}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            style={{
              position: 'absolute',
              left: ripple.x,
              top: ripple.y,
              width: 20,
              height: 20,
              borderRadius: '50%',
              backgroundColor: rippleColor,
              transform: 'translate(-50%, -50%)',
              pointerEvents: 'none'
            }}
          />
        ))}
      </AnimatePresence>
    </button>
  )
}

// Floating Action Button
interface FloatingActionButtonProps {
  children: React.ReactNode
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  className?: string
  onClick?: () => void
  tooltip?: string
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  children,
  position = 'bottom-right',
  className = '',
  onClick,
  tooltip
}) => {
  const [showTooltip, setShowTooltip] = useState(false)

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  }

  return (
    <div className={`fixed ${positionClasses[position]} z-50`}>
      <MagneticButton
        onClick={onClick}
        className={cn(
          'w-14 h-14 bg-accent-500 text-white rounded-full shadow-lg hover:shadow-xl',
          'flex items-center justify-center transition-all duration-200',
          className
        )}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {children}
      </MagneticButton>
      
      {tooltip && (
        <AnimatePresence>
          {showTooltip && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: position.includes('bottom') ? 10 : -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: position.includes('bottom') ? 10 : -10 }}
              className={cn(
                'absolute bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap',
                position.includes('bottom') ? 'bottom-full mb-2' : 'top-full mt-2',
                position.includes('right') ? 'right-0' : 'left-0'
              )}
            >
              {tooltip}
              <div className={cn(
                'absolute w-2 h-2 bg-gray-900 transform rotate-45',
                position.includes('bottom') ? 'top-full -mt-1' : 'bottom-full -mb-1',
                position.includes('right') ? 'right-3' : 'left-3'
              )} />
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  )
}

// Pulse Notification
interface PulseNotificationProps {
  children: React.ReactNode
  count?: number
  className?: string
  color?: 'red' | 'blue' | 'green' | 'purple' | 'orange'
}

export const PulseNotification: React.FC<PulseNotificationProps> = ({
  children,
  count,
  className = '',
  color = 'red'
}) => {
  const colorClasses = {
    red: 'bg-red-500',
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500'
  }

  return (
    <div className={cn('relative inline-block', className)}>
      {children}
      {count && count > 0 && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          whileHover={{ scale: 1.1 }}
          className={cn(
            'absolute -top-2 -right-2 min-w-5 h-5 rounded-full flex items-center justify-center',
            'text-white text-xs font-bold shadow-lg',
            colorClasses[color]
          )}
        >
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [1, 0.8, 1]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-0 rounded-full bg-current opacity-75"
          />
          <span className="relative z-10">
            {count > 99 ? '99+' : count}
          </span>
        </motion.div>
      )}
    </div>
  )
}

// Smooth Reveal Animation
interface SmoothRevealProps {
  children: React.ReactNode
  direction?: 'up' | 'down' | 'left' | 'right'
  delay?: number
  className?: string
}

export const SmoothReveal: React.FC<SmoothRevealProps> = ({
  children,
  direction = 'up',
  delay = 0,
  className = ''
}) => {
  const directionVariants = {
    up: { y: 20, opacity: 0 },
    down: { y: -20, opacity: 0 },
    left: { x: 20, opacity: 0 },
    right: { x: -20, opacity: 0 }
  }

  return (
    <motion.div
      initial={directionVariants[direction]}
      animate={{ x: 0, y: 0, opacity: 1 }}
      transition={{
        duration: 0.6,
        delay,
        ease: [0.22, 1, 0.36, 1]
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Stagger Container for animating lists
interface StaggerContainerProps {
  children: React.ReactNode
  staggerDelay?: number
  className?: string
}

export const StaggerContainer: React.FC<StaggerContainerProps> = ({
  children,
  staggerDelay = 0.1,
  className = ''
}) => {
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: staggerDelay
          }
        }
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Stagger Item for use within StaggerContainer
interface StaggerItemProps {
  children: React.ReactNode
  className?: string
}

export const StaggerItem: React.FC<StaggerItemProps> = ({
  children,
  className = ''
}) => {
  return (
    <motion.div
      variants={{
        hidden: { y: 20, opacity: 0 },
        visible: {
          y: 0,
          opacity: 1,
          transition: { duration: 0.6, ease: [0.22, 1, 0.36, 1] }
        }
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}