/**
 * Product Badge Component
 * 
 * Smart badge system for products with contextual styling and animations.
 * Displays various product states like NEW, LIMITED, BESTSELLER, etc.
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Sparkles, 
  Crown, 
  Timer, 
  Star, 
  Flame, 
  Zap,
  ShieldCheck,
  Percent
} from 'lucide-react'
import { cn } from '@/lib/utils'

export type BadgeType = 
  | 'new'
  | 'limited' 
  | 'bestseller'
  | 'exclusive'
  | 'sale'
  | 'raffle'
  | 'featured'
  | 'premium'

export type BadgeSize = 'sm' | 'md' | 'lg'
export type BadgeVariant = 'solid' | 'outline' | 'glass'

interface ProductBadgeProps {
  type: BadgeType
  size?: BadgeSize
  variant?: BadgeVariant
  className?: string
  pulse?: boolean
  glow?: boolean
  children?: React.ReactNode
}

interface BadgeConfig {
  label: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  colors: {
    solid: string
    outline: string
    glass: string
  }
  animation?: {
    pulse?: boolean
    glow?: boolean
    bounce?: boolean
  }
}

const badgeConfigs: Record<BadgeType, BadgeConfig> = {
  new: {
    label: 'NEW',
    icon: Sparkles,
    colors: {
      solid: 'bg-gradient-to-r from-green-500 to-emerald-600 text-white border-green-400',
      outline: 'border-green-400 text-green-400 bg-green-500/10',
      glass: 'bg-green-500/20 border-green-400/50 text-green-300 backdrop-blur-sm'
    },
    animation: { pulse: true, glow: true }
  },
  limited: {
    label: 'LIMITED',
    icon: Timer,
    colors: {
      solid: 'bg-gradient-to-r from-orange-500 to-red-600 text-white border-orange-400',
      outline: 'border-orange-400 text-orange-400 bg-orange-500/10',
      glass: 'bg-orange-500/20 border-orange-400/50 text-orange-300 backdrop-blur-sm'
    },
    animation: { pulse: true }
  },
  bestseller: {
    label: 'BESTSELLER',
    icon: Crown,
    colors: {
      solid: 'bg-gradient-to-r from-yellow-500 to-amber-600 text-gray-900 border-yellow-400',
      outline: 'border-yellow-400 text-yellow-400 bg-yellow-500/10',
      glass: 'bg-yellow-500/20 border-yellow-400/50 text-yellow-300 backdrop-blur-sm'
    },
    animation: { glow: true }
  },
  exclusive: {
    label: 'EXCLUSIVE',
    icon: ShieldCheck,
    colors: {
      solid: 'bg-gradient-to-r from-purple-500 to-indigo-600 text-white border-purple-400',
      outline: 'border-purple-400 text-purple-400 bg-purple-500/10',
      glass: 'bg-purple-500/20 border-purple-400/50 text-purple-300 backdrop-blur-sm'
    },
    animation: { glow: true }
  },
  sale: {
    label: 'SALE',
    icon: Percent,
    colors: {
      solid: 'bg-gradient-to-r from-red-500 to-pink-600 text-white border-red-400',
      outline: 'border-red-400 text-red-400 bg-red-500/10',
      glass: 'bg-red-500/20 border-red-400/50 text-red-300 backdrop-blur-sm'
    },
    animation: { pulse: true, bounce: true }
  },
  raffle: {
    label: 'RAFFLE',
    icon: Star,
    colors: {
      solid: 'bg-gradient-to-r from-pink-500 to-purple-600 text-white border-pink-400',
      outline: 'border-pink-400 text-pink-400 bg-pink-500/10',
      glass: 'bg-pink-500/20 border-pink-400/50 text-pink-300 backdrop-blur-sm'
    },
    animation: { pulse: true, glow: true }
  },
  featured: {
    label: 'FEATURED',
    icon: Flame,
    colors: {
      solid: 'bg-gradient-to-r from-blue-500 to-cyan-600 text-white border-blue-400',
      outline: 'border-blue-400 text-blue-400 bg-blue-500/10',
      glass: 'bg-blue-500/20 border-blue-400/50 text-blue-300 backdrop-blur-sm'
    },
    animation: { glow: true }
  },
  premium: {
    label: 'PREMIUM',
    icon: Zap,
    colors: {
      solid: 'bg-gradient-to-r from-gray-700 to-gray-900 text-white border-gray-500',
      outline: 'border-gray-400 text-gray-400 bg-gray-500/10',
      glass: 'bg-gray-500/20 border-gray-400/50 text-gray-300 backdrop-blur-sm'
    },
    animation: { glow: true }
  }
}

const sizeClasses: Record<BadgeSize, string> = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-3 py-1.5 text-sm',
  lg: 'px-4 py-2 text-base'
}

const iconSizes: Record<BadgeSize, number> = {
  sm: 12,
  md: 14,
  lg: 16
}

export const ProductBadge: React.FC<ProductBadgeProps> = ({
  type,
  size = 'md',
  variant = 'solid',
  className = '',
  pulse: forcePulse = false,
  glow: forceGlow = false,
  children
}) => {
  const config = badgeConfigs[type]

  // Defensive check to prevent crashes with invalid badge types
  if (!config) {
    console.warn(`ProductBadge: Invalid badge type "${type}". Valid types are: ${Object.keys(badgeConfigs).join(', ')}`)
    return null
  }

  const Icon = config.icon
  
  const shouldPulse = forcePulse || config.animation?.pulse
  const shouldGlow = forceGlow || config.animation?.glow
  const shouldBounce = config.animation?.bounce

  const baseClasses = cn(
    'inline-flex items-center justify-center gap-1.5 font-bold',
    'border rounded-full',
    'transition-all duration-300',
    sizeClasses[size],
    config.colors[variant],
    shouldGlow && 'shadow-lg',
    className
  )

  const animationClasses = cn(
    shouldBounce && 'hover:animate-bounce'
  )

  const glowEffect = shouldGlow ? {
    boxShadow: variant === 'solid' 
      ? `0 0 20px ${config.colors.solid.includes('green') ? '#10b981' : 
                    config.colors.solid.includes('orange') ? '#f97316' :
                    config.colors.solid.includes('yellow') ? '#eab308' :
                    config.colors.solid.includes('purple') ? '#8b5cf6' :
                    config.colors.solid.includes('red') ? '#ef4444' :
                    config.colors.solid.includes('pink') ? '#ec4899' :
                    config.colors.solid.includes('blue') ? '#3b82f6' :
                    '#6b7280'}33`
      : 'none'
  } : {}

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ 
        opacity: shouldPulse ? [1, 0.7, 1] : 1, 
        scale: 1 
      }}
      transition={{
        opacity: shouldPulse 
          ? { duration: 2, repeat: Infinity, ease: "easeInOut" }
          : { duration: 0.3 }
      }}
      whileHover={{ scale: 1.05 }}
      className={cn(baseClasses, animationClasses)}
      style={glowEffect}
    >
      <Icon size={iconSizes[size]} className="flex-shrink-0" />
      <span className="font-semibold tracking-wide">
        {children || config.label}
      </span>
    </motion.div>
  )
}

/**
 * Smart badge detector that determines which badges to show based on product properties
 */
export const getProductBadges = (product: {
  createdAt?: Date | { toDate?: () => Date }
  stock?: number
  soldOut?: boolean
  isRaffle?: boolean
  featured?: boolean
  limited?: boolean
  pointsCost?: number
  price?: number
  originalPrice?: number
}): BadgeType[] => {
  const badges: BadgeType[] = []
  
  // Check if product is new (within last 30 days)
  if (product.createdAt) {
    const createdDate = product.createdAt instanceof Date 
      ? product.createdAt 
      : product.createdAt.toDate?.() || new Date()
    const daysSinceCreated = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceCreated <= 30) {
      badges.push('new')
    }
  }
  
  // Check for sale (has originalPrice higher than current price)
  if (product.originalPrice && product.price && product.originalPrice > product.price) {
    badges.push('sale')
  }
  
  // Check for limited stock
  if (product.stock && product.stock <= 10 && !product.soldOut) {
    badges.push('limited')
  }
  
  // Check for raffle
  if (product.isRaffle) {
    badges.push('raffle')
  }
  
  // Check for featured
  if (product.featured) {
    badges.push('featured')
  }
  
  // Check for premium (points-only products)
  if (product.pointsCost && product.pointsCost > 0) {
    badges.push('premium')
  }
  
  // Check for limited edition
  if (product.limited) {
    badges.push('exclusive')
  }
  
  // Note: 'bestseller' would be determined by sales data from analytics
  
  return badges
}

export default ProductBadge