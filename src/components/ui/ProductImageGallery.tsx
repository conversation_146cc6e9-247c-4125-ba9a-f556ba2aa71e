/**
 * Product Image Gallery Component
 * 
 * Advanced image gallery with hover preview, touch gestures, and lazy loading.
 * Optimized for product cards with smooth transitions and performance.
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, Maximize2, Heart } from 'lucide-react'
import OptimizedImage from './OptimizedImage'
import { cn } from '@/lib/utils'

interface ImageData {
  id: string
  url: string
  alt: string
  thumbnail?: string
  width?: number
  height?: number
}

interface ProductImageGalleryProps {
  images: ImageData[]
  primaryImage: string
  productName: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  enableHover?: boolean
  enableTouch?: boolean
  showControls?: boolean
  onImageChange?: (index: number, image: ImageData) => void
  onFullscreenOpen?: () => void
}

const sizeConfig = {
  sm: {
    container: 'h-48 sm:h-56',
    thumbnail: 'w-12 h-12',
    gap: 'gap-1'
  },
  md: {
    container: 'h-64 sm:h-72 lg:h-80',
    thumbnail: 'w-16 h-16',
    gap: 'gap-2'
  },
  lg: {
    container: 'h-80 sm:h-96',
    thumbnail: 'w-20 h-20',
    gap: 'gap-3'
  }
}

export const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  images,
  primaryImage,
  productName,
  className = '',
  size = 'md',
  enableHover = true,
  enableTouch = true,
  showControls = true,
  onImageChange,
  onFullscreenOpen
}) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isHovered, setIsHovered] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const [preloadedImages, setPreloadedImages] = useState<Set<string>>(new Set())
  const timerRef = useRef<NodeJS.Timeout>()
  
  const config = sizeConfig[size]
  
  // Prepare image array with primary image first
  const allImages: ImageData[] = [
    {
      id: 'primary',
      url: primaryImage,
      alt: `${productName} - Main view`,
      thumbnail: primaryImage
    },
    ...images.filter(img => img.url !== primaryImage)
  ]

  /**
   * Preload next images for smooth transitions
   */
  const preloadImage = useCallback((url: string) => {
    if (preloadedImages.has(url)) return
    
    const img = new Image()
    img.onload = () => {
      setPreloadedImages(prev => new Set([...prev, url]))
    }
    img.src = url
  }, [preloadedImages])

  /**
   * Preload adjacent images when index changes
   */
  useEffect(() => {
    const currentImage = allImages[currentIndex]
    const nextImage = allImages[currentIndex + 1]
    const prevImage = allImages[currentIndex - 1]
    
    if (currentImage) preloadImage(currentImage.url)
    if (nextImage) preloadImage(nextImage.url)
    if (prevImage) preloadImage(prevImage.url)
  }, [currentIndex, allImages, preloadImage])

  /**
   * Auto-advance images on hover
   */
  useEffect(() => {
    if (!enableHover || !isHovered || allImages.length <= 1) return

    timerRef.current = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % allImages.length)
    }, 1500)

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isHovered, enableHover, allImages.length])

  /**
   * Handle touch gestures
   */
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!enableTouch) return
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }, [enableTouch])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!enableTouch) return
    setTouchEnd(e.targetTouches[0].clientX)
  }, [enableTouch])

  const handleTouchEnd = useCallback(() => {
    if (!enableTouch || !touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe && currentIndex < allImages.length - 1) {
      handleNext()
    }
    if (isRightSwipe && currentIndex > 0) {
      handlePrevious()
    }
  }, [enableTouch, touchStart, touchEnd, currentIndex, allImages.length])

  /**
   * Navigation handlers
   */
  const handleNext = useCallback(() => {
    const newIndex = (currentIndex + 1) % allImages.length
    setCurrentIndex(newIndex)
    onImageChange?.(newIndex, allImages[newIndex])
  }, [currentIndex, allImages, onImageChange])

  const handlePrevious = useCallback(() => {
    const newIndex = currentIndex === 0 ? allImages.length - 1 : currentIndex - 1
    setCurrentIndex(newIndex)
    onImageChange?.(newIndex, allImages[newIndex])
  }, [currentIndex, allImages, onImageChange])

  const handleImageSelect = useCallback((index: number) => {
    setCurrentIndex(index)
    onImageChange?.(index, allImages[index])
  }, [allImages, onImageChange])

  const currentImage = allImages[currentIndex]

  if (!currentImage) {
    return (
      <div className={cn('bg-gray-800 rounded-lg flex items-center justify-center', config.container, className)}>
        <span className="text-gray-400">No image available</span>
      </div>
    )
  }

  return (
    <div className={cn('relative group', className)}>
      {/* Main Image Container */}
      <div 
        className={cn(
          'relative overflow-hidden rounded-lg bg-gray-800 cursor-pointer',
          config.container
        )}
        onMouseEnter={() => enableHover && setIsHovered(true)}
        onMouseLeave={() => enableHover && setIsHovered(false)}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={onFullscreenOpen}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="absolute inset-0"
          >
            <OptimizedImage
              src={currentImage.url}
              alt={currentImage.alt}
              width={600}
              height={600}
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
              enableWebP={true}
              enableAVIF={true}
              quality={85}
              loading="lazy"
              placeholder="blur"
            />
          </motion.div>
        </AnimatePresence>

        {/* Navigation Arrows */}
        {showControls && allImages.length > 1 && (
          <>
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                handlePrevious()
              }}
              className={cn(
                'absolute left-2 top-1/2 -translate-y-1/2 p-2',
                'bg-black/50 text-white rounded-full',
                'opacity-0 group-hover:opacity-100 transition-opacity',
                'hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-accent-500'
              )}
              aria-label="Previous image"
            >
              <ChevronLeft size={16} />
            </button>
            
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                handleNext()
              }}
              className={cn(
                'absolute right-2 top-1/2 -translate-y-1/2 p-2',
                'bg-black/50 text-white rounded-full',
                'opacity-0 group-hover:opacity-100 transition-opacity',
                'hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-accent-500'
              )}
              aria-label="Next image"
            >
              <ChevronRight size={16} />
            </button>
          </>
        )}

        {/* Controls Overlay */}
        {showControls && (
          <div className="absolute top-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onFullscreenOpen?.()
              }}
              className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
              aria-label="View fullscreen"
            >
              <Maximize2 size={14} />
            </button>
          </div>
        )}

        {/* Image Counter */}
        {allImages.length > 1 && (
          <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
            {currentIndex + 1} / {allImages.length}
          </div>
        )}

        {/* Progress Indicators */}
        {allImages.length > 1 && (
          <div className="absolute bottom-2 right-2 flex space-x-1">
            {allImages.map((_, index) => (
              <button
                key={index}
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  handleImageSelect(index)
                }}
                className={cn(
                  'w-2 h-2 rounded-full transition-all duration-200',
                  index === currentIndex 
                    ? 'bg-white' 
                    : 'bg-white/50 hover:bg-white/75'
                )}
                aria-label={`View image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Thumbnail Strip */}
      {allImages.length > 1 && size !== 'sm' && (
        <div className={cn('mt-2 flex overflow-x-auto scrollbar-hide', config.gap)}>
          {allImages.map((image, index) => (
            <motion.button
              key={image.id}
              onClick={() => handleImageSelect(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                'relative flex-shrink-0 rounded overflow-hidden border-2 transition-all duration-200',
                config.thumbnail,
                index === currentIndex 
                  ? 'border-accent-500 shadow-lg shadow-accent-500/25' 
                  : 'border-gray-600 hover:border-gray-400'
              )}
            >
              <OptimizedImage
                src={image.thumbnail || image.url}
                alt={image.alt}
                width={80}
                height={80}
                className="w-full h-full object-cover"
                sizes="80px"
                enableWebP={true}
                quality={75}
                loading="lazy"
              />
              
              {/* Active indicator */}
              {index === currentIndex && (
                <div className="absolute inset-0 bg-accent-500/20" />
              )}
            </motion.button>
          ))}
        </div>
      )}
    </div>
  )
}

export default ProductImageGallery