/**
 * Quick Actions Component
 * 
 * Floating action buttons for product interactions.
 * Includes wishlist, share, quick view, and compare functionality.
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Heart, 
  Share2, 
  Eye, 
  ShoppingCart, 
  Plus,
  Check,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface QuickAction {
  id: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  label: string
  onClick: () => void | Promise<void>
  isActive?: boolean
  isLoading?: boolean
  variant?: 'primary' | 'secondary' | 'danger'
  show?: boolean
}

interface QuickActionsProps {
  actions: QuickAction[]
  className?: string
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  layout?: 'horizontal' | 'vertical' | 'grid'
  showOnHover?: boolean
  size?: 'sm' | 'md' | 'lg'
}

const positionClasses = {
  'top-left': 'top-2 left-2',
  'top-right': 'top-2 right-2',
  'bottom-left': 'bottom-2 left-2',
  'bottom-right': 'bottom-2 right-2'
}

const layoutClasses = {
  horizontal: 'flex-row space-x-1',
  vertical: 'flex-col space-y-1',
  grid: 'grid grid-cols-2 gap-1'
}

const sizeClasses = {
  sm: 'p-1.5',
  md: 'p-2',
  lg: 'p-3'
}

const iconSizes = {
  sm: 14,
  md: 16,
  lg: 18
}

const variants = {
  primary: {
    base: 'bg-accent-500/90 hover:bg-accent-600 text-white',
    active: 'bg-accent-600 text-white shadow-lg shadow-accent-500/25'
  },
  secondary: {
    base: 'bg-gray-700/90 hover:bg-gray-600 text-gray-200',
    active: 'bg-gray-600 text-white'
  },
  danger: {
    base: 'bg-red-500/90 hover:bg-red-600 text-white',
    active: 'bg-red-600 text-white shadow-lg shadow-red-500/25'
  }
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  actions,
  className = '',
  position = 'top-right',
  layout = 'vertical',
  showOnHover = true,
  size = 'md'
}) => {
  const [hoveredAction, setHoveredAction] = useState<string | null>(null)

  const visibleActions = actions.filter(action => action.show !== false)

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.2,
        staggerChildren: 0.05
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { opacity: 1, scale: 1 }
  }

  return (
    <AnimatePresence>
      {(!showOnHover || visibleActions.length > 0) && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          className={cn(
            'absolute z-10 flex',
            positionClasses[position],
            layoutClasses[layout],
            className
          )}
        >
          {visibleActions.map((action) => {
            const Icon = action.icon
            const variantStyle = variants[action.variant || 'secondary']

            return (
              <motion.div
                key={action.id}
                variants={itemVariants}
                className="relative group"
                onMouseEnter={() => setHoveredAction(action.id)}
                onMouseLeave={() => setHoveredAction(null)}
              >
                <motion.button
                  onClick={action.onClick}
                  disabled={action.isLoading}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  className={cn(
                    'relative rounded-full backdrop-blur-sm border border-white/20',
                    'transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent-500',
                    sizeClasses[size],
                    action.isActive ? variantStyle.active : variantStyle.base,
                    action.isLoading && 'cursor-not-allowed opacity-75'
                  )}
                  aria-label={action.label}
                >
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={action.isLoading ? 'loading' : action.isActive && (action.id === 'wishlist' || action.id === 'cart') ? 'active' : 'default'}
                      initial={{ opacity: 0, scale: 0.5, rotate: 0 }}
                      animate={{ 
                        opacity: 1, 
                        scale: 1,
                        rotate: action.isLoading ? 360 : 0
                      }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      transition={{ 
                        duration: 0.2,
                        rotate: action.isLoading ? { repeat: Infinity, duration: 1, ease: "linear" } : { duration: 0.2 }
                      }}
                    >
                      {action.isLoading ? (
                        <Loader2 size={iconSizes[size]} className="animate-spin" />
                      ) : action.isActive && (action.id === 'wishlist' || action.id === 'cart') ? (
                        <Check size={iconSizes[size]} />
                      ) : (
                        <Icon 
                          size={iconSizes[size]} 
                          className={cn(
                            action.isActive && action.id === 'wishlist' && 'fill-current'
                          )}
                        />
                      )}
                    </motion.div>
                  </AnimatePresence>

                  {/* Ripple effect */}
                  {action.isActive && (
                    <motion.div
                      className="absolute inset-0 rounded-full bg-white/20"
                      initial={{ scale: 0, opacity: 0.5 }}
                      animate={{ scale: 1.5, opacity: 0 }}
                      transition={{ duration: 0.6, repeat: Infinity }}
                    />
                  )}
                </motion.button>

                {/* Tooltip */}
                <AnimatePresence>
                  {hoveredAction === action.id && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8, y: 10 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.8, y: 10 }}
                      className={cn(
                        'absolute z-50 px-2 py-1 text-xs font-medium text-white',
                        'bg-gray-900 rounded border border-gray-700 shadow-lg',
                        'whitespace-nowrap pointer-events-none',
                        position.includes('top') ? 'top-full mt-2' : 'bottom-full mb-2',
                        position.includes('right') ? 'right-0' : 'left-0'
                      )}
                    >
                      {action.label}
                      
                      {/* Arrow */}
                      <div
                        className={cn(
                          'absolute w-2 h-2 bg-gray-900 border-gray-700 transform rotate-45',
                          position.includes('top') 
                            ? '-top-1 border-b border-r' 
                            : '-bottom-1 border-t border-l',
                          position.includes('right') ? 'right-2' : 'left-2'
                        )}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            )
          })}
        </motion.div>
      )}
    </AnimatePresence>
  )
}

/**
 * Hook to create common product quick actions
 */
export const useProductQuickActions = (
  productId: string,
  isInWishlist: boolean,
  isInCart: boolean,
  onAddToWishlist: () => Promise<void>,
  onAddToCart: () => Promise<void>,
  onShare: () => void,
  onQuickView: () => void
) => {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  const setLoading = (actionId: string, loading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [actionId]: loading }))
  }

  const actions: QuickAction[] = [
    {
      id: 'wishlist',
      icon: Heart,
      label: isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist',
      isActive: isInWishlist,
      isLoading: loadingStates.wishlist,
      variant: isInWishlist ? 'danger' : 'secondary',
      onClick: async () => {
        setLoading('wishlist', true)
        try {
          await onAddToWishlist()
        } finally {
          setLoading('wishlist', false)
        }
      }
    },
    {
      id: 'cart',
      icon: isInCart ? Check : ShoppingCart,
      label: isInCart ? 'Added to Cart' : 'Add to Cart',
      isActive: isInCart,
      isLoading: loadingStates.cart,
      variant: 'primary',
      onClick: async () => {
        if (isInCart) return
        setLoading('cart', true)
        try {
          await onAddToCart()
        } finally {
          setLoading('cart', false)
        }
      }
    },
    {
      id: 'quickview',
      icon: Eye,
      label: 'Quick View',
      variant: 'secondary',
      onClick: onQuickView
    },
    {
      id: 'share',
      icon: Share2,
      label: 'Share Product',
      variant: 'secondary',
      onClick: onShare
    }
  ]

  return { actions, setLoading }
}

export default QuickActions