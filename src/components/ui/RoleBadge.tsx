/**
 * Role Badge Component
 * 
 * Displays user roles with consistent styling and terminology
 * throughout the Syndicaps application. Uses centralized role
 * constants to ensure consistency.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React from 'react'
import { 
  User, 
  Headphones, 
  BarChart3, 
  Shield, 
  Settings, 
  Crown,
  LucideIcon
} from 'lucide-react'
import { 
  ROLE_IDS, 
  getRoleDisplayName, 
  getRoleDescription, 
  getRoleColors,
  isAdminRole,
  isSuperAdminRole,
  type RoleId 
} from '@/constants/roles'

interface RoleBadgeProps {
  /** Role ID */
  role: RoleId
  /** Size variant */
  size?: 'xs' | 'sm' | 'md' | 'lg'
  /** Whether to show icon */
  showIcon?: boolean
  /** Whether to show tooltip */
  showTooltip?: boolean
  /** Custom CSS classes */
  className?: string
  /** Variant style */
  variant?: 'default' | 'outline' | 'subtle'
}

/**
 * Get icon component for role
 */
const getRoleIcon = (role: RoleId): LucideIcon => {
  switch (role) {
    case ROLE_IDS.USER:
      return User
    case ROLE_IDS.SUPPORT:
      return Headphones
    case ROLE_IDS.ANALYST:
      return BarChart3
    case ROLE_IDS.MODERATOR:
      return Shield
    case ROLE_IDS.ADMIN:
      return Settings
    case ROLE_IDS.SUPER_ADMIN:
      return Crown
    default:
      return User
  }
}

/**
 * Get size classes
 */
const getSizeClasses = (size: string) => {
  switch (size) {
    case 'xs':
      return {
        container: 'px-1.5 py-0.5 text-xs',
        icon: 'w-2.5 h-2.5',
        gap: 'gap-1'
      }
    case 'sm':
      return {
        container: 'px-2 py-1 text-xs',
        icon: 'w-3 h-3',
        gap: 'gap-1'
      }
    case 'md':
      return {
        container: 'px-2.5 py-1 text-sm',
        icon: 'w-3.5 h-3.5',
        gap: 'gap-1.5'
      }
    case 'lg':
      return {
        container: 'px-3 py-1.5 text-sm',
        icon: 'w-4 h-4',
        gap: 'gap-2'
      }
    default:
      return {
        container: 'px-2 py-1 text-xs',
        icon: 'w-3 h-3',
        gap: 'gap-1'
      }
  }
}

/**
 * Get variant classes
 */
const getVariantClasses = (role: RoleId, variant: string) => {
  const colors = getRoleColors(role)
  
  switch (variant) {
    case 'outline':
      return `border-2 ${colors.border} ${colors.text} bg-transparent`
    case 'subtle':
      return `${colors.bg} ${colors.text} border border-transparent`
    case 'default':
    default:
      return `${colors.bg} ${colors.text} border ${colors.border}`
  }
}

/**
 * Role Badge Component
 */
const RoleBadge: React.FC<RoleBadgeProps> = ({
  role,
  size = 'sm',
  showIcon = true,
  showTooltip = true,
  className = '',
  variant = 'default'
}) => {
  const displayName = getRoleDisplayName(role)
  const description = getRoleDescription(role)
  const IconComponent = getRoleIcon(role)
  const sizeClasses = getSizeClasses(size)
  const variantClasses = getVariantClasses(role, variant)
  
  // Special styling for admin roles
  const isAdmin = isAdminRole(role)
  const isSuperAdmin = isSuperAdminRole(role)
  
  const badgeElement = (
    <span
      className={`
        inline-flex items-center ${sizeClasses.gap} ${sizeClasses.container}
        rounded-full font-medium transition-colors
        ${variantClasses}
        ${isAdmin ? 'ring-1 ring-offset-1 ring-offset-gray-900' : ''}
        ${isSuperAdmin ? 'ring-red-500/50 shadow-lg shadow-red-500/20' : ''}
        ${isAdmin && !isSuperAdmin ? 'ring-orange-500/50 shadow-lg shadow-orange-500/20' : ''}
        ${className}
      `}
      role="status"
      aria-label={`User role: ${displayName}`}
    >
      {showIcon && (
        <IconComponent 
          className={`${sizeClasses.icon} flex-shrink-0`}
          aria-hidden="true"
        />
      )}
      <span className="font-medium">{displayName}</span>
      
      {/* Special indicators for admin roles */}
      {isSuperAdmin && (
        <div className="flex items-center">
          <div className="w-1 h-1 bg-red-400 rounded-full animate-pulse" />
        </div>
      )}
      {isAdmin && !isSuperAdmin && (
        <div className="flex items-center">
          <div className="w-1 h-1 bg-orange-400 rounded-full animate-pulse" />
        </div>
      )}
    </span>
  )

  // Wrap with tooltip if enabled
  if (showTooltip) {
    return (
      <div className="relative group">
        {badgeElement}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap">
          <div className="font-medium mb-1">{displayName}</div>
          <div className="text-gray-300 text-xs">{description}</div>
          {/* Tooltip arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800" />
        </div>
      </div>
    )
  }

  return badgeElement
}

/**
 * Admin Role Badge - Specialized component for admin roles
 */
export const AdminRoleBadge: React.FC<Omit<RoleBadgeProps, 'role'> & { role: 'admin' | 'super_admin' }> = ({
  role,
  ...props
}) => {
  return (
    <RoleBadge
      role={role}
      variant="default"
      showIcon={true}
      showTooltip={true}
      {...props}
    />
  )
}

/**
 * User Role Indicator - Simple text display for user roles
 */
export const UserRoleIndicator: React.FC<{ role: RoleId; className?: string }> = ({
  role,
  className = ''
}) => {
  const displayName = getRoleDisplayName(role)
  const colors = getRoleColors(role)
  
  return (
    <span className={`${colors.text} text-sm font-medium ${className}`}>
      {displayName}
    </span>
  )
}

/**
 * Role List - Display multiple roles
 */
export const RoleList: React.FC<{
  roles: RoleId[]
  size?: RoleBadgeProps['size']
  variant?: RoleBadgeProps['variant']
  className?: string
}> = ({
  roles,
  size = 'sm',
  variant = 'default',
  className = ''
}) => {
  if (roles.length === 0) return null
  
  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {roles.map((role) => (
        <RoleBadge
          key={role}
          role={role}
          size={size}
          variant={variant}
        />
      ))}
    </div>
  )
}

export default RoleBadge
export type { RoleBadgeProps }
