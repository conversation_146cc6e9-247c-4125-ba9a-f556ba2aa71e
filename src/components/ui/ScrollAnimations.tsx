/**
 * Scroll-triggered Animations
 * 
 * Components for animations triggered by scroll position and viewport visibility.
 * Provides smooth, performant animations that enhance the user experience.
 * 
 * Features:
 * - Intersection Observer based animations
 * - Scroll-to-top functionality
 * - Progress indicators
 * - Parallax effects
 * - Viewport-based reveals
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, useScroll, useTransform, useInView } from 'framer-motion'
import { ArrowUp, ChevronUp } from 'lucide-react'
import { cn } from '@/lib/utils'
import { FloatingActionButton } from './MicroInteractions'

// Scroll to Top Button
interface ScrollToTopProps {
  threshold?: number
  className?: string
  smooth?: boolean
}

export const ScrollToTop: React.FC<ScrollToTopProps> = ({
  threshold = 300,
  className = '',
  smooth = true
}) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > threshold) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener('scroll', toggleVisibility)
    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [threshold])

  const scrollToTop = useCallback(() => {
    if (smooth) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    } else {
      window.scrollTo(0, 0)
    }
  }, [smooth])

  if (!isVisible) return null

  return (
    <FloatingActionButton
      onClick={scrollToTop}
      position="bottom-right"
      tooltip="Scroll to top"
      className={className}
    >
      <ArrowUp size={20} />
    </FloatingActionButton>
  )
}

// Viewport reveal animation
interface ViewportRevealProps {
  children: React.ReactNode
  direction?: 'up' | 'down' | 'left' | 'right'
  distance?: number
  delay?: number
  duration?: number
  once?: boolean
  className?: string
}

export const ViewportReveal: React.FC<ViewportRevealProps> = ({
  children,
  direction = 'up',
  distance = 50,
  delay = 0,
  duration = 0.6,
  once = true,
  className = ''
}) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once, margin: "-100px" })

  const directionVariants = {
    up: { y: distance, opacity: 0 },
    down: { y: -distance, opacity: 0 },
    left: { x: distance, opacity: 0 },
    right: { x: -distance, opacity: 0 }
  }

  return (
    <motion.div
      ref={ref}
      initial={directionVariants[direction]}
      animate={isInView ? { x: 0, y: 0, opacity: 1 } : directionVariants[direction]}
      transition={{
        duration,
        delay,
        ease: [0.22, 1, 0.36, 1]
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Scroll progress indicator
interface ScrollProgressProps {
  className?: string
  color?: string
  height?: number
  position?: 'top' | 'bottom'
}

export const ScrollProgress: React.FC<ScrollProgressProps> = ({
  className = '',
  color = 'bg-accent-500',
  height = 3,
  position = 'top'
}) => {
  const { scrollYProgress } = useScroll()
  const scaleX = useTransform(scrollYProgress, [0, 1], [0, 1])

  return (
    <motion.div
      className={cn(
        'fixed left-0 right-0 z-50 origin-left',
        position === 'top' ? 'top-0' : 'bottom-0',
        color,
        className
      )}
      style={{
        height: `${height}px`,
        scaleX
      }}
    />
  )
}

// Parallax container
interface ParallaxContainerProps {
  children: React.ReactNode
  speed?: number
  className?: string
}

export const ParallaxContainer: React.FC<ParallaxContainerProps> = ({
  children,
  speed = 0.5,
  className = ''
}) => {
  const ref = useRef(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  })
  
  const y = useTransform(scrollYProgress, [0, 1], [0, speed * 100])

  return (
    <div ref={ref} className={cn('relative', className)}>
      <motion.div style={{ y }}>
        {children}
      </motion.div>
    </div>
  )
}

// Staggered fade-in for lists
interface StaggeredFadeInProps {
  children: React.ReactNode
  staggerDelay?: number
  initialDelay?: number
  className?: string
}

export const StaggeredFadeIn: React.FC<StaggeredFadeInProps> = ({
  children,
  staggerDelay = 0.1,
  initialDelay = 0,
  className = ''
}) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-50px" })

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            delay: initialDelay,
            staggerChildren: staggerDelay
          }
        }
      }}
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          variants={{
            hidden: { y: 20, opacity: 0 },
            visible: {
              y: 0,
              opacity: 1,
              transition: { duration: 0.6, ease: [0.22, 1, 0.36, 1] }
            }
          }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  )
}

// Count-up animation for numbers
interface CountUpProps {
  from?: number
  to: number
  duration?: number
  className?: string
  prefix?: string
  suffix?: string
  delay?: number
}

export const CountUp: React.FC<CountUpProps> = ({
  from = 0,
  to,
  duration = 2,
  className = '',
  prefix = '',
  suffix = '',
  delay = 0
}) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })
  const [count, setCount] = useState(from)

  useEffect(() => {
    if (!isInView) return

    const timer = setTimeout(() => {
      const startTime = Date.now()
      const startValue = from

      const updateCount = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / (duration * 1000), 1)
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        const current = Math.round(startValue + (to - startValue) * easeOutQuart)
        
        setCount(current)
        
        if (progress < 1) {
          requestAnimationFrame(updateCount)
        }
      }
      
      updateCount()
    }, delay * 1000)

    return () => clearTimeout(timer)
  }, [isInView, from, to, duration, delay])

  return (
    <span ref={ref} className={className}>
      {prefix}{count.toLocaleString()}{suffix}
    </span>
  )
}

// Scroll-triggered scale animation
interface ScrollScaleProps {
  children: React.ReactNode
  scale?: [number, number]
  className?: string
}

export const ScrollScale: React.FC<ScrollScaleProps> = ({
  children,
  scale = [0.8, 1],
  className = ''
}) => {
  const ref = useRef(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "center center"]
  })
  
  const scaleValue = useTransform(scrollYProgress, [0, 1], scale)

  return (
    <motion.div
      ref={ref}
      style={{ scale: scaleValue }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Mouse follower effect
interface MouseFollowerProps {
  children: React.ReactNode
  strength?: number
  className?: string
}

export const MouseFollower: React.FC<MouseFollowerProps> = ({
  children,
  strength = 0.1,
  className = ''
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!ref.current) return
      
      const rect = ref.current.getBoundingClientRect()
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2
      
      setMousePosition({
        x: (e.clientX - centerX) * strength,
        y: (e.clientY - centerY) * strength
      })
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [strength])

  return (
    <motion.div
      ref={ref}
      animate={{
        x: mousePosition.x,
        y: mousePosition.y
      }}
      transition={{
        type: "spring",
        stiffness: 50,
        damping: 20
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}