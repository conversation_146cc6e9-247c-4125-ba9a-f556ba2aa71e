/**
 * Standardized Form Component
 * 
 * Provides consistent form patterns across the profile system with standardized
 * save/cancel behavior, auto-save functionality, and field-level help text.
 * 
 * Features:
 * - Consistent save/cancel patterns
 * - Auto-save functionality with debouncing
 * - Field-level help text and validation
 * - Loading states and error handling
 * - Keyboard shortcuts (Ctrl+S to save, Escape to cancel)
 * - Dirty state tracking
 * - Form reset functionality
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Save,
  X,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Info,
  Loader2,
  Keyboard
} from 'lucide-react'
import toast from 'react-hot-toast'

interface StandardizedFormProps {
  children: React.ReactNode
  onSave: () => Promise<void> | void
  onCancel?: () => void
  onReset?: () => void
  isDirty?: boolean
  isLoading?: boolean
  autoSave?: boolean
  autoSaveDelay?: number
  showKeyboardShortcuts?: boolean
  saveButtonText?: string
  cancelButtonText?: string
  resetButtonText?: string
  className?: string
  formTitle?: string
  formDescription?: string
}

interface FormFieldProps {
  label: string
  description?: string
  helpText?: string
  error?: string
  required?: boolean
  children: React.ReactNode
  className?: string
}

interface FormSectionProps {
  title: string
  description?: string
  children: React.ReactNode
  className?: string
}

/**
 * Form Field Component with consistent styling and help text
 */
export const FormField: React.FC<FormFieldProps> = ({
  label,
  description,
  helpText,
  error,
  required = false,
  children,
  className = ''
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-white">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>
        {helpText && (
          <div className="group relative">
            <Info size={14} className="text-gray-400 hover:text-white cursor-help" />
            <div className="absolute right-0 top-6 w-64 p-3 bg-gray-800 border border-gray-600 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
              <p className="text-sm text-gray-300">{helpText}</p>
            </div>
          </div>
        )}
      </div>
      
      {description && (
        <p className="text-sm text-gray-400">{description}</p>
      )}
      
      <div className="relative">
        {children}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute -bottom-6 left-0 flex items-center space-x-1 text-red-400 text-sm"
          >
            <AlertTriangle size={14} />
            <span>{error}</span>
          </motion.div>
        )}
      </div>
    </div>
  )
}

/**
 * Form Section Component for grouping related fields
 */
export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className = ''
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="border-b border-gray-700 pb-4">
        <h3 className="text-lg font-semibold text-white">{title}</h3>
        {description && (
          <p className="text-sm text-gray-400 mt-1">{description}</p>
        )}
      </div>
      <div className="space-y-6">
        {children}
      </div>
    </div>
  )
}

/**
 * Auto-save hook with debouncing
 */
const useAutoSave = (
  onSave: () => Promise<void> | void,
  isDirty: boolean,
  delay: number = 2000,
  enabled: boolean = false
) => {
  const timeoutRef = useRef<NodeJS.Timeout>()
  const [isAutoSaving, setIsAutoSaving] = useState(false)

  useEffect(() => {
    if (!enabled || !isDirty) return

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Set new timeout
    timeoutRef.current = setTimeout(async () => {
      try {
        setIsAutoSaving(true)
        await onSave()
        toast.success('Changes saved automatically', { duration: 2000 })
      } catch (error) {
        console.error('Auto-save failed:', error)
        toast.error('Auto-save failed')
      } finally {
        setIsAutoSaving(false)
      }
    }, delay)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [isDirty, onSave, delay, enabled])

  return { isAutoSaving }
}

/**
 * Keyboard shortcuts hook for forms
 */
const useFormKeyboardShortcuts = (
  onSave: () => void,
  onCancel?: () => void,
  enabled: boolean = true
) => {
  useEffect(() => {
    if (!enabled) return

    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement ||
        (event.target as HTMLElement)?.contentEditable === 'true'
      ) {
        // Allow Ctrl+S in inputs
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
          event.preventDefault()
          onSave()
          return
        }
        return
      }

      // Ctrl+S or Cmd+S to save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault()
        onSave()
        return
      }

      // Escape to cancel
      if (event.key === 'Escape' && onCancel) {
        event.preventDefault()
        onCancel()
        return
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onSave, onCancel, enabled])
}

/**
 * Main Standardized Form Component
 */
const StandardizedForm: React.FC<StandardizedFormProps> = ({
  children,
  onSave,
  onCancel,
  onReset,
  isDirty = false,
  isLoading = false,
  autoSave = false,
  autoSaveDelay = 2000,
  showKeyboardShortcuts = true,
  saveButtonText = 'Save Changes',
  cancelButtonText = 'Cancel',
  resetButtonText = 'Reset',
  className = '',
  formTitle,
  formDescription
}) => {
  const [isSaving, setIsSaving] = useState(false)
  const { isAutoSaving } = useAutoSave(onSave, isDirty, autoSaveDelay, autoSave)

  const handleSave = useCallback(async () => {
    if (isSaving || isLoading) return

    try {
      setIsSaving(true)
      await onSave()
      toast.success('Changes saved successfully')
    } catch (error) {
      console.error('Save failed:', error)
      toast.error('Failed to save changes')
    } finally {
      setIsSaving(false)
    }
  }, [onSave, isSaving, isLoading])

  const handleCancel = useCallback(() => {
    if (isDirty) {
      const confirmed = window.confirm('You have unsaved changes. Are you sure you want to cancel?')
      if (!confirmed) return
    }
    onCancel?.()
  }, [onCancel, isDirty])

  const handleReset = useCallback(() => {
    const confirmed = window.confirm('This will reset all fields to their original values. Continue?')
    if (confirmed) {
      onReset?.()
      toast.success('Form reset to original values')
    }
  }, [onReset])

  // Keyboard shortcuts
  useFormKeyboardShortcuts(handleSave, handleCancel, showKeyboardShortcuts)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Form Header */}
      {(formTitle || formDescription) && (
        <div className="border-b border-gray-700 pb-6">
          {formTitle && (
            <h2 className="text-2xl font-bold text-white mb-2">{formTitle}</h2>
          )}
          {formDescription && (
            <p className="text-gray-400">{formDescription}</p>
          )}
          {showKeyboardShortcuts && (
            <div className="flex items-center space-x-4 mt-4 text-xs text-gray-500">
              <div className="flex items-center space-x-1">
                <Keyboard size={12} />
                <span>Ctrl+S to save</span>
              </div>
              {onCancel && (
                <div className="flex items-center space-x-1">
                  <span>Esc to cancel</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Form Content */}
      <div className="space-y-8">
        {children}
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-700">
        <div className="flex items-center space-x-3">
          {/* Auto-save indicator */}
          {autoSave && (
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              {isAutoSaving ? (
                <>
                  <Loader2 size={14} className="animate-spin" />
                  <span>Auto-saving...</span>
                </>
              ) : isDirty ? (
                <>
                  <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                  <span>Unsaved changes</span>
                </>
              ) : (
                <>
                  <CheckCircle size={14} className="text-green-400" />
                  <span>All changes saved</span>
                </>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-3">
          {/* Reset Button */}
          {onReset && (
            <button
              type="button"
              onClick={handleReset}
              disabled={!isDirty || isLoading || isSaving}
              className="px-4 py-2 text-gray-400 hover:text-white hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors flex items-center space-x-2"
            >
              <RotateCcw size={16} />
              <span>{resetButtonText}</span>
            </button>
          )}

          {/* Cancel Button */}
          {onCancel && (
            <button
              type="button"
              onClick={handleCancel}
              disabled={isLoading || isSaving}
              className="px-4 py-2 border border-gray-600 text-gray-300 hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors flex items-center space-x-2"
            >
              <X size={16} />
              <span>{cancelButtonText}</span>
            </button>
          )}

          {/* Save Button */}
          <button
            type="button"
            onClick={handleSave}
            disabled={!isDirty || isLoading || isSaving || isAutoSaving}
            className="px-6 py-2 bg-accent-500 hover:bg-accent-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            {(isSaving || isLoading) ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <Save size={16} />
            )}
            <span>{saveButtonText}</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default StandardizedForm
export type { StandardizedFormProps, FormFieldProps, FormSectionProps }
