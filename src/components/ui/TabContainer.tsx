/**
 * TabContainer Component
 * 
 * Accessible tabbed interface component following Syndicaps design system.
 * Supports keyboard navigation, smooth animations, and mobile-responsive design.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { LucideIcon } from 'lucide-react'

export interface Tab {
  id: string
  label: string
  icon?: LucideIcon
  badge?: string | number
  disabled?: boolean
  description?: string
}

export interface TabContainerProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'pills' | 'underline'
  size?: 'sm' | 'md' | 'lg'
  orientation?: 'horizontal' | 'vertical'
  persistState?: boolean
  storageKey?: string
}

export interface TabPanelProps {
  id: string
  children: React.ReactNode
  className?: string
  lazy?: boolean
}

/**
 * TabPanel Component
 */
export const TabPanel: React.FC<TabPanelProps> = ({ 
  id, 
  children, 
  className = '',
  lazy = true 
}) => {
  return (
    <div
      id={`tabpanel-${id}`}
      role="tabpanel"
      aria-labelledby={`tab-${id}`}
      className={className}
      tabIndex={0}
    >
      {children}
    </div>
  )
}

/**
 * Main TabContainer Component
 */
const TabContainer: React.FC<TabContainerProps> = ({
  tabs,
  activeTab,
  onTabChange,
  children,
  className = '',
  variant = 'default',
  size = 'md',
  orientation = 'horizontal',
  persistState = false,
  storageKey = 'syndicaps-tab-state'
}) => {
  const [focusedTab, setFocusedTab] = useState<string | null>(null)
  const tabListRef = useRef<HTMLDivElement>(null)
  const indicatorRef = useRef<HTMLDivElement>(null)
  const hasLoadedPersistedState = useRef(false)

  // Load persisted state only once
  useEffect(() => {
    if (persistState && typeof window !== 'undefined' && !hasLoadedPersistedState.current) {
      const saved = localStorage.getItem(storageKey)
      if (saved && tabs.find(tab => tab.id === saved)) {
        onTabChange(saved)
      }
      hasLoadedPersistedState.current = true
    }
  }, [persistState, storageKey])

  // Save state when tab changes
  useEffect(() => {
    if (persistState && typeof window !== 'undefined') {
      localStorage.setItem(storageKey, activeTab)
    }
  }, [activeTab, persistState, storageKey])

  // Update indicator position
  useEffect(() => {
    if (indicatorRef.current && tabListRef.current) {
      const activeTabElement = tabListRef.current.querySelector(`[data-tab-id="${activeTab}"]`) as HTMLElement
      if (activeTabElement) {
        const { offsetLeft, offsetWidth, offsetTop, offsetHeight } = activeTabElement
        
        if (orientation === 'horizontal') {
          indicatorRef.current.style.transform = `translateX(${offsetLeft}px)`
          indicatorRef.current.style.width = `${offsetWidth}px`
        } else {
          indicatorRef.current.style.transform = `translateY(${offsetTop}px)`
          indicatorRef.current.style.height = `${offsetHeight}px`
        }
      }
    }
  }, [activeTab, orientation])

  // Keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent, tabId: string) => {
    const currentIndex = tabs.findIndex(tab => tab.id === tabId)
    let nextIndex = currentIndex

    switch (event.key) {
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault()
        nextIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1
        break
      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault()
        nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0
        break
      case 'Home':
        event.preventDefault()
        nextIndex = 0
        break
      case 'End':
        event.preventDefault()
        nextIndex = tabs.length - 1
        break
      case 'Enter':
      case ' ':
        event.preventDefault()
        onTabChange(tabId)
        return
    }

    const nextTab = tabs[nextIndex]
    if (nextTab && !nextTab.disabled) {
      setFocusedTab(nextTab.id)
      const nextTabElement = tabListRef.current?.querySelector(`[data-tab-id="${nextTab.id}"]`) as HTMLElement
      nextTabElement?.focus()
    }
  }

  // Style variants
  const getTabStyles = () => {
    const baseStyles = "relative flex items-center justify-center px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900"
    
    switch (variant) {
      case 'pills':
        return `${baseStyles} rounded-lg`
      case 'underline':
        return `${baseStyles} border-b-2 border-transparent`
      default:
        return `${baseStyles} rounded-t-lg`
    }
  }

  const getActiveTabStyles = () => {
    switch (variant) {
      case 'pills':
        return "bg-accent-500 text-white shadow-lg"
      case 'underline':
        return "text-accent-400 border-accent-400"
      default:
        return "bg-gray-800 text-white border-t border-l border-r border-gray-700"
    }
  }

  const getInactiveTabStyles = () => {
    switch (variant) {
      case 'pills':
        return "text-gray-400 hover:text-white hover:bg-gray-700"
      case 'underline':
        return "text-gray-400 hover:text-white hover:border-gray-600"
      default:
        return "text-gray-400 hover:text-white hover:bg-gray-700"
    }
  }

  // Size variants
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return "text-xs px-3 py-1.5"
      case 'lg':
        return "text-base px-6 py-3"
      default:
        return "text-sm px-4 py-2"
    }
  }

  // Filter children to only show active tab panel
  const activeTabPanel = React.Children.toArray(children).find((child) => {
    if (React.isValidElement(child) && child.props.id === activeTab) {
      return child
    }
    return null
  })

  return (
    <div className={`w-full ${className}`}>
      {/* Tab List */}
      <div
        ref={tabListRef}
        role="tablist"
        aria-orientation={orientation}
        className={`relative flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} ${
          variant === 'underline' ? 'border-b border-gray-700' : ''
        }`}
      >
        {/* Animated Indicator */}
        {variant !== 'pills' && (
          <motion.div
            ref={indicatorRef}
            className={`absolute ${
              variant === 'underline' 
                ? 'bottom-0 h-0.5 bg-accent-500' 
                : 'bottom-0 h-full bg-gray-800 border border-gray-700 rounded-t-lg -z-10'
            }`}
            initial={false}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          />
        )}

        {tabs.map((tab) => {
          const isActive = tab.id === activeTab
          const isFocused = tab.id === focusedTab
          const Icon = tab.icon

          return (
            <button
              key={tab.id}
              id={`tab-${tab.id}`}
              data-tab-id={tab.id}
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
              disabled={tab.disabled}
              className={`
                ${getTabStyles()}
                ${getSizeStyles()}
                ${isActive ? getActiveTabStyles() : getInactiveTabStyles()}
                ${tab.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                ${isFocused ? 'ring-2 ring-accent-500' : ''}
              `}
              onClick={() => !tab.disabled && onTabChange(tab.id)}
              onKeyDown={(e) => handleKeyDown(e, tab.id)}
              onFocus={() => setFocusedTab(tab.id)}
              onBlur={() => setFocusedTab(null)}
            >
              {Icon && (
                <Icon 
                  size={size === 'sm' ? 14 : size === 'lg' ? 20 : 16} 
                  className="mr-2" 
                />
              )}
              
              <span>{tab.label}</span>
              
              {tab.badge && (
                <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                  isActive 
                    ? 'bg-white/20 text-white' 
                    : 'bg-accent-500/20 text-accent-400'
                }`}>
                  {tab.badge}
                </span>
              )}
            </button>
          )
        })}
      </div>

      {/* Tab Panels */}
      <div className="mt-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {activeTabPanel}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  )
}

export default TabContainer
