/**
 * Micro-Interactions Component Tests
 * 
 * Comprehensive test suite for micro-interaction components including
 * magnetic buttons, ripple effects, and animation components.
 * 
 * <AUTHOR> Team
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'

// Comprehensive mocking strategy - apply proven patterns from performance/accessibility tests

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Heart: () => <div data-testid="heart-icon">Heart</div>,
  Share: () => <div data-testid="share-icon">Share</div>,
  ShoppingCart: () => <div data-testid="cart-icon">Cart</div>,
  Plus: () => <div data-testid="plus-icon">Plus</div>,
  Bell: () => <div data-testid="bell-icon">Bell</div>,
  X: () => <div data-testid="x-icon">X</div>,
  ChevronDown: () => <div data-testid="chevron-down-icon">ChevronDown</div>,
  Check: () => <div data-testid="check-icon">Check</div>,
  Filter: () => <div data-testid="filter-icon">Filter</div>,
  Search: () => <div data-testid="search-icon">Search</div>,
  Star: () => <div data-testid="star-icon">Star</div>,
  Trash2: () => <div data-testid="trash-icon">Trash</div>,
  Edit: () => <div data-testid="edit-icon">Edit</div>,
  Eye: () => <div data-testid="eye-icon">Eye</div>,
  EyeOff: () => <div data-testid="eye-off-icon">EyeOff</div>,
  Download: () => <div data-testid="download-icon">Download</div>,
  Upload: () => <div data-testid="upload-icon">Upload</div>,
  Settings: () => <div data-testid="settings-icon">Settings</div>,
  User: () => <div data-testid="user-icon">User</div>,
  Mail: () => <div data-testid="mail-icon">Mail</div>,
  Phone: () => <div data-testid="phone-icon">Phone</div>,
  Calendar: () => <div data-testid="calendar-icon">Calendar</div>,
  Clock: () => <div data-testid="clock-icon">Clock</div>,
  MapPin: () => <div data-testid="map-pin-icon">MapPin</div>,
  Home: () => <div data-testid="home-icon">Home</div>,
  Menu: () => <div data-testid="menu-icon">Menu</div>,
  MoreVertical: () => <div data-testid="more-vertical-icon">MoreVertical</div>
}))

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, className, ...props }: any) {
    return (
      <img
        src={src}
        alt={alt}
        className={className}
        {...props}
      />
    )
  }
})

// Mock framer-motion with comprehensive animation support
jest.mock('framer-motion', () => ({
  motion: {
    button: React.forwardRef(({ children, whileHover, whileTap, initial, animate, exit, transition, ...props }: any, ref: any) => 
      <button ref={ref} {...props}>{children}</button>
    ),
    div: React.forwardRef(({ children, whileHover, whileTap, initial, animate, exit, transition, ...props }: any, ref: any) => 
      <div ref={ref} {...props}>{children}</div>
    ),
    span: React.forwardRef(({ children, whileHover, whileTap, initial, animate, exit, transition, ...props }: any, ref: any) => 
      <span ref={ref} {...props}>{children}</span>
    )
  },
  useMotionValue: () => ({ 
    set: jest.fn(),
    get: jest.fn(() => 0),
    onChange: jest.fn()
  }),
  useSpring: () => ({ 
    set: jest.fn(),
    get: jest.fn(() => 0),
    onChange: jest.fn()
  }),
  useTransform: () => ({ 
    set: jest.fn(),
    get: jest.fn(() => 0)
  }),
  AnimatePresence: ({ children }: any) => <div data-testid="animate-presence">{children}</div>,
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn(),
    set: jest.fn()
  })
}))

// Mock analytics hooks
jest.mock('@/hooks/useAnalytics', () => ({
  useAnalytics: () => ({
    track: jest.fn(),
    identify: jest.fn(),
    page: jest.fn()
  })
}))

// Mock utility functions
jest.mock('@/lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
  formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
  formatDate: (date: Date) => date.toLocaleDateString()
}))

// Mock RAF and timers for animations
global.requestAnimationFrame = jest.fn((cb) => {
  return setTimeout(cb, 16)
})

global.cancelAnimationFrame = jest.fn((id) => {
  clearTimeout(id)
})

// Import components after mocking
import {
  MagneticButton,
  RippleEffect,
  FloatingActionButton,
  PulseNotification,
  SmoothReveal,
  StaggerContainer,
  StaggerItem
} from '../MicroInteractions'

describe('MagneticButton', () => {
  it('renders children correctly', () => {
    render(
      <MagneticButton>
        <span>Test Button</span>
      </MagneticButton>
    )
    
    expect(screen.getByText('Test Button')).toBeInTheDocument()
  })

  it('calls onClick when clicked', async () => {
    const handleClick = jest.fn()
    const user = userEvent.setup()
    
    render(
      <MagneticButton onClick={handleClick}>
        Click me
      </MagneticButton>
    )
    
    await user.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies disabled state correctly', () => {
    render(
      <MagneticButton disabled>
        Disabled Button
      </MagneticButton>
    )
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
  })

  it('handles mouse events without crashing', async () => {
    const user = userEvent.setup()
    
    render(
      <MagneticButton>
        Hover me
      </MagneticButton>
    )
    
    const button = screen.getByRole('button')
    await user.hover(button)
    await user.unhover(button)
    
    expect(button).toBeInTheDocument()
  })
})

describe('RippleEffect', () => {
  it('renders children correctly', () => {
    render(
      <RippleEffect>
        <span>Ripple Button</span>
      </RippleEffect>
    )
    
    expect(screen.getByText('Ripple Button')).toBeInTheDocument()
  })

  it('calls onClick when clicked', async () => {
    const handleClick = jest.fn()
    const user = userEvent.setup()
    
    render(
      <RippleEffect onClick={handleClick}>
        Click for ripple
      </RippleEffect>
    )
    
    await user.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('does not call onClick when disabled', async () => {
    const handleClick = jest.fn()
    const user = userEvent.setup()
    
    render(
      <RippleEffect onClick={handleClick} disabled>
        Disabled Ripple
      </RippleEffect>
    )
    
    await user.click(screen.getByRole('button'))
    expect(handleClick).not.toHaveBeenCalled()
  })
})

describe('FloatingActionButton', () => {
  it('renders at correct position', () => {
    render(
      <FloatingActionButton position="bottom-right" tooltip="Test FAB">
        <span>FAB</span>
      </FloatingActionButton>
    )
    
    expect(screen.getByText('FAB')).toBeInTheDocument()
  })

  it('shows tooltip on hover', async () => {
    const user = userEvent.setup()
    
    render(
      <FloatingActionButton tooltip="Test Tooltip">
        <span>FAB</span>
      </FloatingActionButton>
    )
    
    const button = screen.getByRole('button')
    await user.hover(button)
    
    // Check if tooltip appears (implementation dependent)
    expect(button).toBeInTheDocument()
  })

  it('calls onClick when clicked', async () => {
    const handleClick = jest.fn()
    const user = userEvent.setup()
    
    render(
      <FloatingActionButton onClick={handleClick}>
        <span>FAB</span>
      </FloatingActionButton>
    )
    
    await user.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})

describe('PulseNotification', () => {
  it('renders without count', () => {
    render(
      <PulseNotification>
        <button>Notification Test</button>
      </PulseNotification>
    )
    
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('displays count when provided', () => {
    render(
      <PulseNotification count={5}>
        <button>Notification Test</button>
      </PulseNotification>
    )
    
    expect(screen.getByText('5')).toBeInTheDocument()
  })

  it('displays 99+ for count over 99', () => {
    render(
      <PulseNotification count={150}>
        <button>Notification Test</button>
      </PulseNotification>
    )
    
    expect(screen.getByText('99+')).toBeInTheDocument()
  })

  it('does not display notification for zero count', () => {
    render(
      <PulseNotification count={0}>
        <button>Notification Test</button>
      </PulseNotification>
    )
    
    // Zero count should not show notification badge
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('applies correct color classes', () => {
    render(
      <PulseNotification count={3} color="blue">
        <button>Notification Test</button>
      </PulseNotification>
    )
    
    expect(screen.getByText('3')).toBeInTheDocument()
  })
})

describe('SmoothReveal', () => {
  it('renders children correctly', () => {
    render(
      <SmoothReveal>
        <div>Revealed content</div>
      </SmoothReveal>
    )
    
    expect(screen.getByText('Revealed content')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(
      <SmoothReveal className="custom-class">
        <div>Test content</div>
      </SmoothReveal>
    )
    
    expect(screen.getByText('Test content')).toBeInTheDocument()
  })
})

describe('StaggerContainer', () => {
  it('renders children correctly', () => {
    render(
      <StaggerContainer>
        <StaggerItem>
          <div>Item 1</div>
        </StaggerItem>
        <StaggerItem>
          <div>Item 2</div>
        </StaggerItem>
      </StaggerContainer>
    )
    
    expect(screen.getByText('Item 1')).toBeInTheDocument()
    expect(screen.getByText('Item 2')).toBeInTheDocument()
  })

  it('applies custom className to container', () => {
    render(
      <StaggerContainer className="stagger-container">
        <StaggerItem>
          <div>Test item</div>
        </StaggerItem>
      </StaggerContainer>
    )
    
    expect(screen.getByText('Test item')).toBeInTheDocument()
  })
})

describe('StaggerItem', () => {
  it('renders children correctly', () => {
    render(
      <StaggerItem>
        <div>Stagger item content</div>
      </StaggerItem>
    )
    
    expect(screen.getByText('Stagger item content')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(
      <StaggerItem className="stagger-item">
        <div>Test content</div>
      </StaggerItem>
    )
    
    expect(screen.getByText('Test content')).toBeInTheDocument()
  })
})

describe('Accessibility', () => {
  it('MagneticButton maintains focus management', async () => {
    const user = userEvent.setup()
    
    render(
      <MagneticButton>
        Accessible Button
      </MagneticButton>
    )
    
    const button = screen.getByRole('button')
    await user.tab()
    
    expect(button).toHaveFocus()
  })

  it('FloatingActionButton has proper ARIA attributes', () => {
    render(
      <FloatingActionButton tooltip="Accessible FAB">
        <span>FAB</span>
      </FloatingActionButton>
    )
    
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
  })

  it('PulseNotification maintains semantic structure', () => {
    render(
      <PulseNotification count={5}>
        <button aria-label="Notifications">Bell</button>
      </PulseNotification>
    )
    
    expect(screen.getByLabelText('Notifications')).toBeInTheDocument()
  })
})

describe('Performance', () => {
  it('does not cause memory leaks with multiple renders', () => {
    const { rerender, unmount } = render(
      <MagneticButton>Test</MagneticButton>
    )
    
    // Re-render multiple times
    for (let i = 0; i < 10; i++) {
      rerender(
        <MagneticButton>Test {i}</MagneticButton>
      )
    }
    
    unmount()
    // Should not throw any errors or warnings
  })

  it('handles rapid mouse movements without issues', async () => {
    const user = userEvent.setup()
    
    render(
      <MagneticButton>
        Rapid test
      </MagneticButton>
    )
    
    const button = screen.getByRole('button')
    
    // Simulate rapid mouse movements
    for (let i = 0; i < 3; i++) {
      await user.hover(button)
      await user.unhover(button)
    }
    
    expect(button).toBeInTheDocument()
  })
})