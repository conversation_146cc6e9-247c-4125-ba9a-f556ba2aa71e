/**
 * Role Badge Component Tests
 * 
 * Tests for the standardized role badge component to ensure
 * consistent role terminology and styling across the application.
 * 
 * <AUTHOR> Team
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import RoleBadge, { AdminRoleBadge, UserRoleIndicator } from '../RoleBadge'
import { ROLE_IDS } from '@/constants/roles'

describe('RoleBadge Component', () => {
  describe('Role Display Names', () => {
    it('displays "Super Admin" for super_admin role', () => {
      render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} />)
      expect(screen.getByText('Super Admin')).toBeInTheDocument()
    })

    it('displays "Admin" for admin role', () => {
      render(<RoleBadge role={ROLE_IDS.ADMIN} />)
      expect(screen.getByText('Admin')).toBeInTheDocument()
    })

    it('displays "Moderator" for moderator role', () => {
      render(<RoleBadge role={ROLE_IDS.MODERATOR} />)
      expect(screen.getByText('Moderator')).toBeInTheDocument()
    })

    it('displays "User" for user role', () => {
      render(<RoleBadge role={ROLE_IDS.USER} />)
      expect(screen.getByText('User')).toBeInTheDocument()
    })
  })

  describe('Role Styling', () => {
    it('applies special styling for super admin role', () => {
      render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} />)
      const badge = screen.getByRole('status')
      expect(badge).toHaveClass('ring-red-500/50')
    })

    it('applies admin styling for admin role', () => {
      render(<RoleBadge role={ROLE_IDS.ADMIN} />)
      const badge = screen.getByRole('status')
      expect(badge).toHaveClass('ring-orange-500/50')
    })

    it('does not apply admin styling for user role', () => {
      render(<RoleBadge role={ROLE_IDS.USER} />)
      const badge = screen.getByRole('status')
      expect(badge).not.toHaveClass('ring-red-500/50')
      expect(badge).not.toHaveClass('ring-orange-500/50')
    })
  })

  describe('Size Variants', () => {
    it('applies correct classes for xs size', () => {
      render(<RoleBadge role={ROLE_IDS.ADMIN} size="xs" />)
      const badge = screen.getByRole('status')
      expect(badge).toHaveClass('px-1.5', 'py-0.5', 'text-xs')
    })

    it('applies correct classes for lg size', () => {
      render(<RoleBadge role={ROLE_IDS.ADMIN} size="lg" />)
      const badge = screen.getByRole('status')
      expect(badge).toHaveClass('px-3', 'py-1.5', 'text-sm')
    })
  })

  describe('Accessibility', () => {
    it('has proper aria-label', () => {
      render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} />)
      const badge = screen.getByRole('status')
      expect(badge).toHaveAttribute('aria-label', 'User role: Super Admin')
    })

    it('has proper role attribute', () => {
      render(<RoleBadge role={ROLE_IDS.ADMIN} />)
      const badge = screen.getByRole('status')
      expect(badge).toHaveAttribute('role', 'status')
    })
  })

  describe('Icons', () => {
    it('shows icon by default', () => {
      render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} />)
      const icon = screen.getByRole('status').querySelector('svg')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-hidden', 'true')
    })

    it('hides icon when showIcon is false', () => {
      render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} showIcon={false} />)
      const icon = screen.getByRole('status').querySelector('svg')
      expect(icon).not.toBeInTheDocument()
    })
  })

  describe('Tooltips', () => {
    it('shows tooltip by default', () => {
      render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} />)
      // Tooltip content should be in the DOM but hidden
      expect(screen.getByText('Super Administrator with root-level access to all system features')).toBeInTheDocument()
    })

    it('hides tooltip when showTooltip is false', () => {
      render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} showTooltip={false} />)
      // Tooltip content should not be in the DOM
      expect(screen.queryByText('Super Administrator with root-level access to all system features')).not.toBeInTheDocument()
    })
  })
})

describe('AdminRoleBadge Component', () => {
  it('renders admin role correctly', () => {
    render(<AdminRoleBadge role="admin" />)
    expect(screen.getByText('Admin')).toBeInTheDocument()
  })

  it('renders super admin role correctly', () => {
    render(<AdminRoleBadge role="super_admin" />)
    expect(screen.getByText('Super Admin')).toBeInTheDocument()
  })
})

describe('UserRoleIndicator Component', () => {
  it('displays role name as text', () => {
    render(<UserRoleIndicator role={ROLE_IDS.MODERATOR} />)
    expect(screen.getByText('Moderator')).toBeInTheDocument()
  })

  it('applies correct color classes', () => {
    render(<UserRoleIndicator role={ROLE_IDS.SUPER_ADMIN} />)
    const indicator = screen.getByText('Super Admin')
    expect(indicator).toHaveClass('text-red-400')
  })
})

describe('Role Terminology Consistency', () => {
  it('ensures Super Admin is always displayed with space and title case', () => {
    render(<RoleBadge role={ROLE_IDS.SUPER_ADMIN} />)
    const text = screen.getByText('Super Admin')
    expect(text).toBeInTheDocument()
    expect(text.textContent).toBe('Super Admin')
    expect(text.textContent).not.toBe('super admin')
    expect(text.textContent).not.toBe('Super Administrator')
    expect(text.textContent).not.toBe('superadmin')
  })

  it('ensures Admin is always displayed in title case', () => {
    render(<RoleBadge role={ROLE_IDS.ADMIN} />)
    const text = screen.getByText('Admin')
    expect(text).toBeInTheDocument()
    expect(text.textContent).toBe('Admin')
    expect(text.textContent).not.toBe('admin')
    expect(text.textContent).not.toBe('ADMIN')
  })

  it('ensures all role names use consistent title case', () => {
    const roles = [
      { id: ROLE_IDS.USER, expected: 'User' },
      { id: ROLE_IDS.SUPPORT, expected: 'Support' },
      { id: ROLE_IDS.ANALYST, expected: 'Analyst' },
      { id: ROLE_IDS.MODERATOR, expected: 'Moderator' },
      { id: ROLE_IDS.ADMIN, expected: 'Admin' },
      { id: ROLE_IDS.SUPER_ADMIN, expected: 'Super Admin' }
    ]

    roles.forEach(({ id, expected }) => {
      const { unmount } = render(<RoleBadge role={id} />)
      expect(screen.getByText(expected)).toBeInTheDocument()
      unmount()
    })
  })
})
