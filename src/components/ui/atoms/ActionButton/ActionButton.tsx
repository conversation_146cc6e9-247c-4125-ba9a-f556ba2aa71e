/**
 * Action Button Component
 * 
 * Extracted from Phase 2 refactored components
 * Reusable button component with consistent styling and behavior
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { forwardRef } from 'react'
import { Loader2 } from 'lucide-react'
import { buildAnimationClasses, CSS_ANIMATIONS } from '../../../../lib/animations/cssAnimations'

/**
 * Button variant types
 */
export type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'ghost' | 'outline'

/**
 * Button size types
 */
export type ButtonSize = 'sm' | 'md' | 'lg' | 'xl'

/**
 * Action button props
 */
export interface ActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Button content */
  children: React.ReactNode
  /** Button variant */
  variant?: ButtonVariant
  /** Button size */
  size?: ButtonSize
  /** Loading state */
  loading?: boolean
  /** Icon to display */
  icon?: React.ReactNode
  /** Icon position */
  iconPosition?: 'left' | 'right'
  /** Full width button */
  fullWidth?: boolean
  /** Enable animations */
  animated?: boolean
  /** Custom class name */
  className?: string
}

/**
 * Get variant styles
 */
const getVariantStyles = (variant: ButtonVariant): string => {
  const variants = {
    primary: 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600 hover:border-purple-700',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700',
    danger: 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700',
    ghost: 'bg-transparent hover:bg-gray-700 text-gray-300 hover:text-white border-transparent',
    outline: 'bg-transparent hover:bg-purple-600 text-purple-400 hover:text-white border-purple-600'
  }
  
  return variants[variant]
}

/**
 * Get size styles
 */
const getSizeStyles = (size: ButtonSize): string => {
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  }
  
  return sizes[size]
}

/**
 * Action button component
 */
export const ActionButton = forwardRef<HTMLButtonElement, ActionButtonProps>(({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  animated = true,
  className = '',
  disabled,
  ...props
}, ref) => {
  // Build button classes
  const buttonClasses = buildAnimationClasses(
    [
      'inline-flex',
      'items-center',
      'justify-center',
      'gap-2',
      'font-medium',
      'rounded-lg',
      'border',
      'transition-all',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-purple-500',
      'focus:ring-offset-2',
      'focus:ring-offset-gray-900',
      'disabled:opacity-50',
      'disabled:cursor-not-allowed',
      getVariantStyles(variant),
      getSizeStyles(size),
      fullWidth ? 'w-full' : '',
      className
    ],
    animated ? [CSS_ANIMATIONS.buttonHover] : [],
    {
      [CSS_ANIMATIONS.scaleTap]: animated && !disabled && !loading
    }
  )

  // Determine if button should be disabled
  const isDisabled = disabled || loading

  return (
    <button
      ref={ref}
      className={buttonClasses}
      disabled={isDisabled}
      {...props}
    >
      {/* Loading spinner */}
      {loading && (
        <Loader2 size={size === 'sm' ? 14 : size === 'lg' ? 18 : size === 'xl' ? 20 : 16} className="animate-spin" />
      )}
      
      {/* Left icon */}
      {icon && iconPosition === 'left' && !loading && (
        <span className="flex-shrink-0">
          {icon}
        </span>
      )}
      
      {/* Button content */}
      <span className={loading ? 'opacity-75' : ''}>
        {children}
      </span>
      
      {/* Right icon */}
      {icon && iconPosition === 'right' && !loading && (
        <span className="flex-shrink-0">
          {icon}
        </span>
      )}
    </button>
  )
})

ActionButton.displayName = 'ActionButton'

/**
 * Button group component for related actions
 */
export interface ButtonGroupProps {
  children: React.ReactNode
  orientation?: 'horizontal' | 'vertical'
  spacing?: 'none' | 'sm' | 'md' | 'lg'
  className?: string
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  orientation = 'horizontal',
  spacing = 'md',
  className = ''
}) => {
  const getSpacingClass = () => {
    const spacingMap = {
      none: '',
      sm: orientation === 'horizontal' ? 'space-x-2' : 'space-y-2',
      md: orientation === 'horizontal' ? 'space-x-3' : 'space-y-3',
      lg: orientation === 'horizontal' ? 'space-x-4' : 'space-y-4'
    }
    
    return spacingMap[spacing]
  }

  const groupClasses = buildAnimationClasses(
    [
      'flex',
      orientation === 'horizontal' ? 'flex-row' : 'flex-col',
      getSpacingClass(),
      className
    ],
    [],
    {}
  )

  return (
    <div className={groupClasses}>
      {children}
    </div>
  )
}

/**
 * Icon button variant for icon-only buttons
 */
export interface IconButtonProps extends Omit<ActionButtonProps, 'children' | 'icon' | 'iconPosition'> {
  icon: React.ReactNode
  'aria-label': string
  tooltip?: string
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(({
  icon,
  size = 'md',
  variant = 'ghost',
  className = '',
  ...props
}, ref) => {
  const iconSizes = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-14 h-14'
  }

  return (
    <ActionButton
      ref={ref}
      variant={variant}
      size={size}
      className={`${iconSizes[size]} p-0 ${className}`}
      {...props}
    >
      {icon}
    </ActionButton>
  )
})

IconButton.displayName = 'IconButton'

export default ActionButton
