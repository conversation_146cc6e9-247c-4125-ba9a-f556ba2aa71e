/**
 * Progress Bar Component
 * 
 * Extracted from Phase 2 refactored components
 * Reusable progress indicator with animations and variants
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { buildAnimationClasses } from '../../../../lib/animations/cssAnimations'

/**
 * Progress bar size types
 */
export type ProgressSize = 'sm' | 'md' | 'lg' | 'xl'

/**
 * Progress bar color types
 */
export type ProgressColor = 'purple' | 'blue' | 'green' | 'yellow' | 'red' | 'gray'

/**
 * Progress bar variant types
 */
export type ProgressVariant = 'default' | 'striped' | 'animated' | 'gradient'

/**
 * Progress bar props
 */
export interface ProgressBarProps {
  /** Progress value (0-100) */
  value: number
  /** Maximum value */
  max?: number
  /** Progress color */
  color?: ProgressColor
  /** Progress bar size */
  size?: ProgressSize
  /** Progress variant */
  variant?: ProgressVariant
  /** Show progress label */
  showLabel?: boolean
  /** Show percentage */
  showPercentage?: boolean
  /** Custom label */
  label?: string
  /** Enable animation */
  animated?: boolean
  /** Custom class name */
  className?: string
  /** Custom label class name */
  labelClassName?: string
}

/**
 * Get size styles
 */
const getSizeStyles = (size: ProgressSize): string => {
  const sizes = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
    xl: 'h-4'
  }
  
  return sizes[size]
}

/**
 * Get color styles
 */
const getColorStyles = (color: ProgressColor, variant: ProgressVariant): string => {
  const baseColors = {
    purple: 'bg-purple-600',
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    yellow: 'bg-yellow-600',
    red: 'bg-red-600',
    gray: 'bg-gray-600'
  }
  
  const gradientColors = {
    purple: 'bg-gradient-to-r from-purple-600 to-purple-500',
    blue: 'bg-gradient-to-r from-blue-600 to-blue-500',
    green: 'bg-gradient-to-r from-green-600 to-green-500',
    yellow: 'bg-gradient-to-r from-yellow-600 to-yellow-500',
    red: 'bg-gradient-to-r from-red-600 to-red-500',
    gray: 'bg-gradient-to-r from-gray-600 to-gray-500'
  }
  
  if (variant === 'gradient') {
    return gradientColors[color]
  }
  
  return baseColors[color]
}

/**
 * Get variant styles
 */
const getVariantStyles = (variant: ProgressVariant): string => {
  const variants = {
    default: '',
    striped: 'bg-stripes',
    animated: 'bg-stripes animate-stripes',
    gradient: ''
  }
  
  return variants[variant]
}

/**
 * Progress bar component
 */
export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  color = 'purple',
  size = 'md',
  variant = 'default',
  showLabel = false,
  showPercentage = false,
  label,
  animated = true,
  className = '',
  labelClassName = ''
}) => {
  // Calculate percentage
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  // Build container classes
  const containerClasses = buildAnimationClasses(
    [
      'w-full',
      'bg-gray-700',
      'rounded-full',
      'overflow-hidden',
      getSizeStyles(size),
      className
    ],
    [],
    {}
  )
  
  // Build progress classes
  const progressClasses = buildAnimationClasses(
    [
      'h-full',
      'rounded-full',
      'transition-all',
      'duration-500',
      'ease-out',
      getColorStyles(color, variant),
      getVariantStyles(variant)
    ],
    [],
    {
      'will-change-transform': animated
    }
  )
  
  // Build label classes
  const labelClasses = buildAnimationClasses(
    [
      'flex',
      'justify-between',
      'items-center',
      'text-sm',
      'text-gray-300',
      'mb-1',
      labelClassName
    ],
    [],
    {}
  )

  return (
    <div className="w-full">
      {/* Label and percentage */}
      {(showLabel || showPercentage || label) && (
        <div className={labelClasses}>
          <span>{label || (showLabel ? 'Progress' : '')}</span>
          {showPercentage && (
            <span className="font-medium">
              {percentage.toFixed(0)}%
            </span>
          )}
        </div>
      )}
      
      {/* Progress bar */}
      <div className={containerClasses}>
        <div
          className={progressClasses}
          style={{ width: `${percentage}%` }}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
          aria-label={label || `Progress: ${percentage.toFixed(0)}%`}
        />
      </div>
    </div>
  )
}

/**
 * Circular progress component
 */
export interface CircularProgressProps {
  /** Progress value (0-100) */
  value: number
  /** Maximum value */
  max?: number
  /** Circle size */
  size?: number
  /** Stroke width */
  strokeWidth?: number
  /** Progress color */
  color?: ProgressColor
  /** Show percentage in center */
  showPercentage?: boolean
  /** Custom label */
  label?: string
  /** Custom class name */
  className?: string
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  color = 'purple',
  showPercentage = true,
  label,
  className = ''
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference
  
  const colorMap = {
    purple: '#9333ea',
    blue: '#2563eb',
    green: '#16a34a',
    yellow: '#ca8a04',
    red: '#dc2626',
    gray: '#4b5563'
  }

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#374151"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={colorMap[color]}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-500 ease-out"
        />
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          {showPercentage && (
            <div className="text-lg font-bold text-white">
              {percentage.toFixed(0)}%
            </div>
          )}
          {label && (
            <div className="text-xs text-gray-400 mt-1">
              {label}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

/**
 * Multi-step progress component
 */
export interface MultiStepProgressProps {
  /** Current step (0-based) */
  currentStep: number
  /** Total steps */
  totalSteps: number
  /** Step labels */
  stepLabels?: string[]
  /** Progress color */
  color?: ProgressColor
  /** Show step numbers */
  showStepNumbers?: boolean
  /** Custom class name */
  className?: string
}

export const MultiStepProgress: React.FC<MultiStepProgressProps> = ({
  currentStep,
  totalSteps,
  stepLabels = [],
  color = 'purple',
  showStepNumbers = true,
  className = ''
}) => {
  const percentage = ((currentStep + 1) / totalSteps) * 100

  return (
    <div className={`w-full ${className}`}>
      {/* Progress bar */}
      <ProgressBar
        value={percentage}
        color={color}
        variant="gradient"
        className="mb-4"
      />
      
      {/* Step indicators */}
      <div className="flex justify-between">
        {Array.from({ length: totalSteps }, (_, index) => {
          const isCompleted = index < currentStep
          const isCurrent = index === currentStep
          const stepLabel = stepLabels[index] || `Step ${index + 1}`
          
          return (
            <div
              key={index}
              className="flex flex-col items-center"
            >
              {/* Step circle */}
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                  isCompleted
                    ? `bg-${color}-600 text-white`
                    : isCurrent
                    ? `bg-${color}-600 text-white`
                    : 'bg-gray-600 text-gray-400'
                }`}
              >
                {showStepNumbers ? index + 1 : ''}
              </div>
              
              {/* Step label */}
              <span
                className={`text-xs mt-2 text-center ${
                  isCompleted || isCurrent ? 'text-white' : 'text-gray-400'
                }`}
              >
                {stepLabel}
              </span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default ProgressBar
