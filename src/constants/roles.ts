/**
 * Role Constants for Syndicaps
 * 
 * Centralized role definitions to ensure consistent terminology
 * throughout the application. This prevents inconsistencies between
 * display names, internal identifiers, and type definitions.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// ===== INTERNAL ROLE IDENTIFIERS =====
// These are used in code, database, and API calls
export const ROLE_IDS = {
  USER: 'user',
  ADMIN: 'admin', 
  SUPER_ADMIN: 'super_admin',
  MODERATOR: 'moderator',
  ANALYST: 'analyst',
  SUPPORT: 'support'
} as const

// ===== DISPLAY NAMES =====
// These are shown to users in the interface
export const ROLE_DISPLAY_NAMES = {
  [ROLE_IDS.USER]: 'User',
  [ROLE_IDS.ADMIN]: 'Admin',
  [ROLE_IDS.SUPER_ADMIN]: 'Super Admin',
  [ROLE_IDS.MODERATOR]: 'Moderator', 
  [ROLE_IDS.ANALYST]: 'Analyst',
  [ROLE_IDS.SUPPORT]: 'Support'
} as const

// ===== ROLE DESCRIPTIONS =====
// Detailed descriptions for tooltips and help text
export const ROLE_DESCRIPTIONS = {
  [ROLE_IDS.USER]: 'Standard user with basic access to profile and shopping features',
  [ROLE_IDS.ADMIN]: 'Administrator with full access to most admin panel features',
  [ROLE_IDS.SUPER_ADMIN]: 'Super Administrator with root-level access to all system features',
  [ROLE_IDS.MODERATOR]: 'Community moderator with content management permissions',
  [ROLE_IDS.ANALYST]: 'Data analyst with read-only access to analytics and reports',
  [ROLE_IDS.SUPPORT]: 'Support team member with customer service permissions'
} as const

// ===== ROLE HIERARCHY =====
// Defines the permission hierarchy (higher number = more permissions)
export const ROLE_HIERARCHY = {
  [ROLE_IDS.USER]: 0,
  [ROLE_IDS.SUPPORT]: 1,
  [ROLE_IDS.ANALYST]: 2,
  [ROLE_IDS.MODERATOR]: 3,
  [ROLE_IDS.ADMIN]: 4,
  [ROLE_IDS.SUPER_ADMIN]: 5
} as const

// ===== ROLE COLORS =====
// Colors for badges and UI elements
export const ROLE_COLORS = {
  [ROLE_IDS.USER]: {
    bg: 'bg-gray-500/20',
    text: 'text-gray-400',
    border: 'border-gray-500/30'
  },
  [ROLE_IDS.SUPPORT]: {
    bg: 'bg-blue-500/20',
    text: 'text-blue-400',
    border: 'border-blue-500/30'
  },
  [ROLE_IDS.ANALYST]: {
    bg: 'bg-green-500/20',
    text: 'text-green-400',
    border: 'border-green-500/30'
  },
  [ROLE_IDS.MODERATOR]: {
    bg: 'bg-purple-500/20',
    text: 'text-purple-400',
    border: 'border-purple-500/30'
  },
  [ROLE_IDS.ADMIN]: {
    bg: 'bg-orange-500/20',
    text: 'text-orange-400',
    border: 'border-orange-500/30'
  },
  [ROLE_IDS.SUPER_ADMIN]: {
    bg: 'bg-red-500/20',
    text: 'text-red-400',
    border: 'border-red-500/30'
  }
} as const

// ===== ROLE ICONS =====
// Icon mappings for each role
export const ROLE_ICONS = {
  [ROLE_IDS.USER]: 'User',
  [ROLE_IDS.SUPPORT]: 'Headphones',
  [ROLE_IDS.ANALYST]: 'BarChart3',
  [ROLE_IDS.MODERATOR]: 'Shield',
  [ROLE_IDS.ADMIN]: 'Settings',
  [ROLE_IDS.SUPER_ADMIN]: 'Crown'
} as const

// ===== TYPE DEFINITIONS =====
export type RoleId = typeof ROLE_IDS[keyof typeof ROLE_IDS]
export type RoleDisplayName = typeof ROLE_DISPLAY_NAMES[keyof typeof ROLE_DISPLAY_NAMES]

// ===== UTILITY FUNCTIONS =====

/**
 * Get display name for a role ID
 */
export function getRoleDisplayName(roleId: RoleId): string {
  return ROLE_DISPLAY_NAMES[roleId] || 'Unknown Role'
}

/**
 * Get role description
 */
export function getRoleDescription(roleId: RoleId): string {
  return ROLE_DESCRIPTIONS[roleId] || 'No description available'
}

/**
 * Get role colors
 */
export function getRoleColors(roleId: RoleId) {
  return ROLE_COLORS[roleId] || ROLE_COLORS[ROLE_IDS.USER]
}

/**
 * Get role hierarchy level
 */
export function getRoleLevel(roleId: RoleId): number {
  return ROLE_HIERARCHY[roleId] || 0
}

/**
 * Check if role has higher or equal permissions than another role
 */
export function hasRolePermission(userRole: RoleId, requiredRole: RoleId): boolean {
  return getRoleLevel(userRole) >= getRoleLevel(requiredRole)
}

/**
 * Check if role is admin level or higher
 */
export function isAdminRole(roleId: RoleId): boolean {
  return getRoleLevel(roleId) >= getRoleLevel(ROLE_IDS.ADMIN)
}

/**
 * Check if role is super admin
 */
export function isSuperAdminRole(roleId: RoleId): boolean {
  return roleId === ROLE_IDS.SUPER_ADMIN
}

/**
 * Get all admin roles (admin and super admin)
 */
export function getAdminRoles(): RoleId[] {
  return [ROLE_IDS.ADMIN, ROLE_IDS.SUPER_ADMIN]
}

/**
 * Get all available roles
 */
export function getAllRoles(): RoleId[] {
  return Object.values(ROLE_IDS)
}

/**
 * Validate if a string is a valid role ID
 */
export function isValidRoleId(roleId: string): roleId is RoleId {
  return Object.values(ROLE_IDS).includes(roleId as RoleId)
}

// ===== ROLE GROUPS =====
// Logical groupings of roles for different purposes

export const ROLE_GROUPS = {
  ADMIN_ROLES: [ROLE_IDS.ADMIN, ROLE_IDS.SUPER_ADMIN],
  STAFF_ROLES: [ROLE_IDS.SUPPORT, ROLE_IDS.ANALYST, ROLE_IDS.MODERATOR, ROLE_IDS.ADMIN, ROLE_IDS.SUPER_ADMIN],
  COMMUNITY_ROLES: [ROLE_IDS.MODERATOR, ROLE_IDS.ADMIN, ROLE_IDS.SUPER_ADMIN],
  ANALYTICS_ROLES: [ROLE_IDS.ANALYST, ROLE_IDS.ADMIN, ROLE_IDS.SUPER_ADMIN]
} as const

// ===== LEGACY COMPATIBILITY =====
// For backward compatibility with existing code

/**
 * @deprecated Use ROLE_IDS.SUPER_ADMIN instead
 */
export const SUPER_ADMIN = ROLE_IDS.SUPER_ADMIN

/**
 * @deprecated Use ROLE_IDS.ADMIN instead  
 */
export const ADMIN = ROLE_IDS.ADMIN

/**
 * @deprecated Use getRoleDisplayName() instead
 */
export const ADMIN_DISPLAY_NAMES = {
  admin: 'Admin',
  super_admin: 'Super Admin',
  superadmin: 'Super Admin' // Legacy support
} as const

// Export everything for easy importing
export default {
  ROLE_IDS,
  ROLE_DISPLAY_NAMES,
  ROLE_DESCRIPTIONS,
  ROLE_HIERARCHY,
  ROLE_COLORS,
  ROLE_ICONS,
  ROLE_GROUPS,
  getRoleDisplayName,
  getRoleDescription,
  getRoleColors,
  getRoleLevel,
  hasRolePermission,
  isAdminRole,
  isSuperAdminRole,
  getAdminRoles,
  getAllRoles,
  isValidRoleId
}
