/**
 * Unified Account Data Hook
 * 
 * Provides a single source of truth for all account-related data
 * to eliminate redundancy and ensure consistency across components.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import { useMemo } from 'react'
import { UserProfile } from '@/types/profile'
import {
  getTierInfoByPoints,
  getTierProgress,
  getTierDiscount,
  hasFreeShipping,
  getEarlyAccessHours,
  getTierInfo,
  type MemberTier
} from '@/lib/memberTiers'
import { ProfileCompletionManager } from '@/lib/profileCompletion'
import { getRoleDisplayName, isAdminRole } from '@/constants/roles'

export interface AccountData {
  // User Identity
  user: {
    id: string
    email: string
    displayName: string
    avatar?: string
    role: string
    roleDisplayName: string
    isAdmin: boolean
  }
  
  // Points & Tier System
  points: {
    current: number
    lifetime: number
    pending: number
    nextTierThreshold: number | null
    pointsToNextTier: number | null
  }
  
  // Membership & Tier
  tier: {
    current: string
    level: number
    color: string
    benefits: string[]
    discount: number
    freeShipping: boolean
    earlyAccessHours: number
    progress: number
    nextTier: string | null
  }
  
  // Profile Completion
  completion: {
    percentage: number
    completedSections: number
    totalSections: number
    pointsEarned: number
    pointsPossible: number
    nextActions: Array<{
      id: string
      title: string
      description: string
      points: number
      priority: 'high' | 'medium' | 'low'
      action: string
    }>
  }
  
  // Statistics
  stats: {
    orders: number
    achievements: number
    activityScore: number
    daysActive: number
    profileViews: number
    communityRank: number
  }
  
  // Activity & Engagement
  activity: {
    lastLogin: Date | null
    recentActions: Array<{
      type: string
      description: string
      timestamp: Date
      points?: number
    }>
    streakDays: number
    totalLogins: number
  }
}

/**
 * Hook to get unified account data
 */
export function useAccountData(profile: UserProfile | null): AccountData | null {
  return useMemo(() => {
    if (!profile) return null

    // Get tier information
    const tierInfo = getTierInfoByPoints(profile.points || 0)
    const tierProgress = getTierProgress(profile.points || 0)
    
    // Get next tier info
    const tierOrder: MemberTier[] = ['bronze', 'silver', 'gold', 'platinum', 'diamond']
    const currentTierIndex = tierOrder.findIndex(t => t === tierInfo.tier.toLowerCase())
    const nextTierKey = currentTierIndex < tierOrder.length - 1
      ? tierOrder[currentTierIndex + 1]
      : null
    const nextTier = nextTierKey ? getTierInfo(nextTierKey) : null
    
    // Calculate profile completion
    const completion = ProfileCompletionManager.calculateCompletion(profile)
    const suggestionData = ProfileCompletionManager.getCompletionSuggestions(profile)
    const suggestions = suggestionData?.nextSteps || []
    
    // Get role information
    const roleDisplayName = getRoleDisplayName(profile.role || 'user')
    const isAdmin = isAdminRole(profile.role || 'user')
    
    // Calculate points to next tier
    const pointsToNextTier = nextTier
      ? nextTier.minPoints - (profile.points || 0)
      : null
    
    // Process recent activity
    const recentActions = [
      {
        type: 'login',
        description: 'You logged into your account',
        timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        points: 0
      },
      {
        type: 'points',
        description: 'You earned 50 points from profile completion',
        timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
        points: 50
      },
      {
        type: 'profile',
        description: 'You updated your profile information',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        points: 0
      }
    ]

    return {
      user: {
        id: profile.id || '',
        email: profile.email || '',
        displayName: profile.displayName || `${profile.firstName} ${profile.lastName}`.trim() || 'User',
        avatar: profile.avatar,
        role: profile.role || 'user',
        roleDisplayName,
        isAdmin
      },
      
      points: {
        current: profile.points || 0,
        lifetime: profile.lifetimePoints || profile.points || 0,
        pending: profile.pendingPoints || 0,
        nextTierThreshold: nextTier?.minPoints || null,
        pointsToNextTier
      },
      
      tier: {
        current: tierInfo.name,
        level: tierInfo.level,
        color: tierInfo.color,
        benefits: tierInfo.benefits,
        discount: getTierDiscount(tierInfo.tier),
        freeShipping: hasFreeShipping(tierInfo.tier),
        earlyAccessHours: getEarlyAccessHours(tierInfo.tier),
        progress: tierProgress.progress,
        nextTier: nextTier?.name || null
      },
      
      completion: {
        percentage: completion?.percentage || 0,
        completedSections: completion?.completedSections?.length || 0,
        totalSections: (completion?.completedSections?.length || 0) + suggestions.length,
        pointsEarned: completion?.completedSections?.reduce((sum, section) => sum + (section.points || 0), 0) || 0,
        pointsPossible: (completion?.completedSections?.reduce((sum, section) => sum + (section.points || 0), 0) || 0) +
                       suggestions.reduce((sum, suggestion) => sum + (suggestion.pointsReward || 0), 0),
        nextActions: suggestions.slice(0, 5).map(suggestion => ({
          id: suggestion.sectionId || '',
          title: suggestion.name || '',
          description: suggestion.description || '',
          points: suggestion.pointsReward || 0,
          priority: suggestion.priority || 'medium',
          action: suggestion.sectionId || ''
        }))
      },
      
      stats: {
        orders: profile.orderCount || 0,
        achievements: profile.achievementCount || 0,
        activityScore: profile.activityScore || 1000,
        daysActive: Math.floor((Date.now() - (profile.createdAt?.toMillis() || Date.now())) / (1000 * 60 * 60 * 24)),
        profileViews: profile.profileViews || 0,
        communityRank: profile.communityRank || 0
      },
      
      activity: {
        lastLogin: profile.lastLoginAt?.toDate() || null,
        recentActions,
        streakDays: profile.loginStreak || 0,
        totalLogins: profile.totalLogins || 1
      }
    }
  }, [profile])
}

/**
 * Hook to get specific account data sections
 */
export function useAccountDataSection<T extends keyof AccountData>(
  profile: UserProfile | null, 
  section: T
): AccountData[T] | null {
  const accountData = useAccountData(profile)
  return accountData?.[section] || null
}

/**
 * Validation function to ensure data consistency
 */
export function validateAccountData(data: AccountData): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  // Validate points consistency
  if (data.points.current < 0) {
    errors.push('Current points cannot be negative')
  }
  
  if (data.points.lifetime < data.points.current) {
    errors.push('Lifetime points should be greater than or equal to current points')
  }
  
  // Validate tier consistency
  const expectedTier = getTierInfoByPoints(data.points.current)
  if (data.tier.current.toLowerCase() !== expectedTier.tier.toLowerCase()) {
    errors.push(`Tier mismatch: expected ${expectedTier.tier}, got ${data.tier.current}`)
  }
  
  // Validate completion percentage
  if (data.completion.percentage < 0 || data.completion.percentage > 100) {
    errors.push('Completion percentage must be between 0 and 100')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export type { AccountData }
