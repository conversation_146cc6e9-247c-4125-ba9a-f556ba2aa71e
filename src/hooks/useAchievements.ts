/**
 * Achievement Hooks
 * 
 * React hooks for achievement system integration with real-time updates
 * and optimistic UI updates for better user experience.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  Unsubscribe,
  getDocs,
  getDoc,
  doc
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { collections } from '../lib/api/gamification/gamificationCollections'
import { 
  AchievementEngine, 
  Achievement, 
  UserAchievement, 
  UserActivity,
  AchievementCategory,
  AchievementRarity
} from '../lib/api/gamification/achievementEngine'
import { gamificationCache, CacheKeys } from '../lib/api/gamification/cachingService'
import { toast } from 'react-hot-toast'

// ===== TYPES =====

export interface UseAchievementsReturn {
  // Data
  achievements: Achievement[]
  userAchievements: UserAchievement[]
  unlockedAchievements: UserAchievement[]
  inProgressAchievements: UserAchievement[]
  
  // Stats
  totalAchievements: number
  completedCount: number
  completionPercentage: number
  recentUnlocks: UserAchievement[]
  
  // State
  loading: boolean
  error: string | null
  
  // Actions
  processActivity: (activity: UserActivity) => Promise<string[]>
  refresh: () => Promise<void>
  getAchievementsByCategory: (category: AchievementCategory) => Achievement[]
  getAchievementsByRarity: (rarity: AchievementRarity) => Achievement[]
}

export interface UseAchievementProgressReturn {
  progress: number
  isCompleted: boolean
  isInProgress: boolean
  requirements: Array<{
    type: string
    current: number
    target: number
    progress: number
    isCompleted: boolean
  }>
  estimatedTimeToCompletion?: string
  nextMilestone?: {
    type: string
    current: number
    target: number
    remaining: number
  }
}

export interface AchievementNotification {
  id: string
  achievementId: string
  title: string
  description: string
  icon: string
  rarity: AchievementRarity
  points: number
  timestamp: Date
  isNew: boolean
}

// ===== MAIN ACHIEVEMENTS HOOK =====

export function useAchievements(userId: string | null): UseAchievementsReturn {
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [userAchievements, setUserAchievements] = useState<UserAchievement[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const unsubscribeRef = useRef<Unsubscribe | null>(null)
  const userUnsubscribeRef = useRef<Unsubscribe | null>(null)

  // Load achievements data
  const loadAchievements = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Load cached achievements first
      const cached = await gamificationCache.get<Achievement[]>(CacheKeys.achievementCatalog())
      if (cached) {
        setAchievements(cached)
        setLoading(false)
      }

      // Set up real-time listener for achievements
      const achievementsQuery = query(
        collection(db, collections.achievements),
        where('isActive', '==', true),
        orderBy('sortOrder', 'asc')
      )

      unsubscribeRef.current = onSnapshot(
        achievementsQuery,
        (snapshot) => {
          const achievementsList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Achievement[]
          
          setAchievements(achievementsList)
          
          // Update cache
          gamificationCache.set(
            CacheKeys.achievementCatalog(),
            achievementsList,
            15 * 60 * 1000, // 15 minutes
            ['achievement-catalog']
          )
          
          setLoading(false)
        },
        (err) => {
          console.error('Error listening to achievements:', err)
          setError('Failed to load achievements')
          setLoading(false)
        }
      )
    } catch (err) {
      console.error('Error loading achievements:', err)
      setError('Failed to load achievements')
      setLoading(false)
    }
  }, [])

  // Load user achievements
  const loadUserAchievements = useCallback(async () => {
    if (!userId) {
      setUserAchievements([])
      return
    }

    try {
      // Load cached user achievements first
      const cached = await gamificationCache.get<UserAchievement[]>(
        CacheKeys.userAchievements(userId)
      )
      if (cached) {
        setUserAchievements(cached)
      }

      // Set up real-time listener for user achievements
      const userAchievementsQuery = query(
        collection(db, collections.userAchievements),
        where('userId', '==', userId),
        orderBy('updatedAt', 'desc')
      )

      userUnsubscribeRef.current = onSnapshot(
        userAchievementsQuery,
        (snapshot) => {
          const userAchievementsList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as UserAchievement[]
          
          setUserAchievements(userAchievementsList)
          
          // Update cache
          gamificationCache.set(
            CacheKeys.userAchievements(userId),
            userAchievementsList,
            5 * 60 * 1000, // 5 minutes
            ['user-achievements']
          )
          
          // Check for new unlocks and show notifications
          const newUnlocks = userAchievementsList.filter(ua => 
            ua.isCompleted && 
            ua.unlockedAt && 
            new Date(ua.unlockedAt.toDate()).getTime() > Date.now() - 30000 // Last 30 seconds
          )
          
          newUnlocks.forEach(unlock => {
            const achievement = achievements.find(a => a.id === unlock.achievementId)
            if (achievement) {
              showAchievementNotification(achievement, unlock)
            }
          })
        },
        (err) => {
          console.error('Error listening to user achievements:', err)
          setError('Failed to load user achievements')
        }
      )
    } catch (err) {
      console.error('Error loading user achievements:', err)
      setError('Failed to load user achievements')
    }
  }, [userId, achievements])

  // Process user activity and check for achievement unlocks
  const processActivity = useCallback(async (activity: UserActivity): Promise<string[]> => {
    if (!userId) return []

    try {
      const unlockedIds = await AchievementEngine.processActivity(activity)
      
      if (unlockedIds.length > 0) {
        // Refresh user achievements to get latest data
        await loadUserAchievements()
        
        // Show notifications for newly unlocked achievements
        for (const achievementId of unlockedIds) {
          const achievement = achievements.find(a => a.id === achievementId)
          const userAchievement = userAchievements.find(ua => 
            ua.achievementId === achievementId && ua.isCompleted
          )
          
          if (achievement && userAchievement) {
            showAchievementNotification(achievement, userAchievement)
          }
        }
      }
      
      return unlockedIds
    } catch (error) {
      console.error('Error processing activity:', error)
      return []
    }
  }, [userId, achievements, userAchievements, loadUserAchievements])

  // Refresh all data
  const refresh = useCallback(async () => {
    await Promise.all([loadAchievements(), loadUserAchievements()])
  }, [loadAchievements, loadUserAchievements])

  // Filter achievements by category
  const getAchievementsByCategory = useCallback((category: AchievementCategory) => {
    return achievements.filter(achievement => achievement.category === category)
  }, [achievements])

  // Filter achievements by rarity
  const getAchievementsByRarity = useCallback((rarity: AchievementRarity) => {
    return achievements.filter(achievement => achievement.rarity === rarity)
  }, [achievements])

  // Computed values
  const unlockedAchievements = userAchievements.filter(ua => ua.isCompleted)
  const inProgressAchievements = userAchievements.filter(ua => !ua.isCompleted && ua.progress > 0)
  const totalAchievements = achievements.length
  const completedCount = unlockedAchievements.length
  const completionPercentage = totalAchievements > 0 ? (completedCount / totalAchievements) * 100 : 0
  const recentUnlocks = unlockedAchievements
    .filter(ua => ua.unlockedAt)
    .sort((a, b) => b.unlockedAt!.toDate().getTime() - a.unlockedAt!.toDate().getTime())
    .slice(0, 5)

  // Load data on mount and when userId changes
  useEffect(() => {
    loadAchievements()
  }, [loadAchievements])

  useEffect(() => {
    loadUserAchievements()
  }, [loadUserAchievements])

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }
      if (userUnsubscribeRef.current) {
        userUnsubscribeRef.current()
      }
    }
  }, [])

  return {
    // Data
    achievements,
    userAchievements,
    unlockedAchievements,
    inProgressAchievements,
    
    // Stats
    totalAchievements,
    completedCount,
    completionPercentage,
    recentUnlocks,
    
    // State
    loading,
    error,
    
    // Actions
    processActivity,
    refresh,
    getAchievementsByCategory,
    getAchievementsByRarity
  }
}

// ===== ACHIEVEMENT PROGRESS HOOK =====

export function useAchievementProgress(
  userId: string | null,
  achievementId: string
): UseAchievementProgressReturn {
  const [progress, setProgress] = useState(0)
  const [isCompleted, setIsCompleted] = useState(false)
  const [requirements, setRequirements] = useState<any[]>([])

  useEffect(() => {
    if (!userId || !achievementId) return

    const loadProgress = async () => {
      try {
        const userAchievements = await gamificationCache.getOrSet(
          CacheKeys.userAchievements(userId),
          async () => {
            // Fetch from database if not cached
            const q = query(
              collection(db, collections.userAchievements),
              where('userId', '==', userId),
              where('achievementId', '==', achievementId)
            )
            const snapshot = await getDocs(q)
            return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
          },
          5 * 60 * 1000,
          ['user-achievements']
        )

        const userAchievement = userAchievements.find(ua => ua.achievementId === achievementId)
        
        if (userAchievement) {
          setProgress(userAchievement.progress || 0)
          setIsCompleted(userAchievement.isCompleted || false)
        }

        // Load achievement details for requirements
        const achievement = await gamificationCache.getOrSet(
          `achievement_${achievementId}`,
          async () => {
            const achievementDoc = await getDoc(doc(db, collections.achievements, achievementId))
            return achievementDoc.exists() ? { id: achievementDoc.id, ...achievementDoc.data() } : null
          },
          15 * 60 * 1000,
          ['achievement-catalog']
        )

        if (achievement && achievement.requirements) {
          // Transform requirements for display
          const transformedRequirements = achievement.requirements.map(req => ({
            type: req.type,
            current: 0, // This would need to be calculated based on user stats
            target: req.target,
            progress: 0,
            isCompleted: false
          }))
          
          setRequirements(transformedRequirements)
        }
      } catch (error) {
        console.error('Error loading achievement progress:', error)
      }
    }

    loadProgress()
  }, [userId, achievementId])

  const isInProgress = progress > 0 && !isCompleted
  
  return {
    progress,
    isCompleted,
    isInProgress,
    requirements,
    estimatedTimeToCompletion: undefined, // Would need historical data to calculate
    nextMilestone: undefined // Would need to analyze requirements
  }
}

// ===== ACHIEVEMENT NOTIFICATIONS HOOK =====

export function useAchievementNotifications() {
  const [notifications, setNotifications] = useState<AchievementNotification[]>([])

  const addNotification = useCallback((achievement: Achievement, userAchievement: UserAchievement) => {
    const notification: AchievementNotification = {
      id: `${achievement.id}_${Date.now()}`,
      achievementId: achievement.id!,
      title: achievement.title,
      description: achievement.description,
      icon: achievement.icon,
      rarity: achievement.rarity,
      points: achievement.rewards.points,
      timestamp: new Date(),
      isNew: true
    }

    setNotifications(prev => [notification, ...prev.slice(0, 4)]) // Keep last 5

    // Auto-remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id))
    }, 5000)
  }, [])

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }, [])

  const clearAllNotifications = useCallback(() => {
    setNotifications([])
  }, [])

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications
  }
}

// ===== HELPER FUNCTIONS =====

function showAchievementNotification(achievement: Achievement, userAchievement: UserAchievement) {
  const rarityColors = {
    common: 'bg-gray-500',
    uncommon: 'bg-green-500',
    rare: 'bg-blue-500',
    epic: 'bg-purple-500',
    legendary: 'bg-yellow-500'
  }

  toast.custom((t) => (
    <div
      className={`
        ${t.visible ? 'animate-enter' : 'animate-leave'}
        max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto
        flex ring-1 ring-black ring-opacity-5
      `}
    >
      <div className="flex-1 w-0 p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <div className={`
              w-10 h-10 rounded-full flex items-center justify-center text-white text-lg
              ${rarityColors[achievement.rarity]}
            `}>
              {achievement.icon}
            </div>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              🎉 Achievement Unlocked!
            </p>
            <p className="text-sm font-bold text-gray-900 dark:text-white">
              {achievement.title}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              +{achievement.rewards.points} points
            </p>
          </div>
        </div>
      </div>
      <div className="flex border-l border-gray-200">
        <button
          onClick={() => toast.dismiss(t.id)}
          className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        >
          Close
        </button>
      </div>
    </div>
  ), {
    duration: 5000,
    position: 'top-right'
  })
}

export default useAchievements