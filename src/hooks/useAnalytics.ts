/**
 * User Analytics Hooks
 * 
 * Simplified analytics hooks for tracking user interactions in the shop,
 * product views, and funnel progression.
 * 
 * <AUTHOR> Team
 */

'use client'

import { useCallback } from 'react'

// Types for analytics tracking
interface TrackingEvent {
  action: string
  properties?: Record<string, any>
  timestamp?: Date
}

interface ProductActionEvent extends TrackingEvent {
  productId: string
  action: 'view' | 'add_to_cart' | 'add_to_wishlist' | 'share' | 'quick_view' | 'image_change' | 'scroll'
}

interface SearchEvent extends TrackingEvent {
  query: string
  filters?: Record<string, any>
  resultCount: number
}

interface FunnelEvent extends TrackingEvent {
  step: 'product_view' | 'add_to_cart' | 'checkout_start' | 'checkout_complete'
  productId: string
  value?: number
}

/**
 * Hook for tracking product actions
 */
export const useProductActionTracking = (productId: string) => {
  const trackAction = useCallback((action: ProductActionEvent['action'], properties?: Record<string, any>) => {
    // In a real implementation, this would send data to your analytics service
    // For now, we'll just log to console
    const event: ProductActionEvent = {
      productId,
      action,
      properties,
      timestamp: new Date()
    }
    
    console.log('Product Action:', event)
    
    // TODO: Integrate with actual analytics service (GA4, Mixpanel, etc.)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        product_id: productId,
        ...properties
      })
    }
  }, [productId])

  return { trackAction }
}

/**
 * Hook for tracking search interactions
 */
export const useSearchTracking = () => {
  const trackSearch = useCallback((query: string, filters?: Record<string, any>, resultCount: number = 0) => {
    const event: SearchEvent = {
      action: 'search',
      query,
      filters,
      resultCount,
      timestamp: new Date()
    }
    
    console.log('Search Event:', event)
    
    // TODO: Integrate with actual analytics service
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'search', {
        search_term: query,
        filters: JSON.stringify(filters),
        result_count: resultCount
      })
    }
  }, [])

  return { trackSearch }
}

/**
 * Hook for tracking funnel progression
 */
export const useFunnelTracking = () => {
  const trackFunnelStep = useCallback((step: FunnelEvent['step'], productId: string, value?: number) => {
    const event: FunnelEvent = {
      action: 'funnel_step',
      step,
      productId,
      value,
      timestamp: new Date()
    }
    
    console.log('Funnel Event:', event)
    
    // TODO: Integrate with actual analytics service
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', step, {
        product_id: productId,
        value: value
      })
    }
  }, [])

  return { trackFunnelStep }
}

/**
 * Mock analytics service for development
 */
export const analyticsService = {
  /**
   * Track custom event
   */
  track: (eventName: string, properties?: Record<string, any>) => {
    console.log('Analytics Track:', { eventName, properties, timestamp: new Date() })
    
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, properties)
    }
  },

  /**
   * Identify user
   */
  identify: (userId: string, traits?: Record<string, any>) => {
    console.log('Analytics Identify:', { userId, traits, timestamp: new Date() })
    
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        user_id: userId,
        custom_map: traits
      })
    }
  },

  /**
   * Track page view
   */
  page: (pageName: string, properties?: Record<string, any>) => {
    console.log('Analytics Page:', { pageName, properties, timestamp: new Date() })
    
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: pageName,
        page_location: window.location.href,
        ...properties
      })
    }
  }
}

// Extend the Window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void
  }
}