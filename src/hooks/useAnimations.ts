/**
 * Animations Hook
 * 
 * React hook for managing animations with accessibility considerations,
 * reduced motion support, and gamification-specific micro-interactions.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { useMemo, useCallback, useRef, useEffect } from 'react'
import { useAnimation, AnimationControls, Variants } from 'framer-motion'
import { useTheme } from '../lib/theme/ThemeProvider'
import {
  AnimationDuration,
  AnimationEasing,
  durations,
  easings,
  fadeVariants,
  slideVariants,
  scaleVariants,
  rotateVariants,
  buttonVariants,
  cardVariants,
  inputVariants,
  achievementVariants,
  pointsVariants,
  tierVariants,
  challengeVariants,
  progressBarVariants,
  notificationVariants,
  toastVariants,
  listVariants,
  listItemVariants,
  modalVariants,
  backdropVariants,
  spinnerVariants,
  pulseVariants,
  createReducedMotionVariant,
  getAnimationConfig
} from '../lib/animations/animationConfig'

// ===== TYPES =====

export interface AnimationOptions {
  duration?: AnimationDuration
  easing?: AnimationEasing
  delay?: number
  respectReducedMotion?: boolean
  onComplete?: () => void
}

export interface AnimationTrigger {
  trigger: () => void
  reset: () => void
  isAnimating: boolean
}

export interface GamificationAnimations {
  achievementUnlock: (element: HTMLElement) => Promise<void>
  pointsGain: (amount: number, element: HTMLElement) => Promise<void>
  pointsLoss: (amount: number, element: HTMLElement) => Promise<void>
  tierPromotion: (newTier: string, element: HTMLElement) => Promise<void>
  challengeComplete: (element: HTMLElement) => Promise<void>
  progressUpdate: (progress: number, element: HTMLElement) => Promise<void>
}

// ===== MAIN HOOK =====

export function useAnimations() {
  const { systemPrefersReducedMotion } = useTheme()
  const controls = useAnimation()
  const animationQueue = useRef<(() => Promise<void>)[]>([])
  const isProcessingQueue = useRef(false)

  /**
   * Get animation variants with reduced motion support
   */
  const getVariants = useCallback((variants: Variants, respectReducedMotion = true): Variants => {
    if (respectReducedMotion && systemPrefersReducedMotion) {
      return createReducedMotionVariant(variants)
    }
    return variants
  }, [systemPrefersReducedMotion])

  /**
   * Create animation trigger with queue support
   */
  const createAnimationTrigger = useCallback((
    variants: Variants,
    targetState: string,
    options: AnimationOptions = {}
  ): AnimationTrigger => {
    const animationControls = useAnimation()
    let isAnimating = false

    const trigger = async () => {
      if (isAnimating) return

      isAnimating = true
      const effectiveVariants = getVariants(variants, options.respectReducedMotion)
      
      try {
        await animationControls.start(targetState, {
          duration: options.duration ? durations[options.duration] : undefined,
          ease: options.easing ? easings[options.easing] as any : undefined,
          delay: options.delay
        })
        options.onComplete?.()
      } finally {
        isAnimating = false
      }
    }

    const reset = () => {
      animationControls.stop()
      isAnimating = false
    }

    return { trigger, reset, isAnimating }
  }, [getVariants])

  /**
   * Queue animation for sequential execution
   */
  const queueAnimation = useCallback((animationFn: () => Promise<void>) => {
    animationQueue.current.push(animationFn)
    
    if (!isProcessingQueue.current) {
      processAnimationQueue()
    }
  }, [])

  /**
   * Process animation queue sequentially
   */
  const processAnimationQueue = useCallback(async () => {
    if (isProcessingQueue.current) return
    
    isProcessingQueue.current = true
    
    while (animationQueue.current.length > 0) {
      const animation = animationQueue.current.shift()
      if (animation) {
        await animation()
      }
    }
    
    isProcessingQueue.current = false
  }, [])

  /**
   * Animate element with CSS transforms
   */
  const animateElement = useCallback((
    element: HTMLElement,
    keyframes: Keyframe[] | PropertyIndexedKeyframes,
    options: KeyframeAnimationOptions = {}
  ): Promise<void> => {
    return new Promise((resolve) => {
      if (systemPrefersReducedMotion) {
        resolve()
        return
      }

      const animation = element.animate(keyframes, {
        duration: 300,
        easing: 'ease',
        fill: 'forwards',
        ...options
      })

      animation.addEventListener('finish', () => resolve())
      animation.addEventListener('cancel', () => resolve())
    })
  }, [systemPrefersReducedMotion])

  /**
   * Gamification-specific animations
   */
  const gamification: GamificationAnimations = useMemo(() => ({
    achievementUnlock: async (element: HTMLElement) => {
      if (systemPrefersReducedMotion) return

      await animateElement(element, [
        { transform: 'scale(1) rotate(0deg)', filter: 'brightness(1)', offset: 0 },
        { transform: 'scale(1.1) rotate(5deg)', filter: 'brightness(1.2)', offset: 0.3 },
        { transform: 'scale(1.05) rotate(-2deg)', filter: 'brightness(1.1)', offset: 0.6 },
        { transform: 'scale(1) rotate(0deg)', filter: 'brightness(1)', offset: 1 }
      ], {
        duration: 800,
        easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
      })

      // Add glow effect
      element.style.boxShadow = '0 0 20px rgba(168, 85, 247, 0.6)'
      setTimeout(() => {
        element.style.boxShadow = ''
      }, 1000)
    },

    pointsGain: async (amount: number, element: HTMLElement) => {
      if (systemPrefersReducedMotion) return

      // Create floating text animation
      const floatingText = document.createElement('div')
      floatingText.textContent = `+${amount}`
      floatingText.style.cssText = `
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        color: #10b981;
        font-weight: bold;
        font-size: 1.2em;
        pointer-events: none;
        z-index: 1000;
      `
      
      element.style.position = 'relative'
      element.appendChild(floatingText)

      // Animate the floating text
      await animateElement(floatingText, [
        { transform: 'translateX(-50%) translateY(0) scale(0.8)', opacity: 0 },
        { transform: 'translateX(-50%) translateY(-10px) scale(1.2)', opacity: 1, offset: 0.3 },
        { transform: 'translateX(-50%) translateY(-30px) scale(1)', opacity: 0.8, offset: 0.7 },
        { transform: 'translateX(-50%) translateY(-50px) scale(0.9)', opacity: 0 }
      ], { duration: 1000 })

      // Animate the main element
      await animateElement(element, [
        { transform: 'scale(1)', color: 'currentColor' },
        { transform: 'scale(1.1)', color: '#10b981', offset: 0.5 },
        { transform: 'scale(1)', color: 'currentColor' }
      ], { duration: 400 })

      floatingText.remove()
    },

    pointsLoss: async (amount: number, element: HTMLElement) => {
      if (systemPrefersReducedMotion) return

      // Shake animation for points loss
      await animateElement(element, [
        { transform: 'translateX(0) scale(1)', color: 'currentColor' },
        { transform: 'translateX(-5px) scale(0.98)', color: '#ef4444', offset: 0.2 },
        { transform: 'translateX(5px) scale(0.98)', color: '#ef4444', offset: 0.4 },
        { transform: 'translateX(-3px) scale(0.99)', color: '#ef4444', offset: 0.6 },
        { transform: 'translateX(3px) scale(0.99)', color: '#ef4444', offset: 0.8 },
        { transform: 'translateX(0) scale(1)', color: 'currentColor' }
      ], { duration: 500 })
    },

    tierPromotion: async (newTier: string, element: HTMLElement) => {
      if (systemPrefersReducedMotion) return

      // Multi-stage tier promotion animation
      const stages = [
        // Stage 1: Scale up with glow
        async () => {
          await animateElement(element, [
            { transform: 'scale(1)', filter: 'brightness(1)' },
            { transform: 'scale(1.2)', filter: 'brightness(1.3)' }
          ], { duration: 300 })
        },
        
        // Stage 2: Rotation with sparkle effect
        async () => {
          element.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.8)'
          await animateElement(element, [
            { transform: 'scale(1.2) rotate(0deg)' },
            { transform: 'scale(1.2) rotate(360deg)' }
          ], { duration: 600 })
        },
        
        // Stage 3: Settle back with new tier styling
        async () => {
          await animateElement(element, [
            { transform: 'scale(1.2)', filter: 'brightness(1.3)' },
            { transform: 'scale(1)', filter: 'brightness(1)' }
          ], { duration: 400 })
          
          setTimeout(() => {
            element.style.boxShadow = ''
          }, 500)
        }
      ]

      for (const stage of stages) {
        await stage()
      }
    },

    challengeComplete: async (element: HTMLElement) => {
      if (systemPrefersReducedMotion) return

      // Celebration bounce animation
      await animateElement(element, [
        { transform: 'scale(1) rotate(0deg)', borderColor: 'currentColor' },
        { transform: 'scale(1.05) rotate(2deg)', borderColor: '#10b981', offset: 0.25 },
        { transform: 'scale(1.1) rotate(-1deg)', borderColor: '#10b981', offset: 0.5 },
        { transform: 'scale(1.05) rotate(1deg)', borderColor: '#10b981', offset: 0.75 },
        { transform: 'scale(1) rotate(0deg)', borderColor: '#10b981' }
      ], {
        duration: 800,
        easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
      })

      // Add completion glow
      element.style.boxShadow = '0 0 15px rgba(16, 185, 129, 0.5)'
      setTimeout(() => {
        element.style.boxShadow = ''
      }, 2000)
    },

    progressUpdate: async (progress: number, element: HTMLElement) => {
      if (systemPrefersReducedMotion) return

      const progressBar = element.querySelector('[data-progress-bar]') as HTMLElement
      if (!progressBar) return

      // Animate progress bar fill
      await animateElement(progressBar, [
        { transform: `scaleX(${progress / 100})` }
      ], {
        duration: 600,
        easing: 'ease-out'
      })

      // Pulse effect for milestones
      if (progress % 25 === 0 && progress > 0) {
        await animateElement(element, [
          { transform: 'scale(1)' },
          { transform: 'scale(1.02)' },
          { transform: 'scale(1)' }
        ], { duration: 300 })
      }
    }
  }), [systemPrefersReducedMotion, animateElement])

  /**
   * Common micro-interactions
   */
  const microInteractions = useMemo(() => ({
    button: {
      hover: createAnimationTrigger(buttonVariants, 'hover'),
      tap: createAnimationTrigger(buttonVariants, 'tap'),
      disabled: createAnimationTrigger(buttonVariants, 'disabled'),
      loading: createAnimationTrigger(buttonVariants, 'loading')
    },
    card: {
      hover: createAnimationTrigger(cardVariants, 'hover'),
      tap: createAnimationTrigger(cardVariants, 'tap'),
      flip: createAnimationTrigger(cardVariants, 'flip')
    },
    input: {
      focus: createAnimationTrigger(inputVariants, 'focus'),
      error: createAnimationTrigger(inputVariants, 'error'),
      success: createAnimationTrigger(inputVariants, 'success')
    }
  }), [createAnimationTrigger])

  /**
   * Pre-configured animation variants
   */
  const variants = useMemo(() => ({
    fade: getVariants(fadeVariants),
    slide: getVariants(slideVariants),
    scale: getVariants(scaleVariants),
    rotate: getVariants(rotateVariants),
    achievement: getVariants(achievementVariants),
    points: getVariants(pointsVariants),
    tier: getVariants(tierVariants),
    challenge: getVariants(challengeVariants),
    progressBar: getVariants(progressBarVariants),
    notification: getVariants(notificationVariants),
    toast: getVariants(toastVariants),
    list: getVariants(listVariants),
    listItem: getVariants(listItemVariants),
    modal: getVariants(modalVariants),
    backdrop: getVariants(backdropVariants),
    spinner: getVariants(spinnerVariants),
    pulse: getVariants(pulseVariants)
  }), [getVariants])

  /**
   * Animation utilities
   */
  const utils = useMemo(() => ({
    createStagger: (delay = 0.1) => ({
      staggerChildren: delay,
      delayChildren: 0.1
    }),
    
    createSequence: (animations: (() => Promise<void>)[]) => {
      return async () => {
        for (const animation of animations) {
          await animation()
        }
      }
    },
    
    createParallel: (animations: (() => Promise<void>)[]) => {
      return async () => {
        await Promise.all(animations.map(animation => animation()))
      }
    },
    
    delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
  }), [])

  return {
    // Core animation controls
    controls,
    variants,
    
    // Micro-interactions
    microInteractions,
    
    // Gamification animations
    gamification,
    
    // Animation utilities
    createAnimationTrigger,
    queueAnimation,
    animateElement,
    utils,
    
    // Configuration
    durations,
    easings,
    getAnimationConfig,
    
    // Accessibility
    systemPrefersReducedMotion,
    respectsReducedMotion: systemPrefersReducedMotion,
    isReducedMotion: systemPrefersReducedMotion
  }
}

/**
 * Hook for simple element animations
 */
export function useElementAnimation(options: AnimationOptions = {}) {
  const { animateElement, systemPrefersReducedMotion } = useAnimations()
  const elementRef = useRef<HTMLElement>(null)

  const animate = useCallback(async (
    keyframes: Keyframe[] | PropertyIndexedKeyframes,
    animationOptions: KeyframeAnimationOptions = {}
  ) => {
    if (!elementRef.current) return
    
    await animateElement(elementRef.current, keyframes, {
      duration: options.duration ? durations[options.duration] : 300,
      easing: options.easing ? (typeof easings[options.easing] === 'string' ? easings[options.easing] as string : 'ease') : 'ease',
      ...animationOptions
    })
    
    options.onComplete?.()
  }, [animateElement, options])

  return {
    ref: elementRef,
    animate,
    isReducedMotion: systemPrefersReducedMotion
  }
}

/**
 * Hook for gamification animations
 */
export function useGamificationAnimations() {
  const { gamification } = useAnimations()
  
  return gamification
}

export default useAnimations