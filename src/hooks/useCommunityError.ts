/**
 * Community Error Handling Hook
 * 
 * Provides consistent error handling patterns for community features
 * including retry logic, error categorization, and fallback data.
 * 
 * <AUTHOR> Team
 */

import React, { useState, useCallback, useRef } from 'react'

export interface CommunityError {
  message: string
  type: 'network' | 'firebase' | 'auth' | 'permission' | 'validation' | 'unknown'
  code?: string
  retryable: boolean
  timestamp: Date
}

export interface UseCommunityErrorReturn {
  error: CommunityError | null
  isRetrying: boolean
  retryCount: number
  maxRetries: number
  canRetry: boolean
  setError: (error: Error | string | null) => void
  clearError: () => void
  retry: () => Promise<void>
  handleAsyncOperation: <T>(
    operation: () => Promise<T>,
    options?: {
      retryCount?: number
      fallback?: T
      onSuccess?: (result: T) => void
      onError?: (error: CommunityError) => void
    }
  ) => Promise<T | undefined>
}

const MAX_RETRIES = 3
const RETRY_DELAYS = [1000, 2000, 4000] // Progressive delays in ms

export function useCommunityError(
  onRetry?: () => Promise<void>
): UseCommunityErrorReturn {
  const [error, setErrorState] = useState<CommunityError | null>(null)
  const [isRetrying, setIsRetrying] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const retryTimeoutRef = useRef<NodeJS.Timeout>()

  const categorizeError = (error: Error | string): CommunityError => {
    const message = typeof error === 'string' ? error : error.message
    const lowerMessage = message.toLowerCase()

    let type: CommunityError['type'] = 'unknown'
    let retryable = true
    let code: string | undefined

    // Firebase/Firestore errors
    if (lowerMessage.includes('firebase') || lowerMessage.includes('firestore')) {
      type = 'firebase'
      if (lowerMessage.includes('permission-denied') || lowerMessage.includes('unauthorized')) {
        type = 'permission'
        retryable = false
      }
      if (lowerMessage.includes('unavailable') || lowerMessage.includes('deadline-exceeded')) {
        type = 'network'
        retryable = true
      }
    }
    // Network errors
    else if (
      lowerMessage.includes('network') || 
      lowerMessage.includes('fetch') || 
      lowerMessage.includes('connection') ||
      lowerMessage.includes('timeout')
    ) {
      type = 'network'
      retryable = true
    }
    // Authentication errors
    else if (
      lowerMessage.includes('auth') || 
      lowerMessage.includes('login') || 
      lowerMessage.includes('token')
    ) {
      type = 'auth'
      retryable = false
    }
    // Permission errors
    else if (
      lowerMessage.includes('permission') || 
      lowerMessage.includes('forbidden') || 
      lowerMessage.includes('access denied')
    ) {
      type = 'permission'
      retryable = false
    }
    // Validation errors
    else if (
      lowerMessage.includes('validation') || 
      lowerMessage.includes('invalid') || 
      lowerMessage.includes('required')
    ) {
      type = 'validation'
      retryable = false
    }

    return {
      message,
      type,
      code,
      retryable,
      timestamp: new Date()
    }
  }

  const setError = useCallback((error: Error | string | null) => {
    if (error === null) {
      setErrorState(null)
      return
    }

    const communityError = categorizeError(error)
    setErrorState(communityError)
    
    // Log error for debugging
    console.error('Community Error:', {
      ...communityError,
      originalError: error
    })
  }, [])

  const clearError = useCallback(() => {
    setErrorState(null)
    setRetryCount(0)
    setIsRetrying(false)
    
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
    }
  }, [])

  const retry = useCallback(async () => {
    if (!error?.retryable || retryCount >= MAX_RETRIES || !onRetry) {
      return
    }

    setIsRetrying(true)
    
    try {
      // Add progressive delay
      const delay = RETRY_DELAYS[retryCount] || RETRY_DELAYS[RETRY_DELAYS.length - 1]
      
      await new Promise(resolve => {
        retryTimeoutRef.current = setTimeout(resolve, delay)
      })

      await onRetry()
      
      // Success - clear error state
      clearError()
    } catch (retryError) {
      setRetryCount(prev => prev + 1)
      setError(retryError as Error)
    } finally {
      setIsRetrying(false)
    }
  }, [error, retryCount, onRetry, clearError, setError])

  const handleAsyncOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    options: {
      retryCount?: number
      fallback?: T
      onSuccess?: (result: T) => void
      onError?: (error: CommunityError) => void
    } = {}
  ): Promise<T | undefined> => {
    const { 
      retryCount: maxRetryCount = MAX_RETRIES, 
      fallback, 
      onSuccess, 
      onError 
    } = options

    let attempts = 0
    
    while (attempts <= maxRetryCount) {
      try {
        const result = await operation()
        
        // Success - clear any previous errors
        clearError()
        
        if (onSuccess) {
          onSuccess(result)
        }
        
        return result
      } catch (error) {
        attempts++
        const communityError = categorizeError(error as Error)
        
        // If not retryable or max attempts reached, set error and return fallback
        if (!communityError.retryable || attempts > maxRetryCount) {
          setError(error as Error)
          
          if (onError) {
            onError(communityError)
          }
          
          return fallback
        }
        
        // Wait before retry (progressive delay)
        if (attempts <= maxRetryCount) {
          const delay = RETRY_DELAYS[attempts - 1] || RETRY_DELAYS[RETRY_DELAYS.length - 1]
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    return fallback
  }, [clearError, setError])

  const canRetry = Boolean(
    error?.retryable && 
    retryCount < MAX_RETRIES && 
    !isRetrying && 
    onRetry
  )

  return {
    error,
    isRetrying,
    retryCount,
    maxRetries: MAX_RETRIES,
    canRetry,
    setError,
    clearError,
    retry,
    handleAsyncOperation
  }
}

/**
 * Specialized hook for community data fetching with automatic error handling
 */
export function useCommunityDataFetch<T>(
  fetchFunction: () => Promise<T>,
  options: {
    fallbackData?: T
    autoRetry?: boolean
    retryCount?: number
    dependencies?: any[]
  } = {}
) {
  const {
    fallbackData,
    autoRetry = true,
    retryCount = MAX_RETRIES,
    dependencies = []
  } = options

  const [data, setData] = useState<T | undefined>(fallbackData)
  const [loading, setLoading] = useState(true)
  
  const communityError = useCommunityError(
    autoRetry ? fetchFunction : undefined
  )

  const fetchData = useCallback(async () => {
    setLoading(true)
    
    try {
      const result = await communityError.handleAsyncOperation(
        fetchFunction,
        {
          retryCount,
          fallback: fallbackData,
          onSuccess: (result) => setData(result),
          onError: () => setData(fallbackData)
        }
      )
      
      setData(result)
    } finally {
      setLoading(false)
    }
  }, [fetchFunction, retryCount, fallbackData, communityError])

  // Initial fetch and refetch on dependencies change
  React.useEffect(() => {
    fetchData()
  }, dependencies)

  return {
    data,
    loading,
    ...communityError,
    refetch: fetchData
  }
}

export default useCommunityError