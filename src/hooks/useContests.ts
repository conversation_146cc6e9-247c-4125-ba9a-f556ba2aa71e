/**
 * Contest System Hooks
 * 
 * React hooks for contest management, submission handling, voting,
 * and judge management with real-time updates and caching.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useUser } from '../lib/useUser'
import {
  getContests,
  getContest,
  createContest,
  updateContest,
  deleteContest,
  submitContestEntry,
  updateContestSubmission,
  deleteContestSubmission,
  getContestSubmission,
  submitContestVote,
  updateContestVote,
  getUserVote,
  getSubmissionVotes,
  assignContestJudge,
  updateJudgeStatus,
  getContestJudges,
  getContestLeaderboard,
  getContestVotingProgress
} from '../lib/api/contests'
import type {
  Contest,
  ContestSubmission,
  ContestVote,
  ContestJudge,
  ContestFilters,
  ContestSubmissionFilters,
  ContestLeaderboard,
  ContestVotingProgress,
  ContestCreateInput,
  ContestSubmissionCreateInput,
  ContestVoteCreateInput
} from '../types/contests'

// ===== CONTEST MANAGEMENT HOOKS =====

// Stable default filters to prevent infinite re-renders
const DEFAULT_FILTERS: ContestFilters = {}

/**
 * Hook for fetching and managing contests
 */
export function useContests(filters: ContestFilters = DEFAULT_FILTERS) {
  const [contests, setContests] = useState<Contest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Memoize filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [JSON.stringify(filters)])

  const fetchContests = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await getContests(memoizedFilters)
      setContests(response.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch contests'
      setError(errorMessage)
      console.error('Error fetching contests:', err)
    } finally {
      setLoading(false)
    }
  }, [memoizedFilters])

  useEffect(() => {
    fetchContests()
  }, [fetchContests])

  // Filter contests by status for easy access
  const activeContests = useMemo(() => 
    contests.filter(contest => contest.status === 'active'), [contests]
  )
  
  const upcomingContests = useMemo(() => 
    contests.filter(contest => contest.status === 'upcoming'), [contests]
  )
  
  const votingContests = useMemo(() => 
    contests.filter(contest => contest.status === 'voting'), [contests]
  )
  
  const completedContests = useMemo(() => 
    contests.filter(contest => contest.status === 'completed'), [contests]
  )

  return {
    contests,
    activeContests,
    upcomingContests,
    votingContests,
    completedContests,
    loading,
    error,
    refetch: fetchContests
  }
}

/**
 * Hook for managing a single contest
 */
export function useContest(contestId: string | null) {
  const [contest, setContest] = useState<Contest | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchContest = useCallback(async () => {
    if (!contestId) return

    try {
      setLoading(true)
      setError(null)
      
      const contestData = await getContest(contestId)
      setContest(contestData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch contest'
      setError(errorMessage)
      console.error('Error fetching contest:', err)
    } finally {
      setLoading(false)
    }
  }, [contestId])

  useEffect(() => {
    if (contestId) {
      fetchContest()
    } else {
      setContest(null)
    }
  }, [fetchContest, contestId])

  return {
    contest,
    loading,
    error,
    refetch: fetchContest
  }
}

/**
 * Hook for contest creation and management (admin only)
 */
export function useContestManagement() {
  const [creating, setCreating] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const create = useCallback(async (contestData: ContestCreateInput) => {
    try {
      setCreating(true)
      setError(null)
      
      const contestId = await createContest(contestData)
      return contestId
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create contest'
      setError(errorMessage)
      console.error('Error creating contest:', err)
      return null
    } finally {
      setCreating(false)
    }
  }, [])

  const update = useCallback(async (contestId: string, updates: Partial<Contest>) => {
    try {
      setUpdating(true)
      setError(null)
      
      await updateContest(contestId, updates)
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update contest'
      setError(errorMessage)
      console.error('Error updating contest:', err)
      return false
    } finally {
      setUpdating(false)
    }
  }, [])

  const remove = useCallback(async (contestId: string) => {
    try {
      setDeleting(true)
      setError(null)
      
      await deleteContest(contestId)
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete contest'
      setError(errorMessage)
      console.error('Error deleting contest:', err)
      return false
    } finally {
      setDeleting(false)
    }
  }, [])

  return {
    create,
    update,
    remove,
    creating,
    updating,
    deleting,
    error
  }
}

// ===== SUBMISSION HOOKS =====

/**
 * Hook for contest submissions
 */
export function useContestSubmissions(contestId: string | null, filters: ContestSubmissionFilters = {}) {
  const [submissions, setSubmissions] = useState<ContestSubmission[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchSubmissions = useCallback(async () => {
    if (!contestId) return

    try {
      setLoading(true)
      setError(null)
      
      // Note: This would need to be implemented in the API
      // For now, we'll use a placeholder
      console.log('Fetching submissions for contest:', contestId)
      setSubmissions([])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch submissions'
      setError(errorMessage)
      console.error('Error fetching submissions:', err)
    } finally {
      setLoading(false)
    }
  }, [contestId, filters])

  useEffect(() => {
    if (contestId) {
      fetchSubmissions()
    }
  }, [fetchSubmissions])

  return {
    submissions,
    loading,
    error,
    refetch: fetchSubmissions
  }
}

/**
 * Hook for managing contest submissions
 */
export function useContestSubmission(submissionId: string | null) {
  const { user } = useUser()
  const [submission, setSubmission] = useState<ContestSubmission | null>(null)
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchSubmission = useCallback(async () => {
    if (!submissionId) return

    try {
      setLoading(true)
      setError(null)
      
      const submissionData = await getContestSubmission(submissionId)
      setSubmission(submissionData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch submission'
      setError(errorMessage)
      console.error('Error fetching submission:', err)
    } finally {
      setLoading(false)
    }
  }, [submissionId])

  useEffect(() => {
    if (submissionId) {
      fetchSubmission()
    } else {
      setSubmission(null)
    }
  }, [fetchSubmission, submissionId])

  const submit = useCallback(async (submissionData: ContestSubmissionCreateInput) => {
    if (!user?.uid) {
      setError('User must be logged in to submit')
      return null
    }

    try {
      setSubmitting(true)
      setError(null)
      
      const submissionId = await submitContestEntry({
        ...submissionData,
        userId: user.uid
      })
      
      return submissionId
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit entry'
      setError(errorMessage)
      console.error('Error submitting entry:', err)
      return null
    } finally {
      setSubmitting(false)
    }
  }, [user?.uid])

  const update = useCallback(async (updates: Partial<ContestSubmission>) => {
    if (!submissionId) return false

    try {
      setUpdating(true)
      setError(null)
      
      await updateContestSubmission(submissionId, updates)
      
      // Update local state
      if (submission) {
        setSubmission({ ...submission, ...updates })
      }
      
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update submission'
      setError(errorMessage)
      console.error('Error updating submission:', err)
      return false
    } finally {
      setUpdating(false)
    }
  }, [submissionId, submission])

  const remove = useCallback(async () => {
    if (!submissionId) return false

    try {
      setDeleting(true)
      setError(null)
      
      await deleteContestSubmission(submissionId)
      setSubmission(null)
      
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete submission'
      setError(errorMessage)
      console.error('Error deleting submission:', err)
      return false
    } finally {
      setDeleting(false)
    }
  }, [submissionId])

  const canEdit = useMemo(() => {
    return submission && 
           user?.uid === submission.userId && 
           ['draft', 'submitted'].includes(submission.status)
  }, [submission, user?.uid])

  const canDelete = useMemo(() => {
    return submission && 
           user?.uid === submission.userId && 
           submission.status === 'draft'
  }, [submission, user?.uid])

  return {
    submission,
    loading,
    submitting,
    updating,
    deleting,
    error,
    submit,
    update,
    remove,
    refetch: fetchSubmission,
    canEdit,
    canDelete
  }
}

// ===== VOTING HOOKS =====

/**
 * Hook for contest voting
 */
export function useContestVoting(contestId: string | null, submissionId: string | null) {
  const { user } = useUser()
  const [userVote, setUserVote] = useState<ContestVote | null>(null)
  const [allVotes, setAllVotes] = useState<ContestVote[]>([])
  const [loading, setLoading] = useState(false)
  const [voting, setVoting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchUserVote = useCallback(async () => {
    if (!contestId || !submissionId || !user?.uid) return

    try {
      setLoading(true)
      setError(null)

      const vote = await getUserVote(contestId, submissionId, user.uid)
      setUserVote(vote)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user vote'
      setError(errorMessage)
      console.error('Error fetching user vote:', err)
    } finally {
      setLoading(false)
    }
  }, [contestId, submissionId, user?.uid])

  const fetchAllVotes = useCallback(async () => {
    if (!submissionId) return

    try {
      const votes = await getSubmissionVotes(submissionId)
      setAllVotes(votes)
    } catch (err) {
      console.error('Error fetching submission votes:', err)
    }
  }, [submissionId])

  useEffect(() => {
    if (contestId && submissionId) {
      fetchUserVote()
      fetchAllVotes()
    }
  }, [fetchUserVote, fetchAllVotes])

  const vote = useCallback(async (voteData: Omit<ContestVoteCreateInput, 'contestId' | 'submissionId' | 'voterId'>) => {
    if (!contestId || !submissionId || !user?.uid) {
      setError('Missing required data for voting')
      return false
    }

    try {
      setVoting(true)
      setError(null)

      await submitContestVote({
        ...voteData,
        contestId,
        submissionId,
        voterId: user.uid
      })

      // Refresh user vote and all votes
      await fetchUserVote()
      await fetchAllVotes()

      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit vote'
      setError(errorMessage)
      console.error('Error submitting vote:', err)
      return false
    } finally {
      setVoting(false)
    }
  }, [contestId, submissionId, user?.uid, fetchUserVote, fetchAllVotes])

  const hasVoted = useMemo(() => userVote !== null, [userVote])

  const canVote = useMemo(() => {
    return user && !hasVoted && contestId && submissionId
  }, [user, hasVoted, contestId, submissionId])

  const voteStats = useMemo(() => {
    const communityVotes = allVotes.filter(v => v.type === 'community')
    const expertVotes = allVotes.filter(v => v.type === 'expert')

    return {
      total: allVotes.length,
      community: communityVotes.length,
      expert: expertVotes.length,
      averageRating: allVotes.length > 0
        ? allVotes.reduce((sum, v) => sum + v.rating, 0) / allVotes.length
        : 0
    }
  }, [allVotes])

  return {
    userVote,
    allVotes,
    voteStats,
    loading,
    voting,
    error,
    vote,
    hasVoted,
    canVote,
    refetch: fetchUserVote
  }
}

// ===== JUDGE MANAGEMENT HOOKS =====

/**
 * Hook for contest judges
 */
export function useContestJudges(contestId: string | null) {
  const [judges, setJudges] = useState<ContestJudge[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchJudges = useCallback(async () => {
    if (!contestId) return

    try {
      setLoading(true)
      setError(null)

      const judgeData = await getContestJudges(contestId)
      setJudges(judgeData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch judges'
      setError(errorMessage)
      console.error('Error fetching judges:', err)
    } finally {
      setLoading(false)
    }
  }, [contestId])

  useEffect(() => {
    if (contestId) {
      fetchJudges()
    }
  }, [fetchJudges])

  const updateStatus = useCallback(async (judgeId: string, status: ContestJudge['status']) => {
    try {
      await updateJudgeStatus(judgeId, status)

      // Update local state
      setJudges(prev => prev.map(judge =>
        judge.id === judgeId ? { ...judge, status } : judge
      ))

      return true
    } catch (err) {
      console.error('Error updating judge status:', err)
      return false
    }
  }, [])

  const judgeStats = useMemo(() => {
    const total = judges.length
    const active = judges.filter(j => j.status === 'active').length
    const completed = judges.filter(j => j.status === 'completed').length

    return { total, active, completed }
  }, [judges])

  return {
    judges,
    judgeStats,
    loading,
    error,
    updateStatus,
    refetch: fetchJudges
  }
}

// ===== ANALYTICS HOOKS =====

/**
 * Hook for contest leaderboard
 */
export function useContestLeaderboard(contestId: string | null) {
  const [leaderboard, setLeaderboard] = useState<ContestLeaderboard | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchLeaderboard = useCallback(async () => {
    if (!contestId) return

    try {
      setLoading(true)
      setError(null)

      const leaderboardData = await getContestLeaderboard(contestId)
      setLeaderboard(leaderboardData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch leaderboard'
      setError(errorMessage)
      console.error('Error fetching leaderboard:', err)
    } finally {
      setLoading(false)
    }
  }, [contestId])

  useEffect(() => {
    if (contestId) {
      fetchLeaderboard()
    }
  }, [fetchLeaderboard])

  const topEntries = useMemo(() => {
    return leaderboard?.entries.slice(0, 10) || []
  }, [leaderboard])

  return {
    leaderboard,
    topEntries,
    loading,
    error,
    refetch: fetchLeaderboard
  }
}

/**
 * Hook for contest voting progress
 */
export function useContestVotingProgress(contestId: string | null) {
  const [progress, setProgress] = useState<ContestVotingProgress | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchProgress = useCallback(async () => {
    if (!contestId) return

    try {
      setLoading(true)
      setError(null)

      const progressData = await getContestVotingProgress(contestId)
      setProgress(progressData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch voting progress'
      setError(errorMessage)
      console.error('Error fetching voting progress:', err)
    } finally {
      setLoading(false)
    }
  }, [contestId])

  useEffect(() => {
    if (contestId) {
      fetchProgress()
    }
  }, [fetchProgress])

  return {
    progress,
    loading,
    error,
    refetch: fetchProgress
  }
}

// ===== COMBINED HOOKS =====

/**
 * Main contest hook that combines all contest-related functionality
 */
export function useContestSystem(contestId: string | null) {
  const contest = useContest(contestId)
  const submissions = useContestSubmissions(contestId)
  const judges = useContestJudges(contestId)
  const leaderboard = useContestLeaderboard(contestId)
  const votingProgress = useContestVotingProgress(contestId)

  const loading = contest.loading || submissions.loading || judges.loading ||
                  leaderboard.loading || votingProgress.loading

  const error = contest.error || submissions.error || judges.error ||
                leaderboard.error || votingProgress.error

  const refetchAll = useCallback(() => {
    contest.refetch()
    submissions.refetch()
    judges.refetch()
    leaderboard.refetch()
    votingProgress.refetch()
  }, [contest, submissions, judges, leaderboard, votingProgress])

  return {
    contest: contest.contest,
    submissions: submissions.submissions,
    judges: judges.judges,
    leaderboard: leaderboard.leaderboard,
    votingProgress: votingProgress.progress,
    loading,
    error,
    refetchAll
  }
}
