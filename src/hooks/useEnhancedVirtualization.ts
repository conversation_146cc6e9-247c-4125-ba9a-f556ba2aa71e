/**
 * Enhanced Virtualization Hook
 * 
 * Advanced virtualization with performance monitoring, adaptive sizing,
 * and intelligent preloading for optimal rendering performance.
 * 
 * Phase 2 Performance Optimization - Community System Audit
 * 
 * <AUTHOR> Team
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react'

interface EnhancedVirtualizationOptions<T> {
  items: T[]
  itemHeight: number | ((index: number, item: T) => number)
  containerHeight: number
  overscan?: number
  containerRef?: React.RefObject<HTMLElement>
  enablePerformanceMonitoring?: boolean
  adaptiveHeight?: boolean
  preloadThreshold?: number
  onItemsRendered?: (startIndex: number, endIndex: number) => void
  onScroll?: (scrollTop: number, scrollDirection: 'up' | 'down') => void
}

interface VirtualItem<T> {
  index: number
  item: T
  start: number
  size: number
  end: number
}

interface PerformanceMetrics {
  renderTime: number
  scrollFps: number
  memoryUsage: number
  visibleItemCount: number
  totalItemCount: number
}

export function useEnhancedVirtualization<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
  containerRef,
  enablePerformanceMonitoring = false,
  adaptiveHeight = false,
  preloadThreshold = 10,
  onItemsRendered,
  onScroll
}: EnhancedVirtualizationOptions<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down'>('down')
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    scrollFps: 0,
    memoryUsage: 0,
    visibleItemCount: 0,
    totalItemCount: items.length
  })

  const lastScrollTop = useRef(0)
  const scrollTimeout = useRef<NodeJS.Timeout>()
  const frameCount = useRef(0)
  const lastFrameTime = useRef(Date.now())
  const itemSizeCache = useRef<Map<number, number>>(new Map())

  // Calculate item height with caching
  const getItemHeight = useCallback((index: number, item: T): number => {
    if (typeof itemHeight === 'function') {
      if (!itemSizeCache.current.has(index)) {
        const height = itemHeight(index, item)
        itemSizeCache.current.set(index, height)
        return height
      }
      return itemSizeCache.current.get(index)!
    }
    return itemHeight
  }, [itemHeight])

  // Calculate item positions with memoization
  const itemPositions = useMemo(() => {
    const positions: Array<{ start: number; size: number; end: number }> = []
    let totalSize = 0

    for (let i = 0; i < items.length; i++) {
      const size = getItemHeight(i, items[i])
      positions.push({
        start: totalSize,
        size,
        end: totalSize + size
      })
      totalSize += size
    }

    return { positions, totalSize }
  }, [items, getItemHeight])

  // Find visible range with binary search for better performance
  const visibleRange = useMemo(() => {
    const { positions } = itemPositions
    
    if (positions.length === 0) {
      return { startIndex: 0, endIndex: 0 }
    }

    // Binary search for start index
    let startIndex = 0
    let left = 0
    let right = positions.length - 1

    while (left <= right) {
      const mid = Math.floor((left + right) / 2)
      if (positions[mid].end <= scrollTop) {
        left = mid + 1
      } else {
        startIndex = mid
        right = mid - 1
      }
    }

    // Binary search for end index
    let endIndex = positions.length - 1
    left = startIndex
    right = positions.length - 1
    const viewportEnd = scrollTop + containerHeight

    while (left <= right) {
      const mid = Math.floor((left + right) / 2)
      if (positions[mid].start < viewportEnd) {
        endIndex = mid
        left = mid + 1
      } else {
        right = mid - 1
      }
    }

    // Apply overscan
    const finalStartIndex = Math.max(0, startIndex - overscan)
    const finalEndIndex = Math.min(positions.length - 1, endIndex + overscan)

    return { startIndex: finalStartIndex, endIndex: finalEndIndex }
  }, [scrollTop, containerHeight, itemPositions, overscan])

  // Create virtual items
  const virtualItems = useMemo((): VirtualItem<T>[] => {
    const { startIndex, endIndex } = visibleRange
    const { positions } = itemPositions
    const result: VirtualItem<T>[] = []

    for (let i = startIndex; i <= endIndex && i < items.length; i++) {
      if (positions[i] && items[i]) {
        result.push({
          index: i,
          item: items[i],
          start: positions[i].start,
          size: positions[i].size,
          end: positions[i].end
        })
      }
    }

    return result
  }, [visibleRange, itemPositions, items])

  // Performance monitoring
  useEffect(() => {
    if (!enablePerformanceMonitoring) return

    const startTime = performance.now()
    
    // Measure render time
    const measureRenderTime = () => {
      const renderTime = performance.now() - startTime
      
      setPerformanceMetrics(prev => ({
        ...prev,
        renderTime,
        visibleItemCount: virtualItems.length,
        totalItemCount: items.length
      }))
    }

    // Use requestAnimationFrame to measure after render
    requestAnimationFrame(measureRenderTime)
  }, [virtualItems, items.length, enablePerformanceMonitoring])

  // FPS monitoring
  useEffect(() => {
    if (!enablePerformanceMonitoring || !isScrolling) return

    const measureFPS = () => {
      frameCount.current++
      const now = Date.now()
      const elapsed = now - lastFrameTime.current

      if (elapsed >= 1000) {
        const fps = (frameCount.current * 1000) / elapsed
        setPerformanceMetrics(prev => ({ ...prev, scrollFps: fps }))
        frameCount.current = 0
        lastFrameTime.current = now
      }

      if (isScrolling) {
        requestAnimationFrame(measureFPS)
      }
    }

    requestAnimationFrame(measureFPS)
  }, [isScrolling, enablePerformanceMonitoring])

  // Handle scroll events
  const handleScroll = useCallback((event: React.UIEvent<HTMLElement>) => {
    const newScrollTop = event.currentTarget.scrollTop
    const direction = newScrollTop > lastScrollTop.current ? 'down' : 'up'
    
    setScrollTop(newScrollTop)
    setScrollDirection(direction)
    setIsScrolling(true)
    
    lastScrollTop.current = newScrollTop

    // Call external scroll handler
    onScroll?.(newScrollTop, direction)

    // Clear scrolling state after delay
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current)
    }
    
    scrollTimeout.current = setTimeout(() => {
      setIsScrolling(false)
    }, 150)
  }, [onScroll])

  // Notify about rendered items
  useEffect(() => {
    const { startIndex, endIndex } = visibleRange
    onItemsRendered?.(startIndex, endIndex)
  }, [visibleRange, onItemsRendered])

  // Preload items near viewport
  const shouldPreloadItems = useMemo(() => {
    const { startIndex, endIndex } = visibleRange
    const preloadStart = Math.max(0, startIndex - preloadThreshold)
    const preloadEnd = Math.min(items.length - 1, endIndex + preloadThreshold)
    
    return { preloadStart, preloadEnd }
  }, [visibleRange, preloadThreshold, items.length])

  // Scroll to item
  const scrollToItem = useCallback((index: number, align: 'start' | 'center' | 'end' = 'start') => {
    if (!containerRef?.current || index < 0 || index >= items.length) return

    const { positions } = itemPositions
    const itemPosition = positions[index]
    
    if (!itemPosition) return

    let scrollTo = itemPosition.start

    if (align === 'center') {
      scrollTo = itemPosition.start - (containerHeight - itemPosition.size) / 2
    } else if (align === 'end') {
      scrollTo = itemPosition.end - containerHeight
    }

    containerRef.current.scrollTo({
      top: Math.max(0, scrollTo),
      behavior: 'smooth'
    })
  }, [containerRef, items.length, itemPositions, containerHeight])

  // Get estimated total size
  const estimatedTotalSize = itemPositions.totalSize

  return {
    virtualItems,
    totalSize: estimatedTotalSize,
    visibleRange,
    isScrolling,
    scrollDirection,
    performanceMetrics,
    shouldPreloadItems,
    
    // Scroll element props
    scrollElementProps: {
      onScroll: handleScroll,
      style: {
        height: containerHeight,
        overflow: 'auto'
      }
    },
    
    // Inner element props
    innerElementProps: {
      style: {
        height: estimatedTotalSize,
        position: 'relative' as const
      }
    },
    
    // Utility functions
    scrollToItem,
    
    // Cache management
    clearSizeCache: () => itemSizeCache.current.clear(),
    
    // Performance utilities
    getPerformanceReport: () => ({
      ...performanceMetrics,
      cacheSize: itemSizeCache.current.size,
      efficiency: virtualItems.length / items.length
    })
  }
}

export default useEnhancedVirtualization
