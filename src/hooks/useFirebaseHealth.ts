/**
 * Firebase Health Hook
 * 
 * React hook for monitoring Firebase connection health and providing
 * real-time status updates to components.
 * 
 * <AUTHOR> Team
 */

import { useState, useEffect, useCallback } from 'react'
import { firebaseHealthMonitor, FirebaseHealthStatus } from '@/lib/firebase-health-check'

export interface UseFirebaseHealthReturn {
  healthStatus: FirebaseHealthStatus
  isHealthy: boolean
  isDegraded: boolean
  isUnhealthy: boolean
  forceReconnect: () => Promise<boolean>
  lastUpdated: Date
}

/**
 * Hook for monitoring Firebase health status
 */
export function useFirebaseHealth(): UseFirebaseHealthReturn {
  const [healthStatus, setHealthStatus] = useState<FirebaseHealthStatus>(
    firebaseHealthMonitor.getHealthStatus()
  )
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // Handle health status changes
  const handleHealthChange = useCallback((status: FirebaseHealthStatus) => {
    setHealthStatus(status)
    setLastUpdated(new Date())
  }, [])

  // Force reconnection
  const forceReconnect = useCallback(async (): Promise<boolean> => {
    return await firebaseHealthMonitor.forceReconnect()
  }, [])

  useEffect(() => {
    // Subscribe to health changes
    const unsubscribe = firebaseHealthMonitor.onHealthChange(handleHealthChange)

    // Cleanup subscription on unmount
    return unsubscribe
  }, [handleHealthChange])

  return {
    healthStatus,
    isHealthy: healthStatus.overall === 'healthy',
    isDegraded: healthStatus.overall === 'degraded',
    isUnhealthy: healthStatus.overall === 'unhealthy',
    forceReconnect,
    lastUpdated
  }
}

/**
 * Hook for Firebase connection status (simplified)
 */
export function useFirebaseConnection() {
  const { healthStatus, isHealthy, forceReconnect } = useFirebaseHealth()

  return {
    isConnected: isHealthy,
    isFirestoreConnected: healthStatus.firestore.connected,
    isAuthConnected: healthStatus.auth.connected,
    isOnline: healthStatus.network.online,
    reconnect: forceReconnect
  }
}
