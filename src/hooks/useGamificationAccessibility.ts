/**
 * Gamification Accessibility Hook
 * 
 * Specialized React hook for accessibility features in gamification components.
 * Provides ARIA attributes, screen reader announcements, and keyboard navigation
 * specifically tailored for achievements, challenges, rewards, and leaderboards.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { useCallback, useEffect, useRef } from 'react'
import { AccessibilityUtils, AriaAttributes, AnnouncementOptions } from '../lib/accessibility/accessibilityUtils'
import { useAccessibility } from './useAccessibility'

// ===== TYPES =====

export interface GamificationElement {
  id: string
  title: string
  description: string
  type: 'achievement' | 'challenge' | 'reward' | 'leaderboard' | 'progress' | 'points'
  data: any
  state?: any
}

export interface ProgressUpdate {
  elementId: string
  progress: number
  isComplete?: boolean
  milestone?: string
}

export interface NavigationContext {
  page: string
  section?: string
  totalItems?: number
  currentItem?: number
  keyboardInstructions?: string
}

// ===== MAIN HOOK =====

export function useGamificationAccessibility() {
  const { 
    announce: baseAnnounce, 
    generateId, 
    preferences,
    handleKeyboardNavigation,
    focusManager 
  } = useAccessibility()

  const announcementQueueRef = useRef<Array<{ message: string; options: AnnouncementOptions; timestamp: number }>>([])
  const processingRef = useRef(false)

  /**
   * Enhanced announcement system with priority queuing for gamification events
   */
  const announce = useCallback((message: string, options: AnnouncementOptions = { priority: 'medium' }) => {
    const announcement = {
      message,
      options,
      timestamp: Date.now()
    }

    announcementQueueRef.current.push(announcement)
    
    if (!processingRef.current) {
      processAnnouncementQueue()
    }
  }, [])

  /**
   * Process queued announcements with proper timing
   */
  const processAnnouncementQueue = useCallback(() => {
    if (processingRef.current || announcementQueueRef.current.length === 0) return

    processingRef.current = true

    // Sort by priority and timestamp
    announcementQueueRef.current.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 }
      return priorityWeight[b.options.priority] - priorityWeight[a.options.priority] ||
             a.timestamp - b.timestamp
    })

    const processNext = () => {
      const announcement = announcementQueueRef.current.shift()
      if (!announcement) {
        processingRef.current = false
        return
      }

      baseAnnounce(announcement.message, 'polite')

      // Schedule next announcement
      if (announcementQueueRef.current.length > 0) {
        setTimeout(processNext, announcement.options.delay || 1500)
      } else {
        processingRef.current = false
      }
    }

    processNext()
  }, [baseAnnounce])

  /**
   * Create comprehensive ARIA attributes for gamification elements
   */
  const createAccessibleAttributes = useCallback((element: GamificationElement): AriaAttributes => {
    const baseAttributes = AccessibilityUtils.createGameElementAttributes(
      element.type,
      element.data,
      element.state
    )

    // Add additional context-specific attributes
    const enhancedAttributes: AriaAttributes & { [key: string]: any } = {
      ...baseAttributes,
      id: element.id,
      'data-accessibility-type': element.type,
      'data-accessibility-enhanced': 'true'
    }

    // Add keyboard instructions as data attribute
    const instructions = AccessibilityUtils.createKeyboardInstructions(`${element.type}-grid`)
    if (instructions) {
      enhancedAttributes['data-keyboard-instructions'] = instructions
    }

    return enhancedAttributes
  }, [])

  /**
   * Announce achievement events with proper context
   */
  const achievementAnnouncements = {
    unlock: useCallback((achievement: any) => {
      const message = `🎉 Achievement unlocked! ${achievement.title}. ${achievement.description}. You earned ${achievement.points || 0} points.`
      announce(message, { priority: 'high', delay: 500 })
    }, [announce]),

    progress: useCallback((achievement: any, progress: number, milestone?: string) => {
      let message = `Achievement progress: ${achievement.title}. ${progress}% complete.`
      if (milestone) {
        message += ` Milestone reached: ${milestone}.`
      }
      announce(message, { priority: 'low' })
    }, [announce]),

    nearCompletion: useCallback((achievement: any, progress: number) => {
      if (progress >= 80) {
        const message = `Achievement almost complete: ${achievement.title}. ${progress}% done.`
        announce(message, { priority: 'medium' })
      }
    }, [announce])
  }

  /**
   * Announce challenge events
   */
  const challengeAnnouncements = {
    join: useCallback((challenge: any) => {
      const message = `Challenge joined: ${challenge.title}. ${challenge.description}. Good luck!`
      announce(message, { priority: 'medium' })
    }, [announce]),

    complete: useCallback((challenge: any, rewards?: any) => {
      let message = `🏆 Challenge completed! ${challenge.title}.`
      if (rewards?.points) {
        message += ` You earned ${rewards.points} points.`
      }
      if (rewards?.achievements?.length) {
        message += ` ${rewards.achievements.length} new achievements unlocked.`
      }
      announce(message, { priority: 'high', delay: 500 })
    }, [announce]),

    progress: useCallback((challenge: any, progress: number) => {
      const message = `Challenge progress: ${challenge.title}. ${progress}% complete.`
      announce(message, { priority: 'low' })
    }, [announce]),

    timeWarning: useCallback((challenge: any, timeRemaining: string) => {
      const message = `Time warning: ${challenge.title} expires in ${timeRemaining}.`
      announce(message, { priority: 'medium', delay: 200 })
    }, [announce])
  }

  /**
   * Announce reward and points events
   */
  const rewardAnnouncements = {
    purchase: useCallback((reward: any, cost: number, remainingPoints: number) => {
      const message = `Reward purchased: ${reward.title}. Cost: ${cost} points. Remaining balance: ${remainingPoints} points.`
      announce(message, { priority: 'medium' })
    }, [announce]),

    pointsEarned: useCallback((amount: number, source: string, total: number) => {
      const message = `💰 Points earned! +${amount} from ${source}. Total points: ${total}.`
      announce(message, { priority: 'low' })
    }, [announce]),

    tierPromotion: useCallback((oldTier: string, newTier: string, benefits: string[]) => {
      let message = `🎊 Tier promotion! You've advanced from ${oldTier} to ${newTier}.`
      if (benefits.length > 0) {
        message += ` New benefits: ${benefits.join(', ')}.`
      }
      announce(message, { priority: 'high', delay: 1000 })
    }, [announce])
  }

  /**
   * Announce leaderboard and social events
   */
  const socialAnnouncements = {
    leaderboardPosition: useCallback((position: number, total: number, change?: number) => {
      let message = `Leaderboard position: ${position} of ${total}.`
      if (change !== undefined) {
        if (change > 0) {
          message += ` You've moved up ${change} positions! 📈`
        } else if (change < 0) {
          message += ` You've dropped ${Math.abs(change)} positions.`
        } else {
          message += ` No change in position.`
        }
      }
      announce(message, { priority: 'medium' })
    }, [announce]),

    newFollower: useCallback((followerName: string) => {
      const message = `New follower: ${followerName} is now following your progress.`
      announce(message, { priority: 'low' })
    }, [announce])
  }

  /**
   * Create navigation announcements with context
   */
  const announceNavigation = useCallback((context: NavigationContext) => {
    let message = `Navigated to ${context.page}`
    if (context.section) {
      message += `, ${context.section} section`
    }
    if (context.totalItems) {
      message += `. ${context.totalItems} items available`
    }
    if (context.currentItem && context.totalItems) {
      message += `. Currently on item ${context.currentItem} of ${context.totalItems}`
    }
    message += '.'

    announce(message, { priority: 'low', delay: 200 })

    // Announce keyboard instructions after a delay
    if (context.keyboardInstructions) {
      setTimeout(() => {
        announce(context.keyboardInstructions!, { priority: 'low' })
      }, 1500)
    }
  }, [announce])

  /**
   * Create enhanced keyboard navigation for gamification grids
   */
  const createGameKeyboardNavigation = useCallback((
    containerRef: React.RefObject<HTMLElement>,
    itemSelector: string = '[data-accessibility-enhanced="true"]',
    options: {
      columns?: number
      announceSelection?: boolean
      wrapAround?: boolean
    } = {}
  ) => {
    const { columns = 4, announceSelection = true, wrapAround = true } = options

    return (event: React.KeyboardEvent<HTMLElement> | KeyboardEvent) => {
      const key = event.key

      const handleArrowUp = () => {
        const items = containerRef.current?.querySelectorAll(itemSelector) as NodeListOf<HTMLElement>
        if (!items) return

        const currentIndex = Array.from(items).findIndex(item => item === document.activeElement)
        if (currentIndex === -1) return

        const newIndex = currentIndex - columns
        const targetIndex = newIndex < 0 && wrapAround 
          ? items.length + newIndex
          : Math.max(0, newIndex)

        const targetItem = items[targetIndex]
        if (targetItem) {
          targetItem.focus()
          if (announceSelection) {
            const title = targetItem.getAttribute('aria-label') || targetItem.textContent || 'Item'
            announce(`Selected: ${title}`, { priority: 'low' })
          }
        }
      }

      const handleArrowDown = () => {
        const items = containerRef.current?.querySelectorAll(itemSelector) as NodeListOf<HTMLElement>
        if (!items) return

        const currentIndex = Array.from(items).findIndex(item => item === document.activeElement)
        if (currentIndex === -1) return

        const newIndex = currentIndex + columns
        const targetIndex = newIndex >= items.length && wrapAround
          ? newIndex - items.length
          : Math.min(items.length - 1, newIndex)

        const targetItem = items[targetIndex]
        if (targetItem) {
          targetItem.focus()
          if (announceSelection) {
            const title = targetItem.getAttribute('aria-label') || targetItem.textContent || 'Item'
            announce(`Selected: ${title}`, { priority: 'low' })
          }
        }
      }

      const handleArrowLeft = () => {
        const items = containerRef.current?.querySelectorAll(itemSelector) as NodeListOf<HTMLElement>
        if (!items) return

        const currentIndex = Array.from(items).findIndex(item => item === document.activeElement)
        if (currentIndex === -1) return

        const targetIndex = currentIndex === 0 && wrapAround 
          ? items.length - 1 
          : Math.max(0, currentIndex - 1)

        const targetItem = items[targetIndex]
        if (targetItem) {
          targetItem.focus()
          if (announceSelection) {
            const title = targetItem.getAttribute('aria-label') || targetItem.textContent || 'Item'
            announce(`Selected: ${title}`, { priority: 'low' })
          }
        }
      }

      const handleArrowRight = () => {
        const items = containerRef.current?.querySelectorAll(itemSelector) as NodeListOf<HTMLElement>
        if (!items) return

        const currentIndex = Array.from(items).findIndex(item => item === document.activeElement)
        if (currentIndex === -1) return

        const targetIndex = currentIndex === items.length - 1 && wrapAround
          ? 0
          : Math.min(items.length - 1, currentIndex + 1)

        const targetItem = items[targetIndex]
        if (targetItem) {
          targetItem.focus()
          if (announceSelection) {
            const title = targetItem.getAttribute('aria-label') || targetItem.textContent || 'Item'
            announce(`Selected: ${title}`, { priority: 'low' })
          }
        }
      }

      const handleEnterOrSpace = () => {
        const activeElement = document.activeElement as HTMLElement
        if (activeElement && activeElement.click) {
          activeElement.click()
        }
      }

      switch (key) {
        case 'ArrowUp':
          event.preventDefault()
          handleArrowUp()
          break
        case 'ArrowDown':
          event.preventDefault()
          handleArrowDown()
          break
        case 'ArrowLeft':
          event.preventDefault()
          handleArrowLeft()
          break
        case 'ArrowRight':
          event.preventDefault()
          handleArrowRight()
          break
        case 'Enter':
        case ' ':
          event.preventDefault()
          handleEnterOrSpace()
          break
      }
    }
  }, [announce])

  /**
   * Initialize gamification accessibility on mount
   */
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize the accessibility utils with gamification-specific config
      AccessibilityUtils.initialize({
        enableAnnouncements: true,
        enableDescriptions: true,
        enableLiveRegions: true,
        enableKeyboardNavigation: true,
        enableReducedMotion: preferences.reducedMotion,
        enableHighContrast: preferences.highContrast,
        announcementDelay: 100,
        focusManagement: true
      })

      // Create skip links for gamification sections
      const skipLinks = AccessibilityUtils.createSkipLinks([
        { id: 'achievements-section', label: 'achievements section' },
        { id: 'challenges-section', label: 'challenges section' },
        { id: 'rewards-section', label: 'rewards section' },
        { id: 'leaderboard-section', label: 'leaderboard section' }
      ])

      // Add skip links to page if they don't exist
      if (!document.querySelector('.skip-links')) {
        document.body.insertBefore(skipLinks, document.body.firstChild)
      }
    }

    return () => {
      // Clear announcement queue on unmount
      announcementQueueRef.current = []
      processingRef.current = false
    }
  }, [preferences])

  return {
    // Core accessibility functions
    createAccessibleAttributes,
    announce,
    announceNavigation,
    generateId,
    preferences,
    focusManager,

    // Gamification-specific announcements
    achievements: achievementAnnouncements,
    challenges: challengeAnnouncements,
    rewards: rewardAnnouncements,
    social: socialAnnouncements,

    // Enhanced navigation
    createGameKeyboardNavigation,

    // Utility functions
    getKeyboardInstructions: AccessibilityUtils.createKeyboardInstructions,
    prefersReducedMotion: AccessibilityUtils.prefersReducedMotion,
    prefersHighContrast: AccessibilityUtils.prefersHighContrast,
    
    // Status
    isQueueProcessing: () => processingRef.current,
    getQueueLength: () => announcementQueueRef.current.length
  }
}

/**
 * Simplified hook for common gamification accessibility patterns
 */
export function useGamificationElement(element: GamificationElement) {
  const { createAccessibleAttributes, announce } = useGamificationAccessibility()

  const attributes = createAccessibleAttributes(element)

  const announceInteraction = useCallback((action: string) => {
    const message = `${action}: ${element.title}`
    announce(message, { priority: 'low' })
  }, [element.title, announce])

  return {
    attributes,
    announceInteraction,
    announceSelection: () => announceInteraction('Selected'),
    announceActivation: () => announceInteraction('Activated'),
    announceHover: () => announceInteraction('Focused')
  }
}

export default useGamificationAccessibility