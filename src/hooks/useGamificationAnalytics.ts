/**
 * Gamification Analytics Dashboard Hooks
 * 
 * React hooks for real-time analytics dashboard with comprehensive
 * gamification metrics, user engagement tracking, and performance monitoring.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import {
  AnalyticsEngine,
  AnalyticsMetrics,
  UserEngagementMetrics,
  ChallengeAnalytics,
  AnalyticsFilter,
  TimeSeriesData
} from '../lib/api/gamification/analyticsEngine'
import { gamificationCache } from '../lib/api/gamification/cachingService'
import { toast } from 'react-hot-toast'

// ===== TYPES =====

export interface UseGamificationAnalyticsReturn {
  // Data
  metrics: AnalyticsMetrics | null
  loading: boolean
  error: string | null
  lastUpdated: Date | null
  
  // Actions
  refresh: () => Promise<void>
  generateReport: (type: 'summary' | 'detailed' | 'executive') => Promise<any>
  
  // Filters
  filters: AnalyticsFilter | null
  setFilters: (filters: AnalyticsFilter | null) => void
  
  // Real-time updates
  enableRealTime: boolean
  setEnableRealTime: (enabled: boolean) => void
}

export interface UseUserEngagementReturn {
  // Data
  users: UserEngagementMetrics[]
  loading: boolean
  error: string | null
  
  // Pagination
  hasMore: boolean
  loadMore: () => Promise<void>
  
  // Actions
  refresh: () => Promise<void>
  exportData: () => Promise<void>
  
  // Filtering and sorting
  sortBy: keyof UserEngagementMetrics
  sortOrder: 'asc' | 'desc'
  setSorting: (field: keyof UserEngagementMetrics, order: 'asc' | 'desc') => void
  
  // Filters
  filters: AnalyticsFilter | null
  setFilters: (filters: AnalyticsFilter | null) => void
}

export interface UseChallengeAnalyticsReturn {
  // Data
  challenges: ChallengeAnalytics[]
  selectedChallenge: ChallengeAnalytics | null
  loading: boolean
  error: string | null
  
  // Actions
  selectChallenge: (challengeId: string | null) => void
  refresh: () => Promise<void>
  
  // Comparison
  compareMode: boolean
  setCompareMode: (enabled: boolean) => void
  selectedChallenges: string[]
  toggleChallengeSelection: (challengeId: string) => void
}

export interface UseAnalyticsChartsReturn {
  // Chart data
  chartData: {
    userActivity: TimeSeriesData[]
    pointsDistribution: any[]
    tierDistribution: any[]
    achievementProgress: any[]
    challengePerformance: any[]
  }
  
  // Chart options
  timeRange: '24h' | '7d' | '30d' | '90d'
  setTimeRange: (range: '24h' | '7d' | '30d' | '90d') => void
  
  // Loading states
  loading: boolean
  error: string | null
  
  // Actions
  refresh: () => Promise<void>
}

export interface UseRealtimeAnalyticsReturn {
  // Connection status
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  
  // Real-time data
  liveMetrics: Partial<AnalyticsMetrics>
  liveUsers: UserEngagementMetrics[]
  liveChallenges: ChallengeAnalytics[]
  
  // Controls
  subscribe: (type: 'metrics' | 'users' | 'challenges') => void
  unsubscribe: (type: 'metrics' | 'users' | 'challenges') => void
  reconnect: () => void
  
  // Statistics
  updateCount: number
  lastUpdate: Date | null
}

// ===== MAIN GAMIFICATION ANALYTICS HOOK =====

export function useGamificationAnalytics(
  initialFilters?: AnalyticsFilter,
  autoRefresh: boolean = true
): UseGamificationAnalyticsReturn {
  const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [filters, setFilters] = useState<AnalyticsFilter | null>(initialFilters || null)
  const [enableRealTime, setEnableRealTime] = useState(false)
  
  const unsubscribeRef = useRef<(() => void) | null>(null)

  // Load analytics metrics
  const loadMetrics = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      await AnalyticsEngine.initialize()
      const analyticsMetrics = await AnalyticsEngine.getAnalyticsMetrics(filters || undefined)
      
      setMetrics(analyticsMetrics)
      setLastUpdated(new Date())
    } catch (err) {
      console.error('Error loading analytics metrics:', err)
      setError('Failed to load analytics metrics')
      toast.error('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Refresh metrics
  const refresh = useCallback(async () => {
    await gamificationCache.invalidateByTags(['analytics-metrics'])
    await loadMetrics()
  }, [loadMetrics])

  // Generate report
  const generateReport = useCallback(async (type: 'summary' | 'detailed' | 'executive') => {
    try {
      const report = await AnalyticsEngine.generateReport(type, filters || undefined)
      toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)} report generated successfully`)
      return report
    } catch (error) {
      console.error('Error generating report:', error)
      toast.error('Failed to generate report')
      throw error
    }
  }, [filters])

  // Real-time subscription management
  useEffect(() => {
    if (enableRealTime) {
      unsubscribeRef.current = AnalyticsEngine.subscribeToAnalytics(
        'metrics',
        (updatedMetrics: AnalyticsMetrics) => {
          setMetrics(updatedMetrics)
          setLastUpdated(new Date())
        },
        filters || undefined
      )
    } else {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }
    }
  }, [enableRealTime, filters])

  // Load initial data
  useEffect(() => {
    loadMetrics()
  }, [loadMetrics])

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh || enableRealTime) return

    const interval = setInterval(() => {
      if (!document.hidden) {
        refresh()
      }
    }, 5 * 60 * 1000) // Refresh every 5 minutes

    return () => clearInterval(interval)
  }, [autoRefresh, enableRealTime, refresh])

  return {
    metrics,
    loading,
    error,
    lastUpdated,
    refresh,
    generateReport,
    filters,
    setFilters,
    enableRealTime,
    setEnableRealTime
  }
}

// ===== USER ENGAGEMENT HOOK =====

export function useUserEngagement(
  limit: number = 100,
  initialFilters?: AnalyticsFilter
): UseUserEngagementReturn {
  const [users, setUsers] = useState<UserEngagementMetrics[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [filters, setFilters] = useState<AnalyticsFilter | null>(initialFilters || null)
  const [sortBy, setSortBy] = useState<keyof UserEngagementMetrics>('engagementScore')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  
  const currentLimit = useRef(limit)

  // Load user engagement data
  const loadUsers = useCallback(async (reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true)
        currentLimit.current = limit
      }
      
      setError(null)
      
      const userMetrics = await AnalyticsEngine.getUserEngagementMetrics(
        currentLimit.current,
        filters || undefined
      )
      
      // Apply sorting
      const sortedUsers = [...userMetrics].sort((a, b) => {
        const aValue = a[sortBy]
        const bValue = b[sortBy]
        
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortOrder === 'desc' ? bValue - aValue : aValue - bValue
        }
        
        const aStr = String(aValue)
        const bStr = String(bValue)
        return sortOrder === 'desc' ? bStr.localeCompare(aStr) : aStr.localeCompare(bStr)
      })
      
      setUsers(sortedUsers)
      setHasMore(userMetrics.length === currentLimit.current)
    } catch (err) {
      console.error('Error loading user engagement:', err)
      setError('Failed to load user engagement data')
    } finally {
      setLoading(false)
    }
  }, [filters, sortBy, sortOrder, limit])

  // Load more users
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return
    
    currentLimit.current += limit
    await loadUsers(false)
  }, [hasMore, loading, loadUsers, limit])

  // Refresh data
  const refresh = useCallback(async () => {
    await gamificationCache.invalidateByTags(['analytics-users'])
    currentLimit.current = limit
    await loadUsers(true)
  }, [loadUsers, limit])

  // Export data
  const exportData = useCallback(async () => {
    try {
      const csvData = users.map(user => ({
        Username: user.username || user.userId,
        'Engagement Score': user.engagementScore,
        'Total Points': user.totalPoints,
        'Achievements Unlocked': user.achievementsUnlocked,
        'Challenges Completed': user.challengesCompleted,
        'Current Tier': user.currentTier,
        'Churn Risk': user.churnRisk,
        'Last Active': user.lastActiveAt
      }))
      
      const csv = [
        Object.keys(csvData[0]).join(','),
        ...csvData.map(row => Object.values(row).join(','))
      ].join('\n')
      
      const blob = new Blob([csv], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `user-engagement-${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      URL.revokeObjectURL(url)
      
      toast.success('User engagement data exported successfully')
    } catch (error) {
      console.error('Error exporting data:', error)
      toast.error('Failed to export data')
    }
  }, [users])

  // Set sorting
  const setSorting = useCallback((field: keyof UserEngagementMetrics, order: 'asc' | 'desc') => {
    setSortBy(field)
    setSortOrder(order)
  }, [])

  // Load initial data
  useEffect(() => {
    loadUsers(true)
  }, [loadUsers])

  return {
    users,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    exportData,
    sortBy,
    sortOrder,
    setSorting,
    filters,
    setFilters
  }
}

// ===== CHALLENGE ANALYTICS HOOK =====

export function useChallengeAnalytics(): UseChallengeAnalyticsReturn {
  const [challenges, setChallenges] = useState<ChallengeAnalytics[]>([])
  const [selectedChallenge, setSelectedChallenge] = useState<ChallengeAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [compareMode, setCompareMode] = useState(false)
  const [selectedChallenges, setSelectedChallenges] = useState<string[]>([])

  // Load challenge analytics
  const loadChallenges = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const challengeAnalytics = await AnalyticsEngine.getChallengeAnalytics()
      setChallenges(challengeAnalytics)
    } catch (err) {
      console.error('Error loading challenge analytics:', err)
      setError('Failed to load challenge analytics')
    } finally {
      setLoading(false)
    }
  }, [])

  // Select challenge
  const selectChallenge = useCallback(async (challengeId: string | null) => {
    if (!challengeId) {
      setSelectedChallenge(null)
      return
    }

    try {
      const challengeAnalytics = await AnalyticsEngine.getChallengeAnalytics(challengeId)
      setSelectedChallenge(challengeAnalytics[0] || null)
    } catch (err) {
      console.error('Error loading challenge details:', err)
      toast.error('Failed to load challenge details')
    }
  }, [])

  // Refresh data
  const refresh = useCallback(async () => {
    await gamificationCache.invalidateByTags(['analytics-challenges'])
    await loadChallenges()
    if (selectedChallenge) {
      await selectChallenge(selectedChallenge.challengeId)
    }
  }, [loadChallenges, selectChallenge, selectedChallenge])

  // Toggle challenge selection for comparison
  const toggleChallengeSelection = useCallback((challengeId: string) => {
    setSelectedChallenges(prev => {
      if (prev.includes(challengeId)) {
        return prev.filter(id => id !== challengeId)
      } else if (prev.length < 3) { // Limit to 3 challenges for comparison
        return [...prev, challengeId]
      } else {
        toast.warning('You can compare up to 3 challenges at once')
        return prev
      }
    })
  }, [])

  // Load initial data
  useEffect(() => {
    loadChallenges()
  }, [loadChallenges])

  return {
    challenges,
    selectedChallenge,
    loading,
    error,
    selectChallenge,
    refresh,
    compareMode,
    setCompareMode,
    selectedChallenges,
    toggleChallengeSelection
  }
}

// ===== ANALYTICS CHARTS HOOK =====

export function useAnalyticsCharts(): UseAnalyticsChartsReturn {
  const [chartData, setChartData] = useState({
    userActivity: [] as TimeSeriesData[],
    pointsDistribution: [],
    tierDistribution: [],
    achievementProgress: [],
    challengePerformance: []
  })
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | '90d'>('7d')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load chart data
  const loadChartData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const metrics = await AnalyticsEngine.getAnalyticsMetrics()
      
      if (metrics) {
        // Transform data for charts
        const transformedData = {
          userActivity: timeRange === '24h' ? metrics.activity.hourly :
                       timeRange === '7d' ? metrics.activity.daily :
                       timeRange === '30d' ? metrics.activity.daily :
                       metrics.activity.weekly,
          pointsDistribution: [
            { name: 'Top 10%', value: metrics.pointsDistribution.topPercentile },
            { name: 'Average', value: metrics.pointsDistribution.averagePoints },
            { name: 'Median', value: metrics.pointsDistribution.median }
          ],
          tierDistribution: metrics.tierDistribution.map(tier => ({
            name: tier.tier,
            users: tier.userCount,
            percentage: tier.percentage
          })),
          achievementProgress: metrics.achievementStats.popularAchievements.map(achievement => ({
            name: achievement.name,
            completed: achievement.count,
            percentage: achievement.percentage
          })),
          challengePerformance: metrics.challengeStats.popularChallenges.map(challenge => ({
            name: challenge.name,
            participants: challenge.count,
            percentage: challenge.percentage
          }))
        }
        
        setChartData(transformedData)
      }
    } catch (err) {
      console.error('Error loading chart data:', err)
      setError('Failed to load chart data')
    } finally {
      setLoading(false)
    }
  }, [timeRange])

  // Refresh data
  const refresh = useCallback(async () => {
    await gamificationCache.invalidateByTags(['analytics-metrics'])
    await loadChartData()
  }, [loadChartData])

  // Load data when time range changes
  useEffect(() => {
    loadChartData()
  }, [loadChartData])

  return {
    chartData,
    timeRange,
    setTimeRange,
    loading,
    error,
    refresh
  }
}

// ===== REAL-TIME ANALYTICS HOOK =====

export function useRealtimeAnalytics(): UseRealtimeAnalyticsReturn {
  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [liveMetrics, setLiveMetrics] = useState<Partial<AnalyticsMetrics>>({})
  const [liveUsers, setLiveUsers] = useState<UserEngagementMetrics[]>([])
  const [liveChallenges, setLiveChallenges] = useState<ChallengeAnalytics[]>([])
  const [updateCount, setUpdateCount] = useState(0)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  
  const subscriptionsRef = useRef<Map<string, () => void>>(new Map())

  // Subscribe to real-time updates
  const subscribe = useCallback((type: 'metrics' | 'users' | 'challenges') => {
    try {
      setConnectionStatus('connecting')
      
      const unsubscribe = AnalyticsEngine.subscribeToAnalytics(
        type,
        (data: any) => {
          switch (type) {
            case 'metrics':
              setLiveMetrics(data)
              break
            case 'users':
              setLiveUsers(data)
              break
            case 'challenges':
              setLiveChallenges(data)
              break
          }
          
          setUpdateCount(prev => prev + 1)
          setLastUpdate(new Date())
          setIsConnected(true)
          setConnectionStatus('connected')
        }
      )
      
      subscriptionsRef.current.set(type, unsubscribe)
    } catch (error) {
      console.error(`Error subscribing to ${type}:`, error)
      setConnectionStatus('error')
    }
  }, [])

  // Unsubscribe from updates
  const unsubscribe = useCallback((type: 'metrics' | 'users' | 'challenges') => {
    const unsub = subscriptionsRef.current.get(type)
    if (unsub) {
      unsub()
      subscriptionsRef.current.delete(type)
    }
    
    if (subscriptionsRef.current.size === 0) {
      setIsConnected(false)
      setConnectionStatus('disconnected')
    }
  }, [])

  // Reconnect
  const reconnect = useCallback(() => {
    // Unsubscribe all current subscriptions
    subscriptionsRef.current.forEach(unsub => unsub())
    subscriptionsRef.current.clear()
    
    setIsConnected(false)
    setConnectionStatus('disconnected')
    
    // Reconnect to previously active subscriptions
    setTimeout(() => {
      subscribe('metrics')
      subscribe('users')
      subscribe('challenges')
    }, 1000)
  }, [subscribe])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      subscriptionsRef.current.forEach(unsub => unsub())
      subscriptionsRef.current.clear()
    }
  }, [])

  return {
    isConnected,
    connectionStatus,
    liveMetrics,
    liveUsers,
    liveChallenges,
    subscribe,
    unsubscribe,
    reconnect,
    updateCount,
    lastUpdate
  }
}

export default useGamificationAnalytics