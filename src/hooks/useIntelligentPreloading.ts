/**
 * Intelligent Preloading Hook
 * 
 * Smart preloading system that loads components based on user behavior,
 * interaction patterns, and engagement metrics.
 * 
 * Phase 2 Performance Optimization - Community System Audit
 * 
 * <AUTHOR> Team
 */

import { useEffect, useCallback, useRef } from 'react'
import { useUser } from '@/lib/useUser'
import { preloadCommunityComponent, shouldLoadCommunityFeatures, loadCommunityBundle } from '@/components/dynamic/DynamicCommunityComponents'
import { preloadGamificationComponent } from '@/components/dynamic/DynamicGamificationComponents'

interface PreloadingConfig {
  enabled: boolean
  delayMs: number
  maxConcurrentPreloads: number
  userEngagementThreshold: number
}

interface UserEngagementMetrics {
  timeOnSite: number
  pageViews: number
  communityInteractions: number
  gamificationActivity: number
  lastActiveTimestamp: number
}

const DEFAULT_CONFIG: PreloadingConfig = {
  enabled: true,
  delayMs: 2000,
  maxConcurrentPreloads: 3,
  userEngagementThreshold: 5
}

export function useIntelligentPreloading(config: Partial<PreloadingConfig> = {}) {
  const { user } = useUser()
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const preloadQueue = useRef<string[]>([])
  const preloadedComponents = useRef<Set<string>>(new Set())
  const engagementMetrics = useRef<UserEngagementMetrics>({
    timeOnSite: 0,
    pageViews: 0,
    communityInteractions: 0,
    gamificationActivity: 0,
    lastActiveTimestamp: Date.now()
  })

  // Track user engagement metrics
  const trackEngagement = useCallback((type: keyof UserEngagementMetrics, value?: number) => {
    const metrics = engagementMetrics.current
    
    switch (type) {
      case 'timeOnSite':
        metrics.timeOnSite = Date.now() - metrics.lastActiveTimestamp
        break
      case 'pageViews':
        metrics.pageViews += 1
        break
      case 'communityInteractions':
        metrics.communityInteractions += value || 1
        break
      case 'gamificationActivity':
        metrics.gamificationActivity += value || 1
        break
    }
    
    metrics.lastActiveTimestamp = Date.now()
  }, [])

  // Calculate user engagement score
  const calculateEngagementScore = useCallback((): number => {
    const metrics = engagementMetrics.current
    const timeScore = Math.min(metrics.timeOnSite / (1000 * 60), 10) // Max 10 points for 1 minute
    const pageScore = Math.min(metrics.pageViews * 2, 10) // Max 10 points for 5 page views
    const communityScore = Math.min(metrics.communityInteractions * 3, 15) // Max 15 points
    const gamificationScore = Math.min(metrics.gamificationActivity * 2, 10) // Max 10 points
    
    return timeScore + pageScore + communityScore + gamificationScore
  }, [])

  // Determine what to preload based on user behavior
  const determinePreloadPriority = useCallback((): string[] => {
    if (!user || !finalConfig.enabled) return []
    
    const engagementScore = calculateEngagementScore()
    const priority: string[] = []
    
    // Always preload core features for engaged users
    if (engagementScore >= finalConfig.userEngagementThreshold) {
      priority.push('core-community')
    }
    
    // Preload based on user profile
    if (shouldLoadCommunityFeatures(user)) {
      priority.push('community-achievements', 'community-leaderboard')
    }
    
    // Preload gamification if user has points or achievements
    if (user.points > 0 || user.achievements?.length > 0) {
      priority.push('gamification-rewards', 'gamification-points')
    }
    
    // Preload advanced features for highly engaged users
    if (engagementScore >= 20) {
      priority.push('community-challenges', 'community-voting', 'community-analytics')
    }
    
    // Preload social features if user has community activity
    if (user.communityLevel > 3 || metrics.communityInteractions > 5) {
      priority.push('community-feed', 'community-discussions')
    }
    
    return priority
  }, [user, finalConfig, calculateEngagementScore])

  // Execute preloading with queue management
  const executePreloading = useCallback(async (components: string[]) => {
    const toPreload = components.filter(comp => !preloadedComponents.current.has(comp))
    
    if (toPreload.length === 0) return
    
    // Limit concurrent preloads
    const batch = toPreload.slice(0, finalConfig.maxConcurrentPreloads)
    
    const preloadPromises = batch.map(async (componentName) => {
      try {
        switch (componentName) {
          case 'core-community':
            loadCommunityBundle('Core')
            break
          case 'community-achievements':
            preloadCommunityComponent('achievements')
            break
          case 'community-leaderboard':
            preloadCommunityComponent('leaderboard')
            break
          case 'community-challenges':
            preloadCommunityComponent('challenges')
            break
          case 'community-voting':
            preloadCommunityComponent('voting')
            break
          case 'community-analytics':
            preloadCommunityComponent('analytics')
            break
          case 'community-feed':
            preloadCommunityComponent('feed')
            break
          case 'community-discussions':
            preloadCommunityComponent('discussions')
            break
          case 'gamification-rewards':
            preloadGamificationComponent('rewardShop')
            break
          case 'gamification-points':
            preloadGamificationComponent('achievements')
            break
        }
        
        preloadedComponents.current.add(componentName)
      } catch (error) {
        console.warn(`Failed to preload ${componentName}:`, error)
      }
    })
    
    await Promise.allSettled(preloadPromises)
  }, [finalConfig.maxConcurrentPreloads])

  // Main preloading effect
  useEffect(() => {
    if (!finalConfig.enabled || !user) return
    
    const timer = setTimeout(() => {
      const priorityComponents = determinePreloadPriority()
      executePreloading(priorityComponents)
    }, finalConfig.delayMs)
    
    return () => clearTimeout(timer)
  }, [user, finalConfig, determinePreloadPriority, executePreloading])

  // Track page views
  useEffect(() => {
    trackEngagement('pageViews')
  }, [trackEngagement])

  // Track time on site
  useEffect(() => {
    const startTime = Date.now()
    
    return () => {
      const timeSpent = Date.now() - startTime
      engagementMetrics.current.timeOnSite += timeSpent
    }
  }, [])

  // Preload on user interaction
  const preloadOnInteraction = useCallback((interactionType: 'community' | 'gamification') => {
    if (interactionType === 'community') {
      trackEngagement('communityInteractions')
      setTimeout(() => {
        loadCommunityBundle('Social')
      }, 500)
    } else if (interactionType === 'gamification') {
      trackEngagement('gamificationActivity')
      setTimeout(() => {
        preloadGamificationComponent('rewardShop')
        preloadGamificationComponent('achievements')
      }, 500)
    }
  }, [trackEngagement])

  // Preload on hover (for navigation items)
  const preloadOnHover = useCallback((componentType: string) => {
    if (preloadedComponents.current.has(componentType)) return
    
    setTimeout(() => {
      executePreloading([componentType])
    }, 100) // Quick preload on hover
  }, [executePreloading])

  // Force preload specific component
  const forcePreload = useCallback((componentName: string) => {
    executePreloading([componentName])
  }, [executePreloading])

  return {
    // Metrics
    engagementScore: calculateEngagementScore(),
    preloadedComponents: Array.from(preloadedComponents.current),
    
    // Actions
    preloadOnInteraction,
    preloadOnHover,
    forcePreload,
    trackEngagement,
    
    // Status
    isEnabled: finalConfig.enabled,
    config: finalConfig
  }
}

// Hook for component-specific preloading
export function useComponentPreloading(componentName: string, triggerCondition?: boolean) {
  const { forcePreload } = useIntelligentPreloading()
  
  useEffect(() => {
    if (triggerCondition !== false) {
      forcePreload(componentName)
    }
  }, [componentName, triggerCondition, forcePreload])
}

// Hook for hover-based preloading
export function useHoverPreloading() {
  const { preloadOnHover } = useIntelligentPreloading()
  
  const createHoverHandlers = useCallback((componentName: string) => ({
    onMouseEnter: () => preloadOnHover(componentName),
    onFocus: () => preloadOnHover(componentName)
  }), [preloadOnHover])
  
  return { createHoverHandlers }
}

export default useIntelligentPreloading
