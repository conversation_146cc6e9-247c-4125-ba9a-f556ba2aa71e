/**
 * Optimized Animations Hook
 * 
 * Performance-optimized animation system with reduced motion support,
 * mobile optimizations, and intelligent animation management.
 * 
 * Phase 2 Performance Optimization - Community System Audit
 * 
 * <AUTHOR> Team
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { useAnimation, AnimationControls, Variants, TargetAndTransition } from 'framer-motion'

interface AnimationOptions {
  respectReducedMotion?: boolean
  enableOnMobile?: boolean
  enableOnLowEnd?: boolean
  maxConcurrentAnimations?: number
  performanceMode?: 'auto' | 'high' | 'balanced' | 'low'
}

interface DeviceCapabilities {
  isMobile: boolean
  isLowEnd: boolean
  prefersReducedMotion: boolean
  supportsGPUAcceleration: boolean
  connectionSpeed: 'slow' | 'fast' | 'unknown'
}

const DEFAULT_OPTIONS: AnimationOptions = {
  respectReducedMotion: true,
  enableOnMobile: true,
  enableOnLowEnd: false,
  maxConcurrentAnimations: 10,
  performanceMode: 'auto'
}

export function useOptimizedAnimations(options: AnimationOptions = {}) {
  const finalOptions = { ...DEFAULT_OPTIONS, ...options }
  const [deviceCapabilities, setDeviceCapabilities] = useState<DeviceCapabilities>({
    isMobile: false,
    isLowEnd: false,
    prefersReducedMotion: false,
    supportsGPUAcceleration: true,
    connectionSpeed: 'unknown'
  })
  
  const activeAnimations = useRef<Set<string>>(new Set())
  const animationQueue = useRef<Array<() => void>>([])
  const controls = useAnimation()

  // Detect device capabilities
  useEffect(() => {
    if (typeof window === 'undefined') return

    const detectCapabilities = () => {
      // Check for mobile
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      
      // Check for reduced motion preference
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
      
      // Estimate device performance
      const isLowEnd = (navigator as any).hardwareConcurrency <= 2 || 
                      (navigator as any).deviceMemory <= 2 ||
                      /Android.*Chrome\/[0-5]/.test(navigator.userAgent)
      
      // Check GPU acceleration support
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      const supportsGPUAcceleration = !!gl
      
      // Estimate connection speed
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
      const connectionSpeed = connection ? 
        (connection.effectiveType === '4g' ? 'fast' : 'slow') : 'unknown'

      setDeviceCapabilities({
        isMobile,
        isLowEnd,
        prefersReducedMotion,
        supportsGPUAcceleration,
        connectionSpeed
      })
    }

    detectCapabilities()

    // Listen for reduced motion changes
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    const handleChange = () => {
      setDeviceCapabilities(prev => ({
        ...prev,
        prefersReducedMotion: mediaQuery.matches
      }))
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Determine if animations should be enabled
  const shouldAnimate = useMemo(() => {
    if (finalOptions.respectReducedMotion && deviceCapabilities.prefersReducedMotion) {
      return false
    }
    
    if (!finalOptions.enableOnMobile && deviceCapabilities.isMobile) {
      return false
    }
    
    if (!finalOptions.enableOnLowEnd && deviceCapabilities.isLowEnd) {
      return false
    }
    
    return true
  }, [finalOptions, deviceCapabilities])

  // Get performance mode
  const performanceMode = useMemo(() => {
    if (finalOptions.performanceMode !== 'auto') {
      return finalOptions.performanceMode
    }
    
    if (deviceCapabilities.isLowEnd || deviceCapabilities.connectionSpeed === 'slow') {
      return 'low'
    }
    
    if (deviceCapabilities.isMobile) {
      return 'balanced'
    }
    
    return 'high'
  }, [finalOptions.performanceMode, deviceCapabilities])

  // Create optimized animation variants
  const createOptimizedVariants = useCallback((baseVariants: Variants): Variants => {
    if (!shouldAnimate) {
      // Return instant variants for reduced motion
      const instantVariants: Variants = {}
      Object.keys(baseVariants).forEach(key => {
        const variant = baseVariants[key]
        if (typeof variant === 'object' && variant !== null) {
          instantVariants[key] = {
            ...variant,
            transition: { duration: 0.01 }
          }
        }
      })
      return instantVariants
    }

    // Optimize based on performance mode
    const optimizedVariants: Variants = {}
    Object.keys(baseVariants).forEach(key => {
      const variant = baseVariants[key]
      if (typeof variant === 'object' && variant !== null) {
        let optimizedVariant = { ...variant }
        
        // Adjust based on performance mode
        if (performanceMode === 'low') {
          optimizedVariant = {
            ...optimizedVariant,
            transition: {
              ...optimizedVariant.transition,
              duration: (optimizedVariant.transition?.duration || 0.3) * 0.5,
              ease: 'linear'
            }
          }
        } else if (performanceMode === 'balanced') {
          optimizedVariant = {
            ...optimizedVariant,
            transition: {
              ...optimizedVariant.transition,
              duration: (optimizedVariant.transition?.duration || 0.3) * 0.75
            }
          }
        }
        
        optimizedVariants[key] = optimizedVariant
      }
    })
    
    return optimizedVariants
  }, [shouldAnimate, performanceMode])

  // Create GPU-accelerated animation
  const createGPUOptimizedAnimation = useCallback((animation: TargetAndTransition): TargetAndTransition => {
    if (!deviceCapabilities.supportsGPUAcceleration) {
      return animation
    }

    // Use transform properties for GPU acceleration
    const optimized: TargetAndTransition = { ...animation }
    
    // Convert position changes to transforms
    if ('x' in optimized || 'y' in optimized) {
      optimized.transform = `translate3d(${optimized.x || 0}px, ${optimized.y || 0}px, 0)`
      delete optimized.x
      delete optimized.y
    }
    
    // Ensure will-change is set for GPU acceleration
    optimized.willChange = 'transform, opacity'
    
    return optimized
  }, [deviceCapabilities.supportsGPUAcceleration])

  // Animation queue management
  const queueAnimation = useCallback((animationId: string, animationFn: () => void) => {
    if (activeAnimations.current.size >= finalOptions.maxConcurrentAnimations!) {
      animationQueue.current.push(animationFn)
      return
    }
    
    activeAnimations.current.add(animationId)
    animationFn()
    
    // Clean up after animation
    setTimeout(() => {
      activeAnimations.current.delete(animationId)
      
      // Process queue
      if (animationQueue.current.length > 0) {
        const nextAnimation = animationQueue.current.shift()
        if (nextAnimation) {
          nextAnimation()
        }
      }
    }, 1000) // Assume max animation duration of 1s
  }, [finalOptions.maxConcurrentAnimations])

  // Staggered animation with performance optimization
  const createStaggeredAnimation = useCallback((
    items: any[],
    baseDelay: number = 0.1,
    maxItems: number = 20
  ) => {
    const itemsToAnimate = performanceMode === 'low' ? 
      Math.min(items.length, maxItems) : items.length
    
    const adjustedDelay = performanceMode === 'low' ? 
      baseDelay * 0.5 : baseDelay
    
    return items.slice(0, itemsToAnimate).map((_, index) => ({
      delay: index * adjustedDelay
    }))
  }, [performanceMode])

  // Optimized gesture handlers
  const createOptimizedGestures = useCallback((options: {
    hover?: boolean
    tap?: boolean
    scale?: number
  } = {}) => {
    if (!shouldAnimate) {
      return {}
    }
    
    const { hover = true, tap = true, scale = 1.05 } = options
    const gestures: any = {}
    
    if (hover && !deviceCapabilities.isMobile) {
      gestures.whileHover = createGPUOptimizedAnimation({
        scale: scale,
        transition: { duration: 0.2 }
      })
    }
    
    if (tap) {
      gestures.whileTap = createGPUOptimizedAnimation({
        scale: scale * 0.95,
        transition: { duration: 0.1 }
      })
    }
    
    return gestures
  }, [shouldAnimate, deviceCapabilities.isMobile, createGPUOptimizedAnimation])

  // Performance monitoring
  const [animationMetrics, setAnimationMetrics] = useState({
    activeCount: 0,
    queuedCount: 0,
    averageDuration: 0,
    droppedFrames: 0
  })

  useEffect(() => {
    const updateMetrics = () => {
      setAnimationMetrics({
        activeCount: activeAnimations.current.size,
        queuedCount: animationQueue.current.length,
        averageDuration: 0, // Would need more complex tracking
        droppedFrames: 0 // Would need performance observer
      })
    }
    
    const interval = setInterval(updateMetrics, 1000)
    return () => clearInterval(interval)
  }, [])

  return {
    // Device info
    deviceCapabilities,
    shouldAnimate,
    performanceMode,
    
    // Animation utilities
    createOptimizedVariants,
    createGPUOptimizedAnimation,
    createStaggeredAnimation,
    createOptimizedGestures,
    queueAnimation,
    
    // Controls
    controls,
    
    // Metrics
    animationMetrics,
    
    // Presets for common animations
    presets: {
      fadeIn: createOptimizedVariants({
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.3 } }
      }),
      
      slideUp: createOptimizedVariants({
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }
      }),
      
      scaleIn: createOptimizedVariants({
        hidden: { opacity: 0, scale: 0.9 },
        visible: { opacity: 1, scale: 1, transition: { duration: 0.3 } }
      }),
      
      staggerContainer: {
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: performanceMode === 'low' ? 0.05 : 0.1
          }
        }
      }
    }
  }
}

export default useOptimizedAnimations
