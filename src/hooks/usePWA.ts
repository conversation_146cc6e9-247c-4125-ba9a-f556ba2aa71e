/**
 * <PERSON><PERSON> Hooks
 * 
 * React hooks for Progressive Web App functionality including
 * offline detection, service worker management, and background sync.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  getOfflineManager, 
  getBackgroundSyncManager,
  initializePWA,
  updateServiceWorker,
  CacheManager
} from '../lib/pwa/serviceWorker'

// ===== TYPES =====

export interface PWAStatus {
  isOnline: boolean
  isInstallable: boolean
  isInstalled: boolean
  hasUpdate: boolean
  isLoading: boolean
  error: string | null
}

export interface OfflineQueueStatus {
  pending: number
  failed: number
  processing: boolean
  oldestItem?: number
}

export interface CacheStatus {
  [cacheName: string]: {
    size: number
    maxSize: number
    usage: number
  }
}

// ===== MAIN PWA HOOK =====

export function usePWA() {
  const [status, setStatus] = useState<PWAStatus>({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    isInstallable: false,
    isInstalled: false,
    hasUpdate: false,
    isLoading: true,
    error: null
  })

  const installPromptRef = useRef<any>(null)
  const registrationRef = useRef<ServiceWorkerRegistration | null>(null)

  // Initialize PWA
  useEffect(() => {
    const initPWA = async () => {
      try {
        setStatus(prev => ({ ...prev, isLoading: true, error: null }))

        // Initialize service worker
        const registration = await initializePWA()
        registrationRef.current = registration || null

        // Check if app is installed
        const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                           (window.navigator as any).standalone === true

        setStatus(prev => ({
          ...prev,
          isInstalled,
          isLoading: false
        }))

      } catch (error) {
        console.error('PWA initialization failed:', error)
        setStatus(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : 'PWA initialization failed',
          isLoading: false
        }))
      }
    }

    initPWA()
  }, [])

  // Listen for install prompt
  useEffect(() => {
    const handleBeforeInstallPrompt = (event: any) => {
      event.preventDefault()
      installPromptRef.current = event
      setStatus(prev => ({ ...prev, isInstallable: true }))
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    }
  }, [])

  // Listen for service worker updates
  useEffect(() => {
    const handleSWUpdate = (event: any) => {
      setStatus(prev => ({ ...prev, hasUpdate: true }))
    }

    window.addEventListener('sw-update-available', handleSWUpdate)

    return () => {
      window.removeEventListener('sw-update-available', handleSWUpdate)
    }
  }, [])

  // Listen for online/offline status
  useEffect(() => {
    const offlineManager = getOfflineManager()
    
    const unsubscribe = offlineManager.addListener((isOnline) => {
      setStatus(prev => ({ ...prev, isOnline }))
    })

    return unsubscribe
  }, [])

  // Install app
  const installApp = useCallback(async () => {
    if (!installPromptRef.current) {
      throw new Error('App installation not available')
    }

    try {
      const result = await installPromptRef.current.prompt()
      
      if (result.outcome === 'accepted') {
        setStatus(prev => ({ 
          ...prev, 
          isInstallable: false, 
          isInstalled: true 
        }))
      }
      
      installPromptRef.current = null
      return result.outcome
    } catch (error) {
      console.error('App installation failed:', error)
      throw error
    }
  }, [])

  // Update service worker
  const updateApp = useCallback(async () => {
    if (!registrationRef.current) {
      throw new Error('No service worker registration available')
    }

    try {
      await updateServiceWorker(registrationRef.current)
      setStatus(prev => ({ ...prev, hasUpdate: false }))
      
      // Reload page to apply update
      window.location.reload()
    } catch (error) {
      console.error('Service worker update failed:', error)
      throw error
    }
  }, [])

  return {
    status,
    installApp,
    updateApp
  }
}

// ===== OFFLINE FUNCTIONALITY HOOK =====

export function useOffline() {
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [queueStatus, setQueueStatus] = useState<OfflineQueueStatus>({
    pending: 0,
    failed: 0,
    processing: false
  })

  const offlineManager = getOfflineManager()
  const syncManager = getBackgroundSyncManager()

  // Listen for online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Monitor sync queue status
  useEffect(() => {
    const updateQueueStatus = () => {
      const status = syncManager.getQueueStatus()
      setQueueStatus(prev => ({ ...prev, ...status }))
    }

    // Update initially
    updateQueueStatus()

    // Update periodically
    const interval = setInterval(updateQueueStatus, 5000)

    return () => clearInterval(interval)
  }, [syncManager])

  // Add item to sync queue
  const addToQueue = useCallback(async (type: string, data: any) => {
    return await syncManager.addToQueue(type, data)
  }, [syncManager])

  // Retry failed operations
  const retryOperation = useCallback((retryFn: () => Promise<any>) => {
    offlineManager.addToRetryQueue(retryFn)
  }, [offlineManager])

  // Clear sync queue
  const clearQueue = useCallback(() => {
    syncManager.clearQueue()
    setQueueStatus(prev => ({ ...prev, pending: 0, failed: 0 }))
  }, [syncManager])

  return {
    isOnline,
    queueStatus,
    addToQueue,
    retryOperation,
    clearQueue
  }
}

// ===== CACHE MANAGEMENT HOOK =====

export function useCache() {
  const [cacheStatus, setCacheStatus] = useState<CacheStatus>({})
  const [isLoading, setIsLoading] = useState(false)

  // Get cache status
  const getCacheStatus = useCallback(async () => {
    setIsLoading(true)
    try {
      const cacheManagers = {
        static: new CacheManager('syndicaps-static-v2.0.0', 50),
        dynamic: new CacheManager('syndicaps-dynamic-v2.0.0', 100),
        api: new CacheManager('syndicaps-api-v2.0.0', 50),
        gamification: new CacheManager('syndicaps-gamification-v2.0.0', 50),
        images: new CacheManager('syndicaps-images-v2.0.0', 200)
      }

      const status: CacheStatus = {}
      
      for (const [name, manager] of Object.entries(cacheManagers)) {
        status[name] = await manager.getStats()
      }

      setCacheStatus(status)
    } catch (error) {
      console.error('Failed to get cache status:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Clear specific cache
  const clearCache = useCallback(async (cacheName: string) => {
    try {
      const manager = new CacheManager(cacheName)
      await manager.clear()
      await getCacheStatus() // Refresh status
    } catch (error) {
      console.error('Failed to clear cache:', error)
      throw error
    }
  }, [getCacheStatus])

  // Clear all caches
  const clearAllCaches = useCallback(async () => {
    try {
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        const messageChannel = new MessageChannel()
        
        const promise = new Promise((resolve, reject) => {
          messageChannel.port1.onmessage = (event) => {
            if (event.data.success) {
              resolve(event.data)
            } else {
              reject(new Error('Failed to clear caches'))
            }
          }
        })

        navigator.serviceWorker.controller.postMessage(
          { type: 'CLEAR_CACHE' },
          [messageChannel.port2]
        )

        await promise
        await getCacheStatus() // Refresh status
      }
    } catch (error) {
      console.error('Failed to clear all caches:', error)
      throw error
    }
  }, [getCacheStatus])

  // Load cache status on mount
  useEffect(() => {
    getCacheStatus()
  }, [getCacheStatus])

  return {
    cacheStatus,
    isLoading,
    getCacheStatus,
    clearCache,
    clearAllCaches
  }
}

// ===== GAMIFICATION OFFLINE SYNC HOOK =====

export function useGamificationSync() {
  const { addToQueue, queueStatus, isOnline } = useOffline()
  const [syncHistory, setSyncHistory] = useState<Array<{
    id: string
    type: string
    timestamp: number
    status: 'pending' | 'synced' | 'failed'
  }>>([])

  // Queue achievement progress update
  const syncAchievementProgress = useCallback(async (
    achievementId: string,
    progress: number,
    isCompleted: boolean
  ) => {
    const data = {
      achievementId,
      progress,
      isCompleted,
      timestamp: Date.now()
    }

    const syncId = await addToQueue('achievement-progress', data)
    
    setSyncHistory(prev => [...prev, {
      id: syncId,
      type: 'achievement-progress',
      timestamp: Date.now(),
      status: 'pending'
    }])

    return syncId
  }, [addToQueue])

  // Queue challenge progress update
  const syncChallengeProgress = useCallback(async (
    challengeId: string,
    progress: number,
    isCompleted: boolean
  ) => {
    const data = {
      challengeId,
      progress,
      isCompleted,
      timestamp: Date.now()
    }

    const syncId = await addToQueue('challenge-progress', data)
    
    setSyncHistory(prev => [...prev, {
      id: syncId,
      type: 'challenge-progress',
      timestamp: Date.now(),
      status: 'pending'
    }])

    return syncId
  }, [addToQueue])

  // Queue points transaction
  const syncPointsTransaction = useCallback(async (
    type: 'earned' | 'spent',
    amount: number,
    source: string,
    metadata?: any
  ) => {
    const data = {
      type,
      amount,
      source,
      metadata,
      timestamp: Date.now()
    }

    const syncId = await addToQueue('points-transaction', data)
    
    setSyncHistory(prev => [...prev, {
      id: syncId,
      type: 'points-transaction',
      timestamp: Date.now(),
      status: 'pending'
    }])

    return syncId
  }, [addToQueue])

  // Queue user activity
  const syncUserActivity = useCallback(async (
    activityType: string,
    data: any
  ) => {
    const activityData = {
      type: activityType,
      data,
      timestamp: Date.now()
    }

    const syncId = await addToQueue('user-activity', activityData)
    
    setSyncHistory(prev => [...prev, {
      id: syncId,
      type: 'user-activity',
      timestamp: Date.now(),
      status: 'pending'
    }])

    return syncId
  }, [addToQueue])

  return {
    syncAchievementProgress,
    syncChallengeProgress,
    syncPointsTransaction,
    syncUserActivity,
    syncHistory,
    queueStatus,
    isOnline
  }
}

// ===== INSTALL PROMPT HOOK =====

export function useInstallPrompt() {
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const promptRef = useRef<any>(null)

  useEffect(() => {
    // Check if already installed
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                           (window.navigator as any).standalone === true
      setIsInstalled(isStandalone)
    }

    checkInstalled()

    // Listen for install prompt
    const handleBeforeInstallPrompt = (event: any) => {
      event.preventDefault()
      promptRef.current = event
      setIsInstallable(true)
    }

    // Listen for app installed
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      promptRef.current = null
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const install = useCallback(async () => {
    if (!promptRef.current) {
      throw new Error('Install prompt not available')
    }

    try {
      const result = await promptRef.current.prompt()
      promptRef.current = null
      setIsInstallable(false)
      
      return result.outcome
    } catch (error) {
      console.error('Installation failed:', error)
      throw error
    }
  }, [])

  const dismiss = useCallback(() => {
    promptRef.current = null
    setIsInstallable(false)
  }, [])

  return {
    isInstallable,
    isInstalled,
    install,
    dismiss
  }
}

export default {
  usePWA,
  useOffline,
  useCache,
  useGamificationSync,
  useInstallPrompt
}