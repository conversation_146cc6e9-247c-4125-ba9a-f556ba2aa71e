/**
 * Profile Completion Calculation Debug Test
 * 
 * This test verifies that the profile completion calculation logic
 * works correctly with various profile data scenarios.
 */

import { UserProfile } from '@/types/profile'

// Mock profile completion calculation logic (extracted from ProfileCompletionTracker)
const calculateProfileCompletion = (profile: UserProfile | null) => {
  if (!profile) {
    return {
      percentage: 0,
      sections: [],
      completedCount: 0,
      totalCount: 0
    }
  }

  const sections = [
    {
      id: 'basic_info',
      title: 'Basic Information',
      description: 'Add display name and bio',
      completed: !!(profile.displayName && profile.bio && profile.bio.length > 10),
      weight: 20
    },
    {
      id: 'profile_photo',
      title: 'Profile Photo',
      description: 'Upload a profile picture',
      completed: !!(profile.photoURL || profile.avatar),
      weight: 10
    },
    {
      id: 'contact_personal',
      title: 'Contact & Personal',
      description: 'Add name, email, and personal details',
      completed: !!(profile.firstName && profile.lastName && profile.email),
      weight: 20
    },
    {
      id: 'location',
      title: 'Location',
      description: 'Add your location',
      completed: !!profile.location,
      weight: 10
    },
    {
      id: 'social_links',
      title: 'Social Links',
      description: 'Connect your social media profiles',
      completed: !!(profile.website || 
                   profile.socialLinks?.twitter || 
                   profile.socialLinks?.instagram || 
                   profile.socialLinks?.linkedin),
      weight: 15
    },
    {
      id: 'phone_verification',
      title: 'Phone Verification',
      description: 'Verify your phone number',
      completed: !!profile.phoneVerified,
      weight: 10
    },
    {
      id: 'security_setup',
      title: 'Security Setup',
      description: 'Enable two-factor authentication',
      completed: !!profile.mfaEnabled,
      weight: 10
    }
  ]

  const completedSections = sections.filter(section => section.completed)
  const totalWeight = sections.reduce((sum, section) => sum + section.weight, 0)
  const completedWeight = completedSections.reduce((sum, section) => sum + section.weight, 0)
  const percentage = Math.round((completedWeight / totalWeight) * 100)

  return {
    percentage,
    sections,
    completedCount: completedSections.length,
    totalCount: sections.length,
    completedSections: completedSections.map(s => s.id)
  }
}

// Test scenarios
describe('Profile Completion Calculation', () => {
  test('Empty profile should return 0% completion', () => {
    const emptyProfile: UserProfile = {
      uid: 'test-uid',
      email: '<EMAIL>',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = calculateProfileCompletion(emptyProfile)
    console.log('🔍 Empty profile result:', result)
    
    expect(result.percentage).toBe(0)
    expect(result.completedCount).toBe(0)
    expect(result.totalCount).toBe(7)
  })

  test('Basic profile with name and bio should show partial completion', () => {
    const basicProfile: UserProfile = {
      uid: 'test-uid',
      email: '<EMAIL>',
      displayName: 'John Doe',
      bio: 'I love mechanical keyboards and artisan keycaps!',
      firstName: 'John',
      lastName: 'Doe',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = calculateProfileCompletion(basicProfile)
    console.log('🔍 Basic profile result:', result)
    
    // Should have basic_info (20) + contact_personal (20) = 40/95 = 42%
    expect(result.percentage).toBeGreaterThan(0)
    expect(result.completedCount).toBe(2)
    expect(result.completedSections).toContain('basic_info')
    expect(result.completedSections).toContain('contact_personal')
  })

  test('Complete profile should show 100% completion', () => {
    const completeProfile: UserProfile = {
      uid: 'test-uid',
      email: '<EMAIL>',
      displayName: 'John Doe',
      bio: 'I love mechanical keyboards and artisan keycaps! I collect rare artisan keycaps.',
      firstName: 'John',
      lastName: 'Doe',
      photoURL: 'https://example.com/photo.jpg',
      location: 'San Francisco, CA',
      website: 'https://johndoe.com',
      socialLinks: {
        twitter: '@johndoe',
        instagram: '@johndoe_keys'
      },
      phoneVerified: true,
      mfaEnabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = calculateProfileCompletion(completeProfile)
    console.log('🔍 Complete profile result:', result)
    
    expect(result.percentage).toBe(100)
    expect(result.completedCount).toBe(7)
    expect(result.totalCount).toBe(7)
  })

  test('Profile with only social links should show partial completion', () => {
    const socialProfile: UserProfile = {
      uid: 'test-uid',
      email: '<EMAIL>',
      socialLinks: {
        twitter: '@johndoe'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = calculateProfileCompletion(socialProfile)
    console.log('🔍 Social profile result:', result)
    
    // Should have social_links (15) = 15/95 = 16%
    expect(result.percentage).toBeGreaterThan(0)
    expect(result.completedCount).toBe(1)
    expect(result.completedSections).toContain('social_links')
  })
})

// Run the tests manually for debugging
console.log('🧪 Running Profile Completion Tests...')

// Test 1: Empty profile
const emptyProfile: UserProfile = {
  uid: 'test-uid',
  email: '<EMAIL>',
  createdAt: new Date(),
  updatedAt: new Date()
}
console.log('🔍 Test 1 - Empty profile:', calculateProfileCompletion(emptyProfile))

// Test 2: Basic profile
const basicProfile: UserProfile = {
  uid: 'test-uid',
  email: '<EMAIL>',
  displayName: 'John Doe',
  bio: 'I love mechanical keyboards!',
  firstName: 'John',
  lastName: 'Doe',
  createdAt: new Date(),
  updatedAt: new Date()
}
console.log('🔍 Test 2 - Basic profile:', calculateProfileCompletion(basicProfile))

// Test 3: Profile with social links
const socialProfile: UserProfile = {
  uid: 'test-uid',
  email: '<EMAIL>',
  socialLinks: {
    twitter: '@johndoe'
  },
  createdAt: new Date(),
  updatedAt: new Date()
}
console.log('🔍 Test 3 - Social profile:', calculateProfileCompletion(socialProfile))
