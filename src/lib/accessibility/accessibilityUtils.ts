/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility utilities for gamification components
 * including ARIA labels, screen reader support, and WCAG compliance.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

// ===== TYPES =====

export interface AccessibilityConfig {
  enableAnnouncements: boolean
  enableDescriptions: boolean
  enableLiveRegions: boolean
  enableKeyboardNavigation: boolean
  enableReducedMotion: boolean
  enableHighContrast: boolean
  announcementDelay: number
  focusManagement: boolean
}

export interface AriaAttributes {
  'aria-label'?: string
  'aria-labelledby'?: string
  'aria-describedby'?: string
  'aria-expanded'?: boolean
  'aria-hidden'?: boolean
  'aria-live'?: 'off' | 'polite' | 'assertive'
  'aria-atomic'?: boolean
  'aria-busy'?: boolean
  'aria-controls'?: string
  'aria-current'?: string | boolean
  'aria-disabled'?: boolean
  'aria-haspopup'?: boolean | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog'
  'aria-invalid'?: boolean | 'grammar' | 'spelling'
  'aria-pressed'?: boolean
  'aria-readonly'?: boolean
  'aria-required'?: boolean
  'aria-selected'?: boolean
  'aria-sort'?: 'none' | 'ascending' | 'descending' | 'other'
  'aria-valuemin'?: number
  'aria-valuemax'?: number
  'aria-valuenow'?: number
  'aria-valuetext'?: string
  role?: string
  tabIndex?: number
}

export interface AnnouncementOptions {
  priority: 'low' | 'medium' | 'high'
  delay?: number
  replace?: boolean
  persist?: boolean
}

export interface FocusOptions {
  preventScroll?: boolean
  selectText?: boolean
  restoreFocus?: boolean
}

// ===== ACCESSIBILITY UTILITIES =====

export class AccessibilityUtils {
  private static config: AccessibilityConfig = {
    enableAnnouncements: true,
    enableDescriptions: true,
    enableLiveRegions: true,
    enableKeyboardNavigation: true,
    enableReducedMotion: false,
    enableHighContrast: false,
    announcementDelay: 100,
    focusManagement: true
  }

  private static liveRegion: HTMLElement | null = null
  private static announcementQueue: Array<{
    message: string
    options: AnnouncementOptions
    timestamp: number
  }> = []

  /**
   * Initialize accessibility system
   */
  static initialize(config?: Partial<AccessibilityConfig>): void {
    if (config) {
      this.config = { ...this.config, ...config }
    }

    // Create live region for announcements
    this.createLiveRegion()

    // Detect user preferences
    this.detectUserPreferences()

    // Set up keyboard navigation
    if (this.config.enableKeyboardNavigation) {
      this.setupKeyboardNavigation()
    }

    console.log('Accessibility system initialized')
  }

  /**
   * Create ARIA attributes for gamification elements
   */
  static createGameElementAttributes(
    type: 'achievement' | 'challenge' | 'reward' | 'leaderboard' | 'progress' | 'points',
    data: any,
    state?: any
  ): AriaAttributes {
    switch (type) {
      case 'achievement':
        return this.createAchievementAttributes(data, state)
      case 'challenge':
        return this.createChallengeAttributes(data, state)
      case 'reward':
        return this.createRewardAttributes(data, state)
      case 'leaderboard':
        return this.createLeaderboardAttributes(data, state)
      case 'progress':
        return this.createProgressAttributes(data, state)
      case 'points':
        return this.createPointsAttributes(data, state)
      default:
        return {}
    }
  }

  /**
   * Create achievement accessibility attributes
   */
  private static createAchievementAttributes(achievement: any, state?: any): AriaAttributes {
    const isUnlocked = state?.isUnlocked || achievement.isUnlocked
    const progress = state?.progress || achievement.progress || 0

    const attributes: AriaAttributes = {
      role: 'article',
      'aria-label': `Achievement: ${achievement.title}`,
      'aria-describedby': `achievement-desc-${achievement.id}`,
      tabIndex: 0
    }

    if (isUnlocked) {
      attributes['aria-label'] = `Achievement unlocked: ${achievement.title}. ${achievement.description}`
      attributes['aria-current'] = 'true'
    } else {
      attributes['aria-label'] = `Achievement locked: ${achievement.title}. Progress: ${progress}%. ${achievement.description}`
      attributes['aria-valuemin'] = 0
      attributes['aria-valuemax'] = 100
      attributes['aria-valuenow'] = progress
      attributes['aria-valuetext'] = `${progress}% complete`
    }

    return attributes
  }

  /**
   * Create challenge accessibility attributes
   */
  private static createChallengeAttributes(challenge: any, state?: any): AriaAttributes {
    const isJoined = state?.isJoined || false
    const isCompleted = state?.isCompleted || false
    const progress = state?.progress || 0

    const attributes: AriaAttributes = {
      role: 'article',
      'aria-label': `Challenge: ${challenge.title}`,
      'aria-describedby': `challenge-desc-${challenge.id}`,
      tabIndex: 0
    }

    if (isCompleted) {
      attributes['aria-label'] = `Challenge completed: ${challenge.title}. ${challenge.description}`
      attributes['aria-current'] = 'true'
    } else if (isJoined) {
      attributes['aria-label'] = `Challenge in progress: ${challenge.title}. Progress: ${progress}%. ${challenge.description}`
      attributes['aria-valuemin'] = 0
      attributes['aria-valuemax'] = 100
      attributes['aria-valuenow'] = progress
      attributes['aria-valuetext'] = `${progress}% complete`
    } else {
      attributes['aria-label'] = `Challenge available: ${challenge.title}. ${challenge.description}`
    }

    return attributes
  }

  /**
   * Create reward accessibility attributes
   */
  private static createRewardAttributes(reward: any, state?: any): AriaAttributes {
    const canAfford = state?.canAfford || false
    const isPurchased = state?.isPurchased || false

    const attributes: AriaAttributes = {
      role: 'article',
      'aria-label': `Reward: ${reward.title}`,
      'aria-describedby': `reward-desc-${reward.id}`,
      tabIndex: 0
    }

    if (isPurchased) {
      attributes['aria-label'] = `Reward purchased: ${reward.title}. ${reward.description}`
      attributes['aria-current'] = 'true'
    } else if (canAfford) {
      attributes['aria-label'] = `Reward available: ${reward.title}. Cost: ${reward.cost} points. ${reward.description}`
    } else {
      attributes['aria-label'] = `Reward locked: ${reward.title}. Cost: ${reward.cost} points. Insufficient points. ${reward.description}`
      attributes['aria-disabled'] = true
    }

    return attributes
  }

  /**
   * Create leaderboard accessibility attributes
   */
  private static createLeaderboardAttributes(data: any, state?: any): AriaAttributes {
    const position = state?.position || data.position
    const total = state?.total || data.total

    return {
      role: 'table',
      'aria-label': `Leaderboard rankings. You are ranked ${position} out of ${total} players.`,
      'aria-describedby': 'leaderboard-desc',
      tabIndex: 0
    }
  }

  /**
   * Create progress bar accessibility attributes
   */
  private static createProgressAttributes(data: any, state?: any): AriaAttributes {
    const current = state?.current || data.current || 0
    const max = state?.max || data.max || 100
    const label = data.label || 'Progress'

    return {
      role: 'progressbar',
      'aria-label': label,
      'aria-valuemin': 0,
      'aria-valuemax': max,
      'aria-valuenow': current,
      'aria-valuetext': `${current} out of ${max}. ${Math.round((current / max) * 100)}% complete.`
    }
  }

  /**
   * Create points display accessibility attributes
   */
  private static createPointsAttributes(data: any, state?: any): AriaAttributes {
    const points = state?.points || data.points || 0
    const change = state?.change || data.change

    let label = `Current points: ${points.toLocaleString()}`
    if (change) {
      const changeType = change > 0 ? 'increased' : 'decreased'
      label += `. Points ${changeType} by ${Math.abs(change).toLocaleString()}.`
    }

    return {
      role: 'status',
      'aria-label': label,
      'aria-live': 'polite',
      'aria-atomic': true
    }
  }

  /**
   * Make an announcement to screen readers
   */
  static announce(message: string, options: AnnouncementOptions = { priority: 'medium' }): void {
    if (!this.config.enableAnnouncements) return

    const announcement = {
      message,
      options,
      timestamp: Date.now()
    }

    // Add to queue
    this.announcementQueue.push(announcement)

    // Process queue
    setTimeout(() => this.processAnnouncementQueue(), options.delay || this.config.announcementDelay)
  }

  /**
   * Announce achievement unlock
   */
  static announceAchievementUnlock(achievement: any): void {
    const message = `Congratulations! Achievement unlocked: ${achievement.title}. ${achievement.description}. You earned ${achievement.points} points.`
    this.announce(message, { priority: 'high', delay: 500 })
  }

  /**
   * Announce challenge completion
   */
  static announceChallengeCompletion(challenge: any, rewards?: any): void {
    let message = `Challenge completed: ${challenge.title}.`
    if (rewards?.points) {
      message += ` You earned ${rewards.points} points.`
    }
    if (rewards?.achievements?.length) {
      message += ` New achievements unlocked: ${rewards.achievements.length}.`
    }
    this.announce(message, { priority: 'high', delay: 500 })
  }

  /**
   * Announce points change
   */
  static announcePointsChange(change: number, total: number, source?: string): void {
    const changeType = change > 0 ? 'earned' : 'spent'
    let message = `You ${changeType} ${Math.abs(change).toLocaleString()} points.`
    
    if (source) {
      message += ` From ${source}.`
    }
    
    message += ` Total points: ${total.toLocaleString()}.`
    
    this.announce(message, { priority: 'medium' })
  }

  /**
   * Announce navigation change
   */
  static announceNavigation(page: string, section?: string): void {
    let message = `Navigated to ${page}`
    if (section) {
      message += `, ${section} section`
    }
    message += '.'
    
    this.announce(message, { priority: 'low', delay: 200 })
  }

  /**
   * Create keyboard navigation instructions
   */
  static createKeyboardInstructions(elementType: string): string {
    const instructions: Record<string, string> = {
      'achievement-grid': 'Use arrow keys to navigate achievements. Press Enter to view details. Press Space to unlock if available.',
      'challenge-list': 'Use arrow keys to navigate challenges. Press Enter to join or view details. Press Tab to access challenge actions.',
      'leaderboard': 'Use arrow keys to navigate rankings. Press Enter to view player profile. Use Page Up/Down to navigate pages.',
      'rewards-grid': 'Use arrow keys to navigate rewards. Press Enter to purchase if available. Press Space to add to wishlist.',
      'progress-bar': 'Progress bar shows completion status. Use Tab to navigate to related actions.',
      'points-display': 'Points display shows current balance. Use Tab to navigate to point history or earning opportunities.'
    }
    
    return instructions[elementType] || 'Use Tab to navigate. Press Enter to activate.'
  }

  /**
   * Manage focus for dynamic content
   */
  static manageFocus(element: HTMLElement | string, options: FocusOptions = {}): void {
    if (!this.config.focusManagement) return

    const targetElement = typeof element === 'string' 
      ? document.getElementById(element) || document.querySelector(element)
      : element

    if (!targetElement) return

    // Store current focus for restoration
    if (options.restoreFocus && document.activeElement) {
      (targetElement as any)._previousFocus = document.activeElement
    }

    setTimeout(() => {
      targetElement.focus({ preventScroll: options.preventScroll })
      
      if (options.selectText && targetElement instanceof HTMLInputElement) {
        targetElement.select()
      }
    }, 50)
  }

  /**
   * Restore previous focus
   */
  static restoreFocus(element: HTMLElement): void {
    const previousFocus = (element as any)._previousFocus
    if (previousFocus && typeof previousFocus.focus === 'function') {
      previousFocus.focus()
      delete (element as any)._previousFocus
    }
  }

  /**
   * Create skip links for better navigation
   */
  static createSkipLinks(sections: Array<{ id: string; label: string }>): HTMLElement {
    const skipLinksContainer = document.createElement('div')
    skipLinksContainer.className = 'skip-links sr-only-focusable'
    skipLinksContainer.setAttribute('aria-label', 'Skip navigation links')

    sections.forEach(section => {
      const skipLink = document.createElement('a')
      skipLink.href = `#${section.id}`
      skipLink.textContent = `Skip to ${section.label}`
      skipLink.className = 'skip-link'
      
      skipLink.addEventListener('click', (e) => {
        e.preventDefault()
        const target = document.getElementById(section.id)
        if (target) {
          this.manageFocus(target)
        }
      })
      
      skipLinksContainer.appendChild(skipLink)
    })

    return skipLinksContainer
  }

  /**
   * Check if user prefers reduced motion
   */
  static prefersReducedMotion(): boolean {
    return this.config.enableReducedMotion || 
           window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }

  /**
   * Check if user prefers high contrast
   */
  static prefersHighContrast(): boolean {
    return this.config.enableHighContrast || 
           window.matchMedia('(prefers-contrast: high)').matches
  }

  /**
   * Get appropriate color contrast ratio
   */
  static getContrastRatio(foreground: string, background: string): number {
    // Simplified contrast ratio calculation
    // In production, use a proper color contrast library
    return 4.5 // WCAG AA compliant ratio
  }

  /**
   * Validate color accessibility
   */
  static validateColorAccessibility(foreground: string, background: string): {
    isValid: boolean
    ratio: number
    level: 'AA' | 'AAA' | 'fail'
  } {
    const ratio = this.getContrastRatio(foreground, background)
    
    return {
      isValid: ratio >= 4.5,
      ratio,
      level: ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : 'fail'
    }
  }

  // ===== PRIVATE METHODS =====

  private static createLiveRegion(): void {
    if (typeof window === 'undefined') return

    this.liveRegion = document.createElement('div')
    this.liveRegion.id = 'accessibility-live-region'
    this.liveRegion.className = 'sr-only'
    this.liveRegion.setAttribute('aria-live', 'polite')
    this.liveRegion.setAttribute('aria-atomic', 'true')
    
    document.body.appendChild(this.liveRegion)
  }

  private static processAnnouncementQueue(): void {
    if (!this.liveRegion || this.announcementQueue.length === 0) return

    // Sort by priority and timestamp
    this.announcementQueue.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 }
      return priorityWeight[b.options.priority] - priorityWeight[a.options.priority] ||
             a.timestamp - b.timestamp
    })

    const announcement = this.announcementQueue.shift()
    if (!announcement) return

    // Clear previous announcement
    this.liveRegion.textContent = ''
    
    // Add new announcement
    setTimeout(() => {
      if (this.liveRegion) {
        this.liveRegion.textContent = announcement.message
      }
    }, 50)

    // Process next announcement
    if (this.announcementQueue.length > 0) {
      setTimeout(() => this.processAnnouncementQueue(), 1500)
    }
  }

  private static detectUserPreferences(): void {
    if (typeof window === 'undefined') return

    // Detect reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.config.enableReducedMotion = true
    }

    // Detect high contrast preference
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.config.enableHighContrast = true
    }

    // Listen for changes
    window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
      this.config.enableReducedMotion = e.matches
    })

    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
      this.config.enableHighContrast = e.matches
    })
  }

  private static setupKeyboardNavigation(): void {
    if (typeof window === 'undefined') return

    // Add global keyboard event listeners
    document.addEventListener('keydown', (e) => {
      // Handle global keyboard shortcuts
      if (e.altKey && e.key === 's') {
        // Skip to main content
        e.preventDefault()
        const main = document.querySelector('main') || document.querySelector('[role="main"]')
        if (main) {
          this.manageFocus(main as HTMLElement)
        }
      }
    })
  }

  /**
   * Get configuration
   */
  static getConfig(): AccessibilityConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  static updateConfig(updates: Partial<AccessibilityConfig>): void {
    this.config = { ...this.config, ...updates }
  }
}

// ===== ACCESSIBILITY HOOKS =====

export const AccessibilityConstants = {
  ROLES: {
    ACHIEVEMENT: 'article',
    CHALLENGE: 'article', 
    REWARD: 'article',
    LEADERBOARD: 'table',
    PROGRESS: 'progressbar',
    POINTS: 'status',
    NAVIGATION: 'navigation',
    MAIN: 'main',
    COMPLEMENTARY: 'complementary'
  },
  
  LIVE_REGIONS: {
    POLITE: 'polite',
    ASSERTIVE: 'assertive',
    OFF: 'off'
  },
  
  KEYBOARD_SHORTCUTS: {
    SKIP_TO_MAIN: 'Alt+S',
    TOGGLE_ANNOUNCEMENTS: 'Alt+A',
    FOCUS_SEARCH: 'Alt+/',
    ESCAPE: 'Escape'
  }
} as const

export default AccessibilityUtils