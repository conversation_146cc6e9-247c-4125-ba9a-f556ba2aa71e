/**
 * Address Management Service
 * Handles CRUD operations for user addresses in the profile system
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore'
import { db } from './firebase'
import { UserAddress, AddressType } from '@/types/profile'

const ADDRESSES_COLLECTION = 'userAddresses'

/**
 * Get all addresses for a user
 */
export const getUserAddresses = async (userId: string): Promise<UserAddress[]> => {
  try {
    // First try with orderBy, if it fails (due to missing index), try without
    let q
    try {
      q = query(
        collection(db, ADDRESSES_COLLECTION),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      )
    } catch (indexError) {
      console.warn('Firestore index not available, querying without orderBy:', indexError)
      q = query(
        collection(db, ADDRESSES_COLLECTION),
        where('userId', '==', userId)
      )
    }

    const snapshot = await getDocs(q)
    const addresses = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as UserAddress[]

    // Sort manually if we couldn't use orderBy
    return addresses.sort((a, b) => {
      const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() :
                   a.createdAt?.toDate?.()?.getTime?.() || 0
      const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() :
                   b.createdAt?.toDate?.()?.getTime?.() || 0
      return bTime - aTime // desc order
    })
  } catch (error) {
    console.error('Error fetching user addresses:', error)
    // Return empty array instead of throwing to prevent app crashes
    return []
  }
}

/**
 * Create a new address
 */
export const createUserAddress = async (
  userId: string,
  addressData: Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, ADDRESSES_COLLECTION), {
      ...addressData,
      userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    })
    
    // If this is set as default, update other addresses
    if (addressData.isDefault) {
      await setDefaultAddress(userId, docRef.id)
    }
    
    return docRef.id
  } catch (error) {
    console.error('Error creating address:', error)
    throw new Error('Failed to create address')
  }
}

/**
 * Update an existing address
 */
export const updateUserAddress = async (
  addressId: string,
  addressData: Partial<Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const addressRef = doc(db, ADDRESSES_COLLECTION, addressId)
    
    await updateDoc(addressRef, {
      ...addressData,
      updatedAt: serverTimestamp()
    })
    
    // If this is set as default, update other addresses
    if (addressData.isDefault) {
      const addressDoc = await getDoc(addressRef)
      if (addressDoc.exists()) {
        const address = addressDoc.data() as UserAddress
        await setDefaultAddress(address.userId, addressId)
      }
    }
  } catch (error) {
    console.error('Error updating address:', error)
    throw new Error('Failed to update address')
  }
}

/**
 * Delete an address
 */
export const deleteUserAddress = async (addressId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, ADDRESSES_COLLECTION, addressId))
  } catch (error) {
    console.error('Error deleting address:', error)
    throw new Error('Failed to delete address')
  }
}

/**
 * Set an address as the default (and unset others)
 */
export const setDefaultAddress = async (userId: string, addressId: string): Promise<void> => {
  try {
    const batch = writeBatch(db)
    
    // Get all user addresses
    const q = query(
      collection(db, ADDRESSES_COLLECTION),
      where('userId', '==', userId)
    )
    const snapshot = await getDocs(q)
    
    // Update all addresses to not be default
    snapshot.docs.forEach(doc => {
      batch.update(doc.ref, { 
        isDefault: doc.id === addressId,
        updatedAt: serverTimestamp()
      })
    })
    
    await batch.commit()
  } catch (error) {
    console.error('Error setting default address:', error)
    throw new Error('Failed to set default address')
  }
}

/**
 * Get the default address for a user
 */
export const getDefaultAddress = async (userId: string): Promise<UserAddress | null> => {
  try {
    const q = query(
      collection(db, ADDRESSES_COLLECTION),
      where('userId', '==', userId),
      where('isDefault', '==', true)
    )
    
    const snapshot = await getDocs(q)
    if (snapshot.empty) return null
    
    const doc = snapshot.docs[0]
    return {
      id: doc.id,
      ...doc.data()
    } as UserAddress
  } catch (error) {
    console.error('Error fetching default address:', error)
    throw new Error('Failed to fetch default address')
  }
}

/**
 * Get addresses by type
 */
export const getAddressesByType = async (
  userId: string,
  type: AddressType
): Promise<UserAddress[]> => {
  try {
    const q = query(
      collection(db, ADDRESSES_COLLECTION),
      where('userId', '==', userId),
      where('type', 'in', [type, 'both']),
      orderBy('createdAt', 'desc')
    )
    
    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as UserAddress[]
  } catch (error) {
    console.error('Error fetching addresses by type:', error)
    throw new Error('Failed to fetch addresses by type')
  }
}

/**
 * Validate address data
 */
export const validateAddress = (address: Partial<UserAddress>): string[] => {
  const errors: string[] = []
  
  if (!address.fullName?.trim()) {
    errors.push('Full name is required')
  }
  
  if (!address.addressLine1?.trim()) {
    errors.push('Address line 1 is required')
  }
  
  if (!address.city?.trim()) {
    errors.push('City is required')
  }
  
  if (!address.state?.trim()) {
    errors.push('State/Province is required')
  }
  
  if (!address.postalCode?.trim()) {
    errors.push('Postal/ZIP code is required')
  }
  
  if (!address.country?.trim()) {
    errors.push('Country is required')
  }
  
  if (!address.type) {
    errors.push('Address type is required')
  }
  
  if (!address.label?.trim()) {
    errors.push('Address label is required')
  }
  
  // Validate phone number format if provided
  if (address.phone && address.phone.trim()) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    if (!phoneRegex.test(address.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.push('Please enter a valid phone number')
    }
  }
  
  // Validate postal code format (basic validation)
  if (address.postalCode && address.postalCode.trim()) {
    const postalCodeRegex = /^[A-Za-z0-9\s\-]{3,10}$/
    if (!postalCodeRegex.test(address.postalCode)) {
      errors.push('Please enter a valid postal/ZIP code')
    }
  }
  
  return errors
}

/**
 * Format address for display
 */
export const formatAddressDisplay = (address: UserAddress): string => {
  const parts = [
    address.addressLine1,
    address.addressLine2,
    `${address.city}, ${address.state} ${address.postalCode}`,
    address.country
  ].filter(Boolean)
  
  return parts.join('\n')
}

/**
 * Format address for single line display
 */
export const formatAddressSingleLine = (address: UserAddress): string => {
  const parts = [
    address.addressLine1,
    address.addressLine2,
    address.city,
    address.state,
    address.postalCode,
    address.country
  ].filter(Boolean)
  
  return parts.join(', ')
}
