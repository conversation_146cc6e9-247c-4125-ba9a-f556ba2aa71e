/**
 * Animation Configuration
 * 
 * Comprehensive animation system with micro-interactions, 
 * gamification-specific animations, and accessibility considerations.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { Variants } from 'framer-motion'

// ===== ANIMATION TYPES =====

export type AnimationDuration = 'instant' | 'fast' | 'normal' | 'slow' | 'slower'
export type AnimationEasing = 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bounce' | 'spring'
export type AnimationDirection = 'up' | 'down' | 'left' | 'right' | 'center' | 'scale'

export interface AnimationConfig {
  duration: number
  ease: number[] | string
  delay?: number
  repeat?: number
  repeatType?: 'loop' | 'reverse' | 'mirror'
  respectsReducedMotion?: boolean
}

export interface MicroInteractionConfig {
  hover?: AnimationConfig
  tap?: AnimationConfig
  focus?: AnimationConfig
  disabled?: AnimationConfig
}

export interface GamificationAnimationConfig {
  achievement?: {
    unlock: AnimationConfig
    progress: AnimationConfig
    hover: AnimationConfig
  }
  points?: {
    gain: AnimationConfig
    loss: AnimationConfig
    milestone: AnimationConfig
  }
  tier?: {
    promotion: AnimationConfig
    progress: AnimationConfig
  }
  challenge?: {
    complete: AnimationConfig
    join: AnimationConfig
    progress: AnimationConfig
  }
}

// ===== DURATION PRESETS =====

export const durations: Record<AnimationDuration, number> = {
  instant: 0.1,
  fast: 0.2,
  normal: 0.3,
  slow: 0.5,
  slower: 0.8
}

// ===== EASING PRESETS =====

export const easings: Record<AnimationEasing, number[] | string> = {
  linear: 'linear',
  ease: [0.25, 0.1, 0.25, 1],
  'ease-in': [0.42, 0, 1, 1],
  'ease-out': [0, 0, 0.58, 1],
  'ease-in-out': [0.42, 0, 0.58, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  spring: [0.175, 0.885, 0.32, 1.275]
}

// ===== CORE ANIMATION VARIANTS =====

export const fadeVariants: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: { 
    opacity: 1,
    transition: {
      duration: durations.normal,
      ease: easings.ease
    }
  },
  exit: { 
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

export const slideVariants: Variants = {
  hiddenLeft: { 
    x: -50, 
    opacity: 0 
  },
  hiddenRight: { 
    x: 50, 
    opacity: 0 
  },
  hiddenUp: { 
    y: -50, 
    opacity: 0 
  },
  hiddenDown: { 
    y: 50, 
    opacity: 0 
  },
  visible: { 
    x: 0, 
    y: 0, 
    opacity: 1,
    transition: {
      duration: durations.normal,
      ease: easings.ease
    }
  },
  exit: { 
    y: -20, 
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

export const scaleVariants: Variants = {
  hidden: { 
    scale: 0.8, 
    opacity: 0 
  },
  visible: { 
    scale: 1, 
    opacity: 1,
    transition: {
      duration: durations.normal,
      ease: easings.spring
    }
  },
  exit: { 
    scale: 0.9, 
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

export const rotateVariants: Variants = {
  hidden: { 
    rotate: -10, 
    opacity: 0 
  },
  visible: { 
    rotate: 0, 
    opacity: 1,
    transition: {
      duration: durations.normal,
      ease: easings.spring
    }
  },
  exit: { 
    rotate: 10, 
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

// ===== MICRO-INTERACTION VARIANTS =====

export const buttonVariants: Variants = {
  idle: { 
    scale: 1,
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
  },
  hover: { 
    scale: 1.02,
    boxShadow: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  },
  tap: { 
    scale: 0.98,
    transition: {
      duration: durations.instant,
      ease: easings.ease
    }
  },
  disabled: { 
    scale: 1,
    opacity: 0.6,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  },
  loading: {
    scale: 1,
    opacity: 0.8,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

export const cardVariants: Variants = {
  idle: { 
    scale: 1,
    rotateY: 0,
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
  },
  hover: { 
    scale: 1.03,
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.16), 0 4px 8px rgba(0, 0, 0, 0.23)',
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  },
  tap: { 
    scale: 0.98,
    transition: {
      duration: durations.instant,
      ease: easings.ease
    }
  },
  flip: {
    rotateY: 180,
    transition: {
      duration: durations.slow,
      ease: easings.ease
    }
  }
}

export const inputVariants: Variants = {
  idle: { 
    borderColor: 'rgba(209, 213, 219, 1)',
    boxShadow: '0 0 0 0 rgba(168, 85, 247, 0)'
  },
  focus: { 
    borderColor: 'rgba(168, 85, 247, 1)',
    boxShadow: '0 0 0 3px rgba(168, 85, 247, 0.1)',
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  },
  error: { 
    borderColor: 'rgba(239, 68, 68, 1)',
    boxShadow: '0 0 0 3px rgba(239, 68, 68, 0.1)',
    x: [0, -5, 5, -5, 5, 0],
    transition: {
      duration: durations.normal,
      ease: easings.ease
    }
  },
  success: { 
    borderColor: 'rgba(16, 185, 129, 1)',
    boxShadow: '0 0 0 3px rgba(16, 185, 129, 0.1)',
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

// ===== GAMIFICATION ANIMATION VARIANTS =====

export const achievementVariants: Variants = {
  locked: { 
    scale: 0.95,
    opacity: 0.6,
    filter: 'grayscale(100%)'
  },
  unlocking: {
    scale: [0.95, 1.1, 1],
    opacity: [0.6, 1, 1],
    filter: ['grayscale(100%)', 'grayscale(0%)', 'grayscale(0%)'],
    boxShadow: [
      '0 0 0 rgba(168, 85, 247, 0)',
      '0 0 20px rgba(168, 85, 247, 0.5)',
      '0 0 0 rgba(168, 85, 247, 0)'
    ],
    transition: {
      duration: durations.slower,
      ease: easings.bounce,
      times: [0, 0.6, 1]
    }
  },
  unlocked: { 
    scale: 1,
    opacity: 1,
    filter: 'grayscale(0%)',
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  },
  hover: {
    scale: 1.05,
    y: -2,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

export const pointsVariants: Variants = {
  idle: { 
    scale: 1,
    y: 0
  },
  gain: {
    scale: [1, 1.2, 1],
    y: [0, -10, 0],
    color: ['currentColor', '#10b981', 'currentColor'],
    transition: {
      duration: durations.normal,
      ease: easings.bounce
    }
  },
  loss: {
    scale: [1, 0.9, 1],
    x: [0, -5, 5, -5, 5, 0],
    color: ['currentColor', '#ef4444', 'currentColor'],
    transition: {
      duration: durations.normal,
      ease: easings.ease
    }
  },
  milestone: {
    scale: [1, 1.3, 1.1, 1],
    rotate: [0, 5, -5, 0],
    boxShadow: [
      '0 0 0 rgba(168, 85, 247, 0)',
      '0 0 20px rgba(168, 85, 247, 0.6)',
      '0 0 30px rgba(168, 85, 247, 0.4)',
      '0 0 0 rgba(168, 85, 247, 0)'
    ],
    transition: {
      duration: durations.slower,
      ease: easings.spring
    }
  }
}

export const tierVariants: Variants = {
  current: { 
    scale: 1,
    boxShadow: '0 0 0 rgba(168, 85, 247, 0)'
  },
  promotion: {
    scale: [1, 1.1, 1.05, 1],
    boxShadow: [
      '0 0 0 rgba(255, 215, 0, 0)',
      '0 0 30px rgba(255, 215, 0, 0.8)',
      '0 0 20px rgba(255, 215, 0, 0.6)',
      '0 0 0 rgba(255, 215, 0, 0)'
    ],
    transition: {
      duration: durations.slower,
      ease: easings.bounce,
      repeat: 2,
      repeatType: 'reverse'
    }
  },
  locked: { 
    scale: 0.9,
    opacity: 0.4,
    filter: 'grayscale(100%)'
  }
}

export const challengeVariants: Variants = {
  available: { 
    scale: 1,
    opacity: 1,
    borderColor: 'rgba(209, 213, 219, 1)'
  },
  joining: {
    scale: [1, 1.02, 1],
    borderColor: [
      'rgba(209, 213, 219, 1)',
      'rgba(168, 85, 247, 1)',
      'rgba(168, 85, 247, 1)'
    ],
    transition: {
      duration: durations.normal,
      ease: easings.ease
    }
  },
  active: { 
    scale: 1,
    borderColor: 'rgba(168, 85, 247, 1)',
    boxShadow: '0 0 0 2px rgba(168, 85, 247, 0.2)'
  },
  completing: {
    scale: [1, 1.05, 1.02, 1],
    rotate: [0, 1, -1, 0],
    boxShadow: [
      '0 0 0 2px rgba(168, 85, 247, 0.2)',
      '0 0 20px rgba(16, 185, 129, 0.6)',
      '0 0 15px rgba(16, 185, 129, 0.4)',
      '0 0 0 2px rgba(16, 185, 129, 0.3)'
    ],
    transition: {
      duration: durations.slower,
      ease: easings.spring
    }
  },
  completed: { 
    scale: 1,
    borderColor: 'rgba(16, 185, 129, 1)',
    boxShadow: '0 0 0 2px rgba(16, 185, 129, 0.3)'
  }
}

export const progressBarVariants: Variants = {
  empty: { 
    scaleX: 0,
    originX: 0
  },
  filling: {
    scaleX: 1,
    transition: {
      duration: durations.slow,
      ease: easings.ease
    }
  },
  complete: {
    scaleX: 1,
    backgroundColor: '#10b981',
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  },
  pulse: {
    opacity: [1, 0.7, 1],
    transition: {
      duration: durations.normal,
      ease: easings.ease,
      repeat: Infinity,
      repeatType: 'reverse'
    }
  }
}

// ===== NOTIFICATION VARIANTS =====

export const notificationVariants: Variants = {
  hidden: { 
    x: 300,
    opacity: 0,
    scale: 0.8
  },
  visible: { 
    x: 0,
    opacity: 1,
    scale: 1,
    transition: {
      duration: durations.normal,
      ease: easings.spring
    }
  },
  exit: { 
    x: 300,
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

export const toastVariants: Variants = {
  hidden: { 
    y: -100,
    opacity: 0
  },
  visible: { 
    y: 0,
    opacity: 1,
    transition: {
      duration: durations.normal,
      ease: easings.spring
    }
  },
  exit: { 
    y: -100,
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

// ===== LIST ANIMATION VARIANTS =====

export const listVariants: Variants = {
  hidden: {
    opacity: 0
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const listItemVariants: Variants = {
  hidden: { 
    y: 20, 
    opacity: 0 
  },
  visible: { 
    y: 0, 
    opacity: 1,
    transition: {
      duration: durations.normal,
      ease: easings.ease
    }
  }
}

// ===== MODAL VARIANTS =====

export const modalVariants: Variants = {
  hidden: { 
    opacity: 0,
    scale: 0.8,
    y: 50
  },
  visible: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: durations.normal,
      ease: easings.spring
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.8,
    y: 50,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

export const backdropVariants: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: { 
    opacity: 1,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  },
  exit: { 
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.ease
    }
  }
}

// ===== LOADING VARIANTS =====

export const spinnerVariants: Variants = {
  spin: {
    rotate: 360,
    transition: {
      duration: 1,
      ease: 'linear',
      repeat: Infinity
    }
  }
}

export const pulseVariants: Variants = {
  pulse: {
    scale: [1, 1.05, 1],
    opacity: [0.7, 1, 0.7],
    transition: {
      duration: durations.slow,
      ease: easings.ease,
      repeat: Infinity,
      repeatType: 'reverse'
    }
  }
}

// ===== UTILITY FUNCTIONS =====

export function createStaggeredAnimation(
  baseVariant: Variants,
  staggerDelay: number = 0.1,
  delayChildren: number = 0
) {
  const visibleVariant = baseVariant.visible
  const existingTransition = visibleVariant && typeof visibleVariant === 'object' && 'transition' in visibleVariant 
    ? visibleVariant.transition 
    : {}
  
  return {
    ...baseVariant,
    visible: {
      ...baseVariant.visible,
      transition: {
        ...existingTransition,
        staggerChildren: staggerDelay,
        delayChildren
      }
    }
  }
}

export function createReducedMotionVariant(variants: Variants): Variants {
  const reducedVariants: Variants = {}
  
  Object.keys(variants).forEach(key => {
    const variant = variants[key]
    if (typeof variant === 'object' && variant !== null) {
      reducedVariants[key] = {
        ...variant,
        transition: {
          duration: 0.01,
          ease: 'linear'
        }
      }
    }
  })
  
  return reducedVariants
}

export function getAnimationConfig(
  duration: AnimationDuration = 'normal',
  easing: AnimationEasing = 'ease',
  delay: number = 0
): AnimationConfig {
  return {
    duration: durations[duration],
    ease: easings[easing],
    delay,
    respectsReducedMotion: true
  }
}

export default {
  durations,
  easings,
  fadeVariants,
  slideVariants,
  scaleVariants,
  rotateVariants,
  buttonVariants,
  cardVariants,
  inputVariants,
  achievementVariants,
  pointsVariants,
  tierVariants,
  challengeVariants,
  progressBarVariants,
  notificationVariants,
  toastVariants,
  listVariants,
  listItemVariants,
  modalVariants,
  backdropVariants,
  spinnerVariants,
  pulseVariants,
  createStaggeredAnimation,
  createReducedMotionVariant,
  getAnimationConfig
}