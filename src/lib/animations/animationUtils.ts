/**
 * Animation Utilities
 * 
 * Collection of animation utilities, helpers, and patterns
 * for consistent micro-interactions throughout the application.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { Variants, TargetAndTransition } from 'framer-motion'

// ===== GESTURE HANDLERS =====

/**
 * Standard button gesture handlers with reduced motion support
 */
export function createButtonGestures(options: {
  scaleHover?: number
  scaleTap?: number
  respectReducedMotion?: boolean
} = {}) {
  const {
    scaleHover = 1.02,
    scaleTap = 0.98,
    respectReducedMotion = true
  } = options

  if (respectReducedMotion && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    return {
      whileHover: { opacity: 0.9 },
      whileTap: { opacity: 0.8 }
    }
  }

  return {
    whileHover: { 
      scale: scaleHover,
      transition: { duration: 0.2, ease: 'easeOut' }
    },
    whileTap: { 
      scale: scaleTap,
      transition: { duration: 0.1, ease: 'easeOut' }
    }
  }
}

/**
 * Card hover gestures with elevation effect
 */
export function createCardGestures(options: {
  hoverScale?: number
  hoverY?: number
  tapScale?: number
  respectReducedMotion?: boolean
} = {}) {
  const {
    hoverScale = 1.02,
    hoverY = -4,
    tapScale = 0.98,
    respectReducedMotion = true
  } = options

  if (respectReducedMotion && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    return {
      whileHover: { boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)' },
      whileTap: { boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }
    }
  }

  return {
    whileHover: { 
      scale: hoverScale,
      y: hoverY,
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
      transition: { duration: 0.3, ease: 'easeOut' }
    },
    whileTap: { 
      scale: tapScale,
      y: 0,
      transition: { duration: 0.1, ease: 'easeOut' }
    }
  }
}

/**
 * Input focus gestures with glow effect
 */
export function createInputGestures(options: {
  focusScale?: number
  errorShake?: boolean
  respectReducedMotion?: boolean
} = {}) {
  const {
    focusScale = 1.01,
    errorShake = true,
    respectReducedMotion = true
  } = options

  const gestures: any = {
    whileFocus: {
      scale: focusScale,
      boxShadow: '0 0 0 3px rgba(168, 85, 247, 0.1)',
      transition: { duration: 0.2, ease: 'easeOut' }
    }
  }

  if (errorShake && !respectReducedMotion) {
    gestures.animate = (isError: boolean) => isError ? {
      x: [0, -5, 5, -5, 5, 0],
      transition: { duration: 0.4, ease: 'easeInOut' }
    } : {}
  }

  return gestures
}

// ===== STAGGER ANIMATIONS =====

/**
 * Create staggered list animation
 */
export function createStaggeredList(options: {
  staggerDelay?: number
  childDelay?: number
  direction?: 'up' | 'down' | 'left' | 'right'
} = {}): Variants {
  const {
    staggerDelay = 0.1,
    childDelay = 0.1,
    direction = 'up'
  } = options

  const getChildInitial = () => {
    switch (direction) {
      case 'up': return { y: 20, opacity: 0 }
      case 'down': return { y: -20, opacity: 0 }
      case 'left': return { x: 20, opacity: 0 }
      case 'right': return { x: -20, opacity: 0 }
      default: return { y: 20, opacity: 0 }
    }
  }

  return {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: childDelay
      }
    },
    itemHidden: getChildInitial(),
    itemVisible: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
        ease: 'easeOut'
      }
    }
  }
}

/**
 * Create grid stagger animation
 */
export function createStaggeredGrid(options: {
  columns?: number
  staggerDelay?: number
  childDelay?: number
} = {}) {
  const {
    columns = 3,
    staggerDelay = 0.05,
    childDelay = 0.1
  } = options

  return {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: childDelay
      }
    },
    item: {
      hidden: { 
        scale: 0.8, 
        opacity: 0,
        y: 20
      },
      visible: (index: number) => ({
        scale: 1,
        opacity: 1,
        y: 0,
        transition: {
          delay: Math.floor(index / columns) * 0.1 + (index % columns) * 0.05,
          duration: 0.4,
          ease: 'easeOut'
        }
      })
    }
  }
}

// ===== LOADING ANIMATIONS =====

/**
 * Skeleton loading animation
 */
export function createSkeletonAnimation(): Variants {
  return {
    loading: {
      opacity: [0.6, 1, 0.6],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  }
}

/**
 * Pulse loading animation
 */
export function createPulseAnimation(options: {
  scale?: [number, number]
  duration?: number
} = {}): Variants {
  const { scale = [1, 1.05], duration = 1 } = options

  return {
    pulse: {
      scale,
      transition: {
        duration,
        repeat: Infinity,
        repeatType: 'reverse',
        ease: 'easeInOut'
      }
    }
  }
}

/**
 * Spinner loading animation
 */
export function createSpinnerAnimation(duration = 1): Variants {
  return {
    spin: {
      rotate: 360,
      transition: {
        duration,
        repeat: Infinity,
        ease: 'linear'
      }
    }
  }
}

// ===== GAMIFICATION ANIMATIONS =====

/**
 * Achievement unlock sequence
 */
export function createAchievementUnlockSequence(): Variants {
  return {
    locked: {
      scale: 0.95,
      opacity: 0.6,
      filter: 'grayscale(100%) brightness(0.8)'
    },
    unlocking: {
      scale: [0.95, 1.1, 1.05, 1],
      opacity: [0.6, 1, 1, 1],
      filter: [
        'grayscale(100%) brightness(0.8)',
        'grayscale(0%) brightness(1.2)',
        'grayscale(0%) brightness(1.1)',
        'grayscale(0%) brightness(1)'
      ],
      boxShadow: [
        '0 0 0 rgba(168, 85, 247, 0)',
        '0 0 20px rgba(168, 85, 247, 0.6)',
        '0 0 15px rgba(168, 85, 247, 0.4)',
        '0 0 0 rgba(168, 85, 247, 0)'
      ],
      transition: {
        duration: 1.2,
        ease: [0.68, -0.55, 0.265, 1.55],
        times: [0, 0.4, 0.7, 1]
      }
    },
    unlocked: {
      scale: 1,
      opacity: 1,
      filter: 'grayscale(0%) brightness(1)'
    }
  }
}

/**
 * Points gain animation
 */
export function createPointsGainAnimation(): Variants {
  return {
    idle: { 
      scale: 1,
      y: 0
    },
    gain: {
      scale: [1, 1.2, 1.1, 1],
      y: [0, -5, -3, 0],
      color: ['currentColor', '#10b981', '#10b981', 'currentColor'],
      transition: {
        duration: 0.8,
        ease: 'easeOut',
        times: [0, 0.3, 0.7, 1]
      }
    }
  }
}

/**
 * Tier promotion glow animation
 */
export function createTierPromotionAnimation(): Variants {
  return {
    idle: {
      scale: 1,
      boxShadow: '0 0 0 rgba(255, 215, 0, 0)'
    },
    promoting: {
      scale: [1, 1.05, 1.02, 1],
      boxShadow: [
        '0 0 0 rgba(255, 215, 0, 0)',
        '0 0 30px rgba(255, 215, 0, 0.8)',
        '0 0 20px rgba(255, 215, 0, 0.6)',
        '0 0 10px rgba(255, 215, 0, 0.3)'
      ],
      transition: {
        duration: 2,
        ease: 'easeInOut',
        times: [0, 0.3, 0.7, 1]
      }
    },
    promoted: {
      scale: 1,
      boxShadow: '0 0 10px rgba(255, 215, 0, 0.3)'
    }
  }
}

/**
 * Progress bar fill animation
 */
export function createProgressFillAnimation(options: {
  duration?: number
  ease?: string
} = {}): Variants {
  const { duration = 0.8, ease = 'easeOut' } = options

  return {
    empty: {
      scaleX: 0,
      transformOrigin: 'left'
    },
    filling: (progress: number) => ({
      scaleX: progress / 100,
      transition: {
        duration,
        ease
      }
    }),
    complete: {
      scaleX: 1,
      backgroundColor: '#10b981',
      transition: {
        backgroundColor: {
          duration: 0.3,
          delay: 0.5
        }
      }
    }
  }
}

// ===== MODAL ANIMATIONS =====

/**
 * Modal slide in animation
 */
export function createModalSlideAnimation(direction: 'up' | 'down' | 'left' | 'right' = 'up'): Variants {
  const getTransform = () => {
    switch (direction) {
      case 'up': return { y: 50 }
      case 'down': return { y: -50 }
      case 'left': return { x: 50 }
      case 'right': return { x: -50 }
    }
  }

  return {
    hidden: {
      opacity: 0,
      scale: 0.9,
      ...getTransform()
    },
    visible: {
      opacity: 1,
      scale: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      ...getTransform(),
      transition: {
        duration: 0.2,
        ease: 'easeIn'
      }
    }
  }
}

/**
 * Backdrop fade animation
 */
export function createBackdropAnimation(): Variants {
  return {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.2, ease: 'easeOut' }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.2, ease: 'easeIn' }
    }
  }
}

// ===== NOTIFICATION ANIMATIONS =====

/**
 * Toast notification slide animation
 */
export function createToastAnimation(position: 'top' | 'bottom' | 'left' | 'right' = 'right'): Variants {
  const getTransform = () => {
    switch (position) {
      case 'top': return { y: -100 }
      case 'bottom': return { y: 100 }
      case 'left': return { x: -300 }
      case 'right': return { x: 300 }
    }
  }

  return {
    hidden: {
      opacity: 0,
      scale: 0.9,
      ...getTransform()
    },
    visible: {
      opacity: 1,
      scale: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.3,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      ...getTransform(),
      transition: {
        duration: 0.2,
        ease: 'easeIn'
      }
    }
  }
}

// ===== UTILITY FUNCTIONS =====

/**
 * Create reduced motion variant of any animation
 */
export function withReducedMotion<T extends Variants>(variants: T): T {
  if (typeof window !== 'undefined' && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    const reducedVariants = {} as T
    
    Object.entries(variants).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        reducedVariants[key as keyof T] = {
          ...value,
          transition: { duration: 0.01 }
        } as T[keyof T]
      }
    })
    
    return reducedVariants
  }
  
  return variants
}

/**
 * Create animation with delay
 */
export function withDelay(animation: TargetAndTransition, delay: number): TargetAndTransition {
  return {
    ...animation,
    transition: {
      ...animation.transition,
      delay
    }
  }
}

/**
 * Create spring animation configuration
 */
export function createSpringConfig(options: {
  stiffness?: number
  damping?: number
  mass?: number
} = {}) {
  const { stiffness = 300, damping = 30, mass = 1 } = options
  
  return {
    type: 'spring',
    stiffness,
    damping,
    mass
  }
}

/**
 * Create custom easing function
 */
export function createCustomEasing(points: [number, number, number, number]) {
  return points
}

/**
 * Animation timing presets
 */
export const animationTimings = {
  instant: 0.1,
  fast: 0.2,
  normal: 0.3,
  slow: 0.5,
  slower: 0.8
}

/**
 * Easing presets
 */
export const easingPresets = {
  linear: 'linear',
  ease: [0.25, 0.1, 0.25, 1],
  easeIn: [0.42, 0, 1, 1],
  easeOut: [0, 0, 0.58, 1],
  easeInOut: [0.42, 0, 0.58, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  spring: [0.175, 0.885, 0.32, 1.275]
}

export default {
  createButtonGestures,
  createCardGestures,
  createInputGestures,
  createStaggeredList,
  createStaggeredGrid,
  createSkeletonAnimation,
  createPulseAnimation,
  createSpinnerAnimation,
  createAchievementUnlockSequence,
  createPointsGainAnimation,
  createTierPromotionAnimation,
  createProgressFillAnimation,
  createModalSlideAnimation,
  createBackdropAnimation,
  createToastAnimation,
  withReducedMotion,
  withDelay,
  createSpringConfig,
  createCustomEasing,
  animationTimings,
  easingPresets
}