/**
 * CSS Animation Utilities
 * 
 * High-performance CSS-based animations to replace simple Framer Motion usage
 * Optimized for 60fps performance and reduced bundle size
 * 
 * <AUTHOR> Team
 */

/**
 * Animation timing functions optimized for performance
 */
export const EASING = {
  // Standard easing functions
  ease: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
  easeIn: 'cubic-bezier(0.42, 0, 1, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.58, 1)',
  easeInOut: 'cubic-bezier(0.42, 0, 0.58, 1)',
  
  // Custom optimized easing
  smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  
  // Performance-optimized easing
  fast: 'cubic-bezier(0.4, 0, 1, 1)',
  slow: 'cubic-bezier(0, 0, 0.2, 1)'
} as const

/**
 * Animation duration constants
 */
export const DURATION = {
  instant: '0ms',
  fast: '150ms',
  normal: '200ms',
  slow: '300ms',
  slower: '500ms'
} as const

/**
 * CSS Animation Classes
 * These replace common Framer Motion patterns with optimized CSS
 */
export const CSS_ANIMATIONS = {
  // Fade animations
  fadeIn: 'animate-fade-in',
  fadeOut: 'animate-fade-out',
  fadeInUp: 'animate-fade-in-up',
  fadeInDown: 'animate-fade-in-down',
  
  // Scale animations
  scaleIn: 'animate-scale-in',
  scaleOut: 'animate-scale-out',
  scaleHover: 'hover:scale-105 transition-transform duration-200',
  scaleTap: 'active:scale-95 transition-transform duration-100',
  
  // Slide animations
  slideInLeft: 'animate-slide-in-left',
  slideInRight: 'animate-slide-in-right',
  slideInUp: 'animate-slide-in-up',
  slideInDown: 'animate-slide-in-down',
  
  // Rotation animations
  spin: 'animate-spin',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',
  
  // Interactive states
  buttonHover: 'hover:scale-105 hover:shadow-lg transition-all duration-200',
  cardHover: 'hover:scale-102 hover:shadow-xl transition-all duration-300',
  linkHover: 'hover:text-purple-400 transition-colors duration-200',
  
  // Loading states
  skeleton: 'animate-pulse bg-gray-700',
  shimmer: 'animate-shimmer bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700',
  
  // Stagger animations (using CSS delays)
  stagger1: 'animate-fade-in-up animation-delay-100',
  stagger2: 'animate-fade-in-up animation-delay-200',
  stagger3: 'animate-fade-in-up animation-delay-300',
  stagger4: 'animate-fade-in-up animation-delay-400'
} as const

/**
 * Performance-optimized animation utilities
 */
const createOptimizedAnimation = (
  property: 'transform' | 'opacity' | 'filter',
  duration: keyof typeof DURATION = 'normal',
  easing: keyof typeof EASING = 'smooth'
) => {
  return {
    transition: `${property} ${DURATION[duration]} ${EASING[easing]}`,
    willChange: property
  }
}

/**
 * Stagger animation utility
 */
const createStaggerDelay = (index: number, baseDelay: number = 100) => {
  return {
    animationDelay: `${index * baseDelay}ms`
  }
}

/**
 * Reduced motion utilities
 */
const getReducedMotionStyles = (prefersReducedMotion: boolean) => {
  if (prefersReducedMotion) {
    return {
      transition: 'none',
      animation: 'none',
      transform: 'none'
    }
  }
  return {}
}

/**
 * Performance monitoring utilities
 */
const withPerformanceMonitoring = (animationName: string) => {
  return {
    onAnimationStart: () => {
      performance.mark(`${animationName}-start`)
    },
    onAnimationEnd: () => {
      performance.mark(`${animationName}-end`)
      performance.measure(
        `${animationName}-duration`,
        `${animationName}-start`,
        `${animationName}-end`
      )
    }
  }
}

/**
 * Common animation patterns as CSS classes
 */
export const ANIMATION_PATTERNS = {
  // Card entrance animation
  cardEntrance: [
    'opacity-0',
    'translate-y-4',
    'animate-fade-in-up',
    'animation-fill-forwards'
  ].join(' '),
  
  // Button interaction
  buttonInteraction: [
    'transition-all',
    'duration-200',
    'hover:scale-105',
    'active:scale-95',
    'hover:shadow-lg'
  ].join(' '),
  
  // Modal entrance
  modalEntrance: [
    'opacity-0',
    'scale-95',
    'animate-modal-in',
    'animation-fill-forwards'
  ].join(' '),
  
  // Loading skeleton
  loadingSkeleton: [
    'animate-pulse',
    'bg-gray-700',
    'rounded'
  ].join(' '),
  
  // Hover lift effect
  hoverLift: [
    'transition-all',
    'duration-300',
    'hover:scale-102',
    'hover:shadow-xl',
    'hover:-translate-y-1'
  ].join(' ')
} as const

/**
 * Animation performance budgets
 */
export const PERFORMANCE_BUDGETS = {
  maxAnimationDuration: 500, // ms
  maxSimultaneousAnimations: 10,
  targetFPS: 60,
  maxLayoutShift: 0.1
} as const

/**
 * Device-specific animation settings
 */
const getDeviceOptimizedAnimations = () => {
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768
  const isLowEndDevice = typeof navigator !== 'undefined' && 
    (navigator.hardwareConcurrency || 0) < 4
  
  if (isMobile || isLowEndDevice) {
    return {
      duration: DURATION.fast,
      easing: EASING.fast,
      reduceComplexity: true
    }
  }
  
  return {
    duration: DURATION.normal,
    easing: EASING.smooth,
    reduceComplexity: false
  }
}

/**
 * Animation class builder utility
 */
const buildAnimationClasses = (
  baseClasses: string[],
  animations: string[],
  conditions: Record<string, boolean> = {}
) => {
  const classes = [...baseClasses]
  
  // Add animations
  classes.push(...animations)
  
  // Add conditional classes
  Object.entries(conditions).forEach(([className, condition]) => {
    if (condition) {
      classes.push(className)
    }
  })
  
  return classes.join(' ')
}

/**
 * CSS-in-JS animation styles for complex cases
 */
export const ANIMATION_STYLES = {
  fadeInUp: {
    '@keyframes fadeInUp': {
      '0%': {
        opacity: 0,
        transform: 'translateY(20px)'
      },
      '100%': {
        opacity: 1,
        transform: 'translateY(0)'
      }
    },
    animation: 'fadeInUp 0.3s ease-out forwards'
  },
  
  scaleIn: {
    '@keyframes scaleIn': {
      '0%': {
        opacity: 0,
        transform: 'scale(0.9)'
      },
      '100%': {
        opacity: 1,
        transform: 'scale(1)'
      }
    },
    animation: 'scaleIn 0.2s ease-out forwards'
  },
  
  shimmer: {
    '@keyframes shimmer': {
      '0%': {
        backgroundPosition: '-200% 0'
      },
      '100%': {
        backgroundPosition: '200% 0'
      }
    },
    backgroundSize: '200% 100%',
    animation: 'shimmer 1.5s infinite'
  }
} as const

/**
 * Export utility functions
 */
export {
  createOptimizedAnimation,
  createStaggerDelay,
  getReducedMotionStyles,
  withPerformanceMonitoring,
  getDeviceOptimizedAnimations,
  buildAnimationClasses
}
