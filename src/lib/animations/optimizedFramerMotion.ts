/**
 * Optimized Framer Motion Configuration
 * 
 * Performance-optimized Framer Motion variants and configurations
 * For complex animations that require Framer Motion's advanced features
 * 
 * <AUTHOR> Team
 */

import { Variants, Transition } from 'framer-motion'

/**
 * Performance-optimized transition configurations
 */
const OPTIMIZED_TRANSITIONS = {
  // Fast transitions for UI feedback
  fast: {
    duration: 0.15,
    ease: [0.4, 0, 0.2, 1] // Custom cubic-bezier for performance
  } as Transition,
  
  // Standard transitions for most animations
  standard: {
    duration: 0.2,
    ease: [0.4, 0, 0.2, 1]
  } as Transition,
  
  // Smooth transitions for complex animations
  smooth: {
    duration: 0.3,
    ease: [0.25, 0.1, 0.25, 1]
  } as Transition,
  
  // Spring transitions (use sparingly)
  spring: {
    type: 'spring',
    stiffness: 300,
    damping: 30,
    mass: 1
  } as Transition,
  
  // Optimized spring for mobile
  springMobile: {
    type: 'spring',
    stiffness: 400,
    damping: 40,
    mass: 0.8
  } as Transition
} as const

/**
 * Optimized animation variants for step transitions
 */
const STEP_TRANSITION_VARIANTS: Variants = {
  initial: {
    opacity: 0,
    x: 20,
    // Use transform instead of individual properties for better performance
    transform: 'translateX(20px)'
  },
  animate: {
    opacity: 1,
    x: 0,
    transform: 'translateX(0px)',
    transition: OPTIMIZED_TRANSITIONS.standard
  },
  exit: {
    opacity: 0,
    x: -20,
    transform: 'translateX(-20px)',
    transition: OPTIMIZED_TRANSITIONS.fast
  }
}

/**
 * Optimized modal animation variants
 */
const MODAL_VARIANTS: Variants = {
  initial: {
    opacity: 0,
    scale: 0.95,
    y: 10
  },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: OPTIMIZED_TRANSITIONS.standard
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    y: 10,
    transition: OPTIMIZED_TRANSITIONS.fast
  }
}

/**
 * Optimized backdrop variants
 */
const BACKDROP_VARIANTS: Variants = {
  initial: {
    opacity: 0
  },
  animate: {
    opacity: 1,
    transition: OPTIMIZED_TRANSITIONS.fast
  },
  exit: {
    opacity: 0,
    transition: OPTIMIZED_TRANSITIONS.fast
  }
}

/**
 * Optimized card animation variants (for complex cards only)
 */
const CARD_VARIANTS: Variants = {
  initial: {
    opacity: 0,
    y: 20
  },
  animate: (index: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      ...OPTIMIZED_TRANSITIONS.standard,
      delay: index * 0.05 // Reduced stagger delay
    }
  }),
  hover: {
    y: -2,
    transition: OPTIMIZED_TRANSITIONS.fast
  }
}

/**
 * Optimized list animation variants
 */
const LIST_VARIANTS: Variants = {
  initial: {
    opacity: 0
  },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05, // Reduced stagger
      delayChildren: 0.1
    }
  }
}

const LIST_ITEM_VARIANTS: Variants = {
  initial: {
    opacity: 0,
    x: -10
  },
  animate: {
    opacity: 1,
    x: 0,
    transition: OPTIMIZED_TRANSITIONS.fast
  }
}

/**
 * Performance-optimized animation configurations
 */
const PERFORMANCE_CONFIG = {
  // Reduce motion for low-end devices
  reducedMotion: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.1 }
  },
  
  // Mobile-optimized animations
  mobile: {
    transition: OPTIMIZED_TRANSITIONS.fast,
    // Disable complex animations on mobile
    disableHover: true,
    disableScale: true
  },
  
  // High-performance mode
  highPerformance: {
    // Use only opacity and transform
    allowedProperties: ['opacity', 'transform'],
    // Shorter durations
    maxDuration: 200,
    // Disable layout animations
    layout: false
  }
} as const

/**
 * Utility functions for optimized animations
 */

/**
 * Get device-optimized transition
 */
const getOptimizedTransition = (
  baseTransition: Transition,
  isMobile: boolean = false,
  prefersReducedMotion: boolean = false
): Transition => {
  if (prefersReducedMotion) {
    return { duration: 0.01 }
  }
  
  if (isMobile) {
    return {
      ...baseTransition,
      duration: Math.min((baseTransition as any).duration || 0.2, 0.15)
    }
  }
  
  return baseTransition
}

/**
 * Get optimized variants based on performance settings
 */
const getOptimizedVariants = (
  variants: Variants,
  performanceMode: 'high' | 'balanced' | 'low' = 'balanced'
): Variants => {
  if (performanceMode === 'high') {
    // Strip down to essential properties only
    return Object.entries(variants).reduce((acc, [key, value]) => {
      if (typeof value === 'object' && value !== null) {
        acc[key] = {
          opacity: value.opacity,
          transform: value.transform || `translateX(${value.x || 0}px) translateY(${value.y || 0}px) scale(${value.scale || 1})`
        }
      } else {
        acc[key] = value
      }
      return acc
    }, {} as Variants)
  }
  
  return variants
}

/**
 * Create staggered animation with performance optimization
 */
const createOptimizedStagger = (
  itemCount: number,
  baseDelay: number = 0.05,
  maxDelay: number = 0.3
): Variants => {
  const clampedDelay = Math.min(baseDelay, maxDelay / itemCount)
  
  return {
    animate: {
      transition: {
        staggerChildren: clampedDelay,
        delayChildren: 0.1
      }
    }
  }
}

/**
 * Optimized AnimatePresence configuration
 */
const ANIMATE_PRESENCE_CONFIG = {
  // Faster mode switching
  mode: 'wait' as const,
  // Reduced exit animations
  exitBeforeEnter: true,
  // Performance monitoring
  onExitComplete: () => {
    // Cleanup any remaining animations
    if (typeof window !== 'undefined') {
      window.requestIdleCallback?.(() => {
        // Force garbage collection if available
        if ('gc' in window) {
          (window as any).gc()
        }
      })
    }
  }
}

/**
 * Layout animation optimization
 */
const LAYOUT_CONFIG = {
  // Use layout prop sparingly
  layout: false,
  // Optimize layout animations
  layoutId: undefined,
  // Disable layout animations on mobile
  layoutDependency: undefined
}

/**
 * Gesture optimization for mobile
 */
const GESTURE_CONFIG = {
  // Optimized drag configuration
  drag: {
    dragConstraints: { left: 0, right: 0, top: 0, bottom: 0 },
    dragElastic: 0.1,
    dragMomentum: false
  },
  
  // Optimized tap configuration
  tap: {
    scale: 0.95,
    transition: OPTIMIZED_TRANSITIONS.fast
  },
  
  // Optimized hover (disabled on mobile)
  hover: {
    scale: 1.02,
    transition: OPTIMIZED_TRANSITIONS.fast
  }
}

/**
 * Performance monitoring integration
 */
const withPerformanceTracking = (
  animationName: string,
  variants: Variants
): Variants => {
  return Object.entries(variants).reduce((acc, [key, value]) => {
    if (typeof value === 'object' && value !== null && 'transition' in value) {
      acc[key] = {
        ...value,
        transition: {
          ...value.transition,
          onStart: () => {
            performance.mark(`${animationName}-${key}-start`)
          },
          onComplete: () => {
            performance.mark(`${animationName}-${key}-end`)
            performance.measure(
              `${animationName}-${key}`,
              `${animationName}-${key}-start`,
              `${animationName}-${key}-end`
            )
          }
        }
      }
    } else {
      acc[key] = value
    }
    return acc
  }, {} as Variants)
}

/**
 * Export optimized configurations
 */
export {
  OPTIMIZED_TRANSITIONS,
  STEP_TRANSITION_VARIANTS,
  MODAL_VARIANTS,
  BACKDROP_VARIANTS,
  CARD_VARIANTS,
  LIST_VARIANTS,
  LIST_ITEM_VARIANTS,
  PERFORMANCE_CONFIG,
  ANIMATE_PRESENCE_CONFIG,
  LAYOUT_CONFIG,
  GESTURE_CONFIG,
  getOptimizedTransition,
  getOptimizedVariants,
  createOptimizedStagger,
  withPerformanceTracking
}
