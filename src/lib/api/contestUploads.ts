/**
 * Contest File Upload Service
 *
 * Handles file uploads for contest submissions including image processing,
 * validation, thumbnail generation, and Firebase Storage integration.
 *
 * Features:
 * - Multi-file upload with progress tracking
 * - Image validation and optimization
 * - Automatic thumbnail generation
 * - Firebase Storage integration
 * - Error handling and retry logic
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject,
  uploadBytesResumable,
  UploadTaskSnapshot
} from 'firebase/storage'
import { storage } from '../firebase'
import type { Contest } from '../../types/contests'

// ===== TYPES =====

export interface UploadResult {
  url: string
  thumbnail: string
  metadata: {
    size: number
    dimensions: { width: number; height: number }
    format: string
    originalName: string
  }
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export interface UploadProgress {
  fileIndex: number
  fileName: string
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'complete' | 'error'
  error?: string
}

// ===== VALIDATION =====

/**
 * Validate files against contest requirements
 */
export function validateFiles(files: File[], requirements: Contest['requirements']): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Check file count
  if (files.length === 0) {
    errors.push('At least one file is required')
  }

  if (files.length > 5) {
    errors.push('Maximum 5 files allowed')
  }

  // Validate each file
  files.forEach((file, index) => {
    const filePrefix = `File ${index + 1}: `

    // Check file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (!fileExtension || !requirements.fileTypes.includes(fileExtension)) {
      errors.push(`${filePrefix}Invalid file type. Allowed: ${requirements.fileTypes.join(', ')}`)
    }

    // Check file size
    if (file.size > requirements.maxFileSize) {
      const maxSizeMB = (requirements.maxFileSize / (1024 * 1024)).toFixed(1)
      errors.push(`${filePrefix}File too large. Maximum size: ${maxSizeMB}MB`)
    }

    // Check if it's actually an image
    if (!file.type.startsWith('image/')) {
      errors.push(`${filePrefix}File must be an image`)
    }

    // File size warnings
    if (file.size > 2 * 1024 * 1024) { // 2MB
      warnings.push(`${filePrefix}Large file size may slow upload`)
    }
  })

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate image dimensions
 */
export async function validateImageDimensions(
  file: File, 
  minDimensions?: { width: number; height: number }
): Promise<{ valid: boolean; dimensions: { width: number; height: number }; error?: string }> {
  return new Promise((resolve) => {
    const img = new Image()
    
    img.onload = () => {
      const dimensions = { width: img.width, height: img.height }
      
      if (minDimensions) {
        if (img.width < minDimensions.width || img.height < minDimensions.height) {
          resolve({
            valid: false,
            dimensions,
            error: `Image must be at least ${minDimensions.width}×${minDimensions.height}px`
          })
          return
        }
      }
      
      resolve({ valid: true, dimensions })
    }
    
    img.onerror = () => {
      resolve({
        valid: false,
        dimensions: { width: 0, height: 0 },
        error: 'Invalid image file'
      })
    }
    
    img.src = URL.createObjectURL(file)
  })
}

// ===== IMAGE PROCESSING =====

/**
 * Compress image if needed
 */
export async function compressImage(file: File, maxWidth = 1920, quality = 0.8): Promise<File> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      // Set canvas size
      canvas.width = width
      canvas.height = height
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            resolve(file) // Fallback to original
          }
        },
        file.type,
        quality
      )
    }
    
    img.onerror = () => resolve(file) // Fallback to original
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Generate thumbnail
 */
export async function generateThumbnail(file: File, size = 300): Promise<File> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // Calculate square crop
      const minDimension = Math.min(img.width, img.height)
      const cropX = (img.width - minDimension) / 2
      const cropY = (img.height - minDimension) / 2
      
      // Set canvas size
      canvas.width = size
      canvas.height = size
      
      // Draw cropped and resized image
      ctx?.drawImage(
        img,
        cropX, cropY, minDimension, minDimension,
        0, 0, size, size
      )
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const thumbnailFile = new File([blob], `thumb_${file.name}`, {
              type: 'image/jpeg',
              lastModified: Date.now()
            })
            resolve(thumbnailFile)
          } else {
            resolve(file) // Fallback to original
          }
        },
        'image/jpeg',
        0.8
      )
    }
    
    img.onerror = () => resolve(file) // Fallback to original
    img.src = URL.createObjectURL(file)
  })
}

// ===== UPLOAD FUNCTIONS =====

/**
 * Upload single file to Firebase Storage
 */
export async function uploadSingleFile(
  file: File,
  path: string,
  onProgress?: (progress: number) => void
): Promise<string> {
  try {
    if (!storage) {
      throw new Error('Storage not initialized')
    }
    const storageRef = ref(storage, path)
    
    if (onProgress) {
      // Use resumable upload for progress tracking
      const uploadTask = uploadBytesResumable(storageRef, file)
      
      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot: UploadTaskSnapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100
            onProgress(progress)
          },
          (error) => {
            console.error('Upload error:', error)
            reject(error)
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref)
              resolve(downloadURL)
            } catch (error) {
              reject(error)
            }
          }
        )
      })
    } else {
      // Simple upload without progress
      const snapshot = await uploadBytes(storageRef, file)
      return await getDownloadURL(snapshot.ref)
    }
  } catch (error) {
    console.error('Upload failed:', error)
    throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Upload contest submission images
 */
export async function uploadSubmissionImages(
  files: File[],
  contestId: string,
  userId: string,
  onProgress?: (progress: UploadProgress[]) => void
): Promise<UploadResult[]> {
  const results: UploadResult[] = []
  const progressArray: UploadProgress[] = files.map((file, index) => ({
    fileIndex: index,
    fileName: file.name,
    progress: 0,
    status: 'pending'
  }))

  // Update progress
  const updateProgress = (index: number, updates: Partial<UploadProgress>) => {
    progressArray[index] = { ...progressArray[index], ...updates }
    onProgress?.(progressArray)
  }

  try {
    // Process files sequentially to avoid overwhelming the system
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      updateProgress(i, { status: 'uploading' })

      try {
        // Validate image dimensions
        const dimensionCheck = await validateImageDimensions(file)
        if (!dimensionCheck.valid) {
          throw new Error(dimensionCheck.error || 'Invalid image dimensions')
        }

        // Compress image if needed
        updateProgress(i, { progress: 10, status: 'processing' })
        const compressedFile = await compressImage(file)

        // Generate thumbnail
        updateProgress(i, { progress: 30, status: 'processing' })
        const thumbnailFile = await generateThumbnail(compressedFile)

        // Upload main image
        updateProgress(i, { progress: 50, status: 'uploading' })
        const imagePath = `contests/${contestId}/submissions/${userId}/${Date.now()}_${i}_${file.name}`
        const imageUrl = await uploadSingleFile(compressedFile, imagePath, (progress) => {
          updateProgress(i, { progress: 50 + (progress * 0.4) })
        })

        // Upload thumbnail
        updateProgress(i, { progress: 90, status: 'uploading' })
        const thumbnailPath = `contests/${contestId}/submissions/${userId}/thumbs/${Date.now()}_${i}_thumb_${file.name}`
        const thumbnailUrl = await uploadSingleFile(thumbnailFile, thumbnailPath)

        // Create result
        const result: UploadResult = {
          url: imageUrl,
          thumbnail: thumbnailUrl,
          metadata: {
            size: compressedFile.size,
            dimensions: dimensionCheck.dimensions,
            format: file.type,
            originalName: file.name
          }
        }

        results.push(result)
        updateProgress(i, { progress: 100, status: 'complete' })

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        updateProgress(i, { status: 'error', error: errorMessage })
        console.error(`Failed to upload file ${file.name}:`, error)
        
        // Continue with other files instead of failing completely
        continue
      }
    }

    return results
  } catch (error) {
    console.error('Upload process failed:', error)
    throw error
  }
}

/**
 * Delete uploaded files
 */
export async function deleteSubmissionImages(urls: string[]): Promise<void> {
  try {
    const deletePromises = urls.map(async (url) => {
      try {
        if (!storage) {
          throw new Error('Storage not initialized')
        }
        const fileRef = ref(storage, url)
        await deleteObject(fileRef)
      } catch (error) {
        console.error(`Failed to delete file ${url}:`, error)
        // Continue with other deletions
      }
    })

    await Promise.allSettled(deletePromises)
  } catch (error) {
    console.error('Failed to delete files:', error)
    throw error
  }
}

// ===== UTILITY FUNCTIONS =====

/**
 * Get file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Check if file type is supported
 */
export function isSupportedFileType(file: File, allowedTypes: string[]): boolean {
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  return fileExtension ? allowedTypes.includes(fileExtension) : false
}

/**
 * Generate unique filename
 */
export function generateUniqueFileName(originalName: string, userId: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
  
  return `${userId}_${timestamp}_${random}_${nameWithoutExt}.${extension}`
}
