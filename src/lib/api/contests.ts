/**
 * Contest System API Functions
 * 
 * Comprehensive API for contest management including CRUD operations,
 * submission handling, voting system, and judge management.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  runTransaction,
  writeBatch,
  Timestamp,
  increment,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import type {
  Contest,
  ContestSubmission,
  ContestVote,
  ContestJudge,
  ContestNotification,
  ContestFilters,
  ContestSubmissionFilters,
  ContestVoteFilters,
  ContestLeaderboard,
  ContestVotingProgress,
  ContestPaginatedResponse,
  ContestCreateInput,
  ContestUpdateInput,
  ContestSubmissionCreateInput,
  ContestVoteCreateInput,
  ContestError
} from '../../types/contests'

// ===== CONTEST CRUD OPERATIONS =====

/**
 * Create a new contest
 */
export async function createContest(contestData: ContestCreateInput): Promise<string> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🏆 Creating new contest:', contestData.title)
    
    // Validate contest data
    validateContestData(contestData)
    
    const newContest = {
      ...contestData,
      stats: {
        submissions: 0,
        participants: 0,
        totalVotes: 0,
        communityVotes: 0,
        expertVotes: 0
      },
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
    
    const docRef = await addDoc(collection(db, 'contests'), newContest)
    console.log('✅ Contest created successfully with ID:', docRef.id)
    
    return docRef.id
  } catch (error) {
    console.error('❌ Error creating contest:', error)
    throw new ContestAPIError('CONTEST_CREATE_FAILED', 'Failed to create contest', error)
  }
}

/**
 * Update an existing contest
 */
export async function updateContest(contestId: string, updates: ContestUpdateInput): Promise<void> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🔄 Updating contest:', contestId)
    
    const contestRef = doc(db!, 'contests', contestId)
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    }
    
    await updateDoc(contestRef, updateData)
    console.log('✅ Contest updated successfully')
  } catch (error) {
    console.error('❌ Error updating contest:', error)
    throw new ContestAPIError('CONTEST_UPDATE_FAILED', 'Failed to update contest', error)
  }
}

/**
 * Delete a contest
 */
export async function deleteContest(contestId: string): Promise<void> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🗑️ Deleting contest:', contestId)
    
    // Use transaction to ensure data consistency
    await runTransaction(db, async (transaction) => {
      const contestRef = doc(db!, 'contests', contestId)
      
      // Check if contest exists
      const contestDoc = await transaction.get(contestRef)
      if (!contestDoc.exists()) {
        throw new Error('Contest not found')
      }
      
      // Delete contest
      transaction.delete(contestRef)
      
      // Note: Related submissions, votes, etc. should be handled by Cloud Functions
      // or a separate cleanup process to avoid transaction size limits
    })
    
    console.log('✅ Contest deleted successfully')
  } catch (error) {
    console.error('❌ Error deleting contest:', error)
    throw new ContestAPIError('CONTEST_DELETE_FAILED', 'Failed to delete contest', error)
  }
}

/**
 * Get a single contest by ID
 */
export async function getContest(contestId: string): Promise<Contest | null> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    const docRef = doc(db, 'contests', contestId)
    const docSnap = await getDoc(docRef)
    
    if (!docSnap.exists()) {
      return null
    }
    
    return {
      id: docSnap.id,
      ...docSnap.data()
    } as Contest
  } catch (error) {
    console.error('❌ Error fetching contest:', error)
    throw new ContestAPIError('CONTEST_FETCH_FAILED', 'Failed to fetch contest', error)
  }
}

/**
 * Get contests with filtering and pagination
 */
export async function getContests(filters: ContestFilters = {}): Promise<ContestPaginatedResponse<Contest>> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('📋 Fetching contests with filters:', filters)
    
    let contestQuery = collection(db, 'contests')
    let queryConstraints: any[] = []
    
    // Apply filters
    if (filters.status && filters.status.length > 0) {
      queryConstraints.push(where('status', 'in', filters.status))
    }
    
    if (filters.category && filters.category.length > 0) {
      queryConstraints.push(where('category', 'in', filters.category))
    }
    
    if (filters.featured !== undefined) {
      queryConstraints.push(where('featured', '==', filters.featured))
    }
    
    // Apply sorting
    const sortBy = filters.sortBy || 'createdAt'
    const sortOrder = filters.sortOrder || 'desc'
    queryConstraints.push(orderBy(sortBy, sortOrder))
    
    // Apply pagination
    if (filters.limit) {
      queryConstraints.push(limit(filters.limit))
    }
    
    const q = query(contestQuery, ...queryConstraints)
    const querySnapshot = await getDocs(q)
    
    const contests: Contest[] = []
    querySnapshot.forEach((doc) => {
      contests.push({
        id: doc.id,
        ...doc.data()
      } as Contest)
    })
    
    console.log(`✅ Fetched ${contests.length} contests`)
    
    return {
      data: contests,
      total: contests.length, // Note: This is not the total count, just current page
      page: Math.floor((filters.offset || 0) / (filters.limit || 10)) + 1,
      limit: filters.limit || 10,
      hasMore: contests.length === (filters.limit || 10)
    }
  } catch (error) {
    console.error('❌ Error fetching contests:', error)
    
    // Return mock data for development
    console.log('📝 Returning mock contests for development')
    return getMockContests(filters)
  }
}

// ===== SUBMISSION OPERATIONS =====

/**
 * Submit an entry to a contest
 */
export async function submitContestEntry(submissionData: ContestSubmissionCreateInput): Promise<string> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('📤 Submitting contest entry for contest:', submissionData.contestId)
    
    // Validate submission data
    await validateSubmissionData(submissionData)
    
    const newSubmission = {
      ...submissionData,
      submittedAt: serverTimestamp(),
      lastModified: serverTimestamp(),
      status: 'submitted' as const,
      views: 0,
      likes: 0,
      comments: 0,
      scores: {
        community: { average: 0, count: 0, total: 0 },
        expert: { 
          average: 0, 
          count: 0, 
          total: 0,
          breakdown: { creativity: 0, technical: 0, originality: 0, themeAdherence: 0 }
        },
        final: 0
      },
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
    
    // Use transaction to update contest stats
    const submissionId = await runTransaction(db, async (transaction) => {
      // Add submission
      const submissionRef = doc(collection(db!, 'contest_submissions'))
      transaction.set(submissionRef, newSubmission)
      
      // Update contest stats
      const contestRef = doc(db!, 'contests', submissionData.contestId)
      transaction.update(contestRef, {
        'stats.submissions': increment(1),
        'stats.participants': increment(1), // Note: This might double-count users with multiple submissions
        updatedAt: serverTimestamp()
      })
      
      return submissionRef.id
    })
    
    console.log('✅ Contest entry submitted successfully with ID:', submissionId)
    return submissionId
  } catch (error) {
    console.error('❌ Error submitting contest entry:', error)
    throw new ContestAPIError('SUBMISSION_CREATE_FAILED', 'Failed to submit contest entry', error)
  }
}

/**
 * Update a contest submission
 */
export async function updateContestSubmission(
  submissionId: string, 
  updates: Partial<ContestSubmission>
): Promise<void> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🔄 Updating contest submission:', submissionId)
    
    const submissionRef = doc(db!, 'contest_submissions', submissionId)
    const updateData = {
      ...updates,
      lastModified: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
    
    await updateDoc(submissionRef, updateData)
    console.log('✅ Contest submission updated successfully')
  } catch (error) {
    console.error('❌ Error updating contest submission:', error)
    throw new ContestAPIError('SUBMISSION_UPDATE_FAILED', 'Failed to update submission', error)
  }
}

/**
 * Delete a contest submission
 */
export async function deleteContestSubmission(submissionId: string): Promise<void> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🗑️ Deleting contest submission:', submissionId)
    
    await runTransaction(db, async (transaction) => {
      const submissionRef = doc(db!, 'contest_submissions', submissionId)
      
      // Get submission to update contest stats
      const submissionDoc = await transaction.get(submissionRef)
      if (!submissionDoc.exists()) {
        throw new Error('Submission not found')
      }
      
      const submission = submissionDoc.data() as ContestSubmission
      
      // Delete submission
      transaction.delete(submissionRef)
      
      // Update contest stats
      const contestRef = doc(db!, 'contests', submission.contestId)
      transaction.update(contestRef, {
        'stats.submissions': increment(-1),
        updatedAt: serverTimestamp()
      })
    })
    
    console.log('✅ Contest submission deleted successfully')
  } catch (error) {
    console.error('❌ Error deleting contest submission:', error)
    throw new ContestAPIError('SUBMISSION_DELETE_FAILED', 'Failed to delete submission', error)
  }
}

/**
 * Get a single contest submission
 */
export async function getContestSubmission(submissionId: string): Promise<ContestSubmission | null> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    const docRef = doc(db, 'contest_submissions', submissionId)
    const docSnap = await getDoc(docRef)
    
    if (!docSnap.exists()) {
      return null
    }
    
    return {
      id: docSnap.id,
      ...docSnap.data()
    } as ContestSubmission
  } catch (error) {
    console.error('❌ Error fetching contest submission:', error)
    throw new ContestAPIError('SUBMISSION_FETCH_FAILED', 'Failed to fetch submission', error)
  }
}

// ===== HELPER FUNCTIONS =====

/**
 * Validate contest data before creation
 */
function validateContestData(contestData: ContestCreateInput): void {
  if (!contestData.title?.trim()) {
    throw new Error('Contest title is required')
  }
  
  if (!contestData.description?.trim()) {
    throw new Error('Contest description is required')
  }
  
  if (!contestData.category) {
    throw new Error('Contest category is required')
  }
  
  // Validate dates
  const now = new Date()
  const submissionStart = contestData.submissionStart.toDate()
  const submissionEnd = contestData.submissionEnd.toDate()
  const votingStart = contestData.votingStart.toDate()
  const votingEnd = contestData.votingEnd.toDate()
  
  if (submissionStart >= submissionEnd) {
    throw new Error('Submission start must be before submission end')
  }
  
  if (submissionEnd >= votingStart) {
    throw new Error('Submission end must be before voting start')
  }
  
  if (votingStart >= votingEnd) {
    throw new Error('Voting start must be before voting end')
  }
}

/**
 * Validate submission data
 */
async function validateSubmissionData(submissionData: ContestSubmissionCreateInput): Promise<void> {
  if (!submissionData.title?.trim()) {
    throw new Error('Submission title is required')
  }
  
  if (!submissionData.contestId) {
    throw new Error('Contest ID is required')
  }
  
  if (!submissionData.userId) {
    throw new Error('User ID is required')
  }
  
  if (!submissionData.images || submissionData.images.length === 0) {
    throw new Error('At least one image is required')
  }
  
  // Check if contest exists and is accepting submissions
  const contest = await getContest(submissionData.contestId)
  if (!contest) {
    throw new Error('Contest not found')
  }
  
  if (contest.status !== 'active') {
    throw new Error('Contest is not accepting submissions')
  }
}

/**
 * Custom error class for contest API operations
 */
class ContestAPIError extends Error {
  constructor(
    public code: string,
    message: string,
    public originalError?: any
  ) {
    super(message)
    this.name = 'ContestAPIError'
  }
}

/**
 * Mock data for development
 */
function getMockContests(filters: ContestFilters): ContestPaginatedResponse<Contest> {
  const mockContests: Contest[] = [
    {
      id: 'mock-contest-1',
      title: 'Summer Keycap Design Challenge',
      description: 'Design the most creative summer-themed keycap',
      theme: 'Summer Vibes',
      category: 'keycap_design',
      status: 'active',
      submissionStart: Timestamp.now(),
      submissionEnd: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)),
      votingStart: Timestamp.fromDate(new Date(Date.now() + 8 * 24 * 60 * 60 * 1000)),
      votingEnd: Timestamp.fromDate(new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)),
      resultsDate: Timestamp.fromDate(new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)),
      rules: ['Original designs only', 'Must follow theme', 'One submission per user'],
      requirements: {
        maxSubmissions: 1,
        allowTeams: false,
        fileTypes: ['jpg', 'png'],
        maxFileSize: 5 * 1024 * 1024
      },
      prizes: {
        first: { points: 1000, physicalPrize: 'Custom Keycap Set', storeCredit: 100 },
        second: { points: 500, storeCredit: 50 },
        third: { points: 250, storeCredit: 25 },
        participation: { points: 50 }
      },
      judgingCriteria: {
        creativity: 30,
        technical: 25,
        originality: 25,
        themeAdherence: 20
      },
      stats: {
        submissions: 15,
        participants: 15,
        totalVotes: 45,
        communityVotes: 30,
        expertVotes: 15
      },
      tags: ['summer', 'keycap', 'design'],
      featured: true,
      createdBy: {
        userId: 'admin-1',
        userName: 'Syndicaps Admin',
        userAvatar: '/images/admin-avatar.jpg'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ]
  
  return {
    data: mockContests,
    total: mockContests.length,
    page: 1,
    limit: filters.limit || 10,
    hasMore: false
  }
}

// ===== VOTING OPERATIONS =====

/**
 * Submit a vote for a contest submission
 */
export async function submitContestVote(voteData: ContestVoteCreateInput): Promise<void> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🗳️ Submitting vote for submission:', voteData.submissionId)

    // Validate vote data
    await validateVoteData(voteData)

    const newVote = {
      ...voteData,
      votedAt: serverTimestamp(),
      lastModified: serverTimestamp(),
      verified: true, // Will be validated by security rules
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }

    // Use transaction to update submission scores and contest stats
    await runTransaction(db, async (transaction) => {
      // Check if user already voted on this submission
      const existingVoteQuery = query(
        collection(db!, 'contest_votes'),
        where('contestId', '==', voteData.contestId),
        where('submissionId', '==', voteData.submissionId),
        where('voterId', '==', voteData.voterId)
      )
      const existingVotes = await getDocs(existingVoteQuery)

      if (!existingVotes.empty) {
        throw new Error('User has already voted on this submission')
      }

      // Add vote
      const voteRef = doc(collection(db!, 'contest_votes'))
      transaction.set(voteRef, newVote)

      // Update submission scores (simplified - real implementation would recalculate)
      const submissionRef = doc(db!, 'contest_submissions', voteData.submissionId)
      if (voteData.type === 'community') {
        transaction.update(submissionRef, {
          'scores.community.count': increment(1),
          'scores.community.total': increment(voteData.rating),
          updatedAt: serverTimestamp()
        })
      } else {
        transaction.update(submissionRef, {
          'scores.expert.count': increment(1),
          'scores.expert.total': increment(voteData.rating),
          updatedAt: serverTimestamp()
        })
      }

      // Update contest stats
      const contestRef = doc(db!, 'contests', voteData.contestId)
      const voteField = voteData.type === 'community' ? 'communityVotes' : 'expertVotes'
      transaction.update(contestRef, {
        'stats.totalVotes': increment(1),
        [`stats.${voteField}`]: increment(1),
        updatedAt: serverTimestamp()
      })
    })

    console.log('✅ Vote submitted successfully')
  } catch (error) {
    console.error('❌ Error submitting vote:', error)
    throw new ContestAPIError('VOTE_SUBMIT_FAILED', 'Failed to submit vote', error)
  }
}

/**
 * Update an existing vote
 */
export async function updateContestVote(voteId: string, updates: Partial<ContestVote>): Promise<void> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🔄 Updating vote:', voteId)

    const voteRef = doc(db, 'contest_votes', voteId)
    const updateData = {
      ...updates,
      lastModified: serverTimestamp(),
      updatedAt: serverTimestamp()
    }

    await updateDoc(voteRef, updateData)
    console.log('✅ Vote updated successfully')
  } catch (error) {
    console.error('❌ Error updating vote:', error)
    throw new ContestAPIError('VOTE_UPDATE_FAILED', 'Failed to update vote', error)
  }
}

/**
 * Get user's vote for a specific submission
 */
export async function getUserVote(
  contestId: string,
  submissionId: string,
  userId: string
): Promise<ContestVote | null> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    const voteQuery = query(
      collection(db!, 'contest_votes'),
      where('contestId', '==', contestId),
      where('submissionId', '==', submissionId),
      where('voterId', '==', userId),
      limit(1)
    )

    const querySnapshot = await getDocs(voteQuery)

    if (querySnapshot.empty) {
      return null
    }

    const doc = querySnapshot.docs[0]
    return {
      id: doc.id,
      ...doc.data()
    } as ContestVote
  } catch (error) {
    console.error('❌ Error fetching user vote:', error)
    throw new ContestAPIError('VOTE_FETCH_FAILED', 'Failed to fetch user vote', error)
  }
}

/**
 * Get all votes for a submission
 */
export async function getSubmissionVotes(submissionId: string): Promise<ContestVote[]> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    const votesQuery = query(
      collection(db!, 'contest_votes'),
      where('submissionId', '==', submissionId),
      orderBy('votedAt', 'desc')
    )

    const querySnapshot = await getDocs(votesQuery)
    const votes: ContestVote[] = []

    querySnapshot.forEach((doc) => {
      votes.push({
        id: doc.id,
        ...doc.data()
      } as ContestVote)
    })

    return votes
  } catch (error) {
    console.error('❌ Error fetching submission votes:', error)
    throw new ContestAPIError('VOTES_FETCH_FAILED', 'Failed to fetch submission votes', error)
  }
}

// ===== JUDGE MANAGEMENT =====

/**
 * Assign a judge to a contest
 */
export async function assignContestJudge(judgeData: Omit<ContestJudge, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('👨‍⚖️ Assigning judge to contest:', judgeData.contestId)

    const newJudge = {
      ...judgeData,
      submissionsToReview: 0, // Will be updated when submissions are finalized
      submissionsReviewed: 0,
      averageScore: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }

    const docRef = await addDoc(collection(db!, 'contest_judges'), newJudge)
    console.log('✅ Judge assigned successfully with ID:', docRef.id)

    return docRef.id
  } catch (error) {
    console.error('❌ Error assigning judge:', error)
    throw new ContestAPIError('JUDGE_ASSIGN_FAILED', 'Failed to assign judge', error)
  }
}

/**
 * Update judge status
 */
export async function updateJudgeStatus(judgeId: string, status: ContestJudge['status']): Promise<void> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🔄 Updating judge status:', judgeId, 'to', status)

    const judgeRef = doc(db!, 'contest_judges', judgeId)
    const updateData: any = {
      status,
      updatedAt: serverTimestamp()
    }

    if (status === 'completed') {
      updateData.completedAt = serverTimestamp()
    }

    await updateDoc(judgeRef, updateData)
    console.log('✅ Judge status updated successfully')
  } catch (error) {
    console.error('❌ Error updating judge status:', error)
    throw new ContestAPIError('JUDGE_UPDATE_FAILED', 'Failed to update judge status', error)
  }
}

/**
 * Get judges for a contest
 */
export async function getContestJudges(contestId: string): Promise<ContestJudge[]> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    const judgesQuery = query(
      collection(db!, 'contest_judges'),
      where('contestId', '==', contestId),
      orderBy('assignedAt', 'asc')
    )

    const querySnapshot = await getDocs(judgesQuery)
    const judges: ContestJudge[] = []

    querySnapshot.forEach((doc) => {
      judges.push({
        id: doc.id,
        ...doc.data()
      } as ContestJudge)
    })

    return judges
  } catch (error) {
    console.error('❌ Error fetching contest judges:', error)
    throw new ContestAPIError('JUDGES_FETCH_FAILED', 'Failed to fetch contest judges', error)
  }
}

// ===== ANALYTICS & STATISTICS =====

/**
 * Get contest leaderboard
 */
export async function getContestLeaderboard(contestId: string): Promise<ContestLeaderboard> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('🏆 Fetching contest leaderboard for:', contestId)

    // Get all approved submissions for the contest, sorted by final score
    const submissionsQuery = query(
      collection(db!, 'contest_submissions'),
      where('contestId', '==', contestId),
      where('status', '==', 'approved'),
      orderBy('scores.final', 'desc'),
      limit(50) // Top 50 entries
    )

    const querySnapshot = await getDocs(submissionsQuery)
    const entries: any[] = []

    let rank = 1
    querySnapshot.forEach((doc: import('firebase/firestore').QueryDocumentSnapshot) => {
      const submission = { id: doc.id, ...doc.data() } as ContestSubmission
      entries.push({
        submissionId: doc.id,
        submission,
        rank: rank,
        score: submission.scores.final,
        communityScore: submission.scores.community.average,
        expertScore: submission.scores.expert.average,
        voteCount: submission.scores.community.count + submission.scores.expert.count
      })
      rank++
    })

    return {
      contestId,
      entries,
      lastUpdated: Timestamp.now()
    }
  } catch (error) {
    console.error('❌ Error fetching contest leaderboard:', error)
    throw new ContestAPIError('LEADERBOARD_FETCH_FAILED', 'Failed to fetch contest leaderboard', error)
  }
}

/**
 * Get voting progress for a contest
 */
export async function getContestVotingProgress(contestId: string): Promise<ContestVotingProgress> {
  try {
    if (!db) { throw new Error('Firestore not initialized') }
    console.log('📊 Fetching voting progress for contest:', contestId)

    // Get contest submissions count
    const submissionsQuery = query(
      collection(db!, 'contest_submissions'),
      where('contestId', '==', contestId),
      where('status', '==', 'approved')
    )
    const submissionsSnapshot = await getDocs(submissionsQuery)
    const totalSubmissions = submissionsSnapshot.size

    // Get voting statistics
    const votesQuery = query(
      collection(db!, 'contest_votes'),
      where('contestId', '==', contestId)
    )
    const votesSnapshot = await getDocs(votesQuery)

    let communityVotes = 0
    let expertVotes = 0
    const submissionsWithVotes = new Set<string>()

    votesSnapshot.forEach((doc) => {
      const vote = doc.data() as ContestVote
      if (vote.type === 'community') {
        communityVotes++
      } else {
        expertVotes++
      }
      submissionsWithVotes.add(vote.submissionId)
    })

    const totalVotes = communityVotes + expertVotes
    const averageVotesPerSubmission = totalSubmissions > 0 ? totalVotes / totalSubmissions : 0
    const completionPercentage = totalSubmissions > 0 ? (submissionsWithVotes.size / totalSubmissions) * 100 : 0

    return {
      contestId,
      totalSubmissions,
      submissionsWithVotes: submissionsWithVotes.size,
      totalCommunityVotes: communityVotes,
      totalExpertVotes: expertVotes,
      averageVotesPerSubmission,
      completionPercentage,
      lastUpdated: Timestamp.now()
    }
  } catch (error) {
    console.error('❌ Error fetching voting progress:', error)
    throw new ContestAPIError('VOTING_PROGRESS_FETCH_FAILED', 'Failed to fetch voting progress', error)
  }
}

// ===== VALIDATION HELPERS =====

/**
 * Validate vote data
 */
async function validateVoteData(voteData: ContestVoteCreateInput): Promise<void> {
  if (!voteData.contestId) {
    throw new Error('Contest ID is required')
  }

  if (!voteData.submissionId) {
    throw new Error('Submission ID is required')
  }

  if (!voteData.voterId) {
    throw new Error('Voter ID is required')
  }

  if (!voteData.type || !['community', 'expert'].includes(voteData.type)) {
    throw new Error('Valid vote type is required')
  }

  if (voteData.type === 'community') {
    if (!voteData.rating || voteData.rating < 1 || voteData.rating > 5) {
      throw new Error('Community vote rating must be between 1 and 5')
    }
  }

  if (voteData.type === 'expert' && voteData.expertScores) {
    const scores = voteData.expertScores
    if (!scores.creativity || scores.creativity < 1 || scores.creativity > 10) {
      throw new Error('Expert creativity score must be between 1 and 10')
    }
    if (!scores.technical || scores.technical < 1 || scores.technical > 10) {
      throw new Error('Expert technical score must be between 1 and 10')
    }
    if (!scores.originality || scores.originality < 1 || scores.originality > 10) {
      throw new Error('Expert originality score must be between 1 and 10')
    }
    if (!scores.themeAdherence || scores.themeAdherence < 1 || scores.themeAdherence > 10) {
      throw new Error('Expert theme adherence score must be between 1 and 10')
    }
  }

  // Check if contest is in voting phase
  const contest = await getContest(voteData.contestId)
  if (!contest) {
    throw new Error('Contest not found')
  }

  if (contest.status !== 'voting') {
    throw new Error('Contest is not in voting phase')
  }
}
