/**
 * Achievement Engine - Automated Progress Tracking
 * 
 * Intelligent achievement system that automatically tracks user progress,
 * evaluates complex conditions, and unlocks achievements in real-time.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  runTransaction,
  Timestamp
} from 'firebase/firestore'
import { db } from '../../firebase'
import { collections } from './gamificationCollections'
import { SecurePointSystem, AuditLogger } from './secureTransactions'
import { gamificationCache, CacheKeys } from './cachingService'

// ===== TYPES =====

export interface Achievement {
  id?: string
  title: string
  description: string
  icon: string
  category: AchievementCategory
  rarity: AchievementRarity
  requirements: AchievementRequirement[]
  rewards: AchievementReward
  conditions?: AchievementCondition[]
  isActive: boolean
  isSecret?: boolean
  sortOrder: number
  createdAt: Timestamp
  metadata?: Record<string, any>
}

export interface AchievementRequirement {
  type: RequirementType
  target: number
  current?: number
  operator?: 'gte' | 'lte' | 'eq' | 'between'
  timeframe?: number // in days, 0 = all time
  metadata?: Record<string, any>
}

export interface AchievementCondition {
  type: 'and' | 'or' | 'not'
  requirements?: AchievementRequirement[]
  conditions?: AchievementCondition[]
}

export interface AchievementReward {
  points: number
  badges?: string[]
  title?: string
  unlocks?: string[] // IDs of other achievements or features
  specialRewards?: {
    type: 'tier_boost' | 'discount' | 'access' | 'custom'
    value: any
    description: string
  }[]
}

export interface UserAchievement {
  id?: string
  userId: string
  achievementId: string
  progress: number
  isCompleted: boolean
  unlockedAt?: Timestamp
  progressHistory: ProgressSnapshot[]
  metadata?: Record<string, any>
}

export interface ProgressSnapshot {
  timestamp: Timestamp
  progress: number
  triggerEvent: string
  metadata?: Record<string, any>
}

export interface UserActivity {
  userId: string
  type: ActivityType
  timestamp: Date
  value?: number
  metadata?: Record<string, any>
}

export type AchievementCategory = 
  | 'onboarding'
  | 'engagement'
  | 'social' 
  | 'challenge'
  | 'milestone'
  | 'special'
  | 'seasonal'

export type AchievementRarity = 
  | 'common'
  | 'uncommon'
  | 'rare'
  | 'epic'
  | 'legendary'

export type RequirementType = 
  | 'total_points'
  | 'points_in_timeframe'
  | 'login_streak'
  | 'challenges_completed'
  | 'submissions_count'
  | 'community_posts'
  | 'social_shares'
  | 'referrals'
  | 'purchases_made'
  | 'rewards_redeemed'
  | 'profile_completion'
  | 'level_reached'
  | 'tier_achieved'
  | 'consecutive_days_active'
  | 'custom'

export type ActivityType = 
  | 'points_earned'
  | 'login'
  | 'challenge_joined'
  | 'challenge_completed' 
  | 'submission_created'
  | 'community_post'
  | 'social_share'
  | 'referral_completed'
  | 'purchase_made'
  | 'reward_redeemed'
  | 'profile_updated'
  | 'level_up'
  | 'tier_promotion'

// ===== ACHIEVEMENT TEMPLATES =====

export const AchievementTemplates = {
  // Onboarding achievements
  firstSteps: (): Omit<Achievement, 'id' | 'createdAt'> => ({
    title: 'First Steps',
    description: 'Complete your profile setup',
    icon: '👋',
    category: 'onboarding',
    rarity: 'common',
    requirements: [{
      type: 'profile_completion',
      target: 100,
      operator: 'gte'
    }],
    rewards: { points: 50 },
    isActive: true,
    sortOrder: 1
  }),

  // Point milestones
  pointCollector: (points: number, rarity: AchievementRarity = 'common'): Omit<Achievement, 'id' | 'createdAt'> => ({
    title: `Point Collector`,
    description: `Earn ${points.toLocaleString()} total points`,
    icon: '💰',
    category: 'milestone',
    rarity,
    requirements: [{
      type: 'total_points',
      target: points,
      operator: 'gte'
    }],
    rewards: { points: Math.floor(points * 0.1) },
    isActive: true,
    sortOrder: 10
  }),

  // Streak achievements
  streakMaster: (days: number): Omit<Achievement, 'id' | 'createdAt'> => ({
    title: `${days}-Day Streak`,
    description: `Log in for ${days} consecutive days`,
    icon: '🔥',
    category: 'engagement',
    rarity: days >= 30 ? 'epic' : days >= 7 ? 'rare' : 'common',
    requirements: [{
      type: 'login_streak',
      target: days,
      operator: 'gte'
    }],
    rewards: { 
      points: days * 10,
      badges: [`streak_${days}`]
    },
    isActive: true,
    sortOrder: 20
  }),

  // Social achievements
  socialButterfly: (shares: number): Omit<Achievement, 'id' | 'createdAt'> => ({
    title: 'Social Butterfly',
    description: `Share content ${shares} times`,
    icon: '🦋',
    category: 'social',
    rarity: 'uncommon',
    requirements: [{
      type: 'social_shares',
      target: shares,
      operator: 'gte'
    }],
    rewards: { points: shares * 20 },
    isActive: true,
    sortOrder: 30
  }),

  // Challenge achievements
  challengeChampion: (challenges: number): Omit<Achievement, 'id' | 'createdAt'> => ({
    title: 'Challenge Champion',
    description: `Complete ${challenges} challenges`,
    icon: '🏆',
    category: 'challenge',
    rarity: challenges >= 50 ? 'legendary' : challenges >= 20 ? 'epic' : 'rare',
    requirements: [{
      type: 'challenges_completed',
      target: challenges,
      operator: 'gte'
    }],
    rewards: { 
      points: challenges * 100,
      specialRewards: challenges >= 50 ? [{
        type: 'tier_boost',
        value: 1,
        description: 'Advance to next tier immediately'
      }] : undefined
    },
    isActive: true,
    sortOrder: 40
  }),

  // Complex conditional achievement
  powerUser: (): Omit<Achievement, 'id' | 'createdAt'> => ({
    title: 'Power User',
    description: 'Complete multiple engagement milestones',
    icon: '⚡',
    category: 'special',
    rarity: 'epic',
    requirements: [
      {
        type: 'total_points',
        target: 5000,
        operator: 'gte'
      },
      {
        type: 'challenges_completed',
        target: 10,
        operator: 'gte'
      },
      {
        type: 'login_streak',
        target: 14,
        operator: 'gte'
      }
    ],
    conditions: [{
      type: 'and',
      requirements: [
        { type: 'total_points', target: 5000, operator: 'gte' },
        { type: 'challenges_completed', target: 10, operator: 'gte' },
        { type: 'login_streak', target: 14, operator: 'gte' }
      ]
    }],
    rewards: { 
      points: 1000,
      title: 'Power User',
      badges: ['power_user'],
      specialRewards: [{
        type: 'access',
        value: 'beta_features',
        description: 'Access to beta features'
      }]
    },
    isActive: true,
    sortOrder: 100
  })
}

// ===== ACHIEVEMENT ENGINE =====

export class AchievementEngine {
  private static progressCache = new Map<string, Map<string, number>>()
  private static processingQueue = new Set<string>()

  /**
   * Process user activity and update achievement progress
   */
  static async processActivity(activity: UserActivity): Promise<string[]> {
    const { userId, type } = activity
    const cacheKey = `processing_${userId}_${Date.now()}`
    
    // Prevent duplicate processing
    if (this.processingQueue.has(userId)) {
      return []
    }
    
    this.processingQueue.add(userId)
    
    try {
      const unlockedAchievements = await this.evaluateAchievements(userId, activity)
      
      // Cache progress updates
      await gamificationCache.invalidateByTags(['user-achievements'])
      
      return unlockedAchievements
    } catch (error) {
      console.error('Error processing achievement activity:', error)
      return []
    } finally {
      this.processingQueue.delete(userId)
    }
  }

  /**
   * Evaluate all achievements for a user after an activity
   */
  private static async evaluateAchievements(
    userId: string, 
    activity: UserActivity
  ): Promise<string[]> {
    try {
      // Get all active achievements
      const achievements = await this.getActiveAchievements()
      
      // Get user's current achievements
      const userAchievements = await this.getUserAchievements(userId)
      const completedIds = new Set(
        userAchievements
          .filter(ua => ua.isCompleted)
          .map(ua => ua.achievementId)
      )
      
      // Get user statistics for evaluation
      const userStats = await this.getUserStatistics(userId)
      
      const unlockedAchievements: string[] = []
      
      for (const achievement of achievements) {
        // Skip if already completed
        if (completedIds.has(achievement.id!)) {
          continue
        }
        
        // Check if achievement is relevant to this activity
        if (!this.isAchievementRelevant(achievement, activity)) {
          continue
        }
        
        // Evaluate achievement requirements
        const progress = await this.evaluateAchievementProgress(
          achievement,
          userStats,
          activity
        )
        
        // Update progress
        await this.updateAchievementProgress(
          userId,
          achievement.id!,
          progress,
          activity.type
        )
        
        // Check if achievement is completed
        if (progress >= 100) {
          const unlocked = await this.unlockAchievement(
            userId,
            achievement.id!,
            activity
          )
          
          if (unlocked) {
            unlockedAchievements.push(achievement.id!)
          }
        }
      }
      
      return unlockedAchievements
    } catch (error) {
      console.error('Error evaluating achievements:', error)
      return []
    }
  }

  /**
   * Check if achievement is relevant to the current activity
   */
  private static isAchievementRelevant(
    achievement: Achievement,
    activity: UserActivity
  ): boolean {
    const relevantTypes: Record<ActivityType, RequirementType[]> = {
      points_earned: ['total_points', 'points_in_timeframe'],
      login: ['login_streak', 'consecutive_days_active'],
      challenge_joined: ['challenges_completed'],
      challenge_completed: ['challenges_completed'],
      submission_created: ['submissions_count'],
      community_post: ['community_posts'],
      social_share: ['social_shares'],
      referral_completed: ['referrals'],
      purchase_made: ['purchases_made'],
      reward_redeemed: ['rewards_redeemed'],
      profile_updated: ['profile_completion'],
      level_up: ['level_reached'],
      tier_promotion: ['tier_achieved']
    }
    
    const relevantRequirementTypes = relevantTypes[activity.type] || []
    
    return achievement.requirements.some(req => 
      relevantRequirementTypes.includes(req.type)
    )
  }

  /**
   * Evaluate achievement progress based on current user statistics
   */
  private static async evaluateAchievementProgress(
    achievement: Achievement,
    userStats: Record<string, any>,
    activity: UserActivity
  ): Promise<number> {
    try {
      let totalProgress = 0
      let completedRequirements = 0
      
      for (const requirement of achievement.requirements) {
        const progress = await this.evaluateRequirement(
          requirement,
          userStats,
          activity
        )
        
        totalProgress += progress
        if (progress >= 100) {
          completedRequirements++
        }
      }
      
      // Check if we have complex conditions
      if (achievement.conditions && achievement.conditions.length > 0) {
        return this.evaluateConditions(achievement.conditions[0], userStats, activity)
      }
      
      // For multiple requirements, all must be completed
      if (achievement.requirements.length > 1) {
        return completedRequirements === achievement.requirements.length ? 100 : 
               (completedRequirements / achievement.requirements.length) * 100
      }
      
      // Single requirement
      return Math.min(100, totalProgress)
    } catch (error) {
      console.error('Error evaluating achievement progress:', error)
      return 0
    }
  }

  /**
   * Evaluate a single requirement
   */
  private static async evaluateRequirement(
    requirement: AchievementRequirement,
    userStats: Record<string, any>,
    activity: UserActivity
  ): Promise<number> {
    let currentValue = 0
    
    switch (requirement.type) {
      case 'total_points':
        currentValue = userStats.totalPointsEarned || 0
        break
        
      case 'points_in_timeframe':
        if (requirement.timeframe) {
          currentValue = await this.getPointsInTimeframe(
            userStats.userId,
            requirement.timeframe
          )
        }
        break
        
      case 'login_streak':
        currentValue = userStats.currentLoginStreak || 0
        break
        
      case 'challenges_completed':
        currentValue = userStats.challengesCompleted || 0
        break
        
      case 'submissions_count':
        currentValue = userStats.submissionsCount || 0
        break
        
      case 'community_posts':
        currentValue = userStats.communityPosts || 0
        break
        
      case 'social_shares':
        currentValue = userStats.socialShares || 0
        break
        
      case 'referrals':
        currentValue = userStats.referralsCompleted || 0
        break
        
      case 'purchases_made':
        currentValue = userStats.purchasesMade || 0
        break
        
      case 'rewards_redeemed':
        currentValue = userStats.rewardsRedeemed || 0
        break
        
      case 'profile_completion':
        currentValue = userStats.profileCompletion || 0
        break
        
      case 'level_reached':
        currentValue = userStats.currentLevel || 1
        break
        
      case 'tier_achieved':
        currentValue = this.getTierNumericValue(userStats.currentTier || 'bronze')
        break
        
      case 'consecutive_days_active':
        currentValue = userStats.consecutiveDaysActive || 0
        break
        
      case 'custom':
        currentValue = requirement.metadata?.value || 0
        break
        
      default:
        currentValue = 0
    }
    
    // Apply operator
    const operator = requirement.operator || 'gte'
    const target = requirement.target
    
    switch (operator) {
      case 'gte':
        return Math.min(100, (currentValue / target) * 100)
      case 'lte':
        return currentValue <= target ? 100 : 0
      case 'eq':
        return currentValue === target ? 100 : 0
      case 'between':
        const [min, max] = Array.isArray(target) ? target : [0, target]
        return (currentValue >= min && currentValue <= max) ? 100 : 0
      default:
        return 0
    }
  }

  /**
   * Evaluate complex conditions (AND, OR, NOT)
   */
  private static async evaluateConditions(
    condition: AchievementCondition,
    userStats: Record<string, any>,
    activity: UserActivity
  ): Promise<number> {
    if (condition.requirements) {
      const results = await Promise.all(
        condition.requirements.map(req => 
          this.evaluateRequirement(req, userStats, activity)
        )
      )
      
      switch (condition.type) {
        case 'and':
          return results.every(r => r >= 100) ? 100 : 0
        case 'or':
          return results.some(r => r >= 100) ? 100 : 0
        case 'not':
          return results.every(r => r < 100) ? 100 : 0
      }
    }
    
    if (condition.conditions) {
      const results = await Promise.all(
        condition.conditions.map(c => 
          this.evaluateConditions(c, userStats, activity)
        )
      )
      
      switch (condition.type) {
        case 'and':
          return results.every(r => r >= 100) ? 100 : 0
        case 'or':
          return results.some(r => r >= 100) ? 100 : 0
        case 'not':
          return results.every(r => r < 100) ? 100 : 0
      }
    }
    
    return 0
  }

  /**
   * Update achievement progress for a user
   */
  private static async updateAchievementProgress(
    userId: string,
    achievementId: string,
    progress: number,
    triggerEvent: string
  ): Promise<void> {
    try {
      await runTransaction(db, async (transaction) => {
        // Check for existing progress record
        const progressQuery = query(
          collection(db, collections.userAchievements),
          where('userId', '==', userId),
          where('achievementId', '==', achievementId),
          limit(1)
        )
        
        const progressSnapshot = await getDocs(progressQuery)
        
        const progressSnapshot_data = {
          timestamp: serverTimestamp(),
          progress,
          triggerEvent,
          metadata: { updatedAt: new Date().toISOString() }
        }
        
        if (progressSnapshot.empty) {
          // Create new progress record
          const progressRef = doc(collection(db, collections.userAchievements))
          transaction.set(progressRef, {
            userId,
            achievementId,
            progress,
            isCompleted: progress >= 100,
            progressHistory: [progressSnapshot_data],
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          })
        } else {
          // Update existing progress
          const existingDoc = progressSnapshot.docs[0]
          const existingData = existingDoc.data()
          
          if (progress > (existingData.progress || 0)) {
            transaction.update(existingDoc.ref, {
              progress,
              isCompleted: progress >= 100,
              progressHistory: [
                ...(existingData.progressHistory || []),
                progressSnapshot_data
              ],
              updatedAt: serverTimestamp()
            })
          }
        }
      })
    } catch (error) {
      console.error('Error updating achievement progress:', error)
    }
  }

  /**
   * Unlock an achievement for a user
   */
  private static async unlockAchievement(
    userId: string,
    achievementId: string,
    triggerActivity: UserActivity
  ): Promise<boolean> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get achievement details
        const achievementRef = doc(db, collections.achievements, achievementId)
        const achievementDoc = await transaction.get(achievementRef)
        
        if (!achievementDoc.exists()) {
          throw new Error('Achievement not found')
        }
        
        const achievement = achievementDoc.data() as Achievement
        
        // Check if already unlocked
        const existingQuery = query(
          collection(db, collections.userAchievements),
          where('userId', '==', userId),
          where('achievementId', '==', achievementId),
          where('isCompleted', '==', true),
          limit(1)
        )
        
        const existingSnapshot = await getDocs(existingQuery)
        if (!existingSnapshot.empty) {
          return false // Already unlocked
        }
        
        // Update or create achievement record
        const progressQuery = query(
          collection(db, collections.userAchievements),
          where('userId', '==', userId),
          where('achievementId', '==', achievementId),
          limit(1)
        )
        
        const progressSnapshot = await getDocs(progressQuery)
        
        if (!progressSnapshot.empty) {
          // Update existing record
          const progressDoc = progressSnapshot.docs[0]
          transaction.update(progressDoc.ref, {
            progress: 100,
            isCompleted: true,
            unlockedAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          })
        } else {
          // Create new record
          const progressRef = doc(collection(db, collections.userAchievements))
          transaction.set(progressRef, {
            userId,
            achievementId,
            progress: 100,
            isCompleted: true,
            unlockedAt: serverTimestamp(),
            progressHistory: [{
              timestamp: serverTimestamp(),
              progress: 100,
              triggerEvent: 'achievement_unlocked',
              metadata: { triggerActivity: triggerActivity.type }
            }],
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          })
        }
        
        // Award points
        if (achievement.rewards.points > 0) {
          await SecurePointSystem.awardPoints(
            userId,
            achievement.rewards.points,
            'achievement_unlock',
            `Achievement unlocked: ${achievement.title}`,
            {
              achievementId,
              achievementTitle: achievement.title,
              achievementRarity: achievement.rarity
            }
          )
        }
        
        // Log achievement unlock
        await AuditLogger.logOperation('achievement_unlocked', userId, {
          achievementId,
          achievementTitle: achievement.title,
          achievementRarity: achievement.rarity,
          pointsAwarded: achievement.rewards.points,
          triggerActivity: triggerActivity.type
        })
        
        return true
      })
    } catch (error) {
      console.error('Error unlocking achievement:', error)
      return false
    }
  }

  // ===== HELPER METHODS =====

  private static async getActiveAchievements(): Promise<Achievement[]> {
    return await gamificationCache.getOrSet(
      CacheKeys.achievementCatalog(),
      async () => {
        const q = query(
          collection(db, collections.achievements),
          where('isActive', '==', true),
          orderBy('sortOrder', 'asc')
        )
        
        const snapshot = await getDocs(q)
        return snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Achievement[]
      },
      15 * 60 * 1000, // 15 minutes
      ['achievement-catalog']
    )
  }

  private static async getUserAchievements(userId: string): Promise<UserAchievement[]> {
    return await gamificationCache.getOrSet(
      CacheKeys.userAchievements(userId),
      async () => {
        const q = query(
          collection(db, collections.userAchievements),
          where('userId', '==', userId),
          orderBy('updatedAt', 'desc')
        )
        
        const snapshot = await getDocs(q)
        return snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as UserAchievement[]
      },
      5 * 60 * 1000, // 5 minutes
      ['user-achievements']
    )
  }

  private static async getUserStatistics(userId: string): Promise<Record<string, any>> {
    try {
      const userRef = doc(db, collections.users, userId)
      const userDoc = await getDoc(userRef)
      
      if (!userDoc.exists()) {
        return {}
      }
      
      const userData = userDoc.data()
      return {
        userId,
        totalPointsEarned: userData.gamification?.totalPointsEarned || 0,
        currentLevel: userData.gamification?.level || 1,
        currentTier: userData.gamification?.tier || 'bronze',
        challengesCompleted: userData.stats?.challengesCompleted || 0,
        submissionsCount: userData.stats?.submissionsCount || 0,
        communityPosts: userData.stats?.communityPosts || 0,
        socialShares: userData.stats?.socialShares || 0,
        referralsCompleted: userData.stats?.referralsCompleted || 0,
        purchasesMade: userData.stats?.purchasesMade || 0,
        rewardsRedeemed: userData.stats?.rewardsRedeemed || 0,
        profileCompletion: userData.profileCompletion || 0,
        currentLoginStreak: userData.stats?.currentLoginStreak || 0,
        consecutiveDaysActive: userData.stats?.consecutiveDaysActive || 0
      }
    } catch (error) {
      console.error('Error getting user statistics:', error)
      return {}
    }
  }

  private static async getPointsInTimeframe(userId: string, days: number): Promise<number> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)
      
      const q = query(
        collection(db, collections.pointTransactions),
        where('userId', '==', userId),
        where('type', '==', 'points_earned'),
        where('timestamp', '>=', cutoffDate)
      )
      
      const snapshot = await getDocs(q)
      return snapshot.docs.reduce((total, doc) => {
        return total + (doc.data().amount || 0)
      }, 0)
    } catch (error) {
      console.error('Error getting points in timeframe:', error)
      return 0
    }
  }

  private static getTierNumericValue(tier: string): number {
    const tierValues: Record<string, number> = {
      bronze: 1,
      silver: 2,
      gold: 3,
      platinum: 4
    }
    return tierValues[tier] || 1
  }

  // ===== PUBLIC API =====

  /**
   * Initialize default achievements
   */
  static async initializeDefaultAchievements(): Promise<void> {
    try {
      const defaultAchievements = [
        AchievementTemplates.firstSteps(),
        AchievementTemplates.pointCollector(100),
        AchievementTemplates.pointCollector(500),
        AchievementTemplates.pointCollector(1000, 'uncommon'),
        AchievementTemplates.pointCollector(5000, 'rare'),
        AchievementTemplates.streakMaster(3),
        AchievementTemplates.streakMaster(7),
        AchievementTemplates.streakMaster(30),
        AchievementTemplates.socialButterfly(5),
        AchievementTemplates.socialButterfly(25),
        AchievementTemplates.challengeChampion(1),
        AchievementTemplates.challengeChampion(5),
        AchievementTemplates.challengeChampion(20),
        AchievementTemplates.powerUser()
      ]
      
      for (const achievement of defaultAchievements) {
        await addDoc(collection(db, collections.achievements), {
          ...achievement,
          createdAt: serverTimestamp()
        })
      }
      
      // Clear cache to reload new achievements
      await gamificationCache.invalidateByTags(['achievement-catalog'])
    } catch (error) {
      console.error('Error initializing default achievements:', error)
    }
  }

  /**
   * Manual achievement unlock (admin function)
   */
  static async manualUnlock(
    userId: string,
    achievementId: string,
    adminId: string,
    reason?: string
  ): Promise<boolean> {
    try {
      const activity: UserActivity = {
        userId,
        type: 'tier_promotion', // Generic activity type for manual unlock
        timestamp: new Date(),
        metadata: { adminId, reason, manual: true }
      }
      
      const unlocked = await this.unlockAchievement(userId, achievementId, activity)
      
      if (unlocked) {
        await AuditLogger.logOperation('manual_achievement_unlock', userId, {
          achievementId,
          adminId,
          reason: reason || 'Manual unlock'
        }, adminId)
      }
      
      return unlocked
    } catch (error) {
      console.error('Error manually unlocking achievement:', error)
      return false
    }
  }

  /**
   * Get achievement leaderboard
   */
  static async getAchievementLeaderboard(limit: number = 10): Promise<Array<{
    userId: string
    username: string
    achievementCount: number
    rareAchievements: number
    totalPoints: number
  }>> {
    try {
      // This would require aggregation - for now return empty array
      // In production, this should use a materialized view or aggregation function
      return []
    } catch (error) {
      console.error('Error getting achievement leaderboard:', error)
      return []
    }
  }
}

export default AchievementEngine