/**
 * Admin Authorization System for Gamification
 * 
 * Secure role-based access control for all gamification administrative operations.
 * Implements principle of least privilege with comprehensive audit logging.
 * 
 * <AUTHOR> Team - Phase 1 Security Improvements
 * @version 2.0.0
 */

import {
  doc,
  getDoc,
  updateDoc,
  serverTimestamp,
  arrayUnion,
  arrayRemove,
  Timestamp
} from 'firebase/firestore'
import { db } from '../../firebase'
import { collections } from '../../firestore'
import { AuditLogger } from './secureTransactions'

// ===== TYPES =====

export interface AdminRole {
  id: string
  userId: string
  email: string
  displayName: string
  roles: AdminRoleType[]
  permissions: AdminPermission[]
  status: 'active' | 'suspended' | 'revoked'
  createdAt: Timestamp
  createdBy: string
  lastLogin?: Timestamp
  loginCount: number
  metadata: {
    department?: string
    notes?: string
    accessLevel: 'read' | 'write' | 'admin' | 'super'
  }
}

export type AdminRoleType = 
  | 'gamification_admin'
  | 'content_moderator'
  | 'analytics_viewer'
  | 'reward_manager'
  | 'achievement_manager'
  | 'support_admin'
  | 'super_admin'

export type AdminPermission = 
  // Point Management
  | 'gamification.award_points'
  | 'gamification.spend_points'
  | 'gamification.adjust_balance'
  | 'gamification.repair_balance'
  | 'gamification.view_transactions'
  
  // Reward Management
  | 'gamification.create_rewards'
  | 'gamification.edit_rewards'
  | 'gamification.delete_rewards'
  | 'gamification.manage_stock'
  | 'gamification.view_purchases'
  | 'gamification.fulfill_orders'
  
  // Achievement Management
  | 'gamification.create_achievements'
  | 'gamification.edit_achievements'
  | 'gamification.delete_achievements'
  | 'gamification.unlock_achievements'
  | 'gamification.view_progress'
  
  // Challenge Management
  | 'gamification.create_challenges'
  | 'gamification.edit_challenges'
  | 'gamification.delete_challenges'
  | 'gamification.moderate_submissions'
  
  // Analytics & Reporting
  | 'gamification.view_analytics'
  | 'gamification.export_data'
  | 'gamification.generate_reports'
  
  // System Administration
  | 'gamification.manage_admins'
  | 'gamification.view_audit_logs'
  | 'gamification.system_settings'
  | 'gamification.*' // Wildcard permission

export interface PermissionCheckResult {
  hasPermission: boolean
  reason?: string
  adminRole?: AdminRole
  auditId?: string
}

export interface AdminActionContext {
  adminId: string
  action: string
  targetUserId?: string
  targetResourceId?: string
  ipAddress?: string
  userAgent?: string
  sessionId?: string
  metadata?: Record<string, any>
}

// ===== ROLE DEFINITIONS =====

const ROLE_PERMISSIONS: Record<AdminRoleType, AdminPermission[]> = {
  gamification_admin: [
    'gamification.award_points',
    'gamification.spend_points',
    'gamification.adjust_balance',
    'gamification.view_transactions',
    'gamification.create_rewards',
    'gamification.edit_rewards',
    'gamification.manage_stock',
    'gamification.view_purchases',
    'gamification.create_achievements',
    'gamification.edit_achievements',
    'gamification.unlock_achievements',
    'gamification.view_progress',
    'gamification.view_analytics'
  ],
  
  content_moderator: [
    'gamification.moderate_submissions',
    'gamification.edit_challenges',
    'gamification.view_progress'
  ],
  
  analytics_viewer: [
    'gamification.view_analytics',
    'gamification.view_transactions',
    'gamification.view_purchases',
    'gamification.generate_reports'
  ],
  
  reward_manager: [
    'gamification.create_rewards',
    'gamification.edit_rewards',
    'gamification.delete_rewards',
    'gamification.manage_stock',
    'gamification.view_purchases',
    'gamification.fulfill_orders'
  ],
  
  achievement_manager: [
    'gamification.create_achievements',
    'gamification.edit_achievements',
    'gamification.delete_achievements',
    'gamification.unlock_achievements',
    'gamification.view_progress'
  ],
  
  support_admin: [
    'gamification.award_points',
    'gamification.spend_points',
    'gamification.view_transactions',
    'gamification.unlock_achievements',
    'gamification.fulfill_orders'
  ],
  
  super_admin: [
    'gamification.*'
  ]
}

// ===== ADMIN AUTHORIZATION SERVICE =====

export class AdminAuthService {
  /**
   * Check if admin has required permission for an operation
   */
  static async checkPermission(
    adminId: string,
    permission: AdminPermission,
    context?: AdminActionContext
  ): Promise<PermissionCheckResult> {
    try {
      // Get admin role data
      const adminRole = await this.getAdminRole(adminId)
      
      if (!adminRole) {
        const auditId = await AuditLogger.logOperation('permission_denied', adminId, {
          permission,
          reason: 'Admin not found',
          context
        })
        
        return {
          hasPermission: false,
          reason: 'Admin role not found',
          auditId
        }
      }

      // Check if admin account is active
      if (adminRole.status !== 'active') {
        const auditId = await AuditLogger.logOperation('permission_denied', adminId, {
          permission,
          reason: `Admin status: ${adminRole.status}`,
          context
        })
        
        return {
          hasPermission: false,
          reason: `Admin account is ${adminRole.status}`,
          adminRole,
          auditId
        }
      }

      // Check specific permission or wildcard
      const hasPermission = adminRole.permissions.includes(permission) || 
                           adminRole.permissions.includes('gamification.*')

      if (!hasPermission) {
        const auditId = await AuditLogger.logOperation('permission_denied', adminId, {
          permission,
          reason: 'Insufficient permissions',
          availablePermissions: adminRole.permissions,
          context
        })
        
        return {
          hasPermission: false,
          reason: `Missing permission: ${permission}`,
          adminRole,
          auditId
        }
      }

      // Log successful permission check
      const auditId = await AuditLogger.logOperation('permission_granted', adminId, {
        permission,
        context
      })

      // Update last activity
      await this.updateLastActivity(adminId)

      return {
        hasPermission: true,
        adminRole,
        auditId
      }
    } catch (error) {
      console.error('Error checking admin permission:', error)
      return {
        hasPermission: false,
        reason: 'Permission check failed'
      }
    }
  }

  /**
   * Get admin role information
   */
  static async getAdminRole(adminId: string): Promise<AdminRole | null> {
    try {
      const adminRef = doc(db, collections.admins, adminId)
      const adminDoc = await getDoc(adminRef)

      if (!adminDoc.exists()) {
        return null
      }

      return {
        id: adminDoc.id,
        ...adminDoc.data()
      } as AdminRole
    } catch (error) {
      console.error('Error getting admin role:', error)
      return null
    }
  }

  /**
   * Create new admin role
   */
  static async createAdminRole(
    creatorAdminId: string,
    newAdminData: {
      userId: string
      email: string
      displayName: string
      roles: AdminRoleType[]
      metadata?: {
        department?: string
        notes?: string
        accessLevel: 'read' | 'write' | 'admin' | 'super'
      }
    }
  ): Promise<{ success: boolean; adminId?: string; error?: string }> {
    try {
      // Check creator permissions
      const permissionCheck = await this.checkPermission(creatorAdminId, 'gamification.manage_admins')
      
      if (!permissionCheck.hasPermission) {
        return {
          success: false,
          error: permissionCheck.reason || 'Insufficient permissions'
        }
      }

      // Calculate permissions from roles
      const permissions = this.calculatePermissionsFromRoles(newAdminData.roles)

      // Create admin record
      const adminData: Omit<AdminRole, 'id'> = {
        userId: newAdminData.userId,
        email: newAdminData.email,
        displayName: newAdminData.displayName,
        roles: newAdminData.roles,
        permissions,
        status: 'active',
        createdAt: serverTimestamp() as Timestamp,
        createdBy: creatorAdminId,
        loginCount: 0,
        metadata: {
          accessLevel: 'read',
          ...newAdminData.metadata
        }
      }

      const adminRef = doc(db, collections.admins, newAdminData.userId)
      await updateDoc(adminRef, adminData)

      // Log admin creation
      await AuditLogger.logOperation('admin_created', newAdminData.userId, {
        roles: newAdminData.roles,
        permissions,
        createdBy: creatorAdminId,
        metadata: newAdminData.metadata
      }, creatorAdminId)

      return {
        success: true,
        adminId: newAdminData.userId
      }
    } catch (error) {
      console.error('Error creating admin role:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create admin role'
      }
    }
  }

  /**
   * Update admin permissions
   */
  static async updateAdminPermissions(
    adminId: string,
    targetAdminId: string,
    newRoles: AdminRoleType[],
    reason: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Check permissions
      const permissionCheck = await this.checkPermission(adminId, 'gamification.manage_admins')
      
      if (!permissionCheck.hasPermission) {
        return {
          success: false,
          error: permissionCheck.reason || 'Insufficient permissions'
        }
      }

      // Prevent self-modification of super admin
      if (adminId === targetAdminId && permissionCheck.adminRole?.roles.includes('super_admin')) {
        return {
          success: false,
          error: 'Cannot modify own super admin permissions'
        }
      }

      // Calculate new permissions
      const newPermissions = this.calculatePermissionsFromRoles(newRoles)

      // Update admin record
      const adminRef = doc(db, collections.admins, targetAdminId)
      await updateDoc(adminRef, {
        roles: newRoles,
        permissions: newPermissions,
        updatedAt: serverTimestamp(),
        updatedBy: adminId
      })

      // Log permission change
      await AuditLogger.logOperation('admin_permissions_updated', targetAdminId, {
        oldRoles: permissionCheck.adminRole?.roles || [],
        newRoles,
        oldPermissions: permissionCheck.adminRole?.permissions || [],
        newPermissions,
        reason,
        updatedBy: adminId
      }, adminId)

      return { success: true }
    } catch (error) {
      console.error('Error updating admin permissions:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update permissions'
      }
    }
  }

  /**
   * Suspend or reactivate admin account
   */
  static async updateAdminStatus(
    adminId: string,
    targetAdminId: string,
    newStatus: 'active' | 'suspended' | 'revoked',
    reason: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Check permissions
      const permissionCheck = await this.checkPermission(adminId, 'gamification.manage_admins')
      
      if (!permissionCheck.hasPermission) {
        return {
          success: false,
          error: permissionCheck.reason || 'Insufficient permissions'
        }
      }

      // Prevent self-suspension of super admin
      if (adminId === targetAdminId && permissionCheck.adminRole?.roles.includes('super_admin')) {
        return {
          success: false,
          error: 'Cannot modify own super admin status'
        }
      }

      // Update admin status
      const adminRef = doc(db, collections.admins, targetAdminId)
      await updateDoc(adminRef, {
        status: newStatus,
        statusUpdatedAt: serverTimestamp(),
        statusUpdatedBy: adminId,
        statusReason: reason
      })

      // Log status change
      await AuditLogger.logOperation('admin_status_updated', targetAdminId, {
        newStatus,
        reason,
        updatedBy: adminId
      }, adminId)

      return { success: true }
    } catch (error) {
      console.error('Error updating admin status:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update status'
      }
    }
  }

  /**
   * Update admin last activity
   */
  private static async updateLastActivity(adminId: string): Promise<void> {
    try {
      const adminRef = doc(db, collections.admins, adminId)
      await updateDoc(adminRef, {
        lastLogin: serverTimestamp(),
        loginCount: arrayUnion(1) // This will increment the count
      })
    } catch (error) {
      // Don't fail operations if activity update fails
      console.warn('Failed to update admin activity:', error)
    }
  }

  /**
   * Calculate permissions from roles
   */
  private static calculatePermissionsFromRoles(roles: AdminRoleType[]): AdminPermission[] {
    const permissions = new Set<AdminPermission>()

    for (const role of roles) {
      const rolePermissions = ROLE_PERMISSIONS[role] || []
      rolePermissions.forEach(permission => permissions.add(permission))
    }

    return Array.from(permissions)
  }

  /**
   * Validate admin action with context
   */
  static async validateAdminAction(
    adminId: string,
    permission: AdminPermission,
    action: string,
    context: AdminActionContext
  ): Promise<PermissionCheckResult> {
    const result = await this.checkPermission(adminId, permission, context)

    if (result.hasPermission) {
      // Log the admin action
      await AuditLogger.logOperation(`admin_action_${action}`, context.targetUserId || adminId, {
        action,
        adminId,
        permission,
        context
      }, adminId)
    }

    return result
  }

  /**
   * Get admin activity summary
   */
  static async getAdminActivitySummary(adminId: string): Promise<{
    recentActions: any[]
    permissionDenials: number
    lastLogin?: Date
    totalActions: number
  }> {
    try {
      const adminRole = await this.getAdminRole(adminId)
      
      if (!adminRole) {
        throw new Error('Admin not found')
      }

      // This would typically query audit logs for recent activity
      // For now, return basic info from admin record
      return {
        recentActions: [], // Would be populated from audit logs
        permissionDenials: 0, // Would be calculated from audit logs
        lastLogin: adminRole.lastLogin?.toDate(),
        totalActions: adminRole.loginCount || 0
      }
    } catch (error) {
      console.error('Error getting admin activity summary:', error)
      throw error
    }
  }
}

// ===== MIDDLEWARE HELPER =====

/**
 * Middleware function for protecting admin routes
 */
export async function requireAdminPermission(
  adminId: string,
  permission: AdminPermission,
  context?: Partial<AdminActionContext>
) {
  const result = await AdminAuthService.checkPermission(adminId, permission, context as AdminActionContext)
  
  if (!result.hasPermission) {
    throw new Error(`Access denied: ${result.reason}`)
  }
  
  return result.adminRole
}

/**
 * Decorator for admin methods that require specific permissions
 */
export function RequirePermission(permission: AdminPermission) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const adminId = args[0] // Assume first argument is always adminId
      
      const permissionCheck = await AdminAuthService.checkPermission(adminId, permission)
      
      if (!permissionCheck.hasPermission) {
        throw new Error(`Access denied: ${permissionCheck.reason}`)
      }

      return method.apply(this, args)
    }
  }
}

export default AdminAuthService