/**
 * Gamification Caching Service
 * 
 * Multi-level caching system for gamification data with automatic invalidation
 * and performance monitoring. Provides both memory and browser storage caching.
 * 
 * <AUTHOR> Team - Phase 1 Security Improvements
 * @version 2.0.0
 */

// ===== TYPES =====

interface CacheEntry<T> {
  data: T
  timestamp: number
  expires: number
  version: number
  tags: string[]
}

interface CacheConfig {
  defaultTTL: number
  maxMemoryEntries: number
  enableBrowserStorage: boolean
  enableCompression: boolean
  version: number
}

interface CacheStats {
  hits: number
  misses: number
  invalidations: number
  memoryUsage: number
  totalRequests: number
  hitRate: number
}

type CacheTag = 
  | 'user-points'
  | 'user-achievements' 
  | 'user-rewards'
  | 'global-leaderboard'
  | 'reward-catalog'
  | 'achievement-catalog'
  | 'user-profile'

// ===== CACHE IMPLEMENTATION =====

class GamificationCache {
  private memoryCache = new Map<string, CacheEntry<any>>()
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    invalidations: 0,
    memoryUsage: 0,
    totalRequests: 0,
    hitRate: 0
  }

  private config: CacheConfig = {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxMemoryEntries: 1000,
    enableBrowserStorage: true,
    enableCompression: false,
    version: 1
  }

  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(config?: Partial<CacheConfig>) {
    if (config) {
      this.config = { ...this.config, ...config }
    }

    this.startCleanupInterval()
  }

  /**
   * Get cached data
   */
  async get<T>(key: string): Promise<T | null> {
    this.stats.totalRequests++

    try {
      // Check memory cache first
      const memoryEntry = this.memoryCache.get(key)
      if (memoryEntry && memoryEntry.expires > Date.now()) {
        this.stats.hits++
        this.updateHitRate()
        return memoryEntry.data as T
      }

      // Check browser storage if enabled
      if (this.config.enableBrowserStorage && typeof window !== 'undefined') {
        const browserEntry = await this.getBrowserEntry<T>(key)
        if (browserEntry) {
          // Restore to memory cache
          this.memoryCache.set(key, browserEntry)
          this.stats.hits++
          this.updateHitRate()
          return browserEntry.data
        }
      }

      this.stats.misses++
      this.updateHitRate()
      return null
    } catch (error) {
      console.warn('Cache get error:', error)
      this.stats.misses++
      this.updateHitRate()
      return null
    }
  }

  /**
   * Set cached data
   */
  async set<T>(
    key: string, 
    data: T, 
    ttl?: number, 
    tags: CacheTag[] = []
  ): Promise<void> {
    try {
      const expires = Date.now() + (ttl || this.config.defaultTTL)
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        expires,
        version: this.config.version,
        tags
      }

      // Set in memory cache
      this.memoryCache.set(key, entry)

      // Enforce memory limit
      this.enforceMemoryLimit()

      // Set in browser storage if enabled
      if (this.config.enableBrowserStorage && typeof window !== 'undefined') {
        await this.setBrowserEntry(key, entry)
      }
    } catch (error) {
      console.warn('Cache set error:', error)
    }
  }

  /**
   * Invalidate cached data by key
   */
  async invalidate(key: string): Promise<void> {
    try {
      this.memoryCache.delete(key)
      this.stats.invalidations++

      if (this.config.enableBrowserStorage && typeof window !== 'undefined') {
        localStorage.removeItem(this.getCacheKey(key))
      }
    } catch (error) {
      console.warn('Cache invalidate error:', error)
    }
  }

  /**
   * Invalidate cached data by tags
   */
  async invalidateByTags(tags: CacheTag[]): Promise<void> {
    try {
      const keysToDelete: string[] = []

      // Find keys in memory cache
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.tags.some(tag => tags.includes(tag as CacheTag))) {
          keysToDelete.push(key)
        }
      }

      // Delete from memory
      keysToDelete.forEach(key => {
        this.memoryCache.delete(key)
        this.stats.invalidations++
      })

      // Delete from browser storage
      if (this.config.enableBrowserStorage && typeof window !== 'undefined') {
        for (let i = 0; i < localStorage.length; i++) {
          const storageKey = localStorage.key(i)
          if (storageKey?.startsWith('gamification_cache_')) {
            try {
              const entry = JSON.parse(localStorage.getItem(storageKey) || '{}')
              if (entry.tags?.some((tag: string) => tags.includes(tag as CacheTag))) {
                localStorage.removeItem(storageKey)
              }
            } catch (error) {
              // Invalid JSON, remove it
              localStorage.removeItem(storageKey)
            }
          }
        }
      }
    } catch (error) {
      console.warn('Cache invalidate by tags error:', error)
    }
  }

  /**
   * Clear all cached data
   */
  async clear(): Promise<void> {
    try {
      this.memoryCache.clear()
      this.stats.invalidations = this.stats.totalRequests

      if (this.config.enableBrowserStorage && typeof window !== 'undefined') {
        const keysToRemove: string[] = []
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key?.startsWith('gamification_cache_')) {
            keysToRemove.push(key)
          }
        }

        keysToRemove.forEach(key => localStorage.removeItem(key))
      }
    } catch (error) {
      console.warn('Cache clear error:', error)
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.stats.memoryUsage = this.calculateMemoryUsage()
    return { ...this.stats }
  }

  /**
   * Get or set with automatic caching
   */
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number,
    tags: CacheTag[] = []
  ): Promise<T> {
    const cached = await this.get<T>(key)
    if (cached !== null) {
      return cached
    }

    const data = await fetcher()
    await this.set(key, data, ttl, tags)
    return data
  }

  /**
   * Warm up cache with predefined data
   */
  async warmUp(entries: Array<{
    key: string
    fetcher: () => Promise<any>
    ttl?: number
    tags?: CacheTag[]
  }>): Promise<void> {
    try {
      const promises = entries.map(async (entry) => {
        try {
          const data = await entry.fetcher()
          await this.set(entry.key, data, entry.ttl, entry.tags || [])
        } catch (error) {
          console.warn(`Failed to warm up cache for key ${entry.key}:`, error)
        }
      })

      await Promise.all(promises)
    } catch (error) {
      console.warn('Cache warm up error:', error)
    }
  }

  // ===== PRIVATE METHODS =====

  private getCacheKey(key: string): string {
    return `gamification_cache_${this.config.version}_${key}`
  }

  private async getBrowserEntry<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      const storageKey = this.getCacheKey(key)
      const stored = localStorage.getItem(storageKey)
      
      if (!stored) {
        return null
      }

      const entry: CacheEntry<T> = JSON.parse(stored)
      
      // Check expiration and version
      if (entry.expires < Date.now() || entry.version !== this.config.version) {
        localStorage.removeItem(storageKey)
        return null
      }

      return entry
    } catch (error) {
      return null
    }
  }

  private async setBrowserEntry<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    try {
      const storageKey = this.getCacheKey(key)
      const serialized = JSON.stringify(entry)
      
      // Check storage quota
      if (serialized.length > 1024 * 1024) { // 1MB limit per entry
        console.warn(`Cache entry too large for browser storage: ${key}`)
        return
      }

      localStorage.setItem(storageKey, serialized)
    } catch (error) {
      if (error instanceof DOMException && error.code === 22) {
        // Storage quota exceeded, clear some old entries
        await this.clearOldBrowserEntries()
        
        try {
          localStorage.setItem(this.getCacheKey(key), JSON.stringify(entry))
        } catch (secondError) {
          console.warn('Still unable to store in browser cache:', secondError)
        }
      }
    }
  }

  private async clearOldBrowserEntries(): Promise<void> {
    try {
      const entries: Array<{ key: string; timestamp: number }> = []
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith('gamification_cache_')) {
          try {
            const entry = JSON.parse(localStorage.getItem(key) || '{}')
            entries.push({ key, timestamp: entry.timestamp || 0 })
          } catch (error) {
            // Invalid entry, mark for deletion
            entries.push({ key, timestamp: 0 })
          }
        }
      }

      // Sort by timestamp and remove oldest 25%
      entries.sort((a, b) => a.timestamp - b.timestamp)
      const toRemove = entries.slice(0, Math.floor(entries.length * 0.25))
      
      toRemove.forEach(({ key }) => localStorage.removeItem(key))
    } catch (error) {
      console.warn('Error clearing old browser entries:', error)
    }
  }

  private enforceMemoryLimit(): void {
    if (this.memoryCache.size <= this.config.maxMemoryEntries) {
      return
    }

    // Remove oldest entries
    const entries = Array.from(this.memoryCache.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
    
    const toRemove = entries.slice(0, entries.length - this.config.maxMemoryEntries)
    toRemove.forEach(([key]) => this.memoryCache.delete(key))
  }

  private calculateMemoryUsage(): number {
    let usage = 0
    for (const entry of this.memoryCache.values()) {
      // Rough estimation of memory usage
      usage += JSON.stringify(entry).length * 2 // UTF-16 characters
    }
    return usage
  }

  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests) * 100 
      : 0
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries()
    }, 60000) // Run every minute

    // Cleanup on browser unload
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup()
      })
    }
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expires < now) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.memoryCache.delete(key))
  }

  private cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
  }
}

// ===== SINGLETON INSTANCE =====

export const gamificationCache = new GamificationCache({
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxMemoryEntries: 500,
  enableBrowserStorage: true,
  enableCompression: false,
  version: 1
})

// ===== CACHE KEY GENERATORS =====

export const CacheKeys = {
  userPoints: (userId: string) => `user_points_${userId}`,
  userHistory: (userId: string, limit: number) => `user_history_${userId}_${limit}`,
  userStatistics: (userId: string) => `user_stats_${userId}`,
  userAchievements: (userId: string) => `user_achievements_${userId}`,
  rewardCatalog: () => `reward_catalog`,
  achievementCatalog: () => `achievement_catalog`,
  leaderboard: (type: string, limit: number) => `leaderboard_${type}_${limit}`,
  userRewards: (userId: string) => `user_rewards_${userId}`
}

// ===== CACHE HELPERS =====

export const CacheHelpers = {
  invalidateUserData: async (userId: string) => {
    await gamificationCache.invalidateByTags(['user-points', 'user-achievements', 'user-rewards'])
    await gamificationCache.invalidate(CacheKeys.userPoints(userId))
    await gamificationCache.invalidate(CacheKeys.userStatistics(userId))
    await gamificationCache.invalidate(CacheKeys.userAchievements(userId))
    await gamificationCache.invalidate(CacheKeys.userRewards(userId))
  },

  invalidateGlobalData: async () => {
    await gamificationCache.invalidateByTags(['global-leaderboard', 'reward-catalog', 'achievement-catalog'])
  },

  warmUpUserCache: async (userId: string, services: {
    getBalance: () => Promise<any>
    getHistory: () => Promise<any>
    getStatistics: () => Promise<any>
  }) => {
    await gamificationCache.warmUp([
      {
        key: CacheKeys.userPoints(userId),
        fetcher: services.getBalance,
        ttl: 2 * 60 * 1000, // 2 minutes for points (more frequent updates)
        tags: ['user-points']
      },
      {
        key: CacheKeys.userHistory(userId, 20),
        fetcher: services.getHistory,
        ttl: 5 * 60 * 1000, // 5 minutes for history
        tags: ['user-points']
      },
      {
        key: CacheKeys.userStatistics(userId),
        fetcher: services.getStatistics,
        ttl: 10 * 60 * 1000, // 10 minutes for statistics
        tags: ['user-points']
      }
    ])
  }
}

export default gamificationCache