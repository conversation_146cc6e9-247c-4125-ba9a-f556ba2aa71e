/**
 * Challenge System - Complete Backend Implementation
 * 
 * Comprehensive challenge management system with team collaboration,
 * real-time progress tracking, and automated reward distribution.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  runTransaction,
  onSnapshot,
  Timestamp,
  QueryDocumentSnapshot,
  DocumentData,
  Unsubscribe
} from 'firebase/firestore'
import { db } from '../../firebase'
import { collections } from '../../firebase/gamificationCollections'
import { SecurePointSystem, AuditLogger } from './secureTransactions'
import { AdminAuthService } from './adminAuth'
import { gamificationCache, CacheKeys } from './cachingService'

// ===== TYPES =====

export interface Challenge {
  id?: string
  title: string
  description: string
  longDescription?: string
  category: ChallengeCategory
  type: ChallengeType
  difficulty: ChallengeDifficulty
  status: ChallengeStatus
  
  // Participation
  maxParticipants?: number
  currentParticipants: number
  teamSize?: number // For team challenges
  allowLatejoin: boolean
  
  // Timing
  startDate: Timestamp
  endDate: Timestamp
  registrationEndDate?: Timestamp
  timeZone: string
  
  // Requirements
  requirements: ChallengeRequirement[]
  prerequisites?: string[] // Challenge IDs that must be completed first
  
  // Rewards
  rewards: ChallengeReward
  progressMilestones?: ChallengeMilestone[]
  
  // Metadata
  tags: string[]
  featuredImage?: string
  icon: string
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
  
  // Configuration
  settings: ChallengeSettings
  metadata?: Record<string, any>
}

export interface ChallengeRequirement {
  type: RequirementType
  target: number
  description: string
  metadata?: Record<string, any>
}

export interface ChallengeReward {
  points: number
  badges?: string[]
  achievements?: string[]
  specialRewards?: {
    type: 'tier_boost' | 'discount' | 'access' | 'item' | 'custom'
    value: any
    description: string
  }[]
  teamBonus?: number // Additional points for team completion
}

export interface ChallengeMilestone {
  id: string
  name: string
  description: string
  threshold: number // Percentage of completion (0-100)
  rewards: ChallengeReward
  icon?: string
}

export interface ChallengeSettings {
  isPublic: boolean
  requiresApproval: boolean
  allowTeams: boolean
  autoStart: boolean
  notifyParticipants: boolean
  trackingMethod: 'manual' | 'automatic' | 'hybrid'
  verificationRequired: boolean
  maxSubmissionsPerUser?: number
}

export interface ChallengeParticipation {
  id?: string
  challengeId: string
  userId: string
  teamId?: string
  
  // Status
  status: ParticipationStatus
  joinedAt: Timestamp
  completedAt?: Timestamp
  lastActiveAt: Timestamp
  
  // Progress
  progress: number // 0-100
  currentValue: number
  submissions: ChallengeSubmission[]
  milestones: string[] // Milestone IDs achieved
  
  // Team data (if applicable)
  role?: TeamRole
  
  // Metadata
  metadata?: Record<string, any>
}

export interface ChallengeTeam {
  id?: string
  challengeId: string
  name: string
  description?: string
  captainId: string
  members: TeamMember[]
  
  // Status
  status: TeamStatus
  createdAt: Timestamp
  
  // Progress (aggregate of members)
  progress: number
  totalValue: number
  
  // Settings
  isPrivate: boolean
  requiresApproval: boolean
  maxMembers: number
  
  metadata?: Record<string, any>
}

export interface TeamMember {
  userId: string
  role: TeamRole
  joinedAt: Timestamp
  contribution: number
  status: 'active' | 'inactive' | 'pending'
}

export interface ChallengeSubmission {
  id?: string
  challengeId: string
  userId: string
  teamId?: string
  
  // Content
  type: SubmissionType
  content: any // Flexible content based on submission type
  attachments?: FileAttachment[]
  
  // Status
  status: SubmissionStatus
  submittedAt: Timestamp
  reviewedAt?: Timestamp
  reviewedBy?: string
  
  // Verification
  verificationScore?: number
  verificationNotes?: string
  isVerified: boolean
  
  // Progress impact
  progressValue: number
  
  metadata?: Record<string, any>
}

export interface FileAttachment {
  id: string
  name: string
  url: string
  type: string
  size: number
  uploadedAt: Timestamp
}

export interface ChallengeAnalytics {
  challengeId: string
  
  // Participation metrics
  totalParticipants: number
  activeParticipants: number
  completionRate: number
  dropoutRate: number
  
  // Progress metrics
  averageProgress: number
  progressDistribution: { range: string; count: number }[]
  
  // Time metrics
  averageCompletionTime: number
  fastestCompletion: number
  slowestCompletion: number
  
  // Engagement metrics
  totalSubmissions: number
  averageSubmissionsPerUser: number
  dailyActivity: { date: string; participants: number; submissions: number }[]
  
  // Team metrics (if applicable)
  totalTeams?: number
  averageTeamSize?: number
  teamCompletionRate?: number
  
  lastUpdated: Timestamp
}

// Enums
export type ChallengeCategory = 
  | 'fitness'
  | 'learning'
  | 'creativity'
  | 'social'
  | 'productivity'
  | 'wellness'
  | 'community'
  | 'skill'
  | 'competition'

export type ChallengeType = 
  | 'individual'
  | 'team'
  | 'collaborative'
  | 'competitive'

export type ChallengeDifficulty = 
  | 'beginner'
  | 'intermediate'
  | 'advanced'
  | 'expert'

export type ChallengeStatus = 
  | 'draft'
  | 'scheduled'
  | 'active'
  | 'paused'
  | 'completed'
  | 'cancelled'
  | 'archived'

export type ParticipationStatus = 
  | 'registered'
  | 'active'
  | 'completed'
  | 'withdrawn'
  | 'disqualified'

export type TeamStatus = 
  | 'forming'
  | 'active'
  | 'completed'
  | 'disbanded'

export type TeamRole = 
  | 'captain'
  | 'member'
  | 'pending'

export type RequirementType = 
  | 'points_earned'
  | 'login_days'
  | 'submissions_count'
  | 'social_shares'
  | 'community_posts'
  | 'referrals'
  | 'custom_metric'

export type SubmissionType = 
  | 'text'
  | 'image'
  | 'video'
  | 'file'
  | 'link'
  | 'metric'
  | 'checkin'

export type SubmissionStatus = 
  | 'pending'
  | 'approved'
  | 'rejected'
  | 'requires_revision'

// ===== CHALLENGE SYSTEM CLASS =====

export class ChallengeSystem {
  private static subscriptions = new Map<string, () => void>()

  // ===== CHALLENGE MANAGEMENT =====

  /**
   * Create a new challenge
   */
  static async createChallenge(
    challengeData: Omit<Challenge, 'id' | 'createdAt' | 'updatedAt' | 'currentParticipants'>,
    adminId: string
  ): Promise<{ success: boolean; challengeId?: string; error?: string }> {
    try {
      // Verify admin permissions
      const authCheck = await AdminAuthService.checkPermission(
        adminId,
        'challenge_create',
        { action: 'create_challenge' }
      )

      if (!authCheck.hasPermission) {
        return { success: false, error: authCheck.reason || 'Permission denied' }
      }

      const challenge: Omit<Challenge, 'id'> = {
        ...challengeData,
        currentParticipants: 0,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp
      }

      const challengeRef = await addDoc(collection(db, collections.challenges), challenge)

      // Log challenge creation
      await AuditLogger.logOperation('challenge_created', adminId, {
        challengeId: challengeRef.id,
        challengeTitle: challenge.title,
        challengeType: challenge.type,
        challengeCategory: challenge.category
      }, adminId)

      // Invalidate relevant caches
      await gamificationCache.invalidateByTags(['challenge-catalog'])

      return { success: true, challengeId: challengeRef.id }
    } catch (error) {
      console.error('Error creating challenge:', error)
      return { success: false, error: 'Failed to create challenge' }
    }
  }

  /**
   * Update an existing challenge
   */
  static async updateChallenge(
    challengeId: string,
    updates: Partial<Challenge>,
    adminId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const authCheck = await AdminAuthService.checkPermission(
        adminId,
        'challenge_update',
        { challengeId }
      )

      if (!authCheck.hasPermission) {
        return { success: false, error: authCheck.reason || 'Permission denied' }
      }

      await runTransaction(db, async (transaction) => {
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)

        if (!challengeDoc.exists()) {
          throw new Error('Challenge not found')
        }

        const updateData = {
          ...updates,
          updatedAt: serverTimestamp()
        }

        transaction.update(challengeRef, updateData)
      })

      // Log update
      await AuditLogger.logOperation('challenge_updated', adminId, {
        challengeId,
        updates: Object.keys(updates)
      }, adminId)

      // Invalidate caches
      await gamificationCache.invalidateByTags(['challenge-catalog'])
      await gamificationCache.invalidate(`challenge_${challengeId}`)

      return { success: true }
    } catch (error) {
      console.error('Error updating challenge:', error)
      return { success: false, error: 'Failed to update challenge' }
    }
  }

  /**
   * Delete a challenge
   */
  static async deleteChallenge(
    challengeId: string,
    adminId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const authCheck = await AdminAuthService.checkPermission(
        adminId,
        'challenge_delete',
        { challengeId }
      )

      if (!authCheck.hasPermission) {
        return { success: false, error: authCheck.reason || 'Permission denied' }
      }

      await runTransaction(db, async (transaction) => {
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)

        if (!challengeDoc.exists()) {
          throw new Error('Challenge not found')
        }

        const challenge = challengeDoc.data() as Challenge

        // Check if challenge has active participants
        if (challenge.currentParticipants > 0 && challenge.status === 'active') {
          throw new Error('Cannot delete active challenge with participants')
        }

        // Archive instead of delete if there were participants
        if (challenge.currentParticipants > 0) {
          transaction.update(challengeRef, {
            status: 'archived',
            updatedAt: serverTimestamp()
          })
        } else {
          transaction.delete(challengeRef)
        }
      })

      await AuditLogger.logOperation('challenge_deleted', adminId, {
        challengeId
      }, adminId)

      await gamificationCache.invalidateByTags(['challenge-catalog'])
      await gamificationCache.invalidate(`challenge_${challengeId}`)

      return { success: true }
    } catch (error) {
      console.error('Error deleting challenge:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Failed to delete challenge' }
    }
  }

  // ===== PARTICIPATION MANAGEMENT =====

  /**
   * Join a challenge
   */
  static async joinChallenge(
    challengeId: string,
    userId: string,
    teamId?: string
  ): Promise<{ success: boolean; participationId?: string; error?: string }> {
    try {
      return await runTransaction(db, async (transaction) => {
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)

        if (!challengeDoc.exists()) {
          return { success: false, error: 'Challenge not found' }
        }

        const challenge = challengeDoc.data() as Challenge

        // Check if challenge is joinable
        if (challenge.status !== 'active' && challenge.status !== 'scheduled') {
          return { success: false, error: 'Challenge is not accepting participants' }
        }

        // Check registration deadline
        if (challenge.registrationEndDate && challenge.registrationEndDate.toDate() < new Date()) {
          return { success: false, error: 'Registration period has ended' }
        }

        // Check participant limit
        if (challenge.maxParticipants && challenge.currentParticipants >= challenge.maxParticipants) {
          return { success: false, error: 'Challenge is full' }
        }

        // Check if user already joined
        const existingQuery = query(
          collection(db, collections.challengeParticipations),
          where('challengeId', '==', challengeId),
          where('userId', '==', userId),
          where('status', 'in', ['registered', 'active']),
          limit(1)
        )

        const existingSnapshot = await getDocs(existingQuery)
        if (!existingSnapshot.empty) {
          return { success: false, error: 'Already joined this challenge' }
        }

        // Create participation record
        const participation: Omit<ChallengeParticipation, 'id'> = {
          challengeId,
          userId,
          teamId,
          status: challenge.settings.requiresApproval ? 'registered' : 'active',
          joinedAt: serverTimestamp() as Timestamp,
          lastActiveAt: serverTimestamp() as Timestamp,
          progress: 0,
          currentValue: 0,
          submissions: [],
          milestones: []
        }

        const participationRef = doc(collection(db, collections.challengeParticipations))
        transaction.set(participationRef, participation)

        // Update challenge participant count
        transaction.update(challengeRef, {
          currentParticipants: challenge.currentParticipants + 1,
          updatedAt: serverTimestamp()
        })

        // Log participation
        await AuditLogger.logOperation('challenge_joined', userId, {
          challengeId,
          challengeTitle: challenge.title,
          teamId
        })

        return { success: true, participationId: participationRef.id }
      })
    } catch (error) {
      console.error('Error joining challenge:', error)
      return { success: false, error: 'Failed to join challenge' }
    }
  }

  /**
   * Leave a challenge
   */
  static async leaveChallenge(
    challengeId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Find participation record
        const participationQuery = query(
          collection(db, collections.challengeParticipations),
          where('challengeId', '==', challengeId),
          where('userId', '==', userId),
          where('status', 'in', ['registered', 'active']),
          limit(1)
        )

        const participationSnapshot = await getDocs(participationQuery)
        if (participationSnapshot.empty) {
          return { success: false, error: 'Not participating in this challenge' }
        }

        const participationDoc = participationSnapshot.docs[0]
        const participation = participationDoc.data() as ChallengeParticipation

        // Update participation status
        transaction.update(participationDoc.ref, {
          status: 'withdrawn',
          lastActiveAt: serverTimestamp()
        })

        // Update challenge participant count
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)
        
        if (challengeDoc.exists()) {
          const challenge = challengeDoc.data() as Challenge
          transaction.update(challengeRef, {
            currentParticipants: Math.max(0, challenge.currentParticipants - 1),
            updatedAt: serverTimestamp()
          })
        }

        await AuditLogger.logOperation('challenge_left', userId, {
          challengeId,
          participationId: participationDoc.id
        })

        return { success: true }
      })
    } catch (error) {
      console.error('Error leaving challenge:', error)
      return { success: false, error: 'Failed to leave challenge' }
    }
  }

  // ===== PROGRESS TRACKING =====

  /**
   * Update challenge progress for a user
   */
  static async updateProgress(
    challengeId: string,
    userId: string,
    progressValue: number,
    source: string = 'manual'
  ): Promise<{ success: boolean; milestonesUnlocked?: string[]; completed?: boolean; error?: string }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get challenge details
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)

        if (!challengeDoc.exists()) {
          return { success: false, error: 'Challenge not found' }
        }

        const challenge = challengeDoc.data() as Challenge

        // Find participation record
        const participationQuery = query(
          collection(db, collections.challengeParticipations),
          where('challengeId', '==', challengeId),
          where('userId', '==', userId),
          where('status', '==', 'active'),
          limit(1)
        )

        const participationSnapshot = await getDocs(participationQuery)
        if (participationSnapshot.empty) {
          return { success: false, error: 'User not actively participating in challenge' }
        }

        const participationDoc = participationSnapshot.docs[0]
        const participation = participationDoc.data() as ChallengeParticipation

        // Calculate new progress
        const newValue = Math.max(participation.currentValue, progressValue)
        const totalRequired = challenge.requirements.reduce((sum, req) => sum + req.target, 0)
        const newProgress = Math.min(100, (newValue / totalRequired) * 100)

        // Check for milestone unlocks
        const unlockedMilestones: string[] = []
        if (challenge.progressMilestones) {
          for (const milestone of challenge.progressMilestones) {
            if (newProgress >= milestone.threshold && !participation.milestones.includes(milestone.id)) {
              unlockedMilestones.push(milestone.id)
              
              // Award milestone rewards
              if (milestone.rewards.points > 0) {
                await SecurePointSystem.awardPoints(
                  userId,
                  milestone.rewards.points,
                  'challenge_milestone',
                  `Milestone reached: ${milestone.name}`,
                  { challengeId, milestoneId: milestone.id }
                )
              }
            }
          }
        }

        // Check for completion
        const isCompleted = newProgress >= 100
        let completionRewards = false

        if (isCompleted && participation.status !== 'completed') {
          // Award completion rewards
          if (challenge.rewards.points > 0) {
            await SecurePointSystem.awardPoints(
              userId,
              challenge.rewards.points,
              'challenge_completion',
              `Challenge completed: ${challenge.title}`,
              { challengeId }
            )
          }
          
          completionRewards = true
        }

        // Update participation
        const updates: Partial<ChallengeParticipation> = {
          progress: newProgress,
          currentValue: newValue,
          lastActiveAt: serverTimestamp() as Timestamp,
          milestones: [...participation.milestones, ...unlockedMilestones]
        }

        if (isCompleted) {
          updates.status = 'completed'
          updates.completedAt = serverTimestamp() as Timestamp
        }

        transaction.update(participationDoc.ref, updates)

        // Log progress update
        await AuditLogger.logOperation('challenge_progress_updated', userId, {
          challengeId,
          oldProgress: participation.progress,
          newProgress,
          source,
          milestonesUnlocked: unlockedMilestones.length,
          completed: isCompleted
        })

        return { 
          success: true, 
          milestonesUnlocked: unlockedMilestones.length > 0 ? unlockedMilestones : undefined,
          completed: isCompleted
        }
      })
    } catch (error) {
      console.error('Error updating challenge progress:', error)
      return { success: false, error: 'Failed to update progress' }
    }
  }

  // ===== SUBMISSION MANAGEMENT =====

  /**
   * Submit evidence for a challenge
   */
  static async submitEvidence(
    challengeId: string,
    userId: string,
    submission: Omit<ChallengeSubmission, 'id' | 'challengeId' | 'userId' | 'submittedAt' | 'status' | 'isVerified'>
  ): Promise<{ success: boolean; submissionId?: string; error?: string }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Verify user is participating
        const participationQuery = query(
          collection(db, collections.challengeParticipations),
          where('challengeId', '==', challengeId),
          where('userId', '==', userId),
          where('status', '==', 'active'),
          limit(1)
        )

        const participationSnapshot = await getDocs(participationQuery)
        if (participationSnapshot.empty) {
          return { success: false, error: 'Not actively participating in challenge' }
        }

        // Get challenge settings
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)
        
        if (!challengeDoc.exists()) {
          return { success: false, error: 'Challenge not found' }
        }

        const challenge = challengeDoc.data() as Challenge

        // Check submission limits
        if (challenge.settings.maxSubmissionsPerUser) {
          const userSubmissions = await getDocs(query(
            collection(db, collections.challengeSubmissions),
            where('challengeId', '==', challengeId),
            where('userId', '==', userId)
          ))

          if (userSubmissions.size >= challenge.settings.maxSubmissionsPerUser) {
            return { success: false, error: 'Maximum submissions reached' }
          }
        }

        // Create submission
        const submissionData: Omit<ChallengeSubmission, 'id'> = {
          ...submission,
          challengeId,
          userId,
          status: challenge.settings.verificationRequired ? 'pending' : 'approved',
          submittedAt: serverTimestamp() as Timestamp,
          isVerified: !challenge.settings.verificationRequired
        }

        const submissionRef = doc(collection(db, collections.challengeSubmissions))
        transaction.set(submissionRef, submissionData)

        // If auto-approved, update progress
        if (!challenge.settings.verificationRequired && submission.progressValue > 0) {
          await this.updateProgress(challengeId, userId, submission.progressValue, 'submission')
        }

        return { success: true, submissionId: submissionRef.id }
      })
    } catch (error) {
      console.error('Error submitting evidence:', error)
      return { success: false, error: 'Failed to submit evidence' }
    }
  }

  // ===== TEAM MANAGEMENT =====

  /**
   * Create a team for a challenge
   */
  static async createTeam(
    challengeId: string,
    captainId: string,
    teamData: Omit<ChallengeTeam, 'id' | 'challengeId' | 'captainId' | 'members' | 'createdAt' | 'status' | 'progress' | 'totalValue'>
  ): Promise<{ success: boolean; teamId?: string; error?: string }> {
    try {
      // Verify challenge allows teams
      const challengeDoc = await getDoc(doc(db, collections.challenges, challengeId))
      if (!challengeDoc.exists()) {
        return { success: false, error: 'Challenge not found' }
      }

      const challenge = challengeDoc.data() as Challenge
      if (!challenge.settings.allowTeams) {
        return { success: false, error: 'This challenge does not allow teams' }
      }

      const team: Omit<ChallengeTeam, 'id'> = {
        ...teamData,
        challengeId,
        captainId,
        members: [{
          userId: captainId,
          role: 'captain',
          joinedAt: serverTimestamp() as Timestamp,
          contribution: 0,
          status: 'active'
        }],
        status: 'forming',
        createdAt: serverTimestamp() as Timestamp,
        progress: 0,
        totalValue: 0
      }

      const teamRef = await addDoc(collection(db, collections.challengeTeams), team)

      await AuditLogger.logOperation('challenge_team_created', captainId, {
        challengeId,
        teamId: teamRef.id,
        teamName: team.name
      })

      return { success: true, teamId: teamRef.id }
    } catch (error) {
      console.error('Error creating team:', error)
      return { success: false, error: 'Failed to create team' }
    }
  }

  // ===== QUERY METHODS =====

  /**
   * Get active challenges
   */
  static async getActiveChallenges(
    limit: number = 20,
    startAfterDoc?: QueryDocumentSnapshot<DocumentData>
  ): Promise<{ challenges: Challenge[]; hasMore: boolean; lastDoc?: QueryDocumentSnapshot<DocumentData> }> {
    try {
      return await gamificationCache.getOrSet(
        `active_challenges_${limit}_${startAfterDoc?.id || 'start'}`,
        async () => {
          let q = query(
            collection(db, collections.challenges),
            where('status', '==', 'active'),
            orderBy('startDate', 'desc'),
            limit(limit + 1) // Get one extra to check if there are more
          )

          if (startAfterDoc) {
            q = query(q, startAfter(startAfterDoc))
          }

          const snapshot = await getDocs(q)
          const challenges = snapshot.docs.slice(0, limit).map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Challenge[]

          return {
            challenges,
            hasMore: snapshot.docs.length > limit,
            lastDoc: challenges.length > 0 ? snapshot.docs[challenges.length - 1] : undefined
          }
        },
        2 * 60 * 1000, // 2 minutes
        ['challenge-catalog']
      )
    } catch (error) {
      console.error('Error getting active challenges:', error)
      return { challenges: [], hasMore: false }
    }
  }

  /**
   * Get user's challenge participations
   */
  static async getUserParticipations(userId: string): Promise<ChallengeParticipation[]> {
    try {
      return await gamificationCache.getOrSet(
        `user_participations_${userId}`,
        async () => {
          const q = query(
            collection(db, collections.challengeParticipations),
            where('userId', '==', userId),
            orderBy('joinedAt', 'desc')
          )

          const snapshot = await getDocs(q)
          return snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as ChallengeParticipation[]
        },
        5 * 60 * 1000, // 5 minutes
        ['user-challenges']
      )
    } catch (error) {
      console.error('Error getting user participations:', error)
      return []
    }
  }

  /**
   * Get challenge details by ID
   */
  static async getChallengeById(challengeId: string): Promise<Challenge | null> {
    try {
      return await gamificationCache.getOrSet(
        `challenge_${challengeId}`,
        async () => {
          const doc = await getDoc(doc(db, collections.challenges, challengeId))
          return doc.exists() ? { id: doc.id, ...doc.data() } as Challenge : null
        },
        10 * 60 * 1000, // 10 minutes
        ['challenge-catalog']
      )
    } catch (error) {
      console.error('Error getting challenge:', error)
      return null
    }
  }

  // ===== REAL-TIME SUBSCRIPTIONS =====

  /**
   * Subscribe to challenge updates
   */
  static subscribeToChallenge(
    challengeId: string,
    callback: (challenge: Challenge | null) => void
  ): () => void {
    const challengeRef = doc(db, collections.challenges, challengeId)
    
    const unsubscribe = onSnapshot(challengeRef, (doc) => {
      const challenge = doc.exists() ? { id: doc.id, ...doc.data() } as Challenge : null
      callback(challenge)
    })

    this.subscriptions.set(`challenge_${challengeId}`, unsubscribe)
    return unsubscribe
  }

  /**
   * Subscribe to user's challenge progress
   */
  static subscribeToUserProgress(
    userId: string,
    callback: (participations: ChallengeParticipation[]) => void
  ): () => void {
    const participationsRef = query(
      collection(db, collections.challengeParticipations),
      where('userId', '==', userId),
      where('status', 'in', ['active', 'completed']),
      orderBy('lastActiveAt', 'desc')
    )

    const unsubscribe = onSnapshot(participationsRef, (snapshot) => {
      const participations = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ChallengeParticipation[]
      
      callback(participations)
    })

    this.subscriptions.set(`user_progress_${userId}`, unsubscribe)
    return unsubscribe
  }

  /**
   * Cleanup all subscriptions
   */
  static cleanup(): void {
    this.subscriptions.forEach((unsubscribe) => {
      unsubscribe()
    })
    this.subscriptions.clear()
  }
}

export default ChallengeSystem