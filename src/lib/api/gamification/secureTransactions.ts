/**
 * Secure Gamification Transactions
 * 
 * Atomic, race-condition-free implementations for all gamification operations.
 * Replaces vulnerable transaction patterns with secure, audited operations.
 * 
 * <AUTHOR> Team - Phase 1 Security Improvements
 * @version 2.0.0
 */

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  increment,
  runTransaction,
  Timestamp,
  DocumentReference,
  WriteBatch,
  writeBatch
} from 'firebase/firestore'
import { db } from '../../firebase'
import { collections } from '../../firestore'

// ===== TYPES =====

export interface SecureTransaction {
  id: string
  userId: string
  type: 'points_earned' | 'points_spent' | 'reward_purchase' | 'achievement_unlock' | 'admin_adjustment'
  amount: number
  balanceBefore: number
  balanceAfter: number
  source: string
  description: string
  metadata: Record<string, any>
  timestamp: Timestamp
  adminId?: string
  auditTrail: AuditEntry[]
}

export interface AuditEntry {
  action: string
  timestamp: Timestamp
  details: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

export interface TransactionResult {
  success: boolean
  transactionId?: string
  newBalance?: number
  error?: string
  auditId?: string
}

export interface RewardPurchaseResult extends TransactionResult {
  purchaseId?: string
  rewardName?: string
}

// ===== SECURITY UTILITIES =====

class SecurityValidator {
  static validateUserId(userId: string): void {
    if (!userId || typeof userId !== 'string' || userId.length < 3) {
      throw new Error('Invalid user ID')
    }
  }

  static validateAmount(amount: number, operation: 'earn' | 'spend'): void {
    if (!Number.isInteger(amount) || amount <= 0) {
      throw new Error('Amount must be a positive integer')
    }

    const maxAmount = operation === 'earn' ? 10000 : 100000 // Prevent excessive operations
    if (amount > maxAmount) {
      throw new Error(`Amount exceeds maximum allowed (${maxAmount})`)
    }
  }

  static validateSource(source: string): void {
    const allowedSources = [
      'daily_login', 'challenge_completion', 'achievement_unlock',
      'reward_purchase', 'admin_adjustment', 'profile_completion',
      'social_share', 'referral_bonus', 'special_event'
    ]
    
    if (!allowedSources.includes(source)) {
      throw new Error(`Invalid transaction source: ${source}`)
    }
  }

  static async validateAdminPermission(adminId: string, operation: string): Promise<void> {
    if (!adminId) {
      throw new Error('Admin ID required for administrative operations')
    }

    const adminRef = doc(db, collections.admins, adminId)
    const adminDoc = await getDoc(adminRef)

    if (!adminDoc.exists()) {
      throw new Error('Admin user not found')
    }

    const adminData = adminDoc.data()
    const permissions = adminData.permissions || []
    const requiredPermission = `gamification.${operation}`

    if (!permissions.includes(requiredPermission) && !permissions.includes('gamification.*')) {
      throw new Error(`Insufficient permissions for operation: ${operation}`)
    }
  }
}

// ===== AUDIT LOGGING =====

class AuditLogger {
  static async logOperation(
    operation: string,
    userId: string,
    details: Record<string, any>,
    adminId?: string
  ): Promise<string> {
    try {
      const auditEntry = {
        operation,
        userId,
        adminId,
        details,
        timestamp: serverTimestamp(),
        ipAddress: details.ipAddress || 'unknown',
        userAgent: details.userAgent || 'unknown'
      }

      const auditRef = await addDoc(collection(db, collections.auditLogs), auditEntry)
      return auditRef.id
    } catch (error) {
      console.error('Failed to log audit entry:', error)
      // Don't fail the main operation if audit logging fails
      return 'audit-failed'
    }
  }

  static createAuditTrail(operation: string, details: Record<string, any>): AuditEntry {
    return {
      action: operation,
      timestamp: serverTimestamp() as Timestamp,
      details,
      ipAddress: details.ipAddress,
      userAgent: details.userAgent
    }
  }
}

// ===== SECURE POINT OPERATIONS =====

export class SecurePointSystem {
  /**
   * Atomically award points to a user with full audit trail
   */
  static async awardPoints(
    userId: string,
    amount: number,
    source: string,
    description: string,
    metadata: Record<string, any> = {},
    adminId?: string
  ): Promise<TransactionResult> {
    try {
      // Input validation
      SecurityValidator.validateUserId(userId)
      SecurityValidator.validateAmount(amount, 'earn')
      SecurityValidator.validateSource(source)

      if (adminId && ['admin_adjustment', 'special_event'].includes(source)) {
        await SecurityValidator.validateAdminPermission(adminId, 'award_points')
      }

      return await runTransaction(db, async (transaction) => {
        // Get current user data
        const userRef = doc(db, collections.users, userId)
        const userDoc = await transaction.get(userRef)

        if (!userDoc.exists()) {
          throw new Error('User not found')
        }

        const userData = userDoc.data()
        const currentBalance = userData.gamification?.points || 0
        const newBalance = currentBalance + amount

        // Create audit trail
        const auditTrail = [
          AuditLogger.createAuditTrail('points_awarded', {
            amount,
            balanceBefore: currentBalance,
            balanceAfter: newBalance,
            source,
            description,
            adminId,
            ...metadata
          })
        ]

        // Update user balance
        transaction.update(userRef, {
          'gamification.points': newBalance,
          'gamification.totalPointsEarned': increment(amount),
          'gamification.lastActivity': serverTimestamp(),
          'gamification.lastUpdate': serverTimestamp()
        })

        // Create transaction record
        const transactionRef = doc(collection(db, collections.pointTransactions))
        const transactionId = transactionRef.id

        transaction.set(transactionRef, {
          userId,
          type: 'points_earned',
          amount,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          source,
          description,
          metadata,
          timestamp: serverTimestamp(),
          adminId,
          auditTrail
        })

        // Log user activity
        const activityRef = doc(collection(db, collections.userActivities))
        transaction.set(activityRef, {
          userId,
          type: 'points_earned',
          description: `Earned ${amount} points: ${description}`,
          points: amount,
          metadata: { source, transactionId, ...metadata },
          createdAt: serverTimestamp()
        })

        return {
          success: true,
          transactionId,
          newBalance,
          auditId: await AuditLogger.logOperation('award_points', userId, {
            amount,
            source,
            description,
            newBalance,
            ...metadata
          }, adminId)
        }
      })
    } catch (error) {
      console.error('Error awarding points:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to award points'
      }
    }
  }

  /**
   * Atomically spend points with insufficient balance protection
   */
  static async spendPoints(
    userId: string,
    amount: number,
    source: string,
    description: string,
    metadata: Record<string, any> = {},
    adminId?: string
  ): Promise<TransactionResult> {
    try {
      // Input validation
      SecurityValidator.validateUserId(userId)
      SecurityValidator.validateAmount(amount, 'spend')
      SecurityValidator.validateSource(source)

      if (adminId && source === 'admin_adjustment') {
        await SecurityValidator.validateAdminPermission(adminId, 'spend_points')
      }

      return await runTransaction(db, async (transaction) => {
        // Get current user data
        const userRef = doc(db, collections.users, userId)
        const userDoc = await transaction.get(userRef)

        if (!userDoc.exists()) {
          throw new Error('User not found')
        }

        const userData = userDoc.data()
        const currentBalance = userData.gamification?.points || 0

        // Check sufficient balance
        if (currentBalance < amount) {
          throw new Error(`Insufficient points. Required: ${amount}, Available: ${currentBalance}`)
        }

        const newBalance = currentBalance - amount

        // Create audit trail
        const auditTrail = [
          AuditLogger.createAuditTrail('points_spent', {
            amount,
            balanceBefore: currentBalance,
            balanceAfter: newBalance,
            source,
            description,
            adminId,
            ...metadata
          })
        ]

        // Update user balance
        transaction.update(userRef, {
          'gamification.points': newBalance,
          'gamification.totalPointsSpent': increment(amount),
          'gamification.lastActivity': serverTimestamp(),
          'gamification.lastUpdate': serverTimestamp()
        })

        // Create transaction record
        const transactionRef = doc(collection(db, collections.pointTransactions))
        const transactionId = transactionRef.id

        transaction.set(transactionRef, {
          userId,
          type: 'points_spent',
          amount: -amount,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          source,
          description,
          metadata,
          timestamp: serverTimestamp(),
          adminId,
          auditTrail
        })

        // Log user activity
        const activityRef = doc(collection(db, collections.userActivities))
        transaction.set(activityRef, {
          userId,
          type: 'points_spent',
          description: `Spent ${amount} points: ${description}`,
          points: -amount,
          metadata: { source, transactionId, ...metadata },
          createdAt: serverTimestamp()
        })

        return {
          success: true,
          transactionId,
          newBalance,
          auditId: await AuditLogger.logOperation('spend_points', userId, {
            amount,
            source,
            description,
            newBalance,
            ...metadata
          }, adminId)
        }
      })
    } catch (error) {
      console.error('Error spending points:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to spend points'
      }
    }
  }

  /**
   * Get user's current point balance with caching
   */
  static async getBalance(userId: string): Promise<number> {
    try {
      SecurityValidator.validateUserId(userId)

      const userRef = doc(db, collections.users, userId)
      const userDoc = await getDoc(userRef)

      if (!userDoc.exists()) {
        throw new Error('User not found')
      }

      return userDoc.data().gamification?.points || 0
    } catch (error) {
      console.error('Error getting balance:', error)
      throw error
    }
  }
}

// ===== SECURE REWARD PURCHASES =====

export class SecureRewardSystem {
  /**
   * Atomically purchase a reward with stock management and point deduction
   */
  static async purchaseReward(
    userId: string,
    rewardId: string,
    metadata: Record<string, any> = {}
  ): Promise<RewardPurchaseResult> {
    try {
      SecurityValidator.validateUserId(userId)

      if (!rewardId || typeof rewardId !== 'string') {
        throw new Error('Invalid reward ID')
      }

      return await runTransaction(db, async (transaction) => {
        // Get reward details
        const rewardRef = doc(db, collections.rewards, rewardId)
        const rewardDoc = await transaction.get(rewardRef)

        if (!rewardDoc.exists()) {
          throw new Error('Reward not found')
        }

        const rewardData = rewardDoc.data()

        // Validate reward availability
        if (!rewardData.isActive) {
          throw new Error('Reward is no longer available')
        }

        if (rewardData.stock <= 0) {
          throw new Error('Reward is out of stock')
        }

        // Get user's current balance within transaction
        const userRef = doc(db, collections.users, userId)
        const userDoc = await transaction.get(userRef)

        if (!userDoc.exists()) {
          throw new Error('User not found')
        }

        const userData = userDoc.data()
        const currentBalance = userData.gamification?.points || 0
        const pointsCost = rewardData.pointsCost

        // Check sufficient balance
        if (currentBalance < pointsCost) {
          throw new Error(`Insufficient points. Required: ${pointsCost}, Available: ${currentBalance}`)
        }

        const newBalance = currentBalance - pointsCost

        // Create purchase record
        const purchaseRef = doc(collection(db, collections.rewardPurchases))
        const purchaseId = purchaseRef.id

        transaction.set(purchaseRef, {
          userId,
          rewardId,
          rewardName: rewardData.name,
          pointsCost,
          status: 'pending',
          purchaseDate: serverTimestamp(),
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          metadata: {
            category: rewardData.category,
            rarity: rewardData.rarity,
            ...metadata
          },
          auditTrail: [
            AuditLogger.createAuditTrail('reward_purchased', {
              rewardId,
              rewardName: rewardData.name,
              pointsCost,
              stockBefore: rewardData.stock,
              stockAfter: rewardData.stock - 1,
              ...metadata
            })
          ]
        })

        // Update user's point balance
        transaction.update(userRef, {
          'gamification.points': newBalance,
          'gamification.totalPointsSpent': increment(pointsCost),
          'gamification.lastActivity': serverTimestamp(),
          'gamification.lastUpdate': serverTimestamp()
        })

        // Update reward stock
        transaction.update(rewardRef, {
          stock: increment(-1),
          totalPurchases: increment(1),
          lastPurchased: serverTimestamp(),
          updatedAt: serverTimestamp()
        })

        // Create point transaction record
        const transactionRef = doc(collection(db, collections.pointTransactions))
        transaction.set(transactionRef, {
          userId,
          type: 'points_spent',
          amount: -pointsCost,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          source: 'reward_purchase',
          description: `Purchased reward: ${rewardData.name}`,
          metadata: { rewardId, purchaseId, ...metadata },
          timestamp: serverTimestamp(),
          auditTrail: []
        })

        // Log user activity
        const activityRef = doc(collection(db, collections.userActivities))
        transaction.set(activityRef, {
          userId,
          type: 'reward_purchased',
          description: `Purchased ${rewardData.name} for ${pointsCost} points`,
          points: -pointsCost,
          metadata: { rewardId, purchaseId, rewardName: rewardData.name, ...metadata },
          createdAt: serverTimestamp()
        })

        return {
          success: true,
          purchaseId,
          rewardName: rewardData.name,
          newBalance,
          auditId: await AuditLogger.logOperation('purchase_reward', userId, {
            rewardId,
            rewardName: rewardData.name,
            pointsCost,
            newBalance,
            ...metadata
          })
        }
      })
    } catch (error) {
      console.error('Error purchasing reward:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to purchase reward'
      }
    }
  }
}

// ===== TRANSACTION VERIFICATION =====

export class TransactionVerifier {
  /**
   * Verify transaction integrity and detect potential fraud
   */
  static async verifyTransactionIntegrity(transactionId: string): Promise<{
    isValid: boolean
    issues: string[]
    correctedBalance?: number
  }> {
    try {
      const transactionRef = doc(db, collections.pointTransactions, transactionId)
      const transactionDoc = await getDoc(transactionRef)

      if (!transactionDoc.exists()) {
        return { isValid: false, issues: ['Transaction not found'] }
      }

      const transaction = transactionDoc.data() as SecureTransaction
      const issues: string[] = []

      // Verify balance calculation
      if (transaction.type === 'points_earned') {
        const expectedBalance = transaction.balanceBefore + transaction.amount
        if (expectedBalance !== transaction.balanceAfter) {
          issues.push('Balance calculation error for earning transaction')
        }
      } else if (transaction.type === 'points_spent') {
        const expectedBalance = transaction.balanceBefore + transaction.amount // amount is negative
        if (expectedBalance !== transaction.balanceAfter) {
          issues.push('Balance calculation error for spending transaction')
        }
      }

      // Verify user's current balance matches transaction history
      const currentBalance = await SecurePointSystem.getBalance(transaction.userId)
      const calculatedBalance = await this.calculateBalanceFromHistory(transaction.userId)

      if (currentBalance !== calculatedBalance) {
        issues.push('User balance does not match transaction history')
      }

      return {
        isValid: issues.length === 0,
        issues,
        correctedBalance: issues.length > 0 ? calculatedBalance : undefined
      }
    } catch (error) {
      console.error('Error verifying transaction:', error)
      return {
        isValid: false,
        issues: ['Verification failed: ' + (error instanceof Error ? error.message : 'Unknown error')]
      }
    }
  }

  /**
   * Calculate user's balance from transaction history
   */
  private static async calculateBalanceFromHistory(userId: string): Promise<number> {
    const q = query(
      collection(db, collections.pointTransactions),
      where('userId', '==', userId),
      orderBy('timestamp', 'asc')
    )

    const snapshot = await getDocs(q)
    let balance = 0

    snapshot.docs.forEach(doc => {
      const transaction = doc.data()
      if (transaction.type === 'points_earned') {
        balance += transaction.amount
      } else if (transaction.type === 'points_spent') {
        balance += transaction.amount // amount is negative for spending
      }
    })

    return balance
  }

  /**
   * Repair corrupted user balance
   */
  static async repairUserBalance(userId: string, adminId: string): Promise<TransactionResult> {
    try {
      await SecurityValidator.validateAdminPermission(adminId, 'repair_balance')

      const calculatedBalance = await this.calculateBalanceFromHistory(userId)
      const currentBalance = await SecurePointSystem.getBalance(userId)

      if (calculatedBalance === currentBalance) {
        return { success: true, newBalance: currentBalance }
      }

      // Repair the balance
      const userRef = doc(db, collections.users, userId)
      await updateDoc(userRef, {
        'gamification.points': calculatedBalance,
        'gamification.lastUpdate': serverTimestamp(),
        'gamification.balanceRepaired': serverTimestamp()
      })

      // Log the repair
      const auditId = await AuditLogger.logOperation('balance_repair', userId, {
        oldBalance: currentBalance,
        correctedBalance: calculatedBalance,
        difference: calculatedBalance - currentBalance,
        adminId
      }, adminId)

      return {
        success: true,
        newBalance: calculatedBalance,
        auditId
      }
    } catch (error) {
      console.error('Error repairing balance:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to repair balance'
      }
    }
  }
}

// ===== EXPORTS =====

export {
  SecurityValidator,
  AuditLogger
}