/**
 * User Segmentation and A/B Testing Engine
 * 
 * Advanced user segmentation system for gamification features with
 * A/B testing capabilities, behavioral analysis, and personalized experiences.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  setDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  runTransaction,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  Timestamp
} from 'firebase/firestore'
import { 
  profilesCollection, 
  userAchievementsCollection, 
  pointTransactionsCollection,
  userSessionsCollection,
  abTestsCollection,
  userSegmentsCollection,
  testVariantsCollection,
  analyticsReportsCollection
} from '../../firebase/gamificationCollections'

// ===== TYPES =====

export interface UserSegment {
  id: string
  name: string
  description: string
  criteria: SegmentCriteria
  userCount: number
  isActive: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
  metadata?: Record<string, any>
}

export interface SegmentCriteria {
  pointsRange?: { min?: number; max?: number }
  tierIds?: string[]
  achievementCount?: { min?: number; max?: number }
  registrationDateRange?: { start?: Timestamp; end?: Timestamp }
  activityLevel?: 'low' | 'medium' | 'high' | 'inactive'
  challengeParticipation?: 'none' | 'low' | 'medium' | 'high'
  spendingBehavior?: 'none' | 'low' | 'medium' | 'high'
  customProperties?: Record<string, any>
  behavioralTags?: string[]
}

export interface ABTest {
  id: string
  name: string
  description: string
  feature: string
  status: 'draft' | 'running' | 'paused' | 'completed'
  startDate: Timestamp
  endDate?: Timestamp
  targetSegments: string[]
  variants: TestVariant[]
  trafficAllocation: number // Percentage of users in target segments
  metrics: TestMetric[]
  results?: TestResults
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: string
}

export interface TestVariant {
  id: string
  name: string
  description: string
  config: Record<string, any>
  trafficWeight: number // Percentage within the test
  isControl: boolean
}

export interface TestMetric {
  name: string
  type: 'conversion' | 'engagement' | 'retention' | 'revenue'
  eventName: string
  aggregation: 'count' | 'sum' | 'average' | 'unique'
  target?: number
}

export interface UserTestAssignment {
  userId: string
  testId: string
  variantId: string
  assignedAt: Timestamp
  isActive: boolean
  events: TestEvent[]
}

export interface TestEvent {
  eventName: string
  value?: number
  properties?: Record<string, any>
  timestamp: Timestamp
}

export interface TestResults {
  conversionRates: Record<string, number>
  statisticalSignificance: number
  confidenceInterval: [number, number]
  recommendedAction: 'continue' | 'stop' | 'declare_winner' | 'need_more_data'
  winningVariant?: string
  summary: string
}

export interface SegmentationInsights {
  topSegments: Array<{
    segment: UserSegment
    metrics: {
      averagePoints: number
      achievementRate: number
      retentionRate: number
      engagementScore: number
    }
  }>
  growthOpportunities: Array<{
    segment: string
    potential: number
    recommendations: string[]
  }>
  riskSegments: Array<{
    segment: string
    riskLevel: 'low' | 'medium' | 'high'
    indicators: string[]
  }>
}

// ===== MAIN SEGMENTATION ENGINE CLASS =====

export class SegmentationEngine {
  private static instance: SegmentationEngine | null = null
  private cache = new Map<string, any>()
  private cacheExpiry = new Map<string, number>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  static getInstance(): SegmentationEngine {
    if (!SegmentationEngine.instance) {
      SegmentationEngine.instance = new SegmentationEngine()
    }
    return SegmentationEngine.instance
  }

  // ===== USER SEGMENTATION =====

  async createSegment(
    name: string,
    description: string,
    criteria: SegmentCriteria,
    createdBy: string
  ): Promise<string> {
    try {
      const segmentId = `segment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      const segment: UserSegment = {
        id: segmentId,
        name,
        description,
        criteria,
        userCount: 0,
        isActive: true,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        metadata: {
          createdBy,
          version: '1.0.0'
        }
      }

      await setDoc(doc(userSegmentsCollection, segmentId), segment)

      // Calculate initial user count
      const userCount = await this.calculateSegmentSize(criteria)
      await updateDoc(doc(userSegmentsCollection, segmentId), { userCount })

      this.clearCache()
      return segmentId
    } catch (error) {
      console.error('Failed to create segment:', error)
      throw error
    }
  }

  async getSegmentUsers(segmentId: string, limitCount = 100): Promise<string[]> {
    try {
      const cacheKey = `segment_users_${segmentId}_${limitCount}`
      const cached = this.getCachedData(cacheKey)
      if (cached) return cached

      const segmentDoc = await getDoc(doc(userSegmentsCollection, segmentId))
      if (!segmentDoc.exists()) {
        throw new Error('Segment not found')
      }

      const segment = segmentDoc.data() as UserSegment
      const userIds = await this.findUsersMatchingCriteria(segment.criteria, limitCount)

      this.setCachedData(cacheKey, userIds)
      return userIds
    } catch (error) {
      console.error('Failed to get segment users:', error)
      throw error
    }
  }

  async calculateSegmentSize(criteria: SegmentCriteria): Promise<number> {
    try {
      const userIds = await this.findUsersMatchingCriteria(criteria, 10000)
      return userIds.length
    } catch (error) {
      console.error('Failed to calculate segment size:', error)
      return 0
    }
  }

  private async findUsersMatchingCriteria(
    criteria: SegmentCriteria, 
    limitCount = 1000
  ): Promise<string[]> {
    try {
      let baseQuery = query(profilesCollection, limit(limitCount))
      
      // Apply tier filter if specified
      if (criteria.tierIds && criteria.tierIds.length > 0) {
        baseQuery = query(baseQuery, where('currentTier.id', 'in', criteria.tierIds))
      }

      // Apply registration date filter
      if (criteria.registrationDateRange) {
        if (criteria.registrationDateRange.start) {
          baseQuery = query(baseQuery, where('createdAt', '>=', criteria.registrationDateRange.start))
        }
        if (criteria.registrationDateRange.end) {
          baseQuery = query(baseQuery, where('createdAt', '<=', criteria.registrationDateRange.end))
        }
      }

      const profilesSnapshot = await getDocs(baseQuery)
      const matchingUserIds: string[] = []

      for (const profileDoc of profilesSnapshot.docs) {
        const profile = profileDoc.data()
        const userId = profileDoc.id

        // Check points range
        if (criteria.pointsRange) {
          const points = profile.points || 0
          if (criteria.pointsRange.min !== undefined && points < criteria.pointsRange.min) continue
          if (criteria.pointsRange.max !== undefined && points > criteria.pointsRange.max) continue
        }

        // Check achievement count
        if (criteria.achievementCount) {
          const achievementCount = await this.getUserAchievementCount(userId)
          if (criteria.achievementCount.min !== undefined && achievementCount < criteria.achievementCount.min) continue
          if (criteria.achievementCount.max !== undefined && achievementCount > criteria.achievementCount.max) continue
        }

        // Check activity level
        if (criteria.activityLevel) {
          const activityLevel = await this.calculateUserActivityLevel(userId)
          if (activityLevel !== criteria.activityLevel) continue
        }

        // Check challenge participation
        if (criteria.challengeParticipation) {
          const participationLevel = await this.calculateChallengeParticipation(userId)
          if (participationLevel !== criteria.challengeParticipation) continue
        }

        // Check spending behavior
        if (criteria.spendingBehavior) {
          const spendingLevel = await this.calculateSpendingBehavior(userId)
          if (spendingLevel !== criteria.spendingBehavior) continue
        }

        // Check custom properties
        if (criteria.customProperties) {
          let customMatch = true
          for (const [key, value] of Object.entries(criteria.customProperties)) {
            if (profile[key] !== value) {
              customMatch = false
              break
            }
          }
          if (!customMatch) continue
        }

        matchingUserIds.push(userId)
      }

      return matchingUserIds
    } catch (error) {
      console.error('Failed to find users matching criteria:', error)
      return []
    }
  }

  private async getUserAchievementCount(userId: string): Promise<number> {
    try {
      const q = query(userAchievementsCollection, where('userId', '==', userId))
      const snapshot = await getDocs(q)
      return snapshot.size
    } catch (error) {
      return 0
    }
  }

  private async calculateUserActivityLevel(userId: string): Promise<'low' | 'medium' | 'high' | 'inactive'> {
    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const q = query(
        userSessionsCollection,
        where('userId', '==', userId),
        where('createdAt', '>=', Timestamp.fromDate(thirtyDaysAgo))
      )
      const snapshot = await getDocs(q)
      const sessionCount = snapshot.size

      if (sessionCount === 0) return 'inactive'
      if (sessionCount < 5) return 'low'
      if (sessionCount < 15) return 'medium'
      return 'high'
    } catch (error) {
      return 'inactive'
    }
  }

  private async calculateChallengeParticipation(userId: string): Promise<'none' | 'low' | 'medium' | 'high'> {
    try {
      // This would query challenge participations - simplified for now
      return 'medium'
    } catch (error) {
      return 'none'
    }
  }

  private async calculateSpendingBehavior(userId: string): Promise<'none' | 'low' | 'medium' | 'high'> {
    try {
      const q = query(
        pointTransactionsCollection,
        where('userId', '==', userId),
        where('type', '==', 'spent')
      )
      const snapshot = await getDocs(q)
      
      let totalSpent = 0
      snapshot.docs.forEach(doc => {
        totalSpent += doc.data().amount || 0
      })

      if (totalSpent === 0) return 'none'
      if (totalSpent < 1000) return 'low'
      if (totalSpent < 5000) return 'medium'
      return 'high'
    } catch (error) {
      return 'none'
    }
  }

  // ===== A/B TESTING =====

  async createABTest(
    name: string,
    description: string,
    feature: string,
    targetSegments: string[],
    variants: Omit<TestVariant, 'id'>[],
    metrics: TestMetric[],
    trafficAllocation: number,
    createdBy: string
  ): Promise<string> {
    try {
      const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      // Ensure traffic weights sum to 100
      const totalWeight = variants.reduce((sum, v) => sum + v.trafficWeight, 0)
      if (Math.abs(totalWeight - 100) > 0.01) {
        throw new Error('Variant traffic weights must sum to 100%')
      }

      const testVariants: TestVariant[] = variants.map((variant, index) => ({
        ...variant,
        id: `variant_${index + 1}`
      }))

      const abTest: ABTest = {
        id: testId,
        name,
        description,
        feature,
        status: 'draft',
        startDate: serverTimestamp() as Timestamp,
        targetSegments,
        variants: testVariants,
        trafficAllocation,
        metrics,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        createdBy
      }

      await setDoc(doc(abTestsCollection, testId), abTest)
      return testId
    } catch (error) {
      console.error('Failed to create A/B test:', error)
      throw error
    }
  }

  async startABTest(testId: string): Promise<void> {
    try {
      await updateDoc(doc(abTestsCollection, testId), {
        status: 'running',
        startDate: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      this.clearCache()
    } catch (error) {
      console.error('Failed to start A/B test:', error)
      throw error
    }
  }

  async assignUserToTest(userId: string, testId: string): Promise<string | null> {
    try {
      const testDoc = await getDoc(doc(abTestsCollection, testId))
      if (!testDoc.exists()) {
        throw new Error('Test not found')
      }

      const test = testDoc.data() as ABTest
      
      if (test.status !== 'running') {
        return null
      }

      // Check if user is in target segments
      const userInTargetSegment = await this.isUserInSegments(userId, test.targetSegments)
      if (!userInTargetSegment) {
        return null
      }

      // Check if user is already assigned
      const existingAssignment = await this.getUserTestAssignment(userId, testId)
      if (existingAssignment) {
        return existingAssignment.variantId
      }

      // Assign user to variant based on traffic allocation and weights
      const shouldIncludeUser = Math.random() * 100 < test.trafficAllocation
      if (!shouldIncludeUser) {
        return null
      }

      const variantId = this.selectVariantForUser(userId, test.variants)
      
      const assignment: UserTestAssignment = {
        userId,
        testId,
        variantId,
        assignedAt: serverTimestamp() as Timestamp,
        isActive: true,
        events: []
      }

      await setDoc(
        doc(testVariantsCollection, `${userId}_${testId}`),
        assignment
      )

      return variantId
    } catch (error) {
      console.error('Failed to assign user to test:', error)
      return null
    }
  }

  private async isUserInSegments(userId: string, segmentIds: string[]): Promise<boolean> {
    try {
      for (const segmentId of segmentIds) {
        const segmentUsers = await this.getSegmentUsers(segmentId, 10000)
        if (segmentUsers.includes(userId)) {
          return true
        }
      }
      return false
    } catch (error) {
      return false
    }
  }

  private async getUserTestAssignment(userId: string, testId: string): Promise<UserTestAssignment | null> {
    try {
      const assignmentDoc = await getDoc(doc(testVariantsCollection, `${userId}_${testId}`))
      return assignmentDoc.exists() ? assignmentDoc.data() as UserTestAssignment : null
    } catch (error) {
      return null
    }
  }

  private selectVariantForUser(userId: string, variants: TestVariant[]): string {
    // Use deterministic assignment based on user ID hash
    const hash = this.hashString(userId)
    const normalizedHash = Math.abs(hash) % 100

    let cumulativeWeight = 0
    for (const variant of variants) {
      cumulativeWeight += variant.trafficWeight
      if (normalizedHash < cumulativeWeight) {
        return variant.id
      }
    }

    // Fallback to first variant
    return variants[0].id
  }

  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash
  }

  async trackTestEvent(
    userId: string,
    testId: string,
    eventName: string,
    value?: number,
    properties?: Record<string, any>
  ): Promise<void> {
    try {
      const assignment = await this.getUserTestAssignment(userId, testId)
      if (!assignment || !assignment.isActive) {
        return
      }

      const event: TestEvent = {
        eventName,
        value,
        properties,
        timestamp: serverTimestamp() as Timestamp
      }

      await updateDoc(doc(testVariantsCollection, `${userId}_${testId}`), {
        events: [...(assignment.events || []), event],
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Failed to track test event:', error)
    }
  }

  async getTestResults(testId: string): Promise<TestResults | null> {
    try {
      const testDoc = await getDoc(doc(abTestsCollection, testId))
      if (!testDoc.exists()) {
        return null
      }

      const test = testDoc.data() as ABTest
      
      // Get all assignments for this test
      const q = query(testVariantsCollection, where('testId', '==', testId))
      const assignmentsSnapshot = await getDocs(q)
      
      const variantStats = new Map<string, {
        userCount: number
        events: TestEvent[]
      }>()

      // Initialize variant stats
      test.variants.forEach(variant => {
        variantStats.set(variant.id, { userCount: 0, events: [] })
      })

      // Collect data from assignments
      assignmentsSnapshot.docs.forEach(doc => {
        const assignment = doc.data() as UserTestAssignment
        const stats = variantStats.get(assignment.variantId)
        if (stats) {
          stats.userCount++
          stats.events.push(...(assignment.events || []))
        }
      })

      // Calculate conversion rates for each metric
      const conversionRates: Record<string, number> = {}
      
      for (const metric of test.metrics) {
        for (const [variantId, stats] of variantStats.entries()) {
          const conversions = stats.events.filter(event => event.eventName === metric.eventName).length
          const rate = stats.userCount > 0 ? (conversions / stats.userCount) * 100 : 0
          conversionRates[`${variantId}_${metric.name}`] = rate
        }
      }

      // Simple statistical significance calculation (simplified)
      const statisticalSignificance = this.calculateStatisticalSignificance(variantStats, test.metrics[0])

      const results: TestResults = {
        conversionRates,
        statisticalSignificance,
        confidenceInterval: [0, 0], // Would calculate proper CI in production
        recommendedAction: statisticalSignificance > 0.95 ? 'declare_winner' : 'need_more_data',
        summary: `Test results with ${assignmentsSnapshot.size} total assignments`
      }

      return results
    } catch (error) {
      console.error('Failed to get test results:', error)
      return null
    }
  }

  private calculateStatisticalSignificance(
    variantStats: Map<string, any>,
    metric: TestMetric
  ): number {
    // Simplified significance calculation - in production would use proper statistical tests
    const variants = Array.from(variantStats.entries())
    if (variants.length < 2) return 0

    const [controlId, controlStats] = variants[0]
    const [treatmentId, treatmentStats] = variants[1]

    const controlConversions = controlStats.events.filter((e: TestEvent) => e.eventName === metric.eventName).length
    const treatmentConversions = treatmentStats.events.filter((e: TestEvent) => e.eventName === metric.eventName).length

    const controlRate = controlStats.userCount > 0 ? controlConversions / controlStats.userCount : 0
    const treatmentRate = treatmentStats.userCount > 0 ? treatmentConversions / treatmentStats.userCount : 0

    // Return a mock significance value - in production would use proper statistical testing
    const minSampleSize = Math.min(controlStats.userCount, treatmentStats.userCount)
    return Math.min(0.99, minSampleSize / 1000)
  }

  // ===== SEGMENTATION INSIGHTS =====

  async getSegmentationInsights(): Promise<SegmentationInsights> {
    try {
      const segments = await this.getAllSegments()
      const topSegments = []

      for (const segment of segments.slice(0, 5)) {
        const metrics = await this.calculateSegmentMetrics(segment.id)
        topSegments.push({ segment, metrics })
      }

      const insights: SegmentationInsights = {
        topSegments,
        growthOpportunities: [
          {
            segment: 'Low Activity Users',
            potential: 25,
            recommendations: [
              'Implement re-engagement campaigns',
              'Offer bonus points for returning',
              'Simplify onboarding process'
            ]
          }
        ],
        riskSegments: [
          {
            segment: 'High Spenders with Low Engagement',
            riskLevel: 'medium',
            indicators: ['Decreased session frequency', 'Lower achievement completion rate']
          }
        ]
      }

      return insights
    } catch (error) {
      console.error('Failed to get segmentation insights:', error)
      throw error
    }
  }

  private async getAllSegments(): Promise<UserSegment[]> {
    try {
      const snapshot = await getDocs(query(userSegmentsCollection, where('isActive', '==', true)))
      return snapshot.docs.map(doc => doc.data() as UserSegment)
    } catch (error) {
      console.error('Failed to get all segments:', error)
      return []
    }
  }

  private async calculateSegmentMetrics(segmentId: string) {
    // Simplified metrics calculation
    return {
      averagePoints: 1250,
      achievementRate: 68.5,
      retentionRate: 82.3,
      engagementScore: 7.4
    }
  }

  // ===== CACHE MANAGEMENT =====

  private getCachedData(key: string): any {
    const expiry = this.cacheExpiry.get(key)
    if (expiry && Date.now() > expiry) {
      this.cache.delete(key)
      this.cacheExpiry.delete(key)
      return null
    }
    return this.cache.get(key)
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, data)
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL)
  }

  private clearCache(): void {
    this.cache.clear()
    this.cacheExpiry.clear()
  }
}

export default SegmentationEngine.getInstance()