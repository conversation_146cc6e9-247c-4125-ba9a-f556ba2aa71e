/**
 * Cloudflare Cache Management API
 * Provides comprehensive cache management capabilities for hybrid deployment
 */

import { shouldUseFeature, updateFeatureFlagMetrics } from '../config/featureFlags'

export interface CacheAnalytics {
  cacheHitRatio: number
  bandwidth: number
  requests: number
  errors: number
  avgResponseTime: number
  p95ResponseTime: number
  lastUpdated: string
}

export interface CachePurgeOptions {
  urls?: string[]
  tags?: string[]
  hosts?: string[]
  prefixes?: string[]
  everything?: boolean
}

export interface CacheRule {
  pattern: string
  ttl: number
  cacheLevel: 'bypass' | 'basic' | 'simplified' | 'aggressive' | 'cache_everything'
  edgeCacheTtl?: number
  browserCacheTtl?: number
}

export interface CacheStatus {
  status: 'HIT' | 'MISS' | 'EXPIRED' | 'STALE' | 'BYPASS' | 'DYNAMIC'
  age?: number
  ttl?: number
  lastModified?: string
  etag?: string
}

/**
 * Enhanced Cloudflare Cache Manager
 */
export class CloudflareCacheManager {
  private apiToken: string
  private zoneId: string
  private accountId?: string
  private baseUrl = 'https://api.cloudflare.com/client/v4'

  constructor(apiToken: string, zoneId: string, accountId?: string) {
    this.apiToken = apiToken
    this.zoneId = zoneId
    this.accountId = accountId
  }

  /**
   * Make authenticated API request to Cloudflare
   */
  private async apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    if (!shouldUseFeature('USE_HYBRID_CDN')) {
      throw new Error('Hybrid CDN feature is disabled')
    }

    const url = `${this.baseUrl}${endpoint}`
    const headers = {
      'Authorization': `Bearer ${this.apiToken}`,
      'Content-Type': 'application/json',
      ...options.headers,
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(`Cloudflare API error: ${JSON.stringify(data.errors)}`)
      }

      updateFeatureFlagMetrics('USE_HYBRID_CDN', true)
      return data.result
    } catch (error) {
      updateFeatureFlagMetrics('USE_HYBRID_CDN', false, error)
      throw error
    }
  }

  /**
   * Purge cache with various options
   */
  async purgeCache(options: CachePurgeOptions = {}): Promise<boolean> {
    try {
      console.log('🗑️ Purging cache with options:', options)

      let body: any = {}

      if (options.everything) {
        body.purge_everything = true
      } else {
        if (options.urls) body.files = options.urls
        if (options.tags) body.tags = options.tags
        if (options.hosts) body.hosts = options.hosts
        if (options.prefixes) body.prefixes = options.prefixes
      }

      const result = await this.apiRequest(`/zones/${this.zoneId}/purge_cache`, {
        method: 'POST',
        body: JSON.stringify(body),
      })

      console.log('✅ Cache purged successfully:', result.id)
      return true
    } catch (error) {
      console.error('❌ Cache purge failed:', error)
      return false
    }
  }

  /**
   * Get cache analytics
   */
  async getCacheAnalytics(since?: Date): Promise<CacheAnalytics> {
    try {
      const sinceParam = since ? `&since=${since.toISOString()}` : ''
      const endpoint = `/zones/${this.zoneId}/analytics/dashboard?continuous=true${sinceParam}`
      
      const data = await this.apiRequest(endpoint)

      // Process analytics data
      const analytics: CacheAnalytics = {
        cacheHitRatio: this.calculateCacheHitRatio(data),
        bandwidth: data.totals?.bandwidth?.all || 0,
        requests: data.totals?.requests?.all || 0,
        errors: data.totals?.requests?.http_status?.['4xx'] + data.totals?.requests?.http_status?.['5xx'] || 0,
        avgResponseTime: this.calculateAvgResponseTime(data),
        p95ResponseTime: this.calculateP95ResponseTime(data),
        lastUpdated: new Date().toISOString(),
      }

      console.log('📊 Cache analytics retrieved:', analytics)
      return analytics
    } catch (error) {
      console.error('❌ Failed to get cache analytics:', error)
      throw error
    }
  }

  /**
   * Get cache status for a specific URL
   */
  async getCacheStatus(url: string): Promise<CacheStatus> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: {
          'CF-Cache-Status': '1', // Request cache status header
        },
      })

      const cacheStatus = response.headers.get('CF-Cache-Status') as CacheStatus['status'] || 'UNKNOWN'
      const age = response.headers.get('Age')
      const cacheControl = response.headers.get('Cache-Control')
      const lastModified = response.headers.get('Last-Modified')
      const etag = response.headers.get('ETag')

      // Parse TTL from Cache-Control header
      let ttl: number | undefined
      if (cacheControl) {
        const maxAgeMatch = cacheControl.match(/max-age=(\d+)/)
        if (maxAgeMatch) {
          ttl = parseInt(maxAgeMatch[1])
        }
      }

      const status: CacheStatus = {
        status: cacheStatus,
        age: age ? parseInt(age) : undefined,
        ttl,
        lastModified: lastModified || undefined,
        etag: etag || undefined,
      }

      console.log(`🔍 Cache status for ${url}:`, status)
      return status
    } catch (error) {
      console.error(`❌ Failed to get cache status for ${url}:`, error)
      throw error
    }
  }

  /**
   * Create cache rule
   */
  async createCacheRule(rule: CacheRule): Promise<string> {
    try {
      console.log('📝 Creating cache rule:', rule)

      const pageRule = {
        targets: [
          {
            target: 'url',
            constraint: {
              operator: 'matches',
              value: rule.pattern,
            },
          },
        ],
        actions: [
          {
            id: 'cache_level',
            value: rule.cacheLevel,
          },
        ],
        status: 'active',
      }

      // Add TTL actions if specified
      if (rule.edgeCacheTtl) {
        pageRule.actions.push({
          id: 'edge_cache_ttl',
          value: rule.edgeCacheTtl,
        })
      }

      if (rule.browserCacheTtl) {
        pageRule.actions.push({
          id: 'browser_cache_ttl',
          value: rule.browserCacheTtl,
        })
      }

      const result = await this.apiRequest(`/zones/${this.zoneId}/pagerules`, {
        method: 'POST',
        body: JSON.stringify(pageRule),
      })

      console.log('✅ Cache rule created:', result.id)
      return result.id
    } catch (error) {
      console.error('❌ Failed to create cache rule:', error)
      throw error
    }
  }

  /**
   * Delete cache rule
   */
  async deleteCacheRule(ruleId: string): Promise<boolean> {
    try {
      console.log(`🗑️ Deleting cache rule: ${ruleId}`)

      await this.apiRequest(`/zones/${this.zoneId}/pagerules/${ruleId}`, {
        method: 'DELETE',
      })

      console.log('✅ Cache rule deleted successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to delete cache rule:', error)
      return false
    }
  }

  /**
   * Get all cache rules
   */
  async getCacheRules(): Promise<any[]> {
    try {
      const rules = await this.apiRequest(`/zones/${this.zoneId}/pagerules`)
      console.log(`📋 Retrieved ${rules.length} cache rules`)
      return rules
    } catch (error) {
      console.error('❌ Failed to get cache rules:', error)
      throw error
    }
  }

  /**
   * Preload cache for specific URLs
   */
  async preloadCache(urls: string[]): Promise<boolean> {
    try {
      console.log('🚀 Preloading cache for URLs:', urls)

      // Preload by making requests to the URLs
      const promises = urls.map(async (url) => {
        try {
          await fetch(url, { method: 'HEAD' })
          return true
        } catch {
          return false
        }
      })

      const results = await Promise.all(promises)
      const successCount = results.filter(Boolean).length

      console.log(`✅ Preloaded ${successCount}/${urls.length} URLs`)
      return successCount === urls.length
    } catch (error) {
      console.error('❌ Cache preload failed:', error)
      return false
    }
  }

  /**
   * Calculate cache hit ratio from analytics data
   */
  private calculateCacheHitRatio(data: any): number {
    const cached = data.totals?.requests?.cached || 0
    const total = data.totals?.requests?.all || 1
    return (cached / total) * 100
  }

  /**
   * Calculate average response time from analytics data
   */
  private calculateAvgResponseTime(data: any): number {
    // Simplified calculation - in real implementation, this would use actual response time data
    return data.totals?.responseTimeAvg || 150
  }

  /**
   * Calculate 95th percentile response time from analytics data
   */
  private calculateP95ResponseTime(data: any): number {
    // Simplified calculation - in real implementation, this would use actual response time data
    return data.totals?.responseTimeP95 || 300
  }
}

/**
 * Create cache manager instance
 */
export function createCacheManager(): CloudflareCacheManager | null {
  const apiToken = process.env.CLOUDFLARE_API_TOKEN
  const zoneId = process.env.CLOUDFLARE_ZONE_ID
  const accountId = process.env.CLOUDFLARE_ACCOUNT_ID

  if (!apiToken || !zoneId) {
    console.warn('Cloudflare credentials not configured for cache management')
    return null
  }

  return new CloudflareCacheManager(apiToken, zoneId, accountId)
}

/**
 * Utility function to purge cache for common patterns
 */
export async function purgeCommonCache(): Promise<boolean> {
  const cacheManager = createCacheManager()
  if (!cacheManager) return false

  return await cacheManager.purgeCache({
    tags: ['static', 'api', 'images'],
  })
}

/**
 * Utility function to get cache performance summary
 */
export async function getCachePerformanceSummary(): Promise<CacheAnalytics | null> {
  const cacheManager = createCacheManager()
  if (!cacheManager) return null

  try {
    return await cacheManager.getCacheAnalytics()
  } catch (error) {
    console.error('Failed to get cache performance summary:', error)
    return null
  }
}
