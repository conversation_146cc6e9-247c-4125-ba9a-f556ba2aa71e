/**
 * Cloudflare CDN Configuration for Syndicaps
 *
 * Enhanced CDN configuration service with caching rules, optimization settings,
 * performance monitoring, and feature flag integration for hybrid deployment.
 */

import { shouldUseFeature, updateFeatureFlagMetrics } from '@/lib/config/featureFlags'

export interface CacheRule {
  pattern: string
  cacheControl: string
  edgeCacheTtl: number
  browserTtl?: number
  bypassOnCookie?: boolean
  cacheKey?: string[]
  headers?: Record<string, string>
  optimization?: {
    webp?: boolean
    avif?: boolean
    quality?: number
    minify?: boolean
    polish?: 'off' | 'lossy' | 'lossless'
  }
}

export interface OptimizationSettings {
  minify: {
    html: boolean
    css: boolean
    js: boolean
  }
  compression: {
    gzip: boolean
    brotli: boolean
  }
  images: {
    webp: boolean
    avif: boolean
    polish: 'off' | 'lossy' | 'lossless'
    mirage: boolean
  }
  http3: boolean
  earlyHints: boolean
  rocketLoader: boolean
}

export interface SecuritySettings {
  hotlinkProtection: boolean
  alwaysUseHttps: boolean
  minTlsVersion: '1.0' | '1.1' | '1.2' | '1.3'
  hsts: {
    enabled: boolean
    maxAge: number
    includeSubdomains: boolean
  }
}

export interface PerformanceMetrics {
  cacheHitRatio: number
  bandwidth: number
  requests: number
  errors: number
  avgResponseTime: number
  p95ResponseTime: number
}

export interface CDNConfig {
  static: CacheRule
  images: CacheRule
  api: CacheRule
  html: CacheRule
  admin: CacheRule
  serviceWorker: CacheRule
  optimization: OptimizationSettings
  security: SecuritySettings
  performance: {
    http2: boolean
    http3: boolean
    zeroRtt: boolean
    tieredCaching: boolean
  }
}

/**
 * Enhanced CDN caching rules configuration with feature flag integration
 */
export const CDN_RULES: CDNConfig = {
  // Static assets - long cache with immutable
  static: {
    pattern: '*.{js,css,woff,woff2,ttf,eot,ico}',
    cacheControl: 'public, max-age=31536000, immutable',
    edgeCacheTtl: 31536000,
    browserTtl: 31536000,
    headers: {
      'Cache-Control': 'public, max-age=31536000, immutable',
      'Vary': 'Accept-Encoding'
    },
    optimization: {
      minify: true,
      polish: 'lossless'
    }
  },

  // Images - medium cache with optimization
  images: {
    pattern: '*.{jpg,jpeg,png,gif,webp,avif,svg}',
    cacheControl: 'public, max-age=604800',
    edgeCacheTtl: 604800,
    browserTtl: 604800,
    headers: {
      'Cache-Control': 'public, max-age=604800',
      'Vary': 'Accept, Accept-Encoding'
    },
    optimization: {
      webp: true,
      avif: true,
      quality: 85,
      polish: 'lossy'
    }
  },
  
  // API responses - short cache with auth bypass
  api: {
    pattern: '/api/*',
    cacheControl: 'public, max-age=0, s-maxage=300',
    edgeCacheTtl: 300,
    browserTtl: 0,
    bypassOnCookie: true,
    cacheKey: ['$uri', '$args', '$http_authorization'],
    headers: {
      'Cache-Control': 'public, max-age=0, s-maxage=300',
      'Vary': 'Authorization, Accept-Encoding'
    }
  },

  // HTML pages - very short cache
  html: {
    pattern: '*.html',
    cacheControl: 'public, max-age=0, s-maxage=3600',
    edgeCacheTtl: 3600,
    browserTtl: 0,
    bypassOnCookie: true,
    headers: {
      'Cache-Control': 'public, max-age=0, s-maxage=3600',
      'Vary': 'Accept-Encoding, Cookie'
    },
    optimization: {
      minify: true
    }
  },

  // Admin pages - no cache
  admin: {
    pattern: '/admin/*',
    cacheControl: 'private, no-cache, no-store, must-revalidate',
    edgeCacheTtl: 0,
    browserTtl: 0,
    bypassOnCookie: true,
    headers: {
      'Cache-Control': 'private, no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  },

  // Service worker - short cache
  serviceWorker: {
    pattern: '/sw.js',
    cacheControl: 'public, max-age=0, s-maxage=86400',
    edgeCacheTtl: 86400,
    browserTtl: 0,
    headers: {
      'Cache-Control': 'public, max-age=0, s-maxage=86400',
      'Service-Worker-Allowed': '/'
    }
  },

  // Optimization settings
  optimization: {
    minify: {
      html: true,
      css: true,
      js: true
    },
    compression: {
      gzip: true,
      brotli: true
    },
    images: {
      webp: true,
      avif: true,
      polish: 'lossy',
      mirage: true
    },
    http3: true,
    earlyHints: true,
    rocketLoader: false // Disabled for better compatibility
  },

  // Security settings
  security: {
    hotlinkProtection: true,
    alwaysUseHttps: true,
    minTlsVersion: '1.2',
    hsts: {
      enabled: true,
      maxAge: 31536000,
      includeSubdomains: true
    }
  },

  // Performance settings
  performance: {
    http2: true,
    http3: true,
    zeroRtt: true,
    tieredCaching: true
  }
}

/**
 * Get cache headers for a given URL
 */
export function getCacheHeaders(url: string): Record<string, string> {
  const headers: Record<string, string> = {}
  
  // Find matching rule
  let matchedRule: CacheRule | null = null
  
  for (const [ruleName, rule] of Object.entries(CDN_RULES)) {
    if (matchesPattern(url, rule.pattern)) {
      matchedRule = rule
      break
    }
  }
  
  if (!matchedRule) {
    // Default cache rule
    matchedRule = {
      pattern: '*',
      cacheControl: 'public, max-age=3600',
      edgeCacheTtl: 3600
    }
  }
  
  headers['Cache-Control'] = matchedRule.cacheControl
  headers['CDN-Cache-Control'] = `public, max-age=${matchedRule.edgeCacheTtl}`
  
  // Add optimization headers
  if (matchedRule.optimization?.webp) {
    headers['Vary'] = 'Accept'
  }
  
  return headers
}

/**
 * Check if URL matches a pattern
 */
function matchesPattern(url: string, pattern: string): boolean {
  // Convert glob pattern to regex
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\{([^}]+)\}/g, '($1)')
    .replace(/,/g, '|')
  
  const regex = new RegExp(`^${regexPattern}$`)
  return regex.test(url)
}

/**
 * CDN Performance Monitor
 */
export class CDNPerformanceMonitor {
  private metrics: Map<string, number[]> = new Map()
  
  recordCacheHit(url: string, cacheStatus: 'HIT' | 'MISS' | 'EXPIRED'): void {
    const key = `cache_${cacheStatus.toLowerCase()}`
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, [])
    }
    
    this.metrics.get(key)!.push(Date.now())
  }
  
  recordLoadTime(url: string, loadTime: number): void {
    const category = this.categorizeUrl(url)
    const key = `load_time_${category}`
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, [])
    }
    
    this.metrics.get(key)!.push(loadTime)
  }
  
  getCacheHitRate(): number {
    const hits = this.metrics.get('cache_hit')?.length || 0
    const misses = this.metrics.get('cache_miss')?.length || 0
    const total = hits + misses
    
    return total > 0 ? (hits / total) * 100 : 0
  }
  
  getAverageLoadTime(category: string): number {
    const times = this.metrics.get(`load_time_${category}`) || []
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0
  }
  
  generateReport(): CDNPerformanceReport {
    return {
      timestamp: new Date().toISOString(),
      cacheHitRate: this.getCacheHitRate(),
      averageLoadTimes: {
        static: this.getAverageLoadTime('static'),
        images: this.getAverageLoadTime('images'),
        api: this.getAverageLoadTime('api'),
        html: this.getAverageLoadTime('html')
      },
      totalRequests: Array.from(this.metrics.values()).reduce(
        (total, arr) => total + arr.length, 0
      )
    }
  }
  
  private categorizeUrl(url: string): string {
    if (matchesPattern(url, CDN_RULES.static.pattern)) return 'static'
    if (matchesPattern(url, CDN_RULES.images.pattern)) return 'images'
    if (matchesPattern(url, CDN_RULES.api.pattern)) return 'api'
    if (matchesPattern(url, CDN_RULES.html.pattern)) return 'html'
    return 'other'
  }
}

export interface CDNPerformanceReport {
  timestamp: string
  cacheHitRate: number
  averageLoadTimes: {
    static: number
    images: number
    api: number
    html: number
  }
  totalRequests: number
}

/**
 * Global CDN performance monitor instance
 */
export const cdnPerformanceMonitor = new CDNPerformanceMonitor()

/**
 * Enhanced CDN Configuration Manager
 */
export class CDNConfigManager {
  private config: CDNConfig
  private accountId: string
  private zoneId?: string
  private apiToken: string
  private cacheManager?: CloudflareCacheManager

  constructor(accountId: string, apiToken: string, zoneId?: string) {
    this.accountId = accountId
    this.apiToken = apiToken
    this.zoneId = zoneId
    this.config = { ...CDN_RULES }

    if (zoneId) {
      this.cacheManager = new CloudflareCacheManager(apiToken, zoneId)
    }
  }

  /**
   * Get cache rule for a specific URL path
   */
  getCacheRule(path: string): CacheRule | null {
    // Check each rule pattern
    for (const [key, rule] of Object.entries(this.config)) {
      if (key === 'optimization' || key === 'security' || key === 'performance') continue

      const cacheRule = rule as CacheRule
      if (matchesPattern(path, cacheRule.pattern)) {
        return cacheRule
      }
    }

    return null
  }

  /**
   * Deploy configuration to Cloudflare
   */
  async deployConfig(): Promise<boolean> {
    if (!shouldUseFeature('USE_HYBRID_CDN')) {
      console.log('Hybrid CDN feature disabled, skipping deployment')
      updateFeatureFlagMetrics('USE_HYBRID_CDN', false, 'Feature disabled')
      return false
    }

    try {
      console.log('Deploying CDN configuration to Cloudflare...')

      // Validate configuration first
      const validation = this.validateConfig()
      if (!validation.valid) {
        throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`)
      }

      // Deploy settings (simulated for now)
      await this.deploySettings()

      console.log('CDN configuration deployed successfully')
      updateFeatureFlagMetrics('USE_HYBRID_CDN', true)
      return true
    } catch (error) {
      console.error('Failed to deploy CDN configuration:', error)
      updateFeatureFlagMetrics('USE_HYBRID_CDN', false, error)
      return false
    }
  }

  /**
   * Deploy settings to Cloudflare (simulated)
   */
  private async deploySettings(): Promise<void> {
    console.log('Deploying optimization settings:', this.config.optimization)
    console.log('Deploying security settings:', this.config.security)
    console.log('Deploying performance settings:', this.config.performance)

    // Simulate API calls
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  /**
   * Validate configuration
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // Validate cache rules
    for (const [key, rule] of Object.entries(this.config)) {
      if (['optimization', 'security', 'performance'].includes(key)) continue

      const cacheRule = rule as CacheRule
      if (!cacheRule.pattern) {
        errors.push(`Cache rule ${key} missing pattern`)
      }
      if (cacheRule.edgeCacheTtl < 0) {
        errors.push(`Invalid TTL for ${key}: ${cacheRule.edgeCacheTtl}`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      // Get analytics from Cloudflare if available
      let analytics = null
      if (this.cacheManager) {
        analytics = await this.cacheManager.getCacheAnalytics()
      }

      // Return simulated metrics for now
      const metrics: PerformanceMetrics = {
        cacheHitRatio: analytics?.result?.cacheHitRatio || 85.5,
        bandwidth: analytics?.result?.bandwidth || 1024 * 1024 * 100,
        requests: analytics?.result?.requests || 10000,
        errors: analytics?.result?.errors || 25,
        avgResponseTime: 150,
        p95ResponseTime: 300
      }

      updateFeatureFlagMetrics('USE_HYBRID_CDN', true)
      return metrics
    } catch (error) {
      updateFeatureFlagMetrics('USE_HYBRID_CDN', false, error)
      throw error
    }
  }

  /**
   * Purge cache
   */
  async purgeCache(urls?: string[]): Promise<boolean> {
    if (!this.cacheManager) {
      console.warn('Cache manager not available (no zone ID)')
      return false
    }

    return await this.cacheManager.purgeCache(urls)
  }

  /**
   * Get current configuration
   */
  getConfig(): CDNConfig {
    return { ...this.config }
  }
}

/**
 * Cloudflare API client for cache management
 */
export class CloudflareCacheManager {
  private apiToken: string
  private zoneId: string

  constructor(apiToken: string, zoneId: string) {
    this.apiToken = apiToken
    this.zoneId = zoneId
  }

  async purgeCache(urls?: string[]): Promise<boolean> {
    try {
      const body = urls ? { files: urls } : { purge_everything: true }

      const response = await fetch(
        `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/purge_cache`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        }
      )

      const result = await response.json()
      return result.success
    } catch (error) {
      console.error('Cache purge failed:', error)
      return false
    }
  }

  async getCacheAnalytics(): Promise<any> {
    try {
      const response = await fetch(
        `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/analytics/dashboard`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      )

      return await response.json()
    } catch (error) {
      console.error('Failed to fetch cache analytics:', error)
      return null
    }
  }
}

/**
 * Create CDN configuration manager instance
 */
export function createCDNManager(): CDNConfigManager | null {
  const accountId = process.env.CLOUDFLARE_ACCOUNT_ID
  const apiToken = process.env.CLOUDFLARE_API_TOKEN
  const zoneId = process.env.CLOUDFLARE_ZONE_ID

  if (!accountId || !apiToken) {
    console.warn('Cloudflare credentials not configured')
    return null
  }

  return new CDNConfigManager(accountId, apiToken, zoneId)
}
