/**
 * Hybrid Image Upload Service
 * Automatically selects between R2 and Firebase storage based on feature flags
 */

import { r2StorageService, R2UploadOptions, R2UploadResult } from './r2StorageService'
import { shouldUseFeature } from '../config/featureFlags'
import { hybridPerformanceMonitor } from '../monitoring/hybridPerformanceMonitor'
import { storage } from '../firebase/config'
import { ref, uploadBytes, getDownloadURL, deleteObject, getMetadata } from 'firebase/storage'

export interface HybridUploadOptions {
  file: File | Buffer
  path: string
  metadata?: Record<string, string>
  bucketType?: 'images' | 'documents' | 'temp'
  forceProvider?: 'r2' | 'firebase'
  generateThumbnail?: boolean
  optimizeImage?: boolean
}

export interface HybridUploadResult {
  success: boolean
  url: string
  provider: 'r2' | 'firebase'
  key?: string
  size: number
  contentType: string
  thumbnailUrl?: string
  error?: string
  fallbackUsed?: boolean
}

export interface HybridDeleteOptions {
  url: string
  path?: string
  provider?: 'r2' | 'firebase'
}

export interface ImageOptimizationOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
  progressive?: boolean
}

export class HybridImageUploadService {
  private readonly defaultOptimization: ImageOptimizationOptions = {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 85,
    format: 'webp',
    progressive: true
  }

  /**
   * Upload an image using the optimal storage provider
   */
  async uploadImage(options: HybridUploadOptions): Promise<HybridUploadResult> {
    const startTime = performance.now()
    let provider: 'r2' | 'firebase' = 'firebase' // Default fallback
    let fallbackUsed = false

    try {
      // Determine which provider to use
      provider = this.selectProvider(options.forceProvider)

      // Validate file
      const validationResult = this.validateFile(options.file)
      if (!validationResult.valid) {
        throw new Error(validationResult.error)
      }

      // Optimize image if requested
      let processedFile = options.file
      if (options.optimizeImage && this.isImageFile(options.file)) {
        processedFile = await this.optimizeImage(options.file, this.defaultOptimization)
      }

      // Convert File to Buffer if needed for R2
      let fileBuffer: Buffer
      let contentType: string
      let size: number

      if (processedFile instanceof File) {
        fileBuffer = Buffer.from(await processedFile.arrayBuffer())
        contentType = processedFile.type
        size = processedFile.size
      } else {
        fileBuffer = processedFile
        contentType = this.detectContentType(options.path)
        size = processedFile.length
      }

      let result: HybridUploadResult

      if (provider === 'r2') {
        result = await this.uploadToR2(options, fileBuffer, contentType, size)
      } else {
        result = await this.uploadToFirebase(options, processedFile as File)
      }

      // If primary provider fails, try fallback
      if (!result.success && !options.forceProvider) {
        console.warn(`${provider} upload failed, trying fallback provider`)
        fallbackUsed = true
        
        if (provider === 'r2') {
          result = await this.uploadToFirebase(options, processedFile as File)
          provider = 'firebase'
        } else {
          result = await this.uploadToR2(options, fileBuffer, contentType, size)
          provider = 'r2'
        }
      }

      // Generate thumbnail if requested and upload was successful
      if (result.success && options.generateThumbnail && this.isImageFile(options.file)) {
        try {
          const thumbnailResult = await this.generateAndUploadThumbnail(options, processedFile, provider)
          if (thumbnailResult.success) {
            result.thumbnailUrl = thumbnailResult.url
          }
        } catch (error) {
          console.warn('Thumbnail generation failed:', error)
          // Don't fail the main upload for thumbnail issues
        }
      }

      // Record performance metrics
      const duration = performance.now() - startTime
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: duration,
        unit: 'ms',
        source: provider === 'r2' ? 'cloudflare' : 'firebase',
        metadata: {
          operation: 'hybrid_image_upload',
          provider,
          fallbackUsed,
          size,
          optimized: options.optimizeImage || false,
          thumbnail: options.generateThumbnail || false
        }
      })

      return {
        ...result,
        provider,
        fallbackUsed
      }

    } catch (error) {
      // Record error metrics
      const duration = performance.now() - startTime
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'error_rate',
        value: 1,
        unit: 'count',
        source: provider === 'r2' ? 'cloudflare' : 'firebase',
        metadata: {
          operation: 'hybrid_image_upload',
          provider,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })

      return {
        success: false,
        url: '',
        provider,
        size: 0,
        contentType: '',
        error: error instanceof Error ? error.message : 'Upload failed',
        fallbackUsed
      }
    }
  }

  /**
   * Delete an image from the appropriate storage provider
   */
  async deleteImage(options: HybridDeleteOptions): Promise<{ success: boolean; error?: string }> {
    try {
      const provider = this.detectProviderFromUrl(options.url) || options.provider

      if (!provider) {
        throw new Error('Cannot determine storage provider for deletion')
      }

      if (provider === 'r2') {
        const key = this.extractKeyFromR2Url(options.url) || options.path
        if (!key) {
          throw new Error('Cannot extract key from R2 URL')
        }

        return await r2StorageService.delete({
          bucketType: 'images', // Default to images bucket
          key
        })
      } else {
        const path = options.path || this.extractPathFromFirebaseUrl(options.url)
        if (!path) {
          throw new Error('Cannot extract path from Firebase URL')
        }

        const fileRef = ref(storage, path)
        await deleteObject(fileRef)
        return { success: true }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed'
      }
    }
  }

  /**
   * Get image metadata
   */
  async getImageMetadata(url: string): Promise<{ success: boolean; metadata?: any; error?: string }> {
    try {
      const provider = this.detectProviderFromUrl(url)

      if (!provider) {
        throw new Error('Cannot determine storage provider')
      }

      if (provider === 'r2') {
        const key = this.extractKeyFromR2Url(url)
        if (!key) {
          throw new Error('Cannot extract key from R2 URL')
        }

        return await r2StorageService.getMetadata({
          bucketType: 'images',
          key
        })
      } else {
        const path = this.extractPathFromFirebaseUrl(url)
        if (!path) {
          throw new Error('Cannot extract path from Firebase URL')
        }

        const fileRef = ref(storage, path)
        const metadata = await getMetadata(fileRef)
        
        return {
          success: true,
          metadata: {
            name: metadata.name,
            size: metadata.size,
            contentType: metadata.contentType,
            timeCreated: metadata.timeCreated,
            updated: metadata.updated,
            customMetadata: metadata.customMetadata
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get metadata'
      }
    }
  }

  /**
   * Select the optimal storage provider
   */
  private selectProvider(forceProvider?: 'r2' | 'firebase'): 'r2' | 'firebase' {
    if (forceProvider) {
      return forceProvider
    }

    // Check feature flags to determine provider
    if (shouldUseFeature('USE_R2_STORAGE')) {
      return 'r2'
    }

    return 'firebase'
  }

  /**
   * Upload to R2 storage
   */
  private async uploadToR2(
    options: HybridUploadOptions,
    fileBuffer: Buffer,
    contentType: string,
    size: number
  ): Promise<HybridUploadResult> {
    const bucketType = options.bucketType || 'images'
    const key = this.generateR2Key(options.path)

    const uploadOptions: R2UploadOptions = {
      bucketType,
      key,
      body: fileBuffer,
      contentType,
      metadata: options.metadata,
      cacheControl: 'public, max-age=31536000', // 1 year cache
      acl: 'public-read'
    }

    const result = await r2StorageService.upload(uploadOptions)

    return {
      success: result.success,
      url: result.url || '',
      provider: 'r2',
      key: result.key,
      size,
      contentType,
      error: result.error
    }
  }

  /**
   * Upload to Firebase storage
   */
  private async uploadToFirebase(
    options: HybridUploadOptions,
    file: File
  ): Promise<HybridUploadResult> {
    const storageRef = ref(storage, options.path)
    
    const metadata = {
      contentType: file.type,
      customMetadata: options.metadata || {}
    }

    const snapshot = await uploadBytes(storageRef, file, metadata)
    const url = await getDownloadURL(snapshot.ref)

    return {
      success: true,
      url,
      provider: 'firebase',
      size: file.size,
      contentType: file.type
    }
  }

  /**
   * Generate and upload thumbnail
   */
  private async generateAndUploadThumbnail(
    options: HybridUploadOptions,
    file: File | Buffer,
    provider: 'r2' | 'firebase'
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      // Generate thumbnail (simplified - in real implementation, use canvas or image processing library)
      const thumbnailFile = await this.generateThumbnail(file)
      
      const thumbnailPath = this.generateThumbnailPath(options.path)
      const thumbnailOptions: HybridUploadOptions = {
        ...options,
        file: thumbnailFile,
        path: thumbnailPath,
        forceProvider: provider,
        generateThumbnail: false // Prevent infinite recursion
      }

      const result = await this.uploadImage(thumbnailOptions)
      return {
        success: result.success,
        url: result.url,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Thumbnail generation failed'
      }
    }
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: File | Buffer): { valid: boolean; error?: string } {
    if (file instanceof File) {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        return { valid: false, error: 'File size exceeds 10MB limit' }
      }

      // Check file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        return { valid: false, error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed' }
      }
    } else {
      // Buffer validation
      if (file.length > 10 * 1024 * 1024) {
        return { valid: false, error: 'File size exceeds 10MB limit' }
      }
    }

    return { valid: true }
  }

  /**
   * Check if file is an image
   */
  private isImageFile(file: File | Buffer): boolean {
    if (file instanceof File) {
      return file.type.startsWith('image/')
    }
    // For Buffer, we'd need to check magic bytes or rely on file extension
    return true // Simplified for now
  }

  /**
   * Optimize image (placeholder implementation)
   */
  private async optimizeImage(file: File | Buffer, options: ImageOptimizationOptions): Promise<File | Buffer> {
    // In a real implementation, this would use a library like sharp or canvas
    // For now, return the original file
    return file
  }

  /**
   * Generate thumbnail (placeholder implementation)
   */
  private async generateThumbnail(file: File | Buffer): Promise<File | Buffer> {
    // In a real implementation, this would generate a smaller version
    // For now, return the original file
    return file
  }

  /**
   * Generate R2 key from path
   */
  private generateR2Key(path: string): string {
    // Remove leading slash and ensure proper format
    return path.replace(/^\/+/, '')
  }

  /**
   * Generate thumbnail path
   */
  private generateThumbnailPath(originalPath: string): string {
    const pathParts = originalPath.split('.')
    const extension = pathParts.pop()
    const basePath = pathParts.join('.')
    return `${basePath}_thumb.${extension}`
  }

  /**
   * Detect content type from file path
   */
  private detectContentType(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase()
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg'
      case 'png':
        return 'image/png'
      case 'webp':
        return 'image/webp'
      case 'gif':
        return 'image/gif'
      default:
        return 'application/octet-stream'
    }
  }

  /**
   * Detect storage provider from URL
   */
  private detectProviderFromUrl(url: string): 'r2' | 'firebase' | null {
    if (url.includes('r2.cloudflarestorage.com') || url.includes('images.syndicaps.com')) {
      return 'r2'
    }
    if (url.includes('firebasestorage.googleapis.com')) {
      return 'firebase'
    }
    return null
  }

  /**
   * Extract key from R2 URL
   */
  private extractKeyFromR2Url(url: string): string | null {
    try {
      const urlObj = new URL(url)
      return urlObj.pathname.substring(1) // Remove leading slash
    } catch {
      return null
    }
  }

  /**
   * Extract path from Firebase URL
   */
  private extractPathFromFirebaseUrl(url: string): string | null {
    try {
      const urlObj = new URL(url)
      const pathMatch = urlObj.pathname.match(/\/o\/(.+)\?/)
      return pathMatch ? decodeURIComponent(pathMatch[1]) : null
    } catch {
      return null
    }
  }
}

// Export singleton instance
export const hybridImageUploadService = new HybridImageUploadService()
