/**
 * Presigned URL Service for Cloudflare R2
 * Enables secure direct client uploads to R2 storage
 */

import { r2StorageService } from './r2StorageService'
import { R2_CONFIG, isFileTypeAllowed, isFileSizeAllowed } from './r2Config'
import { shouldUseFeature } from '../config/featureFlags'
import { hybridPerformanceMonitor } from '../monitoring/hybridPerformanceMonitor'

export interface PresignedUrlRequest {
  fileName: string
  fileSize: number
  contentType: string
  bucketType: keyof typeof R2_CONFIG.buckets
  metadata?: Record<string, string>
  expiresIn?: number // seconds, default 3600 (1 hour)
  maxFileSize?: number
  allowedContentTypes?: string[]
}

export interface PresignedUrlResponse {
  success: boolean
  uploadUrl?: string
  downloadUrl?: string
  key?: string
  expiresAt?: Date
  fields?: Record<string, string>
  error?: string
  fallbackToFirebase?: boolean
}

export interface DirectUploadConfig {
  maxFileSize: number
  allowedContentTypes: string[]
  expirationTime: number
  requireAuthentication: boolean
  enableProgressTracking: boolean
  enableMetadata: boolean
}

export interface UploadProgress {
  uploadId: string
  fileName: string
  totalSize: number
  uploadedSize: number
  percentage: number
  status: 'pending' | 'uploading' | 'completed' | 'failed'
  startTime: Date
  estimatedCompletion?: Date
  error?: string
}

export class PresignedUrlService {
  private readonly defaultConfig: DirectUploadConfig = {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedContentTypes: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'application/pdf',
      'text/plain'
    ],
    expirationTime: 3600, // 1 hour
    requireAuthentication: true,
    enableProgressTracking: true,
    enableMetadata: true
  }

  private uploadProgress = new Map<string, UploadProgress>()

  /**
   * Generate presigned URL for direct upload to R2
   */
  async generateUploadUrl(request: PresignedUrlRequest): Promise<PresignedUrlResponse> {
    const startTime = performance.now()

    try {
      // Check if R2 storage is enabled
      if (!shouldUseFeature('USE_R2_STORAGE')) {
        return {
          success: false,
          error: 'R2 storage is not enabled',
          fallbackToFirebase: true
        }
      }

      // Validate request
      const validation = this.validateUploadRequest(request)
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        }
      }

      // Generate unique key for the file
      const key = this.generateFileKey(request.fileName, request.bucketType)

      // Generate presigned URL
      const urlResult = await r2StorageService.generatePresignedUrl(
        'upload',
        {
          bucketType: request.bucketType,
          key
        },
        request.expiresIn || this.defaultConfig.expirationTime
      )

      if (!urlResult.success) {
        throw new Error(urlResult.error || 'Failed to generate presigned URL')
      }

      // Generate download URL (public URL if available)
      const downloadUrl = this.generateDownloadUrl(request.bucketType, key)

      // Calculate expiration time
      const expiresAt = new Date(Date.now() + (request.expiresIn || this.defaultConfig.expirationTime) * 1000)

      // Record performance metrics
      const duration = performance.now() - startTime
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: duration,
        unit: 'ms',
        source: 'cloudflare',
        metadata: {
          operation: 'generate_presigned_url',
          bucket: request.bucketType,
          fileSize: request.fileSize,
          contentType: request.contentType
        }
      })

      return {
        success: true,
        uploadUrl: urlResult.url,
        downloadUrl,
        key,
        expiresAt,
        fields: this.generateUploadFields(request, key)
      }

    } catch (error) {
      // Record error metrics
      const duration = performance.now() - startTime
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'error_rate',
        value: 1,
        unit: 'count',
        source: 'cloudflare',
        metadata: {
          operation: 'generate_presigned_url',
          bucket: request.bucketType,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate presigned URL'
      }
    }
  }

  /**
   * Generate presigned URL for download
   */
  async generateDownloadUrl(
    bucketType: keyof typeof R2_CONFIG.buckets,
    key: string,
    expiresIn: number = 3600
  ): Promise<PresignedUrlResponse> {
    try {
      if (!shouldUseFeature('USE_R2_STORAGE')) {
        return {
          success: false,
          error: 'R2 storage is not enabled'
        }
      }

      const urlResult = await r2StorageService.generatePresignedUrl(
        'download',
        { bucketType, key },
        expiresIn
      )

      if (!urlResult.success) {
        throw new Error(urlResult.error || 'Failed to generate download URL')
      }

      const expiresAt = new Date(Date.now() + expiresIn * 1000)

      return {
        success: true,
        downloadUrl: urlResult.url,
        key,
        expiresAt
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate download URL'
      }
    }
  }

  /**
   * Track upload progress
   */
  startUploadTracking(uploadId: string, fileName: string, totalSize: number): void {
    if (!this.defaultConfig.enableProgressTracking) {
      return
    }

    this.uploadProgress.set(uploadId, {
      uploadId,
      fileName,
      totalSize,
      uploadedSize: 0,
      percentage: 0,
      status: 'pending',
      startTime: new Date()
    })
  }

  /**
   * Update upload progress
   */
  updateUploadProgress(uploadId: string, uploadedSize: number): void {
    const progress = this.uploadProgress.get(uploadId)
    if (!progress) {
      return
    }

    const percentage = Math.round((uploadedSize / progress.totalSize) * 100)
    const now = new Date()
    const elapsed = now.getTime() - progress.startTime.getTime()
    const rate = uploadedSize / elapsed // bytes per ms
    const remaining = progress.totalSize - uploadedSize
    const estimatedCompletion = new Date(now.getTime() + (remaining / rate))

    this.uploadProgress.set(uploadId, {
      ...progress,
      uploadedSize,
      percentage,
      status: percentage === 100 ? 'completed' : 'uploading',
      estimatedCompletion: percentage < 100 ? estimatedCompletion : undefined
    })
  }

  /**
   * Mark upload as failed
   */
  markUploadFailed(uploadId: string, error: string): void {
    const progress = this.uploadProgress.get(uploadId)
    if (!progress) {
      return
    }

    this.uploadProgress.set(uploadId, {
      ...progress,
      status: 'failed',
      error
    })
  }

  /**
   * Get upload progress
   */
  getUploadProgress(uploadId: string): UploadProgress | null {
    return this.uploadProgress.get(uploadId) || null
  }

  /**
   * Clean up completed uploads
   */
  cleanupCompletedUploads(olderThanHours: number = 24): void {
    const cutoff = new Date(Date.now() - olderThanHours * 60 * 60 * 1000)
    
    for (const [uploadId, progress] of this.uploadProgress.entries()) {
      if (progress.startTime < cutoff && (progress.status === 'completed' || progress.status === 'failed')) {
        this.uploadProgress.delete(uploadId)
      }
    }
  }

  /**
   * Validate upload request
   */
  private validateUploadRequest(request: PresignedUrlRequest): { valid: boolean; error?: string } {
    // Check file size
    if (!isFileSizeAllowed(request.bucketType, request.fileSize)) {
      return {
        valid: false,
        error: `File size ${request.fileSize} exceeds limit for bucket ${request.bucketType}`
      }
    }

    // Check content type
    if (!isFileTypeAllowed(request.bucketType, request.contentType)) {
      return {
        valid: false,
        error: `Content type ${request.contentType} not allowed for bucket ${request.bucketType}`
      }
    }

    // Check file name
    if (!request.fileName || request.fileName.trim().length === 0) {
      return {
        valid: false,
        error: 'File name is required'
      }
    }

    // Check for dangerous file names
    if (this.isDangerousFileName(request.fileName)) {
      return {
        valid: false,
        error: 'File name contains invalid characters'
      }
    }

    return { valid: true }
  }

  /**
   * Generate unique file key
   */
  private generateFileKey(fileName: string, bucketType: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const sanitizedName = this.sanitizeFileName(fileName)
    
    return `${bucketType}/${timestamp}-${random}-${sanitizedName}`
  }

  /**
   * Generate upload fields for form data
   */
  private generateUploadFields(request: PresignedUrlRequest, key: string): Record<string, string> {
    const fields: Record<string, string> = {
      'Content-Type': request.contentType,
      'Cache-Control': 'public, max-age=31536000',
      'x-amz-acl': 'public-read'
    }

    // Add metadata fields
    if (request.metadata && this.defaultConfig.enableMetadata) {
      for (const [metaKey, metaValue] of Object.entries(request.metadata)) {
        fields[`x-amz-meta-${metaKey}`] = metaValue
      }
    }

    return fields
  }

  /**
   * Generate public download URL
   */
  private generateDownloadUrl(bucketType: keyof typeof R2_CONFIG.buckets, key: string): string | undefined {
    const bucketConfig = R2_CONFIG.buckets[bucketType]
    if (bucketConfig?.publicUrl) {
      return `${bucketConfig.publicUrl}/${key}`
    }
    return undefined
  }

  /**
   * Check if file name is dangerous
   */
  private isDangerousFileName(fileName: string): boolean {
    const dangerousPatterns = [
      /\.\./,           // Directory traversal
      /[<>:"|?*]/,      // Windows invalid characters
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i, // Windows reserved names
      /^\./,            // Hidden files
      /\.$|\.$/         // Trailing dots
    ]

    return dangerousPatterns.some(pattern => pattern.test(fileName))
  }

  /**
   * Sanitize file name
   */
  private sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace invalid characters with underscore
      .replace(/_{2,}/g, '_')          // Replace multiple underscores with single
      .replace(/^_|_$/g, '')           // Remove leading/trailing underscores
      .toLowerCase()
  }

  /**
   * Get upload configuration for bucket type
   */
  getUploadConfig(bucketType: keyof typeof R2_CONFIG.buckets): DirectUploadConfig {
    const bucketConfig = R2_CONFIG.buckets[bucketType]
    
    return {
      ...this.defaultConfig,
      maxFileSize: bucketConfig.maxFileSize,
      allowedContentTypes: bucketConfig.allowedFileTypes
    }
  }

  /**
   * Validate upload completion
   */
  async validateUploadCompletion(
    bucketType: keyof typeof R2_CONFIG.buckets,
    key: string
  ): Promise<{ success: boolean; metadata?: any; error?: string }> {
    try {
      const result = await r2StorageService.getMetadata({ bucketType, key })
      
      if (result.success) {
        return {
          success: true,
          metadata: result.metadata
        }
      } else {
        return {
          success: false,
          error: result.error || 'Upload validation failed'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload validation failed'
      }
    }
  }
}

// Export singleton instance
export const presignedUrlService = new PresignedUrlService()
