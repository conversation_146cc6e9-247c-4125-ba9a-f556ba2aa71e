/**
 * Cloudflare R2 Storage Configuration
 * Provides configuration for R2 storage buckets, endpoints, and access settings
 */

export interface R2BucketConfig {
  name: string
  endpoint: string
  region: string
  publicUrl?: string
  corsEnabled: boolean
  maxFileSize: number // in bytes
  allowedFileTypes: string[]
  defaultTTL: number // in seconds
}

export interface R2AccountConfig {
  accountId: string
  accessKeyId: string
  secretAccessKey: string
  region: string
  endpoint: string
}

export interface R2StorageConfig {
  account: R2AccountConfig
  buckets: {
    images: R2BucketConfig
    backups: R2BucketConfig
    documents: R2BucketConfig
    temp: R2BucketConfig
  }
  features: {
    enablePresignedUrls: boolean
    enableDirectUpload: boolean
    enableCDNIntegration: boolean
    enableCompression: boolean
    enableMetadata: boolean
  }
  limits: {
    maxUploadSize: number
    maxConcurrentUploads: number
    uploadTimeout: number
    downloadTimeout: number
  }
  fallback: {
    enableFirebaseFallback: boolean
    fallbackThreshold: number // ms
    retryAttempts: number
    retryDelay: number // ms
  }
}

// Environment-based configuration
const getR2Config = (): R2StorageConfig => {
  const accountId = process.env.CLOUDFLARE_ACCOUNT_ID
  const accessKeyId = process.env.R2_ACCESS_KEY_ID
  const secretAccessKey = process.env.R2_SECRET_ACCESS_KEY
  const region = process.env.R2_REGION || 'auto'

  if (!accountId || !accessKeyId || !secretAccessKey) {
    throw new Error('Missing required R2 configuration environment variables')
  }

  const endpoint = `https://${accountId}.r2.cloudflarestorage.com`

  return {
    account: {
      accountId,
      accessKeyId,
      secretAccessKey,
      region,
      endpoint
    },
    buckets: {
      images: {
        name: process.env.R2_IMAGES_BUCKET || 'syndicaps-images',
        endpoint: `${endpoint}/${process.env.R2_IMAGES_BUCKET || 'syndicaps-images'}`,
        region,
        publicUrl: process.env.R2_IMAGES_PUBLIC_URL,
        corsEnabled: true,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedFileTypes: [
          'image/jpeg',
          'image/png',
          'image/webp',
          'image/gif',
          'image/svg+xml'
        ],
        defaultTTL: ******** // 1 year
      },
      backups: {
        name: process.env.R2_BACKUPS_BUCKET || 'syndicaps-backups',
        endpoint: `${endpoint}/${process.env.R2_BACKUPS_BUCKET || 'syndicaps-backups'}`,
        region,
        corsEnabled: false,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        allowedFileTypes: [
          'application/json',
          'application/zip',
          'application/gzip',
          'text/plain'
        ],
        defaultTTL: 2592000 // 30 days
      },
      documents: {
        name: process.env.R2_DOCUMENTS_BUCKET || 'syndicaps-documents',
        endpoint: `${endpoint}/${process.env.R2_DOCUMENTS_BUCKET || 'syndicaps-documents'}`,
        region,
        corsEnabled: true,
        maxFileSize: 50 * 1024 * 1024, // 50MB
        allowedFileTypes: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'text/csv'
        ],
        defaultTTL: 86400 // 1 day
      },
      temp: {
        name: process.env.R2_TEMP_BUCKET || 'syndicaps-temp',
        endpoint: `${endpoint}/${process.env.R2_TEMP_BUCKET || 'syndicaps-temp'}`,
        region,
        corsEnabled: true,
        maxFileSize: 20 * 1024 * 1024, // 20MB
        allowedFileTypes: ['*/*'], // Allow all file types for temp storage
        defaultTTL: 3600 // 1 hour
      }
    },
    features: {
      enablePresignedUrls: process.env.R2_ENABLE_PRESIGNED_URLS !== 'false',
      enableDirectUpload: process.env.R2_ENABLE_DIRECT_UPLOAD !== 'false',
      enableCDNIntegration: process.env.R2_ENABLE_CDN_INTEGRATION !== 'false',
      enableCompression: process.env.R2_ENABLE_COMPRESSION !== 'false',
      enableMetadata: process.env.R2_ENABLE_METADATA !== 'false'
    },
    limits: {
      maxUploadSize: parseInt(process.env.R2_MAX_UPLOAD_SIZE || '*********'), // 100MB default
      maxConcurrentUploads: parseInt(process.env.R2_MAX_CONCURRENT_UPLOADS || '5'),
      uploadTimeout: parseInt(process.env.R2_UPLOAD_TIMEOUT || '300000'), // 5 minutes
      downloadTimeout: parseInt(process.env.R2_DOWNLOAD_TIMEOUT || '60000') // 1 minute
    },
    fallback: {
      enableFirebaseFallback: process.env.R2_ENABLE_FIREBASE_FALLBACK !== 'false',
      fallbackThreshold: parseInt(process.env.R2_FALLBACK_THRESHOLD || '5000'), // 5 seconds
      retryAttempts: parseInt(process.env.R2_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.R2_RETRY_DELAY || '1000') // 1 second
    }
  }
}

// Validation functions
export const validateR2Config = (config: R2StorageConfig): boolean => {
  try {
    // Validate account configuration
    if (!config.account.accountId || !config.account.accessKeyId || !config.account.secretAccessKey) {
      throw new Error('Missing required account credentials')
    }

    // Validate bucket configurations
    for (const [bucketType, bucketConfig] of Object.entries(config.buckets)) {
      if (!bucketConfig.name || !bucketConfig.endpoint) {
        throw new Error(`Invalid configuration for bucket: ${bucketType}`)
      }

      if (bucketConfig.maxFileSize <= 0) {
        throw new Error(`Invalid max file size for bucket: ${bucketType}`)
      }

      if (!Array.isArray(bucketConfig.allowedFileTypes) || bucketConfig.allowedFileTypes.length === 0) {
        throw new Error(`Invalid allowed file types for bucket: ${bucketType}`)
      }
    }

    // Validate limits
    if (config.limits.maxUploadSize <= 0 || config.limits.maxConcurrentUploads <= 0) {
      throw new Error('Invalid upload limits configuration')
    }

    if (config.limits.uploadTimeout <= 0 || config.limits.downloadTimeout <= 0) {
      throw new Error('Invalid timeout configuration')
    }

    // Validate fallback configuration
    if (config.fallback.retryAttempts < 0 || config.fallback.retryDelay < 0) {
      throw new Error('Invalid fallback configuration')
    }

    return true
  } catch (error) {
    console.error('R2 configuration validation failed:', error)
    return false
  }
}

export const isFileTypeAllowed = (bucketType: keyof R2StorageConfig['buckets'], mimeType: string): boolean => {
  const config = getR2Config()
  const bucket = config.buckets[bucketType]
  
  if (!bucket) {
    return false
  }

  // Allow all types if wildcard is present
  if (bucket.allowedFileTypes.includes('*/*')) {
    return true
  }

  return bucket.allowedFileTypes.includes(mimeType)
}

export const isFileSizeAllowed = (bucketType: keyof R2StorageConfig['buckets'], fileSize: number): boolean => {
  const config = getR2Config()
  const bucket = config.buckets[bucketType]
  
  if (!bucket) {
    return false
  }

  return fileSize <= bucket.maxFileSize
}

export const getBucketConfig = (bucketType: keyof R2StorageConfig['buckets']): R2BucketConfig | null => {
  try {
    const config = getR2Config()
    return config.buckets[bucketType] || null
  } catch (error) {
    console.error('Failed to get bucket configuration:', error)
    return null
  }
}

export const getR2Endpoint = (bucketType: keyof R2StorageConfig['buckets']): string | null => {
  const bucketConfig = getBucketConfig(bucketType)
  return bucketConfig?.endpoint || null
}

export const getPublicUrl = (bucketType: keyof R2StorageConfig['buckets'], key: string): string | null => {
  const bucketConfig = getBucketConfig(bucketType)
  
  if (!bucketConfig?.publicUrl) {
    return null
  }

  return `${bucketConfig.publicUrl}/${key}`
}

// Connection testing
export const testR2Connection = async (): Promise<{ success: boolean; message: string; details?: any }> => {
  try {
    const config = getR2Config()
    
    // Validate configuration
    if (!validateR2Config(config)) {
      return {
        success: false,
        message: 'R2 configuration validation failed'
      }
    }

    // Test basic connectivity (this would be implemented in the R2StorageService)
    return {
      success: true,
      message: 'R2 configuration is valid and ready for use',
      details: {
        accountId: config.account.accountId,
        region: config.account.region,
        bucketsConfigured: Object.keys(config.buckets).length,
        featuresEnabled: Object.entries(config.features).filter(([_, enabled]) => enabled).length
      }
    }
  } catch (error) {
    return {
      success: false,
      message: `R2 connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error }
    }
  }
}

// Environment configuration template
export const generateEnvTemplate = (): string => {
  return `# Cloudflare R2 Storage Configuration
# Required variables
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
R2_ACCESS_KEY_ID=your_access_key_here
R2_SECRET_ACCESS_KEY=your_secret_key_here

# Optional variables (with defaults)
R2_REGION=auto
R2_IMAGES_BUCKET=syndicaps-images
R2_BACKUPS_BUCKET=syndicaps-backups
R2_DOCUMENTS_BUCKET=syndicaps-documents
R2_TEMP_BUCKET=syndicaps-temp

# Public URLs (if using custom domains)
R2_IMAGES_PUBLIC_URL=https://images.syndicaps.com
# R2_DOCUMENTS_PUBLIC_URL=https://docs.syndicaps.com

# Feature flags
R2_ENABLE_PRESIGNED_URLS=true
R2_ENABLE_DIRECT_UPLOAD=true
R2_ENABLE_CDN_INTEGRATION=true
R2_ENABLE_COMPRESSION=true
R2_ENABLE_METADATA=true

# Limits and timeouts
R2_MAX_UPLOAD_SIZE=*********  # 100MB
R2_MAX_CONCURRENT_UPLOADS=5
R2_UPLOAD_TIMEOUT=300000      # 5 minutes
R2_DOWNLOAD_TIMEOUT=60000     # 1 minute

# Fallback configuration
R2_ENABLE_FIREBASE_FALLBACK=true
R2_FALLBACK_THRESHOLD=5000    # 5 seconds
R2_RETRY_ATTEMPTS=3
R2_RETRY_DELAY=1000           # 1 second
`
}

// Export the main configuration
export const R2_CONFIG = getR2Config()

// Export default configuration for testing
export const DEFAULT_R2_CONFIG: R2StorageConfig = {
  account: {
    accountId: 'test-account-id',
    accessKeyId: 'test-access-key',
    secretAccessKey: 'test-secret-key',
    region: 'auto',
    endpoint: 'https://test-account-id.r2.cloudflarestorage.com'
  },
  buckets: {
    images: {
      name: 'test-images',
      endpoint: 'https://test-account-id.r2.cloudflarestorage.com/test-images',
      region: 'auto',
      corsEnabled: true,
      maxFileSize: 10 * 1024 * 1024,
      allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp'],
      defaultTTL: ********
    },
    backups: {
      name: 'test-backups',
      endpoint: 'https://test-account-id.r2.cloudflarestorage.com/test-backups',
      region: 'auto',
      corsEnabled: false,
      maxFileSize: 100 * 1024 * 1024,
      allowedFileTypes: ['application/json', 'application/zip'],
      defaultTTL: 2592000
    },
    documents: {
      name: 'test-documents',
      endpoint: 'https://test-account-id.r2.cloudflarestorage.com/test-documents',
      region: 'auto',
      corsEnabled: true,
      maxFileSize: 50 * 1024 * 1024,
      allowedFileTypes: ['application/pdf', 'text/plain'],
      defaultTTL: 86400
    },
    temp: {
      name: 'test-temp',
      endpoint: 'https://test-account-id.r2.cloudflarestorage.com/test-temp',
      region: 'auto',
      corsEnabled: true,
      maxFileSize: 20 * 1024 * 1024,
      allowedFileTypes: ['*/*'],
      defaultTTL: 3600
    }
  },
  features: {
    enablePresignedUrls: true,
    enableDirectUpload: true,
    enableCDNIntegration: true,
    enableCompression: true,
    enableMetadata: true
  },
  limits: {
    maxUploadSize: *********,
    maxConcurrentUploads: 5,
    uploadTimeout: 300000,
    downloadTimeout: 60000
  },
  fallback: {
    enableFirebaseFallback: true,
    fallbackThreshold: 5000,
    retryAttempts: 3,
    retryDelay: 1000
  }
}
