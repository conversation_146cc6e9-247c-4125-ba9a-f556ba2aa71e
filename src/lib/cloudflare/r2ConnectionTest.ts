/**
 * R2 Connection Testing Service
 * Comprehensive health checks and validation for Cloudflare R2 storage
 */

import { r2StorageService } from './r2StorageService'
import { R2_CONFIG, validateR2Config, testR2Connection } from './r2Config'
import { hybridPerformanceMonitor } from '../monitoring/hybridPerformanceMonitor'

export interface ConnectionTestResult {
  success: boolean
  testName: string
  duration: number
  message: string
  details?: any
  error?: string
  timestamp: string
}

export interface HealthCheckResult {
  overall: 'healthy' | 'degraded' | 'unhealthy'
  score: number // 0-100
  tests: ConnectionTestResult[]
  summary: {
    passed: number
    failed: number
    total: number
    averageResponseTime: number
  }
  recommendations?: string[]
}

export interface R2ServiceStatus {
  isAvailable: boolean
  lastChecked: Date
  uptime: number // percentage
  averageResponseTime: number
  errorRate: number // percentage
  recentErrors: string[]
}

export class R2ConnectionTestService {
  private readonly testTimeout = 30000 // 30 seconds
  private serviceStatus: R2ServiceStatus = {
    isAvailable: false,
    lastChecked: new Date(),
    uptime: 0,
    averageResponseTime: 0,
    errorRate: 0,
    recentErrors: []
  }

  /**
   * Run comprehensive health check
   */
  async runHealthCheck(): Promise<HealthCheckResult> {
    const startTime = performance.now()
    const tests: ConnectionTestResult[] = []

    console.log('Starting R2 health check...')

    // Test 1: Configuration validation
    tests.push(await this.testConfiguration())

    // Test 2: Basic connectivity
    tests.push(await this.testBasicConnectivity())

    // Test 3: Bucket access for each bucket type
    for (const bucketType of Object.keys(R2_CONFIG.buckets) as Array<keyof typeof R2_CONFIG.buckets>) {
      tests.push(await this.testBucketAccess(bucketType))
    }

    // Test 4: Upload/download operations
    tests.push(await this.testUploadDownload())

    // Test 5: Presigned URL generation
    tests.push(await this.testPresignedUrls())

    // Test 6: Metadata operations
    tests.push(await this.testMetadataOperations())

    // Test 7: Error handling
    tests.push(await this.testErrorHandling())

    // Calculate results
    const passed = tests.filter(t => t.success).length
    const failed = tests.length - passed
    const score = Math.round((passed / tests.length) * 100)
    const averageResponseTime = tests.reduce((sum, t) => sum + t.duration, 0) / tests.length

    let overall: 'healthy' | 'degraded' | 'unhealthy'
    if (score >= 90) {
      overall = 'healthy'
    } else if (score >= 70) {
      overall = 'degraded'
    } else {
      overall = 'unhealthy'
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(tests, overall)

    // Update service status
    this.updateServiceStatus(tests, overall)

    // Record health check metrics
    const totalDuration = performance.now() - startTime
    hybridPerformanceMonitor.addMetric({
      timestamp: new Date().toISOString(),
      metricType: 'response_time',
      value: totalDuration,
      unit: 'ms',
      source: 'cloudflare',
      metadata: {
        operation: 'r2_health_check',
        score,
        overall,
        testsRun: tests.length
      }
    })

    return {
      overall,
      score,
      tests,
      summary: {
        passed,
        failed,
        total: tests.length,
        averageResponseTime
      },
      recommendations
    }
  }

  /**
   * Test configuration validation
   */
  private async testConfiguration(): Promise<ConnectionTestResult> {
    const startTime = performance.now()
    
    try {
      const isValid = validateR2Config(R2_CONFIG)
      const duration = performance.now() - startTime

      if (isValid) {
        return {
          success: true,
          testName: 'Configuration Validation',
          duration,
          message: 'R2 configuration is valid',
          timestamp: new Date().toISOString(),
          details: {
            accountId: R2_CONFIG.account.accountId,
            region: R2_CONFIG.account.region,
            bucketsConfigured: Object.keys(R2_CONFIG.buckets).length,
            featuresEnabled: Object.entries(R2_CONFIG.features).filter(([_, enabled]) => enabled).length
          }
        }
      } else {
        throw new Error('Configuration validation failed')
      }
    } catch (error) {
      return {
        success: false,
        testName: 'Configuration Validation',
        duration: performance.now() - startTime,
        message: 'Configuration validation failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Test basic connectivity
   */
  private async testBasicConnectivity(): Promise<ConnectionTestResult> {
    const startTime = performance.now()
    
    try {
      const result = await Promise.race([
        testR2Connection(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Connection timeout')), this.testTimeout)
        )
      ])

      const duration = performance.now() - startTime

      if (result.success) {
        return {
          success: true,
          testName: 'Basic Connectivity',
          duration,
          message: result.message,
          timestamp: new Date().toISOString(),
          details: result.details
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      return {
        success: false,
        testName: 'Basic Connectivity',
        duration: performance.now() - startTime,
        message: 'Connection test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Test bucket access
   */
  private async testBucketAccess(bucketType: keyof typeof R2_CONFIG.buckets): Promise<ConnectionTestResult> {
    const startTime = performance.now()
    
    try {
      const result = await Promise.race([
        r2StorageService.list({ bucketType, maxKeys: 1 }),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Bucket access timeout')), this.testTimeout)
        )
      ])

      const duration = performance.now() - startTime

      if (result.success) {
        return {
          success: true,
          testName: `Bucket Access (${bucketType})`,
          duration,
          message: `Successfully accessed ${bucketType} bucket`,
          timestamp: new Date().toISOString(),
          details: {
            bucketType,
            objectsFound: result.objects.length,
            isTruncated: result.isTruncated
          }
        }
      } else {
        throw new Error(result.error || 'Bucket access failed')
      }
    } catch (error) {
      return {
        success: false,
        testName: `Bucket Access (${bucketType})`,
        duration: performance.now() - startTime,
        message: `Failed to access ${bucketType} bucket`,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Test upload and download operations
   */
  private async testUploadDownload(): Promise<ConnectionTestResult> {
    const startTime = performance.now()
    const testKey = `health-check-${Date.now()}.txt`
    const testContent = 'R2 health check test file'
    
    try {
      // Test upload
      const uploadResult = await r2StorageService.upload({
        bucketType: 'temp',
        key: testKey,
        body: Buffer.from(testContent),
        contentType: 'text/plain',
        metadata: { 'test-type': 'health-check' }
      })

      if (!uploadResult.success) {
        throw new Error(`Upload failed: ${uploadResult.error}`)
      }

      // Test download
      const downloadResult = await r2StorageService.download({
        bucketType: 'temp',
        key: testKey
      })

      if (!downloadResult.success) {
        throw new Error(`Download failed: ${downloadResult.error}`)
      }

      // Verify content
      const downloadedContent = downloadResult.data?.toString()
      if (downloadedContent !== testContent) {
        throw new Error('Downloaded content does not match uploaded content')
      }

      // Clean up test file
      await r2StorageService.delete({
        bucketType: 'temp',
        key: testKey
      })

      const duration = performance.now() - startTime

      return {
        success: true,
        testName: 'Upload/Download Operations',
        duration,
        message: 'Upload and download operations successful',
        timestamp: new Date().toISOString(),
        details: {
          uploadSize: Buffer.from(testContent).length,
          downloadSize: downloadResult.data?.length,
          contentMatch: true
        }
      }
    } catch (error) {
      // Attempt cleanup on error
      try {
        await r2StorageService.delete({
          bucketType: 'temp',
          key: testKey
        })
      } catch {
        // Ignore cleanup errors
      }

      return {
        success: false,
        testName: 'Upload/Download Operations',
        duration: performance.now() - startTime,
        message: 'Upload/download test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Test presigned URL generation
   */
  private async testPresignedUrls(): Promise<ConnectionTestResult> {
    const startTime = performance.now()
    
    try {
      if (!R2_CONFIG.features.enablePresignedUrls) {
        return {
          success: true,
          testName: 'Presigned URLs',
          duration: performance.now() - startTime,
          message: 'Presigned URLs are disabled (skipped)',
          timestamp: new Date().toISOString()
        }
      }

      const testKey = `presigned-test-${Date.now()}.txt`

      // Test upload URL generation
      const uploadUrlResult = await r2StorageService.generatePresignedUrl(
        'upload',
        { bucketType: 'temp', key: testKey },
        300 // 5 minutes
      )

      if (!uploadUrlResult.success) {
        throw new Error(`Upload URL generation failed: ${uploadUrlResult.error}`)
      }

      // Test download URL generation
      const downloadUrlResult = await r2StorageService.generatePresignedUrl(
        'download',
        { bucketType: 'temp', key: testKey },
        300 // 5 minutes
      )

      if (!downloadUrlResult.success) {
        throw new Error(`Download URL generation failed: ${downloadUrlResult.error}`)
      }

      const duration = performance.now() - startTime

      return {
        success: true,
        testName: 'Presigned URLs',
        duration,
        message: 'Presigned URL generation successful',
        timestamp: new Date().toISOString(),
        details: {
          uploadUrlGenerated: !!uploadUrlResult.url,
          downloadUrlGenerated: !!downloadUrlResult.url
        }
      }
    } catch (error) {
      return {
        success: false,
        testName: 'Presigned URLs',
        duration: performance.now() - startTime,
        message: 'Presigned URL test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Test metadata operations
   */
  private async testMetadataOperations(): Promise<ConnectionTestResult> {
    const startTime = performance.now()
    const testKey = `metadata-test-${Date.now()}.txt`
    
    try {
      // Upload a test file with metadata
      const uploadResult = await r2StorageService.upload({
        bucketType: 'temp',
        key: testKey,
        body: Buffer.from('metadata test'),
        contentType: 'text/plain',
        metadata: { 
          'test-type': 'metadata-check',
          'created-by': 'health-check'
        }
      })

      if (!uploadResult.success) {
        throw new Error(`Upload failed: ${uploadResult.error}`)
      }

      // Test metadata retrieval
      const metadataResult = await r2StorageService.getMetadata({
        bucketType: 'temp',
        key: testKey
      })

      if (!metadataResult.success) {
        throw new Error(`Metadata retrieval failed: ${metadataResult.error}`)
      }

      // Clean up
      await r2StorageService.delete({
        bucketType: 'temp',
        key: testKey
      })

      const duration = performance.now() - startTime

      return {
        success: true,
        testName: 'Metadata Operations',
        duration,
        message: 'Metadata operations successful',
        timestamp: new Date().toISOString(),
        details: {
          metadataRetrieved: !!metadataResult.metadata,
          size: metadataResult.metadata?.size,
          contentType: metadataResult.metadata?.contentType
        }
      }
    } catch (error) {
      // Attempt cleanup
      try {
        await r2StorageService.delete({
          bucketType: 'temp',
          key: testKey
        })
      } catch {
        // Ignore cleanup errors
      }

      return {
        success: false,
        testName: 'Metadata Operations',
        duration: performance.now() - startTime,
        message: 'Metadata test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Test error handling
   */
  private async testErrorHandling(): Promise<ConnectionTestResult> {
    const startTime = performance.now()
    
    try {
      // Test accessing non-existent object
      const result = await r2StorageService.download({
        bucketType: 'temp',
        key: 'non-existent-file-' + Date.now()
      })

      // This should fail gracefully
      if (result.success) {
        throw new Error('Expected error for non-existent file, but operation succeeded')
      }

      const duration = performance.now() - startTime

      return {
        success: true,
        testName: 'Error Handling',
        duration,
        message: 'Error handling working correctly',
        timestamp: new Date().toISOString(),
        details: {
          errorHandled: true,
          errorMessage: result.error
        }
      }
    } catch (error) {
      return {
        success: false,
        testName: 'Error Handling',
        duration: performance.now() - startTime,
        message: 'Error handling test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(tests: ConnectionTestResult[], overall: string): string[] {
    const recommendations: string[] = []
    const failedTests = tests.filter(t => !t.success)

    if (overall === 'unhealthy') {
      recommendations.push('R2 service is experiencing significant issues. Consider enabling Firebase fallback.')
    }

    if (overall === 'degraded') {
      recommendations.push('R2 service is partially degraded. Monitor closely and prepare for fallback.')
    }

    if (failedTests.some(t => t.testName.includes('Connectivity'))) {
      recommendations.push('Check network connectivity and R2 endpoint configuration.')
    }

    if (failedTests.some(t => t.testName.includes('Bucket Access'))) {
      recommendations.push('Verify bucket permissions and access credentials.')
    }

    if (failedTests.some(t => t.testName.includes('Upload/Download'))) {
      recommendations.push('Check R2 storage quotas and bucket policies.')
    }

    const slowTests = tests.filter(t => t.duration > 5000) // 5 seconds
    if (slowTests.length > 0) {
      recommendations.push('Some operations are slow. Consider optimizing network or checking R2 region settings.')
    }

    return recommendations
  }

  /**
   * Update service status based on test results
   */
  private updateServiceStatus(tests: ConnectionTestResult[], overall: string): void {
    const now = new Date()
    const passed = tests.filter(t => t.success).length
    const total = tests.length
    const averageResponseTime = tests.reduce((sum, t) => sum + t.duration, 0) / tests.length
    const errorRate = ((total - passed) / total) * 100

    this.serviceStatus = {
      isAvailable: overall !== 'unhealthy',
      lastChecked: now,
      uptime: overall === 'healthy' ? 100 : overall === 'degraded' ? 75 : 0,
      averageResponseTime,
      errorRate,
      recentErrors: tests.filter(t => !t.success).map(t => t.error || 'Unknown error').slice(0, 5)
    }
  }

  /**
   * Get current service status
   */
  getServiceStatus(): R2ServiceStatus {
    return { ...this.serviceStatus }
  }

  /**
   * Quick connectivity test
   */
  async quickConnectivityTest(): Promise<{ success: boolean; responseTime: number; error?: string }> {
    const startTime = performance.now()
    
    try {
      const result = await Promise.race([
        r2StorageService.list({ bucketType: 'images', maxKeys: 1 }),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Quick test timeout')), 5000)
        )
      ])

      const responseTime = performance.now() - startTime

      return {
        success: result.success,
        responseTime,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        responseTime: performance.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// Export singleton instance
export const r2ConnectionTestService = new R2ConnectionTestService()
