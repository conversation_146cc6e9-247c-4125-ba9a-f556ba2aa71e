/**
 * R2 Performance Monitor
 * Specialized monitoring for Cloudflare R2 storage operations
 */

import { hybridPerformanceMonitor, R2OperationMetrics } from '../monitoring/hybridPerformanceMonitor'

export interface R2PerformanceTracker {
  startTime: number
  operation: string
  bucketType: string
  fileSize?: number
  contentType?: string
  metadata?: Record<string, any>
}

export interface R2PerformanceSummary {
  period: string
  totalOperations: number
  successRate: number
  averageLatency: number
  p95Latency: number
  p99Latency: number
  throughputMBps: number
  errorsByType: Record<string, number>
  operationBreakdown: Record<string, {
    count: number
    averageLatency: number
    successRate: number
  }>
  bucketPerformance: Record<string, {
    operations: number
    averageLatency: number
    successRate: number
    totalBytes: number
  }>
}

export class R2PerformanceMonitor {
  private activeTrackers = new Map<string, R2PerformanceTracker>()
  private operationHistory: R2OperationMetrics[] = []
  private readonly maxHistorySize = 10000

  /**
   * Start tracking an R2 operation
   */
  startOperation(
    operationId: string,
    operation: 'upload' | 'download' | 'delete' | 'list' | 'metadata' | 'presigned_url',
    bucketType: string,
    options?: {
      fileSize?: number
      contentType?: string
      metadata?: Record<string, any>
    }
  ): void {
    this.activeTrackers.set(operationId, {
      startTime: performance.now(),
      operation,
      bucketType,
      fileSize: options?.fileSize,
      contentType: options?.contentType,
      metadata: options?.metadata
    })
  }

  /**
   * Complete tracking an R2 operation
   */
  completeOperation(
    operationId: string,
    success: boolean,
    options?: {
      error?: string
      retryCount?: number
      fallbackUsed?: boolean
      actualFileSize?: number
    }
  ): void {
    const tracker = this.activeTrackers.get(operationId)
    if (!tracker) {
      console.warn(`No active tracker found for operation: ${operationId}`)
      return
    }

    const duration = performance.now() - tracker.startTime
    const fileSize = options?.actualFileSize || tracker.fileSize || 0
    const throughput = fileSize > 0 && duration > 0 ? (fileSize / duration) * 1000 : undefined // bytes per second

    const metrics: R2OperationMetrics = {
      operation: tracker.operation as any,
      bucketType: tracker.bucketType,
      fileSize,
      contentType: tracker.contentType,
      duration,
      success,
      error: options?.error,
      throughput,
      retryCount: options?.retryCount,
      fallbackUsed: options?.fallbackUsed
    }

    // Record in hybrid performance monitor
    hybridPerformanceMonitor.recordR2Operation(metrics)

    // Store in local history
    this.operationHistory.push(metrics)
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory.shift()
    }

    // Clean up tracker
    this.activeTrackers.delete(operationId)
  }

  /**
   * Record a failed operation
   */
  recordFailure(
    operationId: string,
    error: string,
    options?: {
      retryCount?: number
      fallbackUsed?: boolean
    }
  ): void {
    this.completeOperation(operationId, false, {
      error,
      retryCount: options?.retryCount,
      fallbackUsed: options?.fallbackUsed
    })
  }

  /**
   * Get performance summary for a time period
   */
  getPerformanceSummary(periodMinutes: number = 60): R2PerformanceSummary {
    const cutoff = Date.now() - (periodMinutes * 60 * 1000)
    const recentOps = this.operationHistory.filter(op => 
      (Date.now() - cutoff) <= (periodMinutes * 60 * 1000)
    )

    if (recentOps.length === 0) {
      return this.getEmptySummary(periodMinutes)
    }

    const totalOperations = recentOps.length
    const successfulOps = recentOps.filter(op => op.success).length
    const successRate = (successfulOps / totalOperations) * 100

    // Calculate latency percentiles
    const latencies = recentOps.map(op => op.duration).sort((a, b) => a - b)
    const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length
    const p95Index = Math.floor(latencies.length * 0.95)
    const p99Index = Math.floor(latencies.length * 0.99)
    const p95Latency = latencies[p95Index] || 0
    const p99Latency = latencies[p99Index] || 0

    // Calculate throughput
    const opsWithThroughput = recentOps.filter(op => op.throughput && op.throughput > 0)
    const averageThroughputBps = opsWithThroughput.length > 0
      ? opsWithThroughput.reduce((sum, op) => sum + (op.throughput || 0), 0) / opsWithThroughput.length
      : 0
    const throughputMBps = averageThroughputBps / (1024 * 1024)

    // Error breakdown
    const errorsByType: Record<string, number> = {}
    recentOps.filter(op => !op.success).forEach(op => {
      const errorType = this.categorizeError(op.error || 'unknown')
      errorsByType[errorType] = (errorsByType[errorType] || 0) + 1
    })

    // Operation breakdown
    const operationBreakdown: Record<string, { count: number; averageLatency: number; successRate: number }> = {}
    const operationGroups = this.groupBy(recentOps, 'operation')
    
    Object.entries(operationGroups).forEach(([operation, ops]) => {
      const successfulOpsInGroup = ops.filter(op => op.success).length
      operationBreakdown[operation] = {
        count: ops.length,
        averageLatency: ops.reduce((sum, op) => sum + op.duration, 0) / ops.length,
        successRate: (successfulOpsInGroup / ops.length) * 100
      }
    })

    // Bucket performance
    const bucketPerformance: Record<string, { operations: number; averageLatency: number; successRate: number; totalBytes: number }> = {}
    const bucketGroups = this.groupBy(recentOps, 'bucketType')
    
    Object.entries(bucketGroups).forEach(([bucket, ops]) => {
      const successfulOpsInBucket = ops.filter(op => op.success).length
      const totalBytes = ops.reduce((sum, op) => sum + (op.fileSize || 0), 0)
      
      bucketPerformance[bucket] = {
        operations: ops.length,
        averageLatency: ops.reduce((sum, op) => sum + op.duration, 0) / ops.length,
        successRate: (successfulOpsInBucket / ops.length) * 100,
        totalBytes
      }
    })

    return {
      period: `${periodMinutes} minutes`,
      totalOperations,
      successRate,
      averageLatency,
      p95Latency,
      p99Latency,
      throughputMBps,
      errorsByType,
      operationBreakdown,
      bucketPerformance
    }
  }

  /**
   * Get current active operations
   */
  getActiveOperations(): Array<{ id: string; operation: string; duration: number; bucketType: string }> {
    const now = performance.now()
    return Array.from(this.activeTrackers.entries()).map(([id, tracker]) => ({
      id,
      operation: tracker.operation,
      duration: now - tracker.startTime,
      bucketType: tracker.bucketType
    }))
  }

  /**
   * Get operation history
   */
  getOperationHistory(limit: number = 100): R2OperationMetrics[] {
    return this.operationHistory.slice(-limit)
  }

  /**
   * Clear operation history
   */
  clearHistory(): void {
    this.operationHistory = []
  }

  /**
   * Get performance alerts
   */
  getPerformanceAlerts(): Array<{ type: string; message: string; severity: 'low' | 'medium' | 'high' }> {
    const alerts: Array<{ type: string; message: string; severity: 'low' | 'medium' | 'high' }> = []
    const summary = this.getPerformanceSummary(15) // Last 15 minutes

    // Check success rate
    if (summary.successRate < 95 && summary.totalOperations > 10) {
      alerts.push({
        type: 'success_rate',
        message: `R2 success rate is ${summary.successRate.toFixed(1)}% (below 95% threshold)`,
        severity: summary.successRate < 90 ? 'high' : 'medium'
      })
    }

    // Check average latency
    if (summary.averageLatency > 5000) { // 5 seconds
      alerts.push({
        type: 'high_latency',
        message: `R2 average latency is ${summary.averageLatency.toFixed(0)}ms (above 5s threshold)`,
        severity: summary.averageLatency > 10000 ? 'high' : 'medium'
      })
    }

    // Check P95 latency
    if (summary.p95Latency > 10000) { // 10 seconds
      alerts.push({
        type: 'p95_latency',
        message: `R2 P95 latency is ${summary.p95Latency.toFixed(0)}ms (above 10s threshold)`,
        severity: 'medium'
      })
    }

    // Check for high error rates by type
    Object.entries(summary.errorsByType).forEach(([errorType, count]) => {
      const errorRate = (count / summary.totalOperations) * 100
      if (errorRate > 10) {
        alerts.push({
          type: 'error_rate',
          message: `High ${errorType} error rate: ${errorRate.toFixed(1)}% (${count} errors)`,
          severity: errorRate > 25 ? 'high' : 'medium'
        })
      }
    })

    return alerts
  }

  /**
   * Generate performance report
   */
  generateReport(periodMinutes: number = 60): string {
    const summary = this.getPerformanceSummary(periodMinutes)
    const alerts = this.getPerformanceAlerts()
    
    let report = `# R2 Performance Report (${summary.period})\n\n`
    
    report += `## Overview\n`
    report += `- Total Operations: ${summary.totalOperations}\n`
    report += `- Success Rate: ${summary.successRate.toFixed(1)}%\n`
    report += `- Average Latency: ${summary.averageLatency.toFixed(0)}ms\n`
    report += `- P95 Latency: ${summary.p95Latency.toFixed(0)}ms\n`
    report += `- P99 Latency: ${summary.p99Latency.toFixed(0)}ms\n`
    report += `- Throughput: ${summary.throughputMBps.toFixed(2)} MB/s\n\n`

    if (Object.keys(summary.operationBreakdown).length > 0) {
      report += `## Operation Breakdown\n`
      Object.entries(summary.operationBreakdown).forEach(([op, stats]) => {
        report += `- ${op}: ${stats.count} ops, ${stats.averageLatency.toFixed(0)}ms avg, ${stats.successRate.toFixed(1)}% success\n`
      })
      report += '\n'
    }

    if (Object.keys(summary.bucketPerformance).length > 0) {
      report += `## Bucket Performance\n`
      Object.entries(summary.bucketPerformance).forEach(([bucket, stats]) => {
        const sizeMB = stats.totalBytes / (1024 * 1024)
        report += `- ${bucket}: ${stats.operations} ops, ${stats.averageLatency.toFixed(0)}ms avg, ${sizeMB.toFixed(2)}MB total\n`
      })
      report += '\n'
    }

    if (alerts.length > 0) {
      report += `## Alerts\n`
      alerts.forEach(alert => {
        report += `- [${alert.severity.toUpperCase()}] ${alert.message}\n`
      })
      report += '\n'
    }

    return report
  }

  /**
   * Helper method to group array by property
   */
  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const groupKey = String(item[key])
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(item)
      return groups
    }, {} as Record<string, T[]>)
  }

  /**
   * Categorize error types
   */
  private categorizeError(error: string): string {
    const errorLower = error.toLowerCase()
    
    if (errorLower.includes('timeout') || errorLower.includes('time out')) {
      return 'timeout'
    }
    if (errorLower.includes('network') || errorLower.includes('connection')) {
      return 'network'
    }
    if (errorLower.includes('auth') || errorLower.includes('credential') || errorLower.includes('permission')) {
      return 'authentication'
    }
    if (errorLower.includes('not found') || errorLower.includes('404')) {
      return 'not_found'
    }
    if (errorLower.includes('size') || errorLower.includes('limit') || errorLower.includes('quota')) {
      return 'size_limit'
    }
    if (errorLower.includes('rate') || errorLower.includes('throttle')) {
      return 'rate_limit'
    }
    
    return 'other'
  }

  /**
   * Get empty summary for periods with no data
   */
  private getEmptySummary(periodMinutes: number): R2PerformanceSummary {
    return {
      period: `${periodMinutes} minutes`,
      totalOperations: 0,
      successRate: 0,
      averageLatency: 0,
      p95Latency: 0,
      p99Latency: 0,
      throughputMBps: 0,
      errorsByType: {},
      operationBreakdown: {},
      bucketPerformance: {}
    }
  }
}

// Export singleton instance
export const r2PerformanceMonitor = new R2PerformanceMonitor()
