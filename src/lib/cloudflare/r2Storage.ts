/**
 * Cloudflare R2 Storage Service for Syndicaps
 * 
 * Provides S3-compatible storage interface for images and assets
 * with automatic fallback to Firebase Storage during migration.
 */

import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { shouldUseR2Storage } from '../config/featureFlags'
import { hybridPerformanceMonitor } from '../monitoring/hybridPerformanceMonitor'

export interface R2Config {
  accountId: string
  accessKeyId: string
  secretAccessKey: string
  buckets: {
    images: string
    backups: string
  }
  endpoints: {
    images: string
    backups: string
  }
}

export interface UploadResult {
  url: string
  key: string
  size: number
  contentType: string
  source: 'r2' | 'firebase'
}

export interface UploadOptions {
  contentType?: string
  cacheControl?: string
  metadata?: Record<string, string>
  userId?: string
}

/**
 * R2 Storage configuration
 */
export const R2_CONFIG: R2Config = {
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID!,
  accessKeyId: process.env.R2_ACCESS_KEY_ID!,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  buckets: {
    images: process.env.R2_BUCKET_IMAGES || 'syndicaps-images',
    backups: process.env.R2_BUCKET_BACKUPS || 'syndicaps-backups'
  },
  endpoints: {
    images: `https://${process.env.R2_BUCKET_IMAGES || 'syndicaps-images'}.${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    backups: `https://${process.env.R2_BUCKET_BACKUPS || 'syndicaps-backups'}.${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`
  }
}

/**
 * R2 Storage Service Class
 */
export class R2StorageService {
  private client: S3Client
  private isConfigured: boolean

  constructor() {
    this.isConfigured = this.validateConfig()
    
    if (this.isConfigured) {
      this.client = new S3Client({
        region: 'auto',
        endpoint: `https://${R2_CONFIG.accountId}.r2.cloudflarestorage.com`,
        credentials: {
          accessKeyId: R2_CONFIG.accessKeyId,
          secretAccessKey: R2_CONFIG.secretAccessKey,
        },
      })
    }
  }

  /**
   * Validate R2 configuration
   */
  private validateConfig(): boolean {
    const required = [
      'CLOUDFLARE_ACCOUNT_ID',
      'R2_ACCESS_KEY_ID',
      'R2_SECRET_ACCESS_KEY'
    ]

    for (const key of required) {
      if (!process.env[key]) {
        console.warn(`R2 Storage: Missing environment variable ${key}`)
        return false
      }
    }

    return true
  }

  /**
   * Upload image to R2 storage
   */
  async uploadImage(
    file: Buffer | Uint8Array,
    key: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    if (!this.isConfigured) {
      throw new Error('R2 Storage not configured')
    }

    const startTime = performance.now()

    try {
      const command = new PutObjectCommand({
        Bucket: R2_CONFIG.buckets.images,
        Key: key,
        Body: file,
        ContentType: options.contentType || 'image/jpeg',
        CacheControl: options.cacheControl || 'public, max-age=********',
        Metadata: options.metadata || {},
      })

      await this.client.send(command)

      const url = `${R2_CONFIG.endpoints.images}/${key}`
      const loadTime = performance.now() - startTime

      // Record performance metrics
      hybridPerformanceMonitor.recordImageLoadTime(url, loadTime, 'cloudflare')

      return {
        url,
        key,
        size: file.length,
        contentType: options.contentType || 'image/jpeg',
        source: 'r2'
      }
    } catch (error) {
      const loadTime = performance.now() - startTime
      hybridPerformanceMonitor.recordError('cloudflare', 'r2_upload_failed')
      
      console.error('R2 upload failed:', error)
      throw new Error(`R2 upload failed: ${error}`)
    }
  }

  /**
   * Upload backup data to R2
   */
  async uploadBackup(
    data: Buffer | string,
    key: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    if (!this.isConfigured) {
      throw new Error('R2 Storage not configured')
    }

    try {
      const buffer = typeof data === 'string' ? Buffer.from(data) : data

      const command = new PutObjectCommand({
        Bucket: R2_CONFIG.buckets.backups,
        Key: key,
        Body: buffer,
        ContentType: options.contentType || 'application/json',
        Metadata: options.metadata || {},
      })

      await this.client.send(command)

      const url = `${R2_CONFIG.endpoints.backups}/${key}`

      return {
        url,
        key,
        size: buffer.length,
        contentType: options.contentType || 'application/json',
        source: 'r2'
      }
    } catch (error) {
      hybridPerformanceMonitor.recordError('cloudflare', 'r2_backup_failed')
      console.error('R2 backup upload failed:', error)
      throw new Error(`R2 backup upload failed: ${error}`)
    }
  }

  /**
   * Delete object from R2
   */
  async deleteObject(key: string, bucket: 'images' | 'backups' = 'images'): Promise<void> {
    if (!this.isConfigured) {
      throw new Error('R2 Storage not configured')
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: R2_CONFIG.buckets[bucket],
        Key: key,
      })

      await this.client.send(command)
    } catch (error) {
      hybridPerformanceMonitor.recordError('cloudflare', 'r2_delete_failed')
      console.error('R2 delete failed:', error)
      throw new Error(`R2 delete failed: ${error}`)
    }
  }

  /**
   * Check if object exists in R2
   */
  async objectExists(key: string, bucket: 'images' | 'backups' = 'images'): Promise<boolean> {
    if (!this.isConfigured) {
      return false
    }

    try {
      const command = new HeadObjectCommand({
        Bucket: R2_CONFIG.buckets[bucket],
        Key: key,
      })

      await this.client.send(command)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Generate presigned URL for direct upload
   */
  async generatePresignedUploadUrl(
    key: string,
    contentType: string,
    expiresIn: number = 3600
  ): Promise<string> {
    if (!this.isConfigured) {
      throw new Error('R2 Storage not configured')
    }

    try {
      const command = new PutObjectCommand({
        Bucket: R2_CONFIG.buckets.images,
        Key: key,
        ContentType: contentType,
      })

      return await getSignedUrl(this.client, command, { expiresIn })
    } catch (error) {
      console.error('Failed to generate presigned URL:', error)
      throw new Error(`Failed to generate presigned URL: ${error}`)
    }
  }

  /**
   * Get object metadata
   */
  async getObjectMetadata(key: string, bucket: 'images' | 'backups' = 'images'): Promise<any> {
    if (!this.isConfigured) {
      throw new Error('R2 Storage not configured')
    }

    try {
      const command = new HeadObjectCommand({
        Bucket: R2_CONFIG.buckets[bucket],
        Key: key,
      })

      const response = await this.client.send(command)
      return {
        size: response.ContentLength,
        contentType: response.ContentType,
        lastModified: response.LastModified,
        metadata: response.Metadata
      }
    } catch (error) {
      console.error('Failed to get object metadata:', error)
      throw new Error(`Failed to get object metadata: ${error}`)
    }
  }

  /**
   * Test R2 connection
   */
  async testConnection(): Promise<boolean> {
    if (!this.isConfigured) {
      return false
    }

    try {
      const testKey = `test/connection-test-${Date.now()}.txt`
      const testData = Buffer.from('connection test')

      // Upload test file
      await this.uploadImage(testData, testKey, {
        contentType: 'text/plain'
      })

      // Check if file exists
      const exists = await this.objectExists(testKey)

      // Clean up test file
      if (exists) {
        await this.deleteObject(testKey)
      }

      return exists
    } catch (error) {
      console.error('R2 connection test failed:', error)
      return false
    }
  }
}

/**
 * Hybrid Image Upload Service
 * 
 * Automatically chooses between R2 and Firebase based on feature flags
 */
export class HybridImageUploadService {
  private r2Service: R2StorageService
  private firebaseService: any // Firebase storage service

  constructor(firebaseService?: any) {
    this.r2Service = new R2StorageService()
    this.firebaseService = firebaseService
  }

  /**
   * Upload image with automatic service selection
   */
  async uploadImage(
    file: Buffer | File,
    key: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    const useR2 = shouldUseR2Storage(options.userId)

    if (useR2) {
      try {
        const buffer = file instanceof File ? 
          Buffer.from(await file.arrayBuffer()) : file

        return await this.r2Service.uploadImage(buffer, key, options)
      } catch (error) {
        console.warn('R2 upload failed, falling back to Firebase:', error)
        // Fallback to Firebase
        return await this.uploadToFirebase(file, key, options)
      }
    } else {
      return await this.uploadToFirebase(file, key, options)
    }
  }

  /**
   * Fallback to Firebase upload
   */
  private async uploadToFirebase(
    file: Buffer | File,
    key: string,
    options: UploadOptions
  ): Promise<UploadResult> {
    if (!this.firebaseService) {
      throw new Error('Firebase service not configured')
    }

    const startTime = performance.now()

    try {
      // Firebase upload logic here
      const result = await this.firebaseService.upload(file, key, options)
      
      const loadTime = performance.now() - startTime
      hybridPerformanceMonitor.recordImageLoadTime(result.url, loadTime, 'firebase')

      return {
        ...result,
        source: 'firebase' as const
      }
    } catch (error) {
      const loadTime = performance.now() - startTime
      hybridPerformanceMonitor.recordError('firebase', 'upload_failed')
      throw error
    }
  }
}

// Global instances
export const r2Storage = new R2StorageService()
export const hybridImageUpload = new HybridImageUploadService()
