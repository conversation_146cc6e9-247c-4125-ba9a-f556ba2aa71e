/**
 * Cloudflare R2 Storage Service
 * S3-compatible API for Cloudflare R2 storage operations
 */

import { S3Client, S3ClientConfig } from '@aws-sdk/client-s3'
import {
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  HeadObjectCommand,
  ListO<PERSON>sV2Command,
  CopyObjectCommand,
  PutO<PERSON>CommandInput,
  GetObjectCommandInput,
  DeleteObjectCommandInput,
  HeadObjectCommandInput,
  ListObjectsV2CommandInput,
  CopyObjectCommandInput
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { Upload } from '@aws-sdk/lib-storage'
import { R2_CONFIG, R2BucketConfig, validateR2Config, isFileTypeAllowed, isFileSizeAllowed } from './r2Config'
import { hybridPerformanceMonitor } from '../monitoring/hybridPerformanceMonitor'
import { r2PerformanceMonitor } from './r2PerformanceMonitor'

export interface R2UploadOptions {
  bucketType: keyof typeof R2_CONFIG.buckets
  key: string
  body: Buffer | Uint8Array | string
  contentType?: string
  metadata?: Record<string, string>
  cacheControl?: string
  expires?: Date
  acl?: 'private' | 'public-read'
}

export interface R2DownloadOptions {
  bucketType: keyof typeof R2_CONFIG.buckets
  key: string
  range?: string
  ifModifiedSince?: Date
  ifNoneMatch?: string
}

export interface R2DeleteOptions {
  bucketType: keyof typeof R2_CONFIG.buckets
  key: string
}

export interface R2ListOptions {
  bucketType: keyof typeof R2_CONFIG.buckets
  prefix?: string
  maxKeys?: number
  continuationToken?: string
}

export interface R2ObjectMetadata {
  key: string
  size: number
  lastModified: Date
  etag: string
  contentType?: string
  metadata?: Record<string, string>
  cacheControl?: string
  expires?: Date
}

export interface R2UploadResult {
  success: boolean
  key: string
  url?: string
  etag?: string
  size?: number
  error?: string
}

export interface R2DownloadResult {
  success: boolean
  data?: Buffer
  metadata?: R2ObjectMetadata
  error?: string
}

export interface R2ListResult {
  success: boolean
  objects: R2ObjectMetadata[]
  continuationToken?: string
  isTruncated: boolean
  error?: string
}

export class R2StorageService {
  private s3Client: S3Client
  private config: typeof R2_CONFIG

  constructor() {
    this.config = R2_CONFIG

    // Validate configuration
    if (!validateR2Config(this.config)) {
      throw new Error('Invalid R2 configuration')
    }

    // Initialize S3 client for R2
    const clientConfig: S3ClientConfig = {
      region: this.config.account.region,
      endpoint: this.config.account.endpoint,
      credentials: {
        accessKeyId: this.config.account.accessKeyId,
        secretAccessKey: this.config.account.secretAccessKey
      },
      forcePathStyle: true, // Required for R2
      maxAttempts: this.config.fallback.retryAttempts
    }

    this.s3Client = new S3Client(clientConfig)
  }

  /**
   * Upload a file to R2 storage
   */
  async upload(options: R2UploadOptions): Promise<R2UploadResult> {
    const operationId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const bodySize = this.getBodySize(options.body)

    // Start performance tracking
    r2PerformanceMonitor.startOperation(operationId, 'upload', options.bucketType, {
      fileSize: bodySize,
      contentType: options.contentType,
      metadata: options.metadata
    })

    try {
      // Validate bucket type
      const bucketConfig = this.config.buckets[options.bucketType]
      if (!bucketConfig) {
        throw new Error(`Invalid bucket type: ${options.bucketType}`)
      }

      // Validate file type if content type is provided
      if (options.contentType && !isFileTypeAllowed(options.bucketType, options.contentType)) {
        throw new Error(`File type ${options.contentType} not allowed for bucket ${options.bucketType}`)
      }

      // Validate file size
      const bodySize = this.getBodySize(options.body)
      if (!isFileSizeAllowed(options.bucketType, bodySize)) {
        throw new Error(`File size ${bodySize} exceeds limit for bucket ${options.bucketType}`)
      }

      // Prepare upload parameters
      const uploadParams: PutObjectCommandInput = {
        Bucket: bucketConfig.name,
        Key: options.key,
        Body: options.body,
        ContentType: options.contentType,
        Metadata: options.metadata,
        CacheControl: options.cacheControl || `max-age=${bucketConfig.defaultTTL}`,
        Expires: options.expires,
        ACL: options.acl || 'private'
      }

      // Use multipart upload for large files
      if (bodySize > 5 * 1024 * 1024) { // 5MB threshold
        const upload = new Upload({
          client: this.s3Client,
          params: uploadParams,
          partSize: 5 * 1024 * 1024, // 5MB parts
          queueSize: this.config.limits.maxConcurrentUploads
        })

        const result = await upload.done()

        // Complete performance tracking
        r2PerformanceMonitor.completeOperation(operationId, true, {
          actualFileSize: bodySize
        })

        return {
          success: true,
          key: options.key,
          url: this.getPublicUrl(options.bucketType, options.key),
          etag: result.ETag,
          size: bodySize
        }
      } else {
        // Regular upload for smaller files
        const command = new PutObjectCommand(uploadParams)
        const result = await this.s3Client.send(command)

        // Complete performance tracking
        r2PerformanceMonitor.completeOperation(operationId, true, {
          actualFileSize: bodySize
        })

        return {
          success: true,
          key: options.key,
          url: this.getPublicUrl(options.bucketType, options.key),
          etag: result.ETag,
          size: bodySize
        }
      }
    } catch (error) {
      // Record failure
      r2PerformanceMonitor.recordFailure(operationId, error instanceof Error ? error.message : 'Upload failed')

      return {
        success: false,
        key: options.key,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Download a file from R2 storage
   */
  async download(options: R2DownloadOptions): Promise<R2DownloadResult> {
    const startTime = performance.now()
    
    try {
      const bucketConfig = this.config.buckets[options.bucketType]
      if (!bucketConfig) {
        throw new Error(`Invalid bucket type: ${options.bucketType}`)
      }

      const downloadParams: GetObjectCommandInput = {
        Bucket: bucketConfig.name,
        Key: options.key,
        Range: options.range,
        IfModifiedSince: options.ifModifiedSince,
        IfNoneMatch: options.ifNoneMatch
      }

      const command = new GetObjectCommand(downloadParams)
      const result = await this.s3Client.send(command)

      // Convert stream to buffer
      const chunks: Uint8Array[] = []
      if (result.Body) {
        const reader = result.Body.transformToWebStream().getReader()
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          chunks.push(value)
        }
      }

      const data = Buffer.concat(chunks)

      // Record performance metrics
      const duration = performance.now() - startTime
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: duration,
        unit: 'ms',
        source: 'cloudflare',
        metadata: {
          operation: 'r2_download',
          bucket: options.bucketType,
          size: data.length
        }
      })

      return {
        success: true,
        data,
        metadata: {
          key: options.key,
          size: result.ContentLength || data.length,
          lastModified: result.LastModified || new Date(),
          etag: result.ETag || '',
          contentType: result.ContentType,
          metadata: result.Metadata,
          cacheControl: result.CacheControl,
          expires: result.Expires
        }
      }
    } catch (error) {
      // Record error metrics
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'error_rate',
        value: 1,
        unit: 'count',
        source: 'cloudflare',
        metadata: {
          operation: 'r2_download',
          bucket: options.bucketType,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Download failed'
      }
    }
  }

  /**
   * Delete a file from R2 storage
   */
  async delete(options: R2DeleteOptions): Promise<{ success: boolean; error?: string }> {
    const startTime = performance.now()
    
    try {
      const bucketConfig = this.config.buckets[options.bucketType]
      if (!bucketConfig) {
        throw new Error(`Invalid bucket type: ${options.bucketType}`)
      }

      const deleteParams: DeleteObjectCommandInput = {
        Bucket: bucketConfig.name,
        Key: options.key
      }

      const command = new DeleteObjectCommand(deleteParams)
      await this.s3Client.send(command)

      // Record performance metrics
      const duration = performance.now() - startTime
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'response_time',
        value: duration,
        unit: 'ms',
        source: 'cloudflare',
        metadata: {
          operation: 'r2_delete',
          bucket: options.bucketType
        }
      })

      return { success: true }
    } catch (error) {
      // Record error metrics
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'error_rate',
        value: 1,
        unit: 'count',
        source: 'cloudflare',
        metadata: {
          operation: 'r2_delete',
          bucket: options.bucketType,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed'
      }
    }
  }

  /**
   * Get object metadata without downloading the file
   */
  async getMetadata(options: R2DeleteOptions): Promise<{ success: boolean; metadata?: R2ObjectMetadata; error?: string }> {
    try {
      const bucketConfig = this.config.buckets[options.bucketType]
      if (!bucketConfig) {
        throw new Error(`Invalid bucket type: ${options.bucketType}`)
      }

      const headParams: HeadObjectCommandInput = {
        Bucket: bucketConfig.name,
        Key: options.key
      }

      const command = new HeadObjectCommand(headParams)
      const result = await this.s3Client.send(command)

      return {
        success: true,
        metadata: {
          key: options.key,
          size: result.ContentLength || 0,
          lastModified: result.LastModified || new Date(),
          etag: result.ETag || '',
          contentType: result.ContentType,
          metadata: result.Metadata,
          cacheControl: result.CacheControl,
          expires: result.Expires
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get metadata'
      }
    }
  }

  /**
   * List objects in a bucket
   */
  async list(options: R2ListOptions): Promise<R2ListResult> {
    try {
      const bucketConfig = this.config.buckets[options.bucketType]
      if (!bucketConfig) {
        throw new Error(`Invalid bucket type: ${options.bucketType}`)
      }

      const listParams: ListObjectsV2CommandInput = {
        Bucket: bucketConfig.name,
        Prefix: options.prefix,
        MaxKeys: options.maxKeys || 1000,
        ContinuationToken: options.continuationToken
      }

      const command = new ListObjectsV2Command(listParams)
      const result = await this.s3Client.send(command)

      const objects: R2ObjectMetadata[] = (result.Contents || []).map(obj => ({
        key: obj.Key || '',
        size: obj.Size || 0,
        lastModified: obj.LastModified || new Date(),
        etag: obj.ETag || '',
        contentType: undefined, // Not available in list operation
        metadata: undefined
      }))

      return {
        success: true,
        objects,
        continuationToken: result.NextContinuationToken,
        isTruncated: result.IsTruncated || false
      }
    } catch (error) {
      return {
        success: false,
        objects: [],
        isTruncated: false,
        error: error instanceof Error ? error.message : 'List operation failed'
      }
    }
  }

  /**
   * Generate a presigned URL for direct upload/download
   */
  async generatePresignedUrl(
    operation: 'upload' | 'download',
    options: R2DeleteOptions,
    expiresIn: number = 3600
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      if (!this.config.features.enablePresignedUrls) {
        throw new Error('Presigned URLs are disabled')
      }

      const bucketConfig = this.config.buckets[options.bucketType]
      if (!bucketConfig) {
        throw new Error(`Invalid bucket type: ${options.bucketType}`)
      }

      let command
      if (operation === 'upload') {
        command = new PutObjectCommand({
          Bucket: bucketConfig.name,
          Key: options.key
        })
      } else {
        command = new GetObjectCommand({
          Bucket: bucketConfig.name,
          Key: options.key
        })
      }

      const url = await getSignedUrl(this.s3Client, command, { expiresIn })

      return { success: true, url }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate presigned URL'
      }
    }
  }

  /**
   * Test connection to R2 storage
   */
  async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // Test by listing objects in the images bucket (should be the most commonly used)
      const result = await this.list({ bucketType: 'images', maxKeys: 1 })
      
      if (result.success) {
        return {
          success: true,
          message: 'R2 connection successful',
          details: {
            bucketsConfigured: Object.keys(this.config.buckets).length,
            featuresEnabled: Object.entries(this.config.features).filter(([_, enabled]) => enabled).length
          }
        }
      } else {
        throw new Error(result.error || 'Connection test failed')
      }
    } catch (error) {
      return {
        success: false,
        message: `R2 connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      }
    }
  }

  /**
   * Get public URL for an object
   */
  private getPublicUrl(bucketType: keyof typeof R2_CONFIG.buckets, key: string): string | undefined {
    const bucketConfig = this.config.buckets[bucketType]
    if (bucketConfig?.publicUrl) {
      return `${bucketConfig.publicUrl}/${key}`
    }
    return undefined
  }

  /**
   * Get size of body content
   */
  private getBodySize(body: Buffer | Uint8Array | string): number {
    if (typeof body === 'string') {
      return Buffer.byteLength(body, 'utf8')
    }
    return body.length
  }
}

// Export singleton instance
export const r2StorageService = new R2StorageService()
