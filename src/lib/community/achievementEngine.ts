/**
 * Advanced Achievement Engine - Phase 2 Implementation
 * 
 * Comprehensive achievement system with dynamic requirements, progress tracking,
 * social proof, and reward distribution. Supports complex achievement chains,
 * seasonal events, and community-driven achievements.
 * 
 * <AUTHOR> Team - Phase 2 Community Implementation
 * @version 2.0.0
 */

import { Timestamp, writeBatch, doc, collection, query, where, orderBy, limit, getDocs, increment, setDoc } from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import { 
  Achievement, 
  UserAchievement, 
  AchievementRequirement,
  UserProfile,
  ActivityTrigger
} from './types'
import { NotificationEngine } from './notificationEngine'
import { ActivityLogger } from './activityFeed'
import { PointCalculationEngine } from './pointEngine'

// ===== ACHIEVEMENT CONFIGURATION =====

export const ACHIEVEMENT_CONFIG = {
  // Achievement categories with settings
  CATEGORIES: {
    community_engagement: {
      color: '#3B82F6',
      icon: '🤝',
      displayName: 'Community Engagement',
      description: 'Participating in community activities'
    },
    content_quality: {
      color: '#10B981',
      icon: '⭐',
      displayName: 'Content Quality',
      description: 'Creating high-quality content'
    },
    social_interaction: {
      color: '#F59E0B',
      icon: '💬',
      displayName: 'Social Interaction',
      description: 'Engaging with other users'
    },
    challenges: {
      color: '#8B5CF6',
      icon: '🎯',
      displayName: 'Challenges',
      description: 'Participating in challenges'
    },
    milestones: {
      color: '#EF4444',
      icon: '🏁',
      displayName: 'Milestones',
      description: 'Reaching important milestones'
    },
    moderation: {
      color: '#6366F1',
      icon: '🛡️',
      displayName: 'Moderation',
      description: 'Helping moderate the community'
    },
    special_events: {
      color: '#EC4899',
      icon: '🎊',
      displayName: 'Special Events',
      description: 'Participating in special events'
    },
    legacy: {
      color: '#6B7280',
      icon: '🏛️',
      displayName: 'Legacy',
      description: 'Historical achievements'
    }
  },

  // Rarity settings
  RARITY_SETTINGS: {
    common: {
      color: '#9CA3AF',
      pointMultiplier: 1.0,
      shareBonus: 10,
      displayOrder: 1
    },
    uncommon: {
      color: '#10B981',
      pointMultiplier: 1.2,
      shareBonus: 25,
      displayOrder: 2
    },
    rare: {
      color: '#3B82F6',
      pointMultiplier: 1.5,
      shareBonus: 50,
      displayOrder: 3
    },
    epic: {
      color: '#8B5CF6',
      pointMultiplier: 2.0,
      shareBonus: 100,
      displayOrder: 4
    },
    legendary: {
      color: '#F59E0B',
      pointMultiplier: 3.0,
      shareBonus: 250,
      displayOrder: 5
    }
  },

  // Progress tracking settings
  PROGRESS_SETTINGS: {
    milestone_percentages: [25, 50, 75, 90],
    progress_notification_threshold: 0.25,
    completion_celebration_duration: 5000,
    leaderboard_update_delay: 1000
  },

  // Social features
  SOCIAL_SETTINGS: {
    auto_share_rare_and_above: true,
    celebration_animation_duration: 3000,
    congratulations_timeout: 30000,
    showcase_featured_achievements: true
  }
} as const

// ===== ADVANCED ACHIEVEMENT INTERFACES =====

export interface AchievementProgress {
  userId: string
  achievementId: string
  currentValue: number
  targetValue: number
  progressPercentage: number
  lastUpdated: Timestamp
  milestoneNotifications: number[]
  isCompleted: boolean
  completedAt?: Timestamp
  
  // Progress data for complex achievements
  progressData: {
    daily?: Record<string, number>
    weekly?: Record<string, number>
    monthly?: Record<string, number>
    streaks?: {
      current: number
      longest: number
      lastActivity: Timestamp
    }
    quality?: {
      averageScore: number
      totalRatings: number
    }
    social?: {
      uniqueInteractions: number
      helpfulVotes: number
    }
  }
}

export interface AchievementChain {
  id: string
  name: string
  description: string
  achievements: string[] // Achievement IDs in order
  rewards: {
    chainBonus: number
    specialBadge?: string
    exclusiveContent?: string
  }
  isActive: boolean
  completionCount: number
}

export interface SeasonalEvent {
  id: string
  name: string
  description: string
  startDate: Timestamp
  endDate: Timestamp
  achievements: string[]
  specialRewards: {
    points: number
    badges: string[]
    exclusiveItems: string[]
  }
  participantCount: number
  isActive: boolean
}

// ===== ACHIEVEMENT ENGINE =====

export class AchievementEngine {
  /**
   * Process activity trigger and check for achievement progress
   */
  static async processActivityTrigger(
    trigger: ActivityTrigger
  ): Promise<{
    achievementsProgressed: string[]
    achievementsCompleted: string[]
    newBadges: string[]
    pointsAwarded: number
  }> {
    try {
      const { userId, activityType, data } = trigger
      
      // Get user's active achievements
      const activeAchievements = await this.getUserActiveAchievements(userId)
      
      const result = {
        achievementsProgressed: [] as string[],
        achievementsCompleted: [] as string[],
        newBadges: [] as string[],
        pointsAwarded: 0
      }

      // Process each active achievement
      for (const achievement of activeAchievements) {
        const progressUpdate = await this.updateAchievementProgress(
          userId,
          achievement,
          activityType,
          data
        )

        if (progressUpdate.progressed) {
          result.achievementsProgressed.push(achievement.id!)
        }

        if (progressUpdate.completed) {
          result.achievementsCompleted.push(achievement.id!)
          
          // Award achievement rewards
          const rewards = await this.awardAchievementRewards(userId, achievement)
          result.pointsAwarded += rewards.points
          result.newBadges.push(...rewards.badges)

          // Check for chain completion
          await this.checkChainCompletion(userId, achievement.id!)

          // Send notifications
          await NotificationEngine.notifyAchievementUnlock(userId, achievement, rewards.points)

          // Log activity
          await ActivityLogger.logAchievementUnlocked(
            userId,
            achievement.id!,
            achievement.title,
            rewards.points
          )
        }
      }

      return result

    } catch (error) {
      console.error('Error processing activity trigger:', error)
      return {
        achievementsProgressed: [],
        achievementsCompleted: [],
        newBadges: [],
        pointsAwarded: 0
      }
    }
  }

  /**
   * Create new achievement
   */
  static async createAchievement(
    achievement: Omit<Achievement, 'id' | 'createdAt' | 'updatedAt' | 'analytics'>
  ): Promise<{
    success: boolean
    achievementId?: string
    error?: string
  }> {
    try {
      const achievementDoc: Omit<Achievement, 'id'> = {
        ...achievement,
        analytics: {
          unlockedCount: 0,
          averageTimeToUnlock: 0,
          completionRate: 0,
          socialShares: 0
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }

      const achievementRef = doc(collection(db, collections.achievements))
      await setDoc(achievementRef, achievementDoc)

      return {
        success: true,
        achievementId: achievementRef.id
      }

    } catch (error) {
      console.error('Error creating achievement:', error)
      return {
        success: false,
        error: 'Failed to create achievement'
      }
    }
  }

  /**
   * Get user's achievement progress
   */
  static async getUserAchievementProgress(
    userId: string,
    category?: Achievement['category']
  ): Promise<{
    completed: UserAchievement[]
    inProgress: AchievementProgress[]
    available: Achievement[]
    stats: {
      totalCompleted: number
      totalPoints: number
      rarityBreakdown: Record<Achievement['rarity'], number>
      categoryBreakdown: Record<Achievement['category'], number>
    }
  }> {
    try {
      // Get completed achievements
      let completedQuery = query(
        collection(db, collections.userAchievements),
        where('userId', '==', userId),
        where('isCompleted', '==', true),
        orderBy('unlockedAt', 'desc')
      )

      if (category) {
        // Would need to join with achievements collection for category filter
      }

      const completedSnapshot = await getDocs(completedQuery)
      const completed: UserAchievement[] = []
      
      completedSnapshot.forEach((doc) => {
        completed.push({ id: doc.id, ...doc.data() } as UserAchievement)
      })

      // Get achievements in progress
      const inProgressQuery = query(
        collection(db, collections.userAchievements),
        where('userId', '==', userId),
        where('isCompleted', '==', false),
        orderBy('progress', 'desc')
      )

      const inProgressSnapshot = await getDocs(inProgressQuery)
      const inProgress: AchievementProgress[] = []
      
      inProgressSnapshot.forEach((doc) => {
        const data = doc.data() as UserAchievement
        inProgress.push({
          userId: data.userId,
          achievementId: data.achievementId,
          currentValue: data.progress,
          targetValue: 100, // Would get from achievement requirements
          progressPercentage: data.progress,
          lastUpdated: data.updatedAt,
          milestoneNotifications: [],
          isCompleted: data.isCompleted,
          completedAt: data.unlockedAt,
          progressData: data.progressData || {}
        })
      })

      // Get available achievements
      const allAchievementsQuery = query(
        collection(db, collections.achievements),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      )

      const allAchievementsSnapshot = await getDocs(allAchievementsQuery)
      const allAchievements: Achievement[] = []
      
      allAchievementsSnapshot.forEach((doc) => {
        allAchievements.push({ id: doc.id, ...doc.data() } as Achievement)
      })

      // Filter available achievements (not completed, prerequisites met)
      const completedIds = new Set(completed.map(a => a.achievementId))
      const available = allAchievements.filter(achievement => {
        if (completedIds.has(achievement.id!)) return false
        
        // Check prerequisites
        if (achievement.prerequisites?.length) {
          return achievement.prerequisites.every(prereqId => completedIds.has(prereqId))
        }
        
        return true
      })

      // Calculate stats
      const stats = {
        totalCompleted: completed.length,
        totalPoints: completed.reduce((sum, achievement) => {
          // Would get points from achievement data
          return sum + 100 // placeholder
        }, 0),
        rarityBreakdown: this.calculateRarityBreakdown(completed),
        categoryBreakdown: this.calculateCategoryBreakdown(completed)
      }

      return {
        completed,
        inProgress,
        available,
        stats
      }

    } catch (error) {
      console.error('Error getting user achievement progress:', error)
      return {
        completed: [],
        inProgress: [],
        available: [],
        stats: {
          totalCompleted: 0,
          totalPoints: 0,
          rarityBreakdown: {} as any,
          categoryBreakdown: {} as any
        }
      }
    }
  }

  /**
   * Get achievement leaderboard
   */
  static async getAchievementLeaderboard(
    category?: Achievement['category'],
    timeframe: 'all_time' | 'monthly' | 'weekly' = 'all_time',
    limit: number = 50
  ): Promise<{
    users: {
      userId: string
      userName: string
      userAvatar?: string
      achievementCount: number
      totalPoints: number
      latestAchievement?: UserAchievement
    }[]
    totalUsers: number
  }> {
    try {
      // Implementation would aggregate user achievement data
      // For now, return empty result
      return {
        users: [],
        totalUsers: 0
      }

    } catch (error) {
      console.error('Error getting achievement leaderboard:', error)
      return {
        users: [],
        totalUsers: 0
      }
    }
  }

  /**
   * Share achievement on social media
   */
  static async shareAchievement(
    userId: string,
    achievementId: string,
    platform: 'internal' | 'twitter' | 'facebook' | 'reddit' = 'internal'
  ): Promise<{
    success: boolean
    shareUrl?: string
    error?: string
  }> {
    try {
      // Get achievement details
      const achievement = await this.getAchievement(achievementId)
      if (!achievement) {
        return {
          success: false,
          error: 'Achievement not found'
        }
      }

      // Update achievement analytics
      await this.updateAchievementAnalytics(achievementId, 'share')

      // Award sharing bonus points
      const raritySettings = ACHIEVEMENT_CONFIG.RARITY_SETTINGS[achievement.rarity]
      if (raritySettings.shareBonus > 0) {
        await PointCalculationEngine.awardPoints(
          userId,
          'social_share',
          `Shared achievement: ${achievement.title}`,
          { achievementId, platform }
        )
      }

      // Generate share URL
      const shareUrl = this.generateShareUrl(achievementId, platform)

      return {
        success: true,
        shareUrl
      }

    } catch (error) {
      console.error('Error sharing achievement:', error)
      return {
        success: false,
        error: 'Failed to share achievement'
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async getUserActiveAchievements(userId: string): Promise<Achievement[]> {
    // Get user's incomplete achievements and available achievements
    // Implementation would query both collections
    return []
  }

  private static async updateAchievementProgress(
    userId: string,
    achievement: Achievement,
    activityType: string,
    data: Record<string, any>
  ): Promise<{
    progressed: boolean
    completed: boolean
    newProgress: number
  }> {
    // Check if activity type matches achievement requirements
    const relevantRequirements = achievement.requirements.filter(req => 
      this.isActivityRelevant(req, activityType, data)
    )

    if (relevantRequirements.length === 0) {
      return { progressed: false, completed: false, newProgress: 0 }
    }

    // Get current progress
    const currentProgress = await this.getCurrentProgress(userId, achievement.id!)
    
    // Calculate new progress
    let newProgress = currentProgress
    for (const requirement of relevantRequirements) {
      newProgress += this.calculateProgressIncrement(requirement, activityType, data)
    }

    // Check if completed
    const completed = this.isAchievementCompleted(achievement, newProgress)

    // Update progress in database
    if (newProgress !== currentProgress) {
      await this.saveProgressUpdate(userId, achievement.id!, newProgress, completed)
    }

    return {
      progressed: newProgress > currentProgress,
      completed,
      newProgress
    }
  }

  private static isActivityRelevant(
    requirement: AchievementRequirement,
    activityType: string,
    data: Record<string, any>
  ): boolean {
    // Map activity types to requirement types
    const mappings: Record<string, string[]> = {
      content_created: ['content_created', 'posts_created'],
      achievement_unlocked: ['achievements_earned'],
      tier_promoted: ['tier_advancement'],
      challenge_joined: ['challenges_participated'],
      challenge_completed: ['challenges_won'],
      comment_posted: ['comments_posted', 'community_votes'],
      like_given: ['helpful_contributions'],
      share_content: ['social_shares']
    }

    const relevantTypes = mappings[activityType] || []
    return relevantTypes.includes(requirement.type)
  }

  private static calculateProgressIncrement(
    requirement: AchievementRequirement,
    activityType: string,
    data: Record<string, any>
  ): number {
    // Calculate how much progress this activity adds
    switch (requirement.type) {
      case 'content_created':
      case 'posts_created':
      case 'comments_posted':
      case 'achievements_earned':
      case 'challenges_participated':
      case 'challenges_won':
        return 1

      case 'points_earned':
        return data.pointsAwarded || 0

      case 'community_votes':
        return data.voteValue || 1

      case 'streak_days':
        return data.streakIncrement || 0

      case 'helpful_contributions':
        return data.helpfulnessScore || 1

      default:
        return 0
    }
  }

  private static async getCurrentProgress(userId: string, achievementId: string): Promise<number> {
    // Implementation would get current progress from database
    return 0
  }

  private static isAchievementCompleted(achievement: Achievement, progress: number): boolean {
    // Check if all requirements are met
    return achievement.requirements.every(req => progress >= req.target)
  }

  private static async saveProgressUpdate(
    userId: string,
    achievementId: string,
    progress: number,
    completed: boolean
  ): Promise<void> {
    // Implementation would save progress to database
  }

  private static async awardAchievementRewards(
    userId: string,
    achievement: Achievement
  ): Promise<{
    points: number
    badges: string[]
  }> {
    const raritySettings = ACHIEVEMENT_CONFIG.RARITY_SETTINGS[achievement.rarity]
    const basePoints = achievement.rewards.points
    const points = Math.round(basePoints * raritySettings.pointMultiplier)

    const badges: string[] = []
    if (achievement.rewards.badge) {
      badges.push(achievement.rewards.badge)
    }

    return { points, badges }
  }

  private static async checkChainCompletion(userId: string, achievementId: string): Promise<void> {
    // Implementation would check if this achievement completes any chains
  }

  private static async getAchievement(achievementId: string): Promise<Achievement | null> {
    // Implementation would fetch achievement from database
    return null
  }

  private static async updateAchievementAnalytics(
    achievementId: string,
    event: 'unlock' | 'share' | 'view'
  ): Promise<void> {
    // Implementation would update achievement analytics
  }

  private static generateShareUrl(achievementId: string, platform: string): string {
    const baseUrl = 'https://syndicaps.com'
    return `${baseUrl}/achievements/${achievementId}?ref=${platform}`
  }

  private static calculateRarityBreakdown(completed: UserAchievement[]): Record<Achievement['rarity'], number> {
    // Implementation would calculate rarity breakdown
    return {
      common: 0,
      uncommon: 0,
      rare: 0,
      epic: 0,
      legendary: 0
    }
  }

  private static calculateCategoryBreakdown(completed: UserAchievement[]): Record<Achievement['category'], number> {
    // Implementation would calculate category breakdown
    return {
      community_engagement: 0,
      content_quality: 0,
      social_interaction: 0,
      challenges: 0,
      milestones: 0,
      moderation: 0,
      special_events: 0,
      legacy: 0
    }
  }
}

// ===== ACHIEVEMENT PRESET DEFINITIONS =====

export class AchievementPresets {
  /**
   * Create default achievement set
   */
  static getDefaultAchievements(): Omit<Achievement, 'id' | 'createdAt' | 'updatedAt' | 'analytics'>[] {
    return [
      // Community Engagement
      {
        title: 'Welcome to the Community',
        description: 'Complete your first community interaction',
        icon: '👋',
        category: 'community_engagement',
        rarity: 'common',
        requirements: [
          {
            type: 'community_votes',
            target: 1
          }
        ],
        rewards: {
          points: 50,
          badge: 'newcomer'
        },
        isRepeatable: false,
        isSecret: false,
        isActive: true,
        progressType: 'cumulative',
        trackingMetrics: ['community_votes'],
        isShareable: true,
        prerequisites: []
      },

      {
        title: 'Social Butterfly',
        description: 'Interact with 10 different community members',
        icon: '🦋',
        category: 'social_interaction',
        rarity: 'uncommon',
        requirements: [
          {
            type: 'helpful_contributions',
            target: 10
          }
        ],
        rewards: {
          points: 200,
          badge: 'social'
        },
        isRepeatable: false,
        isSecret: false,
        isActive: true,
        progressType: 'cumulative',
        trackingMetrics: ['helpful_contributions'],
        isShareable: true,
        prerequisites: [],
        createdBy: 'system'
      },

      // Content Quality
      {
        title: 'Quality Creator',
        description: 'Create 5 high-quality posts',
        icon: '⭐',
        category: 'content_quality',
        rarity: 'rare',
        requirements: [
          {
            type: 'content_created',
            target: 5,
            conditions: {
              qualityThreshold: 0.8
            }
          }
        ],
        rewards: {
          points: 500,
          badge: 'creator'
        },
        isRepeatable: false,
        isSecret: false,
        isActive: true,
        progressType: 'cumulative',
        trackingMetrics: ['content_created'],
        isShareable: true,
        prerequisites: []
      },

      // Milestones
      {
        title: 'Point Collector',
        description: 'Earn your first 1,000 points',
        icon: '💰',
        category: 'milestones',
        rarity: 'common',
        requirements: [
          {
            type: 'points_earned',
            target: 1000
          }
        ],
        rewards: {
          points: 100,
          badge: 'collector'
        },
        isRepeatable: false,
        isSecret: false,
        isActive: true,
        progressType: 'milestone',
        trackingMetrics: ['points_earned'],
        isShareable: true,
        prerequisites: []
      },

      {
        title: 'Point Master',
        description: 'Earn 10,000 points',
        icon: '👑',
        category: 'milestones',
        rarity: 'epic',
        requirements: [
          {
            type: 'points_earned',
            target: 10000
          }
        ],
        rewards: {
          points: 1000,
          badge: 'master'
        },
        isRepeatable: false,
        isSecret: false,
        isActive: true,
        progressType: 'milestone',
        trackingMetrics: ['points_earned'],
        isShareable: true,
        prerequisites: []
      },

      // Challenges
      {
        title: 'Challenge Seeker',
        description: 'Participate in your first challenge',
        icon: '🎯',
        category: 'challenges',
        rarity: 'common',
        requirements: [
          {
            type: 'challenges_participated',
            target: 1
          }
        ],
        rewards: {
          points: 100,
          badge: 'seeker'
        },
        isRepeatable: false,
        isSecret: false,
        isActive: true,
        progressType: 'cumulative',
        trackingMetrics: ['challenges_participated'],
        isShareable: true,
        prerequisites: []
      },

      {
        title: 'Challenge Champion',
        description: 'Win 5 challenges',
        icon: '🏆',
        category: 'challenges',
        rarity: 'legendary',
        requirements: [
          {
            type: 'challenges_won',
            target: 5
          }
        ],
        rewards: {
          points: 2500,
          badge: 'champion',
          specialPrivileges: ['challenge_creation']
        },
        isRepeatable: false,
        isSecret: false,
        isActive: true,
        progressType: 'cumulative',
        trackingMetrics: ['challenges_won'],
        isShareable: true,
        prerequisites: []
      }
    ]
  }
}

export default AchievementEngine