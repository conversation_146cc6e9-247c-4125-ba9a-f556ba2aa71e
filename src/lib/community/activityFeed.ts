/**
 * Activity Feed Engine - Phase 2 Implementation
 * 
 * Real-time activity feed system for community engagement tracking.
 * Aggregates user activities, content interactions, and community events
 * into personalized and global activity streams.
 * 
 * <AUTHOR> Team - Phase 2 Community Implementation
 * @version 2.0.0
 */

import { Timestamp, writeBatch, doc, collection, query, where, orderBy, limit, getDocs, onSnapshot, startAfter } from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import { 
  ActivityTrigger,
  UserProfile,
  CommunityContent,
  Achievement,
  TierPromotion,
  Challenge 
} from './types'

// ===== ACTIVITY FEED CONFIGURATION =====

export const ACTIVITY_CONFIG = {
  // Activity types and their display settings
  ACTIVITY_TYPES: {
    content_created: {
      icon: '📝',
      color: '#3B82F6',
      priority: 'medium',
      showInGlobal: true,
      showInPersonal: true,
      groupable: true
    },
    achievement_unlocked: {
      icon: '🏆',
      color: '#F59E0B',
      priority: 'high',
      showInGlobal: true,
      showInPersonal: true,
      groupable: false
    },
    tier_promoted: {
      icon: '⬆️',
      color: '#10B981',
      priority: 'high',
      showInGlobal: true,
      showInPersonal: true,
      groupable: false
    },
    challenge_joined: {
      icon: '🎯',
      color: '#8B5CF6',
      priority: 'medium',
      showInGlobal: true,
      showInPersonal: true,
      groupable: true
    },
    challenge_completed: {
      icon: '🎉',
      color: '#EF4444',
      priority: 'high',
      showInGlobal: true,
      showInPersonal: true,
      groupable: false
    },
    content_liked: {
      icon: '❤️',
      color: '#EC4899',
      priority: 'low',
      showInGlobal: false,
      showInPersonal: true,
      groupable: true
    },
    content_shared: {
      icon: '🔄',
      color: '#06B6D4',
      priority: 'medium',
      showInGlobal: true,
      showInPersonal: true,
      groupable: true
    },
    comment_posted: {
      icon: '💬',
      color: '#6366F1',
      priority: 'low',
      showInGlobal: false,
      showInPersonal: true,
      groupable: true
    },
    user_joined: {
      icon: '👋',
      color: '#059669',
      priority: 'medium',
      showInGlobal: true,
      showInPersonal: false,
      groupable: false
    },
    milestone_reached: {
      icon: '🎊',
      color: '#DC2626',
      priority: 'high',
      showInGlobal: true,
      showInPersonal: true,
      groupable: false
    }
  },

  // Feed settings
  FEED_SETTINGS: {
    global_feed_limit: 50,
    personal_feed_limit: 100,
    trending_window_hours: 24,
    max_grouping_size: 5,
    activity_expiry_days: 30
  },

  // Real-time settings
  REALTIME_SETTINGS: {
    max_listeners: 100,
    update_batch_size: 10,
    debounce_ms: 500
  }
} as const

// ===== ACTIVITY FEED INTERFACES =====

export interface ActivityFeedItem {
  id: string
  type: keyof typeof ACTIVITY_CONFIG.ACTIVITY_TYPES
  userId: string
  userName: string
  userAvatar?: string
  userTier: string
  timestamp: Timestamp
  
  // Activity data
  title: string
  description: string
  metadata: Record<string, any>
  
  // Content references
  contentId?: string
  achievementId?: string
  challengeId?: string
  
  // Display settings
  icon: string
  color: string
  priority: 'low' | 'medium' | 'high'
  
  // Engagement
  likes: number
  comments: number
  shares: number
  
  // Grouping (for similar activities)
  groupId?: string
  groupCount?: number
  relatedUsers?: string[]
  
  // Visibility
  isPublic: boolean
  isPromoted: boolean
  
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface ActivityGroup {
  id: string
  type: keyof typeof ACTIVITY_CONFIG.ACTIVITY_TYPES
  activities: ActivityFeedItem[]
  count: number
  latestTimestamp: Timestamp
  relatedUsers: string[]
  title: string
  description: string
}

// ===== ACTIVITY FEED ENGINE =====

export class ActivityFeedEngine {
  private static listeners: Map<string, () => void> = new Map()

  /**
   * Log user activity and add to feed
   */
  static async logActivity(
    userId: string,
    type: keyof typeof ACTIVITY_CONFIG.ACTIVITY_TYPES,
    title: string,
    description: string,
    metadata: Record<string, any> = {},
    options: {
      contentId?: string
      achievementId?: string
      challengeId?: string
      isPublic?: boolean
      groupId?: string
    } = {}
  ): Promise<{
    success: boolean
    activityId?: string
    error?: string
  }> {
    try {
      // Get activity type configuration
      const activityConfig = ACTIVITY_CONFIG.ACTIVITY_TYPES[type]
      if (!activityConfig) {
        return {
          success: false,
          error: 'Invalid activity type'
        }
      }

      // Get user profile for display info
      const userProfile = await this.getUserProfile(userId)
      if (!userProfile) {
        return {
          success: false,
          error: 'User profile not found'
        }
      }

      // Check if activity should be grouped
      const groupId = options.groupId || await this.findGroupId(userId, type, metadata)

      // Create activity feed item
      const activity: Omit<ActivityFeedItem, 'id'> = {
        type,
        userId,
        userName: userProfile.displayName || 'Anonymous User',
        userAvatar: userProfile.photoURL,
        userTier: userProfile.gamification.currentTier,
        timestamp: Timestamp.now(),
        title,
        description,
        metadata,
        contentId: options.contentId,
        achievementId: options.achievementId,
        challengeId: options.challengeId,
        icon: activityConfig.icon,
        color: activityConfig.color,
        priority: activityConfig.priority,
        likes: 0,
        comments: 0,
        shares: 0,
        groupId,
        isPublic: options.isPublic ?? activityConfig.showInGlobal,
        isPromoted: false,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }

      // Save activity to database
      const activityRef = doc(collection(db, collections.activities))
      await activityRef.set(activity)

      // Update group if applicable
      if (groupId) {
        await this.updateActivityGroup(groupId, activityRef.id)
      }

      // Trigger notifications for significant activities
      if (activityConfig.priority === 'high') {
        await this.triggerActivityNotifications(userId, type, activity)
      }

      return {
        success: true,
        activityId: activityRef.id
      }

    } catch (error) {
      console.error('Error logging activity:', error)
      return {
        success: false,
        error: 'Failed to log activity'
      }
    }
  }

  /**
   * Get global activity feed
   */
  static async getGlobalFeed(
    limit: number = 50,
    lastActivityId?: string
  ): Promise<{
    activities: ActivityFeedItem[]
    groups: ActivityGroup[]
    hasMore: boolean
    lastActivityId?: string
  }> {
    try {
      let feedQuery = query(
        collection(db, collections.activities),
        where('isPublic', '==', true),
        orderBy('timestamp', 'desc'),
        limit(limit + 1)
      )

      // Add pagination if lastActivityId provided
      if (lastActivityId) {
        // Implementation would add startAfter clause
      }

      const snapshot = await getDocs(feedQuery)
      const activities: ActivityFeedItem[] = []

      snapshot.docs.slice(0, limit).forEach((doc) => {
        activities.push({ id: doc.id, ...doc.data() } as ActivityFeedItem)
      })

      // Group related activities
      const groups = await this.groupActivities(activities)

      return {
        activities,
        groups,
        hasMore: snapshot.docs.length > limit,
        lastActivityId: activities.length > 0 ? activities[activities.length - 1].id : undefined
      }

    } catch (error) {
      console.error('Error getting global feed:', error)
      return {
        activities: [],
        groups: [],
        hasMore: false
      }
    }
  }

  /**
   * Get personalized activity feed for user
   */
  static async getPersonalFeed(
    userId: string,
    limit: number = 100,
    lastActivityId?: string
  ): Promise<{
    activities: ActivityFeedItem[]
    groups: ActivityGroup[]
    hasMore: boolean
    lastActivityId?: string
  }> {
    try {
      // Get user's followed users and interests
      const followedUsers = await this.getFollowedUsers(userId)
      const userInterests = await this.getUserInterests(userId)

      // Build query for personalized feed
      let feedQuery = query(
        collection(db, collections.activities),
        where('userId', 'in', [userId, ...followedUsers]),
        orderBy('timestamp', 'desc'),
        limit(limit + 1)
      )

      const snapshot = await getDocs(feedQuery)
      const activities: ActivityFeedItem[] = []

      snapshot.docs.slice(0, limit).forEach((doc) => {
        const activity = { id: doc.id, ...doc.data() } as ActivityFeedItem
        
        // Filter based on user interests and preferences
        if (this.isRelevantToUser(activity, userInterests)) {
          activities.push(activity)
        }
      })

      // Group related activities
      const groups = await this.groupActivities(activities)

      return {
        activities,
        groups,
        hasMore: snapshot.docs.length > limit,
        lastActivityId: activities.length > 0 ? activities[activities.length - 1].id : undefined
      }

    } catch (error) {
      console.error('Error getting personal feed:', error)
      return {
        activities: [],
        groups: [],
        hasMore: false
      }
    }
  }

  /**
   * Get trending activities
   */
  static async getTrendingActivities(
    timeWindow: 'hour' | 'day' | 'week' = 'day',
    limit: number = 20
  ): Promise<ActivityFeedItem[]> {
    try {
      const windowHours = {
        hour: 1,
        day: 24,
        week: 168
      }

      const since = new Date()
      since.setHours(since.getHours() - windowHours[timeWindow])

      const trendingQuery = query(
        collection(db, collections.activities),
        where('timestamp', '>=', Timestamp.fromDate(since)),
        where('isPublic', '==', true),
        orderBy('timestamp', 'desc'),
        limit(limit * 2) // Get more to filter and sort by engagement
      )

      const snapshot = await getDocs(trendingQuery)
      const activities: ActivityFeedItem[] = []

      snapshot.docs.forEach((doc) => {
        activities.push({ id: doc.id, ...doc.data() } as ActivityFeedItem)
      })

      // Sort by engagement score (likes + comments + shares)
      activities.sort((a, b) => {
        const scoreA = a.likes + a.comments + a.shares
        const scoreB = b.likes + b.comments + b.shares
        return scoreB - scoreA
      })

      return activities.slice(0, limit)

    } catch (error) {
      console.error('Error getting trending activities:', error)
      return []
    }
  }

  /**
   * Subscribe to real-time activity feed updates
   */
  static subscribeToFeed(
    feedType: 'global' | 'personal',
    userId?: string,
    callback: (activities: ActivityFeedItem[]) => void
  ): () => void {
    let feedQuery

    if (feedType === 'global') {
      feedQuery = query(
        collection(db, collections.activities),
        where('isPublic', '==', true),
        orderBy('timestamp', 'desc'),
        limit(ACTIVITY_CONFIG.FEED_SETTINGS.global_feed_limit)
      )
    } else {
      if (!userId) {
        throw new Error('User ID required for personal feed')
      }
      
      feedQuery = query(
        collection(db, collections.activities),
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        limit(ACTIVITY_CONFIG.FEED_SETTINGS.personal_feed_limit)
      )
    }

    const unsubscribe = onSnapshot(feedQuery, (snapshot) => {
      const activities: ActivityFeedItem[] = []
      snapshot.forEach((doc) => {
        activities.push({ id: doc.id, ...doc.data() } as ActivityFeedItem)
      })
      callback(activities)
    })

    // Store the unsubscribe function
    const listenerId = `feed_${feedType}_${userId || 'global'}`
    this.listeners.set(listenerId, unsubscribe)

    return () => {
      unsubscribe()
      this.listeners.delete(listenerId)
    }
  }

  /**
   * Like an activity
   */
  static async likeActivity(
    activityId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const activityRef = doc(db, collections.activities, activityId)
      
      // Check if user already liked this activity
      const existingLike = await this.checkExistingLike(activityId, userId)
      if (existingLike) {
        return {
          success: false,
          error: 'Activity already liked'
        }
      }

      // Update activity like count
      const batch = writeBatch(db)
      
      batch.update(activityRef, {
        likes: 1, // Would use increment in real implementation
        updatedAt: Timestamp.now()
      })

      // Create like record
      const likeRef = doc(collection(db, collections.activities, activityId, 'likes'))
      batch.set(likeRef, {
        userId,
        timestamp: Timestamp.now()
      })

      await batch.commit()

      return { success: true }

    } catch (error) {
      console.error('Error liking activity:', error)
      return {
        success: false,
        error: 'Failed to like activity'
      }
    }
  }

  /**
   * Share an activity
   */
  static async shareActivity(
    activityId: string,
    userId: string,
    platform: 'internal' | 'twitter' | 'facebook' | 'reddit' = 'internal'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const activityRef = doc(db, collections.activities, activityId)
      
      // Update share count
      await activityRef.update({
        shares: 1, // Would use increment in real implementation
        updatedAt: Timestamp.now()
      })

      // Log share activity
      if (platform === 'internal') {
        await this.logActivity(
          userId,
          'content_shared',
          'Shared an activity',
          'User shared community activity',
          {
            originalActivityId: activityId,
            platform
          }
        )
      }

      return { success: true }

    } catch (error) {
      console.error('Error sharing activity:', error)
      return {
        success: false,
        error: 'Failed to share activity'
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async getUserProfile(userId: string): Promise<UserProfile | null> {
    // Implementation would fetch from Firestore
    // For now, return mock data
    return {
      id: userId,
      email: '<EMAIL>',
      displayName: 'Test User',
      photoURL: undefined,
      role: 'user',
      gamification: {
        totalPoints: 0,
        currentTier: 'bronze',
        tierProgress: 0,
        lastTierUpdate: Timestamp.now(),
        qualityScore: 0.5,
        achievementCount: 0,
        streakDays: 0,
        lastActivity: Timestamp.now(),
        joinDate: Timestamp.now(),
        membershipDays: 0,
        pointsEarnedToday: 0,
        pointsEarnedThisWeek: 0,
        pointsEarnedThisMonth: 0
      },
      moderation: {
        warningCount: 0,
        suspensionHistory: [],
        lastViolation: null,
        reputationScore: 100,
        reportCount: 0,
        helpfulContributions: 0,
        isSuspended: false,
        suspensionEnds: null,
        suspensionReason: null,
        appealCount: 0,
        lastAppeal: null
      },
      preferences: {
        emailNotifications: true,
        communityNotifications: true,
        moderationAlerts: true,
        privacyLevel: 'public',
        languageFilter: false,
        contentMaturityLevel: 'all',
        autoSaveContent: true,
        displayAchievements: true,
        displayPoints: true,
        displayTier: true
      },
      activity: {
        contentCreated: 0,
        commentsPosted: 0,
        likesGiven: 0,
        likesReceived: 0,
        sharesGiven: 0,
        sharesReceived: 0,
        reportsSubmitted: 0,
        moderationActionsReceived: 0,
        helpfulVotesReceived: 0,
        challengesParticipated: 0,
        challengesWon: 0
      },
      isActive: true,
      lastLoginAt: Timestamp.now(),
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  }

  private static async findGroupId(
    userId: string,
    type: keyof typeof ACTIVITY_CONFIG.ACTIVITY_TYPES,
    metadata: Record<string, any>
  ): Promise<string | undefined> {
    const activityConfig = ACTIVITY_CONFIG.ACTIVITY_TYPES[type]
    if (!activityConfig.groupable) {
      return undefined
    }

    // Implementation would find existing group for similar activities
    // For now, return undefined (no grouping)
    return undefined
  }

  private static async updateActivityGroup(
    groupId: string,
    activityId: string
  ): Promise<void> {
    // Implementation would update activity group
  }

  private static async triggerActivityNotifications(
    userId: string,
    type: keyof typeof ACTIVITY_CONFIG.ACTIVITY_TYPES,
    activity: Omit<ActivityFeedItem, 'id'>
  ): Promise<void> {
    // Implementation would trigger notifications for followers
  }

  private static async groupActivities(activities: ActivityFeedItem[]): Promise<ActivityGroup[]> {
    // Implementation would group similar activities
    return []
  }

  private static async getFollowedUsers(userId: string): Promise<string[]> {
    // Implementation would get user's followed users
    return []
  }

  private static async getUserInterests(userId: string): Promise<string[]> {
    // Implementation would get user's interests
    return []
  }

  private static isRelevantToUser(activity: ActivityFeedItem, interests: string[]): boolean {
    // Implementation would check relevance based on interests
    return true
  }

  private static async checkExistingLike(activityId: string, userId: string): Promise<boolean> {
    // Implementation would check if user already liked activity
    return false
  }

  /**
   * Clean up expired activities
   */
  static async cleanupExpiredActivities(): Promise<{
    success: boolean
    deletedCount: number
    error?: string
  }> {
    try {
      const expiryDate = new Date()
      expiryDate.setDate(expiryDate.getDate() - ACTIVITY_CONFIG.FEED_SETTINGS.activity_expiry_days)

      const expiredQuery = query(
        collection(db, collections.activities),
        where('createdAt', '<', Timestamp.fromDate(expiryDate))
      )

      const snapshot = await getDocs(expiredQuery)
      const batch = writeBatch(db)

      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref)
      })

      await batch.commit()

      return {
        success: true,
        deletedCount: snapshot.docs.length
      }

    } catch (error) {
      console.error('Error cleaning up expired activities:', error)
      return {
        success: false,
        deletedCount: 0,
        error: 'Failed to cleanup expired activities'
      }
    }
  }

  /**
   * Clean up all listeners
   */
  static cleanup(): void {
    this.listeners.forEach((unsubscribe) => {
      unsubscribe()
    })
    this.listeners.clear()
  }
}

// ===== ACTIVITY LOGGING HELPERS =====

export class ActivityLogger {
  /**
   * Log content creation activity
   */
  static async logContentCreated(
    userId: string,
    contentId: string,
    contentType: string,
    title: string
  ): Promise<void> {
    await ActivityFeedEngine.logActivity(
      userId,
      'content_created',
      `Created ${contentType}`,
      title,
      { contentType },
      { contentId, isPublic: true }
    )
  }

  /**
   * Log achievement unlock activity
   */
  static async logAchievementUnlocked(
    userId: string,
    achievementId: string,
    achievementTitle: string,
    pointsAwarded: number
  ): Promise<void> {
    await ActivityFeedEngine.logActivity(
      userId,
      'achievement_unlocked',
      `🏆 Unlocked: ${achievementTitle}`,
      `Earned ${pointsAwarded} points`,
      { pointsAwarded },
      { achievementId, isPublic: true }
    )
  }

  /**
   * Log tier promotion activity
   */
  static async logTierPromotion(
    userId: string,
    fromTier: string,
    toTier: string
  ): Promise<void> {
    await ActivityFeedEngine.logActivity(
      userId,
      'tier_promoted',
      `⬆️ Promoted to ${toTier}`,
      `Advanced from ${fromTier} to ${toTier}`,
      { fromTier, toTier },
      { isPublic: true }
    )
  }

  /**
   * Log challenge participation
   */
  static async logChallengeJoined(
    userId: string,
    challengeId: string,
    challengeTitle: string
  ): Promise<void> {
    await ActivityFeedEngine.logActivity(
      userId,
      'challenge_joined',
      `🎯 Joined Challenge`,
      challengeTitle,
      {},
      { challengeId, isPublic: true }
    )
  }
}

export default ActivityFeedEngine