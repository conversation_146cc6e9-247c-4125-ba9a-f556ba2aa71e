/**
 * Enhanced Challenge Framework - Phase 2 Implementation
 * 
 * Comprehensive challenge system with team support, dynamic requirements,
 * voting mechanisms, and automated judging. Supports multiple challenge types
 * including design contests, community challenges, and seasonal events.
 * 
 * <AUTHOR> Team - Phase 2 Community Implementation
 * @version 2.0.0
 */

import { Timestamp, writeBatch, doc, collection, query, where, orderBy, limit, getDocs, runTransaction } from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import { 
  Challenge, 
  ChallengeParticipation, 
  Submission,
  UserProfile 
} from './types'
import { NotificationEngine } from './notificationEngine'
import { ActivityLogger } from './activityFeed'
import { PointCalculationEngine } from './pointEngine'
import { AchievementEngine } from './achievementEngine'

// ===== CHALLENGE FRAMEWORK CONFIGURATION =====

export const CHALLENGE_CONFIG = {
  // Challenge types with specific settings
  CHALLENGE_TYPES: {
    design: {
      displayName: 'Design Challenge',
      icon: '🎨',
      color: '#8B5CF6',
      maxParticipants: 100,
      allowTeams: false,
      requiresSubmission: true,
      votingEnabled: true,
      expertJudging: true,
      defaultDuration: 168, // 1 week in hours
      submissionFormats: ['image', 'video', 'mixed'],
      judging: {
        criteria: ['creativity', 'technical_skill', 'originality', 'adherence_to_theme'],
        weights: [0.3, 0.25, 0.25, 0.2]
      }
    },
    community: {
      displayName: 'Community Challenge',
      icon: '🤝',
      color: '#10B981',
      maxParticipants: 500,
      allowTeams: true,
      requiresSubmission: false,
      votingEnabled: true,
      expertJudging: false,
      defaultDuration: 336, // 2 weeks in hours
      submissionFormats: ['text', 'image', 'mixed'],
      judging: {
        criteria: ['participation', 'helpfulness', 'engagement'],
        weights: [0.4, 0.3, 0.3]
      }
    },
    engagement: {
      displayName: 'Engagement Challenge',
      icon: '🚀',
      color: '#F59E0B',
      maxParticipants: 1000,
      allowTeams: false,
      requiresSubmission: false,
      votingEnabled: false,
      expertJudging: false,
      defaultDuration: 720, // 30 days in hours
      submissionFormats: [],
      judging: {
        criteria: ['activity_score', 'consistency', 'quality'],
        weights: [0.5, 0.3, 0.2]
      }
    },
    special: {
      displayName: 'Special Event',
      icon: '⭐',
      color: '#EF4444',
      maxParticipants: 200,
      allowTeams: true,
      requiresSubmission: true,
      votingEnabled: true,
      expertJudging: true,
      defaultDuration: 504, // 3 weeks in hours
      submissionFormats: ['image', 'video', 'text', 'mixed'],
      judging: {
        criteria: ['creativity', 'impact', 'community_value', 'execution'],
        weights: [0.25, 0.25, 0.25, 0.25]
      }
    }
  },

  // Difficulty levels
  DIFFICULTY_LEVELS: {
    beginner: {
      displayName: 'Beginner',
      icon: '🌱',
      color: '#10B981',
      pointMultiplier: 1.0,
      minTierRequired: 'bronze',
      estimatedTimeHours: 2
    },
    intermediate: {
      displayName: 'Intermediate',
      icon: '🌿',
      color: '#3B82F6',
      pointMultiplier: 1.5,
      minTierRequired: 'silver',
      estimatedTimeHours: 8
    },
    advanced: {
      displayName: 'Advanced',
      icon: '🌳',
      color: '#8B5CF6',
      pointMultiplier: 2.0,
      minTierRequired: 'gold',
      estimatedTimeHours: 20
    },
    expert: {
      displayName: 'Expert',
      icon: '🏔️',
      color: '#EF4444',
      pointMultiplier: 3.0,
      minTierRequired: 'platinum',
      estimatedTimeHours: 40
    }
  },

  // Reward structures
  REWARD_STRUCTURES: {
    placement_rewards: {
      1: { pointMultiplier: 3.0, badge: 'champion', specialReward: true },
      2: { pointMultiplier: 2.5, badge: 'runner_up', specialReward: true },
      3: { pointMultiplier: 2.0, badge: 'third_place', specialReward: false },
      participation: { pointMultiplier: 0.5, badge: 'participant', specialReward: false }
    },
    team_bonuses: {
      winning_team_bonus: 1.2,
      team_participation_bonus: 1.1
    }
  },

  // Voting and judging settings
  VOTING_SETTINGS: {
    community_voting_weight: 0.7,
    expert_voting_weight: 0.3,
    min_votes_for_validity: 5,
    voting_duration_hours: 72,
    anti_gaming_measures: true
  }
} as const

// ===== ENHANCED CHALLENGE INTERFACES =====

export interface ChallengeTeam {
  id: string
  challengeId: string
  name: string
  description: string
  captainId: string
  members: {
    userId: string
    joinedAt: Timestamp
    role: 'captain' | 'member'
    contribution: number
  }[]
  maxMembers: number
  isOpen: boolean
  inviteCode?: string
  stats: {
    totalScore: number
    submissions: number
    votes: number
    placement?: number
  }
  status: 'forming' | 'active' | 'submitted' | 'completed'
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface ChallengeVote {
  id: string
  challengeId: string
  submissionId: string
  voterId: string
  scores: Record<string, number> // Criterion -> score
  totalScore: number
  comment?: string
  isExpertVote: boolean
  weight: number
  createdAt: Timestamp
}

export interface ChallengeLeaderboard {
  challengeId: string
  rankings: {
    rank: number
    submissionId: string
    teamId?: string
    userId: string
    totalScore: number
    categoryScores: Record<string, number>
    voteCount: number
    isWinner: boolean
  }[]
  lastUpdated: Timestamp
}

// ===== CHALLENGE FRAMEWORK ENGINE =====

export class ChallengeFramework {
  /**
   * Create new challenge
   */
  static async createChallenge(
    challenge: Omit<Challenge, 'id' | 'createdAt' | 'updatedAt' | 'stats'>
  ): Promise<{
    success: boolean
    challengeId?: string
    error?: string
  }> {
    try {
      const challengeDoc: Omit<Challenge, 'id'> = {
        ...challenge,
        stats: {
          participants: 0,
          submissions: 0,
          views: 0,
          likes: 0
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }

      const challengeRef = doc(collection(db, collections.challenges))
      await challengeRef.set(challengeDoc)

      // Send announcement notification to eligible users
      await this.announceNewChallenge(challengeRef.id, challenge)

      return {
        success: true,
        challengeId: challengeRef.id
      }

    } catch (error) {
      console.error('Error creating challenge:', error)
      return {
        success: false,
        error: 'Failed to create challenge'
      }
    }
  }

  /**
   * Join challenge (individual or team)
   */
  static async joinChallenge(
    challengeId: string,
    userId: string,
    teamId?: string
  ): Promise<{
    success: boolean
    participationId?: string
    error?: string
  }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get challenge details
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)
        
        if (!challengeDoc.exists()) {
          throw new Error('Challenge not found')
        }

        const challenge = challengeDoc.data() as Challenge

        // Validate challenge status and eligibility
        const validation = await this.validateChallengeJoin(challenge, userId, teamId)
        if (!validation.valid) {
          throw new Error(validation.reason)
        }

        // Check if user already participating
        const existingParticipation = await this.checkExistingParticipation(challengeId, userId)
        if (existingParticipation) {
          throw new Error('User already participating in this challenge')
        }

        // Create participation record
        const participation: Omit<ChallengeParticipation, 'id'> = {
          challengeId,
          userId,
          joinedAt: Timestamp.now(),
          status: 'active',
          progress: {}
        }

        const participationRef = doc(collection(db, collections.challengeParticipations))
        transaction.set(participationRef, participation)

        // Update challenge stats
        transaction.update(challengeRef, {
          'stats.participants': challenge.stats.participants + 1,
          updatedAt: Timestamp.now()
        })

        // Award participation points
        await PointCalculationEngine.awardPoints(
          userId,
          'challenge_join',
          `Joined challenge: ${challenge.title}`,
          { challengeId }
        )

        // Log activity
        await ActivityLogger.logChallengeJoined(userId, challengeId, challenge.title)

        // Send notification
        await NotificationEngine.sendNotification(
          userId,
          'challenge_update',
          'Challenge Joined!',
          `You've successfully joined "${challenge.title}"`,
          { challengeId }
        )

        return {
          success: true,
          participationId: participationRef.id
        }
      })

    } catch (error) {
      console.error('Error joining challenge:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to join challenge'
      }
    }
  }

  /**
   * Submit to challenge
   */
  static async submitToChallenge(
    challengeId: string,
    userId: string,
    submission: Omit<Submission, 'id' | 'challengeId' | 'userId' | 'createdAt' | 'updatedAt' | 'stats'>
  ): Promise<{
    success: boolean
    submissionId?: string
    error?: string
  }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get challenge details
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)
        
        if (!challengeDoc.exists()) {
          throw new Error('Challenge not found')
        }

        const challenge = challengeDoc.data() as Challenge

        // Validate submission eligibility
        const validation = await this.validateSubmission(challenge, userId, submission)
        if (!validation.valid) {
          throw new Error(validation.reason)
        }

        // Create submission record
        const submissionDoc: Omit<Submission, 'id'> = {
          challengeId,
          userId,
          ...submission,
          stats: {
            views: 0,
            likes: 0,
            comments: 0,
            shares: 0,
            votes: 0,
            rating: 0
          },
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        }

        const submissionRef = doc(collection(db, collections.challengeSubmissions))
        transaction.set(submissionRef, submissionDoc)

        // Update challenge stats
        transaction.update(challengeRef, {
          'stats.submissions': challenge.stats.submissions + 1,
          updatedAt: Timestamp.now()
        })

        // Update participation status
        await this.updateParticipationStatus(userId, challengeId, 'submitted')

        // Award submission points
        const difficultyConfig = CHALLENGE_CONFIG.DIFFICULTY_LEVELS[challenge.difficulty]
        const basePoints = 200 * difficultyConfig.pointMultiplier

        await PointCalculationEngine.awardPoints(
          userId,
          'challenge_submit',
          `Submitted to challenge: ${challenge.title}`,
          { challengeId, submissionId: submissionRef.id }
        )

        // Process achievement triggers
        await AchievementEngine.processActivityTrigger({
          userId,
          activityType: 'challenge_submit',
          data: { challengeId, submissionId: submissionRef.id },
          timestamp: Timestamp.now()
        })

        // Send notification
        await NotificationEngine.sendNotification(
          userId,
          'challenge_update',
          'Submission Received!',
          `Your submission to "${challenge.title}" has been received`,
          { challengeId, submissionId: submissionRef.id }
        )

        return {
          success: true,
          submissionId: submissionRef.id
        }
      })

    } catch (error) {
      console.error('Error submitting to challenge:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to submit to challenge'
      }
    }
  }

  /**
   * Vote on challenge submission
   */
  static async voteOnSubmission(
    challengeId: string,
    submissionId: string,
    voterId: string,
    scores: Record<string, number>,
    comment?: string
  ): Promise<{
    success: boolean
    voteId?: string
    error?: string
  }> {
    try {
      // Get challenge details for judging criteria
      const challenge = await this.getChallenge(challengeId)
      if (!challenge) {
        throw new Error('Challenge not found')
      }

      // Validate voter eligibility
      const eligibility = await this.validateVoterEligibility(challengeId, voterId)
      if (!eligibility.valid) {
        throw new Error(eligibility.reason)
      }

      // Check if user already voted on this submission
      const existingVote = await this.checkExistingVote(submissionId, voterId)
      if (existingVote) {
        throw new Error('User has already voted on this submission')
      }

      // Calculate total score based on criteria weights
      const totalScore = this.calculateWeightedScore(scores, challenge.judging.criteria)

      // Determine vote weight (expert vs community)
      const isExpert = await this.isExpertJudge(voterId, challengeId)
      const weight = isExpert 
        ? CHALLENGE_CONFIG.VOTING_SETTINGS.expert_voting_weight
        : CHALLENGE_CONFIG.VOTING_SETTINGS.community_voting_weight

      // Create vote record
      const vote: Omit<ChallengeVote, 'id'> = {
        challengeId,
        submissionId,
        voterId,
        scores,
        totalScore,
        comment,
        isExpertVote: isExpert,
        weight,
        createdAt: Timestamp.now()
      }

      const voteRef = doc(collection(db, 'challengeVotes'))
      await voteRef.set(vote)

      // Update submission rating
      await this.updateSubmissionRating(submissionId)

      // Update challenge leaderboard
      await this.updateChallengeLeaderboard(challengeId)

      // Award voting points
      await PointCalculationEngine.awardPoints(
        voterId,
        'challenge_vote',
        `Voted on challenge submission`,
        { challengeId, submissionId }
      )

      return {
        success: true,
        voteId: voteRef.id
      }

    } catch (error) {
      console.error('Error voting on submission:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to vote on submission'
      }
    }
  }

  /**
   * Create challenge team
   */
  static async createTeam(
    challengeId: string,
    captainId: string,
    teamName: string,
    description: string,
    maxMembers: number = 5
  ): Promise<{
    success: boolean
    teamId?: string
    error?: string
  }> {
    try {
      // Validate team creation eligibility
      const challenge = await this.getChallenge(challengeId)
      if (!challenge?.allowTeams) {
        throw new Error('Teams not allowed for this challenge')
      }

      // Create team
      const team: Omit<ChallengeTeam, 'id'> = {
        challengeId,
        name: teamName,
        description,
        captainId,
        members: [{
          userId: captainId,
          joinedAt: Timestamp.now(),
          role: 'captain',
          contribution: 0
        }],
        maxMembers,
        isOpen: true,
        inviteCode: this.generateInviteCode(),
        stats: {
          totalScore: 0,
          submissions: 0,
          votes: 0
        },
        status: 'forming',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }

      const teamRef = doc(collection(db, collections.challengeTeams))
      await teamRef.set(team)

      return {
        success: true,
        teamId: teamRef.id
      }

    } catch (error) {
      console.error('Error creating team:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create team'
      }
    }
  }

  /**
   * End challenge and process results
   */
  static async endChallenge(
    challengeId: string,
    processedBy: string
  ): Promise<{
    success: boolean
    results?: {
      winners: Array<{
        rank: number
        userId: string
        submissionId: string
        score: number
        rewards: {
          points: number
          badge?: string
          specialReward?: string
        }
      }>
      totalParticipants: number
      totalSubmissions: number
    }
    error?: string
  }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get challenge
        const challengeRef = doc(db, collections.challenges, challengeId)
        const challengeDoc = await transaction.get(challengeRef)
        
        if (!challengeDoc.exists()) {
          throw new Error('Challenge not found')
        }

        const challenge = challengeDoc.data() as Challenge

        // Update challenge status
        transaction.update(challengeRef, {
          status: 'completed',
          updatedAt: Timestamp.now()
        })

        // Get final leaderboard
        const leaderboard = await this.getFinalLeaderboard(challengeId)

        // Process rewards and notifications
        const results = await this.processWinners(challenge, leaderboard)

        // Send completion notifications
        await this.sendCompletionNotifications(challengeId, results)

        return {
          success: true,
          results
        }
      })

    } catch (error) {
      console.error('Error ending challenge:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to end challenge'
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async announceNewChallenge(
    challengeId: string,
    challenge: Omit<Challenge, 'id' | 'createdAt' | 'updatedAt' | 'stats'>
  ): Promise<void> {
    // Get eligible users based on challenge requirements
    const eligibleUsers = await this.getEligibleUsers(challenge)

    // Send bulk notifications
    await NotificationEngine.sendBulkNotifications(
      eligibleUsers,
      'challenge_update',
      `New ${challenge.type} Challenge: ${challenge.title}`,
      challenge.description,
      { challengeId }
    )
  }

  private static async validateChallengeJoin(
    challenge: Challenge,
    userId: string,
    teamId?: string
  ): Promise<{ valid: boolean; reason?: string }> {
    // Check if challenge is active
    if (challenge.status !== 'active') {
      return { valid: false, reason: 'Challenge is not active' }
    }

    // Check if within registration period
    const now = Timestamp.now()
    if (now > challenge.timeline.submissionDeadline) {
      return { valid: false, reason: 'Registration period has ended' }
    }

    // Check participant limit
    if (challenge.requirements.maxParticipants && 
        challenge.stats.participants >= challenge.requirements.maxParticipants) {
      return { valid: false, reason: 'Challenge is full' }
    }

    // Check user tier requirements
    const userProfile = await this.getUserProfile(userId)
    if (!userProfile) {
      return { valid: false, reason: 'User profile not found' }
    }

    const requiredTier = challenge.requirements.minLevel
    if (requiredTier && !this.meetsTierRequirement(userProfile.gamification.currentTier, requiredTier)) {
      return { valid: false, reason: `Requires ${requiredTier} tier or higher` }
    }

    return { valid: true }
  }

  private static async validateSubmission(
    challenge: Challenge,
    userId: string,
    submission: any
  ): Promise<{ valid: boolean; reason?: string }> {
    // Check if challenge accepts submissions
    if (!challenge.requirements) {
      return { valid: false, reason: 'Challenge does not accept submissions' }
    }

    // Check submission deadline
    const now = Timestamp.now()
    if (now > challenge.timeline.submissionDeadline) {
      return { valid: false, reason: 'Submission deadline has passed' }
    }

    // Validate submission format
    const allowedFormats = CHALLENGE_CONFIG.CHALLENGE_TYPES[challenge.type].submissionFormats
    if (!allowedFormats.includes(submission.content.type)) {
      return { valid: false, reason: `Invalid submission format. Allowed: ${allowedFormats.join(', ')}` }
    }

    return { valid: true }
  }

  private static async validateVoterEligibility(
    challengeId: string,
    voterId: string
  ): Promise<{ valid: boolean; reason?: string }> {
    // Check if user is participating in the challenge
    const participation = await this.checkExistingParticipation(challengeId, voterId)
    if (!participation) {
      return { valid: false, reason: 'Must be participating in challenge to vote' }
    }

    return { valid: true }
  }

  private static calculateWeightedScore(
    scores: Record<string, number>,
    criteria: string[]
  ): number {
    const challengeType = 'design' // Would get from challenge
    const weights = CHALLENGE_CONFIG.CHALLENGE_TYPES[challengeType].judging.weights

    let totalScore = 0
    criteria.forEach((criterion, index) => {
      const score = scores[criterion] || 0
      const weight = weights[index] || 1
      totalScore += score * weight
    })

    return totalScore
  }

  private static async updateSubmissionRating(submissionId: string): Promise<void> {
    // Implementation would calculate new average rating
  }

  private static async updateChallengeLeaderboard(challengeId: string): Promise<void> {
    // Implementation would update real-time leaderboard
  }

  private static async getFinalLeaderboard(challengeId: string): Promise<ChallengeLeaderboard> {
    // Implementation would get final rankings
    return {
      challengeId,
      rankings: [],
      lastUpdated: Timestamp.now()
    }
  }

  private static async processWinners(
    challenge: Challenge,
    leaderboard: ChallengeLeaderboard
  ): Promise<any> {
    // Implementation would process winner rewards
    return {
      winners: [],
      totalParticipants: challenge.stats.participants,
      totalSubmissions: challenge.stats.submissions
    }
  }

  private static async sendCompletionNotifications(
    challengeId: string,
    results: any
  ): Promise<void> {
    // Implementation would send completion notifications
  }

  private static generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 10).toUpperCase()
  }

  private static async getEligibleUsers(challenge: any): Promise<string[]> {
    // Implementation would get eligible users
    return []
  }

  private static async getChallenge(challengeId: string): Promise<Challenge | null> {
    // Implementation would fetch challenge
    return null
  }

  private static async getUserProfile(userId: string): Promise<UserProfile | null> {
    // Implementation would fetch user profile
    return null
  }

  private static async checkExistingParticipation(challengeId: string, userId: string): Promise<boolean> {
    // Implementation would check participation
    return false
  }

  private static async checkExistingVote(submissionId: string, voterId: string): Promise<boolean> {
    // Implementation would check existing vote
    return false
  }

  private static async isExpertJudge(userId: string, challengeId: string): Promise<boolean> {
    // Implementation would check expert status
    return false
  }

  private static async updateParticipationStatus(
    userId: string, 
    challengeId: string, 
    status: string
  ): Promise<void> {
    // Implementation would update participation status
  }

  private static meetsTierRequirement(currentTier: string, requiredTier: string): boolean {
    const tierLevels = { bronze: 1, silver: 2, gold: 3, platinum: 4 }
    return tierLevels[currentTier as keyof typeof tierLevels] >= tierLevels[requiredTier as keyof typeof tierLevels]
  }
}

export default ChallengeFramework