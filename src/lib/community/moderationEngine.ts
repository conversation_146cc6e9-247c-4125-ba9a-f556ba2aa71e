/**
 * Moderation Engine - Phase 1 Implementation
 * 
 * Comprehensive moderation system for community content and user behavior.
 * Handles automated and manual moderation workflows, appeals, and enforcement.
 * 
 * <AUTHOR> Team - Phase 1 Community Implementation
 * @version 1.0.0
 */

import { Timestamp, writeBatch, doc, collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import { 
  ModerationAction, 
  ModerationQueue, 
  ModerationFlag, 
  Appeal, 
  CommunityRule,
  RuleCondition,
  RuleAction,
  UserProfile 
} from './types'

// ===== MODERATION CONFIGURATION =====

export const MODERATION_CONFIG = {
  // Auto-moderation thresholds
  AUTO_MODERATION_THRESHOLDS: {
    spam_score: 0.8,
    toxicity_score: 0.7,
    harassment_score: 0.6,
    misinformation_score: 0.9
  },

  // Action severity escalation
  SEVERITY_ESCALATION: {
    minor: ['warning'],
    moderate: ['warning', 'content_removal', 'temporary_suspension'],
    severe: ['content_removal', 'temporary_suspension', 'point_reduction'],
    critical: ['temporary_suspension', 'permanent_ban', 'point_reduction']
  },

  // Default suspension durations (in hours)
  SUSPENSION_DURATIONS: {
    first_offense: 24,
    second_offense: 72,
    third_offense: 168, // 1 week
    repeat_offender: 720  // 30 days
  },

  // Queue priorities
  QUEUE_PRIORITIES: {
    automatic: 'medium',
    reported: 'high',
    scheduled_review: 'low',
    appeal: 'high',
    escalated: 'critical'
  }
} as const

// ===== MODERATION ENGINE =====

export class ModerationEngine {
  /**
   * Process content through automated moderation
   */
  static async processAutoModeration(
    contentId: string,
    content: string,
    authorId: string,
    metadata: Record<string, any> = {}
  ): Promise<{
    action: 'approve' | 'flag' | 'remove' | 'queue_review'
    confidence: number
    reasons: string[]
    riskScore: number
  }> {
    try {
      const analysis = await this.analyzeContent(content, metadata)
      
      // Determine action based on risk scores
      if (analysis.riskScore > 0.9) {
        return {
          action: 'remove',
          confidence: analysis.confidence,
          reasons: analysis.flaggedReasons,
          riskScore: analysis.riskScore
        }
      }
      
      if (analysis.riskScore > 0.7) {
        return {
          action: 'queue_review',
          confidence: analysis.confidence,
          reasons: analysis.flaggedReasons,
          riskScore: analysis.riskScore
        }
      }
      
      if (analysis.riskScore > 0.5) {
        return {
          action: 'flag',
          confidence: analysis.confidence,
          reasons: analysis.flaggedReasons,
          riskScore: analysis.riskScore
        }
      }
      
      return {
        action: 'approve',
        confidence: analysis.confidence,
        reasons: [],
        riskScore: analysis.riskScore
      }

    } catch (error) {
      console.error('Error in auto-moderation:', error)
      // Default to manual review on error
      return {
        action: 'queue_review',
        confidence: 0.5,
        reasons: ['system_error'],
        riskScore: 0.5
      }
    }
  }

  /**
   * Add content to moderation queue
   */
  static async addToModerationQueue(
    contentId: string,
    queueType: ModerationQueue['queueType'],
    priority: ModerationQueue['priority'],
    reportedBy: string[] = [],
    autoAnalysis?: ModerationQueue['autoAnalysis']
  ): Promise<{
    success: boolean
    queueId?: string
    error?: string
  }> {
    try {
      const queueItem: Omit<ModerationQueue, 'id'> = {
        contentId,
        reportedBy,
        priority,
        queueType,
        estimatedReviewTime: this.estimateReviewTime(priority, queueType),
        complexity: await this.assessComplexity(contentId),
        autoAnalysis: autoAnalysis || await this.generateAutoAnalysis(contentId),
        status: 'pending',
        createdAt: Timestamp.now()
      }

      const queueRef = doc(collection(db, collections.moderationQueue))
      await queueRef.set(queueItem)

      return {
        success: true,
        queueId: queueRef.id
      }

    } catch (error) {
      console.error('Error adding to moderation queue:', error)
      return {
        success: false,
        error: 'Failed to add to moderation queue'
      }
    }
  }

  /**
   * Take moderation action on user or content
   */
  static async takeModerationAction(
    targetUserId: string,
    moderatorId: string,
    actionType: ModerationAction['actionType'],
    reason: string,
    evidence: string[],
    targetContentId?: string,
    customDuration?: number
  ): Promise<{
    success: boolean
    actionId?: string
    error?: string
  }> {
    try {
      const severity = this.determineSeverity(actionType, reason)
      const duration = customDuration || this.calculateDuration(targetUserId, actionType, severity)
      const restrictions = this.generateRestrictions(actionType, severity)

      const action: Omit<ModerationAction, 'id'> = {
        targetUserId,
        targetContentId,
        moderatorId,
        actionType,
        reason,
        evidence,
        severity,
        duration,
        restrictions,
        isAppealable: this.isActionAppealable(actionType, severity),
        appealDeadline: this.calculateAppealDeadline(actionType),
        status: 'active',
        pointsDeducted: this.calculatePointDeduction(actionType, severity),
        featuresRestricted: this.getRestrictedFeatures(restrictions),
        createdAt: Timestamp.now(),
        expiresAt: duration ? this.calculateExpirationDate(duration) : undefined
      }

      // Use batch write for atomic operation
      const batch = writeBatch(db)

      // Create moderation action record
      const actionRef = doc(collection(db, collections.moderationActions))
      batch.set(actionRef, action)

      // Update user profile
      await this.updateUserModerationStatus(batch, targetUserId, action)

      // Update content if applicable
      if (targetContentId && this.affectsContent(actionType)) {
        await this.updateContentModerationStatus(batch, targetContentId, action)
      }

      await batch.commit()

      return {
        success: true,
        actionId: actionRef.id
      }

    } catch (error) {
      console.error('Error taking moderation action:', error)
      return {
        success: false,
        error: 'Failed to take moderation action'
      }
    }
  }

  /**
   * Submit appeal for moderation action
   */
  static async submitAppeal(
    userId: string,
    actionId: string,
    reason: string,
    evidence: string[],
    appealType: Appeal['appealType'],
    additionalContext: string
  ): Promise<{
    success: boolean
    appealId?: string
    error?: string
  }> {
    try {
      // Validate appeal eligibility
      const eligibility = await this.checkAppealEligibility(userId, actionId)
      if (!eligibility.eligible) {
        return {
          success: false,
          error: eligibility.reason
        }
      }

      const appeal: Omit<Appeal, 'id'> = {
        userId,
        actionId,
        reason,
        evidence,
        appealType,
        urgency: this.determineAppealUrgency(appealType, reason),
        additionalContext,
        status: 'submitted',
        submittedAt: Timestamp.now()
      }

      const appealRef = doc(collection(db, collections.appeals))
      await appealRef.set(appeal)

      // Add to moderation queue for review
      await this.addToModerationQueue(
        actionId,
        'appeal',
        'high',
        [userId]
      )

      return {
        success: true,
        appealId: appealRef.id
      }

    } catch (error) {
      console.error('Error submitting appeal:', error)
      return {
        success: false,
        error: 'Failed to submit appeal'
      }
    }
  }

  /**
   * Process appeal decision
   */
  static async processAppealDecision(
    appealId: string,
    reviewerId: string,
    decision: Appeal['decision'],
    reasoning: string
  ): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      const batch = writeBatch(db)

      // Update appeal with decision
      const appealRef = doc(db, collections.appeals, appealId)
      batch.update(appealRef, {
        status: decision?.outcome || 'denied',
        reviewerId,
        decision: {
          ...decision,
          reasoning
        },
        reviewedAt: Timestamp.now(),
        decisionCommunicatedAt: Timestamp.now()
      })

      // If appeal approved, reverse or modify original action
      if (decision?.outcome === 'approved' || decision?.outcome === 'partially_approved') {
        await this.processAppealApproval(batch, appealId, decision)
      }

      await batch.commit()

      return { success: true }

    } catch (error) {
      console.error('Error processing appeal decision:', error)
      return {
        success: false,
        error: 'Failed to process appeal decision'
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async analyzeContent(
    content: string,
    metadata: Record<string, any>
  ): Promise<{
    riskScore: number
    confidence: number
    flaggedReasons: string[]
  }> {
    const flaggedReasons: string[] = []
    let riskScore = 0.0

    // Basic spam detection
    const repeatedChars = /(.)\1{5,}/.test(content)
    const allCaps = content === content.toUpperCase() && content.length > 50
    const excessiveLinks = (content.match(/https?:\/\//g) || []).length > 3

    if (repeatedChars) {
      riskScore += 0.3
      flaggedReasons.push('repeated_characters')
    }

    if (allCaps) {
      riskScore += 0.2
      flaggedReasons.push('excessive_caps')
    }

    if (excessiveLinks) {
      riskScore += 0.4
      flaggedReasons.push('excessive_links')
    }

    // Content length checks
    if (content.length < 5) {
      riskScore += 0.3
      flaggedReasons.push('too_short')
    }

    // Simple toxicity detection (basic keyword matching)
    const toxicKeywords = ['hate', 'spam', 'scam', 'fake']
    const hasToxicContent = toxicKeywords.some(keyword => 
      content.toLowerCase().includes(keyword)
    )

    if (hasToxicContent) {
      riskScore += 0.5
      flaggedReasons.push('potential_toxicity')
    }

    return {
      riskScore: Math.min(1.0, riskScore),
      confidence: 0.7, // Basic confidence for simple rules
      flaggedReasons
    }
  }

  private static estimateReviewTime(
    priority: ModerationQueue['priority'],
    queueType: ModerationQueue['queueType']
  ): number {
    const baseTimes = {
      low: 60,      // 1 hour
      medium: 30,   // 30 minutes
      high: 15,     // 15 minutes
      critical: 5   // 5 minutes
    }

    return baseTimes[priority]
  }

  private static async assessComplexity(contentId: string): Promise<ModerationQueue['complexity']> {
    // Simple complexity assessment
    // In real implementation, would analyze content complexity
    return 'moderate'
  }

  private static async generateAutoAnalysis(contentId: string): Promise<ModerationQueue['autoAnalysis']> {
    return {
      riskScore: 0.5,
      suggestedAction: 'review',
      confidence: 0.7,
      aiFlags: [],
      requiresSpecialist: false
    }
  }

  private static determineSeverity(
    actionType: ModerationAction['actionType'],
    reason: string
  ): ModerationAction['severity'] {
    const criticalActions = ['permanent_ban']
    const severeActions = ['temporary_suspension', 'point_reduction']
    const moderateActions = ['content_removal', 'feature_restriction']

    if (criticalActions.includes(actionType)) return 'critical'
    if (severeActions.includes(actionType)) return 'severe'
    if (moderateActions.includes(actionType)) return 'moderate'
    return 'minor'
  }

  private static calculateDuration(
    userId: string,
    actionType: ModerationAction['actionType'],
    severity: ModerationAction['severity']
  ): number | undefined {
    if (actionType === 'permanent_ban') return undefined

    // Get user's moderation history to determine duration
    // For now, use default durations
    return MODERATION_CONFIG.SUSPENSION_DURATIONS.first_offense
  }

  private static generateRestrictions(
    actionType: ModerationAction['actionType'],
    severity: ModerationAction['severity']
  ): ModerationAction['restrictions'] {
    const restrictions: ModerationAction['restrictions'] = {
      canPost: true,
      canComment: true,
      canMessage: true,
      canParticipateInChallenges: true,
      canEarnPoints: true,
      canAccessMarketplace: true
    }

    switch (actionType) {
      case 'temporary_suspension':
      case 'permanent_ban':
        Object.keys(restrictions).forEach(key => {
          restrictions[key as keyof typeof restrictions] = false
        })
        break

      case 'content_removal':
        restrictions.canPost = false
        break

      case 'feature_restriction':
        restrictions.canParticipateInChallenges = false
        restrictions.canAccessMarketplace = false
        break

      case 'point_reduction':
        restrictions.canEarnPoints = false
        break
    }

    return restrictions
  }

  private static isActionAppealable(
    actionType: ModerationAction['actionType'],
    severity: ModerationAction['severity']
  ): boolean {
    // Most actions are appealable except minor warnings
    return severity !== 'minor' || actionType !== 'warning'
  }

  private static calculateAppealDeadline(actionType: ModerationAction['actionType']): Timestamp {
    // 7 days to appeal for most actions
    const deadlineDays = actionType === 'permanent_ban' ? 30 : 7
    const deadline = new Date()
    deadline.setDate(deadline.getDate() + deadlineDays)
    return Timestamp.fromDate(deadline)
  }

  private static calculatePointDeduction(
    actionType: ModerationAction['actionType'],
    severity: ModerationAction['severity']
  ): number | undefined {
    if (actionType !== 'point_reduction') return undefined

    const deductions = {
      minor: 50,
      moderate: 100,
      severe: 250,
      critical: 500
    }

    return deductions[severity]
  }

  private static getRestrictedFeatures(restrictions: ModerationAction['restrictions']): string[] {
    const features: string[] = []
    
    if (!restrictions.canPost) features.push('posting')
    if (!restrictions.canComment) features.push('commenting')
    if (!restrictions.canMessage) features.push('messaging')
    if (!restrictions.canParticipateInChallenges) features.push('challenges')
    if (!restrictions.canEarnPoints) features.push('point_earning')
    if (!restrictions.canAccessMarketplace) features.push('marketplace')

    return features
  }

  private static calculateExpirationDate(durationHours: number): Timestamp {
    const expiration = new Date()
    expiration.setHours(expiration.getHours() + durationHours)
    return Timestamp.fromDate(expiration)
  }

  private static async updateUserModerationStatus(
    batch: any,
    userId: string,
    action: Omit<ModerationAction, 'id'>
  ): Promise<void> {
    const userRef = doc(db, collections.profiles, userId)
    
    const moderationUpdates: Record<string, any> = {
      'moderation.lastViolation': action.createdAt,
      'moderation.reportCount': 1, // Would increment existing count
      updatedAt: Timestamp.now()
    }

    if (action.actionType === 'temporary_suspension' || action.actionType === 'permanent_ban') {
      moderationUpdates['moderation.isSuspended'] = true
      moderationUpdates['moderation.suspensionEnds'] = action.expiresAt || null
      moderationUpdates['moderation.suspensionReason'] = action.reason
    }

    batch.update(userRef, moderationUpdates)
  }

  private static async updateContentModerationStatus(
    batch: any,
    contentId: string,
    action: Omit<ModerationAction, 'id'>
  ): Promise<void> {
    if (action.actionType === 'content_removal') {
      const contentRef = doc(db, collections.communityContent, contentId)
      batch.update(contentRef, {
        'moderation.status': 'removed',
        'moderation.reviewedBy': action.moderatorId,
        'moderation.reviewedAt': action.createdAt,
        updatedAt: Timestamp.now()
      })
    }
  }

  private static affectsContent(actionType: ModerationAction['actionType']): boolean {
    return actionType === 'content_removal'
  }

  private static async checkAppealEligibility(
    userId: string,
    actionId: string
  ): Promise<{ eligible: boolean; reason?: string }> {
    // Check if action exists and is appealable
    // Check if user hasn't exceeded appeal limits
    // For now, return eligible
    return { eligible: true }
  }

  private static determineAppealUrgency(
    appealType: Appeal['appealType'],
    reason: string
  ): Appeal['urgency'] {
    if (appealType === 'account_suspension') return 'high'
    if (appealType === 'point_deduction') return 'medium'
    return 'low'
  }

  private static async processAppealApproval(
    batch: any,
    appealId: string,
    decision: Appeal['decision']
  ): Promise<void> {
    // Implementation would reverse or modify the original moderation action
    // Update user restrictions, restore points, etc.
  }
}

export default ModerationEngine