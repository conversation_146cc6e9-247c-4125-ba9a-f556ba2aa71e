/**
 * Real-time Notification Engine - Phase 2 Implementation
 * 
 * Comprehensive real-time notification system for community events, achievements,
 * moderation actions, and user interactions with multi-channel delivery.
 * 
 * <AUTHOR> Team - Phase 2 Community Implementation
 * @version 2.0.0
 */

import { Timestamp, writeBatch, doc, collection, query, where, orderBy, limit, getDocs, onSnapshot } from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import { 
  CommunityNotification, 
  UserProfile,
  Achievement,
  ModerationAction,
  TierPromotion 
} from './types'

// ===== NOTIFICATION CONFIGURATION =====

export const NOTIFICATION_CONFIG = {
  // Channel priorities for different notification types
  CHANNEL_PRIORITIES: {
    achievement_unlock: ['in_app', 'push'],
    tier_promotion: ['in_app', 'push', 'email'],
    moderation_action: ['in_app', 'email'],
    content_interaction: ['in_app'],
    challenge_update: ['in_app', 'push'],
    system_announcement: ['in_app', 'push', 'email'],
    community_milestone: ['in_app'],
    appeal_update: ['in_app', 'email']
  },

  // Notification priorities
  PRIORITIES: {
    achievement_unlock: 'normal',
    tier_promotion: 'high',
    moderation_action: 'high',
    content_interaction: 'low',
    challenge_update: 'normal',
    system_announcement: 'urgent',
    community_milestone: 'normal',
    appeal_update: 'high'
  },

  // Batch limits for performance
  BATCH_LIMITS: {
    max_recipients: 100,
    max_notifications_per_batch: 50,
    rate_limit_per_user: 10 // per minute
  },

  // Expiration times (in hours)
  EXPIRATION_TIMES: {
    achievement_unlock: 168, // 1 week
    tier_promotion: 336, // 2 weeks
    moderation_action: 720, // 30 days
    content_interaction: 48, // 2 days
    challenge_update: 72, // 3 days
    system_announcement: 168, // 1 week
    community_milestone: 168, // 1 week
    appeal_update: 336 // 2 weeks
  }
} as const

// ===== REAL-TIME NOTIFICATION ENGINE =====

export class NotificationEngine {
  private static listeners: Map<string, () => void> = new Map()

  /**
   * Send notification to single user
   */
  static async sendNotification(
    userId: string,
    type: CommunityNotification['type'],
    title: string,
    message: string,
    data: CommunityNotification['data'] = {},
    options: {
      icon?: string
      imageUrl?: string
      actionUrl?: string
      customChannels?: CommunityNotification['channels']
      customPriority?: CommunityNotification['priority']
    } = {}
  ): Promise<{
    success: boolean
    notificationId?: string
    error?: string
  }> {
    try {
      // Get user preferences
      const userPreferences = await this.getUserNotificationPreferences(userId)
      if (!userPreferences.enabled) {
        return { success: true } // User has notifications disabled
      }

      // Determine channels and priority
      const channels = options.customChannels || this.getDefaultChannels(type, userPreferences)
      const priority = options.customPriority || NOTIFICATION_CONFIG.PRIORITIES[type]

      // Create notification document
      const notification: Omit<CommunityNotification, 'id'> = {
        userId,
        type,
        title,
        message,
        icon: options.icon,
        imageUrl: options.imageUrl,
        actionUrl: options.actionUrl,
        data,
        priority,
        channels,
        isRead: false,
        isDisplayed: false,
        expiresAt: this.calculateExpirationDate(type),
        createdAt: Timestamp.now()
      }

      // Save to database
      const notificationRef = doc(collection(db, collections.communityNotifications))
      await notificationRef.set(notification)

      // Trigger real-time delivery
      await this.deliverNotification(notificationRef.id, notification, channels)

      return {
        success: true,
        notificationId: notificationRef.id
      }

    } catch (error) {
      console.error('Error sending notification:', error)
      return {
        success: false,
        error: 'Failed to send notification'
      }
    }
  }

  /**
   * Send bulk notifications to multiple users
   */
  static async sendBulkNotifications(
    userIds: string[],
    type: CommunityNotification['type'],
    title: string,
    message: string,
    data: CommunityNotification['data'] = {},
    options: {
      icon?: string
      imageUrl?: string
      actionUrl?: string
      batchSize?: number
    } = {}
  ): Promise<{
    success: boolean
    sentCount: number
    failedCount: number
    errors: string[]
  }> {
    const batchSize = options.batchSize || NOTIFICATION_CONFIG.BATCH_LIMITS.max_recipients
    const errors: string[] = []
    let sentCount = 0
    let failedCount = 0

    // Process in batches
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize)
      
      try {
        const batchResults = await Promise.allSettled(
          batch.map(userId => this.sendNotification(userId, type, title, message, data, options))
        )

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled' && result.value.success) {
            sentCount++
          } else {
            failedCount++
            const error = result.status === 'rejected' 
              ? result.reason 
              : result.value.error || 'Unknown error'
            errors.push(`User ${batch[index]}: ${error}`)
          }
        })

      } catch (error) {
        batch.forEach(() => failedCount++)
        errors.push(`Batch ${i}-${i + batchSize}: ${error}`)
      }
    }

    return {
      success: errors.length === 0,
      sentCount,
      failedCount,
      errors
    }
  }

  /**
   * Send achievement unlock notification
   */
  static async notifyAchievementUnlock(
    userId: string,
    achievement: Achievement,
    pointsAwarded: number
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.sendNotification(
      userId,
      'achievement_unlock',
      `🎉 Achievement Unlocked: ${achievement.title}`,
      `You've earned "${achievement.title}" and ${pointsAwarded} points!`,
      {
        achievementId: achievement.id,
        pointsAwarded
      },
      {
        icon: achievement.icon,
        actionUrl: `/achievements/${achievement.id}`
      }
    )

    return { success: result.success, error: result.error }
  }

  /**
   * Send tier promotion notification
   */
  static async notifyTierPromotion(
    userId: string,
    promotion: TierPromotion
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.sendNotification(
      userId,
      'tier_promotion',
      `🎊 Tier Promotion: Welcome to ${promotion.toTier}!`,
      `Congratulations! You've been promoted from ${promotion.fromTier} to ${promotion.toTier}.`,
      {
        tierChange: {
          from: promotion.fromTier,
          to: promotion.toTier
        }
      },
      {
        icon: '👑',
        actionUrl: '/profile/tier'
      }
    )

    return { success: result.success, error: result.error }
  }

  /**
   * Send moderation action notification
   */
  static async notifyModerationAction(
    userId: string,
    action: ModerationAction
  ): Promise<{ success: boolean; error?: string }> {
    const actionMessages = {
      warning: 'You have received a warning.',
      content_removal: 'Your content has been removed.',
      temporary_suspension: 'Your account has been temporarily suspended.',
      permanent_ban: 'Your account has been permanently banned.',
      point_reduction: 'Points have been deducted from your account.',
      tier_demotion: 'Your tier has been reduced.',
      feature_restriction: 'Some features have been restricted.',
      shadowban: 'Your visibility has been limited.'
    }

    const result = await this.sendNotification(
      userId,
      'moderation_action',
      `⚠️ Moderation Action: ${action.actionType.replace('_', ' ')}`,
      `${actionMessages[action.actionType]} Reason: ${action.reason}`,
      {
        moderationActionId: action.id
      },
      {
        icon: '⚠️',
        actionUrl: action.isAppealable ? `/appeals/create/${action.id}` : '/community/rules'
      }
    )

    return { success: result.success, error: result.error }
  }

  /**
   * Send content interaction notification
   */
  static async notifyContentInteraction(
    userId: string,
    interactionType: 'like' | 'comment' | 'share',
    contentId: string,
    actorUserId: string,
    actorName: string
  ): Promise<{ success: boolean; error?: string }> {
    const interactionMessages = {
      like: `${actorName} liked your post`,
      comment: `${actorName} commented on your post`,
      share: `${actorName} shared your post`
    }

    const interactionIcons = {
      like: '❤️',
      comment: '💬',
      share: '🔄'
    }

    const result = await this.sendNotification(
      userId,
      'content_interaction',
      interactionMessages[interactionType],
      `Check out the activity on your content.`,
      {
        contentId,
        relatedUsers: [actorUserId]
      },
      {
        icon: interactionIcons[interactionType],
        actionUrl: `/content/${contentId}`
      }
    )

    return { success: result.success, error: result.error }
  }

  /**
   * Subscribe to real-time notifications for a user
   */
  static subscribeToUserNotifications(
    userId: string,
    callback: (notifications: CommunityNotification[]) => void
  ): () => void {
    const notificationsQuery = query(
      collection(db, collections.communityNotifications),
      where('userId', '==', userId),
      where('isRead', '==', false),
      orderBy('createdAt', 'desc'),
      limit(50)
    )

    const unsubscribe = onSnapshot(notificationsQuery, (snapshot) => {
      const notifications: CommunityNotification[] = []
      snapshot.forEach((doc) => {
        notifications.push({ id: doc.id, ...doc.data() } as CommunityNotification)
      })
      callback(notifications)
    })

    // Store the unsubscribe function
    const listenerId = `notifications_${userId}`
    this.listeners.set(listenerId, unsubscribe)

    return () => {
      unsubscribe()
      this.listeners.delete(listenerId)
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(
    notificationId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const notificationRef = doc(db, collections.communityNotifications, notificationId)
      await notificationRef.update({
        isRead: true,
        readAt: Timestamp.now()
      })

      return { success: true }

    } catch (error) {
      console.error('Error marking notification as read:', error)
      return {
        success: false,
        error: 'Failed to mark notification as read'
      }
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const notificationsQuery = query(
        collection(db, collections.communityNotifications),
        where('userId', '==', userId),
        where('isRead', '==', false)
      )

      const snapshot = await getDocs(notificationsQuery)
      const batch = writeBatch(db)

      snapshot.docs.forEach((doc) => {
        batch.update(doc.ref, {
          isRead: true,
          readAt: Timestamp.now()
        })
      })

      await batch.commit()

      return { success: true }

    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      return {
        success: false,
        error: 'Failed to mark notifications as read'
      }
    }
  }

  /**
   * Get notification history for a user
   */
  static async getNotificationHistory(
    userId: string,
    limit: number = 50,
    lastNotificationId?: string
  ): Promise<{
    notifications: CommunityNotification[]
    hasMore: boolean
    lastNotificationId?: string
  }> {
    try {
      let notificationsQuery = query(
        collection(db, collections.communityNotifications),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(limit + 1) // Get one extra to check if there are more
      )

      // Add pagination if lastNotificationId provided
      if (lastNotificationId) {
        // Implementation would add startAfter clause
      }

      const snapshot = await getDocs(notificationsQuery)
      const notifications: CommunityNotification[] = []

      snapshot.docs.slice(0, limit).forEach((doc) => {
        notifications.push({ id: doc.id, ...doc.data() } as CommunityNotification)
      })

      return {
        notifications,
        hasMore: snapshot.docs.length > limit,
        lastNotificationId: notifications.length > 0 ? notifications[notifications.length - 1].id : undefined
      }

    } catch (error) {
      console.error('Error getting notification history:', error)
      return {
        notifications: [],
        hasMore: false
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async getUserNotificationPreferences(userId: string): Promise<{
    enabled: boolean
    channels: CommunityNotification['channels']
    types: CommunityNotification['type'][]
  }> {
    // Implementation would fetch user preferences from database
    // For now, return default preferences
    return {
      enabled: true,
      channels: ['in_app', 'push'],
      types: ['achievement_unlock', 'tier_promotion', 'moderation_action', 'content_interaction', 'challenge_update', 'system_announcement', 'community_milestone', 'appeal_update']
    }
  }

  private static getDefaultChannels(
    type: CommunityNotification['type'],
    userPreferences: { channels: CommunityNotification['channels'] }
  ): CommunityNotification['channels'] {
    const defaultChannels = NOTIFICATION_CONFIG.CHANNEL_PRIORITIES[type] || ['in_app']
    return defaultChannels.filter(channel => userPreferences.channels.includes(channel))
  }

  private static calculateExpirationDate(type: CommunityNotification['type']): Timestamp {
    const hours = NOTIFICATION_CONFIG.EXPIRATION_TIMES[type] || 168
    const expiration = new Date()
    expiration.setHours(expiration.getHours() + hours)
    return Timestamp.fromDate(expiration)
  }

  private static async deliverNotification(
    notificationId: string,
    notification: Omit<CommunityNotification, 'id'>,
    channels: CommunityNotification['channels']
  ): Promise<void> {
    // Implementation would handle actual delivery via different channels
    console.log(`Delivering notification ${notificationId} via channels:`, channels)

    // In-app notifications are handled by the real-time subscription
    // Push notifications would be sent via FCM
    // Email notifications would be sent via email service
    // SMS notifications would be sent via SMS service
  }

  /**
   * Clean up expired notifications
   */
  static async cleanupExpiredNotifications(): Promise<{
    success: boolean
    deletedCount: number
    error?: string
  }> {
    try {
      const expiredQuery = query(
        collection(db, collections.communityNotifications),
        where('expiresAt', '<', Timestamp.now())
      )

      const snapshot = await getDocs(expiredQuery)
      const batch = writeBatch(db)

      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref)
      })

      await batch.commit()

      return {
        success: true,
        deletedCount: snapshot.docs.length
      }

    } catch (error) {
      console.error('Error cleaning up expired notifications:', error)
      return {
        success: false,
        deletedCount: 0,
        error: 'Failed to cleanup expired notifications'
      }
    }
  }

  /**
   * Clean up all listeners
   */
  static cleanup(): void {
    this.listeners.forEach((unsubscribe) => {
      unsubscribe()
    })
    this.listeners.clear()
  }
}

// ===== NOTIFICATION TEMPLATES =====

export class NotificationTemplates {
  /**
   * Generate system announcement notification
   */
  static systemAnnouncement(
    title: string,
    message: string,
    actionUrl?: string
  ): {
    type: CommunityNotification['type']
    title: string
    message: string
    data: CommunityNotification['data']
    options: {
      icon: string
      actionUrl?: string
      customPriority: CommunityNotification['priority']
    }
  } {
    return {
      type: 'system_announcement',
      title: `📢 ${title}`,
      message,
      data: {},
      options: {
        icon: '📢',
        actionUrl,
        customPriority: 'urgent'
      }
    }
  }

  /**
   * Generate community milestone notification
   */
  static communityMilestone(
    milestone: string,
    description: string
  ): {
    type: CommunityNotification['type']
    title: string
    message: string
    data: CommunityNotification['data']
    options: {
      icon: string
      customPriority: CommunityNotification['priority']
    }
  } {
    return {
      type: 'community_milestone',
      title: `🎊 Community Milestone: ${milestone}`,
      message: description,
      data: {},
      options: {
        icon: '🎊',
        customPriority: 'normal'
      }
    }
  }
}

export default NotificationEngine