/**
 * Point Calculation Engine - Phase 1 Implementation
 * 
 * Core engine for calculating points with progressive scaling, quality assessment,
 * and comprehensive anti-gaming measures as defined in the community rules.
 * 
 * <AUTHOR> Team - Phase 1 Community Implementation
 * @version 1.0.0
 */

import { Timestamp, writeBatch, doc } from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import { UserProfile, PointTransaction, DailyActivityLimit, ActivityTrigger, QualityAssessment } from './types'
import { POINT_CONFIG as UNIFIED_CONFIG, COMMUNITY_POINTS, TIER_MULTIPLIERS, STREAK_BONUSES, DAILY_LIMITS, RISK_THRESHOLDS } from '../pointConfig'

// ===== POINT CALCULATION CONFIGURATION =====
// Using unified point configuration for consistency

export const POINT_CONFIG = {
  // Base point values for different activities (from unified config)
  BASE_POINTS: {
    // Content creation
    content_creation: COMMUNITY_POINTS.CONTENT_CREATION,
    high_quality_post: COMMUNITY_POINTS.HIGH_QUALITY_POST,
    tutorial_creation: COMMUNITY_POINTS.TUTORIAL_CREATION,
    photo_upload: COMMUNITY_POINTS.PHOTO_UPLOAD,

    // Community interaction
    comment_posted: COMMUNITY_POINTS.COMMENT_POSTED,
    helpful_comment: COMMUNITY_POINTS.HELPFUL_COMMENT,
    like_given: COMMUNITY_POINTS.LIKE_GIVEN,
    share_content: COMMUNITY_POINTS.SHARE_CONTENT,

    // Challenge participation
    challenge_join: COMMUNITY_POINTS.CHALLENGE_JOIN,
    challenge_submit: COMMUNITY_POINTS.CHALLENGE_SUBMIT,
    challenge_win: COMMUNITY_POINTS.CHALLENGE_WIN,

    // Social engagement
    social_share: COMMUNITY_POINTS.SOCIAL_SHARE,
    referral_signup: COMMUNITY_POINTS.REFERRAL_SIGNUP,
    referral_purchase: 500, // Keep existing value for referral purchases

    // Daily activities
    daily_login: COMMUNITY_POINTS.DAILY_LOGIN,
    profile_complete: COMMUNITY_POINTS.PROFILE_COMPLETE,

    // Marketplace (aligned with e-commerce system)
    // Note: purchase_made points are calculated separately in pointsSystem.ts (5 points per $1)
    review_written: COMMUNITY_POINTS.REVIEW_WRITTEN,

    // Moderation rewards
    helpful_report: COMMUNITY_POINTS.HELPFUL_REPORT,
    quality_contribution: COMMUNITY_POINTS.QUALITY_CONTRIBUTION
  },

  // Tier-based multipliers (progressive scaling)
  TIER_MULTIPLIERS: {
    bronze: 1.0,    // Standard rate
    silver: 0.85,   // 15% reduction
    gold: 0.70,     // 30% reduction  
    platinum: 0.55  // 45% reduction
  },

  // Quality score multipliers
  QUALITY_MULTIPLIERS: {
    excellent: 1.5,   // 90-100% quality
    good: 1.2,        // 70-89% quality
    average: 1.0,     // 50-69% quality
    poor: 0.7,        // 30-49% quality
    very_poor: 0.4    // 0-29% quality
  },

  // Tier-based multipliers for progressive scaling (from unified config)
  TIER_MULTIPLIERS,

  // Daily limits per activity type (from unified config)
  DAILY_LIMITS,

  // Streak bonuses (from unified config)
  STREAK_BONUSES,

  // Risk assessment thresholds (from unified config)
  RISK_THRESHOLDS
} as const

// ===== POINT CALCULATION ENGINE =====

export class PointCalculationEngine {
  /**
   * Calculate points for a user activity with comprehensive validation
   */
  static async calculatePoints(
    userId: string,
    activityType: string,
    metadata: Record<string, any> = {},
    qualityAssessment?: QualityAssessment
  ): Promise<{
    pointsAwarded: number
    calculation: PointTransaction['calculation']
    quality: PointTransaction['quality']
    validation: PointTransaction['validation']
    blocked: boolean
    reason?: string
  }> {
    try {
      // 1. Get user profile and current state
      const userProfile = await this.getUserProfile(userId)
      if (!userProfile) {
        throw new Error('User profile not found')
      }

      // 2. Check daily limits
      const dailyCheck = await this.checkDailyLimits(userId, activityType)
      if (dailyCheck.blocked) {
        return {
          pointsAwarded: 0,
          calculation: this.getEmptyCalculation(),
          quality: this.getEmptyQuality(),
          validation: { isValid: false, suspiciousActivity: true, riskScore: 1.0 },
          blocked: true,
          reason: dailyCheck.reason
        }
      }

      // 3. Get base points for activity
      const basePoints = POINT_CONFIG.BASE_POINTS[activityType as keyof typeof POINT_CONFIG.BASE_POINTS] || 0
      if (basePoints === 0) {
        return {
          pointsAwarded: 0,
          calculation: this.getEmptyCalculation(),
          quality: this.getEmptyQuality(),
          validation: { isValid: false, suspiciousActivity: false, riskScore: 0.0 },
          blocked: true,
          reason: 'Unknown activity type'
        }
      }

      // 4. Calculate quality multiplier
      const qualityMultiplier = this.calculateQualityMultiplier(qualityAssessment)
      
      // 5. Apply tier-based progressive scaling
      const tierMultiplier = POINT_CONFIG.TIER_MULTIPLIERS[userProfile.gamification.currentTier]
      
      // 6. Calculate streak bonus
      const streakBonus = this.calculateStreakBonus(userProfile.gamification.streakDays)
      
      // 7. Apply progressive scaling based on recent earnings
      const progressiveScaling = await this.calculateProgressiveScaling(userId, basePoints)
      
      // 8. Calculate final points
      const finalPoints = Math.round(
        basePoints * 
        qualityMultiplier * 
        tierMultiplier * 
        streakBonus * 
        progressiveScaling
      )

      // 9. Perform risk assessment
      const riskAssessment = await this.assessRisk(userId, activityType, finalPoints, metadata)
      
      // 10. Create calculation details
      const calculation: PointTransaction['calculation'] = {
        basePoints,
        qualityMultiplier,
        tierMultiplier,
        progressiveScaling,
        streakBonus,
        finalPoints
      }

      const quality: PointTransaction['quality'] = {
        score: qualityAssessment?.score || 0.5,
        isManuallyReviewed: false,
        flagged: riskAssessment.riskScore > POINT_CONFIG.RISK_THRESHOLDS.suspicious_score
      }

      const validation: PointTransaction['validation'] = {
        isValid: !riskAssessment.blocked,
        suspiciousActivity: riskAssessment.riskScore > POINT_CONFIG.RISK_THRESHOLDS.suspicious_score,
        riskScore: riskAssessment.riskScore
      }

      return {
        pointsAwarded: riskAssessment.blocked ? 0 : finalPoints,
        calculation,
        quality,
        validation,
        blocked: riskAssessment.blocked,
        reason: riskAssessment.reason
      }

    } catch (error) {
      console.error('Error calculating points:', error)
      return {
        pointsAwarded: 0,
        calculation: this.getEmptyCalculation(),
        quality: this.getEmptyQuality(),
        validation: { isValid: false, suspiciousActivity: true, riskScore: 1.0 },
        blocked: true,
        reason: 'Calculation error'
      }
    }
  }

  /**
   * Integration with e-commerce point system
   * Delegates purchase points to pointsSystem.ts for consistency
   */
  static async awardPurchasePoints(
    userId: string,
    orderAmount: number,
    orderId: string
  ): Promise<{
    success: boolean
    pointsAwarded: number
    transactionId?: string
    error?: string
  }> {
    try {
      // Import pointsSystem dynamically to avoid circular dependencies
      const { PointsSystem } = await import('../pointsSystem')

      const pointsAwarded = await PointsSystem.awardPurchasePoints(userId, orderAmount, orderId)

      return {
        success: true,
        pointsAwarded,
        transactionId: `purchase_${orderId}`
      }
    } catch (error) {
      console.error('Failed to award purchase points:', error)
      return {
        success: false,
        pointsAwarded: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Award points to user with full transaction logging
   */
  static async awardPoints(
    userId: string,
    activityType: string,
    description: string,
    metadata: Record<string, any> = {},
    qualityAssessment?: QualityAssessment,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{
    success: boolean
    pointsAwarded: number
    transactionId?: string
    error?: string
  }> {
    try {
      // Calculate points
      const calculation = await this.calculatePoints(userId, activityType, metadata, qualityAssessment)
      
      if (calculation.blocked) {
        return {
          success: false,
          pointsAwarded: 0,
          error: calculation.reason || 'Points blocked'
        }
      }

      // Get user's current balance
      const userProfile = await this.getUserProfile(userId)
      if (!userProfile) {
        return {
          success: false,
          pointsAwarded: 0,
          error: 'User profile not found'
        }
      }

      const balanceBefore = userProfile.gamification.totalPoints
      const balanceAfter = balanceBefore + calculation.pointsAwarded

      // Create point transaction
      const transaction: Omit<PointTransaction, 'id'> = {
        userId,
        activityType,
        pointsEarned: calculation.pointsAwarded,
        balanceBefore,
        balanceAfter,
        calculation: calculation.calculation,
        quality: calculation.quality,
        source: this.mapActivityToSource(activityType),
        description,
        metadata,
        ipAddress,
        userAgent,
        validation: calculation.validation,
        status: 'approved',
        createdAt: Timestamp.now(),
        processedAt: Timestamp.now()
      }

      // Use batch write for atomic transaction
      const batch = writeBatch(db)
      
      // Add transaction record
      const transactionRef = doc(db, collections.pointTransactions)
      batch.set(transactionRef, transaction)

      // Update user profile
      const userRef = doc(db, collections.profiles, userId)
      batch.update(userRef, {
        'gamification.totalPoints': balanceAfter,
        'gamification.pointsEarnedToday': userProfile.gamification.pointsEarnedToday + calculation.pointsAwarded,
        'gamification.lastActivity': Timestamp.now(),
        updatedAt: Timestamp.now()
      })

      // Update daily activity limits
      await this.updateDailyLimits(userId, activityType, calculation.pointsAwarded, calculation.quality.score)

      // Commit batch
      await batch.commit()

      return {
        success: true,
        pointsAwarded: calculation.pointsAwarded,
        transactionId: transactionRef.id
      }

    } catch (error) {
      console.error('Error awarding points:', error)
      return {
        success: false,
        pointsAwarded: 0,
        error: 'Failed to award points'
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async getUserProfile(userId: string): Promise<UserProfile | null> {
    // Implementation would fetch from Firestore
    // For now, return null to indicate needs implementation
    return null
  }

  private static async checkDailyLimits(userId: string, activityType: string): Promise<{
    blocked: boolean
    reason?: string
  }> {
    const limits = POINT_CONFIG.DAILY_LIMITS[activityType as keyof typeof POINT_CONFIG.DAILY_LIMITS]
    if (!limits) {
      return { blocked: false }
    }

    // Implementation would check against daily activity limits collection
    // For now, return not blocked
    return { blocked: false }
  }

  private static calculateQualityMultiplier(assessment?: QualityAssessment): number {
    if (!assessment) return 1.0

    const score = assessment.score
    if (score >= 0.9) return POINT_CONFIG.QUALITY_MULTIPLIERS.excellent
    if (score >= 0.7) return POINT_CONFIG.QUALITY_MULTIPLIERS.good
    if (score >= 0.5) return POINT_CONFIG.QUALITY_MULTIPLIERS.average
    if (score >= 0.3) return POINT_CONFIG.QUALITY_MULTIPLIERS.poor
    return POINT_CONFIG.QUALITY_MULTIPLIERS.very_poor
  }

  private static calculateStreakBonus(streakDays: number): number {
    const bonuses = Object.entries(POINT_CONFIG.STREAK_BONUSES)
      .sort(([a], [b]) => parseInt(b) - parseInt(a))
    
    for (const [days, bonus] of bonuses) {
      if (streakDays >= parseInt(days)) {
        return bonus
      }
    }
    
    return 1.0
  }

  private static async calculateProgressiveScaling(userId: string, basePoints: number): Promise<number> {
    // Progressive scaling based on recent earnings
    // Higher earners get reduced rates to prevent inflation
    
    // Implementation would check recent point earnings
    // For now, return standard scaling
    return 1.0
  }

  private static async assessRisk(
    userId: string,
    activityType: string,
    pointsAwarded: number,
    metadata: Record<string, any>
  ): Promise<{
    riskScore: number
    blocked: boolean
    reason?: string
  }> {
    let riskScore = 0.0

    // Check for suspicious patterns
    // Implementation would include various risk checks
    
    return {
      riskScore,
      blocked: riskScore > POINT_CONFIG.RISK_THRESHOLDS.high_risk_score,
      reason: riskScore > POINT_CONFIG.RISK_THRESHOLDS.high_risk_score ? 'High risk detected' : undefined
    }
  }

  private static async updateDailyLimits(
    userId: string,
    activityType: string,
    pointsAwarded: number,
    qualityScore: number
  ): Promise<void> {
    // Implementation would update daily activity limits collection
    // Track usage against daily limits
  }

  private static mapActivityToSource(activityType: string): PointTransaction['source'] {
    const mapping: Record<string, PointTransaction['source']> = {
      content_creation: 'content_creation',
      high_quality_post: 'content_creation',
      tutorial_creation: 'content_creation',
      photo_upload: 'content_creation',
      comment_posted: 'community_interaction',
      helpful_comment: 'community_interaction',
      like_given: 'community_interaction',
      share_content: 'community_interaction',
      challenge_join: 'challenge_participation',
      challenge_submit: 'challenge_participation',
      challenge_win: 'challenge_participation',
      social_share: 'social_sharing',
      referral_signup: 'referral',
      referral_purchase: 'referral',
      daily_login: 'daily_activity',
      profile_complete: 'daily_activity',
      purchase_made: 'marketplace',
      review_written: 'marketplace',
      helpful_report: 'moderation_reward'
    }

    return mapping[activityType] || 'admin_adjustment'
  }

  private static getEmptyCalculation(): PointTransaction['calculation'] {
    return {
      basePoints: 0,
      qualityMultiplier: 1.0,
      tierMultiplier: 1.0,
      progressiveScaling: 1.0,
      streakBonus: 1.0,
      finalPoints: 0
    }
  }

  private static getEmptyQuality(): PointTransaction['quality'] {
    return {
      score: 0,
      isManuallyReviewed: false,
      flagged: false
    }
  }
}

// ===== QUALITY ASSESSMENT UTILITIES =====

export class QualityAssessmentEngine {
  /**
   * Assess content quality for point calculation
   */
  static async assessContentQuality(
    content: string,
    metadata: Record<string, any> = {}
  ): Promise<QualityAssessment> {
    const factors = {
      contentLength: this.assessContentLength(content),
      readability: this.assessReadability(content),
      engagement: metadata.engagement || 0.5,
      originality: metadata.originality || 0.5,
      helpfulness: metadata.helpfulness || 0.5,
      accuracy: metadata.accuracy || 0.5
    }

    // Calculate weighted score
    const score = (
      factors.contentLength * 0.15 +
      factors.readability * 0.20 +
      factors.engagement * 0.20 +
      factors.originality * 0.15 +
      factors.helpfulness * 0.15 +
      factors.accuracy * 0.15
    )

    return {
      score: Math.max(0, Math.min(1, score)),
      factors
    }
  }

  private static assessContentLength(content: string): number {
    const length = content.length
    if (length < 10) return 0.1
    if (length < 50) return 0.3
    if (length < 100) return 0.5
    if (length < 300) return 0.7
    if (length < 500) return 0.9
    return 1.0
  }

  private static assessReadability(content: string): number {
    // Simple readability assessment
    const sentences = content.split(/[.!?]+/).length
    const words = content.split(/\s+/).length
    const avgWordsPerSentence = words / sentences

    if (avgWordsPerSentence < 10) return 1.0
    if (avgWordsPerSentence < 15) return 0.8
    if (avgWordsPerSentence < 20) return 0.6
    if (avgWordsPerSentence < 25) return 0.4
    return 0.2
  }
}

export default PointCalculationEngine