/**
 * Tier Progression Manager - Phase 2 Implementation
 * 
 * Automated tier progression system with comprehensive validation, 
 * benefits management, and promotion/demotion logic. Handles tier
 * requirements, benefits activation, and progression analytics.
 * 
 * <AUTHOR> Team - Phase 2 Community Implementation
 * @version 2.0.0
 */

import { Timestamp, writeBatch, doc, collection, query, where, orderBy, limit, getDocs, runTransaction } from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firestore'
import { 
  UserProfile, 
  UserTier, 
  TierPromotion,
  PointTransaction 
} from './types'
import { NotificationEngine } from './notificationEngine'
import { ActivityLogger } from './activityFeed'

// ===== TIER MANAGEMENT CONFIGURATION =====

export const TIER_CONFIG = {
  // Tier definitions with updated thresholds from progression analysis
  TIER_DEFINITIONS: {
    bronze: {
      name: 'Bronze',
      slug: 'bronze',
      level: 1,
      pointRequirements: {
        minimum: 0,
        maximum: 1999
      },
      timeRequirements: {
        minimumDays: 0,
        activityRequirements: {
          postsCreated: 0,
          qualityScore: 0.3,
          communityEngagement: 0
        }
      },
      benefits: {
        pointMultiplier: 1.0,
        maxDailyPoints: 200,
        specialFeatures: ['basic_access'],
        prioritySupport: false,
        exclusiveContent: false,
        betaAccess: false,
        customization: {
          profileThemes: ['default'],
          badgeSlots: 1,
          customTitles: false
        }
      },
      display: {
        color: '#CD7F32',
        backgroundColor: '#FEF3E2',
        borderColor: '#CD7F32',
        icon: '🥉',
        gradientStops: ['#CD7F32', '#DEB887']
      },
      maintenanceRequirements: {
        monthlyActivity: 0,
        qualityThreshold: 0.3,
        inactivityDemotionDays: 365 // Very lenient for bronze
      }
    },

    silver: {
      name: 'Silver',
      slug: 'silver',
      level: 2,
      pointRequirements: {
        minimum: 2000,
        maximum: 9999
      },
      timeRequirements: {
        minimumDays: 28, // 4 weeks minimum
        activityRequirements: {
          postsCreated: 5,
          qualityScore: 0.5,
          communityEngagement: 10
        }
      },
      benefits: {
        pointMultiplier: 0.85, // Progressive scaling
        maxDailyPoints: 300,
        specialFeatures: ['enhanced_profiles', 'priority_queue'],
        prioritySupport: false,
        exclusiveContent: false,
        betaAccess: false,
        customization: {
          profileThemes: ['default', 'silver'],
          badgeSlots: 2,
          customTitles: false
        }
      },
      display: {
        color: '#C0C0C0',
        backgroundColor: '#F8FAFC',
        borderColor: '#C0C0C0',
        icon: '🥈',
        gradientStops: ['#C0C0C0', '#E5E7EB']
      },
      maintenanceRequirements: {
        monthlyActivity: 5,
        qualityThreshold: 0.4,
        inactivityDemotionDays: 180
      }
    },

    gold: {
      name: 'Gold',
      slug: 'gold',
      level: 3,
      pointRequirements: {
        minimum: 10000,
        maximum: 49999
      },
      timeRequirements: {
        minimumDays: 84, // 12 weeks minimum
        activityRequirements: {
          postsCreated: 15,
          qualityScore: 0.6,
          communityEngagement: 25
        }
      },
      benefits: {
        pointMultiplier: 0.70, // Progressive scaling
        maxDailyPoints: 400,
        specialFeatures: ['advanced_analytics', 'content_scheduling', 'early_access'],
        prioritySupport: true,
        exclusiveContent: true,
        betaAccess: true,
        customization: {
          profileThemes: ['default', 'silver', 'gold', 'premium'],
          badgeSlots: 3,
          customTitles: true
        }
      },
      display: {
        color: '#FFD700',
        backgroundColor: '#FFFBEB',
        borderColor: '#FFD700',
        icon: '🥇',
        gradientStops: ['#FFD700', '#FCD34D']
      },
      maintenanceRequirements: {
        monthlyActivity: 10,
        qualityThreshold: 0.5,
        inactivityDemotionDays: 120
      }
    },

    platinum: {
      name: 'Platinum',
      slug: 'platinum',
      level: 4,
      pointRequirements: {
        minimum: 50000,
        maximum: undefined
      },
      timeRequirements: {
        minimumDays: 168, // 24 weeks minimum
        activityRequirements: {
          postsCreated: 50,
          qualityScore: 0.7,
          communityEngagement: 50
        }
      },
      benefits: {
        pointMultiplier: 0.55, // Progressive scaling
        maxDailyPoints: 500,
        specialFeatures: ['vip_community', 'moderation_tools', 'influence_system', 'exclusive_events'],
        prioritySupport: true,
        exclusiveContent: true,
        betaAccess: true,
        customization: {
          profileThemes: ['all_themes'],
          badgeSlots: 5,
          customTitles: true
        }
      },
      display: {
        color: '#E5E4E2',
        backgroundColor: '#F9FAFB',
        borderColor: '#E5E4E2',
        icon: '💎',
        gradientStops: ['#E5E4E2', '#F3F4F6']
      },
      maintenanceRequirements: {
        monthlyActivity: 15,
        qualityThreshold: 0.6,
        inactivityDemotionDays: 90
      }
    }
  },

  // Promotion settings
  PROMOTION_SETTINGS: {
    evaluation_frequency_hours: 6, // Check every 6 hours
    grace_period_days: 7, // Grace period for new users
    demotion_warning_days: 14, // Warning before demotion
    promotion_cooldown_days: 1, // Cooldown between promotions
    manual_override_roles: ['admin', 'moderator']
  },

  // Analytics settings
  ANALYTICS_SETTINGS: {
    track_promotion_velocity: true,
    track_retention_by_tier: true,
    track_engagement_by_tier: true,
    monthly_tier_reports: true
  }
} as const

// ===== TIER MANAGER ENGINE =====

export class TierManager {
  /**
   * Evaluate user for tier promotion/demotion
   */
  static async evaluateUserTier(
    userId: string,
    force: boolean = false
  ): Promise<{
    currentTier: keyof typeof TIER_CONFIG.TIER_DEFINITIONS
    eligibleTier: keyof typeof TIER_CONFIG.TIER_DEFINITIONS
    action: 'maintain' | 'promote' | 'demote'
    reasons: string[]
    requiresManualReview: boolean
  }> {
    try {
      // Get user profile
      const userProfile = await this.getUserProfile(userId)
      if (!userProfile) {
        throw new Error('User profile not found')
      }

      const currentTier = userProfile.gamification.currentTier
      const currentTierConfig = TIER_CONFIG.TIER_DEFINITIONS[currentTier]

      // Check promotion eligibility
      const promotionCheck = await this.checkPromotionEligibility(userProfile)
      
      // Check demotion risk
      const demotionCheck = await this.checkDemotionRisk(userProfile)

      // Determine action
      let action: 'maintain' | 'promote' | 'demote' = 'maintain'
      let eligibleTier = currentTier
      let reasons: string[] = []
      let requiresManualReview = false

      if (promotionCheck.eligible && !demotionCheck.atRisk) {
        action = 'promote'
        eligibleTier = promotionCheck.targetTier
        reasons = promotionCheck.reasons
        requiresManualReview = promotionCheck.requiresManualReview
      } else if (demotionCheck.atRisk && !promotionCheck.eligible) {
        action = 'demote'
        eligibleTier = demotionCheck.targetTier as keyof typeof TIER_CONFIG.TIER_DEFINITIONS
        reasons = demotionCheck.reasons
        requiresManualReview = demotionCheck.requiresManualReview
      } else {
        reasons.push('No tier change required')
      }

      return {
        currentTier,
        eligibleTier,
        action,
        reasons,
        requiresManualReview
      }

    } catch (error) {
      console.error('Error evaluating user tier:', error)
      return {
        currentTier: 'bronze',
        eligibleTier: 'bronze',
        action: 'maintain',
        reasons: ['Evaluation error'],
        requiresManualReview: true
      }
    }
  }

  /**
   * Process tier promotion
   */
  static async promoteTier(
    userId: string,
    targetTier: keyof typeof TIER_CONFIG.TIER_DEFINITIONS,
    reason: TierPromotion['reason'] = 'automatic',
    triggeredBy?: string
  ): Promise<{
    success: boolean
    promotionId?: string
    error?: string
  }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get current user profile
        const userRef = doc(db, collections.profiles, userId)
        const userDoc = await transaction.get(userRef)
        
        if (!userDoc.exists()) {
          throw new Error('User profile not found')
        }

        const userProfile = userDoc.data() as UserProfile
        const currentTier = userProfile.gamification.currentTier
        const targetTierConfig = TIER_CONFIG.TIER_DEFINITIONS[targetTier]

        // Validate promotion
        if (targetTierConfig.level <= TIER_CONFIG.TIER_DEFINITIONS[currentTier].level) {
          throw new Error('Invalid promotion: target tier must be higher than current tier')
        }

        // Create promotion record
        const promotion: Omit<TierPromotion, 'id'> = {
          userId,
          fromTier: currentTier,
          toTier: targetTier,
          reason,
          requirements: {
            pointsRequired: targetTierConfig.pointRequirements.minimum,
            pointsActual: userProfile.gamification.totalPoints,
            timeRequired: targetTierConfig.timeRequirements.minimumDays,
            timeActual: userProfile.gamification.membershipDays,
            qualityRequired: targetTierConfig.timeRequirements.activityRequirements.qualityScore,
            qualityActual: userProfile.gamification.qualityScore,
            additionalCriteria: {
              postsCreated: userProfile.activity.contentCreated,
              communityEngagement: userProfile.activity.likesReceived + userProfile.activity.sharesReceived
            }
          },
          triggeredBy,
          processedBy: triggeredBy ? 'admin' : 'system',
          notificationSent: false,
          celebrationShown: false,
          benefitsActivated: false,
          demotionProtectedUntil: this.calculateDemotionProtection(targetTier),
          promotedAt: Timestamp.now(),
          activatedAt: Timestamp.now()
        }

        const promotionRef = doc(collection(db, collections.tierPromotions))
        transaction.set(promotionRef, promotion)

        // Update user profile
        transaction.update(userRef, {
          'gamification.currentTier': targetTier,
          'gamification.lastTierUpdate': Timestamp.now(),
          'gamification.tierProgress': 0, // Reset progress in new tier
          updatedAt: Timestamp.now()
        })

        // Activate tier benefits
        await this.activateTierBenefits(userId, targetTier)

        // Send notifications
        await NotificationEngine.notifyTierPromotion(userId, {
          ...promotion,
          id: promotionRef.id
        })

        // Log activity
        await ActivityLogger.logTierPromotion(userId, currentTier, targetTier)

        // Update analytics
        await this.updateTierAnalytics(currentTier, targetTier, 'promotion')

        return {
          success: true,
          promotionId: promotionRef.id
        }
      })

    } catch (error) {
      console.error('Error promoting tier:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to promote tier'
      }
    }
  }

  /**
   * Process tier demotion
   */
  static async demoteTier(
    userId: string,
    targetTier: keyof typeof TIER_CONFIG.TIER_DEFINITIONS,
    reason: string,
    triggeredBy?: string
  ): Promise<{
    success: boolean
    demotionId?: string
    error?: string
  }> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get current user profile
        const userRef = doc(db, collections.profiles, userId)
        const userDoc = await transaction.get(userRef)
        
        if (!userDoc.exists()) {
          throw new Error('User profile not found')
        }

        const userProfile = userDoc.data() as UserProfile
        const currentTier = userProfile.gamification.currentTier

        // Check demotion protection
        const isProtected = await this.isDemotionProtected(userId)
        if (isProtected && !triggeredBy) {
          throw new Error('User is protected from automatic demotion')
        }

        // Create demotion record (using TierPromotion with negative direction)
        const demotion: Omit<TierPromotion, 'id'> = {
          userId,
          fromTier: currentTier,
          toTier: targetTier,
          reason: 'manual', // Demotions are typically manual
          requirements: {
            pointsRequired: 0,
            pointsActual: userProfile.gamification.totalPoints,
            timeRequired: 0,
            timeActual: userProfile.gamification.membershipDays,
            qualityRequired: 0,
            qualityActual: userProfile.gamification.qualityScore,
            additionalCriteria: { reason }
          },
          triggeredBy,
          processedBy: triggeredBy ? 'admin' : 'system',
          notificationSent: false,
          celebrationShown: false,
          benefitsActivated: false,
          promotedAt: Timestamp.now(),
          activatedAt: Timestamp.now()
        }

        const demotionRef = doc(collection(db, collections.tierPromotions))
        transaction.set(demotionRef, demotion)

        // Update user profile
        transaction.update(userRef, {
          'gamification.currentTier': targetTier,
          'gamification.lastTierUpdate': Timestamp.now(),
          'gamification.tierProgress': 0,
          updatedAt: Timestamp.now()
        })

        // Update tier benefits
        await this.activateTierBenefits(userId, targetTier)

        // Send notification
        await NotificationEngine.sendNotification(
          userId,
          'tier_promotion', // Using same type but different message
          `Tier Update: ${targetTier}`,
          `Your tier has been updated to ${targetTier}. ${reason}`,
          {
            tierChange: {
              from: currentTier,
              to: targetTier
            }
          }
        )

        // Update analytics
        await this.updateTierAnalytics(currentTier, targetTier, 'demotion')

        return {
          success: true,
          demotionId: demotionRef.id
        }
      })

    } catch (error) {
      console.error('Error demoting tier:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to demote tier'
      }
    }
  }

  /**
   * Calculate tier progress percentage
   */
  static calculateTierProgress(
    userProfile: UserProfile,
    targetTier?: keyof typeof TIER_CONFIG.TIER_DEFINITIONS
  ): {
    pointsProgress: number
    timeProgress: number
    activityProgress: number
    overallProgress: number
    nextTier: string | null
    progressToNext: number
  } {
    const currentTier = userProfile.gamification.currentTier
    const currentTierConfig = TIER_CONFIG.TIER_DEFINITIONS[currentTier]
    
    // Find next tier
    const tiers = Object.keys(TIER_CONFIG.TIER_DEFINITIONS) as (keyof typeof TIER_CONFIG.TIER_DEFINITIONS)[]
    const currentIndex = tiers.findIndex(tier => tier === currentTier)
    const nextTier = currentIndex < tiers.length - 1 ? tiers[currentIndex + 1] : null
    
    if (!nextTier) {
      return {
        pointsProgress: 100,
        timeProgress: 100,
        activityProgress: 100,
        overallProgress: 100,
        nextTier: null,
        progressToNext: 100
      }
    }

    const nextTierConfig = TIER_CONFIG.TIER_DEFINITIONS[nextTier]

    // Calculate progress components
    const pointsProgress = Math.min(100, 
      (userProfile.gamification.totalPoints / nextTierConfig.pointRequirements.minimum) * 100
    )

    const timeProgress = Math.min(100,
      (userProfile.gamification.membershipDays / nextTierConfig.timeRequirements.minimumDays) * 100
    )

    // Activity progress (posts created, quality score, community engagement)
    const activityRequirements = nextTierConfig.timeRequirements.activityRequirements
    const postsProgress = Math.min(100,
      (userProfile.activity.contentCreated / activityRequirements.postsCreated) * 100
    )
    const qualityProgress = Math.min(100,
      (userProfile.gamification.qualityScore / activityRequirements.qualityScore) * 100
    )
    const engagementScore = userProfile.activity.likesReceived + userProfile.activity.sharesReceived
    const engagementProgress = Math.min(100,
      (engagementScore / activityRequirements.communityEngagement) * 100
    )

    const activityProgress = (postsProgress + qualityProgress + engagementProgress) / 3

    // Overall progress (all requirements must be met)
    const overallProgress = Math.min(pointsProgress, timeProgress, activityProgress)

    // Progress to next tier
    const progressToNext = overallProgress

    return {
      pointsProgress,
      timeProgress,
      activityProgress,
      overallProgress,
      nextTier,
      progressToNext
    }
  }

  /**
   * Get tier statistics and analytics
   */
  static async getTierAnalytics(): Promise<{
    distribution: Record<string, number>
    promotionVelocity: Record<string, number>
    retentionByTier: Record<string, number>
    averageTimeInTier: Record<string, number>
  }> {
    try {
      // Implementation would aggregate tier data from database
      return {
        distribution: {},
        promotionVelocity: {},
        retentionByTier: {},
        averageTimeInTier: {}
      }

    } catch (error) {
      console.error('Error getting tier analytics:', error)
      return {
        distribution: {},
        promotionVelocity: {},
        retentionByTier: {},
        averageTimeInTier: {}
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async getUserProfile(userId: string): Promise<UserProfile | null> {
    // Implementation would fetch from Firestore
    return null
  }

  private static async checkPromotionEligibility(
    userProfile: UserProfile
  ): Promise<{
    eligible: boolean
    targetTier: keyof typeof TIER_CONFIG.TIER_DEFINITIONS
    reasons: string[]
    requiresManualReview: boolean
  }> {
    const currentTier = userProfile.gamification.currentTier
    const tiers = Object.keys(TIER_CONFIG.TIER_DEFINITIONS) as (keyof typeof TIER_CONFIG.TIER_DEFINITIONS)[]
    const currentIndex = tiers.findIndex(tier => tier === currentTier)
    
    if (currentIndex === tiers.length - 1) {
      return {
        eligible: false,
        targetTier: currentTier,
        reasons: ['Already at highest tier'],
        requiresManualReview: false
      }
    }

    const nextTier = tiers[currentIndex + 1]
    const nextTierConfig = TIER_CONFIG.TIER_DEFINITIONS[nextTier]
    const reasons: string[] = []
    let eligible = true

    // Check point requirements
    if (userProfile.gamification.totalPoints < nextTierConfig.pointRequirements.minimum) {
      eligible = false
      reasons.push(`Needs ${nextTierConfig.pointRequirements.minimum - userProfile.gamification.totalPoints} more points`)
    }

    // Check time requirements
    if (userProfile.gamification.membershipDays < nextTierConfig.timeRequirements.minimumDays) {
      eligible = false
      const daysNeeded = nextTierConfig.timeRequirements.minimumDays - userProfile.gamification.membershipDays
      reasons.push(`Needs ${daysNeeded} more days of membership`)
    }

    // Check activity requirements
    const activityReqs = nextTierConfig.timeRequirements.activityRequirements
    
    if (userProfile.activity.contentCreated < activityReqs.postsCreated) {
      eligible = false
      reasons.push(`Needs ${activityReqs.postsCreated - userProfile.activity.contentCreated} more posts`)
    }

    if (userProfile.gamification.qualityScore < activityReqs.qualityScore) {
      eligible = false
      reasons.push(`Needs quality score of ${activityReqs.qualityScore} (current: ${userProfile.gamification.qualityScore.toFixed(2)})`)
    }

    const engagementScore = userProfile.activity.likesReceived + userProfile.activity.sharesReceived
    if (engagementScore < activityReqs.communityEngagement) {
      eligible = false
      reasons.push(`Needs ${activityReqs.communityEngagement - engagementScore} more community engagement`)
    }

    if (eligible) {
      reasons.push('All requirements met for promotion')
    }

    return {
      eligible,
      targetTier: nextTier,
      reasons,
      requiresManualReview: nextTier === 'platinum' // Platinum promotions require manual review
    }
  }

  private static async checkDemotionRisk(
    userProfile: UserProfile
  ): Promise<{
    atRisk: boolean
    targetTier: string
    reasons: string[]
    requiresManualReview: boolean
  }> {
    const currentTier = userProfile.gamification.currentTier
    const currentTierConfig = TIER_CONFIG.TIER_DEFINITIONS[currentTier]
    const maintenance = currentTierConfig.maintenanceRequirements
    
    const reasons: string[] = []
    let atRisk = false

    // Check inactivity
    const daysSinceLastActivity = this.daysSince(userProfile.gamification.lastActivity)
    if (daysSinceLastActivity > maintenance.inactivityDemotionDays) {
      atRisk = true
      reasons.push(`Inactive for ${daysSinceLastActivity} days (limit: ${maintenance.inactivityDemotionDays})`)
    }

    // Check quality threshold
    if (userProfile.gamification.qualityScore < maintenance.qualityThreshold) {
      atRisk = true
      reasons.push(`Quality score below threshold: ${userProfile.gamification.qualityScore} < ${maintenance.qualityThreshold}`)
    }

    // Check monthly activity
    // Implementation would check recent activity
    const recentActivity = 0 // Placeholder
    if (recentActivity < maintenance.monthlyActivity) {
      atRisk = true
      reasons.push(`Insufficient monthly activity: ${recentActivity} < ${maintenance.monthlyActivity}`)
    }

    // Determine target tier for demotion
    const tiers = Object.keys(TIER_CONFIG.TIER_DEFINITIONS) as (keyof typeof TIER_CONFIG.TIER_DEFINITIONS)[]
    const currentIndex = tiers.findIndex(tier => tier === currentTier)
    const targetTier = currentIndex > 0 ? tiers[currentIndex - 1] : currentTier

    return {
      atRisk,
      targetTier,
      reasons,
      requiresManualReview: true // Demotions typically require manual review
    }
  }

  private static calculateDemotionProtection(tier: string): Timestamp {
    // Protect from demotion for 30 days after promotion
    const protection = new Date()
    protection.setDate(protection.getDate() + 30)
    return Timestamp.fromDate(protection)
  }

  private static async isDemotionProtected(userId: string): Promise<boolean> {
    // Check if user has active demotion protection
    return false // Placeholder
  }

  private static async activateTierBenefits(
    userId: string,
    tier: keyof typeof TIER_CONFIG.TIER_DEFINITIONS
  ): Promise<void> {
    // Implementation would activate tier-specific benefits
  }

  private static async updateTierAnalytics(
    fromTier: string,
    toTier: string,
    type: 'promotion' | 'demotion'
  ): Promise<void> {
    // Implementation would update tier analytics
  }

  private static daysSince(timestamp: Timestamp): number {
    const now = new Date()
    const then = timestamp.toDate()
    const diffTime = Math.abs(now.getTime() - then.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * Batch process tier evaluations
   */
  static async batchEvaluateTiers(
    userIds?: string[],
    dryRun: boolean = false
  ): Promise<{
    processed: number
    promoted: number
    demoted: number
    errors: number
    results: Array<{
      userId: string
      action: string
      success: boolean
      error?: string
    }>
  }> {
    try {
      // If no userIds provided, get all active users
      if (!userIds) {
        userIds = await this.getActiveUserIds()
      }

      const results = []
      let processed = 0
      let promoted = 0
      let demoted = 0
      let errors = 0

      for (const userId of userIds) {
        try {
          const evaluation = await this.evaluateUserTier(userId)
          processed++

          if (!dryRun && evaluation.action !== 'maintain') {
            if (evaluation.action === 'promote') {
              const result = await this.promoteTier(userId, evaluation.eligibleTier as any)
              if (result.success) {
                promoted++
              } else {
                errors++
              }
              results.push({
                userId,
                action: 'promote',
                success: result.success,
                error: result.error
              })
            } else if (evaluation.action === 'demote') {
              const result = await this.demoteTier(userId, evaluation.eligibleTier as any, 'Automatic maintenance')
              if (result.success) {
                demoted++
              } else {
                errors++
              }
              results.push({
                userId,
                action: 'demote',
                success: result.success,
                error: result.error
              })
            }
          } else {
            results.push({
              userId,
              action: evaluation.action,
              success: true
            })
          }

        } catch (error) {
          errors++
          results.push({
            userId,
            action: 'error',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return {
        processed,
        promoted,
        demoted,
        errors,
        results
      }

    } catch (error) {
      console.error('Error in batch tier evaluation:', error)
      return {
        processed: 0,
        promoted: 0,
        demoted: 0,
        errors: 1,
        results: []
      }
    }
  }

  private static async getActiveUserIds(): Promise<string[]> {
    // Implementation would get active user IDs from database
    return []
  }
}

export default TierManager