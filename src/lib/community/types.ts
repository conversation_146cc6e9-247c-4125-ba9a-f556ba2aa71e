/**
 * Community Rules System Types - Phase 1 Implementation
 * 
 * Extended type definitions for the comprehensive community rules system.
 * Includes user profiles, point system, moderation, and community features.
 * 
 * <AUTHOR> Team - Phase 1 Community Implementation
 * @version 1.0.0
 */

import { Timestamp } from 'firebase/firestore'

// ===== ENHANCED USER PROFILE =====

export interface UserProfile {
  id: string
  email: string
  displayName?: string
  photoURL?: string
  role: 'user' | 'moderator' | 'admin' | 'superadmin'
  
  // Enhanced gamification profile
  gamification: {
    totalPoints: number
    currentTier: 'bronze' | 'silver' | 'gold' | 'platinum'
    tierProgress: number
    lastTierUpdate: Timestamp
    qualityScore: number
    achievementCount: number
    streakDays: number
    lastActivity: Timestamp
    joinDate: Timestamp
    membershipDays: number
    pointsEarnedToday: number
    pointsEarnedThisWeek: number
    pointsEarnedThisMonth: number
  }
  
  // Moderation profile
  moderation: {
    warningCount: number
    suspensionHistory: ModerationAction[]
    lastViolation: Timestamp | null
    reputationScore: number
    reportCount: number
    helpfulContributions: number
    isSuspended: boolean
    suspensionEnds: Timestamp | null
    suspensionReason: string | null
    appealCount: number
    lastAppeal: Timestamp | null
  }
  
  // User preferences for community features
  preferences: {
    emailNotifications: boolean
    communityNotifications: boolean
    moderationAlerts: boolean
    privacyLevel: 'public' | 'private' | 'friends'
    languageFilter: boolean
    contentMaturityLevel: 'all' | 'teen' | 'mature'
    autoSaveContent: boolean
    displayAchievements: boolean
    displayPoints: boolean
    displayTier: boolean
  }
  
  // Activity tracking
  activity: {
    contentCreated: number
    commentsPosted: number
    likesGiven: number
    likesReceived: number
    sharesGiven: number
    sharesReceived: number
    reportsSubmitted: number
    moderationActionsReceived: number
    helpfulVotesReceived: number
    challengesParticipated: number
    challengesWon: number
  }
  
  // Timestamps
  isActive: boolean
  lastLoginAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

// ===== POINT SYSTEM =====

export interface PointTransaction {
  id: string
  userId: string
  activityType: string
  pointsEarned: number
  pointsSpent?: number
  balanceBefore: number
  balanceAfter: number
  
  // Enhanced calculation details
  calculation: {
    basePoints: number
    qualityMultiplier: number
    tierMultiplier: number
    progressiveScaling: number
    streakBonus: number
    finalPoints: number
  }
  
  // Quality assessment
  quality: {
    score: number
    isManuallyReviewed: boolean
    reviewedBy?: string
    reviewNotes?: string
    flagged: boolean
  }
  
  // Activity context
  source: 'content_creation' | 'community_interaction' | 'challenge_participation' | 
         'social_sharing' | 'referral' | 'achievement' | 'daily_activity' | 
         'marketplace' | 'moderation_reward' | 'admin_adjustment'
  
  description: string
  metadata: Record<string, any>
  ipAddress?: string
  userAgent?: string
  
  // Validation and fraud prevention
  validation: {
    isValid: boolean
    verifiedBy?: string
    verificationMethod?: string
    suspiciousActivity: boolean
    riskScore: number
  }
  
  status: 'pending' | 'approved' | 'rejected' | 'reversed'
  createdAt: Timestamp
  processedAt?: Timestamp
  reversedAt?: Timestamp
}

export interface DailyActivityLimit {
  userId: string
  date: string // YYYY-MM-DD format
  activities: Record<string, {
    count: number
    pointsEarned: number
    lastActivity: Timestamp
    qualitySum: number
  }>
  resetAt: Timestamp
  createdAt: Timestamp
}

// ===== COMMUNITY CONTENT =====

export interface CommunityContent {
  id: string
  authorId: string
  type: 'post' | 'comment' | 'photo' | 'tutorial' | 'review' | 'discussion' | 'reply'
  title?: string
  content: string
  parentId?: string // For comments/replies
  
  // Content metadata
  metadata: {
    wordCount: number
    imageCount: number
    linkCount: number
    hasCode: boolean
    language: string
    readTime: number // estimated minutes
    tags: string[]
    category?: string
  }
  
  // Quality metrics
  qualityMetrics: {
    score: number
    votes: number
    helpfulVotes: number
    reports: number
    engagement: number
    viewTime: number
    completionRate: number
    flagCount: number
  }
  
  // Moderation status
  moderation: {
    status: 'pending' | 'approved' | 'flagged' | 'removed' | 'edited'
    reviewedBy: string | null
    reviewedAt: Timestamp | null
    flags: ModerationFlag[]
    autoModerationScore: number
    requiresHumanReview: boolean
    appealStatus?: 'none' | 'pending' | 'approved' | 'denied'
  }
  
  // Engagement tracking
  engagement: {
    views: number
    uniqueViews: number
    likes: number
    dislikes: number
    shares: number
    comments: number
    bookmarks: number
    reactions: Record<string, number>
  }
  
  // Content lifecycle
  isEdited: boolean
  editHistory: ContentEdit[]
  isDeleted: boolean
  deletedAt?: Timestamp
  deletedBy?: string
  deletionReason?: string
  
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface ContentEdit {
  editedAt: Timestamp
  editedBy: string
  changes: {
    field: string
    oldValue: any
    newValue: any
  }[]
  reason?: string
  moderatorApproved: boolean
}

// ===== MODERATION SYSTEM =====

export interface ModerationFlag {
  id: string
  flaggedBy: string
  reason: 'spam' | 'harassment' | 'inappropriate' | 'copyright' | 'misinformation' | 'other'
  description: string
  evidence?: string[]
  severity: 'low' | 'medium' | 'high' | 'critical'
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed'
  createdAt: Timestamp
  reviewedAt?: Timestamp
  reviewedBy?: string
}

export interface ModerationAction {
  id: string
  targetUserId: string
  targetContentId?: string
  moderatorId: string
  actionType: 'warning' | 'content_removal' | 'temporary_suspension' | 
             'permanent_ban' | 'point_reduction' | 'tier_demotion' |
             'feature_restriction' | 'shadowban'
  
  // Action details
  reason: string
  evidence: string[]
  severity: 'minor' | 'moderate' | 'severe' | 'critical'
  duration?: number // in hours, null for permanent
  
  // Restrictions
  restrictions: {
    canPost: boolean
    canComment: boolean
    canMessage: boolean
    canParticipateInChallenges: boolean
    canEarnPoints: boolean
    canAccessMarketplace: boolean
  }
  
  // Review and appeal
  isAppealable: boolean
  appealDeadline?: Timestamp
  status: 'active' | 'completed' | 'appealed' | 'reversed'
  reversalReason?: string
  reversedBy?: string
  reversedAt?: Timestamp
  
  // Impact tracking
  pointsDeducted?: number
  tierDemotion?: string
  featuresRestricted: string[]
  
  createdAt: Timestamp
  expiresAt?: Timestamp
  completedAt?: Timestamp
}

export interface ModerationQueue {
  id: string
  contentId: string
  reportedBy: string[]
  assignedTo?: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  
  // Queue metadata
  queueType: 'automatic' | 'reported' | 'scheduled_review'
  estimatedReviewTime: number // minutes
  complexity: 'simple' | 'moderate' | 'complex'
  
  // Auto-analysis results
  autoAnalysis: {
    riskScore: number
    suggestedAction: string
    confidence: number
    aiFlags: string[]
    requiresSpecialist: boolean
  }
  
  status: 'pending' | 'in_review' | 'completed' | 'escalated'
  createdAt: Timestamp
  assignedAt?: Timestamp
  reviewStartedAt?: Timestamp
  completedAt?: Timestamp
}

export interface Appeal {
  id: string
  userId: string
  actionId: string
  reason: string
  evidence: string[]
  
  // Appeal details
  appealType: 'account_suspension' | 'content_removal' | 'point_deduction' | 'tier_demotion'
  urgency: 'low' | 'medium' | 'high'
  additionalContext: string
  
  // Review process
  status: 'submitted' | 'under_review' | 'additional_info_needed' | 
          'approved' | 'partially_approved' | 'denied'
  reviewerId?: string
  decision?: {
    outcome: 'approved' | 'partially_approved' | 'denied'
    reasoning: string
    actionsTaken: string[]
    newRestrictions?: Partial<ModerationAction['restrictions']>
    compensationAwarded?: {
      points: number
      tierRestoration: boolean
      featureRestoration: string[]
    }
  }
  
  submittedAt: Timestamp
  reviewedAt?: Timestamp
  decisionCommunicatedAt?: Timestamp
}

// ===== COMMUNITY RULES ENGINE =====

export interface CommunityRule {
  id: string
  name: string
  description: string
  category: 'content' | 'behavior' | 'interaction' | 'marketplace' | 'system'
  
  // Rule definition
  conditions: RuleCondition[]
  actions: RuleAction[]
  severity: 'info' | 'warning' | 'violation' | 'serious_violation'
  
  // Rule configuration
  isActive: boolean
  priority: number
  applicableUserTypes: ('all' | 'new_users' | 'regular_users' | 'premium_users')[]
  exemptRoles: ('moderator' | 'admin' | 'verified')[]
  
  // Enforcement settings
  enforcement: {
    automatic: boolean
    requiresHumanReview: boolean
    appealable: boolean
    escalationThreshold: number
    cooldownPeriod: number // hours between violations
  }
  
  // Analytics
  analytics: {
    violationCount: number
    falsePositiveRate: number
    appealSuccessRate: number
    lastTriggered?: Timestamp
    averageResolutionTime: number
  }
  
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
  lastReviewedAt?: Timestamp
}

export interface RuleCondition {
  type: 'content_contains' | 'user_tier' | 'account_age' | 'activity_frequency' |
        'content_length' | 'spam_detection' | 'sentiment_analysis' | 'custom'
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'matches_pattern'
  value: any
  weight?: number // for scoring-based rules
  metadata?: Record<string, any>
}

export interface RuleAction {
  type: 'flag_content' | 'auto_remove' | 'require_review' | 'warn_user' |
        'suspend_user' | 'reduce_points' | 'restrict_features' | 'notify_moderators'
  parameters: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
  reversible: boolean
}

// ===== ACHIEVEMENT SYSTEM EXTENSIONS =====

export interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  
  // Achievement classification
  category: 'community_engagement' | 'content_quality' | 'social_interaction' |
           'challenges' | 'milestones' | 'moderation' | 'special_events' | 'legacy'
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  
  // Requirements and conditions
  requirements: AchievementRequirement[]
  prerequisites: string[] // Other achievement IDs required
  
  // Rewards
  rewards: {
    points: number
    badge?: string
    title?: string
    specialPrivileges?: string[]
    physicalReward?: string
  }
  
  // Achievement behavior
  isRepeatable: boolean
  isSecret: boolean // Hidden until unlocked
  isActive: boolean
  expiresAt?: Timestamp
  
  // Progress tracking
  progressType: 'cumulative' | 'streak' | 'milestone' | 'event_based'
  trackingMetrics: string[]
  
  // Social features
  isShareable: boolean
  shareMessage?: string
  celebrationAnimation?: string
  
  // Analytics
  analytics: {
    unlockedCount: number
    averageTimeToUnlock: number // days
    completionRate: number // percentage of users who unlock it
    socialShares: number
  }
  
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
  metadata?: Record<string, any>
}

export interface AchievementRequirement {
  type: 'points_earned' | 'content_created' | 'community_votes' | 'streak_days' |
        'challenges_won' | 'helpful_contributions' | 'tier_advancement' | 'custom'
  target: number
  timeframe?: 'all_time' | 'daily' | 'weekly' | 'monthly' | 'yearly'
  conditions?: Record<string, any>
}

export interface UserAchievement {
  id: string
  userId: string
  achievementId: string
  progress: number
  isCompleted: boolean
  
  // Progress tracking
  progressData: Record<string, any>
  milestones: {
    percentage: number
    reachedAt: Timestamp
    notified: boolean
  }[]
  
  // Completion details
  unlockedAt?: Timestamp
  notificationSent: boolean
  socialShared: boolean
  celebrationViewed: boolean
  
  // Context
  unlockContext?: {
    triggerActivity: string
    location: string
    deviceType: string
    sessionDuration: number
  }
  
  createdAt: Timestamp
  updatedAt: Timestamp
}

// ===== TIER SYSTEM =====

export interface UserTier {
  id: string
  name: string
  slug: string
  level: number
  
  // Tier requirements
  pointRequirements: {
    minimum: number
    maximum?: number
  }
  timeRequirements: {
    minimumDays: number
    activityRequirements: {
      postsCreated: number
      qualityScore: number
      communityEngagement: number
    }
  }
  
  // Tier benefits
  benefits: {
    pointMultiplier: number
    maxDailyPoints: number
    specialFeatures: string[]
    prioritySupport: boolean
    exclusiveContent: boolean
    betaAccess: boolean
    customization: {
      profileThemes: string[]
      badgeSlots: number
      customTitles: boolean
    }
  }
  
  // Visual representation
  display: {
    color: string
    backgroundColor: string
    borderColor: string
    icon: string
    animation?: string
    gradientStops?: string[]
  }
  
  // Tier maintenance
  maintenanceRequirements: {
    monthlyActivity: number
    qualityThreshold: number
    inactivityDemotionDays: number
  }
  
  isActive: boolean
  order: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface TierPromotion {
  id: string
  userId: string
  fromTier: string
  toTier: string
  
  // Promotion details
  reason: 'automatic' | 'manual' | 'appeal' | 'special_event'
  requirements: {
    pointsRequired: number
    pointsActual: number
    timeRequired: number
    timeActual: number
    qualityRequired: number
    qualityActual: number
    additionalCriteria: Record<string, any>
  }
  
  // Promotion process
  triggeredBy?: string // user ID if manual
  approvedBy?: string
  processedBy: 'system' | 'admin'
  
  // Notifications
  notificationSent: boolean
  celebrationShown: boolean
  benefitsActivated: boolean
  
  // Demotion protection
  demotionProtectedUntil?: Timestamp
  
  promotedAt: Timestamp
  activatedAt: Timestamp
  metadata?: Record<string, any>
}

// ===== NOTIFICATIONS =====

export interface CommunityNotification {
  id: string
  userId: string
  type: 'achievement_unlock' | 'tier_promotion' | 'moderation_action' | 
        'content_interaction' | 'challenge_update' | 'system_announcement' |
        'community_milestone' | 'appeal_update'
  
  // Notification content
  title: string
  message: string
  icon?: string
  imageUrl?: string
  actionUrl?: string
  
  // Notification data
  data: {
    achievementId?: string
    tierChange?: {
      from: string
      to: string
    }
    moderationActionId?: string
    contentId?: string
    challengeId?: string
    pointsAwarded?: number
    relatedUsers?: string[]
  }
  
  // Delivery settings
  priority: 'low' | 'normal' | 'high' | 'urgent'
  channels: ('in_app' | 'email' | 'push' | 'sms')[]
  
  // Status tracking
  isRead: boolean
  isDisplayed: boolean
  readAt?: Timestamp
  displayedAt?: Timestamp
  interactedAt?: Timestamp
  
  // Expiration
  expiresAt?: Timestamp
  
  createdAt: Timestamp
}

// ===== ANALYTICS & REPORTING =====

export interface CommunityAnalytics {
  id: string
  date: string // YYYY-MM-DD
  
  // User metrics
  users: {
    total: number
    active: number
    new: number
    returning: number
    byTier: Record<string, number>
    churnRate: number
    retentionRate: number
  }
  
  // Content metrics
  content: {
    postsCreated: number
    commentsPosted: number
    qualityScore: number
    moderationActions: number
    reportedContent: number
    removedContent: number
  }
  
  // Engagement metrics
  engagement: {
    averageSessionTime: number
    pageViews: number
    interactions: number
    socialShares: number
    challengeParticipation: number
  }
  
  // Gamification metrics
  gamification: {
    pointsAwarded: number
    achievementsUnlocked: number
    tierPromotions: number
    challengesCompleted: number
    rewardsRedeemed: number
  }
  
  // Health metrics
  health: {
    moderationResponseTime: number
    userSatisfactionScore: number
    reportResolutionTime: number
    appealSuccessRate: number
    falsePositiveRate: number
  }
  
  calculatedAt: Timestamp
  createdAt: Timestamp
}

// ===== UTILITY TYPES =====

export interface ActivityTrigger {
  userId: string
  activityType: string
  data: Record<string, any>
  timestamp: Timestamp
  sessionId?: string
  deviceInfo?: {
    type: 'desktop' | 'mobile' | 'tablet'
    browser?: string
    os?: string
    ip?: string
    location?: string
  }
}

export interface QualityAssessment {
  score: number // 0.0 to 1.0
  factors: {
    contentLength: number
    readability: number
    engagement: number
    originality: number
    helpfulness: number
    accuracy: number
  }
  aiAnalysis?: {
    sentiment: number
    toxicity: number
    spam: number
    coherence: number
  }
  humanReview?: {
    reviewerId: string
    score: number
    notes: string
    reviewedAt: Timestamp
  }
}

export type UserRole = 'user' | 'moderator' | 'admin' | 'superadmin'
export type ContentType = 'post' | 'comment' | 'photo' | 'tutorial' | 'review' | 'discussion' | 'reply'
export type ModerationStatus = 'pending' | 'approved' | 'flagged' | 'removed' | 'edited'
export type UserTierLevel = 'bronze' | 'silver' | 'gold' | 'platinum'
export type ActivityType = string // Flexible for future activity types

// Export commonly used union types
export type NotificationType = CommunityNotification['type']
export type AchievementCategory = Achievement['category']
export type RuleCategory = CommunityRule['category']
export type ModerationActionType = ModerationAction['actionType']