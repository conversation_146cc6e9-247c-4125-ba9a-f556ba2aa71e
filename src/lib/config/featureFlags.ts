/**
 * Syndicaps Feature Flags for Cloudflare Hybrid Rollout
 * 
 * This system allows gradual rollout of hybrid features with percentage-based
 * user targeting and easy rollback capabilities.
 */

export interface FeatureFlag {
  enabled: boolean
  rolloutPercentage: number
  description: string
  dependencies?: string[]
  emergencyKillSwitch?: boolean
  userTargeting?: {
    includeUsers?: string[]
    excludeUsers?: string[]
    userTypes?: ('admin' | 'premium' | 'beta' | 'regular')[]
  }
  metrics?: {
    successRate?: number
    errorRate?: number
    lastUpdated?: string
  }
}

export interface FeatureFlags {
  USE_R2_STORAGE: FeatureFlag
  USE_CLOUDFLARE_WORKERS: FeatureFlag
  USE_CLOUDFLARE_IMAGES: FeatureFlag
  USE_HYBRID_CDN: FeatureFlag
  USE_EDGE_CACHING: FeatureFlag
  USE_PERFORMANCE_MONITORING: FeatureFlag
  USE_WORKER_IMAGE_OPTIMIZATION: FeatureFlag
  USE_WORKER_API_CACHE: FeatureFlag
  USE_ADVANCED_ANALYTICS: FeatureFlag
  USE_EMERGENCY_FALLBACK: FeatureFlag
}

// Environment-based feature flag configuration
export const FEATURE_FLAGS: FeatureFlags = {
  USE_R2_STORAGE: {
    enabled: process.env.FEATURE_R2_STORAGE === 'true',
    rolloutPercentage: parseInt(process.env.R2_ROLLOUT_PERCENTAGE || '0'),
    description: 'Use Cloudflare R2 for image and asset storage',
    dependencies: [],
    emergencyKillSwitch: process.env.R2_EMERGENCY_DISABLE === 'true',
    userTargeting: {
      userTypes: ['admin', 'beta']
    },
    metrics: {
      successRate: 0,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_CLOUDFLARE_WORKERS: {
    enabled: process.env.FEATURE_CF_WORKERS === 'true',
    rolloutPercentage: parseInt(process.env.CF_WORKERS_ROLLOUT_PERCENTAGE || '0'),
    description: 'Use Cloudflare Workers for edge computing',
    dependencies: [],
    emergencyKillSwitch: process.env.CF_WORKERS_EMERGENCY_DISABLE === 'true',
    userTargeting: {
      userTypes: ['admin', 'beta']
    },
    metrics: {
      successRate: 0,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_CLOUDFLARE_IMAGES: {
    enabled: process.env.FEATURE_CF_IMAGES === 'true',
    rolloutPercentage: parseInt(process.env.CF_IMAGES_ROLLOUT_PERCENTAGE || '0'),
    description: 'Use Cloudflare Images for optimization',
    dependencies: ['USE_R2_STORAGE'],
    emergencyKillSwitch: process.env.CF_IMAGES_EMERGENCY_DISABLE === 'true',
    userTargeting: {
      userTypes: ['admin', 'beta']
    },
    metrics: {
      successRate: 0,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_HYBRID_CDN: {
    enabled: process.env.FEATURE_HYBRID_CDN === 'true',
    rolloutPercentage: parseInt(process.env.HYBRID_CDN_ROLLOUT_PERCENTAGE || '0'),
    description: 'Use hybrid CDN configuration',
    dependencies: [],
    emergencyKillSwitch: process.env.HYBRID_CDN_EMERGENCY_DISABLE === 'true',
    userTargeting: {
      userTypes: ['admin', 'beta']
    },
    metrics: {
      successRate: 0,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_EDGE_CACHING: {
    enabled: process.env.FEATURE_EDGE_CACHING === 'true',
    rolloutPercentage: parseInt(process.env.EDGE_CACHING_ROLLOUT_PERCENTAGE || '0'),
    description: 'Use edge caching for API responses',
    dependencies: ['USE_CLOUDFLARE_WORKERS'],
    emergencyKillSwitch: process.env.EDGE_CACHING_EMERGENCY_DISABLE === 'true',
    userTargeting: {
      userTypes: ['admin', 'beta']
    },
    metrics: {
      successRate: 0,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_PERFORMANCE_MONITORING: {
    enabled: process.env.PERFORMANCE_MONITORING_ENABLED === 'true',
    rolloutPercentage: 100, // Always enabled for monitoring
    description: 'Enable performance monitoring for hybrid features',
    dependencies: [],
    emergencyKillSwitch: false,
    metrics: {
      successRate: 100,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_WORKER_IMAGE_OPTIMIZATION: {
    enabled: process.env.FEATURE_WORKER_IMAGE_OPT === 'true',
    rolloutPercentage: parseInt(process.env.WORKER_IMAGE_OPT_ROLLOUT_PERCENTAGE || '0'),
    description: 'Use Cloudflare Workers for image optimization',
    dependencies: ['USE_CLOUDFLARE_WORKERS', 'USE_R2_STORAGE'],
    emergencyKillSwitch: process.env.WORKER_IMAGE_OPT_EMERGENCY_DISABLE === 'true',
    userTargeting: {
      userTypes: ['admin', 'beta']
    },
    metrics: {
      successRate: 0,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_WORKER_API_CACHE: {
    enabled: process.env.FEATURE_WORKER_API_CACHE === 'true',
    rolloutPercentage: parseInt(process.env.WORKER_API_CACHE_ROLLOUT_PERCENTAGE || '0'),
    description: 'Use Cloudflare Workers for API caching',
    dependencies: ['USE_CLOUDFLARE_WORKERS'],
    emergencyKillSwitch: process.env.WORKER_API_CACHE_EMERGENCY_DISABLE === 'true',
    userTargeting: {
      userTypes: ['admin', 'beta']
    },
    metrics: {
      successRate: 0,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_ADVANCED_ANALYTICS: {
    enabled: process.env.FEATURE_ADVANCED_ANALYTICS === 'true',
    rolloutPercentage: parseInt(process.env.ADVANCED_ANALYTICS_ROLLOUT_PERCENTAGE || '100'),
    description: 'Enable advanced analytics and monitoring',
    dependencies: ['USE_PERFORMANCE_MONITORING'],
    emergencyKillSwitch: false,
    userTargeting: {
      userTypes: ['admin']
    },
    metrics: {
      successRate: 100,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  },

  USE_EMERGENCY_FALLBACK: {
    enabled: true, // Always enabled for safety
    rolloutPercentage: 100,
    description: 'Emergency fallback to Firebase when Cloudflare services fail',
    dependencies: [],
    emergencyKillSwitch: false,
    metrics: {
      successRate: 100,
      errorRate: 0,
      lastUpdated: new Date().toISOString()
    }
  }
}

/**
 * Check if a feature should be enabled for a specific user
 */
export function shouldUseFeature(
  featureName: keyof FeatureFlags,
  userId?: string,
  userType?: 'admin' | 'premium' | 'beta' | 'regular'
): boolean {
  const feature = FEATURE_FLAGS[featureName]

  if (!feature.enabled) return false

  // Emergency kill switch check
  if (feature.emergencyKillSwitch) {
    console.warn(`Feature ${featureName} disabled by emergency kill switch`)
    return false
  }

  // Check dependencies
  if (feature.dependencies) {
    for (const dependency of feature.dependencies) {
      if (!shouldUseFeature(dependency as keyof FeatureFlags, userId, userType)) {
        return false
      }
    }
  }

  // User targeting checks
  if (feature.userTargeting) {
    // Check excluded users
    if (userId && feature.userTargeting.excludeUsers?.includes(userId)) {
      return false
    }

    // Check included users (override other rules)
    if (userId && feature.userTargeting.includeUsers?.includes(userId)) {
      return true
    }

    // Check user types
    if (userType && feature.userTargeting.userTypes &&
        !feature.userTargeting.userTypes.includes(userType)) {
      return false
    }
  }

  if (feature.rolloutPercentage === 100) return true
  if (feature.rolloutPercentage === 0) return false

  // Gradual rollout based on user ID hash
  if (userId) {
    const hash = hashString(userId)
    return (hash % 100) < feature.rolloutPercentage
  }

  // Fallback to random for anonymous users
  return Math.random() * 100 < feature.rolloutPercentage
}

/**
 * Check if R2 storage should be used for a specific user
 */
export function shouldUseR2Storage(userId?: string): boolean {
  return shouldUseFeature('USE_R2_STORAGE', userId)
}

/**
 * Check if Cloudflare Workers should be used
 */
export function shouldUseCloudflareWorkers(userId?: string): boolean {
  return shouldUseFeature('USE_CLOUDFLARE_WORKERS', userId)
}

/**
 * Check if Cloudflare Images should be used
 */
export function shouldUseCloudflareImages(userId?: string): boolean {
  return shouldUseFeature('USE_CLOUDFLARE_IMAGES', userId)
}

/**
 * Get feature flag status for admin dashboard
 */
export function getFeatureFlagStatus(): Record<string, any> {
  return Object.entries(FEATURE_FLAGS).reduce((acc, [key, flag]) => {
    acc[key] = {
      enabled: flag.enabled,
      rolloutPercentage: flag.rolloutPercentage,
      description: flag.description,
      dependencies: flag.dependencies || []
    }
    return acc
  }, {} as Record<string, any>)
}

/**
 * Hash function for consistent user-based rollouts
 */
function hashString(str: string): number {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash)
}

/**
 * Emergency rollback function
 */
export function emergencyRollback(featureName: keyof FeatureFlags, reason?: string): void {
  console.warn(`Emergency rollback triggered for feature: ${featureName}`)
  if (reason) {
    console.warn(`Rollback reason: ${reason}`)
  }

  // Update the feature flag to disable it
  const feature = FEATURE_FLAGS[featureName]
  if (feature) {
    feature.emergencyKillSwitch = true
    feature.enabled = false
    feature.rolloutPercentage = 0

    // Update metrics
    if (feature.metrics) {
      feature.metrics.lastUpdated = new Date().toISOString()
    }

    // Log the rollback event
    logFeatureFlagEvent('emergency_rollback', featureName, {
      reason,
      timestamp: new Date().toISOString(),
      previousState: {
        enabled: feature.enabled,
        rolloutPercentage: feature.rolloutPercentage
      }
    })
  }

  // In production, this would also:
  // 1. Update environment variables via API
  // 2. Send alerts to monitoring systems
  // 3. Trigger automatic fallback mechanisms
}

/**
 * Feature flag middleware for API routes
 */
export function withFeatureFlag(
  featureName: keyof FeatureFlags,
  handler: Function
) {
  return async (req: any, res: any, ...args: any[]) => {
    const userId = req.user?.id || req.headers['x-user-id']
    const userType = req.user?.type || req.headers['x-user-type'] as any

    if (!shouldUseFeature(featureName, userId, userType)) {
      logFeatureFlagEvent('feature_blocked', featureName, {
        userId,
        userType,
        timestamp: new Date().toISOString()
      })

      return res.status(503).json({
        error: 'Feature not available',
        feature: featureName,
        fallbackAvailable: shouldUseFeature('USE_EMERGENCY_FALLBACK', userId, userType)
      })
    }

    // Log successful feature usage
    logFeatureFlagEvent('feature_used', featureName, {
      userId,
      userType,
      timestamp: new Date().toISOString()
    })

    return handler(req, res, ...args)
  }
}

/**
 * Update feature flag metrics
 */
export function updateFeatureFlagMetrics(
  featureName: keyof FeatureFlags,
  success: boolean,
  errorDetails?: any
): void {
  const feature = FEATURE_FLAGS[featureName]
  if (!feature.metrics) return

  // Simple success/error rate tracking
  const currentSuccessRate = feature.metrics.successRate || 0
  const currentErrorRate = feature.metrics.errorRate || 0

  if (success) {
    feature.metrics.successRate = Math.min(100, currentSuccessRate + 0.1)
    feature.metrics.errorRate = Math.max(0, currentErrorRate - 0.05)
  } else {
    feature.metrics.errorRate = Math.min(100, currentErrorRate + 0.5)
    feature.metrics.successRate = Math.max(0, currentSuccessRate - 0.1)

    // Trigger emergency rollback if error rate is too high
    if (feature.metrics.errorRate > 50) {
      emergencyRollback(featureName, `High error rate: ${feature.metrics.errorRate}%`)
    }
  }

  feature.metrics.lastUpdated = new Date().toISOString()

  logFeatureFlagEvent('metrics_updated', featureName, {
    success,
    errorDetails,
    newMetrics: feature.metrics,
    timestamp: new Date().toISOString()
  })
}

/**
 * Log feature flag events for monitoring
 */
function logFeatureFlagEvent(
  eventType: string,
  featureName: keyof FeatureFlags,
  data: any
): void {
  // In production, this would send to monitoring service
  console.log(`[FeatureFlag] ${eventType}:`, {
    feature: featureName,
    ...data
  })
}

/**
 * Get comprehensive feature flag analytics
 */
export function getFeatureFlagAnalytics(): Record<string, any> {
  return Object.entries(FEATURE_FLAGS).reduce((acc, [key, flag]) => {
    acc[key] = {
      enabled: flag.enabled,
      rolloutPercentage: flag.rolloutPercentage,
      description: flag.description,
      dependencies: flag.dependencies || [],
      emergencyKillSwitch: flag.emergencyKillSwitch || false,
      userTargeting: flag.userTargeting || {},
      metrics: flag.metrics || {},
      healthStatus: getFeatureHealthStatus(flag)
    }
    return acc
  }, {} as Record<string, any>)
}

/**
 * Get health status of a feature flag
 */
function getFeatureHealthStatus(flag: FeatureFlag): 'healthy' | 'warning' | 'critical' {
  if (flag.emergencyKillSwitch) return 'critical'
  if (!flag.metrics) return 'healthy'

  const errorRate = flag.metrics.errorRate || 0
  const successRate = flag.metrics.successRate || 100

  if (errorRate > 25 || successRate < 75) return 'critical'
  if (errorRate > 10 || successRate < 90) return 'warning'

  return 'healthy'
}

/**
 * Gradual rollout management
 */
export function adjustRolloutPercentage(
  featureName: keyof FeatureFlags,
  newPercentage: number,
  reason?: string
): boolean {
  if (newPercentage < 0 || newPercentage > 100) {
    console.error('Invalid rollout percentage. Must be between 0 and 100.')
    return false
  }

  const feature = FEATURE_FLAGS[featureName]
  if (!feature) {
    console.error(`Feature ${featureName} not found.`)
    return false
  }

  const oldPercentage = feature.rolloutPercentage
  feature.rolloutPercentage = newPercentage

  if (feature.metrics) {
    feature.metrics.lastUpdated = new Date().toISOString()
  }

  logFeatureFlagEvent('rollout_adjusted', featureName, {
    oldPercentage,
    newPercentage,
    reason,
    timestamp: new Date().toISOString()
  })

  return true
}

/**
 * React hook for feature flags
 */
export function useFeatureFlag(
  featureName: keyof FeatureFlags,
  userId?: string,
  userType?: 'admin' | 'premium' | 'beta' | 'regular'
): boolean {
  return shouldUseFeature(featureName, userId, userType)
}
