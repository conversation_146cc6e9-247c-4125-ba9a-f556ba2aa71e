/**
 * Contest Lifecycle Management System
 *
 * Automated system for managing contest state transitions and lifecycle events.
 * Handles automatic transitions between contest phases, notifications, and
 * cleanup operations.
 *
 * Contest Lifecycle:
 * upcoming → active → voting → completed
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Timestamp } from 'firebase/firestore'
import { updateContest, getContests } from './api/contests'
import type { Contest, ContestStatus, ContestNotificationType } from '../types/contests'

// ===== LIFECYCLE MANAGEMENT =====

/**
 * Contest lifecycle state machine
 */
export class ContestLifecycleManager {
  private static instance: ContestLifecycleManager
  private intervalId: NodeJS.Timeout | null = null
  private readonly CHECK_INTERVAL = 60000 // Check every minute

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): ContestLifecycleManager {
    if (!ContestLifecycleManager.instance) {
      ContestLifecycleManager.instance = new ContestLifecycleManager()
    }
    return ContestLifecycleManager.instance
  }

  /**
   * Start the lifecycle monitoring system
   */
  start(): void {
    if (this.intervalId) {
      console.log('⚠️ Contest lifecycle manager already running')
      return
    }

    console.log('🚀 Starting contest lifecycle manager')
    
    // Run initial check
    this.checkAllContests()
    
    // Set up periodic checks
    this.intervalId = setInterval(() => {
      this.checkAllContests()
    }, this.CHECK_INTERVAL)
  }

  /**
   * Stop the lifecycle monitoring system
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
      console.log('🛑 Contest lifecycle manager stopped')
    }
  }

  /**
   * Check all contests for state transitions
   */
  private async checkAllContests(): Promise<void> {
    try {
      console.log('🔍 Checking contest state transitions...')
      
      const response = await getContests({
        status: ['upcoming', 'active', 'voting'],
        limit: 100 // Check up to 100 active contests
      })

      const contests = response.data
      const now = Timestamp.now()

      for (const contest of contests) {
        await this.checkContestTransition(contest, now)
      }

      console.log(`✅ Checked ${contests.length} contests for transitions`)
    } catch (error) {
      console.error('❌ Error checking contest transitions:', error)
    }
  }

  /**
   * Check if a specific contest needs state transition
   */
  private async checkContestTransition(contest: Contest, now: Timestamp): Promise<void> {
    const newStatus = this.determineNewStatus(contest, now)
    
    if (newStatus && newStatus !== contest.status) {
      console.log(`🔄 Transitioning contest "${contest.title}" from ${contest.status} to ${newStatus}`)
      
      try {
        await this.transitionContest(contest, newStatus)
      } catch (error) {
        console.error(`❌ Failed to transition contest ${contest.id}:`, error)
      }
    }
  }

  /**
   * Determine what status a contest should have based on current time
   */
  private determineNewStatus(contest: Contest, now: Timestamp): ContestStatus | null {
    const nowTime = now.toMillis()
    const submissionStart = contest.submissionStart.toMillis()
    const submissionEnd = contest.submissionEnd.toMillis()
    const votingStart = contest.votingStart.toMillis()
    const votingEnd = contest.votingEnd.toMillis()

    // Upcoming → Active (submission period starts)
    if (contest.status === 'upcoming' && nowTime >= submissionStart) {
      return 'active'
    }

    // Active → Voting (submission period ends, voting starts)
    if (contest.status === 'active' && nowTime >= submissionEnd && nowTime >= votingStart) {
      return 'voting'
    }

    // Voting → Completed (voting period ends)
    if (contest.status === 'voting' && nowTime >= votingEnd) {
      return 'completed'
    }

    return null
  }

  /**
   * Execute contest state transition
   */
  private async transitionContest(contest: Contest, newStatus: ContestStatus): Promise<void> {
    // Update contest status
    await updateContest(contest.id, { status: newStatus })

    // Execute transition-specific actions
    await this.executeTransitionActions(contest, newStatus)

    // Send notifications
    await this.sendTransitionNotifications(contest, newStatus)
  }

  /**
   * Execute actions specific to each transition
   */
  private async executeTransitionActions(contest: Contest, newStatus: ContestStatus): Promise<void> {
    switch (newStatus) {
      case 'active':
        await this.onContestActivated(contest)
        break
      
      case 'voting':
        await this.onVotingStarted(contest)
        break
      
      case 'completed':
        await this.onContestCompleted(contest)
        break
    }
  }

  /**
   * Actions when contest becomes active (submissions open)
   */
  private async onContestActivated(contest: Contest): Promise<void> {
    console.log(`📢 Contest "${contest.title}" is now accepting submissions`)
    
    // Future: Send push notifications to users
    // Future: Update homepage featured contests
    // Future: Create social media posts
  }

  /**
   * Actions when voting period starts
   */
  private async onVotingStarted(contest: Contest): Promise<void> {
    console.log(`🗳️ Voting started for contest "${contest.title}"`)
    
    // Future: Finalize submission list
    // Future: Assign expert judges
    // Future: Send voting notifications
    // Future: Update contest leaderboard
  }

  /**
   * Actions when contest is completed
   */
  private async onContestCompleted(contest: Contest): Promise<void> {
    console.log(`🏆 Contest "${contest.title}" has been completed`)
    
    // Future: Calculate final results
    // Future: Determine winners
    // Future: Distribute prizes
    // Future: Update user achievements
    // Future: Archive contest data
  }

  /**
   * Send notifications for state transitions
   */
  private async sendTransitionNotifications(contest: Contest, newStatus: ContestStatus): Promise<void> {
    const notificationType = this.getNotificationTypeForTransition(newStatus)
    
    if (notificationType) {
      console.log(`📧 Sending ${notificationType} notifications for contest "${contest.title}"`)
      
      // Future: Implement notification system
      // await sendContestNotification(contest.id, notificationType)
    }
  }

  /**
   * Get notification type for status transition
   */
  private getNotificationTypeForTransition(status: ContestStatus): ContestNotificationType | null {
    switch (status) {
      case 'active':
        return 'contest_announced'
      case 'voting':
        return 'voting_started'
      case 'completed':
        return 'results_announced'
      default:
        return null
    }
  }
}

// ===== MANUAL LIFECYCLE OPERATIONS =====

/**
 * Manually transition a contest to a specific status
 */
export async function manuallyTransitionContest(
  contestId: string, 
  newStatus: ContestStatus,
  reason?: string
): Promise<void> {
  try {
    console.log(`🔧 Manually transitioning contest ${contestId} to ${newStatus}`)
    
    await updateContest(contestId, { 
      status: newStatus
      // updatedAt will be handled automatically by the updateContest function
    })

    console.log(`✅ Contest ${contestId} manually transitioned to ${newStatus}`)
    
    if (reason) {
      console.log(`📝 Reason: ${reason}`)
    }
  } catch (error) {
    console.error(`❌ Failed to manually transition contest ${contestId}:`, error)
    throw error
  }
}

/**
 * Force contest to completed status (emergency use)
 */
export async function forceCompleteContest(contestId: string, reason: string): Promise<void> {
  await manuallyTransitionContest(contestId, 'completed', `Force completed: ${reason}`)
}

/**
 * Cancel a contest
 */
export async function cancelContest(contestId: string, reason: string): Promise<void> {
  await manuallyTransitionContest(contestId, 'cancelled', `Cancelled: ${reason}`)
}

// ===== LIFECYCLE UTILITIES =====

/**
 * Get contest phase information
 */
export function getContestPhaseInfo(contest: Contest): {
  currentPhase: string
  nextPhase?: string
  timeUntilNext?: number
  progress: number
} {
  const now = Timestamp.now().toMillis()
  const submissionStart = contest.submissionStart.toMillis()
  const submissionEnd = contest.submissionEnd.toMillis()
  const votingStart = contest.votingStart.toMillis()
  const votingEnd = contest.votingEnd.toMillis()

  if (now < submissionStart) {
    return {
      currentPhase: 'Upcoming',
      nextPhase: 'Submissions Open',
      timeUntilNext: submissionStart - now,
      progress: 0
    }
  }

  if (now >= submissionStart && now < submissionEnd) {
    const totalSubmissionTime = submissionEnd - submissionStart
    const elapsedSubmissionTime = now - submissionStart
    return {
      currentPhase: 'Accepting Submissions',
      nextPhase: 'Voting Period',
      timeUntilNext: submissionEnd - now,
      progress: (elapsedSubmissionTime / totalSubmissionTime) * 100
    }
  }

  if (now >= votingStart && now < votingEnd) {
    const totalVotingTime = votingEnd - votingStart
    const elapsedVotingTime = now - votingStart
    return {
      currentPhase: 'Voting Period',
      nextPhase: 'Results',
      timeUntilNext: votingEnd - now,
      progress: (elapsedVotingTime / totalVotingTime) * 100
    }
  }

  if (now >= votingEnd) {
    return {
      currentPhase: 'Completed',
      progress: 100
    }
  }

  return {
    currentPhase: 'Unknown',
    progress: 0
  }
}

/**
 * Check if contest is in a specific phase
 */
export function isContestInPhase(contest: Contest, phase: 'upcoming' | 'submissions' | 'voting' | 'completed'): boolean {
  const now = Timestamp.now().toMillis()
  const submissionStart = contest.submissionStart.toMillis()
  const submissionEnd = contest.submissionEnd.toMillis()
  const votingStart = contest.votingStart.toMillis()
  const votingEnd = contest.votingEnd.toMillis()

  switch (phase) {
    case 'upcoming':
      return now < submissionStart
    case 'submissions':
      return now >= submissionStart && now < submissionEnd
    case 'voting':
      return now >= votingStart && now < votingEnd
    case 'completed':
      return now >= votingEnd
    default:
      return false
  }
}

/**
 * Get time remaining in current phase
 */
export function getTimeRemainingInPhase(contest: Contest): number | null {
  const now = Timestamp.now().toMillis()
  const submissionStart = contest.submissionStart.toMillis()
  const submissionEnd = contest.submissionEnd.toMillis()
  const votingStart = contest.votingStart.toMillis()
  const votingEnd = contest.votingEnd.toMillis()

  if (now < submissionStart) {
    return submissionStart - now
  }

  if (now >= submissionStart && now < submissionEnd) {
    return submissionEnd - now
  }

  if (now >= votingStart && now < votingEnd) {
    return votingEnd - now
  }

  return null // Contest is completed
}

// ===== INITIALIZATION =====

/**
 * Initialize contest lifecycle management
 */
export function initializeContestLifecycle(): void {
  const manager = ContestLifecycleManager.getInstance()
  manager.start()
  
  // Cleanup on page unload
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
      manager.stop()
    })
  }
}

// Export singleton instance
export const contestLifecycleManager = ContestLifecycleManager.getInstance()
