/**
 * Component Library Extraction System
 *
 * Analyzes Phase 2 refactored components to identify reusable design patterns
 * and extract them into a cohesive design system
 *
 * <AUTHOR> Team
 */

import React from 'react'

/**
 * Component pattern interface
 */
export interface ComponentPattern {
  name: string
  type: 'atom' | 'molecule' | 'organism'
  category: 'form' | 'navigation' | 'feedback' | 'layout' | 'data-display'
  usage: string[]
  props: ComponentProp[]
  variants: ComponentVariant[]
  examples: ComponentExample[]
  extractionPriority: 'high' | 'medium' | 'low'
  estimatedReusability: number // 0-100%
}

/**
 * Component prop interface
 */
export interface ComponentProp {
  name: string
  type: string
  required: boolean
  defaultValue?: any
  description: string
}

/**
 * Component variant interface
 */
export interface ComponentVariant {
  name: string
  description: string
  props: Record<string, any>
}

/**
 * Component example interface
 */
export interface ComponentExample {
  title: string
  description: string
  code: string
  props: Record<string, any>
}

/**
 * Identified reusable patterns from Phase 2 refactored components
 */
export const IDENTIFIED_PATTERNS: ComponentPattern[] = [
  // Form Components
  {
    name: 'FormStep',
    type: 'organism',
    category: 'form',
    usage: [
      'RaffleEntryContainer',
      'ChallengeCreateContainer',
      'BasicInfoStep',
      'ScheduleStep',
      'RequirementsStep',
      'RewardsStep',
      'MediaStep'
    ],
    props: [
      { name: 'title', type: 'string', required: true, description: 'Step title' },
      { name: 'description', type: 'string', required: false, description: 'Step description' },
      { name: 'icon', type: 'React.ReactNode', required: false, description: 'Step icon' },
      { name: 'children', type: 'React.ReactNode', required: true, description: 'Step content' },
      { name: 'isActive', type: 'boolean', required: false, defaultValue: false, description: 'Active state' },
      { name: 'isCompleted', type: 'boolean', required: false, defaultValue: false, description: 'Completed state' },
      { name: 'onNext', type: '() => void', required: false, description: 'Next step handler' },
      { name: 'onPrev', type: '() => void', required: false, description: 'Previous step handler' }
    ],
    variants: [
      { name: 'default', description: 'Standard form step', props: {} },
      { name: 'compact', description: 'Compact form step', props: { compact: true } },
      { name: 'highlighted', description: 'Highlighted form step', props: { highlighted: true } }
    ],
    examples: [
      {
        title: 'Basic Form Step',
        description: 'Standard form step with title and content',
        code: '<FormStep title="Basic Information" icon={<FileText />}>Content here</FormStep>',
        props: { title: 'Basic Information' }
      }
    ],
    extractionPriority: 'high',
    estimatedReusability: 95
  },

  {
    name: 'StepNavigation',
    type: 'molecule',
    category: 'navigation',
    usage: [
      'RaffleEntryContainer',
      'ChallengeCreateContainer'
    ],
    props: [
      { name: 'steps', type: 'Step[]', required: true, description: 'Array of steps' },
      { name: 'currentStep', type: 'number', required: true, description: 'Current step index' },
      { name: 'onStepClick', type: '(index: number) => void', required: false, description: 'Step click handler' },
      { name: 'showProgress', type: 'boolean', required: false, defaultValue: true, description: 'Show progress bar' }
    ],
    variants: [
      { name: 'horizontal', description: 'Horizontal step navigation', props: { orientation: 'horizontal' } },
      { name: 'vertical', description: 'Vertical step navigation', props: { orientation: 'vertical' } },
      { name: 'minimal', description: 'Minimal step navigation', props: { minimal: true } }
    ],
    examples: [
      {
        title: 'Horizontal Navigation',
        description: 'Standard horizontal step navigation',
        code: '<StepNavigation steps={steps} currentStep={0} />',
        props: { steps: [], currentStep: 0 }
      }
    ],
    extractionPriority: 'high',
    estimatedReusability: 90
  },

  // Card Components
  {
    name: 'AnimatedCard',
    type: 'molecule',
    category: 'data-display',
    usage: [
      'EventCard',
      'CampaignCard',
      'EventStatsOverview'
    ],
    props: [
      { name: 'children', type: 'React.ReactNode', required: true, description: 'Card content' },
      { name: 'hover', type: 'boolean', required: false, defaultValue: true, description: 'Enable hover effects' },
      { name: 'animationDelay', type: 'number', required: false, defaultValue: 0, description: 'Animation delay in ms' },
      { name: 'className', type: 'string', required: false, description: 'Additional CSS classes' },
      { name: 'onClick', type: '() => void', required: false, description: 'Click handler' }
    ],
    variants: [
      { name: 'default', description: 'Standard animated card', props: {} },
      { name: 'elevated', description: 'Elevated card with shadow', props: { elevated: true } },
      { name: 'interactive', description: 'Interactive card with hover effects', props: { interactive: true } }
    ],
    examples: [
      {
        title: 'Basic Animated Card',
        description: 'Standard card with fade-in animation',
        code: '<AnimatedCard>Card content</AnimatedCard>',
        props: {}
      }
    ],
    extractionPriority: 'high',
    estimatedReusability: 85
  },

  // Loading Components
  {
    name: 'SkeletonLoader',
    type: 'atom',
    category: 'feedback',
    usage: [
      'EventGridSkeleton',
      'CampaignGrid',
      'EventGrid'
    ],
    props: [
      { name: 'width', type: 'string | number', required: false, defaultValue: '100%', description: 'Skeleton width' },
      { name: 'height', type: 'string | number', required: false, defaultValue: '20px', description: 'Skeleton height' },
      { name: 'variant', type: 'text | circular | rectangular', required: false, defaultValue: 'text', description: 'Skeleton variant' },
      { name: 'animation', type: 'pulse | wave | none', required: false, defaultValue: 'pulse', description: 'Animation type' }
    ],
    variants: [
      { name: 'text', description: 'Text skeleton', props: { variant: 'text' } },
      { name: 'circular', description: 'Circular skeleton', props: { variant: 'circular' } },
      { name: 'rectangular', description: 'Rectangular skeleton', props: { variant: 'rectangular' } }
    ],
    examples: [
      {
        title: 'Text Skeleton',
        description: 'Loading skeleton for text content',
        code: '<SkeletonLoader variant="text" width="200px" />',
        props: { variant: 'text', width: '200px' }
      }
    ],
    extractionPriority: 'medium',
    estimatedReusability: 80
  },

  // Button Components
  {
    name: 'ActionButton',
    type: 'atom',
    category: 'form',
    usage: [
      'EventCard',
      'CampaignCard',
      'RaffleEntryContainer',
      'ChallengeCreateContainer'
    ],
    props: [
      { name: 'children', type: 'React.ReactNode', required: true, description: 'Button content' },
      { name: 'variant', type: 'primary | secondary | danger | ghost', required: false, defaultValue: 'primary', description: 'Button variant' },
      { name: 'size', type: 'sm | md | lg', required: false, defaultValue: 'md', description: 'Button size' },
      { name: 'loading', type: 'boolean', required: false, defaultValue: false, description: 'Loading state' },
      { name: 'disabled', type: 'boolean', required: false, defaultValue: false, description: 'Disabled state' },
      { name: 'onClick', type: '() => void', required: false, description: 'Click handler' },
      { name: 'icon', type: 'React.ReactNode', required: false, description: 'Button icon' }
    ],
    variants: [
      { name: 'primary', description: 'Primary action button', props: { variant: 'primary' } },
      { name: 'secondary', description: 'Secondary action button', props: { variant: 'secondary' } },
      { name: 'danger', description: 'Danger action button', props: { variant: 'danger' } },
      { name: 'ghost', description: 'Ghost action button', props: { variant: 'ghost' } }
    ],
    examples: [
      {
        title: 'Primary Button',
        description: 'Standard primary action button',
        code: '<ActionButton variant="primary">Click me</ActionButton>',
        props: { variant: 'primary' }
      }
    ],
    extractionPriority: 'high',
    estimatedReusability: 95
  },

  // Progress Components
  {
    name: 'ProgressBar',
    type: 'atom',
    category: 'feedback',
    usage: [
      'EventCard',
      'CampaignCard',
      'EventStatsOverview'
    ],
    props: [
      { name: 'value', type: 'number', required: true, description: 'Progress value (0-100)' },
      { name: 'max', type: 'number', required: false, defaultValue: 100, description: 'Maximum value' },
      { name: 'color', type: 'string', required: false, defaultValue: 'purple', description: 'Progress color' },
      { name: 'size', type: 'sm | md | lg', required: false, defaultValue: 'md', description: 'Progress bar size' },
      { name: 'showLabel', type: 'boolean', required: false, defaultValue: false, description: 'Show progress label' },
      { name: 'animated', type: 'boolean', required: false, defaultValue: true, description: 'Enable animation' }
    ],
    variants: [
      { name: 'default', description: 'Standard progress bar', props: {} },
      { name: 'thin', description: 'Thin progress bar', props: { size: 'sm' } },
      { name: 'thick', description: 'Thick progress bar', props: { size: 'lg' } }
    ],
    examples: [
      {
        title: 'Basic Progress Bar',
        description: 'Standard progress bar with 50% completion',
        code: '<ProgressBar value={50} showLabel />',
        props: { value: 50, showLabel: true }
      }
    ],
    extractionPriority: 'medium',
    estimatedReusability: 75
  }
]

/**
 * Component extraction analyzer
 */
export class ComponentExtractor {
  private patterns: ComponentPattern[] = IDENTIFIED_PATTERNS

  /**
   * Analyze component usage across the codebase
   */
  public analyzeUsage(): {
    totalPatterns: number
    highPriorityPatterns: number
    estimatedReusability: number
    extractionRecommendations: string[]
  } {
    const totalPatterns = this.patterns.length
    const highPriorityPatterns = this.patterns.filter(p => p.extractionPriority === 'high').length
    const averageReusability = this.patterns.reduce((sum, p) => sum + p.estimatedReusability, 0) / totalPatterns
    
    const recommendations = this.generateExtractionRecommendations()
    
    return {
      totalPatterns,
      highPriorityPatterns,
      estimatedReusability: Math.round(averageReusability),
      extractionRecommendations: recommendations
    }
  }

  /**
   * Get patterns by priority
   */
  public getPatternsByPriority(priority: 'high' | 'medium' | 'low'): ComponentPattern[] {
    return this.patterns.filter(p => p.extractionPriority === priority)
  }

  /**
   * Get patterns by category
   */
  public getPatternsByCategory(category: ComponentPattern['category']): ComponentPattern[] {
    return this.patterns.filter(p => p.category === category)
  }

  /**
   * Generate extraction recommendations
   */
  private generateExtractionRecommendations(): string[] {
    const recommendations: string[] = []
    
    const highPriorityPatterns = this.getPatternsByPriority('high')
    if (highPriorityPatterns.length > 0) {
      recommendations.push(`Extract ${highPriorityPatterns.length} high-priority components: ${highPriorityPatterns.map(p => p.name).join(', ')}`)
    }
    
    const formComponents = this.getPatternsByCategory('form')
    if (formComponents.length >= 3) {
      recommendations.push('Create dedicated form component library with consistent styling and behavior')
    }
    
    const highReusabilityComponents = this.patterns.filter(p => p.estimatedReusability >= 90)
    if (highReusabilityComponents.length > 0) {
      recommendations.push(`Prioritize components with 90%+ reusability: ${highReusabilityComponents.map(p => p.name).join(', ')}`)
    }
    
    return recommendations
  }

  /**
   * Generate component library structure
   */
  public generateLibraryStructure(): {
    atoms: ComponentPattern[]
    molecules: ComponentPattern[]
    organisms: ComponentPattern[]
    structure: string
  } {
    const atoms = this.patterns.filter(p => p.type === 'atom')
    const molecules = this.patterns.filter(p => p.type === 'molecule')
    const organisms = this.patterns.filter(p => p.type === 'organism')
    
    const structure = `
src/components/ui/
├── atoms/
${atoms.map(a => `│   ├── ${a.name}/`).join('\n')}
├── molecules/
${molecules.map(m => `│   ├── ${m.name}/`).join('\n')}
├── organisms/
${organisms.map(o => `│   ├── ${o.name}/`).join('\n')}
└── index.ts
`
    
    return { atoms, molecules, organisms, structure }
  }

  /**
   * Export component patterns as JSON
   */
  public exportPatterns(): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      totalPatterns: this.patterns.length,
      patterns: this.patterns,
      analysis: this.analyzeUsage()
    }, null, 2)
  }
}

/**
 * Global component extractor instance
 */
export const componentExtractor = new ComponentExtractor()

/**
 * React hook for component extraction analysis
 */
export const useComponentExtraction = () => {
  const [analysis, setAnalysis] = React.useState(componentExtractor.analyzeUsage())
  
  const refreshAnalysis = React.useCallback(() => {
    setAnalysis(componentExtractor.analyzeUsage())
  }, [])
  
  return {
    analysis,
    refreshAnalysis,
    getPatternsByPriority: componentExtractor.getPatternsByPriority.bind(componentExtractor),
    getPatternsByCategory: componentExtractor.getPatternsByCategory.bind(componentExtractor),
    generateLibraryStructure: componentExtractor.generateLibraryStructure.bind(componentExtractor),
    exportPatterns: componentExtractor.exportPatterns.bind(componentExtractor)
  }
}
