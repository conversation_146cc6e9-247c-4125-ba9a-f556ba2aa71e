/**
 * Comprehensive Error Handling System
 * 
 * Advanced error tracking, user feedback, and logging system
 * for community features with detailed error analysis.
 * 
 * Phase 3 Advanced Optimizations - Community System Audit
 * 
 * <AUTHOR> Team
 */

interface ErrorContext {
  userId?: string
  sessionId: string
  userAgent: string
  url: string
  timestamp: Date
  component?: string
  action?: string
  metadata?: Record<string, any>
}

interface ErrorReport {
  id: string
  type: 'javascript' | 'network' | 'validation' | 'security' | 'performance'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  stack?: string
  context: ErrorContext
  fingerprint: string
  count: number
  firstSeen: Date
  lastSeen: Date
  resolved: boolean
}

interface UserFeedback {
  errorId: string
  userId?: string
  feedback: string
  rating: 1 | 2 | 3 | 4 | 5
  timestamp: Date
  contactInfo?: string
}

export class ComprehensiveErrorHandler {
  private static instance: ComprehensiveErrorHandler | null = null
  private sessionId: string
  private errorQueue: ErrorReport[] = []
  private feedbackQueue: UserFeedback[] = []
  private isOnline = navigator.onLine
  private retryInterval: NodeJS.Timeout | null = null

  private constructor() {
    this.sessionId = this.generateSessionId()
    this.setupGlobalErrorHandlers()
    this.setupNetworkMonitoring()
    this.startRetryMechanism()
  }

  static getInstance(): ComprehensiveErrorHandler {
    if (!this.instance) {
      this.instance = new ComprehensiveErrorHandler()
    }
    return this.instance
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleJavaScriptError(event.error, {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    })

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handlePromiseRejection(event.reason, event.promise)
    })

    // React error boundary integration
    window.addEventListener('react-error', (event: any) => {
      this.handleReactError(event.detail.error, event.detail.errorInfo)
    })
  }

  /**
   * Setup network monitoring
   */
  private setupNetworkMonitoring(): void {
    window.addEventListener('online', () => {
      this.isOnline = true
      this.processErrorQueue()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })
  }

  /**
   * Handle JavaScript errors
   */
  private handleJavaScriptError(error: Error, details: any): void {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'javascript',
      severity: this.determineSeverity(error),
      message: error.message || 'Unknown JavaScript error',
      stack: error.stack,
      context: this.getErrorContext(),
      fingerprint: this.generateFingerprint(error),
      count: 1,
      firstSeen: new Date(),
      lastSeen: new Date(),
      resolved: false
    }

    this.reportError(errorReport)
  }

  /**
   * Handle promise rejections
   */
  private handlePromiseRejection(reason: any, promise: Promise<any>): void {
    const error = reason instanceof Error ? reason : new Error(String(reason))
    
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'javascript',
      severity: 'medium',
      message: `Unhandled Promise Rejection: ${error.message}`,
      stack: error.stack,
      context: {
        ...this.getErrorContext(),
        metadata: { promiseRejection: true }
      },
      fingerprint: this.generateFingerprint(error),
      count: 1,
      firstSeen: new Date(),
      lastSeen: new Date(),
      resolved: false
    }

    this.reportError(errorReport)
  }

  /**
   * Handle React errors
   */
  private handleReactError(error: Error, errorInfo: any): void {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'javascript',
      severity: 'high',
      message: `React Error: ${error.message}`,
      stack: error.stack,
      context: {
        ...this.getErrorContext(),
        component: errorInfo.componentStack,
        metadata: { reactError: true, errorInfo }
      },
      fingerprint: this.generateFingerprint(error),
      count: 1,
      firstSeen: new Date(),
      lastSeen: new Date(),
      resolved: false
    }

    this.reportError(errorReport)
  }

  /**
   * Report network errors
   */
  reportNetworkError(
    url: string,
    status: number,
    statusText: string,
    context?: Record<string, any>
  ): void {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'network',
      severity: status >= 500 ? 'high' : 'medium',
      message: `Network Error: ${status} ${statusText} - ${url}`,
      context: {
        ...this.getErrorContext(),
        metadata: { url, status, statusText, ...context }
      },
      fingerprint: `network-${status}-${url}`,
      count: 1,
      firstSeen: new Date(),
      lastSeen: new Date(),
      resolved: false
    }

    this.reportError(errorReport)
  }

  /**
   * Report validation errors
   */
  reportValidationError(
    field: string,
    value: any,
    rule: string,
    context?: Record<string, any>
  ): void {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'validation',
      severity: 'low',
      message: `Validation Error: ${field} failed ${rule} validation`,
      context: {
        ...this.getErrorContext(),
        metadata: { field, value, rule, ...context }
      },
      fingerprint: `validation-${field}-${rule}`,
      count: 1,
      firstSeen: new Date(),
      lastSeen: new Date(),
      resolved: false
    }

    this.reportError(errorReport)
  }

  /**
   * Report security errors
   */
  reportSecurityError(
    type: string,
    details: Record<string, any>
  ): void {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'security',
      severity: 'critical',
      message: `Security Error: ${type}`,
      context: {
        ...this.getErrorContext(),
        metadata: { securityType: type, ...details }
      },
      fingerprint: `security-${type}`,
      count: 1,
      firstSeen: new Date(),
      lastSeen: new Date(),
      resolved: false
    }

    this.reportError(errorReport)
  }

  /**
   * Report performance errors
   */
  reportPerformanceError(
    metric: string,
    value: number,
    threshold: number,
    context?: Record<string, any>
  ): void {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'performance',
      severity: value > threshold * 2 ? 'high' : 'medium',
      message: `Performance Issue: ${metric} (${value}) exceeded threshold (${threshold})`,
      context: {
        ...this.getErrorContext(),
        metadata: { metric, value, threshold, ...context }
      },
      fingerprint: `performance-${metric}`,
      count: 1,
      firstSeen: new Date(),
      lastSeen: new Date(),
      resolved: false
    }

    this.reportError(errorReport)
  }

  /**
   * Collect user feedback for an error
   */
  async collectUserFeedback(
    errorId: string,
    options: {
      showDialog?: boolean
      customMessage?: string
    } = {}
  ): Promise<UserFeedback | null> {
    const { showDialog = true, customMessage } = options

    if (!showDialog) return null

    return new Promise((resolve) => {
      // Create feedback dialog
      const dialog = this.createFeedbackDialog(errorId, customMessage)
      document.body.appendChild(dialog)

      // Handle form submission
      const form = dialog.querySelector('form')
      form?.addEventListener('submit', (e) => {
        e.preventDefault()
        const formData = new FormData(form)
        
        const feedback: UserFeedback = {
          errorId,
          userId: this.getCurrentUserId(),
          feedback: formData.get('feedback') as string,
          rating: parseInt(formData.get('rating') as string) as 1 | 2 | 3 | 4 | 5,
          timestamp: new Date(),
          contactInfo: formData.get('contact') as string || undefined
        }

        this.submitUserFeedback(feedback)
        document.body.removeChild(dialog)
        resolve(feedback)
      })

      // Handle dialog close
      const closeBtn = dialog.querySelector('[data-close]')
      closeBtn?.addEventListener('click', () => {
        document.body.removeChild(dialog)
        resolve(null)
      })
    })
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): {
    totalErrors: number
    errorsByType: Record<string, number>
    errorsBySeverity: Record<string, number>
    topErrors: ErrorReport[]
    recentErrors: ErrorReport[]
  } {
    const stats = {
      totalErrors: this.errorQueue.length,
      errorsByType: {} as Record<string, number>,
      errorsBySeverity: {} as Record<string, number>,
      topErrors: [] as ErrorReport[],
      recentErrors: [] as ErrorReport[]
    }

    // Count by type and severity
    this.errorQueue.forEach(error => {
      stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1
      stats.errorsBySeverity[error.severity] = (stats.errorsBySeverity[error.severity] || 0) + 1
    })

    // Get top errors by count
    stats.topErrors = [...this.errorQueue]
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // Get recent errors
    stats.recentErrors = [...this.errorQueue]
      .sort((a, b) => b.lastSeen.getTime() - a.lastSeen.getTime())
      .slice(0, 10)

    return stats
  }

  // Private helper methods

  private reportError(errorReport: ErrorReport): void {
    // Check for duplicate errors
    const existingError = this.errorQueue.find(e => e.fingerprint === errorReport.fingerprint)
    
    if (existingError) {
      existingError.count++
      existingError.lastSeen = new Date()
    } else {
      this.errorQueue.push(errorReport)
    }

    // Send to error tracking service if online
    if (this.isOnline) {
      this.sendErrorReport(errorReport)
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Report:', errorReport)
    }
  }

  private async sendErrorReport(errorReport: ErrorReport): Promise<void> {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorReport)
      })
    } catch (error) {
      console.warn('Failed to send error report:', error)
    }
  }

  private submitUserFeedback(feedback: UserFeedback): void {
    this.feedbackQueue.push(feedback)
    
    if (this.isOnline) {
      this.sendUserFeedback(feedback)
    }
  }

  private async sendUserFeedback(feedback: UserFeedback): Promise<void> {
    try {
      await fetch('/api/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(feedback)
      })
    } catch (error) {
      console.warn('Failed to send user feedback:', error)
    }
  }

  private processErrorQueue(): void {
    // Send queued errors when back online
    this.errorQueue.forEach(error => this.sendErrorReport(error))
    this.feedbackQueue.forEach(feedback => this.sendUserFeedback(feedback))
  }

  private startRetryMechanism(): void {
    this.retryInterval = setInterval(() => {
      if (this.isOnline && (this.errorQueue.length > 0 || this.feedbackQueue.length > 0)) {
        this.processErrorQueue()
      }
    }, 30000) // Retry every 30 seconds
  }

  private getErrorContext(): ErrorContext {
    return {
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date()
    }
  }

  private getCurrentUserId(): string | undefined {
    // Get user ID from your auth system
    return undefined // Implement based on your auth system
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  private generateErrorId(): string {
    return 'err_' + Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  private generateFingerprint(error: Error): string {
    const message = error.message || 'unknown'
    const stack = error.stack || ''
    const firstStackLine = stack.split('\n')[1] || ''
    return btoa(`${message}-${firstStackLine}`).substring(0, 16)
  }

  private determineSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch')) return 'medium'
    if (message.includes('security') || message.includes('unauthorized')) return 'critical'
    if (message.includes('validation')) return 'low'
    if (message.includes('react') || message.includes('component')) return 'high'
    
    return 'medium'
  }

  private createFeedbackDialog(errorId: string, customMessage?: string): HTMLElement {
    const dialog = document.createElement('div')
    dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'
    dialog.innerHTML = `
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Help us improve</h3>
        <p class="text-gray-600 mb-4">
          ${customMessage || 'We encountered an issue. Your feedback helps us fix it faster.'}
        </p>
        <form>
          <div class="mb-4">
            <label class="block text-sm font-medium mb-2">What happened?</label>
            <textarea name="feedback" class="w-full p-2 border rounded" rows="3" required></textarea>
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium mb-2">How frustrated are you? (1-5)</label>
            <select name="rating" class="w-full p-2 border rounded" required>
              <option value="1">1 - Not at all</option>
              <option value="2">2 - Slightly</option>
              <option value="3">3 - Moderately</option>
              <option value="4">4 - Very</option>
              <option value="5">5 - Extremely</option>
            </select>
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium mb-2">Contact (optional)</label>
            <input type="email" name="contact" class="w-full p-2 border rounded" placeholder="<EMAIL>">
          </div>
          <div class="flex justify-end space-x-2">
            <button type="button" data-close class="px-4 py-2 text-gray-600 hover:text-gray-800">Skip</button>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Send Feedback</button>
          </div>
        </form>
      </div>
    `
    return dialog
  }
}

// Auto-initialize
const errorHandler = ComprehensiveErrorHandler.getInstance()

export default errorHandler
