/**
 * Firebase Health Check Utility
 * 
 * Provides real-time monitoring and health checks for Firebase services
 * to prevent and quickly diagnose connection issues.
 * 
 * <AUTHOR> Team
 */

import { getFirestore, enableNetwork, disableNetwork, doc, getDoc } from 'firebase/firestore'
import { getAuth, onAuthStateChanged } from 'firebase/auth'
import { db, auth } from './firebase-enhanced'

export interface FirebaseHealthStatus {
  firestore: {
    connected: boolean
    lastCheck: Date
    error?: string
  }
  auth: {
    connected: boolean
    lastCheck: Date
    error?: string
  }
  network: {
    online: boolean
    lastCheck: Date
  }
  overall: 'healthy' | 'degraded' | 'unhealthy'
}

class FirebaseHealthMonitor {
  private healthStatus: FirebaseHealthStatus = {
    firestore: { connected: false, lastCheck: new Date() },
    auth: { connected: false, lastCheck: new Date() },
    network: { online: navigator.onLine, lastCheck: new Date() },
    overall: 'unhealthy'
  }

  private listeners: ((status: FirebaseHealthStatus) => void)[] = []
  private checkInterval: NodeJS.Timeout | null = null
  private isMonitoring = false

  /**
   * Start monitoring Firebase health
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    console.log('🔍 Firebase Health Monitor: Starting...')

    // Initial health check
    this.performHealthCheck()

    // Set up periodic health checks
    this.checkInterval = setInterval(() => {
      this.performHealthCheck()
    }, intervalMs)

    // Monitor network status
    window.addEventListener('online', this.handleNetworkOnline.bind(this))
    window.addEventListener('offline', this.handleNetworkOffline.bind(this))

    // Monitor auth state changes
    onAuthStateChanged(auth, (user) => {
      this.healthStatus.auth.connected = true
      this.healthStatus.auth.lastCheck = new Date()
      this.healthStatus.auth.error = undefined
      this.updateOverallHealth()
      this.notifyListeners()
    })
  }

  /**
   * Stop monitoring Firebase health
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    console.log('🛑 Firebase Health Monitor: Stopping...')

    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }

    window.removeEventListener('online', this.handleNetworkOnline.bind(this))
    window.removeEventListener('offline', this.handleNetworkOffline.bind(this))
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<FirebaseHealthStatus> {
    console.log('🔍 Firebase Health Check: Running...')

    // Check network status
    this.healthStatus.network.online = navigator.onLine
    this.healthStatus.network.lastCheck = new Date()

    // Check Firestore connection
    await this.checkFirestoreHealth()

    // Check Auth connection
    await this.checkAuthHealth()

    // Update overall health
    this.updateOverallHealth()

    // Notify listeners
    this.notifyListeners()

    console.log(`📊 Firebase Health Status: ${this.healthStatus.overall}`)
    return { ...this.healthStatus }
  }

  /**
   * Check Firestore connection health
   */
  private async checkFirestoreHealth(): Promise<void> {
    try {
      // Try to enable network first
      await enableNetwork(db)

      // Attempt a simple read operation
      const testDoc = doc(db, 'health-check', 'test')
      await getDoc(testDoc)

      this.healthStatus.firestore.connected = true
      this.healthStatus.firestore.error = undefined
    } catch (error: any) {
      this.healthStatus.firestore.connected = false
      this.healthStatus.firestore.error = error.message

      // Log specific error types
      if (error.code === 'unavailable') {
        console.warn('⚠️ Firestore: Service unavailable')
      } else if (error.code === 'permission-denied') {
        // This is actually OK - means connection works but security rules block access
        this.healthStatus.firestore.connected = true
        this.healthStatus.firestore.error = undefined
      } else {
        console.error('❌ Firestore Health Check Failed:', error)
      }
    } finally {
      this.healthStatus.firestore.lastCheck = new Date()
    }
  }

  /**
   * Check Auth connection health
   */
  private async checkAuthHealth(): Promise<void> {
    try {
      // Check if auth is initialized and working
      const currentUser = auth.currentUser
      this.healthStatus.auth.connected = true
      this.healthStatus.auth.error = undefined
    } catch (error: any) {
      this.healthStatus.auth.connected = false
      this.healthStatus.auth.error = error.message
      console.error('❌ Auth Health Check Failed:', error)
    } finally {
      this.healthStatus.auth.lastCheck = new Date()
    }
  }

  /**
   * Update overall health status
   */
  private updateOverallHealth(): void {
    const { firestore, auth, network } = this.healthStatus

    if (!network.online) {
      this.healthStatus.overall = 'unhealthy'
    } else if (firestore.connected && auth.connected) {
      this.healthStatus.overall = 'healthy'
    } else if (firestore.connected || auth.connected) {
      this.healthStatus.overall = 'degraded'
    } else {
      this.healthStatus.overall = 'unhealthy'
    }
  }

  /**
   * Handle network online event
   */
  private handleNetworkOnline(): void {
    console.log('🟢 Network: Online')
    this.healthStatus.network.online = true
    this.healthStatus.network.lastCheck = new Date()
    
    // Trigger immediate health check when network comes back
    this.performHealthCheck()
  }

  /**
   * Handle network offline event
   */
  private handleNetworkOffline(): void {
    console.log('🔴 Network: Offline')
    this.healthStatus.network.online = false
    this.healthStatus.network.lastCheck = new Date()
    this.healthStatus.overall = 'unhealthy'
    this.notifyListeners()
  }

  /**
   * Subscribe to health status changes
   */
  onHealthChange(callback: (status: FirebaseHealthStatus) => void): () => void {
    this.listeners.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * Notify all listeners of health status changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback({ ...this.healthStatus })
      } catch (error) {
        console.error('Error in health status listener:', error)
      }
    })
  }

  /**
   * Get current health status
   */
  getHealthStatus(): FirebaseHealthStatus {
    return { ...this.healthStatus }
  }

  /**
   * Force reconnection attempt
   */
  async forceReconnect(): Promise<boolean> {
    console.log('🔄 Firebase: Forcing reconnection...')
    
    try {
      // Disable and re-enable network
      await disableNetwork(db)
      await new Promise(resolve => setTimeout(resolve, 1000))
      await enableNetwork(db)
      
      // Perform health check
      await this.performHealthCheck()
      
      return this.healthStatus.overall !== 'unhealthy'
    } catch (error) {
      console.error('❌ Force reconnection failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const firebaseHealthMonitor = new FirebaseHealthMonitor()

// Auto-start monitoring in browser environment
if (typeof window !== 'undefined') {
  // Start monitoring after a short delay to allow Firebase to initialize
  setTimeout(() => {
    firebaseHealthMonitor.startMonitoring()
  }, 2000)
}
