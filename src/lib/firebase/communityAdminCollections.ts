/**
 * Community Admin Collections
 *
 * Firebase Firestore collection definitions and indexing strategies
 * for community admin management features.
 *
 * Features:
 * - Moderation and admin metadata collections
 * - Analytics and reporting collections
 * - Optimized indexes for performance
 * - Audit logging and compliance tracking
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  writeBatch,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from './index';

// ===== COLLECTION CONSTANTS =====

export const COMMUNITY_ADMIN_COLLECTIONS = {
  // Moderation and admin metadata
  DISCUSSION_MODERATION: 'discussionModeration',
  SUBMISSION_MODERATION: 'submissionModeration',
  CHALLENGE_ADMIN: 'challengeAdmin',
  MODERATION_QUEUE: 'moderationQueue',
  MODERATION_ACTIONS: 'moderationActions',
  ADMIN_AUDIT_LOG: 'adminAuditLog',
  
  // Analytics and reporting
  COMMUNITY_ANALYTICS: 'communityAnalytics',
  MODERATION_ANALYTICS: 'moderationAnalytics',
  ADMIN_PERFORMANCE: 'adminPerformance',
  
  // Real-time features
  REAL_TIME_PRESENCE: 'realTimePresence',
  WEBSOCKET_SESSIONS: 'websocketSessions',
  LIVE_CHAT_MODERATION: 'liveChatModeration',
  
  // Social features admin
  USER_CONNECTIONS_ADMIN: 'userConnectionsAdmin',
  MENTORSHIP_ADMIN: 'mentorshipAdmin',
  SOCIAL_REPORTS: 'socialReports'
} as const;

// ===== FIRESTORE INDEXES =====

/**
 * Firestore indexes for optimal querying performance.
 * These should be created in the Firebase console or via Firebase CLI.
 */
export const COMMUNITY_ADMIN_INDEXES = [
  // Moderation queue indexes
  { 
    collection: 'moderationQueue',
    fields: [
      { fieldPath: 'priority', order: 'DESCENDING' },
      { fieldPath: 'createdAt', order: 'ASCENDING' }
    ]
  },
  { 
    collection: 'moderationQueue',
    fields: [
      { fieldPath: 'assignedTo', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  
  // Discussion moderation indexes
  {
    collection: 'discussionModeration',
    fields: [
      { fieldPath: 'moderationStatus', order: 'ASCENDING' },
      { fieldPath: 'escalationLevel', order: 'DESCENDING' },
      { fieldPath: 'moderatedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: 'discussionModeration',
    fields: [
      { fieldPath: 'adminId', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  
  // Submission moderation indexes
  {
    collection: 'submissionModeration',
    fields: [
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'priority', order: 'DESCENDING' },
      { fieldPath: 'submittedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: 'submissionModeration',
    fields: [
      { fieldPath: 'reviewedBy', order: 'ASCENDING' },
      { fieldPath: 'reviewedAt', order: 'DESCENDING' }
    ]
  },
  
  // Admin audit log indexes
  {
    collection: 'adminAuditLog',
    fields: [
      { fieldPath: 'adminId', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  {
    collection: 'adminAuditLog',
    fields: [
      { fieldPath: 'action', order: 'ASCENDING' },
      { fieldPath: 'resourceType', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  
  // Analytics indexes
  {
    collection: 'communityAnalytics',
    fields: [
      { fieldPath: 'date', order: 'DESCENDING' },
      { fieldPath: 'type', order: 'ASCENDING' }
    ]
  },
  {
    collection: 'moderationAnalytics',
    fields: [
      { fieldPath: 'adminId', order: 'ASCENDING' },
      { fieldPath: 'date', order: 'DESCENDING' }
    ]
  }
];

// ===== COLLECTION SCHEMAS =====

/**
 * Discussion Moderation Schema
 */
export interface DiscussionModerationDoc {
  discussionId: string;
  action: 'approve' | 'reject' | 'hide' | 'escalate' | 'warn' | 'ban';
  reason: string;
  adminId: string;
  timestamp: Timestamp;
  previousStatus?: string;
  newStatus: string;
  bulkOperation?: boolean;
  escalationLevel?: 'none' | 'low' | 'medium' | 'high' | 'critical';
  adminNotes?: string;
  autoModerationFlags?: string[];
}

/**
 * Submission Moderation Schema
 */
export interface SubmissionModerationDoc {
  submissionId: string;
  action: 'approve' | 'reject' | 'feature' | 'request_changes' | 'escalate';
  reason: string;
  adminId: string;
  timestamp: Timestamp;
  previousStatus?: string;
  newStatus: string;
  priority: 'low' | 'medium' | 'high';
  qualityScore?: number;
  reviewNotes?: string;
  feedback?: string;
}

/**
 * Moderation Queue Schema
 */
export interface ModerationQueueDoc {
  contentId: string;
  contentType: 'discussion' | 'submission' | 'comment' | 'reply';
  title: string;
  authorId: string;
  priority: 'urgent' | 'high' | 'medium' | 'low';
  flags: Array<{
    type: 'spam' | 'inappropriate' | 'harassment' | 'copyright' | 'other';
    reason: string;
    reportedBy: string;
    reportedAt: Timestamp;
    severity: 'low' | 'medium' | 'high';
  }>;
  createdAt: Timestamp;
  assignedTo?: string;
  assignedAt?: Timestamp;
  status: 'pending' | 'in_review' | 'completed' | 'escalated';
  estimatedReviewTime?: number; // minutes
  autoModerationScore?: number;
}

/**
 * Admin Audit Log Schema
 */
export interface AdminAuditLogDoc {
  adminId: string;
  action: string;
  resourceType: 'discussion' | 'submission' | 'challenge' | 'user' | 'system';
  resourceId?: string;
  resourceIds?: string[];
  details: Record<string, any>;
  timestamp: Timestamp;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  businessJustification?: string;
}

/**
 * Community Analytics Schema
 */
export interface CommunityAnalyticsDoc {
  date: Timestamp;
  type: 'daily' | 'weekly' | 'monthly';
  discussions: {
    total: number;
    created: number;
    moderated: number;
    escalated: number;
    averageResponseTime: number;
  };
  submissions: {
    total: number;
    submitted: number;
    approved: number;
    rejected: number;
    featured: number;
    averageQualityScore: number;
  };
  moderation: {
    queueSize: number;
    actionsPerformed: number;
    averageProcessingTime: number;
    escalationRate: number;
  };
  engagement: {
    activeUsers: number;
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    engagementRate: number;
  };
}

// ===== HELPER FUNCTIONS =====

/**
 * Create a moderation queue item
 */
export const createModerationQueueItem = async (item: Omit<ModerationQueueDoc, 'createdAt'>) => {
  try {
    const docRef = await addDoc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE), {
      ...item,
      createdAt: serverTimestamp()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating moderation queue item:', error);
    throw error;
  }
};

/**
 * Log admin action for audit trail
 */
export const logAdminAction = async (action: Omit<AdminAuditLogDoc, 'timestamp'>) => {
  try {
    const docRef = await addDoc(collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG), {
      ...action,
      timestamp: serverTimestamp()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error logging admin action:', error);
    throw error;
  }
};

/**
 * Get moderation queue items by priority
 */
export const getModerationQueueByPriority = async (priority: 'urgent' | 'high' | 'medium' | 'low') => {
  try {
    const q = query(
      collection(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE),
      where('priority', '==', priority),
      where('status', '==', 'pending'),
      orderBy('createdAt', 'asc'),
      limit(50)
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as (ModerationQueueDoc & { id: string })[];
  } catch (error) {
    console.error('Error fetching moderation queue:', error);
    throw error;
  }
};

/**
 * Assign moderation queue item to admin
 */
export const assignModerationItem = async (itemId: string, adminId: string) => {
  try {
    const itemRef = doc(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE, itemId);
    await updateDoc(itemRef, {
      assignedTo: adminId,
      assignedAt: serverTimestamp(),
      status: 'in_review'
    });
  } catch (error) {
    console.error('Error assigning moderation item:', error);
    throw error;
  }
};

/**
 * Complete moderation queue item
 */
export const completeModerationItem = async (itemId: string, adminId: string) => {
  try {
    const itemRef = doc(db, COMMUNITY_ADMIN_COLLECTIONS.MODERATION_QUEUE, itemId);
    await updateDoc(itemRef, {
      status: 'completed',
      completedAt: serverTimestamp(),
      completedBy: adminId
    });
  } catch (error) {
    console.error('Error completing moderation item:', error);
    throw error;
  }
};

/**
 * Get admin performance metrics
 */
export const getAdminPerformanceMetrics = async (adminId: string, dateRange: { start: Date; end: Date }) => {
  try {
    const q = query(
      collection(db, COMMUNITY_ADMIN_COLLECTIONS.ADMIN_AUDIT_LOG),
      where('adminId', '==', adminId),
      where('timestamp', '>=', Timestamp.fromDate(dateRange.start)),
      where('timestamp', '<=', Timestamp.fromDate(dateRange.end)),
      orderBy('timestamp', 'desc')
    );
    
    const snapshot = await getDocs(q);
    const actions = snapshot.docs.map(doc => doc.data()) as AdminAuditLogDoc[];
    
    // Calculate performance metrics
    const totalActions = actions.length;
    const moderationActions = actions.filter(a => a.action.includes('moderate')).length;
    const escalations = actions.filter(a => a.action.includes('escalate')).length;
    
    return {
      totalActions,
      moderationActions,
      escalations,
      averageActionsPerDay: totalActions / Math.max(1, Math.ceil((dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24))),
      actionBreakdown: actions.reduce((acc, action) => {
        acc[action.action] = (acc[action.action] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
  } catch (error) {
    console.error('Error fetching admin performance metrics:', error);
    throw error;
  }
};
