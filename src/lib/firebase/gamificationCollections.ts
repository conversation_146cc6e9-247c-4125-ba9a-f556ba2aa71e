/**
 * Firebase Collections Configuration
 * 
 * Centralized collection names and index definitions for gamification system.
 * Ensures consistency and provides foundation for security rules.
 * 
 * <AUTHOR> Team - Phase 1 Security Improvements
 * @version 2.0.0
 */

// ===== COLLECTION NAMES =====

export const collections = {
  // Core collections
  users: 'users',
  profiles: 'profiles',
  
  // Gamification collections  
  pointTransactions: 'pointTransactions',
  userActivities: 'userActivities',
  achievements: 'achievements',
  userAchievements: 'userAchievements',
  rewards: 'rewards',
  rewardPurchases: 'rewardPurchases',
  
  // Challenge system collections
  challenges: 'challenges',
  challengeParticipations: 'challengeParticipations',
  challengeTeams: 'challengeTeams',
  challengeSubmissions: 'challengeSubmissions',
  submissions: 'submissions',
  
  // System collections
  admins: 'admins',
  auditLogs: 'auditLogs',
  tiers: 'tiers',
  tierPromotions: 'tierPromotions',

  // Level system collections
  userLevels: 'userLevels',
  xpTransactions: 'xpTransactions',
  levelRewards: 'levelRewards',
  
  // Analytics collections
  leaderboards: 'leaderboards',
  gamificationAnalytics: 'gamificationAnalytics',
  userEngagementMetrics: 'userEngagementMetrics',
  analyticsReports: 'analyticsReports',
  systemMetrics: 'systemMetrics',
  userSessions: 'userSessions',
  
  // Segmentation and A/B Testing collections
  userSegments: 'userSegments',
  abTests: 'abTests',
  testVariants: 'testVariants',
  segmentMemberships: 'segmentMemberships'
} as const

// ===== FIRESTORE INDEXES =====

export const requiredIndexes = [
  // Point transactions for leaderboards and history
  {
    collection: collections.pointTransactions,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.pointTransactions,
    fields: [
      { fieldPath: 'type', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.pointTransactions,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'type', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },

  // User achievements tracking
  {
    collection: collections.userAchievements,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'unlockedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.userAchievements,
    fields: [
      { fieldPath: 'achievementId', order: 'ASCENDING' },
      { fieldPath: 'isCompleted', order: 'ASCENDING' },
      { fieldPath: 'progress', order: 'DESCENDING' }
    ]
  },

  // Reward purchases
  {
    collection: collections.rewardPurchases,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'purchaseDate', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.rewardPurchases,
    fields: [
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'purchaseDate', order: 'DESCENDING' }
    ]
  },

  // User activities for analytics
  {
    collection: collections.userActivities,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'type', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.userActivities,
    fields: [
      { fieldPath: 'type', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },

  // Audit logs for security
  {
    collection: collections.auditLogs,
    fields: [
      { fieldPath: 'operation', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.auditLogs,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.auditLogs,
    fields: [
      { fieldPath: 'adminId', order: 'ASCENDING' },
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },

  // Tier management
  {
    collection: collections.tiers,
    fields: [
      { fieldPath: 'isActive', order: 'ASCENDING' },
      { fieldPath: 'order', order: 'ASCENDING' }
    ]
  },

  // Challenge system - main challenges
  {
    collection: collections.challenges,
    fields: [
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'startDate', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.challenges,
    fields: [
      { fieldPath: 'category', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'startDate', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.challenges,
    fields: [
      { fieldPath: 'isActive', order: 'ASCENDING' },
      { fieldPath: 'sortOrder', order: 'ASCENDING' }
    ]
  },

  // Challenge participations
  {
    collection: collections.challengeParticipations,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'joinedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.challengeParticipations,
    fields: [
      { fieldPath: 'challengeId', order: 'ASCENDING' },
      { fieldPath: 'userId', order: 'ASCENDING' }
    ]
  },
  {
    collection: collections.challengeParticipations,
    fields: [
      { fieldPath: 'challengeId', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'progress', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.challengeParticipations,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'lastActiveAt', order: 'DESCENDING' }
    ]
  },

  // Challenge teams
  {
    collection: collections.challengeTeams,
    fields: [
      { fieldPath: 'challengeId', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.challengeTeams,
    fields: [
      { fieldPath: 'captainId', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' }
    ]
  },

  // Challenge submissions
  {
    collection: collections.challengeSubmissions,
    fields: [
      { fieldPath: 'challengeId', order: 'ASCENDING' },
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'submittedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.challengeSubmissions,
    fields: [
      { fieldPath: 'challengeId', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'submittedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.challengeSubmissions,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'submittedAt', order: 'DESCENDING' }
    ]
  },

  // Analytics collections
  {
    collection: collections.userEngagementMetrics,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'calculatedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.userEngagementMetrics,
    fields: [
      { fieldPath: 'engagementScore', order: 'DESCENDING' },
      { fieldPath: 'calculatedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.userEngagementMetrics,
    fields: [
      { fieldPath: 'churnRisk', order: 'ASCENDING' },
      { fieldPath: 'engagementScore', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.analyticsReports,
    fields: [
      { fieldPath: 'type', order: 'ASCENDING' },
      { fieldPath: 'generatedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.systemMetrics,
    fields: [
      { fieldPath: 'timestamp', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.userSessions,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'startTime', order: 'DESCENDING' }
    ]
  },

  // Segmentation and A/B Testing indexes
  {
    collection: collections.userSegments,
    fields: [
      { fieldPath: 'isActive', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.userSegments,
    fields: [
      { fieldPath: 'userCount', order: 'DESCENDING' },
      { fieldPath: 'isActive', order: 'ASCENDING' }
    ]
  },
  {
    collection: collections.abTests,
    fields: [
      { fieldPath: 'status', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.abTests,
    fields: [
      { fieldPath: 'feature', order: 'ASCENDING' },
      { fieldPath: 'status', order: 'ASCENDING' }
    ]
  },
  {
    collection: collections.testVariants,
    fields: [
      { fieldPath: 'testId', order: 'ASCENDING' },
      { fieldPath: 'userId', order: 'ASCENDING' }
    ]
  },
  {
    collection: collections.testVariants,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'assignedAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.segmentMemberships,
    fields: [
      { fieldPath: 'segmentId', order: 'ASCENDING' },
      { fieldPath: 'userId', order: 'ASCENDING' }
    ]
  },
  {
    collection: collections.segmentMemberships,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'joinedAt', order: 'DESCENDING' }
    ]
  },

  // Level system indexes
  {
    collection: collections.userLevels,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' }
    ]
  },
  {
    collection: collections.userLevels,
    fields: [
      { fieldPath: 'currentLevel', order: 'DESCENDING' },
      { fieldPath: 'totalXP', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.userLevels,
    fields: [
      { fieldPath: 'levelTier', order: 'ASCENDING' },
      { fieldPath: 'currentLevel', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.xpTransactions,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.xpTransactions,
    fields: [
      { fieldPath: 'source', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.xpTransactions,
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'source', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collection: collections.levelRewards,
    fields: [
      { fieldPath: 'level', order: 'ASCENDING' },
      { fieldPath: 'type', order: 'ASCENDING' }
    ]
  },
  {
    collection: collections.levelRewards,
    fields: [
      { fieldPath: 'isExclusive', order: 'ASCENDING' },
      { fieldPath: 'level', order: 'ASCENDING' }
    ]
  }
]

// ===== SECURITY RULES HELPERS =====

export const securityRuleHelpers = {
  // Check if user owns the document
  isOwner: (userId: string) => `resource.data.userId == "${userId}"`,
  
  // Check if user is admin
  isAdmin: (userId: string) => `exists(/databases/$(database)/documents/admins/${userId})`,
  
  // Check if admin has permission
  hasPermission: (adminId: string, permission: string) => 
    `get(/databases/$(database)/documents/admins/${adminId}).data.permissions.hasAny(["${permission}", "gamification.*"])`,
  
  // Validate point transaction amounts
  isValidPointAmount: (amount: number) => `${amount} > 0 && ${amount} <= 10000`,
  
  // Check if reward is in stock
  hasStock: () => 'resource.data.stock > 0',
  
  // Validate timestamp is recent (within 5 minutes)
  isRecentTimestamp: () => 'request.time - resource.data.timestamp < duration.fromSeconds(300)'
}

// ===== COLLECTION VALIDATION SCHEMAS =====

export const validationSchemas = {
  pointTransaction: {
    required: ['userId', 'type', 'amount', 'source', 'description', 'timestamp'],
    types: {
      userId: 'string',
      type: ['points_earned', 'points_spent'],
      amount: 'number',
      source: 'string',
      description: 'string',
      timestamp: 'timestamp',
      balanceBefore: 'number',
      balanceAfter: 'number',
      metadata: 'object',
      auditTrail: 'array'
    },
    constraints: {
      amount: { min: -100000, max: 100000 },
      source: { 
        enum: [
          'daily_login', 'challenge_completion', 'achievement_unlock',
          'reward_purchase', 'admin_adjustment', 'profile_completion',
          'social_share', 'referral_bonus', 'special_event',
          // Phase 1 raffle sources
          'raffle_entry', 'raffle_win', 'raffle_payment', 'social_requirement',
          'instagram_verification', 'discord_verification', 'reddit_verification',
          'viral_engagement', 'referral_raffle'
        ]
      }
    }
  },

  rewardPurchase: {
    required: ['userId', 'rewardId', 'pointsCost', 'status', 'purchaseDate'],
    types: {
      userId: 'string',
      rewardId: 'string',
      rewardName: 'string',
      pointsCost: 'number',
      status: ['pending', 'processing', 'fulfilled', 'cancelled'],
      purchaseDate: 'timestamp',
      fulfillmentDate: 'timestamp',
      metadata: 'object',
      auditTrail: 'array'
    },
    constraints: {
      pointsCost: { min: 1, max: 1000000 }
    }
  },

  userAchievement: {
    required: ['userId', 'achievementId', 'progress', 'isCompleted'],
    types: {
      userId: 'string',
      achievementId: 'string',
      unlockedAt: 'timestamp',
      progress: 'number',
      isCompleted: 'boolean',
      progressData: 'object', // For tracking specific achievement progress
      lastUpdated: 'timestamp',
      phase: 'number', // Track which phase the achievement belongs to
      triggerCount: 'number' // How many times the trigger has fired
    },
    constraints: {
      progress: { min: 0, max: 100 },
      phase: { min: 0, max: 10 },
      triggerCount: { min: 0 }
    }
  },

  achievement: {
    required: ['id', 'title', 'description', 'icon', 'category', 'rarity', 'requirements', 'rewards'],
    types: {
      id: 'string',
      title: 'string',
      description: 'string',
      icon: 'string',
      category: [
        'raffle_entry', 'raffle_success', 'raffle_social', 'raffle_timing', 
        'raffle_engagement', 'social_engagement', 'profile', 'shopping',
        'onboarding', 'milestone', 'social' // Legacy categories
      ],
      rarity: ['common', 'uncommon', 'rare', 'epic', 'legendary'],
      requirements: 'array',
      rewards: 'object',
      gamificationTriggers: 'array',
      isActive: 'boolean',
      progressChain: 'array',
      prerequisites: 'array',
      metadata: 'object'
    }
  },

  auditLog: {
    required: ['operation', 'userId', 'timestamp'],
    types: {
      operation: 'string',
      userId: 'string',
      adminId: 'string',
      details: 'object',
      timestamp: 'timestamp',
      ipAddress: 'string',
      userAgent: 'string'
    }
  },

  // Level system schemas
  userLevel: {
    required: ['userId', 'currentLevel', 'currentXP', 'totalXP', 'levelName', 'levelTier'],
    types: {
      userId: 'string',
      currentLevel: 'number',
      currentXP: 'number',
      totalXP: 'number',
      levelName: 'string',
      levelTier: ['novice', 'intermediate', 'advanced', 'expert'],
      nextLevelXP: 'number',
      progressToNext: 'number',
      levelUpAt: 'timestamp',
      milestoneRewards: 'array',
      createdAt: 'timestamp',
      updatedAt: 'timestamp'
    },
    constraints: {
      currentLevel: { min: 1, max: 50 },
      currentXP: { min: 0 },
      totalXP: { min: 0 },
      progressToNext: { min: 0, max: 100 }
    }
  },

  xpTransaction: {
    required: ['userId', 'amount', 'source', 'description', 'createdAt'],
    types: {
      userId: 'string',
      amount: 'number',
      source: ['purchase', 'activity', 'bonus', 'event', 'manual'],
      sourceId: 'string',
      multiplier: 'number',
      description: 'string',
      metadata: 'object',
      createdAt: 'timestamp'
    },
    constraints: {
      amount: { min: -10000, max: 10000 },
      multiplier: { min: 0.1, max: 10.0 }
    }
  },

  levelReward: {
    required: ['level', 'type', 'name', 'description', 'isExclusive'],
    types: {
      level: 'number',
      type: ['badge', 'points', 'discount', 'keycap', 'access', 'title'],
      name: 'string',
      description: 'string',
      value: 'any',
      isExclusive: 'boolean',
      expiresAt: 'timestamp',
      claimableUntil: 'timestamp',
      createdAt: 'timestamp'
    },
    constraints: {
      level: { min: 1, max: 50 }
    }
  }
}

// ===== DATABASE INITIALIZATION =====

export const initializationQueries = {
  // Create default tiers
  createDefaultTiers: [
    {
      name: 'Bronze',
      slug: 'bronze',
      minPoints: 0,
      maxPoints: 999,
      color: '#CD7F32',
      bgColor: '#CD7F32',
      borderColor: '#A0522D',
      icon: '🥉',
      description: 'Welcome tier for new members',
      benefits: [
        {
          id: '1',
          type: 'access',
          title: 'Basic Access',
          description: 'Access to community features',
          isActive: true
        }
      ],
      isActive: true,
      order: 1
    },
    {
      name: 'Silver',
      slug: 'silver',
      minPoints: 1000,
      maxPoints: 4999,
      color: '#C0C0C0',
      bgColor: '#C0C0C0',
      borderColor: '#A9A9A9',
      icon: '🥈',
      description: 'Intermediate tier with enhanced benefits',
      benefits: [
        {
          id: '1',
          type: 'discount',
          title: '5% Bonus Points',
          description: 'Earn 5% more points on all activities',
          value: 5,
          isActive: true
        }
      ],
      isActive: true,
      order: 2
    },
    {
      name: 'Gold',
      slug: 'gold',
      minPoints: 5000,
      maxPoints: 19999,
      color: '#FFD700',
      bgColor: '#FFD700',
      borderColor: '#FFA500',
      icon: '🥇',
      description: 'Premium tier with exclusive features',
      benefits: [
        {
          id: '1',
          type: 'discount',
          title: '10% Bonus Points',
          description: 'Earn 10% more points on all activities',
          value: 10,
          isActive: true
        },
        {
          id: '2',
          type: 'access',
          title: 'Beta Access',
          description: 'Early access to new features',
          isActive: true
        }
      ],
      isActive: true,
      order: 3
    },
    {
      name: 'Platinum',
      slug: 'platinum',
      minPoints: 20000,
      maxPoints: null,
      color: '#E5E4E2',
      bgColor: '#E5E4E2',
      borderColor: '#C0C0C0',
      icon: '💎',
      description: 'Elite tier with maximum benefits',
      benefits: [
        {
          id: '1',
          type: 'discount',
          title: '15% Bonus Points',
          description: 'Earn 15% more points on all activities',
          value: 15,
          isActive: true
        },
        {
          id: '2',
          type: 'access',
          title: 'VIP Community',
          description: 'Access to exclusive community areas',
          isActive: true
        },
        {
          id: '3',
          type: 'support',
          title: 'Priority Support',
          description: 'Priority customer support',
          isActive: true
        }
      ],
      isActive: true,
      order: 4
    }
  ],

  // Create default achievements (Legacy - Phase 0)
  createDefaultAchievements: [
    {
      title: 'First Steps',
      description: 'Complete your profile',
      icon: '👋',
      category: 'onboarding',
      rarity: 'common',
      requirements: [
        {
          type: 'profile_completion',
          target: 100
        }
      ],
      rewards: { points: 50 },
      isActive: true,
      metadata: { version: '0.1.0', phase: 0, tags: ['legacy', 'onboarding'] }
    },
    {
      title: 'Point Collector',
      description: 'Earn your first 1000 points',
      icon: '💰',
      category: 'milestone',
      rarity: 'common',
      requirements: [
        {
          type: 'total_points',
          target: 1000
        }
      ],
      rewards: { points: 100 },
      isActive: true,
      metadata: { version: '0.1.0', phase: 0, tags: ['legacy', 'milestone'] }
    },
    {
      title: 'Social Butterfly',
      description: 'Share content 10 times',
      icon: '🦋',
      category: 'social',
      rarity: 'rare',
      requirements: [
        {
          type: 'social_shares',
          target: 10
        }
      ],
      rewards: { points: 200 },
      isActive: true,
      metadata: { version: '0.1.0', phase: 0, tags: ['legacy', 'social'] }
    }
  ]
}

// ===== COLLECTION REFERENCES =====

import { collection } from 'firebase/firestore'
import { db } from '../firebase'

// Export actual collection references for use in queries
export const profilesCollection = collection(db, collections.profiles)
export const pointTransactionsCollection = collection(db, collections.pointTransactions)
export const userActivitiesCollection = collection(db, collections.userActivities)
export const achievementsCollection = collection(db, collections.achievements)
export const userAchievementsCollection = collection(db, collections.userAchievements)
export const rewardsCollection = collection(db, collections.rewards)
export const rewardPurchasesCollection = collection(db, collections.rewardPurchases)
export const challengesCollection = collection(db, collections.challenges)
export const challengeParticipationsCollection = collection(db, collections.challengeParticipations)
export const challengeTeamsCollection = collection(db, collections.challengeTeams)
export const challengeSubmissionsCollection = collection(db, collections.challengeSubmissions)
export const adminsCollection = collection(db, collections.admins)
export const auditLogsCollection = collection(db, collections.auditLogs)
export const tiersCollection = collection(db, collections.tiers)
export const tierPromotionsCollection = collection(db, collections.tierPromotions)
export const leaderboardsCollection = collection(db, collections.leaderboards)
export const gamificationAnalyticsCollection = collection(db, collections.gamificationAnalytics)
export const userEngagementMetricsCollection = collection(db, collections.userEngagementMetrics)
export const analyticsReportsCollection = collection(db, collections.analyticsReports)
export const systemMetricsCollection = collection(db, collections.systemMetrics)
export const userSessionsCollection = collection(db, collections.userSessions)

// Segmentation and A/B Testing Collections
export const userSegmentsCollection = collection(db, collections.userSegments)
export const abTestsCollection = collection(db, collections.abTests)
export const testVariantsCollection = collection(db, collections.testVariants)
export const segmentMembershipsCollection = collection(db, collections.segmentMemberships)

// Level System Collections
export const userLevelsCollection = collection(db, collections.userLevels)
export const xpTransactionsCollection = collection(db, collections.xpTransactions)
export const levelRewardsCollection = collection(db, collections.levelRewards)

export default collections