/**
 * Comprehensive Test Suite for Gamification System
 * 
 * Tests all phases of the gamification system including achievements,
 * tracking, evolution, dynamic generation, and integration.
 * 
 * <AUTHOR> Team - Gamification Testing
 * @version 3.0.0
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { 
  GamificationSystem,
  defaultGamificationConfig,
  type GamificationConfig,
  type UserGamificationProfile 
} from '../gamificationSystem'
import { phase1Achievements, validatePhase1Achievement } from '../phase1-achievements'
import { phase2Achievements, validatePhase2Achievement } from '../phase2-achievements'
import { phase3Achievements, validatePhase3Achievement } from '../phase3-achievements'
import { DynamicAchievementGenerator } from '../dynamicAchievements'
import { AchievementEvolutionManager } from '../achievementEvolution'

// ===== MOCK SETUP =====

// Mock Firebase
jest.mock('../firebase', () => ({
  db: {
    collection: jest.fn(),
    doc: jest.fn()
  }
}))

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  getDoc: jest.fn(),
  getDocs: jest.fn(),
  addDoc: jest.fn(),
  updateDoc: jest.fn(),
  serverTimestamp: jest.fn(() => ({ toDate: () => new Date() })),
  Timestamp: {
    fromDate: jest.fn((date: Date) => ({ toDate: () => date }))
  }
}))

// ===== TEST SUITES =====

describe('Gamification System Integration Tests', () => {
  let gamificationSystem: GamificationSystem
  const testConfig: GamificationConfig = {
    ...defaultGamificationConfig,
    settings: {
      autoEvolution: true,
      dynamicGenerationRate: 20,
      seasonalBonuses: true,
      socialSharing: true
    }
  }

  beforeEach(() => {
    // Reset system state
    gamificationSystem = GamificationSystem.getInstance(testConfig)
    
    // Clear any previous state
    jest.clearAllMocks()
  })

  afterEach(() => {
    // Clean up
    jest.resetAllMocks()
  })

  describe('System Initialization', () => {
    test('should initialize with default configuration', async () => {
      expect(() => {
        GamificationSystem.getInstance()
      }).not.toThrow()
    })

    test('should initialize all three phases', async () => {
      const config: GamificationConfig = {
        phases: { phase1: true, phase2: true, phase3: true },
        features: {
          notifications: true,
          socialProof: true,
          leaderboards: true,
          dynamicGeneration: true,
          evolutionSystem: true,
          seasonalEvents: true
        },
        settings: {
          autoEvolution: true,
          dynamicGenerationRate: 10,
          seasonalBonuses: true,
          socialSharing: true
        }
      }

      await expect(GamificationSystem.initialize(config)).resolves.not.toThrow()
    })

    test('should validate system integrity on initialization', async () => {
      const config = { ...testConfig }
      config.phases.phase1 = true
      
      await expect(GamificationSystem.initialize(config)).resolves.not.toThrow()
    })

    test('should handle initialization errors gracefully', async () => {
      // Mock an initialization failure
      const invalidConfig = {
        ...testConfig,
        phases: { phase1: false, phase2: false, phase3: false }
      } as GamificationConfig

      await expect(GamificationSystem.initialize(invalidConfig)).resolves.not.toThrow()
    })
  })

  describe('Achievement Validation', () => {
    test('should validate all Phase 1 achievements', () => {
      phase1Achievements.forEach(achievement => {
        expect(validatePhase1Achievement(achievement)).toBe(true)
      })
    })

    test('should validate all Phase 2 achievements', () => {
      phase2Achievements.forEach(achievement => {
        expect(validatePhase2Achievement(achievement)).toBe(true)
      })
    })

    test('should validate all Phase 3 achievements', () => {
      phase3Achievements.forEach(achievement => {
        expect(validatePhase3Achievement(achievement)).toBe(true)
      })
    })

    test('should detect invalid achievements', () => {
      const invalidAchievement = {
        id: 'test_invalid',
        // Missing required fields
        description: 'Invalid achievement for testing'
      } as any

      expect(validatePhase1Achievement(invalidAchievement)).toBe(false)
    })
  })

  describe('Activity Tracking', () => {
    test('should track Phase 1 activities', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const result = await GamificationSystem.trackActivity(
        'test-user-1',
        'raffle_entry_submitted',
        { raffleId: 'test-raffle-1' }
      )

      expect(result).toBeDefined()
      expect(result.phase1Results).toBeDefined()
      expect(Array.isArray(result.phase1Results)).toBe(true)
    })

    test('should track Phase 2 activities when enabled', async () => {
      const config = { ...testConfig }
      config.phases.phase2 = true
      
      await GamificationSystem.initialize(config)
      
      const result = await GamificationSystem.trackActivity(
        'test-user-2',
        'daily_login',
        { loginTime: new Date().toISOString() }
      )

      expect(result).toBeDefined()
      expect(result.phase2Results).toBeDefined()
    })

    test('should track Phase 3 activities when enabled', async () => {
      const config = { ...testConfig }
      config.phases.phase3 = true
      
      await GamificationSystem.initialize(config)
      
      const result = await GamificationSystem.trackActivity(
        'test-user-3',
        'seasonal_activity',
        { season: 'winter', activity: 'holiday_participation' }
      )

      expect(result).toBeDefined()
      expect(result.phase3Results).toBeDefined()
    })

    test('should handle invalid triggers gracefully', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const result = await GamificationSystem.trackActivity(
        'test-user-4',
        'invalid_trigger_name',
        {}
      )

      expect(result).toBeDefined()
      expect(result.totalPointsEarned).toBe(0)
    })

    test('should determine correct phases for triggers', async () => {
      await GamificationSystem.initialize(testConfig)
      
      // Test phase 1 trigger
      const phase1Result = await GamificationSystem.trackActivity(
        'test-user-5',
        'raffle_entry_submitted',
        {},
        { phase: 1 }
      )
      
      expect(phase1Result.phase1Results).toBeDefined()
    })
  })

  describe('User Profile Management', () => {
    test('should create user profile for new user', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const profile = await GamificationSystem.getUserProfile('new-user-1')
      
      expect(profile).toBeDefined()
      expect(profile.userId).toBe('new-user-1')
      expect(profile.totalPoints).toBe(0)
      expect(profile.tier).toBe('bronze')
    })

    test('should update user profile after activity', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const userId = 'test-user-update'
      
      // Track some activity
      await GamificationSystem.trackActivity(userId, 'raffle_entry_submitted', {})
      
      const profile = await GamificationSystem.getUserProfile(userId)
      expect(profile.lastActivity).toBeDefined()
    })

    test('should calculate correct user tier', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const profile = await GamificationSystem.getUserProfile('tier-test-user')
      
      // Should start at bronze
      expect(profile.tier).toBe('bronze')
    })
  })

  describe('System Statistics', () => {
    test('should provide comprehensive system stats', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const stats = await GamificationSystem.getSystemStats()
      
      expect(stats).toBeDefined()
      expect(stats.achievements).toBeDefined()
      expect(stats.achievements.total).toBeGreaterThan(0)
      expect(stats.users).toBeDefined()
      expect(stats.activity).toBeDefined()
      expect(stats.system).toBeDefined()
      expect(stats.system.version).toBe('3.0.0')
    })

    test('should track user statistics correctly', async () => {
      await GamificationSystem.initialize(testConfig)
      
      // Add some test users
      await GamificationSystem.getUserProfile('stats-user-1')
      await GamificationSystem.getUserProfile('stats-user-2')
      
      const stats = await GamificationSystem.getSystemStats()
      expect(stats.users.total).toBeGreaterThanOrEqual(2)
    })
  })

  describe('Error Handling', () => {
    test('should handle uninitialized system gracefully', async () => {
      // Don't initialize the system
      const result = await GamificationSystem.trackActivity(
        'error-test-user',
        'raffle_entry_submitted',
        {}
      )

      // Should return empty results, not throw
      expect(result.totalPointsEarned).toBe(0)
      expect(result.newAchievements).toEqual([])
    })

    test('should handle invalid user IDs', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const result = await GamificationSystem.trackActivity(
        '', // Empty user ID
        'raffle_entry_submitted',
        {}
      )

      expect(result).toBeDefined()
    })

    test('should handle malformed activity data', async () => {
      await GamificationSystem.initialize(testConfig)
      
      const result = await GamificationSystem.trackActivity(
        'malformed-test-user',
        'raffle_entry_submitted',
        null as any // Invalid data
      )

      expect(result).toBeDefined()
    })
  })
})

describe('Dynamic Achievement Generation Tests', () => {
  let generator: DynamicAchievementGenerator

  beforeEach(() => {
    generator = new DynamicAchievementGenerator()
  })

  describe('Personalized Generation', () => {
    test('should generate personalized achievements', async () => {
      const achievements = await generator.generatePersonalized('test-user-dynamic', 3)
      
      expect(Array.isArray(achievements)).toBe(true)
      expect(achievements.length).toBeLessThanOrEqual(3)
    })

    test('should handle invalid user IDs in generation', async () => {
      const achievements = await generator.generatePersonalized('', 3)
      
      expect(Array.isArray(achievements)).toBe(true)
      expect(achievements.length).toBe(0)
    })

    test('should respect generation count limits', async () => {
      const achievements = await generator.generatePersonalized('test-user-limit', 10)
      
      expect(achievements.length).toBeLessThanOrEqual(10)
    })
  })

  describe('Seasonal Generation', () => {
    test('should generate seasonal achievements', async () => {
      const achievements = await generator.generateSeasonal('winter', 'medium')
      
      expect(Array.isArray(achievements)).toBe(true)
    })

    test('should handle invalid seasons', async () => {
      const achievements = await generator.generateSeasonal('invalid-season', 'easy')
      
      expect(Array.isArray(achievements)).toBe(true)
    })
  })

  describe('Difficulty Adaptation', () => {
    test('should adapt achievement difficulty', async () => {
      // First generate an achievement
      const achievements = await generator.generatePersonalized('adapt-test-user', 1)
      
      if (achievements.length > 0) {
        const achievementId = achievements[0].id
        
        // Should not throw when adapting difficulty
        await expect(
          generator.adaptDifficulty(achievementId, 85)
        ).resolves.not.toThrow()
      }
    })

    test('should handle non-existent achievement IDs', async () => {
      await expect(
        generator.adaptDifficulty('non-existent-id', 50)
      ).resolves.not.toThrow()
    })
  })

  describe('Generation Statistics', () => {
    test('should provide generation statistics', () => {
      const stats = DynamicAchievementGenerator.getGenerationStats()
      
      expect(stats).toBeDefined()
      expect(typeof stats.totalGenerated).toBe('number')
      expect(typeof stats.templatesAvailable).toBe('number')
      expect(typeof stats.activeUserProfiles).toBe('number')
    })
  })
})

describe('Achievement Evolution Tests', () => {
  beforeEach(() => {
    AchievementEvolutionManager.initialize()
  })

  describe('Evolution Opportunities', () => {
    test('should check evolution opportunities for user', async () => {
      const opportunities = await AchievementEvolutionManager.checkEvolutionOpportunities('evolution-test-user')
      
      expect(opportunities).toBeDefined()
      expect(Array.isArray(opportunities.readyToEvolve)).toBe(true)
      expect(Array.isArray(opportunities.nearEvolution)).toBe(true)
      expect(Array.isArray(opportunities.suggestions)).toBe(true)
    })

    test('should handle non-existent users', async () => {
      const opportunities = await AchievementEvolutionManager.checkEvolutionOpportunities('non-existent-user')
      
      expect(opportunities.readyToEvolve).toEqual([])
      expect(opportunities.nearEvolution).toEqual([])
      expect(opportunities.suggestions).toEqual([])
    })
  })

  describe('Evolution Processing', () => {
    test('should process evolution attempts', async () => {
      const result = await AchievementEvolutionManager.processEvolution(
        'evolution-process-user',
        'raffle_rookie_to_virtuoso',
        'completion_based',
        { sourceAchievement: 'raffle_rookie', completionCount: 50 }
      )

      // Result can be null if conditions aren't met
      expect(result === null || typeof result === 'object').toBe(true)
    })

    test('should handle invalid evolution paths', async () => {
      const result = await AchievementEvolutionManager.processEvolution(
        'evolution-invalid-user',
        'non-existent-path',
        'completion_based',
        {}
      )

      expect(result).toBeNull()
    })
  })

  describe('Auto Evolution Checks', () => {
    test('should perform auto evolution checks', async () => {
      const evolutions = await AchievementEvolutionManager.autoCheckEvolutions('auto-evolution-user')
      
      expect(Array.isArray(evolutions)).toBe(true)
    })
  })

  describe('Evolution Statistics', () => {
    test('should provide evolution statistics', async () => {
      const stats = await AchievementEvolutionManager.getEvolutionStats('stats-evolution-user')
      
      expect(stats).toBeDefined()
      expect(typeof stats.totalEvolutions).toBe('number')
      expect(typeof stats.successRate).toBe('number')
      expect(Array.isArray(stats.recentEvolutions)).toBe(true)
      expect(Array.isArray(stats.nextOpportunities)).toBe(true)
    })

    test('should provide system statistics', () => {
      const stats = AchievementEvolutionManager.getSystemStats()
      
      expect(stats).toBeDefined()
      expect(typeof stats.totalEvolutionPaths).toBe('number')
      expect(typeof stats.activeUserProfiles).toBe('number')
    })
  })
})

describe('Integration and Performance Tests', () => {
  test('should handle concurrent user activities', async () => {
    await GamificationSystem.initialize(testConfig)
    
    const userPromises = []
    
    // Simulate 10 concurrent users
    for (let i = 0; i < 10; i++) {
      userPromises.push(
        GamificationSystem.trackActivity(
          `concurrent-user-${i}`,
          'raffle_entry_submitted',
          { raffleId: `test-raffle-${i}` }
        )
      )
    }

    const results = await Promise.all(userPromises)
    
    expect(results).toHaveLength(10)
    results.forEach(result => {
      expect(result).toBeDefined()
    })
  })

  test('should maintain performance with large datasets', async () => {
    await GamificationSystem.initialize(testConfig)
    
    const startTime = Date.now()
    
    // Process 50 activities
    const promises = []
    for (let i = 0; i < 50; i++) {
      promises.push(
        GamificationSystem.trackActivity(
          'performance-test-user',
          'raffle_entry_submitted',
          { iteration: i }
        )
      )
    }
    
    await Promise.all(promises)
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    // Should complete within reasonable time (10 seconds)
    expect(duration).toBeLessThan(10000)
  })

  test('should validate all achievement data integrity', () => {
    // Validate all achievements have required fields
    const allAchievements = [
      ...phase1Achievements,
      ...phase2Achievements,
      ...phase3Achievements
    ]

    allAchievements.forEach(achievement => {
      expect(achievement.id).toBeDefined()
      expect(achievement.title).toBeDefined()
      expect(achievement.description).toBeDefined()
      expect(achievement.icon).toBeDefined()
      expect(achievement.category).toBeDefined()
      expect(achievement.rarity).toBeDefined()
      expect(achievement.requirements).toBeDefined()
      expect(achievement.rewards).toBeDefined()
      expect(achievement.gamificationTriggers).toBeDefined()
    })
  })

  test('should validate no duplicate achievement IDs', () => {
    const allAchievements = [
      ...phase1Achievements,
      ...phase2Achievements,
      ...phase3Achievements
    ]

    const ids = new Set()
    allAchievements.forEach(achievement => {
      expect(ids.has(achievement.id)).toBe(false)
      ids.add(achievement.id)
    })
  })
})

describe('Edge Cases and Error Recovery', () => {
  test('should handle system shutdown gracefully', async () => {
    await GamificationSystem.initialize(testConfig)
    
    // System should handle operations during shutdown
    expect(() => {
      GamificationSystem.clearAllCache()
    }).not.toThrow()
  })

  test('should recover from invalid cache states', async () => {
    await GamificationSystem.initialize(testConfig)
    
    // Clear cache and ensure system continues to function
    GamificationSystem.clearAllCache()
    
    const result = await GamificationSystem.trackActivity(
      'cache-recovery-user',
      'raffle_entry_submitted',
      {}
    )

    expect(result).toBeDefined()
  })

  test('should validate configuration integrity', () => {
    const validConfig = defaultGamificationConfig
    
    expect(validConfig.phases).toBeDefined()
    expect(validConfig.features).toBeDefined()
    expect(validConfig.settings).toBeDefined()
    
    // All phases should have boolean values
    expect(typeof validConfig.phases.phase1).toBe('boolean')
    expect(typeof validConfig.phases.phase2).toBe('boolean')
    expect(typeof validConfig.phases.phase3).toBe('boolean')
  })
})