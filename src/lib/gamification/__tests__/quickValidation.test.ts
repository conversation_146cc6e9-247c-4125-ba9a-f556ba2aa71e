/**
 * Quick Validation Test
 * 
 * Fast validation test to check critical gamification system functionality
 * without heavy dependencies.
 * 
 * <AUTHOR> Team - Gamification Testing
 * @version 3.0.0
 */

import { describe, test, expect } from '@jest/globals'

// Mock Firebase to avoid dependency issues
jest.mock('firebase/firestore', () => ({
  serverTimestamp: jest.fn(() => ({ toDate: () => new Date() })),
  Timestamp: {
    fromDate: jest.fn((date: Date) => ({ toDate: () => date }))
  }
}))

describe('Quick Gamification Validation', () => {
  
  test('Phase 1 achievements can be imported and are valid', async () => {
    const { phase1Achievements, validatePhase1Achievement, getPhase1Stats } = await import('../phase1-achievements')
    
    expect(Array.isArray(phase1Achievements)).toBe(true)
    expect(phase1Achievements.length).toBeGreaterThan(0)
    
    // Test validation function
    const firstAchievement = phase1Achievements[0]
    expect(validatePhase1Achievement(firstAchievement)).toBe(true)
    
    // Test stats function
    const stats = getPhase1Stats()
    expect(stats.totalAchievements).toBe(phase1Achievements.length)
    expect(stats.totalPointsAvailable).toBeGreaterThan(0)
  })

  test('Phase 2 achievements can be imported and are valid', async () => {
    const { phase2Achievements, validatePhase2Achievement, getPhase2Stats } = await import('../phase2-achievements')
    
    expect(Array.isArray(phase2Achievements)).toBe(true)
    expect(phase2Achievements.length).toBeGreaterThan(0)
    
    // Test validation function
    const firstAchievement = phase2Achievements[0]
    expect(validatePhase2Achievement(firstAchievement)).toBe(true)
    
    // Test stats function
    const stats = getPhase2Stats()
    expect(stats.totalAchievements).toBe(phase2Achievements.length)
    expect(stats.totalPointsAvailable).toBeGreaterThan(0)
  })

  test('Phase 3 achievements can be imported and are valid', async () => {
    const { phase3Achievements, validatePhase3Achievement, getPhase3Stats } = await import('../phase3-achievements')
    
    expect(Array.isArray(phase3Achievements)).toBe(true)
    expect(phase3Achievements.length).toBeGreaterThan(0)
    
    // Test validation function
    const firstAchievement = phase3Achievements[0]
    expect(validatePhase3Achievement(firstAchievement)).toBe(true)
    
    // Test stats function
    const stats = getPhase3Stats()
    expect(stats.totalAchievements).toBe(phase3Achievements.length)
    expect(stats.totalPointsAvailable).toBeGreaterThan(0)
  })

  test('Dynamic achievement generator can be imported', async () => {
    const { DynamicAchievementGenerator, achievementTemplates } = await import('../dynamicAchievements')
    
    expect(DynamicAchievementGenerator).toBeDefined()
    expect(Array.isArray(achievementTemplates)).toBe(true)
    expect(achievementTemplates.length).toBeGreaterThan(0)
    
    // Test template structure
    const firstTemplate = achievementTemplates[0]
    expect(firstTemplate.id).toBeDefined()
    expect(firstTemplate.name).toBeDefined()
    expect(firstTemplate.category).toBeDefined()
    expect(firstTemplate.baseStructure).toBeDefined()
    expect(firstTemplate.variableSlots).toBeDefined()
  })

  test('Achievement evolution manager can be imported', async () => {
    const { AchievementEvolutionManager, evolutionPaths } = await import('../achievementEvolution')
    
    expect(AchievementEvolutionManager).toBeDefined()
    expect(Array.isArray(evolutionPaths)).toBe(true)
    expect(evolutionPaths.length).toBeGreaterThan(0)
    
    // Test evolution path structure
    const firstPath = evolutionPaths[0]
    expect(firstPath.id).toBeDefined()
    expect(firstPath.name).toBeDefined()
    expect(firstPath.sourceAchievementId).toBeDefined()
    expect(firstPath.targetAchievementId).toBeDefined()
    expect(Array.isArray(firstPath.triggers)).toBe(true)
    expect(Array.isArray(firstPath.requirements)).toBe(true)
  })

  test('All achievement IDs are unique across phases', async () => {
    const { phase1Achievements } = await import('../phase1-achievements')
    const { phase2Achievements } = await import('../phase2-achievements')
    const { phase3Achievements } = await import('../phase3-achievements')
    
    const allIds = new Set()
    const allAchievements = [
      ...phase1Achievements,
      ...phase2Achievements,
      ...phase3Achievements
    ]
    
    allAchievements.forEach(achievement => {
      expect(allIds.has(achievement.id)).toBe(false)
      allIds.add(achievement.id)
    })
    
    expect(allIds.size).toBe(allAchievements.length)
  })

  test('Points are consistent across all achievements', async () => {
    const { phase1Achievements } = await import('../phase1-achievements')
    const { phase2Achievements } = await import('../phase2-achievements')
    const { phase3Achievements } = await import('../phase3-achievements')
    
    const allAchievements = [
      ...phase1Achievements,
      ...phase2Achievements,
      ...phase3Achievements
    ]
    
    allAchievements.forEach(achievement => {
      expect(achievement.rewards.points).toBeGreaterThan(0)
      expect(typeof achievement.rewards.points).toBe('number')
      
      // Check that points property exists and is a number
      expect(typeof achievement.points).toBe('number')
    })
  })

  test('System integration imports work correctly', async () => {
    // Test that the main system can be imported without errors
    const { defaultGamificationConfig } = await import('../gamificationSystem')
    
    expect(defaultGamificationConfig).toBeDefined()
    expect(defaultGamificationConfig.phases).toBeDefined()
    expect(defaultGamificationConfig.features).toBeDefined()
    expect(defaultGamificationConfig.settings).toBeDefined()
    
    // Verify phase configuration
    expect(typeof defaultGamificationConfig.phases.phase1).toBe('boolean')
    expect(typeof defaultGamificationConfig.phases.phase2).toBe('boolean')
    expect(typeof defaultGamificationConfig.phases.phase3).toBe('boolean')
  })

  test('Phase 3 index exports work correctly', async () => {
    const phase3Index = await import('../phase3-index')
    
    expect(phase3Index.phase3Achievements).toBeDefined()
    expect(phase3Index.DynamicHelpers).toBeDefined()
    expect(phase3Index.EvolutionHelpers).toBeDefined()
    expect(phase3Index.Phase3Helpers).toBeDefined()
    expect(phase3Index.initializePhase3System).toBeDefined()
  })

  test('System statistics are calculated correctly', async () => {
    const { getPhase1Stats } = await import('../phase1-achievements')
    const { getPhase2Stats } = await import('../phase2-achievements')
    const { getPhase3Stats } = await import('../phase3-achievements')
    
    const phase1Stats = getPhase1Stats()
    const phase2Stats = getPhase2Stats()
    const phase3Stats = getPhase3Stats()
    
    // All phases should have valid statistics
    expect(phase1Stats.totalAchievements).toBeGreaterThan(0)
    expect(phase2Stats.totalAchievements).toBeGreaterThan(0)
    expect(phase3Stats.totalAchievements).toBeGreaterThan(0)
    
    // Point totals should be reasonable
    expect(phase1Stats.totalPointsAvailable).toBeGreaterThan(1000)
    expect(phase2Stats.totalPointsAvailable).toBeGreaterThan(1000)
    expect(phase3Stats.totalPointsAvailable).toBeGreaterThan(1000)
    
    // Average points should be reasonable
    expect(phase1Stats.averagePointsPerAchievement).toBeGreaterThan(50)
    expect(phase2Stats.averagePointsPerAchievement).toBeGreaterThan(50)
    expect(phase3Stats.averagePointsPerAchievement).toBeGreaterThan(50)
  })
})