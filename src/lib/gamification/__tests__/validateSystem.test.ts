/**
 * System Validation Tests
 * 
 * Comprehensive validation of the entire gamification system
 * to ensure all components work together correctly.
 * 
 * <AUTHOR> Team - Gamification Testing
 * @version 3.0.0
 */

import { describe, test, expect } from '@jest/globals'
import { phase1Achievements, validatePhase1Achievement, getPhase1Stats } from '../phase1-achievements'
import { phase2Achievements, validatePhase2Achievement, getPhase2Stats } from '../phase2-achievements'
import { phase3Achievements, validatePhase3Achievement, getPhase3Stats } from '../phase3-achievements'
import { achievementTemplates } from '../dynamicAchievements'
import { evolutionPaths } from '../achievementEvolution'

describe('System Validation Tests', () => {
  
  describe('Phase 1 Validation', () => {
    test('all Phase 1 achievements should be valid', () => {
      phase1Achievements.forEach(achievement => {
        expect(validatePhase1Achievement(achievement)).toBe(true)
        expect(achievement.phase).toBeUndefined() // Phase 1 doesn't have phase property
      })
    })

    test('Phase 1 should have no duplicate IDs', () => {
      const ids = new Set()
      phase1Achievements.forEach(achievement => {
        expect(ids.has(achievement.id)).toBe(false)
        ids.add(achievement.id)
      })
    })

    test('Phase 1 statistics should be accurate', () => {
      const stats = getPhase1Stats()
      
      expect(stats.totalAchievements).toBe(phase1Achievements.length)
      expect(stats.totalPointsAvailable).toBeGreaterThan(0)
      expect(stats.averagePointsPerAchievement).toBeGreaterThan(0)
      
      // Verify category counts
      const expectedCounts = {
        raffle_entry: phase1Achievements.filter(a => a.category === 'raffle_entry').length,
        raffle_success: phase1Achievements.filter(a => a.category === 'raffle_success').length,
        raffle_social: phase1Achievements.filter(a => a.category === 'raffle_social').length,
        raffle_timing: phase1Achievements.filter(a => a.category === 'raffle_timing').length,
        social_engagement: phase1Achievements.filter(a => a.category === 'social_engagement').length,
        shopping: phase1Achievements.filter(a => a.category === 'shopping').length
      }
      
      expect(stats.byCategory).toEqual(expectedCounts)
    })

    test('all Phase 1 achievements should have required properties', () => {
      phase1Achievements.forEach(achievement => {
        expect(achievement.id).toBeDefined()
        expect(achievement.title).toBeDefined()
        expect(achievement.description).toBeDefined()
        expect(achievement.icon).toBeDefined()
        expect(achievement.category).toBeDefined()
        expect(achievement.rarity).toBeDefined()
        expect(achievement.requirements).toBeDefined()
        expect(achievement.rewards).toBeDefined()
        expect(achievement.gamificationTriggers).toBeDefined()
        expect(achievement.isActive).toBeDefined()
        expect(achievement.metadata).toBeDefined()
        
        // Verify points consistency
        expect(typeof achievement.points).toBe('number')
        expect(typeof achievement.rewards.points).toBe('number')
      })
    })
  })

  describe('Phase 2 Validation', () => {
    test('all Phase 2 achievements should be valid', () => {
      phase2Achievements.forEach(achievement => {
        expect(validatePhase2Achievement(achievement)).toBe(true)
        expect(achievement.phase).toBe(2)
      })
    })

    test('Phase 2 should have no duplicate IDs', () => {
      const ids = new Set()
      phase2Achievements.forEach(achievement => {
        expect(ids.has(achievement.id)).toBe(false)
        ids.add(achievement.id)
      })
    })

    test('Phase 2 statistics should be accurate', () => {
      const stats = getPhase2Stats()
      
      expect(stats.totalAchievements).toBe(phase2Achievements.length)
      expect(stats.totalPointsAvailable).toBeGreaterThan(0)
      expect(stats.averagePointsPerAchievement).toBeGreaterThan(0)
    })

    test('Phase 2 achievements should have advanced features', () => {
      phase2Achievements.forEach(achievement => {
        expect(achievement.phase).toBe(2)
        expect(achievement.metadata).toBeDefined()
        expect(achievement.metadata.version).toBe('2.0.0')
        
        // Advanced features should be defined where applicable
        if (achievement.advancedFeatures) {
          expect(typeof achievement.advancedFeatures).toBe('object')
        }
      })
    })
  })

  describe('Phase 3 Validation', () => {
    test('all Phase 3 achievements should be valid', () => {
      phase3Achievements.forEach(achievement => {
        expect(validatePhase3Achievement(achievement)).toBe(true)
        expect(achievement.phase).toBe(3)
      })
    })

    test('Phase 3 should have no duplicate IDs', () => {
      const ids = new Set()
      phase3Achievements.forEach(achievement => {
        expect(ids.has(achievement.id)).toBe(false)
        ids.add(achievement.id)
      })
    })

    test('Phase 3 statistics should be accurate', () => {
      const stats = getPhase3Stats()
      
      expect(stats.totalAchievements).toBe(phase3Achievements.length)
      expect(stats.totalPointsAvailable).toBeGreaterThan(0)
      expect(stats.averagePointsPerAchievement).toBeGreaterThan(0)
    })

    test('Phase 3 achievements should have advanced Phase 3 features', () => {
      phase3Achievements.forEach(achievement => {
        expect(achievement.phase).toBe(3)
        expect(achievement.metadata).toBeDefined()
        expect(achievement.metadata.version).toBe('3.0.0')
        
        // Check Phase 3 specific features
        if (achievement.isTimeLimited) {
          expect(achievement.availabilityWindow).toBeDefined()
        }
        
        if (achievement.isDynamic) {
          expect(achievement.dynamicConfig).toBeDefined()
        }
        
        if (achievement.canEvolve) {
          expect(achievement.evolutionRules).toBeDefined()
        }
      })
    })
  })

  describe('Cross-Phase Validation', () => {
    test('no duplicate achievement IDs across all phases', () => {
      const allIds = new Set()
      const allAchievements = [
        ...phase1Achievements,
        ...phase2Achievements,
        ...phase3Achievements
      ]
      
      allAchievements.forEach(achievement => {
        expect(allIds.has(achievement.id)).toBe(false)
        allIds.add(achievement.id)
      })
    })

    test('achievement progression chains should be valid', () => {
      const allAchievements = [
        ...phase1Achievements,
        ...phase2Achievements,
        ...phase3Achievements
      ]
      const allIds = new Set(allAchievements.map(a => a.id))
      
      allAchievements.forEach(achievement => {
        // Check prerequisites exist
        if (achievement.prerequisites) {
          achievement.prerequisites.forEach(prereqId => {
            expect(allIds.has(prereqId)).toBe(true)
          })
        }
        
        // Check progress chains exist
        if (achievement.progressChain) {
          achievement.progressChain.forEach(chainId => {
            expect(allIds.has(chainId)).toBe(true)
          })
        }
      })
    })

    test('point distribution should be balanced across phases', () => {
      const phase1Stats = getPhase1Stats()
      const phase2Stats = getPhase2Stats()
      const phase3Stats = getPhase3Stats()
      
      expect(phase1Stats.totalPointsAvailable).toBeGreaterThan(0)
      expect(phase2Stats.totalPointsAvailable).toBeGreaterThan(0)
      expect(phase3Stats.totalPointsAvailable).toBeGreaterThan(0)
      
      // Phase progression should generally increase in point value
      expect(phase2Stats.averagePointsPerAchievement).toBeGreaterThanOrEqual(
        phase1Stats.averagePointsPerAchievement * 0.8
      )
      expect(phase3Stats.averagePointsPerAchievement).toBeGreaterThanOrEqual(
        phase2Stats.averagePointsPerAchievement * 0.8
      )
    })
  })

  describe('Dynamic Achievement Validation', () => {
    test('all achievement templates should be valid', () => {
      achievementTemplates.forEach(template => {
        expect(template.id).toBeDefined()
        expect(template.name).toBeDefined()
        expect(template.category).toBeDefined()
        expect(template.baseStructure).toBeDefined()
        expect(template.variableSlots).toBeDefined()
        expect(template.difficultyScaling).toBeDefined()
        expect(template.personalizationFactors).toBeDefined()
        
        // Validate difficulty scaling
        expect(template.difficultyScaling.easy).toBeDefined()
        expect(template.difficultyScaling.medium).toBeDefined()
        expect(template.difficultyScaling.hard).toBeDefined()
        
        // Validate base structure
        expect(template.baseStructure.titleTemplate).toBeDefined()
        expect(template.baseStructure.descriptionTemplate).toBeDefined()
        expect(Array.isArray(template.baseStructure.iconOptions)).toBe(true)
        expect(Array.isArray(template.baseStructure.requirementTypes)).toBe(true)
      })
    })

    test('template variable slots should be properly defined', () => {
      achievementTemplates.forEach(template => {
        Object.entries(template.variableSlots).forEach(([key, slot]) => {
          expect(slot.type).toBeDefined()
          expect(['number', 'string', 'category', 'timeframe'].includes(slot.type)).toBe(true)
          
          if (slot.type === 'number' && slot.range) {
            expect(slot.range.min).toBeLessThan(slot.range.max)
          }
          
          if (slot.options) {
            expect(Array.isArray(slot.options)).toBe(true)
            expect(slot.options.length).toBeGreaterThan(0)
          }
        })
      })
    })
  })

  describe('Evolution System Validation', () => {
    test('all evolution paths should be valid', () => {
      evolutionPaths.forEach(path => {
        expect(path.id).toBeDefined()
        expect(path.name).toBeDefined()
        expect(path.description).toBeDefined()
        expect(path.sourceAchievementId).toBeDefined()
        expect(path.targetAchievementId).toBeDefined()
        expect(Array.isArray(path.triggers)).toBe(true)
        expect(Array.isArray(path.requirements)).toBe(true)
        expect(path.rewards).toBeDefined()
        expect(path.probability).toBeGreaterThanOrEqual(0)
        expect(path.probability).toBeLessThanOrEqual(100)
        expect(typeof path.isReversible).toBe('boolean')
        expect(path.metadata).toBeDefined()
      })
    })

    test('evolution triggers should be properly configured', () => {
      evolutionPaths.forEach(path => {
        path.triggers.forEach(trigger => {
          expect(trigger.type).toBeDefined()
          expect(['time_based', 'completion_based', 'streak_based', 'seasonal', 'community', 'performance_based'].includes(trigger.type)).toBe(true)
          expect(trigger.condition).toBeDefined()
          expect(trigger.weight).toBeGreaterThan(0)
          expect(trigger.weight).toBeLessThanOrEqual(100)
        })
      })
    })

    test('evolution requirements should be achievable', () => {
      evolutionPaths.forEach(path => {
        path.requirements.forEach(requirement => {
          expect(requirement.type).toBeDefined()
          expect(requirement.description).toBeDefined()
          expect(requirement.target).toBeGreaterThan(0)
          expect(typeof requirement.isOptional).toBe('boolean')
        })
      })
    })

    test('evolution rewards should be meaningful', () => {
      evolutionPaths.forEach(path => {
        expect(path.rewards.points).toBeGreaterThan(0)
        
        // Higher difficulty evolutions should have better rewards
        if (path.metadata.difficulty === 'legendary') {
          expect(path.rewards.points).toBeGreaterThan(1000)
        } else if (path.metadata.difficulty === 'hard') {
          expect(path.rewards.points).toBeGreaterThan(500)
        }
      })
    })
  })

  describe('Performance and Scalability', () => {
    test('achievement lookups should be efficient', () => {
      const startTime = Date.now()
      
      // Simulate looking up 1000 achievements
      for (let i = 0; i < 1000; i++) {
        const randomAchievement = phase1Achievements[i % phase1Achievements.length]
        expect(randomAchievement.id).toBeDefined()
      }
      
      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete in under 100ms
    })

    test('validation should be fast for large datasets', () => {
      const startTime = Date.now()
      
      // Validate all achievements multiple times
      for (let i = 0; i < 10; i++) {
        phase1Achievements.forEach(achievement => {
          validatePhase1Achievement(achievement)
        })
        phase2Achievements.forEach(achievement => {
          validatePhase2Achievement(achievement)
        })
        phase3Achievements.forEach(achievement => {
          validatePhase3Achievement(achievement)
        })
      }
      
      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(1000) // Should complete in under 1 second
    })
  })

  describe('Data Integrity', () => {
    test('all achievements should have consistent point values', () => {
      const allAchievements = [
        ...phase1Achievements,
        ...phase2Achievements,
        ...phase3Achievements
      ]
      
      allAchievements.forEach(achievement => {
        // Points should be consistent between rewards and direct property
        if (achievement.points !== undefined && achievement.rewards.points !== undefined) {
          // Allow for some flexibility in case of intentional differences
          const difference = Math.abs(achievement.points - achievement.rewards.points)
          expect(difference).toBeLessThanOrEqual(achievement.rewards.points * 0.1) // Within 10%
        }
      })
    })

    test('all achievements should have valid rarity levels', () => {
      const validRarities = ['common', 'uncommon', 'rare', 'epic', 'legendary']
      const allAchievements = [
        ...phase1Achievements,
        ...phase2Achievements,
        ...phase3Achievements
      ]
      
      allAchievements.forEach(achievement => {
        expect(validRarities.includes(achievement.rarity)).toBe(true)
      })
    })

    test('achievements should have meaningful descriptions', () => {
      const allAchievements = [
        ...phase1Achievements,
        ...phase2Achievements,
        ...phase3Achievements
      ]
      
      allAchievements.forEach(achievement => {
        expect(achievement.title.length).toBeGreaterThan(3)
        expect(achievement.description.length).toBeGreaterThan(10)
        expect(achievement.description.length).toBeLessThan(200) // Keep descriptions concise
      })
    })
  })
})