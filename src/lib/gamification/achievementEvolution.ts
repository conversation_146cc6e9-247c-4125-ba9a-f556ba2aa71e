/**
 * Achievement Evolution and Progression System
 * 
 * Advanced system for transforming, upgrading, and evolving achievements
 * based on user behavior, seasonal changes, completion patterns, and 
 * community milestones.
 * 
 * <AUTHOR> Team - Gamification Phase 3
 * @version 1.0.0
 */

import { serverTimestamp, Timestamp } from 'firebase/firestore'
import type { Phase3Achievement, EvolutionRule } from './phase3-achievements'
import type { Achievement, AchievementRarity } from './phase1-achievements'
import type { Phase2Achievement } from './phase2-achievements'

// ===== EVOLUTION TYPES =====

export interface EvolutionTrigger {
  type: 'time_based' | 'completion_based' | 'streak_based' | 'seasonal' | 'community' | 'performance_based';
  condition: EvolutionCondition;
  weight: number; // 0-100, how much this trigger contributes to evolution
}

export interface EvolutionCondition {
  // Time-based conditions
  daysActive?: number;
  hoursPlayed?: number;
  
  // Completion-based conditions
  sourceAchievement?: string;
  completionCount?: number;
  categoryCompletions?: Record<string, number>;
  rarityThreshold?: AchievementRarity;
  
  // Streak-based conditions
  consecutiveDays?: number;
  perfectCompletions?: number;
  streakLength?: number;
  
  // Seasonal conditions
  season?: string;
  holiday?: string;
  monthsActive?: number;
  
  // Community conditions
  communityRank?: number;
  socialShares?: number;
  mentorshipActivities?: number;
  
  // Performance conditions
  completionRate?: number; // percentage
  averageTime?: number; // hours to complete
  difficultyLevel?: number;
}

export interface EvolutionPath {
  id: string;
  name: string;
  description: string;
  sourceAchievementId: string;
  targetAchievementId: string;
  triggers: EvolutionTrigger[];
  requirements: EvolutionRequirement[];
  rewards: EvolutionReward;
  probability: number; // 0-100, chance of evolution occurring
  cooldownPeriod?: number; // hours before next evolution possible
  isReversible: boolean;
  metadata: {
    difficulty: 'easy' | 'medium' | 'hard' | 'legendary';
    estimatedTime: number; // days
    userTypes: string[]; // which user types benefit most
    tags: string[];
  };
}

export interface EvolutionRequirement {
  type: 'points' | 'achievements' | 'social' | 'time' | 'performance';
  description: string;
  target: number;
  current?: number;
  isOptional: boolean;
}

export interface EvolutionReward {
  points: number;
  title?: string;
  badge?: string;
  exclusiveFeatures?: string[];
  socialRecognition?: {
    announcement: boolean;
    showcase: boolean;
    leaderboardBoost: number;
  };
  unlocks?: string[]; // Additional content or features unlocked
}

export interface EvolutionEvent {
  id: string;
  userId: string;
  sourceAchievementId: string;
  targetAchievementId: string;
  evolutionPathId: string;
  triggerType: string;
  triggerData: Record<string, any>;
  evolutionReason: string;
  timestamp: Timestamp;
  wasSuccessful: boolean;
  userReaction?: 'positive' | 'neutral' | 'negative';
  socialImpact: {
    shares: number;
    reactions: number;
    communityDiscussion: boolean;
  };
}

export interface UserEvolutionProfile {
  userId: string;
  totalEvolutions: number;
  evolutionsByType: Record<string, number>;
  evolutionHistory: EvolutionEvent[];
  preferredEvolutionTypes: string[];
  evolutionSuccessRate: number;
  lastEvolutionDate: Timestamp;
  evolutionPotential: {
    readyToEvolve: string[]; // Achievement IDs ready for evolution
    nearEvolution: { achievementId: string; progress: number }[];
    suggestedPaths: string[];
  };
  seasonalEvolutionBonus: number; // 0-100, seasonal multiplier
}

// ===== EVOLUTION PATHS DEFINITIONS =====

export const evolutionPaths: EvolutionPath[] = [
  // Raffle Entry Evolution Chain
  {
    id: 'raffle_rookie_to_virtuoso',
    name: 'Raffle Mastery Evolution',
    description: 'Transform from a rookie raffle participant to a true virtuoso',
    sourceAchievementId: 'raffle_rookie',
    targetAchievementId: 'raffle_rookie_evolved',
    triggers: [
      {
        type: 'completion_based',
        condition: {
          sourceAchievement: 'raffle_rookie',
          completionCount: 50,
          perfectCompletions: 10
        },
        weight: 60
      },
      {
        type: 'time_based',
        condition: {
          daysActive: 30
        },
        weight: 20
      },
      {
        type: 'performance_based',
        condition: {
          completionRate: 85
        },
        weight: 20
      }
    ],
    requirements: [
      {
        type: 'achievements',
        description: 'Complete 50 raffle entries',
        target: 50,
        isOptional: false
      },
      {
        type: 'performance',
        description: 'Maintain 85% completion rate',
        target: 85,
        isOptional: false
      },
      {
        type: 'social',
        description: 'Share raffle achievements 5 times',
        target: 5,
        isOptional: true
      }
    ],
    rewards: {
      points: 1500,
      title: 'Raffle Virtuoso',
      badge: 'virtuoso_medal',
      socialRecognition: {
        announcement: true,
        showcase: true,
        leaderboardBoost: 10
      },
      unlocks: ['premium_raffle_insights', 'early_raffle_access']
    },
    probability: 75,
    cooldownPeriod: 168, // 1 week
    isReversible: false,
    metadata: {
      difficulty: 'medium',
      estimatedTime: 45,
      userTypes: ['active_participant', 'social_engager'],
      tags: ['raffle', 'mastery', 'progression']
    }
  },

  // Social Evolution Chain
  {
    id: 'social_butterfly_ascension',
    name: 'Social Leader Evolution',
    description: 'Evolve from social butterfly to community leader',
    sourceAchievementId: 'social_butterfly',
    targetAchievementId: 'community_leader_evolved',
    triggers: [
      {
        type: 'community',
        condition: {
          socialShares: 25,
          mentorshipActivities: 10,
          communityRank: 50
        },
        weight: 70
      },
      {
        type: 'completion_based',
        condition: {
          categoryCompletions: { 'social_engagement': 10 }
        },
        weight: 30
      }
    ],
    requirements: [
      {
        type: 'social',
        description: 'Share achievements 25 times',
        target: 25,
        isOptional: false
      },
      {
        type: 'social',
        description: 'Help 10 community members',
        target: 10,
        isOptional: false
      },
      {
        type: 'achievements',
        description: 'Complete 10 social engagement achievements',
        target: 10,
        isOptional: false
      }
    ],
    rewards: {
      points: 2000,
      title: 'Community Leader',
      badge: 'leadership_crown',
      exclusiveFeatures: ['community_moderation', 'mentor_program'],
      socialRecognition: {
        announcement: true,
        showcase: true,
        leaderboardBoost: 25
      }
    },
    probability: 60,
    cooldownPeriod: 336, // 2 weeks
    isReversible: false,
    metadata: {
      difficulty: 'hard',
      estimatedTime: 60,
      userTypes: ['social_leader', 'community_builder'],
      tags: ['social', 'leadership', 'community']
    }
  },

  // Seasonal Evolution
  {
    id: 'seasonal_collector_master',
    name: 'Seasonal Master Evolution',
    description: 'Master of all seasons - ultimate seasonal achievement',
    sourceAchievementId: 'seasonal_enthusiast',
    targetAchievementId: 'seasonal_master_evolved',
    triggers: [
      {
        type: 'seasonal',
        condition: {
          monthsActive: 12,
          season: 'all_seasons'
        },
        weight: 50
      },
      {
        type: 'completion_based',
        condition: {
          categoryCompletions: { 'seasonal_holiday': 15 }
        },
        weight: 30
      },
      {
        type: 'time_based',
        condition: {
          daysActive: 365
        },
        weight: 20
      }
    ],
    requirements: [
      {
        type: 'time',
        description: 'Active for 12 months across all seasons',
        target: 12,
        isOptional: false
      },
      {
        type: 'achievements',
        description: 'Complete 15 seasonal achievements',
        target: 15,
        isOptional: false
      },
      {
        type: 'social',
        description: 'Participate in 5 seasonal community events',
        target: 5,
        isOptional: true
      }
    ],
    rewards: {
      points: 3000,
      title: 'Seasonal Grand Master',
      badge: 'seasonal_crown',
      exclusiveFeatures: ['seasonal_preview', 'custom_seasonal_themes'],
      socialRecognition: {
        announcement: true,
        showcase: true,
        leaderboardBoost: 50
      },
      unlocks: ['legendary_seasonal_collection', 'seasonal_event_creation']
    },
    probability: 40,
    cooldownPeriod: 2160, // 3 months
    isReversible: false,
    metadata: {
      difficulty: 'legendary',
      estimatedTime: 365,
      userTypes: ['long_term_user', 'seasonal_collector'],
      tags: ['seasonal', 'mastery', 'legendary', 'year_long']
    }
  },

  // Streak Evolution
  {
    id: 'consistency_king_evolution',
    name: 'Consistency Master Evolution',
    description: 'Ultimate consistency achievement for dedicated users',
    sourceAchievementId: 'consistency_king',
    targetAchievementId: 'consistency_legend_evolved',
    triggers: [
      {
        type: 'streak_based',
        condition: {
          consecutiveDays: 100,
          perfectCompletions: 50,
          streakLength: 100
        },
        weight: 80
      },
      {
        type: 'performance_based',
        condition: {
          completionRate: 95,
          averageTime: 0.5
        },
        weight: 20
      }
    ],
    requirements: [
      {
        type: 'time',
        description: 'Maintain 100-day activity streak',
        target: 100,
        isOptional: false
      },
      {
        type: 'performance',
        description: 'Achieve 95% completion rate',
        target: 95,
        isOptional: false
      },
      {
        type: 'achievements',
        description: '50 perfect completions',
        target: 50,
        isOptional: false
      }
    ],
    rewards: {
      points: 5000,
      title: 'Consistency Legend',
      badge: 'diamond_streak',
      exclusiveFeatures: ['streak_insurance', 'priority_support'],
      socialRecognition: {
        announcement: true,
        showcase: true,
        leaderboardBoost: 100
      }
    },
    probability: 25,
    cooldownPeriod: 4320, // 6 months
    isReversible: false,
    metadata: {
      difficulty: 'legendary',
      estimatedTime: 100,
      userTypes: ['dedicated_user', 'perfectionist'],
      tags: ['streak', 'consistency', 'legendary', 'dedication']
    }
  }
]

// ===== ACHIEVEMENT EVOLUTION MANAGER =====

export class AchievementEvolutionManager {
  private static userProfiles: Map<string, UserEvolutionProfile> = new Map()
  private static activeEvolutions: Map<string, EvolutionEvent[]> = new Map()
  private static evolutionQueue: EvolutionEvent[] = []

  /**
   * Initialize evolution system
   */
  static initialize(): void {
    console.log('Initializing Achievement Evolution Manager...')
    
    this.startEvolutionProcessor()
    this.scheduleEvolutionChecks()
    
    console.log('Achievement Evolution Manager initialized successfully')
  }

  /**
   * Check if user achievements are ready for evolution
   */
  static async checkEvolutionOpportunities(userId: string): Promise<{
    readyToEvolve: EvolutionPath[];
    nearEvolution: { path: EvolutionPath; progress: number }[];
    suggestions: EvolutionPath[];
  }> {
    try {
      const userProfile = await this.getUserEvolutionProfile(userId)
      const userAchievements = await this.getUserCompletedAchievements(userId)
      
      const readyToEvolve: EvolutionPath[] = []
      const nearEvolution: { path: EvolutionPath; progress: number }[] = []
      const suggestions: EvolutionPath[] = []

      for (const path of evolutionPaths) {
        // Check if user has the source achievement
        const hasSourceAchievement = userAchievements.some(
          achievement => achievement.id === path.sourceAchievementId
        )

        if (!hasSourceAchievement) {
          // Suggest this path if user is close to getting source achievement
          suggestions.push(path)
          continue
        }

        // Calculate evolution progress
        const progress = await this.calculateEvolutionProgress(userId, path)
        
        if (progress >= 100) {
          readyToEvolve.push(path)
        } else if (progress >= 70) {
          nearEvolution.push({ path, progress })
        } else if (progress >= 30) {
          suggestions.push(path)
        }
      }

      return {
        readyToEvolve,
        nearEvolution,
        suggestions
      }
    } catch (error) {
      console.error('Error checking evolution opportunities:', error)
      return { readyToEvolve: [], nearEvolution: [], suggestions: [] }
    }
  }

  /**
   * Process achievement evolution
   */
  static async processEvolution(
    userId: string,
    evolutionPathId: string,
    triggerType: string,
    triggerData: Record<string, any> = {}
  ): Promise<EvolutionEvent | null> {
    try {
      const path = evolutionPaths.find(p => p.id === evolutionPathId)
      if (!path) {
        throw new Error(`Evolution path not found: ${evolutionPathId}`)
      }

      // Validate evolution requirements
      const canEvolve = await this.validateEvolutionRequirements(userId, path)
      if (!canEvolve) {
        console.log(`User ${userId} does not meet requirements for evolution ${evolutionPathId}`)
        return null
      }

      // Check cooldown
      if (await this.isEvolutionOnCooldown(userId, path)) {
        console.log(`Evolution ${evolutionPathId} is on cooldown for user ${userId}`)
        return null
      }

      // Calculate evolution probability
      const evolutionRoll = Math.random() * 100
      const willEvolve = evolutionRoll <= path.probability

      // Create evolution event
      const evolutionEvent: EvolutionEvent = {
        id: `evolution_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        sourceAchievementId: path.sourceAchievementId,
        targetAchievementId: path.targetAchievementId,
        evolutionPathId,
        triggerType,
        triggerData,
        evolutionReason: this.generateEvolutionReason(path, triggerType),
        timestamp: serverTimestamp() as Timestamp,
        wasSuccessful: willEvolve,
        socialImpact: {
          shares: 0,
          reactions: 0,
          communityDiscussion: false
        }
      }

      if (willEvolve) {
        // Execute the evolution
        await this.executeEvolution(userId, path, evolutionEvent)
        
        // Update user profile
        await this.updateUserEvolutionProfile(userId, evolutionEvent)
        
        // Queue social notifications
        await this.queueEvolutionNotifications(evolutionEvent)
        
        console.log(`Evolution successful: ${path.name} for user ${userId}`)
      } else {
        console.log(`Evolution failed (probability): ${path.name} for user ${userId}`)
      }

      // Store evolution event
      this.storeEvolutionEvent(evolutionEvent)
      
      return evolutionEvent
    } catch (error) {
      console.error('Error processing evolution:', error)
      return null
    }
  }

  /**
   * Auto-check for evolution opportunities
   */
  static async autoCheckEvolutions(userId: string): Promise<EvolutionEvent[]> {
    try {
      const opportunities = await this.checkEvolutionOpportunities(userId)
      const processedEvolutions: EvolutionEvent[] = []

      // Process automatic evolutions for paths marked as ready
      for (const path of opportunities.readyToEvolve) {
        // Only auto-process certain types of evolutions
        if (this.shouldAutoProcess(path)) {
          const evolutionEvent = await this.processEvolution(
            userId,
            path.id,
            'auto_check',
            { autoTriggered: true }
          )
          
          if (evolutionEvent) {
            processedEvolutions.push(evolutionEvent)
          }
        }
      }

      return processedEvolutions
    } catch (error) {
      console.error('Error in auto evolution check:', error)
      return []
    }
  }

  /**
   * Get user evolution statistics
   */
  static async getEvolutionStats(userId: string): Promise<{
    totalEvolutions: number;
    evolutionsByDifficulty: Record<string, number>;
    averageEvolutionTime: number;
    successRate: number;
    recentEvolutions: EvolutionEvent[];
    nextOpportunities: EvolutionPath[];
  }> {
    try {
      const profile = await this.getUserEvolutionProfile(userId)
      const opportunities = await this.checkEvolutionOpportunities(userId)
      
      const evolutionsByDifficulty: Record<string, number> = {}
      let totalEvolutionTime = 0
      
      profile.evolutionHistory.forEach(event => {
        const path = evolutionPaths.find(p => p.id === event.evolutionPathId)
        if (path) {
          const difficulty = path.metadata.difficulty
          evolutionsByDifficulty[difficulty] = (evolutionsByDifficulty[difficulty] || 0) + 1
          totalEvolutionTime += path.metadata.estimatedTime
        }
      })

      return {
        totalEvolutions: profile.totalEvolutions,
        evolutionsByDifficulty,
        averageEvolutionTime: profile.totalEvolutions > 0 ? 
          totalEvolutionTime / profile.totalEvolutions : 0,
        successRate: profile.evolutionSuccessRate,
        recentEvolutions: profile.evolutionHistory.slice(0, 5),
        nextOpportunities: opportunities.suggestions
      }
    } catch (error) {
      console.error('Error getting evolution stats:', error)
      return {
        totalEvolutions: 0,
        evolutionsByDifficulty: {},
        averageEvolutionTime: 0,
        successRate: 0,
        recentEvolutions: [],
        nextOpportunities: []
      }
    }
  }

  // ===== HELPER METHODS =====

  private static async getUserEvolutionProfile(userId: string): Promise<UserEvolutionProfile> {
    if (this.userProfiles.has(userId)) {
      return this.userProfiles.get(userId)!
    }

    // Create new profile
    const profile: UserEvolutionProfile = {
      userId,
      totalEvolutions: 0,
      evolutionsByType: {},
      evolutionHistory: [],
      preferredEvolutionTypes: [],
      evolutionSuccessRate: 0,
      lastEvolutionDate: serverTimestamp() as Timestamp,
      evolutionPotential: {
        readyToEvolve: [],
        nearEvolution: [],
        suggestedPaths: []
      },
      seasonalEvolutionBonus: 0
    }

    this.userProfiles.set(userId, profile)
    return profile
  }

  private static async getUserCompletedAchievements(userId: string): Promise<(Achievement | Phase2Achievement | Phase3Achievement)[]> {
    // This would fetch from the database
    // For now, return mock data
    return []
  }

  private static async calculateEvolutionProgress(userId: string, path: EvolutionPath): Promise<number> {
    let totalProgress = 0
    let totalWeight = 0

    for (const trigger of path.triggers) {
      const triggerProgress = await this.calculateTriggerProgress(userId, trigger)
      totalProgress += triggerProgress * trigger.weight
      totalWeight += trigger.weight
    }

    return totalWeight > 0 ? Math.min(100, totalProgress / totalWeight) : 0
  }

  private static async calculateTriggerProgress(userId: string, trigger: EvolutionTrigger): Promise<number> {
    // This would calculate actual progress based on user data
    // For now, return mock progress
    switch (trigger.type) {
      case 'completion_based':
        return Math.random() * 100
      case 'time_based':
        return Math.random() * 100
      case 'streak_based':
        return Math.random() * 100
      case 'seasonal':
        return Math.random() * 100
      case 'community':
        return Math.random() * 100
      case 'performance_based':
        return Math.random() * 100
      default:
        return 0
    }
  }

  private static async validateEvolutionRequirements(userId: string, path: EvolutionPath): Promise<boolean> {
    for (const requirement of path.requirements) {
      if (!requirement.isOptional) {
        const currentProgress = await this.getRequirementProgress(userId, requirement)
        if (currentProgress < requirement.target) {
          return false
        }
      }
    }
    return true
  }

  private static async getRequirementProgress(userId: string, requirement: EvolutionRequirement): Promise<number> {
    // This would fetch actual progress from user data
    return requirement.target // Mock: assume requirement is met
  }

  private static async isEvolutionOnCooldown(userId: string, path: EvolutionPath): Promise<boolean> {
    if (!path.cooldownPeriod) return false

    const profile = await this.getUserEvolutionProfile(userId)
    const lastEvolution = profile.evolutionHistory.find(
      event => event.evolutionPathId === path.id && event.wasSuccessful
    )

    if (!lastEvolution) return false

    const cooldownEnd = new Date(lastEvolution.timestamp.toDate())
    cooldownEnd.setHours(cooldownEnd.getHours() + path.cooldownPeriod)

    return new Date() < cooldownEnd
  }

  private static async executeEvolution(
    userId: string,
    path: EvolutionPath,
    evolutionEvent: EvolutionEvent
  ): Promise<void> {
    // This would:
    // 1. Remove/upgrade the source achievement
    // 2. Add the target achievement
    // 3. Award evolution rewards
    // 4. Update user stats
    
    console.log(`Executing evolution: ${path.name} for user ${userId}`)
  }

  private static async updateUserEvolutionProfile(
    userId: string,
    evolutionEvent: EvolutionEvent
  ): Promise<void> {
    const profile = await this.getUserEvolutionProfile(userId)
    
    if (evolutionEvent.wasSuccessful) {
      profile.totalEvolutions++
      profile.evolutionsByType[evolutionEvent.triggerType] = 
        (profile.evolutionsByType[evolutionEvent.triggerType] || 0) + 1
    }
    
    profile.evolutionHistory.unshift(evolutionEvent)
    profile.lastEvolutionDate = evolutionEvent.timestamp
    
    // Calculate success rate
    const totalAttempts = profile.evolutionHistory.length
    const successfulAttempts = profile.evolutionHistory.filter(e => e.wasSuccessful).length
    profile.evolutionSuccessRate = totalAttempts > 0 ? (successfulAttempts / totalAttempts) * 100 : 0
    
    // Keep history reasonable size
    if (profile.evolutionHistory.length > 50) {
      profile.evolutionHistory.splice(50)
    }
  }

  private static async queueEvolutionNotifications(evolutionEvent: EvolutionEvent): Promise<void> {
    // Queue notifications for successful evolutions
    console.log(`Queuing notifications for evolution: ${evolutionEvent.id}`)
  }

  private static storeEvolutionEvent(event: EvolutionEvent): void {
    const userEvents = this.activeEvolutions.get(event.userId) || []
    userEvents.push(event)
    this.activeEvolutions.set(event.userId, userEvents)
  }

  private static shouldAutoProcess(path: EvolutionPath): boolean {
    // Only auto-process easy and medium difficulty evolutions
    return ['easy', 'medium'].includes(path.metadata.difficulty)
  }

  private static generateEvolutionReason(path: EvolutionPath, triggerType: string): string {
    const reasons = {
      'time_based': `Consistent dedication over time has earned this evolution`,
      'completion_based': `Outstanding achievement completion record`,
      'streak_based': `Exceptional consistency and dedication`,
      'seasonal': `Mastery across seasonal events and activities`,
      'community': `Outstanding community leadership and engagement`,
      'performance_based': `Exceptional performance and skill demonstration`,
      'auto_check': `Natural progression through dedicated participation`
    }
    
    return reasons[triggerType as keyof typeof reasons] || 'Achievement excellence recognized'
  }

  private static startEvolutionProcessor(): void {
    // Process evolution queue periodically
    setInterval(() => {
      this.processEvolutionQueue()
    }, 30 * 1000) // Every 30 seconds
  }

  private static scheduleEvolutionChecks(): void {
    // Check for evolution opportunities periodically
    setInterval(() => {
      this.performScheduledEvolutionChecks()
    }, 60 * 60 * 1000) // Every hour
  }

  private static async processEvolutionQueue(): Promise<void> {
    // Process queued evolution events
    while (this.evolutionQueue.length > 0) {
      const event = this.evolutionQueue.shift()
      if (event) {
        // Process the event
        console.log(`Processing queued evolution: ${event.id}`)
      }
    }
  }

  private static async performScheduledEvolutionChecks(): Promise<void> {
    // Perform periodic checks for all active users
    console.log('Performing scheduled evolution checks...')
    
    for (const [userId] of Array.from(this.userProfiles.keys())) {
      try {
        await this.autoCheckEvolutions(userId)
      } catch (error) {
        console.error(`Error in scheduled evolution check for user ${userId}:`, error)
      }
    }
  }

  /**
   * Get system statistics
   */
  static getSystemStats() {
    return {
      totalEvolutionPaths: evolutionPaths.length,
      activeUserProfiles: this.userProfiles.size,
      totalEvolutionEvents: Array.from(this.activeEvolutions.values())
        .reduce((sum, events) => sum + events.length, 0),
      queuedEvolutions: this.evolutionQueue.length,
      pathsByDifficulty: {
        easy: evolutionPaths.filter(p => p.metadata.difficulty === 'easy').length,
        medium: evolutionPaths.filter(p => p.metadata.difficulty === 'medium').length,
        hard: evolutionPaths.filter(p => p.metadata.difficulty === 'hard').length,
        legendary: evolutionPaths.filter(p => p.metadata.difficulty === 'legendary').length
      }
    }
  }
}

export default AchievementEvolutionManager