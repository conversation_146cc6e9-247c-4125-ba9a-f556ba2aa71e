/**
 * Achievement Integration Module
 * 
 * Provides easy integration points for the existing raffle system
 * to trigger achievement tracking and notifications.
 * 
 * <AUTHOR> Team - Gamification Phase 1
 * @version 1.0.0
 */

import { AchievementTracker, createActivityTrigger, trackRaffleEntry, trackSocialRequirement } from './achievementTracking'
import { AchievementNotificationManager } from './achievementNotifications'
import type { AchievementUnlockResult } from './achievementTracking'

// ===== MAIN INTEGRATION FUNCTIONS =====

/**
 * Initialize the achievement system
 */
export async function initializeAchievementSystem(userConfig?: any): Promise<void> {
  try {
    console.log('Initializing Achievement System Phase 1...')
    
    // Initialize notification system
    AchievementNotificationManager.initialize(userConfig?.notifications)
    
    console.log('Achievement System Phase 1 initialized successfully')
  } catch (error) {
    console.error('Failed to initialize Achievement System:', error)
    throw error
  }
}

/**
 * Handle raffle entry submission
 * Call this when a user successfully submits a raffle entry
 */
export async function handleRaffleEntrySubmission(
  userId: string,
  raffleId: string,
  entryData: {
    completionTime?: number; // Time in seconds to complete the entry
    startTime?: Date;
    endTime?: Date;
    errorCount?: number; // Number of form errors encountered
    retryCount?: number; // Number of retry attempts
    socialRequirementsCompleted?: string[]; // Array of completed requirements
    variantSelected?: string;
    shippingMethod?: string;
  }
): Promise<AchievementUnlockResult[]> {
  try {
    console.log(`Processing raffle entry for user ${userId}, raffle ${raffleId}`)
    
    // Calculate completion time if not provided
    let completionTime = entryData.completionTime
    if (!completionTime && entryData.startTime && entryData.endTime) {
      completionTime = Math.round((entryData.endTime.getTime() - entryData.startTime.getTime()) / 1000)
    }
    
    // Determine entry timing (early/late/normal)
    let entryTiming: 'early' | 'late' | 'normal' = 'normal'
    // This would be determined based on when the raffle started
    // For now, we'll use placeholder logic
    const now = new Date()
    const hour = now.getHours()
    if (hour < 2) entryTiming = 'early' // First 2 hours after raffle start
    if (hour > 22) entryTiming = 'late' // Last 2 hours before raffle end
    
    // Create base activity data
    const activityData = {
      raffleId,
      completionTime,
      entryTiming,
      errorFree: (entryData.errorCount || 0) === 0 && (entryData.retryCount || 0) === 0,
      variantSelected: entryData.variantSelected,
      shippingMethod: entryData.shippingMethod
    }
    
    // Track the main raffle entry
    const results = await trackRaffleEntry(userId, raffleId, completionTime, entryTiming)
    
    // Track variant selection if provided
    if (entryData.variantSelected) {
      const variantResults = await AchievementTracker.trackActivity(
        createActivityTrigger(userId, 'raffle_variant_selected', {
          raffleId,
          variantId: entryData.variantSelected
        })
      )
      results.push(...variantResults)
    }
    
    // Track shipping selection if provided
    if (entryData.shippingMethod) {
      const shippingResults = await AchievementTracker.trackActivity(
        createActivityTrigger(userId, 'raffle_shipping_selected', {
          raffleId,
          shippingMethod: entryData.shippingMethod
        })
      )
      results.push(...shippingResults)
    }
    
    // Handle notifications for any unlocked achievements
    for (const result of results) {
      if (result.wasUnlocked) {
        await AchievementNotificationManager.handleAchievementUnlock(result, userId)
      } else {
        // Handle progress updates
        await AchievementNotificationManager.handleProgressUpdate(
          result.achievement,
          userId,
          result.previousProgress,
          result.newProgress
        )
      }
    }
    
    console.log(`Processed ${results.length} achievement updates for raffle entry`)
    return results
    
  } catch (error) {
    console.error('Error handling raffle entry submission:', error)
    return []
  }
}

/**
 * Handle social requirement completion
 * Call this when a user completes a social media requirement
 */
export async function handleSocialRequirementCompletion(
  userId: string,
  raffleId: string,
  platform: 'instagram' | 'discord' | 'reddit',
  requirementType: 'follow' | 'post' | 'join',
  requirementData?: {
    postUrl?: string;
    engagement?: number; // likes, comments, etc.
    serverId?: string;
    subreddit?: string;
    verified?: boolean;
  }
): Promise<AchievementUnlockResult[]> {
  try {
    console.log(`Processing ${platform} ${requirementType} for user ${userId}`)
    
    // Track the specific social requirement
    const results = await trackSocialRequirement(userId, raffleId, platform, requirementType)
    
    // Track viral engagement if applicable
    if (platform === 'instagram' && requirementType === 'post' && requirementData?.engagement) {
      if (requirementData.engagement >= 100) {
        const viralResults = await AchievementTracker.trackActivity(
          createActivityTrigger(userId, 'viral_post_detected', {
            raffleId,
            platform,
            postUrl: requirementData.postUrl,
            engagement: requirementData.engagement
          })
        )
        results.push(...viralResults)
      }
    }
    
    // Track tag verification if applicable
    if (requirementData?.postUrl?.includes('@syndicaps')) {
      const tagResults = await AchievementTracker.trackActivity(
        createActivityTrigger(userId, 'tag_verified', {
          raffleId,
          platform,
          postUrl: requirementData.postUrl
        })
      )
      results.push(...tagResults)
    }
    
    // Handle notifications
    for (const result of results) {
      if (result.wasUnlocked) {
        await AchievementNotificationManager.handleAchievementUnlock(result, userId)
      } else {
        await AchievementNotificationManager.handleProgressUpdate(
          result.achievement,
          userId,
          result.previousProgress,
          result.newProgress
        )
      }
    }
    
    return results
    
  } catch (error) {
    console.error('Error handling social requirement completion:', error)
    return []
  }
}

/**
 * Handle raffle win
 * Call this when a user wins a raffle
 */
export async function handleRaffleWin(
  userId: string,
  raffleId: string,
  winData: {
    prizeValue?: number;
    prizeCategory?: string;
    multiWin?: boolean; // Won multiple prizes in same raffle
    totalWins?: number; // Total wins for this user
  }
): Promise<AchievementUnlockResult[]> {
  try {
    console.log(`Processing raffle win for user ${userId}`)
    
    const results = await AchievementTracker.trackActivity(
      createActivityTrigger(userId, 'raffle_won', {
        raffleId,
        prizeValue: winData.prizeValue,
        prizeCategory: winData.prizeCategory,
        multiWin: winData.multiWin,
        totalWins: winData.totalWins
      })
    )
    
    // Handle notifications
    for (const result of results) {
      if (result.wasUnlocked) {
        await AchievementNotificationManager.handleAchievementUnlock(result, userId)
      }
    }
    
    return results
    
  } catch (error) {
    console.error('Error handling raffle win:', error)
    return []
  }
}

/**
 * Handle raffle payment completion
 * Call this when a winner completes their payment
 */
export async function handleRafflePaymentCompletion(
  userId: string,
  raffleId: string,
  paymentData: {
    paymentTime?: Date; // When payment was completed
    winTime?: Date; // When they won
    paymentMethod?: string;
    shippingMethod?: string;
    amount?: number;
  }
): Promise<AchievementUnlockResult[]> {
  try {
    console.log(`Processing payment completion for user ${userId}`)
    
    // Calculate payment speed in hours
    let paymentSpeed: number | undefined
    if (paymentData.paymentTime && paymentData.winTime) {
      paymentSpeed = (paymentData.paymentTime.getTime() - paymentData.winTime.getTime()) / (1000 * 60 * 60)
    }
    
    const results = await AchievementTracker.trackActivity(
      createActivityTrigger(userId, 'raffle_payment_completed', {
        raffleId,
        paymentSpeed,
        paymentMethod: paymentData.paymentMethod,
        shippingMethod: paymentData.shippingMethod,
        amount: paymentData.amount
      })
    )
    
    // Track express shipping if selected
    if (paymentData.shippingMethod === 'express') {
      const expressResults = await AchievementTracker.trackActivity(
        createActivityTrigger(userId, 'raffle_shipping_selected', {
          raffleId,
          shippingMethod: 'express'
        })
      )
      results.push(...expressResults)
    }
    
    // Handle notifications
    for (const result of results) {
      if (result.wasUnlocked) {
        await AchievementNotificationManager.handleAchievementUnlock(result, userId)
      }
    }
    
    return results
    
  } catch (error) {
    console.error('Error handling payment completion:', error)
    return []
  }
}

/**
 * Handle raffle product viewing
 * Call this when a user views a raffle product page
 */
export async function handleRaffleProductView(
  userId: string,
  productId: string,
  viewData?: {
    duration?: number; // Time spent viewing in seconds
    raffleId?: string;
    category?: string;
  }
): Promise<AchievementUnlockResult[]> {
  try {
    const results = await AchievementTracker.trackActivity(
      createActivityTrigger(userId, 'raffle_product_viewed', {
        productId,
        raffleId: viewData?.raffleId,
        category: viewData?.category,
        duration: viewData?.duration
      })
    )
    
    // Handle notifications (usually just progress updates for viewing)
    for (const result of results) {
      if (result.wasUnlocked) {
        await AchievementNotificationManager.handleAchievementUnlock(result, userId)
      } else if (result.newProgress > result.previousProgress) {
        await AchievementNotificationManager.handleProgressUpdate(
          result.achievement,
          userId,
          result.previousProgress,
          result.newProgress
        )
      }
    }
    
    return results
    
  } catch (error) {
    console.error('Error handling product view:', error)
    return []
  }
}

/**
 * Handle referral raffle entry
 * Call this when a referred user completes a raffle entry
 */
export async function handleReferralRaffleEntry(
  referrerId: string,
  referredUserId: string,
  raffleId: string
): Promise<AchievementUnlockResult[]> {
  try {
    console.log(`Processing referral raffle entry for referrer ${referrerId}`)
    
    const results = await AchievementTracker.trackActivity(
      createActivityTrigger(referrerId, 'referral_raffle_entry', {
        referredUserId,
        raffleId
      })
    )
    
    // Handle notifications
    for (const result of results) {
      if (result.wasUnlocked) {
        await AchievementNotificationManager.handleAchievementUnlock(result, referrerId)
      }
    }
    
    return results
    
  } catch (error) {
    console.error('Error handling referral raffle entry:', error)
    return []
  }
}

// ===== BATCH PROCESSING =====

/**
 * Process multiple activities at once for better performance
 */
export async function handleBatchActivities(
  activities: Array<{
    userId: string;
    type: 'entry' | 'social' | 'win' | 'payment' | 'view';
    data: any;
  }>
): Promise<AchievementUnlockResult[]> {
  try {
    console.log(`Processing ${activities.length} batch activities`)
    
    const allResults: AchievementUnlockResult[] = []
    
    // Process in smaller batches to avoid overwhelming the system
    const batchSize = 10
    
    for (let i = 0; i < activities.length; i += batchSize) {
      const batch = activities.slice(i, i + batchSize)
      
      const batchPromises = batch.map(async activity => {
        switch (activity.type) {
          case 'entry':
            return handleRaffleEntrySubmission(activity.userId, activity.data.raffleId, activity.data)
          case 'social':
            return handleSocialRequirementCompletion(
              activity.userId, 
              activity.data.raffleId, 
              activity.data.platform, 
              activity.data.requirementType, 
              activity.data
            )
          case 'win':
            return handleRaffleWin(activity.userId, activity.data.raffleId, activity.data)
          case 'payment':
            return handleRafflePaymentCompletion(activity.userId, activity.data.raffleId, activity.data)
          case 'view':
            return handleRaffleProductView(activity.userId, activity.data.productId, activity.data)
          default:
            return []
        }
      })
      
      const batchResults = await Promise.all(batchPromises)
      batchResults.forEach(results => allResults.push(...results))
      
      // Small delay between batches to avoid rate limiting
      if (i + batchSize < activities.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    console.log(`Completed batch processing: ${allResults.length} total results`)
    return allResults
    
  } catch (error) {
    console.error('Error handling batch activities:', error)
    return []
  }
}

// ===== TESTING AND UTILITIES =====

/**
 * Test the achievement system with sample data
 */
export async function testAchievementSystem(userId: string): Promise<void> {
  try {
    console.log('Testing achievement system...')
    
    // Test notification system
    await AchievementNotificationManager.testNotification(userId)
    
    // Test a sample raffle entry
    await handleRaffleEntrySubmission(userId, 'test_raffle_1', {
      completionTime: 120,
      errorCount: 0,
      variantSelected: 'test_variant_1'
    })
    
    // Test social requirement
    await handleSocialRequirementCompletion(userId, 'test_raffle_1', 'instagram', 'follow')
    
    console.log('Achievement system test completed')
    
  } catch (error) {
    console.error('Error testing achievement system:', error)
  }
}

/**
 * Get user's achievement summary
 */
export async function getUserAchievementSummary(userId: string) {
  try {
    const progress = await AchievementTracker.getUserAchievementProgress(userId)
    const recentUnlocks = await AchievementTracker.getRecentUnlocks(userId, 5)
    const notifications = AchievementNotificationManager.getUserNotifications(userId, 10)
    const unreadCount = AchievementNotificationManager.getUnreadCount(userId)
    
    return {
      progress,
      recentUnlocks,
      notifications,
      unreadCount,
      totalCompleted: progress.filter(p => p.isCompleted).length,
      totalInProgress: progress.filter(p => p.progress > 0 && !p.isCompleted).length
    }
  } catch (error) {
    console.error('Error getting user achievement summary:', error)
    return null
  }
}

export default {
  initializeAchievementSystem,
  handleRaffleEntrySubmission,
  handleSocialRequirementCompletion,
  handleRaffleWin,
  handleRafflePaymentCompletion,
  handleRaffleProductView,
  handleReferralRaffleEntry,
  handleBatchActivities,
  testAchievementSystem,
  getUserAchievementSummary
}