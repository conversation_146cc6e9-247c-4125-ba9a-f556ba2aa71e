/**
 * Achievement Notification System
 * 
 * Handles achievement unlock notifications, celebrations, and user alerts.
 * Integrates with the existing notification system and provides various
 * notification channels and celebration effects.
 * 
 * <AUTHOR> Team - Gamification Phase 1
 * @version 1.0.0
 */

import { serverTimestamp, Timestamp } from 'firebase/firestore'
import type { Achievement, AchievementRarity } from './phase1-achievements'
import type { AchievementUnlockResult } from './achievementTracking'
import { formatUnlockNotification, getMilestoneNotifications } from './achievementUtils'

// ===== TYPES =====

export interface NotificationConfig {
  enableSound: boolean;
  enableAnimations: boolean;
  enableToasts: boolean;
  enablePush: boolean;
  autoShare: boolean;
  celebrationLevel: 'minimal' | 'normal' | 'epic';
}

export interface AchievementNotification {
  id: string;
  userId: string;
  achievementId: string;
  type: 'unlock' | 'progress' | 'milestone';
  title: string;
  message: string;
  icon: string;
  rarity: AchievementRarity;
  pointsAwarded?: number;
  milestone?: number;
  isRead: boolean;
  createdAt: Timestamp;
  displayUntil: Timestamp;
  metadata: Record<string, any>;
}

export interface CelebrationEffect {
  type: 'confetti' | 'fireworks' | 'sparkles' | 'glow' | 'bounce';
  duration: number;
  intensity: 'low' | 'medium' | 'high';
  colors: string[];
  sounds?: string[];
}

// ===== NOTIFICATION MANAGER =====

export class AchievementNotificationManager {
  private static notifications: Map<string, AchievementNotification[]> = new Map()
  private static config: NotificationConfig = {
    enableSound: true,
    enableAnimations: true,
    enableToasts: true,
    enablePush: false,
    autoShare: false,
    celebrationLevel: 'normal'
  }

  /**
   * Initialize notification system with user preferences
   */
  static initialize(userConfig?: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...userConfig }
    console.log('Achievement notifications initialized')
  }

  /**
   * Handle achievement unlock notification
   */
  static async handleAchievementUnlock(
    result: AchievementUnlockResult,
    userId: string
  ): Promise<void> {
    if (!result.wasUnlocked) return

    try {
      const { achievement, pointsAwarded, tierBonusApplied } = result
      const notificationData = formatUnlockNotification(result)

      // Create notification record
      const notification: AchievementNotification = {
        id: `unlock_${achievement.id}_${Date.now()}`,
        userId,
        achievementId: achievement.id,
        type: 'unlock',
        title: notificationData.title,
        message: notificationData.message,
        icon: achievement.icon,
        rarity: achievement.rarity,
        pointsAwarded,
        isRead: false,
        createdAt: serverTimestamp() as Timestamp,
        displayUntil: this.calculateDisplayUntil(achievement.rarity),
        metadata: {
          category: achievement.category,
          tierBonus: tierBonusApplied,
          phase: 1
        }
      }

      // Store notification
      await this.storeNotification(notification)

      // Trigger celebration effects
      await this.triggerCelebration(achievement, pointsAwarded)

      // Send various notification types
      if (this.config.enableToasts) {
        this.showToastNotification(notification)
      }

      if (this.config.enablePush) {
        await this.sendPushNotification(notification)
      }

      if (this.config.autoShare && achievement.rarity === 'legendary') {
        this.suggestSocialShare(achievement, pointsAwarded)
      }

      console.log(`Achievement unlock notification sent: ${achievement.title}`)
    } catch (error) {
      console.error('Error handling achievement unlock notification:', error)
    }
  }

  /**
   * Handle progress milestone notifications
   */
  static async handleProgressUpdate(
    achievement: Achievement,
    userId: string,
    previousProgress: number,
    newProgress: number
  ): Promise<void> {
    const milestones = getMilestoneNotifications(previousProgress, newProgress)
    
    for (const milestone of milestones) {
      const notification: AchievementNotification = {
        id: `milestone_${achievement.id}_${milestone.milestone}_${Date.now()}`,
        userId,
        achievementId: achievement.id,
        type: 'milestone',
        title: `Progress Update ${achievement.icon}`,
        message: `${milestone.message} - ${achievement.title}`,
        icon: achievement.icon,
        rarity: achievement.rarity,
        milestone: milestone.milestone,
        isRead: false,
        createdAt: serverTimestamp() as Timestamp,
        displayUntil: this.calculateDisplayUntil('common'),
        metadata: {
          progress: newProgress,
          category: achievement.category,
          phase: 1
        }
      }

      await this.storeNotification(notification)

      // Show lighter notification for milestones
      if (this.config.enableToasts && milestone.milestone >= 50) {
        this.showProgressNotification(notification)
      }
    }
  }

  /**
   * Trigger celebration effects based on achievement rarity
   */
  private static async triggerCelebration(
    achievement: Achievement,
    pointsAwarded: number
  ): Promise<void> {
    if (!this.config.enableAnimations) return

    const effect = this.getCelebrationEffect(achievement.rarity)
    
    // Trigger visual effects
    this.showCelebrationAnimation(effect)

    // Play sound effects
    if (this.config.enableSound) {
      this.playCelebrationSound(achievement.rarity)
    }

    // Special effects for high-value achievements
    if (achievement.rarity === 'legendary' && this.config.celebrationLevel === 'epic') {
      setTimeout(() => this.showEpicCelebration(achievement), 500)
    }
  }

  /**
   * Get celebration effect configuration based on rarity
   */
  private static getCelebrationEffect(rarity: AchievementRarity): CelebrationEffect {
    const effects: Record<AchievementRarity, CelebrationEffect> = {
      common: {
        type: 'sparkles',
        duration: 1500,
        intensity: 'low',
        colors: ['#6B7280', '#9CA3AF'],
        sounds: ['ding.mp3']
      },
      uncommon: {
        type: 'glow',
        duration: 2000,
        intensity: 'medium',
        colors: ['#10B981', '#34D399'],
        sounds: ['chime.mp3']
      },
      rare: {
        type: 'confetti',
        duration: 2500,
        intensity: 'medium',
        colors: ['#3B82F6', '#60A5FA'],
        sounds: ['celebration.mp3']
      },
      epic: {
        type: 'fireworks',
        duration: 3000,
        intensity: 'high',
        colors: ['#8B5CF6', '#A78BFA'],
        sounds: ['fanfare.mp3']
      },
      legendary: {
        type: 'fireworks',
        duration: 4000,
        intensity: 'high',
        colors: ['#F59E0B', '#FBBF24', '#EF4444'],
        sounds: ['epic_fanfare.mp3', 'applause.mp3']
      }
    }

    return effects[rarity]
  }

  /**
   * Show toast notification in UI
   */
  private static showToastNotification(notification: AchievementNotification): void {
    // This would integrate with your existing toast notification system
    // For now, we'll create a custom event that components can listen to
    const event = new CustomEvent('achievement-unlock', {
      detail: {
        notification,
        type: 'unlock',
        autoHide: this.getAutoHideDelay(notification.rarity)
      }
    })

    if (typeof window !== 'undefined') {
      window.dispatchEvent(event)
    }

    console.log(`Toast notification: ${notification.title}`)
  }

  /**
   * Show progress notification
   */
  private static showProgressNotification(notification: AchievementNotification): void {
    const event = new CustomEvent('achievement-progress', {
      detail: {
        notification,
        type: 'progress',
        autoHide: 3000
      }
    })

    if (typeof window !== 'undefined') {
      window.dispatchEvent(event)
    }
  }

  /**
   * Show celebration animation
   */
  private static showCelebrationAnimation(effect: CelebrationEffect): void {
    const event = new CustomEvent('achievement-celebration', {
      detail: { effect }
    })

    if (typeof window !== 'undefined') {
      window.dispatchEvent(event)
    }

    console.log(`Celebration effect: ${effect.type} (${effect.intensity})`)
  }

  /**
   * Play celebration sound
   */
  private static playCelebrationSound(rarity: AchievementRarity): void {
    if (typeof window === 'undefined') return

    try {
      const effect = this.getCelebrationEffect(rarity)
      const soundFile = effect.sounds?.[0]

      if (soundFile) {
        const audio = new Audio(`/sounds/achievements/${soundFile}`)
        audio.volume = 0.5
        audio.play().catch(error => {
          console.warn('Could not play achievement sound:', error)
        })
      }
    } catch (error) {
      console.warn('Error playing celebration sound:', error)
    }
  }

  /**
   * Show epic celebration for legendary achievements
   */
  private static showEpicCelebration(achievement: Achievement): void {
    const event = new CustomEvent('achievement-epic-celebration', {
      detail: {
        achievement,
        duration: 5000
      }
    })

    if (typeof window !== 'undefined') {
      window.dispatchEvent(event)
    }
  }

  /**
   * Send push notification (if enabled and supported)
   */
  private static async sendPushNotification(
    notification: AchievementNotification
  ): Promise<void> {
    if (typeof window === 'undefined' || !('Notification' in window)) return

    try {
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/icons/achievement-icon.png',
          badge: '/icons/achievement-badge.png',
          tag: `achievement-${notification.achievementId}`,
          requireInteraction: notification.rarity === 'legendary'
        })
      } else if (Notification.permission === 'default') {
        const permission = await Notification.requestPermission()
        if (permission === 'granted') {
          await this.sendPushNotification(notification)
        }
      }
    } catch (error) {
      console.warn('Error sending push notification:', error)
    }
  }

  /**
   * Suggest social media sharing for epic achievements
   */
  private static suggestSocialShare(achievement: Achievement, points: number): void {
    const event = new CustomEvent('achievement-share-suggestion', {
      detail: {
        achievement,
        points,
        message: `I just unlocked the "${achievement.title}" achievement and earned ${points} points! 🏆`,
        hashtags: ['Syndicaps', 'Achievement', 'Keycaps']
      }
    })

    if (typeof window !== 'undefined') {
      window.dispatchEvent(event)
    }
  }

  /**
   * Store notification in system (would integrate with backend)
   */
  private static async storeNotification(notification: AchievementNotification): Promise<void> {
    try {
      // Add to in-memory store
      if (!this.notifications.has(notification.userId)) {
        this.notifications.set(notification.userId, [])
      }
      
      const userNotifications = this.notifications.get(notification.userId)!
      userNotifications.unshift(notification)
      
      // Keep only last 50 notifications per user
      if (userNotifications.length > 50) {
        userNotifications.splice(50)
      }

      // Here you would also save to your backend/database
      console.log(`Notification stored: ${notification.id}`)
    } catch (error) {
      console.error('Error storing notification:', error)
    }
  }

  /**
   * Get auto-hide delay based on rarity
   */
  private static getAutoHideDelay(rarity: AchievementRarity): number {
    const delays = {
      common: 3000,
      uncommon: 4000,
      rare: 5000,
      epic: 7000,
      legendary: 10000
    }
    
    return delays[rarity]
  }

  /**
   * Calculate when notification should stop displaying
   */
  private static calculateDisplayUntil(rarity: AchievementRarity): Timestamp {
    const hoursToDisplay = {
      common: 2,
      uncommon: 4,
      rare: 8,
      epic: 24,
      legendary: 72
    }[rarity]

    const displayUntil = new Date(Date.now() + hoursToDisplay * 60 * 60 * 1000)
    return Timestamp.fromDate(displayUntil)
  }

  // ===== PUBLIC API =====

  /**
   * Get user's recent notifications
   */
  static getUserNotifications(userId: string, limit: number = 20): AchievementNotification[] {
    const userNotifications = this.notifications.get(userId) || []
    return userNotifications.slice(0, limit)
  }

  /**
   * Mark notification as read
   */
  static markAsRead(userId: string, notificationId: string): void {
    const userNotifications = this.notifications.get(userId) || []
    const notification = userNotifications.find(n => n.id === notificationId)
    
    if (notification) {
      notification.isRead = true
    }
  }

  /**
   * Get unread notification count
   */
  static getUnreadCount(userId: string): number {
    const userNotifications = this.notifications.get(userId) || []
    return userNotifications.filter(n => !n.isRead).length
  }

  /**
   * Clear old notifications
   */
  static clearOldNotifications(userId: string): void {
    const userNotifications = this.notifications.get(userId) || []
    const now = new Date()
    
    const activeNotifications = userNotifications.filter(n => 
      n.displayUntil.toDate() > now
    )
    
    this.notifications.set(userId, activeNotifications)
  }

  /**
   * Update notification configuration
   */
  static updateConfig(newConfig: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('Notification config updated')
  }

  /**
   * Get current configuration
   */
  static getConfig(): NotificationConfig {
    return { ...this.config }
  }

  /**
   * Test notification system
   */
  static async testNotification(userId: string): Promise<void> {
    const testNotification: AchievementNotification = {
      id: `test_${Date.now()}`,
      userId,
      achievementId: 'test_achievement',
      type: 'unlock',
      title: 'Test Achievement! 🧪',
      message: 'This is a test notification - 100 points earned',
      icon: '🧪',
      rarity: 'rare',
      pointsAwarded: 100,
      isRead: false,
      createdAt: serverTimestamp() as Timestamp,
      displayUntil: this.calculateDisplayUntil('rare'),
      metadata: { test: true }
    }

    await this.storeNotification(testNotification)
    this.showToastNotification(testNotification)
    
    if (this.config.enableAnimations) {
      this.showCelebrationAnimation(this.getCelebrationEffect('rare'))
    }
    
    if (this.config.enableSound) {
      this.playCelebrationSound('rare')
    }
  }
}

// ===== REACT HOOK INTEGRATION =====

/**
 * Custom event listeners for React components
 */
export const achievementEventListeners = {
  onUnlock: (callback: (notification: AchievementNotification) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail.notification)
    window.addEventListener('achievement-unlock', handler as EventListener)
    return () => window.removeEventListener('achievement-unlock', handler as EventListener)
  },

  onProgress: (callback: (notification: AchievementNotification) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail.notification)
    window.addEventListener('achievement-progress', handler as EventListener)
    return () => window.removeEventListener('achievement-progress', handler as EventListener)
  },

  onCelebration: (callback: (effect: CelebrationEffect) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail.effect)
    window.addEventListener('achievement-celebration', handler as EventListener)
    return () => window.removeEventListener('achievement-celebration', handler as EventListener)
  },

  onEpicCelebration: (callback: (achievement: Achievement) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail.achievement)
    window.addEventListener('achievement-epic-celebration', handler as EventListener)
    return () => window.removeEventListener('achievement-epic-celebration', handler as EventListener)
  },

  onShareSuggestion: (callback: (data: any) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail)
    window.addEventListener('achievement-share-suggestion', handler as EventListener)
    return () => window.removeEventListener('achievement-share-suggestion', handler as EventListener)
  }
}

export default AchievementNotificationManager