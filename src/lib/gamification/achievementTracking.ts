/**
 * Achievement Tracking System
 * 
 * Handles tracking of user activities for achievement progress and unlocks.
 * Integrates with the existing gamification system and Phase 1 achievements.
 * 
 * <AUTHOR> Team - Gamification Phase 1
 * @version 1.0.0
 */

import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  setDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  runTransaction,
  Timestamp,
  increment
} from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firebase/gamificationCollections'
import { phase1Achievements, type Achievement, type Phase1Trigger } from './phase1-achievements'

// ===== TYPES =====

export interface ActivityTrigger {
  userId: string;
  trigger: Phase1Trigger;
  data: Record<string, any>;
  timestamp?: Timestamp;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface UserAchievementProgress {
  userId: string;
  achievementId: string;
  progress: number; // 0-100
  isCompleted: boolean;
  unlockedAt?: Timestamp;
  progressData: Record<string, any>; // Specific tracking data
  lastUpdated: Timestamp;
  phase: number;
  triggerCount: number;
}

export interface AchievementUnlockResult {
  achievementId: string;
  achievement: Achievement;
  previousProgress: number;
  newProgress: number;
  wasUnlocked: boolean;
  pointsAwarded: number;
  tierBonusApplied?: number;
}

// ===== ACHIEVEMENT TRACKER =====

export class AchievementTracker {
  /**
   * Track a user activity that may trigger achievement progress
   */
  static async trackActivity(trigger: ActivityTrigger): Promise<AchievementUnlockResult[]> {
    try {
      console.log(`Tracking activity: ${trigger.trigger} for user ${trigger.userId}`)
      
      // Find achievements that use this trigger
      const relevantAchievements = phase1Achievements.filter(achievement =>
        achievement.isActive && achievement.gamificationTriggers.includes(trigger.trigger)
      );

      if (relevantAchievements.length === 0) {
        console.log(`No achievements found for trigger: ${trigger.trigger}`)
        return []
      }

      const results: AchievementUnlockResult[] = []

      // Process each relevant achievement
      for (const achievement of relevantAchievements) {
        try {
          const result = await this.processAchievementProgress(achievement, trigger)
          if (result) {
            results.push(result)
          }
        } catch (error) {
          console.error(`Error processing achievement ${achievement.id}:`, error)
        }
      }

      // Log the activity for analytics
      await this.logActivity(trigger)

      return results
    } catch (error) {
      console.error('Error tracking activity:', error)
      return []
    }
  }

  /**
   * Process achievement progress for a specific achievement
   */
  private static async processAchievementProgress(
    achievement: Achievement,
    trigger: ActivityTrigger
  ): Promise<AchievementUnlockResult | null> {
    return await runTransaction(db, async (transaction) => {
      // Get current user achievement progress
      const userAchievementRef = doc(
        collection(db, collections.userAchievements),
        `${trigger.userId}_${achievement.id}`
      )
      
      const userAchievementDoc = await transaction.get(userAchievementRef)
      
      let currentProgress: UserAchievementProgress
      
      if (userAchievementDoc.exists()) {
        currentProgress = userAchievementDoc.data() as UserAchievementProgress
        
        // Skip if already completed
        if (currentProgress.isCompleted) {
          return null
        }
      } else {
        // Initialize new progress tracking
        currentProgress = {
          userId: trigger.userId,
          achievementId: achievement.id,
          progress: 0,
          isCompleted: false,
          progressData: {},
          lastUpdated: serverTimestamp() as Timestamp,
          phase: achievement.metadata?.phase || 1,
          triggerCount: 0
        }
      }

      // Calculate new progress based on achievement requirements
      const { newProgress, progressData } = this.calculateProgress(
        achievement,
        currentProgress,
        trigger
      )

      const previousProgress = currentProgress.progress
      const wasUnlocked = newProgress >= 100 && !currentProgress.isCompleted

      // Update progress
      const updatedProgress: UserAchievementProgress = {
        ...currentProgress,
        progress: newProgress,
        isCompleted: newProgress >= 100,
        unlockedAt: wasUnlocked ? serverTimestamp() as Timestamp : currentProgress.unlockedAt,
        progressData,
        lastUpdated: serverTimestamp() as Timestamp,
        triggerCount: currentProgress.triggerCount + 1
      }

      // Save updated progress
      transaction.set(userAchievementRef, updatedProgress)

      let pointsAwarded = 0
      let tierBonusApplied = 0

      // Award points if achievement was unlocked
      if (wasUnlocked) {
        const basePoints = achievement.rewards.points
        
        // Get user's current tier for bonus calculation
        const userTier = await this.getUserTier(trigger.userId)
        tierBonusApplied = this.calculateTierBonus(basePoints, userTier)
        pointsAwarded = basePoints + tierBonusApplied

        // Create point transaction
        const pointTransactionRef = doc(collection(db, collections.pointTransactions))
        transaction.set(pointTransactionRef, {
          userId: trigger.userId,
          type: 'points_earned',
          amount: pointsAwarded,
          source: 'achievement_unlock',
          description: `Achievement unlocked: ${achievement.title}`,
          timestamp: serverTimestamp(),
          metadata: {
            achievementId: achievement.id,
            achievementTitle: achievement.title,
            basePoints,
            tierBonus: tierBonusApplied,
            rarity: achievement.rarity,
            category: achievement.category
          },
          auditTrail: [{
            action: 'points_awarded',
            timestamp: serverTimestamp(),
            source: 'achievement_system'
          }]
        })

        // Update user's total points
        const userRef = doc(collection(db, collections.users), trigger.userId)
        transaction.update(userRef, {
          'gamification.totalPointsEarned': increment(pointsAwarded),
          'gamification.lastAchievementUnlock': serverTimestamp(),
          'stats.achievementsUnlocked': increment(1)
        })

        console.log(`Achievement unlocked: ${achievement.title} for user ${trigger.userId} (${pointsAwarded} points)`)
      }

      return {
        achievementId: achievement.id,
        achievement,
        previousProgress,
        newProgress,
        wasUnlocked,
        pointsAwarded,
        tierBonusApplied
      }
    })
  }

  /**
   * Calculate progress based on achievement requirements
   */
  private static calculateProgress(
    achievement: Achievement,
    currentProgress: UserAchievementProgress,
    trigger: ActivityTrigger
  ): { newProgress: number; progressData: Record<string, any> } {
    const requirement = achievement.requirements[0] // Phase 1 achievements have single requirements
    const progressData = { ...currentProgress.progressData }

    switch (requirement.type) {
      case 'raffle_entries_count':
      case 'total_raffle_entries':
        progressData.entriesCount = (progressData.entriesCount || 0) + 1
        return {
          newProgress: Math.min(100, (progressData.entriesCount / requirement.target) * 100),
          progressData
        }

      case 'consecutive_raffle_entries':
        if (trigger.trigger === 'raffle_entry_submitted') {
          const lastEntryDate = progressData.lastEntryDate
          const today = new Date().toDateString()
          
          if (lastEntryDate === today) {
            // Same day, don't increment streak
            return { newProgress: currentProgress.progress, progressData }
          }
          
          const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()
          
          if (lastEntryDate === yesterday || !lastEntryDate) {
            // Consecutive day or first entry
            progressData.currentStreak = (progressData.currentStreak || 0) + 1
            progressData.lastEntryDate = today
          } else {
            // Streak broken, reset
            progressData.currentStreak = 1
            progressData.lastEntryDate = today
          }
          
          return {
            newProgress: Math.min(100, (progressData.currentStreak / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'complete_all_social_requirements':
        if (trigger.trigger === 'raffle_social_requirements_completed') {
          progressData.completionsCount = (progressData.completionsCount || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.completionsCount / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'entry_completion_seconds':
        if (trigger.trigger === 'raffle_entry_submitted' && trigger.data.completionTime) {
          const completionTime = trigger.data.completionTime
          if (completionTime <= requirement.target) {
            // Fast completion achieved
            progressData.fastCompletions = (progressData.fastCompletions || 0) + 1
            return {
              newProgress: 100,
              progressData
            }
          }
        }
        break

      case 'early_entry_minutes':
        if (trigger.trigger === 'raffle_entry_submitted' && trigger.data.entryTiming === 'early') {
          progressData.earlyEntries = (progressData.earlyEntries || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.earlyEntries / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'late_entry_minutes':
        if (trigger.trigger === 'raffle_entry_submitted' && trigger.data.entryTiming === 'late') {
          progressData.lateEntries = (progressData.lateEntries || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.lateEntries / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'raffle_products_viewed':
        if (trigger.trigger === 'raffle_product_viewed') {
          const viewedProducts = new Set(progressData.viewedProducts || [])
          viewedProducts.add(trigger.data.productId)
          progressData.viewedProducts = Array.from(viewedProducts)
          
          return {
            newProgress: Math.min(100, (progressData.viewedProducts.length / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'raffle_wins_count':
        if (trigger.trigger === 'raffle_won') {
          progressData.winsCount = (progressData.winsCount || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.winsCount / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'payment_speed_hours':
        if (trigger.trigger === 'raffle_payment_completed' && trigger.data.paymentSpeed) {
          const paymentSpeed = trigger.data.paymentSpeed // in hours
          if (paymentSpeed <= requirement.target) {
            progressData.fastPayments = (progressData.fastPayments || 0) + 1
            return {
              newProgress: 100,
              progressData
            }
          }
        }
        break

      case 'instagram_follow':
        if (trigger.trigger === 'instagram_follow_verified') {
          progressData.instagramFollows = (progressData.instagramFollows || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.instagramFollows / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'instagram_posts_shared':
        if (trigger.trigger === 'instagram_post_verified') {
          progressData.instagramPosts = (progressData.instagramPosts || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.instagramPosts / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'discord_joins_count':
        if (trigger.trigger === 'discord_join_verified') {
          progressData.discordJoins = (progressData.discordJoins || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.discordJoins / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'reddit_follows_count':
        if (trigger.trigger === 'reddit_follow_verified') {
          progressData.redditFollows = (progressData.redditFollows || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.redditFollows / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'multi_platform_completions':
        if (trigger.trigger === 'raffle_social_requirement_completed') {
          // Track platforms completed per raffle
          const raffleId = trigger.data.raffleId
          const platform = trigger.data.platform
          
          if (!progressData.raffleCompletions) {
            progressData.raffleCompletions = {}
          }
          
          if (!progressData.raffleCompletions[raffleId]) {
            progressData.raffleCompletions[raffleId] = new Set()
          }
          
          progressData.raffleCompletions[raffleId].add(platform)
          
          // Count raffles with all 3 platforms completed
          const completedRaffles = Object.values(progressData.raffleCompletions).filter(
            (platforms: any) => platforms.size >= 3
          ).length
          
          return {
            newProgress: Math.min(100, (completedRaffles / requirement.target) * 100),
            progressData
          }
        }
        break

      // Add more requirement types as needed
      default:
        console.warn(`Unknown requirement type: ${requirement.type}`)
    }

    return { newProgress: currentProgress.progress, progressData }
  }

  /**
   * Get user's current tier for bonus calculation
   */
  private static async getUserTier(userId: string): Promise<string> {
    try {
      const userDoc = await getDocs(
        query(
          collection(db, collections.users),
          where('__name__', '==', userId),
          limit(1)
        )
      )
      
      if (!userDoc.empty) {
        const userData = userDoc.docs[0].data()
        return userData.gamification?.tier || 'bronze'
      }
      
      return 'bronze'
    } catch (error) {
      console.error('Error getting user tier:', error)
      return 'bronze'
    }
  }

  /**
   * Calculate tier bonus points
   */
  private static calculateTierBonus(basePoints: number, tier: string): number {
    const bonuses = {
      bronze: 0,
      silver: 0.05,
      gold: 0.10,
      platinum: 0.15
    }
    
    const multiplier = bonuses[tier as keyof typeof bonuses] || 0
    return Math.round(basePoints * multiplier)
  }

  /**
   * Log activity for analytics
   */
  private static async logActivity(trigger: ActivityTrigger): Promise<void> {
    try {
      await addDoc(collection(db, collections.userActivities), {
        userId: trigger.userId,
        type: `achievement_trigger_${trigger.trigger}`,
        data: trigger.data,
        metadata: {
          ...trigger.metadata,
          source: 'achievement_tracking',
          phase: 1
        },
        createdAt: trigger.timestamp || serverTimestamp()
      })
    } catch (error) {
      console.error('Error logging activity:', error)
    }
  }

  /**
   * Get user's achievement progress for display
   */
  static async getUserAchievementProgress(userId: string): Promise<UserAchievementProgress[]> {
    try {
      const snapshot = await getDocs(
        query(
          collection(db, collections.userAchievements),
          where('userId', '==', userId),
          orderBy('lastUpdated', 'desc')
        )
      )

      return snapshot.docs.map(doc => doc.data() as UserAchievementProgress)
    } catch (error) {
      console.error('Error getting user achievement progress:', error)
      return []
    }
  }

  /**
   * Get recently unlocked achievements for a user
   */
  static async getRecentUnlocks(userId: string, limitCount: number = 10): Promise<UserAchievementProgress[]> {
    try {
      const snapshot = await getDocs(
        query(
          collection(db, collections.userAchievements),
          where('userId', '==', userId),
          where('isCompleted', '==', true),
          orderBy('unlockedAt', 'desc'),
          limit(limitCount)
        )
      )

      return snapshot.docs.map(doc => doc.data() as UserAchievementProgress)
    } catch (error) {
      console.error('Error getting recent unlocks:', error)
      return []
    }
  }

  /**
   * Batch process multiple activities (for efficiency)
   */
  static async trackActivitiesBatch(triggers: ActivityTrigger[]): Promise<AchievementUnlockResult[]> {
    const allResults: AchievementUnlockResult[] = []
    
    // Process in batches of 10 to avoid overwhelming Firestore
    const batchSize = 10
    
    for (let i = 0; i < triggers.length; i += batchSize) {
      const batch = triggers.slice(i, i + batchSize)
      
      const batchPromises = batch.map(trigger => this.trackActivity(trigger))
      const batchResults = await Promise.all(batchPromises)
      
      batchResults.forEach(results => allResults.push(...results))
    }
    
    return allResults
  }
}

// ===== HELPER FUNCTIONS =====

/**
 * Create a standardized activity trigger
 */
export function createActivityTrigger(
  userId: string,
  trigger: Phase1Trigger,
  data: Record<string, any> = {},
  metadata: Record<string, any> = {}
): ActivityTrigger {
  return {
    userId,
    trigger,
    data,
    timestamp: serverTimestamp() as Timestamp,
    metadata: {
      ...metadata,
      phase: 1,
      source: 'raffle_system'
    }
  }
}

/**
 * Quick helper for common raffle entry tracking
 */
export async function trackRaffleEntry(
  userId: string,
  raffleId: string,
  completionTime?: number,
  entryTiming?: 'early' | 'late' | 'normal'
): Promise<AchievementUnlockResult[]> {
  return AchievementTracker.trackActivity(
    createActivityTrigger(userId, 'raffle_entry_submitted', {
      raffleId,
      completionTime,
      entryTiming
    })
  )
}

/**
 * Quick helper for social requirement tracking
 */
export async function trackSocialRequirement(
  userId: string,
  raffleId: string,
  platform: 'instagram' | 'discord' | 'reddit',
  requirementType: string
): Promise<AchievementUnlockResult[]> {
  const triggers: ActivityTrigger[] = [
    createActivityTrigger(userId, `${platform}_${requirementType}_verified` as Phase1Trigger, {
      raffleId,
      platform,
      requirementType
    }),
    createActivityTrigger(userId, 'raffle_social_requirement_completed', {
      raffleId,
      platform,
      requirementType
    })
  ]

  return AchievementTracker.trackActivitiesBatch(triggers)
}

export default AchievementTracker;