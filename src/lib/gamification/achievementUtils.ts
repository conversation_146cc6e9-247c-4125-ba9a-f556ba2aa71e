/**
 * Achievement Utilities
 * 
 * Helper functions and utilities for working with achievements,
 * progress tracking, and user interface integration.
 * 
 * <AUTHOR> Team - Gamification Phase 1
 * @version 1.0.0
 */

import { phase1Achievements, type Achievement, type AchievementRarity, type AchievementCategory } from './phase1-achievements'
import type { UserAchievementProgress, AchievementUnlockResult } from './achievementTracking'

// ===== DISPLAY UTILITIES =====

/**
 * Get display color for achievement rarity
 */
export function getRarityColor(rarity: AchievementRarity): {
  primary: string;
  secondary: string;
  gradient: string;
} {
  const colors = {
    common: {
      primary: '#6B7280',
      secondary: '#9CA3AF',
      gradient: 'from-gray-500 to-gray-600'
    },
    uncommon: {
      primary: '#10B981',
      secondary: '#34D399',
      gradient: 'from-emerald-500 to-emerald-600'
    },
    rare: {
      primary: '#3B82F6',
      secondary: '#60A5FA',
      gradient: 'from-blue-500 to-blue-600'
    },
    epic: {
      primary: '#8B5CF6',
      secondary: '#A78BFA',
      gradient: 'from-violet-500 to-violet-600'
    },
    legendary: {
      primary: '#F59E0B',
      secondary: '#FBBF24',
      gradient: 'from-amber-500 to-amber-600'
    }
  }
  
  return colors[rarity]
}

/**
 * Get category display information
 */
export function getCategoryInfo(category: AchievementCategory): {
  name: string;
  description: string;
  icon: string;
  color: string;
} {
  const categoryInfo = {
    raffle_entry: {
      name: 'Raffle Entry',
      description: 'Achievements for participating in raffles',
      icon: '🎫',
      color: '#6366F1'
    },
    raffle_success: {
      name: 'Raffle Success',
      description: 'Achievements for winning raffles',
      icon: '🏆',
      color: '#F59E0B'
    },
    raffle_social: {
      name: 'Social Media',
      description: 'Achievements for social media engagement',
      icon: '📱',
      color: '#EF4444'
    },
    raffle_timing: {
      name: 'Perfect Timing',
      description: 'Achievements for entry timing',
      icon: '⏰',
      color: '#10B981'
    },
    raffle_engagement: {
      name: 'Engagement',
      description: 'Achievements for platform engagement',
      icon: '🔍',
      color: '#8B5CF6'
    },
    social_engagement: {
      name: 'Community',
      description: 'Achievements for community building',
      icon: '🤝',
      color: '#06B6D4'
    },
    profile: {
      name: 'Profile',
      description: 'Achievements for profile completion',
      icon: '👤',
      color: '#84CC16'
    },
    shopping: {
      name: 'Shopping',
      description: 'Achievements for shopping activities',
      icon: '🛍️',
      color: '#F97316'
    }
  }
  
  return categoryInfo[category]
}

/**
 * Format achievement progress percentage
 */
export function formatProgress(progress: number): string {
  return `${Math.round(progress)}%`
}

/**
 * Get progress bar color based on completion
 */
export function getProgressColor(progress: number): string {
  if (progress >= 100) return 'bg-green-500'
  if (progress >= 75) return 'bg-blue-500'
  if (progress >= 50) return 'bg-yellow-500'
  if (progress >= 25) return 'bg-orange-500'
  return 'bg-gray-300'
}

/**
 * Calculate estimated time to complete achievement
 */
export function estimateCompletionTime(
  achievement: Achievement,
  currentProgress: UserAchievementProgress
): string {
  if (currentProgress.isCompleted) return 'Completed'
  
  const progressPercent = currentProgress.progress
  const requirement = achievement.requirements[0]
  
  // Estimate based on current progress rate
  if (currentProgress.triggerCount > 0 && progressPercent > 0) {
    const averageProgressPerTrigger = progressPercent / currentProgress.triggerCount
    const remainingProgress = 100 - progressPercent
    const estimatedTriggersNeeded = Math.ceil(remainingProgress / averageProgressPerTrigger)
    
    // Estimate time based on activity type
    const timeEstimates = {
      raffle_entries_count: 'days',
      instagram_posts_shared: 'weeks',
      discord_joins_count: 'days',
      consecutive_raffle_entries: 'weeks'
    }
    
    const timeUnit = timeEstimates[requirement.type as keyof typeof timeEstimates] || 'days'
    
    if (estimatedTriggersNeeded <= 1) return `< 1 ${timeUnit.slice(0, -1)}`
    if (estimatedTriggersNeeded <= 7) return `${estimatedTriggersNeeded} ${timeUnit}`
    
    return `${Math.ceil(estimatedTriggersNeeded / 7)} weeks`
  }
  
  return 'Unknown'
}

// ===== ACHIEVEMENT FILTERING AND SORTING =====

/**
 * Filter achievements by various criteria
 */
export function filterAchievements(
  achievements: Achievement[],
  filters: {
    category?: AchievementCategory[];
    rarity?: AchievementRarity[];
    completed?: boolean;
    inProgress?: boolean;
    searchTerm?: string;
  }
): Achievement[] {
  return achievements.filter(achievement => {
    // Category filter
    if (filters.category && !filters.category.includes(achievement.category)) {
      return false
    }
    
    // Rarity filter
    if (filters.rarity && !filters.rarity.includes(achievement.rarity)) {
      return false
    }
    
    // Search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      const matchesTitle = achievement.title.toLowerCase().includes(searchLower)
      const matchesDescription = achievement.description.toLowerCase().includes(searchLower)
      
      if (!matchesTitle && !matchesDescription) {
        return false
      }
    }
    
    return true
  })
}

/**
 * Sort achievements by various criteria
 */
export function sortAchievements(
  achievements: Achievement[],
  sortBy: 'title' | 'rarity' | 'category' | 'points' | 'difficulty',
  direction: 'asc' | 'desc' = 'asc'
): Achievement[] {
  const sorted = [...achievements].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'title':
        comparison = a.title.localeCompare(b.title)
        break
        
      case 'rarity':
        const rarityOrder = { common: 0, uncommon: 1, rare: 2, epic: 3, legendary: 4 }
        comparison = rarityOrder[a.rarity] - rarityOrder[b.rarity]
        break
        
      case 'category':
        comparison = a.category.localeCompare(b.category)
        break
        
      case 'points':
        comparison = a.rewards.points - b.rewards.points
        break
        
      case 'difficulty':
        // Estimate difficulty based on requirements
        const aDifficulty = estimateDifficulty(a)
        const bDifficulty = estimateDifficulty(b)
        comparison = aDifficulty - bDifficulty
        break
    }
    
    return direction === 'desc' ? -comparison : comparison
  })
  
  return sorted
}

/**
 * Estimate achievement difficulty (0-10 scale)
 */
function estimateDifficulty(achievement: Achievement): number {
  const requirement = achievement.requirements[0]
  const basePoints = achievement.rewards.points
  
  // Base difficulty from rarity
  const rarityDifficulty = {
    common: 2,
    uncommon: 4,
    rare: 6,
    epic: 8,
    legendary: 10
  }[achievement.rarity]
  
  // Adjust based on requirement target
  let targetDifficulty = 0
  if (requirement.target) {
    if (requirement.target <= 1) targetDifficulty = 1
    else if (requirement.target <= 5) targetDifficulty = 2
    else if (requirement.target <= 10) targetDifficulty = 4
    else if (requirement.target <= 25) targetDifficulty = 6
    else targetDifficulty = 8
  }
  
  // Adjust based on points reward
  const pointsDifficulty = Math.min(3, basePoints / 500)
  
  return Math.round((rarityDifficulty + targetDifficulty + pointsDifficulty) / 3)
}

// ===== ACHIEVEMENT CHAINS AND PREREQUISITES =====

/**
 * Get next achievement in a progress chain
 */
export function getNextInChain(achievement: Achievement): Achievement | null {
  if (!achievement.progressChain) return null
  
  const currentIndex = achievement.progressChain.indexOf(achievement.id)
  if (currentIndex === -1 || currentIndex === achievement.progressChain.length - 1) {
    return null
  }
  
  const nextId = achievement.progressChain[currentIndex + 1]
  return phase1Achievements.find(a => a.id === nextId) || null
}

/**
 * Get previous achievement in a progress chain
 */
export function getPreviousInChain(achievement: Achievement): Achievement | null {
  if (!achievement.progressChain) return null
  
  const currentIndex = achievement.progressChain.indexOf(achievement.id)
  if (currentIndex <= 0) return null
  
  const previousId = achievement.progressChain[currentIndex - 1]
  return phase1Achievements.find(a => a.id === previousId) || null
}

/**
 * Check if achievement prerequisites are met
 */
export function arePrerequisitesMet(
  achievement: Achievement,
  userProgress: UserAchievementProgress[]
): boolean {
  if (!achievement.prerequisites || achievement.prerequisites.length === 0) {
    return true
  }
  
  return achievement.prerequisites.every(prereqId => {
    const prereqProgress = userProgress.find(p => p.achievementId === prereqId)
    return prereqProgress?.isCompleted || false
  })
}

/**
 * Get all achievements in a progress chain
 */
export function getChainAchievements(achievement: Achievement): Achievement[] {
  if (!achievement.progressChain) return [achievement]
  
  return achievement.progressChain
    .map(id => phase1Achievements.find(a => a.id === id))
    .filter(Boolean) as Achievement[]
}

// ===== STATISTICS AND ANALYTICS =====

/**
 * Calculate user achievement statistics
 */
export function calculateUserStats(userProgress: UserAchievementProgress[]): {
  total: number;
  completed: number;
  inProgress: number;
  completionRate: number;
  totalPoints: number;
  averageProgress: number;
  categoryCounts: Record<string, { completed: number; total: number }>;
  rarityCounts: Record<string, { completed: number; total: number }>;
} {
  const completed = userProgress.filter(p => p.isCompleted)
  const inProgress = userProgress.filter(p => p.progress > 0 && !p.isCompleted)
  
  // Calculate points from completed achievements
  const totalPoints = completed.reduce((sum, progress) => {
    const achievement = phase1Achievements.find(a => a.id === progress.achievementId)
    return sum + (achievement?.rewards.points || 0)
  }, 0)
  
  // Calculate average progress
  const averageProgress = userProgress.length > 0
    ? userProgress.reduce((sum, p) => sum + p.progress, 0) / userProgress.length
    : 0
  
  // Category counts
  const categoryCounts: Record<string, { completed: number; total: number }> = {}
  const rarityCounts: Record<string, { completed: number; total: number }> = {}
  
  phase1Achievements.forEach(achievement => {
    const userProg = userProgress.find(p => p.achievementId === achievement.id)
    const isCompleted = userProg?.isCompleted || false
    
    // Category stats
    if (!categoryCounts[achievement.category]) {
      categoryCounts[achievement.category] = { completed: 0, total: 0 }
    }
    categoryCounts[achievement.category].total++
    if (isCompleted) categoryCounts[achievement.category].completed++
    
    // Rarity stats
    if (!rarityCounts[achievement.rarity]) {
      rarityCounts[achievement.rarity] = { completed: 0, total: 0 }
    }
    rarityCounts[achievement.rarity].total++
    if (isCompleted) rarityCounts[achievement.rarity].completed++
  })
  
  return {
    total: userProgress.length,
    completed: completed.length,
    inProgress: inProgress.length,
    completionRate: userProgress.length > 0 ? (completed.length / userProgress.length) * 100 : 0,
    totalPoints,
    averageProgress,
    categoryCounts,
    rarityCounts
  }
}

/**
 * Get achievements close to completion
 */
export function getAlmostCompleted(
  userProgress: UserAchievementProgress[],
  threshold: number = 80
): UserAchievementProgress[] {
  return userProgress
    .filter(p => !p.isCompleted && p.progress >= threshold)
    .sort((a, b) => b.progress - a.progress)
}

/**
 * Get recommended achievements for user
 */
export function getRecommendedAchievements(
  userProgress: UserAchievementProgress[],
  limit: number = 5
): Achievement[] {
  const userAchievementIds = new Set(userProgress.map(p => p.achievementId))
  
  // Find achievements not yet started or in progress
  const availableAchievements = phase1Achievements.filter(achievement => {
    // Skip if already completed
    const progress = userProgress.find(p => p.achievementId === achievement.id)
    if (progress?.isCompleted) return false
    
    // Check prerequisites
    return arePrerequisitesMet(achievement, userProgress)
  })
  
  // Sort by difficulty and points to recommend good starting achievements
  return sortAchievements(availableAchievements, 'difficulty', 'asc')
    .slice(0, limit)
}

// ===== NOTIFICATION HELPERS =====

/**
 * Format achievement unlock notification message
 */
export function formatUnlockNotification(result: AchievementUnlockResult): {
  title: string;
  message: string;
  type: 'success' | 'epic' | 'legendary';
} {
  const { achievement, pointsAwarded, tierBonusApplied } = result
  
  let type: 'success' | 'epic' | 'legendary' = 'success'
  if (achievement.rarity === 'legendary') type = 'legendary'
  else if (achievement.rarity === 'epic') type = 'epic'
  
  const bonusText = tierBonusApplied && tierBonusApplied > 0 
    ? ` (+${tierBonusApplied} tier bonus)`
    : ''
  
  return {
    title: `Achievement Unlocked! ${achievement.icon}`,
    message: `${achievement.title} - ${pointsAwarded} points earned${bonusText}`,
    type
  }
}

/**
 * Get milestone progress notifications
 */
export function getMilestoneNotifications(
  previousProgress: number,
  newProgress: number
): { message: string; milestone: number }[] {
  const milestones = [25, 50, 75]
  const notifications: { message: string; milestone: number }[] = []
  
  milestones.forEach(milestone => {
    if (previousProgress < milestone && newProgress >= milestone) {
      notifications.push({
        message: `${milestone}% progress toward achievement!`,
        milestone
      })
    }
  })
  
  return notifications
}

export default {
  getRarityColor,
  getCategoryInfo,
  formatProgress,
  getProgressColor,
  estimateCompletionTime,
  filterAchievements,
  sortAchievements,
  getNextInChain,
  getPreviousInChain,
  arePrerequisitesMet,
  getChainAchievements,
  calculateUserStats,
  getAlmostCompleted,
  getRecommendedAchievements,
  formatUnlockNotification,
  getMilestoneNotifications
}