/**
 * Advanced Notification System
 * 
 * Enhanced notification system for Phase 2 achievements including email digests,
 * mobile push notifications, social sharing, and advanced celebration features.
 * 
 * <AUTHOR> Team - Gamification Phase 2
 * @version 1.0.0
 */

import { serverTimestamp, Timestamp } from 'firebase/firestore'
import type { Achievement } from './phase1-achievements'
import type { Phase2Achievement } from './phase2-achievements'
import type { AchievementUnlockResult } from './achievementTracking'
import { AchievementNotificationManager, type NotificationConfig, type AchievementNotification } from './achievementNotifications'

// ===== EXTENDED TYPES =====

export interface AdvancedNotificationConfig extends NotificationConfig {
  emailDigests: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    includeProgress: boolean;
    includeRecommendations: boolean;
  };
  mobileNotifications: {
    enabled: boolean;
    categories: string[];
    quietHours: {
      start: string; // HH:MM format
      end: string;   // HH:MM format
    };
    vibration: boolean;
  };
  socialSharing: {
    autoSuggest: boolean;
    platforms: ('twitter' | 'facebook' | 'instagram' | 'discord')[];
    includeStats: boolean;
    customMessages: boolean;
  };
  advancedCelebrations: {
    leaderboardAnnouncements: boolean;
    communityFeed: boolean;
    personalizedEffects: boolean;
    soundPacks: string;
  };
}

export interface EmailDigest {
  userId: string;
  type: 'daily' | 'weekly' | 'monthly';
  achievements: {
    unlocked: AchievementUnlockResult[];
    progress: { achievement: Achievement | Phase2Achievement; progress: number }[];
    recommendations: (Achievement | Phase2Achievement)[];
  };
  stats: {
    totalPoints: number;
    newPoints: number;
    rank: number;
    rankChange: number;
  };
  generatedAt: Timestamp;
}

export interface MobileNotification {
  id: string;
  userId: string;
  title: string;
  body: string;
  category: string;
  priority: 'low' | 'normal' | 'high';
  scheduledFor?: Timestamp;
  data: Record<string, any>;
  deepLink?: string;
}

export interface SocialShareContent {
  platform: 'twitter' | 'facebook' | 'instagram' | 'discord';
  achievement: Achievement | Phase2Achievement;
  message: string;
  hashtags: string[];
  imageUrl?: string;
  stats?: {
    totalAchievements: number;
    totalPoints: number;
    currentRank: number;
  };
}

export interface LeaderboardAnnouncement {
  userId: string;
  achievement: Achievement | Phase2Achievement;
  rank: number;
  category: string;
  message: string;
  timestamp: Timestamp;
}

// ===== ADVANCED NOTIFICATION MANAGER =====

export class AdvancedNotificationManager extends AchievementNotificationManager {
  private static advancedConfig: AdvancedNotificationConfig = {
    // Base config from parent class
    enableSound: true,
    enableAnimations: true,
    enableToasts: true,
    enablePush: false,
    autoShare: false,
    celebrationLevel: 'normal',
    
    // Advanced features
    emailDigests: {
      enabled: false,
      frequency: 'weekly',
      includeProgress: true,
      includeRecommendations: true
    },
    mobileNotifications: {
      enabled: false,
      categories: ['epic', 'legendary', 'milestone'],
      quietHours: {
        start: '22:00',
        end: '08:00'
      },
      vibration: true
    },
    socialSharing: {
      autoSuggest: false,
      platforms: ['twitter', 'discord'],
      includeStats: true,
      customMessages: false
    },
    advancedCelebrations: {
      leaderboardAnnouncements: true,
      communityFeed: true,
      personalizedEffects: false,
      soundPacks: 'default'
    }
  }

  /**
   * Initialize advanced notification system
   */
  static initializeAdvanced(config?: Partial<AdvancedNotificationConfig>): void {
    this.advancedConfig = { ...this.advancedConfig, ...config }
    
    // Initialize base notification system
    super.initialize(this.advancedConfig)
    
    // Setup advanced features
    this.setupEmailDigests()
    this.setupMobileNotifications()
    this.setupSocialSharing()
    
    console.log('Advanced notification system initialized')
  }

  /**
   * Handle Phase 2 achievement unlock with advanced features
   */
  static async handleAdvancedAchievementUnlock(
    result: AchievementUnlockResult,
    userId: string
  ): Promise<void> {
    if (!result.wasUnlocked) return

    try {
      // Handle base notification
      await super.handleAchievementUnlock(result, userId)

      const achievement = result.achievement as Phase2Achievement
      
      // Advanced Phase 2 features
      if (achievement.advancedFeatures) {
        // Email notification for important achievements
        if (achievement.advancedFeatures.emailWorthy && this.advancedConfig.emailDigests.enabled) {
          await this.sendAchievementEmail(result, userId)
        }

        // Mobile push notification
        if (this.shouldSendMobileNotification(achievement)) {
          await this.sendMobileNotification(result, userId)
        }

        // Social sharing suggestion
        if (achievement.advancedFeatures.socialShareable && this.advancedConfig.socialSharing.autoSuggest) {
          await this.suggestAdvancedSocialShare(result, userId)
        }

        // Leaderboard announcement
        if (achievement.advancedFeatures.leaderboardEligible && this.advancedConfig.advancedCelebrations.leaderboardAnnouncements) {
          await this.announceToLeaderboard(result, userId)
        }

        // Community feed update
        if (this.advancedConfig.advancedCelebrations.communityFeed) {
          await this.updateCommunityFeed(result, userId)
        }
      }

      console.log(`Advanced notification processing complete for: ${achievement.title}`)
    } catch (error) {
      console.error('Error handling advanced achievement unlock:', error)
    }
  }

  /**
   * Send achievement email notification
   */
  private static async sendAchievementEmail(
    result: AchievementUnlockResult,
    userId: string
  ): Promise<void> {
    try {
      const achievement = result.achievement
      const emailContent = {
        to: userId, // Would be resolved to actual email
        subject: `🏆 Achievement Unlocked: ${achievement.title}!`,
        template: 'achievement_unlock',
        data: {
          achievementTitle: achievement.title,
          achievementDescription: achievement.description,
          achievementIcon: achievement.icon,
          pointsEarned: result.pointsAwarded,
          rarity: achievement.rarity,
          unlockedAt: new Date().toISOString()
        }
      }

      // Here you would integrate with your email service (SendGrid, Mailgun, etc.)
      console.log('Email notification sent:', emailContent)
      
      // Log the email for analytics
      await this.logEmailNotification(userId, 'achievement_unlock', achievement.id)
      
    } catch (error) {
      console.error('Error sending achievement email:', error)
    }
  }

  /**
   * Send mobile push notification
   */
  private static async sendMobileNotification(
    result: AchievementUnlockResult,
    userId: string
  ): Promise<void> {
    try {
      const achievement = result.achievement
      
      // Check quiet hours
      if (this.isQuietHours()) {
        console.log('Scheduling mobile notification for later (quiet hours)')
        await this.scheduleMobileNotification(result, userId)
        return
      }

      const notification: MobileNotification = {
        id: `achievement_${achievement.id}_${Date.now()}`,
        userId,
        title: `🏆 Achievement Unlocked!`,
        body: `${achievement.title} - ${result.pointsAwarded} points earned`,
        category: achievement.rarity,
        priority: this.getMobilePriority(achievement.rarity),
        data: {
          achievementId: achievement.id,
          points: result.pointsAwarded,
          type: 'achievement_unlock'
        },
        deepLink: `/achievements/${achievement.id}`
      }

      // Send via your mobile notification service
      await this.sendMobileNotificationToService(notification)
      
      console.log('Mobile notification sent:', notification.title)
      
    } catch (error) {
      console.error('Error sending mobile notification:', error)
    }
  }

  /**
   * Suggest social media sharing
   */
  private static async suggestAdvancedSocialShare(
    result: AchievementUnlockResult,
    userId: string
  ): Promise<void> {
    try {
      const achievement = result.achievement
      const userStats = await this.getUserStats(userId)
      
      for (const platform of this.advancedConfig.socialSharing.platforms) {
        const shareContent = this.generateShareContent(platform, achievement, result, userStats)
        
        const event = new CustomEvent('achievement-social-share', {
          detail: {
            platform,
            content: shareContent,
            achievement,
            autoSuggest: true
          }
        })

        if (typeof window !== 'undefined') {
          window.dispatchEvent(event)
        }
      }
      
    } catch (error) {
      console.error('Error suggesting social share:', error)
    }
  }

  /**
   * Announce achievement to leaderboard
   */
  private static async announceToLeaderboard(
    result: AchievementUnlockResult,
    userId: string
  ): Promise<void> {
    try {
      const achievement = result.achievement
      const userRank = await this.getUserRank(userId, achievement.category)
      
      const announcement: LeaderboardAnnouncement = {
        userId,
        achievement,
        rank: userRank,
        category: achievement.category,
        message: `achieved ${achievement.title}!`,
        timestamp: serverTimestamp() as Timestamp
      }

      // Broadcast to leaderboard system
      const event = new CustomEvent('leaderboard-announcement', {
        detail: announcement
      })

      if (typeof window !== 'undefined') {
        window.dispatchEvent(event)
      }

      console.log(`Leaderboard announcement: User rank ${userRank} in ${achievement.category}`)
      
    } catch (error) {
      console.error('Error announcing to leaderboard:', error)
    }
  }

  /**
   * Update community feed
   */
  private static async updateCommunityFeed(
    result: AchievementUnlockResult,
    userId: string
  ): Promise<void> {
    try {
      const achievement = result.achievement
      const username = await this.getUsername(userId)
      
      const feedUpdate = {
        type: 'achievement_unlock',
        userId,
        username,
        achievement: {
          id: achievement.id,
          title: achievement.title,
          icon: achievement.icon,
          rarity: achievement.rarity
        },
        points: result.pointsAwarded,
        timestamp: serverTimestamp()
      }

      // Broadcast to community feed
      const event = new CustomEvent('community-feed-update', {
        detail: feedUpdate
      })

      if (typeof window !== 'undefined') {
        window.dispatchEvent(event)
      }

      console.log(`Community feed updated for ${achievement.title}`)
      
    } catch (error) {
      console.error('Error updating community feed:', error)
    }
  }

  /**
   * Generate email digest
   */
  static async generateEmailDigest(
    userId: string,
    type: 'daily' | 'weekly' | 'monthly'
  ): Promise<EmailDigest | null> {
    try {
      if (!this.advancedConfig.emailDigests.enabled) return null

      const timeframe = this.getTimeframeForDigest(type)
      const achievements = await this.getRecentAchievements(userId, timeframe)
      const progress = await this.getProgressUpdates(userId, timeframe)
      const recommendations = await this.getRecommendations(userId)
      const stats = await this.getUserStats(userId)

      const digest: EmailDigest = {
        userId,
        type,
        achievements: {
          unlocked: achievements,
          progress,
          recommendations
        },
        stats,
        generatedAt: serverTimestamp() as Timestamp
      }

      return digest
      
    } catch (error) {
      console.error('Error generating email digest:', error)
      return null
    }
  }

  /**
   * Schedule email digests
   */
  private static setupEmailDigests(): void {
    if (!this.advancedConfig.emailDigests.enabled) return

    const frequency = this.advancedConfig.emailDigests.frequency
    const intervalMs = {
      daily: 24 * 60 * 60 * 1000,
      weekly: 7 * 24 * 60 * 60 * 1000,
      monthly: 30 * 24 * 60 * 60 * 1000
    }[frequency]

    // Schedule digest generation (would use a proper job scheduler in production)
    setInterval(async () => {
      console.log(`Generating ${frequency} email digests...`)
      // Would iterate through users and generate digests
    }, intervalMs)
  }

  /**
   * Setup mobile notification system
   */
  private static setupMobileNotifications(): void {
    if (!this.advancedConfig.mobileNotifications.enabled) return

    // Initialize mobile notification service
    console.log('Mobile notifications enabled for categories:', this.advancedConfig.mobileNotifications.categories)
  }

  /**
   * Setup social sharing features
   */
  private static setupSocialSharing(): void {
    if (!this.advancedConfig.socialSharing.autoSuggest) return

    console.log('Social sharing enabled for platforms:', this.advancedConfig.socialSharing.platforms)
  }

  // ===== HELPER METHODS =====

  private static shouldSendMobileNotification(achievement: Achievement | Phase2Achievement): boolean {
    if (!this.advancedConfig.mobileNotifications.enabled) return false
    
    const categories = this.advancedConfig.mobileNotifications.categories
    return categories.includes(achievement.rarity) || categories.includes(achievement.category)
  }

  private static isQuietHours(): boolean {
    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    const { start, end } = this.advancedConfig.mobileNotifications.quietHours
    
    // Simple time comparison (doesn't handle overnight spans)
    return currentTime >= start || currentTime <= end
  }

  private static getMobilePriority(rarity: string): 'low' | 'normal' | 'high' {
    switch (rarity) {
      case 'legendary':
      case 'epic':
        return 'high'
      case 'rare':
        return 'normal'
      default:
        return 'low'
    }
  }

  private static generateShareContent(
    platform: string,
    achievement: Achievement | Phase2Achievement,
    result: AchievementUnlockResult,
    userStats: any
  ): SocialShareContent {
    const baseMessage = `Just unlocked the "${achievement.title}" achievement and earned ${result.pointsAwarded} points!`
    
    const platformMessages = {
      twitter: `${baseMessage} 🏆 #Syndicaps #Achievement #Keycaps`,
      facebook: `${baseMessage}\n\nJoin me on Syndicaps for awesome keycap raffles!`,
      instagram: `${baseMessage} 🎮✨`,
      discord: `${baseMessage} 🎉`
    }

    return {
      platform: platform as any,
      achievement,
      message: platformMessages[platform as keyof typeof platformMessages] || baseMessage,
      hashtags: ['Syndicaps', 'Achievement', achievement.rarity],
      stats: this.advancedConfig.socialSharing.includeStats ? userStats : undefined
    }
  }

  private static async sendMobileNotificationToService(notification: MobileNotification): Promise<void> {
    // Integration with mobile notification service (Firebase Cloud Messaging, etc.)
    console.log('Sending mobile notification:', notification)
  }

  private static async scheduleMobileNotification(result: AchievementUnlockResult, userId: string): Promise<void> {
    // Schedule notification for after quiet hours
    console.log('Scheduling mobile notification for after quiet hours')
  }

  private static async logEmailNotification(userId: string, type: string, achievementId: string): Promise<void> {
    console.log(`Email notification logged: ${type} for ${achievementId}`)
  }

  private static async getUserStats(userId: string): Promise<any> {
    // Get user statistics for sharing
    return {
      totalAchievements: 0,
      totalPoints: 0,
      currentRank: 0
    }
  }

  private static async getUserRank(userId: string, category: string): Promise<number> {
    // Get user's rank in the specified category
    return 1
  }

  private static async getUsername(userId: string): Promise<string> {
    // Get user's display name
    return 'User'
  }

  private static getTimeframeForDigest(type: string): { start: Date; end: Date } {
    const now = new Date()
    const start = new Date()
    
    switch (type) {
      case 'daily':
        start.setDate(now.getDate() - 1)
        break
      case 'weekly':
        start.setDate(now.getDate() - 7)
        break
      case 'monthly':
        start.setMonth(now.getMonth() - 1)
        break
    }
    
    return { start, end: now }
  }

  private static async getRecentAchievements(userId: string, timeframe: any): Promise<AchievementUnlockResult[]> {
    // Get recent achievement unlocks
    return []
  }

  private static async getProgressUpdates(userId: string, timeframe: any): Promise<any[]> {
    // Get progress updates
    return []
  }

  private static async getRecommendations(userId: string): Promise<(Achievement | Phase2Achievement)[]> {
    // Get achievement recommendations
    return []
  }

  /**
   * Update advanced configuration
   */
  static updateAdvancedConfig(newConfig: Partial<AdvancedNotificationConfig>): void {
    this.advancedConfig = { ...this.advancedConfig, ...newConfig }
    console.log('Advanced notification config updated')
  }

  /**
   * Get current advanced configuration
   */
  static getAdvancedConfig(): AdvancedNotificationConfig {
    return { ...this.advancedConfig }
  }
}

export default AdvancedNotificationManager