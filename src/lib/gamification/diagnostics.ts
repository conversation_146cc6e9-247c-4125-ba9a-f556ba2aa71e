/**
 * Gamification System Diagnostics
 * 
 * Diagnostic tools to check system health and identify configuration issues.
 * 
 * <AUTHOR> Team - Gamification Diagnostics
 * @version 1.0.0
 */

// ===== DIAGNOSTIC FUNCTIONS =====

/**
 * Check if all gamification modules can be loaded
 */
export async function checkModuleHealth(): Promise<{
  status: 'healthy' | 'warning' | 'error';
  modules: Record<string, boolean>;
  errors: string[];
}> {
  const modules: Record<string, boolean> = {}
  const errors: string[] = []

  try {
    // Test Phase 1
    const phase1 = await import('./phase1-achievements')
    modules.phase1 = true
    console.log(`✅ Phase 1: ${phase1.phase1Achievements.length} achievements loaded`)
  } catch (error) {
    modules.phase1 = false
    errors.push(`Phase 1 import failed: ${error}`)
  }

  try {
    // Test Phase 2
    const phase2 = await import('./phase2-achievements')
    modules.phase2 = true
    console.log(`✅ Phase 2: ${phase2.phase2Achievements.length} achievements loaded`)
  } catch (error) {
    modules.phase2 = false
    errors.push(`Phase 2 import failed: ${error}`)
  }

  try {
    // Test Phase 3
    const phase3 = await import('./phase3-achievements')
    modules.phase3 = true
    console.log(`✅ Phase 3: ${phase3.phase3Achievements.length} achievements loaded`)
  } catch (error) {
    modules.phase3 = false
    errors.push(`Phase 3 import failed: ${error}`)
  }

  try {
    // Test Dynamic Generation
    const dynamic = await import('./dynamicAchievements')
    modules.dynamicGeneration = true
    console.log(`✅ Dynamic Generation: ${dynamic.achievementTemplates.length} templates loaded`)
  } catch (error) {
    modules.dynamicGeneration = false
    errors.push(`Dynamic generation import failed: ${error}`)
  }

  try {
    // Test Evolution
    const evolution = await import('./achievementEvolution')
    modules.evolution = true
    console.log(`✅ Evolution: ${evolution.evolutionPaths.length} paths loaded`)
  } catch (error) {
    modules.evolution = false
    errors.push(`Evolution import failed: ${error}`)
  }

  const status = errors.length === 0 ? 'healthy' : 
                errors.length <= 2 ? 'warning' : 'error'

  return { status, modules, errors }
}

/**
 * Check Firebase connectivity without importing heavy dependencies
 */
export function checkFirebaseConfig(): {
  status: 'configured' | 'missing' | 'invalid';
  config: Record<string, boolean>;
  recommendations: string[];
} {
  const config: Record<string, boolean> = {}
  const recommendations: string[] = []

  // Check environment variables
  config.hasApiKey = !!process.env.NEXT_PUBLIC_FIREBASE_API_KEY
  config.hasAuthDomain = !!process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
  config.hasProjectId = !!process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
  config.hasStorageBucket = !!process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
  config.hasMessagingSenderId = !!process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
  config.hasAppId = !!process.env.NEXT_PUBLIC_FIREBASE_APP_ID

  const missingVars = Object.entries(config)
    .filter(([, hasValue]) => !hasValue)
    .map(([key]) => key)

  if (missingVars.length > 0) {
    recommendations.push(`Missing Firebase environment variables: ${missingVars.join(', ')}`)
    recommendations.push('Check your .env.local file and ensure all NEXT_PUBLIC_FIREBASE_* variables are set')
  }

  if (!config.hasProjectId) {
    recommendations.push('Firebase Project ID is required for Firestore operations')
  }

  const status = missingVars.length === 0 ? 'configured' :
                missingVars.length <= 2 ? 'missing' : 'invalid'

  return { status, config, recommendations }
}

/**
 * Validate achievement data integrity
 */
export async function validateAchievementIntegrity(): Promise<{
  status: 'valid' | 'warnings' | 'errors';
  totalAchievements: number;
  uniqueIds: boolean;
  pointsConsistency: boolean;
  issues: string[];
}> {
  const issues: string[] = []
  let totalAchievements = 0
  let uniqueIds = true
  let pointsConsistency = true

  try {
    const [phase1, phase2, phase3] = await Promise.all([
      import('./phase1-achievements'),
      import('./phase2-achievements'),
      import('./phase3-achievements')
    ])

    const allAchievements = [
      ...phase1.phase1Achievements,
      ...phase2.phase2Achievements,
      ...phase3.phase3Achievements
    ]

    totalAchievements = allAchievements.length

    // Check unique IDs
    const ids = new Set()
    allAchievements.forEach(achievement => {
      if (ids.has(achievement.id)) {
        uniqueIds = false
        issues.push(`Duplicate achievement ID: ${achievement.id}`)
      }
      ids.add(achievement.id)
    })

    // Check points consistency
    allAchievements.forEach(achievement => {
      if (achievement.points !== achievement.rewards.points) {
        pointsConsistency = false
        issues.push(`Points inconsistency in ${achievement.id}: ${achievement.points} vs ${achievement.rewards.points}`)
      }
    })

    console.log(`✅ Validated ${totalAchievements} achievements`)
    
  } catch (error) {
    issues.push(`Validation failed: ${error}`)
  }

  const status = issues.length === 0 ? 'valid' :
                issues.length <= 3 ? 'warnings' : 'errors'

  return {
    status,
    totalAchievements,
    uniqueIds,
    pointsConsistency,
    issues
  }
}

/**
 * Test system performance
 */
export async function performanceTest(): Promise<{
  loadTime: number;
  validationTime: number;
  memoryUsage: number;
  recommendations: string[];
}> {
  const startTime = performance.now()
  const recommendations: string[] = []

  // Test module loading
  const loadStart = performance.now()
  await checkModuleHealth()
  const loadTime = performance.now() - loadStart

  // Test validation
  const validationStart = performance.now()
  await validateAchievementIntegrity()
  const validationTime = performance.now() - validationStart

  const totalTime = performance.now() - startTime

  // Memory usage (if available)
  const memoryUsage = typeof process !== 'undefined' && process.memoryUsage ? 
    process.memoryUsage().heapUsed / 1024 / 1024 : 0

  // Performance recommendations
  if (loadTime > 1000) {
    recommendations.push('Module loading is slow, consider code splitting')
  }

  if (validationTime > 500) {
    recommendations.push('Validation is slow, consider caching validation results')
  }

  if (memoryUsage > 50) {
    recommendations.push('High memory usage detected, consider lazy loading')
  }

  console.log(`📊 Performance: Load ${loadTime.toFixed(2)}ms, Validation ${validationTime.toFixed(2)}ms, Total ${totalTime.toFixed(2)}ms`)

  return {
    loadTime,
    validationTime,
    memoryUsage,
    recommendations
  }
}

/**
 * Run complete system diagnostics
 */
export async function runDiagnostics(): Promise<{
  overallHealth: 'healthy' | 'warning' | 'error';
  modules: any;
  firebase: any;
  integrity: any;
  performance: any;
  summary: string[];
}> {
  console.log('🔍 Running Gamification System Diagnostics...')

  const [modules, firebase, integrity, performance] = await Promise.all([
    checkModuleHealth(),
    Promise.resolve(checkFirebaseConfig()),
    validateAchievementIntegrity(),
    performanceTest()
  ])

  const summary: string[] = []
  
  // Overall health assessment
  const healthScores = [
    modules.status,
    firebase.status === 'configured' ? 'healthy' : firebase.status === 'missing' ? 'warning' : 'error',
    integrity.status === 'valid' ? 'healthy' : integrity.status === 'warnings' ? 'warning' : 'error'
  ]

  const errorCount = healthScores.filter(s => s === 'error').length
  const warningCount = healthScores.filter(s => s === 'warning').length

  const overallHealth = errorCount > 0 ? 'error' : 
                       warningCount > 0 ? 'warning' : 'healthy'

  // Generate summary
  summary.push(`🎮 Gamification System Health: ${overallHealth.toUpperCase()}`)
  summary.push(`📦 Modules Loaded: ${Object.values(modules.modules).filter(Boolean).length}/${Object.keys(modules.modules).length}`)
  summary.push(`🔥 Firebase Config: ${firebase.status}`)
  summary.push(`✅ Achievement Integrity: ${integrity.status}`)
  summary.push(`⚡ Performance: ${performance.loadTime.toFixed(0)}ms load time`)
  summary.push(`🏆 Total Achievements: ${integrity.totalAchievements}`)

  if (modules.errors.length > 0) {
    summary.push(`❌ Module Errors: ${modules.errors.length}`)
  }

  if (firebase.recommendations.length > 0) {
    summary.push(`💡 Firebase Recommendations: ${firebase.recommendations.length}`)
  }

  if (integrity.issues.length > 0) {
    summary.push(`⚠️ Integrity Issues: ${integrity.issues.length}`)
  }

  console.log('✨ Diagnostics Complete')
  summary.forEach(line => console.log(line))

  return {
    overallHealth,
    modules,
    firebase,
    integrity,
    performance,
    summary
  }
}

/**
 * Quick health check for development
 */
export async function quickHealthCheck(): Promise<boolean> {
  try {
    const result = await runDiagnostics()
    return result.overallHealth !== 'error'
  } catch (error) {
    console.error('Health check failed:', error)
    return false
  }
}

export default {
  checkModuleHealth,
  checkFirebaseConfig,
  validateAchievementIntegrity,
  performanceTest,
  runDiagnostics,
  quickHealthCheck
}