/**
 * Syndicaps Gamification System - Complete Integration
 * 
 * Unified entry point for the entire gamification system across all phases.
 * Provides a cohesive API for achievement tracking, rewards, social features,
 * dynamic generation, and evolution systems.
 * 
 * <AUTHOR> Team - Gamification Complete
 * @version 3.0.0
 */

// ===== IMPORTS =====

// Phase 1 - Core system
import { 
  phase1Achievements,
  phase1Triggers,
  validateAchievement as validatePhase1Achievement,
  getPhase1Stats
} from './phase1-achievements'
import { 
  AchievementTracker,
  createActivityTrigger 
} from './achievementTracking'

// Phase 2 - Advanced features
import {
  phase2Achievements,
  phase2Triggers,
  getPhase2Stats,
  validatePhase2Achievement,
  initializePhase2System,
  trackPhase2Activity,
  Phase2Helpers,
  SocialHelpers
} from './phase2-index'

// Phase 3 - Dynamic and evolution systems
import {
  phase3Achievements,
  phase3Triggers,
  getPhase3Stats,
  validatePhase3Achievement,
  initializePhase3System,
  trackPhase3Activity,
  Phase3Helpers,
  DynamicHelpers,
  EvolutionHelpers
} from './phase3-index'

import type { 
  Achievement as BaseAchievement, 
  AchievementRarity, 
  AchievementCategory
} from './phase1-achievements'
import type { AchievementUnlockResult } from './achievementTracking'
import type { ActivityTrigger } from './achievementTracking'
import type { Phase2Achievement, ExtendedAchievementCategory } from './phase2-achievements'
import type { Phase3Achievement, AllAchievementCategories } from './phase3-achievements'

// Ensure all achievement types have a points property
export interface Achievement extends BaseAchievement {
  points: number
}

// ===== UNIFIED TYPES =====

export type AnyAchievement = Achievement | Phase2Achievement | Phase3Achievement
export type AnyCategory = AchievementCategory | ExtendedAchievementCategory | AllAchievementCategories
export type AnyTrigger = typeof phase1Triggers[number] | typeof phase2Triggers[number] | typeof phase3Triggers[number]

export interface GamificationConfig {
  phases: {
    phase1: boolean;
    phase2: boolean;
    phase3: boolean;
  };
  features: {
    notifications: boolean;
    socialProof: boolean;
    leaderboards: boolean;
    dynamicGeneration: boolean;
    evolutionSystem: boolean;
    seasonalEvents: boolean;
  };
  settings: {
    autoEvolution: boolean;
    dynamicGenerationRate: number; // 0-100 percentage
    seasonalBonuses: boolean;
    socialSharing: boolean;
  };
}

export interface UserGamificationProfile {
  userId: string;
  totalPoints: number;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  achievements: {
    phase1: number;
    phase2: number;
    phase3: number;
    total: number;
    byRarity: Record<AchievementRarity, number>;
    byCategory: Record<string, number>;
    recent: AnyAchievement[];
  };
  streaks: {
    current: number;
    longest: number;
    type: string;
  };
  social: {
    shares: number;
    reactions: number;
    rank: number;
  };
  evolution: {
    totalEvolutions: number;
    availableEvolutions: string[];
    nextMilestone: string;
  };
  seasonal: {
    currentSeasonPoints: number;
    seasonalMultiplier: number;
    holidayParticipation: string[];
  };
  lastActivity: Date;
  joinDate: Date;
}

export interface SystemStats {
  achievements: {
    phase1: ReturnType<typeof getPhase1Stats>;
    phase2: ReturnType<typeof getPhase2Stats>;
    phase3: ReturnType<typeof getPhase3Stats>;
    total: number;
  };
  users: {
    total: number;
    active: number;
    byTier: Record<string, number>;
  };
  activity: {
    totalUnlocks: number;
    dailyActivity: number;
    socialShares: number;
    evolutions: number;
  };
  system: {
    version: string;
    uptime: number;
    health: 'healthy' | 'warning' | 'error';
    features: string[];
  };
}

// ===== MAIN GAMIFICATION SYSTEM =====

export class GamificationSystem {
  private static instance: GamificationSystem | null = null
  private static config: GamificationConfig
  private static isInitialized = false
  private static userProfiles: Map<string, UserGamificationProfile> = new Map()
  
  /**
   * Singleton instance
   */
  static getInstance(config?: GamificationConfig): GamificationSystem {
    if (!this.instance) {
      this.instance = new GamificationSystem()
      if (config) {
        this.config = config
      }
    }
    return this.instance
  }

  /**
   * Initialize the complete gamification system
   */
  static async initialize(config: GamificationConfig): Promise<void> {
    try {
      console.log('🎮 Initializing Syndicaps Gamification System v3.0.0...')
      
      this.config = config
      
      // Validate system before initialization
      const validation = this.validateSystem()
      if (!validation.isValid) {
        throw new Error(`System validation failed: ${validation.errors.join(', ')}`)
      }

      // Initialize phases in order
      if (config.phases.phase1) {
        console.log('📈 Initializing Phase 1 (Core System)...')
        // Phase 1 is always available as it's the foundation
      }

      if (config.phases.phase2) {
        console.log('🚀 Initializing Phase 2 (Advanced Features)...')
        await initializePhase2System({
          notifications: config.features.notifications ? {} : undefined,
          leaderboards: config.features.leaderboards,
          socialProof: config.features.socialProof
        })
      }

      if (config.phases.phase3) {
        console.log('🌟 Initializing Phase 3 (Dynamic & Evolution)...')
        await initializePhase3System({
          dynamicGeneration: config.features.dynamicGeneration,
          evolutionSystem: config.features.evolutionSystem,
          seasonalEvents: config.features.seasonalEvents
        })
      }

      // Start system monitoring
      this.startSystemMonitoring()
      
      this.isInitialized = true
      console.log('✅ Gamification System initialized successfully!')
      
    } catch (error) {
      console.error('❌ Failed to initialize Gamification System:', error)
      throw error
    }
  }

  /**
   * Universal activity tracking across all phases
   */
  static async trackActivity(
    userId: string,
    trigger: string,
    data: Record<string, any> = {},
    options: {
      phase?: 1 | 2 | 3 | 'auto';
      deviceType?: 'desktop' | 'mobile' | 'tablet';
      skipNotifications?: boolean;
      skipSocialUpdates?: boolean;
    } = {}
  ): Promise<{
    phase1Results: AchievementUnlockResult[];
    phase2Results: any[];
    phase3Results: any;
    totalPointsEarned: number;
    newAchievements: AnyAchievement[];
    evolutions: any[];
    notifications: any[];
  }> {
    try {
      if (!this.isInitialized) {
        throw new Error('Gamification system not initialized')
      }

      const results = {
        phase1Results: [] as AchievementUnlockResult[],
        phase2Results: [] as any[],
        phase3Results: null as any,
        totalPointsEarned: 0,
        newAchievements: [] as AnyAchievement[],
        evolutions: [] as any[],
        notifications: [] as any[]
      }

      // Determine which phases to process
      const processPhases = this.determineProcessingPhases(trigger, options.phase)

      // Phase 1 tracking (always process if enabled)
      if (processPhases.includes(1) && this.config.phases.phase1) {
        results.phase1Results = await AchievementTracker.trackActivity(
          createActivityTrigger(userId, trigger as any, {
            ...data,
            deviceType: options.deviceType || 'desktop'
          })
        )
        
        // Calculate points earned
        results.totalPointsEarned += results.phase1Results
          .filter(r => r.wasUnlocked)
          .reduce((sum, r) => sum + (r.achievement.points || 0), 0)
      }

      // Phase 2 tracking
      if (processPhases.includes(2) && this.config.phases.phase2) {
        results.phase2Results = await trackPhase2Activity(
          userId,
          trigger as any,
          data,
          options.deviceType || 'desktop'
        )
        
        // Add Phase 2 points
        results.totalPointsEarned += results.phase2Results
          .filter(r => r.wasUnlocked)
          .reduce((sum, r) => sum + (r.achievement.points || 0), 0)
      }

      // Phase 3 tracking
      if (processPhases.includes(3) && this.config.phases.phase3) {
        results.phase3Results = await trackPhase3Activity(
          userId,
          trigger as any,
          data,
          {
            checkEvolution: this.config.settings.autoEvolution,
            generateDynamic: Math.random() < (this.config.settings.dynamicGenerationRate / 100),
            seasonalBonus: this.config.settings.seasonalBonuses
          }
        )
        
        // Add Phase 3 points and evolutions
        if (results.phase3Results) {
          results.totalPointsEarned += results.phase3Results.achievements
            .filter((a: any) => a.wasUnlocked)
            .reduce((sum: number, a: any) => sum + (a.achievement.points || 0), 0)
          
          results.evolutions = results.phase3Results.evolutions || []
        }
      }

      // Update user profile
      await this.updateUserProfile(userId, results)

      // Handle notifications
      if (!options.skipNotifications && this.config.features.notifications) {
        results.notifications = await this.processNotifications(userId, results)
      }

      // Handle social updates
      if (!options.skipSocialUpdates && this.config.features.socialProof) {
        await this.processSocialUpdates(userId, results)
      }

      return results
      
    } catch (error) {
      console.error('Error tracking activity:', error)
      return {
        phase1Results: [],
        phase2Results: [],
        phase3Results: null,
        totalPointsEarned: 0,
        newAchievements: [],
        evolutions: [],
        notifications: []
      }
    }
  }

  /**
   * Get user's complete gamification profile
   */
  static async getUserProfile(userId: string): Promise<UserGamificationProfile> {
    try {
      // Check cache first
      if (this.userProfiles.has(userId)) {
        return this.userProfiles.get(userId)!
      }

      // Build profile from scratch
      const profile = await this.buildUserProfile(userId)
      this.userProfiles.set(userId, profile)
      
      return profile
    } catch (error) {
      console.error('Error getting user profile:', error)
      throw error
    }
  }

  /**
   * Get all achievements across phases
   */
  static getAllAchievements(): AnyAchievement[] {
    const achievements: AnyAchievement[] = []
    
    if (this.config?.phases.phase1) {
      achievements.push(...phase1Achievements)
    }
    
    if (this.config?.phases.phase2) {
      achievements.push(...phase2Achievements)
    }
    
    if (this.config?.phases.phase3) {
      achievements.push(...phase3Achievements)
    }
    
    return achievements
  }

  /**
   * Get system statistics
   */
  static async getSystemStats(): Promise<SystemStats> {
    try {
      const stats: SystemStats = {
        achievements: {
          phase1: getPhase1Stats(),
          phase2: getPhase2Stats(),
          phase3: getPhase3Stats(),
          total: 0
        },
        users: {
          total: this.userProfiles.size,
          active: 0,
          byTier: {}
        },
        activity: {
          totalUnlocks: 0,
          dailyActivity: 0,
          socialShares: 0,
          evolutions: 0
        },
        system: {
          version: '3.0.0',
          uptime: process.uptime(),
          health: 'healthy',
          features: []
        }
      }

      // Calculate totals
      stats.achievements.total = 
        stats.achievements.phase1.totalAchievements +
        stats.achievements.phase2.totalAchievements +
        stats.achievements.phase3.totalAchievements

      // Calculate user statistics
      for (const profile of this.userProfiles.values()) {
        if (profile.lastActivity > new Date(Date.now() - 24 * 60 * 60 * 1000)) {
          stats.users.active++
        }
        
        stats.users.byTier[profile.tier] = (stats.users.byTier[profile.tier] || 0) + 1
        stats.activity.totalUnlocks += profile.achievements.total
        stats.activity.socialShares += profile.social.shares
        stats.activity.evolutions += profile.evolution.totalEvolutions
      }

      // System features
      if (this.config.features.notifications) stats.system.features.push('notifications')
      if (this.config.features.socialProof) stats.system.features.push('social_proof')
      if (this.config.features.leaderboards) stats.system.features.push('leaderboards')
      if (this.config.features.dynamicGeneration) stats.system.features.push('dynamic_generation')
      if (this.config.features.evolutionSystem) stats.system.features.push('evolution_system')
      if (this.config.features.seasonalEvents) stats.system.features.push('seasonal_events')

      return stats
    } catch (error) {
      console.error('Error getting system stats:', error)
      throw error
    }
  }

  // ===== HELPER METHODS =====

  private static determineProcessingPhases(trigger: string, requestedPhase?: 1 | 2 | 3 | 'auto'): number[] {
    if (requestedPhase && requestedPhase !== 'auto') {
      return [requestedPhase]
    }

    // Auto-determine based on trigger type
    const phases: number[] = []
    
    // Check if trigger exists in each phase
    if (phase1Triggers.includes(trigger as any)) phases.push(1)
    if (phase2Triggers.includes(trigger as any)) phases.push(2)
    if (phase3Triggers.includes(trigger as any)) phases.push(3)
    
    // If no specific phase match, process all enabled phases
    if (phases.length === 0) {
      if (this.config.phases.phase1) phases.push(1)
      if (this.config.phases.phase2) phases.push(2)
      if (this.config.phases.phase3) phases.push(3)
    }
    
    return phases
  }

  private static async buildUserProfile(userId: string): Promise<UserGamificationProfile> {
    // This would fetch actual user data from database
    // For now, return a default profile
    return {
      userId,
      totalPoints: 0,
      tier: 'bronze',
      achievements: {
        phase1: 0,
        phase2: 0,
        phase3: 0,
        total: 0,
        byRarity: {
          common: 0,
          uncommon: 0,
          rare: 0,
          epic: 0,
          legendary: 0
        },
        byCategory: {},
        recent: []
      },
      streaks: {
        current: 0,
        longest: 0,
        type: 'daily_login'
      },
      social: {
        shares: 0,
        reactions: 0,
        rank: 0
      },
      evolution: {
        totalEvolutions: 0,
        availableEvolutions: [],
        nextMilestone: ''
      },
      seasonal: {
        currentSeasonPoints: 0,
        seasonalMultiplier: 1.0,
        holidayParticipation: []
      },
      lastActivity: new Date(),
      joinDate: new Date()
    }
  }

  private static async updateUserProfile(userId: string, results: any): Promise<void> {
    const profile = await this.getUserProfile(userId)
    
    // Update points
    profile.totalPoints += results.totalPointsEarned
    
    // Update achievement counts
    profile.achievements.phase1 += results.phase1Results.filter((r: any) => r.wasUnlocked).length
    profile.achievements.phase2 += results.phase2Results.filter((r: any) => r.wasUnlocked).length
    if (results.phase3Results) {
      profile.achievements.phase3 += results.phase3Results.achievements.filter((a: any) => a.wasUnlocked).length
    }
    
    profile.achievements.total = profile.achievements.phase1 + profile.achievements.phase2 + profile.achievements.phase3
    
    // Update tier
    profile.tier = this.calculateUserTier(profile.totalPoints, profile.achievements.total)
    
    // Update last activity
    profile.lastActivity = new Date()
    
    // Cache updated profile
    this.userProfiles.set(userId, profile)
  }

  private static calculateUserTier(points: number, achievements: number): UserGamificationProfile['tier'] {
    if (points >= 50000 || achievements >= 100) return 'diamond'
    if (points >= 25000 || achievements >= 50) return 'platinum'
    if (points >= 10000 || achievements >= 25) return 'gold'
    if (points >= 5000 || achievements >= 10) return 'silver'
    return 'bronze'
  }

  private static async processNotifications(userId: string, results: any): Promise<any[]> {
    const notifications: any[] = []
    
    // Process achievement unlock notifications
    for (const result of results.phase1Results) {
      if (result.wasUnlocked) {
        notifications.push({
          type: 'achievement_unlock',
          achievement: result.achievement,
          points: result.achievement.points
        })
      }
    }
    
    // Process evolution notifications
    for (const evolution of results.evolutions) {
      if (evolution.wasSuccessful) {
        notifications.push({
          type: 'evolution',
          evolution: evolution
        })
      }
    }
    
    return notifications
  }

  private static async processSocialUpdates(userId: string, results: any): Promise<void> {
    // Handle social proof updates
    console.log(`Processing social updates for user ${userId}`)
  }

  private static startSystemMonitoring(): void {
    // Monitor system health
    setInterval(() => {
      this.performHealthCheck()
    }, 5 * 60 * 1000) // Every 5 minutes
  }

  private static performHealthCheck(): void {
    try {
      // Check system health
      const memoryUsage = process.memoryUsage()
      const uptime = process.uptime()
      
      console.log(`System Health Check - Memory: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB, Uptime: ${Math.round(uptime / 60)}min`)
    } catch (error) {
      console.error('Health check failed:', error)
    }
  }

  private static validateSystem(): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []
    
    // Validate phase 1
    phase1Achievements.forEach(achievement => {
      if (!validatePhase1Achievement(achievement)) {
        errors.push(`Invalid Phase 1 achievement: ${achievement.id}`)
      }
    })
    
    // Validate phase 2
    if (this.config?.phases.phase2) {
      phase2Achievements.forEach(achievement => {
        if (!validatePhase2Achievement(achievement)) {
          errors.push(`Invalid Phase 2 achievement: ${achievement.id}`)
        }
      })
    }
    
    // Validate phase 3
    if (this.config?.phases.phase3) {
      phase3Achievements.forEach(achievement => {
        if (!validatePhase3Achievement(achievement)) {
          errors.push(`Invalid Phase 3 achievement: ${achievement.id}`)
        }
      })
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// ===== CONVENIENCE EXPORTS =====

export const Gamification = GamificationSystem

// Phase-specific helpers
export const Phase1 = {
  achievements: phase1Achievements,
  triggers: phase1Triggers,
  stats: getPhase1Stats
}

export const Phase2 = {
  achievements: phase2Achievements,
  triggers: phase2Triggers,
  stats: getPhase2Stats,
  helpers: Phase2Helpers,
  social: SocialHelpers
}

export const Phase3 = {
  achievements: phase3Achievements,
  triggers: phase3Triggers,
  stats: getPhase3Stats,
  helpers: Phase3Helpers,
  dynamic: DynamicHelpers,
  evolution: EvolutionHelpers
}

// Default configuration
export const defaultGamificationConfig: GamificationConfig = {
  phases: {
    phase1: true,
    phase2: true,
    phase3: true
  },
  features: {
    notifications: true,
    socialProof: true,
    leaderboards: true,
    dynamicGeneration: true,
    evolutionSystem: true,
    seasonalEvents: true
  },
  settings: {
    autoEvolution: true,
    dynamicGenerationRate: 10,
    seasonalBonuses: true,
    socialSharing: true
  }
}

export default GamificationSystem