/**
 * Gamification System - Phase 1 Index
 * 
 * Main entry point for the Syndicaps gamification system.
 * Exports all Phase 1 achievement functionality for easy integration.
 * 
 * <AUTHOR> Team - Gamification Phase 1
 * @version 1.0.0
 */

// ===== CORE EXPORTS =====

// Achievement definitions and types
export { 
  phase1Achievements,
  phase1Triggers,
  getAchievementsByCategory,
  getAchievementsByRarity,
  getProgressChainAchievements,
  validateAchievement
} from './phase1-achievements'

export type { 
  Achievement,
  AchievementRarity,
  AchievementCategory,
  AchievementRewards,
  UnlockCondition,
  Phase1Trigger
} from './phase1-achievements'

// Achievement tracking
export { 
  AchievementTracker,
  createActivityTrigger,
  trackRaffleEntry,
  trackSocialRequirement
} from './achievementTracking'

export type {
  ActivityTrigger,
  UserAchievementProgress,
  AchievementUnlockResult
} from './achievementTracking'

// Achievement utilities
export {
  getRarityColor,
  getCategoryInfo,
  formatProgress,
  getProgressColor,
  estimateCompletionTime,
  filterAchievements,
  sortAchievements,
  getNextInChain,
  getPreviousInChain,
  arePrerequisitesMet,
  getChainAchievements,
  calculateUserStats,
  getAlmostCompleted,
  getRecommendedAchievements,
  formatUnlockNotification,
  getMilestoneNotifications
} from './achievementUtils'

// Notification system
export { 
  AchievementNotificationManager,
  achievementEventListeners
} from './achievementNotifications'

export type {
  NotificationConfig,
  AchievementNotification,
  CelebrationEffect
} from './achievementNotifications'

// Integration helpers
export {
  initializeAchievementSystem,
  handleRaffleEntrySubmission,
  handleSocialRequirementCompletion,
  handleRaffleWin,
  handleRafflePaymentCompletion,
  handleRaffleProductView,
  handleReferralRaffleEntry,
  handleBatchActivities,
  testAchievementSystem,
  getUserAchievementSummary
} from './achievementIntegration'

// ===== CONVENIENCE EXPORTS =====

/**
 * Quick setup function for new implementations
 */
export async function setupPhase1Achievements(config?: {
  notifications?: {
    enableSound?: boolean;
    enableAnimations?: boolean;
    celebrationLevel?: 'minimal' | 'normal' | 'epic';
  };
}) {
  const { initializeAchievementSystem } = await import('./achievementIntegration')
  return initializeAchievementSystem(config)
}

/**
 * Get all Phase 1 achievement statistics
 */
export function getPhase1Stats() {
  return {
    totalAchievements: phase1Achievements.length,
    byCategory: {
      raffle_entry: phase1Achievements.filter(a => a.category === 'raffle_entry').length,
      raffle_success: phase1Achievements.filter(a => a.category === 'raffle_success').length,
      raffle_social: phase1Achievements.filter(a => a.category === 'raffle_social').length,
      raffle_timing: phase1Achievements.filter(a => a.category === 'raffle_timing').length,
      social_engagement: phase1Achievements.filter(a => a.category === 'social_engagement').length,
      shopping: phase1Achievements.filter(a => a.category === 'shopping').length
    },
    byRarity: {
      common: phase1Achievements.filter(a => a.rarity === 'common').length,
      uncommon: phase1Achievements.filter(a => a.rarity === 'uncommon').length,
      rare: phase1Achievements.filter(a => a.rarity === 'rare').length,
      epic: phase1Achievements.filter(a => a.rarity === 'epic').length,
      legendary: phase1Achievements.filter(a => a.rarity === 'legendary').length
    },
    totalPointsAvailable: phase1Achievements.reduce((sum, a) => sum + a.rewards.points, 0),
    averagePointsPerAchievement: Math.round(
      phase1Achievements.reduce((sum, a) => sum + a.rewards.points, 0) / phase1Achievements.length
    ),
    triggersSupported: phase1Triggers.length
  }
}

/**
 * Validate Phase 1 system integrity
 */
export function validatePhase1System(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Check for duplicate IDs
  const ids = new Set()
  phase1Achievements.forEach(achievement => {
    if (ids.has(achievement.id)) {
      errors.push(`Duplicate achievement ID: ${achievement.id}`)
    }
    ids.add(achievement.id)
  })
  
  // Check for missing triggers
  phase1Achievements.forEach(achievement => {
    achievement.gamificationTriggers.forEach(trigger => {
      if (!phase1Triggers.includes(trigger as any)) {
        errors.push(`Unknown trigger in ${achievement.id}: ${trigger}`)
      }
    })
  })
  
  // Check for broken prerequisite chains
  phase1Achievements.forEach(achievement => {
    if (achievement.prerequisites) {
      achievement.prerequisites.forEach(prereqId => {
        const prereq = phase1Achievements.find(a => a.id === prereqId)
        if (!prereq) {
          errors.push(`Missing prerequisite ${prereqId} for ${achievement.id}`)
        }
      })
    }
  })
  
  // Check progress chains
  phase1Achievements.forEach(achievement => {
    if (achievement.progressChain) {
      achievement.progressChain.forEach(chainId => {
        const chainAchievement = phase1Achievements.find(a => a.id === chainId)
        if (!chainAchievement) {
          warnings.push(`Missing chain achievement ${chainId} for ${achievement.id}`)
        }
      })
    }
  })
  
  // Check point distribution
  const totalPoints = phase1Achievements.reduce((sum, a) => sum + a.rewards.points, 0)
  if (totalPoints < 10000) {
    warnings.push(`Low total points available: ${totalPoints}`)
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Re-export phase1Achievements for convenience
import { phase1Achievements, phase1Triggers } from './phase1-achievements'
import { AchievementTracker } from './achievementTracking'
import { AchievementNotificationManager } from './achievementNotifications'

// ===== VERSION INFO =====

export const GAMIFICATION_VERSION = '1.0.0'
export const GAMIFICATION_PHASE = 1
export const BUILD_DATE = new Date().toISOString()

export default {
  // Core functions
  setupPhase1Achievements,
  getPhase1Stats,
  validatePhase1System,
  
  // Version info
  version: GAMIFICATION_VERSION,
  phase: GAMIFICATION_PHASE,
  buildDate: BUILD_DATE,
  
  // Quick access to main classes  
  AchievementTracker: AchievementTracker,
  AchievementNotificationManager: AchievementNotificationManager,
  
  // Achievement data
  achievements: phase1Achievements,
  triggers: phase1Triggers
}