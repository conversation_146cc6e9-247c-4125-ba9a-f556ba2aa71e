/**
 * Achievement Leaderboards and Social Features
 * 
 * Competitive and social features for the gamification system including
 * leaderboards, rankings, competitions, and community challenges.
 * 
 * <AUTHOR> Team - Gamification Phase 2
 * @version 1.0.0
 */

import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  runTransaction,
  Timestamp
} from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firebase/gamificationCollections'
import type { Achievement } from './phase1-achievements'
import type { Phase2Achievement } from './phase2-achievements'

// ===== TYPES =====

export interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar?: string;
  score: number;
  rank: number;
  change: number; // Position change from last period
  achievements: {
    total: number;
    byRarity: Record<string, number>;
    recent: string[]; // Recent achievement IDs
  };
  tier: string;
  badge?: string;
  lastActive: Timestamp;
}

export interface LeaderboardConfig {
  id: string;
  name: string;
  description: string;
  type: 'points' | 'achievements' | 'category' | 'streak' | 'custom';
  period: 'daily' | 'weekly' | 'monthly' | 'all-time';
  category?: string;
  maxEntries: number;
  refreshFrequency: number; // minutes
  isActive: boolean;
  rewards?: {
    top1: { points: number; badge?: string };
    top3: { points: number; badge?: string };
    top10: { points: number; badge?: string };
  };
  startDate: Timestamp;
  endDate?: Timestamp;
}

export interface Competition {
  id: string;
  title: string;
  description: string;
  type: 'sprint' | 'marathon' | 'category_challenge' | 'team_battle';
  startDate: Timestamp;
  endDate: Timestamp;
  participants: string[];
  rules: {
    eligibleAchievements?: string[];
    categories?: string[];
    minTier?: string;
    maxParticipants?: number;
  };
  prizes: {
    rank: number;
    reward: {
      points: number;
      badge?: string;
      title?: string;
      exclusive?: any;
    };
  }[];
  status: 'upcoming' | 'active' | 'completed';
  leaderboard: LeaderboardEntry[];
  metadata: Record<string, any>;
}

export interface CommunityChallenge {
  id: string;
  title: string;
  description: string;
  objective: string;
  targetAchievement: string;
  participantGoal: number;
  currentParticipants: string[];
  progressTarget: number;
  currentProgress: number;
  startDate: Timestamp;
  endDate: Timestamp;
  rewards: {
    individual: { points: number; badge?: string };
    community: { unlocks?: string[]; features?: string[] };
  };
  status: 'active' | 'completed' | 'failed';
}

export interface SocialFeature {
  userId: string;
  type: 'achievement_showcase' | 'progress_share' | 'challenge_invitation' | 'peer_recognition';
  targetUserId?: string;
  content: {
    title: string;
    message: string;
    data: Record<string, any>;
  };
  visibility: 'public' | 'friends' | 'private';
  interactions: {
    likes: string[];
    comments: { userId: string; message: string; timestamp: Timestamp }[];
    shares: number;
  };
  createdAt: Timestamp;
}

// ===== LEADERBOARD MANAGER =====

export class LeaderboardManager {
  private static leaderboards: Map<string, LeaderboardEntry[]> = new Map()
  private static competitions: Map<string, Competition> = new Map()
  private static challenges: Map<string, CommunityChallenge> = new Map()

  /**
   * Initialize leaderboard system
   */
  static async initialize(): Promise<void> {
    try {
      console.log('Initializing Leaderboard Manager...')
      
      await this.loadLeaderboardConfigs()
      await this.startRefreshScheduler()
      
      console.log('Leaderboard Manager initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Leaderboard Manager:', error)
      throw error
    }
  }

  /**
   * Get leaderboard entries
   */
  static async getLeaderboard(
    type: 'points' | 'achievements' | 'category' | 'streak',
    period: 'daily' | 'weekly' | 'monthly' | 'all-time' = 'all-time',
    category?: string,
    limitCount: number = 100
  ): Promise<LeaderboardEntry[]> {
    try {
      const leaderboardId = this.generateLeaderboardId(type, period, category)
      
      // Check cache first
      if (this.leaderboards.has(leaderboardId)) {
        return this.leaderboards.get(leaderboardId)!.slice(0, limitCount)
      }

      // Generate fresh leaderboard
      const entries = await this.generateLeaderboard(type, period, category, limitCount)
      this.leaderboards.set(leaderboardId, entries)
      
      return entries
    } catch (error) {
      console.error('Error getting leaderboard:', error)
      return []
    }
  }

  /**
   * Generate leaderboard from user data
   */
  private static async generateLeaderboard(
    type: string,
    period: string,
    category?: string,
    limitCount: number = 100
  ): Promise<LeaderboardEntry[]> {
    try {
      let query_config: any

      switch (type) {
        case 'points':
          query_config = await this.generatePointsLeaderboard(period, limitCount)
          break
        case 'achievements':
          query_config = await this.generateAchievementsLeaderboard(period, limitCount)
          break
        case 'category':
          query_config = await this.generateCategoryLeaderboard(category!, period, limitCount)
          break
        case 'streak':
          query_config = await this.generateStreakLeaderboard(period, limitCount)
          break
        default:
          return []
      }

      return query_config
    } catch (error) {
      console.error('Error generating leaderboard:', error)
      return []
    }
  }

  /**
   * Generate points-based leaderboard
   */
  private static async generatePointsLeaderboard(period: string, limitCount: number): Promise<LeaderboardEntry[]> {
    try {
      // Get users with highest point totals
      const usersSnapshot = await getDocs(
        query(
          collection(db, collections.users),
          orderBy('gamification.totalPointsEarned', 'desc'),
          limit(limitCount)
        )
      )

      const entries: LeaderboardEntry[] = []
      let rank = 1

      for (const userDoc of usersSnapshot.docs) {
        const userData = userDoc.data()
        const userId = userDoc.id

        // Get user's achievements
        const achievementsSnapshot = await getDocs(
          query(
            collection(db, collections.userAchievements),
            where('userId', '==', userId),
            where('isCompleted', '==', true)
          )
        )

        const achievementsByRarity: Record<string, number> = {}
        achievementsSnapshot.docs.forEach(doc => {
          const achievement = doc.data()
          const rarity = achievement.metadata?.rarity || 'common'
          achievementsByRarity[rarity] = (achievementsByRarity[rarity] || 0) + 1
        })

        const entry: LeaderboardEntry = {
          userId,
          username: userData.username || userData.displayName || 'Anonymous',
          avatar: userData.avatar,
          score: userData.gamification?.totalPointsEarned || 0,
          rank,
          change: 0, // Would be calculated from previous period
          achievements: {
            total: achievementsSnapshot.size,
            byRarity: achievementsByRarity,
            recent: achievementsSnapshot.docs.slice(0, 3).map(doc => doc.data().achievementId)
          },
          tier: userData.gamification?.tier || 'bronze',
          lastActive: userData.lastActiveAt || serverTimestamp() as Timestamp
        }

        entries.push(entry)
        rank++
      }

      return entries
    } catch (error) {
      console.error('Error generating points leaderboard:', error)
      return []
    }
  }

  /**
   * Generate achievements-based leaderboard
   */
  private static async generateAchievementsLeaderboard(period: string, limitCount: number): Promise<LeaderboardEntry[]> {
    try {
      // Aggregate achievement counts per user
      const achievementsSnapshot = await getDocs(
        query(
          collection(db, collections.userAchievements),
          where('isCompleted', '==', true)
        )
      )

      const userAchievementCounts: Map<string, number> = new Map()
      achievementsSnapshot.docs.forEach(doc => {
        const data = doc.data()
        const count = userAchievementCounts.get(data.userId) || 0
        userAchievementCounts.set(data.userId, count + 1)
      })

      // Sort by achievement count
      const sortedUsers = Array.from(userAchievementCounts.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, limitCount)

      const entries: LeaderboardEntry[] = []
      let rank = 1

      for (const [userId, achievementCount] of sortedUsers) {
        // Get user data
        const userDoc = await getDocs(
          query(
            collection(db, collections.users),
            where('__name__', '==', userId),
            limit(1)
          )
        )

        if (userDoc.empty) continue

        const userData = userDoc.docs[0].data()

        const entry: LeaderboardEntry = {
          userId,
          username: userData.username || userData.displayName || 'Anonymous',
          avatar: userData.avatar,
          score: achievementCount,
          rank,
          change: 0,
          achievements: {
            total: achievementCount,
            byRarity: {}, // Would be calculated
            recent: []
          },
          tier: userData.gamification?.tier || 'bronze',
          lastActive: userData.lastActiveAt || serverTimestamp() as Timestamp
        }

        entries.push(entry)
        rank++
      }

      return entries
    } catch (error) {
      console.error('Error generating achievements leaderboard:', error)
      return []
    }
  }

  /**
   * Generate category-specific leaderboard
   */
  private static async generateCategoryLeaderboard(category: string, period: string, limitCount: number): Promise<LeaderboardEntry[]> {
    try {
      // Get achievements in the specified category
      const categoryAchievements = await getDocs(
        query(
          collection(db, collections.userAchievements),
          where('isCompleted', '==', true)
          // Would filter by category if available in the document
        )
      )

      // Similar logic to achievements leaderboard but filtered by category
      // Implementation would depend on how category information is stored
      
      return []
    } catch (error) {
      console.error('Error generating category leaderboard:', error)
      return []
    }
  }

  /**
   * Generate streak-based leaderboard
   */
  private static async generateStreakLeaderboard(period: string, limitCount: number): Promise<LeaderboardEntry[]> {
    try {
      // Get users with highest login streaks
      const usersSnapshot = await getDocs(
        query(
          collection(db, collections.users),
          orderBy('stats.currentStreak', 'desc'),
          limit(limitCount)
        )
      )

      const entries: LeaderboardEntry[] = []
      let rank = 1

      for (const userDoc of usersSnapshot.docs) {
        const userData = userDoc.data()
        const userId = userDoc.id

        const entry: LeaderboardEntry = {
          userId,
          username: userData.username || userData.displayName || 'Anonymous',
          avatar: userData.avatar,
          score: userData.stats?.currentStreak || 0,
          rank,
          change: 0,
          achievements: {
            total: 0, // Would be fetched
            byRarity: {},
            recent: []
          },
          tier: userData.gamification?.tier || 'bronze',
          lastActive: userData.lastActiveAt || serverTimestamp() as Timestamp
        }

        entries.push(entry)
        rank++
      }

      return entries
    } catch (error) {
      console.error('Error generating streak leaderboard:', error)
      return []
    }
  }

  /**
   * Create a new competition
   */
  static async createCompetition(config: Omit<Competition, 'id' | 'participants' | 'leaderboard' | 'status'>): Promise<string> {
    try {
      const competition: Competition = {
        ...config,
        id: `comp_${Date.now()}`,
        participants: [],
        leaderboard: [],
        status: 'upcoming'
      }

      // Store in database
      await addDoc(collection(db, 'competitions'), competition)
      
      // Cache locally
      this.competitions.set(competition.id, competition)
      
      console.log(`Competition created: ${competition.title}`)
      return competition.id
    } catch (error) {
      console.error('Error creating competition:', error)
      throw error
    }
  }

  /**
   * Join a competition
   */
  static async joinCompetition(competitionId: string, userId: string): Promise<boolean> {
    try {
      const competition = this.competitions.get(competitionId)
      if (!competition) return false

      if (competition.participants.includes(userId)) return true

      // Check eligibility
      if (!await this.checkCompetitionEligibility(competition, userId)) return false

      // Add participant
      competition.participants.push(userId)
      
      // Update in database
      await updateDoc(doc(collection(db, 'competitions'), competitionId), {
        participants: competition.participants
      })

      console.log(`User ${userId} joined competition ${competitionId}`)
      return true
    } catch (error) {
      console.error('Error joining competition:', error)
      return false
    }
  }

  /**
   * Create community challenge
   */
  static async createCommunityChallenge(config: Omit<CommunityChallenge, 'id' | 'currentParticipants' | 'currentProgress' | 'status'>): Promise<string> {
    try {
      const challenge: CommunityChallenge = {
        ...config,
        id: `challenge_${Date.now()}`,
        currentParticipants: [],
        currentProgress: 0,
        status: 'active'
      }

      // Store in database
      await addDoc(collection(db, 'communityCallenges'), challenge)
      
      // Cache locally
      this.challenges.set(challenge.id, challenge)
      
      console.log(`Community challenge created: ${challenge.title}`)
      return challenge.id
    } catch (error) {
      console.error('Error creating community challenge:', error)
      throw error
    }
  }

  /**
   * Update user rank when achievement is unlocked
   */
  static async updateUserRank(userId: string, achievementId: string): Promise<void> {
    try {
      // Update relevant leaderboards
      await this.refreshLeaderboard('points', 'all-time')
      await this.refreshLeaderboard('achievements', 'all-time')
      
      // Check for rank-based achievements or notifications
      const userRank = await this.getUserRank(userId, 'points')
      
      if (userRank <= 10) {
        // User is in top 10, might trigger special notifications
        console.log(`User ${userId} is now rank ${userRank}!`)
      }
    } catch (error) {
      console.error('Error updating user rank:', error)
    }
  }

  /**
   * Get user's current rank
   */
  static async getUserRank(userId: string, type: string, category?: string): Promise<number> {
    try {
      const leaderboard = await this.getLeaderboard(type as any, 'all-time', category)
      const userEntry = leaderboard.find(entry => entry.userId === userId)
      return userEntry?.rank || 0
    } catch (error) {
      console.error('Error getting user rank:', error)
      return 0
    }
  }

  // ===== HELPER METHODS =====

  private static generateLeaderboardId(type: string, period: string, category?: string): string {
    return `${type}_${period}${category ? `_${category}` : ''}`
  }

  private static async loadLeaderboardConfigs(): Promise<void> {
    // Load leaderboard configurations from database
    console.log('Loading leaderboard configurations...')
  }

  private static async startRefreshScheduler(): Promise<void> {
    // Start periodic leaderboard refresh
    setInterval(async () => {
      console.log('Refreshing leaderboards...')
      await this.refreshAllLeaderboards()
    }, 15 * 60 * 1000) // Every 15 minutes
  }

  private static async refreshAllLeaderboards(): Promise<void> {
    try {
      // Clear cache and regenerate main leaderboards
      this.leaderboards.clear()
      
      await Promise.all([
        this.getLeaderboard('points', 'all-time'),
        this.getLeaderboard('achievements', 'all-time'),
        this.getLeaderboard('points', 'weekly'),
        this.getLeaderboard('achievements', 'weekly')
      ])
    } catch (error) {
      console.error('Error refreshing leaderboards:', error)
    }
  }

  private static async refreshLeaderboard(type: string, period: string, category?: string): Promise<void> {
    const leaderboardId = this.generateLeaderboardId(type, period, category)
    this.leaderboards.delete(leaderboardId)
    await this.getLeaderboard(type as any, period as any, category)
  }

  private static async checkCompetitionEligibility(competition: Competition, userId: string): Promise<boolean> {
    // Check if user meets competition requirements
    return true // Simplified for now
  }

  /**
   * Get leaderboard statistics
   */
  static getLeaderboardStats() {
    return {
      totalLeaderboards: this.leaderboards.size,
      totalCompetitions: this.competitions.size,
      totalChallenges: this.challenges.size,
      cacheSize: Array.from(this.leaderboards.values()).reduce((sum, board) => sum + board.length, 0)
    }
  }
}

export default LeaderboardManager