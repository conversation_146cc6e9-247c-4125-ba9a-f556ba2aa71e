/**
 * Phase 1 Achievement Definitions
 * 
 * Core raffle and social achievements for the Syndicaps gamification system.
 * Implements 40 foundational achievements focusing on raffle participation,
 * social engagement, and basic user activities.
 * 
 * <AUTHOR> Team - Gamification Phase 1
 * @version 1.0.0
 */

export type AchievementRarity = 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
export type AchievementCategory = 
  | 'raffle_entry' 
  | 'raffle_success' 
  | 'raffle_social' 
  | 'raffle_timing' 
  | 'raffle_engagement'
  | 'social_engagement'
  | 'profile'
  | 'shopping';

export interface UnlockCondition {
  type: string;
  target: number;
  timeframe?: 'daily' | 'weekly' | 'monthly' | 'all-time';
  platform?: 'instagram' | 'discord' | 'reddit' | 'all';
}

export interface AchievementRewards {
  points: number;
  badge?: string;
  tierBonus?: number;
}

export interface Achievement {
  points: number;
  id: string;
  title: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  rarity: AchievementRarity;
  requirements: UnlockCondition[];
  rewards: AchievementRewards;
  gamificationTriggers: string[];
  isActive: boolean;
  progressChain?: string[]; // For progressive achievements
  prerequisites?: string[]; // Achievement dependencies
  metadata?: {
    version: string;
    phase: number;
    tags: string[];
  };
}

// ===== PHASE 1 ACHIEVEMENT DEFINITIONS =====

export const phase1Achievements: Achievement[] = [
  // 🎫 RAFFLE ENTRY & PARTICIPATION (12 achievements)
  {
    id: 'raffle_rookie',
    title: 'Raffle Rookie',
    description: 'Submit your first raffle entry',
    icon: '🎫',
    category: 'raffle_entry',
    rarity: 'common',
    requirements: [{ type: 'raffle_entries_count', target: 1 }],
    rewards: { points: 100 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    progressChain: ['raffle_rookie', 'entry_enthusiast', 'raffle_veteran'],
    metadata: { version: '1.0.0', phase: 1, tags: ['starter', 'raffle'] },
    points: 0
  },
  
  {
    id: 'entry_streak_3',
    title: 'Entry Streak',
    description: 'Enter 3 consecutive raffles',
    icon: '🔥',
    category: 'raffle_entry',
    rarity: 'uncommon',
    requirements: [{ type: 'consecutive_raffle_entries', target: 3 }],
    rewards: { points: 200 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    progressChain: ['entry_streak_3', 'streak_master'],
    metadata: { version: '1.0.0', phase: 1, tags: ['streak', 'consistency'] },
    points: 0
  },

  {
    id: 'streak_master',
    title: 'Streak Master',
    description: 'Enter 5 consecutive raffles',
    icon: '🌟',
    category: 'raffle_entry',
    rarity: 'rare',
    requirements: [{ type: 'consecutive_raffle_entries', target: 5 }],
    rewards: { points: 500 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    prerequisites: ['entry_streak_3'],
    metadata: { version: '1.0.0', phase: 1, tags: ['streak', 'dedication'] },
    points: 0
  },

  {
    id: 'entry_completionist',
    title: 'Entry Completionist',
    description: 'Complete all social requirements in a single raffle',
    icon: '✅',
    category: 'raffle_social',
    rarity: 'uncommon',
    requirements: [{ type: 'complete_all_social_requirements', target: 1 }],
    rewards: { points: 250 },
    gamificationTriggers: ['raffle_social_requirements_completed'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['social', 'completion'] },
    points: 0
  },

  {
    id: 'speed_demon',
    title: 'Speed Demon',
    description: 'Complete raffle entry in under 3 minutes',
    icon: '🏃',
    category: 'raffle_timing',
    rarity: 'rare',
    requirements: [{ type: 'entry_completion_seconds', target: 180 }],
    rewards: { points: 300 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['speed', 'efficiency'] },
    points: 0
  },

  {
    id: 'early_bird',
    title: 'Early Bird',
    description: 'Enter within first hour of raffle going live',
    icon: '🐦',
    category: 'raffle_timing',
    rarity: 'uncommon',
    requirements: [{ type: 'early_entry_minutes', target: 60 }],
    rewards: { points: 150 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['timing', 'early'] },
    points: 0
  },

  {
    id: 'last_minute_larry',
    title: 'Last Minute Entry',
    description: 'Enter in final hour before raffle closes',
    icon: '⏰',
    category: 'raffle_timing',
    rarity: 'uncommon',
    requirements: [{ type: 'late_entry_minutes', target: 60 }],
    rewards: { points: 150 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['timing', 'deadline'] },
    points: 0
  },

  {
    id: 'raffle_explorer',
    title: 'Raffle Explorer',
    description: 'View 10 different raffle product pages',
    icon: '🔍',
    category: 'raffle_engagement',
    rarity: 'common',
    requirements: [{ type: 'raffle_products_viewed', target: 10 }],
    rewards: { points: 75 },
    gamificationTriggers: ['raffle_product_viewed'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['exploration', 'discovery'] },
    points: 0
  },

  {
    id: 'entry_enthusiast',
    title: 'Entry Enthusiast',
    description: 'Enter 10 different raffles',
    icon: '🎯',
    category: 'raffle_entry',
    rarity: 'rare',
    requirements: [{ type: 'total_raffle_entries', target: 10 }],
    rewards: { points: 400 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    prerequisites: ['raffle_rookie'],
    metadata: { version: '1.0.0', phase: 1, tags: ['milestone', 'participation'] },
    points: 0
  },

  {
    id: 'raffle_veteran',
    title: 'Raffle Veteran',
    description: 'Enter 25 different raffles',
    icon: '🎖️',
    category: 'raffle_entry',
    rarity: 'epic',
    requirements: [{ type: 'total_raffle_entries', target: 25 }],
    rewards: { points: 1000, badge: 'veteran' },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    prerequisites: ['entry_enthusiast'],
    metadata: { version: '1.0.0', phase: 1, tags: ['milestone', 'veteran'] },
    points: 0
  },

  {
    id: 'perfect_entry',
    title: 'Perfect Entry',
    description: 'Complete entry without any form errors or retries',
    icon: '💎',
    category: 'raffle_entry',
    rarity: 'rare',
    requirements: [{ type: 'error_free_entry', target: 1 }],
    rewards: { points: 350 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['precision', 'quality'] },
    points: 0
  },

  {
    id: 'lightning_fast',
    title: 'Lightning Fast',
    description: 'Complete entry in under 2 minutes',
    icon: '⚡',
    category: 'raffle_timing',
    rarity: 'rare',
    requirements: [{ type: 'entry_completion_seconds', target: 120 }],
    rewards: { points: 400 },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    prerequisites: ['speed_demon'],
    metadata: { version: '1.0.0', phase: 1, tags: ['speed', 'excellence'] },
    points: 0
  },

  // 🏆 RAFFLE SUCCESS & WINNING (8 achievements)
  {
    id: 'lucky_winner',
    title: 'Lucky Winner',
    description: 'Win your first raffle',
    icon: '🍀',
    category: 'raffle_success',
    rarity: 'epic',
    requirements: [{ type: 'raffle_wins_count', target: 1 }],
    rewards: { points: 1000, badge: 'winner' },
    gamificationTriggers: ['raffle_won'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['victory', 'milestone'] },
    points: 0
  },

  {
    id: 'quick_pay',
    title: 'Quick Pay',
    description: 'Complete payment within 24 hours of winning',
    icon: '⚡',
    category: 'raffle_success',
    rarity: 'rare',
    requirements: [{ type: 'payment_speed_hours', target: 24 }],
    rewards: { points: 400 },
    gamificationTriggers: ['raffle_payment_completed'],
    isActive: true,
    prerequisites: ['lucky_winner'],
    metadata: { version: '1.0.0', phase: 1, tags: ['efficiency', 'responsibility'] },
    points: 0
  },

  {
    id: 'double_lucky',
    title: 'Double Lucky',
    description: 'Win multiple prizes in single raffle',
    icon: '🎰',
    category: 'raffle_success',
    rarity: 'legendary',
    requirements: [{ type: 'multi_win_single_raffle', target: 2 }],
    rewards: { points: 2000, badge: 'multi_winner' },
    gamificationTriggers: ['raffle_won'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['exceptional', 'luck'] },
    points: 0
  },

  {
    id: 'serial_winner',
    title: 'Serial Winner',
    description: 'Win 3 different raffles',
    icon: '🏆',
    category: 'raffle_success',
    rarity: 'legendary',
    requirements: [{ type: 'raffle_wins_count', target: 3 }],
    rewards: { points: 3000 },
    gamificationTriggers: ['raffle_won'],
    isActive: true,
    prerequisites: ['lucky_winner'],
    metadata: { version: '1.0.0', phase: 1, tags: ['mastery', 'legend'] },
    points: 0
  },

  {
    id: 'payment_pro',
    title: 'Payment Pro',
    description: 'Complete 5 raffle payments successfully',
    icon: '💳',
    category: 'raffle_success',
    rarity: 'uncommon',
    requirements: [{ type: 'successful_payments', target: 5 }],
    rewards: { points: 200 },
    gamificationTriggers: ['raffle_payment_completed'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['reliability', 'commitment'] },
    points: 0
  },

  {
    id: 'express_winner',
    title: 'Express Winner',
    description: 'Choose express shipping as a winner',
    icon: '📦',
    category: 'raffle_success',
    rarity: 'rare',
    requirements: [{ type: 'express_shipping_selected', target: 1 }],
    rewards: { points: 300 },
    gamificationTriggers: ['raffle_shipping_selected'],
    isActive: true,
    prerequisites: ['lucky_winner'],
    metadata: { version: '1.0.0', phase: 1, tags: ['premium', 'urgency'] },
    points: 0
  },

  {
    id: 'winners_circle',
    title: "Winner's Circle",
    description: 'Win raffles in 3 different product categories',
    icon: '⭐',
    category: 'raffle_success',
    rarity: 'epic',
    requirements: [{ type: 'category_wins', target: 3 }],
    rewards: { points: 1500 },
    gamificationTriggers: ['raffle_won'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['diversity', 'achievement'] },
    points: 0
  },

  {
    id: 'jackpot_hunter',
    title: 'Jackpot Hunter',
    description: 'Win a raffle worth over $200',
    icon: '💰',
    category: 'raffle_success',
    rarity: 'epic',
    requirements: [{ type: 'high_value_win', target: 200 }],
    rewards: { points: 1200 },
    gamificationTriggers: ['raffle_won'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['high-value', 'premium'] },
    points: 0
  },

  // 📱 SOCIAL MEDIA & COMMUNITY (10 achievements)
  {
    id: 'social_butterfly',
    title: 'Social Butterfly',
    description: 'Complete first Instagram requirement',
    icon: '🦋',
    category: 'raffle_social',
    rarity: 'common',
    requirements: [{ type: 'instagram_follow', target: 1, platform: 'instagram' }],
    rewards: { points: 50 },
    gamificationTriggers: ['instagram_follow_verified'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['social', 'starter'] },
    points: 0
  },

  {
    id: 'content_creator',
    title: 'Content Creator',
    description: 'Share 5 high-quality Instagram posts for raffles',
    icon: '📸',
    category: 'raffle_social',
    rarity: 'uncommon',
    requirements: [{ type: 'instagram_posts_shared', target: 5, platform: 'instagram' }],
    rewards: { points: 250 },
    gamificationTriggers: ['instagram_post_verified'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['content', 'creativity'] },
    points: 0
  },

  {
    id: 'discord_champion',
    title: 'Discord Champion',
    description: 'Join Discord for 5 different raffles',
    icon: '💬',
    category: 'raffle_social',
    rarity: 'uncommon',
    requirements: [{ type: 'discord_joins_count', target: 5, platform: 'discord' }],
    rewards: { points: 200 },
    gamificationTriggers: ['discord_join_verified'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['community', 'engagement'] },
    points: 0
  },

  {
    id: 'reddit_ranger',
    title: 'Reddit Ranger',
    description: 'Follow Reddit community for 5 raffles',
    icon: '🤖',
    category: 'raffle_social',
    rarity: 'uncommon',
    requirements: [{ type: 'reddit_follows_count', target: 5, platform: 'reddit' }],
    rewards: { points: 200 },
    gamificationTriggers: ['reddit_follow_verified'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['community', 'platform'] },
    points: 0
  },

  {
    id: 'social_pro',
    title: 'Social Pro',
    description: 'Complete all 3 social platforms in 5 raffles',
    icon: '🌟',
    category: 'raffle_social',
    rarity: 'rare',
    requirements: [{ type: 'multi_platform_completions', target: 5, platform: 'all' }],
    rewards: { points: 500 },
    gamificationTriggers: ['raffle_social_requirement_completed'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['mastery', 'multi-platform'] },
    points: 0
  },

  {
    id: 'tag_master',
    title: 'Tag Master',
    description: 'Successfully tag @syndicaps in 3 posts',
    icon: '🏷️',
    category: 'social_engagement',
    rarity: 'common',
    requirements: [{ type: 'verified_tags', target: 3 }],
    rewards: { points: 100 },
    gamificationTriggers: ['tag_verified'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['branding', 'awareness'] },
    points: 0
  },

  {
    id: 'community_builder',
    title: 'Community Builder',
    description: 'Refer 3 friends who complete raffle entries',
    icon: '🤝',
    category: 'social_engagement',
    rarity: 'rare',
    requirements: [{ type: 'successful_raffle_referrals', target: 3 }],
    rewards: { points: 600 },
    gamificationTriggers: ['referral_raffle_entry'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['growth', 'referral'] },
    points: 0
  },

  {
    id: 'social_consistency',
    title: 'Social Consistency',
    description: 'Complete social requirements in 10 consecutive raffles',
    icon: '📅',
    category: 'raffle_social',
    rarity: 'epic',
    requirements: [{ type: 'consecutive_social_completions', target: 10 }],
    rewards: { points: 1000 },
    gamificationTriggers: ['raffle_social_requirement_completed'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['consistency', 'dedication'] },
    points: 0
  },

  {
    id: 'platform_master',
    title: 'Platform Master',
    description: 'Complete 25 social requirements across all platforms',
    icon: '🚀',
    category: 'raffle_social',
    rarity: 'epic',
    requirements: [{ type: 'total_social_completions', target: 25 }],
    rewards: { points: 1200 },
    gamificationTriggers: ['raffle_social_requirement_completed'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['mastery', 'milestone'] },
    points: 0
  },

  {
    id: 'viral_creator',
    title: 'Viral Creator',
    description: 'Instagram post receives 100+ likes on raffle share',
    icon: '🔥',
    category: 'social_engagement',
    rarity: 'legendary',
    requirements: [{ type: 'viral_engagement', target: 100 }],
    rewards: { points: 2000 },
    gamificationTriggers: ['viral_post_detected'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['viral', 'influence'] },
    points: 0
  },

  // 🛍️ SHOPPING & VARIANTS (10 achievements)
  {
    id: 'variant_explorer',
    title: 'Variant Explorer',
    description: 'Select 10 different product variants',
    icon: '🎨',
    category: 'shopping',
    rarity: 'common',
    requirements: [{ type: 'unique_variants_selected', target: 10 }],
    rewards: { points: 100 },
    gamificationTriggers: ['raffle_variant_selected'],
    isActive: true,
    metadata: { version: '1.0.0', phase: 1, tags: ['variety', 'exploration'] },
    points: 0
  }
];

// ===== ACHIEVEMENT TRACKING TRIGGERS =====

export const phase1Triggers = [
  'raffle_entry_submitted',
  'raffle_won',
  'raffle_payment_completed',
  'raffle_social_requirements_completed',
  'raffle_social_requirement_completed',
  'instagram_follow_verified',
  'instagram_post_verified',
  'discord_join_verified',
  'reddit_follow_verified',
  'tag_verified',
  'referral_raffle_entry',
  'viral_post_detected',
  'raffle_product_viewed',
  'raffle_variant_selected',
  'raffle_shipping_selected'
] as const;

export type Phase1Trigger = typeof phase1Triggers[number];

// ===== HELPER FUNCTIONS =====

export function getAchievementsByCategory(category: AchievementCategory): Achievement[] {
  return phase1Achievements.filter(achievement => achievement.category === category);
}

export function getAchievementsByRarity(rarity: AchievementRarity): Achievement[] {
  return phase1Achievements.filter(achievement => achievement.rarity === rarity);
}

export function getProgressChainAchievements(chainId: string): Achievement[] {
  return phase1Achievements.filter(achievement => 
    achievement.progressChain?.includes(chainId)
  ).sort((a, b) => {
    const aIndex = a.progressChain?.indexOf(chainId) ?? -1;
    const bIndex = b.progressChain?.indexOf(chainId) ?? -1;
    return aIndex - bIndex;
  });
}

export function validateAchievement(achievement: Achievement): boolean {
  const requiredFields = ['id', 'title', 'description', 'icon', 'category', 'rarity', 'requirements', 'rewards'];
  return requiredFields.every(field => achievement[field as keyof Achievement] !== undefined);
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Get Phase 1 achievement statistics
 */
export function getPhase1Stats() {
  return {
    totalAchievements: phase1Achievements.length,
    byCategory: {
      raffle_entry: phase1Achievements.filter(a => a.category === 'raffle_entry').length,
      raffle_success: phase1Achievements.filter(a => a.category === 'raffle_success').length,
      raffle_social: phase1Achievements.filter(a => a.category === 'raffle_social').length,
      raffle_timing: phase1Achievements.filter(a => a.category === 'raffle_timing').length,
      social_engagement: phase1Achievements.filter(a => a.category === 'social_engagement').length,
      shopping: phase1Achievements.filter(a => a.category === 'shopping').length
    },
    byRarity: {
      common: phase1Achievements.filter(a => a.rarity === 'common').length,
      uncommon: phase1Achievements.filter(a => a.rarity === 'uncommon').length,
      rare: phase1Achievements.filter(a => a.rarity === 'rare').length,
      epic: phase1Achievements.filter(a => a.rarity === 'epic').length,
      legendary: phase1Achievements.filter(a => a.rarity === 'legendary').length
    },
    totalPointsAvailable: phase1Achievements.reduce((sum, a) => sum + a.rewards.points, 0),
    averagePointsPerAchievement: Math.round(
      phase1Achievements.reduce((sum, a) => sum + a.rewards.points, 0) / phase1Achievements.length
    ),
    triggersSupported: phase1Triggers.length
  }
}

/**
 * Validate Phase 1 achievement
 */
export function validatePhase1Achievement(achievement: Achievement): boolean {
  const requiredFields = ['id', 'title', 'description', 'icon', 'category', 'rarity', 'requirements', 'rewards']
  return requiredFields.every(field => achievement[field as keyof Achievement] !== undefined)
}

export default phase1Achievements;