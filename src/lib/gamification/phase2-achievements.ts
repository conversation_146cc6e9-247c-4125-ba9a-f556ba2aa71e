/**
 * Phase 2 Achievement Definitions
 * 
 * Advanced engagement and loyalty achievements for the Syndicaps gamification system.
 * Implements 24 additional achievements focusing on long-term engagement,
 * platform exploration, and user loyalty.
 * 
 * <AUTHOR> Team - Gamification Phase 2
 * @version 1.0.0
 */

import type { Achievement, AchievementRarity, AchievementCategory } from './phase1-achievements'

// ===== EXTENDED TYPES FOR PHASE 2 =====

export type Phase2Category = 
  | 'loyalty_streaks'
  | 'analytics_exploration' 
  | 'profile_account'
  | 'shopping_purchasing'

export type ExtendedAchievementCategory = AchievementCategory | Phase2Category

export interface Phase2Achievement extends Omit<Achievement, 'category'> {
  category: ExtendedAchievementCategory;
  phase: 2;
  advancedFeatures?: {
    leaderboardEligible?: boolean;
    socialShareable?: boolean;
    emailWorthy?: boolean;
    competitiveRanking?: boolean;
  };
}

// ===== PHASE 2 TRIGGER TYPES =====

export const phase2Triggers = [
  // Loyalty & Streaks
  'daily_login',
  'weekly_consistency',
  'monthly_active',
  'return_visit',
  'loyalty_milestone',
  
  // Analytics & Exploration
  'feature_discovery',
  'page_exploration',
  'device_usage',
  'session_duration',
  'data_interaction',
  
  // Profile & Account
  'profile_update',
  'security_enhancement',
  'privacy_action',
  'account_verification',
  'settings_optimization',
  
  // Shopping & Purchasing
  'product_discovery',
  'wishlist_action',
  'purchase_milestone',
  'collection_building',
  'spending_threshold',
  'variety_exploration'
] as const

export type Phase2Trigger = typeof phase2Triggers[number]

// ===== PHASE 2 ACHIEVEMENT DEFINITIONS =====

export const phase2Achievements: Phase2Achievement[] = [
  // 🎖️ LOYALTY & STREAKS (6 achievements)
  {
    id: 'daily_visitor',
    title: 'Daily Visitor',
    description: 'Visit the site for 7 consecutive days',
    icon: '📅',
    category: 'loyalty_streaks',
    rarity: 'common',
    phase: 2,
    requirements: [{ type: 'consecutive_daily_visits', target: 7 }],
    rewards: { points: 100 },
    gamificationTriggers: ['daily_login'],
    isActive: true,
    progressChain: ['daily_visitor', 'weekly_regular', 'monthly_devotee'],
    metadata: { version: '2.0.0', phase: 2, tags: ['loyalty', 'streak'] },
    advancedFeatures: {
      leaderboardEligible: true,
      socialShareable: true
    },
    points: 0
  },

  {
    id: 'weekly_regular',
    title: 'Weekly Regular',
    description: 'Visit the site for 30 consecutive days',
    icon: '🗓️',
    category: 'loyalty_streaks',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'consecutive_daily_visits', target: 30 }],
    rewards: { points: 300 },
    gamificationTriggers: ['daily_login'],
    isActive: true,
    prerequisites: ['daily_visitor'],
    progressChain: ['daily_visitor', 'weekly_regular', 'monthly_devotee'],
    metadata: { version: '2.0.0', phase: 2, tags: ['loyalty', 'consistency'] },
    advancedFeatures: {
      leaderboardEligible: true,
      socialShareable: true,
      emailWorthy: true
    },
    points: 0
  },

  {
    id: 'monthly_devotee',
    title: 'Monthly Devotee',
    description: 'Stay active for 3 consecutive months',
    icon: '📆',
    category: 'loyalty_streaks',
    rarity: 'rare',
    phase: 2,
    requirements: [{ type: 'consecutive_monthly_activity', target: 3 }],
    rewards: { points: 500 },
    gamificationTriggers: ['monthly_active'],
    isActive: true,
    prerequisites: ['weekly_regular'],
    progressChain: ['daily_visitor', 'weekly_regular', 'monthly_devotee'],
    metadata: { version: '2.0.0', phase: 2, tags: ['loyalty', 'dedication'] },
    advancedFeatures: {
      leaderboardEligible: true,
      socialShareable: true,
      emailWorthy: true,
      competitiveRanking: true
    },
    points: 0
  },

  {
    id: 'anniversary_member',
    title: 'Anniversary Member',
    description: 'Member for 1 full year',
    icon: '🎂',
    category: 'loyalty_streaks',
    rarity: 'epic',
    phase: 2,
    requirements: [{ type: 'membership_duration_days', target: 365 }],
    rewards: { points: 1000 },
    gamificationTriggers: ['loyalty_milestone'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['milestone', 'anniversary'] },
    advancedFeatures: {
      socialShareable: true,
      emailWorthy: true,
      competitiveRanking: true
    },
    points: 0
  },

  {
    id: 'comeback_champion',
    title: 'Comeback Champion',
    description: 'Return after 30+ days absence with strong engagement',
    icon: '🔄',
    category: 'loyalty_streaks',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'return_engagement_score', target: 80 }],
    rewards: { points: 200 },
    gamificationTriggers: ['return_visit'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['return', 'engagement'] },
    advancedFeatures: {
      socialShareable: true
    },
    points: 0
  },

  {
    id: 'loyalty_legend',
    title: 'Loyalty Legend',
    description: 'Member for 2+ years with consistent high activity',
    icon: '👑',
    category: 'loyalty_streaks',
    rarity: 'legendary',
    phase: 2,
    requirements: [
      { type: 'membership_duration_days', target: 730 },
      { type: 'average_engagement_score', target: 70 }
    ],
    rewards: { points: 3000 },
    gamificationTriggers: ['loyalty_milestone'],
    isActive: true,
    prerequisites: ['anniversary_member'],
    metadata: { version: '2.0.0', phase: 2, tags: ['legendary', 'loyalty'] },
    advancedFeatures: {
      leaderboardEligible: true,
      socialShareable: true,
      emailWorthy: true,
      competitiveRanking: true
    },
    points: 0
  },

  // 📊 ANALYTICS & EXPLORATION (5 achievements)
  {
    id: 'data_explorer',
    title: 'Data Explorer',
    description: 'Check your raffle history 10 times',
    icon: '📊',
    category: 'analytics_exploration',
    rarity: 'common',
    phase: 2,
    requirements: [{ type: 'analytics_page_visits', target: 10 }],
    rewards: { points: 75 },
    gamificationTriggers: ['data_interaction'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['analytics', 'exploration'] },
    points: 0
  },

  {
    id: 'trend_spotter',
    title: 'Trend Spotter',
    description: 'View upcoming raffles page 15 times',
    icon: '📈',
    category: 'analytics_exploration',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'upcoming_page_visits', target: 15 }],
    rewards: { points: 150 },
    gamificationTriggers: ['page_exploration'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['trends', 'discovery'] },
    points: 0
  },

  {
    id: 'feature_discoverer',
    title: 'Feature Discoverer',
    description: 'Use 10 different platform features',
    icon: '🔍',
    category: 'analytics_exploration',
    rarity: 'rare',
    phase: 2,
    requirements: [{ type: 'unique_features_used', target: 10 }],
    rewards: { points: 300 },
    gamificationTriggers: ['feature_discovery'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['features', 'discovery'] },
    advancedFeatures: {
      leaderboardEligible: true
    },
    points: 0
  },

  {
    id: 'mobile_explorer',
    title: 'Mobile Explorer',
    description: 'Complete 10 activities on mobile device',
    icon: '📱',
    category: 'analytics_exploration',
    rarity: 'common',
    phase: 2,
    requirements: [{ type: 'mobile_activities', target: 10 }],
    rewards: { points: 100 },
    gamificationTriggers: ['device_usage'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['mobile', 'device'] },
    points: 0
  },

  {
    id: 'cross_platform_user',
    title: 'Cross-Platform User',
    description: 'Use both desktop and mobile regularly',
    icon: '💻',
    category: 'analytics_exploration',
    rarity: 'uncommon',
    phase: 2,
    requirements: [
      { type: 'desktop_sessions', target: 5 },
      { type: 'mobile_sessions', target: 5 }
    ],
    rewards: { points: 200 },
    gamificationTriggers: ['device_usage'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['cross-platform', 'versatility'] },
    points: 0
  },

  // 👤 PROFILE & ACCOUNT (5 achievements)
  {
    id: 'profile_perfectionist',
    title: 'Profile Perfectionist',
    description: 'Complete 100% of profile information',
    icon: '✨',
    category: 'profile_account',
    rarity: 'common',
    phase: 2,
    requirements: [{ type: 'profile_completion_percentage', target: 100 }],
    rewards: { points: 100 },
    gamificationTriggers: ['profile_update'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['profile', 'completion'] },
    points: 0
  },

  {
    id: 'address_manager',
    title: 'Address Manager',
    description: 'Save 3 different shipping addresses',
    icon: '📍',
    category: 'profile_account',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'shipping_addresses_saved', target: 3 }],
    rewards: { points: 150 },
    gamificationTriggers: ['profile_update'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['addresses', 'management'] },
    points: 0
  },

  {
    id: 'security_conscious',
    title: 'Security Conscious',
    description: 'Enable two-factor authentication',
    icon: '🔒',
    category: 'profile_account',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'two_factor_enabled', target: 1 }],
    rewards: { points: 200 },
    gamificationTriggers: ['security_enhancement'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['security', 'protection'] },
    advancedFeatures: {
      emailWorthy: true
    },
    points: 0
  },

  {
    id: 'privacy_guardian',
    title: 'Privacy Guardian',
    description: 'Review and optimize privacy settings',
    icon: '🛡️',
    category: 'profile_account',
    rarity: 'rare',
    phase: 2,
    requirements: [{ type: 'privacy_settings_reviewed', target: 1 }],
    rewards: { points: 300 },
    gamificationTriggers: ['privacy_action'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['privacy', 'data'] },
    points: 0
  },

  {
    id: 'notification_master',
    title: 'Notification Master',
    description: 'Customize notification preferences for all channels',
    icon: '🔔',
    category: 'profile_account',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'notification_channels_configured', target: 4 }],
    rewards: { points: 150 },
    gamificationTriggers: ['settings_optimization'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['notifications', 'customization'] },
    points: 0
  },

  // 🛍️ SHOPPING & PURCHASING (8 achievements)
  {
    id: 'window_shopper',
    title: 'Window Shopper',
    description: 'Browse 50 different products',
    icon: '👀',
    category: 'shopping_purchasing',
    rarity: 'common',
    phase: 2,
    requirements: [{ type: 'unique_products_viewed', target: 50 }],
    rewards: { points: 100 },
    gamificationTriggers: ['product_discovery'],
    isActive: true,
    progressChain: ['window_shopper', 'product_explorer', 'catalog_master'],
    metadata: { version: '2.0.0', phase: 2, tags: ['browsing', 'discovery'] },
    points: 0
  },

  {
    id: 'product_explorer',
    title: 'Product Explorer',
    description: 'Browse 100 different products',
    icon: '🗺️',
    category: 'shopping_purchasing',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'unique_products_viewed', target: 100 }],
    rewards: { points: 200 },
    gamificationTriggers: ['product_discovery'],
    isActive: true,
    prerequisites: ['window_shopper'],
    progressChain: ['window_shopper', 'product_explorer', 'catalog_master'],
    metadata: { version: '2.0.0', phase: 2, tags: ['exploration', 'catalog'] },
    points: 0
  },

  {
    id: 'catalog_master',
    title: 'Catalog Master',
    description: 'Browse 250 different products',
    icon: '📚',
    category: 'shopping_purchasing',
    rarity: 'rare',
    phase: 2,
    requirements: [{ type: 'unique_products_viewed', target: 250 }],
    rewards: { points: 400 },
    gamificationTriggers: ['product_discovery'],
    isActive: true,
    prerequisites: ['product_explorer'],
    progressChain: ['window_shopper', 'product_explorer', 'catalog_master'],
    metadata: { version: '2.0.0', phase: 2, tags: ['mastery', 'comprehensive'] },
    advancedFeatures: {
      leaderboardEligible: true,
      socialShareable: true
    },
    points: 0
  },

  {
    id: 'wishlist_builder',
    title: 'Wishlist Builder',
    description: 'Add 10 products to your wishlist',
    icon: '⭐',
    category: 'shopping_purchasing',
    rarity: 'common',
    phase: 2,
    requirements: [{ type: 'wishlist_items_added', target: 10 }],
    rewards: { points: 100 },
    gamificationTriggers: ['wishlist_action'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['wishlist', 'planning'] },
    points: 0
  },

  {
    id: 'wishlist_curator',
    title: 'Wishlist Curator',
    description: 'Maintain an active wishlist of 25+ items',
    icon: '📋',
    category: 'shopping_purchasing',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'active_wishlist_size', target: 25 }],
    rewards: { points: 200 },
    gamificationTriggers: ['wishlist_action'],
    isActive: true,
    prerequisites: ['wishlist_builder'],
    metadata: { version: '2.0.0', phase: 2, tags: ['curation', 'organization'] },
    points: 0
  },

  {
    id: 'category_explorer',
    title: 'Category Explorer',
    description: 'View products in 5 different categories',
    icon: '🗂️',
    category: 'shopping_purchasing',
    rarity: 'uncommon',
    phase: 2,
    requirements: [{ type: 'product_categories_explored', target: 5 }],
    rewards: { points: 150 },
    gamificationTriggers: ['product_discovery'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['categories', 'diversity'] },
    points: 0
  },

  {
    id: 'color_connoisseur',
    title: 'Color Connoisseur',
    description: 'View products in 15 different colors',
    icon: '🌈',
    category: 'shopping_purchasing',
    rarity: 'rare',
    phase: 2,
    requirements: [{ type: 'product_colors_viewed', target: 15 }],
    rewards: { points: 300 },
    gamificationTriggers: ['variety_exploration'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['colors', 'aesthetics'] },
    points: 0
  },

  {
    id: 'collection_starter',
    title: 'Collection Starter',
    description: 'Show interest in building a themed collection',
    icon: '💎',
    category: 'shopping_purchasing',
    rarity: 'rare',
    phase: 2,
    requirements: [{ type: 'themed_collection_interest', target: 1 }],
    rewards: { points: 350 },
    gamificationTriggers: ['collection_building'],
    isActive: true,
    metadata: { version: '2.0.0', phase: 2, tags: ['collections', 'themes'] },
    advancedFeatures: {
      socialShareable: true
    },
    points: 0
  }
]

// ===== HELPER FUNCTIONS =====

export function getPhase2AchievementsByCategory(category: Phase2Category): Phase2Achievement[] {
  return phase2Achievements.filter(achievement => achievement.category === category)
}

export function getPhase2AchievementsByFeature(feature: keyof NonNullable<Phase2Achievement['advancedFeatures']>): Phase2Achievement[] {
  return phase2Achievements.filter(achievement => 
    achievement.advancedFeatures?.[feature] === true
  )
}

export function getAllPhase2Triggers(): Phase2Trigger[] {
  return [...phase2Triggers]
}

export function validatePhase2Achievement(achievement: Phase2Achievement): boolean {
  const requiredFields = ['id', 'title', 'description', 'icon', 'category', 'rarity', 'requirements', 'rewards', 'phase']
  return requiredFields.every(field => achievement[field as keyof Phase2Achievement] !== undefined) && 
         achievement.phase === 2
}

// ===== PHASE 2 STATISTICS =====

export function getPhase2Stats() {
  return {
    totalAchievements: phase2Achievements.length,
    byCategory: {
      loyalty_streaks: getPhase2AchievementsByCategory('loyalty_streaks').length,
      analytics_exploration: getPhase2AchievementsByCategory('analytics_exploration').length,
      profile_account: getPhase2AchievementsByCategory('profile_account').length,
      shopping_purchasing: getPhase2AchievementsByCategory('shopping_purchasing').length
    },
    byRarity: {
      common: phase2Achievements.filter(a => a.rarity === 'common').length,
      uncommon: phase2Achievements.filter(a => a.rarity === 'uncommon').length,
      rare: phase2Achievements.filter(a => a.rarity === 'rare').length,
      epic: phase2Achievements.filter(a => a.rarity === 'epic').length,
      legendary: phase2Achievements.filter(a => a.rarity === 'legendary').length
    },
    advancedFeatures: {
      leaderboardEligible: getPhase2AchievementsByFeature('leaderboardEligible').length,
      socialShareable: getPhase2AchievementsByFeature('socialShareable').length,
      emailWorthy: getPhase2AchievementsByFeature('emailWorthy').length,
      competitiveRanking: getPhase2AchievementsByFeature('competitiveRanking').length
    },
    totalPointsAvailable: phase2Achievements.reduce((sum, a) => sum + a.rewards.points, 0),
    averagePointsPerAchievement: Math.round(
      phase2Achievements.reduce((sum, a) => sum + a.rewards.points, 0) / phase2Achievements.length
    )
  }
}

export default phase2Achievements