/**
 * Gamification System - Phase 2 Index
 * 
 * Main entry point for Phase 2 advanced gamification features.
 * Exports all Phase 2 functionality for easy integration.
 * 
 * <AUTHOR> Team - Gamification Phase 2
 * @version 1.0.0
 */

// ===== PHASE 2 CORE EXPORTS =====

// Phase 2 achievement definitions and types
export { 
  phase2Achievements,
  phase2Triggers,
  getPhase2AchievementsByCategory,
  getPhase2AchievementsByFeature,
  getAllPhase2Triggers,
  validatePhase2Achievement,
  getPhase2Stats
} from './phase2-achievements'

export type { 
  Phase2Achievement,
  Phase2Category,
  ExtendedAchievementCategory,
  Phase2Trigger
} from './phase2-achievements'

// Phase 2 tracking system
export { 
  Phase2AchievementTracker,
  createPhase2ActivityTrigger
} from './phase2-tracking'

export type {
  Phase2ActivityTrigger,
  UserStreakData,
  UserExplorationData,
  UserProfileData,
  UserShoppingData
} from './phase2-tracking'

// Advanced notification system
export { 
  AdvancedNotificationManager
} from './advancedNotifications'

export type {
  AdvancedNotificationConfig,
  EmailDigest,
  MobileNotification,
  SocialShareContent,
  LeaderboardAnnouncement
} from './advancedNotifications'

// Import for internal use
import type { AdvancedNotificationConfig } from './advancedNotifications'
import { AdvancedNotificationManager } from './advancedNotifications'
import { LeaderboardManager } from './leaderboards'
import phase2Achievements, { Phase2Trigger, getPhase2Stats, validatePhase2Achievement, phase2Triggers } from './phase2-achievements'
import Phase2AchievementTracker, { createPhase2ActivityTrigger } from './phase2-tracking'
import { SocialProofManager } from './socialProof'

// Leaderboards and competition system
export { 
  LeaderboardManager
} from './leaderboards'

export type {
  LeaderboardEntry,
  LeaderboardConfig,
  Competition,
  CommunityChallenge,
  SocialFeature
} from './leaderboards'

// Social proof and sharing system
export { 
  SocialProofManager
} from './socialProof'

export type {
  AchievementShare,
  SocialProofWidget,
  AchievementShowcase,
  CommunityFeed,
  InfluencerProgram
} from './socialProof'

// ===== INTEGRATION HELPERS =====

/**
 * Initialize complete Phase 2 system
 */
export async function initializePhase2System(config?: {
  notifications?: Partial<AdvancedNotificationConfig>;
  leaderboards?: boolean;
  socialProof?: boolean;
}) {
  try {
    console.log('Initializing Phase 2 Gamification System...')
    
    // Initialize advanced notifications
    if (config?.notifications) {
      AdvancedNotificationManager.initializeAdvanced(config?.notifications)
    }
    
    // Initialize leaderboards
    if (config?.leaderboards !== false) {
      await LeaderboardManager.initialize()
    }
    
    // Initialize social proof
    if (config?.socialProof !== false) {
      SocialProofManager.initialize()
    }
    
    console.log('Phase 2 Gamification System initialized successfully')
    
  } catch (error) {
    console.error('Failed to initialize Phase 2 system:', error)
    throw error
  }
}

/**
 * Handle Phase 2 activity tracking
 */
export async function trackPhase2Activity(
  userId: string,
  trigger: Phase2Trigger,
  data: Record<string, any> = {},
  deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop'
) {
  try {
    const activityTrigger = createPhase2ActivityTrigger(userId, trigger, data, deviceType)
    const results = await Phase2AchievementTracker.trackPhase2Activity(activityTrigger)
    
    // Handle notifications for unlocked achievements
    for (const result of results) {
      if (result.wasUnlocked) {
        await AdvancedNotificationManager.handleAdvancedAchievementUnlock(result, userId)
        await LeaderboardManager.updateUserRank(userId, result.achievementId)
      }
    }
    
    return results
    
  } catch (error) {
    console.error('Error tracking Phase 2 activity:', error)
    return []
  }
}

/**
 * Quick helpers for common Phase 2 activities
 */
export const Phase2Helpers = {
  // Loyalty & Streaks
  async trackDailyLogin(userId: string) {
    return trackPhase2Activity(userId, 'daily_login', {
      loginTime: new Date().toISOString()
    })
  },

  async trackReturnVisit(userId: string, daysSinceLastVisit: number, engagementScore: number) {
    return trackPhase2Activity(userId, 'return_visit', {
      daysSinceLastVisit,
      engagementScore
    })
  },

  // Analytics & Exploration
  async trackFeatureDiscovery(userId: string, feature: string) {
    return trackPhase2Activity(userId, 'feature_discovery', { feature })
  },

  async trackPageExploration(userId: string, page: string, duration: number) {
    return trackPhase2Activity(userId, 'page_exploration', { page, duration })
  },

  async trackDataInteraction(userId: string, section: string) {
    return trackPhase2Activity(userId, 'data_interaction', { section })
  },

  // Profile & Account
  async trackProfileUpdate(userId: string, completionPercentage: number, addressAdded?: boolean) {
    return trackPhase2Activity(userId, 'profile_update', {
      completionPercentage,
      addressAdded
    })
  },

  async trackSecurityEnhancement(userId: string, twoFactorEnabled: boolean) {
    return trackPhase2Activity(userId, 'security_enhancement', { twoFactorEnabled })
  },

  async trackPrivacyAction(userId: string, settingsReviewed: boolean) {
    return trackPhase2Activity(userId, 'privacy_action', { settingsReviewed })
  },

  // Shopping & Purchasing
  async trackProductDiscovery(userId: string, productId: string, category?: string) {
    return trackPhase2Activity(userId, 'product_discovery', { productId, category })
  },

  async trackWishlistAction(userId: string, action: 'add' | 'remove', productId: string, currentSize: number) {
    return trackPhase2Activity(userId, 'wishlist_action', {
      action,
      productId,
      currentSize
    })
  },

  async trackVarietyExploration(userId: string, color: string, category: string) {
    return trackPhase2Activity(userId, 'variety_exploration', { color, category })
  },

  async trackCollectionBuilding(userId: string, theme: string, products: string[]) {
    return trackPhase2Activity(userId, 'collection_building', { theme, products })
  }
}

/**
 * Social features helpers
 */
export const SocialHelpers = {
  async shareAchievement(
    userId: string,
    achievementId: string,
    platform: 'twitter' | 'facebook' | 'instagram' | 'discord' | 'linkedin',
    customMessage?: string
  ) {
    try {
      // Find the achievement
      const achievement = phase2Achievements.find(a => a.id === achievementId)
      if (!achievement) {
        throw new Error(`Achievement ${achievementId} not found`)
      }

      return await SocialProofManager.createAchievementShare(
        userId,
        achievement,
        platform,
        customMessage
      )
    } catch (error) {
      console.error('Error sharing achievement:', error)
      throw error
    }
  },

  async createShowcase(userId: string, featuredAchievements: string[]) {
    return await SocialProofManager.createAchievementShowcase(userId, featuredAchievements)
  },

  async getLeaderboard(type: 'points' | 'achievements', period: 'weekly' | 'monthly' | 'all-time') {
    return await LeaderboardManager.getLeaderboard(type, period)
  },

  async getUserRank(userId: string, type: 'points' | 'achievements') {
    return await LeaderboardManager.getUserRank(userId, type)
  }
}

/**
 * Get comprehensive Phase 2 statistics
 */
export function getPhase2SystemStats() {
  const achievementStats = getPhase2Stats()
  const leaderboardStats = LeaderboardManager.getLeaderboardStats()
  const socialStats = SocialProofManager.getStats()
  
  return {
    achievements: achievementStats,
    leaderboards: leaderboardStats,
    social: socialStats,
    system: {
      phase: 2,
      version: '1.0.0',
      totalFeatures: 4, // tracking, notifications, leaderboards, social
      isFullyInitialized: true
    }
  }
}

/**
 * Validate Phase 2 system integrity
 */
export function validatePhase2System(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Validate achievement definitions
  phase2Achievements.forEach(achievement => {
    if (!validatePhase2Achievement(achievement)) {
      errors.push(`Invalid Phase 2 achievement: ${achievement.id}`)
    }
    
    if (achievement.phase !== 2) {
      errors.push(`Achievement ${achievement.id} has incorrect phase: ${achievement.phase}`)
    }
  })
  
  // Check for duplicate IDs between Phase 1 and Phase 2
  const phase2Ids = new Set(phase2Achievements.map(a => a.id))
  
  // Would need to import Phase 1 achievements to check for conflicts
  // For now, just validate Phase 2 internal consistency
  
  // Check trigger definitions
  const usedTriggers = new Set()
  phase2Achievements.forEach(achievement => {
    achievement.gamificationTriggers.forEach(trigger => {
      if (!phase2Triggers.includes(trigger as any)) {
        errors.push(`Unknown trigger in ${achievement.id}: ${trigger}`)
      }
      usedTriggers.add(trigger)
    })
  })
  
  // Check for unused triggers
  phase2Triggers.forEach(trigger => {
    if (!usedTriggers.has(trigger)) {
      warnings.push(`Unused trigger defined: ${trigger}`)
    }
  })
  
  // Validate point distribution
  const totalPoints = phase2Achievements.reduce((sum, a) => sum + a.rewards.points, 0)
  if (totalPoints < 5000) {
    warnings.push(`Low total points available in Phase 2: ${totalPoints}`)
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// ===== VERSION INFO =====

export const PHASE2_VERSION = '1.0.0'
export const PHASE2_BUILD_DATE = new Date().toISOString()

export default {
  // Initialization
  initializePhase2System,
  
  // Activity tracking
  trackPhase2Activity,
  Phase2Helpers,
  
  // Social features
  SocialHelpers,
  
  // Statistics and validation
  getPhase2SystemStats,
  validatePhase2System,
  
  // Version info
  version: PHASE2_VERSION,
  buildDate: PHASE2_BUILD_DATE,
  
  // Quick access to main classes
  Phase2AchievementTracker,
  AdvancedNotificationManager,
  LeaderboardManager,
  SocialProofManager,
  
  // Achievement data
  achievements: phase2Achievements,
  triggers: phase2Triggers
}