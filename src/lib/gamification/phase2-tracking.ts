/**
 * Phase 2 Achievement Tracking System
 * 
 * Extended tracking system for Phase 2 achievements including loyalty streaks,
 * analytics exploration, profile management, and shopping behavior.
 * 
 * <AUTHOR> Team - Gamification Phase 2
 * @version 1.0.0
 */

import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  setDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  runTransaction,
  Timestamp,
  increment
} from 'firebase/firestore'
import { db } from '../firebase'
import { collections } from '../firebase/gamificationCollections'
import { AchievementTracker, createActivityTrigger } from './achievementTracking'
import { phase2Achievements, type Phase2Achievement, type Phase2Trigger } from './phase2-achievements'
import type { AchievementUnlockResult } from './achievementTracking'

// ===== TYPES =====

export interface Phase2ActivityTrigger {
  userId: string;
  trigger: Phase2Trigger;
  data: Record<string, any>;
  timestamp?: Timestamp;
  sessionId?: string;
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  metadata?: Record<string, any>;
}

export interface UserStreakData {
  userId: string;
  currentLoginStreak: number;
  longestLoginStreak: number;
  lastLoginDate: string; // YYYY-MM-DD format
  consecutiveWeeks: number;
  consecutiveMonths: number;
  lastActiveMonth: string; // YYYY-MM format
  streakBreakDate?: string;
  totalLoginDays: number;
}

export interface UserExplorationData {
  userId: string;
  featuresUsed: string[];
  pagesVisited: string[];
  deviceSessions: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  analyticsInteractions: number;
  sessionDurations: number[]; // in minutes
  lastExplorationDate: Timestamp;
}

export interface UserProfileData {
  userId: string;
  completionPercentage: number;
  shippingAddresses: number;
  twoFactorEnabled: boolean;
  privacySettingsReviewed: boolean;
  notificationChannelsConfigured: number;
  lastProfileUpdate: Timestamp;
  securityScore: number; // 0-100
}

export interface UserShoppingData {
  userId: string;
  uniqueProductsViewed: string[];
  categoriesExplored: string[];
  colorsViewed: string[];
  wishlistItems: string[];
  activeWishlistSize: number;
  lastShoppingActivity: Timestamp;
  browsingPatterns: {
    averageSessionTime: number;
    productViewsPerSession: number;
    categoryPreferences: Record<string, number>;
  };
}

// ===== PHASE 2 ACHIEVEMENT TRACKER =====

export class Phase2AchievementTracker extends AchievementTracker {
  /**
   * Track Phase 2 specific activities
   */
  static async trackPhase2Activity(trigger: Phase2ActivityTrigger): Promise<AchievementUnlockResult[]> {
    try {
      console.log(`Tracking Phase 2 activity: ${trigger.trigger} for user ${trigger.userId}`)
      
      // Find Phase 2 achievements that use this trigger
      const relevantAchievements = phase2Achievements.filter(achievement =>
        achievement.isActive && achievement.gamificationTriggers.includes(trigger.trigger)
      );

      if (relevantAchievements.length === 0) {
        console.log(`No Phase 2 achievements found for trigger: ${trigger.trigger}`)
        return []
      }

      const results: AchievementUnlockResult[] = []

      // Process each relevant achievement
      for (const achievement of relevantAchievements) {
        try {
          const result = await this.processPhase2AchievementProgress(achievement, trigger)
          if (result) {
            results.push(result)
          }
        } catch (error) {
          console.error(`Error processing Phase 2 achievement ${achievement.id}:`, error)
        }
      }

      // Log the activity for analytics
      await this.logPhase2Activity(trigger)

      return results
    } catch (error) {
      console.error('Error tracking Phase 2 activity:', error)
      return []
    }
  }

  /**
   * Process Phase 2 achievement progress calculation
   */
  private static async processPhase2AchievementProgress(
    achievement: Phase2Achievement,
    trigger: Phase2ActivityTrigger
  ): Promise<AchievementUnlockResult | null> {
    return await runTransaction(db, async (transaction) => {
      // Get current user achievement progress
      const userAchievementRef = doc(
        collection(db, collections.userAchievements),
        `${trigger.userId}_${achievement.id}`
      )
      
      const userAchievementDoc = await transaction.get(userAchievementRef)
      
      let currentProgress: any
      
      if (userAchievementDoc.exists()) {
        currentProgress = userAchievementDoc.data()
        
        // Skip if already completed
        if (currentProgress.isCompleted) {
          return null
        }
      } else {
        // Initialize new progress tracking
        currentProgress = {
          userId: trigger.userId,
          achievementId: achievement.id,
          progress: 0,
          isCompleted: false,
          progressData: {},
          lastUpdated: serverTimestamp() as Timestamp,
          phase: 2,
          triggerCount: 0
        }
      }

      // Calculate new progress based on achievement requirements
      const { newProgress, progressData } = await this.calculatePhase2Progress(
        achievement,
        currentProgress,
        trigger
      )

      const previousProgress = currentProgress.progress
      const wasUnlocked = newProgress >= 100 && !currentProgress.isCompleted

      // Update progress
      const updatedProgress = {
        ...currentProgress,
        progress: newProgress,
        isCompleted: newProgress >= 100,
        unlockedAt: wasUnlocked ? serverTimestamp() as Timestamp : currentProgress.unlockedAt,
        progressData,
        lastUpdated: serverTimestamp() as Timestamp,
        triggerCount: currentProgress.triggerCount + 1
      }

      // Save updated progress
      transaction.set(userAchievementRef, updatedProgress)

      let pointsAwarded = 0
      let tierBonusApplied = 0

      // Award points if achievement was unlocked
      if (wasUnlocked) {
        const basePoints = achievement.rewards.points
        
        // Get user's current tier for bonus calculation
        const userTier = await this.getUserTier(trigger.userId)
        tierBonusApplied = await this.calculateTierBonus(basePoints, userTier)
        pointsAwarded = basePoints + tierBonusApplied

        // Create point transaction
        const pointTransactionRef = doc(collection(db, collections.pointTransactions))
        transaction.set(pointTransactionRef, {
          userId: trigger.userId,
          type: 'points_earned',
          amount: pointsAwarded,
          source: 'achievement_unlock',
          description: `Phase 2 Achievement unlocked: ${achievement.title}`,
          timestamp: serverTimestamp(),
          metadata: {
            achievementId: achievement.id,
            achievementTitle: achievement.title,
            basePoints,
            tierBonus: tierBonusApplied,
            rarity: achievement.rarity,
            category: achievement.category,
            phase: 2
          },
          auditTrail: [{
            action: 'points_awarded',
            timestamp: serverTimestamp(),
            source: 'phase2_achievement_system'
          }]
        })

        // Update user's total points
        const userRef = doc(collection(db, collections.users), trigger.userId)
        transaction.update(userRef, {
          'gamification.totalPointsEarned': increment(pointsAwarded),
          'gamification.lastAchievementUnlock': serverTimestamp(),
          'stats.phase2AchievementsUnlocked': increment(1)
        })

        console.log(`Phase 2 Achievement unlocked: ${achievement.title} for user ${trigger.userId} (${pointsAwarded} points)`)
      }

      return {
        achievementId: achievement.id,
        achievement: achievement as any, // Type compatibility
        previousProgress,
        newProgress,
        wasUnlocked,
        pointsAwarded,
        tierBonusApplied
      }
    })
  }

  /**
   * Calculate Phase 2 specific progress
   */
  private static async calculatePhase2Progress(
    achievement: Phase2Achievement,
    currentProgress: any,
    trigger: Phase2ActivityTrigger
  ): Promise<{ newProgress: number; progressData: Record<string, any> }> {
    const requirement = achievement.requirements[0]
    const progressData = { ...currentProgress.progressData }

    switch (requirement.type) {
      // Loyalty & Streaks
      case 'consecutive_daily_visits':
        return await this.calculateLoginStreak(trigger.userId, requirement.target, progressData, trigger)

      case 'consecutive_monthly_activity':
        return await this.calculateMonthlyActivity(trigger.userId, requirement.target, progressData, trigger)

      case 'membership_duration_days':
        return await this.calculateMembershipDuration(trigger.userId, requirement.target, progressData)

      case 'return_engagement_score':
        return await this.calculateReturnEngagement(trigger.userId, requirement.target, progressData, trigger)

      case 'average_engagement_score':
        return await this.calculateAverageEngagement(trigger.userId, requirement.target, progressData)

      // Analytics & Exploration
      case 'analytics_page_visits':
        if (trigger.trigger === 'data_interaction') {
          progressData.analyticsVisits = (progressData.analyticsVisits || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.analyticsVisits / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'upcoming_page_visits':
        if (trigger.trigger === 'page_exploration' && trigger.data.page === 'upcoming') {
          progressData.upcomingVisits = (progressData.upcomingVisits || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.upcomingVisits / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'unique_features_used':
        if (trigger.trigger === 'feature_discovery') {
          const featuresUsed = new Set(progressData.featuresUsed || [])
          featuresUsed.add(trigger.data.feature)
          progressData.featuresUsed = Array.from(featuresUsed)
          
          return {
            newProgress: Math.min(100, (progressData.featuresUsed.length / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'mobile_activities':
        if (trigger.trigger === 'device_usage' && trigger.deviceType === 'mobile') {
          progressData.mobileActivities = (progressData.mobileActivities || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.mobileActivities / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'desktop_sessions':
      case 'mobile_sessions':
        if (trigger.trigger === 'device_usage') {
          const sessionType = requirement.type.replace('_sessions', '') as 'desktop' | 'mobile'
          if (trigger.deviceType === sessionType) {
            progressData[requirement.type] = (progressData[requirement.type] || 0) + 1
            return {
              newProgress: Math.min(100, (progressData[requirement.type] / requirement.target) * 100),
              progressData
            }
          }
        }
        break

      // Profile & Account
      case 'profile_completion_percentage':
        if (trigger.trigger === 'profile_update') {
          const completionPercentage = trigger.data.completionPercentage || 0
          progressData.currentCompletion = completionPercentage
          return {
            newProgress: completionPercentage >= requirement.target ? 100 : (completionPercentage / requirement.target) * 100,
            progressData
          }
        }
        break

      case 'shipping_addresses_saved':
        if (trigger.trigger === 'profile_update' && trigger.data.addressAdded) {
          progressData.addressCount = trigger.data.totalAddresses || (progressData.addressCount || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.addressCount / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'two_factor_enabled':
        if (trigger.trigger === 'security_enhancement' && trigger.data.twoFactorEnabled) {
          progressData.twoFactorEnabled = true
          return {
            newProgress: 100,
            progressData
          }
        }
        break

      case 'privacy_settings_reviewed':
        if (trigger.trigger === 'privacy_action' && trigger.data.settingsReviewed) {
          progressData.privacyReviewed = true
          return {
            newProgress: 100,
            progressData
          }
        }
        break

      case 'notification_channels_configured':
        if (trigger.trigger === 'settings_optimization') {
          progressData.channelsConfigured = trigger.data.channelsConfigured || 0
          return {
            newProgress: Math.min(100, (progressData.channelsConfigured / requirement.target) * 100),
            progressData
          }
        }
        break

      // Shopping & Purchasing
      case 'unique_products_viewed':
        if (trigger.trigger === 'product_discovery') {
          const productsViewed = new Set(progressData.productsViewed || [])
          productsViewed.add(trigger.data.productId)
          progressData.productsViewed = Array.from(productsViewed)
          
          return {
            newProgress: Math.min(100, (progressData.productsViewed.length / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'wishlist_items_added':
        if (trigger.trigger === 'wishlist_action' && trigger.data.action === 'add') {
          progressData.wishlistAdded = (progressData.wishlistAdded || 0) + 1
          return {
            newProgress: Math.min(100, (progressData.wishlistAdded / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'active_wishlist_size':
        if (trigger.trigger === 'wishlist_action') {
          progressData.currentWishlistSize = trigger.data.currentSize || 0
          return {
            newProgress: progressData.currentWishlistSize >= requirement.target ? 100 : 
                        (progressData.currentWishlistSize / requirement.target) * 100,
            progressData
          }
        }
        break

      case 'product_categories_explored':
        if (trigger.trigger === 'product_discovery') {
          const categoriesExplored = new Set(progressData.categoriesExplored || [])
          if (trigger.data.category) {
            categoriesExplored.add(trigger.data.category)
            progressData.categoriesExplored = Array.from(categoriesExplored)
          }
          
          return {
            newProgress: Math.min(100, (progressData.categoriesExplored.length / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'product_colors_viewed':
        if (trigger.trigger === 'variety_exploration') {
          const colorsViewed = new Set(progressData.colorsViewed || [])
          if (trigger.data.color) {
            colorsViewed.add(trigger.data.color)
            progressData.colorsViewed = Array.from(colorsViewed)
          }
          
          return {
            newProgress: Math.min(100, (progressData.colorsViewed.length / requirement.target) * 100),
            progressData
          }
        }
        break

      case 'themed_collection_interest':
        if (trigger.trigger === 'collection_building') {
          progressData.collectionInterest = true
          progressData.collectionTheme = trigger.data.theme
          return {
            newProgress: 100,
            progressData
          }
        }
        break

      default:
        console.warn(`Unknown Phase 2 requirement type: ${requirement.type}`)
    }

    return { newProgress: currentProgress.progress, progressData }
  }

  // ===== HELPER METHODS FOR COMPLEX CALCULATIONS =====

  private static async calculateLoginStreak(
    userId: string,
    targetDays: number,
    progressData: any,
    trigger: Phase2ActivityTrigger
  ): Promise<{ newProgress: number; progressData: Record<string, any> }> {
    const today = new Date().toDateString()
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()
    
    const lastLoginDate = progressData.lastLoginDate
    const currentStreak = progressData.currentStreak || 0
    
    if (lastLoginDate === today) {
      // Already logged in today, no change
      return { newProgress: Math.min(100, (currentStreak / targetDays) * 100), progressData }
    }
    
    let newStreak: number
    
    if (lastLoginDate === yesterday || !lastLoginDate) {
      // Consecutive day or first login
      newStreak = currentStreak + 1
    } else {
      // Streak broken, reset
      newStreak = 1
    }
    
    progressData.currentStreak = newStreak
    progressData.lastLoginDate = today
    progressData.longestStreak = Math.max(progressData.longestStreak || 0, newStreak)
    
    return {
      newProgress: Math.min(100, (newStreak / targetDays) * 100),
      progressData
    }
  }

  private static async calculateMonthlyActivity(
    userId: string,
    targetMonths: number,
    progressData: any,
    trigger: Phase2ActivityTrigger
  ): Promise<{ newProgress: number; progressData: Record<string, any> }> {
    const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format
    const lastActiveMonth = progressData.lastActiveMonth
    const consecutiveMonths = progressData.consecutiveMonths || 0
    
    if (lastActiveMonth === currentMonth) {
      // Already active this month
      return { newProgress: Math.min(100, (consecutiveMonths / targetMonths) * 100), progressData }
    }
    
    // Check if this is a consecutive month
    const lastMonth = new Date()
    lastMonth.setMonth(lastMonth.getMonth() - 1)
    const expectedPreviousMonth = lastMonth.toISOString().slice(0, 7)
    
    let newConsecutiveMonths: number
    
    if (lastActiveMonth === expectedPreviousMonth || !lastActiveMonth) {
      newConsecutiveMonths = consecutiveMonths + 1
    } else {
      newConsecutiveMonths = 1
    }
    
    progressData.consecutiveMonths = newConsecutiveMonths
    progressData.lastActiveMonth = currentMonth
    
    return {
      newProgress: Math.min(100, (newConsecutiveMonths / targetMonths) * 100),
      progressData
    }
  }

  private static async calculateMembershipDuration(
    userId: string,
    targetDays: number,
    progressData: any
  ): Promise<{ newProgress: number; progressData: Record<string, any> }> {
    try {
      // Get user creation date
      const userDoc = await getDocs(
        query(
          collection(db, collections.users),
          where('__name__', '==', userId),
          limit(1)
        )
      )
      
      if (!userDoc.empty) {
        const userData = userDoc.docs[0].data()
        const createdAt = userData.createdAt?.toDate() || new Date()
        const daysSinceCreation = Math.floor((Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
        
        progressData.membershipDays = daysSinceCreation
        
        return {
          newProgress: Math.min(100, (daysSinceCreation / targetDays) * 100),
          progressData
        }
      }
    } catch (error) {
      console.error('Error calculating membership duration:', error)
    }
    
    return { newProgress: 0, progressData }
  }

  private static async calculateReturnEngagement(
    userId: string,
    targetScore: number,
    progressData: any,
    trigger: Phase2ActivityTrigger
  ): Promise<{ newProgress: number; progressData: Record<string, any> }> {
    // Calculate engagement score based on return activity
    const engagementScore = trigger.data.engagementScore || 0
    
    progressData.returnEngagementScore = engagementScore
    progressData.returnDate = new Date().toISOString()
    
    return {
      newProgress: engagementScore >= targetScore ? 100 : (engagementScore / targetScore) * 100,
      progressData
    }
  }

  private static async calculateAverageEngagement(
    userId: string,
    targetScore: number,
    progressData: any
  ): Promise<{ newProgress: number; progressData: Record<string, any> }> {
    try {
      // Get recent engagement metrics
      const engagementSnapshot = await getDocs(
        query(
          collection(db, collections.userEngagementMetrics),
          where('userId', '==', userId),
          orderBy('calculatedAt', 'desc'),
          limit(30)
        )
      )
      
      if (!engagementSnapshot.empty) {
        const scores = engagementSnapshot.docs.map(doc => doc.data().engagementScore || 0)
        const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
        
        progressData.averageEngagementScore = averageScore
        progressData.engagementHistory = scores.slice(0, 10) // Keep last 10 scores
        
        return {
          newProgress: averageScore >= targetScore ? 100 : (averageScore / targetScore) * 100,
          progressData
        }
      }
    } catch (error) {
      console.error('Error calculating average engagement:', error)
    }
    
    return { newProgress: 0, progressData }
  }

  /**
   * Log Phase 2 activity for analytics
   */
  private static async logPhase2Activity(trigger: Phase2ActivityTrigger): Promise<void> {
    try {
      await addDoc(collection(db, collections.userActivities), {
        userId: trigger.userId,
        type: `phase2_trigger_${trigger.trigger}`,
        data: trigger.data,
        deviceType: trigger.deviceType,
        metadata: {
          ...trigger.metadata,
          source: 'phase2_achievement_tracking',
          phase: 2
        },
        createdAt: trigger.timestamp || serverTimestamp()
      })
    } catch (error) {
      console.error('Error logging Phase 2 activity:', error)
    }
  }
}

// ===== HELPER FUNCTIONS =====

/**
 * Create a Phase 2 activity trigger
 */
export function createPhase2ActivityTrigger(
  userId: string,
  trigger: Phase2Trigger,
  data: Record<string, any> = {},
  deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop',
  metadata: Record<string, any> = {}
): Phase2ActivityTrigger {
  return {
    userId,
    trigger,
    data,
    deviceType,
    timestamp: serverTimestamp() as Timestamp,
    metadata: {
      ...metadata,
      phase: 2,
      source: 'phase2_system'
    }
  }
}

export default Phase2AchievementTracker