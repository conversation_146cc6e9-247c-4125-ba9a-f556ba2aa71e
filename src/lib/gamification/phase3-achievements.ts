/**
 * Phase 3 Achievement Definitions
 * 
 * Special events, seasonal achievements, and dynamic gamification features
 * for the Syndicaps gamification system. Implements time-limited, evolving,
 * and AI-powered achievement systems.
 * 
 * <AUTHOR> Team - Gamification Phase 3
 * @version 1.0.0
 */

import type { Achievement } from './phase1-achievements'
import type { Phase2Achievement } from './phase2-achievements'
import { Timestamp } from 'firebase/firestore'

// ===== EXTENDED TYPES FOR PHASE 3 =====

export type Phase3Category = 
  | 'seasonal_holiday'
  | 'special_event'
  | 'dynamic_challenge'
  | 'meta_achievement'
  | 'community_milestone'

export type AllAchievementCategories = 
  | 'raffle_entry' | 'raffle_success' | 'raffle_social' | 'raffle_timing' | 'raffle_engagement'
  | 'social_engagement' | 'profile' | 'shopping'
  | 'loyalty_streaks' | 'analytics_exploration' | 'profile_account' | 'shopping_purchasing'
  | Phase3Category

export interface SeasonalWindow {
  startDate: string; // MM-DD format
  endDate: string;   // MM-DD format
  year?: number;     // Specific year or null for annual
  timezone?: string;
}

export interface SpecialEvent {
  id: string;
  name: string;
  description: string;
  startDate: Timestamp;
  endDate: Timestamp;
  type: 'product_launch' | 'community_milestone' | 'platform_anniversary' | 'collaboration' | 'contest';
  isActive: boolean;
  participantCount?: number;
  maxParticipants?: number;
}

export interface DynamicAchievementConfig {
  template: string;
  variables: Record<string, any>;
  generationRules: {
    minDifficulty: number;
    maxDifficulty: number;
    targetCompletion: number; // percentage of users who should complete it
    adaptiveDifficulty: boolean;
  };
  personalization: {
    userBehaviorWeight: number;
    interestWeight: number;
    skillLevelWeight: number;
  };
}

export interface EvolutionRule {
  triggerType: 'time_based' | 'completion_based' | 'seasonal' | 'event_based';
  triggerCondition: any;
  evolution: {
    newTitle?: string;
    newDescription?: string;
    newIcon?: string;
    newRequirements?: any[];
    newRewards?: any;
    newRarity?: string;
  };
  preserveProgress: boolean;
}

export interface Phase3Achievement extends Omit<Achievement, 'category'> {
  category: AllAchievementCategories;
  phase: 3;
  
  // Time-limited features
  isTimeLimited?: boolean;
  availabilityWindow?: {
    startDate: Timestamp;
    endDate: Timestamp;
    seasonalWindow?: SeasonalWindow;
  };
  
  // Dynamic features
  isDynamic?: boolean;
  dynamicConfig?: DynamicAchievementConfig;
  
  // Evolution features
  canEvolve?: boolean;
  evolutionRules?: EvolutionRule[];
  parentAchievement?: string; // ID of evolved-from achievement
  
  // Special event features
  specialEvent?: string; // Special event ID
  limitedEdition?: boolean;
  maxCompletions?: number; // Global limit on how many users can complete it
  
  // Community features
  communityGenerated?: boolean;
  createdByUser?: string;
  communityVotes?: number;
  
  // Advanced metadata
  aiGenerated?: boolean;
  personalizationScore?: number;
  difficultyAdaptation?: {
    baseDifficulty: number;
    currentDifficulty: number;
    completionRate: number;
  };
}

// ===== PHASE 3 TRIGGER TYPES =====

export const phase3Triggers = [
  // Seasonal & Holiday
  'holiday_participation',
  'seasonal_activity',
  'anniversary_celebration',
  'limited_time_event',
  
  // Special Events
  'product_launch_participation',
  'community_milestone_reached',
  'special_event_completion',
  'collaboration_activity',
  
  // Dynamic & Meta
  'achievement_chain_completion',
  'category_mastery',
  'difficulty_progression',
  'personalized_challenge_completion',
  
  // Community
  'community_contribution',
  'user_generated_content',
  'peer_recognition',
  'community_event_participation'
] as const

export type Phase3Trigger = typeof phase3Triggers[number]

// ===== SEASONAL ACHIEVEMENTS =====

export const seasonalAchievements: Phase3Achievement[] = [
  {
    id: 'holiday_spirit_2024',
    title: 'Holiday Spirit 2024',
    description: 'Participate in holiday-themed activities during December',
    icon: '🎄',
    category: 'seasonal_holiday',
    rarity: 'rare',
    phase: 3,
    requirements: [{ type: 'holiday_activities_completed', target: 5 }],
    rewards: { points: 500, badge: 'holiday_2024' },
    gamificationTriggers: ['holiday_participation'],
    isActive: true,
    isTimeLimited: true,
    availabilityWindow: {
      startDate: new Date('2024-12-01') as any,
      endDate: new Date('2025-01-07') as any,
      seasonalWindow: {
        startDate: '12-01',
        endDate: '01-07'
      }
    },
    metadata: { version: '3.0.0', phase: 3, tags: ['holiday', 'seasonal', 'christmas'] },
    points: 0
  },

  {
    id: 'spooky_season_master',
    title: 'Spooky Season Master',
    description: 'Complete Halloween-themed challenges and activities',
    icon: '🎃',
    category: 'seasonal_holiday',
    rarity: 'rare',
    phase: 3,
    requirements: [
      { type: 'halloween_activities', target: 3 },
      { type: 'spooky_raffle_entries', target: 2 }
    ],
    rewards: { points: 400, badge: 'spooky_master' },
    gamificationTriggers: ['holiday_participation', 'seasonal_activity'],
    isActive: true,
    isTimeLimited: true,
    availabilityWindow: {
      startDate: new Date('2024-10-15') as any,
      endDate: new Date('2024-11-05') as any,
      seasonalWindow: {
        startDate: '10-15',
        endDate: '11-05'
      }
    },
    metadata: { version: '3.0.0', phase: 3, tags: ['halloween', 'seasonal', 'spooky'] },
    points: 0
  },

  {
    id: 'valentine_cupid',
    title: 'Valentine Cupid',
    description: 'Spread love in the community during Valentine season',
    icon: '💝',
    category: 'seasonal_holiday',
    rarity: 'uncommon',
    phase: 3,
    requirements: [
      { type: 'valentine_activities', target: 3 },
      { type: 'community_love_actions', target: 5 }
    ],
    rewards: { points: 250, badge: 'cupid' },
    gamificationTriggers: ['holiday_participation', 'community_contribution'],
    isActive: true,
    isTimeLimited: true,
    availabilityWindow: {
      startDate: new Date('2024-02-10') as any,
      endDate: new Date('2024-02-20') as any,
      seasonalWindow: {
        startDate: '02-10',
        endDate: '02-20'
      }
    },
    metadata: { version: '3.0.0', phase: 3, tags: ['valentine', 'love', 'community'] },
    points: 0
  },

  {
    id: 'summer_vibes',
    title: 'Summer Vibes',
    description: 'Enjoy summer-themed activities and bright collections',
    icon: '☀️',
    category: 'seasonal_holiday',
    rarity: 'common',
    phase: 3,
    requirements: [{ type: 'summer_themed_activities', target: 4 }],
    rewards: { points: 200 },
    gamificationTriggers: ['seasonal_activity'],
    isActive: true,
    isTimeLimited: true,
    availabilityWindow: {
      startDate: new Date('2024-06-21') as any,
      endDate: new Date('2024-09-21') as any,
      seasonalWindow: {
        startDate: '06-21',
        endDate: '09-21'
      }
    },
    metadata: { version: '3.0.0', phase: 3, tags: ['summer', 'seasonal', 'bright'] },
    points: 0
  },

  {
    id: 'anniversary_celebration',
    title: 'Anniversary Celebration',
    description: 'Celebrate Syndicaps platform anniversaries',
    icon: '🎉',
    category: 'seasonal_holiday',
    rarity: 'epic',
    phase: 3,
    requirements: [
      { type: 'anniversary_participation', target: 1 },
      { type: 'celebration_activities', target: 3 }
    ],
    rewards: { points: 750, badge: 'anniversary_celebrant' },
    gamificationTriggers: ['anniversary_celebration', 'special_event_completion'],
    isActive: true,
    isTimeLimited: true,
    limitedEdition: true,
    maxCompletions: 1000,
    metadata: { version: '3.0.0', phase: 3, tags: ['anniversary', 'celebration', 'limited'] },
    points: 0
  }
]

// ===== SPECIAL EVENT ACHIEVEMENTS =====

export const specialEventAchievements: Phase3Achievement[] = [
  {
    id: 'launch_day_hero',
    title: 'Launch Day Hero',
    description: 'Be among the first to participate in a product launch event',
    icon: '🚀',
    category: 'special_event',
    rarity: 'epic',
    phase: 3,
    requirements: [
      { type: 'product_launch_early_participation', target: 1 },
      { type: 'launch_day_activities', target: 3 }
    ],
    rewards: { points: 800, badge: 'launch_hero' },
    gamificationTriggers: ['product_launch_participation'],
    isActive: true,
    limitedEdition: true,
    maxCompletions: 100, // Only first 100 users
    metadata: { version: '3.0.0', phase: 3, tags: ['launch', 'early_adopter', 'exclusive'] },
    points: 0
  },

  {
    id: 'community_builder_1000',
    title: 'Community Builder - 1000',
    description: 'Help the community reach 1000 members milestone',
    icon: '🏗️',
    category: 'community_milestone',
    rarity: 'rare',
    phase: 3,
    requirements: [{ type: 'community_milestone_participation', target: 1 }],
    rewards: { points: 500, badge: 'community_1000' },
    gamificationTriggers: ['community_milestone_reached'],
    isActive: true,
    limitedEdition: true,
    metadata: { version: '3.0.0', phase: 3, tags: ['milestone', 'community', 'growth'] },
    points: 0
  },

  {
    id: 'collaboration_champion',
    title: 'Collaboration Champion',
    description: 'Participate in special brand collaboration events',
    icon: '🤝',
    category: 'special_event',
    rarity: 'rare',
    phase: 3,
    requirements: [
      { type: 'collaboration_activities', target: 2 },
      { type: 'brand_engagement', target: 5 }
    ],
    rewards: { points: 600, badge: 'collaborator' },
    gamificationTriggers: ['collaboration_activity'],
    isActive: true,
    metadata: { version: '3.0.0', phase: 3, tags: ['collaboration', 'brands', 'partnership'] },
    points: 0
  },

  {
    id: 'contest_champion',
    title: 'Contest Champion',
    description: 'Excel in community contests and competitions',
    icon: '🏆',
    category: 'special_event',
    rarity: 'epic',
    phase: 3,
    requirements: [
      { type: 'contest_participation', target: 3 },
      { type: 'contest_top_performance', target: 1 }
    ],
    rewards: { points: 1000, badge: 'contest_champion' },
    gamificationTriggers: ['special_event_completion'],
    isActive: true,
    metadata: { version: '3.0.0', phase: 3, tags: ['contest', 'competition', 'champion'] },
    points: 0
  },

  {
    id: 'beta_tester_supreme',
    title: 'Beta Tester Supreme',
    description: 'Test new features and provide valuable feedback',
    icon: '🧪',
    category: 'special_event',
    rarity: 'legendary',
    phase: 3,
    requirements: [
      { type: 'beta_features_tested', target: 5 },
      { type: 'valuable_feedback_provided', target: 3 }
    ],
    rewards: { points: 1500, badge: 'beta_supreme' },
    gamificationTriggers: ['special_event_completion'],
    isActive: true,
    limitedEdition: true,
    maxCompletions: 50,
    metadata: { version: '3.0.0', phase: 3, tags: ['beta', 'testing', 'feedback', 'exclusive'] },
    points: 0
  },

  {
    id: 'influencer_spotlight',
    title: 'Influencer Spotlight',
    description: 'Get featured in community spotlight for exceptional contributions',
    icon: '⭐',
    category: 'special_event',
    rarity: 'legendary',
    phase: 3,
    requirements: [{ type: 'community_spotlight_feature', target: 1 }],
    rewards: { points: 2000, badge: 'spotlight_star' },
    gamificationTriggers: ['peer_recognition'],
    isActive: true,
    limitedEdition: true,
    maxCompletions: 12, // Monthly spotlight
    metadata: { version: '3.0.0', phase: 3, tags: ['spotlight', 'influence', 'recognition'] },
    points: 0
  }
]

// ===== DYNAMIC & META ACHIEVEMENTS =====

export const dynamicAchievements: Phase3Achievement[] = [
  {
    id: 'category_master_template',
    title: 'Category Master',
    description: 'Complete all achievements in a specific category',
    icon: '👑',
    category: 'meta_achievement',
    rarity: 'epic',
    phase: 3,
    requirements: [{ type: 'category_completion', target: 100 }], // 100% of category
    rewards: { points: 1200, badge: 'category_master' },
    gamificationTriggers: ['category_mastery'],
    isActive: true,
    isDynamic: true,
    dynamicConfig: {
      template: 'category_master_{category}',
      variables: { category: 'dynamic' },
      generationRules: {
        minDifficulty: 70,
        maxDifficulty: 90,
        targetCompletion: 5, // Only 5% of users expected to complete
        adaptiveDifficulty: false
      },
      personalization: {
        userBehaviorWeight: 0.4,
        interestWeight: 0.6,
        skillLevelWeight: 0.3
      }
    },
    metadata: { version: '3.0.0', phase: 3, tags: ['meta', 'mastery', 'completion'] },
    points: 0
  },

  {
    id: 'achievement_hunter',
    title: 'Achievement Hunter',
    description: 'Complete achievement chains across multiple phases',
    icon: '🎯',
    category: 'meta_achievement',
    rarity: 'legendary',
    phase: 3,
    requirements: [
      { type: 'phase1_chain_completion', target: 3 },
      { type: 'phase2_chain_completion', target: 2 },
      { type: 'phase3_achievement_unlock', target: 1 }
    ],
    rewards: { points: 2500, badge: 'achievement_hunter' },
    gamificationTriggers: ['achievement_chain_completion'],
    isActive: true,
    metadata: { version: '3.0.0', phase: 3, tags: ['meta', 'hunter', 'cross-phase'] },
    points: 0
  },

  {
    id: 'personalized_challenge',
    title: 'Personal Best',
    description: 'Complete an AI-generated personalized challenge',
    icon: '🤖',
    category: 'dynamic_challenge',
    rarity: 'rare',
    phase: 3,
    requirements: [{ type: 'personalized_challenge_completion', target: 1 }],
    rewards: { points: 400 },
    gamificationTriggers: ['personalized_challenge_completion'],
    isActive: true,
    isDynamic: true,
    aiGenerated: true,
    dynamicConfig: {
      template: 'personalized_{activity}_{difficulty}',
      variables: { activity: 'dynamic', difficulty: 'adaptive' },
      generationRules: {
        minDifficulty: 30,
        maxDifficulty: 80,
        targetCompletion: 60,
        adaptiveDifficulty: true
      },
      personalization: {
        userBehaviorWeight: 0.8,
        interestWeight: 0.7,
        skillLevelWeight: 0.9
      }
    },
    metadata: { version: '3.0.0', phase: 3, tags: ['ai', 'personalized', 'adaptive'] },
    points: 0
  },

  {
    id: 'community_creator',
    title: 'Community Creator',
    description: 'Have your achievement idea implemented by the community',
    icon: '💡',
    category: 'community_milestone',
    rarity: 'legendary',
    phase: 3,
    requirements: [
      { type: 'achievement_idea_submitted', target: 1 },
      { type: 'community_votes_received', target: 100 },
      { type: 'achievement_implemented', target: 1 }
    ],
    rewards: { points: 3000, badge: 'creator' },
    gamificationTriggers: ['community_contribution'],
    isActive: true,
    communityGenerated: true,
    limitedEdition: true,
    metadata: { version: '3.0.0', phase: 3, tags: ['creator', 'community', 'innovation'] },
    points: 0
  }
]

// ===== EVOLUTION ACHIEVEMENTS =====

export const evolutionAchievements: Phase3Achievement[] = [
  {
    id: 'raffle_rookie_evolved',
    title: 'Raffle Virtuoso',
    description: 'Evolved from Raffle Rookie - Master of raffle participation',
    icon: '🎭',
    category: 'raffle_entry',
    rarity: 'epic',
    phase: 3,
    requirements: [
      { type: 'total_raffle_entries', target: 100 },
      { type: 'perfect_entries', target: 10 },
      { type: 'social_completion_rate', target: 95 }
    ],
    rewards: { points: 1500, badge: 'virtuoso' },
    gamificationTriggers: ['raffle_entry_submitted'],
    isActive: true,
    canEvolve: false,
    parentAchievement: 'raffle_rookie',
    evolutionRules: [{
      triggerType: 'completion_based',
      triggerCondition: { sourceAchievement: 'raffle_rookie', userEntries: 50 },
      evolution: {
        newTitle: 'Raffle Virtuoso',
        newDescription: 'Evolved from Raffle Rookie - Master of raffle participation',
        newIcon: '🎭',
        newRarity: 'epic'
      },
      preserveProgress: true
    }],
    metadata: { version: '3.0.0', phase: 3, tags: ['evolution', 'mastery', 'raffle'] },
    points: 0
  }
]

// ===== COMBINED PHASE 3 ACHIEVEMENTS =====

export const phase3Achievements: Phase3Achievement[] = [
  ...seasonalAchievements,
  ...specialEventAchievements,
  ...dynamicAchievements,
  ...evolutionAchievements
]

// ===== HELPER FUNCTIONS =====

export function getPhase3AchievementsByCategory(category: Phase3Category): Phase3Achievement[] {
  return phase3Achievements.filter(achievement => achievement.category === category)
}

export function getSeasonalAchievements(season?: string): Phase3Achievement[] {
  return seasonalAchievements.filter(achievement => 
    !season || achievement.metadata?.tags?.includes(season)
  )
}

export function getActiveSeasonalAchievements(): Phase3Achievement[] {
  const now = new Date()
  return seasonalAchievements.filter(achievement => {
    if (!achievement.availabilityWindow) return true
    
    const start = achievement.availabilityWindow.startDate.toDate()
    const end = achievement.availabilityWindow.endDate.toDate()
    
    return now >= start && now <= end
  })
}

export function getSpecialEventAchievements(eventId?: string): Phase3Achievement[] {
  return specialEventAchievements.filter(achievement => 
    !eventId || achievement.specialEvent === eventId
  )
}

export function getDynamicAchievements(): Phase3Achievement[] {
  return dynamicAchievements.filter(achievement => achievement.isDynamic)
}

export function getEvolutionAchievements(): Phase3Achievement[] {
  return evolutionAchievements.filter(achievement => achievement.parentAchievement)
}

export function validatePhase3Achievement(achievement: Phase3Achievement): boolean {
  const requiredFields = ['id', 'title', 'description', 'icon', 'category', 'rarity', 'requirements', 'rewards', 'phase']
  const isValid = requiredFields.every(field => achievement[field as keyof Phase3Achievement] !== undefined) && 
                  achievement.phase === 3

  // Additional validation for Phase 3 features
  if (achievement.isTimeLimited && !achievement.availabilityWindow) {
    return false
  }

  if (achievement.isDynamic && !achievement.dynamicConfig) {
    return false
  }

  if (achievement.canEvolve && !achievement.evolutionRules) {
    return false
  }

  return isValid
}

/**
 * Get Phase 3 achievement statistics
 */
export function getPhase3Stats() {
  return {
    totalAchievements: phase3Achievements.length,
    byCategory: {
      seasonal_holiday: getPhase3AchievementsByCategory('seasonal_holiday').length,
      special_event: getPhase3AchievementsByCategory('special_event').length,
      dynamic_challenge: getPhase3AchievementsByCategory('dynamic_challenge').length,
      meta_achievement: getPhase3AchievementsByCategory('meta_achievement').length,
      community_milestone: getPhase3AchievementsByCategory('community_milestone').length
    },
    byType: {
      seasonal: seasonalAchievements.length,
      specialEvent: specialEventAchievements.length,
      dynamic: dynamicAchievements.length,
      evolution: evolutionAchievements.length
    },
    features: {
      timeLimited: phase3Achievements.filter(a => a.isTimeLimited).length,
      dynamic: phase3Achievements.filter(a => a.isDynamic).length,
      canEvolve: phase3Achievements.filter(a => a.canEvolve).length,
      limitedEdition: phase3Achievements.filter(a => a.limitedEdition).length,
      aiGenerated: phase3Achievements.filter(a => a.aiGenerated).length,
      communityGenerated: phase3Achievements.filter(a => a.communityGenerated).length
    },
    totalPointsAvailable: phase3Achievements.reduce((sum, a) => sum + a.rewards.points, 0),
    averagePointsPerAchievement: Math.round(
      phase3Achievements.reduce((sum, a) => sum + a.rewards.points, 0) / phase3Achievements.length
    )
  }
}

export default phase3Achievements