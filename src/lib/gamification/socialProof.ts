/**
 * Achievement Sharing and Social Proof System
 * 
 * Social features for sharing achievements, building community engagement,
 * and creating social proof around the gamification system.
 * 
 * <AUTHOR> Team - Gamification Phase 2
 * @version 1.0.0
 */

import { serverTimestamp, Timestamp } from 'firebase/firestore'
import type { Achievement } from './phase1-achievements'
import type { Phase2Achievement } from './phase2-achievements'
import type { AchievementUnlockResult } from './achievementTracking'

// ===== TYPES =====

export interface AchievementShare {
  id: string;
  userId: string;
  username: string;
  avatar?: string;
  achievement: Achievement | Phase2Achievement;
  platform: 'twitter' | 'facebook' | 'instagram' | 'discord' | 'linkedin' | 'internal';
  shareContent: {
    text: string;
    hashtags: string[];
    image?: string;
    video?: string;
  };
  engagement: {
    views: number;
    likes: number;
    comments: number;
    reshares: number;
  };
  createdAt: Timestamp;
  metadata: Record<string, any>;
}

export interface SocialProofWidget {
  type: 'recent_achievements' | 'top_performers' | 'milestone_celebration' | 'competition_update';
  title: string;
  content: {
    users: {
      userId: string;
      username: string;
      avatar?: string;
      achievement?: Achievement | Phase2Achievement;
      stat?: number;
      timeAgo: string;
    }[];
    totalCount?: number;
    timeframe?: string;
  };
  location: 'homepage' | 'profile' | 'raffle_page' | 'achievement_page';
  priority: number;
  isActive: boolean;
  refreshInterval: number; // minutes
}

export interface AchievementShowcase {
  userId: string;
  featuredAchievements: string[]; // Achievement IDs
  displayMode: 'grid' | 'carousel' | 'timeline';
  privacy: 'public' | 'friends' | 'private';
  customization: {
    theme: string;
    backgroundColor: string;
    accentColor: string;
    showProgress: boolean;
    showStats: boolean;
  };
  isPublic: boolean;
  lastUpdated: Timestamp;
}

export interface CommunityFeed {
  id: string;
  type: 'achievement_unlock' | 'milestone_reached' | 'competition_win' | 'streak_achievement';
  userId: string;
  username: string;
  avatar?: string;
  content: {
    title: string;
    description: string;
    achievement?: Achievement | Phase2Achievement;
    stats?: Record<string, number>;
    media?: {
      type: 'image' | 'gif' | 'video';
      url: string;
    };
  };
  reactions: {
    type: 'like' | 'celebrate' | 'fire' | 'clap';
    userId: string;
    timestamp: Timestamp;
  }[];
  comments: {
    id: string;
    userId: string;
    username: string;
    message: string;
    timestamp: Timestamp;
  }[];
  visibility: 'public' | 'friends' | 'community';
  createdAt: Timestamp;
}

export interface InfluencerProgram {
  userId: string;
  tier: 'ambassador' | 'advocate' | 'champion' | 'legend';
  requirements: {
    minAchievements: number;
    minFollowers: number;
    minEngagementRate: number;
  };
  benefits: {
    exclusiveAchievements: string[];
    earlyAccess: boolean;
    customBadges: boolean;
    amplificationBoost: number;
  };
  stats: {
    sharesGenerated: number;
    clicksGenerated: number;
    conversionsGenerated: number;
    totalReach: number;
  };
  isActive: boolean;
  joinedAt: Timestamp;
}

// ===== SOCIAL PROOF MANAGER =====

export class SocialProofManager {
  private static shares: Map<string, AchievementShare> = new Map()
  private static widgets: SocialProofWidget[] = []
  private static communityFeed: CommunityFeed[] = []
  private static showcases: Map<string, AchievementShowcase> = new Map()

  /**
   * Initialize social proof system
   */
  static initialize(): void {
    console.log('Initializing Social Proof Manager...')
    
    this.setupDefaultWidgets()
    this.startFeedRefresh()
    
    console.log('Social Proof Manager initialized')
  }

  /**
   * Create achievement share
   */
  static async createAchievementShare(
    userId: string,
    achievement: Achievement | Phase2Achievement,
    platform: AchievementShare['platform'],
    customMessage?: string
  ): Promise<AchievementShare> {
    try {
      const username = await this.getUsername(userId)
      const avatar = await this.getUserAvatar(userId)
      
      const shareContent = this.generateShareContent(achievement, platform, customMessage)
      
      const share: AchievementShare = {
        id: `share_${achievement.id}_${Date.now()}`,
        userId,
        username,
        avatar,
        achievement,
        platform,
        shareContent,
        engagement: {
          views: 0,
          likes: 0,
          comments: 0,
          reshares: 0
        },
        createdAt: serverTimestamp() as Timestamp,
        metadata: {
          userStats: await this.getUserStats(userId),
          achievementRarity: achievement.rarity,
          platformSpecific: this.getPlatformSpecificData(platform)
        }
      }

      // Store the share
      this.shares.set(share.id, share)
      
      // Add to community feed if appropriate
      if (this.shouldAddToFeed(achievement)) {
        await this.addToCommunityFeed(share)
      }
      
      // Track for analytics
      await this.trackShare(share)
      
      console.log(`Achievement share created: ${achievement.title} on ${platform}`)
      return share
      
    } catch (error) {
      console.error('Error creating achievement share:', error)
      throw error
    }
  }

  /**
   * Generate share content for different platforms
   */
  private static generateShareContent(
    achievement: Achievement | Phase2Achievement,
    platform: string,
    customMessage?: string
  ): AchievementShare['shareContent'] {
    const baseMessage = customMessage || `Just unlocked "${achievement.title}" on Syndicaps! ${achievement.icon}`
    
    const platformContent = {
      twitter: {
        text: `${baseMessage}\n\n🎮 Join the community: syndicaps.com\n\n#Syndicaps #Achievement #Keycaps #Gaming`,
        hashtags: ['Syndicaps', 'Achievement', 'Keycaps', achievement.rarity],
        characterLimit: 280
      },
      facebook: {
        text: `${baseMessage}\n\nI'm part of the Syndicaps community where every raffle entry and interaction earns achievements and points! Join me for awesome keycap collections and epic rewards.\n\n🏆 ${achievement.description}`,
        hashtags: ['Syndicaps', 'KeycapCommunity', 'Achievement'],
        characterLimit: 2000
      },
      instagram: {
        text: `${baseMessage}\n\n✨ Living my best keycap life\n🎯 Achievement unlocked\n🚀 Next level gaming setup\n\n#Syndicaps #Keycaps #Achievement #${achievement.rarity}Achievement #GamingSetup`,
        hashtags: ['Syndicaps', 'Keycaps', 'Achievement', 'GamingSetup', 'MechanicalKeyboards'],
        characterLimit: 2200
      },
      discord: {
        text: `🎉 ${baseMessage}\n\n**${achievement.title}**\n*${achievement.description}*\n\nPoints earned: **${achievement.rewards.points}** 💎\nRarity: **${achievement.rarity}** ⭐\n\nWho else is crushing it on Syndicaps? 🔥`,
        hashtags: [],
        characterLimit: 2000
      },
      linkedin: {
        text: `Professional achievement unlocked! 🏆\n\nI've been exploring the intersection of community engagement and digital rewards through Syndicaps, and just achieved "${achievement.title}".\n\nThe platform demonstrates excellent gamification principles:\n✅ Clear progression systems\n✅ Meaningful rewards\n✅ Community building\n✅ Social recognition\n\n${achievement.description}\n\n#CommunityEngagement #Gamification #UserExperience #DigitalCommunity`,
        hashtags: ['CommunityEngagement', 'Gamification', 'UserExperience'],
        characterLimit: 3000
      },
      internal: {
        text: baseMessage,
        hashtags: [achievement.category, achievement.rarity],
        characterLimit: 500
      }
    }

    const content = platformContent[platform as keyof typeof platformContent] || platformContent.internal
    
    return {
      text: content.text.slice(0, content.characterLimit),
      hashtags: content.hashtags,
      image: this.generateAchievementImage(achievement),
      video: this.shouldGenerateVideo(achievement) ? this.generateAchievementVideo(achievement) : undefined
    }
  }

  /**
   * Generate achievement showcase
   */
  static async createAchievementShowcase(
    userId: string,
    featuredAchievements: string[],
    customization?: Partial<AchievementShowcase['customization']>
  ): Promise<AchievementShowcase> {
    try {
      const showcase: AchievementShowcase = {
        userId,
        featuredAchievements: featuredAchievements.slice(0, 9), // Max 9 featured
        displayMode: 'grid',
        privacy: 'public',
        customization: {
          theme: 'dark',
          backgroundColor: '#1a1a1a',
          accentColor: '#6366f1',
          showProgress: true,
          showStats: true,
          ...customization
        },
        isPublic: true,
        lastUpdated: serverTimestamp() as Timestamp
      }

      this.showcases.set(userId, showcase)
      
      console.log(`Achievement showcase created for user ${userId}`)
      return showcase
      
    } catch (error) {
      console.error('Error creating achievement showcase:', error)
      throw error
    }
  }

  /**
   * Add to community feed
   */
  private static async addToCommunityFeed(share: AchievementShare): Promise<void> {
    try {
      const feedItem: CommunityFeed = {
        id: `feed_${Date.now()}`,
        type: 'achievement_unlock',
        userId: share.userId,
        username: share.username,
        avatar: share.avatar,
        content: {
          title: `${share.username} unlocked ${share.achievement.title}!`,
          description: share.achievement.description,
          achievement: share.achievement,
          stats: {
            points: share.achievement.rewards.points,
            rarity: ['common', 'uncommon', 'rare', 'epic', 'legendary'].indexOf(share.achievement.rarity)
          },
          media: share.shareContent.image ? {
            type: 'image',
            url: share.shareContent.image
          } : undefined
        },
        reactions: [],
        comments: [],
        visibility: 'public',
        createdAt: serverTimestamp() as Timestamp
      }

      this.communityFeed.unshift(feedItem)
      
      // Keep feed to reasonable size
      if (this.communityFeed.length > 1000) {
        this.communityFeed.splice(1000)
      }

      // Broadcast to community
      this.broadcastFeedUpdate(feedItem)
      
    } catch (error) {
      console.error('Error adding to community feed:', error)
    }
  }

  /**
   * Get social proof widgets
   */
  static getSocialProofWidgets(location: string): SocialProofWidget[] {
    return this.widgets
      .filter(widget => widget.location === location && widget.isActive)
      .sort((a, b) => b.priority - a.priority)
  }

  /**
   * Get community feed
   */
  static getCommunityFeed(limit: number = 50): CommunityFeed[] {
    return this.communityFeed.slice(0, limit)
  }

  /**
   * Get user's achievement showcase
   */
  static getUserShowcase(userId: string): AchievementShowcase | null {
    return this.showcases.get(userId) || null
  }

  /**
   * Generate social proof statistics
   */
  static async generateSocialProofStats(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<{
    totalShares: number;
    platformBreakdown: Record<string, number>;
    topAchievements: { achievement: Achievement | Phase2Achievement; shares: number }[];
    engagementRate: number;
    reachEstimate: number;
  }> {
    try {
      const shares = Array.from(this.shares.values())
      
      // Filter by timeframe
      const cutoff = new Date()
      switch (timeframe) {
        case 'day':
          cutoff.setDate(cutoff.getDate() - 1)
          break
        case 'week':
          cutoff.setDate(cutoff.getDate() - 7)
          break
        case 'month':
          cutoff.setMonth(cutoff.getMonth() - 1)
          break
      }
      
      const filteredShares = shares.filter(share => 
        share.createdAt.toDate() >= cutoff
      )

      // Platform breakdown
      const platformBreakdown: Record<string, number> = {}
      filteredShares.forEach(share => {
        platformBreakdown[share.platform] = (platformBreakdown[share.platform] || 0) + 1
      })

      // Top achievements
      const achievementCounts: Map<string, number> = new Map()
      filteredShares.forEach(share => {
        const key = share.achievement.id
        achievementCounts.set(key, (achievementCounts.get(key) || 0) + 1)
      })

      const topAchievements = Array.from(achievementCounts.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([achievementId, shares]) => {
          const share = filteredShares.find(s => s.achievement.id === achievementId)!
          return { achievement: share.achievement, shares }
        })

      // Engagement metrics
      const totalEngagement = filteredShares.reduce((sum, share) => 
        sum + share.engagement.likes + share.engagement.comments + share.engagement.reshares, 0
      )
      const engagementRate = filteredShares.length > 0 ? (totalEngagement / filteredShares.length) : 0

      // Reach estimate (simplified)
      const reachEstimate = filteredShares.reduce((sum, share) => 
        sum + share.engagement.views + (share.engagement.reshares * 50), 0
      )

      return {
        totalShares: filteredShares.length,
        platformBreakdown,
        topAchievements,
        engagementRate,
        reachEstimate
      }
      
    } catch (error) {
      console.error('Error generating social proof stats:', error)
      return {
        totalShares: 0,
        platformBreakdown: {},
        topAchievements: [],
        engagementRate: 0,
        reachEstimate: 0
      }
    }
  }

  /**
   * Track share engagement
   */
  static async trackShareEngagement(
    shareId: string,
    type: 'view' | 'like' | 'comment' | 'reshare',
    count: number = 1
  ): Promise<void> {
    try {
      const share = this.shares.get(shareId)
      if (!share) return

      switch (type) {
        case 'view':
          share.engagement.views += count
          break
        case 'like':
          share.engagement.likes += count
          break
        case 'comment':
          share.engagement.comments += count
          break
        case 'reshare':
          share.engagement.reshares += count
          break
      }

      // Update analytics
      await this.updateShareAnalytics(shareId, type, count)
      
    } catch (error) {
      console.error('Error tracking share engagement:', error)
    }
  }

  // ===== HELPER METHODS =====

  private static setupDefaultWidgets(): void {
    this.widgets = [
      {
        type: 'recent_achievements',
        title: 'Latest Achievements',
        content: { users: [], totalCount: 0 },
        location: 'homepage',
        priority: 10,
        isActive: true,
        refreshInterval: 5
      },
      {
        type: 'top_performers',
        title: 'Top Performers This Week',
        content: { users: [], timeframe: 'week' },
        location: 'homepage',
        priority: 8,
        isActive: true,
        refreshInterval: 60
      },
      {
        type: 'milestone_celebration',
        title: 'Community Milestones',
        content: { users: [], totalCount: 0 },
        location: 'achievement_page',
        priority: 9,
        isActive: true,
        refreshInterval: 30
      }
    ]
  }

  private static startFeedRefresh(): void {
    // Refresh community feed widgets periodically
    setInterval(() => {
      this.refreshSocialProofWidgets()
    }, 5 * 60 * 1000) // Every 5 minutes
  }

  private static async refreshSocialProofWidgets(): Promise<void> {
    try {
      // Update widget content with fresh data
      for (const widget of this.widgets) {
        if (widget.isActive) {
          await this.updateWidgetContent(widget)
        }
      }
    } catch (error) {
      console.error('Error refreshing social proof widgets:', error)
    }
  }

  private static async updateWidgetContent(widget: SocialProofWidget): Promise<void> {
    // Implementation would fetch real data based on widget type
    console.log(`Updating widget: ${widget.title}`)
  }

  private static shouldAddToFeed(achievement: Achievement | Phase2Achievement): boolean {
    // Only add rare+ achievements to community feed
    return ['rare', 'epic', 'legendary'].includes(achievement.rarity)
  }

  private static generateAchievementImage(achievement: Achievement | Phase2Achievement): string {
    // Generate or return URL for achievement share image
    return `/api/achievement-images/${achievement.id}`
  }

  private static shouldGenerateVideo(achievement: Achievement | Phase2Achievement): boolean {
    // Generate videos for epic+ achievements
    return ['epic', 'legendary'].includes(achievement.rarity)
  }

  private static generateAchievementVideo(achievement: Achievement | Phase2Achievement): string {
    // Generate or return URL for achievement celebration video
    return `/api/achievement-videos/${achievement.id}`
  }

  private static getPlatformSpecificData(platform: string): Record<string, any> {
    return {
      platform,
      optimalPostTime: new Date().toISOString(),
      suggestedHashtags: this.getSuggestedHashtags(platform)
    }
  }

  private static getSuggestedHashtags(platform: string): string[] {
    const platformHashtags = {
      twitter: ['#Syndicaps', '#Achievement', '#Keycaps'],
      instagram: ['#Syndicaps', '#Keycaps', '#GamingSetup'],
      facebook: ['Syndicaps', 'KeycapCommunity'],
      discord: [],
      linkedin: ['CommunityEngagement', 'Gamification']
    }
    
    return platformHashtags[platform as keyof typeof platformHashtags] || []
  }

  private static broadcastFeedUpdate(feedItem: CommunityFeed): void {
    const event = new CustomEvent('community-feed-update', {
      detail: feedItem
    })

    if (typeof window !== 'undefined') {
      window.dispatchEvent(event)
    }
  }

  private static async trackShare(share: AchievementShare): Promise<void> {
    console.log(`Tracking share: ${share.achievement.title} on ${share.platform}`)
  }

  private static async updateShareAnalytics(shareId: string, type: string, count: number): Promise<void> {
    console.log(`Share analytics: ${shareId} - ${type}: ${count}`)
  }

  private static async getUsername(userId: string): Promise<string> {
    // Fetch username from user data
    return 'User'
  }

  private static async getUserAvatar(userId: string): Promise<string | undefined> {
    // Fetch user avatar
    return undefined
  }

  private static async getUserStats(userId: string): Promise<Record<string, any>> {
    // Fetch user statistics
    return {}
  }

  /**
   * Get social proof statistics
   */
  static getStats() {
    return {
      totalShares: this.shares.size,
      totalShowcases: this.showcases.size,
      activeFeedItems: this.communityFeed.length,
      activeWidgets: this.widgets.filter(w => w.isActive).length
    }
  }
}

export default SocialProofManager