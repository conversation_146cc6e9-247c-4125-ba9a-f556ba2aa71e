/**
 * Advanced Image Optimization System
 * 
 * Comprehensive image optimization with WebP/AVIF conversion,
 * responsive images, progressive loading, and performance monitoring.
 * 
 * Phase 3 Advanced Optimizations - Community System Audit
 * 
 * <AUTHOR> Team
 */

interface ImageOptimizationConfig {
  formats: {
    webp: boolean
    avif: boolean
    fallback: 'jpeg' | 'png'
  }
  quality: {
    high: number
    medium: number
    low: number
  }
  sizes: {
    thumbnail: { width: number; height: number }
    small: { width: number; height: number }
    medium: { width: number; height: number }
    large: { width: number; height: number }
    xlarge: { width: number; height: number }
  }
  progressive: boolean
  lazyLoading: boolean
  placeholder: {
    enabled: boolean
    blur: boolean
    color: string
  }
}

interface OptimizedImageSet {
  original: string
  webp?: string
  avif?: string
  sizes: {
    thumbnail: string
    small: string
    medium: string
    large: string
    xlarge: string
  }
  placeholder?: string
  metadata: {
    width: number
    height: number
    aspectRatio: number
    format: string
    size: number
  }
}

export class AdvancedImageOptimizer {
  private static config: ImageOptimizationConfig = {
    formats: {
      webp: true,
      avif: true,
      fallback: 'jpeg'
    },
    quality: {
      high: 90,
      medium: 80,
      low: 70
    },
    sizes: {
      thumbnail: { width: 150, height: 150 },
      small: { width: 300, height: 300 },
      medium: { width: 600, height: 600 },
      large: { width: 1200, height: 1200 },
      xlarge: { width: 1920, height: 1920 }
    },
    progressive: true,
    lazyLoading: true,
    placeholder: {
      enabled: true,
      blur: true,
      color: '#f3f4f6'
    }
  }

  /**
   * Check browser support for modern image formats
   */
  static checkFormatSupport(): {
    webp: boolean
    avif: boolean
  } {
    if (typeof window === 'undefined') {
      return { webp: false, avif: false }
    }

    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1

    return {
      webp: canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0,
      avif: canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
    }
  }

  /**
   * Generate optimized image URLs for different formats and sizes
   */
  static generateOptimizedImageSet(
    originalUrl: string,
    options: {
      quality?: 'high' | 'medium' | 'low'
      includeAllSizes?: boolean
      customSizes?: Array<{ width: number; height: number; name: string }>
    } = {}
  ): OptimizedImageSet {
    const { quality = 'medium', includeAllSizes = true, customSizes } = options
    const qualityValue = this.config.quality[quality]

    // For external URLs (like Unsplash), use URL parameters
    if (this.isExternalUrl(originalUrl)) {
      return this.generateExternalOptimizedSet(originalUrl, qualityValue, includeAllSizes)
    }

    // For internal images, use Next.js Image optimization
    return this.generateInternalOptimizedSet(originalUrl, qualityValue, includeAllSizes, customSizes)
  }

  /**
   * Generate responsive image srcSet
   */
  static generateSrcSet(imageSet: OptimizedImageSet, format: 'webp' | 'avif' | 'original' = 'original'): string {
    const sizes = imageSet.sizes
    const baseUrl = format === 'webp' ? imageSet.webp : 
                   format === 'avif' ? imageSet.avif : 
                   imageSet.original

    if (!baseUrl) return ''

    const srcSetEntries = Object.entries(sizes).map(([sizeName, url]) => {
      const sizeConfig = this.config.sizes[sizeName as keyof typeof this.config.sizes]
      return `${url} ${sizeConfig.width}w`
    })

    return srcSetEntries.join(', ')
  }

  /**
   * Generate sizes attribute for responsive images
   */
  static generateSizesAttribute(breakpoints?: {
    mobile?: string
    tablet?: string
    desktop?: string
    wide?: string
  }): string {
    const defaultBreakpoints = {
      mobile: '(max-width: 640px) 100vw',
      tablet: '(max-width: 768px) 50vw',
      desktop: '(max-width: 1024px) 33vw',
      wide: '25vw'
    }

    const sizes = { ...defaultBreakpoints, ...breakpoints }
    return Object.values(sizes).join(', ')
  }

  /**
   * Create progressive loading placeholder
   */
  static async generatePlaceholder(
    imageUrl: string,
    options: {
      width?: number
      height?: number
      blur?: boolean
      quality?: number
    } = {}
  ): Promise<string> {
    const { width = 20, height = 20, blur = true, quality = 10 } = options

    try {
      // For external URLs, create a low-quality version
      if (this.isExternalUrl(imageUrl)) {
        const url = new URL(imageUrl)
        url.searchParams.set('w', width.toString())
        url.searchParams.set('h', height.toString())
        url.searchParams.set('q', quality.toString())
        if (blur) {
          url.searchParams.set('blur', '5')
        }
        return url.toString()
      }

      // For internal images, use Next.js optimization
      return `/_next/image?url=${encodeURIComponent(imageUrl)}&w=${width}&h=${height}&q=${quality}`
    } catch (error) {
      console.warn('Failed to generate placeholder:', error)
      return this.generateColorPlaceholder(width, height)
    }
  }

  /**
   * Generate color-based placeholder
   */
  static generateColorPlaceholder(width: number, height: number, color: string = '#f3f4f6'): string {
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${color}"/>
      </svg>
    `
    return `data:image/svg+xml;base64,${btoa(svg)}`
  }

  /**
   * Preload critical images
   */
  static preloadCriticalImages(images: string[], priority: 'high' | 'low' = 'high'): void {
    if (typeof window === 'undefined') return

    const formatSupport = this.checkFormatSupport()

    images.forEach(imageUrl => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      
      // Use best supported format
      if (formatSupport.avif && this.config.formats.avif) {
        link.href = this.convertToFormat(imageUrl, 'avif')
      } else if (formatSupport.webp && this.config.formats.webp) {
        link.href = this.convertToFormat(imageUrl, 'webp')
      } else {
        link.href = imageUrl
      }

      if (priority === 'high') {
        link.fetchPriority = 'high'
      }

      document.head.appendChild(link)
    })
  }

  /**
   * Monitor image loading performance
   */
  static monitorImagePerformance(): {
    startMonitoring: () => void
    getMetrics: () => ImagePerformanceMetrics
  } {
    const metrics: ImagePerformanceMetrics = {
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      largestImage: 0,
      totalBytes: 0,
      formatUsage: {
        webp: 0,
        avif: 0,
        jpeg: 0,
        png: 0,
        other: 0
      }
    }

    const loadTimes: number[] = []

    const startMonitoring = () => {
      if (typeof window === 'undefined') return

      // Monitor existing images
      const images = document.querySelectorAll('img')
      metrics.totalImages = images.length

      images.forEach(img => {
        const startTime = performance.now()

        img.addEventListener('load', () => {
          const loadTime = performance.now() - startTime
          loadTimes.push(loadTime)
          metrics.loadedImages++
          metrics.averageLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length

          // Track format usage
          const format = this.getImageFormat(img.src)
          if (format in metrics.formatUsage) {
            metrics.formatUsage[format as keyof typeof metrics.formatUsage]++
          }

          // Track size if available
          if (img.naturalWidth && img.naturalHeight) {
            const size = img.naturalWidth * img.naturalHeight
            if (size > metrics.largestImage) {
              metrics.largestImage = size
            }
          }
        })

        img.addEventListener('error', () => {
          metrics.failedImages++
        })
      })

      // Monitor new images added dynamically
      const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              const newImages = element.tagName === 'IMG' ? [element] : element.querySelectorAll('img')
              metrics.totalImages += newImages.length
            }
          })
        })
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })
    }

    const getMetrics = () => ({ ...metrics })

    return { startMonitoring, getMetrics }
  }

  /**
   * Optimize images in community content
   */
  static optimizeCommunityImages(content: string): string {
    // Replace image URLs in markdown/HTML content with optimized versions
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)|<img[^>]+src="([^"]+)"[^>]*>/g
    
    return content.replace(imageRegex, (match, altText, markdownUrl, htmlUrl) => {
      const imageUrl = markdownUrl || htmlUrl
      const optimizedSet = this.generateOptimizedImageSet(imageUrl)
      
      if (markdownUrl) {
        // Markdown format - convert to HTML with responsive image
        return this.generateResponsiveImageHTML(optimizedSet, altText || 'Community image')
      } else {
        // HTML format - enhance existing img tag
        return this.enhanceImageTag(match, optimizedSet)
      }
    })
  }

  // Private helper methods

  private static isExternalUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname !== window.location.hostname
    } catch {
      return false
    }
  }

  private static generateExternalOptimizedSet(
    url: string,
    quality: number,
    includeAllSizes: boolean
  ): OptimizedImageSet {
    const urlObj = new URL(url)
    const sizes: any = {}

    if (includeAllSizes) {
      Object.entries(this.config.sizes).forEach(([sizeName, sizeConfig]) => {
        const sizeUrl = new URL(url)
        sizeUrl.searchParams.set('w', sizeConfig.width.toString())
        sizeUrl.searchParams.set('h', sizeConfig.height.toString())
        sizeUrl.searchParams.set('q', quality.toString())
        sizes[sizeName] = sizeUrl.toString()
      })
    }

    // Generate WebP version
    let webpUrl: string | undefined
    if (this.config.formats.webp) {
      const webpUrlObj = new URL(url)
      webpUrlObj.searchParams.set('fm', 'webp')
      webpUrlObj.searchParams.set('q', quality.toString())
      webpUrl = webpUrlObj.toString()
    }

    // Generate AVIF version
    let avifUrl: string | undefined
    if (this.config.formats.avif) {
      const avifUrlObj = new URL(url)
      avifUrlObj.searchParams.set('fm', 'avif')
      avifUrlObj.searchParams.set('q', quality.toString())
      avifUrl = avifUrlObj.toString()
    }

    return {
      original: url,
      webp: webpUrl,
      avif: avifUrl,
      sizes,
      metadata: {
        width: 0, // Would need to fetch to determine
        height: 0,
        aspectRatio: 1,
        format: 'unknown',
        size: 0
      }
    }
  }

  private static generateInternalOptimizedSet(
    url: string,
    quality: number,
    includeAllSizes: boolean,
    customSizes?: Array<{ width: number; height: number; name: string }>
  ): OptimizedImageSet {
    const sizes: any = {}
    const sizesToProcess = customSizes || Object.entries(this.config.sizes).map(([name, config]) => ({
      name,
      width: config.width,
      height: config.height
    }))

    sizesToProcess.forEach(({ name, width, height }) => {
      sizes[name] = `/_next/image?url=${encodeURIComponent(url)}&w=${width}&h=${height}&q=${quality}`
    })

    return {
      original: url,
      webp: `/_next/image?url=${encodeURIComponent(url)}&q=${quality}&fm=webp`,
      avif: `/_next/image?url=${encodeURIComponent(url)}&q=${quality}&fm=avif`,
      sizes,
      metadata: {
        width: 0,
        height: 0,
        aspectRatio: 1,
        format: 'unknown',
        size: 0
      }
    }
  }

  private static convertToFormat(url: string, format: 'webp' | 'avif'): string {
    if (this.isExternalUrl(url)) {
      const urlObj = new URL(url)
      urlObj.searchParams.set('fm', format)
      return urlObj.toString()
    }
    
    return `/_next/image?url=${encodeURIComponent(url)}&fm=${format}`
  }

  private static getImageFormat(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'webp': return 'webp'
      case 'avif': return 'avif'
      case 'jpg':
      case 'jpeg': return 'jpeg'
      case 'png': return 'png'
      default: return 'other'
    }
  }

  private static generateResponsiveImageHTML(imageSet: OptimizedImageSet, altText: string): string {
    const formatSupport = this.checkFormatSupport()
    
    return `
      <picture>
        ${formatSupport.avif && imageSet.avif ? `
          <source srcset="${this.generateSrcSet(imageSet, 'avif')}" type="image/avif">
        ` : ''}
        ${formatSupport.webp && imageSet.webp ? `
          <source srcset="${this.generateSrcSet(imageSet, 'webp')}" type="image/webp">
        ` : ''}
        <img 
          src="${imageSet.original}" 
          srcset="${this.generateSrcSet(imageSet, 'original')}"
          sizes="${this.generateSizesAttribute()}"
          alt="${altText}"
          loading="lazy"
          decoding="async"
        >
      </picture>
    `.trim()
  }

  private static enhanceImageTag(originalTag: string, imageSet: OptimizedImageSet): string {
    // This would parse and enhance the existing img tag
    // For now, return the original tag
    return originalTag
  }
}

interface ImagePerformanceMetrics {
  totalImages: number
  loadedImages: number
  failedImages: number
  averageLoadTime: number
  largestImage: number
  totalBytes: number
  formatUsage: {
    webp: number
    avif: number
    jpeg: number
    png: number
    other: number
  }
}

export default AdvancedImageOptimizer
