/**
 * User Level System Configuration
 * 
 * Comprehensive level progression system for Syndicaps with keycap-themed naming,
 * XP mechanics, and level-based rewards. Integrates with existing tier system.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Timestamp } from 'firebase/firestore'

// ===== TYPES AND INTERFACES =====

export type LevelTier = 'novice' | 'intermediate' | 'advanced' | 'expert'

export interface LevelDefinition {
  level: number
  name: string
  tier: LevelTier
  xpRequired: number
  xpToNext: number
  rewards: LevelReward[]
}

export interface LevelReward {
  id: string
  type: 'badge' | 'points' | 'discount' | 'keycap' | 'access' | 'title'
  name: string
  description: string
  value: any
  isExclusive: boolean
  expiresAt?: Timestamp
}

export interface UserLevel {
  id: string
  userId: string
  currentLevel: number
  currentXP: number
  totalXP: number
  levelName: string
  levelTier: LevelTier
  nextLevelXP: number
  progressToNext: number // percentage 0-100
  levelUpAt?: Timestamp
  milestoneRewards: string[] // claimed reward IDs
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface XPTransaction {
  id: string
  userId: string
  amount: number
  source: 'purchase' | 'activity' | 'bonus' | 'event' | 'manual'
  sourceId?: string // order ID, activity ID, etc.
  multiplier: number
  description: string
  metadata: Record<string, any>
  createdAt: Timestamp
}

// ===== LEVEL DEFINITIONS =====

export const LEVEL_DEFINITIONS: LevelDefinition[] = [
  // Novice Tier (Levels 1-10)
  { level: 1, name: "Switch Novice", tier: 'novice', xpRequired: 0, xpToNext: 100, rewards: [] },
  { level: 2, name: "Key Curious", tier: 'novice', xpRequired: 100, xpToNext: 150, rewards: [] },
  { level: 3, name: "Cap Collector", tier: 'novice', xpRequired: 250, xpToNext: 200, rewards: [] },
  { level: 4, name: "Switch Seeker", tier: 'novice', xpRequired: 450, xpToNext: 250, rewards: [] },
  { level: 5, name: "Profile Builder", tier: 'novice', xpRequired: 700, xpToNext: 300, rewards: [] },
  { level: 6, name: "Community Member", tier: 'novice', xpRequired: 1000, xpToNext: 350, rewards: [] },
  { level: 7, name: "Design Appreciator", tier: 'novice', xpRequired: 1350, xpToNext: 400, rewards: [] },
  { level: 8, name: "Layout Explorer", tier: 'novice', xpRequired: 1750, xpToNext: 450, rewards: [] },
  { level: 9, name: "Switch Sampler", tier: 'novice', xpRequired: 2200, xpToNext: 500, rewards: [] },
  { level: 10, name: "Keycap Enthusiast", tier: 'novice', xpRequired: 2700, xpToNext: 550, rewards: [] },

  // Intermediate Tier (Levels 11-25)
  { level: 11, name: "Tactile Tactician", tier: 'intermediate', xpRequired: 3250, xpToNext: 600, rewards: [] },
  { level: 12, name: "Linear Lover", tier: 'intermediate', xpRequired: 3850, xpToNext: 650, rewards: [] },
  { level: 13, name: "Clicky Connoisseur", tier: 'intermediate', xpRequired: 4500, xpToNext: 700, rewards: [] },
  { level: 14, name: "Profile Pioneer", tier: 'intermediate', xpRequired: 5200, xpToNext: 750, rewards: [] },
  { level: 15, name: "Artisan Admirer", tier: 'intermediate', xpRequired: 5950, xpToNext: 800, rewards: [] },
  { level: 16, name: "Set Strategist", tier: 'intermediate', xpRequired: 6750, xpToNext: 850, rewards: [] },
  { level: 17, name: "Color Coordinator", tier: 'intermediate', xpRequired: 7600, xpToNext: 900, rewards: [] },
  { level: 18, name: "Theme Theorist", tier: 'intermediate', xpRequired: 8500, xpToNext: 950, rewards: [] },
  { level: 19, name: "Design Devotee", tier: 'intermediate', xpRequired: 9450, xpToNext: 1000, rewards: [] },
  { level: 20, name: "Community Contributor", tier: 'intermediate', xpRequired: 10450, xpToNext: 1050, rewards: [] },
  { level: 21, name: "Challenge Champion", tier: 'intermediate', xpRequired: 11500, xpToNext: 1100, rewards: [] },
  { level: 22, name: "Social Sharer", tier: 'intermediate', xpRequired: 12600, xpToNext: 1150, rewards: [] },
  { level: 23, name: "Review Specialist", tier: 'intermediate', xpRequired: 13750, xpToNext: 1200, rewards: [] },
  { level: 24, name: "Trend Tracker", tier: 'intermediate', xpRequired: 14950, xpToNext: 1250, rewards: [] },
  { level: 25, name: "Keycap Curator", tier: 'intermediate', xpRequired: 16200, xpToNext: 1300, rewards: [] },

  // Advanced Tier (Levels 26-40)
  { level: 26, name: "Artisan Architect", tier: 'advanced', xpRequired: 17500, xpToNext: 1350, rewards: [] },
  { level: 27, name: "Profile Perfectionist", tier: 'advanced', xpRequired: 18850, xpToNext: 1400, rewards: [] },
  { level: 28, name: "Set Sommelier", tier: 'advanced', xpRequired: 20250, xpToNext: 1450, rewards: [] },
  { level: 29, name: "Design Director", tier: 'advanced', xpRequired: 21700, xpToNext: 1500, rewards: [] },
  { level: 30, name: "Community Captain", tier: 'advanced', xpRequired: 23200, xpToNext: 1550, rewards: [] },
  { level: 31, name: "Keycap Kingpin", tier: 'advanced', xpRequired: 24750, xpToNext: 1600, rewards: [] },
  { level: 32, name: "Switch Sage", tier: 'advanced', xpRequired: 26350, xpToNext: 1650, rewards: [] },
  { level: 33, name: "Layout Legend", tier: 'advanced', xpRequired: 28000, xpToNext: 1700, rewards: [] },
  { level: 34, name: "Artisan Authority", tier: 'advanced', xpRequired: 29700, xpToNext: 1750, rewards: [] },
  { level: 35, name: "Design Deity", tier: 'advanced', xpRequired: 31450, xpToNext: 1800, rewards: [] },
  { level: 36, name: "Profile Prophet", tier: 'advanced', xpRequired: 33250, xpToNext: 1850, rewards: [] },
  { level: 37, name: "Set Sovereign", tier: 'advanced', xpRequired: 35100, xpToNext: 1900, rewards: [] },
  { level: 38, name: "Keycap Kaiser", tier: 'advanced', xpRequired: 37000, xpToNext: 1950, rewards: [] },
  { level: 39, name: "Switch Supreme", tier: 'advanced', xpRequired: 38950, xpToNext: 2000, rewards: [] },
  { level: 40, name: "Mechanical Master", tier: 'advanced', xpRequired: 40950, xpToNext: 2050, rewards: [] },

  // Expert Tier (Levels 41-50)
  { level: 41, name: "Artisan Emperor", tier: 'expert', xpRequired: 43000, xpToNext: 2100, rewards: [] },
  { level: 42, name: "Design Demigod", tier: 'expert', xpRequired: 45100, xpToNext: 2150, rewards: [] },
  { level: 43, name: "Keycap Overlord", tier: 'expert', xpRequired: 47250, xpToNext: 2200, rewards: [] },
  { level: 44, name: "Switch Virtuoso", tier: 'expert', xpRequired: 49450, xpToNext: 2250, rewards: [] },
  { level: 45, name: "Layout Luminary", tier: 'expert', xpRequired: 51700, xpToNext: 2300, rewards: [] },
  { level: 46, name: "Profile Paragon", tier: 'expert', xpRequired: 54000, xpToNext: 2350, rewards: [] },
  { level: 47, name: "Mechanical Monarch", tier: 'expert', xpRequired: 56350, xpToNext: 2400, rewards: [] },
  { level: 48, name: "Keycap Cosmos", tier: 'expert', xpRequired: 58750, xpToNext: 2450, rewards: [] },
  { level: 49, name: "Switch Singularity", tier: 'expert', xpRequired: 61200, xpToNext: 2500, rewards: [] },
  { level: 50, name: "Syndicaps Legend", tier: 'expert', xpRequired: 63700, xpToNext: 0, rewards: [] },
]

// ===== XP EARNING CONFIGURATION =====

export const XP_SOURCES = {
  // Purchase-based XP
  PURCHASE_BASE: 2, // 2 XP per $1 spent
  LARGE_ORDER_BONUS: 0.15, // 15% bonus for orders $300+
  LARGE_ORDER_THRESHOLD: 300,

  // Activity-based XP
  DAILY_LOGIN: 10,
  PROFILE_COMPLETION: 50,
  PRODUCT_REVIEW: 25,
  PRODUCT_REVIEW_WITH_MEDIA: 40, // 25 + 15 bonus
  COMMUNITY_POST: 20,
  CHALLENGE_PARTICIPATION: 75,
  CHALLENGE_COMPLETION: 150,
  SOCIAL_SHARE: 15,
  REFERRAL_SUCCESS: 200,
  ACHIEVEMENT_UNLOCK_COMMON: 50,
  ACHIEVEMENT_UNLOCK_RARE: 100,
  ACHIEVEMENT_UNLOCK_EPIC: 200,
  ACHIEVEMENT_UNLOCK_LEGENDARY: 500,

  // Bonus multipliers
  WEEKLY_STREAK_MULTIPLIER: 1.5, // 50% bonus for 7 consecutive days
  MONTHLY_CHALLENGE_MULTIPLIER: 2.0, // 2x XP during special events
  BIRTHDAY_MONTH_MULTIPLIER: 2.0, // 2x XP for entire birthday month
  ANNIVERSARY_MULTIPLIER: 3.0, // 3x XP for platform milestones
} as const

// ===== TIER MULTIPLIERS =====

export const TIER_XP_MULTIPLIERS = {
  bronze: 1.0,
  silver: 1.1,
  gold: 1.2,
  platinum: 1.3,
} as const

// ===== UTILITY FUNCTIONS =====

/**
 * Get level definition by level number
 */
export function getLevelDefinition(level: number): LevelDefinition | null {
  return LEVEL_DEFINITIONS.find(def => def.level === level) || null
}

/**
 * Get level definition by total XP
 */
export function getLevelByXP(totalXP: number): LevelDefinition {
  // Find the highest level where xpRequired <= totalXP
  let currentLevel = LEVEL_DEFINITIONS[0]
  
  for (const levelDef of LEVEL_DEFINITIONS) {
    if (totalXP >= levelDef.xpRequired) {
      currentLevel = levelDef
    } else {
      break
    }
  }
  
  return currentLevel
}

/**
 * Calculate XP needed for next level
 */
export function getXPToNextLevel(currentLevel: number, currentXP: number): number {
  const nextLevelDef = getLevelDefinition(currentLevel + 1)
  if (!nextLevelDef) return 0 // Max level reached
  
  return nextLevelDef.xpRequired - currentXP
}

/**
 * Calculate progress percentage to next level
 */
export function getProgressToNextLevel(currentLevel: number, currentXP: number): number {
  const currentLevelDef = getLevelDefinition(currentLevel)
  const nextLevelDef = getLevelDefinition(currentLevel + 1)
  
  if (!currentLevelDef || !nextLevelDef) return 100 // Max level
  
  const xpInCurrentLevel = currentXP - currentLevelDef.xpRequired
  const xpNeededForLevel = nextLevelDef.xpRequired - currentLevelDef.xpRequired
  
  return Math.min(100, Math.max(0, (xpInCurrentLevel / xpNeededForLevel) * 100))
}

/**
 * Check if user leveled up after XP gain
 */
export function checkLevelUp(oldXP: number, newXP: number): { leveledUp: boolean; newLevel?: number; oldLevel?: number } {
  const oldLevel = getLevelByXP(oldXP)
  const newLevel = getLevelByXP(newXP)
  
  if (newLevel.level > oldLevel.level) {
    return {
      leveledUp: true,
      newLevel: newLevel.level,
      oldLevel: oldLevel.level
    }
  }
  
  return { leveledUp: false }
}

/**
 * Calculate XP with tier multiplier
 */
export function calculateXPWithTierMultiplier(baseXP: number, userTier: keyof typeof TIER_XP_MULTIPLIERS): number {
  const multiplier = TIER_XP_MULTIPLIERS[userTier] || 1.0
  return Math.floor(baseXP * multiplier)
}

/**
 * Get tier styling configuration
 */
export function getTierStyling(tier: LevelTier) {
  switch (tier) {
    case 'novice':
      return {
        color: 'text-green-400',
        bgColor: 'bg-green-500/20',
        borderColor: 'border-green-400/30',
        gradientColors: 'from-green-400 to-green-600',
        icon: '🌱'
      }
    case 'intermediate':
      return {
        color: 'text-blue-400',
        bgColor: 'bg-blue-500/20',
        borderColor: 'border-blue-400/30',
        gradientColors: 'from-blue-400 to-blue-600',
        icon: '⚡'
      }
    case 'advanced':
      return {
        color: 'text-purple-400',
        bgColor: 'bg-purple-500/20',
        borderColor: 'border-purple-400/30',
        gradientColors: 'from-purple-400 to-purple-600',
        icon: '🔥'
      }
    case 'expert':
      return {
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-500/20',
        borderColor: 'border-yellow-400/30',
        gradientColors: 'from-yellow-400 to-yellow-600',
        icon: '👑'
      }
  }
}
