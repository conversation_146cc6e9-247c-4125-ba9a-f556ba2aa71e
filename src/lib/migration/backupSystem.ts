/**
 * Backup and Rollback System
 * Comprehensive backup system for migration safety with rollback capabilities
 */

import { 
  doc, 
  getDoc, 
  setDoc, 
  deleteDoc,
  collection,
  getDocs,
  query,
  where,
  writeBatch,
  DocumentData,
  DocumentSnapshot
} from 'firebase/firestore'
import { db } from '../firebase/config'
import { ImageReference } from './imageScanner'
import { UpdateOperation } from './databaseUpdater'
import { R2StorageService } from '../cloudflare/r2StorageService'

export interface BackupMetadata {
  id: string
  timestamp: Date
  migrationSessionId: string
  description: string
  totalDocuments: number
  totalImages: number
  backupSize: number
  status: 'creating' | 'completed' | 'failed' | 'restored' | 'deleted'
  collections: string[]
  version: string
}

export interface DocumentBackup {
  collection: string
  documentId: string
  originalData: DocumentData
  timestamp: Date
  fieldBackups: FieldBackup[]
}

export interface FieldBackup {
  fieldPath: string
  originalValue: any
  newValue?: any
  backupType: 'url' | 'data' | 'file'
}

export interface ImageBackup {
  imageId: string
  originalUrl: string
  r2Url?: string
  fileName: string
  contentType: string
  size: number
  backupLocation: string
  status: 'pending' | 'backed_up' | 'failed' | 'restored'
}

export interface RollbackPlan {
  id: string
  backupId: string
  timestamp: Date
  description: string
  operations: RollbackOperation[]
  estimatedDuration: number
  riskLevel: 'low' | 'medium' | 'high'
  prerequisites: string[]
}

export interface RollbackOperation {
  type: 'restore_document' | 'restore_field' | 'delete_r2_file' | 'restore_firebase_url'
  collection?: string
  documentId?: string
  fieldPath?: string
  originalValue?: any
  r2Key?: string
  priority: number
}

export interface RollbackResult {
  success: boolean
  totalOperations: number
  successfulOperations: number
  failedOperations: number
  duration: number
  errors: Array<{
    operation: RollbackOperation
    error: string
  }>
}

export class BackupSystem {
  private r2Service: R2StorageService
  private backupCollection = 'migration_backups'
  private documentBackupCollection = 'document_backups'
  private imageBackupCollection = 'image_backups'

  constructor() {
    this.r2Service = new R2StorageService()
  }

  /**
   * Create comprehensive backup before migration
   */
  async createMigrationBackup(
    migrationSessionId: string,
    imageReferences: ImageReference[],
    description: string = 'Pre-migration backup'
  ): Promise<BackupMetadata> {
    const backupId = `backup_${migrationSessionId}_${Date.now()}`
    
    const metadata: BackupMetadata = {
      id: backupId,
      timestamp: new Date(),
      migrationSessionId,
      description,
      totalDocuments: 0,
      totalImages: imageReferences.length,
      backupSize: 0,
      status: 'creating',
      collections: [...new Set(imageReferences.map(img => img.collection))],
      version: '1.0'
    }

    try {
      // Save initial metadata
      await setDoc(doc(db, this.backupCollection, backupId), metadata)

      // Backup documents
      const documentBackups = await this.backupDocuments(backupId, imageReferences)
      metadata.totalDocuments = documentBackups.length

      // Backup images to R2
      const imageBackups = await this.backupImages(backupId, imageReferences)
      
      // Calculate total backup size
      metadata.backupSize = imageBackups.reduce((total, img) => total + img.size, 0)
      metadata.status = 'completed'

      // Update metadata
      await setDoc(doc(db, this.backupCollection, backupId), metadata)

      return metadata
    } catch (error) {
      metadata.status = 'failed'
      await setDoc(doc(db, this.backupCollection, backupId), metadata)
      throw new Error(`Backup creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Backup documents that will be modified
   */
  private async backupDocuments(
    backupId: string,
    imageReferences: ImageReference[]
  ): Promise<DocumentBackup[]> {
    const documentBackups: DocumentBackup[] = []
    const processedDocs = new Set<string>()

    for (const imageRef of imageReferences) {
      const docKey = `${imageRef.collection}/${imageRef.documentId}`
      
      if (processedDocs.has(docKey)) {
        continue
      }
      
      try {
        const docRef = doc(db, imageRef.collection, imageRef.documentId)
        const docSnapshot = await getDoc(docRef)
        
        if (docSnapshot.exists()) {
          const documentBackup: DocumentBackup = {
            collection: imageRef.collection,
            documentId: imageRef.documentId,
            originalData: docSnapshot.data(),
            timestamp: new Date(),
            fieldBackups: []
          }

          // Create field-specific backups for images in this document
          const imageFields = imageReferences
            .filter(img => img.collection === imageRef.collection && img.documentId === imageRef.documentId)
            .map(img => ({
              fieldPath: img.fieldPath,
              originalValue: this.getNestedValue(documentBackup.originalData, img.fieldPath),
              backupType: 'url' as const
            }))

          documentBackup.fieldBackups = imageFields

          // Save document backup
          const backupDocId = `${backupId}_${imageRef.collection}_${imageRef.documentId}`
          await setDoc(doc(db, this.documentBackupCollection, backupDocId), documentBackup)
          
          documentBackups.push(documentBackup)
          processedDocs.add(docKey)
        }
      } catch (error) {
        console.error(`Failed to backup document ${docKey}:`, error)
      }
    }

    return documentBackups
  }

  /**
   * Backup images to R2 backup bucket
   */
  private async backupImages(
    backupId: string,
    imageReferences: ImageReference[]
  ): Promise<ImageBackup[]> {
    const imageBackups: ImageBackup[] = []

    for (const imageRef of imageReferences) {
      try {
        const imageBackup: ImageBackup = {
          imageId: imageRef.id,
          originalUrl: imageRef.url,
          fileName: imageRef.fileName || 'unknown',
          contentType: imageRef.contentType || 'image/jpeg',
          size: imageRef.size || 0,
          backupLocation: `backups/${backupId}/${imageRef.id}`,
          status: 'pending'
        }

        // Download original image
        const response = await fetch(imageRef.url)
        if (!response.ok) {
          throw new Error(`Failed to download image: ${response.statusText}`)
        }

        const imageData = await response.arrayBuffer()
        imageBackup.size = imageData.byteLength

        // Upload to R2 backup location
        const uploadResult = await this.r2Service.upload({
          bucketType: 'backups',
          key: imageBackup.backupLocation,
          body: imageData,
          contentType: imageBackup.contentType,
          metadata: {
            backupId,
            originalUrl: imageRef.url,
            migrationId: imageRef.id,
            collection: imageRef.collection,
            documentId: imageRef.documentId
          }
        })

        if (uploadResult.success) {
          imageBackup.status = 'backed_up'
        } else {
          imageBackup.status = 'failed'
          throw new Error(uploadResult.error || 'Backup upload failed')
        }

        // Save image backup metadata
        const backupDocId = `${backupId}_${imageRef.id}`
        await setDoc(doc(db, this.imageBackupCollection, backupDocId), imageBackup)
        
        imageBackups.push(imageBackup)
      } catch (error) {
        console.error(`Failed to backup image ${imageRef.id}:`, error)
        
        // Save failed backup record
        const failedBackup: ImageBackup = {
          imageId: imageRef.id,
          originalUrl: imageRef.url,
          fileName: imageRef.fileName || 'unknown',
          contentType: imageRef.contentType || 'image/jpeg',
          size: imageRef.size || 0,
          backupLocation: `backups/${backupId}/${imageRef.id}`,
          status: 'failed'
        }
        
        const backupDocId = `${backupId}_${imageRef.id}`
        await setDoc(doc(db, this.imageBackupCollection, backupDocId), failedBackup)
        imageBackups.push(failedBackup)
      }
    }

    return imageBackups
  }

  /**
   * Create rollback plan
   */
  async createRollbackPlan(
    backupId: string,
    description: string = 'Rollback to pre-migration state'
  ): Promise<RollbackPlan> {
    try {
      // Get backup metadata
      const backupDoc = await getDoc(doc(db, this.backupCollection, backupId))
      if (!backupDoc.exists()) {
        throw new Error(`Backup ${backupId} not found`)
      }

      const backupMetadata = backupDoc.data() as BackupMetadata

      // Get document backups
      const documentBackupsQuery = query(
        collection(db, this.documentBackupCollection),
        where('__name__', '>=', `${backupId}_`),
        where('__name__', '<', `${backupId}_\uf8ff`)
      )
      const documentBackupsSnapshot = await getDocs(documentBackupsQuery)

      // Get image backups
      const imageBackupsQuery = query(
        collection(db, this.imageBackupCollection),
        where('__name__', '>=', `${backupId}_`),
        where('__name__', '<', `${backupId}_\uf8ff`)
      )
      const imageBackupsSnapshot = await getDocs(imageBackupsQuery)

      const operations: RollbackOperation[] = []
      let priority = 1

      // Create document restore operations
      documentBackupsSnapshot.forEach(doc => {
        const backup = doc.data() as DocumentBackup
        operations.push({
          type: 'restore_document',
          collection: backup.collection,
          documentId: backup.documentId,
          originalValue: backup.originalData,
          priority: priority++
        })
      })

      // Create R2 cleanup operations
      imageBackupsSnapshot.forEach(doc => {
        const backup = doc.data() as ImageBackup
        if (backup.r2Url) {
          operations.push({
            type: 'delete_r2_file',
            r2Key: this.extractR2KeyFromUrl(backup.r2Url),
            priority: priority++
          })
        }
      })

      const rollbackPlan: RollbackPlan = {
        id: `rollback_${backupId}_${Date.now()}`,
        backupId,
        timestamp: new Date(),
        description,
        operations,
        estimatedDuration: this.estimateRollbackDuration(operations),
        riskLevel: this.assessRollbackRisk(operations, backupMetadata),
        prerequisites: this.generatePrerequisites(backupMetadata)
      }

      return rollbackPlan
    } catch (error) {
      throw new Error(`Failed to create rollback plan: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Execute rollback plan
   */
  async executeRollback(rollbackPlan: RollbackPlan): Promise<RollbackResult> {
    const startTime = Date.now()
    const errors: Array<{ operation: RollbackOperation; error: string }> = []
    let successfulOperations = 0
    let failedOperations = 0

    // Sort operations by priority
    const sortedOperations = rollbackPlan.operations.sort((a, b) => a.priority - b.priority)

    for (const operation of sortedOperations) {
      try {
        await this.executeRollbackOperation(operation)
        successfulOperations++
      } catch (error) {
        errors.push({
          operation,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        failedOperations++
      }
    }

    const duration = Date.now() - startTime

    return {
      success: failedOperations === 0,
      totalOperations: rollbackPlan.operations.length,
      successfulOperations,
      failedOperations,
      duration,
      errors
    }
  }

  /**
   * Execute a single rollback operation
   */
  private async executeRollbackOperation(operation: RollbackOperation): Promise<void> {
    switch (operation.type) {
      case 'restore_document':
        if (operation.collection && operation.documentId && operation.originalValue) {
          const docRef = doc(db, operation.collection, operation.documentId)
          await setDoc(docRef, operation.originalValue)
        }
        break

      case 'restore_field':
        if (operation.collection && operation.documentId && operation.fieldPath && operation.originalValue !== undefined) {
          const docRef = doc(db, operation.collection, operation.documentId)
          const updateData = this.createUpdateData(operation.fieldPath, operation.originalValue)
          await setDoc(docRef, updateData, { merge: true })
        }
        break

      case 'delete_r2_file':
        if (operation.r2Key) {
          await this.r2Service.delete({
            bucketType: 'images', // Assuming images bucket
            key: operation.r2Key
          })
        }
        break

      case 'restore_firebase_url':
        // This would restore Firebase URLs in documents
        if (operation.collection && operation.documentId && operation.fieldPath && operation.originalValue) {
          const docRef = doc(db, operation.collection, operation.documentId)
          const updateData = this.createUpdateData(operation.fieldPath, operation.originalValue)
          await setDoc(docRef, updateData, { merge: true })
        }
        break

      default:
        throw new Error(`Unknown rollback operation type: ${(operation as any).type}`)
    }
  }

  /**
   * List available backups
   */
  async listBackups(): Promise<BackupMetadata[]> {
    const backupsSnapshot = await getDocs(collection(db, this.backupCollection))
    return backupsSnapshot.docs.map(doc => doc.data() as BackupMetadata)
  }

  /**
   * Get backup details
   */
  async getBackupDetails(backupId: string): Promise<{
    metadata: BackupMetadata
    documentBackups: DocumentBackup[]
    imageBackups: ImageBackup[]
  }> {
    // Get metadata
    const metadataDoc = await getDoc(doc(db, this.backupCollection, backupId))
    if (!metadataDoc.exists()) {
      throw new Error(`Backup ${backupId} not found`)
    }

    // Get document backups
    const documentBackupsQuery = query(
      collection(db, this.documentBackupCollection),
      where('__name__', '>=', `${backupId}_`),
      where('__name__', '<', `${backupId}_\uf8ff`)
    )
    const documentBackupsSnapshot = await getDocs(documentBackupsQuery)
    const documentBackups = documentBackupsSnapshot.docs.map(doc => doc.data() as DocumentBackup)

    // Get image backups
    const imageBackupsQuery = query(
      collection(db, this.imageBackupCollection),
      where('__name__', '>=', `${backupId}_`),
      where('__name__', '<', `${backupId}_\uf8ff`)
    )
    const imageBackupsSnapshot = await getDocs(imageBackupsQuery)
    const imageBackups = imageBackupsSnapshot.docs.map(doc => doc.data() as ImageBackup)

    return {
      metadata: metadataDoc.data() as BackupMetadata,
      documentBackups,
      imageBackups
    }
  }

  /**
   * Delete backup
   */
  async deleteBackup(backupId: string): Promise<void> {
    try {
      // Delete backup metadata
      await deleteDoc(doc(db, this.backupCollection, backupId))

      // Delete document backups
      const documentBackupsQuery = query(
        collection(db, this.documentBackupCollection),
        where('__name__', '>=', `${backupId}_`),
        where('__name__', '<', `${backupId}_\uf8ff`)
      )
      const documentBackupsSnapshot = await getDocs(documentBackupsQuery)
      
      const batch = writeBatch(db)
      documentBackupsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref)
      })
      await batch.commit()

      // Delete image backups from Firestore
      const imageBackupsQuery = query(
        collection(db, this.imageBackupCollection),
        where('__name__', '>=', `${backupId}_`),
        where('__name__', '<', `${backupId}_\uf8ff`)
      )
      const imageBackupsSnapshot = await getDocs(imageBackupsQuery)
      
      const imageBatch = writeBatch(db)
      imageBackupsSnapshot.docs.forEach(doc => {
        imageBatch.delete(doc.ref)
      })
      await imageBatch.commit()

      // Delete backup files from R2
      // Note: This would require listing and deleting all files in the backup folder
      // Implementation depends on R2 service capabilities
    } catch (error) {
      throw new Error(`Failed to delete backup: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Utility methods
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null
    }, obj)
  }

  private createUpdateData(fieldPath: string, newValue: any): Record<string, any> {
    const updateData: Record<string, any> = {}
    updateData[fieldPath] = newValue
    return updateData
  }

  private extractR2KeyFromUrl(url: string): string {
    // Extract R2 key from URL
    try {
      const urlObj = new URL(url)
      return urlObj.pathname.substring(1) // Remove leading slash
    } catch {
      return url
    }
  }

  private estimateRollbackDuration(operations: RollbackOperation[]): number {
    // Estimate duration in minutes based on operation types and count
    const baseTimePerOperation = 0.1 // 6 seconds per operation
    return Math.ceil(operations.length * baseTimePerOperation)
  }

  private assessRollbackRisk(operations: RollbackOperation[], metadata: BackupMetadata): 'low' | 'medium' | 'high' {
    if (operations.length > 1000 || metadata.totalDocuments > 500) {
      return 'high'
    }
    if (operations.length > 100 || metadata.totalDocuments > 50) {
      return 'medium'
    }
    return 'low'
  }

  private generatePrerequisites(metadata: BackupMetadata): string[] {
    const prerequisites = [
      'Ensure R2 storage is accessible',
      'Verify Firebase connectivity',
      'Confirm backup integrity'
    ]

    if (metadata.totalDocuments > 100) {
      prerequisites.push('Schedule maintenance window')
    }

    if (metadata.collections.includes('users') || metadata.collections.includes('orders')) {
      prerequisites.push('Notify users of potential service interruption')
    }

    return prerequisites
  }
}
