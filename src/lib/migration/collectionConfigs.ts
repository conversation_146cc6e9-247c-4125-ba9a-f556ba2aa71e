/**
 * Collection Configurations for Image Scanning
 * Defines which collections and fields to scan for images
 */

import { CollectionScanConfig } from './imageScanner'

/**
 * Default collection configurations for Syndicaps
 * Based on the existing Firestore schema
 */
export const DEFAULT_COLLECTION_CONFIGS: CollectionScanConfig[] = [
  // User profiles and avatars
  {
    name: 'users',
    imageFields: [
      'photoURL',
      'avatar',
      'profileImage',
      'profile.photo',
      'profile.avatar',
      'settings.avatar'
    ],
    batchSize: 100,
    orderByField: 'createdAt'
  },

  // Product images
  {
    name: 'products',
    imageFields: [
      'imageUrl',
      'images',
      'thumbnailUrl',
      'gallery',
      'mainImage',
      'productImages',
      'variants.image',
      'variants.images',
      'specifications.image'
    ],
    batchSize: 50,
    orderByField: 'createdAt'
  },

  // Product categories
  {
    name: 'categories',
    imageFields: [
      'imageUrl',
      'icon',
      'banner',
      'thumbnail'
    ],
    batchSize: 100,
    orderByField: 'name'
  },

  // Orders (for product images in order history)
  {
    name: 'orders',
    imageFields: [
      'items.imageUrl',
      'items.productImage',
      'items.thumbnail'
    ],
    batchSize: 100,
    orderByField: 'createdAt'
  },

  // Cart items
  {
    name: 'carts',
    imageFields: [
      'items.imageUrl',
      'items.productImage',
      'items.thumbnail'
    ],
    batchSize: 100,
    orderByField: 'updatedAt'
  },

  // Wishlist items
  {
    name: 'wishlists',
    imageFields: [
      'items.imageUrl',
      'items.productImage',
      'items.thumbnail'
    ],
    batchSize: 100,
    orderByField: 'updatedAt'
  },

  // Community posts and content
  {
    name: 'posts',
    imageFields: [
      'imageUrl',
      'images',
      'attachments',
      'media',
      'content.images',
      'gallery'
    ],
    batchSize: 50,
    orderByField: 'createdAt'
  },

  // Contest submissions
  {
    name: 'contests',
    imageFields: [
      'bannerImage',
      'thumbnail',
      'submissions.imageUrl',
      'submissions.images',
      'prizes.image'
    ],
    batchSize: 50,
    orderByField: 'createdAt'
  },

  // Contest submissions (separate collection)
  {
    name: 'contest_submissions',
    imageFields: [
      'imageUrl',
      'images',
      'designImages',
      'thumbnails',
      'gallery'
    ],
    batchSize: 50,
    orderByField: 'submittedAt'
  },

  // Achievements and badges
  {
    name: 'achievements',
    imageFields: [
      'icon',
      'badge',
      'image',
      'thumbnail'
    ],
    batchSize: 100,
    orderByField: 'name'
  },

  // User achievements
  {
    name: 'user_achievements',
    imageFields: [
      'achievement.icon',
      'achievement.badge',
      'achievement.image'
    ],
    batchSize: 100,
    orderByField: 'earnedAt'
  },

  // Notifications (may contain images)
  {
    name: 'notifications',
    imageFields: [
      'imageUrl',
      'icon',
      'data.imageUrl',
      'data.productImage'
    ],
    batchSize: 100,
    orderByField: 'createdAt'
  },

  // Reviews (may contain uploaded images)
  {
    name: 'reviews',
    imageFields: [
      'images',
      'photos',
      'attachments'
    ],
    batchSize: 100,
    orderByField: 'createdAt'
  },

  // Blog posts or articles
  {
    name: 'articles',
    imageFields: [
      'featuredImage',
      'thumbnail',
      'content.images',
      'gallery',
      'author.avatar'
    ],
    batchSize: 50,
    orderByField: 'publishedAt'
  },

  // Site settings and configuration
  {
    name: 'site_settings',
    imageFields: [
      'logo',
      'favicon',
      'banners',
      'heroImages',
      'backgroundImages',
      'socialImages'
    ],
    batchSize: 10,
    orderByField: '__name__'
  },

  // Promotional banners
  {
    name: 'banners',
    imageFields: [
      'imageUrl',
      'mobileImage',
      'desktopImage',
      'thumbnail'
    ],
    batchSize: 50,
    orderByField: 'createdAt'
  },

  // Support tickets (may contain screenshots)
  {
    name: 'support_tickets',
    imageFields: [
      'attachments',
      'screenshots',
      'images'
    ],
    batchSize: 100,
    orderByField: 'createdAt'
  }
]

/**
 * High-priority collections that should be migrated first
 */
export const HIGH_PRIORITY_COLLECTIONS = [
  'products',
  'users',
  'categories',
  'site_settings'
]

/**
 * Collections that can be migrated in parallel
 */
export const PARALLEL_SAFE_COLLECTIONS = [
  'reviews',
  'notifications',
  'user_achievements',
  'support_tickets'
]

/**
 * Collections that require special handling
 */
export const SPECIAL_HANDLING_COLLECTIONS = [
  'orders', // Historical data, handle carefully
  'carts', // Active user data, minimal downtime
  'wishlists' // Active user data, minimal downtime
]

/**
 * Get collection config by name
 */
export function getCollectionConfig(name: string): CollectionScanConfig | undefined {
  return DEFAULT_COLLECTION_CONFIGS.find(config => config.name === name)
}

/**
 * Get collections by priority
 */
export function getCollectionsByPriority(): {
  high: CollectionScanConfig[]
  normal: CollectionScanConfig[]
  parallel: CollectionScanConfig[]
} {
  const high = DEFAULT_COLLECTION_CONFIGS.filter(config => 
    HIGH_PRIORITY_COLLECTIONS.includes(config.name)
  )
  
  const parallel = DEFAULT_COLLECTION_CONFIGS.filter(config => 
    PARALLEL_SAFE_COLLECTIONS.includes(config.name)
  )
  
  const normal = DEFAULT_COLLECTION_CONFIGS.filter(config => 
    !HIGH_PRIORITY_COLLECTIONS.includes(config.name) && 
    !PARALLEL_SAFE_COLLECTIONS.includes(config.name)
  )

  return { high, normal, parallel }
}

/**
 * Validate collection configuration
 */
export function validateCollectionConfig(config: CollectionScanConfig): {
  valid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (!config.name || typeof config.name !== 'string') {
    errors.push('Collection name is required and must be a string')
  }

  if (!config.imageFields || !Array.isArray(config.imageFields) || config.imageFields.length === 0) {
    errors.push('Image fields array is required and must not be empty')
  }

  if (config.batchSize && (typeof config.batchSize !== 'number' || config.batchSize < 1 || config.batchSize > 1000)) {
    errors.push('Batch size must be a number between 1 and 1000')
  }

  if (config.orderByField && typeof config.orderByField !== 'string') {
    errors.push('Order by field must be a string')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Create custom collection configuration
 */
export function createCollectionConfig(
  name: string,
  imageFields: string[],
  options?: {
    batchSize?: number
    orderByField?: string
    filters?: any[]
  }
): CollectionScanConfig {
  const config: CollectionScanConfig = {
    name,
    imageFields,
    batchSize: options?.batchSize || 100,
    orderByField: options?.orderByField || 'createdAt',
    filters: options?.filters
  }

  const validation = validateCollectionConfig(config)
  if (!validation.valid) {
    throw new Error(`Invalid collection configuration: ${validation.errors.join(', ')}`)
  }

  return config
}

/**
 * Estimate migration complexity for a collection
 */
export function estimateCollectionComplexity(config: CollectionScanConfig): {
  complexity: 'low' | 'medium' | 'high'
  factors: string[]
  estimatedTimeMinutes: number
} {
  const factors: string[] = []
  let complexity: 'low' | 'medium' | 'high' = 'low'
  let estimatedTimeMinutes = 5

  // Check number of image fields
  if (config.imageFields.length > 5) {
    factors.push('Many image fields to scan')
    complexity = 'medium'
    estimatedTimeMinutes += config.imageFields.length * 2
  }

  // Check for nested fields
  const hasNestedFields = config.imageFields.some(field => field.includes('.') || field.includes('['))
  if (hasNestedFields) {
    factors.push('Complex nested field structures')
    complexity = 'medium'
    estimatedTimeMinutes += 10
  }

  // Check for array fields
  const hasArrayFields = config.imageFields.some(field => field.includes('[') || field.includes('items.'))
  if (hasArrayFields) {
    factors.push('Array fields requiring iteration')
    if (complexity === 'low') complexity = 'medium'
    estimatedTimeMinutes += 15
  }

  // Check batch size
  if (config.batchSize && config.batchSize < 50) {
    factors.push('Small batch size may slow processing')
    estimatedTimeMinutes += 10
  }

  // Special handling collections
  if (SPECIAL_HANDLING_COLLECTIONS.includes(config.name)) {
    factors.push('Requires special handling procedures')
    complexity = 'high'
    estimatedTimeMinutes += 20
  }

  // High priority collections need extra care
  if (HIGH_PRIORITY_COLLECTIONS.includes(config.name)) {
    factors.push('High priority collection requiring careful handling')
    if (complexity === 'low') complexity = 'medium'
    estimatedTimeMinutes += 10
  }

  return {
    complexity,
    factors,
    estimatedTimeMinutes
  }
}
