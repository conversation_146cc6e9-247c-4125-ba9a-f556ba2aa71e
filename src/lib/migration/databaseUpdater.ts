/**
 * Database Reference Updater
 * Updates Firestore document references from Firebase URLs to R2 URLs
 */

import { 
  doc, 
  updateDoc, 
  writeBatch, 
  getDoc, 
  collection,
  query,
  where,
  getDocs,
  DocumentReference,
  DocumentData,
  WriteBatch
} from 'firebase/firestore'
import { db } from '../firebase/config'
import { ImageReference } from './imageScanner'

export interface UpdateOperation {
  id: string
  collection: string
  documentId: string
  fieldPath: string
  oldValue: string
  newValue: string
  status: 'pending' | 'completed' | 'failed' | 'rolled_back'
  timestamp: Date
  error?: string
  retryCount: number
}

export interface BatchUpdateResult {
  success: boolean
  totalOperations: number
  successfulUpdates: number
  failedUpdates: number
  operations: UpdateOperation[]
  errors: Array<{
    operation: UpdateOperation
    error: string
  }>
}

export interface RollbackResult {
  success: boolean
  totalOperations: number
  successfulRollbacks: number
  failedRollbacks: number
  errors: Array<{
    operationId: string
    error: string
  }>
}

export interface UpdateProgress {
  totalOperations: number
  completedOperations: number
  successfulUpdates: number
  failedUpdates: number
  currentBatch: number
  totalBatches: number
  startTime: Date
  estimatedCompletion?: Date
  currentOperation: string
}

export class DatabaseUpdater {
  private operations: UpdateOperation[] = []
  private progress: UpdateProgress
  private onProgressUpdate?: (progress: UpdateProgress) => void
  private readonly maxBatchSize = 500 // Firestore batch limit

  constructor(onProgressUpdate?: (progress: UpdateProgress) => void) {
    this.onProgressUpdate = onProgressUpdate
    this.progress = {
      totalOperations: 0,
      completedOperations: 0,
      successfulUpdates: 0,
      failedUpdates: 0,
      currentBatch: 0,
      totalBatches: 0,
      startTime: new Date(),
      currentOperation: 'Initializing'
    }
  }

  /**
   * Update database references for migrated images
   */
  async updateImageReferences(
    imageReferences: ImageReference[],
    urlMappings: Record<string, string> // imageId -> new R2 URL
  ): Promise<BatchUpdateResult> {
    try {
      this.progress.startTime = new Date()
      this.progress.currentOperation = 'Preparing update operations'
      
      // Create update operations
      this.operations = this.createUpdateOperations(imageReferences, urlMappings)
      this.progress.totalOperations = this.operations.length
      this.progress.totalBatches = Math.ceil(this.operations.length / this.maxBatchSize)
      
      this.updateProgress()

      // Execute updates in batches
      const batches = this.createBatches(this.operations, this.maxBatchSize)
      
      for (let i = 0; i < batches.length; i++) {
        this.progress.currentBatch = i + 1
        this.progress.currentOperation = `Processing batch ${i + 1} of ${batches.length}`
        this.updateProgress()

        await this.executeBatch(batches[i])
      }

      this.progress.currentOperation = 'Update completed'
      this.updateProgress()

      return this.generateBatchResult()
    } catch (error) {
      throw new Error(`Database update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Create update operations from image references
   */
  private createUpdateOperations(
    imageReferences: ImageReference[],
    urlMappings: Record<string, string>
  ): UpdateOperation[] {
    const operations: UpdateOperation[] = []

    imageReferences.forEach(imageRef => {
      const newUrl = urlMappings[imageRef.id]
      if (newUrl) {
        operations.push({
          id: `update_${imageRef.id}`,
          collection: imageRef.collection,
          documentId: imageRef.documentId,
          fieldPath: imageRef.fieldPath,
          oldValue: imageRef.url,
          newValue: newUrl,
          status: 'pending',
          timestamp: new Date(),
          retryCount: 0
        })
      }
    })

    return operations
  }

  /**
   * Create batches from operations
   */
  private createBatches(operations: UpdateOperation[], batchSize: number): UpdateOperation[][] {
    const batches: UpdateOperation[][] = []
    
    for (let i = 0; i < operations.length; i += batchSize) {
      batches.push(operations.slice(i, i + batchSize))
    }
    
    return batches
  }

  /**
   * Execute a batch of update operations
   */
  private async executeBatch(operations: UpdateOperation[]): Promise<void> {
    const batch = writeBatch(db)
    const batchOperations: UpdateOperation[] = []

    try {
      // Prepare batch operations
      for (const operation of operations) {
        try {
          const docRef = doc(db, operation.collection, operation.documentId)
          const updateData = this.createUpdateData(operation.fieldPath, operation.newValue)
          
          batch.update(docRef, updateData)
          batchOperations.push(operation)
        } catch (error) {
          operation.status = 'failed'
          operation.error = `Failed to prepare update: ${error instanceof Error ? error.message : 'Unknown error'}`
          this.progress.failedUpdates++
        }
      }

      // Execute batch
      if (batchOperations.length > 0) {
        await batch.commit()
        
        // Mark operations as completed
        batchOperations.forEach(operation => {
          operation.status = 'completed'
          this.progress.successfulUpdates++
        })
      }

    } catch (error) {
      // Mark all batch operations as failed
      batchOperations.forEach(operation => {
        operation.status = 'failed'
        operation.error = `Batch commit failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        this.progress.failedUpdates++
      })
    }

    this.progress.completedOperations += operations.length
    this.updateProgress()
  }

  /**
   * Create update data for nested field paths
   */
  private createUpdateData(fieldPath: string, newValue: string): Record<string, any> {
    const updateData: Record<string, any> = {}
    
    if (fieldPath.includes('[') && fieldPath.includes(']')) {
      // Handle array fields like "images[0]" or "items[1].imageUrl"
      const arrayMatch = fieldPath.match(/^([^[]+)\[(\d+)\](.*)$/)
      if (arrayMatch) {
        const [, arrayField, indexStr, remainingPath] = arrayMatch
        const index = parseInt(indexStr)
        
        if (remainingPath) {
          // Nested field in array item like "items[0].imageUrl"
          updateData[`${arrayField}.${index}${remainingPath}`] = newValue
        } else {
          // Direct array item like "images[0]"
          updateData[`${arrayField}.${index}`] = newValue
        }
      } else {
        // Fallback to direct field update
        updateData[fieldPath] = newValue
      }
    } else {
      // Handle dot notation like "profile.avatar"
      updateData[fieldPath] = newValue
    }
    
    return updateData
  }

  /**
   * Rollback database updates
   */
  async rollbackUpdates(operations?: UpdateOperation[]): Promise<RollbackResult> {
    const operationsToRollback = operations || this.operations.filter(op => op.status === 'completed')
    
    this.progress.currentOperation = 'Rolling back database updates'
    this.progress.totalOperations = operationsToRollback.length
    this.progress.completedOperations = 0
    this.progress.successfulUpdates = 0
    this.progress.failedUpdates = 0
    this.updateProgress()

    const errors: Array<{ operationId: string; error: string }> = []
    let successfulRollbacks = 0
    let failedRollbacks = 0

    // Process rollbacks in batches
    const batches = this.createBatches(operationsToRollback, this.maxBatchSize)
    
    for (let i = 0; i < batches.length; i++) {
      this.progress.currentBatch = i + 1
      this.progress.currentOperation = `Rolling back batch ${i + 1} of ${batches.length}`
      this.updateProgress()

      const batch = writeBatch(db)
      const batchOperations: UpdateOperation[] = []

      try {
        // Prepare rollback batch
        for (const operation of batches[i]) {
          try {
            const docRef = doc(db, operation.collection, operation.documentId)
            const updateData = this.createUpdateData(operation.fieldPath, operation.oldValue)
            
            batch.update(docRef, updateData)
            batchOperations.push(operation)
          } catch (error) {
            errors.push({
              operationId: operation.id,
              error: `Failed to prepare rollback: ${error instanceof Error ? error.message : 'Unknown error'}`
            })
            failedRollbacks++
          }
        }

        // Execute rollback batch
        if (batchOperations.length > 0) {
          await batch.commit()
          
          // Mark operations as rolled back
          batchOperations.forEach(operation => {
            operation.status = 'rolled_back'
            successfulRollbacks++
          })
        }

      } catch (error) {
        // Mark all batch operations as failed rollback
        batchOperations.forEach(operation => {
          errors.push({
            operationId: operation.id,
            error: `Rollback batch commit failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          })
          failedRollbacks++
        })
      }

      this.progress.completedOperations += batches[i].length
      this.updateProgress()
    }

    return {
      success: failedRollbacks === 0,
      totalOperations: operationsToRollback.length,
      successfulRollbacks,
      failedRollbacks,
      errors
    }
  }

  /**
   * Validate database updates
   */
  async validateUpdates(operations?: UpdateOperation[]): Promise<{
    valid: boolean
    validatedOperations: number
    invalidOperations: number
    errors: Array<{ operationId: string; error: string }>
  }> {
    const operationsToValidate = operations || this.operations.filter(op => op.status === 'completed')
    const errors: Array<{ operationId: string; error: string }> = []
    let validatedOperations = 0
    let invalidOperations = 0

    this.progress.currentOperation = 'Validating database updates'
    this.updateProgress()

    for (const operation of operationsToValidate) {
      try {
        const docRef = doc(db, operation.collection, operation.documentId)
        const docSnapshot = await getDoc(docRef)
        
        if (!docSnapshot.exists()) {
          errors.push({
            operationId: operation.id,
            error: 'Document no longer exists'
          })
          invalidOperations++
          continue
        }

        const currentValue = this.getNestedValue(docSnapshot.data(), operation.fieldPath)
        
        if (currentValue === operation.newValue) {
          validatedOperations++
        } else {
          errors.push({
            operationId: operation.id,
            error: `Value mismatch: expected "${operation.newValue}", found "${currentValue}"`
          })
          invalidOperations++
        }
      } catch (error) {
        errors.push({
          operationId: operation.id,
          error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
        invalidOperations++
      }
    }

    return {
      valid: invalidOperations === 0,
      validatedOperations,
      invalidOperations,
      errors
    }
  }

  /**
   * Find documents with Firebase Storage URLs
   */
  async findDocumentsWithFirebaseUrls(
    collectionName: string,
    fieldPath: string
  ): Promise<Array<{ documentId: string; url: string }>> {
    const results: Array<{ documentId: string; url: string }> = []
    
    try {
      // Note: This is a simplified implementation
      // In practice, you might need more sophisticated querying
      const collectionRef = collection(db, collectionName)
      const snapshot = await getDocs(collectionRef)
      
      snapshot.forEach(doc => {
        const data = doc.data()
        const value = this.getNestedValue(data, fieldPath)
        
        if (value && typeof value === 'string' && this.isFirebaseStorageUrl(value)) {
          results.push({
            documentId: doc.id,
            url: value
          })
        }
      })
    } catch (error) {
      throw new Error(`Failed to find documents: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
    
    return results
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      // Handle array notation
      if (key.includes('[') && key.includes(']')) {
        const arrayMatch = key.match(/^([^[]+)\[(\d+)\]$/)
        if (arrayMatch) {
          const [, arrayKey, indexStr] = arrayMatch
          const index = parseInt(indexStr)
          return current && current[arrayKey] && current[arrayKey][index]
        }
      }
      
      return current && current[key] !== undefined ? current[key] : null
    }, obj)
  }

  /**
   * Check if URL is a Firebase Storage URL
   */
  private isFirebaseStorageUrl(url: string): boolean {
    if (typeof url !== 'string') return false
    
    return url.includes('firebasestorage.googleapis.com') ||
           url.includes('firebase.google.com') ||
           url.includes('appspot.com')
  }

  /**
   * Update progress and notify listeners
   */
  private updateProgress(): void {
    // Calculate estimated completion time
    if (this.progress.completedOperations > 0) {
      const elapsed = Date.now() - this.progress.startTime.getTime()
      const rate = this.progress.completedOperations / elapsed
      const remaining = this.progress.totalOperations - this.progress.completedOperations
      const estimatedMs = remaining / rate
      this.progress.estimatedCompletion = new Date(Date.now() + estimatedMs)
    }

    if (this.onProgressUpdate) {
      this.onProgressUpdate({ ...this.progress })
    }
  }

  /**
   * Generate batch result summary
   */
  private generateBatchResult(): BatchUpdateResult {
    const errors = this.operations
      .filter(op => op.status === 'failed')
      .map(op => ({
        operation: op,
        error: op.error || 'Unknown error'
      }))

    return {
      success: this.progress.failedUpdates === 0,
      totalOperations: this.operations.length,
      successfulUpdates: this.progress.successfulUpdates,
      failedUpdates: this.progress.failedUpdates,
      operations: this.operations,
      errors
    }
  }

  /**
   * Get update operations
   */
  getOperations(): UpdateOperation[] {
    return [...this.operations]
  }

  /**
   * Export operations to JSON
   */
  exportOperations(): string {
    return JSON.stringify(this.operations, null, 2)
  }

  /**
   * Import operations from JSON
   */
  importOperations(jsonData: string): void {
    try {
      this.operations = JSON.parse(jsonData)
    } catch (error) {
      throw new Error(`Failed to import operations: ${error instanceof Error ? error.message : 'Invalid JSON'}`)
    }
  }
}
