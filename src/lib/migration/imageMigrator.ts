/**
 * Image Migration System
 * Migrates images from Firebase Storage to Cloudflare R2 with progress tracking
 */

import { ImageReference, ImageScanner, ScanResult } from './imageScanner'
import { R2StorageService } from '../cloudflare/r2StorageService'
import { r2PerformanceMonitor } from '../cloudflare/r2PerformanceMonitor'
import { doc, updateDoc, writeBatch, getDoc } from 'firebase/firestore'
import { db } from '../firebase/config'

export interface MigrationConfig {
  batchSize: number
  maxConcurrentBatches: number
  retryAttempts: number
  retryDelayMs: number
  pauseOnErrorRate: number // Percentage (e.g., 5 for 5%)
  dryRun: boolean
  backupOriginalUrls: boolean
  validateAfterMigration: boolean
}

export interface MigrationProgress {
  totalImages: number
  processedImages: number
  successfulMigrations: number
  failedMigrations: number
  skippedImages: number
  currentBatch: number
  totalBatches: number
  startTime: Date
  estimatedCompletion?: Date
  currentOperation: string
  errorRate: number
  isPaused: boolean
  isCompleted: boolean
  isCancelled: boolean
}

export interface MigrationResult {
  success: boolean
  progress: MigrationProgress
  errors: MigrationError[]
  summary: {
    totalProcessed: number
    successRate: number
    totalSizeMigrated: number
    averageSpeed: number
    timeElapsed: number
  }
  backupData?: BackupData
}

export interface MigrationError {
  imageId: string
  imageUrl: string
  collection: string
  documentId: string
  fieldPath: string
  error: string
  timestamp: Date
  retryCount: number
  isFatal: boolean
}

export interface BackupData {
  timestamp: Date
  originalUrls: Record<string, string> // imageId -> original URL
  documentUpdates: Record<string, any> // documentPath -> original field values
}

export interface MigrationBatch {
  id: string
  images: ImageReference[]
  status: 'pending' | 'processing' | 'completed' | 'failed'
  startTime?: Date
  endTime?: Date
  errors: MigrationError[]
}

export class ImageMigrator {
  private r2Service: R2StorageService
  private progress: MigrationProgress
  private errors: MigrationError[] = []
  private backupData: BackupData
  private onProgressUpdate?: (progress: MigrationProgress) => void
  private onError?: (error: MigrationError) => void
  private isPaused = false
  private isCancelled = false

  constructor(
    onProgressUpdate?: (progress: MigrationProgress) => void,
    onError?: (error: MigrationError) => void
  ) {
    this.r2Service = new R2StorageService()
    this.onProgressUpdate = onProgressUpdate
    this.onError = onError
    
    this.progress = {
      totalImages: 0,
      processedImages: 0,
      successfulMigrations: 0,
      failedMigrations: 0,
      skippedImages: 0,
      currentBatch: 0,
      totalBatches: 0,
      startTime: new Date(),
      currentOperation: 'Initializing',
      errorRate: 0,
      isPaused: false,
      isCompleted: false,
      isCancelled: false
    }

    this.backupData = {
      timestamp: new Date(),
      originalUrls: {},
      documentUpdates: {}
    }
  }

  /**
   * Migrate all images from scan result
   */
  async migrateImages(
    scanResult: ScanResult,
    config: MigrationConfig
  ): Promise<MigrationResult> {
    try {
      this.progress.totalImages = scanResult.images.length
      this.progress.totalBatches = Math.ceil(scanResult.images.length / config.batchSize)
      this.progress.startTime = new Date()
      this.progress.currentOperation = 'Starting migration'
      
      this.updateProgress()

      // Create batches
      const batches = this.createBatches(scanResult.images, config.batchSize)
      
      // Process batches
      for (let i = 0; i < batches.length; i++) {
        if (this.isCancelled) {
          this.progress.isCancelled = true
          break
        }

        // Check if we should pause due to high error rate
        if (this.progress.errorRate > config.pauseOnErrorRate) {
          this.progress.isPaused = true
          this.progress.currentOperation = `Paused due to high error rate (${this.progress.errorRate.toFixed(1)}%)`
          this.updateProgress()
          
          // Wait for manual intervention or automatic resume
          await this.waitForResume()
        }

        this.progress.currentBatch = i + 1
        this.progress.currentOperation = `Processing batch ${i + 1} of ${batches.length}`
        this.updateProgress()

        await this.processBatch(batches[i], config)
      }

      this.progress.isCompleted = true
      this.progress.currentOperation = 'Migration completed'
      this.updateProgress()

      return this.generateMigrationResult(config)
    } catch (error) {
      this.addError(
        'migration_system',
        '',
        'system',
        'system',
        'system',
        error instanceof Error ? error.message : 'Unknown migration error',
        true
      )
      throw error
    }
  }

  /**
   * Process a single batch of images
   */
  private async processBatch(batch: MigrationBatch, config: MigrationConfig): Promise<void> {
    batch.status = 'processing'
    batch.startTime = new Date()

    const promises = batch.images.map(image => 
      this.migrateImage(image, config).catch(error => {
        this.addError(
          image.id,
          image.url,
          image.collection,
          image.documentId,
          image.fieldPath,
          error instanceof Error ? error.message : 'Unknown error',
          false
        )
      })
    )

    try {
      await Promise.all(promises)
      batch.status = 'completed'
    } catch (error) {
      batch.status = 'failed'
      throw error
    } finally {
      batch.endTime = new Date()
    }
  }

  /**
   * Migrate a single image
   */
  private async migrateImage(image: ImageReference, config: MigrationConfig): Promise<void> {
    const operationId = `migrate_${image.id}`
    
    try {
      // Start performance tracking
      r2PerformanceMonitor.startOperation(
        operationId,
        'upload',
        this.determineBucketType(image),
        {
          fileSize: image.size,
          contentType: image.contentType,
          metadata: { migrationId: image.id }
        }
      )

      // Download from Firebase
      const response = await fetch(image.url)
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.statusText}`)
      }

      const imageData = await response.arrayBuffer()
      const actualSize = imageData.byteLength

      // Generate R2 key
      const r2Key = this.generateR2Key(image)

      if (config.dryRun) {
        // Simulate upload for dry run
        await new Promise(resolve => setTimeout(resolve, 100))
        this.progress.skippedImages++
      } else {
        // Upload to R2
        const uploadResult = await this.r2Service.upload({
          bucketType: this.determineBucketType(image),
          key: r2Key,
          body: imageData,
          contentType: image.contentType || 'image/jpeg',
          metadata: {
            originalUrl: image.url,
            migrationId: image.id,
            collection: image.collection,
            documentId: image.documentId,
            fieldPath: image.fieldPath
          }
        })

        if (!uploadResult.success) {
          throw new Error(uploadResult.error || 'Upload failed')
        }

        // Backup original URL if requested
        if (config.backupOriginalUrls) {
          this.backupData.originalUrls[image.id] = image.url
        }

        // Update Firestore reference
        await this.updateFirestoreReference(image, uploadResult.url!, config)

        // Validate migration if requested
        if (config.validateAfterMigration) {
          await this.validateMigration(image, uploadResult.url!)
        }

        this.progress.successfulMigrations++
      }

      // Complete performance tracking
      r2PerformanceMonitor.completeOperation(operationId, true, {
        actualFileSize: actualSize
      })

    } catch (error) {
      // Record failure
      r2PerformanceMonitor.recordFailure(
        operationId,
        error instanceof Error ? error.message : 'Migration failed'
      )

      this.progress.failedMigrations++
      throw error
    } finally {
      this.progress.processedImages++
      this.updateProgress()
    }
  }

  /**
   * Update Firestore document reference
   */
  private async updateFirestoreReference(
    image: ImageReference,
    newUrl: string,
    config: MigrationConfig
  ): Promise<void> {
    if (config.dryRun) return

    try {
      const docRef = doc(db, image.collection, image.documentId)
      
      // Backup original value if requested
      if (config.backupOriginalUrls) {
        const docSnapshot = await getDoc(docRef)
        if (docSnapshot.exists()) {
          const documentPath = `${image.collection}/${image.documentId}`
          this.backupData.documentUpdates[documentPath] = docSnapshot.data()
        }
      }

      // Update the field
      const updateData = this.createUpdateData(image.fieldPath, newUrl)
      await updateDoc(docRef, updateData)

    } catch (error) {
      throw new Error(`Failed to update Firestore reference: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Create update data object for nested field paths
   */
  private createUpdateData(fieldPath: string, newValue: string): Record<string, any> {
    const updateData: Record<string, any> = {}
    
    // Handle array notation like "images[0]"
    if (fieldPath.includes('[') && fieldPath.includes(']')) {
      // For array fields, we need to update the specific index
      // This is a simplified implementation - in production, you might need more sophisticated handling
      const baseField = fieldPath.split('[')[0]
      updateData[baseField] = newValue // This is simplified - real implementation would preserve array structure
    } else {
      // Handle dot notation like "profile.avatar"
      updateData[fieldPath] = newValue
    }
    
    return updateData
  }

  /**
   * Validate that migration was successful
   */
  private async validateMigration(image: ImageReference, newUrl: string): Promise<void> {
    try {
      // Test that the new URL is accessible
      const response = await fetch(newUrl, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`New URL not accessible: ${response.statusText}`)
      }

      // Verify content length if available
      if (image.size && response.headers.get('content-length')) {
        const newSize = parseInt(response.headers.get('content-length')!)
        if (Math.abs(newSize - image.size) > 1024) { // Allow 1KB difference
          throw new Error(`Size mismatch: original ${image.size}, new ${newSize}`)
        }
      }
    } catch (error) {
      throw new Error(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Determine appropriate R2 bucket type for image
   */
  private determineBucketType(image: ImageReference): string {
    // Determine bucket based on collection and context
    if (image.collection === 'users' || image.fieldPath.includes('avatar') || image.fieldPath.includes('profile')) {
      return 'images' // User avatars and profiles
    }
    
    if (image.collection === 'products' || image.collection === 'categories') {
      return 'images' // Product and category images
    }
    
    if (image.collection === 'posts' || image.collection === 'contest_submissions') {
      return 'images' // User-generated content
    }
    
    if (image.collection === 'site_settings' || image.collection === 'banners') {
      return 'images' // Site assets
    }
    
    return 'images' // Default to images bucket
  }

  /**
   * Generate R2 key for image
   */
  private generateR2Key(image: ImageReference): string {
    const timestamp = new Date().toISOString().split('T')[0] // YYYY-MM-DD
    const fileName = image.fileName || 'unknown'
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
    
    return `migrated/${timestamp}/${image.collection}/${image.documentId}/${sanitizedFileName}`
  }

  /**
   * Create batches from images array
   */
  private createBatches(images: ImageReference[], batchSize: number): MigrationBatch[] {
    const batches: MigrationBatch[] = []
    
    for (let i = 0; i < images.length; i += batchSize) {
      const batchImages = images.slice(i, i + batchSize)
      batches.push({
        id: `batch_${Math.floor(i / batchSize) + 1}`,
        images: batchImages,
        status: 'pending',
        errors: []
      })
    }
    
    return batches
  }

  /**
   * Add error to error list
   */
  private addError(
    imageId: string,
    imageUrl: string,
    collection: string,
    documentId: string,
    fieldPath: string,
    error: string,
    isFatal: boolean
  ): void {
    const migrationError: MigrationError = {
      imageId,
      imageUrl,
      collection,
      documentId,
      fieldPath,
      error,
      timestamp: new Date(),
      retryCount: 0,
      isFatal
    }
    
    this.errors.push(migrationError)
    this.updateErrorRate()
    
    if (this.onError) {
      this.onError(migrationError)
    }
  }

  /**
   * Update error rate calculation
   */
  private updateErrorRate(): void {
    if (this.progress.processedImages > 0) {
      this.progress.errorRate = (this.progress.failedMigrations / this.progress.processedImages) * 100
    }
  }

  /**
   * Update progress and notify listeners
   */
  private updateProgress(): void {
    // Calculate estimated completion time
    if (this.progress.processedImages > 0) {
      const elapsed = Date.now() - this.progress.startTime.getTime()
      const rate = this.progress.processedImages / elapsed
      const remaining = this.progress.totalImages - this.progress.processedImages
      const estimatedMs = remaining / rate
      this.progress.estimatedCompletion = new Date(Date.now() + estimatedMs)
    }

    if (this.onProgressUpdate) {
      this.onProgressUpdate({ ...this.progress })
    }
  }

  /**
   * Wait for resume signal
   */
  private async waitForResume(): Promise<void> {
    return new Promise((resolve) => {
      const checkResume = () => {
        if (!this.isPaused || this.isCancelled) {
          resolve()
        } else {
          setTimeout(checkResume, 1000)
        }
      }
      checkResume()
    })
  }

  /**
   * Pause migration
   */
  pause(): void {
    this.isPaused = true
    this.progress.isPaused = true
    this.progress.currentOperation = 'Migration paused'
    this.updateProgress()
  }

  /**
   * Resume migration
   */
  resume(): void {
    this.isPaused = false
    this.progress.isPaused = false
    this.progress.currentOperation = 'Migration resumed'
    this.updateProgress()
  }

  /**
   * Cancel migration
   */
  cancel(): void {
    this.isCancelled = true
    this.progress.isCancelled = true
    this.progress.currentOperation = 'Migration cancelled'
    this.updateProgress()
  }

  /**
   * Generate final migration result
   */
  private generateMigrationResult(config: MigrationConfig): MigrationResult {
    const timeElapsed = Date.now() - this.progress.startTime.getTime()
    const successRate = this.progress.totalImages > 0
      ? (this.progress.successfulMigrations / this.progress.totalImages) * 100
      : 0

    return {
      success: this.progress.successfulMigrations > 0 && this.progress.errorRate < 50,
      progress: this.progress,
      errors: this.errors,
      summary: {
        totalProcessed: this.progress.processedImages,
        successRate,
        totalSizeMigrated: 0, // Would need to track this during migration
        averageSpeed: this.progress.processedImages / (timeElapsed / 1000), // images per second
        timeElapsed: timeElapsed / 1000 // seconds
      },
      backupData: config.backupOriginalUrls ? this.backupData : undefined
    }
  }

  /**
   * Export migration results to JSON
   */
  exportResults(result: MigrationResult): string {
    return JSON.stringify(result, null, 2)
  }

  /**
   * Save migration results to file
   */
  async saveMigrationResults(result: MigrationResult, fileName?: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const defaultFileName = `migration-results-${timestamp}.json`

    try {
      const data = this.exportResults(result)

      if (typeof window !== 'undefined') {
        // Browser environment
        const blob = new Blob([data], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = fileName || defaultFileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      } else {
        // Node.js environment
        const fs = await import('fs')
        fs.writeFileSync(fileName || defaultFileName, data)
      }
    } catch (error) {
      throw new Error(`Failed to save migration results: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
