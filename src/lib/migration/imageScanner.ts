/**
 * Image Scanner and Inventory System
 * Scans Firestore collections to identify all images for migration
 */

import { 
  collection, 
  getDocs, 
  query, 
  orderBy, 
  limit, 
  startAfter,
  DocumentSnapshot,
  QueryDocumentSnapshot,
  DocumentData 
} from 'firebase/firestore'
import { db } from '../firebase/config'

export interface ImageReference {
  id: string
  collection: string
  documentId: string
  fieldPath: string
  url: string
  fileName?: string
  contentType?: string
  size?: number
  uploadedAt?: string
  metadata?: Record<string, any>
}

export interface CollectionScanConfig {
  name: string
  imageFields: string[]
  batchSize?: number
  orderByField?: string
  filters?: any[]
}

export interface ScanProgress {
  totalCollections: number
  completedCollections: number
  currentCollection: string
  totalDocuments: number
  scannedDocuments: number
  foundImages: number
  errors: number
  startTime: Date
  estimatedCompletion?: Date
}

export interface ScanResult {
  images: ImageReference[]
  progress: ScanProgress
  errors: Array<{
    collection: string
    documentId?: string
    error: string
    timestamp: Date
  }>
  summary: {
    totalImages: number
    imagesByCollection: Record<string, number>
    imagesByType: Record<string, number>
    totalSize: number
    oldestImage?: Date
    newestImage?: Date
  }
}

export class ImageScanner {
  private progress: ScanProgress
  private images: ImageReference[] = []
  private errors: Array<{ collection: string; documentId?: string; error: string; timestamp: Date }> = []
  private onProgressUpdate?: (progress: ScanProgress) => void

  constructor(onProgressUpdate?: (progress: ScanProgress) => void) {
    this.onProgressUpdate = onProgressUpdate
    this.progress = {
      totalCollections: 0,
      completedCollections: 0,
      currentCollection: '',
      totalDocuments: 0,
      scannedDocuments: 0,
      foundImages: 0,
      errors: 0,
      startTime: new Date()
    }
  }

  /**
   * Scan all configured collections for images
   */
  async scanAllCollections(collections: CollectionScanConfig[]): Promise<ScanResult> {
    this.progress.totalCollections = collections.length
    this.progress.startTime = new Date()
    
    try {
      // First pass: count total documents
      await this.countTotalDocuments(collections)
      
      // Second pass: scan for images
      for (const collectionConfig of collections) {
        this.progress.currentCollection = collectionConfig.name
        await this.scanCollection(collectionConfig)
        this.progress.completedCollections++
        this.updateProgress()
      }

      return this.generateScanResult()
    } catch (error) {
      this.addError('scanner', undefined, error instanceof Error ? error.message : 'Unknown scanning error')
      throw error
    }
  }

  /**
   * Scan a specific collection for images
   */
  async scanCollection(config: CollectionScanConfig): Promise<ImageReference[]> {
    const collectionImages: ImageReference[] = []
    const batchSize = config.batchSize || 100
    let lastDoc: QueryDocumentSnapshot<DocumentData> | null = null
    let hasMore = true

    try {
      while (hasMore) {
        // Build query
        let q = query(
          collection(db, config.name),
          orderBy(config.orderByField || '__name__'),
          limit(batchSize)
        )

        if (lastDoc) {
          q = query(
            collection(db, config.name),
            orderBy(config.orderByField || '__name__'),
            startAfter(lastDoc),
            limit(batchSize)
          )
        }

        const snapshot = await getDocs(q)
        
        if (snapshot.empty) {
          hasMore = false
          break
        }

        // Process documents in batch
        for (const doc of snapshot.docs) {
          try {
            const docImages = this.extractImagesFromDocument(
              config.name,
              doc.id,
              doc.data(),
              config.imageFields
            )
            collectionImages.push(...docImages)
            this.images.push(...docImages)
            this.progress.foundImages += docImages.length
          } catch (error) {
            this.addError(config.name, doc.id, error instanceof Error ? error.message : 'Document processing error')
          }
          
          this.progress.scannedDocuments++
        }

        lastDoc = snapshot.docs[snapshot.docs.length - 1]
        this.updateProgress()

        // Check if we got fewer docs than requested (end of collection)
        if (snapshot.docs.length < batchSize) {
          hasMore = false
        }
      }
    } catch (error) {
      this.addError(config.name, undefined, error instanceof Error ? error.message : 'Collection scanning error')
      throw error
    }

    return collectionImages
  }

  /**
   * Extract image URLs from a document
   */
  private extractImagesFromDocument(
    collectionName: string,
    documentId: string,
    data: DocumentData,
    imageFields: string[]
  ): ImageReference[] {
    const images: ImageReference[] = []

    for (const fieldPath of imageFields) {
      try {
        const value = this.getNestedValue(data, fieldPath)
        
        if (value) {
          if (Array.isArray(value)) {
            // Handle array of image URLs
            value.forEach((url, index) => {
              if (this.isFirebaseStorageUrl(url)) {
                images.push(this.createImageReference(
                  collectionName,
                  documentId,
                  `${fieldPath}[${index}]`,
                  url
                ))
              }
            })
          } else if (typeof value === 'string' && this.isFirebaseStorageUrl(value)) {
            // Handle single image URL
            images.push(this.createImageReference(
              collectionName,
              documentId,
              fieldPath,
              value
            ))
          } else if (typeof value === 'object' && value !== null) {
            // Handle object with image properties
            const imageUrl = value.url || value.downloadURL || value.src
            if (imageUrl && this.isFirebaseStorageUrl(imageUrl)) {
              images.push(this.createImageReference(
                collectionName,
                documentId,
                fieldPath,
                imageUrl,
                {
                  fileName: value.name || value.fileName,
                  contentType: value.contentType || value.type,
                  size: value.size,
                  uploadedAt: value.uploadedAt || value.createdAt,
                  metadata: value.metadata
                }
              ))
            }
          }
        }
      } catch (error) {
        this.addError(
          collectionName,
          documentId,
          `Error processing field ${fieldPath}: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      }
    }

    return images
  }

  /**
   * Create an image reference object
   */
  private createImageReference(
    collection: string,
    documentId: string,
    fieldPath: string,
    url: string,
    additionalData?: {
      fileName?: string
      contentType?: string
      size?: number
      uploadedAt?: string
      metadata?: Record<string, any>
    }
  ): ImageReference {
    return {
      id: `${collection}_${documentId}_${fieldPath}`,
      collection,
      documentId,
      fieldPath,
      url,
      fileName: additionalData?.fileName || this.extractFileNameFromUrl(url),
      contentType: additionalData?.contentType || this.guessContentTypeFromUrl(url),
      size: additionalData?.size,
      uploadedAt: additionalData?.uploadedAt,
      metadata: additionalData?.metadata
    }
  }

  /**
   * Check if URL is a Firebase Storage URL
   */
  private isFirebaseStorageUrl(url: string): boolean {
    if (typeof url !== 'string') return false
    
    return url.includes('firebasestorage.googleapis.com') ||
           url.includes('firebase.google.com') ||
           url.includes('appspot.com')
  }

  /**
   * Extract file name from Firebase Storage URL
   */
  private extractFileNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      const pathParts = urlObj.pathname.split('/')
      const fileName = pathParts[pathParts.length - 1]
      return decodeURIComponent(fileName.split('?')[0])
    } catch {
      return 'unknown'
    }
  }

  /**
   * Guess content type from URL
   */
  private guessContentTypeFromUrl(url: string): string {
    const fileName = this.extractFileNameFromUrl(url).toLowerCase()
    
    if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) return 'image/jpeg'
    if (fileName.endsWith('.png')) return 'image/png'
    if (fileName.endsWith('.gif')) return 'image/gif'
    if (fileName.endsWith('.webp')) return 'image/webp'
    if (fileName.endsWith('.svg')) return 'image/svg+xml'
    
    return 'image/unknown'
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null
    }, obj)
  }

  /**
   * Count total documents across all collections
   */
  private async countTotalDocuments(collections: CollectionScanConfig[]): Promise<void> {
    let total = 0
    
    for (const config of collections) {
      try {
        // For counting, we'll use a simple query to get document count
        // Note: In production, you might want to use aggregation queries if available
        const snapshot = await getDocs(collection(db, config.name))
        total += snapshot.size
      } catch (error) {
        this.addError(config.name, undefined, `Error counting documents: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
    
    this.progress.totalDocuments = total
  }

  /**
   * Add error to error list
   */
  private addError(collection: string, documentId?: string, error?: string): void {
    this.errors.push({
      collection,
      documentId,
      error: error || 'Unknown error',
      timestamp: new Date()
    })
    this.progress.errors++
  }

  /**
   * Update progress and notify listeners
   */
  private updateProgress(): void {
    // Calculate estimated completion time
    if (this.progress.scannedDocuments > 0) {
      const elapsed = Date.now() - this.progress.startTime.getTime()
      const rate = this.progress.scannedDocuments / elapsed
      const remaining = this.progress.totalDocuments - this.progress.scannedDocuments
      const estimatedMs = remaining / rate
      this.progress.estimatedCompletion = new Date(Date.now() + estimatedMs)
    }

    if (this.onProgressUpdate) {
      this.onProgressUpdate({ ...this.progress })
    }
  }

  /**
   * Generate final scan result
   */
  private generateScanResult(): ScanResult {
    const imagesByCollection: Record<string, number> = {}
    const imagesByType: Record<string, number> = {}
    let totalSize = 0
    let oldestImage: Date | undefined
    let newestImage: Date | undefined

    this.images.forEach(image => {
      // Count by collection
      imagesByCollection[image.collection] = (imagesByCollection[image.collection] || 0) + 1
      
      // Count by type
      const type = image.contentType || 'unknown'
      imagesByType[type] = (imagesByType[type] || 0) + 1
      
      // Sum total size
      if (image.size) {
        totalSize += image.size
      }
      
      // Track oldest and newest
      if (image.uploadedAt) {
        const uploadDate = new Date(image.uploadedAt)
        if (!oldestImage || uploadDate < oldestImage) {
          oldestImage = uploadDate
        }
        if (!newestImage || uploadDate > newestImage) {
          newestImage = uploadDate
        }
      }
    })

    return {
      images: this.images,
      progress: this.progress,
      errors: this.errors,
      summary: {
        totalImages: this.images.length,
        imagesByCollection,
        imagesByType,
        totalSize,
        oldestImage,
        newestImage
      }
    }
  }

  /**
   * Export scan results to JSON
   */
  exportResults(result: ScanResult): string {
    return JSON.stringify(result, null, 2)
  }

  /**
   * Save scan results to file
   */
  async saveScanResults(result: ScanResult, fileName?: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const defaultFileName = `image-scan-results-${timestamp}.json`
    
    try {
      // In a browser environment, this would trigger a download
      // In Node.js, this would save to the file system
      const data = this.exportResults(result)
      
      if (typeof window !== 'undefined') {
        // Browser environment
        const blob = new Blob([data], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = fileName || defaultFileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      } else {
        // Node.js environment (for server-side scanning)
        const fs = await import('fs')
        fs.writeFileSync(fileName || defaultFileName, data)
      }
    } catch (error) {
      throw new Error(`Failed to save scan results: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
