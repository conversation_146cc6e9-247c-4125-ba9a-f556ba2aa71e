/**
 * Migration Monitoring and Reporting System
 * Real-time monitoring with progress reports and error alerts
 */

import { MigrationProgress, MigrationResult, MigrationError } from './imageMigrator'
import { ScanProgress } from './imageScanner'
import { UpdateProgress } from './databaseUpdater'
import { r2PerformanceMonitor } from '../cloudflare/r2PerformanceMonitor'

export interface MigrationSession {
  id: string
  startTime: Date
  endTime?: Date
  status: 'scanning' | 'migrating' | 'updating' | 'completed' | 'failed' | 'cancelled'
  phase: 'scan' | 'backup' | 'migrate' | 'update' | 'validate' | 'cleanup'
  totalImages: number
  processedImages: number
  successRate: number
  errorRate: number
  estimatedCompletion?: Date
  currentOperation: string
}

export interface MigrationAlert {
  id: string
  sessionId: string
  timestamp: Date
  level: 'info' | 'warning' | 'error' | 'critical'
  type: 'progress' | 'error' | 'performance' | 'system'
  title: string
  message: string
  data?: any
  acknowledged: boolean
  autoResolve: boolean
}

export interface MigrationReport {
  sessionId: string
  timestamp: Date
  reportType: 'progress' | 'summary' | 'error' | 'performance'
  data: any
  format: 'json' | 'html' | 'markdown'
}

export interface PerformanceMetrics {
  timestamp: Date
  imagesPerSecond: number
  averageUploadTime: number
  averageDownloadTime: number
  errorRate: number
  memoryUsage: number
  networkThroughput: number
  r2Performance: any
}

export class MigrationMonitor {
  private sessions = new Map<string, MigrationSession>()
  private alerts = new Map<string, MigrationAlert>()
  private reports: MigrationReport[] = []
  private performanceHistory: PerformanceMetrics[] = []
  private listeners = new Map<string, Set<(data: any) => void>>()
  private alertThresholds = {
    errorRate: 5, // 5%
    slowPerformance: 2000, // 2 seconds per image
    memoryUsage: 80, // 80%
    networkTimeout: 30000 // 30 seconds
  }

  /**
   * Start monitoring a migration session
   */
  startSession(sessionId: string): MigrationSession {
    const session: MigrationSession = {
      id: sessionId,
      startTime: new Date(),
      status: 'scanning',
      phase: 'scan',
      totalImages: 0,
      processedImages: 0,
      successRate: 0,
      errorRate: 0,
      currentOperation: 'Initializing migration session'
    }

    this.sessions.set(sessionId, session)
    this.emit('session:started', session)
    
    // Start performance monitoring
    this.startPerformanceMonitoring(sessionId)
    
    return session
  }

  /**
   * Update session with scan progress
   */
  updateScanProgress(sessionId: string, progress: ScanProgress): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    session.status = 'scanning'
    session.phase = 'scan'
    session.totalImages = progress.foundImages
    session.currentOperation = `Scanning ${progress.currentCollection} (${progress.scannedDocuments}/${progress.totalDocuments})`
    
    if (progress.estimatedCompletion) {
      session.estimatedCompletion = progress.estimatedCompletion
    }

    this.sessions.set(sessionId, session)
    this.emit('scan:progress', { sessionId, progress })

    // Check for alerts
    this.checkScanAlerts(sessionId, progress)
  }

  /**
   * Update session with migration progress
   */
  updateMigrationProgress(sessionId: string, progress: MigrationProgress): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    session.status = 'migrating'
    session.phase = 'migrate'
    session.totalImages = progress.totalImages
    session.processedImages = progress.processedImages
    session.successRate = progress.totalImages > 0 
      ? (progress.successfulMigrations / progress.totalImages) * 100 
      : 0
    session.errorRate = progress.errorRate
    session.currentOperation = progress.currentOperation
    session.estimatedCompletion = progress.estimatedCompletion

    this.sessions.set(sessionId, session)
    this.emit('migration:progress', { sessionId, progress })

    // Check for alerts
    this.checkMigrationAlerts(sessionId, progress)

    // Generate progress report
    if (progress.processedImages % 100 === 0) { // Every 100 images
      this.generateProgressReport(sessionId, progress)
    }
  }

  /**
   * Update session with database update progress
   */
  updateDatabaseProgress(sessionId: string, progress: UpdateProgress): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    session.status = 'updating'
    session.phase = 'update'
    session.currentOperation = progress.currentOperation
    session.estimatedCompletion = progress.estimatedCompletion

    this.sessions.set(sessionId, session)
    this.emit('database:progress', { sessionId, progress })
  }

  /**
   * Complete migration session
   */
  completeSession(sessionId: string, result: MigrationResult): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    session.endTime = new Date()
    session.status = result.success ? 'completed' : 'failed'
    session.phase = 'cleanup'
    session.successRate = result.summary.successRate
    session.errorRate = (result.errors.length / result.progress.totalImages) * 100
    session.currentOperation = result.success ? 'Migration completed successfully' : 'Migration failed'

    this.sessions.set(sessionId, session)
    this.emit('session:completed', { sessionId, result })

    // Generate final report
    this.generateSummaryReport(sessionId, result)

    // Stop performance monitoring
    this.stopPerformanceMonitoring(sessionId)
  }

  /**
   * Record migration error
   */
  recordError(sessionId: string, error: MigrationError): void {
    const alert = this.createAlert(
      sessionId,
      'error',
      'error',
      'Migration Error',
      `Error in ${error.collection}/${error.documentId}: ${error.error}`,
      error
    )

    this.addAlert(alert)
    this.emit('error:recorded', { sessionId, error, alert })
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(sessionId: string): void {
    const interval = setInterval(() => {
      this.collectPerformanceMetrics(sessionId)
    }, 10000) // Every 10 seconds

    // Store interval for cleanup
    ;(this as any)[`interval_${sessionId}`] = interval
  }

  /**
   * Stop performance monitoring
   */
  private stopPerformanceMonitoring(sessionId: string): void {
    const interval = (this as any)[`interval_${sessionId}`]
    if (interval) {
      clearInterval(interval)
      delete (this as any)[`interval_${sessionId}`]
    }
  }

  /**
   * Collect performance metrics
   */
  private collectPerformanceMetrics(sessionId: string): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    const r2Stats = r2PerformanceMonitor.getPerformanceSummary(1) // Last 1 minute
    
    const metrics: PerformanceMetrics = {
      timestamp: new Date(),
      imagesPerSecond: this.calculateImagesPerSecond(session),
      averageUploadTime: r2Stats.averageLatency,
      averageDownloadTime: 0, // Would need to track this separately
      errorRate: session.errorRate,
      memoryUsage: this.getMemoryUsage(),
      networkThroughput: r2Stats.throughputMBps,
      r2Performance: r2Stats
    }

    this.performanceHistory.push(metrics)
    
    // Keep only last 1000 metrics
    if (this.performanceHistory.length > 1000) {
      this.performanceHistory.shift()
    }

    this.emit('performance:metrics', { sessionId, metrics })

    // Check performance alerts
    this.checkPerformanceAlerts(sessionId, metrics)
  }

  /**
   * Check for scan-related alerts
   */
  private checkScanAlerts(sessionId: string, progress: ScanProgress): void {
    // Check for high error rate during scanning
    if (progress.errors > 0 && progress.scannedDocuments > 100) {
      const errorRate = (progress.errors / progress.scannedDocuments) * 100
      if (errorRate > 10) {
        this.createAlert(
          sessionId,
          'warning',
          'error',
          'High Scan Error Rate',
          `Scan error rate is ${errorRate.toFixed(1)}% (${progress.errors} errors in ${progress.scannedDocuments} documents)`
        )
      }
    }

    // Check for slow scanning
    if (progress.estimatedCompletion) {
      const remainingTime = progress.estimatedCompletion.getTime() - Date.now()
      if (remainingTime > 3600000) { // More than 1 hour
        this.createAlert(
          sessionId,
          'info',
          'performance',
          'Slow Scanning Progress',
          `Estimated completion time is more than 1 hour (${Math.round(remainingTime / 60000)} minutes)`
        )
      }
    }
  }

  /**
   * Check for migration-related alerts
   */
  private checkMigrationAlerts(sessionId: string, progress: MigrationProgress): void {
    // High error rate alert
    if (progress.errorRate > this.alertThresholds.errorRate) {
      this.createAlert(
        sessionId,
        'error',
        'error',
        'High Error Rate',
        `Migration error rate is ${progress.errorRate.toFixed(1)}% (threshold: ${this.alertThresholds.errorRate}%)`
      )
    }

    // Migration paused alert
    if (progress.isPaused) {
      this.createAlert(
        sessionId,
        'warning',
        'system',
        'Migration Paused',
        'Migration has been paused due to high error rate or manual intervention'
      )
    }

    // Migration cancelled alert
    if (progress.isCancelled) {
      this.createAlert(
        sessionId,
        'critical',
        'system',
        'Migration Cancelled',
        'Migration has been cancelled by user or system'
      )
    }
  }

  /**
   * Check for performance-related alerts
   */
  private checkPerformanceAlerts(sessionId: string, metrics: PerformanceMetrics): void {
    // Slow performance alert
    if (metrics.averageUploadTime > this.alertThresholds.slowPerformance) {
      this.createAlert(
        sessionId,
        'warning',
        'performance',
        'Slow Upload Performance',
        `Average upload time is ${metrics.averageUploadTime.toFixed(0)}ms (threshold: ${this.alertThresholds.slowPerformance}ms)`
      )
    }

    // High memory usage alert
    if (metrics.memoryUsage > this.alertThresholds.memoryUsage) {
      this.createAlert(
        sessionId,
        'warning',
        'system',
        'High Memory Usage',
        `Memory usage is ${metrics.memoryUsage.toFixed(1)}% (threshold: ${this.alertThresholds.memoryUsage}%)`
      )
    }

    // Low throughput alert
    if (metrics.networkThroughput < 1) { // Less than 1 MB/s
      this.createAlert(
        sessionId,
        'info',
        'performance',
        'Low Network Throughput',
        `Network throughput is ${metrics.networkThroughput.toFixed(2)} MB/s`
      )
    }
  }

  /**
   * Create alert
   */
  private createAlert(
    sessionId: string,
    level: 'info' | 'warning' | 'error' | 'critical',
    type: 'progress' | 'error' | 'performance' | 'system',
    title: string,
    message: string,
    data?: any
  ): MigrationAlert {
    const alert: MigrationAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sessionId,
      timestamp: new Date(),
      level,
      type,
      title,
      message,
      data,
      acknowledged: false,
      autoResolve: level === 'info'
    }

    return alert
  }

  /**
   * Add alert
   */
  private addAlert(alert: MigrationAlert): void {
    this.alerts.set(alert.id, alert)
    this.emit('alert:created', alert)

    // Auto-resolve info alerts after 5 minutes
    if (alert.autoResolve) {
      setTimeout(() => {
        this.resolveAlert(alert.id)
      }, 300000)
    }
  }

  /**
   * Generate progress report
   */
  private generateProgressReport(sessionId: string, progress: MigrationProgress): void {
    const report: MigrationReport = {
      sessionId,
      timestamp: new Date(),
      reportType: 'progress',
      data: {
        progress,
        performance: this.getRecentPerformanceMetrics(),
        alerts: this.getActiveAlerts(sessionId)
      },
      format: 'json'
    }

    this.reports.push(report)
    this.emit('report:generated', report)
  }

  /**
   * Generate summary report
   */
  private generateSummaryReport(sessionId: string, result: MigrationResult): void {
    const session = this.sessions.get(sessionId)
    const allAlerts = Array.from(this.alerts.values()).filter(a => a.sessionId === sessionId)
    
    const report: MigrationReport = {
      sessionId,
      timestamp: new Date(),
      reportType: 'summary',
      data: {
        session,
        result,
        alerts: allAlerts,
        performance: this.performanceHistory.slice(-100), // Last 100 metrics
        r2Performance: r2PerformanceMonitor.generateReport(60)
      },
      format: 'markdown'
    }

    this.reports.push(report)
    this.emit('report:generated', report)
  }

  /**
   * Utility methods
   */
  private calculateImagesPerSecond(session: MigrationSession): number {
    if (!session.startTime || session.processedImages === 0) return 0
    
    const elapsed = (Date.now() - session.startTime.getTime()) / 1000
    return session.processedImages / elapsed
  }

  private getMemoryUsage(): number {
    // In a real implementation, this would get actual memory usage
    // For now, return a mock value
    return Math.random() * 100
  }

  private getRecentPerformanceMetrics(): PerformanceMetrics[] {
    return this.performanceHistory.slice(-10) // Last 10 metrics
  }

  private getActiveAlerts(sessionId: string): MigrationAlert[] {
    return Array.from(this.alerts.values())
      .filter(alert => alert.sessionId === sessionId && !alert.acknowledged)
  }

  /**
   * Public API methods
   */
  getSession(sessionId: string): MigrationSession | undefined {
    return this.sessions.get(sessionId)
  }

  getAllSessions(): MigrationSession[] {
    return Array.from(this.sessions.values())
  }

  getAlerts(sessionId?: string): MigrationAlert[] {
    const alerts = Array.from(this.alerts.values())
    return sessionId ? alerts.filter(a => a.sessionId === sessionId) : alerts
  }

  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId)
    if (alert) {
      alert.acknowledged = true
      this.alerts.set(alertId, alert)
      this.emit('alert:acknowledged', alert)
      return true
    }
    return false
  }

  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId)
    if (alert) {
      this.alerts.delete(alertId)
      this.emit('alert:resolved', alert)
      return true
    }
    return false
  }

  getReports(sessionId?: string): MigrationReport[] {
    return sessionId 
      ? this.reports.filter(r => r.sessionId === sessionId)
      : this.reports
  }

  getPerformanceHistory(sessionId?: string, limit: number = 100): PerformanceMetrics[] {
    return this.performanceHistory.slice(-limit)
  }

  /**
   * Event system
   */
  on(event: string, callback: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(callback)
  }

  off(event: string, callback: (data: any) => void): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.delete(callback)
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error)
        }
      })
    }
  }

  /**
   * Export monitoring data
   */
  exportData(sessionId?: string): string {
    const data = {
      sessions: sessionId ? [this.getSession(sessionId)] : this.getAllSessions(),
      alerts: this.getAlerts(sessionId),
      reports: this.getReports(sessionId),
      performance: this.getPerformanceHistory(sessionId)
    }

    return JSON.stringify(data, null, 2)
  }
}

// Export singleton instance
export const migrationMonitor = new MigrationMonitor()
