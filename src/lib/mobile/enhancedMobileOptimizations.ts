/**
 * Enhanced Mobile Optimizations
 * 
 * Advanced mobile optimizations including PWA features, touch gestures,
 * performance monitoring, and mobile-specific enhancements.
 * 
 * Phase 3 Advanced Optimizations - Community System Audit
 * 
 * <AUTHOR> Team
 */

interface MobileCapabilities {
  touchSupport: boolean
  orientationSupport: boolean
  vibrationSupport: boolean
  deviceMotionSupport: boolean
  networkInformation: boolean
  batteryAPI: boolean
  isStandalone: boolean
  installPromptAvailable: boolean
}

interface PWAInstallPrompt {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

export class EnhancedMobileOptimizer {
  private static capabilities: MobileCapabilities | null = null
  private static installPrompt: PWAInstallPrompt | null = null
  private static performanceObserver: PerformanceObserver | null = null

  /**
   * Initialize mobile optimizations
   */
  static async initialize(): Promise<void> {
    if (typeof window === 'undefined') return

    // Detect capabilities
    this.capabilities = this.detectCapabilities()
    
    // Setup PWA install prompt
    this.setupPWAInstallPrompt()
    
    // Setup performance monitoring
    this.setupPerformanceMonitoring()
    
    // Apply mobile-specific optimizations
    this.applyMobileOptimizations()
    
    // Setup orientation handling
    this.setupOrientationHandling()
    
    // Setup network monitoring
    this.setupNetworkMonitoring()
  }

  /**
   * Detect mobile device capabilities
   */
  private static detectCapabilities(): MobileCapabilities {
    return {
      touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      orientationSupport: 'orientation' in window || 'onorientationchange' in window,
      vibrationSupport: 'vibrate' in navigator,
      deviceMotionSupport: 'DeviceMotionEvent' in window,
      networkInformation: 'connection' in navigator,
      batteryAPI: 'getBattery' in navigator,
      isStandalone: window.matchMedia('(display-mode: standalone)').matches,
      installPromptAvailable: false // Will be updated when prompt is available
    }
  }

  /**
   * Setup PWA install prompt handling
   */
  private static setupPWAInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault()
      this.installPrompt = e as any
      if (this.capabilities) {
        this.capabilities.installPromptAvailable = true
      }
      
      // Dispatch custom event for components to listen
      window.dispatchEvent(new CustomEvent('pwa-install-available'))
    })

    // Listen for app installation
    window.addEventListener('appinstalled', () => {
      this.installPrompt = null
      if (this.capabilities) {
        this.capabilities.installPromptAvailable = false
      }
      
      window.dispatchEvent(new CustomEvent('pwa-installed'))
    })
  }

  /**
   * Trigger PWA install prompt
   */
  static async triggerPWAInstall(): Promise<'accepted' | 'dismissed' | 'unavailable'> {
    if (!this.installPrompt) {
      return 'unavailable'
    }

    try {
      await this.installPrompt.prompt()
      const choice = await this.installPrompt.userChoice
      return choice.outcome
    } catch (error) {
      console.error('PWA install failed:', error)
      return 'unavailable'
    }
  }

  /**
   * Setup performance monitoring for mobile
   */
  private static setupPerformanceMonitoring(): void {
    if (!('PerformanceObserver' in window)) return

    // Monitor Long Tasks (> 50ms)
    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'longtask') {
            console.warn('Long task detected:', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            })
            
            // Dispatch event for monitoring
            window.dispatchEvent(new CustomEvent('mobile-performance-warning', {
              detail: { type: 'longtask', duration: entry.duration }
            }))
          }
        }
      })

      this.performanceObserver.observe({ entryTypes: ['longtask'] })
    } catch (error) {
      console.warn('Performance monitoring not available:', error)
    }
  }

  /**
   * Apply mobile-specific optimizations
   */
  private static applyMobileOptimizations(): void {
    // Disable hover effects on touch devices
    if (this.capabilities?.touchSupport) {
      document.documentElement.classList.add('touch-device')
      
      // Add CSS to disable hover on touch devices
      const style = document.createElement('style')
      style.textContent = `
        @media (hover: none) and (pointer: coarse) {
          .hover\\:scale-105:hover { transform: none !important; }
          .hover\\:bg-gray-800:hover { background-color: inherit !important; }
          .hover\\:text-accent-400:hover { color: inherit !important; }
        }
      `
      document.head.appendChild(style)
    }

    // Optimize scrolling performance
    document.documentElement.style.setProperty('-webkit-overflow-scrolling', 'touch')
    
    // Prevent zoom on input focus (iOS)
    const viewport = document.querySelector('meta[name="viewport"]')
    if (viewport && this.isMobileDevice()) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
      )
    }

    // Add mobile-specific event listeners
    this.setupMobileEventListeners()
  }

  /**
   * Setup mobile-specific event listeners
   */
  private static setupMobileEventListeners(): void {
    // Prevent pull-to-refresh on iOS
    document.addEventListener('touchstart', (e) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }, { passive: false })

    // Handle visibility changes (app backgrounding)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // App went to background - pause non-critical operations
        window.dispatchEvent(new CustomEvent('app-backgrounded'))
      } else {
        // App came to foreground - resume operations
        window.dispatchEvent(new CustomEvent('app-foregrounded'))
      }
    })

    // Handle memory warnings (iOS)
    window.addEventListener('pagehide', () => {
      // Clean up resources before page unload
      window.dispatchEvent(new CustomEvent('memory-cleanup-needed'))
    })
  }

  /**
   * Setup orientation handling
   */
  private static setupOrientationHandling(): void {
    if (!this.capabilities?.orientationSupport) return

    const handleOrientationChange = () => {
      // Delay to allow for orientation change to complete
      setTimeout(() => {
        const orientation = screen.orientation?.angle || window.orientation || 0
        const isLandscape = Math.abs(orientation) === 90
        
        window.dispatchEvent(new CustomEvent('orientation-changed', {
          detail: { orientation, isLandscape }
        }))
        
        // Update CSS custom property
        document.documentElement.style.setProperty(
          '--orientation', 
          isLandscape ? 'landscape' : 'portrait'
        )
      }, 100)
    }

    if ('orientation' in screen) {
      screen.orientation.addEventListener('change', handleOrientationChange)
    } else {
      window.addEventListener('orientationchange', handleOrientationChange)
    }

    // Initial orientation
    handleOrientationChange()
  }

  /**
   * Setup network monitoring
   */
  private static setupNetworkMonitoring(): void {
    if (!this.capabilities?.networkInformation) return

    const connection = (navigator as any).connection
    if (!connection) return

    const updateNetworkInfo = () => {
      const networkInfo = {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      }

      window.dispatchEvent(new CustomEvent('network-changed', {
        detail: networkInfo
      }))

      // Apply optimizations based on connection
      if (connection.saveData || connection.effectiveType === 'slow-2g') {
        document.documentElement.classList.add('data-saver-mode')
      } else {
        document.documentElement.classList.remove('data-saver-mode')
      }
    }

    connection.addEventListener('change', updateNetworkInfo)
    updateNetworkInfo() // Initial check
  }

  /**
   * Optimize images for mobile
   */
  static optimizeImagesForMobile(): void {
    const images = document.querySelectorAll('img[data-mobile-optimize]')
    
    images.forEach((img: Element) => {
      const imgElement = img as HTMLImageElement
      
      // Use smaller images on mobile
      if (this.isMobileDevice() && imgElement.dataset.mobileSrc) {
        imgElement.src = imgElement.dataset.mobileSrc
      }
      
      // Add intersection observer for lazy loading
      if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              if (img.dataset.src) {
                img.src = img.dataset.src
                img.removeAttribute('data-src')
                observer.unobserve(img)
              }
            }
          })
        }, { rootMargin: '50px' })
        
        observer.observe(imgElement)
      }
    })
  }

  /**
   * Enable haptic feedback
   */
  static hapticFeedback(type: 'light' | 'medium' | 'heavy' = 'light'): void {
    if (!this.capabilities?.vibrationSupport) return

    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30]
    }

    navigator.vibrate(patterns[type])
  }

  /**
   * Get device information
   */
  static getDeviceInfo(): {
    isMobile: boolean
    isTablet: boolean
    isDesktop: boolean
    userAgent: string
    screenSize: { width: number; height: number }
    pixelRatio: number
    capabilities: MobileCapabilities | null
  } {
    return {
      isMobile: this.isMobileDevice(),
      isTablet: this.isTabletDevice(),
      isDesktop: !this.isMobileDevice() && !this.isTabletDevice(),
      userAgent: navigator.userAgent,
      screenSize: {
        width: window.screen.width,
        height: window.screen.height
      },
      pixelRatio: window.devicePixelRatio || 1,
      capabilities: this.capabilities
    }
  }

  /**
   * Check if device is mobile
   */
  private static isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           window.innerWidth <= 768
  }

  /**
   * Check if device is tablet
   */
  private static isTabletDevice(): boolean {
    return /iPad|Android/i.test(navigator.userAgent) && 
           window.innerWidth > 768 && 
           window.innerWidth <= 1024
  }

  /**
   * Cleanup resources
   */
  static cleanup(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect()
      this.performanceObserver = null
    }
  }
}

// Auto-initialize on import
if (typeof window !== 'undefined') {
  EnhancedMobileOptimizer.initialize()
}

export default EnhancedMobileOptimizer
