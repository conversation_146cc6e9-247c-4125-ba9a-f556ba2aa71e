/**
 * Hybrid Performance Monitor for Cloudflare Deployment
 * Comprehensive monitoring system for performance metrics, alerts, and reporting
 */

import { shouldUseFeature } from '../config/featureFlags'

// Enhanced performance metric interfaces
export interface PerformanceMetric {
  timestamp: string
  metricType: 'response_time' | 'cache_hit_ratio' | 'error_rate' | 'bandwidth' | 'core_web_vitals' | 'r2_operation' | 'storage_throughput' | 'storage_latency'
  value: number
  unit: string
  source: 'cloudflare' | 'firebase' | 'client' | 'worker'
  metadata?: Record<string, any>
}

export interface CoreWebVitals {
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  fcp: number // First Contentful Paint
  ttfb: number // Time to First Byte
  timestamp: string
  url: string
  userAgent?: string
}

export interface R2OperationMetrics {
  operation: 'upload' | 'download' | 'delete' | 'list' | 'metadata' | 'presigned_url'
  bucketType: string
  fileSize?: number
  contentType?: string
  duration: number
  success: boolean
  error?: string
  throughput?: number // bytes per second
  retryCount?: number
  fallbackUsed?: boolean
}

export interface R2PerformanceStats {
  totalOperations: number
  successRate: number
  averageResponseTime: number
  averageThroughput: number
  operationBreakdown: Record<string, number>
  bucketStats: Record<string, {
    operations: number
    averageSize: number
    successRate: number
  }>
  errorBreakdown: Record<string, number>
  fallbackRate: number
}

export interface AlertRule {
  id: string
  name: string
  metricType: string
  condition: 'greater_than' | 'less_than' | 'equals' | 'not_equals'
  threshold: number
  duration: number // minutes
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  channels: ('email' | 'slack' | 'webhook')[]
  metadata?: Record<string, any>
}

export interface Alert {
  id: string
  ruleId: string
  ruleName: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: string
  resolved: boolean
  resolvedAt?: string
  metadata?: Record<string, any>
}

export interface PerformanceReport {
  period: string
  startTime: string
  endTime: string
  metrics: {
    avgResponseTime: number
    cacheHitRatio: number
    errorRate: number
    totalRequests: number
    bandwidth: number
    coreWebVitals: {
      avgLCP: number
      avgFID: number
      avgCLS: number
      avgFCP: number
      avgTTFB: number
    }
  }
  trends: {
    responseTime: 'improving' | 'degrading' | 'stable'
    cacheHitRatio: 'improving' | 'degrading' | 'stable'
    errorRate: 'improving' | 'degrading' | 'stable'
  }
  alerts: Alert[]
  recommendations: string[]
}

export interface PerformanceAlert {
  level: 'info' | 'warning' | 'critical'
  message: string
  metric: string
  threshold: number
  currentValue: number
}

// Main performance monitor class
export class HybridPerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private alerts: Alert[] = []
  private alertRules: AlertRule[] = []
  private isMonitoring = false
  private monitoringInterval?: NodeJS.Timeout

  constructor() {
    this.initializeDefaultAlertRules()
  }

  // Initialize default alert rules
  private initializeDefaultAlertRules(): void {
    this.alertRules = [
      {
        id: 'high_response_time',
        name: 'High Response Time',
        metricType: 'response_time',
        condition: 'greater_than',
        threshold: 2000,
        duration: 5,
        severity: 'high',
        enabled: true,
        channels: ['email'],
      },
      {
        id: 'low_cache_hit_ratio',
        name: 'Low Cache Hit Ratio',
        metricType: 'cache_hit_ratio',
        condition: 'less_than',
        threshold: 70,
        duration: 10,
        severity: 'medium',
        enabled: true,
        channels: ['email'],
      },
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        metricType: 'error_rate',
        condition: 'greater_than',
        threshold: 5,
        duration: 5,
        severity: 'critical',
        enabled: true,
        channels: ['email', 'slack'],
      },
      {
        id: 'poor_lcp',
        name: 'Poor Largest Contentful Paint',
        metricType: 'core_web_vitals',
        condition: 'greater_than',
        threshold: 2500,
        duration: 15,
        severity: 'medium',
        enabled: true,
        channels: ['email'],
      },
    ]
  }
  // Start monitoring
  async startMonitoring(intervalMinutes: number = 5): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Performance monitoring is already running')
      return
    }

    console.log('Starting hybrid performance monitoring...')
    this.isMonitoring = true

    // Initial metrics collection
    await this.collectMetrics()

    // Set up periodic monitoring
    this.monitoringInterval = setInterval(async () => {
      await this.collectMetrics()
      await this.checkAlerts()
      this.cleanupOldData()
    }, intervalMinutes * 60 * 1000)

    console.log(`Performance monitoring started with ${intervalMinutes} minute intervals`)
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      console.warn('Performance monitoring is not running')
      return
    }

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = undefined
    }

    this.isMonitoring = false
    console.log('Performance monitoring stopped')
  }

  // Collect performance metrics
  async collectMetrics(): Promise<void> {
    const timestamp = new Date().toISOString()

    try {
      // Collect Cloudflare metrics if feature enabled
      if (shouldUseFeature('USE_CLOUDFLARE_ANALYTICS')) {
        await this.collectCloudflareMetrics(timestamp)
      }

      // Collect Firebase metrics
      await this.collectFirebaseMetrics(timestamp)

      // Collect client-side metrics if available
      await this.collectClientMetrics(timestamp)

      console.log(`Metrics collected at ${timestamp}`)
    } catch (error) {
      console.error('Error collecting metrics:', error)
      this.addMetric({
        timestamp,
        metricType: 'error_rate',
        value: 1,
        unit: 'count',
        source: 'cloudflare',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' },
      })
    }
  }

  // Collect Cloudflare-specific metrics
  private async collectCloudflareMetrics(timestamp: string): Promise<void> {
    try {
      // Simulate Cloudflare Analytics API call
      // In production, this would use the actual Cloudflare Analytics API
      const mockCloudflareData = {
        responseTime: Math.random() * 1000 + 200,
        cacheHitRatio: Math.random() * 30 + 70,
        bandwidth: Math.random() * 1000000 + 500000,
        requests: Math.floor(Math.random() * 1000) + 100,
        errors: Math.floor(Math.random() * 10),
      }

      this.addMetric({
        timestamp,
        metricType: 'response_time',
        value: mockCloudflareData.responseTime,
        unit: 'ms',
        source: 'cloudflare',
      })

      this.addMetric({
        timestamp,
        metricType: 'cache_hit_ratio',
        value: mockCloudflareData.cacheHitRatio,
        unit: 'percentage',
        source: 'cloudflare',
      })

      this.addMetric({
        timestamp,
        metricType: 'bandwidth',
        value: mockCloudflareData.bandwidth,
        unit: 'bytes',
        source: 'cloudflare',
      })

      const errorRate = (mockCloudflareData.errors / mockCloudflareData.requests) * 100
      this.addMetric({
        timestamp,
        metricType: 'error_rate',
        value: errorRate,
        unit: 'percentage',
        source: 'cloudflare',
      })
    } catch (error) {
      console.error('Error collecting Cloudflare metrics:', error)
    }
  }

  // Collect Firebase-specific metrics
  private async collectFirebaseMetrics(timestamp: string): Promise<void> {
    try {
      // Simulate Firebase performance data
      const mockFirebaseData = {
        responseTime: Math.random() * 500 + 100,
        errorRate: Math.random() * 2,
        activeUsers: Math.floor(Math.random() * 100) + 10,
      }

      this.addMetric({
        timestamp,
        metricType: 'response_time',
        value: mockFirebaseData.responseTime,
        unit: 'ms',
        source: 'firebase',
      })

      this.addMetric({
        timestamp,
        metricType: 'error_rate',
        value: mockFirebaseData.errorRate,
        unit: 'percentage',
        source: 'firebase',
      })
    } catch (error) {
      console.error('Error collecting Firebase metrics:', error)
    }
  }

  // Collect client-side metrics
  private async collectClientMetrics(timestamp: string): Promise<void> {
    // This would typically be called from the client-side
    // For now, we'll simulate some Core Web Vitals data
    const mockCoreWebVitals = {
      lcp: Math.random() * 1000 + 1500,
      fid: Math.random() * 50 + 50,
      cls: Math.random() * 0.1 + 0.05,
      fcp: Math.random() * 500 + 800,
      ttfb: Math.random() * 200 + 100,
    }

    this.addMetric({
      timestamp,
      metricType: 'core_web_vitals',
      value: mockCoreWebVitals.lcp,
      unit: 'ms',
      source: 'client',
      metadata: { metric: 'lcp' },
    })

    this.addMetric({
      timestamp,
      metricType: 'core_web_vitals',
      value: mockCoreWebVitals.fid,
      unit: 'ms',
      source: 'client',
      metadata: { metric: 'fid' },
    })

    this.addMetric({
      timestamp,
      metricType: 'core_web_vitals',
      value: mockCoreWebVitals.cls,
      unit: 'score',
      source: 'client',
      metadata: { metric: 'cls' },
    })
  }

  // Add a metric to the collection
  addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric)
  }

  // Check alert rules and trigger alerts
  async checkAlerts(): Promise<void> {
    for (const rule of this.alertRules) {
      if (!rule.enabled) continue

      const recentMetrics = this.getRecentMetrics(rule.metricType, rule.duration)
      if (recentMetrics.length === 0) continue

      const avgValue = recentMetrics.reduce((sum, m) => sum + m.value, 0) / recentMetrics.length

      let shouldAlert = false
      switch (rule.condition) {
        case 'greater_than':
          shouldAlert = avgValue > rule.threshold
          break
        case 'less_than':
          shouldAlert = avgValue < rule.threshold
          break
        case 'equals':
          shouldAlert = avgValue === rule.threshold
          break
        case 'not_equals':
          shouldAlert = avgValue !== rule.threshold
          break
      }

      if (shouldAlert) {
        await this.triggerAlert(rule, avgValue)
      }
    }
  }

  // Get recent metrics for a specific type and duration
  private getRecentMetrics(metricType: string, durationMinutes: number): PerformanceMetric[] {
    const cutoffTime = new Date(Date.now() - durationMinutes * 60 * 1000)
    return this.metrics.filter(
      m => m.metricType === metricType && new Date(m.timestamp) > cutoffTime
    )
  }

  // Trigger an alert
  private async triggerAlert(rule: AlertRule, value: number): Promise<void> {
    // Check if we already have an active alert for this rule
    const existingAlert = this.alerts.find(
      a => a.ruleId === rule.id && !a.resolved
    )

    if (existingAlert) {
      return // Don't spam alerts
    }

    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ruleId: rule.id,
      ruleName: rule.name,
      severity: rule.severity,
      message: `${rule.name}: ${value.toFixed(2)} ${rule.condition.replace('_', ' ')} ${rule.threshold}`,
      timestamp: new Date().toISOString(),
      resolved: false,
      metadata: { value, threshold: rule.threshold, condition: rule.condition },
    }

    this.alerts.push(alert)
    console.warn(`ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`)

    // Send alert notifications
    await this.sendAlertNotifications(alert, rule)
  }
  // Send alert notifications
  private async sendAlertNotifications(alert: Alert, rule: AlertRule): Promise<void> {
    for (const channel of rule.channels) {
      try {
        switch (channel) {
          case 'email':
            await this.sendEmailAlert(alert)
            break
          case 'slack':
            await this.sendSlackAlert(alert)
            break
          case 'webhook':
            await this.sendWebhookAlert(alert)
            break
        }
      } catch (error) {
        console.error(`Failed to send ${channel} alert:`, error)
      }
    }
  }

  // Send email alert (placeholder)
  private async sendEmailAlert(alert: Alert): Promise<void> {
    console.log(`Email alert sent: ${alert.message}`)
    // In production, integrate with email service
  }

  // Send Slack alert (placeholder)
  private async sendSlackAlert(alert: Alert): Promise<void> {
    console.log(`Slack alert sent: ${alert.message}`)
    // In production, integrate with Slack API
  }

  // Send webhook alert (placeholder)
  private async sendWebhookAlert(alert: Alert): Promise<void> {
    console.log(`Webhook alert sent: ${alert.message}`)
    // In production, send to webhook URL
  }

  // Calculate trend for metrics
  private calculateTrend(metrics: PerformanceMetric[]): 'improving' | 'degrading' | 'stable' {
    if (metrics.length < 2) return 'stable'

    const firstHalf = metrics.slice(0, Math.floor(metrics.length / 2))
    const secondHalf = metrics.slice(Math.floor(metrics.length / 2))

    const firstAvg = firstHalf.reduce((sum, m) => sum + m.value, 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((sum, m) => sum + m.value, 0) / secondHalf.length

    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100

    if (Math.abs(changePercent) < 5) return 'stable'
    return changePercent > 0 ? 'degrading' : 'improving'
  }
  // Generate performance report
  generateReport(periodHours: number = 24): PerformanceReport {
    const endTime = new Date()
    const startTime = new Date(endTime.getTime() - periodHours * 60 * 60 * 1000)

    const periodMetrics = this.metrics.filter(
      m => new Date(m.timestamp) >= startTime && new Date(m.timestamp) <= endTime
    )

    const responseTimeMetrics = periodMetrics.filter(m => m.metricType === 'response_time')
    const cacheMetrics = periodMetrics.filter(m => m.metricType === 'cache_hit_ratio')
    const errorMetrics = periodMetrics.filter(m => m.metricType === 'error_rate')
    const bandwidthMetrics = periodMetrics.filter(m => m.metricType === 'bandwidth')
    const coreWebVitalsMetrics = periodMetrics.filter(m => m.metricType === 'core_web_vitals')

    const avgResponseTime = responseTimeMetrics.length > 0
      ? responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length
      : 0

    const avgCacheHitRatio = cacheMetrics.length > 0
      ? cacheMetrics.reduce((sum, m) => sum + m.value, 0) / cacheMetrics.length
      : 0

    const avgErrorRate = errorMetrics.length > 0
      ? errorMetrics.reduce((sum, m) => sum + m.value, 0) / errorMetrics.length
      : 0

    const totalBandwidth = bandwidthMetrics.reduce((sum, m) => sum + m.value, 0)

    // Calculate Core Web Vitals averages
    const lcpMetrics = coreWebVitalsMetrics.filter(m => m.metadata?.metric === 'lcp')
    const fidMetrics = coreWebVitalsMetrics.filter(m => m.metadata?.metric === 'fid')
    const clsMetrics = coreWebVitalsMetrics.filter(m => m.metadata?.metric === 'cls')
    const fcpMetrics = coreWebVitalsMetrics.filter(m => m.metadata?.metric === 'fcp')
    const ttfbMetrics = coreWebVitalsMetrics.filter(m => m.metadata?.metric === 'ttfb')

    const recentAlerts = this.alerts.filter(
      a => new Date(a.timestamp) >= startTime && new Date(a.timestamp) <= endTime
    )

    return {
      period: `${periodHours}h`,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      metrics: {
        avgResponseTime,
        cacheHitRatio: avgCacheHitRatio,
        errorRate: avgErrorRate,
        totalRequests: periodMetrics.length,
        bandwidth: totalBandwidth,
        coreWebVitals: {
          avgLCP: lcpMetrics.length > 0 ? lcpMetrics.reduce((sum, m) => sum + m.value, 0) / lcpMetrics.length : 0,
          avgFID: fidMetrics.length > 0 ? fidMetrics.reduce((sum, m) => sum + m.value, 0) / fidMetrics.length : 0,
          avgCLS: clsMetrics.length > 0 ? clsMetrics.reduce((sum, m) => sum + m.value, 0) / clsMetrics.length : 0,
          avgFCP: fcpMetrics.length > 0 ? fcpMetrics.reduce((sum, m) => sum + m.value, 0) / fcpMetrics.length : 0,
          avgTTFB: ttfbMetrics.length > 0 ? ttfbMetrics.reduce((sum, m) => sum + m.value, 0) / ttfbMetrics.length : 0,
        },
      },
      trends: {
        responseTime: this.calculateTrend(responseTimeMetrics),
        cacheHitRatio: this.calculateTrend(cacheMetrics),
        errorRate: this.calculateTrend(errorMetrics),
      },
      alerts: recentAlerts,
      recommendations: this.generateRecommendations(avgResponseTime, avgCacheHitRatio, avgErrorRate),
    }
  }
  // Generate recommendations based on metrics
  private generateRecommendations(responseTime: number, cacheHitRatio: number, errorRate: number): string[] {
    const recommendations: string[] = []

    if (responseTime > 1000) {
      recommendations.push('Consider enabling more aggressive caching or optimizing slow endpoints')
    }

    if (cacheHitRatio < 80) {
      recommendations.push('Review cache rules and TTL settings to improve cache hit ratio')
    }

    if (errorRate > 2) {
      recommendations.push('Investigate error sources and implement better error handling')
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance metrics are within acceptable ranges')
    }

    return recommendations
  }

  // Clean up old data
  private cleanupOldData(): void {
    const cutoffTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days

    this.metrics = this.metrics.filter(m => new Date(m.timestamp) > cutoffTime)
    this.alerts = this.alerts.filter(a => new Date(a.timestamp) > cutoffTime)
  }

  // Get current metrics
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  // Get current alerts
  getAlerts(): Alert[] {
    return [...this.alerts]
  }

  // Get alert rules
  getAlertRules(): AlertRule[] {
    return [...this.alertRules]
  }

  // Update alert rule
  updateAlertRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    const ruleIndex = this.alertRules.findIndex(r => r.id === ruleId)
    if (ruleIndex === -1) return false

    this.alertRules[ruleIndex] = { ...this.alertRules[ruleIndex], ...updates }
    return true
  }

  // Resolve alert
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId)
    if (!alert || alert.resolved) return false

    alert.resolved = true
    alert.resolvedAt = new Date().toISOString()
    return true
  }

  // R2-specific monitoring methods
  recordR2Operation(metrics: R2OperationMetrics): void {
    // Record the operation metric
    this.addMetric({
      timestamp: new Date().toISOString(),
      metricType: 'r2_operation',
      value: metrics.duration,
      unit: 'ms',
      source: 'cloudflare',
      metadata: {
        operation: metrics.operation,
        bucketType: metrics.bucketType,
        fileSize: metrics.fileSize,
        contentType: metrics.contentType,
        success: metrics.success,
        error: metrics.error,
        retryCount: metrics.retryCount,
        fallbackUsed: metrics.fallbackUsed
      }
    })

    // Record throughput if available
    if (metrics.throughput) {
      this.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'storage_throughput',
        value: metrics.throughput,
        unit: 'bytes/sec',
        source: 'cloudflare',
        metadata: {
          operation: metrics.operation,
          bucketType: metrics.bucketType,
          fileSize: metrics.fileSize
        }
      })
    }

    // Record error if operation failed
    if (!metrics.success) {
      this.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'error_rate',
        value: 1,
        unit: 'count',
        source: 'cloudflare',
        metadata: {
          operation: `r2_${metrics.operation}`,
          bucketType: metrics.bucketType,
          error: metrics.error,
          fallbackUsed: metrics.fallbackUsed
        }
      })
    }
  }

  // Get R2 performance statistics
  getR2PerformanceStats(timeRange: number = 3600000): R2PerformanceStats {
    const cutoff = Date.now() - timeRange
    const r2Metrics = this.metrics.filter(m =>
      m.metricType === 'r2_operation' &&
      new Date(m.timestamp).getTime() > cutoff
    )

    if (r2Metrics.length === 0) {
      return {
        totalOperations: 0,
        successRate: 0,
        averageResponseTime: 0,
        averageThroughput: 0,
        operationBreakdown: {},
        bucketStats: {},
        errorBreakdown: {},
        fallbackRate: 0
      }
    }

    const totalOperations = r2Metrics.length
    const successfulOps = r2Metrics.filter(m => m.metadata?.success === true).length
    const successRate = (successfulOps / totalOperations) * 100
    const averageResponseTime = r2Metrics.reduce((sum, m) => sum + m.value, 0) / totalOperations

    // Calculate operation breakdown
    const operationBreakdown: Record<string, number> = {}
    r2Metrics.forEach(m => {
      const op = m.metadata?.operation || 'unknown'
      operationBreakdown[op] = (operationBreakdown[op] || 0) + 1
    })

    // Calculate bucket stats
    const bucketStats: Record<string, { operations: number; averageSize: number; successRate: number }> = {}
    r2Metrics.forEach(m => {
      const bucket = m.metadata?.bucketType || 'unknown'
      if (!bucketStats[bucket]) {
        bucketStats[bucket] = { operations: 0, averageSize: 0, successRate: 0 }
      }
      bucketStats[bucket].operations++
    })

    // Calculate success rates and average sizes for each bucket
    Object.keys(bucketStats).forEach(bucket => {
      const bucketMetrics = r2Metrics.filter(m => m.metadata?.bucketType === bucket)
      const successfulBucketOps = bucketMetrics.filter(m => m.metadata?.success === true).length
      bucketStats[bucket].successRate = (successfulBucketOps / bucketMetrics.length) * 100

      const sizesWithData = bucketMetrics.filter(m => m.metadata?.fileSize).map(m => m.metadata?.fileSize || 0)
      bucketStats[bucket].averageSize = sizesWithData.length > 0
        ? sizesWithData.reduce((sum, size) => sum + size, 0) / sizesWithData.length
        : 0
    })

    // Calculate error breakdown
    const errorBreakdown: Record<string, number> = {}
    r2Metrics.filter(m => !m.metadata?.success).forEach(m => {
      const error = m.metadata?.error || 'unknown_error'
      errorBreakdown[error] = (errorBreakdown[error] || 0) + 1
    })

    // Calculate fallback rate
    const fallbackOps = r2Metrics.filter(m => m.metadata?.fallbackUsed === true).length
    const fallbackRate = (fallbackOps / totalOperations) * 100

    // Calculate average throughput
    const throughputMetrics = this.metrics.filter(m =>
      m.metricType === 'storage_throughput' &&
      new Date(m.timestamp).getTime() > cutoff
    )
    const averageThroughput = throughputMetrics.length > 0
      ? throughputMetrics.reduce((sum, m) => sum + m.value, 0) / throughputMetrics.length
      : 0

    return {
      totalOperations,
      successRate,
      averageResponseTime,
      averageThroughput,
      operationBreakdown,
      bucketStats,
      errorBreakdown,
      fallbackRate
    }
  }

  // Get R2 health score
  getR2HealthScore(): { score: number; status: 'healthy' | 'degraded' | 'unhealthy'; details: any } {
    const stats = this.getR2PerformanceStats()

    let score = 100
    const details: any = {}

    // Deduct points for low success rate
    if (stats.successRate < 95) {
      const deduction = (95 - stats.successRate) * 2
      score -= deduction
      details.successRateIssue = `Success rate is ${stats.successRate.toFixed(1)}% (target: 95%+)`
    }

    // Deduct points for high response time
    if (stats.averageResponseTime > 2000) {
      const deduction = Math.min(20, (stats.averageResponseTime - 2000) / 100)
      score -= deduction
      details.responseTimeIssue = `Average response time is ${stats.averageResponseTime.toFixed(0)}ms (target: <2000ms)`
    }

    // Deduct points for high fallback rate
    if (stats.fallbackRate > 5) {
      const deduction = Math.min(15, stats.fallbackRate)
      score -= deduction
      details.fallbackIssue = `Fallback rate is ${stats.fallbackRate.toFixed(1)}% (target: <5%)`
    }

    // Deduct points for low throughput (if we have data)
    if (stats.averageThroughput > 0 && stats.averageThroughput < 1024 * 1024) { // 1MB/s
      const deduction = 10
      score -= deduction
      details.throughputIssue = `Average throughput is ${(stats.averageThroughput / 1024 / 1024).toFixed(2)}MB/s (target: >1MB/s)`
    }

    score = Math.max(0, Math.round(score))

    let status: 'healthy' | 'degraded' | 'unhealthy'
    if (score >= 90) {
      status = 'healthy'
    } else if (score >= 70) {
      status = 'degraded'
    } else {
      status = 'unhealthy'
    }

    return { score, status, details }
  }
}

// Singleton instance
export const hybridPerformanceMonitor = new HybridPerformanceMonitor()

// Client-side Core Web Vitals collection
export function collectCoreWebVitals(): void {
  if (typeof window === 'undefined') return

  // Collect LCP
  new PerformanceObserver((entryList) => {
    const entries = entryList.getEntries()
    const lastEntry = entries[entries.length - 1]

    hybridPerformanceMonitor.addMetric({
      timestamp: new Date().toISOString(),
      metricType: 'core_web_vitals',
      value: lastEntry.startTime,
      unit: 'ms',
      source: 'client',
      metadata: { metric: 'lcp', url: window.location.href },
    })
  }).observe({ entryTypes: ['largest-contentful-paint'] })

  // Collect FID
  new PerformanceObserver((entryList) => {
    const entries = entryList.getEntries()
    entries.forEach((entry: any) => {
      hybridPerformanceMonitor.addMetric({
        timestamp: new Date().toISOString(),
        metricType: 'core_web_vitals',
        value: entry.processingStart - entry.startTime,
        unit: 'ms',
        source: 'client',
        metadata: { metric: 'fid', url: window.location.href },
      })
    })
  }).observe({ entryTypes: ['first-input'] })

  // Collect CLS
  let clsValue = 0
  new PerformanceObserver((entryList) => {
    const entries = entryList.getEntries()
    entries.forEach((entry: any) => {
      if (!entry.hadRecentInput) {
        clsValue += entry.value
      }
    })

    hybridPerformanceMonitor.addMetric({
      timestamp: new Date().toISOString(),
      metricType: 'core_web_vitals',
      value: clsValue,
      unit: 'score',
      source: 'client',
      metadata: { metric: 'cls', url: window.location.href },
    })
  }).observe({ entryTypes: ['layout-shift'] })
}

// Legacy compatibility methods
export function recordImageLoadTime(url: string, loadTime: number, source: 'firebase' | 'cloudflare'): void {
  hybridPerformanceMonitor.addMetric({
    timestamp: new Date().toISOString(),
    metricType: 'response_time',
    value: loadTime,
    unit: 'ms',
    source: source === 'firebase' ? 'firebase' : 'cloudflare',
    metadata: {
      url,
      type: 'image',
      isR2: url.includes('r2.cloudflarestorage.com'),
      isOptimized: url.includes('imagedelivery.net')
    },
  })
}

export function recordApiResponseTime(endpoint: string, responseTime: number, cached: boolean): void {
  hybridPerformanceMonitor.addMetric({
    timestamp: new Date().toISOString(),
    metricType: 'response_time',
    value: responseTime,
    unit: 'ms',
    source: cached ? 'cloudflare' : 'firebase',
    metadata: {
      endpoint,
      cached,
      cacheStatus: cached ? 'HIT' : 'MISS'
    },
  })
}

export function recordCacheEvent(url: string, status: 'HIT' | 'MISS' | 'EXPIRED'): void {
  hybridPerformanceMonitor.addMetric({
    timestamp: new Date().toISOString(),
    metricType: 'cache_hit_ratio',
    value: status === 'HIT' ? 100 : 0,
    unit: 'percentage',
    source: 'cloudflare',
    metadata: { url, status },
  })
}
