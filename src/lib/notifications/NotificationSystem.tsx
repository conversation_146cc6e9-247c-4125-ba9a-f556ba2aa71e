/**
 * Notification System
 * 
 * Global notification system with animations, queuing,
 * and gamification-specific notification types.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { AnimatePresence } from 'framer-motion'
import { AnimatedNotification } from '../../components/animations/AnimatedComponents'

// ===== TYPES =====

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'achievement' | 'points' | 'tier' | 'challenge'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message?: string
  icon?: React.ReactNode
  duration?: number
  persistent?: boolean
  data?: any
  onAction?: () => void
  actionLabel?: string
  className?: string
}

export interface NotificationContextType {
  notifications: Notification[]
  addNotification: (notification: Omit<Notification, 'id'>) => string
  removeNotification: (id: string) => void
  clearAll: () => void
  
  // Gamification-specific helpers
  notifyAchievementUnlock: (achievement: any) => void
  notifyPointsGain: (amount: number, source?: string) => void
  notifyPointsLoss: (amount: number, reason?: string) => void
  notifyTierPromotion: (oldTier: string, newTier: string) => void
  notifyChallengeComplete: (challenge: any) => void
  notifyChallengeJoin: (challenge: any) => void
}

interface NotificationProviderProps {
  children: React.ReactNode
  maxNotifications?: number
  defaultDuration?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
}

// ===== CONTEXT =====

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function useNotifications(): NotificationContextType {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

// ===== PROVIDER =====

export function NotificationProvider({
  children,
  maxNotifications = 5,
  defaultDuration = 5000,
  position = 'top-right'
}: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [container, setContainer] = useState<HTMLDivElement | null>(null)
  const nextId = useRef(1)
  const timers = useRef<Map<string, NodeJS.Timeout>>(new Map())

  // Create notification container
  useEffect(() => {
    const div = document.createElement('div')
    div.id = 'notification-container'
    div.className = `fixed z-50 ${getPositionClasses(position)}`
    document.body.appendChild(div)
    setContainer(div)

    return () => {
      if (document.body.contains(div)) {
        document.body.removeChild(div)
      }
      // Clear all timers
      timers.current.forEach(timer => clearTimeout(timer))
      timers.current.clear()
    }
  }, [position])

  // Add notification
  const addNotification = useCallback((notification: Omit<Notification, 'id'>): string => {
    const id = `notification-${nextId.current++}`
    const newNotification: Notification = {
      id,
      duration: defaultDuration,
      ...notification
    }

    setNotifications(prev => {
      const updated = [newNotification, ...prev]
      // Limit number of notifications
      return updated.slice(0, maxNotifications)
    })

    // Set auto-remove timer if not persistent
    if (!newNotification.persistent && newNotification.duration && newNotification.duration > 0) {
      const timer = setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
      timers.current.set(id, timer)
    }

    return id
  }, [defaultDuration, maxNotifications])

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
    
    // Clear timer if exists
    const timer = timers.current.get(id)
    if (timer) {
      clearTimeout(timer)
      timers.current.delete(id)
    }
  }, [])

  // Clear all notifications
  const clearAll = useCallback(() => {
    setNotifications([])
    timers.current.forEach(timer => clearTimeout(timer))
    timers.current.clear()
  }, [])

  // Gamification-specific notification helpers
  const notifyAchievementUnlock = useCallback((achievement: any) => {
    addNotification({
      type: 'achievement',
      title: 'Achievement Unlocked! 🏆',
      message: `${achievement.title} - ${achievement.description}`,
      icon: <span className="text-2xl">{achievement.icon}</span>,
      duration: 8000,
      data: { achievement }
    })
  }, [addNotification])

  const notifyPointsGain = useCallback((amount: number, source?: string) => {
    addNotification({
      type: 'points',
      title: `+${amount.toLocaleString()} Points! 💰`,
      message: source ? `Earned from ${source}` : undefined,
      icon: <span className="text-2xl text-green-500">💰</span>,
      duration: 4000,
      data: { amount, source, type: 'gain' }
    })
  }, [addNotification])

  const notifyPointsLoss = useCallback((amount: number, reason?: string) => {
    addNotification({
      type: 'points',
      title: `-${amount.toLocaleString()} Points`,
      message: reason ? `Spent on ${reason}` : undefined,
      icon: <span className="text-2xl text-red-500">💸</span>,
      duration: 3000,
      data: { amount, reason, type: 'loss' }
    })
  }, [addNotification])

  const notifyTierPromotion = useCallback((oldTier: string, newTier: string) => {
    addNotification({
      type: 'tier',
      title: 'Tier Promotion! 🎊',
      message: `Congratulations! You've advanced from ${oldTier} to ${newTier}`,
      icon: <span className="text-2xl">🎊</span>,
      duration: 10000,
      data: { oldTier, newTier }
    })
  }, [addNotification])

  const notifyChallengeComplete = useCallback((challenge: any) => {
    addNotification({
      type: 'challenge',
      title: 'Challenge Completed! 🏆',
      message: `${challenge.title} - Great job!`,
      icon: <span className="text-2xl">🏆</span>,
      duration: 6000,
      data: { challenge, type: 'complete' }
    })
  }, [addNotification])

  const notifyChallengeJoin = useCallback((challenge: any) => {
    addNotification({
      type: 'challenge',
      title: 'Challenge Joined! 🚀',
      message: `You've joined ${challenge.title}`,
      icon: <span className="text-2xl">🚀</span>,
      duration: 4000,
      data: { challenge, type: 'join' }
    })
  }, [addNotification])

  const contextValue: NotificationContextType = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    notifyAchievementUnlock,
    notifyPointsGain,
    notifyPointsLoss,
    notifyTierPromotion,
    notifyChallengeComplete,
    notifyChallengeJoin
  }

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      {container && createPortal(
        <NotificationContainer
          notifications={notifications}
          onRemove={removeNotification}
        />,
        container
      )}
    </NotificationContext.Provider>
  )
}

// ===== NOTIFICATION CONTAINER =====

interface NotificationContainerProps {
  notifications: Notification[]
  onRemove: (id: string) => void
}

function NotificationContainer({ notifications, onRemove }: NotificationContainerProps) {
  return (
    <div className="space-y-4 w-full max-w-sm">
      <AnimatePresence mode="popLayout">
        {notifications.map(notification => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onRemove={onRemove}
          />
        ))}
      </AnimatePresence>
    </div>
  )
}

// ===== NOTIFICATION ITEM =====

interface NotificationItemProps {
  notification: Notification
  onRemove: (id: string) => void
}

function NotificationItem({ notification, onRemove }: NotificationItemProps) {
  const getNotificationType = (type: NotificationType) => {
    switch (type) {
      case 'achievement':
      case 'tier':
        return 'success'
      case 'points':
        return notification.data?.type === 'gain' ? 'success' : 'warning'
      case 'challenge':
        return 'info'
      default:
        return type
    }
  }

  const getEnhancedNotification = () => {
    switch (notification.type) {
      case 'achievement':
        return {
          ...notification,
          className: 'border-l-4 border-l-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20'
        }
      case 'tier':
        return {
          ...notification,
          className: 'border-l-4 border-l-purple-400 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20'
        }
      case 'points':
        const isGain = notification.data?.type === 'gain'
        return {
          ...notification,
          className: `border-l-4 ${isGain ? 'border-l-green-400' : 'border-l-red-400'} bg-gradient-to-r ${isGain ? 'from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20' : 'from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20'}`
        }
      case 'challenge':
        return {
          ...notification,
          className: 'border-l-4 border-l-blue-400 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20'
        }
      default:
        return notification
    }
  }

  const enhancedNotification = getEnhancedNotification()

  return (
    <AnimatedNotification
      type={getNotificationType(notification.type)}
      title={notification.title}
      message={notification.message}
      icon={notification.icon}
      onClose={() => onRemove(notification.id)}
      duration={0} // Disable auto-close, handled by provider
      className={enhancedNotification.className}
    />
  )
}

// ===== UTILITY FUNCTIONS =====

function getPositionClasses(position: string): string {
  switch (position) {
    case 'top-right':
      return 'top-4 right-4'
    case 'top-left':
      return 'top-4 left-4'
    case 'bottom-right':
      return 'bottom-4 right-4'
    case 'bottom-left':
      return 'bottom-4 left-4'
    case 'top-center':
      return 'top-4 left-1/2 transform -translate-x-1/2'
    case 'bottom-center':
      return 'bottom-4 left-1/2 transform -translate-x-1/2'
    default:
      return 'top-4 right-4'
  }
}

// ===== NOTIFICATION HOOKS =====

/**
 * Hook for common notification patterns
 */
export function useCommonNotifications() {
  const { addNotification } = useNotifications()

  const notifySuccess = useCallback((title: string, message?: string) => {
    return addNotification({
      type: 'success',
      title,
      message,
      icon: <span className="text-green-500">✅</span>
    })
  }, [addNotification])

  const notifyError = useCallback((title: string, message?: string) => {
    return addNotification({
      type: 'error',
      title,
      message,
      icon: <span className="text-red-500">❌</span>,
      duration: 8000
    })
  }, [addNotification])

  const notifyInfo = useCallback((title: string, message?: string) => {
    return addNotification({
      type: 'info',
      title,
      message,
      icon: <span className="text-blue-500">💡</span>
    })
  }, [addNotification])

  const notifyWarning = useCallback((title: string, message?: string) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      icon: <span className="text-yellow-500">⚠️</span>
    })
  }, [addNotification])

  const notifyLoading = useCallback((title: string, message?: string) => {
    return addNotification({
      type: 'info',
      title,
      message,
      icon: (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500" />
      ),
      persistent: true
    })
  }, [addNotification])

  return {
    notifySuccess,
    notifyError,
    notifyInfo,
    notifyWarning,
    notifyLoading
  }
}

/**
 * Hook for gamification notifications
 */
export function useGamificationNotifications() {
  const {
    notifyAchievementUnlock,
    notifyPointsGain,
    notifyPointsLoss,
    notifyTierPromotion,
    notifyChallengeComplete,
    notifyChallengeJoin
  } = useNotifications()

  return {
    achievement: {
      unlock: notifyAchievementUnlock
    },
    points: {
      gain: notifyPointsGain,
      loss: notifyPointsLoss
    },
    tier: {
      promotion: notifyTierPromotion
    },
    challenge: {
      complete: notifyChallengeComplete,
      join: notifyChallengeJoin
    }
  }
}

export default NotificationProvider