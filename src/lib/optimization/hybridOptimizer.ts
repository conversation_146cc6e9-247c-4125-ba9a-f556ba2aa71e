/**
 * Hybrid Performance Optimizer for Syndicaps
 * 
 * Automatically optimizes performance across Firebase and Cloudflare services
 * based on real-time metrics and usage patterns.
 */

import { hybridPerformanceMonitor } from '../monitoring/hybridPerformanceMonitor'
import { cdnPerformanceMonitor } from '../cloudflare/cdnConfig'
import { shouldUseR2Storage, shouldUseCloudflareWorkers } from '../config/featureFlags'

export interface OptimizationRule {
  id: string
  name: string
  condition: (metrics: any) => boolean
  action: (context: OptimizationContext) => Promise<void>
  priority: number
  cooldown: number // minutes
  lastExecuted?: number
}

export interface OptimizationContext {
  metrics: any
  userAgent?: string
  location?: string
  userId?: string
  sessionId?: string
}

export interface OptimizationResult {
  ruleId: string
  action: string
  impact: 'low' | 'medium' | 'high'
  metrics: {
    before: any
    after: any
    improvement: number
  }
  timestamp: number
}

export class HybridPerformanceOptimizer {
  private rules: OptimizationRule[] = []
  private executionHistory: Map<string, number> = new Map()
  private optimizationResults: OptimizationResult[] = []

  constructor() {
    this.initializeOptimizationRules()
  }

  /**
   * Initialize performance optimization rules
   */
  private initializeOptimizationRules(): void {
    this.rules = [
      {
        id: 'slow-images-to-r2',
        name: 'Migrate slow images to R2',
        condition: (metrics) => {
          const firebaseImageTime = metrics['image_load_time_firebase']?.average || 0
          const cloudflareImageTime = metrics['image_load_time_cloudflare']?.average || 0
          return firebaseImageTime > 500 && firebaseImageTime > cloudflareImageTime * 1.5
        },
        action: this.migrateSlowImagesToR2.bind(this),
        priority: 1,
        cooldown: 60
      },
      
      {
        id: 'increase-cache-ttl',
        name: 'Increase cache TTL for stable content',
        condition: (metrics) => {
          const cacheHitRate = metrics['cache_hit_rate_cloudflare']?.average || 0
          return cacheHitRate < 70
        },
        action: this.increaseCacheTTL.bind(this),
        priority: 2,
        cooldown: 30
      },
      
      {
        id: 'enable-workers-for-slow-apis',
        name: 'Enable Workers for slow API endpoints',
        condition: (metrics) => {
          const apiResponseTime = metrics['api_response_time_firebase']?.average || 0
          return apiResponseTime > 1000
        },
        action: this.enableWorkersForSlowAPIs.bind(this),
        priority: 1,
        cooldown: 120
      },
      
      {
        id: 'optimize-image-quality',
        name: 'Optimize image quality based on connection',
        condition: (metrics) => {
          const imageLoadTime = metrics['image_load_time_cloudflare']?.average || 0
          return imageLoadTime > 300
        },
        action: this.optimizeImageQuality.bind(this),
        priority: 3,
        cooldown: 15
      },
      
      {
        id: 'preload-critical-resources',
        name: 'Preload critical resources',
        condition: (metrics) => {
          const lcp = metrics['core_web_vitals_lcp']?.average || 0
          return lcp > 2500
        },
        action: this.preloadCriticalResources.bind(this),
        priority: 1,
        cooldown: 60
      },
      
      {
        id: 'reduce-bundle-size',
        name: 'Reduce JavaScript bundle size',
        condition: (metrics) => {
          const fid = metrics['core_web_vitals_fid']?.average || 0
          return fid > 100
        },
        action: this.reduceBundleSize.bind(this),
        priority: 2,
        cooldown: 180
      }
    ]
  }

  /**
   * Run optimization analysis and apply rules
   */
  async optimize(context: OptimizationContext): Promise<OptimizationResult[]> {
    const results: OptimizationResult[] = []
    const currentTime = Date.now()

    // Sort rules by priority
    const sortedRules = this.rules.sort((a, b) => a.priority - b.priority)

    for (const rule of sortedRules) {
      try {
        // Check cooldown
        const lastExecuted = this.executionHistory.get(rule.id) || 0
        const cooldownMs = rule.cooldown * 60 * 1000
        
        if (currentTime - lastExecuted < cooldownMs) {
          continue
        }

        // Check condition
        if (!rule.condition(context.metrics)) {
          continue
        }

        console.log(`Executing optimization rule: ${rule.name}`)

        // Capture before metrics
        const beforeMetrics = this.captureMetrics()

        // Execute optimization action
        await rule.action(context)

        // Update execution history
        this.executionHistory.set(rule.id, currentTime)

        // Wait a bit for metrics to update
        await this.delay(5000)

        // Capture after metrics
        const afterMetrics = this.captureMetrics()

        // Calculate improvement
        const improvement = this.calculateImprovement(beforeMetrics, afterMetrics)

        const result: OptimizationResult = {
          ruleId: rule.id,
          action: rule.name,
          impact: this.determineImpact(improvement),
          metrics: {
            before: beforeMetrics,
            after: afterMetrics,
            improvement
          },
          timestamp: currentTime
        }

        results.push(result)
        this.optimizationResults.push(result)

        console.log(`Optimization completed: ${rule.name}, improvement: ${improvement}%`)

      } catch (error) {
        console.error(`Optimization rule failed: ${rule.name}`, error)
      }
    }

    return results
  }

  /**
   * Migrate slow images to R2 storage
   */
  private async migrateSlowImagesToR2(context: OptimizationContext): Promise<void> {
    // This would trigger the image migration process for slow-loading images
    console.log('Triggering migration of slow images to R2...')
    
    // In a real implementation, this would:
    // 1. Identify slow-loading images from Firebase
    // 2. Queue them for migration to R2
    // 3. Update database references
    // 4. Monitor migration progress
  }

  /**
   * Increase cache TTL for stable content
   */
  private async increaseCacheTTL(context: OptimizationContext): Promise<void> {
    console.log('Increasing cache TTL for stable content...')
    
    // This would update CDN cache rules to increase TTL for:
    // - Static assets that rarely change
    // - API responses with stable data
    // - Images that are frequently accessed
  }

  /**
   * Enable Workers for slow API endpoints
   */
  private async enableWorkersForSlowAPIs(context: OptimizationContext): Promise<void> {
    console.log('Enabling Workers for slow API endpoints...')
    
    // This would:
    // 1. Identify slow API endpoints
    // 2. Enable caching Workers for those endpoints
    // 3. Configure appropriate cache rules
    // 4. Monitor performance improvement
  }

  /**
   * Optimize image quality based on connection
   */
  private async optimizeImageQuality(context: OptimizationContext): Promise<void> {
    console.log('Optimizing image quality based on connection...')
    
    // This would adjust image optimization parameters based on:
    // - User's connection speed
    // - Device capabilities
    // - Geographic location
    // - Time of day (network congestion)
  }

  /**
   * Preload critical resources
   */
  private async preloadCriticalResources(context: OptimizationContext): Promise<void> {
    console.log('Preloading critical resources...')
    
    // This would:
    // 1. Identify critical resources for faster LCP
    // 2. Add preload hints to HTML
    // 3. Implement resource prioritization
    // 4. Use service worker for intelligent prefetching
  }

  /**
   * Reduce JavaScript bundle size
   */
  private async reduceBundleSize(context: OptimizationContext): Promise<void> {
    console.log('Reducing JavaScript bundle size...')
    
    // This would:
    // 1. Analyze bundle composition
    // 2. Implement code splitting
    // 3. Remove unused dependencies
    // 4. Enable tree shaking optimizations
  }

  /**
   * Capture current performance metrics
   */
  private captureMetrics(): any {
    const realTimeMetrics = hybridPerformanceMonitor.getRealTimeMetrics()
    const cdnReport = cdnPerformanceMonitor.generateReport()
    
    return {
      ...realTimeMetrics,
      cacheHitRate: cdnReport.cacheHitRate,
      totalRequests: cdnReport.totalRequests,
      timestamp: Date.now()
    }
  }

  /**
   * Calculate performance improvement percentage
   */
  private calculateImprovement(before: any, after: any): number {
    // Calculate improvement based on key metrics
    const improvements: number[] = []

    // Image load time improvement
    const beforeImageTime = before['image_load_time_cloudflare'] || before['image_load_time_firebase'] || 0
    const afterImageTime = after['image_load_time_cloudflare'] || after['image_load_time_firebase'] || 0
    
    if (beforeImageTime > 0 && afterImageTime > 0) {
      improvements.push(((beforeImageTime - afterImageTime) / beforeImageTime) * 100)
    }

    // API response time improvement
    const beforeApiTime = before['api_response_time_firebase'] || 0
    const afterApiTime = after['api_response_time_firebase'] || 0
    
    if (beforeApiTime > 0 && afterApiTime > 0) {
      improvements.push(((beforeApiTime - afterApiTime) / beforeApiTime) * 100)
    }

    // Cache hit rate improvement
    const beforeCacheHit = before.cacheHitRate || 0
    const afterCacheHit = after.cacheHitRate || 0
    
    if (beforeCacheHit > 0) {
      improvements.push(((afterCacheHit - beforeCacheHit) / beforeCacheHit) * 100)
    }

    // Return average improvement
    return improvements.length > 0 
      ? improvements.reduce((a, b) => a + b, 0) / improvements.length 
      : 0
  }

  /**
   * Determine impact level based on improvement percentage
   */
  private determineImpact(improvement: number): 'low' | 'medium' | 'high' {
    if (improvement >= 20) return 'high'
    if (improvement >= 10) return 'medium'
    return 'low'
  }

  /**
   * Get optimization history
   */
  getOptimizationHistory(): OptimizationResult[] {
    return this.optimizationResults.slice(-50) // Last 50 optimizations
  }

  /**
   * Get optimization recommendations
   */
  getRecommendations(): string[] {
    const metrics = this.captureMetrics()
    const recommendations: string[] = []

    for (const rule of this.rules) {
      if (rule.condition(metrics)) {
        recommendations.push(`Consider: ${rule.name}`)
      }
    }

    return recommendations
  }

  /**
   * Schedule automatic optimization
   */
  scheduleOptimization(intervalMinutes: number = 30): void {
    setInterval(async () => {
      try {
        const metrics = hybridPerformanceMonitor.getRealTimeMetrics()
        const context: OptimizationContext = { metrics }
        
        await this.optimize(context)
      } catch (error) {
        console.error('Scheduled optimization failed:', error)
      }
    }, intervalMinutes * 60 * 1000)
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Global optimizer instance
export const hybridOptimizer = new HybridPerformanceOptimizer()

// Auto-start optimization scheduling in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  hybridOptimizer.scheduleOptimization(30) // Every 30 minutes
}
