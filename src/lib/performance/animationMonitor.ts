/**
 * Animation Performance Monitor
 *
 * Tracks and analyzes animation performance across the application
 * Provides real-time metrics and optimization recommendations
 *
 * <AUTHOR> Team
 */

import React from 'react'

/**
 * Performance metrics interface
 */
export interface AnimationMetrics {
  name: string
  duration: number
  fps: number
  startTime: number
  endTime: number
  frameCount: number
  droppedFrames: number
  layoutShifts: number
  memoryUsage: number
  cpuUsage: number
}

/**
 * Performance budget interface
 */
export interface PerformanceBudget {
  maxDuration: number
  minFPS: number
  maxLayoutShifts: number
  maxMemoryIncrease: number
}

/**
 * Animation performance monitor class
 */
export class AnimationPerformanceMonitor {
  private metrics: Map<string, AnimationMetrics> = new Map()
  private observers: Map<string, PerformanceObserver> = new Map()
  private frameCounters: Map<string, number> = new Map()
  private isMonitoring: boolean = false
  
  private readonly defaultBudget: PerformanceBudget = {
    maxDuration: 500, // ms
    minFPS: 60,
    maxLayoutShifts: 0.1,
    maxMemoryIncrease: 10 // MB
  }

  /**
   * Start monitoring animations
   */
  public startMonitoring(): void {
    if (this.isMonitoring) return
    
    this.isMonitoring = true
    this.setupPerformanceObservers()
    console.log('[AnimationMonitor] Started monitoring animation performance')
  }

  /**
   * Stop monitoring animations
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) return
    
    this.isMonitoring = false
    this.cleanupObservers()
    console.log('[AnimationMonitor] Stopped monitoring animation performance')
  }

  /**
   * Track animation start
   */
  public trackAnimationStart(name: string): void {
    if (!this.isMonitoring) return
    
    const startTime = performance.now()
    const initialMemory = this.getMemoryUsage()
    
    performance.mark(`${name}-start`)
    
    const metrics: AnimationMetrics = {
      name,
      duration: 0,
      fps: 0,
      startTime,
      endTime: 0,
      frameCount: 0,
      droppedFrames: 0,
      layoutShifts: 0,
      memoryUsage: initialMemory,
      cpuUsage: 0
    }
    
    this.metrics.set(name, metrics)
    this.frameCounters.set(name, 0)
    
    // Start frame counting
    this.startFrameCounting(name)
  }

  /**
   * Track animation end
   */
  public trackAnimationEnd(name: string): AnimationMetrics | null {
    if (!this.isMonitoring || !this.metrics.has(name)) return null
    
    const endTime = performance.now()
    const metrics = this.metrics.get(name)!
    const finalMemory = this.getMemoryUsage()
    
    performance.mark(`${name}-end`)
    performance.measure(`${name}-duration`, `${name}-start`, `${name}-end`)
    
    // Update metrics
    metrics.endTime = endTime
    metrics.duration = endTime - metrics.startTime
    metrics.memoryUsage = finalMemory - metrics.memoryUsage
    metrics.fps = this.calculateFPS(name, metrics.duration)
    
    // Calculate layout shifts
    metrics.layoutShifts = this.getLayoutShifts(name)
    
    // Log performance data
    this.logPerformanceData(metrics)
    
    // Check against budget
    this.checkPerformanceBudget(metrics)
    
    // Cleanup
    this.frameCounters.delete(name)
    
    return metrics
  }

  /**
   * Get current performance metrics
   */
  public getMetrics(): AnimationMetrics[] {
    return Array.from(this.metrics.values())
  }

  /**
   * Get performance summary
   */
  public getPerformanceSummary(): {
    totalAnimations: number
    averageFPS: number
    averageDuration: number
    budgetViolations: number
    recommendations: string[]
  } {
    const metrics = this.getMetrics()
    const totalAnimations = metrics.length
    
    if (totalAnimations === 0) {
      return {
        totalAnimations: 0,
        averageFPS: 0,
        averageDuration: 0,
        budgetViolations: 0,
        recommendations: []
      }
    }
    
    const averageFPS = metrics.reduce((sum, m) => sum + m.fps, 0) / totalAnimations
    const averageDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / totalAnimations
    
    const budgetViolations = metrics.filter(m => 
      m.duration > this.defaultBudget.maxDuration ||
      m.fps < this.defaultBudget.minFPS ||
      m.layoutShifts > this.defaultBudget.maxLayoutShifts
    ).length
    
    const recommendations = this.generateRecommendations(metrics)
    
    return {
      totalAnimations,
      averageFPS,
      averageDuration,
      budgetViolations,
      recommendations
    }
  }

  /**
   * Setup performance observers
   */
  private setupPerformanceObservers(): void {
    // Layout shift observer
    if ('LayoutShift' in window) {
      const layoutShiftObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'layout-shift') {
            this.recordLayoutShift(entry as any)
          }
        }
      })
      
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })
      this.observers.set('layout-shift', layoutShiftObserver)
    }
    
    // Long task observer
    if ('PerformanceLongTaskTiming' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'longtask') {
            console.warn('[AnimationMonitor] Long task detected:', entry.duration, 'ms')
          }
        }
      })
      
      longTaskObserver.observe({ entryTypes: ['longtask'] })
      this.observers.set('longtask', longTaskObserver)
    }
  }

  /**
   * Start frame counting for animation
   */
  private startFrameCounting(name: string): void {
    let frameCount = 0
    
    const countFrame = () => {
      if (this.frameCounters.has(name)) {
        frameCount++
        this.frameCounters.set(name, frameCount)
        requestAnimationFrame(countFrame)
      }
    }
    
    requestAnimationFrame(countFrame)
  }

  /**
   * Calculate FPS for animation
   */
  private calculateFPS(name: string, duration: number): number {
    const frameCount = this.frameCounters.get(name) || 0
    const durationInSeconds = duration / 1000
    return durationInSeconds > 0 ? Math.round(frameCount / durationInSeconds) : 0
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return 0
  }

  /**
   * Get layout shifts for animation
   */
  private getLayoutShifts(_name: string): number {
    // This would be implemented with a more sophisticated tracking system
    // For now, return 0 as placeholder
    return 0
  }

  /**
   * Record layout shift
   */
  private recordLayoutShift(entry: any): void {
    if (entry.value > 0.1) {
      console.warn('[AnimationMonitor] Significant layout shift detected:', entry.value)
    }
  }

  /**
   * Log performance data
   */
  private logPerformanceData(metrics: AnimationMetrics): void {
    const { name, duration, fps, memoryUsage, layoutShifts } = metrics
    
    console.group(`[AnimationMonitor] ${name}`)
    console.log(`Duration: ${duration.toFixed(2)}ms`)
    console.log(`FPS: ${fps}`)
    console.log(`Memory: ${memoryUsage.toFixed(2)}MB`)
    console.log(`Layout Shifts: ${layoutShifts}`)
    console.groupEnd()
  }

  /**
   * Check performance against budget
   */
  private checkPerformanceBudget(metrics: AnimationMetrics): void {
    const violations: string[] = []
    
    if (metrics.duration > this.defaultBudget.maxDuration) {
      violations.push(`Duration exceeded: ${metrics.duration}ms > ${this.defaultBudget.maxDuration}ms`)
    }
    
    if (metrics.fps < this.defaultBudget.minFPS) {
      violations.push(`FPS below target: ${metrics.fps} < ${this.defaultBudget.minFPS}`)
    }
    
    if (metrics.layoutShifts > this.defaultBudget.maxLayoutShifts) {
      violations.push(`Layout shifts exceeded: ${metrics.layoutShifts} > ${this.defaultBudget.maxLayoutShifts}`)
    }
    
    if (violations.length > 0) {
      console.warn(`[AnimationMonitor] Budget violations for ${metrics.name}:`, violations)
    }
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(metrics: AnimationMetrics[]): string[] {
    const recommendations: string[] = []
    
    const lowFPSAnimations = metrics.filter(m => m.fps < 60)
    if (lowFPSAnimations.length > 0) {
      recommendations.push('Consider optimizing animations with low FPS (< 60)')
    }
    
    const longAnimations = metrics.filter(m => m.duration > 300)
    if (longAnimations.length > 0) {
      recommendations.push('Consider reducing duration of long animations (> 300ms)')
    }
    
    const memoryIntensiveAnimations = metrics.filter(m => m.memoryUsage > 5)
    if (memoryIntensiveAnimations.length > 0) {
      recommendations.push('Consider optimizing memory usage in animations')
    }
    
    return recommendations
  }

  /**
   * Cleanup observers
   */
  private cleanupObservers(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
  }

  /**
   * Export performance data
   */
  public exportPerformanceData(): string {
    const summary = this.getPerformanceSummary()
    const metrics = this.getMetrics()
    
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      summary,
      metrics
    }, null, 2)
  }
}

/**
 * Global animation monitor instance
 */
export const animationMonitor = new AnimationPerformanceMonitor()

/**
 * React hook for animation monitoring
 */
export const useAnimationMonitoring = (enabled: boolean = true) => {
  if (typeof React !== 'undefined' && React.useEffect) {
    React.useEffect(() => {
      if (enabled && typeof window !== 'undefined') {
        animationMonitor.startMonitoring()

        return () => {
          animationMonitor.stopMonitoring()
        }
      }
    }, [enabled])
  }

  return {
    trackStart: animationMonitor.trackAnimationStart.bind(animationMonitor),
    trackEnd: animationMonitor.trackAnimationEnd.bind(animationMonitor),
    getMetrics: animationMonitor.getMetrics.bind(animationMonitor),
    getSummary: animationMonitor.getPerformanceSummary.bind(animationMonitor),
    exportData: animationMonitor.exportPerformanceData.bind(animationMonitor)
  }
}
