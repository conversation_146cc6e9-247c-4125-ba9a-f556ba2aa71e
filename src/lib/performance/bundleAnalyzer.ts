/**
 * Bundle Analyzer Utility
 * 
 * Provides runtime bundle analysis and optimization recommendations
 * for the community system and other features.
 * 
 * Phase 2 Performance Optimization - Community System Audit
 * 
 * <AUTHOR> Team
 */

interface BundleChunk {
  name: string
  size: number
  gzipSize?: number
  modules: string[]
  loadTime?: number
  cached: boolean
}

interface BundleAnalysis {
  totalSize: number
  totalGzipSize: number
  chunks: BundleChunk[]
  recommendations: BundleRecommendation[]
  performance: {
    loadTime: number
    parseTime: number
    executeTime: number
  }
}

interface BundleRecommendation {
  type: 'split' | 'lazy' | 'preload' | 'remove'
  priority: 'high' | 'medium' | 'low'
  description: string
  estimatedSavings: string
  action: string
}

class BundleAnalyzer {
  private chunks: Map<string, BundleChunk> = new Map()
  private loadTimes: Map<string, number> = new Map()
  private observer?: PerformanceObserver

  constructor() {
    this.initializeMonitoring()
  }

  /**
   * Initialize performance monitoring for bundle analysis
   */
  private initializeMonitoring() {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    // Monitor resource loading
    this.observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          
          // Track JavaScript chunks
          if (resourceEntry.name.includes('/_next/static/chunks/')) {
            const chunkName = this.extractChunkName(resourceEntry.name)
            this.loadTimes.set(chunkName, resourceEntry.duration)
            
            // Warn about slow chunks
            if (resourceEntry.duration > 1000) {
              console.warn(`Slow chunk detected: ${chunkName} (${resourceEntry.duration.toFixed(2)}ms)`)
            }
          }
        }
      }
    })

    this.observer.observe({ entryTypes: ['resource'] })
  }

  /**
   * Extract chunk name from URL
   */
  private extractChunkName(url: string): string {
    const match = url.match(/chunks\/([^\/]+)\.js/)
    return match ? match[1] : 'unknown'
  }

  /**
   * Analyze current bundle composition
   */
  async analyzeBundles(): Promise<BundleAnalysis> {
    const chunks = await this.getLoadedChunks()
    const recommendations = this.generateRecommendations(chunks)
    const performance = this.getPerformanceMetrics()

    return {
      totalSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0),
      totalGzipSize: chunks.reduce((sum, chunk) => sum + (chunk.gzipSize || chunk.size * 0.3), 0),
      chunks,
      recommendations,
      performance
    }
  }

  /**
   * Get information about loaded chunks
   */
  private async getLoadedChunks(): Promise<BundleChunk[]> {
    const chunks: BundleChunk[] = []
    
    // Get script tags for chunk analysis
    const scripts = document.querySelectorAll('script[src*="/_next/static/chunks/"]')
    
    for (let i = 0; i < scripts.length; i++) {
      const script = scripts[i]
      const src = script.getAttribute('src')
      if (!src) continue
      
      const chunkName = this.extractChunkName(src)
      const loadTime = this.loadTimes.get(chunkName)
      
      // Estimate size (would need build-time data for accuracy)
      const estimatedSize = await this.estimateChunkSize(src)
      
      chunks.push({
        name: chunkName,
        size: estimatedSize,
        gzipSize: estimatedSize * 0.3, // Rough estimate
        modules: [], // Would need webpack stats for accuracy
        loadTime,
        cached: this.isChunkCached(src)
      })
    }
    
    return chunks
  }

  /**
   * Estimate chunk size (rough approximation)
   */
  private async estimateChunkSize(src: string): Promise<number> {
    try {
      const response = await fetch(src, { method: 'HEAD' })
      const contentLength = response.headers.get('content-length')
      return contentLength ? parseInt(contentLength) : 50000 // Default estimate
    } catch {
      return 50000 // Default estimate if fetch fails
    }
  }

  /**
   * Check if chunk is cached
   */
  private isChunkCached(src: string): boolean {
    // Check if resource was served from cache
    const entries = performance.getEntriesByName(src)
    if (entries.length > 0) {
      const entry = entries[0] as PerformanceResourceTiming
      return entry.transferSize === 0 && entry.decodedBodySize > 0
    }
    return false
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(chunks: BundleChunk[]): BundleRecommendation[] {
    const recommendations: BundleRecommendation[] = []
    
    // Check for large chunks
    const largeChunks = chunks.filter(chunk => chunk.size > 200000) // 200KB
    largeChunks.forEach(chunk => {
      recommendations.push({
        type: 'split',
        priority: 'high',
        description: `Chunk "${chunk.name}" is large (${(chunk.size / 1024).toFixed(1)}KB)`,
        estimatedSavings: `~${(chunk.size * 0.3 / 1024).toFixed(1)}KB`,
        action: `Consider splitting ${chunk.name} into smaller chunks`
      })
    })
    
    // Check for slow loading chunks
    const slowChunks = chunks.filter(chunk => chunk.loadTime && chunk.loadTime > 1000)
    slowChunks.forEach(chunk => {
      recommendations.push({
        type: 'preload',
        priority: 'medium',
        description: `Chunk "${chunk.name}" loads slowly (${chunk.loadTime?.toFixed(0)}ms)`,
        estimatedSavings: `~${chunk.loadTime ? (chunk.loadTime * 0.5).toFixed(0) : 0}ms`,
        action: `Consider preloading ${chunk.name} for critical paths`
      })
    })
    
    // Check for community-specific optimizations
    const communityChunks = chunks.filter(chunk => 
      chunk.name.includes('community') || 
      chunk.name.includes('achievements') ||
      chunk.name.includes('gamification')
    )
    
    if (communityChunks.length > 0) {
      const totalCommunitySize = communityChunks.reduce((sum, chunk) => sum + chunk.size, 0)
      
      if (totalCommunitySize > 300000) { // 300KB
        recommendations.push({
          type: 'lazy',
          priority: 'medium',
          description: `Community features total ${(totalCommunitySize / 1024).toFixed(1)}KB`,
          estimatedSavings: `~${(totalCommunitySize * 0.7 / 1024).toFixed(1)}KB`,
          action: 'Implement lazy loading for non-critical community features'
        })
      }
    }
    
    return recommendations
  }

  /**
   * Get performance metrics
   */
  private getPerformanceMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    return {
      loadTime: navigation.loadEventEnd - navigation.loadEventStart,
      parseTime: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      executeTime: navigation.loadEventEnd - navigation.domContentLoadedEventEnd
    }
  }

  /**
   * Get bundle health score
   */
  getBundleHealthScore(analysis: BundleAnalysis): number {
    let score = 100
    
    // Deduct points for large total size
    if (analysis.totalGzipSize > 500000) score -= 20 // 500KB
    else if (analysis.totalGzipSize > 300000) score -= 10 // 300KB
    
    // Deduct points for slow performance
    if (analysis.performance.loadTime > 2000) score -= 15
    else if (analysis.performance.loadTime > 1000) score -= 5
    
    // Deduct points for high-priority recommendations
    const highPriorityRecs = analysis.recommendations.filter(r => r.priority === 'high')
    score -= highPriorityRecs.length * 10
    
    return Math.max(0, score)
  }

  /**
   * Generate optimization report
   */
  generateReport(analysis: BundleAnalysis): string {
    const healthScore = this.getBundleHealthScore(analysis)
    
    return `
# Bundle Analysis Report

## Health Score: ${healthScore}/100

## Bundle Overview
- **Total Size**: ${(analysis.totalSize / 1024).toFixed(1)}KB
- **Gzipped Size**: ${(analysis.totalGzipSize / 1024).toFixed(1)}KB
- **Chunks**: ${analysis.chunks.length}
- **Load Time**: ${analysis.performance.loadTime.toFixed(0)}ms

## Chunk Breakdown
${analysis.chunks.map(chunk => `
- **${chunk.name}**: ${(chunk.size / 1024).toFixed(1)}KB ${chunk.cached ? '(cached)' : ''}
  ${chunk.loadTime ? `Load time: ${chunk.loadTime.toFixed(0)}ms` : ''}
`).join('')}

## Recommendations
${analysis.recommendations.map((rec, index) => `
${index + 1}. **${rec.type.toUpperCase()}** (${rec.priority} priority)
   - ${rec.description}
   - Action: ${rec.action}
   - Estimated savings: ${rec.estimatedSavings}
`).join('')}

## Next Steps
1. Implement high-priority recommendations first
2. Monitor bundle size in CI/CD pipeline
3. Set up performance budgets
4. Regular bundle analysis reviews
`
  }

  /**
   * Cleanup
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

// Singleton instance
let bundleAnalyzer: BundleAnalyzer | null = null

export function getBundleAnalyzer(): BundleAnalyzer {
  if (!bundleAnalyzer) {
    bundleAnalyzer = new BundleAnalyzer()
  }
  return bundleAnalyzer
}

export { BundleAnalyzer, type BundleAnalysis, type BundleRecommendation }
export default getBundleAnalyzer
