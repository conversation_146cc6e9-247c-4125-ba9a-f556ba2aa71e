/**
 * Phase 3 Code Splitting Implementation
 * 
 * Dynamic imports and lazy loading for Phase 2 refactored components
 * Optimized for the new component architecture
 * 
 * <AUTHOR> Team
 */

import React, { lazy, ComponentType, LazyExoticComponent, Suspense } from 'react'
import { getBundleAnalyzer } from './bundleAnalyzer'

/**
 * Component loading priorities
 */
export type LoadingPriority = 'critical' | 'high' | 'medium' | 'low'

/**
 * Code splitting configuration for Phase 2 components
 */
export interface ComponentConfig {
  name: string
  priority: LoadingPriority
  preload: boolean
  chunkName?: string
  dependencies: string[]
  estimatedSize: number
}

/**
 * Phase 2 refactored component configurations
 */
export const PHASE2_COMPONENT_CONFIG: Record<string, ComponentConfig> = {
  // Main container components (critical path)
  RaffleEntryContainer: {
    name: 'RaffleEntryContainer',
    priority: 'critical',
    preload: true,
    chunkName: 'raffle-entry',
    dependencies: ['framer-motion', 'react-hot-toast'],
    estimatedSize: 45000
  },
  
  ChallengeCreateContainer: {
    name: 'Challenge<PERSON>reateContainer',
    priority: 'high',
    preload: false,
    chunkName: 'challenge-create',
    dependencies: ['framer-motion', 'react-hot-toast'],
    estimatedSize: 42000
  },
  
  SeasonalEventsContainer: {
    name: 'SeasonalEventsContainer',
    priority: 'high',
    preload: false,
    chunkName: 'seasonal-events',
    dependencies: ['framer-motion', 'react-hot-toast'],
    estimatedSize: 38000
  },
  
  // Event system components
  EventGrid: {
    name: 'EventGrid',
    priority: 'medium',
    preload: true,
    chunkName: 'event-grid',
    dependencies: ['framer-motion'],
    estimatedSize: 15000
  },
  
  EventCalendar: {
    name: 'EventCalendar',
    priority: 'low',
    preload: false,
    chunkName: 'event-calendar',
    dependencies: ['framer-motion'],
    estimatedSize: 20000
  },
  
  AchievementsView: {
    name: 'AchievementsView',
    priority: 'low',
    preload: false,
    chunkName: 'achievements',
    dependencies: ['framer-motion'],
    estimatedSize: 18000
  },
  
  // Challenge creation steps (admin only)
  BasicInfoStep: {
    name: 'BasicInfoStep',
    priority: 'medium',
    preload: false,
    chunkName: 'challenge-steps',
    dependencies: ['framer-motion'],
    estimatedSize: 8000
  },
  
  ScheduleStep: {
    name: 'ScheduleStep',
    priority: 'medium',
    preload: false,
    chunkName: 'challenge-steps',
    dependencies: ['framer-motion'],
    estimatedSize: 9000
  },
  
  RequirementsStep: {
    name: 'RequirementsStep',
    priority: 'medium',
    preload: false,
    chunkName: 'challenge-steps',
    dependencies: ['framer-motion'],
    estimatedSize: 10000
  },
  
  RewardsStep: {
    name: 'RewardsStep',
    priority: 'medium',
    preload: false,
    chunkName: 'challenge-steps',
    dependencies: ['framer-motion'],
    estimatedSize: 11000
  },
  
  MediaStep: {
    name: 'MediaStep',
    priority: 'medium',
    preload: false,
    chunkName: 'challenge-steps',
    dependencies: ['framer-motion'],
    estimatedSize: 12000
  }
}

/**
 * Enhanced lazy component factory with performance monitoring
 */
export const createOptimizedLazyComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  config: ComponentConfig
): LazyExoticComponent<T> => {
  const LazyComponent = lazy(async () => {
    const startTime = performance.now()
    const bundleAnalyzer = getBundleAnalyzer()
    
    try {
      // Track component loading
      performance.mark(`${config.name}-load-start`)
      
      const module = await importFn()
      const endTime = performance.now()
      const loadTime = endTime - startTime
      
      // Performance tracking
      performance.mark(`${config.name}-load-end`)
      performance.measure(
        `${config.name}-load-duration`,
        `${config.name}-load-start`,
        `${config.name}-load-end`
      )
      
      // Log performance data
      console.log(`[CodeSplitting] Loaded ${config.name} in ${loadTime.toFixed(2)}ms`)
      
      // Warn about slow loading components
      if (loadTime > 1000) {
        console.warn(`[CodeSplitting] Slow component load: ${config.name} (${loadTime.toFixed(2)}ms)`)
      }
      
      return module
    } catch (error) {
      console.error(`[CodeSplitting] Failed to load ${config.name}:`, error)
      
      // Track failed loads
      performance.mark(`${config.name}-load-error`)
      
      throw error
    }
  })
  
  ;(LazyComponent as any).displayName = `Lazy(${config.name})`
  return LazyComponent
}

/**
 * Preload component based on priority and user interaction
 */
export const preloadComponent = (
  importFn: () => Promise<any>,
  config: ComponentConfig
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const preloadFn = () => {
      console.log(`[CodeSplitting] Preloading ${config.name}`)
      importFn()
        .then(() => {
          console.log(`[CodeSplitting] Preloaded ${config.name}`)
          resolve()
        })
        .catch((error) => {
          console.error(`[CodeSplitting] Failed to preload ${config.name}:`, error)
          reject(error)
        })
    }
    
    // Preload based on priority
    switch (config.priority) {
      case 'critical':
        // Immediate preload
        preloadFn()
        break
        
      case 'high':
        // Preload after initial render
        setTimeout(preloadFn, 100)
        break
        
      case 'medium':
        // Preload on idle
        if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
          window.requestIdleCallback(preloadFn)
        } else {
          setTimeout(preloadFn, 1000)
        }
        break
        
      case 'low':
        // Preload on user interaction or after delay
        if (typeof window !== 'undefined') {
          const preloadOnInteraction = () => {
            preloadFn()
            window.removeEventListener('mouseover', preloadOnInteraction)
            window.removeEventListener('touchstart', preloadOnInteraction)
          }
          
          window.addEventListener('mouseover', preloadOnInteraction, { once: true })
          window.addEventListener('touchstart', preloadOnInteraction, { once: true })
          
          // Fallback timeout
          setTimeout(preloadFn, 5000)
        }
        break
    }
  })
}

/**
 * Optimized Suspense wrapper with loading states
 */
export const OptimizedSuspense: React.FC<{
  children: React.ReactNode
  fallback?: React.ReactNode
  componentName?: string
}> = ({ children, fallback, componentName }) => {
  const defaultFallback = (
    <div className="flex items-center justify-center p-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      {componentName && (
        <span className="ml-3 text-gray-400">Loading {componentName}...</span>
      )}
    </div>
  )
  
  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  )
}

/**
 * Phase 2 lazy loaded components
 */
export const Phase2LazyComponents = {
  // Raffle Entry System
  RaffleEntryContainer: createOptimizedLazyComponent(
    () => import('../../components/raffle/RaffleEntry/RaffleEntryContainer'),
    PHASE2_COMPONENT_CONFIG.RaffleEntryContainer
  ),
  
  // Challenge Create System
  ChallengeCreateContainer: createOptimizedLazyComponent(
    () => import('../../components/admin/challenges/ChallengeCreate/ChallengeCreateContainer'),
    PHASE2_COMPONENT_CONFIG.ChallengeCreateContainer
  ),
  
  BasicInfoStep: createOptimizedLazyComponent(
    () => import('../../components/admin/challenges/ChallengeCreate/steps/BasicInfoStep'),
    PHASE2_COMPONENT_CONFIG.BasicInfoStep
  ),
  
  ScheduleStep: createOptimizedLazyComponent(
    () => import('../../components/admin/challenges/ChallengeCreate/steps/ScheduleStep'),
    PHASE2_COMPONENT_CONFIG.ScheduleStep
  ),
  
  RequirementsStep: createOptimizedLazyComponent(
    () => import('../../components/admin/challenges/ChallengeCreate/steps/RequirementsStep'),
    PHASE2_COMPONENT_CONFIG.RequirementsStep
  ),
  
  RewardsStep: createOptimizedLazyComponent(
    () => import('../../components/admin/challenges/ChallengeCreate/steps/RewardsStep'),
    PHASE2_COMPONENT_CONFIG.RewardsStep
  ),
  
  MediaStep: createOptimizedLazyComponent(
    () => import('../../components/admin/challenges/ChallengeCreate/steps/MediaStep'),
    PHASE2_COMPONENT_CONFIG.MediaStep
  ),
  
  // Seasonal Events System
  SeasonalEventsContainer: createOptimizedLazyComponent(
    () => import('../../components/events/SeasonalEvents/SeasonalEventsContainer'),
    PHASE2_COMPONENT_CONFIG.SeasonalEventsContainer
  ),
  
  EventGrid: createOptimizedLazyComponent(
    () => import('../../components/events/SeasonalEvents/components/EventGrid'),
    PHASE2_COMPONENT_CONFIG.EventGrid
  ),
  
  EventCalendar: createOptimizedLazyComponent(
    () => import('../../components/events/SeasonalEvents/components/EventCalendar'),
    PHASE2_COMPONENT_CONFIG.EventCalendar
  ),
  
  AchievementsView: createOptimizedLazyComponent(
    () => import('../../components/events/SeasonalEvents/components/AchievementsView'),
    PHASE2_COMPONENT_CONFIG.AchievementsView
  )
}

/**
 * Initialize preloading for critical components
 */
export const initializePhase2Preloading = (): void => {
  console.log('[CodeSplitting] Initializing Phase 2 component preloading')
  
  // Preload components marked for preloading
  Object.entries(PHASE2_COMPONENT_CONFIG).forEach(([componentName, config]) => {
    if (config.preload) {
      const importFn = getComponentImportFunction(componentName)
      if (importFn) {
        preloadComponent(importFn, config).catch(console.error)
      }
    }
  })
}

/**
 * Get import function for component
 */
const getComponentImportFunction = (componentName: string): (() => Promise<any>) | null => {
  const importMap: Record<string, () => Promise<any>> = {
    RaffleEntryContainer: () => import('../../components/raffle/RaffleEntry/RaffleEntryContainer'),
    ChallengeCreateContainer: () => import('../../components/admin/challenges/ChallengeCreate/ChallengeCreateContainer'),
    SeasonalEventsContainer: () => import('../../components/events/SeasonalEvents/SeasonalEventsContainer'),
    EventGrid: () => import('../../components/events/SeasonalEvents/components/EventGrid'),
    EventCalendar: () => import('../../components/events/SeasonalEvents/components/EventCalendar'),
    AchievementsView: () => import('../../components/events/SeasonalEvents/components/AchievementsView'),
    BasicInfoStep: () => import('../../components/admin/challenges/ChallengeCreate/steps/BasicInfoStep'),
    ScheduleStep: () => import('../../components/admin/challenges/ChallengeCreate/steps/ScheduleStep'),
    RequirementsStep: () => import('../../components/admin/challenges/ChallengeCreate/steps/RequirementsStep'),
    RewardsStep: () => import('../../components/admin/challenges/ChallengeCreate/steps/RewardsStep'),
    MediaStep: () => import('../../components/admin/challenges/ChallengeCreate/steps/MediaStep')
  }
  
  return importMap[componentName] || null
}

/**
 * Get bundle size report for Phase 2 components
 */
export const getPhase2BundleReport = (): {
  totalEstimatedSize: number
  componentCount: number
  criticalPathSize: number
  lazyLoadedSize: number
  recommendations: string[]
} => {
  const components = Object.values(PHASE2_COMPONENT_CONFIG)
  const totalEstimatedSize = components.reduce((sum, config) => sum + config.estimatedSize, 0)
  const criticalComponents = components.filter(c => c.priority === 'critical')
  const criticalPathSize = criticalComponents.reduce((sum, config) => sum + config.estimatedSize, 0)
  const lazyLoadedSize = totalEstimatedSize - criticalPathSize
  
  const recommendations: string[] = []
  
  if (criticalPathSize > 100000) {
    recommendations.push('Consider reducing critical path bundle size (currently > 100KB)')
  }
  
  if (components.filter(c => c.dependencies.includes('framer-motion')).length > 5) {
    recommendations.push('High Framer Motion usage detected - consider CSS animation optimization')
  }
  
  const largeComponents = components.filter(c => c.estimatedSize > 30000)
  if (largeComponents.length > 0) {
    recommendations.push(`Large components detected: ${largeComponents.map(c => c.name).join(', ')}`)
  }
  
  return {
    totalEstimatedSize,
    componentCount: components.length,
    criticalPathSize,
    lazyLoadedSize,
    recommendations
  }
}
