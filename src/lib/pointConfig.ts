/**
 * Unified Point System Configuration
 * 
 * Central configuration for all point calculations across the Syndicaps platform.
 * Ensures consistency between e-commerce and community point systems.
 * 
 * <AUTHOR> Team
 */

// ===== CORE POINT VALUES =====

/**
 * E-commerce point calculations
 * These values are used by both pointsSystem.ts and community features
 */
export const ECOMMERCE_POINTS = {
  // Base purchase points: 5 points per $1 spent
  PER_DOLLAR_SPENT: 5,
  
  // Large order bonus: +10% for orders over $300
  LARGE_ORDER_THRESHOLD: 300,
  LARGE_ORDER_BONUS_PERCENT: 10,
  
  // Review points
  TEXT_REVIEW: 20,
  MEDIA_REVIEW_BONUS: 50,
  
  // Referral points
  SUCCESSFUL_REFERRAL: 500,
} as const

/**
 * Community activity points
 * These values are used by pointEngine.ts for community interactions
 */
export const COMMUNITY_POINTS = {
  // Content creation
  CONTENT_CREATION: 25,
  HIGH_QUALITY_POST: 50,
  TUTORIAL_CREATION: 100,
  PHOTO_UPLOAD: 15,
  
  // Community interaction
  COMMENT_POSTED: 5,
  HELPFUL_COMMENT: 15,
  LIKE_GIVEN: 1,
  SHARE_CONTENT: 10,
  
  // Challenge participation
  CHALLENGE_JOIN: 20,
  CHALLENGE_SUBMIT: 50,
  CHALLENGE_WIN: 200,
  
  // Social engagement
  SOCIAL_SHARE: 25,
  REFERRAL_SIGNUP: 100,
  
  // Daily activities
  DAILY_LOGIN: 10,
  PROFILE_COMPLETE: 50,
  
  // Moderation rewards
  HELPFUL_REPORT: 25,
  QUALITY_CONTRIBUTION: 50,
  
  // Review points (aligned with e-commerce)
  REVIEW_WRITTEN: 30,
} as const

/**
 * Gamification system points
 */
export const GAMIFICATION_POINTS = {
  // Account setup
  SIGNUP_BONUS: 200,
  COMPLETE_PROFILE: 50,
  NEWSLETTER_SUBSCRIPTION: 100,
  BIRTHDAY_PRESENT: 500,
  
  // Social media
  SOCIAL_MEDIA_SHARE: 150,
  
  // Missions
  MONTHLY_MISSION_BASIC: 200,
  MONTHLY_MISSION_ADVANCED: 350,
  MONTHLY_MISSION_EXPERT: 500,
  
  // Streak system (base values - multipliers applied separately)
  STREAK_DAILY_ENGAGEMENT: 5,
  STREAK_WEEKLY_CHALLENGE: 25,
  STREAK_SOCIAL_ACTIVITY: 10,
  STREAK_CREATOR_ACTIVITY: 15,
  
  // Variable rewards
  MYSTERY_BOX_MIN: 50,
  MYSTERY_BOX_MAX: 500,
  DAILY_SURPRISE_MIN: 10,
  DAILY_SURPRISE_MAX: 100,
} as const

// ===== MULTIPLIERS AND BONUSES =====

/**
 * Tier-based multipliers for progressive scaling
 */
export const TIER_MULTIPLIERS = {
  bronze: 1.0,
  silver: 1.1,
  gold: 1.2,
  platinum: 1.3,
  diamond: 1.5
} as const

/**
 * Streak bonuses
 */
export const STREAK_BONUSES = {
  7: 1.1,    // 10% bonus for 1 week streak
  14: 1.15,  // 15% bonus for 2 week streak
  30: 1.2,   // 20% bonus for 1 month streak
  90: 1.25,  // 25% bonus for 3 month streak
  365: 1.3   // 30% bonus for 1 year streak
} as const

/**
 * Quality multipliers based on content assessment
 */
export const QUALITY_MULTIPLIERS = {
  POOR: 0.5,      // 0.0 - 0.3 quality score
  FAIR: 0.75,     // 0.3 - 0.5 quality score
  GOOD: 1.0,      // 0.5 - 0.7 quality score
  EXCELLENT: 1.25, // 0.7 - 0.9 quality score
  OUTSTANDING: 1.5 // 0.9 - 1.0 quality score
} as const

// ===== DAILY LIMITS =====

/**
 * Daily limits per activity type to prevent abuse
 */
export const DAILY_LIMITS = {
  CONTENT_CREATION: { count: 5, points: 250 },
  COMMENT_POSTED: { count: 20, points: 100 },
  LIKE_GIVEN: { count: 50, points: 50 },
  SHARE_CONTENT: { count: 10, points: 250 },
  SOCIAL_SHARE: { count: 5, points: 125 },
  DAILY_LOGIN: { count: 1, points: 10 },
  REVIEW_WRITTEN: { count: 3, points: 90 }
} as const

// ===== RISK ASSESSMENT =====

/**
 * Risk assessment thresholds for fraud prevention
 */
export const RISK_THRESHOLDS = {
  SUSPICIOUS_SCORE: 0.7,
  HIGH_RISK_SCORE: 0.9,
  MAX_POINTS_PER_HOUR: 500,
  MAX_POINTS_PER_DAY: 2000
} as const

// ===== UTILITY FUNCTIONS =====

/**
 * Calculate purchase points with large order bonus
 */
export function calculatePurchasePoints(orderAmount: number): {
  basePoints: number
  bonusPoints: number
  totalPoints: number
} {
  const basePoints = Math.floor(orderAmount * ECOMMERCE_POINTS.PER_DOLLAR_SPENT)
  
  let bonusPoints = 0
  if (orderAmount >= ECOMMERCE_POINTS.LARGE_ORDER_THRESHOLD) {
    bonusPoints = Math.floor(basePoints * (ECOMMERCE_POINTS.LARGE_ORDER_BONUS_PERCENT / 100))
  }
  
  return {
    basePoints,
    bonusPoints,
    totalPoints: basePoints + bonusPoints
  }
}

/**
 * Get quality multiplier based on score
 */
export function getQualityMultiplier(qualityScore: number): number {
  if (qualityScore >= 0.9) return QUALITY_MULTIPLIERS.OUTSTANDING
  if (qualityScore >= 0.7) return QUALITY_MULTIPLIERS.EXCELLENT
  if (qualityScore >= 0.5) return QUALITY_MULTIPLIERS.GOOD
  if (qualityScore >= 0.3) return QUALITY_MULTIPLIERS.FAIR
  return QUALITY_MULTIPLIERS.POOR
}

/**
 * Get streak bonus multiplier
 */
export function getStreakBonus(streakDays: number): number {
  const bonusKeys = Object.keys(STREAK_BONUSES)
    .map(Number)
    .sort((a, b) => b - a) // Sort descending
  
  for (const threshold of bonusKeys) {
    if (streakDays >= threshold) {
      return STREAK_BONUSES[threshold as keyof typeof STREAK_BONUSES]
    }
  }
  
  return 1.0 // No bonus
}

/**
 * Validate daily limits for activity
 */
export function validateDailyLimit(
  activityType: string,
  currentCount: number,
  currentPoints: number
): { allowed: boolean; reason?: string } {
  const limit = DAILY_LIMITS[activityType.toUpperCase() as keyof typeof DAILY_LIMITS]
  
  if (!limit) {
    return { allowed: true } // No limit defined
  }
  
  if (currentCount >= limit.count) {
    return {
      allowed: false,
      reason: `Daily limit reached: ${limit.count} ${activityType} activities per day`
    }
  }
  
  if (currentPoints >= limit.points) {
    return {
      allowed: false,
      reason: `Daily points limit reached: ${limit.points} points from ${activityType} activities`
    }
  }
  
  return { allowed: true }
}

// ===== EXPORTS =====

/**
 * Combined point values for easy access
 */
export const POINT_VALUES = {
  ...ECOMMERCE_POINTS,
  ...COMMUNITY_POINTS,
  ...GAMIFICATION_POINTS
} as const

/**
 * All configuration objects
 */
export const POINT_CONFIG = {
  ECOMMERCE: ECOMMERCE_POINTS,
  COMMUNITY: COMMUNITY_POINTS,
  GAMIFICATION: GAMIFICATION_POINTS,
  TIER_MULTIPLIERS,
  STREAK_BONUSES,
  QUALITY_MULTIPLIERS,
  DAILY_LIMITS,
  RISK_THRESHOLDS
} as const

export default POINT_CONFIG
