/**
 * PWA Provider
 * 
 * React context provider for Progressive Web App functionality
 * including offline detection, service worker management, and sync.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { initializePWA, getOfflineManager, getBackgroundSyncManager } from './serviceWorker'
import { 
  InstallPrompt, 
  OfflineIndicator, 
  UpdateNotification, 
  SyncStatusIndicator 
} from '../../components/pwa/PWAComponents'

// ===== TYPES =====

interface PWAContextType {
  isOnline: boolean
  isInstallable: boolean
  isInstalled: boolean
  hasUpdate: boolean
  syncQueueCount: number
  installApp: () => Promise<void>
  updateApp: () => Promise<void>
  addToSyncQueue: (type: string, data: any) => Promise<string>
}

interface PWAProviderProps {
  children: React.ReactNode
  enableInstallPrompt?: boolean
  enableOfflineIndicator?: boolean
  enableUpdateNotification?: boolean
  enableSyncIndicator?: boolean
  installPromptDelay?: number
}

// ===== CONTEXT =====

const PWAContext = createContext<PWAContextType | undefined>(undefined)

export function usePWAContext(): PWAContextType {
  const context = useContext(PWAContext)
  if (context === undefined) {
    throw new Error('usePWAContext must be used within a PWAProvider')
  }
  return context
}

// ===== PROVIDER =====

export function PWAProvider({
  children,
  enableInstallPrompt = true,
  enableOfflineIndicator = true,
  enableUpdateNotification = true,
  enableSyncIndicator = true,
  installPromptDelay = 10000
}: PWAProviderProps) {
  const [isOnline, setIsOnline] = useState(typeof navigator !== 'undefined' ? navigator.onLine : true)
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [hasUpdate, setHasUpdate] = useState(false)
  const [syncQueueCount, setSyncQueueCount] = useState(0)
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState<ServiceWorkerRegistration | null>(null)
  const [installPromptEvent, setInstallPromptEvent] = useState<any>(null)

  // Initialize PWA functionality
  useEffect(() => {
    const initPWA = async () => {
      try {
        console.log('Initializing PWA...')
        
        // Initialize service worker
        const registration = await initializePWA()
        setServiceWorkerRegistration(registration || null)

        // Initialize offline manager
        const offlineManager = getOfflineManager()
        offlineManager.addListener(setIsOnline)

        // Initialize background sync manager
        const syncManager = getBackgroundSyncManager()
        
        // Monitor sync queue
        const updateSyncCount = () => {
          const status = syncManager.getQueueStatus()
          setSyncQueueCount(status.pending)
        }
        
        updateSyncCount()
        const syncInterval = setInterval(updateSyncCount, 5000)

        // Check if app is installed
        const checkInstalled = () => {
          const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                               (window.navigator as any).standalone === true
          setIsInstalled(isStandalone)
        }
        
        checkInstalled()

        console.log('PWA initialized successfully')

        return () => {
          clearInterval(syncInterval)
        }
      } catch (error) {
        console.error('PWA initialization failed:', error)
      }
    }

    initPWA()
  }, [])

  // Listen for install prompt
  useEffect(() => {
    const handleBeforeInstallPrompt = (event: any) => {
      event.preventDefault()
      setInstallPromptEvent(event)
      setIsInstallable(true)
      console.log('Install prompt available')
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setInstallPromptEvent(null)
      console.log('App installed successfully')
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  // Listen for service worker updates
  useEffect(() => {
    const handleSWUpdate = () => {
      setHasUpdate(true)
      console.log('Service worker update available')
    }

    window.addEventListener('sw-update-available', handleSWUpdate)

    return () => {
      window.removeEventListener('sw-update-available', handleSWUpdate)
    }
  }, [])

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      console.log('App is online')
    }

    const handleOffline = () => {
      setIsOnline(false)
      console.log('App is offline')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Install app function
  const installApp = async () => {
    if (!installPromptEvent) {
      throw new Error('Install prompt not available')
    }

    try {
      const result = await installPromptEvent.prompt()
      setInstallPromptEvent(null)
      setIsInstallable(false)
      
      if (result.outcome === 'accepted') {
        console.log('App installation accepted')
      } else {
        console.log('App installation dismissed')
      }
    } catch (error) {
      console.error('App installation failed:', error)
      throw error
    }
  }

  // Update app function
  const updateApp = async () => {
    if (!serviceWorkerRegistration || !serviceWorkerRegistration.waiting) {
      throw new Error('No service worker update available')
    }

    try {
      // Tell the waiting service worker to skip waiting
      serviceWorkerRegistration.waiting.postMessage({ type: 'SKIP_WAITING' })
      
      // Wait for the new service worker to take control
      await new Promise((resolve) => {
        const handleControllerChange = () => {
          navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange)
          resolve(true)
        }
        navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange)
      })

      setHasUpdate(false)
      
      // Reload the page to apply updates
      window.location.reload()
    } catch (error) {
      console.error('App update failed:', error)
      throw error
    }
  }

  // Add to sync queue function
  const addToSyncQueue = async (type: string, data: any): Promise<string> => {
    const syncManager = getBackgroundSyncManager()
    const syncId = await syncManager.addToQueue(type, data)
    
    // Update sync count
    const status = syncManager.getQueueStatus()
    setSyncQueueCount(status.pending)
    
    return syncId
  }

  // Context value
  const contextValue: PWAContextType = {
    isOnline,
    isInstallable,
    isInstalled,
    hasUpdate,
    syncQueueCount,
    installApp,
    updateApp,
    addToSyncQueue
  }

  return (
    <PWAContext.Provider value={contextValue}>
      {children}
      
      {/* PWA UI Components */}
      {enableInstallPrompt && isInstallable && !isInstalled && (
        <InstallPrompt 
          showDelay={installPromptDelay}
          onInstall={() => {
            installApp().catch(console.error)
          }}
        />
      )}
      
      {enableOfflineIndicator && (
        <OfflineIndicator />
      )}
      
      {enableUpdateNotification && hasUpdate && (
        <UpdateNotification />
      )}
      
      {enableSyncIndicator && syncQueueCount > 0 && (
        <SyncStatusIndicator />
      )}
    </PWAContext.Provider>
  )
}

// ===== PWA STATUS HOOK =====

export function usePWAStatus() {
  const context = usePWAContext()
  
  return {
    isOnline: context.isOnline,
    isInstallable: context.isInstallable,
    isInstalled: context.isInstalled,
    hasUpdate: context.hasUpdate,
    syncQueueCount: context.syncQueueCount
  }
}

// ===== PWA ACTIONS HOOK =====

export function usePWAActions() {
  const context = usePWAContext()
  
  return {
    installApp: context.installApp,
    updateApp: context.updateApp,
    addToSyncQueue: context.addToSyncQueue
  }
}

// ===== GAMIFICATION PWA INTEGRATION =====

export function useGamificationPWA() {
  const { addToSyncQueue, isOnline } = usePWAContext()

  const syncAchievementProgress = async (achievementId: string, progress: number) => {
    if (isOnline) {
      // If online, sync immediately
      return null
    }
    
    // If offline, add to sync queue
    return await addToSyncQueue('achievement-progress', {
      achievementId,
      progress,
      timestamp: Date.now()
    })
  }

  const syncChallengeProgress = async (challengeId: string, progress: number, isCompleted: boolean) => {
    if (isOnline) {
      return null
    }
    
    return await addToSyncQueue('challenge-progress', {
      challengeId,
      progress,
      isCompleted,
      timestamp: Date.now()
    })
  }

  const syncPointsTransaction = async (type: 'earned' | 'spent', amount: number, source: string) => {
    if (isOnline) {
      return null
    }
    
    return await addToSyncQueue('points-transaction', {
      type,
      amount,
      source,
      timestamp: Date.now()
    })
  }

  const syncUserActivity = async (activityType: string, data: any) => {
    if (isOnline) {
      return null
    }
    
    return await addToSyncQueue('user-activity', {
      type: activityType,
      data,
      timestamp: Date.now()
    })
  }

  return {
    syncAchievementProgress,
    syncChallengeProgress,
    syncPointsTransaction,
    syncUserActivity,
    isOnline
  }
}

// ===== OFFLINE STORAGE HELPERS =====

export function useOfflineStorage() {
  const store = (key: string, data: any) => {
    try {
      localStorage.setItem(`offline_${key}`, JSON.stringify({
        data,
        timestamp: Date.now()
      }))
    } catch (error) {
      console.warn('Failed to store offline data:', error)
    }
  }

  const retrieve = (key: string, maxAge: number = 24 * 60 * 60 * 1000) => {
    try {
      const stored = localStorage.getItem(`offline_${key}`)
      if (!stored) return null

      const { data, timestamp } = JSON.parse(stored)
      
      // Check if data is too old
      if (Date.now() - timestamp > maxAge) {
        localStorage.removeItem(`offline_${key}`)
        return null
      }

      return data
    } catch (error) {
      console.warn('Failed to retrieve offline data:', error)
      return null
    }
  }

  const remove = (key: string) => {
    try {
      localStorage.removeItem(`offline_${key}`)
    } catch (error) {
      console.warn('Failed to remove offline data:', error)
    }
  }

  const clear = () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('offline_'))
      keys.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('Failed to clear offline data:', error)
    }
  }

  return {
    store,
    retrieve,
    remove,
    clear
  }
}

export default PWAProvider