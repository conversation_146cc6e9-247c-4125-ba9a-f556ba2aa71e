/**
 * Service Worker Configuration
 * 
 * Comprehensive service worker for offline functionality,
 * caching strategies, and background sync capabilities.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

// ===== CACHE CONFIGURATION =====

export const CACHE_CONFIG = {
  // Cache names with versioning
  STATIC_CACHE: 'syndicaps-static-v1',
  DYNAMIC_CACHE: 'syndicaps-dynamic-v1',
  API_CACHE: 'syndicaps-api-v1',
  IMAGE_CACHE: 'syndicaps-images-v1',
  GAMIFICATION_CACHE: 'syndicaps-gamification-v1',
  
  // Cache limits
  MAX_DYNAMIC_ITEMS: 100,
  MAX_API_ITEMS: 50,
  MAX_IMAGE_ITEMS: 200,
  
  // Cache durations (in milliseconds)
  API_CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
  IMAGE_CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours
  STATIC_CACHE_DURATION: 7 * 24 * 60 * 60 * 1000, // 7 days
  
  // Network timeout
  NETWORK_TIMEOUT: 3000
}

// ===== STATIC RESOURCES =====

export const STATIC_RESOURCES = [
  '/',
  '/manifest.json',
  '/offline.html',
  '/static/icons/icon-192x192.png',
  '/static/icons/icon-512x512.png',
  '/static/icons/apple-touch-icon.png',
  '/static/fonts/inter.woff2',
  '/static/images/logo.svg'
]

// ===== API ENDPOINTS FOR CACHING =====

export const CACHEABLE_API_PATTERNS = [
  /\/api\/users\/profile/,
  /\/api\/achievements/,
  /\/api\/challenges/,
  /\/api\/rewards/,
  /\/api\/leaderboard/,
  /\/api\/tiers/,
  /\/api\/gamification\/analytics/
]

// ===== SERVICE WORKER REGISTRATION =====

export function registerServiceWorker(): Promise<ServiceWorkerRegistration | undefined> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    console.warn('Service Worker not supported')
    return Promise.resolve(undefined)
  }

  // Temporarily disable service worker registration to prevent Firebase Installations errors
  console.log('⚠️ Service Worker registration temporarily disabled to prevent Firebase errors')
  console.log('TODO: Re-enable after fixing Firebase Installations configuration')
  return Promise.resolve(undefined)

  /* DISABLED - Re-enable after fixing Firebase Installations
  return navigator.serviceWorker.register('/sw.js', {
    scope: '/',
    updateViaCache: 'none'
  })
    .then((registration) => {
      console.log('Service Worker registered successfully:', registration)

      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New version available
              dispatchEvent(new CustomEvent('sw-update-available', {
                detail: { registration }
              }))
            }
          })
        }
      })

      return registration
    })
    .catch((error) => {
      console.error('Service Worker registration failed:', error)
      throw error
    })
  */
}

// ===== SERVICE WORKER UPDATE MANAGEMENT =====

export function updateServiceWorker(registration: ServiceWorkerRegistration): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!registration.waiting) {
      reject(new Error('No waiting service worker'))
      return
    }

    const messageChannel = new MessageChannel()
    messageChannel.port1.onmessage = (event) => {
      if (event.data.error) {
        reject(new Error(event.data.error))
      } else {
        resolve()
      }
    }

    registration.waiting.postMessage(
      { type: 'SKIP_WAITING' },
      [messageChannel.port2]
    )
  })
}

// ===== OFFLINE DETECTION =====

export class OfflineManager {
  private isOnline: boolean = navigator.onLine
  private listeners: Set<(isOnline: boolean) => void> = new Set()
  private retryQueue: Array<() => Promise<any>> = []
  private retryInterval: NodeJS.Timeout | null = null

  constructor() {
    this.setupEventListeners()
    this.startRetryManager()
  }

  private setupEventListeners(): void {
    window.addEventListener('online', this.handleOnline.bind(this))
    window.addEventListener('offline', this.handleOffline.bind(this))
  }

  private handleOnline(): void {
    this.isOnline = true
    this.notifyListeners()
    this.processRetryQueue()
    
    // Dispatch custom event
    dispatchEvent(new CustomEvent('app-online'))
  }

  private handleOffline(): void {
    this.isOnline = false
    this.notifyListeners()
    
    // Dispatch custom event
    dispatchEvent(new CustomEvent('app-offline'))
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.isOnline))
  }

  private async processRetryQueue(): Promise<void> {
    if (!this.isOnline || this.retryQueue.length === 0) return

    const queue = [...this.retryQueue]
    this.retryQueue = []

    for (const retryFn of queue) {
      try {
        await retryFn()
      } catch (error) {
        console.warn('Retry failed:', error)
        // Re-add to queue if still offline
        if (!this.isOnline) {
          this.retryQueue.push(retryFn)
        }
      }
    }
  }

  private startRetryManager(): void {
    this.retryInterval = setInterval(() => {
      if (this.isOnline && this.retryQueue.length > 0) {
        this.processRetryQueue()
      }
    }, 30000) // Retry every 30 seconds
  }

  public getOnlineStatus(): boolean {
    return this.isOnline
  }

  public addListener(listener: (isOnline: boolean) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  public addToRetryQueue(retryFn: () => Promise<any>): void {
    this.retryQueue.push(retryFn)
  }

  public clearRetryQueue(): void {
    this.retryQueue = []
  }

  public destroy(): void {
    window.removeEventListener('online', this.handleOnline.bind(this))
    window.removeEventListener('offline', this.handleOffline.bind(this))
    if (this.retryInterval) {
      clearInterval(this.retryInterval)
    }
    this.listeners.clear()
    this.retryQueue = []
  }
}

// ===== CACHE MANAGEMENT =====

export class CacheManager {
  private cacheName: string
  private maxItems: number
  private cacheDuration: number

  constructor(cacheName: string, maxItems: number = 50, cacheDuration: number = 24 * 60 * 60 * 1000) {
    this.cacheName = cacheName
    this.maxItems = maxItems
    this.cacheDuration = cacheDuration
  }

  async get(key: string): Promise<Response | undefined> {
    try {
      const cache = await caches.open(this.cacheName)
      const response = await cache.match(key)
      
      if (response) {
        // Check if cache is still valid
        const cachedTime = response.headers.get('sw-cached-time')
        if (cachedTime) {
          const cacheAge = Date.now() - parseInt(cachedTime)
          if (cacheAge > this.cacheDuration) {
            await cache.delete(key)
            return undefined
          }
        }
        
        return response
      }
    } catch (error) {
      console.warn('Cache get error:', error)
    }
    
    return undefined
  }

  async set(key: string, response: Response): Promise<void> {
    try {
      const cache = await caches.open(this.cacheName)
      
      // Clone response and add timestamp
      const responseToCache = response.clone()
      const headers = new Headers(responseToCache.headers)
      headers.set('sw-cached-time', Date.now().toString())
      
      const modifiedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers
      })
      
      await cache.put(key, modifiedResponse)
      
      // Enforce cache size limit
      await this.enforceCacheLimit(cache)
    } catch (error) {
      console.warn('Cache set error:', error)
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      const cache = await caches.open(this.cacheName)
      return await cache.delete(key)
    } catch (error) {
      console.warn('Cache delete error:', error)
      return false
    }
  }

  async clear(): Promise<void> {
    try {
      await caches.delete(this.cacheName)
    } catch (error) {
      console.warn('Cache clear error:', error)
    }
  }

  private async enforceCacheLimit(cache: Cache): Promise<void> {
    try {
      const keys = await cache.keys()
      if (keys.length > this.maxItems) {
        // Remove oldest entries
        const keysToDelete = keys.slice(0, keys.length - this.maxItems)
        await Promise.all(keysToDelete.map(key => cache.delete(key)))
      }
    } catch (error) {
      console.warn('Cache limit enforcement error:', error)
    }
  }

  async getStats(): Promise<{
    size: number
    maxSize: number
    usage: number
  }> {
    try {
      const cache = await caches.open(this.cacheName)
      const keys = await cache.keys()
      
      return {
        size: keys.length,
        maxSize: this.maxItems,
        usage: (keys.length / this.maxItems) * 100
      }
    } catch (error) {
      console.warn('Cache stats error:', error)
      return { size: 0, maxSize: this.maxItems, usage: 0 }
    }
  }
}

// ===== BACKGROUND SYNC =====

export class BackgroundSyncManager {
  private syncQueue: Array<{
    id: string
    type: string
    data: any
    timestamp: number
    retries: number
  }> = []

  private maxRetries: number = 3
  private retryDelay: number = 5000

  constructor() {
    this.loadQueue()
    this.setupSyncListener()
  }

  private loadQueue(): void {
    try {
      const stored = localStorage.getItem('sync-queue')
      if (stored) {
        this.syncQueue = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load sync queue:', error)
    }
  }

  private saveQueue(): void {
    try {
      localStorage.setItem('sync-queue', JSON.stringify(this.syncQueue))
    } catch (error) {
      console.warn('Failed to save sync queue:', error)
    }
  }

  private setupSyncListener(): void {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.type === 'BACKGROUND_SYNC') {
          this.processQueue()
        }
      })
    }
  }

  async addToQueue(type: string, data: any): Promise<string> {
    const id = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    this.syncQueue.push({
      id,
      type,
      data,
      timestamp: Date.now(),
      retries: 0
    })
    
    this.saveQueue()
    
    // Try to register background sync
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      try {
        const registration = await navigator.serviceWorker.ready
        await (registration as any).sync.register(`sync-${type}`)
      } catch (error) {
        console.warn('Background sync registration failed:', error)
        // Fallback to immediate processing if online
        if (navigator.onLine) {
          setTimeout(() => this.processQueue(), 1000)
        }
      }
    }
    
    return id
  }

  async processQueue(): Promise<void> {
    if (this.syncQueue.length === 0) return

    const itemsToProcess = [...this.syncQueue]
    
    for (const item of itemsToProcess) {
      try {
        const success = await this.processSyncItem(item)
        
        if (success) {
          // Remove from queue
          this.syncQueue = this.syncQueue.filter(q => q.id !== item.id)
        } else {
          // Increment retry count
          const queueItem = this.syncQueue.find(q => q.id === item.id)
          if (queueItem) {
            queueItem.retries++
            
            // Remove if max retries exceeded
            if (queueItem.retries >= this.maxRetries) {
              this.syncQueue = this.syncQueue.filter(q => q.id !== item.id)
              console.warn(`Sync item ${item.id} exceeded max retries`)
            }
          }
        }
      } catch (error) {
        console.error('Sync processing error:', error)
      }
    }
    
    this.saveQueue()
  }

  private async processSyncItem(item: any): Promise<boolean> {
    try {
      switch (item.type) {
        case 'achievement-progress':
          return await this.syncAchievementProgress(item.data)
        case 'challenge-progress':
          return await this.syncChallengeProgress(item.data)
        case 'user-activity':
          return await this.syncUserActivity(item.data)
        case 'points-transaction':
          return await this.syncPointsTransaction(item.data)
        default:
          console.warn(`Unknown sync type: ${item.type}`)
          return false
      }
    } catch (error) {
      console.error(`Sync error for ${item.type}:`, error)
      return false
    }
  }

  private async syncAchievementProgress(data: any): Promise<boolean> {
    // Implementation would sync with Firebase
    console.log('Syncing achievement progress:', data)
    return true
  }

  private async syncChallengeProgress(data: any): Promise<boolean> {
    // Implementation would sync with Firebase
    console.log('Syncing challenge progress:', data)
    return true
  }

  private async syncUserActivity(data: any): Promise<boolean> {
    // Implementation would sync with Firebase
    console.log('Syncing user activity:', data)
    return true
  }

  private async syncPointsTransaction(data: any): Promise<boolean> {
    // Implementation would sync with Firebase
    console.log('Syncing points transaction:', data)
    return true
  }

  getQueueStatus(): {
    pending: number
    failed: number
    oldestItem?: number
  } {
    const failed = this.syncQueue.filter(item => item.retries > 0).length
    const oldestItem = this.syncQueue.length > 0 
      ? Math.min(...this.syncQueue.map(item => item.timestamp))
      : undefined

    return {
      pending: this.syncQueue.length,
      failed,
      oldestItem
    }
  }

  clearQueue(): void {
    this.syncQueue = []
    this.saveQueue()
  }
}

// ===== EXPORTS =====

let offlineManager: OfflineManager | null = null
let backgroundSyncManager: BackgroundSyncManager | null = null

export function getOfflineManager(): OfflineManager {
  if (!offlineManager) {
    offlineManager = new OfflineManager()
  }
  return offlineManager
}

export function getBackgroundSyncManager(): BackgroundSyncManager {
  if (!backgroundSyncManager) {
    backgroundSyncManager = new BackgroundSyncManager()
  }
  return backgroundSyncManager
}

export function initializePWA(): Promise<ServiceWorkerRegistration | undefined> {
  return registerServiceWorker()
}

export default {
  CACHE_CONFIG,
  STATIC_RESOURCES,
  CACHEABLE_API_PATTERNS,
  registerServiceWorker,
  updateServiceWorker,
  OfflineManager,
  CacheManager,
  BackgroundSyncManager,
  getOfflineManager,
  getBackgroundSyncManager,
  initializePWA
}