/**
 * Optimized WebSocket Manager
 * 
 * High-performance WebSocket implementation with connection pooling,
 * automatic reconnection, and real-time monitoring for community features.
 * 
 * Phase 3 Advanced Optimizations - Community System Audit
 * 
 * <AUTHOR> Team
 */

interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnect: {
    enabled: boolean
    maxAttempts: number
    delay: number
    backoffMultiplier: number
    maxDelay: number
  }
  heartbeat: {
    enabled: boolean
    interval: number
    timeout: number
  }
  compression: boolean
  binaryType: 'blob' | 'arraybuffer'
  maxMessageSize: number
  queueSize: number
}

interface ConnectionMetrics {
  connectionTime: number
  reconnectCount: number
  messagesSent: number
  messagesReceived: number
  bytesTransferred: number
  lastActivity: Date
  latency: number
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
}

interface QueuedMessage {
  data: any
  timestamp: number
  priority: 'high' | 'normal' | 'low'
  retries: number
  maxRetries: number
}

export class OptimizedWebSocketManager {
  private static instances: Map<string, OptimizedWebSocketManager> = new Map()
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private metrics: ConnectionMetrics
  private messageQueue: QueuedMessage[] = []
  private heartbeatInterval: NodeJS.Timeout | null = null
  private reconnectTimeout: NodeJS.Timeout | null = null
  private listeners: Map<string, Set<Function>> = new Map()
  private isReconnecting = false
  private connectionPromise: Promise<void> | null = null

  private constructor(config: WebSocketConfig) {
    this.config = config
    this.metrics = {
      connectionTime: 0,
      reconnectCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0,
      lastActivity: new Date(),
      latency: 0,
      status: 'disconnected'
    }
  }

  /**
   * Get or create WebSocket instance (singleton pattern)
   */
  static getInstance(key: string, config: WebSocketConfig): OptimizedWebSocketManager {
    if (!this.instances.has(key)) {
      this.instances.set(key, new OptimizedWebSocketManager(config))
    }
    return this.instances.get(key)!
  }

  /**
   * Connect to WebSocket with optimizations
   */
  async connect(): Promise<void> {
    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = this.performConnection()
    return this.connectionPromise
  }

  private async performConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.metrics.status = 'connecting'
        const startTime = performance.now()

        // Create WebSocket with optimizations
        this.ws = new WebSocket(this.config.url, this.config.protocols)
        this.ws.binaryType = this.config.binaryType

        // Connection opened
        this.ws.onopen = () => {
          this.metrics.connectionTime = performance.now() - startTime
          this.metrics.status = 'connected'
          this.metrics.lastActivity = new Date()
          this.isReconnecting = false
          this.connectionPromise = null

          // Start heartbeat
          if (this.config.heartbeat.enabled) {
            this.startHeartbeat()
          }

          // Process queued messages
          this.processMessageQueue()

          this.emit('connected', { metrics: this.metrics })
          resolve()
        }

        // Message received
        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }

        // Connection closed
        this.ws.onclose = (event) => {
          this.handleClose(event)
        }

        // Connection error
        this.ws.onerror = (error) => {
          this.metrics.status = 'error'
          this.emit('error', error)
          
          if (!this.isReconnecting) {
            reject(error)
          }
        }

        // Connection timeout
        setTimeout(() => {
          if (this.metrics.status === 'connecting') {
            this.ws?.close()
            reject(new Error('Connection timeout'))
          }
        }, 10000) // 10 second timeout

      } catch (error) {
        this.metrics.status = 'error'
        reject(error)
      }
    })
  }

  /**
   * Send message with queuing and retry logic
   */
  send(data: any, options: {
    priority?: 'high' | 'normal' | 'low'
    maxRetries?: number
    compress?: boolean
  } = {}): Promise<void> {
    const { priority = 'normal', maxRetries = 3, compress = false } = options

    return new Promise((resolve, reject) => {
      const message: QueuedMessage = {
        data,
        timestamp: Date.now(),
        priority,
        retries: 0,
        maxRetries
      }

      // If connected, send immediately
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.sendMessage(message)
          .then(resolve)
          .catch(reject)
      } else {
        // Queue message for later
        this.queueMessage(message)
        resolve() // Resolve immediately for queued messages
      }
    })
  }

  /**
   * Subscribe to events
   */
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(callback)
  }

  /**
   * Unsubscribe from events
   */
  off(event: string, callback: Function): void {
    this.listeners.get(event)?.delete(callback)
  }

  /**
   * Get connection metrics
   */
  getMetrics(): ConnectionMetrics {
    return { ...this.metrics }
  }

  /**
   * Close connection
   */
  close(): void {
    this.stopHeartbeat()
    this.clearReconnectTimeout()
    
    if (this.ws) {
      this.ws.close(1000, 'Client closing')
      this.ws = null
    }
    
    this.metrics.status = 'disconnected'
  }

  /**
   * Force reconnection
   */
  async reconnect(): Promise<void> {
    this.close()
    await this.connect()
  }

  // Private methods

  private handleMessage(event: MessageEvent): void {
    this.metrics.messagesReceived++
    this.metrics.lastActivity = new Date()
    
    // Calculate message size
    const messageSize = typeof event.data === 'string' 
      ? new Blob([event.data]).size 
      : event.data.byteLength || 0
    
    this.metrics.bytesTransferred += messageSize

    // Handle heartbeat response
    if (this.isHeartbeatMessage(event.data)) {
      this.handleHeartbeatResponse(event.data)
      return
    }

    // Parse message
    try {
      const message = typeof event.data === 'string' 
        ? JSON.parse(event.data) 
        : event.data

      this.emit('message', message)
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
      this.emit('parseError', { error, data: event.data })
    }
  }

  private handleClose(event: CloseEvent): void {
    this.metrics.status = 'disconnected'
    this.stopHeartbeat()
    
    this.emit('disconnected', {
      code: event.code,
      reason: event.reason,
      wasClean: event.wasClean
    })

    // Auto-reconnect if enabled and not a clean close
    if (this.config.reconnect.enabled && !event.wasClean && !this.isReconnecting) {
      this.scheduleReconnect()
    }
  }

  private scheduleReconnect(): void {
    if (this.metrics.reconnectCount >= this.config.reconnect.maxAttempts) {
      this.emit('maxReconnectAttemptsReached', { attempts: this.metrics.reconnectCount })
      return
    }

    this.isReconnecting = true
    this.metrics.reconnectCount++

    // Calculate delay with exponential backoff
    const delay = Math.min(
      this.config.reconnect.delay * Math.pow(this.config.reconnect.backoffMultiplier, this.metrics.reconnectCount - 1),
      this.config.reconnect.maxDelay
    )

    this.emit('reconnecting', { 
      attempt: this.metrics.reconnectCount, 
      delay,
      maxAttempts: this.config.reconnect.maxAttempts 
    })

    this.reconnectTimeout = setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error)
        this.scheduleReconnect() // Try again
      })
    }, delay)
  }

  private clearReconnectTimeout(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        const heartbeat = {
          type: 'heartbeat',
          timestamp: Date.now()
        }
        this.ws.send(JSON.stringify(heartbeat))
      }
    }, this.config.heartbeat.interval)
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private isHeartbeatMessage(data: any): boolean {
    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data
      return message.type === 'heartbeat' || message.type === 'pong'
    } catch {
      return false
    }
  }

  private handleHeartbeatResponse(data: any): void {
    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data
      if (message.timestamp) {
        this.metrics.latency = Date.now() - message.timestamp
      }
    } catch (error) {
      console.warn('Failed to parse heartbeat response:', error)
    }
  }

  private async sendMessage(message: QueuedMessage): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not connected')
    }

    try {
      const serialized = JSON.stringify(message.data)
      
      // Check message size
      if (serialized.length > this.config.maxMessageSize) {
        throw new Error(`Message too large: ${serialized.length} bytes`)
      }

      this.ws.send(serialized)
      this.metrics.messagesSent++
      this.metrics.bytesTransferred += serialized.length
      this.metrics.lastActivity = new Date()
    } catch (error) {
      message.retries++
      if (message.retries < message.maxRetries) {
        // Retry after delay
        setTimeout(() => {
          this.sendMessage(message).catch(console.error)
        }, 1000 * message.retries)
      } else {
        throw error
      }
    }
  }

  private queueMessage(message: QueuedMessage): void {
    // Remove oldest messages if queue is full
    while (this.messageQueue.length >= this.config.queueSize) {
      this.messageQueue.shift()
    }

    // Insert message based on priority
    const insertIndex = this.findInsertIndex(message.priority)
    this.messageQueue.splice(insertIndex, 0, message)
  }

  private findInsertIndex(priority: 'high' | 'normal' | 'low'): number {
    const priorityOrder = { high: 0, normal: 1, low: 2 }
    const targetPriority = priorityOrder[priority]

    for (let i = 0; i < this.messageQueue.length; i++) {
      if (priorityOrder[this.messageQueue[i].priority] > targetPriority) {
        return i
      }
    }
    return this.messageQueue.length
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const message = this.messageQueue.shift()!
      this.sendMessage(message).catch(error => {
        console.error('Failed to send queued message:', error)
      })
    }
  }

  private emit(event: string, data?: any): void {
    this.listeners.get(event)?.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('Error in WebSocket event listener:', error)
      }
    })
  }
}

// Default configurations for different use cases
export const WebSocketConfigs = {
  community: {
    url: process.env.NEXT_PUBLIC_WS_URL || 'wss://api.syndicaps.com/community',
    reconnect: {
      enabled: true,
      maxAttempts: 5,
      delay: 1000,
      backoffMultiplier: 2,
      maxDelay: 30000
    },
    heartbeat: {
      enabled: true,
      interval: 30000,
      timeout: 5000
    },
    compression: true,
    binaryType: 'arraybuffer' as const,
    maxMessageSize: 64 * 1024, // 64KB
    queueSize: 100
  },

  realtime: {
    url: process.env.NEXT_PUBLIC_WS_URL || 'wss://api.syndicaps.com/realtime',
    reconnect: {
      enabled: true,
      maxAttempts: 10,
      delay: 500,
      backoffMultiplier: 1.5,
      maxDelay: 10000
    },
    heartbeat: {
      enabled: true,
      interval: 15000,
      timeout: 3000
    },
    compression: true,
    binaryType: 'arraybuffer' as const,
    maxMessageSize: 32 * 1024, // 32KB
    queueSize: 50
  }
}

export default OptimizedWebSocketManager
