/**
 * Enhanced Security Implementation
 * 
 * Comprehensive security hardening for community features including
 * CSP headers, rate limiting, XSS/CSRF protection, and input validation.
 * 
 * Phase 3 Advanced Optimizations - Community System Audit
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import rateLimit from 'express-rate-limit'
import helmet from 'helmet'
import DOMPurify from 'isomorphic-dompurify'
import { z } from 'zod'

interface SecurityConfig {
  rateLimit: {
    windowMs: number
    max: number
    skipSuccessfulRequests: boolean
  }
  csp: {
    enabled: boolean
    reportOnly: boolean
    directives: Record<string, string[]>
  }
  cors: {
    origin: string[]
    credentials: boolean
    methods: string[]
  }
  validation: {
    maxInputLength: number
    allowedTags: string[]
    allowedAttributes: Record<string, string[]>
  }
}

export class EnhancedSecurityManager {
  private static config: SecurityConfig = {
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      skipSuccessfulRequests: false
    },
    csp: {
      enabled: true,
      reportOnly: false,
      directives: {
        'default-src': ["'self'"],
        'script-src': [
          "'self'",
          "'unsafe-inline'", // Required for Next.js
          "'unsafe-eval'", // Required for development
          'https://vercel.live',
          'https://www.googletagmanager.com',
          'https://www.google-analytics.com'
        ],
        'style-src': [
          "'self'",
          "'unsafe-inline'", // Required for styled-components
          'https://fonts.googleapis.com'
        ],
        'img-src': [
          "'self'",
          'data:',
          'blob:',
          'https:',
          'https://images.unsplash.com',
          'https://res.cloudinary.com'
        ],
        'font-src': [
          "'self'",
          'https://fonts.gstatic.com'
        ],
        'connect-src': [
          "'self'",
          'https://api.syndicaps.com',
          'https://firestore.googleapis.com',
          'https://firebase.googleapis.com',
          'https://www.google-analytics.com'
        ],
        'frame-src': [
          "'self'",
          'https://www.youtube.com',
          'https://player.vimeo.com'
        ],
        'object-src': ["'none'"],
        'base-uri': ["'self'"],
        'form-action': ["'self'"],
        'frame-ancestors': ["'none'"],
        'upgrade-insecure-requests': []
      }
    },
    cors: {
      origin: [
        'https://syndicaps.com',
        'https://www.syndicaps.com',
        'https://app.syndicaps.com'
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    },
    validation: {
      maxInputLength: 10000,
      allowedTags: [
        'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li', 'blockquote', 'code', 'pre', 'a', 'img'
      ],
      allowedAttributes: {
        'a': ['href', 'title', 'target'],
        'img': ['src', 'alt', 'title', 'width', 'height'],
        'code': ['class'],
        'pre': ['class']
      }
    }
  }

  /**
   * Generate Content Security Policy header
   */
  static generateCSPHeader(): string {
    const { directives } = this.config.csp
    
    const cspParts = Object.entries(directives).map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive
      }
      return `${directive} ${sources.join(' ')}`
    })

    return cspParts.join('; ')
  }

  /**
   * Apply security headers to response
   */
  static applySecurityHeaders(response: NextResponse): NextResponse {
    // Content Security Policy
    if (this.config.csp.enabled) {
      const cspHeader = this.generateCSPHeader()
      const headerName = this.config.csp.reportOnly 
        ? 'Content-Security-Policy-Report-Only'
        : 'Content-Security-Policy'
      
      response.headers.set(headerName, cspHeader)
    }

    // Security headers
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
    
    // HSTS (only in production)
    if (process.env.NODE_ENV === 'production') {
      response.headers.set(
        'Strict-Transport-Security',
        'max-age=31536000; includeSubDomains; preload'
      )
    }

    return response
  }

  /**
   * Rate limiting middleware
   */
  static createRateLimiter(options?: Partial<SecurityConfig['rateLimit']>) {
    const config = { ...this.config.rateLimit, ...options }
    
    return rateLimit({
      windowMs: config.windowMs,
      max: config.max,
      skipSuccessfulRequests: config.skipSuccessfulRequests,
      message: {
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: Math.ceil(config.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Too many requests from this IP, please try again later.',
          retryAfter: Math.ceil(config.windowMs / 1000)
        })
      }
    })
  }

  /**
   * Sanitize HTML content
   */
  static sanitizeHTML(content: string): string {
    const { allowedTags, allowedAttributes } = this.config.validation

    return DOMPurify.sanitize(content, {
      ALLOWED_TAGS: allowedTags,
      ALLOWED_ATTR: Object.keys(allowedAttributes).reduce((acc, tag) => {
        return [...acc, ...allowedAttributes[tag]]
      }, [] as string[]),
      ALLOW_DATA_ATTR: false,
      ALLOW_UNKNOWN_PROTOCOLS: false,
      SANITIZE_DOM: true,
      KEEP_CONTENT: true
    })
  }

  /**
   * Validate and sanitize user input
   */
  static validateInput<T>(
    input: unknown,
    schema: z.ZodSchema<T>,
    sanitize: boolean = true
  ): { success: boolean; data?: T; errors?: string[] } {
    try {
      // Basic validation with Zod
      const validated = schema.parse(input)
      
      // Additional security checks
      if (typeof validated === 'object' && validated !== null) {
        const sanitized = sanitize ? this.sanitizeObject(validated) : validated
        return { success: true, data: sanitized as T }
      }
      
      return { success: true, data: validated }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        }
      }
      
      return {
        success: false,
        errors: ['Invalid input format']
      }
    }
  }

  /**
   * Sanitize object recursively
   */
  private static sanitizeObject(obj: any): any {
    if (typeof obj === 'string') {
      return this.sanitizeHTML(obj)
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item))
    }
    
    if (typeof obj === 'object' && obj !== null) {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.sanitizeObject(value)
      }
      return sanitized
    }
    
    return obj
  }

  /**
   * Check for suspicious patterns
   */
  static detectSuspiciousActivity(request: NextRequest): {
    isSuspicious: boolean
    reasons: string[]
    riskScore: number
  } {
    const reasons: string[] = []
    let riskScore = 0

    // Check for SQL injection patterns
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/i,
      /(UNION|OR|AND)\s+\d+\s*=\s*\d+/i,
      /['"]\s*(OR|AND)\s*['"]\s*=\s*['"]?/i
    ]

    const body = request.body?.toString() || ''
    const url = request.url || ''
    const userAgent = request.headers.get('user-agent') || ''

    sqlPatterns.forEach(pattern => {
      if (pattern.test(body) || pattern.test(url)) {
        reasons.push('Potential SQL injection attempt')
        riskScore += 0.8
      }
    })

    // Check for XSS patterns
    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>/gi
    ]

    xssPatterns.forEach(pattern => {
      if (pattern.test(body)) {
        reasons.push('Potential XSS attempt')
        riskScore += 0.7
      }
    })

    // Check for suspicious user agents
    const suspiciousUAPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i
    ]

    suspiciousUAPatterns.forEach(pattern => {
      if (pattern.test(userAgent)) {
        reasons.push('Suspicious user agent')
        riskScore += 0.3
      }
    })

    // Check for rapid requests (would need session storage)
    // This would be implemented with Redis or similar

    return {
      isSuspicious: riskScore > 0.5,
      reasons,
      riskScore: Math.min(riskScore, 1.0)
    }
  }

  /**
   * Generate CSRF token
   */
  static generateCSRFToken(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Validate CSRF token
   */
  static validateCSRFToken(token: string, sessionToken: string): boolean {
    if (!token || !sessionToken) {
      return false
    }
    
    // Constant-time comparison to prevent timing attacks
    if (token.length !== sessionToken.length) {
      return false
    }
    
    let result = 0
    for (let i = 0; i < token.length; i++) {
      result |= token.charCodeAt(i) ^ sessionToken.charCodeAt(i)
    }
    
    return result === 0
  }

  /**
   * Log security event
   */
  static logSecurityEvent(event: {
    type: 'rate_limit' | 'suspicious_activity' | 'validation_error' | 'csrf_error'
    ip: string
    userAgent: string
    details: any
    timestamp: Date
  }): void {
    // In production, this would send to a security monitoring service
    console.warn('Security Event:', {
      ...event,
      timestamp: event.timestamp.toISOString()
    })
    
    // Could integrate with services like:
    // - Sentry for error tracking
    // - DataDog for monitoring
    // - Custom security dashboard
  }

  /**
   * Create security middleware for API routes
   */
  static createSecurityMiddleware() {
    return async (request: NextRequest) => {
      // Apply rate limiting
      // Note: This would need to be implemented with a proper rate limiting service
      
      // Check for suspicious activity
      const suspiciousCheck = this.detectSuspiciousActivity(request)
      
      if (suspiciousCheck.isSuspicious) {
        this.logSecurityEvent({
          type: 'suspicious_activity',
          ip: request.ip || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          details: {
            reasons: suspiciousCheck.reasons,
            riskScore: suspiciousCheck.riskScore
          },
          timestamp: new Date()
        })
        
        // Block high-risk requests
        if (suspiciousCheck.riskScore > 0.8) {
          return new NextResponse('Forbidden', { status: 403 })
        }
      }
      
      return null // Continue to next middleware
    }
  }
}

// Export validation schemas for common community inputs
export const CommunityInputSchemas = {
  post: z.object({
    title: z.string().min(1).max(200),
    content: z.string().min(1).max(10000),
    tags: z.array(z.string().max(50)).max(10).optional(),
    category: z.string().max(50).optional()
  }),
  
  comment: z.object({
    content: z.string().min(1).max(2000),
    parentId: z.string().optional()
  }),
  
  profile: z.object({
    displayName: z.string().min(1).max(100),
    bio: z.string().max(500).optional(),
    website: z.string().url().optional(),
    location: z.string().max(100).optional()
  }),
  
  submission: z.object({
    title: z.string().min(1).max(200),
    description: z.string().min(1).max(2000),
    imageUrl: z.string().url(),
    tags: z.array(z.string().max(50)).max(10).optional()
  })
}

export default EnhancedSecurityManager
