/**
 * Community SEO Metadata Generator
 * 
 * Generates optimized meta tags, structured data, and Open Graph tags
 * for community pages to improve search engine visibility and social sharing.
 * 
 * Phase 3 Advanced Optimizations - Community System Audit
 * 
 * <AUTHOR> Team
 */

import { Metadata } from 'next'

interface CommunityPageData {
  type: 'profile' | 'discussion' | 'challenge' | 'submission' | 'leaderboard' | 'achievement'
  title: string
  description: string
  author?: {
    name: string
    username: string
    avatar?: string
    level?: number
    points?: number
  }
  content?: {
    excerpt: string
    tags: string[]
    category: string
    createdAt: Date
    updatedAt?: Date
    engagement?: {
      likes: number
      comments: number
      views: number
    }
  }
  images?: string[]
  url: string
}

interface StructuredData {
  '@context': string
  '@type': string
  [key: string]: any
}

export class CommunityMetadataGenerator {
  private static readonly SITE_NAME = 'Syndicaps'
  private static readonly SITE_URL = 'https://syndicaps.com'
  private static readonly DEFAULT_IMAGE = '/images/syndicaps-og-default.jpg'
  private static readonly TWITTER_HANDLE = '@syndicaps'

  /**
   * Generate comprehensive metadata for community pages
   */
  static generateMetadata(data: CommunityPageData): Metadata {
    const {
      title: pageTitle,
      description,
      author,
      content,
      images = [],
      url,
      type
    } = data

    // Generate optimized title
    const title = this.generateTitle(pageTitle, type, author)
    
    // Generate optimized description
    const metaDescription = this.generateDescription(description, content, type)
    
    // Generate canonical URL
    const canonicalUrl = `${this.SITE_URL}${url}`
    
    // Select best image
    const ogImage = this.selectBestImage(images, type)
    
    // Generate keywords
    const keywords = this.generateKeywords(content, type, author)

    return {
      title,
      description: metaDescription,
      keywords: keywords.join(', '),
      
      // Open Graph
      openGraph: {
        title,
        description: metaDescription,
        url: canonicalUrl,
        siteName: this.SITE_NAME,
        type: this.getOpenGraphType(type),
        images: [
          {
            url: ogImage,
            width: 1200,
            height: 630,
            alt: title,
          }
        ],
        locale: 'en_US',
        ...(author && {
          authors: [author.name]
        }),
        ...(content?.createdAt && {
          publishedTime: content.createdAt.toISOString()
        }),
        ...(content?.updatedAt && {
          modifiedTime: content.updatedAt.toISOString()
        })
      },
      
      // Twitter
      twitter: {
        card: 'summary_large_image',
        site: this.TWITTER_HANDLE,
        creator: this.TWITTER_HANDLE,
        title,
        description: metaDescription,
        images: [ogImage]
      },
      
      // Additional meta tags
      other: {
        'article:author': author?.name,
        'article:section': content?.category || 'Community',
        'article:tag': content?.tags?.join(','),
        'og:image:alt': title,
        'twitter:image:alt': title,
        ...(content?.engagement && {
          'article:engagement:likes': content.engagement.likes.toString(),
          'article:engagement:comments': content.engagement.comments.toString(),
          'article:engagement:views': content.engagement.views.toString()
        })
      },
      
      // Canonical URL
      alternates: {
        canonical: canonicalUrl
      },
      
      // Robots
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      }
    }
  }

  /**
   * Generate structured data for community content
   */
  static generateStructuredData(data: CommunityPageData): StructuredData[] {
    const structuredData: StructuredData[] = []
    
    // Website schema
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: this.SITE_NAME,
      url: this.SITE_URL,
      potentialAction: {
        '@type': 'SearchAction',
        target: `${this.SITE_URL}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    })

    // Organization schema
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: this.SITE_NAME,
      url: this.SITE_URL,
      logo: `${this.SITE_URL}/images/logo.png`,
      sameAs: [
        'https://twitter.com/syndicaps',
        'https://discord.gg/syndicaps',
        'https://instagram.com/syndicaps'
      ]
    })

    // Content-specific schemas
    switch (data.type) {
      case 'profile':
        structuredData.push(this.generatePersonSchema(data))
        break
      case 'discussion':
        structuredData.push(this.generateDiscussionSchema(data))
        break
      case 'challenge':
        structuredData.push(this.generateChallengeSchema(data))
        break
      case 'submission':
        structuredData.push(this.generateCreativeWorkSchema(data))
        break
      case 'leaderboard':
        structuredData.push(this.generateRankingSchema(data))
        break
      case 'achievement':
        structuredData.push(this.generateAchievementSchema(data))
        break
    }

    return structuredData
  }

  /**
   * Generate optimized title
   */
  private static generateTitle(title: string, type: string, author?: CommunityPageData['author']): string {
    const typeLabels = {
      profile: 'Community Profile',
      discussion: 'Discussion',
      challenge: 'Challenge',
      submission: 'Submission',
      leaderboard: 'Leaderboard',
      achievement: 'Achievement'
    }

    const typeLabel = typeLabels[type as keyof typeof typeLabels] || 'Community'
    
    if (author && (type === 'profile' || type === 'submission')) {
      return `${title} by ${author.name} | ${typeLabel} | ${this.SITE_NAME}`
    }
    
    return `${title} | ${typeLabel} | ${this.SITE_NAME}`
  }

  /**
   * Generate optimized description
   */
  private static generateDescription(
    description: string, 
    content?: CommunityPageData['content'], 
    type?: string
  ): string {
    let metaDescription = description

    // Add engagement info if available
    if (content?.engagement) {
      const { likes, comments, views } = content.engagement
      if (views > 0) {
        metaDescription += ` • ${views.toLocaleString()} views`
      }
      if (likes > 0) {
        metaDescription += ` • ${likes} likes`
      }
      if (comments > 0) {
        metaDescription += ` • ${comments} comments`
      }
    }

    // Add category if available
    if (content?.category) {
      metaDescription += ` • ${content.category}`
    }

    // Ensure description is within optimal length (150-160 characters)
    if (metaDescription.length > 160) {
      metaDescription = metaDescription.substring(0, 157) + '...'
    }

    return metaDescription
  }

  /**
   * Generate SEO keywords
   */
  private static generateKeywords(
    content?: CommunityPageData['content'],
    type?: string,
    author?: CommunityPageData['author']
  ): string[] {
    const keywords = ['syndicaps', 'community', 'keycaps', 'mechanical keyboards']
    
    if (type) {
      keywords.push(type)
    }
    
    if (content?.tags) {
      keywords.push(...content.tags)
    }
    
    if (content?.category) {
      keywords.push(content.category.toLowerCase())
    }
    
    if (author?.username) {
      keywords.push(author.username)
    }

    // Add type-specific keywords
    const typeKeywords = {
      profile: ['user profile', 'member'],
      discussion: ['forum', 'discussion', 'community chat'],
      challenge: ['contest', 'competition', 'challenge'],
      submission: ['showcase', 'gallery', 'user creation'],
      leaderboard: ['ranking', 'top users', 'leaderboard'],
      achievement: ['badges', 'achievements', 'rewards']
    }

    if (type && typeKeywords[type as keyof typeof typeKeywords]) {
      keywords.push(...typeKeywords[type as keyof typeof typeKeywords])
    }

    return [...new Set(keywords)] // Remove duplicates
  }

  /**
   * Select best image for Open Graph
   */
  private static selectBestImage(images: string[], type: string): string {
    if (images.length > 0) {
      return images[0]
    }
    
    // Type-specific default images
    const defaultImages = {
      profile: '/images/og-profile-default.jpg',
      discussion: '/images/og-discussion-default.jpg',
      challenge: '/images/og-challenge-default.jpg',
      submission: '/images/og-submission-default.jpg',
      leaderboard: '/images/og-leaderboard-default.jpg',
      achievement: '/images/og-achievement-default.jpg'
    }
    
    return defaultImages[type as keyof typeof defaultImages] || this.DEFAULT_IMAGE
  }

  /**
   * Get Open Graph type
   */
  private static getOpenGraphType(type: string): string {
    const ogTypes = {
      profile: 'profile',
      discussion: 'article',
      challenge: 'article',
      submission: 'article',
      leaderboard: 'website',
      achievement: 'website'
    }
    
    return ogTypes[type as keyof typeof ogTypes] || 'website'
  }

  // Schema generators for different content types
  private static generatePersonSchema(data: CommunityPageData): StructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'Person',
      name: data.author?.name || data.title,
      alternateName: data.author?.username,
      image: data.author?.avatar,
      url: `${this.SITE_URL}${data.url}`,
      description: data.description,
      ...(data.author?.points && {
        award: `${data.author.points} community points`
      })
    }
  }

  private static generateDiscussionSchema(data: CommunityPageData): StructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'DiscussionForumPosting',
      headline: data.title,
      description: data.description,
      author: data.author ? {
        '@type': 'Person',
        name: data.author.name,
        alternateName: data.author.username
      } : undefined,
      datePublished: data.content?.createdAt?.toISOString(),
      dateModified: data.content?.updatedAt?.toISOString(),
      url: `${this.SITE_URL}${data.url}`,
      ...(data.content?.engagement && {
        interactionStatistic: [
          {
            '@type': 'InteractionCounter',
            interactionType: 'https://schema.org/LikeAction',
            userInteractionCount: data.content.engagement.likes
          },
          {
            '@type': 'InteractionCounter',
            interactionType: 'https://schema.org/CommentAction',
            userInteractionCount: data.content.engagement.comments
          }
        ]
      })
    }
  }

  private static generateChallengeSchema(data: CommunityPageData): StructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'Event',
      name: data.title,
      description: data.description,
      url: `${this.SITE_URL}${data.url}`,
      eventStatus: 'https://schema.org/EventScheduled',
      eventAttendanceMode: 'https://schema.org/OnlineEventAttendanceMode',
      organizer: {
        '@type': 'Organization',
        name: this.SITE_NAME,
        url: this.SITE_URL
      },
      ...(data.content?.createdAt && {
        startDate: data.content.createdAt.toISOString()
      })
    }
  }

  private static generateCreativeWorkSchema(data: CommunityPageData): StructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'CreativeWork',
      name: data.title,
      description: data.description,
      creator: data.author ? {
        '@type': 'Person',
        name: data.author.name,
        alternateName: data.author.username
      } : undefined,
      dateCreated: data.content?.createdAt?.toISOString(),
      url: `${this.SITE_URL}${data.url}`,
      ...(data.images && data.images.length > 0 && {
        image: data.images[0]
      })
    }
  }

  private static generateRankingSchema(data: CommunityPageData): StructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: data.title,
      description: data.description,
      url: `${this.SITE_URL}${data.url}`,
      mainEntity: {
        '@type': 'ItemList',
        name: 'Community Leaderboard',
        description: 'Top community members ranked by points and achievements'
      }
    }
  }

  private static generateAchievementSchema(data: CommunityPageData): StructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'Achievement',
      name: data.title,
      description: data.description,
      url: `${this.SITE_URL}${data.url}`,
      ...(data.author && {
        recipient: {
          '@type': 'Person',
          name: data.author.name,
          alternateName: data.author.username
        }
      })
    }
  }
}

export default CommunityMetadataGenerator
