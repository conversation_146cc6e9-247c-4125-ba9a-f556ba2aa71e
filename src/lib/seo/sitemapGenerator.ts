/**
 * Community Sitemap Generator
 * 
 * Generates dynamic sitemaps for community content to improve
 * search engine crawling and indexing of community pages.
 * 
 * Phase 3 Advanced Optimizations - Community System Audit
 * 
 * <AUTHOR> Team
 */

import { MetadataRoute } from 'next'
import { collection, query, where, orderBy, limit, getDocs, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase'

interface SitemapEntry {
  url: string
  lastModified?: Date
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority?: number
}

interface CommunityContent {
  id: string
  type: 'discussion' | 'challenge' | 'submission' | 'profile'
  slug?: string
  username?: string
  title?: string
  isPublic: boolean
  isActive: boolean
  createdAt: Timestamp
  updatedAt?: Timestamp
  engagement?: {
    views: number
    likes: number
    comments: number
  }
}

export class CommunitySitemapGenerator {
  private static readonly BASE_URL = 'https://syndicaps.com'
  private static readonly MAX_ENTRIES_PER_SITEMAP = 50000
  private static readonly CACHE_DURATION = 3600000 // 1 hour in milliseconds

  /**
   * Generate main community sitemap
   */
  static async generateCommunitySitemap(): Promise<MetadataRoute.Sitemap> {
    const sitemap: MetadataRoute.Sitemap = []

    try {
      // Add static community pages
      sitemap.push(...this.getStaticCommunityPages())

      // Add dynamic community content
      const dynamicPages = await this.getDynamicCommunityPages()
      sitemap.push(...dynamicPages)

      // Sort by priority and last modified
      sitemap.sort((a, b) => {
        if (a.priority !== b.priority) {
          return (b.priority || 0) - (a.priority || 0)
        }
        const aDate = a.lastModified || new Date(0)
        const bDate = b.lastModified || new Date(0)
        return bDate.getTime() - aDate.getTime()
      })

      // Limit entries
      return sitemap.slice(0, this.MAX_ENTRIES_PER_SITEMAP)
    } catch (error) {
      console.error('Error generating community sitemap:', error)
      return this.getFallbackSitemap()
    }
  }

  /**
   * Generate user profiles sitemap
   */
  static async generateProfilesSitemap(): Promise<MetadataRoute.Sitemap> {
    const sitemap: MetadataRoute.Sitemap = []

    try {
      if (!db) {
        console.warn('Firebase not initialized')
        return sitemap
      }

      // Get active public profiles
      const profilesQuery = query(
        collection(db, 'communityMembers'),
        where('isPublic', '==', true),
        where('isActive', '==', true),
        orderBy('points', 'desc'),
        limit(1000) // Limit to top 1000 profiles
      )

      const profilesSnapshot = await getDocs(profilesQuery)
      
      profilesSnapshot.docs.forEach(doc => {
        const profile = doc.data()
        if (profile.username) {
          sitemap.push({
            url: `${this.BASE_URL}/community/profile/${profile.username}`,
            lastModified: profile.updatedAt?.toDate() || profile.createdAt?.toDate() || new Date(),
            changeFrequency: 'weekly',
            priority: this.calculateProfilePriority(profile)
          })
        }
      })

      return sitemap
    } catch (error) {
      console.error('Error generating profiles sitemap:', error)
      return []
    }
  }

  /**
   * Generate discussions sitemap
   */
  static async generateDiscussionsSitemap(): Promise<MetadataRoute.Sitemap> {
    const sitemap: MetadataRoute.Sitemap = []

    try {
      if (!db) {
        console.warn('Firebase not initialized')
        return sitemap
      }

      // Get public discussions
      const discussionsQuery = query(
        collection(db, 'discussions'),
        where('isPublic', '==', true),
        where('status', '==', 'active'),
        orderBy('createdAt', 'desc'),
        limit(2000)
      )

      const discussionsSnapshot = await getDocs(discussionsQuery)
      
      discussionsSnapshot.docs.forEach(doc => {
        const discussion = doc.data()
        const slug = discussion.slug || this.generateSlug(discussion.title)
        
        sitemap.push({
          url: `${this.BASE_URL}/community/discussions/${doc.id}/${slug}`,
          lastModified: discussion.updatedAt?.toDate() || discussion.createdAt?.toDate() || new Date(),
          changeFrequency: 'daily',
          priority: this.calculateDiscussionPriority(discussion)
        })
      })

      return sitemap
    } catch (error) {
      console.error('Error generating discussions sitemap:', error)
      return []
    }
  }

  /**
   * Generate challenges sitemap
   */
  static async generateChallengesSitemap(): Promise<MetadataRoute.Sitemap> {
    const sitemap: MetadataRoute.Sitemap = []

    try {
      if (!db) {
        console.warn('Firebase not initialized')
        return sitemap
      }

      // Get public challenges
      const challengesQuery = query(
        collection(db, 'challenges'),
        where('isPublic', '==', true),
        orderBy('createdAt', 'desc'),
        limit(500)
      )

      const challengesSnapshot = await getDocs(challengesQuery)
      
      challengesSnapshot.docs.forEach(doc => {
        const challenge = doc.data()
        const slug = challenge.slug || this.generateSlug(challenge.title)
        
        sitemap.push({
          url: `${this.BASE_URL}/community/challenges/${doc.id}/${slug}`,
          lastModified: challenge.updatedAt?.toDate() || challenge.createdAt?.toDate() || new Date(),
          changeFrequency: 'weekly',
          priority: this.calculateChallengePriority(challenge)
        })
      })

      return sitemap
    } catch (error) {
      console.error('Error generating challenges sitemap:', error)
      return []
    }
  }

  /**
   * Generate submissions sitemap
   */
  static async generateSubmissionsSitemap(): Promise<MetadataRoute.Sitemap> {
    const sitemap: MetadataRoute.Sitemap = []

    try {
      if (!db) {
        console.warn('Firebase not initialized')
        return sitemap
      }

      // Get featured and approved submissions
      const submissionsQuery = query(
        collection(db, 'submissions'),
        where('status', '==', 'approved'),
        where('isPublic', '==', true),
        orderBy('submittedAt', 'desc'),
        limit(1500)
      )

      const submissionsSnapshot = await getDocs(submissionsQuery)
      
      submissionsSnapshot.docs.forEach(doc => {
        const submission = doc.data()
        const slug = submission.slug || this.generateSlug(submission.title)
        
        sitemap.push({
          url: `${this.BASE_URL}/community/submissions/${doc.id}/${slug}`,
          lastModified: submission.updatedAt?.toDate() || submission.submittedAt?.toDate() || new Date(),
          changeFrequency: 'monthly',
          priority: this.calculateSubmissionPriority(submission)
        })
      })

      return sitemap
    } catch (error) {
      console.error('Error generating submissions sitemap:', error)
      return []
    }
  }

  /**
   * Get static community pages
   */
  private static getStaticCommunityPages(): SitemapEntry[] {
    return [
      {
        url: `${this.BASE_URL}/community`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0
      },
      {
        url: `${this.BASE_URL}/community/leaderboard`,
        lastModified: new Date(),
        changeFrequency: 'hourly',
        priority: 0.9
      },
      {
        url: `${this.BASE_URL}/community/challenges`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9
      },
      {
        url: `${this.BASE_URL}/community/discussions`,
        lastModified: new Date(),
        changeFrequency: 'hourly',
        priority: 0.8
      },
      {
        url: `${this.BASE_URL}/community/submissions`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8
      },
      {
        url: `${this.BASE_URL}/community/achievements`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.7
      }
    ]
  }

  /**
   * Get dynamic community pages
   */
  private static async getDynamicCommunityPages(): Promise<SitemapEntry[]> {
    const pages: SitemapEntry[] = []

    try {
      // Add top profiles
      const topProfiles = await this.generateProfilesSitemap()
      pages.push(...topProfiles.slice(0, 100)) // Top 100 profiles

      // Add recent discussions
      const recentDiscussions = await this.generateDiscussionsSitemap()
      pages.push(...recentDiscussions.slice(0, 200)) // Recent 200 discussions

      // Add active challenges
      const activeChallenges = await this.generateChallengesSitemap()
      pages.push(...activeChallenges.slice(0, 50)) // Active challenges

      // Add featured submissions
      const featuredSubmissions = await this.generateSubmissionsSitemap()
      pages.push(...featuredSubmissions.slice(0, 150)) // Featured submissions

    } catch (error) {
      console.error('Error getting dynamic community pages:', error)
    }

    return pages
  }

  /**
   * Calculate priority for profiles
   */
  private static calculateProfilePriority(profile: any): number {
    let priority = 0.5 // Base priority

    // Boost based on points
    if (profile.points > 10000) priority += 0.3
    else if (profile.points > 5000) priority += 0.2
    else if (profile.points > 1000) priority += 0.1

    // Boost based on level
    if (profile.level > 20) priority += 0.1
    else if (profile.level > 10) priority += 0.05

    // Boost for verified users
    if (profile.isVerified) priority += 0.1

    // Boost for active users
    const lastActive = profile.lastActiveAt?.toDate()
    if (lastActive && Date.now() - lastActive.getTime() < 7 * 24 * 60 * 60 * 1000) {
      priority += 0.1 // Active within last week
    }

    return Math.min(priority, 1.0)
  }

  /**
   * Calculate priority for discussions
   */
  private static calculateDiscussionPriority(discussion: any): number {
    let priority = 0.4 // Base priority

    // Boost based on engagement
    const engagement = discussion.engagement || {}
    if (engagement.views > 1000) priority += 0.2
    if (engagement.likes > 50) priority += 0.1
    if (engagement.comments > 20) priority += 0.1

    // Boost for pinned discussions
    if (discussion.isPinned) priority += 0.2

    // Boost for recent activity
    const lastActivity = discussion.lastActivityAt?.toDate()
    if (lastActivity && Date.now() - lastActivity.getTime() < 24 * 60 * 60 * 1000) {
      priority += 0.1 // Activity within last day
    }

    return Math.min(priority, 0.9)
  }

  /**
   * Calculate priority for challenges
   */
  private static calculateChallengePriority(challenge: any): number {
    let priority = 0.6 // Base priority for challenges

    // Boost for active challenges
    if (challenge.status === 'active') priority += 0.2

    // Boost based on participation
    if (challenge.participantCount > 100) priority += 0.1
    else if (challenge.participantCount > 50) priority += 0.05

    // Boost for featured challenges
    if (challenge.isFeatured) priority += 0.1

    return Math.min(priority, 0.9)
  }

  /**
   * Calculate priority for submissions
   */
  private static calculateSubmissionPriority(submission: any): number {
    let priority = 0.3 // Base priority

    // Boost for featured submissions
    if (submission.featured) priority += 0.3

    // Boost based on engagement
    const engagement = submission.engagement || {}
    if (engagement.views > 500) priority += 0.1
    if (engagement.likes > 25) priority += 0.1

    // Boost for award-winning submissions
    if (submission.awards && submission.awards.length > 0) priority += 0.2

    return Math.min(priority, 0.8)
  }

  /**
   * Generate URL-friendly slug
   */
  private static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
      .substring(0, 50)
  }

  /**
   * Get fallback sitemap for errors
   */
  private static getFallbackSitemap(): MetadataRoute.Sitemap {
    return this.getStaticCommunityPages()
  }
}

export default CommunitySitemapGenerator
