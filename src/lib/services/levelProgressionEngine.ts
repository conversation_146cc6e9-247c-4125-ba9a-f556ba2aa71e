/**
 * Level Progression Engine
 * 
 * Core engine for handling level progression, XP calculations, and user advancement.
 * Integrates with existing points system and provides comprehensive level management.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { 
  doc, 
  runTransaction,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { collections } from '@/lib/firebase/gamificationCollections'
import { 
  UserLevel, 
  XP_SOURCES,
  getLevelByXP,
  checkLevelUp,
  getXPToNextLevel,
  getProgressToNextLevel
} from '@/lib/levelSystem'
import XPService, { XPAwardContext, XPEarningResult } from './xpService'
import LevelRewardsService from './levelRewardsService'
import ProfileLevelService from './profileLevelService'
import { getUserTier } from '@/lib/memberTiers'

// ===== TYPES =====

export interface LevelProgressionContext {
  userId: string
  activityType: string
  activityData?: Record<string, any>
  skipNotifications?: boolean
  customMultiplier?: number
}

export interface LevelProgressionResult {
  xpAwarded: number
  levelUpOccurred: boolean
  oldLevel?: number
  newLevel?: number
  newLevelName?: string
  rewardsEarned?: string[]
  notificationsSent?: boolean
}

export interface BulkXPAwardResult {
  totalUsers: number
  successfulAwards: number
  failedAwards: number
  totalXPAwarded: number
  levelUpsOccurred: number
  errors: Array<{
    userId: string
    error: string
  }>
}

// ===== LEVEL PROGRESSION ENGINE CLASS =====

export class LevelProgressionEngine {
  /**
   * Process purchase and award XP
   */
  static async processPurchase(
    userId: string,
    orderAmount: number,
    orderId: string,
    context?: Partial<LevelProgressionContext>
  ): Promise<LevelProgressionResult> {
    try {
      const result = await XPService.awardPurchaseXP(userId, orderAmount, orderId)
      
      // Handle level up notifications and rewards
      if (result.levelUpResult.leveledUp) {
        await this.handleLevelUpEffects(userId, result, context)
      }

      return this.buildProgressionResult(result, context)
    } catch (error) {
      console.error('Error processing purchase XP:', error)
      throw error
    }
  }

  /**
   * Process activity and award XP
   */
  static async processActivity(
    userId: string,
    activityType: keyof typeof XP_SOURCES,
    context?: Partial<LevelProgressionContext>
  ): Promise<LevelProgressionResult> {
    try {
      const result = await XPService.awardActivityXP(
        userId,
        activityType,
        context?.activityData?.id,
        context?.activityData
      )

      // Handle level up notifications and rewards
      if (result.levelUpResult.leveledUp) {
        await this.handleLevelUpEffects(userId, result, context)
      }

      return this.buildProgressionResult(result, context)
    } catch (error) {
      console.error('Error processing activity XP:', error)
      throw error
    }
  }

  /**
   * Award custom XP with full control
   */
  static async awardCustomXP(
    userId: string,
    xpAmount: number,
    xpContext: XPAwardContext,
    context?: Partial<LevelProgressionContext>
  ): Promise<LevelProgressionResult> {
    try {
      const result = await XPService.awardXP(userId, xpAmount, xpContext)

      // Handle level up notifications and rewards
      if (result.levelUpResult.leveledUp) {
        await this.handleLevelUpEffects(userId, result, context)
      }

      return this.buildProgressionResult(result, context)
    } catch (error) {
      console.error('Error awarding custom XP:', error)
      throw error
    }
  }

  /**
   * Bulk award XP to multiple users
   */
  static async bulkAwardXP(
    userIds: string[],
    xpAmount: number,
    xpContext: Omit<XPAwardContext, 'userId'>,
    context?: Partial<LevelProgressionContext>
  ): Promise<BulkXPAwardResult> {
    const results: BulkXPAwardResult = {
      totalUsers: userIds.length,
      successfulAwards: 0,
      failedAwards: 0,
      totalXPAwarded: 0,
      levelUpsOccurred: 0,
      errors: []
    }

    // Process in batches to avoid overwhelming the database
    const batchSize = 10
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize)
      
      await Promise.allSettled(
        batch.map(async (userId) => {
          try {
            const result = await this.awardCustomXP(userId, xpAmount, xpContext, context)
            results.successfulAwards++
            results.totalXPAwarded += result.xpAwarded
            if (result.levelUpOccurred) {
              results.levelUpsOccurred++
            }
          } catch (error) {
            results.failedAwards++
            results.errors.push({
              userId,
              error: error instanceof Error ? error.message : 'Unknown error'
            })
          }
        })
      )
    }

    return results
  }

  /**
   * Recalculate user level based on current XP
   */
  static async recalculateUserLevel(userId: string): Promise<UserLevel> {
    return await runTransaction(db, async (transaction) => {
      const userLevelRef = doc(db, collections.userLevels, userId)
      const userLevelDoc = await transaction.get(userLevelRef)

      if (!userLevelDoc.exists()) {
        throw new Error('User level data not found')
      }

      const currentLevelData = userLevelDoc.data() as UserLevel
      const correctLevel = getLevelByXP(currentLevelData.totalXP)

      // Update if level is incorrect
      if (correctLevel.level !== currentLevelData.currentLevel) {
        const updatedData = {
          currentLevel: correctLevel.level,
          currentXP: currentLevelData.totalXP - correctLevel.xpRequired,
          levelName: correctLevel.name,
          levelTier: correctLevel.tier,
          nextLevelXP: getXPToNextLevel(correctLevel.level, currentLevelData.totalXP),
          progressToNext: getProgressToNextLevel(correctLevel.level, currentLevelData.totalXP),
          updatedAt: serverTimestamp()
        }

        transaction.update(userLevelRef, updatedData)
        return { ...currentLevelData, ...updatedData } as UserLevel
      }

      return currentLevelData
    })
  }

  /**
   * Get user's level progression summary
   */
  static async getUserProgressionSummary(userId: string) {
    const levelData = await XPService.getUserLevel(userId)
    if (!levelData) {
      return null
    }

    const xpHistory = await XPService.getXPHistory(userId, 100)
    const featureAccess = await ProfileLevelService.getLevelFeatureAccess(userId)

    // Calculate XP earned in different periods
    const now = new Date()
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)

    const xpToday = xpHistory
      .filter(tx => tx.createdAt.toDate() >= todayStart)
      .reduce((sum, tx) => sum + tx.amount, 0)

    const xpThisWeek = xpHistory
      .filter(tx => tx.createdAt.toDate() >= weekStart)
      .reduce((sum, tx) => sum + tx.amount, 0)

    const xpThisMonth = xpHistory
      .filter(tx => tx.createdAt.toDate() >= monthStart)
      .reduce((sum, tx) => sum + tx.amount, 0)

    return {
      level: levelData,
      xpEarned: {
        today: xpToday,
        thisWeek: xpThisWeek,
        thisMonth: xpThisMonth
      },
      featureAccess,
      recentTransactions: xpHistory.slice(0, 10)
    }
  }

  /**
   * Handle level up effects (notifications, rewards, etc.)
   */
  private static async handleLevelUpEffects(
    userId: string,
    xpResult: XPEarningResult,
    context?: Partial<LevelProgressionContext>
  ): Promise<void> {
    if (!xpResult.levelUpResult.leveledUp || !xpResult.levelUpResult.newLevel) {
      return
    }

    const newLevel = xpResult.levelUpResult.newLevel

    try {
      // Award level milestone rewards
      const rewards = await LevelRewardsService.getRewardsForLevel(newLevel)
      for (const reward of rewards) {
        await LevelRewardsService.claimReward(userId, reward.id)
      }

      // Send level up notification (if not skipped)
      if (!context?.skipNotifications) {
        await this.sendLevelUpNotification(userId, newLevel, xpResult.levelUpResult.newLevelName!)
      }

      // Update profile with level up timestamp
      await ProfileLevelService.updateProfileWithLevel(userId, {
        level: {
          lastLevelUp: serverTimestamp() as Timestamp,
          levelUpNotificationSeen: false
        }
      })

      // Log level up event
      console.log(`User ${userId} leveled up to level ${newLevel} (${xpResult.levelUpResult.newLevelName})`)
    } catch (error) {
      console.error('Error handling level up effects:', error)
      // Don't throw - level up should still succeed even if effects fail
    }
  }

  /**
   * Send level up notification
   */
  private static async sendLevelUpNotification(
    userId: string,
    newLevel: number,
    levelName: string
  ): Promise<void> {
    // TODO: Implement notification system integration
    // This could send push notifications, emails, or in-app notifications
    console.log(`Sending level up notification to ${userId}: Level ${newLevel} - ${levelName}`)
  }

  /**
   * Build progression result from XP result
   */
  private static buildProgressionResult(
    xpResult: XPEarningResult,
    context?: Partial<LevelProgressionContext>
  ): LevelProgressionResult {
    return {
      xpAwarded: xpResult.xpAwarded,
      levelUpOccurred: xpResult.levelUpResult.leveledUp,
      oldLevel: xpResult.levelUpResult.oldLevel,
      newLevel: xpResult.levelUpResult.newLevel,
      newLevelName: xpResult.levelUpResult.newLevelName,
      rewardsEarned: xpResult.levelUpResult.rewardsEarned,
      notificationsSent: !context?.skipNotifications && xpResult.levelUpResult.leveledUp
    }
  }

  /**
   * Initialize level system for new user
   */
  static async initializeNewUser(userId: string): Promise<UserLevel> {
    // Initialize level data
    const levelData = await ProfileLevelService.initializeUserLevelData(userId)

    // Award welcome XP
    await this.awardCustomXP(userId, XP_SOURCES.PROFILE_COMPLETION, {
      source: 'activity',
      description: 'Welcome to Syndicaps! Profile setup bonus',
      metadata: {
        welcomeBonus: true,
        newUser: true
      }
    })

    return levelData
  }

  /**
   * Migrate existing user to level system
   */
  static async migrateExistingUser(userId: string): Promise<void> {
    const migrationData = await ProfileLevelService.migrateUserToLevelSystem(userId)
    
    console.log(`Migrated user ${userId} to level system:`, {
      estimatedLevel: migrationData.estimatedLevel,
      estimatedXP: migrationData.estimatedXP,
      originalPoints: migrationData.currentPoints
    })
  }
}

export default LevelProgressionEngine
