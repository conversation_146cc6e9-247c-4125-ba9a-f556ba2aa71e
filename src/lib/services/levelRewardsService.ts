/**
 * Level Rewards Service
 * 
 * Manages level-based rewards, claiming logic, and reward definitions.
 * Handles milestone rewards, exclusive benefits, and level-gated features.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { 
  doc, 
  collection, 
  addDoc, 
  updateDoc, 
  getDoc, 
  getDocs,
  query, 
  where, 
  orderBy, 
  runTransaction,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { 
  collections, 
  levelRewardsCollection 
} from '@/lib/firebase/gamificationCollections'
import { LevelReward } from '@/lib/levelSystem'

// ===== TYPES =====

export interface ClaimableReward extends LevelReward {
  canClaim: boolean
  alreadyClaimed: boolean
  claimDeadline?: Date
}

export interface RewardClaimResult {
  success: boolean
  reward?: LevelReward
  error?: string
}

// ===== LEVEL REWARD DEFINITIONS =====

export const LEVEL_REWARD_DEFINITIONS: Omit<LevelReward, 'id' | 'createdAt'>[] = [
  // Every 5 levels - Badge + Points + Discount
  { level: 5, type: 'badge', name: 'Community Member Badge', description: 'Exclusive badge for reaching level 5', value: 'community_member_badge', isExclusive: true },
  { level: 5, type: 'points', name: '100 Bonus Points', description: 'Bonus points for level milestone', value: 100, isExclusive: false },
  { level: 5, type: 'discount', name: '5% Discount Voucher', description: 'One-time 5% discount on next purchase', value: 5, isExclusive: false },
  { level: 5, type: 'access', name: 'Community Posting', description: 'Unlock community posting privileges', value: 'community_posting', isExclusive: false },

  { level: 10, type: 'badge', name: 'Keycap Enthusiast Badge', description: 'Badge for dedicated keycap enthusiasts', value: 'keycap_enthusiast_badge', isExclusive: true },
  { level: 10, type: 'points', name: '200 Bonus Points', description: 'Bonus points for level milestone', value: 200, isExclusive: false },
  { level: 10, type: 'discount', name: '7% Discount Voucher', description: 'One-time 7% discount on next purchase', value: 7, isExclusive: false },
  { level: 10, type: 'keycap', name: 'Exclusive Level 10 Keycap', description: 'Limited edition keycap for level 10 achievers', value: 'level_10_exclusive_keycap', isExclusive: true },
  { level: 10, type: 'access', name: 'Free Shipping Reduction', description: 'Reduced free shipping threshold', value: 'free_shipping_reduction', isExclusive: false },
  { level: 10, type: 'access', name: 'Challenge Creation', description: 'Ability to create community challenges', value: 'challenge_creation', isExclusive: false },

  { level: 15, type: 'badge', name: 'Artisan Admirer Badge', description: 'Badge for artisan keycap appreciation', value: 'artisan_admirer_badge', isExclusive: true },
  { level: 15, type: 'points', name: '300 Bonus Points', description: 'Bonus points for level milestone', value: 300, isExclusive: false },
  { level: 15, type: 'discount', name: '10% Discount Voucher', description: 'One-time 10% discount on next purchase', value: 10, isExclusive: false },
  { level: 15, type: 'access', name: 'Voting Weight Increase', description: 'Increased voting weight in community decisions', value: 'voting_weight_1_5x', isExclusive: false },

  { level: 20, type: 'badge', name: 'Community Contributor Badge', description: 'Badge for active community contribution', value: 'community_contributor_badge', isExclusive: true },
  { level: 20, type: 'points', name: '400 Bonus Points', description: 'Bonus points for level milestone', value: 400, isExclusive: false },
  { level: 20, type: 'discount', name: '12% Discount Voucher', description: 'One-time 12% discount on next purchase', value: 12, isExclusive: false },
  { level: 20, type: 'keycap', name: 'Exclusive Level 20 Keycap', description: 'Limited edition keycap for level 20 achievers', value: 'level_20_exclusive_keycap', isExclusive: true },
  { level: 20, type: 'access', name: 'Early Access', description: '24-hour early access to new products', value: 'early_access_24h', isExclusive: false },
  { level: 20, type: 'access', name: 'Mentorship Program', description: 'Access to mentorship program', value: 'mentorship_program', isExclusive: false },

  { level: 25, type: 'badge', name: 'Keycap Curator Badge', description: 'Badge for keycap curation expertise', value: 'keycap_curator_badge', isExclusive: true },
  { level: 25, type: 'points', name: '500 Bonus Points', description: 'Bonus points for level milestone', value: 500, isExclusive: false },
  { level: 25, type: 'discount', name: '15% Discount Voucher', description: 'One-time 15% discount on next purchase', value: 15, isExclusive: false },
  { level: 25, type: 'title', name: 'Keycap Curator Title', description: 'Exclusive community title', value: 'Keycap Curator', isExclusive: true },
  { level: 25, type: 'access', name: 'Beta Testing', description: 'Participation in beta testing programs', value: 'beta_testing', isExclusive: false },

  { level: 30, type: 'badge', name: 'Community Captain Badge', description: 'Badge for community leadership', value: 'community_captain_badge', isExclusive: true },
  { level: 30, type: 'points', name: '750 Bonus Points', description: 'Bonus points for level milestone', value: 750, isExclusive: false },
  { level: 30, type: 'keycap', name: 'Exclusive Level 30 Keycap', description: 'Limited edition keycap for level 30 achievers', value: 'level_30_exclusive_keycap', isExclusive: true },
  { level: 30, type: 'access', name: 'Custom Keycap Design', description: 'Custom keycap design service', value: 'custom_keycap_design', isExclusive: true },
  { level: 30, type: 'access', name: 'Community Moderation', description: 'Community moderation tools access', value: 'community_moderation', isExclusive: false },

  { level: 35, type: 'badge', name: 'Design Deity Badge', description: 'Badge for design mastery', value: 'design_deity_badge', isExclusive: true },
  { level: 35, type: 'points', name: '1000 Bonus Points', description: 'Bonus points for level milestone', value: 1000, isExclusive: false },
  { level: 35, type: 'title', name: 'Design Deity Title', description: 'Exclusive community title', value: 'Design Deity', isExclusive: true },
  { level: 35, type: 'access', name: 'Exclusive Discord Channels', description: 'Access to exclusive Discord channels', value: 'exclusive_discord', isExclusive: true },

  { level: 40, type: 'badge', name: 'Mechanical Master Badge', description: 'Badge for mechanical keyboard mastery', value: 'mechanical_master_badge', isExclusive: true },
  { level: 40, type: 'points', name: '1500 Bonus Points', description: 'Bonus points for level milestone', value: 1500, isExclusive: false },
  { level: 40, type: 'keycap', name: 'Exclusive Level 40 Keycap', description: 'Limited edition keycap for level 40 achievers', value: 'level_40_exclusive_keycap', isExclusive: true },
  { level: 40, type: 'access', name: 'Design Contest Judging', description: 'Ability to judge design contests', value: 'design_contest_judging', isExclusive: true },
  { level: 40, type: 'access', name: 'Bulk Order Discounts', description: 'Special bulk order pricing', value: 'bulk_order_discounts', isExclusive: false },

  { level: 45, type: 'badge', name: 'Layout Luminary Badge', description: 'Badge for layout expertise', value: 'layout_luminary_badge', isExclusive: true },
  { level: 45, type: 'points', name: '2000 Bonus Points', description: 'Bonus points for level milestone', value: 2000, isExclusive: false },
  { level: 45, type: 'title', name: 'Layout Luminary Title', description: 'Exclusive community title', value: 'Layout Luminary', isExclusive: true },
  { level: 45, type: 'access', name: 'Product Feedback Panel', description: 'Participation in product feedback panel', value: 'product_feedback_panel', isExclusive: true },

  { level: 50, type: 'badge', name: 'Syndicaps Legend Badge', description: 'Ultimate achievement badge', value: 'syndicaps_legend_badge', isExclusive: true },
  { level: 50, type: 'points', name: '5000 Bonus Points', description: 'Massive bonus points for reaching max level', value: 5000, isExclusive: false },
  { level: 50, type: 'keycap', name: 'Legendary Syndicaps Keycap', description: 'Ultra-exclusive legendary keycap', value: 'legendary_syndicaps_keycap', isExclusive: true },
  { level: 50, type: 'title', name: 'Syndicaps Legend Title', description: 'Ultimate community title', value: 'Syndicaps Legend', isExclusive: true },
  { level: 50, type: 'access', name: 'Lifetime VIP Status', description: 'Permanent VIP benefits and recognition', value: 'lifetime_vip', isExclusive: true },
  { level: 50, type: 'access', name: 'Ambassador Program', description: 'Syndicaps Ambassador program access', value: 'ambassador_program', isExclusive: true },
]

// ===== LEVEL REWARDS SERVICE CLASS =====

export class LevelRewardsService {
  /**
   * Initialize level rewards in database
   */
  static async initializeLevelRewards(): Promise<void> {
    const batch = []
    
    for (const rewardDef of LEVEL_REWARD_DEFINITIONS) {
      const rewardData = {
        ...rewardDef,
        createdAt: serverTimestamp()
      }
      
      batch.push(addDoc(levelRewardsCollection, rewardData))
    }
    
    await Promise.all(batch)
  }

  /**
   * Get rewards for a specific level
   */
  static async getRewardsForLevel(level: number): Promise<LevelReward[]> {
    const q = query(
      levelRewardsCollection,
      where('level', '==', level),
      orderBy('type', 'asc')
    )

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as LevelReward[]
  }

  /**
   * Get claimable rewards for user based on their level
   */
  static async getClaimableRewards(
    userId: string,
    userLevel: number,
    claimedRewards: string[]
  ): Promise<ClaimableReward[]> {
    const q = query(
      levelRewardsCollection,
      where('level', '<=', userLevel),
      orderBy('level', 'desc')
    )

    const snapshot = await getDocs(q)
    const allRewards = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as LevelReward[]

    return allRewards.map(reward => ({
      ...reward,
      canClaim: userLevel >= reward.level,
      alreadyClaimed: claimedRewards.includes(reward.id),
      claimDeadline: reward.expiresAt ? reward.expiresAt.toDate() : undefined
    }))
  }

  /**
   * Claim a reward for a user
   */
  static async claimReward(
    userId: string,
    rewardId: string
  ): Promise<RewardClaimResult> {
    return await runTransaction(db, async (transaction) => {
      // Get reward details
      const rewardDoc = await transaction.get(doc(levelRewardsCollection, rewardId))
      
      if (!rewardDoc.exists()) {
        return { success: false, error: 'Reward not found' }
      }

      const reward = { id: rewardId, ...rewardDoc.data() } as LevelReward

      // Get user level to verify eligibility
      const userLevelDoc = await transaction.get(doc(db, collections.userLevels, userId))
      
      if (!userLevelDoc.exists()) {
        return { success: false, error: 'User level not found' }
      }

      const userLevel = userLevelDoc.data()
      
      // Check if user is eligible
      if (userLevel.currentLevel < reward.level) {
        return { success: false, error: 'User level too low for this reward' }
      }

      // Check if already claimed
      if (userLevel.milestoneRewards?.includes(rewardId)) {
        return { success: false, error: 'Reward already claimed' }
      }

      // Check if reward has expired
      if (reward.expiresAt && reward.expiresAt.toDate() < new Date()) {
        return { success: false, error: 'Reward has expired' }
      }

      // Update user's claimed rewards
      const updatedClaimedRewards = [...(userLevel.milestoneRewards || []), rewardId]
      transaction.update(doc(db, collections.userLevels, userId), {
        milestoneRewards: updatedClaimedRewards,
        updatedAt: serverTimestamp()
      })

      // Apply the reward based on type
      await this.applyReward(userId, reward, transaction)

      return { success: true, reward }
    })
  }

  /**
   * Apply reward effects to user account
   */
  private static async applyReward(
    userId: string,
    reward: LevelReward,
    transaction: any
  ): Promise<void> {
    switch (reward.type) {
      case 'points':
        // Add points to user's account
        const profileRef = doc(db, collections.profiles, userId)
        const profileDoc = await transaction.get(profileRef)
        
        if (profileDoc.exists()) {
          const currentPoints = profileDoc.data().points || 0
          transaction.update(profileRef, {
            points: currentPoints + reward.value,
            updatedAt: serverTimestamp()
          })
        }
        break

      case 'badge':
        // Add badge to user's collection (implement when badge system is ready)
        break

      case 'discount':
        // Create discount voucher (implement when voucher system is ready)
        break

      case 'keycap':
        // Add exclusive keycap to user's inventory (implement when inventory system is ready)
        break

      case 'access':
        // Grant access permissions (implement when permission system is ready)
        break

      case 'title':
        // Update user's title (implement when title system is ready)
        break

      default:
        console.warn(`Unknown reward type: ${reward.type}`)
    }
  }

  /**
   * Get all rewards for a specific level range
   */
  static async getRewardsInRange(
    minLevel: number,
    maxLevel: number
  ): Promise<LevelReward[]> {
    const q = query(
      levelRewardsCollection,
      where('level', '>=', minLevel),
      where('level', '<=', maxLevel),
      orderBy('level', 'asc')
    )

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as LevelReward[]
  }

  /**
   * Check if user has access to a specific feature
   */
  static async hasFeatureAccess(
    userId: string,
    featureId: string
  ): Promise<boolean> {
    // Get user's claimed rewards
    const userLevelDoc = await getDoc(doc(db, collections.userLevels, userId))
    
    if (!userLevelDoc.exists()) {
      return false
    }

    const claimedRewards = userLevelDoc.data().milestoneRewards || []
    
    // Check if any claimed reward grants this feature
    for (const rewardId of claimedRewards) {
      const rewardDoc = await getDoc(doc(levelRewardsCollection, rewardId))
      
      if (rewardDoc.exists()) {
        const reward = rewardDoc.data() as LevelReward
        
        if (reward.type === 'access' && reward.value === featureId) {
          return true
        }
      }
    }

    return false
  }
}

export default LevelRewardsService
