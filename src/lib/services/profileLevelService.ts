/**
 * Profile Level Service
 * 
 * Handles integration between user profiles and the level system.
 * Provides seamless data synchronization and profile enhancement.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { 
  doc, 
  getDoc, 
  updateDoc, 
  runTransaction,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { collections } from '@/lib/firebase/gamificationCollections'
import { UserProfile } from '@/types/profile'
import { 
  UserProfileWithLevel, 
  ProfileLevelData, 
  LevelStats,
  LevelFeatureAccess,
  CompleteUserProfile,
  LevelMigrationData
} from '@/types/levelProfile'
import { UserLevel, getLevelByXP } from '@/lib/levelSystem'
import XPService from './xpService'
import LevelRewardsService from './levelRewardsService'

// ===== PROFILE LEVEL SERVICE CLASS =====

export class ProfileLevelService {
  /**
   * Get complete user profile with level data
   */
  static async getCompleteProfile(userId: string): Promise<CompleteUserProfile | null> {
    try {
      // Get base profile
      const profileDoc = await getDoc(doc(db, collections.profiles, userId))
      if (!profileDoc.exists()) {
        return null
      }

      const baseProfile = { id: userId, ...profileDoc.data() } as UserProfile

      // Get level data
      const levelData = await XPService.getUserLevel(userId)
      if (!levelData) {
        // Initialize level data for user if it doesn't exist
        await this.initializeUserLevelData(userId)
        const newLevelData = await XPService.getUserLevel(userId)
        if (!newLevelData) {
          throw new Error('Failed to initialize level data')
        }
        return this.buildCompleteProfile(baseProfile, newLevelData)
      }

      return this.buildCompleteProfile(baseProfile, levelData)
    } catch (error) {
      console.error('Error getting complete profile:', error)
      return null
    }
  }

  /**
   * Get user profile with level data integrated
   */
  static async getProfileWithLevel(userId: string): Promise<UserProfileWithLevel | null> {
    try {
      const completeProfile = await this.getCompleteProfile(userId)
      if (!completeProfile) {
        return null
      }

      // Extract just the profile with level data
      const { levelStats, featureAccess, progressionAnalytics, displayPreferences, ...profileWithLevel } = completeProfile
      return profileWithLevel
    } catch (error) {
      console.error('Error getting profile with level:', error)
      return null
    }
  }

  /**
   * Update profile with level data synchronization
   */
  static async updateProfileWithLevel(
    userId: string,
    updates: Partial<UserProfile & ProfileLevelData>
  ): Promise<void> {
    await runTransaction(db, async (transaction) => {
      const profileRef = doc(db, collections.profiles, userId)
      const levelRef = doc(db, collections.userLevels, userId)

      // Separate profile updates from level updates
      const { level, ...profileUpdates } = updates
      
      // Update profile
      if (Object.keys(profileUpdates).length > 0) {
        transaction.update(profileRef, {
          ...profileUpdates,
          updatedAt: serverTimestamp()
        })
      }

      // Update level data if provided
      if (level) {
        transaction.update(levelRef, {
          ...level,
          updatedAt: serverTimestamp()
        })
      }
    })
  }

  /**
   * Initialize level data for existing user
   */
  static async initializeUserLevelData(userId: string): Promise<UserLevel> {
    return await runTransaction(db, async (transaction) => {
      // Check if level data already exists
      const levelRef = doc(db, collections.userLevels, userId)
      const levelDoc = await transaction.get(levelRef)
      
      if (levelDoc.exists()) {
        return { id: userId, ...levelDoc.data() } as UserLevel
      }

      // Get user's current points to estimate initial level
      const profileRef = doc(db, collections.profiles, userId)
      const profileDoc = await transaction.get(profileRef)
      
      let estimatedXP = 0
      if (profileDoc.exists()) {
        const profileData = profileDoc.data()
        const currentPoints = profileData.points || profileData.totalPoints || 0
        // Estimate XP based on points (rough conversion: 1 point = 0.4 XP)
        estimatedXP = Math.floor(currentPoints * 0.4)
      }

      // Create initial level data
      const initialLevel = getLevelByXP(estimatedXP)
      const levelData: Omit<UserLevel, 'id'> = {
        userId,
        currentLevel: initialLevel.level,
        currentXP: estimatedXP - initialLevel.xpRequired,
        totalXP: estimatedXP,
        levelName: initialLevel.name,
        levelTier: initialLevel.tier,
        nextLevelXP: initialLevel.xpToNext,
        progressToNext: 0,
        milestoneRewards: [],
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp
      }

      transaction.set(levelRef, levelData)
      return { id: userId, ...levelData } as UserLevel
    })
  }

  /**
   * Migrate existing user to level system
   */
  static async migrateUserToLevelSystem(userId: string): Promise<LevelMigrationData> {
    const profileDoc = await getDoc(doc(db, collections.profiles, userId))
    
    if (!profileDoc.exists()) {
      throw new Error('User profile not found')
    }

    const profileData = profileDoc.data()
    const currentPoints = profileData.points || profileData.totalPoints || 0
    
    // Estimate level based on current points and activity
    const estimatedXP = this.calculateEstimatedXP(profileData)
    const estimatedLevel = getLevelByXP(estimatedXP)

    // Initialize level data
    await this.initializeUserLevelData(userId)

    // Award initial XP if estimated
    if (estimatedXP > 0) {
      await XPService.awardXP(userId, estimatedXP, {
        source: 'manual',
        description: 'Migration XP from existing activity',
        metadata: {
          migration: true,
          originalPoints: currentPoints,
          migrationDate: new Date().toISOString()
        },
        skipTierMultiplier: true
      })
    }

    return {
      userId,
      currentPoints,
      estimatedLevel: estimatedLevel.level,
      estimatedXP,
      migrationDate: new Date(),
      preservedData: {
        achievements: profileData.achievements?.map((a: any) => a.achievementId) || [],
        tier: profileData.currentTier || 'bronze',
        joinDate: profileData.createdAt?.toDate() || new Date()
      }
    }
  }

  /**
   * Get level-based feature access for user
   */
  static async getLevelFeatureAccess(userId: string): Promise<LevelFeatureAccess> {
    const levelData = await XPService.getUserLevel(userId)
    
    if (!levelData) {
      return this.getDefaultFeatureAccess()
    }

    const level = levelData.currentLevel
    
    return {
      communityPosting: level >= 5,
      challengeCreation: level >= 10,
      votingWeightMultiplier: level >= 15 ? 1.5 : 1.0,
      mentorshipProgram: level >= 20,
      betaTesting: level >= 25,
      communityModeration: level >= 30,
      exclusiveDiscord: level >= 35,
      designContestJudging: level >= 40,
      productFeedbackPanel: level >= 45,
      ambassadorProgram: level >= 50,
      customKeycapDesign: level >= 30,
      bulkOrderDiscounts: level >= 40,
      lifetimeVIP: level >= 50
    }
  }

  /**
   * Build complete profile from base profile and level data
   */
  private static async buildCompleteProfile(
    baseProfile: UserProfile,
    levelData: UserLevel
  ): Promise<CompleteUserProfile> {
    // Build level stats
    const levelStats: LevelStats = {
      currentLevel: levelData.currentLevel,
      levelName: levelData.levelName,
      tier: levelData.levelTier,
      totalXP: levelData.totalXP,
      xpToNextLevel: levelData.nextLevelXP,
      progressPercentage: levelData.progressToNext,
      levelUpDate: levelData.levelUpAt?.toDate(),
      recentLevelUps: [], // TODO: Implement level up history
      xpEarnedToday: 0, // TODO: Calculate from XP transactions
      xpEarnedThisWeek: 0, // TODO: Calculate from XP transactions
      xpEarnedThisMonth: 0 // TODO: Calculate from XP transactions
    }

    // Get feature access
    const featureAccess = await this.getLevelFeatureAccess(baseProfile.id)

    // Build profile level data
    const profileLevelData: ProfileLevelData = {
      level: {
        current: levelData.currentLevel,
        currentXP: levelData.currentXP,
        totalXP: levelData.totalXP,
        levelName: levelData.levelName,
        tier: levelData.levelTier,
        nextLevelXP: levelData.nextLevelXP,
        progressToNext: levelData.progressToNext,
        lastLevelUp: levelData.levelUpAt,
        unclaimedRewards: [], // TODO: Calculate unclaimed rewards
        milestoneRewards: levelData.milestoneRewards,
        levelUpNotificationSeen: true // TODO: Track this properly
      }
    }

    return {
      ...baseProfile,
      ...profileLevelData,
      levelStats,
      featureAccess,
      progressionAnalytics: {
        userId: baseProfile.id,
        averageXPPerDay: 0, // TODO: Calculate
        averageXPPerWeek: 0, // TODO: Calculate
        averageXPPerMonth: 0, // TODO: Calculate
        levelUpVelocity: 0, // TODO: Calculate
        xpSources: { purchase: 0, activity: 0, bonus: 0, event: 0 }, // TODO: Calculate
        engagementScore: 0, // TODO: Calculate
        progressionTrend: 'stable', // TODO: Calculate
        calculatedAt: new Date()
      },
      displayPreferences: {
        showLevelInProfile: true,
        showXPProgress: true,
        showLevelBadge: true,
        showRecentAchievements: true,
        levelNotifications: true,
        xpGainAnimations: true
      }
    }
  }

  /**
   * Calculate estimated XP for migration
   */
  private static calculateEstimatedXP(profileData: any): number {
    let estimatedXP = 0

    // Base XP from points (conservative estimate)
    const points = profileData.points || profileData.totalPoints || 0
    estimatedXP += Math.floor(points * 0.4)

    // XP from achievements
    const achievements = profileData.achievements || []
    estimatedXP += achievements.length * 50

    // XP from profile completion
    if (profileData.profileCompletion?.percentage > 80) {
      estimatedXP += 100
    }

    // XP from account age (rough estimate)
    const createdAt = profileData.createdAt?.toDate() || new Date()
    const accountAgeMonths = Math.floor((Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30))
    estimatedXP += Math.min(accountAgeMonths * 20, 500) // Cap at 500 XP for age

    return Math.max(0, estimatedXP)
  }

  /**
   * Get default feature access for users without level data
   */
  private static getDefaultFeatureAccess(): LevelFeatureAccess {
    return {
      communityPosting: false,
      challengeCreation: false,
      votingWeightMultiplier: 1.0,
      mentorshipProgram: false,
      betaTesting: false,
      communityModeration: false,
      exclusiveDiscord: false,
      designContestJudging: false,
      productFeedbackPanel: false,
      ambassadorProgram: false,
      customKeycapDesign: false,
      bulkOrderDiscounts: false,
      lifetimeVIP: false
    }
  }
}

export default ProfileLevelService
