/**
 * XP Service
 * 
 * Handles all XP transactions, level progression, and user level management.
 * Integrates with existing points system and tier structure.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { 
  doc, 
  collection, 
  addDoc, 
  updateDoc, 
  getDoc, 
  getDocs,
  query, 
  where, 
  orderBy, 
  limit,
  runTransaction,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { 
  collections, 
  userLevelsCollection, 
  xpTransactionsCollection 
} from '@/lib/firebase/gamificationCollections'
import { 
  UserLevel, 
  XPTransaction, 
  XP_SOURCES, 
  TIER_XP_MULTIPLIERS,
  getLevelByXP,
  checkLevelUp,
  getXPToNextLevel,
  getProgressToNextLevel,
  calculateXPWithTierMultiplier
} from '@/lib/levelSystem'
import { getUserTier } from '@/lib/memberTiers'

// ===== TYPES =====

export interface XPAwardContext {
  source: XPTransaction['source']
  sourceId?: string
  description: string
  metadata?: Record<string, any>
  multiplier?: number
  skipTierMultiplier?: boolean
}

export interface LevelUpResult {
  leveledUp: boolean
  oldLevel?: number
  newLevel?: number
  newLevelName?: string
  rewardsEarned?: string[]
}

export interface XPEarningResult {
  xpAwarded: number
  finalXP: number
  levelUpResult: LevelUpResult
  transaction: XPTransaction
}

// ===== XP SERVICE CLASS =====

export class XPService {
  /**
   * Award XP to a user and handle level progression
   */
  static async awardXP(
    userId: string,
    baseXP: number,
    context: XPAwardContext
  ): Promise<XPEarningResult> {
    return await runTransaction(db, async (transaction) => {
      // Get user's current level data
      const userLevelRef = doc(userLevelsCollection, userId)
      const userLevelDoc = await transaction.get(userLevelRef)
      
      let currentUserLevel: UserLevel
      
      if (!userLevelDoc.exists()) {
        // Initialize user level if doesn't exist
        currentUserLevel = await this.initializeUserLevel(userId, transaction)
      } else {
        currentUserLevel = { id: userId, ...userLevelDoc.data() } as UserLevel
      }

      // Calculate final XP with multipliers
      let finalXP = baseXP
      
      // Apply custom multiplier if provided
      if (context.multiplier && context.multiplier !== 1) {
        finalXP = Math.floor(finalXP * context.multiplier)
      }
      
      // Apply tier multiplier unless skipped
      if (!context.skipTierMultiplier) {
        const userPoints = await this.getUserPoints(userId)
        const userTier = getUserTier(userPoints)
        const tierKey = userTier.toLowerCase() as keyof typeof TIER_XP_MULTIPLIERS
        finalXP = calculateXPWithTierMultiplier(finalXP, tierKey)
      }

      // Check for level up
      const oldTotalXP = currentUserLevel.totalXP
      const newTotalXP = oldTotalXP + finalXP
      const levelUpResult = checkLevelUp(oldTotalXP, newTotalXP)

      // Create XP transaction record
      const xpTransactionData: Omit<XPTransaction, 'id'> = {
        userId,
        amount: finalXP,
        source: context.source,
        sourceId: context.sourceId,
        multiplier: context.multiplier || 1,
        description: context.description,
        metadata: context.metadata || {},
        createdAt: serverTimestamp() as Timestamp
      }

      const xpTransactionRef = doc(collection(db, collections.xpTransactions))
      transaction.set(xpTransactionRef, xpTransactionData)

      // Update user level
      const newLevelData = this.calculateNewLevelData(currentUserLevel, finalXP, levelUpResult)
      transaction.update(userLevelRef, newLevelData)

      // Handle level up rewards if applicable
      let rewardsEarned: string[] = []
      if (levelUpResult.leveledUp && levelUpResult.newLevel) {
        rewardsEarned = await this.handleLevelUpRewards(userId, levelUpResult.newLevel, transaction)
      }

      return {
        xpAwarded: finalXP,
        finalXP: newTotalXP,
        levelUpResult: {
          ...levelUpResult,
          newLevelName: levelUpResult.leveledUp ? newLevelData.levelName : undefined,
          rewardsEarned
        },
        transaction: {
          id: xpTransactionRef.id,
          ...xpTransactionData
        } as XPTransaction
      }
    })
  }

  /**
   * Award XP for purchase
   */
  static async awardPurchaseXP(
    userId: string,
    orderAmount: number,
    orderId: string
  ): Promise<XPEarningResult> {
    // Calculate base XP (2 XP per $1)
    const baseXP = Math.floor(orderAmount * XP_SOURCES.PURCHASE_BASE)
    
    // Check for large order bonus
    let bonusXP = 0
    let description = `Purchase XP for order ${orderId} ($${orderAmount.toFixed(2)})`
    
    if (orderAmount >= XP_SOURCES.LARGE_ORDER_THRESHOLD) {
      bonusXP = Math.floor(baseXP * XP_SOURCES.LARGE_ORDER_BONUS)
      description += ` with large order bonus (+${Math.round(XP_SOURCES.LARGE_ORDER_BONUS * 100)}%)`
    }

    const totalXP = baseXP + bonusXP

    return await this.awardXP(userId, totalXP, {
      source: 'purchase',
      sourceId: orderId,
      description,
      metadata: {
        orderAmount,
        baseXP,
        bonusXP,
        largeOrderBonus: bonusXP > 0
      }
    })
  }

  /**
   * Award XP for activity
   */
  static async awardActivityXP(
    userId: string,
    activityType: keyof typeof XP_SOURCES,
    activityId?: string,
    metadata?: Record<string, any>
  ): Promise<XPEarningResult> {
    const xpAmount = XP_SOURCES[activityType]
    
    if (typeof xpAmount !== 'number') {
      throw new Error(`Invalid activity type: ${activityType}`)
    }

    return await this.awardXP(userId, xpAmount, {
      source: 'activity',
      sourceId: activityId,
      description: `${activityType.replace(/_/g, ' ')} activity`,
      metadata: {
        activityType,
        ...metadata
      }
    })
  }

  /**
   * Get user's current level data
   */
  static async getUserLevel(userId: string): Promise<UserLevel | null> {
    const userLevelDoc = await getDoc(doc(userLevelsCollection, userId))
    
    if (!userLevelDoc.exists()) {
      return null
    }

    return { id: userId, ...userLevelDoc.data() } as UserLevel
  }

  /**
   * Get user's XP transaction history
   */
  static async getXPHistory(
    userId: string,
    limitCount: number = 50
  ): Promise<XPTransaction[]> {
    const q = query(
      xpTransactionsCollection,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    )

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as XPTransaction[]
  }

  /**
   * Initialize user level for new users
   */
  private static async initializeUserLevel(
    userId: string,
    transaction: any
  ): Promise<UserLevel> {
    const initialLevel = getLevelByXP(0)
    
    const userLevelData: Omit<UserLevel, 'id'> = {
      userId,
      currentLevel: initialLevel.level,
      currentXP: 0,
      totalXP: 0,
      levelName: initialLevel.name,
      levelTier: initialLevel.tier,
      nextLevelXP: initialLevel.xpToNext,
      progressToNext: 0,
      milestoneRewards: [],
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp
    }

    const userLevelRef = doc(userLevelsCollection, userId)
    transaction.set(userLevelRef, userLevelData)

    return { id: userId, ...userLevelData } as UserLevel
  }

  /**
   * Calculate new level data after XP gain
   */
  private static calculateNewLevelData(
    currentLevel: UserLevel,
    xpGained: number,
    levelUpResult: LevelUpResult
  ): Partial<UserLevel> {
    const newTotalXP = currentLevel.totalXP + xpGained
    
    if (levelUpResult.leveledUp && levelUpResult.newLevel) {
      const newLevelDef = getLevelByXP(newTotalXP)
      
      return {
        currentLevel: newLevelDef.level,
        currentXP: newTotalXP - newLevelDef.xpRequired,
        totalXP: newTotalXP,
        levelName: newLevelDef.name,
        levelTier: newLevelDef.tier,
        nextLevelXP: getXPToNextLevel(newLevelDef.level, newTotalXP),
        progressToNext: getProgressToNextLevel(newLevelDef.level, newTotalXP),
        levelUpAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp
      }
    } else {
      return {
        currentXP: currentLevel.currentXP + xpGained,
        totalXP: newTotalXP,
        nextLevelXP: getXPToNextLevel(currentLevel.currentLevel, newTotalXP),
        progressToNext: getProgressToNextLevel(currentLevel.currentLevel, newTotalXP),
        updatedAt: serverTimestamp() as Timestamp
      }
    }
  }

  /**
   * Handle level up rewards
   */
  private static async handleLevelUpRewards(
    userId: string,
    newLevel: number,
    transaction: any
  ): Promise<string[]> {
    // TODO: Implement level reward claiming logic
    // This will be implemented when we create the levelRewards collection
    return []
  }

  /**
   * Get user's current points for tier calculation
   */
  private static async getUserPoints(userId: string): Promise<number> {
    try {
      const profileDoc = await getDoc(doc(db, collections.profiles, userId))
      if (profileDoc.exists()) {
        const profileData = profileDoc.data()
        return profileData.points || profileData.totalPoints || 0
      }
      return 0
    } catch (error) {
      console.error('Error getting user points:', error)
      return 0
    }
  }
}

export default XPService
