/**
 * Mock Level System Test Data
 * 
 * Comprehensive test data for the user level/ranking system simulation.
 * Includes diverse user profiles, XP scenarios, and progression states.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Timestamp } from 'firebase/firestore'
import { UserLevel, XPTransaction, LevelReward, getLevelByXP } from '@/lib/levelSystem'
import { UserProfileWithLevel } from '@/types/levelProfile'

// ===== MOCK USER PROFILES =====

export const mockUsers: UserProfileWithLevel[] = [
  // New User - Level 1
  {
    id: 'user-newbie',
    email: '<EMAIL>',
    displayName: '<PERSON> Newbie',
    firstName: '<PERSON>',
    lastName: 'Newbie',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=alex',
    bio: 'Just discovered the amazing world of mechanical keyboards!',
    location: 'San Francisco, CA',
    role: 'user',
    points: 25,
    isDiscordLinked: false,
    emailVerified: true,
    phoneVerified: false,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-18'),
    preferences: {
      emailNotifications: true,
      smsNotifications: false,
      raffleNotifications: true,
      orderUpdates: true,
      marketingEmails: true,
      newsletter: true,
      language: 'en',
      timezone: 'America/Los_Angeles',
      currency: 'USD'
    },
    profileCompletion: {
      percentage: 60,
      completedSections: ['basic', 'contact'],
      missingSections: ['bio', 'social', 'preferences']
    },
    achievements: [],
    onboardingCompleted: true,
    mfaEnabled: false,
    mfaSetupCompleted: false,
    level: {
      current: 1,
      currentXP: 50,
      totalXP: 50,
      levelName: 'Switch Novice',
      tier: 'novice',
      nextLevelXP: 50,
      progressToNext: 50,
      unclaimedRewards: [],
      milestoneRewards: [],
      levelUpNotificationSeen: true
    }
  },

  // Mid-Level User - Level 15
  {
    id: 'user-enthusiast',
    email: '<EMAIL>',
    displayName: 'Jordan KeyMaster',
    firstName: 'Jordan',
    lastName: 'KeyMaster',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=jordan',
    bio: 'Artisan keycap collector and mechanical keyboard enthusiast. Always hunting for the perfect switch!',
    location: 'Austin, TX',
    website: 'https://jordankeys.blog',
    socialLinks: {
      discord: 'JordanKeys#1234',
      instagram: '@jordankeycaps'
    },
    role: 'user',
    points: 1250,
    isDiscordLinked: true,
    emailVerified: true,
    phoneVerified: true,
    createdAt: new Date('2023-08-10'),
    updatedAt: new Date('2024-01-18'),
    lastLoginAt: new Date('2024-01-18'),
    preferences: {
      emailNotifications: true,
      smsNotifications: true,
      raffleNotifications: true,
      orderUpdates: true,
      marketingEmails: true,
      newsletter: true,
      language: 'en',
      timezone: 'America/Chicago',
      currency: 'USD'
    },
    profileCompletion: {
      percentage: 95,
      completedSections: ['basic', 'contact', 'bio', 'social', 'preferences'],
      missingSections: ['verification']
    },
    achievements: [
      { achievementId: 'first_purchase', unlockedAt: new Date('2023-08-15') },
      { achievementId: 'community_member', unlockedAt: new Date('2023-09-01') },
      { achievementId: 'review_master', unlockedAt: new Date('2023-10-20') }
    ],
    onboardingCompleted: true,
    mfaEnabled: true,
    mfaSetupCompleted: true,
    level: {
      current: 15,
      currentXP: 200,
      totalXP: 5950,
      levelName: 'Artisan Admirer',
      tier: 'intermediate',
      nextLevelXP: 600,
      progressToNext: 25,
      lastLevelUp: Timestamp.fromDate(new Date('2024-01-10')),
      unclaimedRewards: ['reward-level-15-badge'],
      milestoneRewards: ['reward-level-5-badge', 'reward-level-10-keycap'],
      levelUpNotificationSeen: false
    }
  },

  // High-Level User - Level 35
  {
    id: 'user-expert',
    email: '<EMAIL>',
    displayName: 'Casey DesignDeity',
    firstName: 'Casey',
    lastName: 'DesignDeity',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=casey',
    bio: 'Professional keycap designer and community leader. Creating beautiful artisan pieces for the mechanical keyboard community.',
    location: 'Seattle, WA',
    website: 'https://caseydesigns.com',
    socialLinks: {
      discord: 'CaseyDesign#0001',
      instagram: '@caseydesigns',
      twitter: '@caseykeycaps'
    },
    role: 'user',
    points: 4200,
    isDiscordLinked: true,
    emailVerified: true,
    phoneVerified: true,
    createdAt: new Date('2022-03-15'),
    updatedAt: new Date('2024-01-18'),
    lastLoginAt: new Date('2024-01-18'),
    preferences: {
      emailNotifications: true,
      smsNotifications: true,
      raffleNotifications: true,
      orderUpdates: true,
      marketingEmails: true,
      newsletter: true,
      language: 'en',
      timezone: 'America/Los_Angeles',
      currency: 'USD'
    },
    profileCompletion: {
      percentage: 100,
      completedSections: ['basic', 'contact', 'bio', 'social', 'preferences', 'verification'],
      missingSections: []
    },
    achievements: [
      { achievementId: 'first_purchase', unlockedAt: new Date('2022-03-20') },
      { achievementId: 'community_leader', unlockedAt: new Date('2022-06-15') },
      { achievementId: 'design_master', unlockedAt: new Date('2022-12-01') },
      { achievementId: 'platinum_tier', unlockedAt: new Date('2023-05-10') },
      { achievementId: 'contest_winner', unlockedAt: new Date('2023-08-20') }
    ],
    onboardingCompleted: true,
    mfaEnabled: true,
    mfaSetupCompleted: true,
    level: {
      current: 35,
      currentXP: 1200,
      totalXP: 31450,
      levelName: 'Design Deity',
      tier: 'advanced',
      nextLevelXP: 550,
      progressToNext: 68,
      lastLevelUp: Timestamp.fromDate(new Date('2024-01-05')),
      unclaimedRewards: [],
      milestoneRewards: [
        'reward-level-5-badge', 'reward-level-10-keycap', 'reward-level-15-badge',
        'reward-level-20-keycap', 'reward-level-25-title', 'reward-level-30-keycap'
      ],
      levelUpNotificationSeen: true
    }
  },

  // Max Level User - Level 50
  {
    id: 'user-legend',
    email: '<EMAIL>',
    displayName: 'Sam SyndicapsLegend',
    firstName: 'Sam',
    lastName: 'Legend',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sam',
    bio: 'Syndicaps Legend and community ambassador. Helping shape the future of mechanical keyboards and artisan keycaps.',
    location: 'New York, NY',
    website: 'https://samlegend.com',
    socialLinks: {
      discord: 'SamLegend#0000',
      instagram: '@samsyndicaps',
      twitter: '@samlegend',
      linkedin: 'samlegend'
    },
    role: 'user',
    points: 8500,
    isDiscordLinked: true,
    emailVerified: true,
    phoneVerified: true,
    createdAt: new Date('2021-06-01'),
    updatedAt: new Date('2024-01-18'),
    lastLoginAt: new Date('2024-01-18'),
    preferences: {
      emailNotifications: true,
      smsNotifications: true,
      raffleNotifications: true,
      orderUpdates: true,
      marketingEmails: true,
      newsletter: true,
      language: 'en',
      timezone: 'America/New_York',
      currency: 'USD'
    },
    profileCompletion: {
      percentage: 100,
      completedSections: ['basic', 'contact', 'bio', 'social', 'preferences', 'verification'],
      missingSections: []
    },
    achievements: [
      { achievementId: 'first_purchase', unlockedAt: new Date('2021-06-05') },
      { achievementId: 'community_founder', unlockedAt: new Date('2021-08-01') },
      { achievementId: 'design_legend', unlockedAt: new Date('2022-01-15') },
      { achievementId: 'platinum_tier', unlockedAt: new Date('2022-03-20') },
      { achievementId: 'contest_champion', unlockedAt: new Date('2022-07-10') },
      { achievementId: 'ambassador', unlockedAt: new Date('2023-01-01') },
      { achievementId: 'max_level', unlockedAt: new Date('2023-12-15') }
    ],
    onboardingCompleted: true,
    mfaEnabled: true,
    mfaSetupCompleted: true,
    level: {
      current: 50,
      currentXP: 2500,
      totalXP: 66200,
      levelName: 'Syndicaps Legend',
      tier: 'expert',
      nextLevelXP: 0,
      progressToNext: 100,
      lastLevelUp: Timestamp.fromDate(new Date('2023-12-15')),
      unclaimedRewards: [],
      milestoneRewards: [
        'reward-level-5-badge', 'reward-level-10-keycap', 'reward-level-15-badge',
        'reward-level-20-keycap', 'reward-level-25-title', 'reward-level-30-keycap',
        'reward-level-35-badge', 'reward-level-40-keycap', 'reward-level-45-title',
        'reward-level-50-legend'
      ],
      levelUpNotificationSeen: true
    }
  }
]

// ===== MOCK XP TRANSACTIONS =====

export const mockXPTransactions: XPTransaction[] = [
  // Recent transactions for testing
  {
    id: 'xp-001',
    userId: 'user-enthusiast',
    amount: 150,
    source: 'purchase',
    sourceId: 'order-12345',
    multiplier: 1.1,
    description: 'Purchase XP for order #12345 ($75.00) with Silver tier bonus',
    metadata: {
      orderAmount: 75,
      baseXP: 150,
      tierBonus: true
    },
    createdAt: Timestamp.fromDate(new Date('2024-01-18T10:30:00'))
  },
  {
    id: 'xp-002',
    userId: 'user-enthusiast',
    amount: 25,
    source: 'activity',
    sourceId: 'review-789',
    multiplier: 1.0,
    description: 'Product review activity',
    metadata: {
      activityType: 'PRODUCT_REVIEW',
      hasMedia: true
    },
    createdAt: Timestamp.fromDate(new Date('2024-01-18T14:15:00'))
  },
  {
    id: 'xp-003',
    userId: 'user-expert',
    amount: 200,
    source: 'bonus',
    sourceId: 'weekly-streak',
    multiplier: 1.5,
    description: 'Weekly streak bonus',
    metadata: {
      streakDays: 7,
      bonusType: 'weekly_streak'
    },
    createdAt: Timestamp.fromDate(new Date('2024-01-18T09:00:00'))
  },
  {
    id: 'xp-004',
    userId: 'user-legend',
    amount: 500,
    source: 'event',
    sourceId: 'anniversary-2024',
    multiplier: 3.0,
    description: 'Anniversary celebration event XP',
    metadata: {
      eventType: 'anniversary',
      specialMultiplier: true
    },
    createdAt: Timestamp.fromDate(new Date('2024-01-15T12:00:00'))
  }
]

// ===== MOCK LEVEL REWARDS =====

export const mockLevelRewards: LevelReward[] = [
  {
    id: 'reward-level-5-badge',
    level: 5,
    type: 'badge',
    name: 'Community Member Badge',
    description: 'Welcome to the Syndicaps community!',
    value: 'community_member_badge',
    isExclusive: true,
    tier: 'novice'
  },
  {
    id: 'reward-level-10-keycap',
    level: 10,
    type: 'keycap',
    name: 'Enthusiast Keycap',
    description: 'Exclusive keycap for reaching level 10',
    value: 'enthusiast_keycap_blue',
    isExclusive: true,
    tier: 'novice'
  },
  {
    id: 'reward-level-15-badge',
    level: 15,
    type: 'badge',
    name: 'Artisan Admirer Badge',
    description: 'You appreciate the finer things in keycaps',
    value: 'artisan_admirer_badge',
    isExclusive: true,
    tier: 'intermediate'
  },
  {
    id: 'reward-level-20-keycap',
    level: 20,
    type: 'keycap',
    name: 'Contributor Keycap',
    description: 'Limited edition keycap for active contributors',
    value: 'contributor_keycap_purple',
    isExclusive: true,
    tier: 'intermediate'
  },
  {
    id: 'reward-level-25-title',
    level: 25,
    type: 'title',
    name: 'Keycap Curator',
    description: 'Exclusive community title',
    value: 'Keycap Curator',
    isExclusive: true,
    tier: 'intermediate'
  },
  {
    id: 'reward-level-30-keycap',
    level: 30,
    type: 'keycap',
    name: 'Captain Keycap',
    description: 'Leadership recognition keycap',
    value: 'captain_keycap_gold',
    isExclusive: true,
    tier: 'advanced'
  },
  {
    id: 'reward-level-50-legend',
    level: 50,
    type: 'title',
    name: 'Syndicaps Legend',
    description: 'Ultimate achievement - Syndicaps Legend status',
    value: 'Syndicaps Legend',
    isExclusive: true,
    tier: 'expert'
  }
]

// ===== SIMULATION SCENARIOS =====

export const simulationScenarios = {
  newUser: {
    name: 'New User Journey',
    description: 'Fresh account with minimal XP',
    user: mockUsers[0],
    suggestedActions: [
      'Award welcome bonus (50 XP)',
      'Complete profile (50 XP)',
      'First purchase simulation (100 XP)',
      'Level up to Level 2'
    ]
  },
  
  levelUpReady: {
    name: 'Level Up Ready',
    description: 'User close to next level',
    user: { ...mockUsers[1], level: { ...mockUsers[1].level, currentXP: 750, progressToNext: 95 } },
    suggestedActions: [
      'Small XP award to trigger level up',
      'Test level up modal',
      'Claim level rewards'
    ]
  },
  
  rewardClaiming: {
    name: 'Reward Claiming',
    description: 'User with unclaimed rewards',
    user: { 
      ...mockUsers[1], 
      level: { 
        ...mockUsers[1].level, 
        unclaimedRewards: ['reward-level-15-badge', 'reward-level-10-keycap'] 
      } 
    },
    suggestedActions: [
      'View rewards panel',
      'Claim available rewards',
      'Test reward claiming flow'
    ]
  },
  
  maxLevel: {
    name: 'Max Level User',
    description: 'Level 50 Syndicaps Legend',
    user: mockUsers[3],
    suggestedActions: [
      'Test max level display',
      'Award XP without level up',
      'Show all claimed rewards'
    ]
  }
}

// ===== UTILITY FUNCTIONS =====

export const generateRandomXP = (min: number = 10, max: number = 500): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export const createMockXPTransaction = (
  userId: string,
  source: XPTransaction['source'],
  amount?: number
): Omit<XPTransaction, 'id'> => {
  const xpAmount = amount || generateRandomXP()
  
  return {
    userId,
    amount: xpAmount,
    source,
    sourceId: `${source}-${Date.now()}`,
    multiplier: 1.0,
    description: `Test ${source} XP award`,
    metadata: {
      test: true,
      timestamp: new Date().toISOString()
    },
    createdAt: Timestamp.fromDate(new Date())
  }
}

export const getUserByLevel = (targetLevel: number): UserProfileWithLevel | null => {
  return mockUsers.find(user => user.level.current === targetLevel) || null
}

export const getUsersByTier = (tier: 'novice' | 'intermediate' | 'advanced' | 'expert'): UserProfileWithLevel[] => {
  return mockUsers.filter(user => user.level.tier === tier)
}

export default {
  mockUsers,
  mockXPTransactions,
  mockLevelRewards,
  simulationScenarios,
  generateRandomXP,
  createMockXPTransaction,
  getUserByLevel,
  getUsersByTier
}
