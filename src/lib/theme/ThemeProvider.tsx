/**
 * Theme Provider
 * 
 * React context provider for theme management with persistence,
 * system preference detection, and accessibility support.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

'use client'

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { 
  ThemeMode, 
  ColorScheme, 
  ThemeConfig, 
  ThemeColors,
  defaultThemeConfig,
  getThemeColors,
  createCSSVariables
} from './themeConfig'

// ===== TYPES =====

interface ThemeContextType {
  // Current theme state
  mode: ThemeMode
  colorScheme: ColorScheme
  colors: ThemeColors
  config: ThemeConfig
  
  // System preferences
  systemPrefersDark: boolean
  systemPrefersReducedMotion: boolean
  systemPrefersHighContrast: boolean
  
  // Theme actions
  setMode: (mode: ThemeMode) => void
  setColorScheme: (scheme: ColorScheme) => void
  toggleMode: () => void
  resetToDefaults: () => void
  
  // Utility functions
  isDark: boolean
  isHighContrast: boolean
  getColor: (path: string) => string
  
  // Status
  isLoading: boolean
  isHydrated: boolean
}

interface ThemeProviderProps {
  children: ReactNode
  defaultMode?: ThemeMode
  defaultColorScheme?: ColorScheme
  enablePersistence?: boolean
  storageKey?: string
}

// ===== CONTEXT =====

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// ===== PROVIDER =====

export function ThemeProvider({
  children,
  defaultMode = 'system',
  defaultColorScheme = 'default',
  enablePersistence = true,
  storageKey = 'syndicaps-theme'
}: ThemeProviderProps) {
  const [mode, setModeState] = useState<ThemeMode>(defaultMode)
  const [colorScheme, setColorSchemeState] = useState<ColorScheme>(defaultColorScheme)
  const [systemPrefersDark, setSystemPrefersDark] = useState(false)
  const [systemPrefersReducedMotion, setSystemPrefersReducedMotion] = useState(false)
  const [systemPrefersHighContrast, setSystemPrefersHighContrast] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isHydrated, setIsHydrated] = useState(false)

  // ===== SYSTEM PREFERENCE DETECTION =====

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Detect system preferences
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)')

    // Set initial values
    setSystemPrefersDark(darkModeQuery.matches)
    setSystemPrefersReducedMotion(reducedMotionQuery.matches)
    setSystemPrefersHighContrast(highContrastQuery.matches)

    // Create listeners
    const handleDarkModeChange = (e: MediaQueryListEvent) => setSystemPrefersDark(e.matches)
    const handleReducedMotionChange = (e: MediaQueryListEvent) => setSystemPrefersReducedMotion(e.matches)
    const handleHighContrastChange = (e: MediaQueryListEvent) => setSystemPrefersHighContrast(e.matches)

    // Add listeners
    darkModeQuery.addEventListener('change', handleDarkModeChange)
    reducedMotionQuery.addEventListener('change', handleReducedMotionChange)
    highContrastQuery.addEventListener('change', handleHighContrastChange)

    return () => {
      darkModeQuery.removeEventListener('change', handleDarkModeChange)
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange)
      highContrastQuery.removeEventListener('change', handleHighContrastChange)
    }
  }, [])

  // ===== PERSISTENCE =====

  // Load theme from storage
  useEffect(() => {
    if (typeof window === 'undefined' || !enablePersistence) {
      setIsLoading(false)
      setIsHydrated(true)
      return
    }

    try {
      const savedTheme = localStorage.getItem(storageKey)
      if (savedTheme) {
        const { mode: savedMode, colorScheme: savedColorScheme } = JSON.parse(savedTheme)
        if (savedMode) setModeState(savedMode)
        if (savedColorScheme) setColorSchemeState(savedColorScheme)
      }
    } catch (error) {
      console.warn('Failed to load theme from storage:', error)
    } finally {
      setIsLoading(false)
      setIsHydrated(true)
    }
  }, [enablePersistence, storageKey])

  // Save theme to storage
  useEffect(() => {
    if (!isHydrated || !enablePersistence || typeof window === 'undefined') return

    try {
      localStorage.setItem(storageKey, JSON.stringify({ mode, colorScheme }))
    } catch (error) {
      console.warn('Failed to save theme to storage:', error)
    }
  }, [mode, colorScheme, isHydrated, enablePersistence, storageKey])

  // ===== COMPUTED VALUES =====

  const colors = getThemeColors(mode, colorScheme, systemPrefersDark)
  const isDark = mode === 'dark' || (mode === 'system' && systemPrefersDark)
  const isHighContrast = colorScheme === 'high-contrast' || systemPrefersHighContrast

  const config: ThemeConfig = {
    ...defaultThemeConfig,
    mode,
    colorScheme,
    colors
  }

  // ===== CSS VARIABLES =====

  useEffect(() => {
    if (typeof window === 'undefined') return

    const root = document.documentElement
    const cssVariables = createCSSVariables(colors)

    // Apply CSS variables
    Object.entries(cssVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })

    // Add theme classes to body
    const body = document.body
    body.classList.remove('theme-light', 'theme-dark', 'theme-high-contrast', 'theme-colorblind-friendly')
    
    body.classList.add(isDark ? 'theme-dark' : 'theme-light')
    if (colorScheme !== 'default') {
      body.classList.add(`theme-${colorScheme}`)
    }

    // Add reduced motion class
    if (systemPrefersReducedMotion) {
      body.classList.add('motion-reduce')
    } else {
      body.classList.remove('motion-reduce')
    }

    // Update meta theme-color for mobile browsers
    const themeColorMeta = document.querySelector('meta[name="theme-color"]')
    if (themeColorMeta) {
      themeColorMeta.setAttribute('content', colors.components.background.primary)
    }

    // Update color-scheme for native browser features
    root.style.colorScheme = isDark ? 'dark' : 'light'

  }, [colors, isDark, colorScheme, systemPrefersReducedMotion])

  // ===== ACTIONS =====

  const setMode = useCallback((newMode: ThemeMode) => {
    setModeState(newMode)
  }, [])

  const setColorScheme = useCallback((newColorScheme: ColorScheme) => {
    setColorSchemeState(newColorScheme)
  }, [])

  const toggleMode = useCallback(() => {
    if (mode === 'system') {
      setModeState(systemPrefersDark ? 'light' : 'dark')
    } else if (mode === 'light') {
      setModeState('dark')
    } else {
      setModeState('light')
    }
  }, [mode, systemPrefersDark])

  const resetToDefaults = useCallback(() => {
    setModeState(defaultMode)
    setColorSchemeState(defaultColorScheme)
  }, [defaultMode, defaultColorScheme])

  // ===== UTILITY FUNCTIONS =====

  const getColor = useCallback((path: string): string => {
    const keys = path.split('.')
    let value: any = colors
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        console.warn(`Color path "${path}" not found in theme`)
        return colors.gray[500] // Fallback color
      }
    }
    
    return typeof value === 'string' ? value : colors.gray[500]
  }, [colors])

  // ===== CONTEXT VALUE =====

  const contextValue: ThemeContextType = {
    // Current state
    mode,
    colorScheme,
    colors,
    config,
    
    // System preferences
    systemPrefersDark,
    systemPrefersReducedMotion,
    systemPrefersHighContrast,
    
    // Actions
    setMode,
    setColorScheme,
    toggleMode,
    resetToDefaults,
    
    // Computed values
    isDark,
    isHighContrast,
    getColor,
    
    // Status
    isLoading,
    isHydrated
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  )
}

// ===== THEME HOOKS =====

/**
 * Hook for accessing theme colors with intellisense
 */
export function useThemeColors() {
  const { colors, getColor } = useTheme()
  return { colors, getColor }
}

/**
 * Hook for theme mode management
 */
export function useThemeMode() {
  const { mode, setMode, toggleMode, isDark, systemPrefersDark } = useTheme()
  return { mode, setMode, toggleMode, isDark, systemPrefersDark }
}

/**
 * Hook for accessibility preferences
 */
export function useThemeAccessibility() {
  const { 
    colorScheme, 
    setColorScheme, 
    isHighContrast, 
    systemPrefersReducedMotion,
    systemPrefersHighContrast 
  } = useTheme()
  
  return {
    colorScheme,
    setColorScheme,
    isHighContrast,
    prefersReducedMotion: systemPrefersReducedMotion,
    prefersHighContrast: systemPrefersHighContrast
  }
}

/**
 * Hook for gamification-specific theme utilities
 */
export function useGamificationTheme() {
  const { colors, getColor, isDark } = useTheme()
  
  const getAchievementColor = useCallback((rarity: string) => {
    const rarityMap = {
      common: colors.gamification.achievement.common,
      uncommon: colors.gamification.achievement.uncommon,
      rare: colors.gamification.achievement.rare,
      epic: colors.gamification.achievement.epic,
      legendary: colors.gamification.achievement.legendary
    }
    return rarityMap[rarity as keyof typeof rarityMap] || colors.gamification.achievement.common
  }, [colors])

  const getTierColor = useCallback((tier: string) => {
    const tierMap = {
      bronze: colors.gamification.tier.bronze,
      silver: colors.gamification.tier.silver,
      gold: colors.gamification.tier.gold,
      platinum: colors.gamification.tier.platinum,
      diamond: colors.gamification.tier.diamond
    }
    return tierMap[tier as keyof typeof tierMap] || colors.gamification.tier.bronze
  }, [colors])

  const getProgressColor = useCallback((type: string) => {
    const progressMap = {
      empty: colors.gamification.progress.empty,
      partial: colors.gamification.progress.partial,
      complete: colors.gamification.progress.complete,
      overflow: colors.gamification.progress.overflow
    }
    return progressMap[type as keyof typeof progressMap] || colors.gamification.progress.partial
  }, [colors])

  return {
    gamificationColors: colors.gamification,
    getAchievementColor,
    getTierColor,
    getProgressColor,
    isDark
  }
}

export default ThemeProvider