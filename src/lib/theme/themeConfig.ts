/**
 * Theme Configuration
 * 
 * Comprehensive theme system with light/dark mode support, 
 * color schemes, and accessibility considerations.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

// ===== THEME TYPES =====

export type ThemeMode = 'light' | 'dark' | 'system'
export type ColorScheme = 'default' | 'high-contrast' | 'colorblind-friendly'

export interface ThemeColors {
  // Base colors
  white: string
  black: string
  transparent: string
  
  // Gray scale
  gray: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }

  // Primary accent colors
  accent: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }

  // Gamification colors
  gamification: {
    achievement: {
      common: string
      uncommon: string
      rare: string
      epic: string
      legendary: string
    }
    tier: {
      bronze: string
      silver: string
      gold: string
      platinum: string
      diamond: string
    }
    progress: {
      empty: string
      partial: string
      complete: string
      overflow: string
    }
    points: {
      positive: string
      negative: string
      neutral: string
    }
  }

  // Semantic colors
  semantic: {
    success: string
    warning: string
    error: string
    info: string
    successBg: string
    warningBg: string
    errorBg: string
    infoBg: string
  }

  // Component colors
  components: {
    background: {
      primary: string
      secondary: string
      tertiary: string
      elevated: string
      overlay: string
    }
    text: {
      primary: string
      secondary: string
      tertiary: string
      inverse: string
      disabled: string
    }
    border: {
      primary: string
      secondary: string
      focus: string
      error: string
    }
    interactive: {
      primary: string
      primaryHover: string
      primaryActive: string
      secondary: string
      secondaryHover: string
      secondaryActive: string
      disabled: string
    }
  }
}

export interface ThemeConfig {
  mode: ThemeMode
  colorScheme: ColorScheme
  colors: ThemeColors
  fonts: {
    sans: string[]
    mono: string[]
  }
  spacing: Record<string, string>
  borderRadius: Record<string, string>
  shadows: Record<string, string>
  transitions: Record<string, string>
  zIndex: Record<string, number>
}

// ===== LIGHT THEME =====

export const lightTheme: ThemeColors = {
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
  
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712'
  },

  accent: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7',
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87',
    950: '#3b0764'
  },

  gamification: {
    achievement: {
      common: '#10b981',
      uncommon: '#3b82f6',
      rare: '#8b5cf6',
      epic: '#f59e0b',
      legendary: '#ef4444'
    },
    tier: {
      bronze: '#cd7f32',
      silver: '#c0c0c0',
      gold: '#ffd700',
      platinum: '#e5e4e2',
      diamond: '#b9f2ff'
    },
    progress: {
      empty: '#e5e7eb',
      partial: '#a855f7',
      complete: '#10b981',
      overflow: '#f59e0b'
    },
    points: {
      positive: '#10b981',
      negative: '#ef4444',
      neutral: '#6b7280'
    }
  },

  semantic: {
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    successBg: '#d1fae5',
    warningBg: '#fef3c7',
    errorBg: '#fee2e2',
    infoBg: '#dbeafe'
  },

  components: {
    background: {
      primary: '#ffffff',
      secondary: '#f9fafb',
      tertiary: '#f3f4f6',
      elevated: '#ffffff',
      overlay: 'rgba(0, 0, 0, 0.5)'
    },
    text: {
      primary: '#111827',
      secondary: '#4b5563',
      tertiary: '#6b7280',
      inverse: '#ffffff',
      disabled: '#9ca3af'
    },
    border: {
      primary: '#e5e7eb',
      secondary: '#d1d5db',
      focus: '#a855f7',
      error: '#ef4444'
    },
    interactive: {
      primary: '#a855f7',
      primaryHover: '#9333ea',
      primaryActive: '#7c3aed',
      secondary: '#f3f4f6',
      secondaryHover: '#e5e7eb',
      secondaryActive: '#d1d5db',
      disabled: '#9ca3af'
    }
  }
}

// ===== DARK THEME =====

export const darkTheme: ThemeColors = {
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
  
  gray: {
    50: '#030712',
    100: '#111827',
    200: '#1f2937',
    300: '#374151',
    400: '#4b5563',
    500: '#6b7280',
    600: '#9ca3af',
    700: '#d1d5db',
    800: '#e5e7eb',
    900: '#f3f4f6',
    950: '#f9fafb'
  },

  accent: {
    50: '#3b0764',
    100: '#581c87',
    200: '#6b21a8',
    300: '#7c3aed',
    400: '#9333ea',
    500: '#a855f7',
    600: '#c084fc',
    700: '#d8b4fe',
    800: '#e9d5ff',
    900: '#f3e8ff',
    950: '#faf5ff'
  },

  gamification: {
    achievement: {
      common: '#34d399',
      uncommon: '#60a5fa',
      rare: '#a78bfa',
      epic: '#fbbf24',
      legendary: '#f87171'
    },
    tier: {
      bronze: '#d4a574',
      silver: '#d1d5db',
      gold: '#fde047',
      platinum: '#f3f4f6',
      diamond: '#7dd3fc'
    },
    progress: {
      empty: '#374151',
      partial: '#a855f7',
      complete: '#10b981',
      overflow: '#f59e0b'
    },
    points: {
      positive: '#34d399',
      negative: '#f87171',
      neutral: '#9ca3af'
    }
  },

  semantic: {
    success: '#34d399',
    warning: '#fbbf24',
    error: '#f87171',
    info: '#60a5fa',
    successBg: '#064e3b',
    warningBg: '#78350f',
    errorBg: '#7f1d1d',
    infoBg: '#1e3a8a'
  },

  components: {
    background: {
      primary: '#111827',
      secondary: '#1f2937',
      tertiary: '#374151',
      elevated: '#1f2937',
      overlay: 'rgba(0, 0, 0, 0.75)'
    },
    text: {
      primary: '#f9fafb',
      secondary: '#d1d5db',
      tertiary: '#9ca3af',
      inverse: '#111827',
      disabled: '#6b7280'
    },
    border: {
      primary: '#374151',
      secondary: '#4b5563',
      focus: '#a855f7',
      error: '#f87171'
    },
    interactive: {
      primary: '#a855f7',
      primaryHover: '#c084fc',
      primaryActive: '#d8b4fe',
      secondary: '#374151',
      secondaryHover: '#4b5563',
      secondaryActive: '#6b7280',
      disabled: '#6b7280'
    }
  }
}

// ===== HIGH CONTRAST THEMES =====

export const lightHighContrastTheme: ThemeColors = {
  ...lightTheme,
  components: {
    ...lightTheme.components,
    background: {
      primary: '#ffffff',
      secondary: '#ffffff',
      tertiary: '#f3f4f6',
      elevated: '#ffffff',
      overlay: 'rgba(0, 0, 0, 0.8)'
    },
    text: {
      primary: '#000000',
      secondary: '#000000',
      tertiary: '#374151',
      inverse: '#ffffff',
      disabled: '#6b7280'
    },
    border: {
      primary: '#000000',
      secondary: '#374151',
      focus: '#0066cc',
      error: '#cc0000'
    }
  }
}

export const darkHighContrastTheme: ThemeColors = {
  ...darkTheme,
  components: {
    ...darkTheme.components,
    background: {
      primary: '#000000',
      secondary: '#000000',
      tertiary: '#1f2937',
      elevated: '#111827',
      overlay: 'rgba(255, 255, 255, 0.8)'
    },
    text: {
      primary: '#ffffff',
      secondary: '#ffffff',
      tertiary: '#d1d5db',
      inverse: '#000000',
      disabled: '#9ca3af'
    },
    border: {
      primary: '#ffffff',
      secondary: '#d1d5db',
      focus: '#66b3ff',
      error: '#ff6666'
    }
  }
}

// ===== DEFAULT THEME CONFIG =====

export const defaultThemeConfig: ThemeConfig = {
  mode: 'system',
  colorScheme: 'default',
  colors: lightTheme,
  fonts: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'Menlo', 'Monaco', 'monospace']
  },
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem'
  },
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    default: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    default: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none'
  },
  transitions: {
    none: 'none',
    all: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    default: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    colors: 'color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    opacity: 'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    shadow: 'box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    transform: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1)'
  },
  zIndex: {
    auto: 0,
    0: 0,
    10: 10,
    20: 20,
    30: 30,
    40: 40,
    50: 50,
    modal: 1000,
    popover: 1010,
    tooltip: 1020,
    toast: 1030,
    dropdown: 1040
  }
}

// ===== THEME UTILITIES =====

export function getThemeColors(mode: ThemeMode, colorScheme: ColorScheme, systemPrefersDark: boolean): ThemeColors {
  const isDark = mode === 'dark' || (mode === 'system' && systemPrefersDark)
  
  if (colorScheme === 'high-contrast') {
    return isDark ? darkHighContrastTheme : lightHighContrastTheme
  }
  
  // For colorblind-friendly, we'd modify colors for better accessibility
  if (colorScheme === 'colorblind-friendly') {
    const baseTheme = isDark ? darkTheme : lightTheme
    return {
      ...baseTheme,
      gamification: {
        ...baseTheme.gamification,
        achievement: {
          common: '#2563eb',    // Blue instead of green
          uncommon: '#7c3aed',  // Purple
          rare: '#dc2626',      // Red instead of purple
          epic: '#ea580c',      // Orange
          legendary: '#facc15'  // Yellow instead of red
        }
      }
    }
  }
  
  return isDark ? darkTheme : lightTheme
}

export function createCSSVariables(colors: ThemeColors): Record<string, string> {
  const variables: Record<string, string> = {}
  
  // Flatten the color object into CSS custom properties
  const flattenColors = (obj: any, prefix = '') => {
    Object.entries(obj).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        flattenColors(value, `${prefix}${key}-`)
      } else {
        variables[`--color-${prefix}${key}`] = value as string
      }
    })
  }
  
  flattenColors(colors)
  
  return variables
}

export default {
  defaultThemeConfig,
  lightTheme,
  darkTheme,
  lightHighContrastTheme,
  darkHighContrastTheme,
  getThemeColors,
  createCSSVariables
}