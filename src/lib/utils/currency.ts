/**
 * Currency Formatting Utilities
 * 
 * Provides consistent currency formatting throughout the application.
 * Supports multiple currencies and locales.
 * 
 * <AUTHOR> Team
 */

export interface CurrencyOptions {
  currency?: string
  locale?: string
  minimumFractionDigits?: number
  maximumFractionDigits?: number
}

/**
 * Format a number as currency
 */
export const formatCurrency = (
  amount: number,
  options: CurrencyOptions = {}
): string => {
  const {
    currency = 'USD',
    locale = 'en-US',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2
  } = options

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits,
    maximumFractionDigits
  }).format(amount)
}

/**
 * Format a number as a compact currency (e.g., $1.2K)
 */
export const formatCompactCurrency = (
  amount: number,
  options: CurrencyOptions = {}
): string => {
  const {
    currency = 'USD',
    locale = 'en-US'
  } = options

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    notation: 'compact',
    maximumFractionDigits: 1
  }).format(amount)
}

/**
 * Parse a currency string to number
 */
export const parseCurrency = (currencyString: string): number => {
  // Remove currency symbols and parse
  const cleaned = currencyString.replace(/[^0-9.-]/g, '')
  return parseFloat(cleaned) || 0
}

/**
 * Calculate discount percentage
 */
export const calculateDiscountPercentage = (
  originalPrice: number,
  salePrice: number
): number => {
  if (originalPrice <= 0) return 0
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100)
}

/**
 * Format discount display
 */
export const formatDiscount = (
  originalPrice: number,
  salePrice: number,
  options: CurrencyOptions = {}
): {
  original: string
  sale: string
  percentage: number
  savings: string
} => {
  const percentage = calculateDiscountPercentage(originalPrice, salePrice)
  const savings = originalPrice - salePrice

  return {
    original: formatCurrency(originalPrice, options),
    sale: formatCurrency(salePrice, options),
    percentage,
    savings: formatCurrency(savings, options)
  }
}