/**
 * API Route: Cache Analytics
 * Provides cache performance analytics for Cloudflare hybrid deployment
 */

import type { NextApiRequest, NextApiResponse } from 'next'
import { createCacheManager, CacheAnalytics } from '../../../lib/cloudflare/cacheManager'
import { shouldUseFeature } from '../../../lib/config/featureFlags'

interface AnalyticsRequest {
  since?: string // ISO date string
  period?: '1h' | '6h' | '24h' | '7d' | '30d'
}

interface AnalyticsResponse {
  success: boolean
  data?: CacheAnalytics
  message?: string
  timestamp: string
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<AnalyticsResponse>
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
      timestamp: new Date().toISOString(),
    })
  }

  // Check if hybrid CDN feature is enabled
  if (!shouldUseFeature('USE_HYBRID_CDN')) {
    return res.status(503).json({
      success: false,
      message: 'Hybrid CDN feature is disabled',
      timestamp: new Date().toISOString(),
    })
  }

  try {
    // Parse query parameters
    const { since, period } = req.query as AnalyticsRequest

    // Create cache manager
    const cacheManager = createCacheManager()
    if (!cacheManager) {
      return res.status(500).json({
        success: false,
        message: 'Cache manager not available - check Cloudflare configuration',
        timestamp: new Date().toISOString(),
      })
    }

    // Calculate since date based on period
    let sinceDate: Date | undefined
    if (since) {
      sinceDate = new Date(since)
    } else if (period) {
      const now = new Date()
      switch (period) {
        case '1h':
          sinceDate = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '6h':
          sinceDate = new Date(now.getTime() - 6 * 60 * 60 * 1000)
          break
        case '24h':
          sinceDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          sinceDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          sinceDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          sinceDate = new Date(now.getTime() - 24 * 60 * 60 * 1000) // Default to 24h
      }
    }

    // Get cache analytics
    const analytics = await cacheManager.getCacheAnalytics(sinceDate)

    return res.status(200).json({
      success: true,
      data: analytics,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('❌ Cache analytics API error:', error)
    
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString(),
    })
  }
}

// Export for testing
export { handler }
