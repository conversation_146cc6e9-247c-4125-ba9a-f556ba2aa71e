/**
 * API Route: Cache Purge
 * Handles cache purging requests for Cloudflare hybrid deployment
 */

import type { NextApiRequest, NextApiResponse } from 'next'
import { createCacheManager, CachePurgeOptions } from '../../../lib/cloudflare/cacheManager'
import { shouldUseFeature } from '../../../lib/config/featureFlags'

interface PurgeRequest {
  urls?: string[]
  tags?: string[]
  hosts?: string[]
  prefixes?: string[]
  everything?: boolean
  reason?: string
}

interface PurgeResponse {
  success: boolean
  message: string
  purgeId?: string
  timestamp: string
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PurgeResponse>
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
      timestamp: new Date().toISOString(),
    })
  }

  // Check if hybrid CDN feature is enabled
  if (!shouldUseFeature('USE_HYBRID_CDN')) {
    return res.status(503).json({
      success: false,
      message: 'Hybrid CDN feature is disabled',
      timestamp: new Date().toISOString(),
    })
  }

  try {
    // Validate request body
    const { urls, tags, hosts, prefixes, everything, reason }: PurgeRequest = req.body

    // Basic validation
    if (!urls && !tags && !hosts && !prefixes && !everything) {
      return res.status(400).json({
        success: false,
        message: 'At least one purge option must be specified',
        timestamp: new Date().toISOString(),
      })
    }

    // Create cache manager
    const cacheManager = createCacheManager()
    if (!cacheManager) {
      return res.status(500).json({
        success: false,
        message: 'Cache manager not available - check Cloudflare configuration',
        timestamp: new Date().toISOString(),
      })
    }

    // Prepare purge options
    const purgeOptions: CachePurgeOptions = {
      urls,
      tags,
      hosts,
      prefixes,
      everything,
    }

    // Log purge request
    console.log('🗑️ Cache purge requested:', {
      options: purgeOptions,
      reason: reason || 'No reason provided',
      timestamp: new Date().toISOString(),
    })

    // Execute cache purge
    const success = await cacheManager.purgeCache(purgeOptions)

    if (success) {
      return res.status(200).json({
        success: true,
        message: 'Cache purged successfully',
        timestamp: new Date().toISOString(),
      })
    } else {
      return res.status(500).json({
        success: false,
        message: 'Cache purge failed',
        timestamp: new Date().toISOString(),
      })
    }
  } catch (error) {
    console.error('❌ Cache purge API error:', error)
    
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString(),
    })
  }
}

// Export for testing
export { handler }
