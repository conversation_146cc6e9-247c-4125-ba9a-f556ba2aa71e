/**
 * API Route: Cache Status
 * Provides cache status information for specific URLs
 */

import type { NextApiRequest, NextApiResponse } from 'next'
import { createCacheManager, CacheStatus } from '../../../lib/cloudflare/cacheManager'
import { shouldUseFeature } from '../../../lib/config/featureFlags'

interface StatusRequest {
  url: string
}

interface StatusResponse {
  success: boolean
  data?: CacheStatus
  message?: string
  timestamp: string
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<StatusResponse>
) {
  // Allow both GET and POST requests
  if (req.method !== 'GET' && req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
      timestamp: new Date().toISOString(),
    })
  }

  // Check if hybrid CDN feature is enabled
  if (!shouldUseFeature('USE_HYBRID_CDN')) {
    return res.status(503).json({
      success: false,
      message: 'Hybrid CDN feature is disabled',
      timestamp: new Date().toISOString(),
    })
  }

  try {
    // Get URL from query or body
    let url: string
    if (req.method === 'GET') {
      url = req.query.url as string
    } else {
      url = req.body.url
    }

    // Validate URL
    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL parameter is required',
        timestamp: new Date().toISOString(),
      })
    }

    // Validate URL format
    try {
      new URL(url)
    } catch {
      return res.status(400).json({
        success: false,
        message: 'Invalid URL format',
        timestamp: new Date().toISOString(),
      })
    }

    // Create cache manager
    const cacheManager = createCacheManager()
    if (!cacheManager) {
      return res.status(500).json({
        success: false,
        message: 'Cache manager not available - check Cloudflare configuration',
        timestamp: new Date().toISOString(),
      })
    }

    // Get cache status
    const status = await cacheManager.getCacheStatus(url)

    return res.status(200).json({
      success: true,
      data: status,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('❌ Cache status API error:', error)
    
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString(),
    })
  }
}

// Export for testing
export { handler }
