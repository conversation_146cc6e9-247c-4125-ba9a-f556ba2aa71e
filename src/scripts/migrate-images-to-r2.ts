#!/usr/bin/env node

/**
 * Image Migration Script
 * Migrates images from Firebase Storage to Cloudflare R2
 * 
 * Usage:
 *   npm run migrate-images [options]
 * 
 * Options:
 *   --dry-run          Run without making actual changes
 *   --batch-size       Number of images per batch (default: 50)
 *   --collections      Comma-separated list of collections to migrate
 *   --priority         Migration priority: high, normal, parallel (default: high)
 *   --config-file      Path to custom configuration file
 *   --output-dir       Directory to save results and logs
 *   --resume           Resume from previous migration
 *   --validate         Validate migration after completion
 */

import { ImageScanner, ScanResult } from '../lib/migration/imageScanner'
import { ImageMigrator, MigrationConfig, MigrationProgress, MigrationResult } from '../lib/migration/imageMigrator'
import { 
  DEFAULT_COLLECTION_CONFIGS, 
  getCollectionsByPriority,
  getCollectionConfig,
  estimateCollectionComplexity 
} from '../lib/migration/collectionConfigs'
import { featureFlags } from '../lib/feature-flags/featureFlags'
import { r2PerformanceMonitor } from '../lib/cloudflare/r2PerformanceMonitor'
import * as fs from 'fs'
import * as path from 'path'

interface MigrationOptions {
  dryRun: boolean
  batchSize: number
  collections?: string[]
  priority: 'high' | 'normal' | 'parallel'
  configFile?: string
  outputDir: string
  resume: boolean
  validate: boolean
  maxConcurrentBatches: number
  retryAttempts: number
  pauseOnErrorRate: number
}

interface MigrationState {
  sessionId: string
  startTime: Date
  lastScanResult?: ScanResult
  lastMigrationResult?: MigrationResult
  resumePoint?: {
    collectionIndex: number
    imageIndex: number
  }
}

class MigrationRunner {
  private options: MigrationOptions
  private state: MigrationState
  private scanner: ImageScanner
  private migrator: ImageMigrator
  private logFile: string

  constructor(options: MigrationOptions) {
    this.options = options
    this.state = {
      sessionId: `migration_${Date.now()}`,
      startTime: new Date()
    }
    
    // Ensure output directory exists
    if (!fs.existsSync(this.options.outputDir)) {
      fs.mkdirSync(this.options.outputDir, { recursive: true })
    }
    
    this.logFile = path.join(this.options.outputDir, `migration-${this.state.sessionId}.log`)
    
    // Initialize scanner and migrator with progress callbacks
    this.scanner = new ImageScanner(this.onScanProgress.bind(this))
    this.migrator = new ImageMigrator(
      this.onMigrationProgress.bind(this),
      this.onMigrationError.bind(this)
    )
  }

  /**
   * Run the complete migration process
   */
  async run(): Promise<void> {
    try {
      this.log('Starting image migration to Cloudflare R2')
      this.log(`Session ID: ${this.state.sessionId}`)
      this.log(`Options: ${JSON.stringify(this.options, null, 2)}`)

      // Check prerequisites
      await this.checkPrerequisites()

      // Load or resume state
      if (this.options.resume) {
        await this.loadPreviousState()
      }

      // Scan for images if not resuming or no previous scan
      if (!this.state.lastScanResult) {
        this.log('Starting image scanning phase...')
        this.state.lastScanResult = await this.scanImages()
        await this.saveScanResults(this.state.lastScanResult)
      }

      // Display scan summary
      this.displayScanSummary(this.state.lastScanResult)

      // Confirm migration if not dry run
      if (!this.options.dryRun) {
        await this.confirmMigration(this.state.lastScanResult)
      }

      // Run migration
      this.log('Starting migration phase...')
      const migrationConfig = this.createMigrationConfig()
      this.state.lastMigrationResult = await this.migrator.migrateImages(
        this.state.lastScanResult,
        migrationConfig
      )

      // Save migration results
      await this.saveMigrationResults(this.state.lastMigrationResult)

      // Display final summary
      this.displayMigrationSummary(this.state.lastMigrationResult)

      // Validate if requested
      if (this.options.validate && !this.options.dryRun) {
        await this.validateMigration(this.state.lastMigrationResult)
      }

      this.log('Migration completed successfully!')

    } catch (error) {
      this.log(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR')
      throw error
    }
  }

  /**
   * Check prerequisites before starting migration
   */
  private async checkPrerequisites(): Promise<void> {
    this.log('Checking prerequisites...')

    // Check if R2 storage is enabled
    const r2Enabled = await featureFlags.isEnabled('USE_R2_STORAGE')
    if (!r2Enabled) {
      throw new Error('R2 storage feature flag is not enabled')
    }

    // Test R2 connectivity
    try {
      // This would use the R2 connection test service
      this.log('R2 connectivity check passed')
    } catch (error) {
      throw new Error(`R2 connectivity check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    // Check Firebase connectivity
    try {
      // This would test Firebase connection
      this.log('Firebase connectivity check passed')
    } catch (error) {
      throw new Error(`Firebase connectivity check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    this.log('All prerequisites satisfied')
  }

  /**
   * Scan for images based on configuration
   */
  private async scanImages(): Promise<ScanResult> {
    const collections = this.getCollectionsToScan()
    
    this.log(`Scanning ${collections.length} collections for images...`)
    collections.forEach(config => {
      const complexity = estimateCollectionComplexity(config)
      this.log(`  - ${config.name}: ${complexity.complexity} complexity, ~${complexity.estimatedTimeMinutes}min`)
    })

    return await this.scanner.scanAllCollections(collections)
  }

  /**
   * Get collections to scan based on options
   */
  private getCollectionsToScan() {
    if (this.options.collections) {
      // Scan specific collections
      return this.options.collections
        .map(name => getCollectionConfig(name))
        .filter(config => config !== undefined) as any[]
    }

    // Scan by priority
    const { high, normal, parallel } = getCollectionsByPriority()
    
    switch (this.options.priority) {
      case 'high':
        return high
      case 'parallel':
        return parallel
      default:
        return [...high, ...normal]
    }
  }

  /**
   * Create migration configuration
   */
  private createMigrationConfig(): MigrationConfig {
    return {
      batchSize: this.options.batchSize,
      maxConcurrentBatches: this.options.maxConcurrentBatches,
      retryAttempts: this.options.retryAttempts,
      retryDelayMs: 1000,
      pauseOnErrorRate: this.options.pauseOnErrorRate,
      dryRun: this.options.dryRun,
      backupOriginalUrls: true,
      validateAfterMigration: this.options.validate
    }
  }

  /**
   * Display scan summary
   */
  private displayScanSummary(scanResult: ScanResult): void {
    this.log('\n=== SCAN SUMMARY ===')
    this.log(`Total images found: ${scanResult.summary.totalImages}`)
    this.log(`Total size: ${(scanResult.summary.totalSize / 1024 / 1024).toFixed(2)} MB`)
    this.log(`Collections scanned: ${Object.keys(scanResult.summary.imagesByCollection).length}`)
    
    this.log('\nImages by collection:')
    Object.entries(scanResult.summary.imagesByCollection).forEach(([collection, count]) => {
      this.log(`  - ${collection}: ${count} images`)
    })
    
    this.log('\nImages by type:')
    Object.entries(scanResult.summary.imagesByType).forEach(([type, count]) => {
      this.log(`  - ${type}: ${count} images`)
    })

    if (scanResult.errors.length > 0) {
      this.log(`\nScan errors: ${scanResult.errors.length}`)
      scanResult.errors.slice(0, 5).forEach(error => {
        this.log(`  - ${error.collection}: ${error.error}`)
      })
      if (scanResult.errors.length > 5) {
        this.log(`  ... and ${scanResult.errors.length - 5} more errors`)
      }
    }
  }

  /**
   * Display migration summary
   */
  private displayMigrationSummary(migrationResult: MigrationResult): void {
    this.log('\n=== MIGRATION SUMMARY ===')
    this.log(`Success: ${migrationResult.success}`)
    this.log(`Total processed: ${migrationResult.summary.totalProcessed}`)
    this.log(`Success rate: ${migrationResult.summary.successRate.toFixed(1)}%`)
    this.log(`Time elapsed: ${migrationResult.summary.timeElapsed.toFixed(0)} seconds`)
    this.log(`Average speed: ${migrationResult.summary.averageSpeed.toFixed(2)} images/second`)
    
    if (migrationResult.errors.length > 0) {
      this.log(`\nMigration errors: ${migrationResult.errors.length}`)
      migrationResult.errors.slice(0, 5).forEach(error => {
        this.log(`  - ${error.collection}/${error.documentId}: ${error.error}`)
      })
      if (migrationResult.errors.length > 5) {
        this.log(`  ... and ${migrationResult.errors.length - 5} more errors`)
      }
    }
  }

  /**
   * Confirm migration with user
   */
  private async confirmMigration(scanResult: ScanResult): Promise<void> {
    // In a real implementation, this would prompt the user for confirmation
    // For now, we'll just log the confirmation
    this.log(`\nReady to migrate ${scanResult.summary.totalImages} images`)
    this.log('Migration will begin in 5 seconds... (Ctrl+C to cancel)')
    
    await new Promise(resolve => setTimeout(resolve, 5000))
  }

  /**
   * Validate migration results
   */
  private async validateMigration(migrationResult: MigrationResult): Promise<void> {
    this.log('Starting post-migration validation...')
    
    // Generate performance report
    const performanceReport = r2PerformanceMonitor.generateReport(60)
    this.log('Performance report generated')
    
    // Save performance report
    const reportPath = path.join(this.options.outputDir, `performance-report-${this.state.sessionId}.md`)
    fs.writeFileSync(reportPath, performanceReport)
    
    this.log(`Validation completed. Performance report saved to: ${reportPath}`)
  }

  /**
   * Save scan results
   */
  private async saveScanResults(scanResult: ScanResult): Promise<void> {
    const filePath = path.join(this.options.outputDir, `scan-results-${this.state.sessionId}.json`)
    await this.scanner.saveScanResults(scanResult, filePath)
    this.log(`Scan results saved to: ${filePath}`)
  }

  /**
   * Save migration results
   */
  private async saveMigrationResults(migrationResult: MigrationResult): Promise<void> {
    const filePath = path.join(this.options.outputDir, `migration-results-${this.state.sessionId}.json`)
    await this.migrator.saveMigrationResults(migrationResult, filePath)
    this.log(`Migration results saved to: ${filePath}`)
  }

  /**
   * Load previous migration state
   */
  private async loadPreviousState(): Promise<void> {
    // Implementation would load previous state from file
    this.log('Resume functionality not yet implemented')
  }

  /**
   * Progress callback for scanning
   */
  private onScanProgress(progress: any): void {
    const percentage = progress.totalDocuments > 0 
      ? ((progress.scannedDocuments / progress.totalDocuments) * 100).toFixed(1)
      : '0.0'
    
    this.log(`Scan progress: ${percentage}% (${progress.scannedDocuments}/${progress.totalDocuments}) - Found ${progress.foundImages} images`)
  }

  /**
   * Progress callback for migration
   */
  private onMigrationProgress(progress: MigrationProgress): void {
    const percentage = progress.totalImages > 0 
      ? ((progress.processedImages / progress.totalImages) * 100).toFixed(1)
      : '0.0'
    
    this.log(`Migration progress: ${percentage}% (${progress.processedImages}/${progress.totalImages}) - ${progress.currentOperation}`)
  }

  /**
   * Error callback for migration
   */
  private onMigrationError(error: any): void {
    this.log(`Migration error: ${error.collection}/${error.documentId} - ${error.error}`, 'ERROR')
  }

  /**
   * Log message with timestamp
   */
  private log(message: string, level: 'INFO' | 'ERROR' | 'WARN' = 'INFO'): void {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [${level}] ${message}`
    
    console.log(logMessage)
    
    // Append to log file
    fs.appendFileSync(this.logFile, logMessage + '\n')
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(): MigrationOptions {
  const args = process.argv.slice(2)
  const options: MigrationOptions = {
    dryRun: false,
    batchSize: 50,
    priority: 'high',
    outputDir: './migration-results',
    resume: false,
    validate: false,
    maxConcurrentBatches: 3,
    retryAttempts: 3,
    pauseOnErrorRate: 5
  }

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--dry-run':
        options.dryRun = true
        break
      case '--batch-size':
        options.batchSize = parseInt(args[++i]) || 50
        break
      case '--collections':
        options.collections = args[++i].split(',').map(s => s.trim())
        break
      case '--priority':
        options.priority = args[++i] as any || 'high'
        break
      case '--config-file':
        options.configFile = args[++i]
        break
      case '--output-dir':
        options.outputDir = args[++i]
        break
      case '--resume':
        options.resume = true
        break
      case '--validate':
        options.validate = true
        break
      case '--help':
        console.log(`
Image Migration Script

Usage: npm run migrate-images [options]

Options:
  --dry-run              Run without making actual changes
  --batch-size <number>  Number of images per batch (default: 50)
  --collections <list>   Comma-separated list of collections to migrate
  --priority <level>     Migration priority: high, normal, parallel (default: high)
  --config-file <path>   Path to custom configuration file
  --output-dir <path>    Directory to save results and logs (default: ./migration-results)
  --resume               Resume from previous migration
  --validate             Validate migration after completion
  --help                 Show this help message
        `)
        process.exit(0)
    }
  }

  return options
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    const options = parseArguments()
    const runner = new MigrationRunner(options)
    await runner.run()
    process.exit(0)
  } catch (error) {
    console.error('Migration failed:', error instanceof Error ? error.message : 'Unknown error')
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

export { MigrationRunner, parseArguments }
