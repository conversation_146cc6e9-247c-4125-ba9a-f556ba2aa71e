/**
 * CSS Animations for Performance Optimization
 * 
 * High-performance CSS animations to replace Framer Motion
 * Optimized for 60fps and reduced bundle size
 * 
 * <AUTHOR> Team
 */

/* ===== KEYFRAME DEFINITIONS ===== */

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scale-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes bounce-subtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(147, 51, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.8);
  }
}

/* ===== ANIMATION CLASSES ===== */

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.animate-fade-out {
  animation: fade-out 0.3s ease-out forwards;
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out forwards;
}

.animate-fade-in-down {
  animation: fade-in-down 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.2s ease-out forwards;
}

.animate-scale-out {
  animation: scale-out 0.2s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.3s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

.animate-slide-in-up {
  animation: slide-in-up 0.3s ease-out forwards;
}

.animate-slide-in-down {
  animation: slide-in-down 0.3s ease-out forwards;
}

.animate-modal-in {
  animation: modal-in 0.2s ease-out forwards;
}

.animate-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.animate-bounce-subtle {
  animation: bounce-subtle 1s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* ===== ANIMATION DELAYS ===== */

.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* ===== INTERACTION STATES ===== */

.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hover-scale {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.6);
}

.tap-scale {
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

.tap-scale:active {
  transform: scale(0.95);
}

/* ===== LOADING STATES ===== */

.skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 0.375rem;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

/* ===== REDUCED MOTION SUPPORT ===== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-spin {
    animation: none;
  }
  
  .animate-bounce {
    animation: none;
  }
  
  .animate-pulse {
    animation: none;
  }
}

/* ===== MOBILE OPTIMIZATIONS ===== */

@media (max-width: 768px) {
  .animate-fade-in-up,
  .animate-fade-in-down,
  .animate-slide-in-left,
  .animate-slide-in-right {
    animation-duration: 0.2s;
  }
  
  .hover-lift:hover {
    transform: none;
    box-shadow: none;
  }
  
  .hover-scale:hover {
    transform: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */

@media (prefers-contrast: high) {
  .animate-glow {
    animation: none;
  }
  
  .hover-glow:hover {
    box-shadow: 0 0 0 2px currentColor;
  }
}

/* ===== UTILITY CLASSES ===== */

.animation-fill-forwards {
  animation-fill-mode: forwards;
}

.animation-fill-backwards {
  animation-fill-mode: backwards;
}

.animation-fill-both {
  animation-fill-mode: both;
}

.animation-infinite {
  animation-iteration-count: infinite;
}

.animation-once {
  animation-iteration-count: 1;
}

.animation-paused {
  animation-play-state: paused;
}

.animation-running {
  animation-play-state: running;
}
