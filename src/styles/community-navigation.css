/**
 * Enhanced Community Navigation Styles
 * 
 * Advanced hover effects, animations, and transitions for the
 * Syndicaps community tab navigation system.
 * 
 * <AUTHOR> Team
 */

/* CSS Custom Properties for Syndicaps Design System */
:root {
  --syndicaps-transition-fast: 150ms ease-out;
  --syndicaps-transition-normal: 300ms ease-out;
  --syndicaps-transition-slow: 500ms ease-out;
  --syndicaps-accent-hue: 270;
  --syndicaps-glow-intensity: 0.3;
}

/* Enhanced Tab Base Styles */
.community-tab {
  @apply relative overflow-hidden;
  @apply transition-all duration-300 ease-out;
  @apply hover:scale-105;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-400 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900;
  @apply focus-visible:shadow-lg focus-visible:shadow-accent-500/30;
}

/* Neon Glow Hover Effects */
.community-tab:hover {
  @apply shadow-lg;
  box-shadow: 
    0 0 20px rgba(139, 92, 246, 0.3),
    0 0 40px rgba(139, 92, 246, 0.1),
    0 0 80px rgba(139, 92, 246, 0.05);
  @apply bg-gradient-to-r from-accent-600/20 to-purple-600/20;
  @apply border-accent-400/50;
}

/* Shimmer Effect for Hover */
.community-tab::before {
  @apply absolute inset-0 bg-gradient-to-r from-accent-500/0 via-accent-500/20 to-accent-500/0;
  @apply translate-x-[-100%] transition-transform duration-500;
  content: '';
  z-index: 1;
}

.community-tab:hover::before {
  @apply translate-x-[100%];
}

/* Enhanced Active Tab Styling */
.community-tab.active {
  @apply relative;
  @apply bg-gradient-to-r from-accent-600/30 to-purple-600/30;
  @apply border-accent-400/70;
  @apply shadow-lg shadow-accent-500/20;
}

.community-tab.active::after {
  @apply absolute bottom-0 left-0 right-0 h-0.5;
  @apply bg-gradient-to-r from-accent-400 to-purple-400;
  @apply shadow-lg shadow-accent-500/50;
  content: '';
  z-index: 2;
}

/* Icon-Specific Animations */
.tab-icon {
  @apply transition-all duration-300 ease-out;
  @apply relative z-10;
}

/* Discover - Rocket Launch */
.tab-icon.discover:hover {
  animation: rocketLaunch 0.6s ease-out;
}

@keyframes rocketLaunch {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(-5deg); }
  100% { transform: translateY(0) rotate(0deg); }
}

/* Challenges - Target Pulse */
.tab-icon.challenges:hover {
  animation: targetPulse 0.8s ease-out;
}

@keyframes targetPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.tab-icon.challenges:hover::after {
  content: '';
  @apply absolute inset-0 border-2 border-accent-400/50 rounded-full;
  animation: expandingRing 0.8s ease-out;
}

@keyframes expandingRing {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.5); opacity: 0; }
}

/* Contests - Medal Shine */
.tab-icon.contests:hover {
  animation: medalShine 0.7s ease-out;
}

@keyframes medalShine {
  0% { filter: brightness(1); }
  50% { filter: brightness(1.3) drop-shadow(0 0 8px rgba(255, 215, 0, 0.6)); }
  100% { filter: brightness(1); }
}

/* Rankings - Trophy Glow */
.tab-icon.rankings:hover {
  animation: trophyGlow 0.6s ease-out;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));
}

@keyframes trophyGlow {
  0%, 100% { filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3)); }
  50% { filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.7)); }
}

/* Submissions - Brush Stroke */
.tab-icon.submissions:hover {
  animation: brushStroke 0.5s ease-out;
}

@keyframes brushStroke {
  0% { transform: translateX(0) rotate(0deg); }
  25% { transform: translateX(-2px) rotate(-3deg); }
  75% { transform: translateX(2px) rotate(3deg); }
  100% { transform: translateX(0) rotate(0deg); }
}

/* Discussions - Speech Bubble Bounce */
.tab-icon.discussions:hover {
  animation: speechBubbleBounce 0.6s ease-out;
}

@keyframes speechBubbleBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

/* Ideas - Lightbulb Flicker */
.tab-icon.ideas:hover {
  animation: lightbulbFlicker 0.8s ease-out;
}

@keyframes lightbulbFlicker {
  0%, 100% { filter: brightness(1); }
  25% { filter: brightness(1.4) drop-shadow(0 0 6px rgba(255, 255, 0, 0.6)); }
  50% { filter: brightness(0.8); }
  75% { filter: brightness(1.2) drop-shadow(0 0 4px rgba(255, 255, 0, 0.4)); }
}

/* Activity - Electric Spark */
.tab-icon.activity:hover {
  animation: electricSpark 0.5s ease-out;
}

@keyframes electricSpark {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.5) drop-shadow(0 0 8px rgba(255, 69, 0, 0.8)); }
}

/* Enhanced Badge Styling */
.tab-badge {
  @apply transition-all duration-200;
  @apply hover:scale-110 hover:bg-accent-500 hover:shadow-accent-500/50;
  @apply hover:animate-pulse;
  @apply relative z-10;
}

/* Ripple Effect for Tab Clicks */
.tab-ripple {
  @apply relative overflow-hidden;
}

.tab-ripple::after {
  @apply absolute inset-0 bg-accent-400/30 rounded-full;
  @apply scale-0 opacity-0 transition-all duration-300;
  content: '';
  pointer-events: none;
}

.tab-ripple:active::after {
  @apply scale-150 opacity-100;
}

/* Loading Shimmer Overlay */
.loading-shimmer {
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/10 to-transparent;
  animation: shimmer 1.5s infinite;
  z-index: 50;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Mobile Enhancements */
.mobile-tabs {
  @apply flex overflow-x-auto snap-x snap-mandatory;
  @apply scrollbar-hide pb-2;
  scroll-padding: 1rem;
}

.mobile-tab {
  @apply snap-center flex-shrink-0;
  @apply min-w-[120px] touch-manipulation;
  @apply transition-all duration-200;
}

.mobile-tab:hover {
  @apply bg-gray-800/50;
}

.mobile-tab.active {
  @apply bg-gradient-to-b from-accent-600/20 to-purple-600/20;
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .community-tab,
  .tab-icon,
  .tab-badge {
    animation: none !important;
    transition: none !important;
  }
  
  .community-tab:hover {
    transform: none !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .community-tab.active {
    @apply border-2 border-accent-400;
  }
  
  .community-tab:hover {
    @apply border border-accent-300;
  }
}

/* Dark Mode Optimizations */
@media (prefers-color-scheme: dark) {
  :root {
    --syndicaps-glow-intensity: 0.4;
  }
}

/* Utility Classes */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Shimmer Animation Keyframes */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}
