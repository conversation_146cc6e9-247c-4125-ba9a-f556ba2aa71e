/**
 * Theme CSS
 * 
 * Global CSS for the theme system, including base styles,
 * theme transitions, and accessibility enhancements.
 * 
 * <AUTHOR> Team - Phase 2 Feature Completion
 * @version 2.0.0
 */

/* ===== CSS CUSTOM PROPERTIES ===== */

:root {
  /* Color scheme */
  color-scheme: light;
  
  /* Base colors that get overridden by theme provider */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-transparent: transparent;
  
  /* Typography */
  --font-sans: Inter, system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', Menlo, Monaco, monospace;
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-full: 9999px;
}

/* Dark mode color scheme */
.theme-dark {
  color-scheme: dark;
}

/* ===== BASE STYLES ===== */

* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Disable transitions for reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.motion-reduce * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* ===== THEME TRANSITIONS ===== */

html {
  transition: background-color var(--transition-normal);
}

body {
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Smooth theme transitions for all themed elements */
[data-theme],
[class*="bg-"],
[class*="text-"],
[class*="border-"] {
  transition: 
    background-color var(--transition-normal),
    color var(--transition-normal),
    border-color var(--transition-normal),
    opacity var(--transition-normal);
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* High contrast mode adjustments */
.theme-high-contrast {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.theme-high-contrast * {
  border-width: 2px !important;
}

.theme-high-contrast button,
.theme-high-contrast a,
.theme-high-contrast input,
.theme-high-contrast textarea,
.theme-high-contrast select {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.theme-high-contrast button:focus,
.theme-high-contrast a:focus,
.theme-high-contrast input:focus,
.theme-high-contrast textarea:focus,
.theme-high-contrast select:focus {
  outline: 3px solid var(--color-accent-500);
  outline-offset: 2px;
}

/* Colorblind friendly adjustments */
.theme-colorblind-friendly {
  /* Enhanced border styles for better shape recognition */
  --border-style-primary: 2px solid;
  --border-style-secondary: 2px dashed;
  --border-style-accent: 2px dotted;
}

/* ===== SKIP LINKS ===== */

.skip-links {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  padding: 8px 16px;
  background: var(--color-components-background-primary);
  color: var(--color-components-text-primary);
  border: 2px solid var(--color-components-border-focus);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 500;
  transition: top var(--transition-fast);
  box-shadow: var(--shadow-lg);
}

.skip-link:focus {
  top: 8px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Screen reader only when focused */
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* ===== GAMIFICATION SPECIFIC STYLES ===== */

/* Achievement unlock animation */
@keyframes achievement-unlock {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1) rotate(5deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.achievement-unlock {
  animation: achievement-unlock 0.6s ease-out;
}

/* Points change animation */
@keyframes points-change {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px) scale(1);
    opacity: 0;
  }
}

.points-animation {
  animation: points-change 1s ease-out forwards;
}

/* Progress bar animation */
@keyframes progress-fill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width, 0%);
  }
}

.progress-animated {
  animation: progress-fill 1s ease-out;
}

/* Tier promotion glow effect */
@keyframes tier-promotion-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--tier-color, var(--color-accent-500));
  }
  50% {
    box-shadow: 0 0 20px var(--tier-color, var(--color-accent-500)), 
                0 0 30px var(--tier-color, var(--color-accent-500));
  }
}

.tier-promotion-glow {
  animation: tier-promotion-glow 2s ease-in-out infinite;
}

/* Challenge completion celebration */
@keyframes challenge-complete {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05) rotate(1deg);
  }
  50% {
    transform: scale(1.1) rotate(-1deg);
  }
  75% {
    transform: scale(1.05) rotate(1deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

.challenge-complete {
  animation: challenge-complete 0.8s ease-in-out;
}

/* ===== INTERACTIVE STATES ===== */

/* Enhanced focus states */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--color-components-border-focus);
  outline-offset: 2px;
  border-radius: var(--radius-md);
}

/* Hover states with reduced motion respect */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  button:hover,
  a:hover,
  [role="button"]:hover {
    transform: translateY(-1px);
  }
}

/* Active states */
button:active,
a:active,
[role="button"]:active {
  transform: translateY(0);
}

/* Disabled states */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled,
[aria-disabled="true"] {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== LOADING STATES ===== */

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Loading skeleton */
@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.loading-skeleton {
  animation: skeleton-loading 1.5s ease-in-out infinite;
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-300) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
}

.theme-dark .loading-skeleton {
  background: linear-gradient(
    90deg,
    var(--color-gray-700) 25%,
    var(--color-gray-600) 50%,
    var(--color-gray-700) 75%
  );
  background-size: 200% 100%;
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Container with max width and center alignment */
.container-responsive {
  width: 100%;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  * {
    animation: none !important;
    transition: none !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  a {
    text-decoration: underline;
  }
  
  a[href^="http"]:after {
    content: " (" attr(href) ")";
  }
}