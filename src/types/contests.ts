/**
 * Contest System Type Definitions
 * 
 * TypeScript interfaces for the Domestika-inspired contest system
 * including contests, submissions, voting, judging, and prizes.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Timestamp } from 'firebase/firestore'
import { BaseEntity, UserReference } from './community'

// ===== CONTEST CORE TYPES =====

/**
 * Contest category enumeration
 */
export type ContestCategory = 
  | 'keycap_design'
  | 'photography' 
  | 'build_showcase'
  | 'modifications'
  | 'setup_aesthetics'
  | 'community_story'

/**
 * Contest status lifecycle
 */
export type ContestStatus = 
  | 'upcoming'    // Contest announced but not started
  | 'active'      // Accepting submissions
  | 'voting'      // Submission period ended, voting in progress
  | 'completed'   // Voting ended, results announced
  | 'cancelled'   // Contest cancelled

/**
 * User tier requirements for contest participation
 */
export type UserTier = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'

/**
 * Contest submission status
 */
export type ContestSubmissionStatus = 
  | 'draft'         // User is still working on submission
  | 'submitted'     // Submitted and awaiting review
  | 'under_review'  // Being reviewed by moderators
  | 'approved'      // Approved for voting
  | 'rejected'      // Rejected by moderators
  | 'disqualified'  // Disqualified after approval

/**
 * Vote type enumeration
 */
export type ContestVoteType = 'community' | 'expert'

/**
 * Judge role enumeration
 */
export type ContestJudgeRole = 'lead_judge' | 'expert_judge' | 'community_judge'

/**
 * Judge status enumeration
 */
export type ContestJudgeStatus = 'invited' | 'accepted' | 'declined' | 'active' | 'completed'

// ===== CONTEST INTERFACES =====

/**
 * Contest requirements and restrictions
 */
export interface ContestRequirements {
  /** Minimum user tier to participate */
  minTier?: UserTier
  /** Maximum submissions per user */
  maxSubmissions: number
  /** Whether team submissions are allowed */
  allowTeams: boolean
  /** Allowed file types for submissions */
  fileTypes: string[]
  /** Maximum file size in bytes */
  maxFileSize: number
  /** Minimum image dimensions if applicable */
  minImageDimensions?: {
    width: number
    height: number
  }
}

/**
 * Contest prize structure
 */
export interface ContestPrize {
  /** Points awarded */
  points: number
  /** Physical prize description */
  physicalPrize?: string
  /** Store credit amount */
  storeCredit?: number
  /** Prize image URL */
  image?: string
}

/**
 * Contest prizes for different placements
 */
export interface ContestPrizes {
  /** First place prize */
  first: ContestPrize
  /** Second place prize */
  second: ContestPrize
  /** Third place prize */
  third: ContestPrize
  /** Participation prize for all valid submissions */
  participation: ContestPrize
  /** Community choice award (highest community votes) */
  communityChoice?: ContestPrize
}

/**
 * Judging criteria with weights
 */
export interface ContestJudgingCriteria {
  /** Creativity weight percentage (0-100) */
  creativity: number
  /** Technical execution weight percentage (0-100) */
  technical: number
  /** Originality weight percentage (0-100) */
  originality: number
  /** Theme adherence weight percentage (0-100) */
  themeAdherence: number
}

/**
 * Contest statistics
 */
export interface ContestStats {
  /** Total number of submissions */
  submissions: number
  /** Total number of participants */
  participants: number
  /** Total votes cast */
  totalVotes: number
  /** Community votes count */
  communityVotes: number
  /** Expert votes count */
  expertVotes: number
  /** Average submission rating */
  averageRating?: number
}

/**
 * Main Contest interface
 */
export interface Contest extends BaseEntity {
  /** Contest title */
  title: string
  /** Detailed contest description */
  description: string
  /** Contest theme or prompt */
  theme: string
  /** Contest category */
  category: ContestCategory
  /** Current contest status */
  status: ContestStatus
  
  // Timing
  /** When submissions open */
  submissionStart: Timestamp
  /** When submissions close */
  submissionEnd: Timestamp
  /** When voting opens */
  votingStart: Timestamp
  /** When voting closes */
  votingEnd: Timestamp
  /** When results are announced */
  resultsDate: Timestamp
  
  // Rules & Requirements
  /** Contest rules and guidelines */
  rules: string[]
  /** Participation requirements */
  requirements: ContestRequirements
  
  // Prizes & Judging
  /** Prize structure */
  prizes: ContestPrizes
  /** Judging criteria and weights */
  judgingCriteria: ContestJudgingCriteria
  
  // Statistics
  /** Contest statistics */
  stats: ContestStats
  
  // Metadata
  /** Contest banner image URL */
  bannerImage?: string
  /** Contest tags for categorization */
  tags: string[]
  /** Whether this contest is featured */
  featured: boolean
  /** User who created the contest */
  createdBy: UserReference
}

// ===== SUBMISSION INTERFACES =====

/**
 * Team member information for team submissions
 */
export interface ContestTeamMember {
  /** User ID */
  userId: string
  /** User's role in the team */
  role: string
  /** When they joined the team */
  joinedAt: Timestamp
}

/**
 * Team submission details
 */
export interface ContestTeam {
  /** Team members */
  members: ContestTeamMember[]
  /** Team roles mapping */
  roles: { [userId: string]: string }
  /** Team name */
  name?: string
}

/**
 * Submission scoring breakdown
 */
export interface ContestSubmissionScores {
  /** Community voting scores */
  community: {
    /** Average rating */
    average: number
    /** Number of votes */
    count: number
    /** Total points */
    total: number
  }
  /** Expert jury scores */
  expert: {
    /** Average rating */
    average: number
    /** Number of expert votes */
    count: number
    /** Total points */
    total: number
    /** Detailed breakdown by criteria */
    breakdown: {
      creativity: number
      technical: number
      originality: number
      themeAdherence: number
    }
  }
  /** Final weighted score */
  final: number
}

/**
 * Contest submission interface
 */
export interface ContestSubmission extends BaseEntity {
  /** Associated contest ID */
  contestId: string
  /** Submitting user ID */
  userId: string
  
  // Content
  /** Submission title */
  title: string
  /** Submission description */
  description: string
  /** Submission image URLs */
  images: string[]
  /** Optimized thumbnail URLs */
  thumbnails: string[]
  
  // Metadata
  /** Submission category */
  category: string
  /** Submission tags */
  tags: string[]
  /** When submission was made */
  submittedAt: Timestamp
  /** Last modification time */
  lastModified: Timestamp
  
  // Status
  /** Current submission status */
  status: ContestSubmissionStatus
  /** Moderation notes if rejected */
  moderationNotes?: string
  
  // Engagement
  /** View count */
  views: number
  /** Like count */
  likes: number
  /** Comment count */
  comments: number
  
  // Scoring
  /** Submission scores */
  scores: ContestSubmissionScores
  
  // Team submission (if applicable)
  /** Team details for team submissions */
  team?: ContestTeam
}

// ===== VOTING INTERFACES =====

/**
 * Expert judge detailed scoring
 */
export interface ContestExpertScores {
  /** Creativity score (1-10) */
  creativity: number
  /** Technical execution score (1-10) */
  technical: number
  /** Originality score (1-10) */
  originality: number
  /** Theme adherence score (1-10) */
  themeAdherence: number
  /** Judge's notes */
  notes: string
}

/**
 * Contest vote interface
 */
export interface ContestVote extends BaseEntity {
  /** Associated contest ID */
  contestId: string
  /** Submission being voted on */
  submissionId: string
  /** User casting the vote */
  voterId: string

  // Vote details
  /** Type of vote */
  type: ContestVoteType
  /** Rating (1-5 stars for community, calculated for experts) */
  rating: number
  /** Optional comment */
  comment?: string

  // Expert-specific scoring
  /** Detailed expert scores */
  expertScores?: ContestExpertScores

  // Metadata
  /** When vote was cast */
  votedAt: Timestamp
  /** Last modification time */
  lastModified: Timestamp

  // Validation
  /** Whether vote has been verified */
  verified: boolean
  /** IP address for anti-gaming (hashed) */
  ipAddress?: string
}

// ===== JUDGE INTERFACES =====

/**
 * Contest judge interface
 */
export interface ContestJudge extends BaseEntity {
  /** Associated contest ID */
  contestId: string
  /** Judge user ID */
  userId: string

  // Judge details
  /** Judge role */
  role: ContestJudgeRole
  /** Areas of expertise */
  expertise: string[]
  /** Judge bio/credentials */
  bio: string

  // Assignment
  /** When judge was assigned */
  assignedAt: Timestamp
  /** Who assigned the judge */
  assignedBy: string
  /** Current judge status */
  status: ContestJudgeStatus

  // Progress
  /** Number of submissions to review */
  submissionsToReview: number
  /** Number of submissions reviewed */
  submissionsReviewed: number
  /** Average score given */
  averageScore: number

  // Metadata
  /** Additional notes */
  notes?: string
  /** When judging was completed */
  completedAt?: Timestamp
}

// ===== FILTER & QUERY INTERFACES =====

/**
 * Contest filtering options
 */
export interface ContestFilters {
  /** Filter by status */
  status?: ContestStatus[]
  /** Filter by category */
  category?: ContestCategory[]
  /** Filter by featured status */
  featured?: boolean
  /** Result limit */
  limit?: number
  /** Result offset for pagination */
  offset?: number
  /** Sort field */
  sortBy?: 'submissionStart' | 'votingStart' | 'featured' | 'submissions' | 'createdAt'
  /** Sort order */
  sortOrder?: 'asc' | 'desc'
}

/**
 * Contest submission filtering options
 */
export interface ContestSubmissionFilters {
  /** Filter by status */
  status?: ContestSubmissionStatus[]
  /** Filter by user */
  userId?: string
  /** Filter by contest */
  contestId?: string
  /** Result limit */
  limit?: number
  /** Result offset for pagination */
  offset?: number
  /** Sort field */
  sortBy?: 'submittedAt' | 'scores.final' | 'views' | 'likes' | 'createdAt'
  /** Sort order */
  sortOrder?: 'asc' | 'desc'
}

/**
 * Contest vote filtering options
 */
export interface ContestVoteFilters {
  /** Filter by contest */
  contestId?: string
  /** Filter by submission */
  submissionId?: string
  /** Filter by voter */
  voterId?: string
  /** Filter by vote type */
  type?: ContestVoteType
  /** Result limit */
  limit?: number
  /** Result offset for pagination */
  offset?: number
  /** Sort field */
  sortBy?: 'votedAt' | 'rating' | 'createdAt'
  /** Sort order */
  sortOrder?: 'asc' | 'desc'
}

// ===== ANALYTICS & STATISTICS INTERFACES =====

/**
 * Contest leaderboard entry
 */
export interface ContestLeaderboardEntry {
  /** Submission ID */
  submissionId: string
  /** Submission details */
  submission: ContestSubmission
  /** Current rank */
  rank: number
  /** Final score */
  score: number
  /** Community score */
  communityScore: number
  /** Expert score */
  expertScore: number
  /** Vote count */
  voteCount: number
}

/**
 * Contest leaderboard
 */
export interface ContestLeaderboard {
  /** Contest ID */
  contestId: string
  /** Leaderboard entries */
  entries: ContestLeaderboardEntry[]
  /** Last updated timestamp */
  lastUpdated: Timestamp
}

/**
 * Voting progress statistics
 */
export interface ContestVotingProgress {
  /** Contest ID */
  contestId: string
  /** Total submissions */
  totalSubmissions: number
  /** Submissions with votes */
  submissionsWithVotes: number
  /** Total community votes */
  totalCommunityVotes: number
  /** Total expert votes */
  totalExpertVotes: number
  /** Average votes per submission */
  averageVotesPerSubmission: number
  /** Voting completion percentage */
  completionPercentage: number
  /** Last updated timestamp */
  lastUpdated: Timestamp
}

// ===== API RESPONSE INTERFACES =====

/**
 * Paginated response wrapper
 */
export interface ContestPaginatedResponse<T> {
  /** Response data */
  data: T[]
  /** Total count */
  total: number
  /** Current page */
  page: number
  /** Items per page */
  limit: number
  /** Whether more pages exist */
  hasMore: boolean
}

/**
 * Contest creation input
 */
export type ContestCreateInput = Omit<Contest, 'id' | 'createdAt' | 'updatedAt' | 'stats'>

/**
 * Contest update input
 */
export type ContestUpdateInput = Partial<Omit<Contest, 'id' | 'createdAt' | 'updatedAt'>>

/**
 * Contest submission creation input
 */
export type ContestSubmissionCreateInput = Omit<ContestSubmission, 'id' | 'createdAt' | 'updatedAt' | 'submittedAt' | 'scores' | 'views' | 'likes' | 'comments'>

/**
 * Contest vote creation input
 */
export type ContestVoteCreateInput = Omit<ContestVote, 'id' | 'createdAt' | 'updatedAt' | 'votedAt' | 'verified'>

// ===== ERROR INTERFACES =====

/**
 * Contest-specific error
 */
export interface ContestError {
  /** Error code */
  code: string
  /** Error message */
  message: string
  /** Additional error details */
  details?: any
  /** Error timestamp */
  timestamp: Timestamp
}

// ===== NOTIFICATION INTERFACES =====

/**
 * Contest notification types
 */
export type ContestNotificationType =
  | 'contest_announced'
  | 'submission_deadline_reminder'
  | 'voting_started'
  | 'voting_deadline_reminder'
  | 'results_announced'
  | 'winner_notification'
  | 'submission_approved'
  | 'submission_rejected'
  | 'judge_assigned'
  | 'new_submission'

/**
 * Contest notification
 */
export interface ContestNotification extends BaseEntity {
  /** Notification type */
  type: ContestNotificationType
  /** Target user ID */
  userId: string
  /** Related contest ID */
  contestId?: string
  /** Related submission ID */
  submissionId?: string
  /** Notification title */
  title: string
  /** Notification message */
  message: string
  /** Whether notification has been read */
  read: boolean
  /** Additional metadata */
  metadata?: Record<string, any>
}
