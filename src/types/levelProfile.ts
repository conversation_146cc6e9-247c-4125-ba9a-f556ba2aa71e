/**
 * Level Profile Types
 * 
 * Type extensions for integrating level system data with existing user profiles.
 * Provides seamless integration between level progression and user profile data.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Timestamp } from 'firebase/firestore'
import { UserProfile } from './profile'
import { LevelTier } from '@/lib/levelSystem'

// ===== LEVEL PROFILE EXTENSIONS =====

/**
 * Level data to be integrated into existing UserProfile
 */
export interface ProfileLevelData {
  level: {
    current: number
    currentXP: number
    totalXP: number
    levelName: string
    tier: LevelTier
    nextLevelXP: number
    progressToNext: number // percentage 0-100
    lastLevelUp?: Timestamp
    unclaimedRewards: string[] // reward IDs that can be claimed
    milestoneRewards: string[] // reward IDs that have been claimed
    levelUpNotificationSeen: boolean
  }
}

/**
 * Extended UserProfile with level integration
 */
export interface UserProfileWithLevel extends UserProfile, ProfileLevelData {}

/**
 * Level statistics for profile display
 */
export interface LevelStats {
  currentLevel: number
  levelName: string
  tier: LevelTier
  totalXP: number
  xpToNextLevel: number
  progressPercentage: number
  levelUpDate?: Date
  recentLevelUps: LevelUpHistory[]
  xpEarnedToday: number
  xpEarnedThisWeek: number
  xpEarnedThisMonth: number
}

/**
 * Level up history for tracking progression
 */
export interface LevelUpHistory {
  level: number
  levelName: string
  achievedAt: Date
  xpRequired: number
  rewardsEarned: string[]
}

/**
 * Level display preferences
 */
export interface LevelDisplayPreferences {
  showLevelInProfile: boolean
  showXPProgress: boolean
  showLevelBadge: boolean
  showRecentAchievements: boolean
  levelNotifications: boolean
  xpGainAnimations: boolean
}

/**
 * Level-based feature access
 */
export interface LevelFeatureAccess {
  communityPosting: boolean
  challengeCreation: boolean
  votingWeightMultiplier: number
  mentorshipProgram: boolean
  betaTesting: boolean
  communityModeration: boolean
  exclusiveDiscord: boolean
  designContestJudging: boolean
  productFeedbackPanel: boolean
  ambassadorProgram: boolean
  customKeycapDesign: boolean
  bulkOrderDiscounts: boolean
  lifetimeVIP: boolean
}

/**
 * Level progression analytics
 */
export interface LevelProgressionAnalytics {
  userId: string
  averageXPPerDay: number
  averageXPPerWeek: number
  averageXPPerMonth: number
  levelUpVelocity: number // levels per month
  xpSources: {
    purchase: number
    activity: number
    bonus: number
    event: number
  }
  engagementScore: number
  progressionTrend: 'increasing' | 'stable' | 'decreasing'
  predictedNextLevelDate?: Date
  calculatedAt: Date
}

// ===== UTILITY TYPES =====

/**
 * Level comparison data for leaderboards
 */
export interface LevelComparison {
  userId: string
  displayName: string
  avatar?: string
  level: number
  levelName: string
  tier: LevelTier
  totalXP: number
  rank: number
  isCurrentUser: boolean
}

/**
 * Level milestone data
 */
export interface LevelMilestone {
  level: number
  name: string
  tier: LevelTier
  xpRequired: number
  rewards: {
    badges: string[]
    points: number
    discounts: number[]
    exclusiveItems: string[]
    featureAccess: string[]
    titles: string[]
  }
  isAchieved: boolean
  achievedAt?: Date
}

/**
 * XP earning summary
 */
export interface XPEarningSummary {
  period: 'today' | 'week' | 'month' | 'all-time'
  totalXP: number
  breakdown: {
    purchases: number
    activities: number
    bonuses: number
    events: number
  }
  topSources: Array<{
    source: string
    amount: number
    percentage: number
  }>
  averagePerDay: number
  trend: 'up' | 'down' | 'stable'
}

// ===== PROFILE INTEGRATION HELPERS =====

/**
 * Helper type for profile updates that include level data
 */
export type ProfileUpdateWithLevel = Partial<UserProfile> & Partial<ProfileLevelData>

/**
 * Helper type for profile queries that include level data
 */
export interface ProfileQueryOptions {
  includeLevelData?: boolean
  includeXPHistory?: boolean
  includeRewards?: boolean
  includeFeatureAccess?: boolean
}

/**
 * Complete profile data with all level integrations
 */
export interface CompleteUserProfile extends UserProfileWithLevel {
  levelStats: LevelStats
  featureAccess: LevelFeatureAccess
  progressionAnalytics: LevelProgressionAnalytics
  displayPreferences: LevelDisplayPreferences
}

// ===== MIGRATION HELPERS =====

/**
 * Migration data for existing users
 */
export interface LevelMigrationData {
  userId: string
  currentPoints: number
  estimatedLevel: number
  estimatedXP: number
  migrationDate: Date
  preservedData: {
    achievements: string[]
    tier: string
    joinDate: Date
  }
}

/**
 * Batch migration result
 */
export interface BatchMigrationResult {
  totalUsers: number
  successfulMigrations: number
  failedMigrations: number
  errors: Array<{
    userId: string
    error: string
  }>
  completedAt: Date
}

export default ProfileLevelData
