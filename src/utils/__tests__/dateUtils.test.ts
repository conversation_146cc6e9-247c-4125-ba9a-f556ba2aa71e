/**
 * Date Utilities Tests
 * 
 * Tests for date utility functions to ensure proper handling of both
 * JavaScript Date objects and Firestore Timestamp objects.
 * 
 * <AUTHOR> Team
 */

import {
  getTimestamp,
  toDate,
  getDateDifference,
  getAgeInDays,
  getAgeInHours,
  isWithinDays,
  isOlderThan,
  isNewerThan,
  formatDate,
  getRelativeTime,
  isValidDateLike,
  TIME_CONSTANTS
} from '../dateUtils'

// Mock Firestore Timestamp
class MockFirestoreTimestamp {
  constructor(private date: Date) {}
  
  toDate(): Date {
    return this.date
  }
}

describe('Date Utilities', () => {
  const testDate = new Date('2023-01-15T10:30:00Z')
  const testTimestamp = testDate.getTime()
  const mockFirestoreTimestamp = new MockFirestoreTimestamp(testDate)

  describe('getTimestamp', () => {
    it('handles JavaScript Date objects', () => {
      expect(getTimestamp(testDate)).toBe(testTimestamp)
    })

    it('handles Firestore Timestamp objects', () => {
      expect(getTimestamp(mockFirestoreTimestamp as any)).toBe(testTimestamp)
    })

    it('returns null for null/undefined', () => {
      expect(getTimestamp(null)).toBeNull()
      expect(getTimestamp(undefined)).toBeNull()
    })

    it('returns null for invalid objects', () => {
      expect(getTimestamp({} as any)).toBeNull()
      expect(getTimestamp('invalid' as any)).toBeNull()
    })
  })

  describe('toDate', () => {
    it('handles JavaScript Date objects', () => {
      const result = toDate(testDate)
      expect(result).toBeInstanceOf(Date)
      expect(result?.getTime()).toBe(testTimestamp)
    })

    it('handles Firestore Timestamp objects', () => {
      const result = toDate(mockFirestoreTimestamp as any)
      expect(result).toBeInstanceOf(Date)
      expect(result?.getTime()).toBe(testTimestamp)
    })

    it('returns null for null/undefined', () => {
      expect(toDate(null)).toBeNull()
      expect(toDate(undefined)).toBeNull()
    })
  })

  describe('getDateDifference', () => {
    const laterDate = new Date('2023-01-20T10:30:00Z')
    const laterTimestamp = laterDate.getTime()

    it('calculates difference between Date objects', () => {
      const difference = getDateDifference(laterDate, testDate)
      expect(difference).toBe(laterTimestamp - testTimestamp)
    })

    it('calculates difference between mixed types', () => {
      const difference = getDateDifference(laterDate, mockFirestoreTimestamp as any)
      expect(difference).toBe(laterTimestamp - testTimestamp)
    })

    it('returns null for invalid dates', () => {
      expect(getDateDifference(null, testDate)).toBeNull()
      expect(getDateDifference(testDate, null)).toBeNull()
    })
  })

  describe('getAgeInDays', () => {
    it('calculates age in days correctly', () => {
      const referenceDate = new Date('2023-01-20T10:30:00Z') // 5 days later
      const age = getAgeInDays(testDate, referenceDate)
      expect(age).toBe(5)
    })

    it('handles Firestore Timestamps', () => {
      const referenceDate = new Date('2023-01-20T10:30:00Z')
      const age = getAgeInDays(mockFirestoreTimestamp as any, referenceDate)
      expect(age).toBe(5)
    })

    it('returns null for invalid dates', () => {
      expect(getAgeInDays(null)).toBeNull()
    })
  })

  describe('isWithinDays', () => {
    it('correctly identifies dates within range (past)', () => {
      const threeDaysAgo = new Date(Date.now() - 3 * TIME_CONSTANTS.DAY)
      expect(isWithinDays(threeDaysAgo, 5)).toBe(true)
      expect(isWithinDays(threeDaysAgo, 2)).toBe(false)
    })

    it('correctly identifies dates within range (future)', () => {
      const threeDaysFromNow = new Date(Date.now() + 3 * TIME_CONSTANTS.DAY)
      expect(isWithinDays(threeDaysFromNow, 5, 'future')).toBe(true)
      expect(isWithinDays(threeDaysFromNow, 2, 'future')).toBe(false)
    })

    it('handles Firestore Timestamps', () => {
      const threeDaysAgo = new Date(Date.now() - 3 * TIME_CONSTANTS.DAY)
      const mockTimestamp = new MockFirestoreTimestamp(threeDaysAgo)
      expect(isWithinDays(mockTimestamp as any, 5)).toBe(true)
    })

    it('returns false for invalid dates', () => {
      expect(isWithinDays(null, 5)).toBe(false)
    })
  })

  describe('isOlderThan', () => {
    it('correctly identifies older dates', () => {
      const tenDaysAgo = new Date(Date.now() - 10 * TIME_CONSTANTS.DAY)
      expect(isOlderThan(tenDaysAgo, 5)).toBe(true)
      expect(isOlderThan(tenDaysAgo, 15)).toBe(false)
    })

    it('handles Firestore Timestamps', () => {
      const tenDaysAgo = new Date(Date.now() - 10 * TIME_CONSTANTS.DAY)
      const mockTimestamp = new MockFirestoreTimestamp(tenDaysAgo)
      expect(isOlderThan(mockTimestamp as any, 5)).toBe(true)
    })

    it('returns false for invalid dates', () => {
      expect(isOlderThan(null, 5)).toBe(false)
    })
  })

  describe('isNewerThan', () => {
    it('correctly identifies newer dates', () => {
      const twoDaysAgo = new Date(Date.now() - 2 * TIME_CONSTANTS.DAY)
      expect(isNewerThan(twoDaysAgo, 5)).toBe(true)
      expect(isNewerThan(twoDaysAgo, 1)).toBe(false)
    })

    it('handles Firestore Timestamps', () => {
      const twoDaysAgo = new Date(Date.now() - 2 * TIME_CONSTANTS.DAY)
      const mockTimestamp = new MockFirestoreTimestamp(twoDaysAgo)
      expect(isNewerThan(mockTimestamp as any, 5)).toBe(true)
    })
  })

  describe('formatDate', () => {
    it('formats Date objects correctly', () => {
      const formatted = formatDate(testDate)
      expect(formatted).toMatch(/Jan 15, 2023/)
    })

    it('formats Firestore Timestamps correctly', () => {
      const formatted = formatDate(mockFirestoreTimestamp as any)
      expect(formatted).toMatch(/Jan 15, 2023/)
    })

    it('returns "Invalid Date" for null/undefined', () => {
      expect(formatDate(null)).toBe('Invalid Date')
      expect(formatDate(undefined)).toBe('Invalid Date')
    })

    it('uses custom format options', () => {
      const formatted = formatDate(testDate, { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
      expect(formatted).toMatch(/January 15, 2023/)
    })
  })

  describe('getRelativeTime', () => {
    it('returns "just now" for very recent dates', () => {
      const now = new Date()
      const almostNow = new Date(now.getTime() - 30000) // 30 seconds ago
      expect(getRelativeTime(almostNow, now)).toBe('just now')
    })

    it('returns correct relative time for minutes', () => {
      const now = new Date()
      const fiveMinutesAgo = new Date(now.getTime() - 5 * TIME_CONSTANTS.MINUTE)
      expect(getRelativeTime(fiveMinutesAgo, now)).toBe('5 minutes ago')
    })

    it('returns correct relative time for hours', () => {
      const now = new Date()
      const twoHoursAgo = new Date(now.getTime() - 2 * TIME_CONSTANTS.HOUR)
      expect(getRelativeTime(twoHoursAgo, now)).toBe('2 hours ago')
    })

    it('returns correct relative time for days', () => {
      const now = new Date()
      const threeDaysAgo = new Date(now.getTime() - 3 * TIME_CONSTANTS.DAY)
      expect(getRelativeTime(threeDaysAgo, now)).toBe('3 days ago')
    })

    it('handles future dates', () => {
      const now = new Date()
      const inTwoHours = new Date(now.getTime() + 2 * TIME_CONSTANTS.HOUR)
      expect(getRelativeTime(inTwoHours, now)).toBe('2 hours from now')
    })

    it('handles Firestore Timestamps', () => {
      const now = new Date()
      const twoHoursAgo = new Date(now.getTime() - 2 * TIME_CONSTANTS.HOUR)
      const mockTimestamp = new MockFirestoreTimestamp(twoHoursAgo)
      expect(getRelativeTime(mockTimestamp as any, now)).toBe('2 hours ago')
    })

    it('returns "Invalid Date" for null/undefined', () => {
      expect(getRelativeTime(null)).toBe('Invalid Date')
    })
  })

  describe('isValidDateLike', () => {
    it('validates JavaScript Date objects', () => {
      expect(isValidDateLike(testDate)).toBe(true)
      expect(isValidDateLike(new Date('invalid'))).toBe(false)
    })

    it('validates Firestore Timestamp objects', () => {
      expect(isValidDateLike(mockFirestoreTimestamp)).toBe(true)
    })

    it('rejects invalid values', () => {
      expect(isValidDateLike(null)).toBe(false)
      expect(isValidDateLike(undefined)).toBe(false)
      expect(isValidDateLike({})).toBe(false)
      expect(isValidDateLike('string')).toBe(false)
      expect(isValidDateLike(123)).toBe(false)
    })
  })

  describe('TIME_CONSTANTS', () => {
    it('has correct time constants', () => {
      expect(TIME_CONSTANTS.MINUTE).toBe(60 * 1000)
      expect(TIME_CONSTANTS.HOUR).toBe(60 * 60 * 1000)
      expect(TIME_CONSTANTS.DAY).toBe(24 * 60 * 60 * 1000)
      expect(TIME_CONSTANTS.WEEK).toBe(7 * 24 * 60 * 60 * 1000)
      expect(TIME_CONSTANTS.MONTH).toBe(30 * 24 * 60 * 60 * 1000)
      expect(TIME_CONSTANTS.YEAR).toBe(365 * 24 * 60 * 60 * 1000)
    })
  })

  describe('SecurityScoreDashboard Integration', () => {
    it('handles password change date checking', () => {
      // Test the specific use case from SecurityScoreDashboard
      const recentPasswordChange = new Date(Date.now() - 30 * TIME_CONSTANTS.DAY)
      const oldPasswordChange = new Date(Date.now() - 120 * TIME_CONSTANTS.DAY)
      
      expect(isWithinDays(recentPasswordChange, 90)).toBe(true)
      expect(isWithinDays(oldPasswordChange, 90)).toBe(false)
    })

    it('handles login activity checking', () => {
      // Test the specific use case from SecurityScoreDashboard
      const recentLogin = new Date(Date.now() - 3 * TIME_CONSTANTS.DAY)
      const oldLogin = new Date(Date.now() - 10 * TIME_CONSTANTS.DAY)
      
      expect(isWithinDays(recentLogin, 7)).toBe(true)
      expect(isWithinDays(oldLogin, 7)).toBe(false)
    })

    it('handles account age checking', () => {
      // Test the specific use case from SecurityScoreDashboard
      const oldAccount = new Date(Date.now() - 60 * TIME_CONSTANTS.DAY)
      const newAccount = new Date(Date.now() - 15 * TIME_CONSTANTS.DAY)
      
      expect(isOlderThan(oldAccount, 30)).toBe(true)
      expect(isOlderThan(newAccount, 30)).toBe(false)
    })
  })
})
