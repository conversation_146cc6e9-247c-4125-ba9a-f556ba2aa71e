/**
 * Profile Privacy Utilities Tests
 * 
 * Tests to ensure that email addresses and phone numbers are NEVER
 * displayed in public mode, regardless of user privacy settings.
 * 
 * <AUTHOR> Team
 */

import { 
  filterProfileForViewMode, 
  isProfileVisible, 
  getViewModeForProfile,
  getContactVisibilityRules,
  sanitizeProfileForPublicDisplay,
  validateProfileDisplayPrivacy,
  type ViewMode 
} from '../profilePrivacy'
import { UserProfile } from '@/types/profile'

// Mock profile data for testing
const createMockProfile = (overrides: Partial<UserProfile> = {}): UserProfile => ({
  id: 'test-user-id',
  firebaseUid: 'firebase-uid',
  email: '<EMAIL>',
  phone: '+1234567890',
  displayName: 'Test User',
  bio: 'This is a test bio',
  location: 'Test City',
  website: 'https://example.com',
  socialLinks: {
    twitter: 'https://twitter.com/test',
    instagram: 'https://instagram.com/test'
  },
  achievements: ['achievement1', 'achievement2'],
  points: 1000,
  role: 'user',
  privacy: {
    profileVisibility: 'public',
    showEmail: true,
    showPhone: true,
    showLocation: true,
    showBio: true,
    showSocialLinks: true,
    showWebsite: true,
    showAchievements: true,
    showPoints: true
  },
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
})

describe('Profile Privacy Utilities', () => {
  describe('filterProfileForViewMode', () => {
    describe('Public Mode Privacy Protection', () => {
      it('NEVER shows email in public mode regardless of privacy settings', () => {
        // Test with showEmail: true
        const profileWithEmailEnabled = createMockProfile({
          privacy: {
            profileVisibility: 'public',
            showEmail: true, // User wants to show email
            showPhone: false,
            showLocation: true,
            showBio: true,
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: true,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(profileWithEmailEnabled, 'public')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.email).toBeUndefined()
        expect(filtered!.displayName).toBe('Test User') // Other data should remain
      })

      it('NEVER shows phone in public mode regardless of privacy settings', () => {
        // Test with showPhone: true
        const profileWithPhoneEnabled = createMockProfile({
          privacy: {
            profileVisibility: 'public',
            showEmail: false,
            showPhone: true, // User wants to show phone
            showLocation: true,
            showBio: true,
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: true,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(profileWithPhoneEnabled, 'public')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.phone).toBeUndefined()
        expect(filtered!.displayName).toBe('Test User') // Other data should remain
      })

      it('hides both email and phone in public mode even when both are enabled', () => {
        const profileWithContactEnabled = createMockProfile({
          privacy: {
            profileVisibility: 'public',
            showEmail: true, // User wants to show email
            showPhone: true, // User wants to show phone
            showLocation: true,
            showBio: true,
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: true,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(profileWithContactEnabled, 'public')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.email).toBeUndefined()
        expect(filtered!.phone).toBeUndefined()
        expect(filtered!.bio).toBe('This is a test bio') // Other data should remain
      })

      it('respects other privacy settings in public mode', () => {
        const profileWithMixedSettings = createMockProfile({
          privacy: {
            profileVisibility: 'public',
            showEmail: true,
            showPhone: true,
            showLocation: false, // User doesn't want to show location
            showBio: false, // User doesn't want to show bio
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: false,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(profileWithMixedSettings, 'public')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.email).toBeUndefined() // Always hidden in public
        expect(filtered!.phone).toBeUndefined() // Always hidden in public
        expect(filtered!.location).toBeUndefined() // Hidden per user setting
        expect(filtered!.bio).toBeUndefined() // Hidden per user setting
        expect(filtered!.socialLinks).toBeDefined() // Shown per user setting
        expect(filtered!.website).toBeDefined() // Shown per user setting
        expect(filtered!.achievements).toBeUndefined() // Hidden per user setting
        expect(filtered!.points).toBeDefined() // Shown per user setting
      })

      it('returns null if profile is not public', () => {
        const privateProfile = createMockProfile({
          privacy: {
            profileVisibility: 'private',
            showEmail: true,
            showPhone: true,
            showLocation: true,
            showBio: true,
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: true,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(privateProfile, 'public')
        expect(filtered).toBeNull()
      })
    })

    describe('Friends Mode', () => {
      it('respects email privacy setting in friends mode', () => {
        const profileHidingEmail = createMockProfile({
          privacy: {
            profileVisibility: 'public',
            showEmail: false, // User doesn't want friends to see email
            showPhone: true,
            showLocation: true,
            showBio: true,
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: true,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(profileHidingEmail, 'friends')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.email).toBeUndefined()
        expect(filtered!.phone).toBe('+1234567890') // Should be visible
      })

      it('respects phone privacy setting in friends mode', () => {
        const profileHidingPhone = createMockProfile({
          privacy: {
            profileVisibility: 'public',
            showEmail: true,
            showPhone: false, // User doesn't want friends to see phone
            showLocation: true,
            showBio: true,
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: true,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(profileHidingPhone, 'friends')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.email).toBe('<EMAIL>') // Should be visible
        expect(filtered!.phone).toBeUndefined()
      })

      it('returns null if profile is private', () => {
        const privateProfile = createMockProfile({
          privacy: {
            profileVisibility: 'private',
            showEmail: true,
            showPhone: true,
            showLocation: true,
            showBio: true,
            showSocialLinks: true,
            showWebsite: true,
            showAchievements: true,
            showPoints: true
          }
        })

        const filtered = filterProfileForViewMode(privateProfile, 'friends')
        expect(filtered).toBeNull()
      })
    })

    describe('Private Mode', () => {
      it('shows all information in private mode', () => {
        const profile = createMockProfile()
        const filtered = filterProfileForViewMode(profile, 'private')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.email).toBe('<EMAIL>')
        expect(filtered!.phone).toBe('+1234567890')
        expect(filtered!.bio).toBe('This is a test bio')
        expect(filtered!.location).toBe('Test City')
      })
    })

    describe('No Privacy Settings', () => {
      it('hides contact info in public mode when no privacy settings exist', () => {
        const profileWithoutPrivacy = createMockProfile({ privacy: undefined })
        const filtered = filterProfileForViewMode(profileWithoutPrivacy, 'public')
        
        expect(filtered).not.toBeNull()
        expect(filtered!.email).toBeUndefined()
        expect(filtered!.phone).toBeUndefined()
        expect(filtered!.displayName).toBe('Test User') // Other data should remain
      })
    })
  })

  describe('getContactVisibilityRules', () => {
    it('always hides contact info in public mode', () => {
      const rules = getContactVisibilityRules('public')
      expect(rules.hideEmail).toBe(true)
      expect(rules.hidePhone).toBe(true)
      expect(rules.reason).toContain('never shown in public view')
    })

    it('respects settings in friends mode', () => {
      const rules = getContactVisibilityRules('friends')
      expect(rules.hideEmail).toBe(false)
      expect(rules.hidePhone).toBe(false)
      expect(rules.reason).toContain('depends on user privacy settings')
    })

    it('shows all in private mode', () => {
      const rules = getContactVisibilityRules('private')
      expect(rules.hideEmail).toBe(false)
      expect(rules.hidePhone).toBe(false)
      expect(rules.reason).toContain('see all their own')
    })
  })

  describe('sanitizeProfileForPublicDisplay', () => {
    it('removes all sensitive information', () => {
      const profile = createMockProfile({
        firebaseUid: 'firebase-uid',
        stripeCustomerId: 'stripe-customer-id',
        trustedDevices: ['device1', 'device2'],
        mfaEnabled: true,
        lastPasswordChange: new Date(),
        loginHistory: [{ date: new Date(), ip: '127.0.0.1' }]
      })

      const sanitized = sanitizeProfileForPublicDisplay(profile)
      
      expect(sanitized).not.toBeNull()
      expect(sanitized!.email).toBeUndefined()
      expect(sanitized!.phone).toBeUndefined()
      expect(sanitized!.firebaseUid).toBeUndefined()
      expect(sanitized!.stripeCustomerId).toBeUndefined()
      expect(sanitized!.trustedDevices).toBeUndefined()
      expect(sanitized!.mfaEnabled).toBeUndefined()
      expect(sanitized!.lastPasswordChange).toBeUndefined()
      expect(sanitized!.loginHistory).toBeUndefined()
      
      // Non-sensitive data should remain
      expect(sanitized!.displayName).toBe('Test User')
      expect(sanitized!.bio).toBe('This is a test bio')
    })
  })

  describe('validateProfileDisplayPrivacy', () => {
    it('detects email violation in public mode', () => {
      const displayedData = {
        displayName: 'Test User',
        email: '<EMAIL>' // This should not be displayed
      }

      const result = validateProfileDisplayPrivacy(displayedData, 'public')
      
      expect(result.isValid).toBe(false)
      expect(result.violations).toContain('Email address should never be displayed in public view')
    })

    it('detects phone violation in public mode', () => {
      const displayedData = {
        displayName: 'Test User',
        phone: '+1234567890' // This should not be displayed
      }

      const result = validateProfileDisplayPrivacy(displayedData, 'public')
      
      expect(result.isValid).toBe(false)
      expect(result.violations).toContain('Phone number should never be displayed in public view')
    })

    it('passes validation for safe public data', () => {
      const displayedData = {
        displayName: 'Test User',
        bio: 'This is a test bio',
        location: 'Test City'
      }

      const result = validateProfileDisplayPrivacy(displayedData, 'public')
      
      expect(result.isValid).toBe(true)
      expect(result.violations).toHaveLength(0)
    })
  })
})
