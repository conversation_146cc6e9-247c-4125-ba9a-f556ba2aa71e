/**
 * Firestore Cache Clearing Utility
 * 
 * Utility functions to clear Firestore offline cache and force online mode
 * when experiencing "client is offline" errors.
 * 
 * <AUTHOR> Team
 */

import { 
  getFirestore, 
  enableNetwork, 
  disableNetwork, 
  clearIndexedDbPersistence,
  terminate,
  waitForPendingWrites
} from 'firebase/firestore'
import { db } from '@/lib/firebase-enhanced'

/**
 * Clear Firestore offline cache and force online mode
 */
export async function clearFirestoreCache(): Promise<boolean> {
  try {
    console.log('🧹 Clearing Firestore cache...')
    
    // Step 1: Wait for pending writes
    try {
      await waitForPendingWrites(db)
      console.log('✅ Pending writes completed')
    } catch (error) {
      console.warn('⚠️ Pending writes warning:', error)
    }
    
    // Step 2: Disable network
    try {
      await disableNetwork(db)
      console.log('✅ Network disabled')
    } catch (error) {
      console.warn('⚠️ Network disable warning:', error)
    }
    
    // Step 3: Clear IndexedDB persistence
    try {
      await clearIndexedDbPersistence(db)
      console.log('✅ IndexedDB persistence cleared')
    } catch (error) {
      console.warn('⚠️ IndexedDB clear warning:', error)
    }
    
    // Step 4: Re-enable network
    await enableNetwork(db)
    console.log('✅ Network re-enabled')
    
    console.log('🎉 Firestore cache cleared successfully')
    return true
    
  } catch (error) {
    console.error('❌ Failed to clear Firestore cache:', error)
    return false
  }
}

/**
 * Force Firestore online (without clearing cache)
 */
export async function forceFirestoreOnline(): Promise<boolean> {
  try {
    console.log('🔄 Forcing Firestore online...')
    
    // Disable then re-enable network to force reconnection
    try {
      await disableNetwork(db)
      await new Promise(resolve => setTimeout(resolve, 1000))
      await enableNetwork(db)
      console.log('✅ Firestore forced online')
      return true
    } catch (error) {
      console.error('❌ Failed to force Firestore online:', error)
      return false
    }
  } catch (error) {
    console.error('❌ Force online error:', error)
    return false
  }
}

/**
 * Clear browser storage related to Firebase
 */
export function clearBrowserFirebaseStorage(): void {
  try {
    console.log('🧹 Clearing browser Firebase storage...')
    
    // Clear localStorage items related to Firebase
    const firebaseKeys = Object.keys(localStorage).filter(key => 
      key.includes('firebase') || 
      key.includes('firestore') || 
      key.includes('auth')
    )
    
    firebaseKeys.forEach(key => {
      localStorage.removeItem(key)
      console.log(`🗑️ Removed localStorage: ${key}`)
    })
    
    // Clear sessionStorage items related to Firebase
    const sessionFirebaseKeys = Object.keys(sessionStorage).filter(key => 
      key.includes('firebase') || 
      key.includes('firestore') || 
      key.includes('auth')
    )
    
    sessionFirebaseKeys.forEach(key => {
      sessionStorage.removeItem(key)
      console.log(`🗑️ Removed sessionStorage: ${key}`)
    })
    
    console.log('✅ Browser Firebase storage cleared')
    
  } catch (error) {
    console.error('❌ Failed to clear browser storage:', error)
  }
}

/**
 * Complete Firestore recovery process
 */
export async function recoverFirestoreConnection(): Promise<boolean> {
  try {
    console.log('🚑 Starting Firestore connection recovery...')
    
    // Step 1: Clear browser storage
    clearBrowserFirebaseStorage()
    
    // Step 2: Force online
    const onlineSuccess = await forceFirestoreOnline()
    if (!onlineSuccess) {
      console.log('⚠️ Force online failed, trying cache clear...')
      
      // Step 3: Clear cache if force online failed
      const cacheSuccess = await clearFirestoreCache()
      if (!cacheSuccess) {
        console.error('❌ Cache clear also failed')
        return false
      }
    }
    
    console.log('🎉 Firestore connection recovery completed')
    return true
    
  } catch (error) {
    console.error('❌ Firestore recovery failed:', error)
    return false
  }
}

/**
 * Check if Firestore is offline
 */
export function isFirestoreOffline(): boolean {
  // This is a simple check - in a real app you'd want more sophisticated detection
  return !navigator.onLine
}

/**
 * Auto-recovery hook for React components
 */
export function useFirestoreAutoRecovery() {
  const recover = async () => {
    if (isFirestoreOffline()) {
      console.log('🔄 Auto-recovery triggered')
      return await recoverFirestoreConnection()
    }
    return true
  }
  
  return { recover, isOffline: isFirestoreOffline() }
}
