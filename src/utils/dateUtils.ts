/**
 * Date Utilities
 * 
 * Utility functions for handling dates and timestamps consistently
 * across the application. Handles both JavaScript Date objects and
 * Firestore Timestamp objects seamlessly.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * Type definition for date-like objects that can be either
 * JavaScript Date or Firestore Timestamp
 */
export type DateLike = Date | { toDate(): Date } | null | undefined

/**
 * Safely get timestamp from a date-like object
 * Handles both JavaScript Date objects and Firestore Timestamps
 * 
 * @param dateValue - The date value to convert
 * @returns Timestamp in milliseconds or null if invalid
 */
export function getTimestamp(dateValue: DateLike): number | null {
  if (!dateValue) return null
  
  try {
    if (dateValue instanceof Date) {
      return dateValue.getTime()
    }
    
    // Handle Firestore Timestamp
    if (typeof dateValue === 'object' && 'toDate' in dateValue && typeof dateValue.toDate === 'function') {
      return dateValue.toDate().getTime()
    }
    
    return null
  } catch (error) {
    console.warn('Error converting date to timestamp:', error)
    return null
  }
}

/**
 * Safely convert a date-like object to a JavaScript Date
 * 
 * @param dateValue - The date value to convert
 * @returns JavaScript Date object or null if invalid
 */
export function toDate(dateValue: DateLike): Date | null {
  if (!dateValue) return null
  
  try {
    if (dateValue instanceof Date) {
      return dateValue
    }
    
    // Handle Firestore Timestamp
    if (typeof dateValue === 'object' && 'toDate' in dateValue && typeof dateValue.toDate === 'function') {
      return dateValue.toDate()
    }
    
    return null
  } catch (error) {
    console.warn('Error converting to Date:', error)
    return null
  }
}

/**
 * Calculate the difference between two date-like objects in milliseconds
 * 
 * @param date1 - First date (usually the later date)
 * @param date2 - Second date (usually the earlier date)
 * @returns Difference in milliseconds or null if either date is invalid
 */
export function getDateDifference(date1: DateLike, date2: DateLike): number | null {
  const timestamp1 = getTimestamp(date1)
  const timestamp2 = getTimestamp(date2)
  
  if (timestamp1 === null || timestamp2 === null) return null
  
  return timestamp1 - timestamp2
}

/**
 * Calculate the age of something in days from a given date
 * 
 * @param dateValue - The date to calculate age from
 * @param referenceDate - The reference date (defaults to now)
 * @returns Age in days or null if date is invalid
 */
export function getAgeInDays(dateValue: DateLike, referenceDate: DateLike = new Date()): number | null {
  const difference = getDateDifference(referenceDate, dateValue)
  if (difference === null) return null
  
  return Math.floor(difference / (24 * 60 * 60 * 1000))
}

/**
 * Calculate the age of something in hours from a given date
 * 
 * @param dateValue - The date to calculate age from
 * @param referenceDate - The reference date (defaults to now)
 * @returns Age in hours or null if date is invalid
 */
export function getAgeInHours(dateValue: DateLike, referenceDate: DateLike = new Date()): number | null {
  const difference = getDateDifference(referenceDate, dateValue)
  if (difference === null) return null
  
  return Math.floor(difference / (60 * 60 * 1000))
}

/**
 * Check if a date is within a certain number of days from now
 * 
 * @param dateValue - The date to check
 * @param days - Number of days to check within
 * @param direction - 'past' to check if date is within the last X days, 'future' for next X days
 * @returns True if within the specified timeframe, false otherwise
 */
export function isWithinDays(
  dateValue: DateLike, 
  days: number, 
  direction: 'past' | 'future' = 'past'
): boolean {
  const ageInDays = getAgeInDays(dateValue)
  if (ageInDays === null) return false
  
  if (direction === 'past') {
    return ageInDays >= 0 && ageInDays <= days
  } else {
    return ageInDays <= 0 && Math.abs(ageInDays) <= days
  }
}

/**
 * Check if a date is older than a certain number of days
 * 
 * @param dateValue - The date to check
 * @param days - Number of days to compare against
 * @returns True if date is older than specified days, false otherwise
 */
export function isOlderThan(dateValue: DateLike, days: number): boolean {
  const ageInDays = getAgeInDays(dateValue)
  if (ageInDays === null) return false
  
  return ageInDays > days
}

/**
 * Check if a date is newer than a certain number of days
 * 
 * @param dateValue - The date to check
 * @param days - Number of days to compare against
 * @returns True if date is newer than specified days, false otherwise
 */
export function isNewerThan(dateValue: DateLike, days: number): boolean {
  const ageInDays = getAgeInDays(dateValue)
  if (ageInDays === null) return false
  
  return ageInDays < days
}

/**
 * Format a date-like object for display
 * 
 * @param dateValue - The date to format
 * @param options - Intl.DateTimeFormat options
 * @returns Formatted date string or 'Invalid Date' if conversion fails
 */
export function formatDate(
  dateValue: DateLike, 
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  }
): string {
  const date = toDate(dateValue)
  if (!date) return 'Invalid Date'
  
  try {
    return new Intl.DateTimeFormat('en-US', options).format(date)
  } catch (error) {
    console.warn('Error formatting date:', error)
    return 'Invalid Date'
  }
}

/**
 * Get a relative time string (e.g., "2 days ago", "in 3 hours")
 * 
 * @param dateValue - The date to get relative time for
 * @param referenceDate - The reference date (defaults to now)
 * @returns Relative time string or 'Invalid Date' if conversion fails
 */
export function getRelativeTime(dateValue: DateLike, referenceDate: DateLike = new Date()): string {
  const date = toDate(dateValue)
  const reference = toDate(referenceDate)
  
  if (!date || !reference) return 'Invalid Date'
  
  const difference = reference.getTime() - date.getTime()
  const absDifference = Math.abs(difference)
  
  const minute = 60 * 1000
  const hour = minute * 60
  const day = hour * 24
  const week = day * 7
  const month = day * 30
  const year = day * 365
  
  const isPast = difference > 0
  const suffix = isPast ? 'ago' : 'from now'
  
  if (absDifference < minute) {
    return 'just now'
  } else if (absDifference < hour) {
    const minutes = Math.floor(absDifference / minute)
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ${suffix}`
  } else if (absDifference < day) {
    const hours = Math.floor(absDifference / hour)
    return `${hours} hour${hours !== 1 ? 's' : ''} ${suffix}`
  } else if (absDifference < week) {
    const days = Math.floor(absDifference / day)
    return `${days} day${days !== 1 ? 's' : ''} ${suffix}`
  } else if (absDifference < month) {
    const weeks = Math.floor(absDifference / week)
    return `${weeks} week${weeks !== 1 ? 's' : ''} ${suffix}`
  } else if (absDifference < year) {
    const months = Math.floor(absDifference / month)
    return `${months} month${months !== 1 ? 's' : ''} ${suffix}`
  } else {
    const years = Math.floor(absDifference / year)
    return `${years} year${years !== 1 ? 's' : ''} ${suffix}`
  }
}

/**
 * Validate if a value is a valid date-like object
 * 
 * @param dateValue - The value to validate
 * @returns True if valid date-like object, false otherwise
 */
export function isValidDateLike(dateValue: any): dateValue is DateLike {
  if (!dateValue) return false
  
  if (dateValue instanceof Date) {
    return !isNaN(dateValue.getTime())
  }
  
  if (typeof dateValue === 'object' && 'toDate' in dateValue && typeof dateValue.toDate === 'function') {
    try {
      const date = dateValue.toDate()
      return date instanceof Date && !isNaN(date.getTime())
    } catch {
      return false
    }
  }
  
  return false
}

// Export commonly used time constants
export const TIME_CONSTANTS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  MONTH: 30 * 24 * 60 * 60 * 1000,
  YEAR: 365 * 24 * 60 * 60 * 1000
} as const
