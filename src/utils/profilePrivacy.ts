/**
 * Profile Privacy Utilities
 * 
 * Centralized privacy filtering logic to ensure consistent privacy
 * protection across all profile display components. Provides default
 * privacy protection for sensitive information in public views.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { UserProfile } from '@/types/profile'

export type ViewMode = 'public' | 'friends' | 'private'

/**
 * Privacy filtering rules for different view modes
 */
export const PRIVACY_RULES = {
  PUBLIC: {
    // ALWAYS hide sensitive contact information in public mode
    // This provides default privacy protection regardless of user settings
    hideEmail: true,
    hidePhone: true,
    respectOtherSettings: true,
    description: 'Public view - sensitive contact info always hidden'
  },
  FRIENDS: {
    // Respect individual privacy settings for friends
    hideEmail: false,
    hidePhone: false,
    respectOtherSettings: true,
    description: 'Friends view - respects individual privacy settings'
  },
  PRIVATE: {
    // User can see everything
    hideEmail: false,
    hidePhone: false,
    respectOtherSettings: false,
    description: 'Private view - no filtering applied'
  }
} as const

/**
 * Filter profile data based on privacy settings and view mode
 * 
 * PRIVACY PROTECTION RULES:
 * - Public mode: Email and phone are NEVER shown (default privacy protection)
 * - Friends mode: Respects individual privacy settings for email/phone
 * - Private mode: User can see everything
 * 
 * @param profile - The user profile to filter
 * @param viewMode - The viewing context ('public', 'friends', 'private')
 * @returns Filtered profile or null if not visible
 */
export function filterProfileForViewMode(
  profile: UserProfile | null, 
  viewMode: ViewMode
): UserProfile | null {
  if (!profile) return null

  // If no privacy settings, apply default filtering
  if (!profile.privacy) {
    if (viewMode === 'public') {
      const filtered = { ...profile }
      filtered.email = undefined
      filtered.phone = undefined
      return filtered
    }
    return profile
  }

  const privacy = profile.privacy
  const filtered: any = { ...profile }

  // Apply privacy filters based on view mode
  switch (viewMode) {
    case 'public':
      // Check if profile is visible to public
      if (privacy.profileVisibility !== 'public') return null
      
      // ALWAYS hide sensitive contact information in public mode
      // This provides default privacy protection regardless of user settings
      filtered.email = undefined
      filtered.phone = undefined
      
      // Apply other privacy settings
      if (!privacy.showLocation) filtered.location = undefined
      if (!privacy.showBio) filtered.bio = undefined
      if (!privacy.showSocialLinks) filtered.socialLinks = undefined
      if (!privacy.showWebsite) filtered.website = undefined
      if (!privacy.showAchievements) filtered.achievements = undefined
      if (!privacy.showPoints) filtered.points = undefined
      break

    case 'friends':
      // Check if profile is visible to friends
      if (privacy.profileVisibility === 'private') return null
      
      // Friends can see more but still respect specific privacy settings
      if (!privacy.showEmail) filtered.email = undefined
      if (!privacy.showPhone) filtered.phone = undefined
      if (!privacy.showLocation) filtered.location = undefined
      if (!privacy.showBio) filtered.bio = undefined
      if (!privacy.showSocialLinks) filtered.socialLinks = undefined
      if (!privacy.showWebsite) filtered.website = undefined
      if (!privacy.showAchievements) filtered.achievements = undefined
      if (!privacy.showPoints) filtered.points = undefined
      break

    case 'private':
      // User can see everything - no filtering applied
      break

    default:
      // Unknown view mode - apply public filtering as safe default
      filtered.email = undefined
      filtered.phone = undefined
      break
  }

  return filtered
}

/**
 * Check if a profile is visible in the given view mode
 * 
 * @param profile - The user profile to check
 * @param viewMode - The viewing context
 * @returns True if profile is visible, false otherwise
 */
export function isProfileVisible(
  profile: UserProfile | null, 
  viewMode: ViewMode
): boolean {
  if (!profile) return false
  if (!profile.privacy) return true // Default to visible if no privacy settings

  const privacy = profile.privacy

  switch (viewMode) {
    case 'public':
      return privacy.profileVisibility === 'public'
    case 'friends':
      return privacy.profileVisibility !== 'private'
    case 'private':
      return true
    default:
      return false
  }
}

/**
 * Get the appropriate view mode for a profile based on relationship
 * 
 * @param profile - The profile being viewed
 * @param viewerProfile - The profile of the person viewing (null if not logged in)
 * @param isFriend - Whether the viewer is a friend of the profile owner
 * @returns The appropriate view mode
 */
export function getViewModeForProfile(
  profile: UserProfile | null,
  viewerProfile: UserProfile | null,
  isFriend: boolean = false
): ViewMode {
  if (!profile || !viewerProfile) return 'public'
  
  // User viewing their own profile
  if (profile.id === viewerProfile.id) return 'private'
  
  // Friend viewing profile
  if (isFriend) return 'friends'
  
  // Public viewing
  return 'public'
}

/**
 * Check if contact information should be hidden for the given view mode
 * 
 * @param viewMode - The viewing context
 * @returns Object indicating what contact info should be hidden
 */
export function getContactVisibilityRules(viewMode: ViewMode) {
  switch (viewMode) {
    case 'public':
      return {
        hideEmail: true,
        hidePhone: true,
        reason: 'Contact information is never shown in public view for privacy protection'
      }
    case 'friends':
      return {
        hideEmail: false, // Respects privacy settings
        hidePhone: false, // Respects privacy settings
        reason: 'Contact information visibility depends on user privacy settings'
      }
    case 'private':
      return {
        hideEmail: false,
        hidePhone: false,
        reason: 'User can see all their own contact information'
      }
    default:
      return {
        hideEmail: true,
        hidePhone: true,
        reason: 'Default privacy protection applied'
      }
  }
}

/**
 * Sanitize profile data for safe public display
 * Removes any potentially sensitive information that should never be public
 * 
 * @param profile - The profile to sanitize
 * @returns Sanitized profile safe for public display
 */
export function sanitizeProfileForPublicDisplay(profile: UserProfile | null): UserProfile | null {
  if (!profile) return null

  const sanitized: any = { ...profile }
  
  // Always remove sensitive information for public display
  delete sanitized.email
  delete sanitized.phone
  delete sanitized.trustedDevices
  delete sanitized.mfaEnabled
  delete sanitized.lastPasswordChange
  delete sanitized.loginHistory
  
  // Remove internal IDs and sensitive metadata
  delete sanitized.firebaseUid
  delete sanitized.stripeCustomerId
  
  return sanitized
}

/**
 * Validate that a profile display component is following privacy rules
 * Used for development/testing to ensure components don't leak sensitive data
 * 
 * @param displayedData - The data being displayed by a component
 * @param viewMode - The view mode the component is using
 * @returns Validation result with any privacy violations
 */
export function validateProfileDisplayPrivacy(
  displayedData: any,
  viewMode: ViewMode
): {
  isValid: boolean
  violations: string[]
  warnings: string[]
} {
  const violations: string[] = []
  const warnings: string[] = []

  if (viewMode === 'public') {
    if (displayedData.email) {
      violations.push('Email address should never be displayed in public view')
    }
    if (displayedData.phone) {
      violations.push('Phone number should never be displayed in public view')
    }
    if (displayedData.trustedDevices) {
      violations.push('Trusted devices should never be displayed in public view')
    }
    if (displayedData.mfaEnabled !== undefined) {
      warnings.push('MFA status should not be displayed in public view')
    }
  }

  return {
    isValid: violations.length === 0,
    violations,
    warnings
  }
}

// Export types for use in components
export type { ViewMode }
