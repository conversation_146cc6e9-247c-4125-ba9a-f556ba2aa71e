/**
 * Basic AVA Setup Test
 * 
 * Simple test to verify AVA is working correctly
 */

import test from 'ava';

// Basic functionality tests
test('AVA is working correctly', t => {
  t.pass('AVA test runner is functioning');
});

test('basic arithmetic works', t => {
  t.is(2 + 2, 4);
  t.is(10 * 5, 50);
  t.is(100 / 4, 25);
});

test('string operations work', t => {
  t.is('hello'.toUpperCase(), 'HELLO');
  t.is('WORLD'.toLowerCase(), 'world');
  t.is('test'.length, 4);
});

test('array operations work', t => {
  const arr = [1, 2, 3, 4, 5];
  t.is(arr.length, 5);
  t.is(arr[0], 1);
  t.is(arr[arr.length - 1], 5);
  t.deepEqual(arr.slice(1, 3), [2, 3]);
});

test('async operations work', async t => {
  const result = await Promise.resolve('async test');
  t.is(result, 'async test');
});

test('object comparisons work', t => {
  const obj1 = { name: 'test', value: 42 };
  const obj2 = { name: 'test', value: 42 };
  
  t.deepEqual(obj1, obj2);
  t.not(obj1, obj2); // Different references
});

// Test macros (AVA feature)
const mathMacro = test.macro((t, input, expected) => {
  t.is(input.a + input.b, expected);
});

test('addition: 5 + 3', mathMacro, { a: 5, b: 3 }, 8);
test('addition: 10 + 15', mathMacro, { a: 10, b: 15 }, 25);
test('addition: 0 + 0', mathMacro, { a: 0, b: 0 }, 0);

// Parallel execution test
test('concurrent operations are consistent', async t => {
  const operations = [
    Promise.resolve(1 * 2),
    Promise.resolve(3 * 4),
    Promise.resolve(5 * 6),
    Promise.resolve(7 * 8)
  ];
  
  const results = await Promise.all(operations);
  const expected = [2, 12, 30, 56];
  
  t.deepEqual(results, expected);
});
