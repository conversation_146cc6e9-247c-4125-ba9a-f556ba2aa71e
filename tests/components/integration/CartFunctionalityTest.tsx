/**
 * Cart Functionality Test Component
 * 
 * Test component to verify cart functionality across all product types
 */

import React from 'react';
import ProductCard from './ProductCard';
import { Product } from '../../lib/firestore';
import { useCartStore } from '../../store/cartStore';
import { useRewardCartStore } from '../../store/rewardCartStore';
import { Timestamp } from 'firebase/firestore';

const testProducts: Product[] = [
  // Regular product
  {
    id: 'test-regular-1',
    name: 'Regular Keycap',
    description: 'Standard artisan keycap for testing cart functionality',
    price: 25.99,
    image: '/images/products/sample-keycap.jpg',
    category: 'Artisan',
    stock: 10,
    soldOut: false,
    isRaffle: false,
    featured: false,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  // Raffle product - active
  {
    id: 'test-raffle-1',
    name: 'Raffle Keycap - Active',
    description: 'Active raffle keycap for testing raffle functionality',
    price: 75.00,
    image: '/images/products/sample-keycap.jpg',
    category: 'Limited',
    stock: 1,
    soldOut: false,
    isRaffle: true,
    featured: true,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  // Reward product
  {
    id: 'test-reward-1',
    name: 'Reward Keycap',
    description: 'Reward shop exclusive keycap for testing reward cart',
    price: 50.00,
    pointsCost: 750,
    image: '/images/products/sample-keycap.jpg',
    category: 'Exclusive',
    stock: 5,
    soldOut: false,
    isRaffle: false,
    featured: false,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  // Sold out product
  {
    id: 'test-soldout-1',
    name: 'Sold Out Keycap',
    description: 'Out of stock keycap for testing sold out state',
    price: 30.00,
    image: '/images/products/sample-keycap.jpg',
    category: 'Artisan',
    stock: 0,
    soldOut: true,
    isRaffle: false,
    featured: false,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  }
];

/**
 * Cart Functionality Test Component
 */
const CartFunctionalityTest: React.FC = () => {
  const { items: cartItems, total } = useCartStore();
  const { items: rewardItems, getTotalPoints } = useRewardCartStore();

  return (
    <div className="min-h-screen bg-gray-950 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-white text-3xl font-bold mb-8 text-center">
          Cart Functionality Test
        </h1>
        
        {/* Test Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {testProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {/* Cart Status Display */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Regular Cart Status */}
          <div className="bg-gray-900 rounded-lg p-6">
            <h2 className="text-white text-xl font-semibold mb-4">Regular Cart</h2>
            <div className="space-y-2">
              <p className="text-gray-300">Items: {cartItems.length}</p>
              <p className="text-gray-300">Total: ${total().toFixed(2)}</p>
              {cartItems.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-white font-medium mb-2">Items in cart:</h3>
                  <ul className="space-y-1">
                    {cartItems.map((item, index) => (
                      <li key={index} className="text-gray-400 text-sm">
                        {item.product.name} (x{item.quantity}) - ${(item.product.price * item.quantity).toFixed(2)}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Reward Cart Status */}
          <div className="bg-gray-900 rounded-lg p-6">
            <h2 className="text-white text-xl font-semibold mb-4">Reward Cart</h2>
            <div className="space-y-2">
              <p className="text-gray-300">Items: {rewardItems.length}</p>
              <p className="text-gray-300">Total Points: {getTotalPoints()}</p>
              {rewardItems.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-white font-medium mb-2">Items in reward cart:</h3>
                  <ul className="space-y-1">
                    {rewardItems.map((item, index) => (
                      <li key={index} className="text-gray-400 text-sm">
                        {item.reward.name} (x{item.quantity}) - {item.reward.pointsCost * item.quantity} points
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="mt-12 bg-gray-900 rounded-lg p-6">
          <h2 className="text-white text-xl font-semibold mb-4">Test Instructions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-white font-medium mb-2">Regular Products:</h3>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>✓ Click "Add to Cart" on regular keycap</li>
                <li>✓ Verify success toast notification appears</li>
                <li>✓ Check cart counter updates</li>
                <li>✓ Verify loading state during add operation</li>
                <li>✓ Test sold out products show disabled state</li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Raffle Products:</h3>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>✓ Click "Join Raffle" redirects to product page</li>
                <li>✓ Verify raffle badge displays correctly</li>
                <li>✓ Check different raffle states (upcoming/active/ended)</li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Reward Products:</h3>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>✓ Click "Add to Reward Cart" on reward keycap</li>
                <li>✓ Verify success toast notification appears</li>
                <li>✓ Check reward cart counter updates</li>
                <li>✓ Verify point cost displays correctly</li>
                <li>✓ Test insufficient points state</li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Error Handling:</h3>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>✓ Network errors show error toast</li>
                <li>✓ Invalid operations are prevented</li>
                <li>✓ Loading states prevent double-clicks</li>
                <li>✓ Proper error messages display</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartFunctionalityTest;
