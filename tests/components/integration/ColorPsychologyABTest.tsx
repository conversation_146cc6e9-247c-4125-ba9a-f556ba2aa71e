'use client'

/**
 * Color Psychology A/B Testing Component
 * 
 * Implements A/B testing framework for color psychology optimizations
 * with user segmentation, variant assignment, and analytics tracking.
 * 
 * <AUTHOR> UX Psychology Team
 * @version 1.0
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { colorPsychologyUtils, UserSegment, TestVariant } from '@/lib/utils/colorPsychologyUtils'
import { COLOR_PSYCHOLOGY_CONFIG } from '@/lib/config/colorPsychology'

interface ColorPsychologyTestContextType {
  userSegment: UserSegment
  testVariants: Record<string, TestVariant>
  getTestColor: (testName: string, fallback?: string) => string
  trackColorInteraction: (eventType: string, context: string, additionalData?: any) => void
  isTestActive: (testName: string) => boolean
}

const ColorPsychologyTestContext = createContext<ColorPsychologyTestContextType | null>(null)

interface ColorPsychologyTestProviderProps {
  children: ReactNode
  userId?: string
  userSegment?: UserSegment
  enableTesting?: boolean
}

/**
 * Color Psychology A/B Test Provider
 */
export const ColorPsychologyTestProvider: React.FC<ColorPsychologyTestProviderProps> = ({
  children,
  userId,
  userSegment = 'community',
  enableTesting = true
}) => {
  const [testVariants, setTestVariants] = useState<Record<string, TestVariant>>({})
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    if (!enableTesting || !userId) {
      setIsInitialized(true)
      return
    }

    // Initialize A/B test assignments
    const initializeTests = () => {
      const variants: Record<string, TestVariant> = {}
      
      // Community Orange Test
      variants.communityOrange = colorPsychologyUtils.abTest.getVariant(
        'communityOrange',
        userId,
        userSegment
      )
      
      // Authority Blue Test
      variants.authorityBlue = colorPsychologyUtils.abTest.getVariant(
        'authorityBlue',
        userId,
        userSegment
      )
      
      // Gaming Neon Test
      variants.gamingNeon = colorPsychologyUtils.abTest.getVariant(
        'gamingNeon',
        userId,
        userSegment
      )

      setTestVariants(variants)
      setIsInitialized(true)

      // Track test initialization
      colorPsychologyUtils.tracker.trackColorInteraction(
        'ab_test_initialized',
        'testing',
        userSegment,
        { variants, userId }
      )
    }

    initializeTests()
  }, [userId, userSegment, enableTesting])

  const getTestColor = (testName: string, fallback?: string): string => {
    if (!enableTesting || !testVariants[testName]) {
      return fallback || '#000000'
    }

    const variant = testVariants[testName]
    
    try {
      const testColor = colorPsychologyUtils.abTest.getTestColor(
        testName as keyof typeof COLOR_PSYCHOLOGY_CONFIG.testing.variants,
        userId || 'anonymous',
        userSegment
      )
      
      return typeof testColor === 'string' ? testColor : fallback || '#000000'
    } catch (error) {
      console.warn(`Failed to get test color for ${testName}:`, error)
      return fallback || '#000000'
    }
  }

  const trackColorInteraction = (
    eventType: string,
    context: string,
    additionalData?: any
  ) => {
    if (!enableTesting) return

    colorPsychologyUtils.tracker.trackColorInteraction(
      eventType,
      context as any,
      userSegment,
      {
        ...additionalData,
        testVariants,
        userId
      }
    )
  }

  const isTestActive = (testName: string): boolean => {
    return enableTesting && COLOR_PSYCHOLOGY_CONFIG.testing.enabled && !!testVariants[testName]
  }

  const contextValue: ColorPsychologyTestContextType = {
    userSegment,
    testVariants,
    getTestColor,
    trackColorInteraction,
    isTestActive
  }

  if (!isInitialized) {
    return <div>Loading color psychology tests...</div>
  }

  return (
    <ColorPsychologyTestContext.Provider value={contextValue}>
      {children}
    </ColorPsychologyTestContext.Provider>
  )
}

/**
 * Hook to use Color Psychology A/B Testing
 */
export const useColorPsychologyTest = () => {
  const context = useContext(ColorPsychologyTestContext)
  
  if (!context) {
    // Return fallback values when context is not available
    return {
      userSegment: 'community' as UserSegment,
      testVariants: {},
      getTestColor: (testName: string, fallback?: string) => fallback || '#000000',
      trackColorInteraction: () => {},
      isTestActive: () => false
    }
  }
  
  return context
}

/**
 * Color Psychology Test Button Component
 * Example component that uses A/B testing
 */
interface ColorTestButtonProps {
  testName: string
  children: ReactNode
  onClick?: () => void
  className?: string
  fallbackColor?: string
}

export const ColorTestButton: React.FC<ColorTestButtonProps> = ({
  testName,
  children,
  onClick,
  className = '',
  fallbackColor = '#0ea5e9'
}) => {
  const { getTestColor, trackColorInteraction, isTestActive } = useColorPsychologyTest()
  
  const buttonColor = getTestColor(testName, fallbackColor)
  
  const handleClick = () => {
    trackColorInteraction('button_click', testName, {
      color: buttonColor,
      testActive: isTestActive(testName)
    })
    
    onClick?.()
  }

  return (
    <button
      onClick={handleClick}
      className={`px-6 py-3 rounded-lg font-medium text-white transition-all duration-200 hover:opacity-90 ${className}`}
      style={{ backgroundColor: buttonColor }}
    >
      {children}
    </button>
  )
}

/**
 * Color Psychology Test Card Component
 * Example component that uses A/B testing for borders/accents
 */
interface ColorTestCardProps {
  testName: string
  children: ReactNode
  className?: string
  fallbackColor?: string
}

export const ColorTestCard: React.FC<ColorTestCardProps> = ({
  testName,
  children,
  className = '',
  fallbackColor = '#0ea5e9'
}) => {
  const { getTestColor, trackColorInteraction } = useColorPsychologyTest()
  
  const accentColor = getTestColor(testName, fallbackColor)
  
  const handleInteraction = (interactionType: string) => {
    trackColorInteraction(interactionType, testName, {
      color: accentColor
    })
  }

  return (
    <div
      className={`bg-gray-900 rounded-lg p-6 border-2 transition-all duration-200 ${className}`}
      style={{ borderColor: accentColor + '40' }} // 40 for opacity
      onMouseEnter={() => handleInteraction('card_hover')}
      onClick={() => handleInteraction('card_click')}
    >
      {children}
    </div>
  )
}

/**
 * Color Psychology Test Results Component
 * Displays current test assignments for debugging
 */
export const ColorTestResults: React.FC = () => {
  const { userSegment, testVariants, isTestActive } = useColorPsychologyTest()

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 border border-gray-600 rounded-lg p-4 text-sm text-white max-w-sm">
      <h4 className="font-semibold mb-2">Color Psychology Tests</h4>
      <div className="space-y-1">
        <div>Segment: <span className="text-accent-400">{userSegment}</span></div>
        {Object.entries(testVariants).map(([testName, variant]) => (
          <div key={testName}>
            {testName}: <span className="text-blue-400">{variant}</span>
            {isTestActive(testName) && <span className="text-green-400 ml-2">✓</span>}
          </div>
        ))}
      </div>
    </div>
  )
}

export default ColorPsychologyTestProvider
