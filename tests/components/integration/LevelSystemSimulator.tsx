/**
 * Level System Simulator Component
 * 
 * Advanced simulation controls for testing the level system with various scenarios,
 * edge cases, and user progression paths.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Play,
  Pause,
  RotateCcw,
  FastForward,
  Settings,
  Users,
  Zap,
  Trophy,
  Gift,
  Target,
  BarChart3,
  Clock,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { 
  LevelBadge,
  LevelProgressBar,
  XPGainNotification,
  useXPNotificationQueue
} from '@/components/level'
import { UserProfileWithLevel } from '@/types/levelProfile'
import { XPTransaction, getLevelByXP } from '@/lib/levelSystem'
import { mockUsers, generateRandomXP } from '@/lib/testing/mockLevelData'

// ===== TYPES =====

interface SimulationState {
  isRunning: boolean
  speed: number
  autoMode: boolean
  currentUser: UserProfileWithLevel
  simulationLog: string[]
  totalXPAwarded: number
  levelUpsTriggered: number
  notificationsShown: number
}

interface SimulationConfig {
  xpPerSecond: number
  enableNotifications: boolean
  enableLevelUps: boolean
  enableRandomEvents: boolean
  maxSimulationTime: number
}

// ===== COMPONENT =====

export const LevelSystemSimulator: React.FC<{
  initialUser?: UserProfileWithLevel
  onUserChange?: (user: UserProfileWithLevel) => void
  onSimulationEvent?: (event: string) => void
}> = ({
  initialUser = mockUsers[1],
  onUserChange,
  onSimulationEvent
}) => {
  const [state, setState] = useState<SimulationState>({
    isRunning: false,
    speed: 1,
    autoMode: false,
    currentUser: initialUser,
    simulationLog: [],
    totalXPAwarded: 0,
    levelUpsTriggered: 0,
    notificationsShown: 0
  })

  const [config, setConfig] = useState<SimulationConfig>({
    xpPerSecond: 10,
    enableNotifications: true,
    enableLevelUps: true,
    enableRandomEvents: true,
    maxSimulationTime: 300 // 5 minutes
  })

  const { addNotification } = useXPNotificationQueue()

  // Simulation timer
  useEffect(() => {
    if (!state.isRunning) return

    const interval = setInterval(() => {
      if (state.autoMode) {
        simulateRandomXPGain()
      }
    }, 1000 / state.speed)

    return () => clearInterval(interval)
  }, [state.isRunning, state.speed, state.autoMode])

  // Add log entry
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setState(prev => ({
      ...prev,
      simulationLog: [`${timestamp}: ${message}`, ...prev.simulationLog.slice(0, 19)]
    }))
    onSimulationEvent?.(message)
  }

  // Simulate random XP gain
  const simulateRandomXPGain = () => {
    const sources: XPTransaction['source'][] = ['purchase', 'activity', 'bonus', 'event']
    const source = sources[Math.floor(Math.random() * sources.length)]
    const amount = generateRandomXP(5, 100)
    
    awardXP(source, amount)
  }

  // Award XP to current user
  const awardXP = (source: XPTransaction['source'], amount: number) => {
    const oldLevel = state.currentUser.level.current
    const newTotalXP = state.currentUser.level.totalXP + amount
    const newLevel = getLevelByXP(newTotalXP)

    // Update user
    const updatedUser: UserProfileWithLevel = {
      ...state.currentUser,
      level: {
        ...state.currentUser.level,
        totalXP: newTotalXP,
        currentXP: newTotalXP - newLevel.xpRequired,
        current: newLevel.level,
        levelName: newLevel.name,
        tier: newLevel.tier,
        nextLevelXP: newLevel.xpToNext,
        progressToNext: ((newTotalXP - newLevel.xpRequired) / newLevel.xpToNext) * 100
      }
    }

    setState(prev => ({
      ...prev,
      currentUser: updatedUser,
      totalXPAwarded: prev.totalXPAwarded + amount,
      levelUpsTriggered: newLevel.level > oldLevel ? prev.levelUpsTriggered + 1 : prev.levelUpsTriggered,
      notificationsShown: config.enableNotifications ? prev.notificationsShown + 1 : prev.notificationsShown
    }))

    onUserChange?.(updatedUser)

    // Show notification
    if (config.enableNotifications) {
      addNotification({
        xpAmount: amount,
        source,
        description: `Simulation: ${source} XP gain`,
        duration: 2000
      })
    }

    // Log event
    if (newLevel.level > oldLevel) {
      addLog(`Level up! ${oldLevel} → ${newLevel.level} (+${amount} XP from ${source})`)
    } else {
      addLog(`+${amount} XP from ${source} (Level ${newLevel.level})`)
    }
  }

  // Reset simulation
  const resetSimulation = () => {
    setState(prev => ({
      ...prev,
      isRunning: false,
      currentUser: initialUser,
      simulationLog: [],
      totalXPAwarded: 0,
      levelUpsTriggered: 0,
      notificationsShown: 0
    }))
    addLog('Simulation reset')
  }

  // Toggle simulation
  const toggleSimulation = () => {
    setState(prev => ({
      ...prev,
      isRunning: !prev.isRunning
    }))
    addLog(state.isRunning ? 'Simulation paused' : 'Simulation started')
  }

  // Load test user
  const loadTestUser = (userIndex: number) => {
    const user = mockUsers[userIndex]
    setState(prev => ({
      ...prev,
      currentUser: user,
      isRunning: false
    }))
    onUserChange?.(user)
    addLog(`Loaded test user: ${user.displayName} (Level ${user.level.current})`)
  }

  return (
    <div className="space-y-6">
      {/* Simulation Controls */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-purple-400" />
            Simulation Controls
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Controls */}
          <div className="flex items-center gap-3">
            <Button
              onClick={toggleSimulation}
              className={state.isRunning ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}
            >
              {state.isRunning ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
              {state.isRunning ? 'Pause' : 'Start'}
            </Button>
            
            <Button onClick={resetSimulation} variant="outline">
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>

            <div className="flex items-center gap-2">
              <Switch
                checked={state.autoMode}
                onCheckedChange={(checked) => setState(prev => ({ ...prev, autoMode: checked }))}
              />
              <span className="text-sm text-gray-400">Auto Mode</span>
            </div>
          </div>

          {/* Speed Control */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Simulation Speed</span>
              <Badge>{state.speed}x</Badge>
            </div>
            <Slider
              value={[state.speed]}
              onValueChange={([value]) => setState(prev => ({ ...prev, speed: value }))}
              min={0.5}
              max={5}
              step={0.5}
              className="w-full"
            />
          </div>

          {/* XP Per Second */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">XP Per Second (Auto Mode)</span>
              <Badge>{config.xpPerSecond}</Badge>
            </div>
            <Slider
              value={[config.xpPerSecond]}
              onValueChange={([value]) => setConfig(prev => ({ ...prev, xpPerSecond: value }))}
              min={1}
              max={100}
              step={1}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Manual XP Controls */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-yellow-400" />
            Manual XP Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button 
              onClick={() => awardXP('purchase', 50)}
              variant="outline"
              className="border-green-500/30 hover:bg-green-500/10"
            >
              <Gift className="w-4 h-4 mr-2" />
              +50 Purchase
            </Button>
            <Button 
              onClick={() => awardXP('activity', 25)}
              variant="outline"
              className="border-blue-500/30 hover:bg-blue-500/10"
            >
              <Trophy className="w-4 h-4 mr-2" />
              +25 Activity
            </Button>
            <Button 
              onClick={() => awardXP('bonus', 100)}
              variant="outline"
              className="border-purple-500/30 hover:bg-purple-500/10"
            >
              <Target className="w-4 h-4 mr-2" />
              +100 Bonus
            </Button>
            <Button 
              onClick={() => awardXP('event', 200)}
              variant="outline"
              className="border-yellow-500/30 hover:bg-yellow-500/10"
            >
              <FastForward className="w-4 h-4 mr-2" />
              +200 Event
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current User Status */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-blue-400" />
            Current User Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-white font-semibold">{state.currentUser.displayName}</h3>
              <p className="text-gray-400 text-sm">{state.currentUser.level.levelName}</p>
            </div>
            <LevelBadge
              level={state.currentUser.level.current}
              levelName={state.currentUser.level.levelName}
              tier={state.currentUser.level.tier}
              size="lg"
              glow={true}
            />
          </div>

          <LevelProgressBar
            currentXP={state.currentUser.level.currentXP}
            nextLevelXP={state.currentUser.level.nextLevelXP}
            progressPercentage={state.currentUser.level.progressToNext}
            currentLevel={state.currentUser.level.current}
            nextLevel={state.currentUser.level.current + 1}
            tier={state.currentUser.level.tier}
            showXP={true}
            showPercentage={true}
            animated={true}
          />

          {/* Test User Selector */}
          <div className="border-t border-gray-700 pt-4">
            <h4 className="text-sm font-semibold text-white mb-3">Load Test User</h4>
            <div className="grid grid-cols-2 gap-2">
              {mockUsers.map((user, index) => (
                <Button
                  key={user.id}
                  onClick={() => loadTestUser(index)}
                  variant="outline"
                  size="sm"
                  className={state.currentUser.id === user.id ? 'border-purple-500 bg-purple-500/10' : ''}
                >
                  {user.displayName} (Lv.{user.level.current})
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Simulation Statistics */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-green-400" />
            Simulation Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-800/50 rounded">
              <div className="text-lg font-bold text-white">{state.totalXPAwarded.toLocaleString()}</div>
              <div className="text-xs text-gray-400">Total XP Awarded</div>
            </div>
            <div className="text-center p-3 bg-gray-800/50 rounded">
              <div className="text-lg font-bold text-white">{state.levelUpsTriggered}</div>
              <div className="text-xs text-gray-400">Level Ups</div>
            </div>
            <div className="text-center p-3 bg-gray-800/50 rounded">
              <div className="text-lg font-bold text-white">{state.notificationsShown}</div>
              <div className="text-xs text-gray-400">Notifications</div>
            </div>
            <div className="text-center p-3 bg-gray-800/50 rounded">
              <div className="text-lg font-bold text-white">{state.simulationLog.length}</div>
              <div className="text-xs text-gray-400">Log Entries</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Simulation Log */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-orange-400" />
            Simulation Log
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-1 max-h-48 overflow-y-auto">
            {state.simulationLog.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No simulation events yet</p>
            ) : (
              state.simulationLog.map((entry, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="text-sm text-gray-300 font-mono bg-gray-800/30 p-2 rounded"
                >
                  {entry}
                </motion.div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default LevelSystemSimulator
