/**
 * ProductCard Test Component
 * 
 * Test component to verify the new ProductCard layout with example data
 */

import React from 'react';
import ProductCard from './ProductCard';
import { Product } from '../../lib/firestore';
import { Timestamp } from 'firebase/firestore';

const testProducts: Product[] = [
  {
    id: 'test-1',
    name: 'Monihead',
    description: 'Premium artisan keycap with intricate design',
    price: 1,
    image: '/images/products/sample-keycap.jpg',
    category: 'Sirius',
    stock: 10,
    soldOut: false,
    isRaffle: false,
    featured: false,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  {
    id: 'test-2',
    name: '<PERSON>ih<PERSON> - Raffle',
    description: 'Limited edition raffle keycap',
    price: 50,
    image: '/images/products/sample-keycap.jpg',
    category: 'Sirius',
    stock: 1,
    soldOut: false,
    isRaffle: true,
    featured: true,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  {
    id: 'test-3',
    name: '<PERSON><PERSON><PERSON> - <PERSON><PERSON>',
    description: 'Reward shop exclusive keycap',
    price: 75,
    pointsCost: 500,
    image: '/images/products/sample-keycap.jpg',
    category: 'Sirius',
    stock: 5,
    soldOut: false,
    isRaffle: false,
    featured: false,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  {
    id: 'test-4',
    name: 'Monihead - Sold Out',
    description: 'Out of stock keycap',
    price: 25,
    image: '/images/products/sample-keycap.jpg',
    category: 'Sirius',
    stock: 0,
    soldOut: true,
    isRaffle: false,
    featured: false,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  }
];

/**
 * Test component for ProductCard layout verification
 */
const ProductCardTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-950 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-white text-3xl font-bold mb-8 text-center">
          ProductCard Layout Test - Reference Design
        </h1>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {testProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        <div className="mt-12 text-center">
          <h2 className="text-white text-xl font-semibold mb-4">Layout Features Tested:</h2>
          <ul className="text-gray-300 space-y-2">
            <li>✓ Product name "Monihead" positioned at bottom-left</li>
            <li>✓ Artist/Category "Sirius" below product name</li>
            <li>✓ Price positioned at bottom-left corner</li>
            <li>✓ Action buttons positioned at bottom-right</li>
            <li>✓ Clean, compact overlay design</li>
            <li>✓ Consistent styling across product types</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ProductCardTest;
