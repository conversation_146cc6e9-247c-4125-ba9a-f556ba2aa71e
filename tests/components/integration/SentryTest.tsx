/**
 * Sentry Integration Test Component
 * 
 * Test component to verify Sentry error tracking is working correctly.
 * This component should only be used in development/testing environments.
 * 
 * <AUTHOR> Team
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSentry } from '@/hooks/useSentry';
import { getSentryStatus } from '@/lib/sentry/init';
import { AlertCircle, CheckCircle, Bug, Zap, ShoppingCart, Trophy } from 'lucide-react';

export default function SentryTest() {
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const sentry = useSentry();
  const sentryStatus = getSentryStatus();

  const runTest = (testName: string, testFn: () => void) => {
    try {
      testFn();
      setTestResults(prev => ({ ...prev, [testName]: true }));
    } catch (error) {
      setTestResults(prev => ({ ...prev, [testName]: false }));
      console.error(`Test ${testName} failed:`, error);
    }
  };

  const testError = () => {
    runTest('error', () => {
      const error = new Error('Test error from Sentry integration test');
      sentry.captureError(error, { testType: 'manual', component: 'SentryTest' });
    });
  };

  const testMessage = () => {
    runTest('message', () => {
      sentry.captureMessage('Test message from Sentry integration', 'info', {
        testType: 'manual',
        component: 'SentryTest'
      });
    });
  };

  const testUserAction = () => {
    runTest('userAction', () => {
      sentry.trackAction('test_button_click', {
        buttonName: 'Sentry Test Button',
        timestamp: new Date().toISOString()
      });
    });
  };

  const testApiCall = () => {
    runTest('apiCall', () => {
      sentry.trackApiCall('GET', '/api/test/sentry', 200, 150);
    });
  };

  const testEcommerce = () => {
    runTest('ecommerce', () => {
      sentry.trackAddToCart('test-product-123', {
        productName: 'Test Product',
        price: 29.99,
        quantity: 1
      });
    });
  };

  const testGamification = () => {
    runTest('gamification', () => {
      sentry.trackPointsEarned(100, 'test_action', {
        actionType: 'sentry_test',
        userId: 'test-user'
      });
    });
  };

  const testPerformance = () => {
    runTest('performance', () => {
      const transaction = sentry.startPerformanceTransaction('test_transaction', 'test');
      if (transaction) {
        setTimeout(() => {
          transaction.setStatus('ok');
          transaction.finish();
        }, 100);
      }
    });
  };

  const testComponentError = () => {
    runTest('componentError', () => {
      // This will trigger an error boundary
      throw new Error('Test component error for Sentry');
    });
  };

  const getStatusIcon = (status: boolean | undefined) => {
    if (status === undefined) return null;
    return status ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <AlertCircle className="w-4 h-4 text-red-500" />
    );
  };

  if (process.env.NODE_ENV === 'production') {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="p-6 text-center">
          <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">
            Sentry test component is not available in production.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="w-5 h-5" />
            Sentry Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <h3 className="font-semibold mb-2">Sentry Status</h3>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  {sentryStatus.initialized ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                  <span>Initialized: {sentryStatus.initialized ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex items-center gap-2">
                  {sentryStatus.dsn === 'configured' ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-yellow-500" />
                  )}
                  <span>DSN: {sentryStatus.dsn}</span>
                </div>
                <div className="text-gray-600">
                  Environment: {sentryStatus.environment}
                </div>
                <div className="text-gray-600">
                  Release: {sentryStatus.release}
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">Test Results</h3>
              <div className="space-y-1 text-sm">
                {Object.entries(testResults).map(([test, result]) => (
                  <div key={test} className="flex items-center gap-2">
                    {getStatusIcon(result)}
                    <span className="capitalize">{test}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button
              onClick={testError}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <AlertCircle className="w-4 h-4" />
              Test Error
            </Button>

            <Button
              onClick={testMessage}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Zap className="w-4 h-4" />
              Test Message
            </Button>

            <Button
              onClick={testUserAction}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <CheckCircle className="w-4 h-4" />
              User Action
            </Button>

            <Button
              onClick={testApiCall}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Zap className="w-4 h-4" />
              API Call
            </Button>

            <Button
              onClick={testEcommerce}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <ShoppingCart className="w-4 h-4" />
              E-commerce
            </Button>

            <Button
              onClick={testGamification}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Trophy className="w-4 h-4" />
              Gamification
            </Button>

            <Button
              onClick={testPerformance}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Zap className="w-4 h-4" />
              Performance
            </Button>

            <Button
              onClick={testComponentError}
              variant="destructive"
              size="sm"
              className="flex items-center gap-2"
            >
              <Bug className="w-4 h-4" />
              Component Error
            </Button>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Instructions</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Click buttons to test different Sentry features</li>
              <li>• Check your Sentry dashboard to verify events are captured</li>
              <li>• Component Error button will trigger an error boundary</li>
              <li>• All tests add breadcrumbs and context to Sentry</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
