/**
 * Social Share Functionality Test Component
 * 
 * Test component to verify social share functionality across all platforms
 */

import React, { useState } from 'react';
import SocialShare, { useProductShare, QuickShareButton, ShareContent } from './SocialShare';
import { Product } from '../../lib/firestore';
import { Timestamp } from 'firebase/firestore';
import { Share2, CheckCircle, XCircle } from 'lucide-react';

const testProduct: Product = {
  id: 'test-share-product',
  name: 'Monihead - Test Share',
  description: 'Premium artisan keycap perfect for testing social share functionality',
  price: 45.99,
  image: '/images/products/sample-keycap.jpg',
  category: 'Artisan',
  stock: 5,
  soldOut: false,
  isRaffle: false,
  featured: false,
  tags: ['premium', 'sculpted', 'limited'],
  createdAt: Timestamp.now(),
  updatedAt: Timestamp.now()
};

/**
 * Social Share Test Component
 */
const SocialShareTest: React.FC = () => {
  const { createProductShareContent, createWishlistShareContent, createAchievementShareContent } = useProductShare();
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [showModal, setShowModal] = useState(false);

  // Create test content
  const productShareContent = createProductShareContent(testProduct);
  const wishlistShareContent = createWishlistShareContent([testProduct]);
  const achievementShareContent = createAchievementShareContent({
    title: 'First Purchase',
    description: 'Made your first purchase',
    pointsEarned: 200
  });

  /**
   * Handle share test
   */
  const handleShareTest = (platform: string, success: boolean) => {
    setTestResults(prev => ({
      ...prev,
      [platform]: success
    }));
  };

  /**
   * Test share content creation
   */
  const testShareContent = () => {
    const tests = [
      {
        name: 'Product Share Content',
        test: () => {
          return (
            productShareContent.title === testProduct.name &&
            productShareContent.url.includes(`/shop/${testProduct.id}`) &&
            productShareContent.hashtags?.includes('syndicaps')
          );
        }
      },
      {
        name: 'Wishlist Share Content',
        test: () => {
          return (
            wishlistShareContent.title.includes('Wishlist') &&
            wishlistShareContent.url.includes('/wishlist')
          );
        }
      },
      {
        name: 'Achievement Share Content',
        test: () => {
          return (
            achievementShareContent.title.includes('Achievement Unlocked') &&
            achievementShareContent.description.includes('200 points')
          );
        }
      }
    ];

    return tests.map(test => ({
      ...test,
      passed: test.test()
    }));
  };

  const contentTests = testShareContent();

  return (
    <div className="min-h-screen bg-gray-950 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-white text-3xl font-bold mb-8 text-center">
          Social Share Functionality Test
        </h1>

        {/* Share Content Tests */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-white text-xl font-semibold mb-4">Share Content Creation Tests</h2>
          <div className="space-y-3">
            {contentTests.map((test, index) => (
              <div key={index} className="flex items-center space-x-3">
                {test.passed ? (
                  <CheckCircle size={20} className="text-green-400" />
                ) : (
                  <XCircle size={20} className="text-red-400" />
                )}
                <span className={`${test.passed ? 'text-green-400' : 'text-red-400'}`}>
                  {test.name}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Modal Share Test */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-white text-xl font-semibold mb-4">Modal Share Test</h2>
          <p className="text-gray-400 mb-4">
            Test the modal share functionality with the product content
          </p>
          <button
            onClick={() => setShowModal(true)}
            className="bg-accent-500 text-white px-4 py-2 rounded-lg hover:bg-accent-600 transition-colors flex items-center space-x-2"
          >
            <Share2 size={20} />
            <span>Open Share Modal</span>
          </button>

          {showModal && (
            <SocialShare
              content={productShareContent}
              modal={true}
              onShare={(platform) => {
                console.log(`Shared on ${platform}`);
                handleShareTest(`modal-${platform}`, true);
                setShowModal(false);
              }}
            />
          )}
        </div>

        {/* Quick Share Buttons Test */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-white text-xl font-semibold mb-4">Quick Share Buttons Test</h2>
          <p className="text-gray-400 mb-4">
            Test individual platform share buttons
          </p>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
            <QuickShareButton
              content={productShareContent}
              platform="facebook"
              size="md"
            />
            <QuickShareButton
              content={productShareContent}
              platform="twitter"
              size="md"
            />
            <QuickShareButton
              content={productShareContent}
              platform="whatsapp"
              size="md"
            />
            <QuickShareButton
              content={productShareContent}
              platform="email"
              size="md"
            />
            <QuickShareButton
              content={productShareContent}
              platform="instagram"
              size="md"
            />
            <QuickShareButton
              content={productShareContent}
              platform="copy"
              size="md"
            />
          </div>
        </div>

        {/* Inline Share Test */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-white text-xl font-semibold mb-4">Inline Share Test</h2>
          <p className="text-gray-400 mb-4">
            Test inline share component (non-modal)
          </p>
          <SocialShare
            content={productShareContent}
            modal={false}
            onShare={(platform) => {
              console.log(`Shared on ${platform}`);
              handleShareTest(`inline-${platform}`, true);
            }}
          />
        </div>

        {/* Test Results */}
        <div className="bg-gray-900 rounded-lg p-6">
          <h2 className="text-white text-xl font-semibold mb-4">Test Results</h2>
          {Object.keys(testResults).length > 0 ? (
            <div className="space-y-2">
              {Object.entries(testResults).map(([platform, success]) => (
                <div key={platform} className="flex items-center space-x-3">
                  {success ? (
                    <CheckCircle size={20} className="text-green-400" />
                  ) : (
                    <XCircle size={20} className="text-red-400" />
                  )}
                  <span className={`${success ? 'text-green-400' : 'text-red-400'}`}>
                    {platform} share test
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-400">No share tests completed yet. Try sharing to see results.</p>
          )}
        </div>

        {/* Test Instructions */}
        <div className="mt-8 bg-gray-900 rounded-lg p-6">
          <h2 className="text-white text-xl font-semibold mb-4">Test Instructions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-white font-medium mb-2">Modal Tests:</h3>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>✓ Click "Open Share Modal" button</li>
                <li>✓ Verify modal opens with content preview</li>
                <li>✓ Test each platform button</li>
                <li>✓ Verify modal closes after sharing</li>
                <li>✓ Check copy to clipboard functionality</li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Platform Tests:</h3>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>✓ Facebook opens in new window</li>
                <li>✓ Twitter includes hashtags</li>
                <li>✓ WhatsApp formats message correctly</li>
                <li>✓ Email opens mail client</li>
                <li>✓ Instagram shows manual copy message</li>
                <li>✓ Copy button copies URL to clipboard</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialShareTest;
