/**
 * Resource Loading Test Component
 * 
 * Test component to verify resource loading error handling
 * and crash protection improvements.
 */

'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, XCircle, RefreshCw } from 'lucide-react'
import { resourceLoader } from '@/lib/utils/resourceLoader'
import { crashProtection } from '@/lib/protection/crashProtection'

interface TestResult {
  test: string
  status: 'pending' | 'success' | 'error'
  message: string
  timestamp: string
}

export default function ResourceLoadingTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addTestResult = (test: string, status: 'success' | 'error', message: string) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  const runResourceLoadingTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: Valid CSS loading
    try {
      await resourceLoader.loadCSS('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap', {
        timeout: 5000,
        onSuccess: () => addTestResult('Valid CSS Loading', 'success', 'Google Fonts loaded successfully'),
        onError: (error) => addTestResult('Valid CSS Loading', 'error', error.message)
      })
    } catch (error) {
      addTestResult('Valid CSS Loading', 'error', (error as Error).message)
    }

    // Test 2: Invalid CSS loading (should fail gracefully)
    try {
      await resourceLoader.loadCSS('https://invalid-domain-that-does-not-exist.com/style.css', {
        timeout: 3000,
        onError: (error) => addTestResult('Invalid CSS Loading', 'success', 'Error handled gracefully: ' + error.message)
      })
    } catch (error) {
      addTestResult('Invalid CSS Loading', 'success', 'Error caught and handled: ' + (error as Error).message)
    }

    // Test 3: Resource status check
    const resourceStatus = resourceLoader.getResourceStatus()
    addTestResult('Resource Status Check', 'success', `Tracked ${resourceStatus.length} resources`)

    // Test 4: Crash protection tracking
    try {
      crashProtection.trackError('test-component', new Error('Test error for validation'))
      addTestResult('Crash Protection', 'success', 'Error tracking working correctly')
    } catch (error) {
      addTestResult('Crash Protection', 'error', 'Error tracking failed: ' + (error as Error).message)
    }

    // Test 5: Memory usage check
    try {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const memoryUsage = memory.usedJSHeapSize / (1024 * 1024)
        addTestResult('Memory Check', 'success', `Memory usage: ${memoryUsage.toFixed(2)}MB`)
      } else {
        addTestResult('Memory Check', 'success', 'Memory API not available (normal in some browsers)')
      }
    } catch (error) {
      addTestResult('Memory Check', 'error', 'Memory check failed: ' + (error as Error).message)
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-600">Success</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="secondary">Pending</Badge>
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            Resource Loading & Crash Protection Test
          </CardTitle>
          <CardDescription>
            Test the improved resource loading error handling and crash protection system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button 
              onClick={runResourceLoadingTests}
              disabled={isRunning}
              className="w-full"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Running Tests...
                </>
              ) : (
                'Run Resource Loading Tests'
              )}
            </Button>

            {testResults.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Test Results</h3>
                {testResults.map((result, index) => (
                  <div 
                    key={index}
                    className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    {getStatusIcon(result.status)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{result.test}</span>
                        {getStatusBadge(result.status)}
                        <span className="text-sm text-gray-500">{result.timestamp}</span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {result.message}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                What This Test Validates:
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• Resource loading with proper error handling</li>
                <li>• Graceful fallback for failed external resources</li>
                <li>• Crash protection error tracking</li>
                <li>• Memory usage monitoring</li>
                <li>• Resource status tracking</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
