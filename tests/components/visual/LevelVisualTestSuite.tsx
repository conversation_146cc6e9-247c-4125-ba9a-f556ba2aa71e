/**
 * Level Visual Test Suite
 * 
 * Comprehensive visual testing for all level system components across different states,
 * sizes, variants, and edge cases.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Eye,
  Palette,
  Layers,
  Monitor,
  Smartphone,
  Tablet,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  LevelBadge,
  LevelProgressBar,
  XPGainNotification,
  LevelUpModal,
  LevelRewardsPanel,
  EnhancedUserProfileWithLevel,
  useLevelUpModal
} from '@/components/level'
import { mockUsers, mockLevelRewards } from '@/lib/testing/mockLevelData'
import { LevelTier } from '@/lib/levelSystem'

// ===== TYPES =====

interface TestCase {
  name: string
  description: string
  component: React.ReactNode
  status: 'pass' | 'warning' | 'info'
}

interface VisualTestGroup {
  title: string
  description: string
  tests: TestCase[]
}

// ===== COMPONENT =====

export const LevelVisualTestSuite: React.FC = () => {
  const [selectedViewport, setSelectedViewport] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const { showLevelUp, closeLevelUp, isOpen, levelUpData } = useLevelUpModal()

  // Test data
  const tiers: LevelTier[] = ['novice', 'intermediate', 'advanced', 'expert']
  const sizes = ['xs', 'sm', 'md', 'lg', 'xl'] as const
  const variants = ['solid', 'outline', 'glass', 'minimal'] as const

  // Viewport styles
  const viewportStyles = {
    desktop: 'w-full',
    tablet: 'w-[768px] mx-auto',
    mobile: 'w-[375px] mx-auto'
  }

  // Test groups
  const testGroups: VisualTestGroup[] = [
    {
      title: 'Level Badges',
      description: 'Testing all badge sizes, variants, and tier styles',
      tests: [
        {
          name: 'Size Variants',
          description: 'All badge sizes from XS to XL',
          status: 'pass',
          component: (
            <div className="flex items-center gap-4 flex-wrap">
              {sizes.map(size => (
                <div key={size} className="text-center">
                  <LevelBadge 
                    level={25} 
                    levelName="Keycap Curator" 
                    tier="intermediate" 
                    size={size}
                    showName={size === 'lg' || size === 'xl'}
                  />
                  <p className="text-xs text-gray-400 mt-1">{size.toUpperCase()}</p>
                </div>
              ))}
            </div>
          )
        },
        {
          name: 'Tier Styles',
          description: 'All tier color schemes and styling',
          status: 'pass',
          component: (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {tiers.map((tier, index) => (
                <div key={tier} className="text-center space-y-2">
                  <LevelBadge 
                    level={(index + 1) * 10} 
                    levelName={`${tier.charAt(0).toUpperCase() + tier.slice(1)} Level`}
                    tier={tier}
                    size="lg"
                    showName={true}
                    glow={tier === 'expert'}
                  />
                  <p className="text-xs text-gray-400 capitalize">{tier}</p>
                </div>
              ))}
            </div>
          )
        },
        {
          name: 'Badge Variants',
          description: 'Different visual styles',
          status: 'pass',
          component: (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {variants.map(variant => (
                <div key={variant} className="text-center space-y-2">
                  <LevelBadge 
                    level={35} 
                    levelName="Design Deity" 
                    tier="advanced" 
                    size="md"
                    variant={variant}
                  />
                  <p className="text-xs text-gray-400 capitalize">{variant}</p>
                </div>
              ))}
            </div>
          )
        }
      ]
    },
    {
      title: 'Progress Bars',
      description: 'Testing progress visualization components',
      tests: [
        {
          name: 'Linear Progress',
          description: 'Standard horizontal progress bars',
          status: 'pass',
          component: (
            <div className="space-y-4">
              {[
                { progress: 25, tier: 'novice' as LevelTier, level: 5 },
                { progress: 60, tier: 'intermediate' as LevelTier, level: 18 },
                { progress: 85, tier: 'advanced' as LevelTier, level: 32 },
                { progress: 100, tier: 'expert' as LevelTier, level: 50 }
              ].map((item, index) => (
                <LevelProgressBar
                  key={index}
                  currentXP={item.progress * 10}
                  nextLevelXP={1000}
                  progressPercentage={item.progress}
                  currentLevel={item.level}
                  nextLevel={item.level + 1}
                  tier={item.tier}
                  showXP={true}
                  showPercentage={true}
                />
              ))}
            </div>
          )
        },
        {
          name: 'Circular Progress',
          description: 'Circular progress indicators',
          status: 'pass',
          component: (
            <div className="flex justify-center gap-6">
              {[
                { progress: 30, tier: 'novice' as LevelTier, level: 8 },
                { progress: 70, tier: 'intermediate' as LevelTier, level: 22 },
                { progress: 90, tier: 'advanced' as LevelTier, level: 38 }
              ].map((item, index) => (
                <LevelProgressBar
                  key={index}
                  currentXP={item.progress * 5}
                  nextLevelXP={500}
                  progressPercentage={item.progress}
                  currentLevel={item.level}
                  tier={item.tier}
                  variant="circular"
                  size="md"
                  showPercentage={true}
                />
              ))}
            </div>
          )
        },
        {
          name: 'Compact Progress',
          description: 'Space-efficient progress display',
          status: 'pass',
          component: (
            <div className="space-y-3">
              {[
                { progress: 45, tier: 'novice' as LevelTier, level: 7 },
                { progress: 75, tier: 'intermediate' as LevelTier, level: 19 },
                { progress: 95, tier: 'advanced' as LevelTier, level: 35 }
              ].map((item, index) => (
                <LevelProgressBar
                  key={index}
                  currentXP={item.progress * 8}
                  nextLevelXP={800}
                  progressPercentage={item.progress}
                  currentLevel={item.level}
                  tier={item.tier}
                  variant="compact"
                  showXP={true}
                />
              ))}
            </div>
          )
        }
      ]
    },
    {
      title: 'Profile Integration',
      description: 'Testing profile components with level data',
      tests: [
        {
          name: 'Full Profile',
          description: 'Complete profile with all level features',
          status: 'pass',
          component: (
            <div className="max-w-2xl">
              <EnhancedUserProfileWithLevel 
                variant="full"
                showLevelInfo={true}
                showDetailedProgress={true}
                showFeatures={true}
              />
            </div>
          )
        },
        {
          name: 'Compact Profile',
          description: 'Condensed profile display',
          status: 'pass',
          component: (
            <div className="max-w-md">
              <EnhancedUserProfileWithLevel 
                variant="compact"
                showLevelInfo={true}
              />
            </div>
          )
        },
        {
          name: 'Minimal Profile',
          description: 'Minimal profile for tight spaces',
          status: 'pass',
          component: (
            <div className="max-w-xs">
              <EnhancedUserProfileWithLevel 
                variant="minimal"
              />
            </div>
          )
        }
      ]
    },
    {
      title: 'Interactive Elements',
      description: 'Testing interactive components and modals',
      tests: [
        {
          name: 'Level Up Modal',
          description: 'Celebration modal with rewards',
          status: 'info',
          component: (
            <div className="text-center">
              <Button 
                onClick={() => showLevelUp({
                  oldLevel: 24,
                  newLevel: 25,
                  newLevelName: 'Keycap Curator',
                  tier: 'intermediate',
                  xpGained: 150,
                  rewards: mockLevelRewards.filter(r => r.level === 25)
                })}
                className="bg-purple-600 hover:bg-purple-700"
              >
                Test Level Up Modal
              </Button>
              <p className="text-xs text-gray-400 mt-2">Click to see level up celebration</p>
            </div>
          )
        },
        {
          name: 'Rewards Panel',
          description: 'Level rewards management interface',
          status: 'pass',
          component: (
            <div className="max-w-4xl">
              <LevelRewardsPanel
                currentLevel={25}
                rewards={mockLevelRewards.slice(0, 6).map(reward => ({
                  ...reward,
                  canClaim: Math.random() > 0.5,
                  alreadyClaimed: Math.random() > 0.7
                }))}
                onClaimReward={async (id) => {
                  console.log('Claiming reward:', id)
                  await new Promise(resolve => setTimeout(resolve, 1000))
                }}
              />
            </div>
          )
        }
      ]
    },
    {
      title: 'Edge Cases',
      description: 'Testing edge cases and error states',
      tests: [
        {
          name: 'Max Level User',
          description: 'Level 50 with no next level',
          status: 'warning',
          component: (
            <div className="space-y-4">
              <LevelBadge 
                level={50} 
                levelName="Syndicaps Legend" 
                tier="expert" 
                size="xl"
                showName={true}
                glow={true}
              />
              <LevelProgressBar
                currentXP={2500}
                nextLevelXP={0}
                progressPercentage={100}
                currentLevel={50}
                tier="expert"
                showXP={true}
                showPercentage={true}
              />
            </div>
          )
        },
        {
          name: 'New User (Level 1)',
          description: 'Minimal XP and progress',
          status: 'info',
          component: (
            <div className="space-y-4">
              <LevelBadge 
                level={1} 
                levelName="Switch Novice" 
                tier="novice" 
                size="lg"
                showName={true}
              />
              <LevelProgressBar
                currentXP={25}
                nextLevelXP={100}
                progressPercentage={25}
                currentLevel={1}
                nextLevel={2}
                tier="novice"
                showXP={true}
                showPercentage={true}
              />
            </div>
          )
        },
        {
          name: 'Long Level Names',
          description: 'Testing text overflow handling',
          status: 'warning',
          component: (
            <div className="space-y-3">
              <LevelBadge 
                level={42} 
                levelName="Super Ultra Mega Keycap Design Master Supreme" 
                tier="expert" 
                size="lg"
                showName={true}
              />
              <LevelBadge 
                level={42} 
                levelName="Super Ultra Mega Keycap Design Master Supreme" 
                tier="expert" 
                size="md"
                showName={true}
              />
            </div>
          )
        }
      ]
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-blue-400" />
            Visual Test Suite
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Viewport:</span>
              <div className="flex gap-1">
                {[
                  { key: 'desktop', icon: Monitor, label: 'Desktop' },
                  { key: 'tablet', icon: Tablet, label: 'Tablet' },
                  { key: 'mobile', icon: Smartphone, label: 'Mobile' }
                ].map(({ key, icon: Icon, label }) => (
                  <Button
                    key={key}
                    onClick={() => setSelectedViewport(key as any)}
                    variant={selectedViewport === key ? 'default' : 'outline'}
                    size="sm"
                  >
                    <Icon className="w-4 h-4 mr-1" />
                    {label}
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="flex items-center gap-4 ml-auto">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-sm text-gray-400">Pass</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-gray-400">Warning</span>
              </div>
              <div className="flex items-center gap-2">
                <Info className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-gray-400">Info</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Groups */}
      <div className={viewportStyles[selectedViewport]}>
        <div className="space-y-8">
          {testGroups.map((group, groupIndex) => (
            <Card key={groupIndex} className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="w-5 h-5 text-purple-400" />
                  {group.title}
                </CardTitle>
                <p className="text-gray-400 text-sm">{group.description}</p>
              </CardHeader>
              <CardContent className="space-y-6">
                {group.tests.map((test, testIndex) => (
                  <motion.div
                    key={testIndex}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: testIndex * 0.1 }}
                    className="space-y-3"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-white flex items-center gap-2">
                          {test.status === 'pass' && <CheckCircle className="w-4 h-4 text-green-400" />}
                          {test.status === 'warning' && <AlertTriangle className="w-4 h-4 text-yellow-400" />}
                          {test.status === 'info' && <Info className="w-4 h-4 text-blue-400" />}
                          {test.name}
                        </h4>
                        <p className="text-sm text-gray-400">{test.description}</p>
                      </div>
                      <Badge 
                        className={
                          test.status === 'pass' ? 'bg-green-500/20 text-green-400' :
                          test.status === 'warning' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-blue-500/20 text-blue-400'
                        }
                      >
                        {test.status.toUpperCase()}
                      </Badge>
                    </div>
                    
                    <div className="p-6 bg-gray-800/30 rounded-lg border border-gray-700">
                      {test.component}
                    </div>
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Level Up Modal */}
      {levelUpData && (
        <LevelUpModal
          {...levelUpData}
          isOpen={isOpen}
          onClose={closeLevelUp}
        />
      )}
    </div>
  )
}

export default LevelVisualTestSuite
