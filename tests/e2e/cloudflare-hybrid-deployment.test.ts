/**
 * Comprehensive End-to-End Test Suite for Cloudflare Hybrid Deployment
 * 
 * This test suite validates the complete hybrid deployment system including:
 * - Infrastructure setup and configuration
 * - CDN optimization and feature flags
 * - R2 storage and image migration
 * - Workers functionality (image optimization and API caching)
 * - Performance optimization engine
 * - Monitoring dashboard
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { test, expect, Page, Browser } from '@playwright/test'
import { chromium } from 'playwright'

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  cloudflareWorkerImageUrl: process.env.CLOUDFLARE_WORKER_IMAGE_URL,
  cloudflareWorkerApiUrl: process.env.CLOUDFLARE_WORKER_API_URL,
  testTimeout: 30000,
  adminEmail: '<EMAIL>',
  adminPassword: process.env.TEST_ADMIN_PASSWORD || 'test-password'
}

// Test data
const TEST_IMAGES = [
  { name: 'test-keycap-1.jpg', size: '1920x1080', format: 'jpeg' },
  { name: 'test-keycap-2.png', size: '800x600', format: 'png' },
  { name: 'test-keycap-3.webp', size: '1200x800', format: 'webp' }
]

const TEST_API_ENDPOINTS = [
  '/api/products',
  '/api/auth/session',
  '/api/admin/dashboard/status',
  '/api/admin/dashboard/metrics'
]

// Helper functions
async function loginAsAdmin(page: Page) {
  await page.goto(`${TEST_CONFIG.baseUrl}/admin/login`)
  await page.fill('[data-testid="email-input"]', TEST_CONFIG.adminEmail)
  await page.fill('[data-testid="password-input"]', TEST_CONFIG.adminPassword)
  await page.click('[data-testid="login-button"]')
  await page.waitForURL('**/admin/dashboard')
}

async function uploadTestImage(page: Page, imageName: string) {
  const fileInput = page.locator('[data-testid="image-upload-input"]')
  await fileInput.setInputFiles(`tests/fixtures/images/${imageName}`)
  await page.waitForSelector('[data-testid="upload-success"]', { timeout: 10000 })
}

async function measurePagePerformance(page: Page) {
  const performanceMetrics = await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    return {
      loadTime: navigation.loadEventEnd - navigation.loadEventStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
      largestContentfulPaint: performance.getEntriesByName('largest-contentful-paint')[0]?.startTime || 0
    }
  })
  return performanceMetrics
}

// Test Suite: Infrastructure and Configuration
test.describe('Infrastructure and Configuration', () => {
  test('should validate Cloudflare infrastructure setup', async ({ page }) => {
    // Test Cloudflare Workers health endpoints
    const imageWorkerResponse = await page.request.get(`${TEST_CONFIG.cloudflareWorkerImageUrl}/health`)
    expect(imageWorkerResponse.status()).toBe(200)

    const apiWorkerResponse = await page.request.get(`${TEST_CONFIG.cloudflareWorkerApiUrl}/health`)
    expect(apiWorkerResponse.status()).toBe(200)

    // Validate worker responses
    const imageWorkerData = await imageWorkerResponse.json()
    expect(imageWorkerData.status).toBe('healthy')

    const apiWorkerData = await apiWorkerResponse.json()
    expect(apiWorkerData.status).toBe('healthy')
  })

  test('should validate CDN configuration and headers', async ({ page }) => {
    const response = await page.goto(TEST_CONFIG.baseUrl)
    
    // Check security headers
    expect(response?.headers()['x-frame-options']).toBe('DENY')
    expect(response?.headers()['x-content-type-options']).toBe('nosniff')
    expect(response?.headers()['referrer-policy']).toBe('strict-origin-when-cross-origin')
    
    // Check cache headers for static assets
    await page.goto(`${TEST_CONFIG.baseUrl}/_next/static/css/app.css`)
    const cssResponse = await page.waitForResponse('**/*.css')
    expect(cssResponse.headers()['cache-control']).toContain('public')
  })

  test('should validate environment variables and configuration', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/system/config`)
    
    // Check that critical configuration is loaded
    await expect(page.locator('[data-testid="cloudflare-config-status"]')).toHaveText('Connected')
    await expect(page.locator('[data-testid="r2-config-status"]')).toHaveText('Active')
    await expect(page.locator('[data-testid="firebase-config-status"]')).toHaveText('Connected')
  })
})

// Test Suite: Feature Flags and CDN Optimization
test.describe('Feature Flags and CDN Optimization', () => {
  test('should manage feature flags correctly', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    await page.click('[data-testid="feature-flags-tab"]')

    // Test feature flag toggle
    const hybridStorageFlag = page.locator('[data-testid="feature-flag-hybrid-r2-storage"]')
    const initialState = await hybridStorageFlag.locator('[data-testid="toggle-switch"]').isChecked()
    
    await hybridStorageFlag.locator('[data-testid="toggle-switch"]').click()
    await page.waitForTimeout(1000) // Wait for API call
    
    const newState = await hybridStorageFlag.locator('[data-testid="toggle-switch"]').isChecked()
    expect(newState).toBe(!initialState)

    // Test rollout percentage adjustment
    await hybridStorageFlag.locator('[data-testid="rollout-slider"]').fill('75')
    await page.waitForTimeout(1000)
    
    const rolloutValue = await hybridStorageFlag.locator('[data-testid="rollout-percentage"]').textContent()
    expect(rolloutValue).toBe('75%')
  })

  test('should validate CDN caching behavior', async ({ page }) => {
    // Test cache hit/miss for static assets
    const response1 = await page.goto(`${TEST_CONFIG.baseUrl}/images/logo.png`)
    const cacheStatus1 = response1?.headers()['cf-cache-status']
    
    // Second request should be cached
    const response2 = await page.goto(`${TEST_CONFIG.baseUrl}/images/logo.png`)
    const cacheStatus2 = response2?.headers()['cf-cache-status']
    
    expect(['HIT', 'MISS', 'DYNAMIC'].includes(cacheStatus1 || '')).toBeTruthy()
    expect(['HIT', 'MISS', 'DYNAMIC'].includes(cacheStatus2 || '')).toBeTruthy()
  })

  test('should validate cache purging functionality', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/cache-management`)
    
    // Test cache purge
    await page.click('[data-testid="purge-all-cache-button"]')
    await expect(page.locator('[data-testid="purge-success-message"]')).toBeVisible()
    
    // Verify cache purge API response
    const purgeResponse = await page.waitForResponse('**/api/admin/cache/purge')
    expect(purgeResponse.status()).toBe(200)
  })
})

// Test Suite: R2 Storage and Image Migration
test.describe('R2 Storage and Image Migration', () => {
  test('should upload images to R2 storage', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/media`)

    for (const testImage of TEST_IMAGES) {
      await uploadTestImage(page, testImage.name)
      
      // Verify image appears in media library
      await expect(page.locator(`[data-testid="image-${testImage.name}"]`)).toBeVisible()
      
      // Verify R2 URL format
      const imageUrl = await page.locator(`[data-testid="image-${testImage.name}"] img`).getAttribute('src')
      expect(imageUrl).toContain('r2.cloudflarestorage.com')
    }
  })

  test('should handle hybrid storage fallback', async ({ page }) => {
    await loginAsAdmin(page)
    
    // Disable R2 storage feature flag
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    await page.click('[data-testid="feature-flags-tab"]')
    await page.locator('[data-testid="feature-flag-hybrid-r2-storage"] [data-testid="toggle-switch"]').uncheck()
    
    // Upload image - should fallback to Firebase
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/media`)
    await uploadTestImage(page, TEST_IMAGES[0].name)
    
    const imageUrl = await page.locator(`[data-testid="image-${TEST_IMAGES[0].name}"] img`).getAttribute('src')
    expect(imageUrl).toContain('firebasestorage.googleapis.com')
    
    // Re-enable R2 storage
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    await page.click('[data-testid="feature-flags-tab"]')
    await page.locator('[data-testid="feature-flag-hybrid-r2-storage"] [data-testid="toggle-switch"]').check()
  })

  test('should execute image migration successfully', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/migration`)
    
    // Start migration process
    await page.click('[data-testid="start-migration-button"]')
    
    // Wait for migration to complete (with timeout)
    await expect(page.locator('[data-testid="migration-status"]')).toHaveText('Completed', { timeout: 60000 })
    
    // Verify migration results
    const migratedCount = await page.locator('[data-testid="migrated-images-count"]').textContent()
    expect(parseInt(migratedCount || '0')).toBeGreaterThan(0)
    
    // Verify no errors
    const errorCount = await page.locator('[data-testid="migration-errors-count"]').textContent()
    expect(errorCount).toBe('0')
  })
})

// Test Suite: Workers Functionality
test.describe('Workers Functionality', () => {
  test('should optimize images through image worker', async ({ page }) => {
    const testImageUrl = `${TEST_CONFIG.cloudflareWorkerImageUrl}/optimize`
    
    // Test different optimization parameters
    const optimizationTests = [
      { width: 400, height: 300, format: 'webp', quality: 80 },
      { width: 800, height: 600, format: 'jpeg', quality: 90 },
      { width: 200, height: 200, format: 'png', quality: 85 }
    ]

    for (const params of optimizationTests) {
      const url = `${testImageUrl}?width=${params.width}&height=${params.height}&format=${params.format}&quality=${params.quality}&url=${encodeURIComponent('https://example.com/test-image.jpg')}`
      
      const response = await page.request.get(url)
      expect(response.status()).toBe(200)
      expect(response.headers()['content-type']).toContain(`image/${params.format === 'jpeg' ? 'jpeg' : params.format}`)
    }
  })

  test('should cache API responses through API worker', async ({ page }) => {
    for (const endpoint of TEST_API_ENDPOINTS) {
      // First request
      const response1 = await page.request.get(`${TEST_CONFIG.cloudflareWorkerApiUrl}${endpoint}`)
      expect(response1.status()).toBe(200)
      
      const cacheStatus1 = response1.headers()['cf-cache-status']
      
      // Second request should be cached
      const response2 = await page.request.get(`${TEST_CONFIG.cloudflareWorkerApiUrl}${endpoint}`)
      expect(response2.status()).toBe(200)
      
      const cacheStatus2 = response2.headers()['cf-cache-status']
      
      // Verify caching behavior
      expect(['HIT', 'MISS', 'DYNAMIC'].includes(cacheStatus1 || '')).toBeTruthy()
      expect(['HIT', 'MISS', 'DYNAMIC'].includes(cacheStatus2 || '')).toBeTruthy()
    }
  })

  test('should handle rate limiting correctly', async ({ page }) => {
    const testEndpoint = `${TEST_CONFIG.cloudflareWorkerApiUrl}/api/test-rate-limit`
    
    // Make multiple rapid requests to trigger rate limiting
    const requests = Array.from({ length: 20 }, () => page.request.get(testEndpoint))
    const responses = await Promise.all(requests)
    
    // Check that some requests are rate limited
    const rateLimitedResponses = responses.filter(r => r.status() === 429)
    expect(rateLimitedResponses.length).toBeGreaterThan(0)
    
    // Verify rate limit headers
    const rateLimitResponse = rateLimitedResponses[0]
    expect(rateLimitResponse.headers()['x-ratelimit-limit']).toBeDefined()
    expect(rateLimitResponse.headers()['x-ratelimit-remaining']).toBeDefined()
  })
})

// Test Suite: Performance Optimization
test.describe('Performance Optimization', () => {
  test('should execute automated optimization rules', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/optimization`)
    
    // Trigger manual optimization
    await page.click('[data-testid="run-optimization-button"]')
    
    // Wait for optimization to complete
    await expect(page.locator('[data-testid="optimization-status"]')).toHaveText('Completed', { timeout: 30000 })
    
    // Verify optimization results
    const optimizationsApplied = await page.locator('[data-testid="optimizations-applied-count"]').textContent()
    expect(parseInt(optimizationsApplied || '0')).toBeGreaterThanOrEqual(0)
    
    // Check performance improvement metrics
    const performanceImprovement = await page.locator('[data-testid="performance-improvement"]').textContent()
    expect(performanceImprovement).toMatch(/\d+(\.\d+)?%/)
  })

  test('should track optimization impact', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/optimization/reports`)
    
    // Verify optimization impact tracking
    await expect(page.locator('[data-testid="optimization-impact-chart"]')).toBeVisible()
    
    // Check that metrics are being collected
    const totalOptimizations = await page.locator('[data-testid="total-optimizations"]').textContent()
    expect(parseInt(totalOptimizations || '0')).toBeGreaterThanOrEqual(0)
  })
})

// Test Suite: Monitoring Dashboard
test.describe('Monitoring Dashboard', () => {
  test('should display real-time system status', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    
    // Verify system status indicators
    await expect(page.locator('[data-testid="system-status-overall"]')).toBeVisible()
    await expect(page.locator('[data-testid="workers-status"]')).toBeVisible()
    await expect(page.locator('[data-testid="r2-storage-status"]')).toBeVisible()
    await expect(page.locator('[data-testid="firebase-status"]')).toBeVisible()
    
    // Verify status values are valid
    const overallStatus = await page.locator('[data-testid="system-status-overall"]').textContent()
    expect(['healthy', 'warning', 'critical'].includes(overallStatus?.toLowerCase() || '')).toBeTruthy()
  })

  test('should display performance metrics', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    await page.click('[data-testid="performance-tab"]')
    
    // Verify performance charts are loaded
    await expect(page.locator('[data-testid="response-time-chart"]')).toBeVisible()
    await expect(page.locator('[data-testid="core-web-vitals-chart"]')).toBeVisible()
    await expect(page.locator('[data-testid="geographic-performance-chart"]')).toBeVisible()
    
    // Verify metrics have valid values
    const responseTime = await page.locator('[data-testid="avg-response-time"]').textContent()
    expect(parseFloat(responseTime?.replace('ms', '') || '0')).toBeGreaterThan(0)
  })

  test('should display usage and cost monitoring', async ({ page }) => {
    await loginAsAdmin(page)
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    await page.click('[data-testid="usage-cost-tab"]')
    
    // Verify cost breakdown
    await expect(page.locator('[data-testid="current-month-cost"]')).toBeVisible()
    await expect(page.locator('[data-testid="projected-cost"]')).toBeVisible()
    await expect(page.locator('[data-testid="budget-usage"]')).toBeVisible()
    
    // Verify service breakdown
    await expect(page.locator('[data-testid="r2-storage-cost"]')).toBeVisible()
    await expect(page.locator('[data-testid="bandwidth-cost"]')).toBeVisible()
    await expect(page.locator('[data-testid="workers-cost"]')).toBeVisible()
  })
})

// Test Suite: End-to-End User Scenarios
test.describe('End-to-End User Scenarios', () => {
  test('should handle complete image upload and optimization workflow', async ({ page }) => {
    // User uploads image
    await page.goto(`${TEST_CONFIG.baseUrl}/shop/upload`)
    await uploadTestImage(page, TEST_IMAGES[0].name)
    
    // Image is processed and optimized
    await page.waitForSelector('[data-testid="image-processed"]', { timeout: 15000 })
    
    // User views optimized image
    await page.goto(`${TEST_CONFIG.baseUrl}/gallery`)
    await expect(page.locator(`[data-testid="image-${TEST_IMAGES[0].name}"]`)).toBeVisible()
    
    // Verify image is served through workers
    const imageElement = page.locator(`[data-testid="image-${TEST_IMAGES[0].name}"] img`)
    const imageSrc = await imageElement.getAttribute('src')
    expect(imageSrc).toContain(TEST_CONFIG.cloudflareWorkerImageUrl)
  })

  test('should handle API caching for product browsing', async ({ page }) => {
    // Browse products (triggers API caching)
    await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
    await page.waitForSelector('[data-testid="product-grid"]')
    
    // Measure initial load time
    const initialMetrics = await measurePagePerformance(page)
    
    // Navigate away and back (should use cached data)
    await page.goto(`${TEST_CONFIG.baseUrl}/`)
    await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
    await page.waitForSelector('[data-testid="product-grid"]')
    
    // Measure cached load time
    const cachedMetrics = await measurePagePerformance(page)
    
    // Cached version should be faster
    expect(cachedMetrics.loadTime).toBeLessThanOrEqual(initialMetrics.loadTime)
  })

  test('should handle system failover scenarios', async ({ page }) => {
    await loginAsAdmin(page)
    
    // Simulate R2 storage failure by disabling feature flag
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    await page.click('[data-testid="feature-flags-tab"]')
    await page.locator('[data-testid="feature-flag-hybrid-r2-storage"] [data-testid="emergency-kill-button"]').click()
    
    // Confirm emergency kill
    await page.click('[data-testid="confirm-emergency-kill"]')
    
    // Verify system falls back to Firebase
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/media`)
    await uploadTestImage(page, TEST_IMAGES[1].name)
    
    const imageUrl = await page.locator(`[data-testid="image-${TEST_IMAGES[1].name}"] img`).getAttribute('src')
    expect(imageUrl).toContain('firebasestorage.googleapis.com')
    
    // Restore R2 storage
    await page.goto(`${TEST_CONFIG.baseUrl}/admin/dashboard`)
    await page.click('[data-testid="feature-flags-tab"]')
    await page.locator('[data-testid="feature-flag-hybrid-r2-storage"] [data-testid="toggle-switch"]').check()
  })
})

// Performance benchmarks
test.describe('Performance Benchmarks', () => {
  test('should meet Core Web Vitals thresholds', async ({ page }) => {
    await page.goto(TEST_CONFIG.baseUrl)
    
    const metrics = await measurePagePerformance(page)
    
    // Core Web Vitals thresholds
    expect(metrics.largestContentfulPaint).toBeLessThan(2500) // LCP < 2.5s
    expect(metrics.firstContentfulPaint).toBeLessThan(1800)   // FCP < 1.8s
    expect(metrics.loadTime).toBeLessThan(3000)               // Load time < 3s
  })

  test('should achieve target response times', async ({ page }) => {
    const endpoints = [
      '/',
      '/shop',
      '/api/products',
      '/api/auth/session'
    ]

    for (const endpoint of endpoints) {
      const startTime = Date.now()
      const response = await page.request.get(`${TEST_CONFIG.baseUrl}${endpoint}`)
      const responseTime = Date.now() - startTime
      
      expect(response.status()).toBe(200)
      expect(responseTime).toBeLessThan(1000) // < 1 second response time
    }
  })
})
