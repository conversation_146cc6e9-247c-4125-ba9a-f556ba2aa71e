/**
 * End-to-End Tests for Cloudflare Hybrid Deployment
 * 
 * Comprehensive E2E tests to validate the complete hybrid deployment
 * functionality including performance, reliability, and user experience.
 */

import { test, expect, Page } from '@playwright/test'

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'https://staging.syndicaps.com',
  timeout: 30000,
  performanceThresholds: {
    imageLoadTime: 1000, // ms
    apiResponseTime: 2000, // ms
    pageLoadTime: 3000, // ms
    cacheHitRate: 70 // %
  }
}

test.describe('Cloudflare Hybrid Deployment E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up performance monitoring
    await page.addInitScript(() => {
      window.performanceMetrics = {
        imageLoadTimes: [],
        apiResponseTimes: [],
        cacheHits: 0,
        cacheMisses: 0
      }
    })
  })

  test.describe('Image Optimization and R2 Storage', () => {
    test('should load optimized images from R2 storage', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
      
      // Wait for images to load
      await page.waitForLoadState('networkidle')
      
      // Check that images are loaded from R2
      const imageRequests = await page.evaluate(() => {
        const images = Array.from(document.querySelectorAll('img'))
        return images.map(img => ({
          src: img.src,
          naturalWidth: img.naturalWidth,
          naturalHeight: img.naturalHeight,
          complete: img.complete
        }))
      })
      
      // Verify R2 URLs
      const r2Images = imageRequests.filter(img => 
        img.src.includes('r2.cloudflarestorage.com') || 
        img.src.includes('imagedelivery.net')
      )
      
      expect(r2Images.length).toBeGreaterThan(0)
      
      // Verify images are loaded
      r2Images.forEach(img => {
        expect(img.complete).toBe(true)
        expect(img.naturalWidth).toBeGreaterThan(0)
        expect(img.naturalHeight).toBeGreaterThan(0)
      })
    })

    test('should serve different image formats based on browser support', async ({ page, browserName }) => {
      await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
      
      // Get image sources
      const imageSources = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('img')).map(img => img.src)
      })
      
      // Check for modern image formats in supported browsers
      if (browserName === 'chromium') {
        const webpImages = imageSources.filter(src => src.includes('f=webp') || src.includes('.webp'))
        expect(webpImages.length).toBeGreaterThan(0)
      }
    })

    test('should handle image optimization parameters', async ({ page }) => {
      // Test different image sizes
      const testCases = [
        { width: 300, height: 200, quality: 80 },
        { width: 600, height: 400, quality: 90 },
        { width: 150, height: 150, quality: 70 }
      ]
      
      for (const testCase of testCases) {
        const imageUrl = `${TEST_CONFIG.baseUrl}/images/test-product.jpg?w=${testCase.width}&h=${testCase.height}&q=${testCase.quality}`
        
        const response = await page.request.get(imageUrl)
        expect(response.status()).toBe(200)
        expect(response.headers()['content-type']).toContain('image/')
        
        // Verify image dimensions (would require image processing library in real test)
        const contentLength = parseInt(response.headers()['content-length'] || '0')
        expect(contentLength).toBeGreaterThan(0)
      }
    })
  })

  test.describe('API Caching and Workers', () => {
    test('should cache API responses effectively', async ({ page }) => {
      // First request - should be cache MISS
      const firstResponse = await page.request.get(`${TEST_CONFIG.baseUrl}/api/products`)
      expect(firstResponse.status()).toBe(200)
      
      const firstCacheStatus = firstResponse.headers()['cf-cache-status']
      expect(['MISS', 'DYNAMIC']).toContain(firstCacheStatus)
      
      // Second request - should be cache HIT
      await page.waitForTimeout(1000) // Small delay
      const secondResponse = await page.request.get(`${TEST_CONFIG.baseUrl}/api/products`)
      expect(secondResponse.status()).toBe(200)
      
      const secondCacheStatus = secondResponse.headers()['cf-cache-status']
      expect(['HIT', 'REVALIDATED']).toContain(secondCacheStatus)
    })

    test('should respect cache control headers', async ({ page }) => {
      // Test no-cache endpoint (admin routes)
      const adminResponse = await page.request.get(`${TEST_CONFIG.baseUrl}/api/admin/health`, {
        headers: { 'Authorization': 'Bearer test-token' }
      })
      
      const cacheControl = adminResponse.headers()['cache-control']
      expect(cacheControl).toContain('no-cache')
    })

    test('should handle rate limiting', async ({ page }) => {
      const requests = []
      
      // Make multiple rapid requests
      for (let i = 0; i < 10; i++) {
        requests.push(page.request.get(`${TEST_CONFIG.baseUrl}/api/test-rate-limit`))
      }
      
      const responses = await Promise.all(requests)
      
      // Check that some requests succeed and rate limiting is applied if needed
      const successfulRequests = responses.filter(r => r.status() === 200)
      const rateLimitedRequests = responses.filter(r => r.status() === 429)
      
      expect(successfulRequests.length).toBeGreaterThan(0)
      
      // If rate limiting is active, check headers
      if (rateLimitedRequests.length > 0) {
        const rateLimitResponse = rateLimitedRequests[0]
        expect(rateLimitResponse.headers()['retry-after']).toBeDefined()
      }
    })
  })

  test.describe('Performance Monitoring', () => {
    test('should meet performance thresholds', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      expect(loadTime).toBeLessThan(TEST_CONFIG.performanceThresholds.pageLoadTime)
      
      // Check Core Web Vitals
      const webVitals = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const vitals = {
              lcp: 0,
              fid: 0,
              cls: 0
            }
            
            entries.forEach((entry) => {
              if (entry.entryType === 'largest-contentful-paint') {
                vitals.lcp = entry.startTime
              } else if (entry.entryType === 'first-input') {
                vitals.fid = entry.processingStart - entry.startTime
              } else if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                vitals.cls += entry.value
              }
            })
            
            resolve(vitals)
          }).observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] })
          
          // Fallback timeout
          setTimeout(() => resolve({ lcp: 0, fid: 0, cls: 0 }), 5000)
        })
      })
      
      // Validate Core Web Vitals
      if (webVitals.lcp > 0) {
        expect(webVitals.lcp).toBeLessThan(2500) // Good LCP threshold
      }
      if (webVitals.fid > 0) {
        expect(webVitals.fid).toBeLessThan(100) // Good FID threshold
      }
      if (webVitals.cls > 0) {
        expect(webVitals.cls).toBeLessThan(0.1) // Good CLS threshold
      }
    })

    test('should track performance metrics', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseUrl}/admin/cloudflare-dashboard`)
      
      // Wait for dashboard to load
      await page.waitForSelector('[data-testid="performance-metrics"]', { timeout: 10000 })
      
      // Check that metrics are displayed
      const cacheHitRate = await page.textContent('[data-testid="cache-hit-rate"]')
      expect(cacheHitRate).toMatch(/\d+%/)
      
      const imageLoadTime = await page.textContent('[data-testid="image-load-time"]')
      expect(imageLoadTime).toMatch(/\d+ms/)
      
      const apiResponseTime = await page.textContent('[data-testid="api-response-time"]')
      expect(apiResponseTime).toMatch(/\d+ms/)
    })
  })

  test.describe('Feature Flags and Rollout', () => {
    test('should respect feature flag configurations', async ({ page }) => {
      // Test with R2 storage disabled
      await page.addInitScript(() => {
        localStorage.setItem('feature-flags-override', JSON.stringify({
          USE_R2_STORAGE: false
        }))
      })
      
      await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
      await page.waitForLoadState('networkidle')
      
      // Check that images are served from Firebase
      const imageRequests = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('img')).map(img => img.src)
      })
      
      const firebaseImages = imageRequests.filter(src => 
        src.includes('firebasestorage.googleapis.com')
      )
      
      expect(firebaseImages.length).toBeGreaterThan(0)
    })

    test('should handle gradual rollout', async ({ page }) => {
      // Simulate different user IDs for rollout testing
      const testUsers = ['user1', 'user2', 'user3', 'user4', 'user5']
      const r2Users = []
      const firebaseUsers = []
      
      for (const userId of testUsers) {
        await page.addInitScript((id) => {
          localStorage.setItem('test-user-id', id)
        }, userId)
        
        await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
        await page.waitForLoadState('networkidle')
        
        const imageRequests = await page.evaluate(() => {
          return Array.from(document.querySelectorAll('img')).map(img => img.src)
        })
        
        const hasR2Images = imageRequests.some(src => 
          src.includes('r2.cloudflarestorage.com') || 
          src.includes('imagedelivery.net')
        )
        
        if (hasR2Images) {
          r2Users.push(userId)
        } else {
          firebaseUsers.push(userId)
        }
      }
      
      // Verify that rollout is working (some users get R2, some get Firebase)
      expect(r2Users.length + firebaseUsers.length).toBe(testUsers.length)
    })
  })

  test.describe('Error Handling and Resilience', () => {
    test('should fallback gracefully when R2 is unavailable', async ({ page }) => {
      // Simulate R2 unavailability by blocking R2 requests
      await page.route('**/*r2.cloudflarestorage.com/**', route => {
        route.abort()
      })
      
      await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
      await page.waitForLoadState('networkidle')
      
      // Check that images still load (from Firebase fallback)
      const images = await page.locator('img').all()
      
      for (const img of images) {
        const isLoaded = await img.evaluate((el: HTMLImageElement) => el.complete && el.naturalWidth > 0)
        expect(isLoaded).toBe(true)
      }
    })

    test('should handle worker failures gracefully', async ({ page }) => {
      // Simulate worker failure by blocking worker routes
      await page.route('**/api/**', route => {
        if (route.request().headers()['cf-worker']) {
          route.abort()
        } else {
          route.continue()
        }
      })
      
      await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
      
      // API should still work (direct to Firebase)
      const response = await page.request.get(`${TEST_CONFIG.baseUrl}/api/products`)
      expect(response.status()).toBe(200)
    })

    test('should maintain functionality during cache purge', async ({ page }) => {
      // First, load the page normally
      await page.goto(`${TEST_CONFIG.baseUrl}/shop`)
      await page.waitForLoadState('networkidle')
      
      // Simulate cache purge by adding cache-busting headers
      await page.setExtraHTTPHeaders({
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      })
      
      // Reload and verify functionality
      await page.reload()
      await page.waitForLoadState('networkidle')
      
      // Check that page still loads correctly
      const title = await page.title()
      expect(title).toContain('Syndicaps')
      
      // Check that images still load
      const imageCount = await page.locator('img').count()
      expect(imageCount).toBeGreaterThan(0)
    })
  })

  test.describe('Admin Dashboard Integration', () => {
    test('should display real-time metrics in admin dashboard', async ({ page }) => {
      // Login as admin (assuming auth is set up)
      await page.goto(`${TEST_CONFIG.baseUrl}/admin/login`)
      // Add login steps here
      
      await page.goto(`${TEST_CONFIG.baseUrl}/admin/cloudflare-dashboard`)
      
      // Wait for dashboard to load
      await page.waitForSelector('[data-testid="cloudflare-dashboard"]')
      
      // Check that all metric cards are present
      await expect(page.locator('[data-testid="cache-hit-rate"]')).toBeVisible()
      await expect(page.locator('[data-testid="r2-storage-usage"]')).toBeVisible()
      await expect(page.locator('[data-testid="total-requests"]')).toBeVisible()
      await expect(page.locator('[data-testid="optimizations-count"]')).toBeVisible()
      
      // Check that charts are rendered
      await expect(page.locator('[data-testid="performance-chart"]')).toBeVisible()
      await expect(page.locator('[data-testid="usage-chart"]')).toBeVisible()
    })

    test('should allow feature flag management', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseUrl}/admin/cloudflare-dashboard`)
      
      // Navigate to features tab
      await page.click('[data-testid="features-tab"]')
      
      // Check that feature flags are displayed
      await expect(page.locator('[data-testid="r2-storage-flag"]')).toBeVisible()
      await expect(page.locator('[data-testid="workers-flag"]')).toBeVisible()
      
      // Test toggling a feature flag
      const initialState = await page.locator('[data-testid="r2-storage-toggle"]').isChecked()
      await page.click('[data-testid="r2-storage-toggle"]')
      
      // Verify state changed
      const newState = await page.locator('[data-testid="r2-storage-toggle"]').isChecked()
      expect(newState).toBe(!initialState)
    })
  })
})

// Helper function to measure performance
async function measurePerformance(page: Page, url: string) {
  const startTime = Date.now()
  
  await page.goto(url)
  await page.waitForLoadState('networkidle')
  
  const endTime = Date.now()
  const loadTime = endTime - startTime
  
  const performanceMetrics = await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
    }
  })
  
  return {
    totalLoadTime: loadTime,
    ...performanceMetrics
  }
}
