/**
 * Improved Community Discover E2E Tests
 * 
 * Enhanced test suite using:
 * - Page Object Model
 * - Custom fixtures
 * - Performance measurement
 * - Type-safe configuration
 * - Comprehensive test scenarios
 * 
 * <AUTHOR> Team
 */

import { test, expect } from './fixtures/community-fixtures'
import { COMMUNITY_TEST_CONFIG } from './config/community-test-config'
import { DiscoverPage, CommunityLayoutPage, SearchPage, RealtimePage } from './utils/community-page-objects'

const { performance, timeouts, mockData } = COMMUNITY_TEST_CONFIG

test.describe('Community Discover - Improved E2E Tests', () => {
  
  test.describe('Page Loading and Performance', () => {
    test('loads discover page within performance threshold', async ({ performancePage }) => {
      const discoverPage = new DiscoverPage(performancePage)
      
      // Measure page load performance
      await performancePage.evaluate(() => {
        window.testPerformance.mark('page-load-start')
      })
      
      await discoverPage.goto()
      
      const loadTime = await performancePage.evaluate(() => {
        window.testPerformance.mark('page-load-end')
        return window.testPerformance.measure('page-load-time', 'page-load-start', 'page-load-end')
      })
      
      // Verify performance threshold
      expect(loadTime).toBeLessThan(performance.pageLoad)
      
      // Verify page structure
      await discoverPage.verifyElementVisible('discover-page')
      await discoverPage.verifyElementVisible('community-hero')
      await discoverPage.verifyElementVisible('community-search')
      await discoverPage.verifyElementVisible('featured-content')
      
      console.log(`Discover page loaded in ${loadTime}ms`)
    })

    test('displays proper page hierarchy and accessibility', async ({ authenticatedPage }) => {
      const discoverPage = new DiscoverPage(authenticatedPage)
      await discoverPage.goto()
      
      // Check heading hierarchy
      const h1Count = await authenticatedPage.locator('h1').count()
      const h2Count = await authenticatedPage.locator('h2').count()
      
      expect(h1Count).toBe(1)
      expect(h2Count).toBeGreaterThan(0)
      
      // Check ARIA attributes
      await expect(authenticatedPage.locator('[data-testid="community-search"]')).toHaveAttribute('role', 'search')
      await expect(authenticatedPage.locator('[data-testid="search-input"]')).toHaveAttribute('aria-label')
      
      // Verify tab structure
      const layout = new CommunityLayoutPage(authenticatedPage)
      await layout.verifyTabsVisible()
      await layout.verifyActiveTab('discover')
    })

    test('handles responsive design across viewports', async ({ authenticatedPage }) => {
      const discoverPage = new DiscoverPage(authenticatedPage)
      
      // Test mobile viewport
      await authenticatedPage.setViewportSize({ width: 375, height: 667 })
      await discoverPage.goto()
      
      await discoverPage.verifyElementVisible('discover-page')
      await discoverPage.verifyElementVisible('community-hero')
      
      // Test tablet viewport
      await authenticatedPage.setViewportSize({ width: 768, height: 1024 })
      await authenticatedPage.reload()
      
      await discoverPage.verifyElementVisible('discover-page')
      await discoverPage.verifyElementVisible('featured-content')
      
      // Test desktop viewport
      await authenticatedPage.setViewportSize({ width: 1920, height: 1080 })
      await authenticatedPage.reload()
      
      await discoverPage.verifyElementVisible('discover-page')
      await discoverPage.verifyFeaturedContent()
    })
  })

  test.describe('Community Statistics', () => {
    test('displays live community statistics with proper formatting', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      await discoverPage.goto()
      await discoverPage.waitForStatsLoad()
      
      // Verify stats display with mock data
      await discoverPage.verifyStats(mockData.stats)
      
      // Verify stats are properly formatted (e.g., comma-separated numbers)
      const membersText = await discoverPage.totalMembers.textContent()
      expect(membersText).toMatch(/\d{1,3}(,\d{3})*/) // Should have comma separators
    })

    test('updates statistics in real-time', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      const realtimePage = new RealtimePage(mockDataPage)
      
      await discoverPage.goto()
      await realtimePage.waitForConnection()
      await discoverPage.waitForStatsLoad()
      
      // Get initial stats
      await discoverPage.verifyElementText('total-members', '2847')
      
      // Simulate real-time update
      await realtimePage.simulateRealtimeUpdate('stats-update', {
        ...mockData.stats,
        totalMembers: 2848
      })
      
      // Verify update
      await discoverPage.verifyElementText('total-members', '2848')
      
      // Verify animation class is applied
      await expect(discoverPage.totalMembers).toHaveClass(/updating/, { timeout: 1000 })
    })

    test('handles stats loading errors gracefully', async ({ authenticatedPage }) => {
      // Mock API failure
      await authenticatedPage.route('**/api/community/stats', route => route.abort())
      
      const discoverPage = new DiscoverPage(authenticatedPage)
      await discoverPage.goto()
      
      // Should show error state or fallback data
      const hasErrorState = await authenticatedPage.locator('[data-testid="stats-error"]').isVisible()
      const hasLoadingState = await authenticatedPage.locator('[data-testid="stats-loading"]').isVisible()
      const hasFallbackData = await discoverPage.totalMembers.isVisible()
      
      expect(hasErrorState || hasLoadingState || hasFallbackData).toBe(true)
      
      // Main page structure should still be intact
      await discoverPage.verifyElementVisible('discover-page')
    })
  })

  test.describe('Search Functionality', () => {
    test('performs search with debouncing and proper results', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      const searchPage = new SearchPage(mockDataPage)
      
      await discoverPage.goto()
      
      // Measure search performance
      await mockDataPage.evaluate(() => {
        window.testPerformance.mark('search-start')
      })
      
      await discoverPage.performSearch('artisan')
      
      const searchTime = await mockDataPage.evaluate(() => {
        window.testPerformance.mark('search-end')
        return window.testPerformance.measure('search-time', 'search-start', 'search-end')
      })
      
      // Verify performance
      expect(searchTime).toBeLessThan(performance.searchResponse)
      
      // Verify results
      await discoverPage.verifySearchResults(1) // Mock data has 1 matching submission
      
      console.log(`Search completed in ${searchTime}ms`)
    })

    test('filters search results by content type', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      const searchPage = new SearchPage(mockDataPage)
      
      await discoverPage.goto()
      await discoverPage.performSearch('test')
      
      // Should show mixed results initially
      await discoverPage.verifySearchResults(2) // 2 test submissions in mock data
      
      // Filter by submissions only
      await searchPage.selectFilter('submissions')
      await searchPage.verifyFilterActive('submissions')
      
      // Verify only submission results are shown
      await searchPage.verifyResultType('Submission')
    })

    test('handles trending searches interaction', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      
      await discoverPage.goto()
      
      // Verify trending searches are visible
      await discoverPage.verifyElementVisible('trending-searches')
      
      // Click first trending search
      await discoverPage.clickTrendingSearch(0)
      
      // Should populate search and show results
      await discoverPage.verifyElementVisible('search-results')
    })

    test('shows no results state appropriately', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      const searchPage = new SearchPage(mockDataPage)
      
      await discoverPage.goto()
      await discoverPage.performSearch('nonexistentquery12345')
      
      // Should show no results message
      await searchPage.verifyElementVisible('no-results')
      await searchPage.verifyElementText('no-results', 'No results found')
    })
  })

  test.describe('Featured Content', () => {
    test('displays featured submissions with proper data', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      await discoverPage.goto()
      
      await discoverPage.verifyFeaturedContent()
      
      // Verify submission cards have required data
      const firstCard = mockDataPage.locator('[data-testid="submission-card"]').first()
      await expect(firstCard.locator('[data-testid="submission-title"]')).toBeVisible()
      await expect(firstCard.locator('[data-testid="submission-author"]')).toBeVisible()
      await expect(firstCard.locator('[data-testid="submission-likes"]')).toBeVisible()
      
      // Verify featured badge if applicable
      const hasFeaturedBadge = await firstCard.locator('[data-testid="featured-badge"]').isVisible()
      if (hasFeaturedBadge) {
        await expect(firstCard.locator('[data-testid="featured-badge"]')).toBeVisible()
      }
    })

    test('handles featured content interactions', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      await discoverPage.goto()
      
      // Click on first featured submission
      const firstSubmission = mockDataPage.locator('[data-testid="submission-card"]').first()
      await firstSubmission.click()
      
      // Should navigate to submission detail or open modal
      const hasModal = await mockDataPage.locator('[data-testid="submission-modal"]').isVisible()
      const hasDetail = await mockDataPage.locator('[data-testid="submission-detail"]').isVisible()
      const urlChanged = mockDataPage.url().includes('/submissions/')
      
      expect(hasModal || hasDetail || urlChanged).toBe(true)
    })

    test('loads images progressively with proper fallbacks', async ({ mockDataPage }) => {
      const discoverPage = new DiscoverPage(mockDataPage)
      
      // Mock slow image loading
      await mockDataPage.route('**/images/**', async route => {
        await mockDataPage.waitForTimeout(1000)
        await route.continue()
      })
      
      await discoverPage.goto()
      
      // Should show placeholder or loading state initially
      const submissionImages = mockDataPage.locator('[data-testid="submission-image"]')
      const firstImage = submissionImages.first()
      
      // Check for loading placeholder
      const hasPlaceholder = await firstImage.locator('[data-testid="image-placeholder"]').isVisible()
      const hasLoadingSpinner = await firstImage.locator('[data-testid="image-loading"]').isVisible()
      
      if (hasPlaceholder || hasLoadingSpinner) {
        // Wait for image to load
        await mockDataPage.waitForLoadState('networkidle')
        
        // Should eventually show actual image
        await expect(firstImage.locator('img')).toBeVisible({ timeout: timeouts.long })
      }
    })
  })

  test.describe('Real-time Features', () => {
    test('establishes real-time connection and shows status', async ({ mockDataPage }) => {
      const realtimePage = new RealtimePage(mockDataPage)
      const discoverPage = new DiscoverPage(mockDataPage)
      
      await discoverPage.goto()
      
      // Should show connecting initially
      await realtimePage.verifyConnectionStatus('connecting')
      
      // Then should connect
      await realtimePage.waitForConnection()
      await realtimePage.verifyConnectionStatus('connected')
    })

    test('handles real-time activity updates', async ({ mockDataPage }) => {
      const realtimePage = new RealtimePage(mockDataPage)
      const discoverPage = new DiscoverPage(mockDataPage)
      
      await discoverPage.goto()
      await realtimePage.waitForConnection()
      
      // Get initial activity count
      const initialCount = await realtimePage.activityItems.count()
      
      // Simulate new activity
      await realtimePage.simulateRealtimeUpdate('new-activity', {
        id: 'realtime-activity-001',
        type: 'submission',
        user: { userName: 'LiveTester' },
        content: { title: 'New Real-time Activity' }
      })
      
      // Should add new activity
      await realtimePage.verifyActivityCount(initialCount + 1)
      await realtimePage.verifyNewActivity('LiveTester', 'New Real-time Activity')
    })

    test('recovers from connection failures', async ({ mockDataPage }) => {
      const realtimePage = new RealtimePage(mockDataPage)
      const discoverPage = new DiscoverPage(mockDataPage)
      
      await discoverPage.goto()
      await realtimePage.waitForConnection()
      
      // Simulate connection loss
      await mockDataPage.evaluate(() => {
        window.mockWebSocket.disconnect()
      })
      
      await realtimePage.verifyConnectionStatus('disconnected')
      
      // Simulate reconnection
      await mockDataPage.evaluate(() => {
        window.mockWebSocket.connect()
      })
      
      await realtimePage.waitForConnection()
      await realtimePage.verifyConnectionStatus('connected')
    })
  })

  test.describe('Error Handling and Edge Cases', () => {
    test('handles network failures gracefully', async ({ authenticatedPage }) => {
      // Mock network failure
      await authenticatedPage.route('**/api/**', route => route.abort())
      
      const discoverPage = new DiscoverPage(authenticatedPage)
      await discoverPage.goto()
      
      // Should show error states but maintain page structure
      await discoverPage.verifyElementVisible('discover-page')
      
      const hasErrorMessage = await authenticatedPage.locator('[data-testid="error-message"]').isVisible()
      const hasRetryButton = await authenticatedPage.locator('[data-testid="retry-button"]').isVisible()
      
      if (hasErrorMessage) {
        expect(hasRetryButton).toBe(true)
      }
    })

    test('maintains functionality during partial failures', async ({ authenticatedPage }) => {
      // Mock partial failure (stats fail, but content loads)
      await authenticatedPage.route('**/api/community/stats', route => route.abort())
      
      const discoverPage = new DiscoverPage(authenticatedPage)
      await discoverPage.goto()
      
      // Search should still work
      await discoverPage.performSearch('test', false) // Don't wait for results due to mocked API
      
      // Main structure should be intact
      await discoverPage.verifyElementVisible('discover-page')
      await discoverPage.verifyElementVisible('community-search')
    })

    test('handles malformed data gracefully', async ({ mockDataPage }) => {
      // Override mock data with malformed data
      await mockDataPage.addInitScript(() => {
        window.mockCommunityData = {
          stats: { invalid: 'data' },
          submissions: null,
          activities: undefined
        }
      })
      
      const discoverPage = new DiscoverPage(mockDataPage)
      await discoverPage.goto()
      
      // Should not crash and should show fallback content
      await discoverPage.verifyElementVisible('discover-page')
      
      // Should show appropriate error states or fallback data
      const hasErrorState = await mockDataPage.locator('[data-testid="stats-error"]').isVisible()
      const hasFallbackData = await discoverPage.totalMembers.isVisible()
      
      expect(hasErrorState || hasFallbackData).toBe(true)
    })
  })

  test.describe('Accessibility and Keyboard Navigation', () => {
    test('supports full keyboard navigation', async ({ authenticatedPage }) => {
      const discoverPage = new DiscoverPage(authenticatedPage)
      await discoverPage.goto()
      
      // Tab to search input
      await authenticatedPage.keyboard.press('Tab')
      await expect(discoverPage.searchInput).toBeFocused()
      
      // Tab through interactive elements
      await authenticatedPage.keyboard.press('Tab')
      await authenticatedPage.keyboard.press('Tab')
      
      // Should maintain focus visibility
      const focusedElement = authenticatedPage.locator(':focus')
      await expect(focusedElement).toBeVisible()
      
      // Test arrow key navigation if applicable
      await authenticatedPage.keyboard.press('ArrowDown')
      
      // Should maintain accessibility
      const newFocusedElement = authenticatedPage.locator(':focus')
      await expect(newFocusedElement).toBeVisible()
    })

    test('provides proper screen reader support', async ({ authenticatedPage }) => {
      const discoverPage = new DiscoverPage(authenticatedPage)
      await discoverPage.goto()
      
      // Check for live regions
      await expect(authenticatedPage.locator('[aria-live="polite"]')).toBeVisible()
      
      // Verify search announcements
      await discoverPage.performSearch('test')
      
      const announcement = authenticatedPage.locator('[data-testid="search-announcement"]')
      if (await announcement.isVisible()) {
        await expect(announcement).toContainText('results found')
      }
    })
  })
})

// Test suite for admin-specific functionality
test.describe('Community Discover - Admin Features', () => {
  test('shows admin-specific controls when authenticated as admin', async ({ adminPage }) => {
    const discoverPage = new DiscoverPage(adminPage)
    await discoverPage.goto()
    
    // Should show admin controls
    const hasAdminControls = await adminPage.locator('[data-testid="admin-controls"]').isVisible()
    const hasModerateButton = await adminPage.locator('[data-testid="moderate-content"]').isVisible()
    
    expect(hasAdminControls || hasModerateButton).toBe(true)
  })
})

// Performance-focused test suite
test.describe('Community Discover - Performance Tests', () => {
  test('meets Core Web Vitals thresholds', async ({ performancePage }) => {
    const discoverPage = new DiscoverPage(performancePage)
    
    await performancePage.evaluate(() => {
      window.testPerformance.mark('navigation-start')
    })
    
    await discoverPage.goto()
    
    const vitals = await performancePage.evaluate(() => {
      window.testPerformance.mark('navigation-end')
      return {
        loadTime: window.testPerformance.measure('page-load', 'navigation-start', 'navigation-end'),
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      }
    })
    
    // Verify performance thresholds
    expect(vitals.loadTime).toBeLessThan(performance.pageLoad)
    
    if (vitals.firstContentfulPaint > 0) {
      expect(vitals.firstContentfulPaint).toBeLessThan(1500) // 1.5s FCP threshold
    }
    
    console.log(`Performance metrics:`, vitals)
  })
})