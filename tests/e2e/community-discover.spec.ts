/**
 * End-to-End Tests for Community Discover Page
 * 
 * Tests the community discover tab functionality including:
 * - Community hero statistics
 * - Community search
 * - Featured content
 * - Quick actions
 * - Community spotlight
 * 
 * <AUTHOR> Team
 */

import { test, expect, Page } from '@playwright/test'

const COMMUNITY_DISCOVER_URL = '/community/discover'
const PERFORMANCE_THRESHOLDS = {
  pageLoad: 3000,
  searchResponse: 500,
  imageLoad: 2000
}

// Helper functions
const waitForDiscoverPageLoad = async (page: Page) => {
  await page.waitForSelector('[data-testid="discover-page"]', { timeout: 10000 })
  await page.waitForLoadState('networkidle')
}

const waitForStatsLoad = async (page: Page) => {
  await page.waitForSelector('[data-testid="community-stats"]', { timeout: 5000 })
  await page.waitForFunction(() => {
    const statsElement = document.querySelector('[data-testid="community-stats"]')
    return statsElement && statsElement.textContent && !statsElement.textContent.includes('Loading')
  })
}

const mockCommunityStats = {
  totalMembers: 2847,
  activeChallenges: 8,
  onlineUsers: 234,
  totalSubmissions: 1856,
  weeklyGrowth: 15
}

test.describe('Community Discover Page E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/')
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('test-user', JSON.stringify({
        uid: 'test-user-123',
        email: '<EMAIL>',
        displayName: 'Test Explorer'
      }))
    })

    // Mock Firebase services for consistent testing
    await page.addInitScript(() => {
      window.mockCommunityStats = {
        totalMembers: 2847,
        activeChallenges: 8,
        onlineUsers: 234,
        totalSubmissions: 1856,
        weeklyGrowth: 15
      }
    })
  })

  test.describe('Page Loading and Structure', () => {
    test('loads discover page successfully', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      const loadTime = Date.now() - startTime
      
      // Verify page loaded
      await expect(page.locator('[data-testid="discover-page"]')).toBeVisible()
      await expect(page.locator('h1')).toContainText('Discover')
      
      // Performance assertion
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad)
      
      console.log(`Community discover page loaded in ${loadTime}ms`)
    })

    test('displays all main sections', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for hero section
      await expect(page.locator('[data-testid="community-hero"]')).toBeVisible()
      
      // Check for search section
      await expect(page.locator('[data-testid="community-search"]')).toBeVisible()
      
      // Check for featured content
      await expect(page.locator('[data-testid="featured-content"]')).toBeVisible()
      
      // Check for quick actions
      await expect(page.locator('[data-testid="quick-actions"]')).toBeVisible()
      
      // Check for community spotlight
      await expect(page.locator('[data-testid="community-spotlight"]')).toBeVisible()
    })

    test('has proper heading hierarchy', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for proper heading structure
      const h1 = page.locator('h1')
      const h2 = page.locator('h2')
      
      await expect(h1).toHaveCount(1)
      await expect(h1).toContainText('Discover')
      
      // Should have section headings
      await expect(h2).toContainText('Featured Content')
      await expect(h2).toContainText('Community Spotlight')
    })
  })

  test.describe('Community Hero Statistics', () => {
    test('displays live community statistics', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      await waitForStatsLoad(page)
      
      // Check for statistics display
      await expect(page.locator('[data-testid="total-members"]')).toBeVisible()
      await expect(page.locator('[data-testid="active-challenges"]')).toBeVisible()
      await expect(page.locator('[data-testid="online-users"]')).toBeVisible()
      await expect(page.locator('[data-testid="total-submissions"]')).toBeVisible()
      
      // Verify statistics have numeric values
      const membersText = await page.locator('[data-testid="total-members"]').textContent()
      const challengesText = await page.locator('[data-testid="active-challenges"]').textContent()
      
      expect(membersText).toMatch(/\d+/)
      expect(challengesText).toMatch(/\d+/)
    })

    test('shows growth indicators', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      await waitForStatsLoad(page)
      
      // Check for growth percentage
      await expect(page.locator('[data-testid="weekly-growth"]')).toBeVisible()
      
      // Should show positive growth
      const growthText = await page.locator('[data-testid="weekly-growth"]').textContent()
      expect(growthText).toMatch(/\+?\d+%/)
    })

    test('displays welcome message for authenticated users', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Should show personalized welcome
      await expect(page.locator('text=Welcome back')).toBeVisible()
      await expect(page.locator('text=Test Explorer')).toBeVisible()
    })

    test('handles stats loading states', async ({ page }) => {
      // Delay network requests to test loading state
      await page.route('**/community_stats/**', async route => {
        await page.waitForTimeout(1000)
        await route.continue()
      })
      
      await page.goto(COMMUNITY_DISCOVER_URL)
      
      // Should show loading state initially
      await expect(page.locator('[data-testid="stats-loading"]')).toBeVisible()
      
      // Then show actual stats
      await waitForStatsLoad(page)
      await expect(page.locator('[data-testid="total-members"]')).toBeVisible()
    })
  })

  test.describe('Community Search Integration', () => {
    test('displays search interface', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for search components
      await expect(page.locator('[data-testid="search-input"]')).toBeVisible()
      await expect(page.locator('[data-testid="search-filters"]')).toBeVisible()
      await expect(page.locator('[data-testid="trending-searches"]')).toBeVisible()
    })

    test('shows trending search suggestions', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for trending searches
      const trendingContainer = page.locator('[data-testid="trending-searches"]')
      await expect(trendingContainer).toBeVisible()
      
      // Should have trending items
      await expect(trendingContainer.locator('[data-testid="trending-item"]')).toHaveCount(3, { timeout: 5000 })
    })

    test('handles search input interactions', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      const searchInput = page.locator('[data-testid="search-input"]')
      
      // Test search input
      await searchInput.fill('artisan keycap')
      await expect(searchInput).toHaveValue('artisan keycap')
      
      // Should show search suggestions
      await expect(page.locator('[data-testid="search-suggestions"]')).toBeVisible({ timeout: 1000 })
    })

    test('performs search with debouncing', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      const searchInput = page.locator('[data-testid="search-input"]')
      
      // Type search query
      await searchInput.fill('rgb keyboard')
      
      // Wait for debounced search
      await page.waitForTimeout(500)
      
      // Should show search results
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible({ timeout: 2000 })
    })

    test('filters search results by content type', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      const searchInput = page.locator('[data-testid="search-input"]')
      await searchInput.fill('challenge')
      
      // Wait for initial results
      await page.waitForTimeout(500)
      
      // Click filter for submissions only
      await page.locator('[data-testid="filter-submissions"]').click()
      
      // Should update results
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
      
      // Should show only submission results
      const resultItems = page.locator('[data-testid="result-item"]')
      const firstResult = resultItems.first()
      await expect(firstResult.locator('[data-testid="result-type"]')).toContainText('Submission')
    })
  })

  test.describe('Featured Content Section', () => {
    test('displays featured submissions', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for featured submissions
      const featuredSection = page.locator('[data-testid="featured-submissions"]')
      await expect(featuredSection).toBeVisible()
      
      // Should have submission cards
      await expect(featuredSection.locator('[data-testid="submission-card"]')).toHaveCount(4, { timeout: 5000 })
    })

    test('displays trending discussions', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for trending discussions
      const discussionsSection = page.locator('[data-testid="trending-discussions"]')
      await expect(discussionsSection).toBeVisible()
      
      // Should have discussion items
      await expect(discussionsSection.locator('[data-testid="discussion-item"]')).toHaveCount(6, { timeout: 5000 })
    })

    test('handles featured content interactions', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Click on first featured submission
      const firstSubmission = page.locator('[data-testid="submission-card"]').first()
      await expect(firstSubmission).toBeVisible()
      
      await firstSubmission.click()
      
      // Should navigate to submission detail or open modal
      await expect(page.locator('[data-testid="submission-detail"]')).toBeVisible({ timeout: 3000 })
    })

    test('loads images progressively', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for image loading states
      const submissionImages = page.locator('[data-testid="submission-image"]')
      
      // Should have placeholder or loading state initially
      const firstImage = submissionImages.first()
      await expect(firstImage).toBeVisible()
      
      // Wait for images to load
      await page.waitForLoadState('networkidle')
      
      // Images should have loaded
      const imageCount = await submissionImages.count()
      expect(imageCount).toBeGreaterThan(0)
    })
  })

  test.describe('Quick Actions Cards', () => {
    test('displays action cards', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for quick action cards
      const quickActions = page.locator('[data-testid="quick-actions"]')
      await expect(quickActions).toBeVisible()
      
      // Should have action cards
      await expect(quickActions.locator('[data-testid="action-card"]')).toHaveCount(4, { timeout: 5000 })
    })

    test('navigates to correct pages on action clicks', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Click "Submit Work" action
      const submitAction = page.locator('[data-testid="action-submit"]')
      await expect(submitAction).toBeVisible()
      
      await submitAction.click()
      
      // Should navigate to submission page
      await expect(page.url()).toContain('/submissions')
    })

    test('shows appropriate icons and descriptions', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for action descriptions
      await expect(page.locator('text=Submit Your Work')).toBeVisible()
      await expect(page.locator('text=Join Challenge')).toBeVisible()
      await expect(page.locator('text=Browse Gallery')).toBeVisible()
      await expect(page.locator('text=Start Discussion')).toBeVisible()
      
      // Check for action icons
      const actionCards = page.locator('[data-testid="action-card"]')
      const firstCard = actionCards.first()
      await expect(firstCard.locator('svg, [data-testid="action-icon"]')).toBeVisible()
    })
  })

  test.describe('Community Spotlight', () => {
    test('displays featured member spotlight', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for spotlight section
      const spotlight = page.locator('[data-testid="community-spotlight"]')
      await expect(spotlight).toBeVisible()
      
      // Should show member details
      await expect(spotlight.locator('[data-testid="member-avatar"]')).toBeVisible()
      await expect(spotlight.locator('[data-testid="member-name"]')).toBeVisible()
      await expect(spotlight.locator('[data-testid="member-bio"]')).toBeVisible()
    })

    test('shows member achievements and badges', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      const spotlight = page.locator('[data-testid="community-spotlight"]')
      
      // Check for achievements
      await expect(spotlight.locator('[data-testid="member-achievements"]')).toBeVisible()
      await expect(spotlight.locator('[data-testid="achievement-badge"]')).toHaveCount(3, { timeout: 5000 })
      
      // Check for member level
      await expect(spotlight.locator('[data-testid="member-level"]')).toBeVisible()
    })

    test('links to member profile', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Click on spotlight member
      const memberLink = page.locator('[data-testid="spotlight-member-link"]')
      await expect(memberLink).toBeVisible()
      
      await memberLink.click()
      
      // Should navigate to member profile
      await expect(page.url()).toContain('/profile/')
    })

    test('displays member recent work', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      const spotlight = page.locator('[data-testid="community-spotlight"]')
      
      // Check for recent work section
      await expect(spotlight.locator('[data-testid="recent-work"]')).toBeVisible()
      await expect(spotlight.locator('[data-testid="work-item"]')).toHaveCount(2, { timeout: 5000 })
    })
  })

  test.describe('Responsive Design', () => {
    test('works on mobile viewport', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // All main sections should be visible
      await expect(page.locator('[data-testid="community-hero"]')).toBeVisible()
      await expect(page.locator('[data-testid="community-search"]')).toBeVisible()
      await expect(page.locator('[data-testid="featured-content"]')).toBeVisible()
      
      // Stats should stack vertically
      const statsContainer = page.locator('[data-testid="community-stats"]')
      await expect(statsContainer).toBeVisible()
    })

    test('adapts layout for tablet', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check grid layouts adapt
      const featuredGrid = page.locator('[data-testid="featured-submissions"]')
      await expect(featuredGrid).toBeVisible()
      
      // Quick actions should be in appropriate grid
      const quickActions = page.locator('[data-testid="quick-actions"]')
      await expect(quickActions).toBeVisible()
    })

    test('utilizes full desktop layout', async ({ page }) => {
      await page.setViewportSize({ width: 1920, height: 1080 })
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // All sections should be properly laid out
      await expect(page.locator('[data-testid="discover-page"]')).toBeVisible()
      
      // Featured content should show full grid
      const submissionCards = page.locator('[data-testid="submission-card"]')
      await expect(submissionCards).toHaveCount(4, { timeout: 5000 })
    })
  })

  test.describe('Performance and Accessibility', () => {
    test('meets performance thresholds', async ({ page }) => {
      const startTime = Date.now()
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      const loadTime = Date.now() - startTime
      
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad)
      
      // Test search response time
      const searchStart = Date.now()
      await page.locator('[data-testid="search-input"]').fill('test')
      await page.waitForTimeout(100)
      const searchTime = Date.now() - searchStart
      
      expect(searchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.searchResponse)
    })

    test('supports keyboard navigation', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Tab through interactive elements
      await page.keyboard.press('Tab')
      
      // Should focus on search input
      await expect(page.locator('[data-testid="search-input"]')).toBeFocused()
      
      // Continue tabbing through elements
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      // Should maintain visible focus
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    })

    test('has proper ARIA labels and roles', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Check for proper ARIA labels
      await expect(page.locator('[aria-label]')).toHaveCount(10, { timeout: 5000 })
      
      // Check for proper roles
      await expect(page.locator('[role="search"]')).toBeVisible()
      await expect(page.locator('[role="list"]')).toHaveCount(2, { timeout: 5000 })
      
      // Check for proper heading structure
      await expect(page.locator('h1')).toHaveAttribute('aria-level', '1')
    })

    test('provides alternative text for images', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // All images should have alt text
      const images = page.locator('img')
      const imageCount = await images.count()
      
      for (let i = 0; i < imageCount; i++) {
        const image = images.nth(i)
        await expect(image).toHaveAttribute('alt')
      }
    })
  })

  test.describe('Error Handling', () => {
    test('handles API failures gracefully', async ({ page }) => {
      // Simulate API failures
      await page.route('**/community/**', route => route.abort())
      
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Should show error states
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible({ timeout: 5000 })
      
      // Should still show basic page structure
      await expect(page.locator('h1')).toContainText('Discover')
    })

    test('recovers from network failures', async ({ page }) => {
      let requestCount = 0
      
      // Simulate intermittent failures
      await page.route('**/community_stats/**', route => {
        requestCount++
        if (requestCount <= 1) {
          route.abort()
        } else {
          route.continue()
        }
      })
      
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Should eventually load successfully
      await waitForStatsLoad(page)
      await expect(page.locator('[data-testid="total-members"]')).toBeVisible()
    })

    test('shows retry options for failed loads', async ({ page }) => {
      // Simulate failure
      await page.route('**/featured/**', route => route.abort())
      
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForDiscoverPageLoad(page)
      
      // Should show retry button
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible({ timeout: 5000 })
      
      // Click retry should attempt reload
      await page.locator('[data-testid="retry-button"]').click()
      
      // Should attempt to reload content
      await page.waitForTimeout(1000)
    })
  })
})