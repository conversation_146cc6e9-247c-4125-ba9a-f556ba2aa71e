/**
 * End-to-End Tests for Community Navigation and Routing
 * 
 * Tests the complete community navigation system including:
 * - Tab-based navigation between community sections
 * - Deep linking and URL routing
 * - Breadcrumb navigation
 * - Back/forward browser navigation
 * - Mobile navigation patterns
 * 
 * <AUTHOR> Team
 */

import { test, expect, Page } from '@playwright/test'

const COMMUNITY_BASE_URL = '/community'
const COMMUNITY_ROUTES = {
  discover: '/community/discover',
  submissions: '/community/submissions',
  discussions: '/community/discussions',
  challenges: '/community/challenges',
  leaderboard: '/community/leaderboard'
}

// Helper functions
const waitForPageNavigation = async (page: Page, expectedPath: string) => {
  await page.waitForURL(`**${expectedPath}`)
  await page.waitForLoadState('networkidle')
}

const waitForTabLoad = async (page: Page, tabTestId: string) => {
  await page.waitForSelector(`[data-testid="${tabTestId}"]`, { timeout: 10000 })
  await page.waitForLoadState('networkidle')
}

const navigateToTab = async (page: Page, tabName: string) => {
  const tabSelector = `[data-testid="tab-${tabName}"]`
  await page.locator(tabSelector).click()
  await waitForTabLoad(page, `${tabName}-content`)
}

const checkActiveTab = async (page: Page, expectedTab: string) => {
  const activeTab = page.locator(`[data-testid="tab-${expectedTab}"][aria-selected="true"]`)
  await expect(activeTab).toBeVisible()
}

test.describe('Community Navigation E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/')
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('test-user', JSON.stringify({
        uid: 'test-user-123',
        email: '<EMAIL>',
        displayName: 'Test Navigator'
      }))
    })
  })

  test.describe('Basic Navigation Structure', () => {
    test('loads community main page with tab navigation', async ({ page }) => {
      await page.goto(COMMUNITY_BASE_URL)
      await waitForPageNavigation(page, COMMUNITY_BASE_URL)
      
      // Check for main navigation structure
      await expect(page.locator('[data-testid="community-layout"]')).toBeVisible()
      await expect(page.locator('[data-testid="community-tabs"]')).toBeVisible()
      
      // Check for all tab buttons
      await expect(page.locator('[data-testid="tab-discover"]')).toBeVisible()
      await expect(page.locator('[data-testid="tab-submissions"]')).toBeVisible()
      await expect(page.locator('[data-testid="tab-discussions"]')).toBeVisible()
      await expect(page.locator('[data-testid="tab-challenges"]')).toBeVisible()
      await expect(page.locator('[data-testid="tab-leaderboard"]')).toBeVisible()
    })

    test('defaults to discover tab when visiting community root', async ({ page }) => {
      await page.goto(COMMUNITY_BASE_URL)
      await waitForPageNavigation(page, COMMUNITY_BASE_URL)
      
      // Should redirect to discover tab
      await expect(page.url()).toContain('/discover')
      await checkActiveTab(page, 'discover')
      
      // Should show discover content
      await expect(page.locator('[data-testid="discover-content"]')).toBeVisible()
    })

    test('displays tab labels and icons correctly', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Check tab labels
      await expect(page.locator('[data-testid="tab-discover"]')).toContainText('Discover')
      await expect(page.locator('[data-testid="tab-submissions"]')).toContainText('Submissions')
      await expect(page.locator('[data-testid="tab-discussions"]')).toContainText('Discussions')
      await expect(page.locator('[data-testid="tab-challenges"]')).toContainText('Challenges')
      await expect(page.locator('[data-testid="tab-leaderboard"]')).toContainText('Leaderboard')
      
      // Check for tab icons
      await expect(page.locator('[data-testid="tab-discover"] svg')).toBeVisible()
      await expect(page.locator('[data-testid="tab-submissions"] svg')).toBeVisible()
      await expect(page.locator('[data-testid="tab-discussions"] svg')).toBeVisible()
      await expect(page.locator('[data-testid="tab-challenges"] svg')).toBeVisible()
      await expect(page.locator('[data-testid="tab-leaderboard"] svg')).toBeVisible()
    })

    test('shows active tab state correctly', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Discover should be active
      await checkActiveTab(page, 'discover')
      await expect(page.locator('[data-testid="tab-discover"]')).toHaveClass(/active/)
      
      // Other tabs should not be active
      await expect(page.locator('[data-testid="tab-submissions"]')).not.toHaveClass(/active/)
      await expect(page.locator('[data-testid="tab-discussions"]')).not.toHaveClass(/active/)
    })
  })

  test.describe('Tab Navigation', () => {
    test('navigates between all tabs successfully', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Navigate to each tab
      await navigateToTab(page, 'submissions')
      await expect(page.url()).toContain('/submissions')
      await checkActiveTab(page, 'submissions')
      await expect(page.locator('[data-testid="submissions-content"]')).toBeVisible()
      
      await navigateToTab(page, 'discussions')
      await expect(page.url()).toContain('/discussions')
      await checkActiveTab(page, 'discussions')
      await expect(page.locator('[data-testid="discussions-content"]')).toBeVisible()
      
      await navigateToTab(page, 'challenges')
      await expect(page.url()).toContain('/challenges')
      await checkActiveTab(page, 'challenges')
      await expect(page.locator('[data-testid="challenges-content"]')).toBeVisible()
      
      await navigateToTab(page, 'leaderboard')
      await expect(page.url()).toContain('/leaderboard')
      await checkActiveTab(page, 'leaderboard')
      await expect(page.locator('[data-testid="leaderboard-content"]')).toBeVisible()
      
      // Navigate back to discover
      await navigateToTab(page, 'discover')
      await expect(page.url()).toContain('/discover')
      await checkActiveTab(page, 'discover')
      await expect(page.locator('[data-testid="discover-content"]')).toBeVisible()
    })

    test('maintains state during tab navigation', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Perform action in discover tab (e.g., search)
      const searchInput = page.locator('[data-testid="search-input"]')
      if (await searchInput.isVisible()) {
        await searchInput.fill('test search')
      }
      
      // Navigate to another tab
      await navigateToTab(page, 'submissions')
      
      // Navigate back to discover
      await navigateToTab(page, 'discover')
      
      // Search input should maintain value
      if (await searchInput.isVisible()) {
        await expect(searchInput).toHaveValue('test search')
      }
    })

    test('handles rapid tab switching', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Rapidly switch between tabs
      await page.locator('[data-testid="tab-submissions"]').click()
      await page.locator('[data-testid="tab-discussions"]').click()
      await page.locator('[data-testid="tab-challenges"]').click()
      await page.locator('[data-testid="tab-leaderboard"]').click()
      await page.locator('[data-testid="tab-discover"]').click()
      
      // Should end up on discover tab
      await waitForTabLoad(page, 'discover-content')
      await checkActiveTab(page, 'discover')
      await expect(page.url()).toContain('/discover')
    })

    test('updates document title for each tab', async ({ page }) => {
      // Test each tab's document title
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      await expect(page).toHaveTitle(/Discover.*Community/)
      
      await navigateToTab(page, 'submissions')
      await expect(page).toHaveTitle(/Submissions.*Community/)
      
      await navigateToTab(page, 'discussions')
      await expect(page).toHaveTitle(/Discussions.*Community/)
      
      await navigateToTab(page, 'challenges')
      await expect(page).toHaveTitle(/Challenges.*Community/)
      
      await navigateToTab(page, 'leaderboard')
      await expect(page).toHaveTitle(/Leaderboard.*Community/)
    })
  })

  test.describe('Direct URL Navigation', () => {
    test('handles direct navigation to each tab URL', async ({ page }) => {
      // Test direct navigation to each route
      const routes = Object.entries(COMMUNITY_ROUTES)
      
      for (const [tabName, route] of routes) {
        await page.goto(route)
        await waitForPageNavigation(page, route)
        
        // Should show correct tab as active
        await checkActiveTab(page, tabName)
        
        // Should show correct content
        await expect(page.locator(`[data-testid="${tabName}-content"]`)).toBeVisible()
        
        console.log(`✓ Direct navigation to ${tabName} tab working`)
      }
    })

    test('handles invalid community routes', async ({ page }) => {
      await page.goto('/community/invalid-tab')
      
      // Should redirect to discover tab or show 404
      await page.waitForTimeout(2000)
      
      const currentUrl = page.url()
      const isRedirected = currentUrl.includes('/discover')
      const is404 = await page.locator('text=404').isVisible() || await page.locator('text=Not Found').isVisible()
      
      expect(isRedirected || is404).toBe(true)
    })

    test('preserves query parameters during navigation', async ({ page }) => {
      await page.goto('/community/discover?filter=artisan&sort=recent')
      await waitForTabLoad(page, 'discover-content')
      
      // Navigate to another tab
      await navigateToTab(page, 'submissions')
      
      // Navigate back to discover
      await navigateToTab(page, 'discover')
      
      // Should preserve query parameters
      expect(page.url()).toContain('filter=artisan')
      expect(page.url()).toContain('sort=recent')
    })

    test('handles deep linking within tabs', async ({ page }) => {
      // Test deep link to specific submission
      await page.goto('/community/submissions/123')
      await waitForPageNavigation(page, '/community/submissions/123')
      
      // Should show submissions tab as active
      await checkActiveTab(page, 'submissions')
      
      // Should show submission detail if it exists
      const hasSubmissionDetail = await page.locator('[data-testid="submission-detail"]').isVisible()
      const hasSubmissionsList = await page.locator('[data-testid="submissions-content"]').isVisible()
      
      expect(hasSubmissionDetail || hasSubmissionsList).toBe(true)
    })
  })

  test.describe('Browser Navigation', () => {
    test('supports browser back and forward buttons', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Navigate through tabs
      await navigateToTab(page, 'submissions')
      await navigateToTab(page, 'discussions')
      await navigateToTab(page, 'challenges')
      
      // Use browser back button
      await page.goBack()
      await waitForPageNavigation(page, '/community/discussions')
      await checkActiveTab(page, 'discussions')
      
      await page.goBack()
      await waitForPageNavigation(page, '/community/submissions')
      await checkActiveTab(page, 'submissions')
      
      await page.goBack()
      await waitForPageNavigation(page, '/community/discover')
      await checkActiveTab(page, 'discover')
      
      // Use browser forward button
      await page.goForward()
      await waitForPageNavigation(page, '/community/submissions')
      await checkActiveTab(page, 'submissions')
    })

    test('maintains scroll position during navigation', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Scroll down in discover tab
      await page.evaluate(() => window.scrollTo(0, 500))
      await page.waitForTimeout(500)
      
      const scrollPosition = await page.evaluate(() => window.pageYOffset)
      expect(scrollPosition).toBeGreaterThan(400)
      
      // Navigate to another tab
      await navigateToTab(page, 'submissions')
      
      // Navigate back
      await navigateToTab(page, 'discover')
      
      // Should restore scroll position
      await page.waitForTimeout(500)
      const restoredPosition = await page.evaluate(() => window.pageYOffset)
      expect(restoredPosition).toBeGreaterThan(300) // Allow some tolerance
    })

    test('handles page refresh correctly', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.submissions)
      await waitForTabLoad(page, 'submissions-content')
      
      // Verify we're on submissions tab
      await checkActiveTab(page, 'submissions')
      
      // Refresh page
      await page.reload()
      await waitForTabLoad(page, 'submissions-content')
      
      // Should maintain the same tab
      await checkActiveTab(page, 'submissions')
      await expect(page.url()).toContain('/submissions')
      await expect(page.locator('[data-testid="submissions-content"]')).toBeVisible()
    })
  })

  test.describe('Breadcrumb Navigation', () => {
    test('displays breadcrumb navigation', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Check for breadcrumb component
      const breadcrumb = page.locator('[data-testid="breadcrumb"]')
      if (await breadcrumb.isVisible()) {
        await expect(breadcrumb).toContainText('Community')
        await expect(breadcrumb).toContainText('Discover')
      }
    })

    test('updates breadcrumb for different tabs', async ({ page }) => {
      const breadcrumb = page.locator('[data-testid="breadcrumb"]')
      
      // Test breadcrumb for each tab
      await page.goto(COMMUNITY_ROUTES.submissions)
      await waitForTabLoad(page, 'submissions-content')
      
      if (await breadcrumb.isVisible()) {
        await expect(breadcrumb).toContainText('Submissions')
      }
      
      await navigateToTab(page, 'discussions')
      if (await breadcrumb.isVisible()) {
        await expect(breadcrumb).toContainText('Discussions')
      }
    })

    test('allows navigation via breadcrumb links', async ({ page }) => {
      await page.goto('/community/submissions/123')
      await waitForPageNavigation(page, '/community/submissions/123')
      
      const breadcrumb = page.locator('[data-testid="breadcrumb"]')
      if (await breadcrumb.isVisible()) {
        // Click on Community breadcrumb
        const communityLink = breadcrumb.locator('text=Community')
        if (await communityLink.isVisible()) {
          await communityLink.click()
          await waitForPageNavigation(page, '/community/discover')
          await checkActiveTab(page, 'discover')
        }
      }
    })
  })

  test.describe('Mobile Navigation', () => {
    test('displays mobile navigation menu', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // On mobile, tabs might be in a dropdown or hamburger menu
      const mobileNav = page.locator('[data-testid="mobile-nav"]')
      const tabsContainer = page.locator('[data-testid="community-tabs"]')
      
      // Either mobile nav or horizontal tabs should be visible
      const hasMobileNav = await mobileNav.isVisible()
      const hasTabsContainer = await tabsContainer.isVisible()
      
      expect(hasMobileNav || hasTabsContainer).toBe(true)
    })

    test('handles mobile tab navigation', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Check if mobile navigation is present
      const mobileMenuButton = page.locator('[data-testid="mobile-menu-button"]')
      
      if (await mobileMenuButton.isVisible()) {
        // Test mobile menu navigation
        await mobileMenuButton.click()
        await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
        
        // Navigate to submissions via mobile menu
        await page.locator('[data-testid="mobile-tab-submissions"]').click()
        await waitForTabLoad(page, 'submissions-content')
        await checkActiveTab(page, 'submissions')
      } else {
        // Test horizontal tab navigation on mobile
        await navigateToTab(page, 'submissions')
        await checkActiveTab(page, 'submissions')
      }
    })

    test('supports swipe navigation on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Simulate swipe gesture (if implemented)
      const content = page.locator('[data-testid="community-content"]')
      
      if (await content.isVisible()) {
        // Simulate swipe left (next tab)
        await content.hover()
        await page.mouse.down()
        await page.mouse.move(200, 0) // Swipe left
        await page.mouse.up()
        
        await page.waitForTimeout(500)
        
        // Check if navigation occurred
        const currentUrl = page.url()
        const isStillOnDiscover = currentUrl.includes('/discover')
        
        // Swipe navigation may or may not be implemented
        console.log(`Swipe navigation test: URL is ${currentUrl}`)
      }
    })
  })

  test.describe('Accessibility Navigation', () => {
    test('supports keyboard navigation between tabs', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Focus on first tab
      await page.keyboard.press('Tab')
      await expect(page.locator('[data-testid="tab-discover"]')).toBeFocused()
      
      // Use arrow keys to navigate between tabs
      await page.keyboard.press('ArrowRight')
      await expect(page.locator('[data-testid="tab-submissions"]')).toBeFocused()
      
      await page.keyboard.press('ArrowRight')
      await expect(page.locator('[data-testid="tab-discussions"]')).toBeFocused()
      
      // Use arrow left to go back
      await page.keyboard.press('ArrowLeft')
      await expect(page.locator('[data-testid="tab-submissions"]')).toBeFocused()
      
      // Press Enter to activate tab
      await page.keyboard.press('Enter')
      await waitForTabLoad(page, 'submissions-content')
      await checkActiveTab(page, 'submissions')
    })

    test('has proper ARIA attributes for tabs', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Check tab list has proper role
      await expect(page.locator('[data-testid="community-tabs"]')).toHaveAttribute('role', 'tablist')
      
      // Check individual tabs have proper attributes
      const discoverTab = page.locator('[data-testid="tab-discover"]')
      await expect(discoverTab).toHaveAttribute('role', 'tab')
      await expect(discoverTab).toHaveAttribute('aria-selected', 'true')
      await expect(discoverTab).toHaveAttribute('aria-controls')
      
      // Check non-active tabs
      const submissionsTab = page.locator('[data-testid="tab-submissions"]')
      await expect(submissionsTab).toHaveAttribute('role', 'tab')
      await expect(submissionsTab).toHaveAttribute('aria-selected', 'false')
    })

    test('provides skip navigation links', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Check for skip to content link
      const skipLink = page.locator('[data-testid="skip-to-content"]')
      if (await skipLink.isVisible()) {
        await skipLink.click()
        
        // Should focus on main content
        const mainContent = page.locator('[data-testid="main-content"]')
        await expect(mainContent).toBeFocused()
      }
    })

    test('announces tab changes to screen readers', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Check for live region
      const liveRegion = page.locator('[aria-live="polite"]')
      await expect(liveRegion).toBeAttached()
      
      // Navigate to different tab
      await navigateToTab(page, 'submissions')
      
      // Should announce tab change
      await expect(liveRegion).toContainText('Submissions')
    })
  })

  test.describe('Performance and Error Handling', () => {
    test('loads tabs efficiently without full page reloads', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Monitor network requests during tab navigation
      let requestCount = 0
      page.on('request', request => {
        if (request.url().includes('/community/')) {
          requestCount++
        }
      })
      
      // Navigate between tabs
      await navigateToTab(page, 'submissions')
      await navigateToTab(page, 'discussions')
      await navigateToTab(page, 'discover')
      
      // Should minimize network requests (client-side routing)
      expect(requestCount).toBeLessThan(10) // Allow some API calls but not full page loads
    })

    test('handles navigation errors gracefully', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Simulate network error during navigation
      await page.route('**/community/submissions**', route => route.abort())
      
      await navigateToTab(page, 'submissions')
      
      // Should show error state or fallback
      const hasError = await page.locator('[data-testid="error-message"]').isVisible()
      const hasContent = await page.locator('[data-testid="submissions-content"]').isVisible()
      
      // Should handle gracefully (either show error or cached content)
      expect(hasError || hasContent).toBe(true)
    })

    test('maintains navigation state during network interruptions', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Navigate to submissions
      await navigateToTab(page, 'submissions')
      
      // Simulate network disconnection
      await page.context().setOffline(true)
      
      // Try to navigate to discussions
      await navigateToTab(page, 'discussions')
      
      // Should maintain navigation state even offline
      await checkActiveTab(page, 'discussions')
      
      // Restore network
      await page.context().setOffline(false)
    })

    test('preloads tab content for better performance', async ({ page }) => {
      await page.goto(COMMUNITY_ROUTES.discover)
      await waitForTabLoad(page, 'discover-content')
      
      // Check if other tab content is preloaded
      const submissionsContent = page.locator('[data-testid="submissions-content"]')
      
      // Wait a moment for potential preloading
      await page.waitForTimeout(2000)
      
      // Navigate to submissions should be fast
      const startTime = Date.now()
      await navigateToTab(page, 'submissions')
      const loadTime = Date.now() - startTime
      
      // Should load quickly (under 1 second)
      expect(loadTime).toBeLessThan(1000)
    })
  })
})