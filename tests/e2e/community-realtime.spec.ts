/**
 * End-to-End Tests for Real-time Community Features
 * 
 * Tests the real-time functionality including:
 * - Live community statistics updates
 * - Real-time activity feed
 * - Live leaderboard updates
 * - Real-time submission likes and comments
 * - Live challenge participation counts
 * - WebSocket connection handling
 * 
 * <AUTHOR> Team
 */

import { test, expect, Page } from '@playwright/test'

const COMMUNITY_DISCOVER_URL = '/community/discover'
const COMMUNITY_LEADERBOARD_URL = '/community/leaderboard'
const REALTIME_UPDATE_TIMEOUT = 5000

// Mock real-time data updates
const mockRealtimeUpdates = {
  statsUpdate: {
    totalMembers: 2848, // +1 from initial
    activeChallenges: 9, // +1 from initial
    onlineUsers: 235,   // +1 from initial
    totalSubmissions: 1857, // +1 from initial
    weeklyGrowth: 16    // +1 from initial
  },
  newActivity: {
    id: 'activity-realtime-001',
    type: 'submission',
    user: {
      userId: 'user-realtime-001',
      userName: 'LiveTester',
      userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiveTester',
      userLevel: 5
    },
    content: {
      title: 'New Real-time Submission',
      description: 'LiveT<PERSON> shared a new artisan keycap creation'
    },
    timestamp: new Date(),
    engagement: {
      likes: 0,
      comments: 0,
      shares: 0
    }
  },
  leaderboardUpdate: {
    userId: 'user-realtime-001',
    userName: 'LiveTester',
    userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiveTester',
    points: 1150,
    level: 5,
    rank: 10,
    change: 3 // moved up 3 positions
  }
}

// Helper functions
const waitForRealtimeConnection = async (page: Page) => {
  await page.waitForSelector('[data-testid="realtime-status"]', { timeout: 10000 })
  
  // Wait for connection indicator to show connected
  await page.waitForFunction(() => {
    const status = document.querySelector('[data-testid="realtime-status"]')
    return status && status.textContent?.includes('connected')
  }, { timeout: 10000 })
}

const simulateRealtimeUpdate = async (page: Page, updateType: string, data: any) => {
  await page.evaluate(([type, updateData]) => {
    // Simulate real-time update by dispatching custom event
    window.dispatchEvent(new CustomEvent('realtime-update', {
      detail: { type, data: updateData }
    }))
  }, [updateType, data])
}

const waitForStatUpdate = async (page: Page, statElement: string, expectedValue: string) => {
  await page.waitForFunction(([selector, value]) => {
    const element = document.querySelector(selector)
    return element && element.textContent?.includes(value)
  }, [statElement, expectedValue], { timeout: REALTIME_UPDATE_TIMEOUT })
}

const triggerLikeUpdate = async (page: Page, submissionId: string) => {
  await page.evaluate((id) => {
    window.dispatchEvent(new CustomEvent('like-update', {
      detail: { submissionId: id, newLikeCount: 135 }
    }))
  }, submissionId)
}

test.describe('Real-time Community Features E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/')
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('test-user', JSON.stringify({
        uid: 'test-user-123',
        email: '<EMAIL>',
        displayName: 'Real-time Tester'
      }))
    })

    // Mock WebSocket connection for real-time features
    await page.addInitScript(() => {
      // Mock WebSocket API
      window.mockWebSocket = {
        connected: false,
        connect: () => {
          window.mockWebSocket.connected = true
          window.dispatchEvent(new CustomEvent('websocket-connected'))
        },
        disconnect: () => {
          window.mockWebSocket.connected = false
          window.dispatchEvent(new CustomEvent('websocket-disconnected'))
        },
        send: (data: any) => {
          console.log('Mock WebSocket send:', data)
        }
      }
      
      // Auto-connect after a short delay
      setTimeout(() => {
        window.mockWebSocket.connect()
      }, 1000)
    })
  })

  test.describe('Real-time Connection', () => {
    test('establishes real-time connection on page load', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      
      // Wait for real-time connection
      await waitForRealtimeConnection(page)
      
      // Should show connected status
      const statusIndicator = page.locator('[data-testid="realtime-status"]')
      await expect(statusIndicator).toContainText('connected')
      await expect(statusIndicator).toHaveClass(/connected/)
    })

    test('shows connection status indicator', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      
      // Initially should show connecting
      const statusIndicator = page.locator('[data-testid="realtime-status"]')
      await expect(statusIndicator).toContainText('connecting')
      
      // Then should show connected
      await waitForRealtimeConnection(page)
      await expect(statusIndicator).toContainText('connected')
      await expect(statusIndicator).toHaveClass(/connected/)
    })

    test('handles connection failures gracefully', async ({ page }) => {
      // Mock connection failure
      await page.addInitScript(() => {
        window.mockWebSocketFailure = true
      })
      
      await page.goto(COMMUNITY_DISCOVER_URL)
      
      // Should show disconnected status
      const statusIndicator = page.locator('[data-testid="realtime-status"]')
      await expect(statusIndicator).toContainText('disconnected', { timeout: 5000 })
      await expect(statusIndicator).toHaveClass(/disconnected/)
      
      // Should show retry option
      await expect(page.locator('[data-testid="reconnect-button"]')).toBeVisible()
    })

    test('attempts automatic reconnection', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate connection loss
      await page.evaluate(() => {
        window.mockWebSocket.disconnect()
      })
      
      // Should show disconnected
      const statusIndicator = page.locator('[data-testid="realtime-status"]')
      await expect(statusIndicator).toContainText('disconnected')
      
      // Simulate reconnection after delay
      await page.waitForTimeout(2000)
      await page.evaluate(() => {
        window.mockWebSocket.connect()
      })
      
      // Should show connected again
      await expect(statusIndicator).toContainText('connected')
    })

    test('allows manual reconnection', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      
      // Mock initial connection failure
      await page.evaluate(() => {
        window.mockWebSocket.connected = false
      })
      
      // Click reconnect button
      await page.locator('[data-testid="reconnect-button"]').click()
      
      // Should attempt reconnection
      await page.evaluate(() => {
        window.mockWebSocket.connect()
      })
      
      // Should show connected
      await waitForRealtimeConnection(page)
      const statusIndicator = page.locator('[data-testid="realtime-status"]')
      await expect(statusIndicator).toContainText('connected')
    })
  })

  test.describe('Live Community Statistics', () => {
    test('updates community stats in real-time', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Get initial stats
      const membersCount = page.locator('[data-testid="total-members"]')
      const challengesCount = page.locator('[data-testid="active-challenges"]')
      
      await expect(membersCount).toContainText('2847')
      await expect(challengesCount).toContainText('8')
      
      // Simulate real-time stats update
      await simulateRealtimeUpdate(page, 'stats-update', mockRealtimeUpdates.statsUpdate)
      
      // Should update to new values
      await waitForStatUpdate('[data-testid="total-members"]', '2848')
      await waitForStatUpdate('[data-testid="active-challenges"]', '9')
      
      await expect(membersCount).toContainText('2848')
      await expect(challengesCount).toContainText('9')
    })

    test('animates stat counter changes', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      const membersCount = page.locator('[data-testid="total-members"]')
      
      // Simulate stats update
      await simulateRealtimeUpdate(page, 'stats-update', {
        totalMembers: 2850 // +3 from initial
      })
      
      // Should show animation class during update
      await expect(membersCount).toHaveClass(/updating/, { timeout: 2000 })
      
      // Should settle on new value
      await waitForStatUpdate('[data-testid="total-members"]', '2850')
    })

    test('shows growth indicators for positive changes', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate growth update
      await simulateRealtimeUpdate(page, 'stats-update', {
        weeklyGrowth: 18 // Increased from 15%
      })
      
      // Should show growth indicator
      const growthIndicator = page.locator('[data-testid="growth-indicator"]')
      await expect(growthIndicator).toContainText('+18%')
      await expect(growthIndicator).toHaveClass(/positive/)
    })

    test('updates online user count frequently', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      const onlineUsers = page.locator('[data-testid="online-users"]')
      await expect(onlineUsers).toContainText('234')
      
      // Simulate frequent online user updates
      for (let i = 235; i <= 238; i++) {
        await simulateRealtimeUpdate(page, 'online-users-update', { onlineUsers: i })
        await waitForStatUpdate('[data-testid="online-users"]', i.toString())
        await page.waitForTimeout(500)
      }
      
      await expect(onlineUsers).toContainText('238')
    })

    test('handles stat update errors gracefully', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate malformed stats update
      await simulateRealtimeUpdate(page, 'stats-update', { invalid: 'data' })
      
      // Should maintain previous stats
      await expect(page.locator('[data-testid="total-members"]')).toContainText('2847')
      
      // Should not crash or show errors
      await expect(page.locator('[data-testid="community-hero"]')).toBeVisible()
    })
  })

  test.describe('Real-time Activity Feed', () => {
    test('displays new activities in real-time', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Get initial activity count
      const activities = page.locator('[data-testid="activity-item"]')
      const initialCount = await activities.count()
      
      // Simulate new activity
      await simulateRealtimeUpdate(page, 'new-activity', mockRealtimeUpdates.newActivity)
      
      // Should add new activity to feed
      await expect(activities).toHaveCount(initialCount + 1, { timeout: REALTIME_UPDATE_TIMEOUT })
      
      // Should show new activity at top
      const firstActivity = activities.first()
      await expect(firstActivity).toContainText('LiveTester')
      await expect(firstActivity).toContainText('New Real-time Submission')
    })

    test('animates new activity appearance', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate new activity
      await simulateRealtimeUpdate(page, 'new-activity', mockRealtimeUpdates.newActivity)
      
      // Should show animation for new activity
      const newActivity = page.locator('[data-testid="activity-item"]').first()
      await expect(newActivity).toHaveClass(/new-activity/, { timeout: 2000 })
      
      // Animation should fade out
      await expect(newActivity).not.toHaveClass(/new-activity/, { timeout: 3000 })
    })

    test('updates activity engagement in real-time', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Find activity with like button
      const firstActivity = page.locator('[data-testid="activity-item"]').first()
      const likeCount = firstActivity.locator('[data-testid="like-count"]')
      
      // Get initial like count
      const initialLikes = await likeCount.textContent()
      
      // Simulate like update
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('activity-engagement-update', {
          detail: {
            activityId: 'activity-001',
            engagement: { likes: 6, comments: 2, shares: 1 }
          }
        }))
      })
      
      // Should update like count
      await expect(likeCount).toContainText('6', { timeout: REALTIME_UPDATE_TIMEOUT })
    })

    test('maintains activity feed scroll position during updates', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Scroll down in activity feed
      const activityFeed = page.locator('[data-testid="activity-feed"]')
      await activityFeed.hover()
      await page.mouse.wheel(0, 300)
      
      const scrollPosition = await activityFeed.evaluate(el => el.scrollTop)
      expect(scrollPosition).toBeGreaterThan(200)
      
      // Add new activity
      await simulateRealtimeUpdate(page, 'new-activity', mockRealtimeUpdates.newActivity)
      
      // Should maintain scroll position
      await page.waitForTimeout(1000)
      const newScrollPosition = await activityFeed.evaluate(el => el.scrollTop)
      expect(newScrollPosition).toBeCloseTo(scrollPosition, 50) // Allow 50px tolerance
    })

    test('limits activity feed to prevent memory issues', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Add many activities rapidly
      for (let i = 0; i < 20; i++) {
        await simulateRealtimeUpdate(page, 'new-activity', {
          ...mockRealtimeUpdates.newActivity,
          id: `activity-${i}`,
          content: {
            title: `Activity ${i}`,
            description: `Test activity number ${i}`
          }
        })
        await page.waitForTimeout(100)
      }
      
      // Should limit activities (e.g., max 50 activities)
      const activities = page.locator('[data-testid="activity-item"]')
      const activityCount = await activities.count()
      expect(activityCount).toBeLessThanOrEqual(50)
    })

    test('shows activity loading states', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      
      // Should show loading state initially
      await expect(page.locator('[data-testid="activity-loading"]')).toBeVisible()
      
      await waitForRealtimeConnection(page)
      
      // Should hide loading and show activities
      await expect(page.locator('[data-testid="activity-loading"]')).not.toBeVisible()
      await expect(page.locator('[data-testid="activity-item"]')).toHaveCount(3, { timeout: 5000 })
    })
  })

  test.describe('Live Leaderboard Updates', () => {
    test('updates leaderboard rankings in real-time', async ({ page }) => {
      await page.goto(COMMUNITY_LEADERBOARD_URL)
      await waitForRealtimeConnection(page)
      
      // Check initial leaderboard
      const leaderboardItems = page.locator('[data-testid="leaderboard-item"]')
      await expect(leaderboardItems).toHaveCount(3, { timeout: 5000 })
      
      // Simulate leaderboard update
      await simulateRealtimeUpdate(page, 'leaderboard-update', mockRealtimeUpdates.leaderboardUpdate)
      
      // Should update leaderboard
      await expect(leaderboardItems).toHaveCount(4, { timeout: REALTIME_UPDATE_TIMEOUT })
      
      // Should show new member in correct position
      const newMember = page.locator('[data-testid="leaderboard-item"]').filter({ hasText: 'LiveTester' })
      await expect(newMember).toBeVisible()
    })

    test('animates rank changes', async ({ page }) => {
      await page.goto(COMMUNITY_LEADERBOARD_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate rank change
      await simulateRealtimeUpdate(page, 'rank-change', {
        userId: 'user_artisan_001',
        newRank: 1,
        oldRank: 3,
        change: 2
      })
      
      // Should show rank change animation
      const memberItem = page.locator('[data-testid="leaderboard-item"]').filter({ hasText: 'WinterCrafter' })
      await expect(memberItem).toHaveClass(/rank-up/, { timeout: 2000 })
      
      // Should show rank change indicator
      const rankChange = memberItem.locator('[data-testid="rank-change"]')
      await expect(rankChange).toContainText('+2')
      await expect(rankChange).toHaveClass(/positive/)
    })

    test('updates member points in real-time', async ({ page }) => {
      await page.goto(COMMUNITY_LEADERBOARD_URL)
      await waitForRealtimeConnection(page)
      
      // Find member points
      const memberItem = page.locator('[data-testid="leaderboard-item"]').first()
      const pointsElement = memberItem.locator('[data-testid="member-points"]')
      
      // Simulate points update
      await simulateRealtimeUpdate(page, 'points-update', {
        userId: 'user_artisan_001',
        newPoints: 3500,
        pointsGained: 50
      })
      
      // Should update points with animation
      await expect(pointsElement).toContainText('3500', { timeout: REALTIME_UPDATE_TIMEOUT })
      await expect(pointsElement).toHaveClass(/points-updated/)
    })

    test('shows live activity indicators', async ({ page }) => {
      await page.goto(COMMUNITY_LEADERBOARD_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate member online status
      await simulateRealtimeUpdate(page, 'member-online', {
        userId: 'user_artisan_001',
        isOnline: true
      })
      
      // Should show online indicator
      const memberItem = page.locator('[data-testid="leaderboard-item"]').filter({ hasText: 'WinterCrafter' })
      await expect(memberItem.locator('[data-testid="online-indicator"]')).toBeVisible()
      await expect(memberItem.locator('[data-testid="online-indicator"]')).toHaveClass(/online/)
    })
  })

  test.describe('Real-time Submission Interactions', () => {
    test('updates submission likes in real-time', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Find submission with like button
      const submissionCard = page.locator('[data-testid="submission-card"]').first()
      const likeButton = submissionCard.locator('[data-testid="like-button"]')
      const likeCount = submissionCard.locator('[data-testid="like-count"]')
      
      // Get initial like count
      await expect(likeCount).toContainText('134')
      
      // Click like button
      await likeButton.click()
      
      // Should immediately update
      await expect(likeCount).toContainText('135')
      await expect(likeButton).toHaveClass(/liked/)
      
      // Simulate real-time update from other user
      await triggerLikeUpdate(page, 'sub-001')
      
      // Should update again
      await expect(likeCount).toContainText('136', { timeout: REALTIME_UPDATE_TIMEOUT })
    })

    test('shows real-time comment counts', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      const submissionCard = page.locator('[data-testid="submission-card"]').first()
      const commentCount = submissionCard.locator('[data-testid="comment-count"]')
      
      await expect(commentCount).toContainText('45')
      
      // Simulate new comment
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('comment-update', {
          detail: {
            submissionId: 'sub-001',
            newCommentCount: 46
          }
        }))
      })
      
      // Should update comment count
      await expect(commentCount).toContainText('46', { timeout: REALTIME_UPDATE_TIMEOUT })
    })

    test('updates view counts for submissions', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      const submissionCard = page.locator('[data-testid="submission-card"]').first()
      const viewCount = submissionCard.locator('[data-testid="view-count"]')
      
      // Simulate view count update
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('view-update', {
          detail: {
            submissionId: 'sub-001',
            newViewCount: 1251
          }
        }))
      })
      
      // Should update view count
      await expect(viewCount).toContainText('1251', { timeout: REALTIME_UPDATE_TIMEOUT })
    })
  })

  test.describe('Real-time Challenge Updates', () => {
    test('updates challenge participation in real-time', async ({ page }) => {
      await page.goto('/community/challenges')
      await waitForRealtimeConnection(page)
      
      const challengeCard = page.locator('[data-testid="challenge-card"]').first()
      const participantCount = challengeCard.locator('[data-testid="participant-count"]')
      
      await expect(participantCount).toContainText('47')
      
      // Simulate new participant
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('challenge-update', {
          detail: {
            challengeId: 'keycap-artistry-2024',
            newParticipantCount: 48
          }
        }))
      })
      
      // Should update participant count
      await expect(participantCount).toContainText('48', { timeout: REALTIME_UPDATE_TIMEOUT })
    })

    test('shows real-time challenge countdown', async ({ page }) => {
      await page.goto('/community/challenges')
      await waitForRealtimeConnection(page)
      
      const challengeCard = page.locator('[data-testid="challenge-card"]').first()
      const countdown = challengeCard.locator('[data-testid="challenge-countdown"]')
      
      // Should show countdown timer
      await expect(countdown).toBeVisible()
      
      // Wait for countdown to update (should update every second)
      const initialTime = await countdown.textContent()
      await page.waitForTimeout(2000)
      const updatedTime = await countdown.textContent()
      
      // Time should have changed
      expect(initialTime).not.toBe(updatedTime)
    })

    test('updates challenge submission counts', async ({ page }) => {
      await page.goto('/community/challenges')
      await waitForRealtimeConnection(page)
      
      const challengeCard = page.locator('[data-testid="challenge-card"]').first()
      const submissionCount = challengeCard.locator('[data-testid="submission-count"]')
      
      // Simulate new submission
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('challenge-submission', {
          detail: {
            challengeId: 'keycap-artistry-2024',
            newSubmissionCount: 48
          }
        }))
      })
      
      // Should update submission count
      await expect(submissionCount).toContainText('48', { timeout: REALTIME_UPDATE_TIMEOUT })
    })
  })

  test.describe('Performance and Error Handling', () => {
    test('handles high-frequency updates efficiently', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate rapid updates
      for (let i = 0; i < 50; i++) {
        await simulateRealtimeUpdate(page, 'stats-update', {
          onlineUsers: 234 + i
        })
        await page.waitForTimeout(50)
      }
      
      // Should handle updates without crashing
      await expect(page.locator('[data-testid="community-hero"]')).toBeVisible()
      
      // Final value should be correct
      await expect(page.locator('[data-testid="online-users"]')).toContainText('283')
    })

    test('throttles update animations to prevent performance issues', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      const membersCount = page.locator('[data-testid="total-members"]')
      
      // Simulate rapid stat updates
      for (let i = 0; i < 10; i++) {
        await simulateRealtimeUpdate(page, 'stats-update', {
          totalMembers: 2847 + i
        })
        await page.waitForTimeout(100)
      }
      
      // Should not have excessive animation classes
      const hasAnimation = await membersCount.evaluate(el => 
        el.classList.contains('updating')
      )
      
      // Should settle on final value
      await expect(membersCount).toContainText('2856')
    })

    test('handles malformed real-time data gracefully', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Send malformed data
      await simulateRealtimeUpdate(page, 'invalid-update', {
        corrupted: 'data',
        missing: null,
        undefined: undefined
      })
      
      // Should not crash
      await expect(page.locator('[data-testid="community-hero"]')).toBeVisible()
      
      // Should maintain existing data
      await expect(page.locator('[data-testid="total-members"]')).toContainText('2847')
    })

    test('maintains real-time features during network fluctuations', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Simulate network interruption
      await page.evaluate(() => {
        window.mockWebSocket.disconnect()
      })
      
      // Should show disconnected state
      const statusIndicator = page.locator('[data-testid="realtime-status"]')
      await expect(statusIndicator).toContainText('disconnected')
      
      // Reconnect
      await page.evaluate(() => {
        window.mockWebSocket.connect()
      })
      
      // Should resume real-time updates
      await waitForRealtimeConnection(page)
      
      // Test update after reconnection
      await simulateRealtimeUpdate(page, 'stats-update', {
        totalMembers: 2849
      })
      
      await expect(page.locator('[data-testid="total-members"]')).toContainText('2849')
    })

    test('cleans up real-time subscriptions on page leave', async ({ page }) => {
      await page.goto(COMMUNITY_DISCOVER_URL)
      await waitForRealtimeConnection(page)
      
      // Navigate away
      await page.goto('/')
      
      // Should clean up connections
      const isConnected = await page.evaluate(() => {
        return window.mockWebSocket && window.mockWebSocket.connected
      })
      
      expect(isConnected).toBe(false)
    })
  })
})