/**
 * End-to-End Tests for Community Search Functionality
 * 
 * Tests the comprehensive search system including:
 * - Multi-collection search across submissions, discussions, challenges, members
 * - Search filters and content type selection
 * - Trending searches and suggestions
 * - Real-time search results
 * - Search performance and debouncing
 * 
 * <AUTHOR> Team
 */

import { test, expect, Page } from '@playwright/test'

const COMMUNITY_SEARCH_URL = '/community/discover'
const SEARCH_PERFORMANCE_THRESHOLD = 500 // 500ms max search response time

// Mock search data for consistent testing
const mockSearchResults = {
  submissions: [
    {
      id: 'sub-001',
      title: 'Sakura Cherry Blossom Artisan',
      description: 'Hand-sculpted cherry blossom keycap with translucent resin petals',
      category: 'Artisan Keycap',
      author: 'ArtisanMaster',
      likes: 134,
      image: '/images/submissions/sakura-keycap.jpg'
    },
    {
      id: 'sub-002',
      title: 'RGB Gaming Keyboard Setup',
      description: 'Full RGB mechanical keyboard with custom lighting effects',
      category: 'Setup Showcase',
      author: 'RGBMaster',
      likes: 89,
      image: '/images/submissions/rgb-setup.jpg'
    }
  ],
  discussions: [
    {
      id: 'disc-001',
      title: 'Best practices for artisan keycap creation',
      content: 'Looking for tips on creating professional-quality artisan keycaps',
      author: 'CraftingNoob',
      replies: 23,
      category: 'Techniques'
    }
  ],
  challenges: [
    {
      id: 'chal-001',
      title: 'Winter Artisan Challenge 2024',
      description: 'Create winter-themed artisan keycaps',
      participants: 47,
      status: 'active'
    }
  ],
  members: [
    {
      id: 'user-001',
      username: 'ArtisanMaster',
      level: 12,
      badges: ['Artisan Expert', 'Community Helper'],
      avatar: '/avatars/artisan-master.jpg'
    }
  ]
}

const mockTrendingSearches = [
  'artisan keycap',
  'rgb keyboard',
  'mechanical switches',
  'custom build'
]

// Helper functions
const waitForSearchInterface = async (page: Page) => {
  await page.waitForSelector('[data-testid="community-search"]', { timeout: 10000 })
  await page.waitForSelector('[data-testid="search-input"]', { timeout: 5000 })
}

const performSearch = async (page: Page, query: string) => {
  const searchInput = page.locator('[data-testid="search-input"]')
  await searchInput.fill(query)
  
  // Wait for debounced search
  await page.waitForTimeout(400)
  
  // Wait for results to appear
  await page.waitForSelector('[data-testid="search-results"]', { timeout: 3000 })
}

const clearSearch = async (page: Page) => {
  const searchInput = page.locator('[data-testid="search-input"]')
  await searchInput.clear()
  await page.waitForTimeout(200)
}

test.describe('Community Search E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/')
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('test-user', JSON.stringify({
        uid: 'test-user-123',
        email: '<EMAIL>',
        displayName: 'Test Searcher'
      }))
    })

    // Mock search data for consistent testing
    await page.addInitScript(() => {
      window.mockSearchResults = {
        submissions: [
          {
            id: 'sub-001',
            title: 'Sakura Cherry Blossom Artisan',
            description: 'Hand-sculpted cherry blossom keycap with translucent resin petals',
            category: 'Artisan Keycap',
            author: 'ArtisanMaster',
            likes: 134,
            image: '/images/submissions/sakura-keycap.jpg'
          }
        ],
        discussions: [
          {
            id: 'disc-001',
            title: 'Best practices for artisan keycap creation',
            content: 'Looking for tips on creating professional-quality artisan keycaps',
            author: 'CraftingNoob',
            replies: 23
          }
        ],
        challenges: [],
        members: []
      }
      
      window.mockTrendingSearches = [
        'artisan keycap',
        'rgb keyboard',
        'mechanical switches'
      ]
    })

    await page.goto(COMMUNITY_SEARCH_URL)
    await waitForSearchInterface(page)
  })

  test.describe('Search Interface', () => {
    test('displays search interface correctly', async ({ page }) => {
      // Check for search input
      await expect(page.locator('[data-testid="search-input"]')).toBeVisible()
      await expect(page.locator('[data-testid="search-input"]')).toHaveAttribute('placeholder', /Search community content/i)
      
      // Check for search filters
      await expect(page.locator('[data-testid="search-filters"]')).toBeVisible()
      
      // Check for trending searches
      await expect(page.locator('[data-testid="trending-searches"]')).toBeVisible()
      
      // Check for search icon
      await expect(page.locator('[data-testid="search-icon"]')).toBeVisible()
    })

    test('shows trending searches by default', async ({ page }) => {
      const trendingContainer = page.locator('[data-testid="trending-searches"]')
      await expect(trendingContainer).toBeVisible()
      
      // Should show trending search items
      await expect(trendingContainer.locator('[data-testid="trending-item"]')).toHaveCount(3, { timeout: 5000 })
      
      // Check for trending search text
      await expect(page.locator('text=Trending Searches')).toBeVisible()
    })

    test('allows clicking on trending searches', async ({ page }) => {
      // Click on first trending search
      const firstTrending = page.locator('[data-testid="trending-item"]').first()
      await expect(firstTrending).toBeVisible()
      
      const trendingText = await firstTrending.textContent()
      await firstTrending.click()
      
      // Should populate search input
      await expect(page.locator('[data-testid="search-input"]')).toHaveValue(trendingText?.trim() || '')
      
      // Should show search results
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible({ timeout: 2000 })
    })

    test('displays content type filters', async ({ page }) => {
      const filtersContainer = page.locator('[data-testid="search-filters"]')
      await expect(filtersContainer).toBeVisible()
      
      // Check for filter options
      await expect(page.locator('[data-testid="filter-all"]')).toBeVisible()
      await expect(page.locator('[data-testid="filter-submissions"]')).toBeVisible()
      await expect(page.locator('[data-testid="filter-discussions"]')).toBeVisible()
      await expect(page.locator('[data-testid="filter-challenges"]')).toBeVisible()
      await expect(page.locator('[data-testid="filter-members"]')).toBeVisible()
    })
  })

  test.describe('Search Functionality', () => {
    test('performs basic search with results', async ({ page }) => {
      const startTime = Date.now()
      
      await performSearch(page, 'artisan')
      
      const searchTime = Date.now() - startTime
      
      // Should show search results
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
      
      // Should have result items
      await expect(page.locator('[data-testid="result-item"]')).toHaveCount(2, { timeout: 3000 })
      
      // Performance check
      expect(searchTime).toBeLessThan(SEARCH_PERFORMANCE_THRESHOLD)
      
      console.log(`Search completed in ${searchTime}ms`)
    })

    test('shows appropriate results for different content types', async ({ page }) => {
      await performSearch(page, 'keycap')
      
      // Should show mixed results
      const results = page.locator('[data-testid="result-item"]')
      await expect(results).toHaveCount(2, { timeout: 3000 })
      
      // Check for submission result
      const submissionResult = results.filter({ hasText: 'Sakura Cherry Blossom Artisan' })
      await expect(submissionResult).toBeVisible()
      
      // Check for discussion result
      const discussionResult = results.filter({ hasText: 'Best practices for artisan keycap creation' })
      await expect(discussionResult).toBeVisible()
    })

    test('handles empty search results', async ({ page }) => {
      await performSearch(page, 'nonexistent query xyz')
      
      // Should show no results message
      await expect(page.locator('[data-testid="no-results"]')).toBeVisible({ timeout: 3000 })
      await expect(page.locator('text=No results found')).toBeVisible()
      
      // Should suggest trying different terms
      await expect(page.locator('text=Try different search terms')).toBeVisible()
    })

    test('performs search with debouncing', async ({ page }) => {
      const searchInput = page.locator('[data-testid="search-input"]')
      
      // Type rapidly
      await searchInput.type('a', { delay: 50 })
      await searchInput.type('r', { delay: 50 })
      await searchInput.type('t', { delay: 50 })
      await searchInput.type('i', { delay: 50 })
      await searchInput.type('s', { delay: 50 })
      await searchInput.type('a', { delay: 50 })
      await searchInput.type('n', { delay: 50 })
      
      // Should not trigger search immediately
      await page.waitForTimeout(200)
      
      // Results should not be visible yet
      const resultsVisible = await page.locator('[data-testid="search-results"]').isVisible()
      expect(resultsVisible).toBe(false)
      
      // Wait for debounce delay
      await page.waitForTimeout(400)
      
      // Now results should appear
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    })

    test('clears results when search is cleared', async ({ page }) => {
      // Perform search first
      await performSearch(page, 'artisan')
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
      
      // Clear search
      await clearSearch(page)
      
      // Results should be hidden
      await expect(page.locator('[data-testid="search-results"]')).not.toBeVisible()
      
      // Trending searches should reappear
      await expect(page.locator('[data-testid="trending-searches"]')).toBeVisible()
    })
  })

  test.describe('Content Type Filtering', () => {
    test('filters by submissions only', async ({ page }) => {
      await performSearch(page, 'keycap')
      
      // Click submissions filter
      await page.locator('[data-testid="filter-submissions"]').click()
      
      // Should show only submission results
      const results = page.locator('[data-testid="result-item"]')
      await expect(results).toHaveCount(1, { timeout: 3000 })
      
      // Should show submission type indicator
      await expect(results.first().locator('[data-testid="result-type"]')).toContainText('Submission')
    })

    test('filters by discussions only', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Click discussions filter
      await page.locator('[data-testid="filter-discussions"]').click()
      
      // Should show only discussion results
      const results = page.locator('[data-testid="result-item"]')
      await expect(results).toHaveCount(1, { timeout: 3000 })
      
      // Should show discussion type indicator
      await expect(results.first().locator('[data-testid="result-type"]')).toContainText('Discussion')
    })

    test('returns to all results when "All" filter is selected', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Filter by submissions first
      await page.locator('[data-testid="filter-submissions"]').click()
      await expect(page.locator('[data-testid="result-item"]')).toHaveCount(1)
      
      // Switch back to all
      await page.locator('[data-testid="filter-all"]').click()
      
      // Should show all results again
      await expect(page.locator('[data-testid="result-item"]')).toHaveCount(2, { timeout: 3000 })
    })

    test('maintains filter selection during search', async ({ page }) => {
      // Set filter first
      await page.locator('[data-testid="filter-discussions"]').click()
      
      // Perform search
      await performSearch(page, 'best practices')
      
      // Should maintain discussion filter
      await expect(page.locator('[data-testid="filter-discussions"]')).toHaveAttribute('aria-pressed', 'true')
      
      // Should show only discussion results
      const results = page.locator('[data-testid="result-item"]')
      const resultType = results.first().locator('[data-testid="result-type"]')
      await expect(resultType).toContainText('Discussion')
    })

    test('shows filter counts', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Should show result counts for each filter
      await expect(page.locator('[data-testid="filter-all"] [data-testid="filter-count"]')).toContainText('2')
      await expect(page.locator('[data-testid="filter-submissions"] [data-testid="filter-count"]')).toContainText('1')
      await expect(page.locator('[data-testid="filter-discussions"] [data-testid="filter-count"]')).toContainText('1')
      await expect(page.locator('[data-testid="filter-challenges"] [data-testid="filter-count"]')).toContainText('0')
      await expect(page.locator('[data-testid="filter-members"] [data-testid="filter-count"]')).toContainText('0')
    })
  })

  test.describe('Search Results Display', () => {
    test('displays submission results correctly', async ({ page }) => {
      await performSearch(page, 'sakura')
      
      const submissionResult = page.locator('[data-testid="result-item"]').filter({ hasText: 'Sakura' })
      
      // Should show submission details
      await expect(submissionResult.locator('[data-testid="result-title"]')).toContainText('Sakura Cherry Blossom Artisan')
      await expect(submissionResult.locator('[data-testid="result-description"]')).toContainText('Hand-sculpted cherry blossom keycap')
      await expect(submissionResult.locator('[data-testid="result-author"]')).toContainText('ArtisanMaster')
      await expect(submissionResult.locator('[data-testid="result-likes"]')).toContainText('134')
      
      // Should show submission image
      await expect(submissionResult.locator('[data-testid="result-image"]')).toBeVisible()
      
      // Should show content type badge
      await expect(submissionResult.locator('[data-testid="result-type"]')).toContainText('Submission')
    })

    test('displays discussion results correctly', async ({ page }) => {
      await performSearch(page, 'best practices')
      
      const discussionResult = page.locator('[data-testid="result-item"]').filter({ hasText: 'Best practices' })
      
      // Should show discussion details
      await expect(discussionResult.locator('[data-testid="result-title"]')).toContainText('Best practices for artisan keycap creation')
      await expect(discussionResult.locator('[data-testid="result-content"]')).toContainText('Looking for tips')
      await expect(discussionResult.locator('[data-testid="result-author"]')).toContainText('CraftingNoob')
      await expect(discussionResult.locator('[data-testid="result-replies"]')).toContainText('23 replies')
      
      // Should show content type badge
      await expect(discussionResult.locator('[data-testid="result-type"]')).toContainText('Discussion')
    })

    test('makes result items clickable', async ({ page }) => {
      await performSearch(page, 'sakura')
      
      const firstResult = page.locator('[data-testid="result-item"]').first()
      await expect(firstResult).toBeVisible()
      
      // Click on result
      await firstResult.click()
      
      // Should navigate to detail page or open modal
      await expect(page.locator('[data-testid="content-detail"]')).toBeVisible({ timeout: 3000 })
    })

    test('shows search query highlighting', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Should highlight search terms in results
      await expect(page.locator('[data-testid="highlighted-text"]')).toHaveCount(2, { timeout: 3000 })
      
      // Highlighted text should match search query
      const highlightedElements = page.locator('[data-testid="highlighted-text"]')
      for (let i = 0; i < await highlightedElements.count(); i++) {
        const text = await highlightedElements.nth(i).textContent()
        expect(text?.toLowerCase()).toContain('artisan')
      }
    })
  })

  test.describe('Search Performance', () => {
    test('meets search response time requirements', async ({ page }) => {
      const searchQueries = ['artisan', 'keyboard', 'rgb', 'keycap', 'discussion']
      
      for (const query of searchQueries) {
        const startTime = Date.now()
        
        await clearSearch(page)
        await performSearch(page, query)
        
        const searchTime = Date.now() - startTime
        
        expect(searchTime).toBeLessThan(SEARCH_PERFORMANCE_THRESHOLD)
        console.log(`Search "${query}" completed in ${searchTime}ms`)
      }
    })

    test('handles rapid search queries', async ({ page }) => {
      const queries = ['a', 'ar', 'art', 'arti', 'artis', 'artisa', 'artisan']
      
      for (const query of queries) {
        await page.locator('[data-testid="search-input"]').fill(query)
        await page.waitForTimeout(50) // Rapid typing simulation
      }
      
      // Wait for final debounced search
      await page.waitForTimeout(400)
      
      // Should show results for final query
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    })

    test('caches search results efficiently', async ({ page }) => {
      // Perform initial search
      const startTime1 = Date.now()
      await performSearch(page, 'artisan')
      const firstSearchTime = Date.now() - startTime1
      
      // Clear and search again
      await clearSearch(page)
      
      const startTime2 = Date.now()
      await performSearch(page, 'artisan')
      const secondSearchTime = Date.now() - startTime2
      
      // Second search should be faster (cached)
      console.log(`First search: ${firstSearchTime}ms, Second search: ${secondSearchTime}ms`)
      
      // Both should still meet performance requirements
      expect(firstSearchTime).toBeLessThan(SEARCH_PERFORMANCE_THRESHOLD)
      expect(secondSearchTime).toBeLessThan(SEARCH_PERFORMANCE_THRESHOLD)
    })
  })

  test.describe('Accessibility and Keyboard Navigation', () => {
    test('supports keyboard navigation', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Tab to first result
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab') // Skip search input
      
      // Should focus on first result
      const firstResult = page.locator('[data-testid="result-item"]').first()
      await expect(firstResult).toBeFocused()
      
      // Arrow down to next result
      await page.keyboard.press('ArrowDown')
      
      // Should focus on second result
      const secondResult = page.locator('[data-testid="result-item"]').nth(1)
      await expect(secondResult).toBeFocused()
      
      // Enter should activate result
      await page.keyboard.press('Enter')
      await expect(page.locator('[data-testid="content-detail"]')).toBeVisible({ timeout: 3000 })
    })

    test('has proper ARIA labels and roles', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Search container should have search role
      await expect(page.locator('[data-testid="community-search"]')).toHaveAttribute('role', 'search')
      
      // Search input should have proper labels
      await expect(page.locator('[data-testid="search-input"]')).toHaveAttribute('aria-label')
      
      // Results should have list role
      await expect(page.locator('[data-testid="search-results"]')).toHaveAttribute('role', 'list')
      
      // Result items should have listitem role
      const resultItems = page.locator('[data-testid="result-item"]')
      for (let i = 0; i < await resultItems.count(); i++) {
        await expect(resultItems.nth(i)).toHaveAttribute('role', 'listitem')
      }
    })

    test('announces search results to screen readers', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Should have live region for announcing results
      await expect(page.locator('[aria-live="polite"]')).toBeVisible()
      
      // Should announce result count
      const announcement = page.locator('[data-testid="search-announcement"]')
      await expect(announcement).toContainText('2 results found')
    })

    test('provides clear focus indicators', async ({ page }) => {
      await performSearch(page, 'artisan')
      
      // Tab through interactive elements
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      // Should have visible focus indicator
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
      
      // Focus should be clearly visible
      const focusStyles = await focusedElement.evaluate((el) => {
        const styles = getComputedStyle(el)
        return {
          outline: styles.outline,
          boxShadow: styles.boxShadow
        }
      })
      
      // Should have focus styling (outline or box-shadow)
      expect(focusStyles.outline !== 'none' || focusStyles.boxShadow !== 'none').toBe(true)
    })
  })

  test.describe('Error Handling', () => {
    test('handles search API failures', async ({ page }) => {
      // Mock search API failure
      await page.route('**/search/**', route => route.abort())
      
      await performSearch(page, 'artisan')
      
      // Should show error message
      await expect(page.locator('[data-testid="search-error"]')).toBeVisible({ timeout: 3000 })
      await expect(page.locator('text=Unable to search at this time')).toBeVisible()
      
      // Should show retry option
      await expect(page.locator('[data-testid="search-retry"]')).toBeVisible()
    })

    test('recovers from temporary search failures', async ({ page }) => {
      let requestCount = 0
      
      // Simulate intermittent failures
      await page.route('**/search/**', route => {
        requestCount++
        if (requestCount <= 1) {
          route.abort()
        } else {
          route.continue()
        }
      })
      
      await performSearch(page, 'artisan')
      
      // Should eventually show results
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible({ timeout: 5000 })
    })

    test('handles malformed search queries gracefully', async ({ page }) => {
      const malformedQueries = ['', '   ', '!@#$%^&*()', '<script>', 'a'.repeat(1000)]
      
      for (const query of malformedQueries) {
        await clearSearch(page)
        await performSearch(page, query)
        
        // Should not crash or show errors
        await expect(page.locator('[data-testid="community-search"]')).toBeVisible()
        
        // Should handle gracefully
        const hasResults = await page.locator('[data-testid="search-results"]').isVisible()
        const hasNoResults = await page.locator('[data-testid="no-results"]').isVisible()
        const hasTrending = await page.locator('[data-testid="trending-searches"]').isVisible()
        
        // Should show one of: results, no results, or trending
        expect(hasResults || hasNoResults || hasTrending).toBe(true)
      }
    })
  })
})