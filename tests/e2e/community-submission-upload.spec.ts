/**
 * End-to-End Tests for Community Submission Upload Flow
 * 
 * Tests the complete submission upload process including:
 * - File selection and drag & drop
 * - Image validation and preview
 * - Form completion and submission
 * - Progress tracking and error handling
 * - Upload success and navigation
 * 
 * <AUTHOR> Team
 */

import { test, expect, Page } from '@playwright/test'
import path from 'path'

const SUBMISSION_UPLOAD_URL = '/community/submissions/upload'
const PERFORMANCE_THRESHOLDS = {
  pageLoad: 3000,
  fileUpload: 10000,
  formSubmission: 5000
}

// Test file paths (relative to test directory)
const TEST_FILES = {
  validImage: 'fixtures/test-keycap.jpg',
  largeImage: 'fixtures/large-image.jpg',
  invalidFile: 'fixtures/test-document.pdf',
  multipleImages: [
    'fixtures/test-keycap-1.jpg',
    'fixtures/test-keycap-2.jpg',
    'fixtures/test-keycap-3.jpg'
  ]
}

// Mock upload data
const mockSubmissionData = {
  title: 'Test Artisan Keycap',
  description: 'A beautiful hand-crafted artisan keycap for testing purposes',
  category: 'Artisan Keycap',
  tags: ['handmade', 'resin', 'artisan', 'test'],
  challengeId: 'winter-challenge-2024'
}

// Helper functions
const waitForUploadPageLoad = async (page: Page) => {
  await page.waitForSelector('[data-testid="upload-page"]', { timeout: 10000 })
  await page.waitForLoadState('networkidle')
}

const fillSubmissionForm = async (page: Page, data = mockSubmissionData) => {
  await page.locator('[data-testid="title-input"]').fill(data.title)
  await page.locator('[data-testid="description-textarea"]').fill(data.description)
  await page.locator('[data-testid="category-select"]').selectOption(data.category)
  
  // Add tags
  for (const tag of data.tags) {
    await page.locator('[data-testid="tag-input"]').fill(tag)
    await page.keyboard.press('Enter')
  }
}

const uploadFile = async (page: Page, filePath: string) => {
  const fileUpload = page.locator('[data-testid="file-input"]')
  await fileUpload.setInputFiles(path.join(__dirname, '..', filePath))
}

const uploadMultipleFiles = async (page: Page, filePaths: string[]) => {
  const fullPaths = filePaths.map(filePath => path.join(__dirname, '..', filePath))
  const fileUpload = page.locator('[data-testid="file-input"]')
  await fileUpload.setInputFiles(fullPaths)
}

const simulateDragAndDrop = async (page: Page, filePath: string) => {
  const dropZone = page.locator('[data-testid="drop-zone"]')
  
  // Create file for drag and drop simulation
  const fileBuffer = await require('fs').readFileSync(path.join(__dirname, '..', filePath))
  const file = new File([fileBuffer], path.basename(filePath), { type: 'image/jpeg' })
  
  // Simulate drag and drop
  await dropZone.dispatchEvent('dragenter')
  await dropZone.dispatchEvent('dragover')
  
  await page.evaluate((file) => {
    const dt = new DataTransfer()
    dt.items.add(file)
    
    const dropEvent = new DragEvent('drop', {
      dataTransfer: dt,
      bubbles: true,
      cancelable: true
    })
    
    document.querySelector('[data-testid="drop-zone"]')?.dispatchEvent(dropEvent)
  }, file)
}

test.describe('Community Submission Upload E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/')
    
    // Mock authentication - required for upload
    await page.evaluate(() => {
      localStorage.setItem('test-user', JSON.stringify({
        uid: 'test-user-123',
        email: '<EMAIL>',
        displayName: 'Test Creator',
        isAuthenticated: true
      }))
    })

    // Mock Firebase Storage for file uploads
    await page.addInitScript(() => {
      window.mockFirebaseStorage = {
        uploadProgress: 0,
        uploadComplete: false,
        uploadError: null
      }
    })

    await page.goto(SUBMISSION_UPLOAD_URL)
    await waitForUploadPageLoad(page)
  })

  test.describe('Page Access and Authentication', () => {
    test('loads upload page for authenticated users', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      const loadTime = Date.now() - startTime
      
      // Verify page loaded
      await expect(page.locator('[data-testid="upload-page"]')).toBeVisible()
      await expect(page.locator('h1')).toContainText('Submit Your Work')
      
      // Performance check
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad)
      
      console.log(`Upload page loaded in ${loadTime}ms`)
    })

    test('redirects unauthenticated users to login', async ({ page }) => {
      // Clear authentication
      await page.evaluate(() => {
        localStorage.removeItem('test-user')
      })
      
      await page.goto(SUBMISSION_UPLOAD_URL)
      
      // Should redirect to login or show auth prompt
      await expect(page.locator('[data-testid="auth-required"]')).toBeVisible({ timeout: 5000 })
      await expect(page.locator('text=Please sign in to submit')).toBeVisible()
    })

    test('displays user information when authenticated', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Should show user greeting
      await expect(page.locator('text=Test Creator')).toBeVisible()
      await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
    })
  })

  test.describe('Form Structure and Validation', () => {
    test('displays all required form fields', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Check for form fields
      await expect(page.locator('[data-testid="title-input"]')).toBeVisible()
      await expect(page.locator('[data-testid="description-textarea"]')).toBeVisible()
      await expect(page.locator('[data-testid="category-select"]')).toBeVisible()
      await expect(page.locator('[data-testid="tag-input"]')).toBeVisible()
      await expect(page.locator('[data-testid="file-upload-section"]')).toBeVisible()
      await expect(page.locator('[data-testid="submit-button"]')).toBeVisible()
    })

    test('validates required fields', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Try to submit empty form
      await page.locator('[data-testid="submit-button"]').click()
      
      // Should show validation errors
      await expect(page.locator('[data-testid="title-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="description-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="file-error"]')).toBeVisible()
      
      // Error messages should be descriptive
      await expect(page.locator('text=Title is required')).toBeVisible()
      await expect(page.locator('text=Description is required')).toBeVisible()
      await expect(page.locator('text=At least one image is required')).toBeVisible()
    })

    test('validates title length constraints', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Test title too short
      await page.locator('[data-testid="title-input"]').fill('Hi')
      await page.locator('[data-testid="submit-button"]').click()
      
      await expect(page.locator('text=Title must be at least 3 characters')).toBeVisible()
      
      // Test title too long
      const longTitle = 'A'.repeat(101)
      await page.locator('[data-testid="title-input"]').fill(longTitle)
      await page.locator('[data-testid="submit-button"]').click()
      
      await expect(page.locator('text=Title must be less than 100 characters')).toBeVisible()
    })

    test('validates description length', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Test description too short
      await page.locator('[data-testid="description-textarea"]').fill('Too short')
      await page.locator('[data-testid="submit-button"]').click()
      
      await expect(page.locator('text=Description must be at least 10 characters')).toBeVisible()
      
      // Test description too long
      const longDescription = 'A'.repeat(1001)
      await page.locator('[data-testid="description-textarea"]').fill(longDescription)
      await page.locator('[data-testid="submit-button"]').click()
      
      await expect(page.locator('text=Description must be less than 1000 characters')).toBeVisible()
    })

    test('displays category options', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Check category dropdown
      const categorySelect = page.locator('[data-testid="category-select"]')
      await categorySelect.click()
      
      // Should have category options
      await expect(page.locator('option[value="Artisan Keycap"]')).toBeVisible()
      await expect(page.locator('option[value="Setup Showcase"]')).toBeVisible()
      await expect(page.locator('option[value="Build Process"]')).toBeVisible()
      await expect(page.locator('option[value="Review"]')).toBeVisible()
    })

    test('handles tag input and display', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Add tags
      const tagInput = page.locator('[data-testid="tag-input"]')
      
      await tagInput.fill('handmade')
      await page.keyboard.press('Enter')
      
      await tagInput.fill('resin')
      await page.keyboard.press('Enter')
      
      // Should display tags
      await expect(page.locator('[data-testid="tag-item"]')).toHaveCount(2)
      await expect(page.locator('text=handmade')).toBeVisible()
      await expect(page.locator('text=resin')).toBeVisible()
      
      // Should allow tag removal
      await page.locator('[data-testid="remove-tag"]').first().click()
      await expect(page.locator('[data-testid="tag-item"]')).toHaveCount(1)
    })
  })

  test.describe('File Upload Interface', () => {
    test('displays file upload drop zone', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Check for upload interface
      await expect(page.locator('[data-testid="drop-zone"]')).toBeVisible()
      await expect(page.locator('[data-testid="file-input"]')).toBeVisible()
      await expect(page.locator('text=Drag & drop images here')).toBeVisible()
      await expect(page.locator('text=or click to browse')).toBeVisible()
      
      // Should show upload guidelines
      await expect(page.locator('text=Maximum 10 images')).toBeVisible()
      await expect(page.locator('text=15MB per file')).toBeVisible()
      await expect(page.locator('text=PNG, JPG, GIF formats')).toBeVisible()
    })

    test('handles single file upload via file input', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Upload file
      await uploadFile(page, TEST_FILES.validImage)
      
      // Should show file preview
      await expect(page.locator('[data-testid="file-preview"]')).toBeVisible({ timeout: 3000 })
      await expect(page.locator('[data-testid="preview-image"]')).toBeVisible()
      
      // Should show file details
      await expect(page.locator('[data-testid="file-name"]')).toContainText('test-keycap.jpg')
      await expect(page.locator('[data-testid="file-size"]')).toBeVisible()
    })

    test('handles multiple file uploads', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Upload multiple files
      await uploadMultipleFiles(page, TEST_FILES.multipleImages)
      
      // Should show all file previews
      await expect(page.locator('[data-testid="file-preview"]')).toHaveCount(3, { timeout: 5000 })
      
      // Should show file counter
      await expect(page.locator('[data-testid="file-counter"]')).toContainText('3 / 10 files')
    })

    test('validates file types', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Try to upload invalid file type
      await uploadFile(page, TEST_FILES.invalidFile)
      
      // Should show error
      await expect(page.locator('[data-testid="file-error"]')).toBeVisible({ timeout: 3000 })
      await expect(page.locator('text=Only image files are allowed')).toBeVisible()
      
      // Should not show preview
      await expect(page.locator('[data-testid="file-preview"]')).not.toBeVisible()
    })

    test('validates file size limits', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Mock large file upload
      await page.evaluate(() => {
        window.mockLargeFile = true
      })
      
      await uploadFile(page, TEST_FILES.largeImage)
      
      // Should show size error
      await expect(page.locator('text=File size exceeds 15MB limit')).toBeVisible({ timeout: 3000 })
    })

    test('handles drag and drop upload', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      const dropZone = page.locator('[data-testid="drop-zone"]')
      
      // Test drag enter effect
      await dropZone.dispatchEvent('dragenter')
      await expect(dropZone).toHaveClass(/drag-active/)
      
      // Test drag leave
      await dropZone.dispatchEvent('dragleave')
      await expect(dropZone).not.toHaveClass(/drag-active/)
      
      // Simulate file drop
      await simulateDragAndDrop(page, TEST_FILES.validImage)
      
      // Should show file preview
      await expect(page.locator('[data-testid="file-preview"]')).toBeVisible({ timeout: 3000 })
    })

    test('allows file removal', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Upload file
      await uploadFile(page, TEST_FILES.validImage)
      await expect(page.locator('[data-testid="file-preview"]')).toBeVisible()
      
      // Remove file
      await page.locator('[data-testid="remove-file"]').click()
      
      // Should remove preview
      await expect(page.locator('[data-testid="file-preview"]')).not.toBeVisible()
      
      // Should reset file counter
      await expect(page.locator('[data-testid="file-counter"]')).toContainText('0 / 10 files')
    })

    test('shows upload progress', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Mock slow upload for progress testing
      await page.route('**/upload/**', async route => {
        await page.waitForTimeout(2000)
        await route.continue()
      })
      
      await uploadFile(page, TEST_FILES.validImage)
      
      // Should show progress bar
      await expect(page.locator('[data-testid="upload-progress"]')).toBeVisible({ timeout: 1000 })
      await expect(page.locator('[data-testid="progress-bar"]')).toBeVisible()
      
      // Should show percentage
      await expect(page.locator('[data-testid="progress-text"]')).toContainText('%')
    })
  })

  test.describe('Form Submission Flow', () => {
    test('submits complete form successfully', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Fill form
      await fillSubmissionForm(page)
      
      // Upload file
      await uploadFile(page, TEST_FILES.validImage)
      await expect(page.locator('[data-testid="file-preview"]')).toBeVisible()
      
      // Submit form
      const startTime = Date.now()
      await page.locator('[data-testid="submit-button"]').click()
      
      // Should show submission progress
      await expect(page.locator('[data-testid="submission-progress"]')).toBeVisible({ timeout: 2000 })
      
      // Should complete within time limit
      await expect(page.locator('[data-testid="submission-success"]')).toBeVisible({ timeout: PERFORMANCE_THRESHOLDS.formSubmission })
      
      const submissionTime = Date.now() - startTime
      console.log(`Form submitted in ${submissionTime}ms`)
    })

    test('shows submission progress states', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Fill and submit form
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      
      // Mock multi-step submission process
      await page.route('**/submissions/**', async route => {
        await page.waitForTimeout(1000)
        await route.continue()
      })
      
      await page.locator('[data-testid="submit-button"]').click()
      
      // Should show progress steps
      await expect(page.locator('[data-testid="step-uploading"]')).toBeVisible()
      await expect(page.locator('text=Uploading images...')).toBeVisible()
      
      // Wait for next step
      await expect(page.locator('[data-testid="step-processing"]')).toBeVisible({ timeout: 3000 })
      await expect(page.locator('text=Processing submission...')).toBeVisible()
      
      // Finally success
      await expect(page.locator('[data-testid="submission-success"]')).toBeVisible({ timeout: 5000 })
    })

    test('disables form during submission', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      
      // Mock slow submission
      await page.route('**/submissions/**', async route => {
        await page.waitForTimeout(3000)
        await route.continue()
      })
      
      await page.locator('[data-testid="submit-button"]').click()
      
      // Form should be disabled
      await expect(page.locator('[data-testid="title-input"]')).toBeDisabled()
      await expect(page.locator('[data-testid="description-textarea"]')).toBeDisabled()
      await expect(page.locator('[data-testid="submit-button"]')).toBeDisabled()
      
      // Should show loading state
      await expect(page.locator('[data-testid="submit-button"]')).toContainText('Submitting...')
    })

    test('handles submission success', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      
      await page.locator('[data-testid="submit-button"]').click()
      
      // Should show success state
      await expect(page.locator('[data-testid="submission-success"]')).toBeVisible({ timeout: 5000 })
      await expect(page.locator('text=Submission uploaded successfully!')).toBeVisible()
      
      // Should show next actions
      await expect(page.locator('[data-testid="view-submission"]')).toBeVisible()
      await expect(page.locator('[data-testid="submit-another"]')).toBeVisible()
      
      // Should show submission ID
      await expect(page.locator('[data-testid="submission-id"]')).toBeVisible()
    })

    test('navigates after successful submission', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      await page.locator('[data-testid="submit-button"]').click()
      
      // Wait for success
      await expect(page.locator('[data-testid="submission-success"]')).toBeVisible({ timeout: 5000 })
      
      // Click view submission
      await page.locator('[data-testid="view-submission"]').click()
      
      // Should navigate to submission page
      await expect(page.url()).toContain('/submissions/')
    })

    test('allows submitting another after success', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      await page.locator('[data-testid="submit-button"]').click()
      
      // Wait for success
      await expect(page.locator('[data-testid="submission-success"]')).toBeVisible({ timeout: 5000 })
      
      // Click submit another
      await page.locator('[data-testid="submit-another"]').click()
      
      // Should reset form
      await expect(page.locator('[data-testid="title-input"]')).toHaveValue('')
      await expect(page.locator('[data-testid="description-textarea"]')).toHaveValue('')
      await expect(page.locator('[data-testid="file-preview"]')).not.toBeVisible()
    })
  })

  test.describe('Error Handling', () => {
    test('handles upload failures gracefully', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Mock upload failure
      await page.route('**/upload/**', route => route.abort())
      
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      await page.locator('[data-testid="submit-button"]').click()
      
      // Should show error message
      await expect(page.locator('[data-testid="submission-error"]')).toBeVisible({ timeout: 5000 })
      await expect(page.locator('text=Upload failed')).toBeVisible()
      
      // Should show retry option
      await expect(page.locator('[data-testid="retry-submission"]')).toBeVisible()
    })

    test('handles network failures with retry', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      let requestCount = 0
      
      // Simulate intermittent failure
      await page.route('**/submissions/**', route => {
        requestCount++
        if (requestCount <= 1) {
          route.abort()
        } else {
          route.continue()
        }
      })
      
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      await page.locator('[data-testid="submit-button"]').click()
      
      // Should show error first
      await expect(page.locator('[data-testid="submission-error"]')).toBeVisible({ timeout: 3000 })
      
      // Retry submission
      await page.locator('[data-testid="retry-submission"]').click()
      
      // Should succeed on retry
      await expect(page.locator('[data-testid="submission-success"]')).toBeVisible({ timeout: 5000 })
    })

    test('validates file limits during upload', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Try to upload more than 10 files
      const manyFiles = Array(12).fill(TEST_FILES.validImage)
      await uploadMultipleFiles(page, manyFiles)
      
      // Should show limit error
      await expect(page.locator('text=Maximum 10 files allowed')).toBeVisible()
      
      // Should only show first 10 files
      await expect(page.locator('[data-testid="file-preview"]')).toHaveCount(10)
    })

    test('handles server validation errors', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Mock server validation error
      await page.route('**/submissions/**', route => {
        route.fulfill({
          status: 400,
          body: JSON.stringify({
            error: 'Invalid submission data',
            details: {
              title: 'Title contains inappropriate content',
              tags: 'Too many tags provided'
            }
          })
        })
      })
      
      await fillSubmissionForm(page)
      await uploadFile(page, TEST_FILES.validImage)
      await page.locator('[data-testid="submit-button"]').click()
      
      // Should show server validation errors
      await expect(page.locator('[data-testid="server-error"]')).toBeVisible({ timeout: 3000 })
      await expect(page.locator('text=Title contains inappropriate content')).toBeVisible()
      await expect(page.locator('text=Too many tags provided')).toBeVisible()
    })
  })

  test.describe('Accessibility and Performance', () => {
    test('supports keyboard navigation', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Tab through form elements
      await page.keyboard.press('Tab') // Title input
      await expect(page.locator('[data-testid="title-input"]')).toBeFocused()
      
      await page.keyboard.press('Tab') // Description textarea
      await expect(page.locator('[data-testid="description-textarea"]')).toBeFocused()
      
      await page.keyboard.press('Tab') // Category select
      await expect(page.locator('[data-testid="category-select"]')).toBeFocused()
      
      // Should maintain focus visibility
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    })

    test('has proper ARIA labels and roles', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Check form accessibility
      await expect(page.locator('[data-testid="submission-form"]')).toHaveAttribute('role', 'form')
      await expect(page.locator('[data-testid="title-input"]')).toHaveAttribute('aria-label')
      await expect(page.locator('[data-testid="description-textarea"]')).toHaveAttribute('aria-label')
      
      // Check file upload accessibility
      await expect(page.locator('[data-testid="file-input"]')).toHaveAttribute('aria-label')
      await expect(page.locator('[data-testid="drop-zone"]')).toHaveAttribute('role', 'button')
      await expect(page.locator('[data-testid="drop-zone"]')).toHaveAttribute('aria-label')
    })

    test('provides screen reader announcements', async ({ page }) => {
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Should have live regions for announcements
      await expect(page.locator('[aria-live="polite"]')).toBeVisible()
      
      // Upload file and check announcement
      await uploadFile(page, TEST_FILES.validImage)
      
      // Should announce file upload
      await expect(page.locator('[data-testid="upload-announcement"]')).toContainText('File uploaded successfully')
    })

    test('meets performance requirements', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      const loadTime = Date.now() - startTime
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad)
      
      // Test file upload performance
      const uploadStart = Date.now()
      await uploadFile(page, TEST_FILES.validImage)
      await expect(page.locator('[data-testid="file-preview"]')).toBeVisible()
      const uploadTime = Date.now() - uploadStart
      
      expect(uploadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.fileUpload)
      
      console.log(`Page load: ${loadTime}ms, File upload: ${uploadTime}ms`)
    })
  })

  test.describe('Mobile Responsiveness', () => {
    test('works on mobile viewport', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // All elements should be visible and accessible
      await expect(page.locator('[data-testid="upload-page"]')).toBeVisible()
      await expect(page.locator('[data-testid="title-input"]')).toBeVisible()
      await expect(page.locator('[data-testid="description-textarea"]')).toBeVisible()
      await expect(page.locator('[data-testid="drop-zone"]')).toBeVisible()
      
      // Upload should work on mobile
      await uploadFile(page, TEST_FILES.validImage)
      await expect(page.locator('[data-testid="file-preview"]')).toBeVisible()
    })

    test('adapts layout for tablet', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await page.goto(SUBMISSION_UPLOAD_URL)
      await waitForUploadPageLoad(page)
      
      // Form should adapt to tablet layout
      await expect(page.locator('[data-testid="submission-form"]')).toBeVisible()
      
      // File upload area should be appropriately sized
      const dropZone = page.locator('[data-testid="drop-zone"]')
      const boundingBox = await dropZone.boundingBox()
      expect(boundingBox?.width).toBeGreaterThan(300)
    })
  })
})