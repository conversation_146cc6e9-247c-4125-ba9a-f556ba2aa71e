/**
 * Community Test Configuration
 * 
 * Centralized configuration for community E2E tests including:
 * - Test environment settings
 * - Performance thresholds
 * - Mock data management
 * - Test utilities and fixtures
 * 
 * <AUTHOR> Team
 */

export const COMMUNITY_TEST_CONFIG = {
  // Base URLs and Routes
  routes: {
    base: '/community',
    discover: '/community/discover',
    submissions: '/community/submissions',
    discussions: '/community/discussions',
    challenges: '/community/challenges',
    leaderboard: '/community/leaderboard',
    upload: '/community/submissions/upload'
  },

  // Performance Thresholds
  performance: {
    pageLoad: 3000,          // 3 seconds max page load
    searchResponse: 500,     // 500ms max search response
    fileUpload: 10000,       // 10 seconds max file upload
    formSubmission: 5000,    // 5 seconds max form submission
    imageLoad: 2000,         // 2 seconds max image load
    realtimeUpdate: 1000     // 1 second max real-time update
  },

  // Test Timeouts
  timeouts: {
    short: 2000,    // 2 seconds
    default: 5000,  // 5 seconds  
    long: 10000,    // 10 seconds
    upload: 15000   // 15 seconds for uploads
  },

  // Test Data Limits
  limits: {
    maxFileSize: 15 * 1024 * 1024,  // 15MB
    maxFiles: 10,                    // 10 files max
    maxActivities: 50,               // 50 activities in feed
    searchDebounce: 300             // 300ms search debounce
  },

  // Test Users
  users: {
    authenticated: {
      uid: 'test-user-123',
      email: '<EMAIL>',
      displayName: 'Test User',
      isAuthenticated: true,
      level: 3,
      points: 450
    },
    admin: {
      uid: 'admin-user-123', 
      email: '<EMAIL>',
      displayName: 'Admin User',
      isAuthenticated: true,
      isAdmin: true,
      level: 15,
      points: 10000
    },
    powerUser: {
      uid: 'power-user-123',
      email: '<EMAIL>', 
      displayName: 'Power User',
      isAuthenticated: true,
      level: 12,
      points: 5000
    },
    newUser: {
      uid: 'new-user-123',
      email: '<EMAIL>',
      displayName: 'New User', 
      isAuthenticated: true,
      level: 1,
      points: 0,
      isNew: true
    }
  },

  // Test Selectors (data-testid values)
  selectors: {
    // Layout
    communityLayout: 'community-layout',
    communityTabs: 'community-tabs',
    mainContent: 'main-content',
    
    // Navigation
    tabDiscover: 'tab-discover',
    tabSubmissions: 'tab-submissions', 
    tabDiscussions: 'tab-discussions',
    tabChallenges: 'tab-challenges',
    tabLeaderboard: 'tab-leaderboard',
    
    // Discover Page
    discoverPage: 'discover-page',
    communityHero: 'community-hero',
    communityStats: 'community-stats',
    totalMembers: 'total-members',
    activeChallenges: 'active-challenges',
    onlineUsers: 'online-users',
    totalSubmissions: 'total-submissions',
    weeklyGrowth: 'weekly-growth',
    
    // Search
    communitySearch: 'community-search',
    searchInput: 'search-input',
    searchFilters: 'search-filters',
    searchResults: 'search-results',
    trendingSearches: 'trending-searches',
    trendingItem: 'trending-item',
    resultItem: 'result-item',
    noResults: 'no-results',
    
    // Featured Content
    featuredContent: 'featured-content',
    featuredSubmissions: 'featured-submissions',
    trendingDiscussions: 'trending-discussions',
    submissionCard: 'submission-card',
    discussionItem: 'discussion-item',
    
    // Upload
    uploadPage: 'upload-page',
    submissionForm: 'submission-form',
    titleInput: 'title-input',
    descriptionTextarea: 'description-textarea',
    categorySelect: 'category-select',
    tagInput: 'tag-input',
    fileInput: 'file-input',
    dropZone: 'drop-zone',
    filePreview: 'file-preview',
    submitButton: 'submit-button',
    
    // Real-time
    realtimeStatus: 'realtime-status',
    activityFeed: 'activity-feed',
    activityItem: 'activity-item',
    
    // Error States
    errorMessage: 'error-message',
    retryButton: 'retry-button',
    loadingState: 'loading-state'
  },

  // Mock Data Templates
  mockData: {
    stats: {
      totalMembers: 2847,
      activeChallenges: 8,
      onlineUsers: 234,
      totalSubmissions: 1856,
      weeklyGrowth: 15
    },
    
    submission: {
      id: 'test-submission-001',
      title: 'Test Artisan Keycap',
      description: 'A test submission for E2E testing',
      category: 'Artisan Keycap',
      tags: ['test', 'e2e', 'artisan'],
      likes: 42,
      views: 123,
      comments: 5,
      featured: true,
      status: 'approved'
    },
    
    activity: {
      id: 'test-activity-001',
      type: 'submission',
      content: {
        title: 'Test Activity',
        description: 'A test activity for E2E testing'
      },
      engagement: {
        likes: 5,
        comments: 2,
        shares: 1
      }
    }
  },

  // File Test Data
  testFiles: {
    validImage: {
      name: 'test-keycap.jpg',
      size: 512 * 1024, // 512KB
      type: 'image/jpeg'
    },
    largeImage: {
      name: 'large-image.jpg', 
      size: 20 * 1024 * 1024, // 20MB (over limit)
      type: 'image/jpeg'
    },
    invalidFile: {
      name: 'test-document.pdf',
      size: 100 * 1024, // 100KB
      type: 'application/pdf'
    }
  },

  // API Endpoints
  api: {
    stats: '/api/community/stats',
    submissions: '/api/community/submissions',
    discussions: '/api/community/discussions', 
    challenges: '/api/community/challenges',
    activities: '/api/community/activities',
    search: '/api/community/search',
    upload: '/api/community/upload',
    leaderboard: '/api/community/leaderboard'
  },

  // Feature Flags for Test Scenarios
  features: {
    realtimeUpdates: true,
    fileUpload: true,
    searchFilters: true,
    infiniteScroll: false,
    darkMode: true,
    mobileOptimized: true
  }
} as const

export type CommunityTestConfig = typeof COMMUNITY_TEST_CONFIG