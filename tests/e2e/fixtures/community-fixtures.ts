/**
 * Community Test Fixtures
 * 
 * Playwright fixtures for community tests providing:
 * - Pre-configured pages with authentication
 * - Mock data setup
 * - Common test utilities
 * - Performance measurement tools
 * 
 * <AUTHOR> Team
 */

import { test as base, Page } from '@playwright/test'
import { COMMUNITY_TEST_CONFIG } from '../config/community-test-config'

// Define fixture types
type CommunityFixtures = {
  authenticatedPage: Page
  adminPage: Page
  communityPage: Page
  uploadPage: Page
  mockDataPage: Page
  performancePage: Page
}

// Community Page Fixture - Pre-configured for community testing
const setupCommunityPage = async (page: Page, userType: 'authenticated' | 'admin' | 'new' = 'authenticated') => {
  // Setup authentication
  const user = COMMUNITY_TEST_CONFIG.users[userType]
  await page.evaluate((userData) => {
    localStorage.setItem('test-user', JSON.stringify(userData))
  }, user)

  // Setup Firebase mocks
  await page.addInitScript(() => {
    // Mock Firebase SDK
    window.mockFirebase = {
      initialized: true,
      connected: true,
      auth: {
        currentUser: JSON.parse(localStorage.getItem('test-user') || '{}')
      }
    }

    // Mock Firestore
    window.mockFirestore = {
      doc: (path: string) => ({
        get: () => Promise.resolve({ 
          data: () => window.mockFirestore.collections?.get(path),
          exists: true
        }),
        set: (data: any) => Promise.resolve(),
        onSnapshot: (callback: Function) => {
          const unsubscribe = () => {}
          setTimeout(() => callback({ 
            data: () => window.mockFirestore.collections?.get(path),
            exists: true
          }), 100)
          return unsubscribe
        }
      }),
      collection: (name: string) => ({
        where: () => ({ 
          orderBy: () => ({ 
            limit: () => ({ 
              get: () => Promise.resolve({ docs: [] })
            })
          })
        }),
        orderBy: () => ({ 
          limit: () => ({ 
            get: () => Promise.resolve({ docs: [] })
          })
        }),
        get: () => Promise.resolve({ docs: [] })
      })
    }

    // Mock Firebase Storage
    window.mockFirebaseStorage = {
      ref: (path: string) => ({
        put: (file: File) => ({
          on: (event: string, progress?: Function, error?: Function, complete?: Function) => {
            if (progress) {
              setTimeout(() => progress({ bytesTransferred: file.size * 0.5, totalBytes: file.size }), 500)
              setTimeout(() => progress({ bytesTransferred: file.size, totalBytes: file.size }), 1000)
            }
            if (complete) {
              setTimeout(() => complete(), 1200)
            }
          }
        }),
        getDownloadURL: () => Promise.resolve(`https://storage.example.com/${path}`)
      })
    }

    // Mock WebSocket for real-time features
    window.mockWebSocket = {
      connected: false,
      listeners: new Map(),
      connect: () => {
        window.mockWebSocket.connected = true
        window.dispatchEvent(new CustomEvent('websocket-connected'))
      },
      disconnect: () => {
        window.mockWebSocket.connected = false
        window.dispatchEvent(new CustomEvent('websocket-disconnected'))
      },
      send: (data: any) => console.log('Mock WebSocket send:', data)
    }

    // Auto-connect WebSocket
    setTimeout(() => window.mockWebSocket.connect(), 1000)
  })

  return page
}

// Mock Data Setup Fixture
const setupMockData = async (page: Page) => {
  await page.addInitScript((testConfig) => {
    // Setup mock community data
    window.mockCommunityData = {
      stats: testConfig.mockData.stats,
      submissions: [
        testConfig.mockData.submission,
        {
          ...testConfig.mockData.submission,
          id: 'test-submission-002',
          title: 'RGB Gaming Setup',
          category: 'Setup Showcase',
          tags: ['rgb', 'gaming', 'setup']
        }
      ],
      activities: [
        testConfig.mockData.activity,
        {
          ...testConfig.mockData.activity,
          id: 'test-activity-002',
          type: 'like',
          content: {
            title: 'Liked a submission',
            description: 'User liked an artisan keycap'
          }
        }
      ]
    }

    // Override fetch for API calls
    const originalFetch = window.fetch
    window.fetch = async (url: string, options?: RequestInit) => {
      // Mock API responses
      if (url.includes('/api/community/stats')) {
        return new Response(JSON.stringify(window.mockCommunityData.stats), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      }
      
      if (url.includes('/api/community/submissions')) {
        return new Response(JSON.stringify(window.mockCommunityData.submissions), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      }

      if (url.includes('/api/community/activities')) {
        return new Response(JSON.stringify(window.mockCommunityData.activities), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      }

      if (url.includes('/api/community/search')) {
        const searchParams = new URL(url).searchParams
        const query = searchParams.get('q')
        
        // Simple mock search logic
        const results = {
          submissions: window.mockCommunityData.submissions.filter((s: any) => 
            s.title.toLowerCase().includes(query?.toLowerCase() || '')
          ),
          discussions: [],
          challenges: [],
          members: []
        }

        return new Response(JSON.stringify(results), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      }

      // Fall back to original fetch for other requests
      return originalFetch(url, options)
    }
  }, COMMUNITY_TEST_CONFIG)

  return page
}

// Performance Measurement Fixture
const setupPerformancePage = async (page: Page) => {
  await page.addInitScript(() => {
    // Performance measurement utilities
    window.testPerformance = {
      marks: new Map(),
      measurements: new Map(),
      
      mark: (name: string) => {
        performance.mark(name)
        window.testPerformance.marks.set(name, performance.now())
      },
      
      measure: (name: string, startMark: string, endMark?: string) => {
        const start = window.testPerformance.marks.get(startMark)
        const end = endMark ? window.testPerformance.marks.get(endMark) : performance.now()
        
        if (start && end) {
          const duration = end - start
          window.testPerformance.measurements.set(name, duration)
          performance.measure(name, startMark, endMark)
          return duration
        }
        return 0
      },
      
      getMeasurement: (name: string) => {
        return window.testPerformance.measurements.get(name) || 0
      },
      
      clear: () => {
        performance.clearMarks()
        performance.clearMeasures()
        window.testPerformance.marks.clear()
        window.testPerformance.measurements.clear()
      }
    }

    // Track page load performance
    window.addEventListener('load', () => {
      window.testPerformance.mark('page-load-complete')
    })
  })

  return page
}

// Define the fixtures
export const test = base.extend<CommunityFixtures>({
  // Authenticated page fixture
  authenticatedPage: async ({ page }, use) => {
    const communityPage = await setupCommunityPage(page, 'authenticated')
    await use(communityPage)
  },

  // Admin page fixture  
  adminPage: async ({ page }, use) => {
    const adminPage = await setupCommunityPage(page, 'admin')
    await use(adminPage)
  },

  // General community page fixture
  communityPage: async ({ page }, use) => {
    const communityPage = await setupCommunityPage(page)
    await use(communityPage)
  },

  // Upload-specific page fixture
  uploadPage: async ({ page }, use) => {
    const uploadPage = await setupCommunityPage(page, 'authenticated')
    
    // Additional upload-specific setup
    await uploadPage.addInitScript(() => {
      // Mock file validation
      window.mockFileValidation = {
        validateFileType: (file: File) => {
          const allowedTypes = ['image/jpeg', 'image/png', 'image/gif']
          return allowedTypes.includes(file.type)
        },
        validateFileSize: (file: File) => {
          return file.size <= 15 * 1024 * 1024 // 15MB
        }
      }
    })
    
    await use(uploadPage)
  },

  // Mock data page fixture
  mockDataPage: async ({ page }, use) => {
    const mockPage = await setupCommunityPage(page)
    await setupMockData(mockPage)
    await use(mockPage)
  },

  // Performance page fixture
  performancePage: async ({ page }, use) => {
    const perfPage = await setupCommunityPage(page)
    await setupPerformancePage(perfPage)
    await use(perfPage)
  }
})

export { expect } from '@playwright/test'