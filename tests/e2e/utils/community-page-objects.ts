/**
 * Community Page Objects
 * 
 * Page Object Model implementation for community features providing:
 * - Encapsulated page interactions
 * - Reusable component methods  
 * - Type-safe element selectors
 * - Common action patterns
 * 
 * <AUTHOR> Team
 */

import { Page, Locator, expect } from '@playwright/test'
import { COMMUNITY_TEST_CONFIG } from '../config/community-test-config'

const { selectors, routes, timeouts } = COMMUNITY_TEST_CONFIG

// Base Page Object
export class BaseCommunityPage {
  constructor(protected page: Page) {}

  async goto(route: string) {
    await this.page.goto(route)
    await this.page.waitForLoadState('networkidle')
  }

  async waitForElement(selector: string, timeout = timeouts.default) {
    return await this.page.waitForSelector(`[data-testid="${selector}"]`, { timeout })
  }

  getElement(selector: string): Locator {
    return this.page.locator(`[data-testid="${selector}"]`)
  }

  async verifyElementVisible(selector: string) {
    await expect(this.getElement(selector)).toBeVisible()
  }

  async verifyElementText(selector: string, text: string) {
    await expect(this.getElement(selector)).toContainText(text)
  }

  async clickElement(selector: string) {
    await this.getElement(selector).click()
  }
}

// Community Layout Page Object
export class CommunityLayoutPage extends BaseCommunityPage {
  // Navigation elements
  get discoverTab() { return this.getElement(selectors.tabDiscover) }
  get submissionsTab() { return this.getElement(selectors.tabSubmissions) }
  get discussionsTab() { return this.getElement(selectors.tabDiscussions) }
  get challengesTab() { return this.getElement(selectors.tabChallenges) }
  get leaderboardTab() { return this.getElement(selectors.tabLeaderboard) }

  async navigateToDiscover() {
    await this.discoverTab.click()
    await this.waitForElement(selectors.discoverPage)
  }

  async navigateToSubmissions() {
    await this.submissionsTab.click()
    await this.waitForElement('submissions-content')
  }

  async navigateToDiscussions() {
    await this.discussionsTab.click()
    await this.waitForElement('discussions-content')
  }

  async navigateToChallenges() {
    await this.challengesTab.click()
    await this.waitForElement('challenges-content')
  }

  async navigateToLeaderboard() {
    await this.leaderboardTab.click()
    await this.waitForElement('leaderboard-content')
  }

  async verifyActiveTab(tabName: string) {
    const tab = this.getElement(`tab-${tabName}`)
    await expect(tab).toHaveAttribute('aria-selected', 'true')
  }

  async verifyTabsVisible() {
    await this.verifyElementVisible(selectors.tabDiscover)
    await this.verifyElementVisible(selectors.tabSubmissions)
    await this.verifyElementVisible(selectors.tabDiscussions)
    await this.verifyElementVisible(selectors.tabChallenges)
    await this.verifyElementVisible(selectors.tabLeaderboard)
  }
}

// Discover Page Object
export class DiscoverPage extends BaseCommunityPage {
  // Hero section elements
  get totalMembers() { return this.getElement(selectors.totalMembers) }
  get activeChallenges() { return this.getElement(selectors.activeChallenges) }
  get onlineUsers() { return this.getElement(selectors.onlineUsers) }
  get totalSubmissions() { return this.getElement(selectors.totalSubmissions) }
  get weeklyGrowth() { return this.getElement(selectors.weeklyGrowth) }

  // Search elements
  get searchInput() { return this.getElement(selectors.searchInput) }
  get searchResults() { return this.getElement(selectors.searchResults) }
  get trendingSearches() { return this.getElement(selectors.trendingSearches) }

  // Featured content elements
  get featuredSubmissions() { return this.getElement(selectors.featuredSubmissions) }
  get trendingDiscussions() { return this.getElement(selectors.trendingDiscussions) }

  async goto() {
    await super.goto(routes.discover)
    await this.waitForElement(selectors.discoverPage)
  }

  async waitForStatsLoad() {
    await this.waitForElement(selectors.communityStats)
    await this.page.waitForFunction(() => {
      const statsElement = document.querySelector(`[data-testid="${selectors.communityStats}"]`)
      return statsElement && !statsElement.textContent?.includes('Loading')
    })
  }

  async verifyStats(expectedStats: any) {
    await this.verifyElementText(selectors.totalMembers, expectedStats.totalMembers.toString())
    await this.verifyElementText(selectors.activeChallenges, expectedStats.activeChallenges.toString())
    await this.verifyElementText(selectors.onlineUsers, expectedStats.onlineUsers.toString())
    await this.verifyElementText(selectors.totalSubmissions, expectedStats.totalSubmissions.toString())
  }

  async performSearch(query: string, waitForResults = true) {
    await this.searchInput.fill(query)
    
    if (waitForResults) {
      await this.page.waitForTimeout(400) // Debounce delay
      await this.waitForElement(selectors.searchResults)
    }
  }

  async clearSearch() {
    await this.searchInput.clear()
    await this.page.waitForTimeout(200)
  }

  async verifySearchResults(expectedCount: number) {
    const results = this.getElement(selectors.resultItem)
    await expect(results).toHaveCount(expectedCount)
  }

  async clickTrendingSearch(index = 0) {
    const trendingItem = this.page.locator(`[data-testid="${selectors.trendingItem}"]`).nth(index)
    const text = await trendingItem.textContent()
    await trendingItem.click()
    
    await expect(this.searchInput).toHaveValue(text?.trim() || '')
  }

  async verifyFeaturedContent() {
    await this.verifyElementVisible(selectors.featuredSubmissions)
    await this.verifyElementVisible(selectors.trendingDiscussions)
    
    const submissionCards = this.page.locator(`[data-testid="${selectors.submissionCard}"]`)
    await expect(submissionCards).toHaveCount(4, { timeout: timeouts.default })
  }
}

// Search Page Object
export class SearchPage extends BaseCommunityPage {
  get searchInput() { return this.getElement(selectors.searchInput) }
  get searchFilters() { return this.getElement(selectors.searchFilters) }
  get searchResults() { return this.getElement(selectors.searchResults) }
  get noResults() { return this.getElement(selectors.noResults) }

  async selectFilter(filterType: string) {
    await this.getElement(`filter-${filterType}`).click()
  }

  async verifyFilterActive(filterType: string) {
    const filter = this.getElement(`filter-${filterType}`)
    await expect(filter).toHaveAttribute('aria-pressed', 'true')
  }

  async verifyFilterCounts(expectedCounts: Record<string, number>) {
    for (const [filterType, count] of Object.entries(expectedCounts)) {
      const filterCount = this.getElement(`filter-${filterType}`).locator(`[data-testid="filter-count"]`)
      await expect(filterCount).toContainText(count.toString())
    }
  }

  async verifyResultType(expectedType: string) {
    const firstResult = this.page.locator(`[data-testid="${selectors.resultItem}"]`).first()
    await expect(firstResult.locator(`[data-testid="result-type"]`)).toContainText(expectedType)
  }
}

// Upload Page Object  
export class UploadPage extends BaseCommunityPage {
  get titleInput() { return this.getElement(selectors.titleInput) }
  get descriptionTextarea() { return this.getElement(selectors.descriptionTextarea) }
  get categorySelect() { return this.getElement(selectors.categorySelect) }
  get tagInput() { return this.getElement(selectors.tagInput) }
  get fileInput() { return this.getElement(selectors.fileInput) }
  get dropZone() { return this.getElement(selectors.dropZone) }
  get filePreview() { return this.getElement(selectors.filePreview) }
  get submitButton() { return this.getElement(selectors.submitButton) }

  async goto() {
    await super.goto(routes.upload)
    await this.waitForElement(selectors.uploadPage)
  }

  async fillForm(data: {
    title: string
    description: string
    category: string
    tags?: string[]
  }) {
    await this.titleInput.fill(data.title)
    await this.descriptionTextarea.fill(data.description)
    await this.categorySelect.selectOption(data.category)
    
    // Add tags
    if (data.tags) {
      for (const tag of data.tags) {
        await this.tagInput.fill(tag)
        await this.page.keyboard.press('Enter')
      }
    }
  }

  async uploadFile(filePath: string) {
    await this.fileInput.setInputFiles(filePath)
  }

  async uploadMultipleFiles(filePaths: string[]) {
    await this.fileInput.setInputFiles(filePaths)
  }

  async verifyFilePreview(fileName: string) {
    await this.verifyElementVisible(selectors.filePreview)
    await this.verifyElementText('file-name', fileName)
  }

  async simulateDragAndDrop(fileData: any) {
    await this.dropZone.dispatchEvent('dragenter')
    await this.dropZone.dispatchEvent('dragover')
    
    await this.page.evaluate((file) => {
      const dt = new DataTransfer()
      const fileObj = new File([file.content], file.name, { type: file.type })
      dt.items.add(fileObj)
      
      const dropEvent = new DragEvent('drop', {
        dataTransfer: dt,
        bubbles: true,
        cancelable: true
      })
      
      document.querySelector(`[data-testid="${selectors.dropZone}"]`)?.dispatchEvent(dropEvent)
    }, fileData)
  }

  async submitForm() {
    await this.submitButton.click()
  }

  async verifySubmissionSuccess() {
    await this.verifyElementVisible('submission-success')
    await this.verifyElementText('submission-success', 'Submission uploaded successfully!')
  }

  async verifyValidationError(field: string, message: string) {
    await this.verifyElementVisible(`${field}-error`)
    await this.verifyElementText(`${field}-error`, message)
  }

  async verifyFormDisabled() {
    await expect(this.titleInput).toBeDisabled()
    await expect(this.descriptionTextarea).toBeDisabled()
    await expect(this.submitButton).toBeDisabled()
  }
}

// Real-time Features Page Object
export class RealtimePage extends BaseCommunityPage {
  get realtimeStatus() { return this.getElement(selectors.realtimeStatus) }
  get activityFeed() { return this.getElement(selectors.activityFeed) }
  get activityItems() { return this.page.locator(`[data-testid="${selectors.activityItem}"]`) }

  async waitForConnection() {
    await this.waitForElement(selectors.realtimeStatus)
    await this.page.waitForFunction(() => {
      const status = document.querySelector(`[data-testid="${selectors.realtimeStatus}"]`)
      return status && status.textContent?.includes('connected')
    }, { timeout: timeouts.long })
  }

  async verifyConnectionStatus(expectedStatus: 'connected' | 'disconnected' | 'connecting') {
    await expect(this.realtimeStatus).toContainText(expectedStatus)
    await expect(this.realtimeStatus).toHaveClass(new RegExp(expectedStatus))
  }

  async simulateRealtimeUpdate(updateType: string, data: any) {
    await this.page.evaluate(([type, updateData]) => {
      window.dispatchEvent(new CustomEvent('realtime-update', {
        detail: { type, data: updateData }
      }))
    }, [updateType, data])
  }

  async verifyActivityCount(expectedCount: number) {
    await expect(this.activityItems).toHaveCount(expectedCount, { timeout: timeouts.default })
  }

  async verifyNewActivity(userName: string, activityTitle: string) {
    const firstActivity = this.activityItems.first()
    await expect(firstActivity).toContainText(userName)
    await expect(firstActivity).toContainText(activityTitle)
  }
}

// Error Handling Page Object
export class ErrorPage extends BaseCommunityPage {
  get errorMessage() { return this.getElement(selectors.errorMessage) }
  get retryButton() { return this.getElement(selectors.retryButton) }

  async verifyErrorMessage(expectedMessage: string) {
    await this.verifyElementVisible(selectors.errorMessage)
    await this.verifyElementText(selectors.errorMessage, expectedMessage)
  }

  async verifyRetryButton() {
    await this.verifyElementVisible(selectors.retryButton)
  }

  async clickRetry() {
    await this.retryButton.click()
  }

  async verifyGracefulDegradation(mainContentSelector: string) {
    await this.verifyElementVisible(mainContentSelector)
  }
}

// Export all page objects
export {
  BaseCommunityPage,
  CommunityLayoutPage,
  DiscoverPage,
  SearchPage,
  UploadPage,
  RealtimePage,
  ErrorPage
}