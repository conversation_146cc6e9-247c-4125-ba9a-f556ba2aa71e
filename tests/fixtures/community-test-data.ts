/**
 * Test Data Fixtures for Community E2E Tests
 * 
 * Provides comprehensive test data for community features including:
 * - Mock community statistics
 * - Sample submissions, discussions, challenges
 * - User profiles and activity data
 * - Real-time update data
 * 
 * <AUTHOR> Team
 */

import { Timestamp } from 'firebase/firestore'

// Utility function to generate timestamp
const generateTimestamp = (daysAgo = 0) => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return Timestamp.fromDate(date)
}

// User reference generator
const generateUserRef = (userId: string, userName: string, level = 1) => ({
  userId,
  userName,
  userAvatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userName}`,
  userLevel: level,
  userTier: level > 10 ? 'gold' : level > 5 ? 'silver' : 'bronze'
})

// Community Statistics Test Data
export const mockCommunityStats = {
  default: {
    totalMembers: 2847,
    activeChallenges: 8,
    onlineUsers: 234,
    totalSubmissions: 1856,
    weeklyGrowth: 15,
    monthlyActiveUsers: 1234,
    lastUpdated: generateTimestamp(),
    createdAt: generateTimestamp()
  },
  updated: {
    totalMembers: 2850,
    activeChallenges: 9,
    onlineUsers: 237,
    totalSubmissions: 1859,
    weeklyGrowth: 18,
    monthlyActiveUsers: 1245,
    lastUpdated: generateTimestamp(),
    createdAt: generateTimestamp()
  },
  large: {
    totalMembers: 15847,
    activeChallenges: 25,
    onlineUsers: 1234,
    totalSubmissions: 8567,
    weeklyGrowth: 22,
    monthlyActiveUsers: 5678,
    lastUpdated: generateTimestamp(),
    createdAt: generateTimestamp()
  }
}

// User Profiles Test Data
export const mockUsers = {
  artisanMaster: {
    userId: 'user_artisan_001',
    userName: 'ArtisanMaster',
    displayName: 'Artisan Master',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ArtisanMaster',
    level: 12,
    points: 3450,
    tier: 'gold',
    badges: ['Artisan Expert', 'Winter Challenger', 'Community Helper'],
    joinedAt: generateTimestamp(120),
    lastActive: generateTimestamp(),
    stats: {
      submissions: 18,
      likes: 234,
      comments: 45,
      challengesCompleted: 3
    },
    bio: 'Passionate about creating unique artisan keycaps with nature-inspired themes.',
    achievements: ['first-submission', 'five-submissions', 'popular-creator']
  },
  neonRunner: {
    userId: 'user_setup_001',
    userName: 'NeonRunner',
    displayName: 'Neon Runner',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=NeonRunner',
    level: 8,
    points: 2100,
    tier: 'silver',
    badges: ['Setup Master', 'RGB Specialist'],
    joinedAt: generateTimestamp(80),
    lastActive: generateTimestamp(),
    stats: {
      submissions: 12,
      likes: 189,
      comments: 32,
      challengesCompleted: 2
    },
    bio: 'RGB lighting enthusiast and setup showcase creator.'
  },
  grainMaster: {
    userId: 'user_wood_001',
    userName: 'GrainMaster',
    displayName: 'Grain Master',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=GrainMaster',
    level: 10,
    points: 2800,
    tier: 'gold',
    badges: ['Wood Worker', 'Sustainable Creator', 'Craftsperson'],
    joinedAt: generateTimestamp(95),
    lastActive: generateTimestamp(),
    stats: {
      submissions: 15,
      likes: 156,
      comments: 28,
      challengesCompleted: 1
    },
    bio: 'Specializing in handcrafted wooden keycaps and sustainable materials.'
  },
  testUser: {
    userId: 'test-user-123',
    userName: 'TestExplorer',
    displayName: 'Test Explorer',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=TestExplorer',
    level: 3,
    points: 450,
    tier: 'bronze',
    badges: ['New Member'],
    joinedAt: generateTimestamp(5),
    lastActive: generateTimestamp(),
    stats: {
      submissions: 1,
      likes: 12,
      comments: 5,
      challengesCompleted: 0
    },
    bio: 'New to the community and excited to learn!'
  }
}

// Challenges Test Data
export const mockChallenges = {
  winterArtisan: {
    id: 'winter-challenge-2024',
    title: 'Winter Artisan Challenge 2024',
    description: 'Create winter-themed artisan keycaps showcasing seasonal beauty and craftsmanship.',
    category: 'Artisan Keycap',
    difficulty: 'intermediate',
    status: 'active',
    startDate: generateTimestamp(7),
    endDate: generateTimestamp(-21),
    bannerImage: '/images/challenges/winter-challenge.jpg',
    rewards: {
      points: 500,
      badges: ['Winter Artisan', 'Seasonal Creator'],
      prizes: ['Featured Gallery Spot', 'Community Spotlight']
    },
    requirements: [
      'Must be original winter-themed design',
      'Submit high-quality photos from multiple angles',
      'Include brief description of inspiration and process'
    ],
    submissions: 47,
    participants: 43,
    createdBy: generateUserRef('admin', 'Syndicaps Team', 15),
    createdAt: generateTimestamp(10),
    updatedAt: generateTimestamp(1)
  },
  rgbHarmony: {
    id: 'rgb-harmony-2024',
    title: 'RGB Color Harmony Challenge',
    description: 'Design a keyboard setup that demonstrates perfect RGB color harmony and coordination.',
    category: 'Setup Showcase',
    difficulty: 'beginner',
    status: 'active',
    startDate: generateTimestamp(3),
    endDate: generateTimestamp(-17),
    bannerImage: '/images/challenges/rgb-challenge.jpg',
    rewards: {
      points: 300,
      badges: ['Color Master', 'RGB Specialist'],
      prizes: ['RGB Keycap Set']
    },
    requirements: [
      'Demonstrate color theory principles',
      'Include lighting setup details',
      'Show color palette inspiration'
    ],
    submissions: 28,
    participants: 26,
    createdBy: generateUserRef('admin', 'Syndicaps Team', 15),
    createdAt: generateTimestamp(5),
    updatedAt: generateTimestamp()
  },
  minimalistMastery: {
    id: 'minimalist-mastery-2024',
    title: 'Minimalist Mastery',
    description: 'Create the ultimate minimalist keyboard setup with clean lines and purposeful design.',
    category: 'Setup Showcase',
    difficulty: 'advanced',
    status: 'upcoming',
    startDate: generateTimestamp(-7),
    endDate: generateTimestamp(-35),
    bannerImage: '/images/challenges/minimalist-challenge.jpg',
    rewards: {
      points: 750,
      badges: ['Minimalist Master', 'Design Guru'],
      prizes: ['Premium Minimalist Keycap Set', 'Featured Article']
    },
    requirements: [
      'Maximum 3 colors in entire setup',
      'Demonstrate functionality over aesthetics',
      'Include design philosophy explanation'
    ],
    submissions: 0,
    participants: 156,
    createdBy: generateUserRef('admin', 'Syndicaps Team', 15),
    createdAt: generateTimestamp(1),
    updatedAt: generateTimestamp()
  }
}

// Submissions Test Data
export const mockSubmissions = {
  sakuraKeycap: {
    id: 'sub-001',
    title: 'Sakura Cherry Blossom Artisan',
    description: 'Hand-sculpted cherry blossom keycap with translucent resin petals and silver leaf accents.',
    authorId: 'user_artisan_001',
    author: generateUserRef('user_artisan_001', 'ArtisanMaster', 12),
    images: [
      '/images/submissions/sakura-keycap-main.jpg',
      '/images/submissions/sakura-keycap-side.jpg',
      '/images/submissions/sakura-keycap-process.jpg'
    ],
    category: 'Artisan Keycap',
    tags: ['sakura', 'resin', 'translucent', 'nature', 'handcrafted'],
    likes: 134,
    views: 567,
    comments: 23,
    featured: true,
    status: 'approved',
    challengeId: 'winter-challenge-2024',
    submittedAt: generateTimestamp(2),
    createdAt: generateTimestamp(2),
    updatedAt: generateTimestamp(1)
  },
  cyberpunkSetup: {
    id: 'sub-002',
    title: 'Cyberpunk Neon Battlestation',
    description: 'Complete cyberpunk-themed setup with custom RGB underglow, neon keycaps, and atmospheric lighting.',
    authorId: 'user_setup_001',
    author: generateUserRef('user_setup_001', 'NeonRunner', 8),
    images: [
      '/images/submissions/cyberpunk-main.jpg',
      '/images/submissions/cyberpunk-detail.jpg',
      '/images/submissions/cyberpunk-night.jpg'
    ],
    category: 'Setup Showcase',
    tags: ['cyberpunk', 'rgb', 'neon', 'battlestation', 'atmospheric'],
    likes: 89,
    views: 432,
    comments: 15,
    featured: true,
    status: 'approved',
    challengeId: 'rgb-harmony-2024',
    submittedAt: generateTimestamp(1),
    createdAt: generateTimestamp(1),
    updatedAt: generateTimestamp()
  },
  woodenKeycaps: {
    id: 'sub-003',
    title: 'Handcrafted Walnut Keycap Set',
    description: 'Full keycap set carved from sustainably sourced walnut wood, featuring natural grain patterns.',
    authorId: 'user_wood_001',
    author: generateUserRef('user_wood_001', 'GrainMaster', 10),
    images: [
      '/images/submissions/walnut-set-main.jpg',
      '/images/submissions/walnut-detail.jpg'
    ],
    category: 'Artisan Keycap',
    tags: ['wood', 'walnut', 'natural', 'sustainable', 'handcrafted'],
    likes: 76,
    views: 298,
    comments: 11,
    featured: false,
    status: 'approved',
    submittedAt: generateTimestamp(3),
    createdAt: generateTimestamp(3),
    updatedAt: generateTimestamp(2)
  }
}

// Discussions Test Data
export const mockDiscussions = {
  resinCasting: {
    id: 'disc-001',
    title: 'Best practices for resin casting?',
    content: 'I\'m new to resin casting and looking for tips on getting bubble-free results. What are your go-to techniques?',
    authorId: 'user_newbie_001',
    author: generateUserRef('user_newbie_001', 'ResinNewbie', 2),
    category: 'techniques',
    tags: ['resin', 'casting', 'tips', 'beginner'],
    replies: 18,
    views: 234,
    likes: 45,
    isLocked: false,
    isPinned: false,
    isHot: true,
    lastActivity: generateTimestamp(),
    lastReplyBy: generateUserRef('user_expert_001', 'ResinExpert', 12),
    createdAt: generateTimestamp(4),
    updatedAt: generateTimestamp()
  },
  weeklyShowcase: {
    id: 'disc-002',
    title: 'Weekly Build Showcase Thread',
    content: 'Share your latest keyboard builds, works in progress, and get feedback from the community!',
    authorId: 'admin',
    author: generateUserRef('admin', 'ModeratorBot', 15),
    category: 'showcase',
    tags: ['builds', 'showcase', 'weekly', 'feedback'],
    replies: 67,
    views: 1234,
    likes: 89,
    isLocked: false,
    isPinned: true,
    isHot: true,
    lastActivity: generateTimestamp(),
    lastReplyBy: generateUserRef('user_builder_001', 'BuildMaster', 9),
    createdAt: generateTimestamp(7),
    updatedAt: generateTimestamp()
  },
  switchPreferences: {
    id: 'disc-003',
    title: 'Switch preferences for different activities?',
    content: 'Do you use different switch types for gaming vs typing vs programming?',
    authorId: 'user_switcher_001',
    author: generateUserRef('user_switcher_001', 'SwitchExplorer', 7),
    category: 'hardware',
    tags: ['switches', 'gaming', 'typing', 'preferences'],
    replies: 34,
    views: 789,
    likes: 56,
    isLocked: false,
    isPinned: false,
    isHot: false,
    lastActivity: generateTimestamp(1),
    lastReplyBy: generateUserRef('user_gamer_001', 'ProGamer', 6),
    createdAt: generateTimestamp(6),
    updatedAt: generateTimestamp(1)
  }
}

// Activities Test Data
export const mockActivities = [
  {
    id: 'activity-001',
    type: 'submission',
    userId: 'user_artisan_001',
    user: generateUserRef('user_artisan_001', 'ArtisanMaster', 12),
    content: {
      title: 'New submission: Sakura Cherry Blossom Artisan',
      description: 'ArtisanMaster shared a new artisan keycap creation',
      image: '/images/submissions/sakura-keycap-main.jpg'
    },
    target: {
      type: 'submission',
      id: 'sub-001',
      title: 'Sakura Cherry Blossom Artisan'
    },
    metadata: {
      category: 'Artisan Keycap',
      points: 50
    },
    engagement: {
      likes: 23,
      comments: 5,
      shares: 2
    },
    isPublic: true,
    timestamp: generateTimestamp(1)
  },
  {
    id: 'activity-002',
    type: 'achievement',
    userId: 'user_newbie_001',
    user: generateUserRef('user_newbie_001', 'ResinNewbie', 2),
    content: {
      title: 'Achievement unlocked: First Discussion',
      description: 'ResinNewbie started their first community discussion'
    },
    metadata: {
      badge: 'First Discussion',
      points: 25
    },
    engagement: {
      likes: 8,
      comments: 0,
      shares: 0
    },
    isPublic: true,
    timestamp: generateTimestamp(2)
  },
  {
    id: 'activity-003',
    type: 'challenge',
    userId: 'user_setup_001',
    user: generateUserRef('user_setup_001', 'NeonRunner', 8),
    content: {
      title: 'Joined Winter Artisan Challenge',
      description: 'NeonRunner joined the Winter Artisan Challenge 2024'
    },
    target: {
      type: 'challenge',
      id: 'winter-challenge-2024',
      title: 'Winter Artisan Challenge 2024'
    },
    engagement: {
      likes: 5,
      comments: 1,
      shares: 0
    },
    isPublic: true,
    timestamp: generateTimestamp(3)
  }
]

// Leaderboard Test Data
export const mockLeaderboard = [
  {
    userId: 'user_artisan_001',
    userName: 'ArtisanMaster',
    userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ArtisanMaster',
    points: 3450,
    level: 12,
    rank: 1,
    change: 2,
    badges: ['Artisan Expert', 'Winter Challenger'],
    streak: 15,
    period: 'weekly',
    isOnline: true,
    lastUpdated: generateTimestamp()
  },
  {
    userId: 'user_wood_001',
    userName: 'GrainMaster',
    userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=GrainMaster',
    points: 2800,
    level: 10,
    rank: 2,
    change: -1,
    badges: ['Wood Worker', 'Sustainable Creator'],
    streak: 8,
    period: 'weekly',
    isOnline: false,
    lastUpdated: generateTimestamp()
  },
  {
    userId: 'user_setup_001',
    userName: 'NeonRunner',
    userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=NeonRunner',
    points: 2100,
    level: 8,
    rank: 3,
    change: 1,
    badges: ['Setup Master', 'RGB Specialist'],
    streak: 5,
    period: 'weekly',
    isOnline: true,
    lastUpdated: generateTimestamp()
  }
]

// Spotlight Test Data
export const mockSpotlight = {
  currentMonth: {
    userId: 'user_artisan_001',
    name: 'ArtisanMaster',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ArtisanMaster',
    level: 12,
    achievements: ['Artisan Expert', 'Winter Challenger', 'Community Helper'],
    joinDate: generateTimestamp(120),
    bio: 'Passionate artisan keycap creator specializing in nature-inspired designs with intricate resin work.',
    recentWork: [
      {
        id: 'sub-001',
        title: 'Sakura Cherry Blossom Artisan',
        image: '/images/submissions/sakura-keycap-main.jpg',
        likes: 134
      },
      {
        id: 'sub-004',
        title: 'Autumn Maple Leaf Keycap',
        image: '/images/submissions/maple-keycap.jpg',
        likes: 98
      }
    ],
    socialLinks: {
      instagram: 'artisan_master_keys',
      website: 'https://artisanmaster.com'
    },
    period: new Date().toISOString().slice(0, 7),
    createdAt: generateTimestamp(1),
    updatedAt: generateTimestamp()
  }
}

// Search Results Test Data
export const mockSearchResults = {
  artisan: {
    query: 'artisan',
    total: 15,
    results: {
      submissions: [mockSubmissions.sakuraKeycap, mockSubmissions.woodenKeycaps],
      discussions: [mockDiscussions.resinCasting],
      challenges: [mockChallenges.winterArtisan],
      members: [mockUsers.artisanMaster]
    }
  },
  rgb: {
    query: 'rgb',
    total: 8,
    results: {
      submissions: [mockSubmissions.cyberpunkSetup],
      discussions: [],
      challenges: [mockChallenges.rgbHarmony],
      members: [mockUsers.neonRunner]
    }
  },
  empty: {
    query: 'nonexistent',
    total: 0,
    results: {
      submissions: [],
      discussions: [],
      challenges: [],
      members: []
    }
  }
}

// Trending Searches Test Data
export const mockTrendingSearches = [
  'artisan keycap',
  'rgb keyboard',
  'mechanical switches',
  'custom build',
  'resin casting',
  'wood keycaps'
]

// Real-time Updates Test Data
export const mockRealtimeUpdates = {
  statsUpdate: {
    totalMembers: 2848,
    activeChallenges: 9,
    onlineUsers: 235,
    totalSubmissions: 1857,
    weeklyGrowth: 16
  },
  newActivity: {
    id: 'activity-realtime-001',
    type: 'submission',
    user: generateUserRef('user-realtime-001', 'LiveTester', 5),
    content: {
      title: 'New Real-time Submission',
      description: 'LiveTester shared a new artisan keycap creation'
    },
    timestamp: new Date(),
    engagement: {
      likes: 0,
      comments: 0,
      shares: 0
    }
  },
  leaderboardUpdate: {
    userId: 'user-realtime-001',
    userName: 'LiveTester',
    userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiveTester',
    points: 1150,
    level: 5,
    rank: 10,
    change: 3
  }
}

// Test File Data
export const testFiles = {
  validImage: {
    name: 'test-keycap.jpg',
    size: 1024 * 512, // 512KB
    type: 'image/jpeg',
    content: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAoHBwgHBgoICAoLCwoMDxkODw4ODx4XFhQREhQV'
  },
  largeImage: {
    name: 'large-image.jpg',
    size: 1024 * 1024 * 20, // 20MB (over limit)
    type: 'image/jpeg',
    content: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAoHBwgHBgoICAoLCwoMDxkODw4ODx4XFhQREhQV'
  },
  invalidFile: {
    name: 'test-document.pdf',
    size: 1024 * 100, // 100KB
    type: 'application/pdf',
    content: 'data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsOzCjIgMCBvYmo...'
  }
}

// Export all test data
export const communityTestData = {
  stats: mockCommunityStats,
  users: mockUsers,
  challenges: mockChallenges,
  submissions: mockSubmissions,
  discussions: mockDiscussions,
  activities: mockActivities,
  leaderboard: mockLeaderboard,
  spotlight: mockSpotlight,
  search: mockSearchResults,
  trending: mockTrendingSearches,
  realtime: mockRealtimeUpdates,
  files: testFiles
}

export default communityTestData