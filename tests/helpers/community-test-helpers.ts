/**
 * Community Test Helpers and Utilities
 * 
 * Provides reusable helper functions for community E2E tests including:
 * - Authentication setup
 * - Firebase mocking
 * - Page navigation utilities
 * - Data verification helpers
 * - Performance measurement tools
 * 
 * <AUTHOR> Team
 */

import { Page, expect } from '@playwright/test'
import { communityTestData } from '../fixtures/community-test-data'

// Constants
export const COMMUNITY_ROUTES = {
  discover: '/community/discover',
  submissions: '/community/submissions',
  discussions: '/community/discussions',
  challenges: '/community/challenges',
  leaderboard: '/community/leaderboard'
} as const

export const PERFORMANCE_THRESHOLDS = {
  pageLoad: 3000,
  searchResponse: 500,
  fileUpload: 10000,
  formSubmission: 5000,
  imageLoad: 2000
} as const

export const TEST_TIMEOUTS = {
  default: 5000,
  long: 10000,
  short: 2000
} as const

// Authentication Helpers
export class AuthHelpers {
  static async setupTestUser(page: Page, userType: 'authenticated' | 'admin' | 'new' = 'authenticated') {
    const users = {
      authenticated: {
        uid: 'test-user-123',
        email: '<EMAIL>',
        displayName: 'Test User',
        isAuthenticated: true
      },
      admin: {
        uid: 'admin-user-123',
        email: '<EMAIL>',
        displayName: 'Admin User',
        isAuthenticated: true,
        isAdmin: true
      },
      new: {
        uid: 'new-user-123',
        email: '<EMAIL>',
        displayName: 'New User',
        isAuthenticated: true,
        isNew: true
      }
    }

    await page.evaluate((user) => {
      localStorage.setItem('test-user', JSON.stringify(user))
    }, users[userType])
  }

  static async clearAuth(page: Page) {
    await page.evaluate(() => {
      localStorage.removeItem('test-user')
      sessionStorage.clear()
    })
  }

  static async verifyAuthState(page: Page, shouldBeAuthenticated: boolean) {
    const isAuthenticated = await page.evaluate(() => {
      const user = localStorage.getItem('test-user')
      return user ? JSON.parse(user).isAuthenticated : false
    })

    expect(isAuthenticated).toBe(shouldBeAuthenticated)
  }
}

// Firebase Mocking Helpers
export class FirebaseMockHelpers {
  static async setupFirebaseMocks(page: Page) {
    await page.addInitScript(() => {
      // Mock Firebase SDK
      window.mockFirebase = {
        initialized: true,
        connected: true,
        collections: new Map(),
        subscriptions: new Map()
      }

      // Mock Firestore
      window.mockFirestore = {
        doc: (path: string) => ({
          get: () => Promise.resolve({ data: () => window.mockFirebase.collections.get(path) }),
          set: (data: any) => {
            window.mockFirebase.collections.set(path, data)
            return Promise.resolve()
          },
          onSnapshot: (callback: Function) => {
            const unsubscribe = () => {}
            setTimeout(() => callback({ data: () => window.mockFirebase.collections.get(path) }), 100)
            return unsubscribe
          }
        }),
        collection: (name: string) => ({
          where: () => ({ get: () => Promise.resolve({ docs: [] }) }),
          orderBy: () => ({ limit: () => ({ get: () => Promise.resolve({ docs: [] }) }) }),
          get: () => Promise.resolve({ docs: [] })
        })
      }

      // Mock Firebase Storage
      window.mockFirebaseStorage = {
        ref: (path: string) => ({
          put: (file: File) => ({
            on: (event: string, progress: Function, error: Function, complete: Function) => {
              setTimeout(() => progress({ bytesTransferred: file.size * 0.5, totalBytes: file.size }), 500)
              setTimeout(() => progress({ bytesTransferred: file.size, totalBytes: file.size }), 1000)
              setTimeout(() => complete(), 1200)
            }
          }),
          getDownloadURL: () => Promise.resolve(`https://storage.example.com/${path}`)
        })
      }
    })
  }

  static async mockCommunityData(page: Page) {
    await page.addInitScript((testData) => {
      window.mockCommunityData = testData
      
      // Override API calls to return mock data
      window.mockApiResponses = {
        '/api/community/stats': testData.stats.default,
        '/api/community/submissions': testData.submissions,
        '/api/community/discussions': testData.discussions,
        '/api/community/challenges': testData.challenges,
        '/api/community/activities': testData.activities,
        '/api/community/leaderboard': testData.leaderboard,
        '/api/community/search': testData.search,
        '/api/community/trending': testData.trending
      }
    }, communityTestData)
  }

  static async mockRealtimeUpdates(page: Page) {
    await page.addInitScript(() => {
      // Mock WebSocket for real-time features
      window.mockWebSocket = {
        connected: false,
        listeners: new Map(),
        connect: () => {
          window.mockWebSocket.connected = true
          window.dispatchEvent(new CustomEvent('websocket-connected'))
        },
        disconnect: () => {
          window.mockWebSocket.connected = false
          window.dispatchEvent(new CustomEvent('websocket-disconnected'))
        },
        on: (event: string, callback: Function) => {
          window.mockWebSocket.listeners.set(event, callback)
        },
        emit: (event: string, data: any) => {
          const callback = window.mockWebSocket.listeners.get(event)
          if (callback) callback(data)
        },
        send: (data: any) => console.log('Mock WebSocket send:', data)
      }

      // Auto-connect after delay
      setTimeout(() => window.mockWebSocket.connect(), 1000)
    })
  }

  static async simulateNetworkError(page: Page, pattern: string) {
    await page.route(pattern, route => route.abort())
  }

  static async simulateSlowNetwork(page: Page, pattern: string, delay: number = 2000) {
    await page.route(pattern, async route => {
      await page.waitForTimeout(delay)
      await route.continue()
    })
  }
}

// Navigation Helpers
export class NavigationHelpers {
  static async waitForPageLoad(page: Page, expectedPath: string, timeout = TEST_TIMEOUTS.default) {
    await page.waitForURL(`**${expectedPath}`, { timeout })
    await page.waitForLoadState('networkidle')
  }

  static async waitForTabLoad(page: Page, tabTestId: string, timeout = TEST_TIMEOUTS.default) {
    await page.waitForSelector(`[data-testid="${tabTestId}"]`, { timeout })
    await page.waitForLoadState('networkidle')
  }

  static async navigateToTab(page: Page, tabName: string) {
    const tabSelector = `[data-testid="tab-${tabName}"]`
    await page.locator(tabSelector).click()
    await this.waitForTabLoad(page, `${tabName}-content`)
  }

  static async verifyActiveTab(page: Page, expectedTab: string) {
    const activeTab = page.locator(`[data-testid="tab-${expectedTab}"][aria-selected="true"]`)
    await expect(activeTab).toBeVisible()
  }

  static async navigateWithHistory(page: Page, route: string) {
    await page.goto(route)
    await this.waitForPageLoad(page, route)
  }

  static async checkBreadcrumb(page: Page, expectedItems: string[]) {
    const breadcrumb = page.locator('[data-testid="breadcrumb"]')
    
    if (await breadcrumb.isVisible()) {
      for (const item of expectedItems) {
        await expect(breadcrumb).toContainText(item)
      }
    }
  }
}

// Data Verification Helpers
export class DataVerificationHelpers {
  static async verifyStatsDisplay(page: Page, expectedStats: any) {
    await expect(page.locator('[data-testid="total-members"]')).toContainText(expectedStats.totalMembers.toString())
    await expect(page.locator('[data-testid="active-challenges"]')).toContainText(expectedStats.activeChallenges.toString())
    await expect(page.locator('[data-testid="online-users"]')).toContainText(expectedStats.onlineUsers.toString())
    await expect(page.locator('[data-testid="total-submissions"]')).toContainText(expectedStats.totalSubmissions.toString())
  }

  static async verifySubmissionCard(page: Page, submission: any, index = 0) {
    const card = page.locator('[data-testid="submission-card"]').nth(index)
    
    await expect(card.locator('[data-testid="submission-title"]')).toContainText(submission.title)
    await expect(card.locator('[data-testid="submission-author"]')).toContainText(submission.author.userName)
    await expect(card.locator('[data-testid="submission-likes"]')).toContainText(submission.likes.toString())
    
    if (submission.featured) {
      await expect(card.locator('[data-testid="featured-badge"]')).toBeVisible()
    }
  }

  static async verifyDiscussionItem(page: Page, discussion: any, index = 0) {
    const item = page.locator('[data-testid="discussion-item"]').nth(index)
    
    await expect(item.locator('[data-testid="discussion-title"]')).toContainText(discussion.title)
    await expect(item.locator('[data-testid="discussion-author"]')).toContainText(discussion.author.userName)
    await expect(item.locator('[data-testid="discussion-replies"]')).toContainText(discussion.replies.toString())
    
    if (discussion.isPinned) {
      await expect(item.locator('[data-testid="pinned-indicator"]')).toBeVisible()
    }
  }

  static async verifyChallengeCard(page: Page, challenge: any, index = 0) {
    const card = page.locator('[data-testid="challenge-card"]').nth(index)
    
    await expect(card.locator('[data-testid="challenge-title"]')).toContainText(challenge.title)
    await expect(card.locator('[data-testid="challenge-participants"]')).toContainText(challenge.participants.toString())
    await expect(card.locator('[data-testid="challenge-difficulty"]')).toContainText(challenge.difficulty)
    
    if (challenge.status === 'active') {
      await expect(card.locator('[data-testid="active-badge"]')).toBeVisible()
    }
  }

  static async verifyLeaderboardItem(page: Page, member: any, index = 0) {
    const item = page.locator('[data-testid="leaderboard-item"]').nth(index)
    
    await expect(item.locator('[data-testid="member-name"]')).toContainText(member.userName)
    await expect(item.locator('[data-testid="member-points"]')).toContainText(member.points.toString())
    await expect(item.locator('[data-testid="member-rank"]')).toContainText(member.rank.toString())
    
    if (member.isOnline) {
      await expect(item.locator('[data-testid="online-indicator"]')).toBeVisible()
    }
  }

  static async verifyActivityItem(page: Page, activity: any, index = 0) {
    const item = page.locator('[data-testid="activity-item"]').nth(index)
    
    await expect(item.locator('[data-testid="activity-user"]')).toContainText(activity.user.userName)
    await expect(item.locator('[data-testid="activity-content"]')).toContainText(activity.content.title)
    
    if (activity.metadata?.points) {
      await expect(item.locator('[data-testid="activity-points"]')).toContainText(`+${activity.metadata.points}`)
    }
  }
}

// Search Helpers
export class SearchHelpers {
  static async performSearch(page: Page, query: string, waitForResults = true) {
    const searchInput = page.locator('[data-testid="search-input"]')
    await searchInput.fill(query)
    
    if (waitForResults) {
      // Wait for debounced search
      await page.waitForTimeout(400)
      await page.waitForSelector('[data-testid="search-results"]', { timeout: TEST_TIMEOUTS.default })
    }
  }

  static async clearSearch(page: Page) {
    const searchInput = page.locator('[data-testid="search-input"]')
    await searchInput.clear()
    await page.waitForTimeout(200)
  }

  static async selectFilter(page: Page, filterType: string) {
    await page.locator(`[data-testid="filter-${filterType}"]`).click()
  }

  static async verifySearchResults(page: Page, expectedCount: number, contentType?: string) {
    const results = page.locator('[data-testid="result-item"]')
    await expect(results).toHaveCount(expectedCount, { timeout: TEST_TIMEOUTS.default })
    
    if (contentType) {
      const firstResult = results.first()
      await expect(firstResult.locator('[data-testid="result-type"]')).toContainText(contentType)
    }
  }

  static async verifyTrendingSearches(page: Page, expectedCount: number) {
    const trendingItems = page.locator('[data-testid="trending-item"]')
    await expect(trendingItems).toHaveCount(expectedCount, { timeout: TEST_TIMEOUTS.default })
  }

  static async clickTrendingSearch(page: Page, index = 0) {
    const trendingItem = page.locator('[data-testid="trending-item"]').nth(index)
    const text = await trendingItem.textContent()
    await trendingItem.click()
    
    // Verify search input was populated
    const searchInput = page.locator('[data-testid="search-input"]')
    await expect(searchInput).toHaveValue(text?.trim() || '')
  }
}

// Upload Helpers
export class UploadHelpers {
  static async uploadFile(page: Page, filePath: string) {
    const fileUpload = page.locator('[data-testid="file-input"]')
    await fileUpload.setInputFiles(filePath)
  }

  static async uploadMultipleFiles(page: Page, filePaths: string[]) {
    const fileUpload = page.locator('[data-testid="file-input"]')
    await fileUpload.setInputFiles(filePaths)
  }

  static async simulateDragAndDrop(page: Page, file: any) {
    const dropZone = page.locator('[data-testid="drop-zone"]')
    
    await dropZone.dispatchEvent('dragenter')
    await dropZone.dispatchEvent('dragover')
    
    await page.evaluate((fileData) => {
      const dt = new DataTransfer()
      const file = new File([fileData.content], fileData.name, { type: fileData.type })
      dt.items.add(file)
      
      const dropEvent = new DragEvent('drop', {
        dataTransfer: dt,
        bubbles: true,
        cancelable: true
      })
      
      document.querySelector('[data-testid="drop-zone"]')?.dispatchEvent(dropEvent)
    }, file)
  }

  static async fillSubmissionForm(page: Page, data: any) {
    await page.locator('[data-testid="title-input"]').fill(data.title)
    await page.locator('[data-testid="description-textarea"]').fill(data.description)
    await page.locator('[data-testid="category-select"]').selectOption(data.category)
    
    // Add tags
    for (const tag of data.tags || []) {
      await page.locator('[data-testid="tag-input"]').fill(tag)
      await page.keyboard.press('Enter')
    }
  }

  static async verifyFilePreview(page: Page, fileName: string) {
    await expect(page.locator('[data-testid="file-preview"]')).toBeVisible()
    await expect(page.locator('[data-testid="file-name"]')).toContainText(fileName)
  }

  static async verifyUploadProgress(page: Page) {
    await expect(page.locator('[data-testid="upload-progress"]')).toBeVisible()
    await expect(page.locator('[data-testid="progress-bar"]')).toBeVisible()
  }

  static async submitForm(page: Page) {
    await page.locator('[data-testid="submit-button"]').click()
  }

  static async verifySubmissionSuccess(page: Page) {
    await expect(page.locator('[data-testid="submission-success"]')).toBeVisible({ timeout: TEST_TIMEOUTS.long })
    await expect(page.locator('text=Submission uploaded successfully!')).toBeVisible()
  }
}

// Real-time Helpers
export class RealtimeHelpers {
  static async waitForRealtimeConnection(page: Page) {
    await page.waitForSelector('[data-testid="realtime-status"]', { timeout: TEST_TIMEOUTS.long })
    
    await page.waitForFunction(() => {
      const status = document.querySelector('[data-testid="realtime-status"]')
      return status && status.textContent?.includes('connected')
    }, { timeout: TEST_TIMEOUTS.long })
  }

  static async simulateRealtimeUpdate(page: Page, updateType: string, data: any) {
    await page.evaluate(([type, updateData]) => {
      window.dispatchEvent(new CustomEvent('realtime-update', {
        detail: { type, data: updateData }
      }))
    }, [updateType, data])
  }

  static async verifyRealtimeUpdate(page: Page, selector: string, expectedValue: string) {
    await page.waitForFunction(([sel, value]) => {
      const element = document.querySelector(sel)
      return element && element.textContent?.includes(value)
    }, [selector, expectedValue], { timeout: TEST_TIMEOUTS.default })
  }

  static async triggerLikeUpdate(page: Page, submissionId: string, newCount: number) {
    await page.evaluate(([id, count]) => {
      window.dispatchEvent(new CustomEvent('like-update', {
        detail: { submissionId: id, newLikeCount: count }
      }))
    }, [submissionId, newCount])
  }

  static async verifyConnectionStatus(page: Page, expectedStatus: 'connected' | 'disconnected' | 'connecting') {
    const statusIndicator = page.locator('[data-testid="realtime-status"]')
    await expect(statusIndicator).toContainText(expectedStatus)
    await expect(statusIndicator).toHaveClass(new RegExp(expectedStatus))
  }
}

// Performance Helpers
export class PerformanceHelpers {
  static async measurePageLoad(page: Page, url: string): Promise<number> {
    const startTime = Date.now()
    await page.goto(url)
    await page.waitForLoadState('networkidle')
    return Date.now() - startTime
  }

  static async measureSearchPerformance(page: Page, query: string): Promise<number> {
    const startTime = Date.now()
    await SearchHelpers.performSearch(page, query)
    return Date.now() - startTime
  }

  static async measureUploadPerformance(page: Page, filePath: string): Promise<number> {
    const startTime = Date.now()
    await UploadHelpers.uploadFile(page, filePath)
    await page.waitForSelector('[data-testid="file-preview"]')
    return Date.now() - startTime
  }

  static verifyPerformance(actualTime: number, threshold: number, operation: string) {
    expect(actualTime).toBeLessThan(threshold)
    console.log(`${operation} completed in ${actualTime}ms (threshold: ${threshold}ms)`)
  }

  static async getWebVitals(page: Page) {
    return await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const vitals: any = {}
          
          entries.forEach((entry) => {
            if (entry.entryType === 'measure') {
              vitals[entry.name] = entry.duration
            }
          })
          
          resolve(vitals)
        }).observe({ entryTypes: ['measure'] })
        
        // Trigger measurement
        performance.mark('vitals-start')
        setTimeout(() => {
          performance.mark('vitals-end')
          performance.measure('page-load', 'vitals-start', 'vitals-end')
        }, 100)
      })
    })
  }
}

// Accessibility Helpers
export class AccessibilityHelpers {
  static async verifyKeyboardNavigation(page: Page, elements: string[]) {
    for (let i = 0; i < elements.length; i++) {
      await page.keyboard.press('Tab')
      const focusedElement = page.locator(elements[i])
      await expect(focusedElement).toBeFocused()
    }
  }

  static async verifyAriaLabels(page: Page, selectors: string[]) {
    for (const selector of selectors) {
      await expect(page.locator(selector)).toHaveAttribute('aria-label')
    }
  }

  static async verifyHeadingHierarchy(page: Page) {
    const h1Count = await page.locator('h1').count()
    expect(h1Count).toBe(1)
    
    const h2Count = await page.locator('h2').count()
    expect(h2Count).toBeGreaterThan(0)
  }

  static async verifyFocusVisibility(page: Page) {
    await page.keyboard.press('Tab')
    
    const focusedElement = page.locator(':focus')
    await expect(focusedElement).toBeVisible()
    
    const focusStyles = await focusedElement.evaluate((el) => {
      const styles = getComputedStyle(el)
      return {
        outline: styles.outline,
        boxShadow: styles.boxShadow
      }
    })
    
    expect(focusStyles.outline !== 'none' || focusStyles.boxShadow !== 'none').toBe(true)
  }

  static async verifyLiveRegions(page: Page) {
    const liveRegions = await page.locator('[aria-live]').count()
    expect(liveRegions).toBeGreaterThan(0)
  }
}

// Error Helpers
export class ErrorHelpers {
  static async verifyErrorMessage(page: Page, expectedMessage: string) {
    await expect(page.locator('[data-testid="error-message"]')).toContainText(expectedMessage)
  }

  static async verifyRetryButton(page: Page) {
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
  }

  static async clickRetry(page: Page) {
    await page.locator('[data-testid="retry-button"]').click()
  }

  static async verifyGracefulDegradation(page: Page, mainContent: string) {
    // Even with errors, main content should still be visible
    await expect(page.locator(`[data-testid="${mainContent}"]`)).toBeVisible()
  }
}

// Export all helpers
export {
  communityTestData,
  COMMUNITY_ROUTES,
  PERFORMANCE_THRESHOLDS,
  TEST_TIMEOUTS
}