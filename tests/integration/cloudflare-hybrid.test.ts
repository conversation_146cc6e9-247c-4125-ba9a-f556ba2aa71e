/**
 * Cloudflare Hybrid Integration Tests
 * 
 * Comprehensive test suite for validating Cloudflare hybrid deployment
 * components including R2 storage, CDN optimization, and performance monitoring.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/testing-library/jest-dom'
import { r2Storage, HybridImageUploadService } from '../../src/lib/cloudflare/r2Storage'
import { cdnPerformanceMonitor } from '../../src/lib/cloudflare/cdnConfig'
import { hybridPerformanceMonitor } from '../../src/lib/monitoring/hybridPerformanceMonitor'
import { shouldUseR2Storage, shouldUseCloudflareWorkers } from '../../src/lib/config/featureFlags'

// Test configuration
const TEST_CONFIG = {
  testImageUrl: 'https://via.placeholder.com/300x200.jpg',
  testImageBuffer: Buffer.from('test image data'),
  testUserId: 'test-user-123',
  timeout: 30000
}

describe('Cloudflare Hybrid Integration Tests', () => {
  let hybridUploadService: HybridImageUploadService

  beforeAll(async () => {
    // Initialize services
    hybridUploadService = new HybridImageUploadService()
    
    // Set test environment variables
    process.env.FEATURE_R2_STORAGE = 'true'
    process.env.R2_ROLLOUT_PERCENTAGE = '100'
    process.env.FEATURE_CF_WORKERS = 'true'
    process.env.CF_WORKERS_ROLLOUT_PERCENTAGE = '100'
  }, TEST_CONFIG.timeout)

  afterAll(async () => {
    // Cleanup test environment
    delete process.env.FEATURE_R2_STORAGE
    delete process.env.R2_ROLLOUT_PERCENTAGE
    delete process.env.FEATURE_CF_WORKERS
    delete process.env.CF_WORKERS_ROLLOUT_PERCENTAGE
  })

  describe('R2 Storage Service', () => {
    it('should test R2 connection successfully', async () => {
      const isConnected = await r2Storage.testConnection()
      expect(isConnected).toBe(true)
    }, TEST_CONFIG.timeout)

    it('should upload image to R2', async () => {
      const testKey = `test/upload-${Date.now()}.jpg`
      
      const result = await r2Storage.uploadImage(TEST_CONFIG.testImageBuffer, testKey, {
        contentType: 'image/jpeg',
        metadata: { test: 'true' }
      })

      expect(result).toMatchObject({
        key: testKey,
        source: 'r2',
        contentType: 'image/jpeg'
      })
      expect(result.url).toContain(testKey)

      // Cleanup
      await r2Storage.deleteObject(testKey)
    }, TEST_CONFIG.timeout)

    it('should check if object exists', async () => {
      const testKey = `test/exists-${Date.now()}.jpg`
      
      // Should not exist initially
      let exists = await r2Storage.objectExists(testKey)
      expect(exists).toBe(false)

      // Upload and check again
      await r2Storage.uploadImage(TEST_CONFIG.testImageBuffer, testKey)
      exists = await r2Storage.objectExists(testKey)
      expect(exists).toBe(true)

      // Cleanup
      await r2Storage.deleteObject(testKey)
    }, TEST_CONFIG.timeout)

    it('should delete object from R2', async () => {
      const testKey = `test/delete-${Date.now()}.jpg`
      
      // Upload first
      await r2Storage.uploadImage(TEST_CONFIG.testImageBuffer, testKey)
      
      // Verify exists
      let exists = await r2Storage.objectExists(testKey)
      expect(exists).toBe(true)

      // Delete
      await r2Storage.deleteObject(testKey)
      
      // Verify deleted
      exists = await r2Storage.objectExists(testKey)
      expect(exists).toBe(false)
    }, TEST_CONFIG.timeout)

    it('should generate presigned upload URL', async () => {
      const testKey = `test/presigned-${Date.now()}.jpg`
      
      const presignedUrl = await r2Storage.generatePresignedUploadUrl(
        testKey,
        'image/jpeg',
        3600
      )

      expect(presignedUrl).toContain(testKey)
      expect(presignedUrl).toContain('X-Amz-Signature')
    }, TEST_CONFIG.timeout)

    it('should get object metadata', async () => {
      const testKey = `test/metadata-${Date.now()}.jpg`
      const testMetadata = { test: 'metadata', purpose: 'testing' }
      
      // Upload with metadata
      await r2Storage.uploadImage(TEST_CONFIG.testImageBuffer, testKey, {
        metadata: testMetadata
      })

      // Get metadata
      const metadata = await r2Storage.getObjectMetadata(testKey)
      
      expect(metadata).toMatchObject({
        size: TEST_CONFIG.testImageBuffer.length,
        contentType: 'image/jpeg',
        metadata: testMetadata
      })

      // Cleanup
      await r2Storage.deleteObject(testKey)
    }, TEST_CONFIG.timeout)

    it('should upload backup data', async () => {
      const testKey = `test/backup-${Date.now()}.json`
      const testData = { test: 'backup', timestamp: Date.now() }
      
      const result = await r2Storage.uploadBackup(
        JSON.stringify(testData),
        testKey,
        { contentType: 'application/json' }
      )

      expect(result).toMatchObject({
        key: testKey,
        source: 'r2',
        contentType: 'application/json'
      })

      // Cleanup
      await r2Storage.deleteObject(testKey, 'backups')
    }, TEST_CONFIG.timeout)
  })

  describe('Feature Flags', () => {
    beforeEach(() => {
      // Reset environment variables
      delete process.env.FEATURE_R2_STORAGE
      delete process.env.R2_ROLLOUT_PERCENTAGE
      delete process.env.FEATURE_CF_WORKERS
      delete process.env.CF_WORKERS_ROLLOUT_PERCENTAGE
    })

    it('should respect R2 storage feature flag', () => {
      // Disabled
      process.env.FEATURE_R2_STORAGE = 'false'
      expect(shouldUseR2Storage(TEST_CONFIG.testUserId)).toBe(false)

      // Enabled with 0% rollout
      process.env.FEATURE_R2_STORAGE = 'true'
      process.env.R2_ROLLOUT_PERCENTAGE = '0'
      expect(shouldUseR2Storage(TEST_CONFIG.testUserId)).toBe(false)

      // Enabled with 100% rollout
      process.env.R2_ROLLOUT_PERCENTAGE = '100'
      expect(shouldUseR2Storage(TEST_CONFIG.testUserId)).toBe(true)
    })

    it('should respect Cloudflare Workers feature flag', () => {
      // Disabled
      process.env.FEATURE_CF_WORKERS = 'false'
      expect(shouldUseCloudflareWorkers(TEST_CONFIG.testUserId)).toBe(false)

      // Enabled with 100% rollout
      process.env.FEATURE_CF_WORKERS = 'true'
      process.env.CF_WORKERS_ROLLOUT_PERCENTAGE = '100'
      expect(shouldUseCloudflareWorkers(TEST_CONFIG.testUserId)).toBe(true)
    })

    it('should provide consistent rollout for same user', () => {
      process.env.FEATURE_R2_STORAGE = 'true'
      process.env.R2_ROLLOUT_PERCENTAGE = '50'

      const result1 = shouldUseR2Storage(TEST_CONFIG.testUserId)
      const result2 = shouldUseR2Storage(TEST_CONFIG.testUserId)
      
      expect(result1).toBe(result2)
    })
  })

  describe('Hybrid Image Upload Service', () => {
    beforeEach(() => {
      process.env.FEATURE_R2_STORAGE = 'true'
      process.env.R2_ROLLOUT_PERCENTAGE = '100'
    })

    it('should upload image using R2 when feature flag is enabled', async () => {
      const testKey = `test/hybrid-${Date.now()}.jpg`
      
      const result = await hybridUploadService.uploadImage(
        TEST_CONFIG.testImageBuffer,
        testKey,
        { userId: TEST_CONFIG.testUserId }
      )

      expect(result.source).toBe('r2')
      expect(result.key).toBe(testKey)

      // Cleanup
      await r2Storage.deleteObject(testKey)
    }, TEST_CONFIG.timeout)

    it('should handle R2 upload failure gracefully', async () => {
      // Temporarily break R2 configuration
      const originalAccountId = process.env.CLOUDFLARE_ACCOUNT_ID
      delete process.env.CLOUDFLARE_ACCOUNT_ID

      const testKey = `test/fallback-${Date.now()}.jpg`
      
      // Should fallback to Firebase (or throw error if Firebase not configured)
      await expect(
        hybridUploadService.uploadImage(
          TEST_CONFIG.testImageBuffer,
          testKey,
          { userId: TEST_CONFIG.testUserId }
        )
      ).rejects.toThrow()

      // Restore configuration
      process.env.CLOUDFLARE_ACCOUNT_ID = originalAccountId
    }, TEST_CONFIG.timeout)
  })

  describe('Performance Monitoring', () => {
    beforeEach(() => {
      // Clear previous metrics
      hybridPerformanceMonitor.cleanup()
    })

    it('should record image load times', () => {
      const testUrl = 'https://test.r2.cloudflarestorage.com/test.jpg'
      const loadTime = 150

      hybridPerformanceMonitor.recordImageLoadTime(testUrl, loadTime, 'cloudflare')

      const realTimeMetrics = hybridPerformanceMonitor.getRealTimeMetrics()
      expect(realTimeMetrics['image_load_time_cloudflare']).toBe(loadTime)
    })

    it('should record API response times', () => {
      const endpoint = '/api/products'
      const responseTime = 250

      hybridPerformanceMonitor.recordApiResponseTime(endpoint, responseTime, true)

      const realTimeMetrics = hybridPerformanceMonitor.getRealTimeMetrics()
      expect(realTimeMetrics['api_response_time_cloudflare']).toBe(responseTime)
    })

    it('should record cache events', () => {
      const testUrl = 'https://test.com/api/data'

      hybridPerformanceMonitor.recordCacheEvent(testUrl, 'HIT')
      hybridPerformanceMonitor.recordCacheEvent(testUrl, 'MISS')

      const realTimeMetrics = hybridPerformanceMonitor.getRealTimeMetrics()
      expect(realTimeMetrics['cache_hit_rate_cloudflare']).toBe(0.5) // 50% hit rate
    })

    it('should generate performance report', () => {
      // Record some test metrics
      hybridPerformanceMonitor.recordImageLoadTime('test1.jpg', 100, 'cloudflare')
      hybridPerformanceMonitor.recordImageLoadTime('test2.jpg', 200, 'cloudflare')
      hybridPerformanceMonitor.recordApiResponseTime('/api/test', 300, false)

      const report = hybridPerformanceMonitor.generatePerformanceReport()

      expect(report).toMatchObject({
        timestamp: expect.any(String),
        metrics: expect.any(Object),
        recommendations: expect.any(Array),
        alerts: expect.any(Array)
      })

      expect(report.metrics['image_load_time_cloudflare']).toMatchObject({
        average: 150,
        samples: 2
      })
    })

    it('should generate alerts for threshold violations', () => {
      // Record metrics that exceed thresholds
      hybridPerformanceMonitor.recordImageLoadTime('slow.jpg', 1000, 'cloudflare') // Exceeds 500ms threshold
      hybridPerformanceMonitor.recordApiResponseTime('/api/slow', 2000, false) // Exceeds 1000ms threshold

      const report = hybridPerformanceMonitor.generatePerformanceReport()

      expect(report.alerts.length).toBeGreaterThan(0)
      expect(report.alerts.some(alert => alert.metric.includes('image_load_time'))).toBe(true)
      expect(report.alerts.some(alert => alert.metric.includes('api_response_time'))).toBe(true)
    })

    it('should provide recommendations based on metrics', () => {
      // Record Firebase vs Cloudflare performance difference
      hybridPerformanceMonitor.recordImageLoadTime('firebase.jpg', 500, 'firebase')
      hybridPerformanceMonitor.recordImageLoadTime('cloudflare.jpg', 200, 'cloudflare')

      const report = hybridPerformanceMonitor.generatePerformanceReport()

      expect(report.recommendations.length).toBeGreaterThan(0)
      expect(report.recommendations.some(rec => 
        rec.includes('migrating more images to Cloudflare R2')
      )).toBe(true)
    })
  })

  describe('CDN Performance Monitor', () => {
    beforeEach(() => {
      // Reset CDN monitor
      cdnPerformanceMonitor['metrics'].clear()
    })

    it('should record cache hits and misses', () => {
      cdnPerformanceMonitor.recordCacheHit('/test.jpg', 'HIT')
      cdnPerformanceMonitor.recordCacheHit('/test2.jpg', 'MISS')
      cdnPerformanceMonitor.recordCacheHit('/test3.jpg', 'HIT')

      const hitRate = cdnPerformanceMonitor.getCacheHitRate()
      expect(hitRate).toBeCloseTo(66.67, 1) // 2/3 = 66.67%
    })

    it('should record load times by category', () => {
      cdnPerformanceMonitor.recordLoadTime('/static/app.js', 100)
      cdnPerformanceMonitor.recordLoadTime('/static/style.css', 150)
      cdnPerformanceMonitor.recordLoadTime('/images/logo.png', 200)

      const staticLoadTime = cdnPerformanceMonitor.getAverageLoadTime('static')
      const imageLoadTime = cdnPerformanceMonitor.getAverageLoadTime('images')

      expect(staticLoadTime).toBe(125) // (100 + 150) / 2
      expect(imageLoadTime).toBe(200)
    })

    it('should generate CDN performance report', () => {
      // Record various metrics
      cdnPerformanceMonitor.recordCacheHit('/test1.jpg', 'HIT')
      cdnPerformanceMonitor.recordCacheHit('/test2.jpg', 'HIT')
      cdnPerformanceMonitor.recordCacheHit('/test3.jpg', 'MISS')
      cdnPerformanceMonitor.recordLoadTime('/static/app.js', 100)
      cdnPerformanceMonitor.recordLoadTime('/images/hero.jpg', 200)

      const report = cdnPerformanceMonitor.generateReport()

      expect(report).toMatchObject({
        timestamp: expect.any(String),
        cacheHitRate: expect.any(Number),
        averageLoadTimes: expect.any(Object),
        totalRequests: expect.any(Number)
      })

      expect(report.cacheHitRate).toBeCloseTo(66.67, 1)
      expect(report.totalRequests).toBe(5)
    })
  })

  describe('Error Handling and Resilience', () => {
    it('should handle R2 service unavailability', async () => {
      // Temporarily break R2 configuration
      const originalAccessKey = process.env.R2_ACCESS_KEY_ID
      process.env.R2_ACCESS_KEY_ID = 'invalid-key'

      const testKey = `test/error-${Date.now()}.jpg`
      
      await expect(
        r2Storage.uploadImage(TEST_CONFIG.testImageBuffer, testKey)
      ).rejects.toThrow()

      // Restore configuration
      process.env.R2_ACCESS_KEY_ID = originalAccessKey
    })

    it('should handle network timeouts gracefully', async () => {
      // This test would require mocking network delays
      // For now, we'll test that the service doesn't crash on errors
      
      const invalidUrl = 'https://invalid-domain-that-does-not-exist.com/image.jpg'
      
      await expect(
        fetch(invalidUrl)
      ).rejects.toThrow()
    })

    it('should record errors in performance monitoring', () => {
      hybridPerformanceMonitor.recordError('cloudflare', 'r2_upload_failed')
      hybridPerformanceMonitor.recordError('firebase', 'storage_quota_exceeded')

      const realTimeMetrics = hybridPerformanceMonitor.getRealTimeMetrics()
      expect(realTimeMetrics['error_rate_cloudflare']).toBe(1)
      expect(realTimeMetrics['error_rate_firebase']).toBe(1)
    })
  })
})
