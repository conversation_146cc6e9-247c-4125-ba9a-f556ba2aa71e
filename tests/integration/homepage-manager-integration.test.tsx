/**
 * Homepage Manager Integration Tests
 * Tests the complete workflow and integration between components
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import HomepageManagerPage from '@/app/admin/homepage/page'

// Mock Firebase and other external dependencies
jest.mock('@/lib/firestore', () => ({
  getProducts: jest.fn(() => Promise.resolve([
    {
      id: 'prod_1',
      name: 'Artisan Keycap Set',
      price: 45.99,
      image: '/images/keycap1.jpg',
      category: 'artisan'
    },
    {
      id: 'prod_2',
      name: 'Custom Spacebar',
      price: 15.99,
      image: '/images/spacebar1.jpg',
      category: 'accessories'
    }
  ])),
  updateProduct: jest.fn(() => Promise.resolve()),
  createProduct: jest.fn(() => Promise.resolve({ id: 'new_prod' }))
}))

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>
}))

describe('Homepage Manager Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Complete User Workflows', () => {
    test('complete hero slider configuration workflow', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Start with Hero Slider tab (default)
      await waitFor(() => {
        expect(screen.getByText('Hero Slider Settings')).toBeInTheDocument()
      })
      
      // 2. Configure hero slider settings
      const autoPlayToggle = screen.getByLabelText(/Auto-play slides/)
      await user.click(autoPlayToggle)
      
      const intervalInput = screen.getByLabelText(/Slide Interval/)
      await user.clear(intervalInput)
      await user.type(intervalInput, '5')
      
      // 3. Add a new slide
      const addSlideButton = screen.getByText('Add Slide')
      await user.click(addSlideButton)
      
      // 4. Enable preview mode to see changes
      const previewToggle = screen.getByLabelText(/Preview Mode/)
      await user.click(previewToggle)
      
      await waitFor(() => {
        expect(screen.getByText('Homepage Preview')).toBeInTheDocument()
      })
      
      // 5. Save changes
      const saveButton = screen.getByText('Save Changes')
      await user.click(saveButton)
      
      // Verify workflow completion
      expect(autoPlayToggle).toBeChecked()
      expect(intervalInput).toHaveValue(5)
    })

    test('A/B testing setup and monitoring workflow', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Navigate to A/B Testing tab
      const abTestingTab = screen.getByText('A/B Testing')
      await user.click(abTestingTab)
      
      await waitFor(() => {
        expect(screen.getByText('A/B Testing Settings')).toBeInTheDocument()
      })
      
      // 2. Enable A/B testing
      const enableToggle = screen.getByLabelText(/Enable A\/B Testing/)
      await user.click(enableToggle)
      
      // 3. Check active tests
      await waitFor(() => {
        expect(screen.getByText('Hero Banner A/B Test')).toBeInTheDocument()
        expect(screen.getByText('running')).toBeInTheDocument()
      })
      
      // 4. Review test metrics
      expect(screen.getByText('2,847')).toBeInTheDocument() // visitor count
      expect(screen.getByText('87')).toBeInTheDocument() // conversions
      
      // 5. Check statistical significance
      expect(screen.getByText('94.2%')).toBeInTheDocument() // significance
    })

    test('AI analytics and optimization workflow', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Navigate to Analytics tab
      const analyticsTab = screen.getByText('Analytics')
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('AI Performance Insights')).toBeInTheDocument()
      })
      
      // 2. Review AI suggestions
      expect(screen.getByText('Hero slider CTR increased 23% with 5-second intervals')).toBeInTheDocument()
      expect(screen.getByText('Community trending performs best during 2-6 PM EST')).toBeInTheDocument()
      
      // 3. Check performance heatmap
      expect(screen.getByText('Section Performance Heatmap')).toBeInTheDocument()
      expect(screen.getByText('Hero Slider')).toBeInTheDocument()
      expect(screen.getByText('95%')).toBeInTheDocument() // engagement rate
      
      // 4. View real-time metrics
      expect(screen.getByText('Real-time Metrics')).toBeInTheDocument()
      expect(screen.getByText('247')).toBeInTheDocument() // active users
      expect(screen.getByText('3.2s')).toBeInTheDocument() // load time
    })

    test('smart scheduling and optimization workflow', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Navigate to Smart Scheduling tab
      const schedulingTab = screen.getByText('Smart Scheduling')
      await user.click(schedulingTab)
      
      await waitFor(() => {
        expect(screen.getByText('Smart Scheduling Settings')).toBeInTheDocument()
      })
      
      // 2. Enable smart scheduling features
      const enableToggle = screen.getByLabelText(/Enable Smart Scheduling/)
      await user.click(enableToggle)
      
      const autoPublishToggle = screen.getByLabelText(/Auto-Publish/)
      await user.click(autoPublishToggle)
      
      // 3. Review optimization insights
      expect(screen.getByText('Content Performance Optimization')).toBeInTheDocument()
      expect(screen.getByText('Active Optimization')).toBeInTheDocument()
      expect(screen.getByText('Hero slider reordered for mobile users')).toBeInTheDocument()
      
      // 4. Check performance metrics
      expect(screen.getByText('2.1s (-0.8s)')).toBeInTheDocument() // load time improvement
      expect(screen.getByText('94% (+7%)')).toBeInTheDocument() // efficiency improvement
    })
  })

  describe('Cross-Tab Data Consistency', () => {
    test('settings changes persist across tab switches', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Configure hero slider
      const autoPlayToggle = screen.getByLabelText(/Auto-play slides/)
      await user.click(autoPlayToggle)
      
      // 2. Switch to another tab
      const communityTab = screen.getByText('Community Trending')
      await user.click(communityTab)
      
      await waitFor(() => {
        expect(screen.getByText('Community Trending Settings')).toBeInTheDocument()
      })
      
      // 3. Switch back to hero slider
      const heroTab = screen.getByText('Hero Slider')
      await user.click(heroTab)
      
      await waitFor(() => {
        const autoPlayToggleAgain = screen.getByLabelText(/Auto-play slides/)
        expect(autoPlayToggleAgain).toBeChecked()
      })
    })

    test('preview mode reflects all tab configurations', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Configure multiple sections
      // Hero slider
      const autoPlayToggle = screen.getByLabelText(/Auto-play slides/)
      await user.click(autoPlayToggle)
      
      // Community trending
      const communityTab = screen.getByText('Community Trending')
      await user.click(communityTab)
      
      await waitFor(() => {
        const enableToggle = screen.getByLabelText(/Enable Community Trending/)
        await user.click(enableToggle)
      })
      
      // 2. Enable preview mode
      const previewToggle = screen.getByLabelText(/Preview Mode/)
      await user.click(previewToggle)
      
      // 3. Verify all configurations are reflected
      await waitFor(() => {
        expect(screen.getByText('Homepage Preview')).toBeInTheDocument()
        expect(screen.getByText('Community Trending')).toBeInTheDocument()
      })
    })
  })

  describe('Performance and Load Testing', () => {
    test('handles large datasets efficiently', async () => {
      // Mock large dataset
      const largeProductList = Array.from({ length: 100 }, (_, i) => ({
        id: `prod_${i}`,
        name: `Product ${i}`,
        price: 25.99 + i,
        image: `/images/product${i}.jpg`,
        category: i % 2 === 0 ? 'artisan' : 'accessories'
      }))
      
      jest.mocked(require('@/lib/firestore').getProducts).mockResolvedValueOnce(largeProductList)
      
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // Navigate to featured products
      const featuredTab = screen.getByText('Featured Products')
      await user.click(featuredTab)
      
      await waitFor(() => {
        expect(screen.getByText('Featured Products Settings')).toBeInTheDocument()
      })
      
      // Component should handle large dataset without performance issues
      expect(screen.getByText('Featured Products Settings')).toBeInTheDocument()
    })

    test('renders quickly with complex configurations', async () => {
      const startTime = performance.now()
      
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
      })
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // Should render within reasonable time (less than 2 seconds)
      expect(renderTime).toBeLessThan(2000)
    })
  })

  describe('Error Recovery and Resilience', () => {
    test('recovers from API failures gracefully', async () => {
      // Mock API failure
      jest.mocked(require('@/lib/firestore').getProducts).mockRejectedValueOnce(new Error('Network Error'))
      
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // Component should still be functional
      await waitFor(() => {
        expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
      })
      
      // User should still be able to navigate tabs
      const analyticsTab = screen.getByText('Analytics')
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('Analytics Settings')).toBeInTheDocument()
      })
    })

    test('handles invalid data gracefully', async () => {
      // Mock invalid data response
      jest.mocked(require('@/lib/firestore').getProducts).mockResolvedValueOnce([
        null,
        undefined,
        { id: 'valid_1', name: 'Valid Product', price: 25.99 },
        { id: 'invalid_1' }, // missing required fields
      ])
      
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
      })
      
      // Should not crash and should handle invalid data gracefully
      expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
    })
  })

  describe('Real-world Usage Scenarios', () => {
    test('marketing manager creating campaign workflow', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Create new hero banner for campaign
      const addSlideButton = screen.getByText('Add Slide')
      await user.click(addSlideButton)
      
      // 2. Set up A/B testing for campaign effectiveness
      const abTestingTab = screen.getByText('A/B Testing')
      await user.click(abTestingTab)
      
      await waitFor(() => {
        const enableToggle = screen.getByLabelText(/Enable A/B Testing/)
        await user.click(enableToggle)
      })
      
      // 3. Schedule campaign content
      const schedulingTab = screen.getByText('Smart Scheduling')
      await user.click(schedulingTab)
      
      await waitFor(() => {
        const scheduleButton = screen.getByText('Schedule Content')
        expect(scheduleButton).toBeInTheDocument()
      })
      
      // 4. Monitor performance
      const analyticsTab = screen.getByText('Analytics')
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('Real-time Metrics')).toBeInTheDocument()
      })
    })

    test('content manager optimizing user engagement', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // 1. Review current performance
      const analyticsTab = screen.getByText('Analytics')
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('Section Performance Heatmap')).toBeInTheDocument()
      })
      
      // 2. Enable community features for engagement
      const communityTab = screen.getByText('Community Trending')
      await user.click(communityTab)
      
      await waitFor(() => {
        const enableToggle = screen.getByLabelText(/Enable Community Trending/)
        await user.click(enableToggle)
      })
      
      // 3. Set up user-generated content
      const ugcTab = screen.getByText('User Content')
      await user.click(ugcTab)
      
      await waitFor(() => {
        expect(screen.getByText('User Content Settings')).toBeInTheDocument()
      })
      
      // 4. Optimize with AI recommendations
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('AI Optimization Suggestions')).toBeInTheDocument()
      })
    })
  })
})