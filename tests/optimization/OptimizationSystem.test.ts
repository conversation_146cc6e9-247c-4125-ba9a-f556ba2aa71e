/**
 * Optimization System Tests
 * Comprehensive tests for optimization rules, impact measurement, and automated execution
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { HybridPerformanceOptimizer, OptimizerConfig, OptimizationRule } from '../../lib/optimization/HybridPerformanceOptimizer'
import { OptimizationRules } from '../../lib/optimization/OptimizationRules'
import { OptimizationScheduler, ScheduleConfig, SchedulerConfig } from '../../lib/optimization/OptimizationScheduler'
import { OptimizationTracker, TrackerConfig, MetricSnapshot } from '../../lib/optimization/OptimizationTracker'

describe('Optimization System Integration Tests', () => {
  let optimizer: HybridPerformanceOptimizer
  let scheduler: OptimizationScheduler
  let tracker: OptimizationTracker
  let mockConfig: OptimizerConfig
  let mockSchedulerConfig: SchedulerConfig
  let mockTrackerConfig: TrackerConfig

  beforeEach(() => {
    // Mock configurations
    mockConfig = {
      metricsCollectionInterval: 5000,
      maxConcurrentOptimizations: 5,
      enableRollback: true,
      validationTimeout: 30000
    }

    mockSchedulerConfig = {
      maxHistorySize: 1000,
      enableLogging: true,
      logLevel: 'info'
    }

    mockTrackerConfig = {
      maxRecords: 10000,
      exportPath: './test-exports',
      reportsPath: './test-reports',
      enableRealTimeTracking: true
    }

    // Initialize components
    optimizer = new HybridPerformanceOptimizer(mockConfig)
    tracker = new OptimizationTracker(mockTrackerConfig)
    scheduler = new OptimizationScheduler(optimizer, mockSchedulerConfig)

    // Mock console methods to reduce test noise
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('HybridPerformanceOptimizer', () => {
    test('should initialize successfully', async () => {
      await expect(optimizer.initialize()).resolves.not.toThrow()
    })

    test('should add and remove optimization rules', () => {
      const testRule: OptimizationRule = {
        id: 'test-rule-001',
        name: 'Test Cache Rule',
        description: 'Test rule for cache optimization',
        category: 'cache',
        priority: 80,
        conditions: [
          {
            metric: 'cacheHitRate',
            operator: 'lt',
            value: 80,
            timeWindow: '10m',
            aggregation: 'avg'
          }
        ],
        actions: [
          {
            type: 'cache_update',
            parameters: { action: 'increase_ttl', multiplier: 1.5 }
          }
        ],
        cooldown: 1800000,
        enabled: true,
        metadata: {
          author: 'Test',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['test', 'cache']
        }
      }

      // Add rule
      expect(() => optimizer.addRule(testRule)).not.toThrow()

      // Remove rule
      expect(optimizer.removeRule('test-rule-001')).toBe(true)
      expect(optimizer.removeRule('non-existent')).toBe(false)
    })

    test('should validate rule structure', () => {
      const invalidRule = {
        id: '',
        name: 'Invalid Rule',
        conditions: [],
        actions: []
      } as OptimizationRule

      expect(() => optimizer.addRule(invalidRule)).toThrow('Invalid rule structure')
    })

    test('should execute optimization cycle', async () => {
      // Add test rules
      const rules = OptimizationRules.getCacheOptimizationRules().slice(0, 2)
      rules.forEach(rule => optimizer.addRule(rule))

      await optimizer.initialize()
      
      const results = await optimizer.executeOptimizationCycle()
      expect(Array.isArray(results)).toBe(true)
    })

    test('should provide optimization statistics', async () => {
      await optimizer.initialize()
      
      const stats = optimizer.getOptimizationStats()
      expect(stats).toHaveProperty('totalRules')
      expect(stats).toHaveProperty('enabledRules')
      expect(stats).toHaveProperty('totalExecutions')
      expect(stats).toHaveProperty('successRate')
      expect(typeof stats.successRate).toBe('number')
    })
  })

  describe('OptimizationRules', () => {
    test('should provide all predefined rules', () => {
      const allRules = OptimizationRules.getAllRules()
      expect(Array.isArray(allRules)).toBe(true)
      expect(allRules.length).toBeGreaterThan(0)
      
      // Verify rule structure
      allRules.forEach(rule => {
        expect(rule).toHaveProperty('id')
        expect(rule).toHaveProperty('name')
        expect(rule).toHaveProperty('category')
        expect(rule).toHaveProperty('conditions')
        expect(rule).toHaveProperty('actions')
        expect(Array.isArray(rule.conditions)).toBe(true)
        expect(Array.isArray(rule.actions)).toBe(true)
      })
    })

    test('should filter rules by category', () => {
      const cacheRules = OptimizationRules.getRulesByCategory('cache')
      const imageRules = OptimizationRules.getRulesByCategory('image')
      const apiRules = OptimizationRules.getRulesByCategory('api')

      expect(Array.isArray(cacheRules)).toBe(true)
      expect(Array.isArray(imageRules)).toBe(true)
      expect(Array.isArray(apiRules)).toBe(true)

      // Verify category filtering
      cacheRules.forEach(rule => expect(rule.category).toBe('cache'))
      imageRules.forEach(rule => expect(rule.category).toBe('image'))
      apiRules.forEach(rule => expect(rule.category).toBe('api'))
    })

    test('should filter rules by priority', () => {
      const highPriorityRules = OptimizationRules.getRulesByPriority(80, 100)
      const mediumPriorityRules = OptimizationRules.getRulesByPriority(50, 79)

      expect(Array.isArray(highPriorityRules)).toBe(true)
      expect(Array.isArray(mediumPriorityRules)).toBe(true)

      // Verify priority filtering
      highPriorityRules.forEach(rule => {
        expect(rule.priority).toBeGreaterThanOrEqual(80)
        expect(rule.priority).toBeLessThanOrEqual(100)
      })
      mediumPriorityRules.forEach(rule => {
        expect(rule.priority).toBeGreaterThanOrEqual(50)
        expect(rule.priority).toBeLessThanOrEqual(79)
      })
    })

    test('should get only enabled rules', () => {
      const enabledRules = OptimizationRules.getEnabledRules()
      expect(Array.isArray(enabledRules)).toBe(true)
      enabledRules.forEach(rule => expect(rule.enabled).toBe(true))
    })

    test('should create custom rule template', () => {
      const customRule = OptimizationRules.createCustomRule(
        'custom-001',
        'Custom Test Rule',
        'Custom rule for testing',
        'cache',
        75
      )

      expect(customRule.id).toBe('custom-001')
      expect(customRule.name).toBe('Custom Test Rule')
      expect(customRule.category).toBe('cache')
      expect(customRule.priority).toBe(75)
      expect(customRule.enabled).toBe(false) // Custom rules disabled by default
      expect(Array.isArray(customRule.conditions)).toBe(true)
      expect(Array.isArray(customRule.actions)).toBe(true)
    })
  })

  describe('OptimizationScheduler', () => {
    test('should start and stop successfully', async () => {
      await expect(scheduler.start()).resolves.not.toThrow()
      await expect(scheduler.stop()).resolves.not.toThrow()
    })

    test('should add and remove schedules', () => {
      const testSchedule: ScheduleConfig = {
        id: 'test-schedule-001',
        name: 'Test Schedule',
        description: 'Test optimization schedule',
        interval: 60000, // 1 minute
        enabled: true,
        ruleCategories: ['cache'],
        metadata: {
          author: 'Test',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['test']
        }
      }

      // Add schedule
      expect(() => scheduler.addSchedule(testSchedule)).not.toThrow()

      // Remove schedule
      expect(scheduler.removeSchedule('test-schedule-001')).toBe(true)
      expect(scheduler.removeSchedule('non-existent')).toBe(false)
    })

    test('should toggle schedule enabled state', () => {
      const testSchedule: ScheduleConfig = {
        id: 'test-schedule-002',
        name: 'Test Schedule 2',
        description: 'Another test schedule',
        interval: 120000, // 2 minutes
        enabled: true,
        metadata: {
          author: 'Test',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['test']
        }
      }

      scheduler.addSchedule(testSchedule)

      // Disable schedule
      expect(scheduler.toggleSchedule('test-schedule-002', false)).toBe(true)
      
      // Enable schedule
      expect(scheduler.toggleSchedule('test-schedule-002', true)).toBe(true)
      
      // Non-existent schedule
      expect(scheduler.toggleSchedule('non-existent', true)).toBe(false)
    })

    test('should provide schedule statistics', () => {
      const stats = scheduler.getScheduleStats()
      expect(stats).toHaveProperty('totalSchedules')
      expect(stats).toHaveProperty('enabledSchedules')
      expect(stats).toHaveProperty('activeSchedules')
      expect(stats).toHaveProperty('successRate')
      expect(typeof stats.successRate).toBe('number')
    })

    test('should execute schedule manually', async () => {
      const testSchedule: ScheduleConfig = {
        id: 'test-schedule-003',
        name: 'Manual Test Schedule',
        description: 'Schedule for manual execution test',
        interval: 300000, // 5 minutes
        enabled: true,
        ruleCategories: ['cache'],
        metadata: {
          author: 'Test',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['test', 'manual']
        }
      }

      scheduler.addSchedule(testSchedule)
      
      const execution = await scheduler.executeScheduleManually('test-schedule-003')
      expect(execution).toHaveProperty('scheduleId', 'test-schedule-003')
      expect(execution).toHaveProperty('startTime')
      expect(execution).toHaveProperty('status')
      expect(['running', 'completed', 'failed', 'skipped']).toContain(execution.status)
    })

    test('should validate schedule configuration', () => {
      const invalidSchedule = {
        id: '',
        name: 'Invalid Schedule',
        interval: 30000, // Less than minimum
        enabled: true,
        metadata: {
          author: 'Test',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['test']
        }
      } as ScheduleConfig

      expect(() => scheduler.addSchedule(invalidSchedule)).toThrow()
    })
  })

  describe('OptimizationTracker', () => {
    test('should record optimization impact', async () => {
      const beforeMetrics: MetricSnapshot = {
        timestamp: new Date(),
        responseTime: 1000,
        errorRate: 5,
        cacheHitRate: 70,
        throughput: 100,
        cpuUsage: 50,
        memoryUsage: 60,
        bandwidthUsage: 80,
        cost: 100,
        customMetrics: {}
      }

      const afterMetrics: MetricSnapshot = {
        timestamp: new Date(),
        responseTime: 800,
        errorRate: 3,
        cacheHitRate: 85,
        throughput: 120,
        cpuUsage: 45,
        memoryUsage: 55,
        bandwidthUsage: 70,
        cost: 95,
        customMetrics: {}
      }

      const mockResult = {
        ruleId: 'test-rule-001',
        success: true,
        executionTime: 1500,
        impact: {
          performanceChange: 20,
          costChange: 5,
          reliabilityChange: 40,
          metrics: {}
        }
      }

      const recordId = await tracker.recordImpact(
        mockResult,
        'Test Cache Rule',
        'cache',
        beforeMetrics,
        afterMetrics
      )

      expect(typeof recordId).toBe('string')
      expect(recordId.length).toBeGreaterThan(0)
    })

    test('should take metric snapshots', async () => {
      const snapshot = await tracker.takeMetricSnapshot('test-snapshot')
      
      expect(snapshot).toHaveProperty('timestamp')
      expect(snapshot).toHaveProperty('responseTime')
      expect(snapshot).toHaveProperty('errorRate')
      expect(snapshot).toHaveProperty('cacheHitRate')
      expect(snapshot).toHaveProperty('customMetrics')
      expect(typeof snapshot.responseTime).toBe('number')
      expect(typeof snapshot.errorRate).toBe('number')
    })

    test('should generate impact summary', () => {
      const startDate = new Date(Date.now() - 86400000) // 24 hours ago
      const endDate = new Date()

      const summary = tracker.generateImpactSummary(startDate, endDate)
      
      expect(summary).toHaveProperty('timeRange')
      expect(summary).toHaveProperty('totalOptimizations')
      expect(summary).toHaveProperty('successRate')
      expect(summary).toHaveProperty('averageImpact')
      expect(summary).toHaveProperty('categoryBreakdown')
      expect(summary).toHaveProperty('topPerformingRules')
      expect(summary).toHaveProperty('trends')
      expect(summary).toHaveProperty('recommendations')
      
      expect(Array.isArray(summary.categoryBreakdown)).toBe(true)
      expect(Array.isArray(summary.topPerformingRules)).toBe(true)
      expect(Array.isArray(summary.recommendations)).toBe(true)
    })

    test('should generate comprehensive impact report', async () => {
      const startDate = new Date(Date.now() - 86400000) // 24 hours ago
      const endDate = new Date()

      const report = await tracker.generateImpactReport(startDate, endDate)
      
      expect(report).toHaveProperty('id')
      expect(report).toHaveProperty('title')
      expect(report).toHaveProperty('generatedAt')
      expect(report).toHaveProperty('summary')
      expect(report).toHaveProperty('detailedAnalysis')
      expect(report).toHaveProperty('visualizations')
      expect(report).toHaveProperty('recommendations')
      expect(report).toHaveProperty('appendix')
      
      expect(Array.isArray(report.visualizations)).toBe(true)
      expect(Array.isArray(report.recommendations)).toBe(true)
    })

    test('should provide optimization statistics', () => {
      const stats = tracker.getOptimizationStats()
      
      expect(stats).toHaveProperty('totalOptimizations')
      expect(stats).toHaveProperty('successfulOptimizations')
      expect(stats).toHaveProperty('successRate')
      expect(stats).toHaveProperty('averagePerformanceImpact')
      expect(stats).toHaveProperty('categoryStats')
      expect(stats).toHaveProperty('ruleStats')
      
      expect(typeof stats.totalOptimizations).toBe('number')
      expect(typeof stats.successRate).toBe('number')
      expect(typeof stats.categoryStats).toBe('object')
      expect(typeof stats.ruleStats).toBe('object')
    })
  })

  describe('Integration Scenarios', () => {
    test('should handle complete optimization workflow', async () => {
      // Initialize optimizer
      await optimizer.initialize()
      
      // Add optimization rules
      const rules = OptimizationRules.getCacheOptimizationRules().slice(0, 2)
      rules.forEach(rule => optimizer.addRule(rule))
      
      // Take before snapshot
      const beforeSnapshot = await tracker.takeMetricSnapshot('before-optimization')
      
      // Execute optimization cycle
      const results = await optimizer.executeOptimizationCycle()
      
      // Take after snapshot
      const afterSnapshot = await tracker.takeMetricSnapshot('after-optimization')
      
      // Record impacts
      for (const result of results) {
        await tracker.recordImpact(
          result,
          `Rule ${result.ruleId}`,
          'cache',
          beforeSnapshot,
          afterSnapshot
        )
      }
      
      // Generate summary
      const summary = tracker.generateImpactSummary(
        new Date(Date.now() - 3600000), // 1 hour ago
        new Date()
      )
      
      expect(Array.isArray(results)).toBe(true)
      expect(summary.totalOptimizations).toBeGreaterThanOrEqual(0)
    })

    test('should handle scheduled optimization execution', async () => {
      // Create test schedule
      const testSchedule: ScheduleConfig = {
        id: 'integration-test-schedule',
        name: 'Integration Test Schedule',
        description: 'Schedule for integration testing',
        interval: 60000,
        enabled: true,
        ruleCategories: ['cache'],
        metadata: {
          author: 'Test',
          version: '1.0',
          lastModified: '2025-07-27',
          tags: ['integration', 'test']
        }
      }
      
      // Add schedule
      scheduler.addSchedule(testSchedule)
      
      // Execute manually
      const execution = await scheduler.executeScheduleManually('integration-test-schedule')
      
      expect(execution.scheduleId).toBe('integration-test-schedule')
      expect(['running', 'completed', 'failed', 'skipped']).toContain(execution.status)
    })

    test('should validate optimization rule conditions', () => {
      const rules = OptimizationRules.getAllRules()
      
      rules.forEach(rule => {
        // Validate rule structure
        expect(rule.id).toBeTruthy()
        expect(rule.name).toBeTruthy()
        expect(rule.category).toBeTruthy()
        expect(rule.priority).toBeGreaterThanOrEqual(0)
        expect(rule.priority).toBeLessThanOrEqual(100)
        expect(rule.cooldown).toBeGreaterThanOrEqual(0)
        
        // Validate conditions
        rule.conditions.forEach(condition => {
          expect(condition.metric).toBeTruthy()
          expect(['gt', 'lt', 'eq', 'gte', 'lte', 'contains']).toContain(condition.operator)
          expect(['avg', 'max', 'min', 'sum', 'count']).toContain(condition.aggregation)
          expect(condition.timeWindow).toBeTruthy()
        })
        
        // Validate actions
        rule.actions.forEach(action => {
          expect(action.type).toBeTruthy()
          expect(typeof action.parameters).toBe('object')
        })
        
        // Validate metadata
        expect(rule.metadata.author).toBeTruthy()
        expect(rule.metadata.version).toBeTruthy()
        expect(rule.metadata.lastModified).toBeTruthy()
        expect(Array.isArray(rule.metadata.tags)).toBe(true)
      })
    })

    test('should measure performance improvements', async () => {
      // Simulate performance improvement scenario
      const beforeMetrics: MetricSnapshot = {
        timestamp: new Date(),
        responseTime: 1500, // Slow response
        errorRate: 8, // High error rate
        cacheHitRate: 60, // Low cache hit rate
        throughput: 80,
        cpuUsage: 70,
        memoryUsage: 80,
        bandwidthUsage: 90,
        cost: 120,
        customMetrics: {}
      }

      const afterMetrics: MetricSnapshot = {
        timestamp: new Date(),
        responseTime: 800, // Improved response
        errorRate: 2, // Lower error rate
        cacheHitRate: 90, // Better cache hit rate
        throughput: 150,
        cpuUsage: 50,
        memoryUsage: 60,
        bandwidthUsage: 70,
        cost: 100,
        customMetrics: {}
      }

      const mockResult = {
        ruleId: 'performance-test-rule',
        success: true,
        executionTime: 2000,
        impact: {
          performanceChange: 46.7, // (1500-800)/1500 * 100
          costChange: 16.7, // (120-100)/120 * 100
          reliabilityChange: 75, // (8-2)/8 * 100
          metrics: {}
        }
      }

      await tracker.recordImpact(
        mockResult,
        'Performance Test Rule',
        'cache',
        beforeMetrics,
        afterMetrics
      )

      const stats = tracker.getOptimizationStats()
      expect(stats.totalOptimizations).toBeGreaterThan(0)
    })

    test('should validate system performance under load', async () => {
      // Simulate multiple optimization cycles
      await optimizer.initialize()

      const rules = OptimizationRules.getAllRules().slice(0, 5)
      rules.forEach(rule => optimizer.addRule(rule))

      const startTime = Date.now()
      const cycles = 3

      for (let i = 0; i < cycles; i++) {
        const results = await optimizer.executeOptimizationCycle()
        expect(Array.isArray(results)).toBe(true)
      }

      const endTime = Date.now()
      const totalTime = endTime - startTime

      // Should complete within reasonable time (10 seconds for 3 cycles)
      expect(totalTime).toBeLessThan(10000)

      const stats = optimizer.getOptimizationStats()
      expect(stats.totalExecutions).toBeGreaterThanOrEqual(0)
    })
  })
})
