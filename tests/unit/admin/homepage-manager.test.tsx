/**
 * Homepage Manager Test Suite
 * Comprehensive testing for the admin homepage manager functionality
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import HomepageManagerPage from '../../../app/admin/homepage/page'

// Mock dependencies
jest.mock('@/lib/firestore', () => ({
  getProducts: jest.fn(() => Promise.resolve([
    {
      id: 'prod_1',
      name: 'Test Keycap',
      price: 25.99,
      image: '/test-image.jpg',
      category: 'artisan'
    }
  ]))
}))

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>
}))

describe('Homepage Manager', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Component Rendering', () => {
    test('renders homepage manager with all tab options', async () => {
      render(<HomepageManagerPage />)
      
      // Check main title
      expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
      
      // Check all tab buttons
      await waitFor(() => {
        expect(screen.getByText('Hero Slider')).toBeInTheDocument()
        expect(screen.getByText('Community Trending')).toBeInTheDocument()
        expect(screen.getByText('Raffle Countdowns')).toBeInTheDocument()
        expect(screen.getByText('AI Recommendations')).toBeInTheDocument()
        expect(screen.getByText('User Content')).toBeInTheDocument()
        expect(screen.getByText('Analytics')).toBeInTheDocument()
        expect(screen.getByText('A/B Testing')).toBeInTheDocument()
        expect(screen.getByText('Smart Scheduling')).toBeInTheDocument()
        expect(screen.getByText('Featured Products')).toBeInTheDocument()
        expect(screen.getByText('Announcements')).toBeInTheDocument()
      })
    })

    test('renders preview mode toggle', async () => {
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Preview Mode')).toBeInTheDocument()
      })
    })

    test('renders save changes button', async () => {
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Save Changes')).toBeInTheDocument()
      })
    })
  })

  describe('Tab Navigation', () => {
    test('switches between tabs correctly', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // Default tab should be Hero Slider
      await waitFor(() => {
        expect(screen.getByText('Hero Slider Settings')).toBeInTheDocument()
      })
      
      // Click on Community Trending tab
      const communityTab = screen.getByText('Community Trending')
      await user.click(communityTab)
      
      await waitFor(() => {
        expect(screen.getByText('Community Trending Settings')).toBeInTheDocument()
      })
      
      // Click on Analytics tab
      const analyticsTab = screen.getByText('Analytics')
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('Analytics Settings')).toBeInTheDocument()
      })
    })

    test('maintains active tab styling', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        const heroTab = screen.getByText('Hero Slider')
        expect(heroTab.closest('button')).toHaveClass('bg-purple-600')
      })
      
      // Switch to another tab
      const communityTab = screen.getByText('Community Trending')
      await user.click(communityTab)
      
      await waitFor(() => {
        expect(communityTab.closest('button')).toHaveClass('bg-purple-600')
      })
    })
  })

  describe('Hero Slider Functionality', () => {
    test('allows adding new hero slides', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        const addSlideButton = screen.getByText('Add Slide')
        expect(addSlideButton).toBeInTheDocument()
      })
      
      const addSlideButton = screen.getByText('Add Slide')
      await user.click(addSlideButton)
      
      // Check if a new slide form appears or slide is added
      await waitFor(() => {
        expect(screen.getAllByText(/Slide \d+/)).toHaveLength(4) // 3 default + 1 new
      })
    })

    test('allows editing slide settings', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Auto-play slides/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Show navigation dots/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Show navigation arrows/)).toBeInTheDocument()
      })
      
      const autoPlayToggle = screen.getByLabelText(/Auto-play slides/)
      await user.click(autoPlayToggle)
      
      // Verify the setting was toggled
      expect(autoPlayToggle).toBeChecked()
    })

    test('validates slide interval input', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        const intervalInput = screen.getByLabelText(/Slide Interval/)
        expect(intervalInput).toBeInTheDocument()
      })
      
      const intervalInput = screen.getByLabelText(/Slide Interval/)
      await user.clear(intervalInput)
      await user.type(intervalInput, '2')
      
      expect(intervalInput).toHaveValue(2)
    })
  })

  describe('Preview Mode', () => {
    test('toggles preview mode correctly', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        const previewToggle = screen.getByLabelText(/Preview Mode/)
        expect(previewToggle).toBeInTheDocument()
      })
      
      const previewToggle = screen.getByLabelText(/Preview Mode/)
      await user.click(previewToggle)
      
      await waitFor(() => {
        expect(screen.getByText('Homepage Preview')).toBeInTheDocument()
      })
    })

    test('shows preview content when enabled', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      const previewToggle = screen.getByLabelText(/Preview Mode/)
      await user.click(previewToggle)
      
      await waitFor(() => {
        // Check for preview elements
        expect(screen.getByText('New Artisan Collection')).toBeInTheDocument()
        expect(screen.getByText('Summer Sale Banner')).toBeInTheDocument()
      })
    })
  })

  describe('Analytics Features', () => {
    test('displays analytics metrics', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // Navigate to analytics tab
      const analyticsTab = screen.getByText('Analytics')
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('AI Performance Insights')).toBeInTheDocument()
        expect(screen.getByText('Performance Heatmap')).toBeInTheDocument()
        expect(screen.getByText('Real-time Metrics')).toBeInTheDocument()
      })
    })

    test('shows AI optimization suggestions', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      const analyticsTab = screen.getByText('Analytics')
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('AI Optimization Suggestions')).toBeInTheDocument()
        expect(screen.getByText('Hero slider CTR increased 23% with 5-second intervals')).toBeInTheDocument()
      })
    })
  })

  describe('A/B Testing', () => {
    test('displays A/B testing interface', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      const abTestingTab = screen.getByText('A/B Testing')
      await user.click(abTestingTab)
      
      await waitFor(() => {
        expect(screen.getByText('A/B Testing Settings')).toBeInTheDocument()
        expect(screen.getByText('Active A/B Tests')).toBeInTheDocument()
      })
    })

    test('shows test variants and metrics', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      const abTestingTab = screen.getByText('A/B Testing')
      await user.click(abTestingTab)
      
      await waitFor(() => {
        expect(screen.getByText('Hero Banner A/B Test')).toBeInTheDocument()
        expect(screen.getByText('Community Section Layout Test')).toBeInTheDocument()
      })
    })
  })

  describe('Smart Scheduling', () => {
    test('displays scheduling interface', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      const schedulingTab = screen.getByText('Smart Scheduling')
      await user.click(schedulingTab)
      
      await waitFor(() => {
        expect(screen.getByText('Smart Scheduling Settings')).toBeInTheDocument()
        expect(screen.getByText('Content Performance Optimization')).toBeInTheDocument()
      })
    })

    test('shows optimization insights', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      const schedulingTab = screen.getByText('Smart Scheduling')
      await user.click(schedulingTab)
      
      await waitFor(() => {
        expect(screen.getByText('Active Optimization')).toBeInTheDocument()
        expect(screen.getByText('Performance Boost')).toBeInTheDocument()
      })
    })
  })

  describe('Data Persistence', () => {
    test('saves changes when save button is clicked', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      // Make a change
      const autoPlayToggle = screen.getByLabelText(/Auto-play slides/)
      await user.click(autoPlayToggle)
      
      // Click save
      const saveButton = screen.getByText('Save Changes')
      await user.click(saveButton)
      
      // Verify save action (in a real app, this would check API calls)
      expect(saveButton).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    test('handles missing product data gracefully', async () => {
      // Mock failed product fetch
      jest.mocked(require('@/lib/firestore').getProducts).mockRejectedValueOnce(new Error('API Error'))
      
      render(<HomepageManagerPage />)
      
      // Component should still render without crashing
      await waitFor(() => {
        expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
      })
    })

    test('displays loading states appropriately', async () => {
      render(<HomepageManagerPage />)
      
      // Check for any loading indicators
      await waitFor(() => {
        expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
      })
    })
  })

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', async () => {
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        // Check for proper labeling
        expect(screen.getByLabelText(/Preview Mode/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Auto-play slides/)).toBeInTheDocument()
      })
    })

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      render(<HomepageManagerPage />)
      
      await waitFor(() => {
        const firstTab = screen.getByText('Hero Slider')
        expect(firstTab).toBeInTheDocument()
      })
      
      // Test tab navigation with keyboard
      const firstTab = screen.getByText('Hero Slider')
      firstTab.focus()
      
      await user.keyboard('{Tab}')
      
      // Next focusable element should receive focus
      expect(document.activeElement).not.toBe(firstTab)
    })
  })

  describe('Responsive Design', () => {
    test('renders properly on mobile viewports', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<HomepageManagerPage />)
      
      expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
    })

    test('adapts layout for tablet viewports', () => {
      // Mock tablet viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      })
      
      render(<HomepageManagerPage />)
      
      expect(screen.getByText('Homepage Manager')).toBeInTheDocument()
    })
  })
})