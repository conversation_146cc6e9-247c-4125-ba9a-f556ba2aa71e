# Syndicaps Workers Deployment Makefile
# Simplified commands for development and deployment

.PHONY: help install clean build test deploy health-check logs status

# Default target
help: ## Show this help message
	@echo "Syndicaps Workers Deployment Commands"
	@echo "====================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development commands
install: ## Install dependencies
	npm ci

clean: ## Clean build artifacts
	npm run clean

build: ## Build all workers
	npm run build

build-watch: ## Build workers in watch mode
	npm run build:watch

dev: ## Start development servers for both workers
	npm run dev:both

dev-image: ## Start development server for image optimizer
	npm run dev:image-optimizer

dev-api: ## Start development server for API cache
	npm run dev:api-cache

# Testing commands
test: ## Run all tests
	npm run test

test-watch: ## Run tests in watch mode
	npm run test:watch

test-coverage: ## Run tests with coverage
	npm run test:coverage

test-performance: ## Run performance tests
	npm run test:performance

test-integration: ## Run integration tests
	npm run test:integration

test-comprehensive: ## Run comprehensive test suite
	npm run test:comprehensive

# Quality assurance
lint: ## Run linter
	npm run lint

lint-fix: ## Run linter with auto-fix
	npm run lint:fix

type-check: ## Run TypeScript type checking
	npm run type-check

validate: ## Run all validation checks
	npm run validate

# Deployment commands
deploy-staging: ## Deploy to staging environment
	@echo "🚀 Deploying to staging..."
	npm run cd:staging

deploy-production: ## Deploy to production environment
	@echo "🚀 Deploying to production..."
	npm run cd:production

deploy-staging-only: ## Deploy to staging without full CI
	npm run deploy:staging

deploy-production-only: ## Deploy to production without full CI
	npm run deploy:production

deploy-image-staging: ## Deploy only image optimizer to staging
	npm run deploy:staging:image-optimizer

deploy-image-production: ## Deploy only image optimizer to production
	npm run deploy:production:image-optimizer

deploy-api-staging: ## Deploy only API cache to staging
	npm run deploy:staging:api-cache

deploy-api-production: ## Deploy only API cache to production
	npm run deploy:production:api-cache

# Rollback commands
rollback: ## Rollback production deployment
	@echo "🔄 Rolling back production deployment..."
	npm run deploy:rollback

rollback-image: ## Rollback image optimizer
	npm run deploy:rollback:image-optimizer

rollback-api: ## Rollback API cache
	npm run deploy:rollback:api-cache

# Monitoring commands
health-check: ## Check health of production workers
	npm run health-check:production

health-check-staging: ## Check health of staging workers
	npm run health-check:staging

logs: ## View production logs
	npm run logs

logs-staging: ## View staging logs
	npm run logs:staging

logs-image: ## View image optimizer logs
	npm run monitor:image-optimizer

logs-api: ## View API cache logs
	npm run monitor:api-cache

status: ## Check deployment status
	npm run status

status-staging: ## Check staging deployment status
	npm run status:staging

# Utility commands
setup: install build ## Initial setup for development

ci: ## Run CI pipeline locally
	npm run ci

quick-deploy-staging: clean build deploy-staging-only health-check-staging ## Quick staging deployment

quick-deploy-production: clean build deploy-production-only health-check ## Quick production deployment

full-deploy-staging: validate build deploy-staging health-check-staging ## Full staging deployment with validation

full-deploy-production: validate build deploy-production health-check ## Full production deployment with validation

# Emergency commands
emergency-rollback: ## Emergency rollback with immediate health check
	@echo "🚨 EMERGENCY ROLLBACK INITIATED"
	npm run deploy:rollback
	npm run health-check:production

emergency-status: ## Check emergency status of all systems
	@echo "🚨 EMERGENCY STATUS CHECK"
	npm run health-check:production
	npm run status

# Development workflow shortcuts
dev-cycle: clean build test ## Complete development cycle

staging-cycle: dev-cycle deploy-staging health-check-staging ## Complete staging deployment cycle

production-cycle: dev-cycle deploy-production health-check ## Complete production deployment cycle

# Maintenance commands
update-deps: ## Update dependencies
	npm update
	npm audit fix

security-audit: ## Run security audit
	npm audit
	npm audit fix

# Docker commands (if using containerized deployment)
docker-build: ## Build Docker images for workers
	docker build -t syndicaps-workers .

docker-run: ## Run workers in Docker
	docker run -p 8787:8787 -p 8788:8788 syndicaps-workers

# Documentation
docs: ## Generate documentation
	@echo "📚 Generating documentation..."
	@echo "Workers documentation available at:"
	@echo "  - Image Optimizer: docs/image-optimization-worker.md"
	@echo "  - API Cache: docs/api-cache-worker.md"
	@echo "  - Deployment: docs/workers-deployment.md"

# Environment setup
env-staging: ## Set up staging environment variables
	@echo "Setting up staging environment..."
	@echo "Please ensure the following environment variables are set:"
	@echo "  - CLOUDFLARE_API_TOKEN"
	@echo "  - CLOUDFLARE_ACCOUNT_ID"

env-production: ## Set up production environment variables
	@echo "Setting up production environment..."
	@echo "Please ensure the following environment variables are set:"
	@echo "  - CLOUDFLARE_API_TOKEN"
	@echo "  - CLOUDFLARE_ACCOUNT_ID"
	@echo "  - DEPLOYMENT_WEBHOOK_URL"

# Performance monitoring
perf-test: ## Run performance tests
	npm run test:performance

perf-monitor: ## Monitor performance in real-time
	@echo "🔍 Monitoring worker performance..."
	npm run monitor

# Aliases for common commands
s: deploy-staging ## Alias for deploy-staging
p: deploy-production ## Alias for deploy-production
t: test ## Alias for test
b: build ## Alias for build
h: health-check ## Alias for health-check
l: logs ## Alias for logs
