/**
 * Comprehensive Test Suite for API Cache Worker
 * Tests caching behavior, rate limiting, analytics, and performance
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import worker from '../api-cache'

// Mock environment
const mockEnv = {
  API_CACHE_KV: {
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  API_METADATA_KV: {
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  RATE_LIMIT_KV: {
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  FIREBASE_FUNCTIONS_URL: 'https://us-central1-syndicaps.cloudfunctions.net',
  FIREBASE_PROJECT_ID: 'syndicaps',
  FIREBASE_REGION: 'us-central1',
  ENVIRONMENT: 'test',
  RATE_LIMIT_ENABLED: 'true'
}

// Mock execution context
const mockCtx = {
  waitUntil: vi.fn(),
  passThroughOnException: vi.fn()
}

// Mock fetch
global.fetch = vi.fn()

describe('API Cache Worker', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default mock responses
    mockEnv.API_CACHE_KV.get.mockResolvedValue(null)
    mockEnv.API_METADATA_KV.get.mockResolvedValue(null)
    mockEnv.RATE_LIMIT_KV.get.mockResolvedValue(null)
    
    // Mock successful Firebase response
    global.fetch.mockResolvedValue(new Response(
      JSON.stringify({ data: 'test response' }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    ))
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Health Check', () => {
    it('should return health check response', async () => {
      const request = new Request('https://api-cache.syndicaps.com/health')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(await response.text()).toBe('API Cache Worker OK')
    })
  })

  describe('CORS Handling', () => {
    it('should handle OPTIONS preflight requests', async () => {
      const request = new Request('https://api-cache.syndicaps.com/api/products', {
        method: 'OPTIONS'
      })
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(204)
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('GET')
    })

    it('should add CORS headers to responses', async () => {
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
    })
  })

  describe('Cache Behavior', () => {
    it('should cache GET requests', async () => {
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      
      // First request - cache miss
      const response1 = await worker.fetch(request, mockEnv, mockCtx)
      expect(response1.status).toBe(200)
      expect(mockEnv.API_CACHE_KV.put).toHaveBeenCalled()
      
      // Verify cache storage was called
      expect(mockCtx.waitUntil).toHaveBeenCalled()
    })

    it('should serve from cache on cache hit', async () => {
      const cachedData = new ArrayBuffer(8)
      const metadata = {
        timestamp: Date.now() - 60000, // 1 minute ago
        ttl: 300, // 5 minutes
        tags: ['products'],
        hitCount: 0,
        contentType: 'application/json',
        etag: '"test-etag"',
        size: 8
      }

      mockEnv.API_CACHE_KV.get.mockResolvedValue(cachedData)
      mockEnv.API_METADATA_KV.get.mockResolvedValue(JSON.stringify(metadata))

      const request = new Request('https://api-cache.syndicaps.com/api/products')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('X-Cache-Status')).toBe('HIT')
      expect(response.headers.get('ETag')).toBe('"test-etag"')
    })

    it('should serve stale content and refresh in background', async () => {
      const cachedData = new ArrayBuffer(8)
      const metadata = {
        timestamp: Date.now() - 400000, // 6.67 minutes ago (stale)
        ttl: 300, // 5 minutes
        tags: ['products'],
        hitCount: 5,
        contentType: 'application/json',
        etag: '"stale-etag"',
        size: 8
      }

      mockEnv.API_CACHE_KV.get.mockResolvedValue(cachedData)
      mockEnv.API_METADATA_KV.get.mockResolvedValue(JSON.stringify(metadata))

      const request = new Request('https://api-cache.syndicaps.com/api/products')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('X-Cache-Status')).toBe('STALE')
      expect(mockCtx.waitUntil).toHaveBeenCalled() // Background refresh
    })

    it('should bypass cache for non-GET requests', async () => {
      const request = new Request('https://api-cache.syndicaps.com/api/products', {
        method: 'POST',
        body: JSON.stringify({ name: 'test' })
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
      expect(global.fetch).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST'
        })
      )
    })

    it('should bypass cache for auth endpoints', async () => {
      const request = new Request('https://api-cache.syndicaps.com/api/auth/login')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(global.fetch).toHaveBeenCalled()
      expect(mockEnv.API_CACHE_KV.put).not.toHaveBeenCalled()
    })
  })

  describe('Rate Limiting', () => {
    it('should allow requests within rate limit', async () => {
      mockEnv.RATE_LIMIT_KV.get.mockResolvedValue(JSON.stringify({ count: 50, window: Date.now() }))
      
      const request = new Request('https://api-cache.syndicaps.com/api/products', {
        headers: { 'CF-Connecting-IP': '***********' }
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
    })

    it('should block requests exceeding rate limit', async () => {
      mockEnv.RATE_LIMIT_KV.get.mockResolvedValue(JSON.stringify({ count: 150, window: Date.now() }))
      
      const request = new Request('https://api-cache.syndicaps.com/api/products', {
        headers: { 'CF-Connecting-IP': '***********' }
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(429)
      expect(response.headers.get('Retry-After')).toBeDefined()
    })

    it('should have different limits for authenticated users', async () => {
      const request = new Request('https://api-cache.syndicaps.com/api/products', {
        headers: { 
          'Authorization': 'Bearer test-token',
          'CF-Connecting-IP': '***********'
        }
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
    })
  })

  describe('Cache Administration', () => {
    const adminHeaders = {
      'Authorization': 'Bearer admin-token',
      'Content-Type': 'application/json'
    }

    it('should require authentication for admin endpoints', async () => {
      const request = new Request('https://api-cache.syndicaps.com/cache-admin/stats')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(401)
    })

    it('should return cache statistics', async () => {
      mockEnv.API_CACHE_KV.list.mockResolvedValue({ keys: [{ name: 'cache:test1' }, { name: 'cache:test2' }] })
      mockEnv.API_METADATA_KV.list.mockResolvedValue({ keys: [{ name: 'cache:test1:meta' }] })
      
      const request = new Request('https://api-cache.syndicaps.com/cache-admin/stats', {
        headers: adminHeaders
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
      
      const stats = await response.json()
      expect(stats).toHaveProperty('totalEntries')
    })

    it('should invalidate cache by tag', async () => {
      const request = new Request('https://api-cache.syndicaps.com/cache-admin/invalidate/tag', {
        method: 'POST',
        headers: adminHeaders,
        body: JSON.stringify({ tags: ['products'], backgroundRefresh: true })
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
      
      const result = await response.json()
      expect(result).toHaveProperty('success')
    })

    it('should warm cache endpoints', async () => {
      const request = new Request('https://api-cache.syndicaps.com/cache-admin/warm', {
        method: 'POST',
        headers: adminHeaders,
        body: JSON.stringify({ endpoints: ['/api/products', '/api/categories'] })
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
      
      const result = await response.json()
      expect(result.warmed).toBe(2)
    })
  })

  describe('Analytics', () => {
    it('should record analytics for all requests', async () => {
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      await worker.fetch(request, mockEnv, mockCtx)
      
      // Verify analytics recording was called
      expect(mockEnv.API_METADATA_KV.put).toHaveBeenCalledWith(
        expect.stringMatching(/analytics:/),
        expect.any(String),
        expect.any(Object)
      )
    })

    it('should provide analytics dashboard data', async () => {
      const request = new Request('https://api-cache.syndicaps.com/cache-admin/analytics/dashboard', {
        headers: { 'Authorization': 'Bearer admin-token' }
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
      
      const dashboard = await response.json()
      expect(dashboard).toHaveProperty('current')
      expect(dashboard).toHaveProperty('hourly')
    })

describe('Integration Tests', () => {
  describe('End-to-End Cache Flow', () => {
    it('should complete full cache lifecycle', async () => {
      // 1. First request - cache miss
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      const response1 = await worker.fetch(request, mockEnv, mockCtx)

      expect(response1.status).toBe(200)
      expect(mockEnv.API_CACHE_KV.put).toHaveBeenCalled()

      // 2. Second request - cache hit
      const cachedData = new ArrayBuffer(8)
      const metadata = {
        timestamp: Date.now() - 60000,
        ttl: 300,
        tags: ['products'],
        hitCount: 0,
        contentType: 'application/json',
        etag: '"test-etag"',
        size: 8
      }

      mockEnv.API_CACHE_KV.get.mockResolvedValue(cachedData)
      mockEnv.API_METADATA_KV.get.mockResolvedValue(JSON.stringify(metadata))

      const response2 = await worker.fetch(request, mockEnv, mockCtx)
      expect(response2.headers.get('X-Cache-Status')).toBe('HIT')

      // 3. Cache invalidation
      const invalidateRequest = new Request('https://api-cache.syndicaps.com/cache-admin/invalidate/tag', {
        method: 'POST',
        headers: { 'Authorization': 'Bearer admin-token', 'Content-Type': 'application/json' },
        body: JSON.stringify({ tags: ['products'] })
      })

      const invalidateResponse = await worker.fetch(invalidateRequest, mockEnv, mockCtx)
      expect(invalidateResponse.status).toBe(200)

      // 4. Next request should be cache miss again
      mockEnv.API_CACHE_KV.get.mockResolvedValue(null)
      mockEnv.API_METADATA_KV.get.mockResolvedValue(null)

      const response3 = await worker.fetch(request, mockEnv, mockCtx)
      expect(response3.status).toBe(200)
    })

    it('should handle rate limiting with cache', async () => {
      // Setup rate limit near threshold
      mockEnv.RATE_LIMIT_KV.get.mockResolvedValue(JSON.stringify({ count: 99, window: Date.now() }))

      const request = new Request('https://api-cache.syndicaps.com/api/products', {
        headers: { 'CF-Connecting-IP': '***********' }
      })

      // First request should succeed
      const response1 = await worker.fetch(request, mockEnv, mockCtx)
      expect(response1.status).toBe(200)

      // Setup rate limit exceeded
      mockEnv.RATE_LIMIT_KV.get.mockResolvedValue(JSON.stringify({ count: 101, window: Date.now() }))

      // Second request should be rate limited
      const response2 = await worker.fetch(request, mockEnv, mockCtx)
      expect(response2.status).toBe(429)
    })
  })

  describe('Performance Benchmarks', () => {
    it('should meet performance targets', async () => {
      const performanceTests = [
        { name: 'Cache Hit', setup: () => setupCacheHit() },
        { name: 'Cache Miss', setup: () => setupCacheMiss() },
        { name: 'Rate Limited', setup: () => setupRateLimit() }
      ]

      for (const test of performanceTests) {
        test.setup()

        const start = performance.now()
        const request = new Request('https://api-cache.syndicaps.com/api/products')
        const response = await worker.fetch(request, mockEnv, mockCtx)
        const duration = performance.now() - start

        console.log(`${test.name}: ${duration.toFixed(2)}ms`)

        // Performance targets
        if (test.name === 'Cache Hit') {
          expect(duration).toBeLessThan(50) // Cache hits should be very fast
        } else {
          expect(duration).toBeLessThan(500) // Other operations should be reasonable
        }
      }
    })

    function setupCacheHit() {
      const cachedData = new ArrayBuffer(8)
      const metadata = {
        timestamp: Date.now() - 60000,
        ttl: 300,
        tags: ['products'],
        hitCount: 5,
        contentType: 'application/json',
        etag: '"perf-test"',
        size: 8
      }

      mockEnv.API_CACHE_KV.get.mockResolvedValue(cachedData)
      mockEnv.API_METADATA_KV.get.mockResolvedValue(JSON.stringify(metadata))
    }

    function setupCacheMiss() {
      mockEnv.API_CACHE_KV.get.mockResolvedValue(null)
      mockEnv.API_METADATA_KV.get.mockResolvedValue(null)
    }

    function setupRateLimit() {
      mockEnv.RATE_LIMIT_KV.get.mockResolvedValue(JSON.stringify({ count: 150, window: Date.now() }))
    }
  })

  describe('Stress Tests', () => {
    it('should handle high request volume', async () => {
      const requestCount = 100
      const requests = Array.from({ length: requestCount }, (_, i) =>
        new Request(`https://api-cache.syndicaps.com/api/products?id=${i}`)
      )

      const start = performance.now()
      const responses = await Promise.all(
        requests.map(request => worker.fetch(request, mockEnv, mockCtx))
      )
      const duration = performance.now() - start

      console.log(`Processed ${requestCount} requests in ${duration.toFixed(2)}ms`)
      console.log(`Average: ${(duration / requestCount).toFixed(2)}ms per request`)

      // All requests should complete successfully
      responses.forEach((response, index) => {
        expect(response.status).toBeGreaterThanOrEqual(200)
        expect(response.status).toBeLessThan(600)
      })

      // Should maintain reasonable throughput
      expect(duration / requestCount).toBeLessThan(100) // Less than 100ms per request on average
    })

    it('should handle memory efficiently', async () => {
      // Test with large response bodies
      const largeResponse = 'x'.repeat(1024 * 1024) // 1MB response
      global.fetch.mockResolvedValue(new Response(largeResponse, {
        status: 200,
        headers: { 'Content-Type': 'text/plain' }
      }))

      const request = new Request('https://api-cache.syndicaps.com/api/large-data')
      const response = await worker.fetch(request, mockEnv, mockCtx)

      expect(response.status).toBe(200)
      expect(await response.text()).toBe(largeResponse)
    })
  })
})

    it('should provide endpoint-specific analytics', async () => {
      const request = new Request('https://api-cache.syndicaps.com/cache-admin/analytics/endpoint?endpoint=/api/products&window=3600', {
        headers: { 'Authorization': 'Bearer admin-token' }
      })
      
      const response = await worker.fetch(request, mockEnv, mockCtx)
      expect(response.status).toBe(200)
    })
  })

  describe('Error Handling', () => {
    it('should handle KV storage errors gracefully', async () => {
      mockEnv.API_CACHE_KV.get.mockRejectedValue(new Error('KV Error'))
      
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200) // Should fallback to origin
    })

    it('should handle Firebase Functions errors', async () => {
      global.fetch.mockRejectedValue(new Error('Network Error'))
      
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(503)
    })

    it('should serve stale content on origin errors', async () => {
      const cachedData = new ArrayBuffer(8)
      const metadata = {
        timestamp: Date.now() - 400000, // Old cache
        ttl: 300,
        tags: ['products'],
        hitCount: 1,
        contentType: 'application/json',
        etag: '"error-fallback"',
        size: 8
      }

      mockEnv.API_CACHE_KV.get.mockResolvedValue(cachedData)
      mockEnv.API_METADATA_KV.get.mockResolvedValue(JSON.stringify(metadata))
      global.fetch.mockRejectedValue(new Error('Origin Error'))
      
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('X-Cache-Status')).toBe('ERROR')
    })
  })

  describe('Performance', () => {
    it('should complete requests within acceptable time', async () => {
      const start = Date.now()
      
      const request = new Request('https://api-cache.syndicaps.com/api/products')
      await worker.fetch(request, mockEnv, mockCtx)
      
      const duration = Date.now() - start
      expect(duration).toBeLessThan(1000) // Should complete within 1 second
    })

    it('should handle concurrent requests', async () => {
      const requests = Array.from({ length: 10 }, (_, i) => 
        new Request(`https://api-cache.syndicaps.com/api/products?page=${i}`)
      )
      
      const responses = await Promise.all(
        requests.map(request => worker.fetch(request, mockEnv, mockCtx))
      )
      
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })
    })
  })

  describe('Cache Key Generation', () => {
    it('should generate different keys for different endpoints', async () => {
      const request1 = new Request('https://api-cache.syndicaps.com/api/products')
      const request2 = new Request('https://api-cache.syndicaps.com/api/categories')
      
      await worker.fetch(request1, mockEnv, mockCtx)
      await worker.fetch(request2, mockEnv, mockCtx)
      
      // Verify different cache keys were used
      const putCalls = mockEnv.API_CACHE_KV.put.mock.calls
      expect(putCalls.length).toBeGreaterThan(0)
      
      if (putCalls.length >= 2) {
        expect(putCalls[0][0]).not.toBe(putCalls[1][0])
      }
    })

    it('should vary cache by user type', async () => {
      const anonRequest = new Request('https://api-cache.syndicaps.com/api/products')
      const authRequest = new Request('https://api-cache.syndicaps.com/api/products', {
        headers: { 'Authorization': 'Bearer test-token' }
      })
      
      await worker.fetch(anonRequest, mockEnv, mockCtx)
      await worker.fetch(authRequest, mockEnv, mockCtx)
      
      // Should generate different cache keys for different user types
      expect(mockEnv.API_CACHE_KV.put).toHaveBeenCalledTimes(2)
    })
  })
})
