/**
 * Comprehensive tests for Image Optimization Worker
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'

// Mock environment for testing
const mockEnv = {
  IMAGE_CACHE_KV: {
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  IMAGE_METADATA_KV: {
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  R2_IMAGES: {
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  CLOUDFLARE_IMAGES_ACCOUNT_ID: 'test-account-id',
  CLOUDFLARE_IMAGES_API_TOKEN: 'test-api-token',
  R2_ACCOUNT_ID: 'test-r2-account',
  R2_ACCESS_KEY_ID: 'test-access-key',
  R2_SECRET_ACCESS_KEY: 'test-secret-key',
  ENVIRONMENT: 'test'
}

// Mock execution context
const mockCtx = {
  waitUntil: vi.fn(),
  passThroughOnException: vi.fn()
}

// Sample image data for testing
const sampleImageData = new ArrayBuffer(1024)
const sampleImageView = new Uint8Array(sampleImageData)
sampleImageView.fill(255) // Fill with white pixels

const sampleR2Object = {
  body: sampleImageData,
  arrayBuffer: () => Promise.resolve(sampleImageData),
  httpMetadata: {
    contentType: 'image/jpeg'
  },
  size: sampleImageData.byteLength
}

describe('Image Optimization Worker', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock responses
    mockEnv.R2_IMAGES.get.mockResolvedValue(sampleR2Object)
    mockEnv.IMAGE_CACHE_KV.get.mockResolvedValue(null)
    mockEnv.IMAGE_METADATA_KV.get.mockResolvedValue(null)
    
    // Mock fetch for Cloudflare Images API
    global.fetch = vi.fn()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Basic Functionality', () => {
    it('should handle health check requests', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/health')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(await response.text()).toBe('OK')
    })

    it('should handle CORS preflight requests', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/test.jpg', {
        method: 'OPTIONS'
      })
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(204)
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
      expect(response.headers.get('Access-Control-Allow-Methods')).toBe('GET, HEAD, OPTIONS')
    })

    it('should reject unsupported HTTP methods', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/test.jpg', {
        method: 'POST'
      })
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(405)
      expect(await response.text()).toBe('Method not allowed')
    })
  })

  describe('URL Parsing and Parameter Validation', () => {
    it('should parse transformation parameters correctly', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/w=300,h=200,q=85,f=webp/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(mockEnv.R2_IMAGES.get).toHaveBeenCalledWith('test.jpg')
    })

    it('should validate width parameters', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/w=5000/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(400)
      const errorData = await response.json()
      expect(errorData.error).toContain('Width must be between 1 and 2048 pixels')
    })

    it('should validate height parameters', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/h=5000/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(400)
      const errorData = await response.json()
      expect(errorData.error).toContain('Height must be between 1 and 2048 pixels')
    })

    it('should validate quality parameters', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/q=150/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(400)
      const errorData = await response.json()
      expect(errorData.error).toContain('Quality must be between 1 and 100')
    })

    it('should validate format parameters', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/f=invalid/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(400)
      const errorData = await response.json()
      expect(errorData.error).toContain('Invalid format')
    })
  })

  describe('Caching Functionality', () => {
    it('should return cached images when available', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Mock cached image data
      const cachedMetadata = {
        originalSize: 2048,
        optimizedSize: 1024,
        format: 'webp',
        transformations: { width: 300 },
        timestamp: Date.now(),
        hitCount: 5,
        contentType: 'image/webp',
        etag: 'test-etag'
      }
      
      mockEnv.IMAGE_CACHE_KV.get.mockResolvedValue(sampleImageData)
      mockEnv.IMAGE_METADATA_KV.get.mockResolvedValue(cachedMetadata)
      
      const request = new Request('https://images.syndicaps.com/w=300/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('X-Cache-Status')).toBe('HIT')
      expect(response.headers.get('Content-Type')).toBe('image/webp')
    })

    it('should cache optimized images', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/w=300/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(mockEnv.IMAGE_CACHE_KV.put).toHaveBeenCalled()
      expect(mockEnv.IMAGE_METADATA_KV.put).toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should handle missing images gracefully', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      mockEnv.R2_IMAGES.get.mockResolvedValue(null)
      
      const request = new Request('https://images.syndicaps.com/nonexistent.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(404)
    })

    it('should handle R2 storage errors', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      mockEnv.R2_IMAGES.get.mockRejectedValue(new Error('R2 connection failed'))
      
      const request = new Request('https://images.syndicaps.com/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(500)
    })

    it('should handle cache errors gracefully', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      mockEnv.IMAGE_CACHE_KV.get.mockRejectedValue(new Error('Cache error'))
      
      const request = new Request('https://images.syndicaps.com/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      // Should still process the image even if cache fails
      expect(response.status).toBe(200)
    })

    it('should handle oversized images', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Mock large image
      const largeImageData = new ArrayBuffer(15 * 1024 * 1024) // 15MB
      const largeR2Object = {
        ...sampleR2Object,
        body: largeImageData,
        arrayBuffer: () => Promise.resolve(largeImageData),
        size: largeImageData.byteLength
      }
      
      mockEnv.R2_IMAGES.get.mockResolvedValue(largeR2Object)
      
      const request = new Request('https://images.syndicaps.com/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(413)
      const errorData = await response.json()
      expect(errorData.error).toContain('Image too large')
    })
  })

  describe('Cloudflare Images Integration', () => {
    it('should use Cloudflare Images when configured', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Mock successful Cloudflare Images upload
      const uploadResponse = {
        ok: true,
        json: () => Promise.resolve({
          result: { id: 'test-image-id' }
        })
      }
      
      // Mock successful optimization
      const optimizedResponse = {
        ok: true,
        arrayBuffer: () => Promise.resolve(sampleImageData),
        headers: new Headers({ 'Content-Type': 'image/webp' })
      }
      
      global.fetch = vi.fn()
        .mockResolvedValueOnce(uploadResponse) // Upload call
        .mockResolvedValueOnce(optimizedResponse) // Optimization call
      
      const request = new Request('https://images.syndicaps.com/w=300,f=webp/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(global.fetch).toHaveBeenCalledTimes(2)
    })

    it('should fallback to basic optimization when Cloudflare Images fails', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Mock failed Cloudflare Images upload
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 500,
        text: () => Promise.resolve('Upload failed')
      })
      
      const request = new Request('https://images.syndicaps.com/w=300/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200) // Should still succeed with fallback
    })
  })

  describe('Performance and Metrics', () => {
    it('should record performance metrics', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      // Verify metrics were recorded
      expect(mockEnv.IMAGE_METADATA_KV.put).toHaveBeenCalledWith(
        expect.stringMatching(/^metrics:/),
        expect.any(String),
        expect.any(Object)
      )
    })

    it('should include optimization headers', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/w=300/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('X-Image-Optimized')).toBe('true')
      expect(response.headers.get('X-Cache-Status')).toBe('MISS')
      expect(response.headers.get('X-Original-Size')).toBeTruthy()
      expect(response.headers.get('X-Optimized-Size')).toBeTruthy()
      expect(response.headers.get('X-Compression-Ratio')).toBeTruthy()
    })
  })

  describe('Format Support', () => {
    const formats = ['webp', 'avif', 'jpeg', 'png']
    
    formats.forEach(format => {
      it(`should handle ${format} format requests`, async () => {
        const { default: worker } = await import('../image-optimizer')
        
        const request = new Request(`https://images.syndicaps.com/f=${format}/test.jpg`)
        const response = await worker.fetch(request, mockEnv, mockCtx)
        
        expect(response.status).toBe(200)
      })
    })

    it('should handle auto format detection', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/f=auto/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      // Auto format should default to WebP
      expect(response.headers.get('Content-Type')).toBe('image/webp')
    })
  })

  describe('Transformation Parameters', () => {
    it('should handle multiple transformation parameters', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/w=300,h=200,q=85,f=webp,fit=cover,g=center/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
    })

    it('should handle blur parameter', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/blur=5/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
    })

    it('should handle sharpen parameter', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/sharpen=2/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
    })

    it('should handle background color parameter', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const request = new Request('https://images.syndicaps.com/bg=ffffff/test.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
    })
  })
})
