/**
 * Performance tests for Image Optimization Worker
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'

// Performance test configuration
const PERFORMANCE_CONFIG = {
  maxResponseTime: 500, // 500ms for cached responses
  maxOptimizationTime: 5000, // 5s for optimization
  concurrentRequests: 10,
  testImageSizes: [
    { name: 'small', size: 50 * 1024 }, // 50KB
    { name: 'medium', size: 500 * 1024 }, // 500KB
    { name: 'large', size: 2 * 1024 * 1024 }, // 2MB
  ],
  testFormats: ['jpeg', 'png', 'webp', 'avif'],
  testTransformations: [
    { name: 'resize', params: 'w=300,h=200' },
    { name: 'quality', params: 'q=75' },
    { name: 'format', params: 'f=webp' },
    { name: 'complex', params: 'w=400,h=300,q=85,f=webp,fit=cover' }
  ]
}

// Mock environment for performance testing
const mockEnv = {
  IMAGE_CACHE_KV: {
    get: async (key: string) => {
      // Simulate cache miss for performance testing
      return null
    },
    put: async (key: string, value: any, options?: any) => {
      // Simulate successful cache storage
      return Promise.resolve()
    },
    delete: async (key: string) => Promise.resolve(),
    list: async () => ({ keys: [] })
  },
  IMAGE_METADATA_KV: {
    get: async (key: string) => null,
    put: async (key: string, value: any, options?: any) => Promise.resolve(),
    delete: async (key: string) => Promise.resolve(),
    list: async () => ({ keys: [] })
  },
  R2_IMAGES: {
    get: async (key: string) => {
      // Generate test image data based on size
      const size = PERFORMANCE_CONFIG.testImageSizes.find(s => key.includes(s.name))?.size || 100 * 1024
      const imageData = new ArrayBuffer(size)
      
      return {
        body: imageData,
        arrayBuffer: () => Promise.resolve(imageData),
        httpMetadata: { contentType: 'image/jpeg' },
        size: imageData.byteLength
      }
    },
    put: async () => Promise.resolve(),
    delete: async () => Promise.resolve(),
    list: async () => ({ objects: [] })
  },
  CLOUDFLARE_IMAGES_ACCOUNT_ID: 'test-account',
  CLOUDFLARE_IMAGES_API_TOKEN: 'test-token',
  R2_ACCOUNT_ID: 'test-r2-account',
  R2_ACCESS_KEY_ID: 'test-access-key',
  R2_SECRET_ACCESS_KEY: 'test-secret-key',
  ENVIRONMENT: 'test'
}

const mockCtx = {
  waitUntil: () => {},
  passThroughOnException: () => {}
}

describe('Image Optimization Worker Performance', () => {
  beforeAll(() => {
    // Mock fetch for Cloudflare Images API
    global.fetch = async (url: string, options?: any) => {
      if (url.includes('api.cloudflare.com')) {
        // Mock upload response
        return {
          ok: true,
          json: () => Promise.resolve({ result: { id: 'test-image-id' } })
        }
      } else if (url.includes('imagedelivery.net')) {
        // Mock optimization response
        const optimizedData = new ArrayBuffer(50 * 1024) // Simulated optimized size
        return {
          ok: true,
          arrayBuffer: () => Promise.resolve(optimizedData),
          headers: new Headers({ 'Content-Type': 'image/webp' })
        }
      }
      
      return { ok: false, status: 404 }
    }
  })

  afterAll(() => {
    // Restore fetch
    delete global.fetch
  })

  describe('Response Time Performance', () => {
    it('should respond within acceptable time limits', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const startTime = Date.now()
      const request = new Request('https://images.syndicaps.com/w=300/test-medium.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      const endTime = Date.now()
      
      const responseTime = endTime - startTime
      
      expect(response.status).toBe(200)
      expect(responseTime).toBeLessThan(PERFORMANCE_CONFIG.maxOptimizationTime)
    })

    it('should handle cached responses quickly', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Mock cached response
      const cachedData = new ArrayBuffer(100 * 1024)
      const cachedMetadata = {
        originalSize: 200 * 1024,
        optimizedSize: 100 * 1024,
        format: 'webp',
        transformations: { width: 300 },
        timestamp: Date.now(),
        hitCount: 1,
        contentType: 'image/webp',
        etag: 'test-etag'
      }
      
      mockEnv.IMAGE_CACHE_KV.get = async () => cachedData
      mockEnv.IMAGE_METADATA_KV.get = async () => cachedMetadata
      
      const startTime = Date.now()
      const request = new Request('https://images.syndicaps.com/w=300/test-cached.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      const endTime = Date.now()
      
      const responseTime = endTime - startTime
      
      expect(response.status).toBe(200)
      expect(response.headers.get('X-Cache-Status')).toBe('HIT')
      expect(responseTime).toBeLessThan(PERFORMANCE_CONFIG.maxResponseTime)
    })
  })

  describe('Concurrent Request Handling', () => {
    it('should handle multiple concurrent requests', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const requests = Array.from({ length: PERFORMANCE_CONFIG.concurrentRequests }, (_, i) => {
        const request = new Request(`https://images.syndicaps.com/w=300/test-concurrent-${i}.jpg`)
        return worker.fetch(request, mockEnv, mockCtx)
      })
      
      const startTime = Date.now()
      const responses = await Promise.all(requests)
      const endTime = Date.now()
      
      const totalTime = endTime - startTime
      const avgTimePerRequest = totalTime / PERFORMANCE_CONFIG.concurrentRequests
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })
      
      // Average time per request should be reasonable
      expect(avgTimePerRequest).toBeLessThan(PERFORMANCE_CONFIG.maxOptimizationTime)
    })
  })

  describe('Image Size Performance', () => {
    PERFORMANCE_CONFIG.testImageSizes.forEach(({ name, size }) => {
      it(`should handle ${name} images (${Math.round(size / 1024)}KB) efficiently`, async () => {
        const { default: worker } = await import('../image-optimizer')
        
        const startTime = Date.now()
        const request = new Request(`https://images.syndicaps.com/w=300/test-${name}.jpg`)
        const response = await worker.fetch(request, mockEnv, mockCtx)
        const endTime = Date.now()
        
        const processingTime = endTime - startTime
        
        expect(response.status).toBe(200)
        
        // Larger images may take longer, but should still be within limits
        const timeLimit = size > 1024 * 1024 ? PERFORMANCE_CONFIG.maxOptimizationTime * 2 : PERFORMANCE_CONFIG.maxOptimizationTime
        expect(processingTime).toBeLessThan(timeLimit)
      })
    })
  })

  describe('Transformation Performance', () => {
    PERFORMANCE_CONFIG.testTransformations.forEach(({ name, params }) => {
      it(`should handle ${name} transformations efficiently`, async () => {
        const { default: worker } = await import('../image-optimizer')
        
        const startTime = Date.now()
        const request = new Request(`https://images.syndicaps.com/${params}/test-transform.jpg`)
        const response = await worker.fetch(request, mockEnv, mockCtx)
        const endTime = Date.now()
        
        const processingTime = endTime - startTime
        
        expect(response.status).toBe(200)
        expect(processingTime).toBeLessThan(PERFORMANCE_CONFIG.maxOptimizationTime)
      })
    })
  })

  describe('Format Conversion Performance', () => {
    PERFORMANCE_CONFIG.testFormats.forEach(format => {
      it(`should convert to ${format} format efficiently`, async () => {
        const { default: worker } = await import('../image-optimizer')
        
        const startTime = Date.now()
        const request = new Request(`https://images.syndicaps.com/f=${format}/test-format.jpg`)
        const response = await worker.fetch(request, mockEnv, mockCtx)
        const endTime = Date.now()
        
        const processingTime = endTime - startTime
        
        expect(response.status).toBe(200)
        expect(processingTime).toBeLessThan(PERFORMANCE_CONFIG.maxOptimizationTime)
      })
    })
  })

  describe('Memory Usage', () => {
    it('should not leak memory during processing', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Process multiple images to test for memory leaks
      const iterations = 20
      const requests = []
      
      for (let i = 0; i < iterations; i++) {
        const request = new Request(`https://images.syndicaps.com/w=300/test-memory-${i}.jpg`)
        requests.push(worker.fetch(request, mockEnv, mockCtx))
      }
      
      const responses = await Promise.all(requests)
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })
      
      // Memory usage should be stable (this is a basic check)
      // In a real environment, you'd monitor actual memory usage
      expect(responses.length).toBe(iterations)
    })
  })

  describe('Error Handling Performance', () => {
    it('should handle errors quickly', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Mock R2 error
      mockEnv.R2_IMAGES.get = async () => {
        throw new Error('R2 connection failed')
      }
      
      const startTime = Date.now()
      const request = new Request('https://images.syndicaps.com/w=300/test-error.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      const endTime = Date.now()
      
      const errorHandlingTime = endTime - startTime
      
      expect(response.status).toBe(500)
      expect(errorHandlingTime).toBeLessThan(1000) // Error handling should be fast
    })

    it('should handle validation errors quickly', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const startTime = Date.now()
      const request = new Request('https://images.syndicaps.com/w=5000/test-validation.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      const endTime = Date.now()
      
      const validationTime = endTime - startTime
      
      expect(response.status).toBe(400)
      expect(validationTime).toBeLessThan(100) // Validation should be very fast
    })
  })

  describe('Cache Performance', () => {
    it('should cache results efficiently', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      // Reset cache mocks
      mockEnv.IMAGE_CACHE_KV.get = async () => null
      mockEnv.IMAGE_METADATA_KV.get = async () => null
      
      let cachePutCalls = 0
      mockEnv.IMAGE_CACHE_KV.put = async () => {
        cachePutCalls++
        return Promise.resolve()
      }
      
      const request = new Request('https://images.syndicaps.com/w=300/test-cache-perf.jpg')
      const response = await worker.fetch(request, mockEnv, mockCtx)
      
      expect(response.status).toBe(200)
      expect(cachePutCalls).toBe(1) // Should cache the result
    })
  })

  describe('Stress Testing', () => {
    it('should handle rapid successive requests', async () => {
      const { default: worker } = await import('../image-optimizer')
      
      const rapidRequests = 50
      const requests = []
      
      const startTime = Date.now()
      
      for (let i = 0; i < rapidRequests; i++) {
        const request = new Request(`https://images.syndicaps.com/w=200/test-rapid-${i}.jpg`)
        requests.push(worker.fetch(request, mockEnv, mockCtx))
      }
      
      const responses = await Promise.all(requests)
      const endTime = Date.now()
      
      const totalTime = endTime - startTime
      const avgTimePerRequest = totalTime / rapidRequests
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })
      
      // Average time should be reasonable even under stress
      expect(avgTimePerRequest).toBeLessThan(PERFORMANCE_CONFIG.maxOptimizationTime)
    })
  })
})
