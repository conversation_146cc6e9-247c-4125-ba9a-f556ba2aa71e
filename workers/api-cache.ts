/**
 * Cloudflare Worker: API Cache for Syndicaps
 *
 * Provides intelligent caching layer for Firebase Functions API calls
 * with cache invalidation, rate limiting, and performance monitoring.
 */

import {
  CacheInvalidationManager,
  StaleWhileRevalidateManager,
  CacheWarmingManager,
  type InvalidationRequest,
  type InvalidationResult
} from './lib/cache-invalidation'

import {
  RateLimiter,
  type RateLimitConfig,
  type RateLimitRule,
  type RateLimitResult
} from './lib/rate-limiter'

import {
  CacheAnalytics,
  type CacheMetrics,
  type PerformanceMetrics
} from './lib/analytics'

export interface Env {
  API_CACHE_KV: KVNamespace
  API_METADATA_KV: KVNamespace
  RATE_LIMIT_KV: KVNamespace
  FIREBASE_FUNCTIONS_URL: string
  FIREBASE_PROJECT_ID: string
  FIREBASE_REGION: string
  ENVIRONMENT: string
  RATE_LIMIT_ENABLED?: string
}

interface CacheConfig {
  ttl: number
  staleWhileRevalidate: number
  maxAge: number
  tags: string[]
  varyBy: string[]
  private?: boolean
  bypass?: boolean
}

interface RateLimitConfig {
  requests: number
  burst: number
  window: number
  identifier: 'ip' | 'user-id'
}

interface CacheMetadata {
  timestamp: number
  ttl: number
  tags: string[]
  varyBy: Record<string, string>
  hitCount: number
  lastAccessed: number
  responseTime: number
  contentType: string
  etag: string
  size: number
}

interface AnalyticsData {
  endpoint: string
  method: string
  cacheStatus: 'HIT' | 'MISS' | 'STALE' | 'BYPASS'
  responseTime: number
  originTime?: number
  cacheAge?: number
  userType: string
  timestamp: number
  rateLimited: boolean
}

// Enhanced cache configuration for different API endpoints
const CACHE_CONFIGS: Record<string, CacheConfig> = {
  '/api/products': {
    ttl: 300,              // 5 minutes
    staleWhileRevalidate: 600,  // 10 minutes
    maxAge: 1800,          // 30 minutes
    tags: ['products', 'catalog'],
    varyBy: ['user-type', 'region']
  },
  '/api/categories': {
    ttl: 600,              // 10 minutes
    staleWhileRevalidate: 1200, // 20 minutes
    maxAge: 3600,          // 1 hour
    tags: ['categories', 'catalog'],
    varyBy: ['locale']
  },
  '/api/user/profile': {
    ttl: 60,               // 1 minute
    staleWhileRevalidate: 300,  // 5 minutes
    maxAge: 900,           // 15 minutes
    tags: ['user', 'profile'],
    varyBy: ['user-id'],
    private: true
  },
  '/api/auth': {
    ttl: 0,                // No caching
    staleWhileRevalidate: 0,
    maxAge: 0,
    tags: ['auth'],
    varyBy: [],
    bypass: true
  },
  '/api/cart': {
    ttl: 0,                // No cache
    staleWhileRevalidate: 0,
    maxAge: 0,
    tags: ['cart'],
    varyBy: ['user-id'],
    bypass: true
  },
  '/api/admin': {
    ttl: 0,                // No cache for admin endpoints
    staleWhileRevalidate: 0,
    maxAge: 0,
    tags: ['admin'],
    varyBy: [],
    bypass: true
  },
  '/api/content/static': {
    ttl: 3600,             // 1 hour
    staleWhileRevalidate: 7200,  // 2 hours
    maxAge: 86400,         // 24 hours
    tags: ['content', 'static'],
    varyBy: ['locale']
  },
  '/api/notifications': {
    ttl: 30,               // 30 seconds
    staleWhileRevalidate: 60,   // 1 minute
    maxAge: 300,           // 5 minutes
    tags: ['notifications', 'realtime'],
    varyBy: ['user-id']
  },
  '/api/analytics': {
    ttl: 900,              // 15 minutes
    staleWhileRevalidate: 1800, // 30 minutes
    maxAge: 3600,          // 1 hour
    tags: ['analytics'],
    varyBy: ['date-range', 'user-role']
  }
}

// Enhanced rate limiting configuration
const RATE_LIMIT_CONFIG: RateLimitConfig = {
  defaultRule: 'anonymous',
  enableBurstProtection: true,
  enableAdaptiveThrottling: true,
  whitelistIPs: [
    '127.0.0.1',
    '::1'
  ],
  blacklistIPs: [],
  rules: {
    'anonymous': {
      requests: 100,         // per hour
      burst: 20,             // per minute
      window: 3600,          // 1 hour
      identifier: 'ip',
      priority: 1
    },
    'authenticated': {
      requests: 1000,        // per hour
      burst: 100,            // per minute
      window: 3600,          // 1 hour
      identifier: 'user-id',
      priority: 2
    },
    'premium': {
      requests: 5000,        // per hour
      burst: 500,            // per minute
      window: 3600,          // 1 hour
      identifier: 'user-id',
      priority: 3
    },
    'admin': {
      requests: 10000,       // per hour
      burst: 1000,           // per minute
      window: 3600,          // 1 hour
      identifier: 'user-id',
      priority: 4,
      skipPaths: ['/cache-admin/']
    },
    'health-check': {
      requests: 1000,        // per hour
      burst: 60,             // per minute
      window: 3600,          // 1 hour
      identifier: 'ip',
      priority: 0,
      skipPaths: ['/health']
    }
  }
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const startTime = Date.now()
    const url = new URL(request.url)
    const { pathname } = url
    const method = request.method

    try {
      // Health check
      if (pathname === '/health') {
        return new Response('API Cache Worker OK', {
          status: 200,
          headers: { 'Content-Type': 'text/plain' }
        })
      }

      // Cache management endpoints
      if (pathname.startsWith('/cache-admin/')) {
        return await handleCacheAdminRequest(request, env, pathname)
      }

      // Handle CORS preflight
      if (method === 'OPTIONS') {
        return handleCorsPrelight()
      }

      // Only handle GET requests for caching
      if (method !== 'GET') {
        const response = await forwardToFirebase(request, env, false)
        await recordAnalytics(env, pathname, method, 'BYPASS', Date.now() - startTime, request)
        return addCorsHeaders(response)
      }

      // Enhanced rate limiting
      if (env.RATE_LIMIT_ENABLED === 'true') {
        const rateLimiter = new RateLimiter(env, RATE_LIMIT_CONFIG)
        const rateLimitResult = await rateLimiter.checkRateLimit(request)

        if (!rateLimitResult.allowed) {
          await recordAnalytics(env, pathname, method, 'BYPASS', Date.now() - startTime, request, undefined, true)
          return createEnhancedRateLimitResponse(rateLimitResult)
        }

        // Add rate limit headers to successful responses
        ctx.waitUntil(addRateLimitHeaders(rateLimitResult))
      }

      // Get cache configuration for this endpoint
      const cacheConfig = getCacheConfig(pathname)

      // Bypass cache if configured
      if (cacheConfig.bypass) {
        const response = await forwardToFirebase(request, env, false)
        await recordAnalytics(env, pathname, method, 'BYPASS', Date.now() - startTime, request)
        return addCorsHeaders(response)
      }

      // Generate cache key
      const cacheKey = await generateCacheKey(request, cacheConfig)

      // Try to get from cache
      const cachedResult = await getCachedResponse(env, cacheKey)

      if (cachedResult) {
        const cacheAge = Date.now() - cachedResult.metadata.timestamp

        // Fresh cache hit
        if (cacheAge < cacheConfig.ttl * 1000) {
          await updateCacheHitCount(env, cacheKey)
          await recordAnalytics(env, pathname, method, 'HIT', Date.now() - startTime, request, cacheAge)
          return createCachedResponse(cachedResult.response, cachedResult.metadata, 'HIT')
        }

        // Stale but within stale-while-revalidate window
        if (cacheAge < cacheConfig.staleWhileRevalidate * 1000) {
          // Serve stale content immediately
          const staleResponse = createCachedResponse(cachedResult.response, cachedResult.metadata, 'STALE')

          // Enhanced background refresh with invalidation manager
          const invalidationManager = new CacheInvalidationManager(env)
          ctx.waitUntil(invalidationManager.scheduleBackgroundRefresh([cacheKey]))

          await recordAnalytics(env, pathname, method, 'STALE', Date.now() - startTime, request, cacheAge)
          return staleResponse
        }
      }

      // Cache miss - fetch from origin
      const originResponse = await forwardToFirebase(request, env, true)
      const responseTime = Date.now() - startTime

      // Cache the response if successful
      if (originResponse.ok && shouldCacheResponse(originResponse, cacheConfig)) {
        ctx.waitUntil(cacheResponse(env, cacheKey, originResponse.clone(), cacheConfig, responseTime))
      }

      await recordAnalytics(env, pathname, method, 'MISS', responseTime, request)
      return addCacheHeaders(originResponse, 'MISS')

    } catch (error) {
      console.error('API Cache Worker error:', error)
      await recordAnalytics(env, pathname, method, 'BYPASS', Date.now() - startTime, request)

      // Try to serve stale content on error
      try {
        const cacheConfig = getCacheConfig(pathname)
        const cacheKey = await generateCacheKey(request, cacheConfig)
        const cachedResult = await getCachedResponse(env, cacheKey)

        if (cachedResult) {
          return createCachedResponse(cachedResult.response, cachedResult.metadata, 'ERROR')
        }
      } catch (fallbackError) {
        console.error('Fallback cache error:', fallbackError)
      }

      // Final fallback to origin
      try {
        return await forwardToFirebase(request, env, false)
      } catch (originError) {
        return new Response('Service Temporarily Unavailable', {
          status: 503,
          headers: { 'Content-Type': 'text/plain' }
        })
      }
    }
  },
}

/**
 * Get cache configuration for endpoint
 */
function getCacheConfig(pathname: string): CacheConfig {
  // Find the most specific matching configuration
  const sortedPaths = Object.keys(CACHE_CONFIGS).sort((a, b) => b.length - a.length)

  for (const path of sortedPaths) {
    if (pathname.startsWith(path)) {
      return CACHE_CONFIGS[path]
    }
  }

  // Default configuration
  return {
    ttl: 300,
    staleWhileRevalidate: 600,
    maxAge: 1800,
    tags: ['default'],
    varyBy: []
  }
}

/**
 * Generate cache key based on request and configuration
 */
async function generateCacheKey(request: Request, config: CacheConfig): Promise<string> {
  const url = new URL(request.url)
  const baseKey = `${request.method}:${url.pathname}${url.search}`

  // Add vary-by parameters
  const varyParts: string[] = []
  for (const varyBy of config.varyBy) {
    switch (varyBy) {
      case 'user-id':
        const userId = extractUserId(request)
        if (userId) varyParts.push(`user:${userId}`)
        break
      case 'user-type':
        const userType = extractUserType(request)
        varyParts.push(`type:${userType}`)
        break
      case 'region':
        const region = request.headers.get('CF-IPCountry') || 'unknown'
        varyParts.push(`region:${region}`)
        break
      case 'locale':
        const locale = request.headers.get('Accept-Language')?.split(',')[0] || 'en'
        varyParts.push(`locale:${locale}`)
        break
      case 'date-range':
        const dateRange = url.searchParams.get('dateRange') || 'default'
        varyParts.push(`date:${dateRange}`)
        break
      case 'user-role':
        const userRole = extractUserRole(request)
        varyParts.push(`role:${userRole}`)
        break
    }
  }

  const fullKey = varyParts.length > 0 ? `${baseKey}:${varyParts.join(':')}` : baseKey

  // Hash the key to ensure consistent length
  const encoder = new TextEncoder()
  const data = encoder.encode(fullKey)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')

  return `cache:${hashHex}`
}

/**
 * Extract user ID from request
 */
function extractUserId(request: Request): string | null {
  const authHeader = request.headers.get('Authorization')
  if (authHeader?.startsWith('Bearer ')) {
    // In a real implementation, decode JWT token
    // For now, return a placeholder based on token
    const token = authHeader.substring(7)
    return token.length > 10 ? `user-${token.substring(0, 8)}` : null
  }
  return null
}

/**
 * Extract user type from request
 */
function extractUserType(request: Request): string {
  const userId = extractUserId(request)
  if (!userId) return 'anonymous'

  // In a real implementation, check user role from token or database
  // For now, return based on presence of auth
  return 'authenticated'
}

/**
 * Extract user role from request
 */
function extractUserRole(request: Request): string {
  const authHeader = request.headers.get('Authorization')
  if (!authHeader) return 'anonymous'

  // In a real implementation, decode JWT and extract role
  // For now, return default role
  return 'user'
}

/**
 * Get cached response from KV
 */
async function getCachedResponse(env: Env, cacheKey: string): Promise<{ response: ArrayBuffer; metadata: CacheMetadata } | null> {
  try {
    const [responseData, metadataData] = await Promise.all([
      env.API_CACHE_KV.get(cacheKey, 'arrayBuffer'),
      env.API_METADATA_KV.get(`${cacheKey}:meta`, 'json')
    ])

    if (responseData && metadataData) {
      return {
        response: responseData,
        metadata: metadataData as CacheMetadata
      }
    }

    return null
  } catch (error) {
    console.error('Cache retrieval error:', error)
    return null
  }
}

/**
 * Create cached response with appropriate headers
 */
function createCachedResponse(responseData: ArrayBuffer, metadata: CacheMetadata, cacheStatus: string): Response {
  const cacheAge = Math.floor((Date.now() - metadata.timestamp) / 1000)

  return new Response(responseData, {
    headers: {
      'Content-Type': metadata.contentType,
      'ETag': metadata.etag,
      'X-Cache-Status': cacheStatus,
      'X-Cache-Age': cacheAge.toString(),
      'X-Cache-Worker': 'syndicaps-api-cache',
      'Cache-Control': `public, max-age=${metadata.ttl}`,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
    }
  })
}

/**
 * Handle CORS preflight requests
 */
function handleCorsPrelight(): Response {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400'
    }
  })
}

/**
 * Add CORS headers to response
 */
function addCorsHeaders(response: Response): Response {
  const newResponse = new Response(response.body, response)
  newResponse.headers.set('Access-Control-Allow-Origin', '*')
  newResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  newResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  return newResponse
}

/**
 * Add cache headers to response
 */
function addCacheHeaders(response: Response, cacheStatus: string): Response {
  const newResponse = new Response(response.body, response)
  newResponse.headers.set('X-Cache-Status', cacheStatus)
  newResponse.headers.set('X-Cache-Worker', 'syndicaps-api-cache')
  newResponse.headers.set('Access-Control-Allow-Origin', '*')
  return newResponse
}

/**
 * Forward request to Firebase Functions
 */
async function forwardToFirebase(request: Request, env: Env, addCacheHeaders: boolean): Promise<Response> {
  const url = new URL(request.url)
  const firebaseUrl = `${env.FIREBASE_FUNCTIONS_URL}${url.pathname}${url.search}`

  // Create new request with Firebase URL
  const firebaseRequest = new Request(firebaseUrl, {
    method: request.method,
    headers: request.headers,
    body: request.body
  })

  const response = await fetch(firebaseRequest)

  if (addCacheHeaders) {
    return addCacheHeaders(response, 'MISS')
  }

  return addCorsHeaders(response)
}

/**
 * Check if response should be cached
 */
function shouldCacheResponse(response: Response, config: CacheConfig): boolean {
  if (!response.ok) return false
  if (config.bypass) return false
  if (config.ttl === 0) return false

  const contentType = response.headers.get('Content-Type') || ''
  const cacheControl = response.headers.get('Cache-Control')

  // Don't cache if response has Cache-Control: no-cache or no-store
  if (cacheControl && (cacheControl.includes('no-cache') || cacheControl.includes('no-store'))) {
    return false
  }

  // Only cache JSON and text responses
  return contentType.includes('application/json') || contentType.includes('text/')
}

/**
 * Generate ETag for response
 */
function generateETag(data: ArrayBuffer): string {
  // Simple hash-based ETag
  const hash = Array.from(new Uint8Array(data.slice(0, 32)))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
  return `"${hash.substring(0, 16)}"`
}

/**
 * Cache response in KV
 */
async function cacheResponse(
  env: Env,
  cacheKey: string,
  response: Response,
  config: CacheConfig,
  responseTime: number
): Promise<void> {
  try {
    const responseBody = await response.arrayBuffer()
    const metadata: CacheMetadata = {
      timestamp: Date.now(),
      ttl: config.ttl,
      tags: config.tags,
      varyBy: {},
      hitCount: 0,
      lastAccessed: Date.now(),
      responseTime,
      contentType: response.headers.get('Content-Type') || 'application/json',
      etag: generateETag(responseBody),
      size: responseBody.byteLength
    }

    await Promise.all([
      env.API_CACHE_KV.put(cacheKey, responseBody, {
        expirationTtl: config.staleWhileRevalidate
      }),
      env.API_METADATA_KV.put(`${cacheKey}:meta`, JSON.stringify(metadata), {
        expirationTtl: config.staleWhileRevalidate
      })
    ])
  } catch (error) {
    console.error('Cache storage error:', error)
  }
}

/**
 * Update cache hit count
 */
async function updateCacheHitCount(env: Env, cacheKey: string): Promise<void> {
  try {
    const metadata = await env.API_METADATA_KV.get(`${cacheKey}:meta`, 'json') as CacheMetadata
    if (metadata) {
      metadata.hitCount++
      metadata.lastAccessed = Date.now()
      await env.API_METADATA_KV.put(`${cacheKey}:meta`, JSON.stringify(metadata))
    }
  } catch (error) {
    console.error('Hit count update error:', error)
  }
}

/**
 * Refresh cache in background
 */
async function refreshCache(request: Request, env: Env, cacheKey: string, config: CacheConfig): Promise<void> {
  try {
    const response = await forwardToFirebase(request, env, false)
    if (response.ok) {
      await cacheResponse(env, cacheKey, response, config, 0)
    }
  } catch (error) {
    console.error('Background refresh error:', error)
  }
}

/**
 * Create enhanced rate limit response
 */
function createEnhancedRateLimitResponse(rateLimitResult: RateLimitResult): Response {
  const headers = {
    'X-RateLimit-Limit': RATE_LIMIT_CONFIG.rules[rateLimitResult.rule]?.requests?.toString() || '100',
    'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
    'X-RateLimit-Reset': rateLimitResult.resetTime.toString(),
    'X-RateLimit-Rule': rateLimitResult.rule,
    'X-RateLimit-Identifier-Type': RATE_LIMIT_CONFIG.rules[rateLimitResult.rule]?.identifier || 'ip',
    'Retry-After': rateLimitResult.retryAfter.toString(),
    'Access-Control-Allow-Origin': '*',
    'Content-Type': 'application/json'
  }

  const body = {
    error: 'Rate limit exceeded',
    message: `Too many requests. Limit: ${headers['X-RateLimit-Limit']} requests per hour.`,
    retryAfter: rateLimitResult.retryAfter,
    resetTime: new Date(rateLimitResult.resetTime).toISOString(),
    rule: rateLimitResult.rule
  }

  return new Response(JSON.stringify(body), {
    status: 429,
    headers
  })
}

/**
 * Add rate limit headers to response context
 */
async function addRateLimitHeaders(rateLimitResult: RateLimitResult): Promise<void> {
  // This would be used to add headers to successful responses
  // Implementation depends on how you want to handle this in the response pipeline
}

/**
 * Record enhanced analytics data
 */
async function recordAnalytics(
  env: Env,
  endpoint: string,
  method: string,
  cacheStatus: string,
  responseTime: number,
  request: Request,
  cacheAge?: number,
  rateLimited: boolean = false
): Promise<void> {
  try {
    // Enhanced analytics with CacheAnalytics system
    const analytics = new CacheAnalytics(env)

    const performanceMetrics: PerformanceMetrics = {
      timestamp: Date.now(),
      endpoint,
      method,
      cacheStatus: cacheStatus as any,
      responseTime,
      cacheAge,
      userType: extractUserType(request),
      region: request.headers.get('CF-IPCountry') || undefined,
      userAgent: request.headers.get('User-Agent') || undefined,
      rateLimited
    }

    await analytics.recordMetrics(performanceMetrics)

    // Also maintain legacy analytics for backward compatibility
    const legacyAnalytics: AnalyticsData = {
      endpoint,
      method,
      cacheStatus: cacheStatus as any,
      responseTime,
      cacheAge,
      userType: extractUserType(request),
      timestamp: Date.now(),
      rateLimited
    }

    const date = new Date().toISOString().split('T')[0]
    const hour = new Date().getHours()
    const analyticsKey = `analytics:${date}:${hour}`

    const existing = await env.API_METADATA_KV.get(analyticsKey, 'json') || []
    const analyticsArray = Array.isArray(existing) ? existing : []
    analyticsArray.push(legacyAnalytics)

    // Keep only last 100 entries per hour
    if (analyticsArray.length > 100) {
      analyticsArray.splice(0, analyticsArray.length - 100)
    }

    await env.API_METADATA_KV.put(analyticsKey, JSON.stringify(analyticsArray), {
      expirationTtl: 7 * 24 * 60 * 60 // 7 days
    })
  } catch (error) {
    console.error('Analytics recording error:', error)
  }
}

/**
 * Handle cache administration requests
 */
async function handleCacheAdminRequest(request: Request, env: Env, pathname: string): Promise<Response> {
  const url = new URL(request.url)
  const method = request.method

  // Basic authentication check (in production, use proper auth)
  const authHeader = request.headers.get('Authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response('Unauthorized', {
      status: 401,
      headers: { 'Content-Type': 'text/plain' }
    })
  }

  const invalidationManager = new CacheInvalidationManager(env)
  const warmingManager = new CacheWarmingManager(env)

  try {
    switch (pathname) {
      case '/cache-admin/invalidate/tag':
        if (method === 'POST') {
          const body = await request.json() as { tags: string[], backgroundRefresh?: boolean }
          const result = await invalidationManager.invalidateByTag(body.tags, body.backgroundRefresh)
          return new Response(JSON.stringify(result), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/invalidate/pattern':
        if (method === 'POST') {
          const body = await request.json() as { pattern: string, backgroundRefresh?: boolean }
          const result = await invalidationManager.invalidateByPattern(body.pattern, body.backgroundRefresh)
          return new Response(JSON.stringify(result), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/invalidate/key':
        if (method === 'POST') {
          const body = await request.json() as { key: string, backgroundRefresh?: boolean }
          const result = await invalidationManager.invalidateByKey(body.key, body.backgroundRefresh)
          return new Response(JSON.stringify(result), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/invalidate/all':
        if (method === 'POST') {
          const body = await request.json() as { backgroundRefresh?: boolean }
          const result = await invalidationManager.invalidateAll(body.backgroundRefresh)
          return new Response(JSON.stringify(result), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/warm':
        if (method === 'POST') {
          const body = await request.json() as { endpoints: string[] }
          await warmingManager.warmPopularEndpoints(body.endpoints)
          return new Response(JSON.stringify({ success: true, warmed: body.endpoints.length }), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/stats':
        if (method === 'GET') {
          const stats = await getCacheStats(env)
          return new Response(JSON.stringify(stats), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/rate-limit/stats':
        if (method === 'GET') {
          const rateLimiter = new RateLimiter(env, RATE_LIMIT_CONFIG)
          const timeWindow = parseInt(url.searchParams.get('window') || '3600')
          const stats = await rateLimiter.getMetrics(timeWindow)
          return new Response(JSON.stringify(stats), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/rate-limit/reset':
        if (method === 'POST') {
          const body = await request.json() as { identifier: string }
          const rateLimiter = new RateLimiter(env, RATE_LIMIT_CONFIG)
          await rateLimiter.resetLimits(body.identifier)
          return new Response(JSON.stringify({ success: true, reset: body.identifier }), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/analytics/metrics':
        if (method === 'GET') {
          const analytics = new CacheAnalytics(env)
          const timeWindow = parseInt(url.searchParams.get('window') || '3600')
          const metrics = await analytics.getCacheMetrics(timeWindow)
          return new Response(JSON.stringify(metrics), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/analytics/dashboard':
        if (method === 'GET') {
          const analytics = new CacheAnalytics(env)
          const dashboardData = await analytics.getDashboardData()
          return new Response(JSON.stringify(dashboardData), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      case '/cache-admin/analytics/endpoint':
        if (method === 'GET') {
          const analytics = new CacheAnalytics(env)
          const endpoint = url.searchParams.get('endpoint')
          const timeWindow = parseInt(url.searchParams.get('window') || '3600')

          if (!endpoint) {
            return new Response(JSON.stringify({ error: 'Endpoint parameter required' }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            })
          }

          const endpointData = await analytics.getEndpointAnalytics(endpoint, timeWindow)
          return new Response(JSON.stringify(endpointData), {
            headers: { 'Content-Type': 'application/json' }
          })
        }
        break

      default:
        return new Response('Not Found', { status: 404 })
    }

    return new Response('Method Not Allowed', { status: 405 })

  } catch (error) {
    console.error('Cache admin error:', error)
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

/**
 * Get cache statistics
 */
async function getCacheStats(env: Env): Promise<any> {
  try {
    const cacheList = await env.API_CACHE_KV.list({ limit: 1000 })
    const metadataList = await env.API_METADATA_KV.list({ limit: 1000 })

    let totalSize = 0
    let totalHits = 0
    let oldestEntry = Date.now()
    let newestEntry = 0
    const tagCounts: Record<string, number> = {}

    // Analyze metadata
    for (const item of metadataList.keys) {
      if (item.name.endsWith(':meta')) {
        try {
          const metadata = await env.API_METADATA_KV.get(item.name, 'json') as CacheMetadata
          if (metadata) {
            totalSize += metadata.size || 0
            totalHits += metadata.hitCount || 0

            if (metadata.timestamp < oldestEntry) {
              oldestEntry = metadata.timestamp
            }
            if (metadata.timestamp > newestEntry) {
              newestEntry = metadata.timestamp
            }

            // Count tags
            if (metadata.tags) {
              for (const tag of metadata.tags) {
                tagCounts[tag] = (tagCounts[tag] || 0) + 1
              }
            }
          }
        } catch (error) {
          // Skip invalid metadata
        }
      }
    }

    return {
      totalEntries: cacheList.keys.length,
      totalSize,
      totalHits,
      oldestEntry: new Date(oldestEntry).toISOString(),
      newestEntry: new Date(newestEntry).toISOString(),
      tagCounts,
      averageSize: cacheList.keys.length > 0 ? Math.round(totalSize / cacheList.keys.length) : 0,
      averageHits: cacheList.keys.length > 0 ? Math.round(totalHits / cacheList.keys.length) : 0
    }
  } catch (error) {
    console.error('Cache stats error:', error)
    return { error: 'Failed to get cache statistics' }
  }
}
