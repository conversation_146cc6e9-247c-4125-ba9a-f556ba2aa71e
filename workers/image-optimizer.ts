/**
 * Cloudflare Workers Image Optimization Service
 * Provides dynamic image resizing, format conversion, and caching
 */

import { ImageCacheManager, CacheKeyUtils, type CacheMetadata } from './lib/cache-manager'

// Worker environment interface
interface Env {
  // KV Namespaces
  IMAGE_CACHE_KV: KVNamespace
  IMAGE_METADATA_KV: KVNamespace

  // R2 Storage
  R2_IMAGES: R2Bucket

  // Environment Variables
  CLOUDFLARE_IMAGES_ACCOUNT_ID: string
  CLOUDFLARE_IMAGES_API_TOKEN: string
  R2_ACCOUNT_ID: string
  R2_ACCESS_KEY_ID: string
  R2_SECRET_ACCESS_KEY: string
  ENVIRONMENT: string
}

// Image transformation parameters
interface ImageTransformParams {
  width?: number
  height?: number
  quality?: number
  format?: 'webp' | 'avif' | 'jpeg' | 'png' | 'auto'
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside'
  gravity?: 'center' | 'north' | 'south' | 'east' | 'west' | 'northeast' | 'northwest' | 'southeast' | 'southwest'
  background?: string
  blur?: number
  sharpen?: number
}

// Cache metadata interface
interface CacheMetadata {
  originalSize: number
  optimizedSize: number
  format: string
  transformations: ImageTransformParams
  timestamp: number
  hitCount: number
}

// Performance metrics interface
interface PerformanceMetrics {
  transformationTime: number
  cacheHit: boolean
  originalSize: number
  optimizedSize: number
  compressionRatio: number
  timestamp: number
}

// Error types for better error handling
enum ImageErrorType {
  NOT_FOUND = 'NOT_FOUND',
  INVALID_FORMAT = 'INVALID_FORMAT',
  TOO_LARGE = 'TOO_LARGE',
  OPTIMIZATION_FAILED = 'OPTIMIZATION_FAILED',
  CACHE_ERROR = 'CACHE_ERROR',
  R2_ERROR = 'R2_ERROR',
  CLOUDFLARE_IMAGES_ERROR = 'CLOUDFLARE_IMAGES_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR'
}

// Custom error class for image processing
class ImageProcessingError extends Error {
  constructor(
    public type: ImageErrorType,
    message: string,
    public statusCode: number = 500,
    public retryable: boolean = false
  ) {
    super(message)
    this.name = 'ImageProcessingError'
  }
}

class ImageOptimizer {
  private env: Env
  private cacheManager: ImageCacheManager
  private maxImageSize = 10 * 1024 * 1024 // 10MB
  private allowedFormats = ['jpeg', 'jpg', 'png', 'webp', 'avif']
  private defaultQuality = 85
  private cacheMaxAge = 31536000 // 1 year
  private maxTransformationTime = 30000 // 30 seconds

  constructor(env: Env) {
    this.env = env
    this.cacheManager = new ImageCacheManager(env.IMAGE_CACHE_KV, env.IMAGE_METADATA_KV, {
      defaultTTL: this.cacheMaxAge,
      enableCompression: true,
      enableMetrics: true
    })
  }

  /**
   * Handle incoming image optimization request with comprehensive error handling
   */
  async handleRequest(request: Request): Promise<Response> {
    const startTime = Date.now()
    let imagePath = ''

    try {
      // Parse URL and extract parameters
      const url = new URL(request.url)
      const { imagePath: parsedPath, transformParams } = this.parseImageUrl(url.pathname)
      imagePath = parsedPath

      // Validate parameters
      const validationError = this.validateParameters(transformParams)
      if (validationError) {
        throw new ImageProcessingError(ImageErrorType.VALIDATION_ERROR, validationError, 400)
      }

      // Generate cache key
      const cacheKey = CacheKeyUtils.generateKey(imagePath, transformParams)

      // Check cache first with error handling
      const cachedEntry = await this.getCachedImageSafely(cacheKey)
      if (cachedEntry) {
        await this.recordMetrics({
          transformationTime: Date.now() - startTime,
          cacheHit: true,
          originalSize: cachedEntry.metadata.originalSize,
          optimizedSize: cachedEntry.metadata.optimizedSize,
          compressionRatio: cachedEntry.metadata.optimizedSize / cachedEntry.metadata.originalSize,
          timestamp: Date.now()
        })

        return this.createImageResponse(cachedEntry.data, cachedEntry.metadata, true)
      }

      // Fetch source image from R2 with fallbacks
      const sourceImage = await this.fetchSourceImageWithFallbacks(imagePath)
      if (!sourceImage) {
        throw new ImageProcessingError(ImageErrorType.NOT_FOUND, 'Image not found', 404)
      }

      // Validate image size
      if (sourceImage.size > this.maxImageSize) {
        throw new ImageProcessingError(ImageErrorType.TOO_LARGE, 'Image too large', 413)
      }

      // Optimize image with fallbacks
      const optimizedResult = await this.optimizeImageWithFallbacks(sourceImage, transformParams)
      if (!optimizedResult) {
        throw new ImageProcessingError(ImageErrorType.OPTIMIZATION_FAILED, 'Image optimization failed', 500, true)
      }

      // Cache the result with error handling
      await this.cacheResultSafely(cacheKey, optimizedResult, sourceImage.size, transformParams)

      // Record metrics
      await this.recordMetrics({
        transformationTime: Date.now() - startTime,
        cacheHit: false,
        originalSize: sourceImage.size,
        optimizedSize: optimizedResult.size,
        compressionRatio: optimizedResult.size / sourceImage.size,
        timestamp: Date.now()
      })

      return this.createImageResponse(optimizedResult.image, {
        originalSize: sourceImage.size,
        optimizedSize: optimizedResult.size,
        format: optimizedResult.format,
        transformations: transformParams,
        timestamp: Date.now(),
        hitCount: 0,
        contentType: this.getContentType(optimizedResult.format),
        etag: this.generateETag(optimizedResult.image)
      }, false)

    } catch (error) {
      return await this.handleError(error, imagePath, startTime)
    }
  }

  /**
   * Comprehensive error handler with fallbacks
   */
  private async handleError(error: any, imagePath: string, startTime: number): Promise<Response> {
    console.error('Image optimization error:', error)

    // Record error metrics
    await this.recordErrorMetrics(error, Date.now() - startTime)

    if (error instanceof ImageProcessingError) {
      // Handle specific image processing errors
      switch (error.type) {
        case ImageErrorType.NOT_FOUND:
          return await this.handleNotFoundError(imagePath)

        case ImageErrorType.TOO_LARGE:
          return this.createErrorResponse(413, 'Image too large. Maximum size is 10MB.')

        case ImageErrorType.INVALID_FORMAT:
          return this.createErrorResponse(400, 'Unsupported image format.')

        case ImageErrorType.VALIDATION_ERROR:
          return this.createErrorResponse(400, error.message)

        case ImageErrorType.OPTIMIZATION_FAILED:
          return await this.handleOptimizationFailure(imagePath)

        case ImageErrorType.TIMEOUT_ERROR:
          return this.createErrorResponse(504, 'Request timeout. Please try again.')

        default:
          return await this.handleGenericError(imagePath)
      }
    }

    // Handle unexpected errors
    return await this.handleGenericError(imagePath)
  }

  /**
   * Parse image URL and extract path and transformation parameters
   */
  private parseImageUrl(pathname: string): { imagePath: string; transformParams: ImageTransformParams } {
    // URL format: /{transformation_params}/{image_path}
    const parts = pathname.split('/')

    if (parts.length < 3) {
      return { imagePath: pathname, transformParams: {} }
    }

    const transformString = parts[1]
    const imagePath = '/' + parts.slice(2).join('/')

    const transformParams: ImageTransformParams = {}

    if (transformString) {
      const params = transformString.split(',')

      for (const param of params) {
        const [key, value] = param.split('=')

        switch (key) {
          case 'w':
            transformParams.width = parseInt(value)
            break
          case 'h':
            transformParams.height = parseInt(value)
            break
          case 'q':
            transformParams.quality = parseInt(value)
            break
          case 'f':
            transformParams.format = value as any
            break
          case 'fit':
            transformParams.fit = value as any
            break
          case 'g':
            transformParams.gravity = value as any
            break
          case 'bg':
            transformParams.background = value
            break
          case 'blur':
            transformParams.blur = parseInt(value)
            break
          case 'sharpen':
            transformParams.sharpen = parseInt(value)
            break
        }
      }
    }

    return { imagePath, transformParams }
  }

  /**
   * Validate transformation parameters
   */
  private validateParameters(params: ImageTransformParams): string | null {
    if (params.width && (params.width < 1 || params.width > 2048)) {
      return 'Width must be between 1 and 2048 pixels'
    }

    if (params.height && (params.height < 1 || params.height > 2048)) {
      return 'Height must be between 1 and 2048 pixels'
    }

    if (params.quality && (params.quality < 1 || params.quality > 100)) {
      return 'Quality must be between 1 and 100'
    }

    if (params.format && !['webp', 'avif', 'jpeg', 'png', 'auto'].includes(params.format)) {
      return 'Invalid format. Supported: webp, avif, jpeg, png, auto'
    }

    if (params.blur && (params.blur < 0 || params.blur > 250)) {
      return 'Blur must be between 0 and 250'
    }

    if (params.sharpen && (params.sharpen < 0 || params.sharpen > 10)) {
      return 'Sharpen must be between 0 and 10'
    }

    return null
  }

  /**
   * Invalidate cache for specific image
   */
  async invalidateImageCache(imagePath: string): Promise<number> {
    const pattern = CacheKeyUtils.generatePattern(imagePath)
    return await this.cacheManager.invalidatePattern(pattern)
  }

  /**
   * Get cache statistics
   */
  async getCacheStats() {
    return await this.cacheManager.getStats()
  }

  /**
   * Handle not found error with placeholder fallback
   */
  private async handleNotFoundError(imagePath: string): Promise<Response> {
    // Try to serve a placeholder image
    const placeholderResponse = await this.getPlaceholderImage()
    if (placeholderResponse) {
      return placeholderResponse
    }

    return this.createErrorResponse(404, 'Image not found')
  }

  /**
   * Handle optimization failure with original image fallback
   */
  private async handleOptimizationFailure(imagePath: string): Promise<Response> {
    try {
      // Try to serve the original image without optimization
      const sourceImage = await this.fetchSourceImageWithFallbacks(imagePath)
      if (sourceImage) {
        return new Response(sourceImage.data, {
          headers: {
            'Content-Type': sourceImage.contentType,
            'Cache-Control': 'public, max-age=3600', // Shorter cache for fallback
            'X-Fallback': 'original-image',
            'X-Optimization-Failed': 'true'
          }
        })
      }
    } catch (error) {
      console.error('Original image fallback failed:', error)
    }

    return this.createErrorResponse(500, 'Image optimization failed')
  }

  /**
   * Handle generic errors
   */
  private async handleGenericError(imagePath: string): Response {
    // Try placeholder as last resort
    const placeholderResponse = await this.getPlaceholderImage()
    if (placeholderResponse) {
      return placeholderResponse
    }

    return this.createErrorResponse(500, 'Internal server error')
  }

  /**
   * Get placeholder image for error cases
   */
  private async getPlaceholderImage(): Promise<Response | null> {
    try {
      // Try to get a placeholder image from R2
      const placeholderKey = 'placeholders/image-not-found.png'
      const placeholder = await this.env.R2_IMAGES.get(placeholderKey)

      if (placeholder) {
        const data = await placeholder.arrayBuffer()
        return new Response(data, {
          headers: {
            'Content-Type': 'image/png',
            'Cache-Control': 'public, max-age=86400', // Cache placeholder for 24 hours
            'X-Placeholder': 'true'
          }
        })
      }

      return null
    } catch (error) {
      console.error('Placeholder image fetch failed:', error)
      return null
    }
  }

  /**
   * Record error metrics
   */
  private async recordErrorMetrics(error: any, processingTime: number): Promise<void> {
    try {
      const errorType = error instanceof ImageProcessingError ? error.type : 'UNKNOWN'
      const date = new Date().toISOString().split('T')[0]
      const hour = new Date().getHours()
      const errorKey = `errors:${date}:${hour}`

      const errorMetric = {
        type: errorType,
        message: error.message,
        statusCode: error.statusCode || 500,
        processingTime,
        timestamp: Date.now(),
        retryable: error.retryable || false
      }

      // Get existing error metrics
      const existingErrors = await this.env.IMAGE_METADATA_KV.get(errorKey, 'json') || []
      const errorsArray = Array.isArray(existingErrors) ? existingErrors : []

      // Add new error
      errorsArray.push(errorMetric)

      // Keep only last 50 errors per hour
      if (errorsArray.length > 50) {
        errorsArray.splice(0, errorsArray.length - 50)
      }

      await this.env.IMAGE_METADATA_KV.put(errorKey, JSON.stringify(errorsArray), {
        expirationTtl: 7 * 24 * 60 * 60 // 7 days
      })
    } catch (metricsError) {
      console.error('Error metrics recording failed:', metricsError)
    }
  }

  /**
   * Safely get cached image with error handling
   */
  private async getCachedImageSafely(cacheKey: string) {
    try {
      return await this.cacheManager.get(cacheKey)
    } catch (error) {
      console.error('Cache retrieval error:', error)
      return null
    }
  }

  /**
   * Fetch source image from R2 storage with fallbacks
   */
  private async fetchSourceImageWithFallbacks(imagePath: string): Promise<{ data: ArrayBuffer; size: number; contentType: string } | null> {
    try {
      // Remove leading slash for R2 key
      const key = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath

      const object = await this.env.R2_IMAGES.get(key)
      if (!object) {
        throw new ImageProcessingError(ImageErrorType.NOT_FOUND, `Image not found: ${imagePath}`, 404)
      }

      const data = await object.arrayBuffer()

      return {
        data,
        size: data.byteLength,
        contentType: object.httpMetadata?.contentType || 'image/jpeg'
      }
    } catch (error) {
      if (error instanceof ImageProcessingError) {
        throw error
      }

      console.error('R2 fetch error:', error)
      throw new ImageProcessingError(ImageErrorType.R2_ERROR, 'Failed to fetch image from storage', 500, true)
    }
  }

  /**
   * Safely cache optimization result
   */
  private async cacheResultSafely(
    cacheKey: string,
    optimizedResult: { image: ArrayBuffer; size: number; format: string },
    originalSize: number,
    transformParams: ImageTransformParams
  ): Promise<void> {
    try {
      await this.cacheManager.set(cacheKey, optimizedResult.image, {
        originalSize,
        optimizedSize: optimizedResult.size,
        format: optimizedResult.format,
        transformations: transformParams,
        contentType: this.getContentType(optimizedResult.format),
        etag: this.generateETag(optimizedResult.image)
      })
    } catch (error) {
      console.error('Cache storage error:', error)
      // Don't throw - caching failure shouldn't break the response
    }
  }

  /**
   * Optimize image with comprehensive fallback strategy
   */
  private async optimizeImageWithFallbacks(
    sourceImage: { data: ArrayBuffer; size: number; contentType: string },
    params: ImageTransformParams
  ): Promise<{ image: ArrayBuffer; size: number; format: string } | null> {
    const fallbackChain = [
      () => this.optimizeWithCloudflareImages(sourceImage, params),
      () => this.basicOptimization(sourceImage, params),
      () => this.returnOriginalImage(sourceImage, params)
    ]

    for (const [index, optimizationMethod] of fallbackChain.entries()) {
      try {
        // Skip Cloudflare Images if not configured
        if (index === 0 && (!this.env.CLOUDFLARE_IMAGES_ACCOUNT_ID || !this.env.CLOUDFLARE_IMAGES_API_TOKEN)) {
          continue
        }

        const result = await optimizationMethod()
        if (result) {
          return result
        }
      } catch (error) {
        console.error(`Optimization method ${index} failed:`, error)

        // If this is the last method, throw the error
        if (index === fallbackChain.length - 1) {
          throw new ImageProcessingError(ImageErrorType.OPTIMIZATION_FAILED, 'All optimization methods failed', 500)
        }

        // Continue to next fallback method
        continue
      }
    }

    throw new ImageProcessingError(ImageErrorType.OPTIMIZATION_FAILED, 'No optimization method succeeded', 500)
  }

  /**
   * Return original image as final fallback
   */
  private async returnOriginalImage(
    sourceImage: { data: ArrayBuffer; size: number; contentType: string },
    params: ImageTransformParams
  ): Promise<{ image: ArrayBuffer; size: number; format: string }> {
    return {
      image: sourceImage.data,
      size: sourceImage.size,
      format: this.determineOutputFormat(params.format, sourceImage.contentType)
    }
  }

  /**
   * Optimize image using Cloudflare Images service
   */
  private async optimizeWithCloudflareImages(
    sourceImage: { data: ArrayBuffer; size: number; contentType: string },
    params: ImageTransformParams
  ): Promise<{ image: ArrayBuffer; size: number; format: string } | null> {
    try {
      // Upload to Cloudflare Images if not already there
      const imageId = await this.uploadToCloudflareImages(sourceImage)

      // Build transformation URL
      const transformations = this.buildCloudflareTransformations(params)
      const optimizedUrl = `https://imagedelivery.net/${this.env.CLOUDFLARE_IMAGES_ACCOUNT_ID}/${imageId}/${transformations}`

      // Fetch optimized image
      const response = await fetch(optimizedUrl)

      if (!response.ok) {
        throw new Error(`Cloudflare Images optimization failed: ${response.status}`)
      }

      const optimizedData = await response.arrayBuffer()
      const outputFormat = this.determineOutputFormat(params.format, response.headers.get('Content-Type') || 'image/jpeg')

      return {
        image: optimizedData,
        size: optimizedData.byteLength,
        format: outputFormat
      }
    } catch (error) {
      console.error('Cloudflare Images optimization error:', error)
      // Fallback to basic optimization
      return await this.basicOptimization(sourceImage, params)
    }
  }

  /**
   * Upload image to Cloudflare Images
   */
  private async uploadToCloudflareImages(
    sourceImage: { data: ArrayBuffer; size: number; contentType: string }
  ): Promise<string> {
    const formData = new FormData()
    formData.append('file', new Blob([sourceImage.data], { type: sourceImage.contentType }))

    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${this.env.CLOUDFLARE_IMAGES_ACCOUNT_ID}/images/v1`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.env.CLOUDFLARE_IMAGES_API_TOKEN}`
        },
        body: formData
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Failed to upload to Cloudflare Images: ${response.status} - ${errorText}`)
    }

    const result = await response.json() as any
    return result.result.id
  }

  /**
   * Build transformation string for Cloudflare Images
   */
  private buildCloudflareTransformations(params: ImageTransformParams): string {
    const transformations: string[] = []

    if (params.width) transformations.push(`w=${params.width}`)
    if (params.height) transformations.push(`h=${params.height}`)
    if (params.quality) transformations.push(`q=${params.quality}`)
    if (params.format && params.format !== 'auto') transformations.push(`f=${params.format}`)
    if (params.fit) transformations.push(`fit=${params.fit}`)
    if (params.gravity) transformations.push(`gravity=${params.gravity}`)
    if (params.background) transformations.push(`background=${params.background}`)
    if (params.blur) transformations.push(`blur=${params.blur}`)
    if (params.sharpen) transformations.push(`sharpen=${params.sharpen}`)

    return transformations.join(',') || 'public'
  }

  /**
   * Basic image optimization without Cloudflare Images
   */
  private async basicOptimization(
    sourceImage: { data: ArrayBuffer; size: number; contentType: string },
    params: ImageTransformParams
  ): Promise<{ image: ArrayBuffer; size: number; format: string }> {
    // For basic optimization, we'll return the original with proper format detection
    // In a production environment, you might want to integrate with other image processing libraries

    const outputFormat = this.determineOutputFormat(params.format, sourceImage.contentType)

    return {
      image: sourceImage.data,
      size: sourceImage.size,
      format: outputFormat
    }
  }

  /**
   * Determine output format based on format parameter and original image
   */
  private determineOutputFormat(requestedFormat?: string, originalContentType?: string): string {
    if (requestedFormat === 'webp') return 'webp'
    if (requestedFormat === 'avif') return 'avif'
    if (requestedFormat === 'png') return 'png'
    if (requestedFormat === 'jpeg') return 'jpeg'

    // Auto format detection
    if (requestedFormat === 'auto') {
      // Prefer WebP for better compression
      return 'webp'
    }

    // Extract format from content type
    if (originalContentType?.includes('webp')) return 'webp'
    if (originalContentType?.includes('avif')) return 'avif'
    if (originalContentType?.includes('png')) return 'png'

    // Default to JPEG
    return 'jpeg'
  }



  /**
   * Record performance metrics
   */
  private async recordMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      const date = new Date().toISOString().split('T')[0]
      const hour = new Date().getHours()
      const metricsKey = `metrics:${date}:${hour}`

      // Get existing metrics for this hour
      const existingMetrics = await this.env.IMAGE_METADATA_KV.get(metricsKey, 'json') || []
      const metricsArray = Array.isArray(existingMetrics) ? existingMetrics : []

      // Add new metrics
      metricsArray.push(metrics)

      // Keep only last 100 entries per hour to manage storage
      if (metricsArray.length > 100) {
        metricsArray.splice(0, metricsArray.length - 100)
      }

      await this.env.IMAGE_METADATA_KV.put(metricsKey, JSON.stringify(metricsArray), {
        expirationTtl: 30 * 24 * 60 * 60 // 30 days
      })
    } catch (error) {
      console.error('Metrics recording error:', error)
    }
  }

  /**
   * Create image response with proper headers
   */
  private createImageResponse(
    imageData: ArrayBuffer,
    metadata: CacheMetadata,
    fromCache: boolean
  ): Response {
    const headers = new Headers({
      'Content-Type': this.getContentType(metadata.format),
      'Content-Length': imageData.byteLength.toString(),
      'Cache-Control': `public, max-age=${this.cacheMaxAge}, immutable`,
      'ETag': `"${this.generateETag(imageData)}"`,
      'X-Image-Optimized': 'true',
      'X-Cache-Status': fromCache ? 'HIT' : 'MISS',
      'X-Original-Size': metadata.originalSize.toString(),
      'X-Optimized-Size': metadata.optimizedSize.toString(),
      'X-Compression-Ratio': (metadata.optimizedSize / metadata.originalSize).toFixed(2),
      'Vary': 'Accept',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    })

    return new Response(imageData, {
      status: 200,
      headers
    })
  }

  /**
   * Create error response
   */
  private createErrorResponse(status: number, message: string): Response {
    return new Response(JSON.stringify({ error: message }), {
      status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    })
  }

  /**
   * Get content type for format
   */
  private getContentType(format: string): string {
    const contentTypes = {
      'webp': 'image/webp',
      'avif': 'image/avif',
      'jpeg': 'image/jpeg',
      'jpg': 'image/jpeg',
      'png': 'image/png'
    }

    return contentTypes[format] || 'image/jpeg'
  }

  /**
   * Generate ETag for image data
   */
  private generateETag(data: ArrayBuffer): string {
    // Simple hash based on data length and first/last bytes
    const view = new Uint8Array(data)
    const hash = view.length + (view[0] || 0) + (view[view.length - 1] || 0)
    return hash.toString(36)
  }
}

/**
 * Worker fetch event handler
 */
export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Max-Age': '86400'
        }
      })
    }

    // Only handle GET and HEAD requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      return new Response('Method not allowed', { status: 405 })
    }

    const optimizer = new ImageOptimizer(env)
    return await optimizer.handleRequest(request)
  }
}
