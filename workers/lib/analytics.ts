/**
 * Cache Analytics and Monitoring System
 * Provides comprehensive metrics collection, performance tracking,
 * and real-time monitoring capabilities
 */

export interface CacheMetrics {
  // Performance metrics
  totalRequests: number
  cacheHits: number
  cacheMisses: number
  staleHits: number
  bypassed: number
  errors: number
  
  // Response time metrics
  averageResponseTime: number
  p50ResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  
  // Cache efficiency
  hitRatio: number
  missRatio: number
  staleRatio: number
  bypassRatio: number
  
  // Size metrics
  totalCacheSize: number
  averageEntrySize: number
  largestEntry: number
  
  // Endpoint metrics
  topEndpoints: Array<{ endpoint: string; requests: number; hitRatio: number }>
  slowestEndpoints: Array<{ endpoint: string; averageTime: number }>
  
  // Time-based metrics
  requestsPerHour: number
  peakHour: { hour: number; requests: number }
  
  // Error metrics
  errorRate: number
  topErrors: Array<{ error: string; count: number }>
}

export interface PerformanceMetrics {
  timestamp: number
  endpoint: string
  method: string
  cacheStatus: 'HIT' | 'MISS' | 'STALE' | 'BYPASS' | 'ERROR'
  responseTime: number
  cacheAge?: number
  entrySize?: number
  userType: string
  region?: string
  userAgent?: string
  rateLimited: boolean
}

export interface AlertRule {
  name: string
  metric: string
  threshold: number
  operator: 'gt' | 'lt' | 'eq'
  window: number // seconds
  enabled: boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface Alert {
  id: string
  rule: AlertRule
  value: number
  timestamp: number
  resolved: boolean
  resolvedAt?: number
}

export class CacheAnalytics {
  private env: any
  private alertRules: AlertRule[]

  constructor(env: any) {
    this.env = env
    this.initializeAlertRules()
  }

  /**
   * Record performance metrics
   */
  async recordMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      // Store detailed metrics
      await this.storeDetailedMetrics(metrics)
      
      // Update aggregated metrics
      await this.updateAggregatedMetrics(metrics)
      
      // Check alert rules
      await this.checkAlertRules(metrics)
      
    } catch (error) {
      console.error('Metrics recording error:', error)
    }
  }

  /**
   * Get comprehensive cache metrics
   */
  async getCacheMetrics(timeWindow: number = 3600): Promise<CacheMetrics> {
    try {
      const now = Date.now()
      const windowStart = now - (timeWindow * 1000)
      
      // Get aggregated metrics from multiple time buckets
      const buckets = this.getTimeBuckets(windowStart, now, 300) // 5-minute buckets
      const bucketData = await Promise.all(
        buckets.map(bucket => this.getBucketMetrics(bucket))
      )

      // Aggregate all bucket data
      return this.aggregateBucketData(bucketData, timeWindow)

    } catch (error) {
      console.error('Cache metrics error:', error)
      return this.getEmptyMetrics()
    }
  }

  /**
   * Get real-time performance dashboard data
   */
  async getDashboardData(): Promise<any> {
    try {
      const [
        currentMetrics,
        hourlyMetrics,
        alerts,
        topEndpoints,
        recentErrors
      ] = await Promise.all([
        this.getCacheMetrics(300), // Last 5 minutes
        this.getCacheMetrics(3600), // Last hour
        this.getActiveAlerts(),
        this.getTopEndpoints(24 * 3600), // Last 24 hours
        this.getRecentErrors(3600) // Last hour
      ])

      return {
        current: currentMetrics,
        hourly: hourlyMetrics,
        alerts,
        topEndpoints,
        recentErrors,
        timestamp: Date.now()
      }

    } catch (error) {
      console.error('Dashboard data error:', error)
      return { error: 'Failed to get dashboard data' }
    }
  }

  /**
   * Get endpoint-specific analytics
   */
  async getEndpointAnalytics(endpoint: string, timeWindow: number = 3600): Promise<any> {
    try {
      const now = Date.now()
      const windowStart = now - (timeWindow * 1000)
      
      const analyticsKey = `endpoint_analytics:${this.hashEndpoint(endpoint)}`
      const data = await this.env.API_METADATA_KV.get(analyticsKey, 'json') || []
      
      // Filter data by time window
      const filteredData = data.filter((item: any) => 
        item.timestamp >= windowStart && item.timestamp <= now
      )

      return this.analyzeEndpointData(filteredData)

    } catch (error) {
      console.error('Endpoint analytics error:', error)
      return { error: 'Failed to get endpoint analytics' }
    }
  }

  /**
   * Store detailed metrics
   */
  private async storeDetailedMetrics(metrics: PerformanceMetrics): Promise<void> {
    const bucket = Math.floor(metrics.timestamp / 300000) * 300000 // 5-minute buckets
    const bucketKey = `metrics:${bucket}`
    
    const existing = await this.env.API_METADATA_KV.get(bucketKey, 'json') || []
    existing.push(metrics)
    
    // Keep only last 1000 entries per bucket
    if (existing.length > 1000) {
      existing.splice(0, existing.length - 1000)
    }
    
    await this.env.API_METADATA_KV.put(bucketKey, JSON.stringify(existing), {
      expirationTtl: 7 * 24 * 3600 // 7 days
    })
  }

  /**
   * Update aggregated metrics
   */
  private async updateAggregatedMetrics(metrics: PerformanceMetrics): Promise<void> {
    const hour = Math.floor(metrics.timestamp / 3600000) * 3600000
    const aggregateKey = `aggregate:${hour}`
    
    const existing = await this.env.API_METADATA_KV.get(aggregateKey, 'json') || {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      staleHits: 0,
      bypassed: 0,
      errors: 0,
      totalResponseTime: 0,
      responseTimes: [],
      endpoints: {},
      userTypes: {},
      regions: {}
    }

    // Update counters
    existing.totalRequests++
    existing.totalResponseTime += metrics.responseTime
    existing.responseTimes.push(metrics.responseTime)
    
    // Update cache status counters
    switch (metrics.cacheStatus) {
      case 'HIT':
        existing.cacheHits++
        break
      case 'MISS':
        existing.cacheMisses++
        break
      case 'STALE':
        existing.staleHits++
        break
      case 'BYPASS':
        existing.bypassed++
        break
      case 'ERROR':
        existing.errors++
        break
    }

    // Update endpoint metrics
    if (!existing.endpoints[metrics.endpoint]) {
      existing.endpoints[metrics.endpoint] = {
        requests: 0,
        hits: 0,
        totalTime: 0
      }
    }
    existing.endpoints[metrics.endpoint].requests++
    existing.endpoints[metrics.endpoint].totalTime += metrics.responseTime
    if (metrics.cacheStatus === 'HIT') {
      existing.endpoints[metrics.endpoint].hits++
    }

    // Update user type metrics
    existing.userTypes[metrics.userType] = (existing.userTypes[metrics.userType] || 0) + 1

    // Update region metrics
    if (metrics.region) {
      existing.regions[metrics.region] = (existing.regions[metrics.region] || 0) + 1
    }

    // Keep response times array manageable
    if (existing.responseTimes.length > 10000) {
      existing.responseTimes = existing.responseTimes.slice(-5000)
    }

    await this.env.API_METADATA_KV.put(aggregateKey, JSON.stringify(existing), {
      expirationTtl: 30 * 24 * 3600 // 30 days
    })
  }

  /**
   * Check alert rules
   */
  private async checkAlertRules(metrics: PerformanceMetrics): Promise<void> {
    for (const rule of this.alertRules) {
      if (!rule.enabled) continue

      try {
        const value = await this.calculateMetricValue(rule.metric, rule.window)
        const shouldAlert = this.evaluateAlertCondition(value, rule.threshold, rule.operator)

        if (shouldAlert) {
          await this.triggerAlert(rule, value)
        }
      } catch (error) {
        console.error(`Alert rule evaluation error for ${rule.name}:`, error)
      }
    }
  }

  /**
   * Calculate metric value for alert rules
   */
  private async calculateMetricValue(metric: string, window: number): Promise<number> {
    const now = Date.now()
    const windowStart = now - (window * 1000)
    
    switch (metric) {
      case 'hit_ratio':
        const hitRatio = await this.calculateHitRatio(windowStart, now)
        return hitRatio
      
      case 'response_time_p95':
        const p95 = await this.calculatePercentile(windowStart, now, 95)
        return p95
      
      case 'error_rate':
        const errorRate = await this.calculateErrorRate(windowStart, now)
        return errorRate
      
      case 'requests_per_minute':
        const rpm = await this.calculateRequestsPerMinute(windowStart, now)
        return rpm
      
      default:
        return 0
    }
  }

  /**
   * Evaluate alert condition
   */
  private evaluateAlertCondition(value: number, threshold: number, operator: string): boolean {
    switch (operator) {
      case 'gt':
        return value > threshold
      case 'lt':
        return value < threshold
      case 'eq':
        return value === threshold
      default:
        return false
    }
  }

  /**
   * Trigger alert
   */
  private async triggerAlert(rule: AlertRule, value: number): Promise<void> {
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      rule,
      value,
      timestamp: Date.now(),
      resolved: false
    }

    const alertsKey = `alerts:${Math.floor(Date.now() / 3600000)}`
    const existing = await this.env.API_METADATA_KV.get(alertsKey, 'json') || []
    existing.push(alert)

    await this.env.API_METADATA_KV.put(alertsKey, JSON.stringify(existing), {
      expirationTtl: 7 * 24 * 3600 // 7 days
    })

    // Log alert
    console.warn(`ALERT: ${rule.name} - ${rule.metric} is ${value} (threshold: ${rule.threshold})`)
  }

  /**
   * Get active alerts
   */
  private async getActiveAlerts(): Promise<Alert[]> {
    try {
      const now = Date.now()
      const hoursToCheck = 24 // Check last 24 hours
      const alerts: Alert[] = []

      for (let i = 0; i < hoursToCheck; i++) {
        const hour = Math.floor((now - i * 3600000) / 3600000)
        const alertsKey = `alerts:${hour}`
        const hourAlerts = await this.env.API_METADATA_KV.get(alertsKey, 'json') || []
        alerts.push(...hourAlerts.filter((alert: Alert) => !alert.resolved))
      }

      return alerts.sort((a, b) => b.timestamp - a.timestamp)
    } catch (error) {
      console.error('Get active alerts error:', error)
      return []
    }
  }

  /**
   * Initialize alert rules
   */
  private initializeAlertRules(): void {
    this.alertRules = [
      {
        name: 'Low Cache Hit Ratio',
        metric: 'hit_ratio',
        threshold: 0.8,
        operator: 'lt',
        window: 600, // 10 minutes
        enabled: true,
        severity: 'medium'
      },
      {
        name: 'High Response Time P95',
        metric: 'response_time_p95',
        threshold: 2000, // 2 seconds
        operator: 'gt',
        window: 300, // 5 minutes
        enabled: true,
        severity: 'high'
      },
      {
        name: 'High Error Rate',
        metric: 'error_rate',
        threshold: 0.05, // 5%
        operator: 'gt',
        window: 300, // 5 minutes
        enabled: true,
        severity: 'critical'
      },
      {
        name: 'High Request Rate',
        metric: 'requests_per_minute',
        threshold: 1000,
        operator: 'gt',
        window: 60, // 1 minute
        enabled: true,
        severity: 'medium'
      }
    ]
  }

  /**
   * Helper methods for calculations
   */
  private getTimeBuckets(start: number, end: number, bucketSize: number): number[] {
    const buckets: number[] = []
    for (let time = start; time < end; time += bucketSize * 1000) {
      buckets.push(Math.floor(time / (bucketSize * 1000)) * (bucketSize * 1000))
    }
    return buckets
  }

  private async getBucketMetrics(bucket: number): Promise<any> {
    const bucketKey = `metrics:${bucket}`
    return await this.env.API_METADATA_KV.get(bucketKey, 'json') || []
  }

  private aggregateBucketData(bucketData: any[], timeWindow: number): CacheMetrics {
    // Implementation for aggregating bucket data into comprehensive metrics
    // This would calculate all the metrics defined in CacheMetrics interface
    const allMetrics = bucketData.flat()
    
    const totalRequests = allMetrics.length
    const cacheHits = allMetrics.filter(m => m.cacheStatus === 'HIT').length
    const cacheMisses = allMetrics.filter(m => m.cacheStatus === 'MISS').length
    const staleHits = allMetrics.filter(m => m.cacheStatus === 'STALE').length
    const bypassed = allMetrics.filter(m => m.cacheStatus === 'BYPASS').length
    const errors = allMetrics.filter(m => m.cacheStatus === 'ERROR').length

    const responseTimes = allMetrics.map(m => m.responseTime).sort((a, b) => a - b)
    
    return {
      totalRequests,
      cacheHits,
      cacheMisses,
      staleHits,
      bypassed,
      errors,
      averageResponseTime: responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
      p50ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.5)] || 0,
      p95ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.95)] || 0,
      p99ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.99)] || 0,
      hitRatio: totalRequests > 0 ? cacheHits / totalRequests : 0,
      missRatio: totalRequests > 0 ? cacheMisses / totalRequests : 0,
      staleRatio: totalRequests > 0 ? staleHits / totalRequests : 0,
      bypassRatio: totalRequests > 0 ? bypassed / totalRequests : 0,
      totalCacheSize: 0, // Would need to calculate from cache entries
      averageEntrySize: 0,
      largestEntry: 0,
      topEndpoints: [],
      slowestEndpoints: [],
      requestsPerHour: totalRequests * (3600 / timeWindow),
      peakHour: { hour: 0, requests: 0 },
      errorRate: totalRequests > 0 ? errors / totalRequests : 0,
      topErrors: []
    }
  }

  private getEmptyMetrics(): CacheMetrics {
    return {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      staleHits: 0,
      bypassed: 0,
      errors: 0,
      averageResponseTime: 0,
      p50ResponseTime: 0,
      p95ResponseTime: 0,
      p99ResponseTime: 0,
      hitRatio: 0,
      missRatio: 0,
      staleRatio: 0,
      bypassRatio: 0,
      totalCacheSize: 0,
      averageEntrySize: 0,
      largestEntry: 0,
      topEndpoints: [],
      slowestEndpoints: [],
      requestsPerHour: 0,
      peakHour: { hour: 0, requests: 0 },
      errorRate: 0,
      topErrors: []
    }
  }

  private hashEndpoint(endpoint: string): string {
    // Simple hash function for endpoint names
    let hash = 0
    for (let i = 0; i < endpoint.length; i++) {
      const char = endpoint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }

  private analyzeEndpointData(data: any[]): any {
    // Analyze endpoint-specific data
    return {
      totalRequests: data.length,
      averageResponseTime: data.reduce((sum, item) => sum + item.responseTime, 0) / data.length || 0,
      cacheHitRatio: data.filter(item => item.cacheStatus === 'HIT').length / data.length || 0,
      timeline: data.map(item => ({
        timestamp: item.timestamp,
        responseTime: item.responseTime,
        cacheStatus: item.cacheStatus
      }))
    }
  }

  private async calculateHitRatio(start: number, end: number): Promise<number> {
    // Implementation for calculating hit ratio in time window
    return 0.85 // Placeholder
  }

  private async calculatePercentile(start: number, end: number, percentile: number): Promise<number> {
    // Implementation for calculating response time percentiles
    return 500 // Placeholder
  }

  private async calculateErrorRate(start: number, end: number): Promise<number> {
    // Implementation for calculating error rate
    return 0.01 // Placeholder
  }

  private async calculateRequestsPerMinute(start: number, end: number): Promise<number> {
    // Implementation for calculating requests per minute
    return 100 // Placeholder
  }

  private async getTopEndpoints(timeWindow: number): Promise<any[]> {
    // Implementation for getting top endpoints by traffic
    return []
  }

  private async getRecentErrors(timeWindow: number): Promise<any[]> {
    // Implementation for getting recent errors
    return []
  }
}
