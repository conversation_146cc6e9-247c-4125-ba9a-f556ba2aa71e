/**
 * Intelligent Cache Invalidation System
 * Provides tag-based, pattern-based, and time-based cache invalidation
 * with stale-while-revalidate and background refresh capabilities
 */

export interface InvalidationRequest {
  type: 'tag' | 'pattern' | 'key' | 'all'
  value?: string
  tags?: string[]
  pattern?: string
  key?: string
  immediate?: boolean
  backgroundRefresh?: boolean
}

export interface InvalidationResult {
  success: boolean
  invalidatedCount: number
  errors: string[]
  refreshScheduled: number
}

export interface CacheInvalidationConfig {
  batchSize: number
  maxConcurrentRefresh: number
  refreshTimeout: number
  retryAttempts: number
  retryDelay: number
}

export class CacheInvalidationManager {
  private env: any
  private config: CacheInvalidationConfig
  private refreshQueue: Set<string> = new Set()
  private refreshInProgress: Set<string> = new Set()

  constructor(env: any, config?: Partial<CacheInvalidationConfig>) {
    this.env = env
    this.config = {
      batchSize: 50,
      maxConcurrentRefresh: 10,
      refreshTimeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    }
  }

  /**
   * Invalidate cache entries by tag
   */
  async invalidateByTag(tags: string | string[], backgroundRefresh: boolean = true): Promise<InvalidationResult> {
    const tagArray = Array.isArray(tags) ? tags : [tags]
    const result: InvalidationResult = {
      success: true,
      invalidatedCount: 0,
      errors: [],
      refreshScheduled: 0
    }

    try {
      // Get all cache keys with metadata
      const cacheKeys = await this.getCacheKeysByTags(tagArray)
      
      if (cacheKeys.length === 0) {
        return result
      }

      // Process in batches
      const batches = this.createBatches(cacheKeys, this.config.batchSize)
      
      for (const batch of batches) {
        try {
          await this.invalidateBatch(batch, backgroundRefresh)
          result.invalidatedCount += batch.length
          
          if (backgroundRefresh) {
            result.refreshScheduled += batch.length
          }
        } catch (error) {
          result.errors.push(`Batch invalidation failed: ${error}`)
          result.success = false
        }
      }

    } catch (error) {
      result.success = false
      result.errors.push(`Tag invalidation failed: ${error}`)
    }

    return result
  }

  /**
   * Invalidate cache entries by pattern
   */
  async invalidateByPattern(pattern: string, backgroundRefresh: boolean = true): Promise<InvalidationResult> {
    const result: InvalidationResult = {
      success: true,
      invalidatedCount: 0,
      errors: [],
      refreshScheduled: 0
    }

    try {
      // Convert pattern to regex
      const regex = this.patternToRegex(pattern)
      
      // Get all cache keys
      const allKeys = await this.getAllCacheKeys()
      
      // Filter keys matching pattern
      const matchingKeys = allKeys.filter(key => regex.test(key))
      
      if (matchingKeys.length === 0) {
        return result
      }

      // Process in batches
      const batches = this.createBatches(matchingKeys, this.config.batchSize)
      
      for (const batch of batches) {
        try {
          await this.invalidateBatch(batch, backgroundRefresh)
          result.invalidatedCount += batch.length
          
          if (backgroundRefresh) {
            result.refreshScheduled += batch.length
          }
        } catch (error) {
          result.errors.push(`Pattern batch invalidation failed: ${error}`)
          result.success = false
        }
      }

    } catch (error) {
      result.success = false
      result.errors.push(`Pattern invalidation failed: ${error}`)
    }

    return result
  }

  /**
   * Invalidate specific cache key
   */
  async invalidateByKey(key: string, backgroundRefresh: boolean = true): Promise<InvalidationResult> {
    const result: InvalidationResult = {
      success: true,
      invalidatedCount: 0,
      errors: [],
      refreshScheduled: 0
    }

    try {
      await this.invalidateBatch([key], backgroundRefresh)
      result.invalidatedCount = 1
      
      if (backgroundRefresh) {
        result.refreshScheduled = 1
      }
    } catch (error) {
      result.success = false
      result.errors.push(`Key invalidation failed: ${error}`)
    }

    return result
  }

  /**
   * Invalidate all cache entries
   */
  async invalidateAll(backgroundRefresh: boolean = false): Promise<InvalidationResult> {
    const result: InvalidationResult = {
      success: true,
      invalidatedCount: 0,
      errors: [],
      refreshScheduled: 0
    }

    try {
      // Get all cache keys
      const allKeys = await this.getAllCacheKeys()
      
      // Process in batches
      const batches = this.createBatches(allKeys, this.config.batchSize)
      
      for (const batch of batches) {
        try {
          await this.invalidateBatch(batch, backgroundRefresh)
          result.invalidatedCount += batch.length
          
          if (backgroundRefresh) {
            result.refreshScheduled += batch.length
          }
        } catch (error) {
          result.errors.push(`All invalidation batch failed: ${error}`)
          result.success = false
        }
      }

    } catch (error) {
      result.success = false
      result.errors.push(`Full invalidation failed: ${error}`)
    }

    return result
  }

  /**
   * Schedule background refresh for cache keys
   */
  async scheduleBackgroundRefresh(keys: string[]): Promise<void> {
    for (const key of keys) {
      if (!this.refreshInProgress.has(key) && this.refreshInProgress.size < this.config.maxConcurrentRefresh) {
        this.refreshQueue.add(key)
      }
    }

    // Process refresh queue
    await this.processRefreshQueue()
  }

  /**
   * Process background refresh queue
   */
  private async processRefreshQueue(): Promise<void> {
    const keysToProcess = Array.from(this.refreshQueue).slice(0, this.config.maxConcurrentRefresh - this.refreshInProgress.size)
    
    for (const key of keysToProcess) {
      this.refreshQueue.delete(key)
      this.refreshInProgress.add(key)
      
      // Process refresh in background
      this.refreshCacheKey(key).finally(() => {
        this.refreshInProgress.delete(key)
      })
    }
  }

  /**
   * Refresh individual cache key
   */
  private async refreshCacheKey(cacheKey: string): Promise<void> {
    try {
      // Get original request information from metadata
      const metadata = await this.env.API_METADATA_KV.get(`${cacheKey}:meta`, 'json')
      if (!metadata) {
        return
      }

      // Reconstruct request from cache key
      const originalRequest = await this.reconstructRequest(cacheKey, metadata)
      if (!originalRequest) {
        return
      }

      // Fetch fresh data with timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.config.refreshTimeout)

      try {
        const response = await fetch(originalRequest, { signal: controller.signal })
        clearTimeout(timeoutId)

        if (response.ok) {
          // Update cache with fresh data
          await this.updateCacheWithFreshData(cacheKey, response, metadata)
        }
      } catch (fetchError) {
        clearTimeout(timeoutId)
        console.error(`Background refresh failed for ${cacheKey}:`, fetchError)
      }

    } catch (error) {
      console.error(`Cache refresh error for ${cacheKey}:`, error)
    }
  }

  /**
   * Get cache keys by tags
   */
  private async getCacheKeysByTags(tags: string[]): Promise<string[]> {
    const matchingKeys: string[] = []
    
    try {
      // List all metadata keys
      const listResult = await this.env.API_METADATA_KV.list({ prefix: 'cache:' })
      
      for (const item of listResult.keys) {
        if (item.name.endsWith(':meta')) {
          const metadata = await this.env.API_METADATA_KV.get(item.name, 'json')
          if (metadata && metadata.tags) {
            const hasMatchingTag = tags.some(tag => metadata.tags.includes(tag))
            if (hasMatchingTag) {
              const cacheKey = item.name.replace(':meta', '')
              matchingKeys.push(cacheKey)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error getting cache keys by tags:', error)
    }

    return matchingKeys
  }

  /**
   * Get all cache keys
   */
  private async getAllCacheKeys(): Promise<string[]> {
    const keys: string[] = []
    
    try {
      const listResult = await this.env.API_CACHE_KV.list({ prefix: 'cache:' })
      keys.push(...listResult.keys.map(item => item.name))
    } catch (error) {
      console.error('Error getting all cache keys:', error)
    }

    return keys
  }

  /**
   * Convert pattern to regex
   */
  private patternToRegex(pattern: string): RegExp {
    // Convert glob-style pattern to regex
    const escaped = pattern
      .replace(/[.+^${}()|[\]\\]/g, '\\$&') // Escape regex special chars
      .replace(/\*/g, '.*') // Convert * to .*
      .replace(/\?/g, '.') // Convert ? to .
    
    return new RegExp(`^${escaped}$`)
  }

  /**
   * Create batches from array
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = []
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize))
    }
    return batches
  }

  /**
   * Invalidate batch of cache keys
   */
  private async invalidateBatch(keys: string[], backgroundRefresh: boolean): Promise<void> {
    // Delete cache entries
    const deletePromises = keys.flatMap(key => [
      this.env.API_CACHE_KV.delete(key),
      this.env.API_METADATA_KV.delete(`${key}:meta`)
    ])

    await Promise.all(deletePromises)

    // Schedule background refresh if requested
    if (backgroundRefresh) {
      await this.scheduleBackgroundRefresh(keys)
    }
  }

  /**
   * Reconstruct request from cache key and metadata
   */
  private async reconstructRequest(cacheKey: string, metadata: any): Promise<Request | null> {
    try {
      // This is a simplified reconstruction
      // In a real implementation, you'd store more request details in metadata
      const url = metadata.originalUrl || `${this.env.FIREBASE_FUNCTIONS_URL}/api/unknown`
      
      return new Request(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Syndicaps-Cache-Refresh/1.0'
        }
      })
    } catch (error) {
      console.error('Request reconstruction error:', error)
      return null
    }
  }

  /**
   * Update cache with fresh data
   */
  private async updateCacheWithFreshData(cacheKey: string, response: Response, oldMetadata: any): Promise<void> {
    try {
      const responseBody = await response.arrayBuffer()
      const newMetadata = {
        ...oldMetadata,
        timestamp: Date.now(),
        lastRefreshed: Date.now(),
        size: responseBody.byteLength,
        contentType: response.headers.get('Content-Type') || oldMetadata.contentType
      }

      await Promise.all([
        this.env.API_CACHE_KV.put(cacheKey, responseBody, {
          expirationTtl: oldMetadata.ttl + (oldMetadata.staleWhileRevalidate || 600)
        }),
        this.env.API_METADATA_KV.put(`${cacheKey}:meta`, JSON.stringify(newMetadata), {
          expirationTtl: oldMetadata.ttl + (oldMetadata.staleWhileRevalidate || 600)
        })
      ])
    } catch (error) {
      console.error('Cache update error:', error)
    }
  }
}

/**
 * Stale-while-revalidate handler
 */
export class StaleWhileRevalidateManager {
  private invalidationManager: CacheInvalidationManager

  constructor(env: any) {
    this.invalidationManager = new CacheInvalidationManager(env)
  }

  /**
   * Check if cache entry is stale and needs refresh
   */
  isStaleButValid(metadata: any, config: any): boolean {
    const age = Date.now() - metadata.timestamp
    const ttl = config.ttl * 1000
    const staleLimit = config.staleWhileRevalidate * 1000

    return age > ttl && age < (ttl + staleLimit)
  }

  /**
   * Trigger background refresh for stale cache
   */
  async triggerBackgroundRefresh(cacheKey: string): Promise<void> {
    await this.invalidationManager.scheduleBackgroundRefresh([cacheKey])
  }
}

/**
 * Cache warming utilities
 */
export class CacheWarmingManager {
  private env: any

  constructor(env: any) {
    this.env = env
  }

  /**
   * Warm cache for popular endpoints
   */
  async warmPopularEndpoints(endpoints: string[]): Promise<void> {
    const warmingPromises = endpoints.map(endpoint => this.warmEndpoint(endpoint))
    await Promise.allSettled(warmingPromises)
  }

  /**
   * Warm individual endpoint
   */
  private async warmEndpoint(endpoint: string): Promise<void> {
    try {
      const url = `${this.env.FIREBASE_FUNCTIONS_URL}${endpoint}`
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Syndicaps-Cache-Warmer/1.0'
        }
      })

      if (response.ok) {
        console.log(`Cache warmed for ${endpoint}`)
      }
    } catch (error) {
      console.error(`Cache warming failed for ${endpoint}:`, error)
    }
  }
}
