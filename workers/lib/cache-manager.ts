/**
 * Advanced Cache Manager for Image Optimization Worker
 * Provides intelligent caching, TTL management, and cache invalidation
 */

export interface CacheConfig {
  defaultTTL: number
  maxCacheSize: number
  compressionThreshold: number
  enableCompression: boolean
  enableMetrics: boolean
}

export interface CacheEntry {
  data: ArrayBuffer
  metadata: CacheMetadata
  timestamp: number
  accessCount: number
  lastAccessed: number
  size: number
  compressed: boolean
}

export interface CacheMetadata {
  originalSize: number
  optimizedSize: number
  format: string
  transformations: Record<string, any>
  timestamp: number
  hitCount: number
  contentType: string
  etag: string
}

export interface CacheStats {
  totalEntries: number
  totalSize: number
  hitRate: number
  missRate: number
  evictionCount: number
  compressionRatio: number
}

export class ImageCacheManager {
  private imageKV: KVNamespace
  private metadataKV: KVNamespace
  private config: CacheConfig
  private stats: CacheStats

  constructor(imageKV: KVNamespace, metadataKV: KVNamespace, config?: Partial<CacheConfig>) {
    this.imageKV = imageKV
    this.metadataKV = metadataKV
    this.config = {
      defaultTTL: 31536000, // 1 year
      maxCacheSize: 100 * 1024 * 1024, // 100MB per worker
      compressionThreshold: 1024 * 1024, // 1MB
      enableCompression: true,
      enableMetrics: true,
      ...config
    }
    this.stats = {
      totalEntries: 0,
      totalSize: 0,
      hitRate: 0,
      missRate: 0,
      evictionCount: 0,
      compressionRatio: 0
    }
  }

  /**
   * Get cached image with intelligent TTL management
   */
  async get(cacheKey: string): Promise<CacheEntry | null> {
    try {
      const [imageData, metadataData] = await Promise.all([
        this.imageKV.get(cacheKey, 'arrayBuffer'),
        this.metadataKV.get(`meta:${cacheKey}`, 'json')
      ])

      if (!imageData || !metadataData) {
        await this.recordMiss(cacheKey)
        return null
      }

      const metadata = metadataData as CacheMetadata
      const now = Date.now()

      // Update access statistics
      metadata.hitCount++
      metadata.timestamp = now

      // Create cache entry
      const entry: CacheEntry = {
        data: imageData,
        metadata,
        timestamp: metadata.timestamp,
        accessCount: metadata.hitCount,
        lastAccessed: now,
        size: imageData.byteLength,
        compressed: false // Will be determined by compression headers
      }

      // Update metadata with new access info
      await this.metadataKV.put(`meta:${cacheKey}`, JSON.stringify(metadata), {
        expirationTtl: this.calculateMetadataTTL(metadata)
      })

      await this.recordHit(cacheKey)
      return entry

    } catch (error) {
      console.error('Cache retrieval error:', error)
      await this.recordMiss(cacheKey)
      return null
    }
  }

  /**
   * Store image in cache with intelligent compression and TTL
   */
  async set(
    cacheKey: string,
    imageData: ArrayBuffer,
    metadata: Omit<CacheMetadata, 'hitCount' | 'timestamp'>
  ): Promise<void> {
    try {
      const now = Date.now()
      const fullMetadata: CacheMetadata = {
        ...metadata,
        hitCount: 0,
        timestamp: now
      }

      // Determine if compression should be applied
      let finalImageData = imageData
      let compressed = false

      if (this.config.enableCompression && imageData.byteLength > this.config.compressionThreshold) {
        const compressedData = await this.compressImage(imageData)
        if (compressedData && compressedData.byteLength < imageData.byteLength * 0.9) {
          finalImageData = compressedData
          compressed = true
        }
      }

      // Calculate TTL based on image characteristics
      const imageTTL = this.calculateImageTTL(fullMetadata)
      const metadataTTL = this.calculateMetadataTTL(fullMetadata)

      // Store image and metadata
      await Promise.all([
        this.imageKV.put(cacheKey, finalImageData, {
          expirationTtl: imageTTL,
          metadata: {
            compressed: compressed.toString(),
            originalSize: imageData.byteLength.toString(),
            compressedSize: finalImageData.byteLength.toString()
          }
        }),
        this.metadataKV.put(`meta:${cacheKey}`, JSON.stringify(fullMetadata), {
          expirationTtl: metadataTTL
        })
      ])

      // Update cache statistics
      await this.updateCacheStats(finalImageData.byteLength, compressed)

    } catch (error) {
      console.error('Cache storage error:', error)
      throw error
    }
  }

  /**
   * Invalidate specific cache entry
   */
  async invalidate(cacheKey: string): Promise<void> {
    try {
      await Promise.all([
        this.imageKV.delete(cacheKey),
        this.metadataKV.delete(`meta:${cacheKey}`)
      ])
    } catch (error) {
      console.error('Cache invalidation error:', error)
    }
  }

  /**
   * Invalidate cache entries by pattern
   */
  async invalidatePattern(pattern: string): Promise<number> {
    try {
      // List keys matching pattern
      const listResult = await this.imageKV.list({ prefix: pattern })
      const keys = listResult.keys.map(key => key.name)

      // Delete matching entries
      const deletePromises = keys.flatMap(key => [
        this.imageKV.delete(key),
        this.metadataKV.delete(`meta:${key}`)
      ])

      await Promise.all(deletePromises)
      return keys.length

    } catch (error) {
      console.error('Pattern invalidation error:', error)
      return 0
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats> {
    try {
      const statsData = await this.metadataKV.get('cache:stats', 'json')
      return statsData as CacheStats || this.stats
    } catch (error) {
      console.error('Stats retrieval error:', error)
      return this.stats
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    try {
      // This is a simplified implementation
      // In production, you'd need to list and delete all keys
      console.warn('Cache clear operation initiated - this may take time')
      
      // Clear stats
      await this.metadataKV.delete('cache:stats')
      
      // Reset local stats
      this.stats = {
        totalEntries: 0,
        totalSize: 0,
        hitRate: 0,
        missRate: 0,
        evictionCount: 0,
        compressionRatio: 0
      }
    } catch (error) {
      console.error('Cache clear error:', error)
    }
  }

  /**
   * Compress image data (simplified implementation)
   */
  private async compressImage(imageData: ArrayBuffer): Promise<ArrayBuffer | null> {
    try {
      // This is a placeholder for actual compression
      // In production, you might use compression algorithms or
      // rely on Cloudflare's automatic compression
      
      // For now, we'll simulate compression by returning the original
      // In a real implementation, you'd use compression libraries
      return imageData
    } catch (error) {
      console.error('Image compression error:', error)
      return null
    }
  }

  /**
   * Calculate TTL for image data based on characteristics
   */
  private calculateImageTTL(metadata: CacheMetadata): number {
    let ttl = this.config.defaultTTL

    // Longer TTL for smaller, frequently accessed images
    if (metadata.optimizedSize < 100 * 1024) { // < 100KB
      ttl = ttl * 2 // 2 years
    }

    // Shorter TTL for very large images
    if (metadata.optimizedSize > 5 * 1024 * 1024) { // > 5MB
      ttl = ttl / 2 // 6 months
    }

    // Adjust based on compression ratio
    const compressionRatio = metadata.optimizedSize / metadata.originalSize
    if (compressionRatio < 0.5) { // Good compression
      ttl = ttl * 1.5
    }

    return Math.min(ttl, 2 * 365 * 24 * 60 * 60) // Max 2 years
  }

  /**
   * Calculate TTL for metadata
   */
  private calculateMetadataTTL(metadata: CacheMetadata): number {
    // Metadata TTL is typically shorter than image TTL
    return Math.min(this.config.defaultTTL / 4, 30 * 24 * 60 * 60) // Max 30 days
  }

  /**
   * Record cache hit
   */
  private async recordHit(cacheKey: string): Promise<void> {
    if (!this.config.enableMetrics) return

    try {
      const stats = await this.getStats()
      stats.hitRate = (stats.hitRate * stats.totalEntries + 1) / (stats.totalEntries + 1)
      await this.saveStats(stats)
    } catch (error) {
      console.error('Hit recording error:', error)
    }
  }

  /**
   * Record cache miss
   */
  private async recordMiss(cacheKey: string): Promise<void> {
    if (!this.config.enableMetrics) return

    try {
      const stats = await this.getStats()
      stats.missRate = (stats.missRate * stats.totalEntries + 1) / (stats.totalEntries + 1)
      await this.saveStats(stats)
    } catch (error) {
      console.error('Miss recording error:', error)
    }
  }

  /**
   * Update cache statistics
   */
  private async updateCacheStats(size: number, compressed: boolean): Promise<void> {
    if (!this.config.enableMetrics) return

    try {
      const stats = await this.getStats()
      stats.totalEntries++
      stats.totalSize += size
      
      if (compressed) {
        stats.compressionRatio = (stats.compressionRatio * (stats.totalEntries - 1) + 0.7) / stats.totalEntries
      }

      await this.saveStats(stats)
    } catch (error) {
      console.error('Stats update error:', error)
    }
  }

  /**
   * Save statistics to KV
   */
  private async saveStats(stats: CacheStats): Promise<void> {
    try {
      await this.metadataKV.put('cache:stats', JSON.stringify(stats), {
        expirationTtl: 24 * 60 * 60 // 24 hours
      })
    } catch (error) {
      console.error('Stats save error:', error)
    }
  }
}

/**
 * Cache key utilities
 */
export class CacheKeyUtils {
  /**
   * Generate cache key from image path and transformations
   */
  static generateKey(imagePath: string, transformations: Record<string, any>): string {
    const paramString = Object.entries(transformations)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join(',')
    
    return `img:${btoa(imagePath)}:${btoa(paramString)}`
  }

  /**
   * Parse cache key to extract components
   */
  static parseKey(cacheKey: string): { imagePath: string; transformations: Record<string, any> } | null {
    try {
      const parts = cacheKey.split(':')
      if (parts.length !== 3 || parts[0] !== 'img') {
        return null
      }

      const imagePath = atob(parts[1])
      const paramString = atob(parts[2])
      
      const transformations: Record<string, any> = {}
      if (paramString) {
        const params = paramString.split(',')
        for (const param of params) {
          const [key, value] = param.split('=')
          transformations[key] = value
        }
      }

      return { imagePath, transformations }
    } catch (error) {
      return null
    }
  }

  /**
   * Generate pattern for cache invalidation
   */
  static generatePattern(imagePath: string): string {
    return `img:${btoa(imagePath)}:`
  }
}
