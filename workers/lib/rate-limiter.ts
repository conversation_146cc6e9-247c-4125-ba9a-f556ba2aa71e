/**
 * Advanced Rate Limiting System
 * Provides IP-based and user-based rate limiting with sliding window,
 * burst protection, and intelligent throttling
 */

export interface RateLimitRule {
  requests: number        // Max requests per window
  window: number         // Window duration in seconds
  burst: number          // Max burst requests per minute
  identifier: 'ip' | 'user-id' | 'api-key'
  skipSuccessful?: boolean
  skipPaths?: string[]
  priority?: number      // Higher priority rules override lower ones
}

export interface RateLimitResult {
  allowed: boolean
  limited: boolean
  remaining: number
  resetTime: number
  retryAfter: number
  rule: string
  identifier: string
}

export interface RateLimitConfig {
  defaultRule: string
  rules: Record<string, RateLimitRule>
  enableBurstProtection: boolean
  enableAdaptiveThrottling: boolean
  whitelistIPs?: string[]
  blacklistIPs?: string[]
}

export interface RateLimitMetrics {
  totalRequests: number
  blockedRequests: number
  uniqueIdentifiers: number
  topOffenders: Array<{ identifier: string; requests: number; blocked: number }>
  ruleUsage: Record<string, number>
}

export class RateLimiter {
  private env: any
  private config: RateLimitConfig

  constructor(env: any, config: RateLimitConfig) {
    this.env = env
    this.config = config
  }

  /**
   * Check if request should be rate limited
   */
  async checkRateLimit(request: Request): Promise<RateLimitResult> {
    try {
      // Check IP whitelist/blacklist
      const clientIP = this.getClientIP(request)
      
      if (this.config.blacklistIPs?.includes(clientIP)) {
        return this.createBlockedResult('blacklisted', clientIP)
      }

      if (this.config.whitelistIPs?.includes(clientIP)) {
        return this.createAllowedResult('whitelisted', clientIP)
      }

      // Determine applicable rule
      const rule = this.selectRule(request)
      const identifier = this.getIdentifier(request, rule.identifier)

      // Check burst protection
      if (this.config.enableBurstProtection) {
        const burstResult = await this.checkBurstLimit(identifier, rule)
        if (!burstResult.allowed) {
          await this.recordViolation(identifier, rule.identifier, 'burst')
          return burstResult
        }
      }

      // Check main rate limit
      const mainResult = await this.checkMainLimit(identifier, rule)
      
      // Record metrics
      await this.recordRequest(identifier, rule.identifier, !mainResult.allowed)

      // Apply adaptive throttling if enabled
      if (this.config.enableAdaptiveThrottling && !mainResult.allowed) {
        await this.applyAdaptiveThrottling(identifier, rule)
      }

      return mainResult

    } catch (error) {
      console.error('Rate limit check error:', error)
      // Fail open - allow request on error
      return this.createAllowedResult('error', 'unknown')
    }
  }

  /**
   * Get rate limit metrics
   */
  async getMetrics(timeWindow: number = 3600): Promise<RateLimitMetrics> {
    try {
      const now = Date.now()
      const windowStart = now - (timeWindow * 1000)
      
      const metrics: RateLimitMetrics = {
        totalRequests: 0,
        blockedRequests: 0,
        uniqueIdentifiers: 0,
        topOffenders: [],
        ruleUsage: {}
      }

      // Get metrics from KV storage
      const metricsKey = `rate_limit_metrics:${Math.floor(now / 3600000)}`
      const storedMetrics = await this.env.RATE_LIMIT_KV.get(metricsKey, 'json')
      
      if (storedMetrics) {
        Object.assign(metrics, storedMetrics)
      }

      return metrics

    } catch (error) {
      console.error('Rate limit metrics error:', error)
      return {
        totalRequests: 0,
        blockedRequests: 0,
        uniqueIdentifiers: 0,
        topOffenders: [],
        ruleUsage: {}
      }
    }
  }

  /**
   * Reset rate limits for identifier
   */
  async resetLimits(identifier: string): Promise<void> {
    try {
      const keys = [
        `rate_limit:${identifier}:main`,
        `rate_limit:${identifier}:burst`,
        `rate_limit:${identifier}:adaptive`
      ]

      await Promise.all(keys.map(key => this.env.RATE_LIMIT_KV.delete(key)))
    } catch (error) {
      console.error('Rate limit reset error:', error)
    }
  }

  /**
   * Get client IP address
   */
  private getClientIP(request: Request): string {
    return request.headers.get('CF-Connecting-IP') || 
           request.headers.get('X-Forwarded-For')?.split(',')[0] || 
           request.headers.get('X-Real-IP') || 
           'unknown'
  }

  /**
   * Select appropriate rate limit rule
   */
  private selectRule(request: Request): RateLimitRule {
    const url = new URL(request.url)
    const userType = this.getUserType(request)
    
    // Check for user-specific rules first
    if (userType !== 'anonymous' && this.config.rules[userType]) {
      return this.config.rules[userType]
    }

    // Check for path-specific rules
    for (const [ruleName, rule] of Object.entries(this.config.rules)) {
      if (rule.skipPaths?.some(path => url.pathname.startsWith(path))) {
        continue
      }
    }

    // Return default rule
    return this.config.rules[this.config.defaultRule] || this.config.rules['anonymous']
  }

  /**
   * Get identifier based on rule type
   */
  private getIdentifier(request: Request, type: string): string {
    switch (type) {
      case 'ip':
        return this.getClientIP(request)
      case 'user-id':
        return this.getUserId(request) || this.getClientIP(request)
      case 'api-key':
        return request.headers.get('X-API-Key') || this.getClientIP(request)
      default:
        return this.getClientIP(request)
    }
  }

  /**
   * Get user type from request
   */
  private getUserType(request: Request): string {
    const authHeader = request.headers.get('Authorization')
    if (!authHeader) return 'anonymous'
    
    // In a real implementation, decode JWT and extract user type
    // For now, return based on presence of auth
    return 'authenticated'
  }

  /**
   * Get user ID from request
   */
  private getUserId(request: Request): string | null {
    const authHeader = request.headers.get('Authorization')
    if (authHeader?.startsWith('Bearer ')) {
      // In a real implementation, decode JWT token
      const token = authHeader.substring(7)
      return token.length > 10 ? `user-${token.substring(0, 8)}` : null
    }
    return null
  }

  /**
   * Check burst limit
   */
  private async checkBurstLimit(identifier: string, rule: RateLimitRule): Promise<RateLimitResult> {
    const burstKey = `rate_limit:${identifier}:burst`
    const now = Date.now()
    const windowStart = now - 60000 // 1 minute window for burst

    try {
      const burstData = await this.env.RATE_LIMIT_KV.get(burstKey, 'json') || { count: 0, window: now }
      
      // Reset if window expired
      if (now - burstData.window > 60000) {
        burstData.count = 0
        burstData.window = now
      }

      if (burstData.count >= rule.burst) {
        return {
          allowed: false,
          limited: true,
          remaining: 0,
          resetTime: burstData.window + 60000,
          retryAfter: Math.ceil((burstData.window + 60000 - now) / 1000),
          rule: 'burst',
          identifier
        }
      }

      // Increment burst counter
      burstData.count++
      await this.env.RATE_LIMIT_KV.put(burstKey, JSON.stringify(burstData), { expirationTtl: 120 })

      return {
        allowed: true,
        limited: false,
        remaining: rule.burst - burstData.count,
        resetTime: burstData.window + 60000,
        retryAfter: 0,
        rule: 'burst',
        identifier
      }

    } catch (error) {
      console.error('Burst limit check error:', error)
      return this.createAllowedResult('burst-error', identifier)
    }
  }

  /**
   * Check main rate limit
   */
  private async checkMainLimit(identifier: string, rule: RateLimitRule): Promise<RateLimitResult> {
    const limitKey = `rate_limit:${identifier}:main`
    const now = Date.now()
    const windowStart = now - (rule.window * 1000)

    try {
      const limitData = await this.env.RATE_LIMIT_KV.get(limitKey, 'json') || { count: 0, window: now }
      
      // Reset if window expired
      if (now - limitData.window > rule.window * 1000) {
        limitData.count = 0
        limitData.window = now
      }

      if (limitData.count >= rule.requests) {
        return {
          allowed: false,
          limited: true,
          remaining: 0,
          resetTime: limitData.window + (rule.window * 1000),
          retryAfter: Math.ceil((limitData.window + (rule.window * 1000) - now) / 1000),
          rule: 'main',
          identifier
        }
      }

      // Increment counter
      limitData.count++
      await this.env.RATE_LIMIT_KV.put(limitKey, JSON.stringify(limitData), { 
        expirationTtl: rule.window + 60 
      })

      return {
        allowed: true,
        limited: false,
        remaining: rule.requests - limitData.count,
        resetTime: limitData.window + (rule.window * 1000),
        retryAfter: 0,
        rule: 'main',
        identifier
      }

    } catch (error) {
      console.error('Main limit check error:', error)
      return this.createAllowedResult('main-error', identifier)
    }
  }

  /**
   * Apply adaptive throttling
   */
  private async applyAdaptiveThrottling(identifier: string, rule: RateLimitRule): Promise<void> {
    try {
      const adaptiveKey = `rate_limit:${identifier}:adaptive`
      const adaptiveData = await this.env.RATE_LIMIT_KV.get(adaptiveKey, 'json') || { 
        violations: 0, 
        lastViolation: Date.now(),
        throttleUntil: 0
      }

      adaptiveData.violations++
      adaptiveData.lastViolation = Date.now()
      
      // Increase throttle duration based on violations
      const throttleDuration = Math.min(adaptiveData.violations * 60000, 3600000) // Max 1 hour
      adaptiveData.throttleUntil = Date.now() + throttleDuration

      await this.env.RATE_LIMIT_KV.put(adaptiveKey, JSON.stringify(adaptiveData), { 
        expirationTtl: Math.ceil(throttleDuration / 1000) + 60 
      })

    } catch (error) {
      console.error('Adaptive throttling error:', error)
    }
  }

  /**
   * Record rate limit violation
   */
  private async recordViolation(identifier: string, type: string, rule: string): Promise<void> {
    try {
      const violationKey = `violations:${Math.floor(Date.now() / 3600000)}` // Hourly buckets
      const violations = await this.env.RATE_LIMIT_KV.get(violationKey, 'json') || []
      
      violations.push({
        identifier,
        type,
        rule,
        timestamp: Date.now()
      })

      // Keep only last 1000 violations per hour
      if (violations.length > 1000) {
        violations.splice(0, violations.length - 1000)
      }

      await this.env.RATE_LIMIT_KV.put(violationKey, JSON.stringify(violations), { 
        expirationTtl: 7 * 24 * 3600 // 7 days
      })

    } catch (error) {
      console.error('Violation recording error:', error)
    }
  }

  /**
   * Record request metrics
   */
  private async recordRequest(identifier: string, type: string, blocked: boolean): Promise<void> {
    try {
      const metricsKey = `rate_limit_metrics:${Math.floor(Date.now() / 3600000)}`
      const metrics = await this.env.RATE_LIMIT_KV.get(metricsKey, 'json') || {
        totalRequests: 0,
        blockedRequests: 0,
        identifiers: new Set(),
        ruleUsage: {}
      }

      metrics.totalRequests++
      if (blocked) metrics.blockedRequests++
      metrics.identifiers = new Set([...metrics.identifiers, identifier])
      metrics.ruleUsage[type] = (metrics.ruleUsage[type] || 0) + 1

      await this.env.RATE_LIMIT_KV.put(metricsKey, JSON.stringify({
        ...metrics,
        identifiers: Array.from(metrics.identifiers)
      }), { expirationTtl: 7 * 24 * 3600 })

    } catch (error) {
      console.error('Request recording error:', error)
    }
  }

  /**
   * Create allowed result
   */
  private createAllowedResult(rule: string, identifier: string): RateLimitResult {
    return {
      allowed: true,
      limited: false,
      remaining: 1000,
      resetTime: Date.now() + 3600000,
      retryAfter: 0,
      rule,
      identifier
    }
  }

  /**
   * Create blocked result
   */
  private createBlockedResult(rule: string, identifier: string): RateLimitResult {
    return {
      allowed: false,
      limited: true,
      remaining: 0,
      resetTime: Date.now() + 3600000,
      retryAfter: 3600,
      rule,
      identifier
    }
  }
}
