/**
 * Cloudflare Workers Configuration
 * Generated automatically by worker routes configurator
 */

export interface WorkerConfig {
  imageOptimizer: {
    staging: string
    production: string
  }
  apiCache: {
    staging: string
    production: string
  }
}

export const WORKER_CONFIG: WorkerConfig = {
  "imageOptimizer": {
    "staging": "https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev",
    "production": "https://syndicaps-image-optimizer.syndicaps22.workers.dev"
  },
  "apiCache": {
    "staging": "https://syndicaps-api-cache-staging.syndicaps22.workers.dev",
    "production": "https://syndicaps-api-cache.syndicaps22.workers.dev"
  }
}

export const getWorkerUrl = (
  service: keyof WorkerConfig,
  environment: 'staging' | 'production' = process.env.NODE_ENV === 'production' ? 'production' : 'staging'
): string => {
  return WORKER_CONFIG[service][environment]
}

export const getImageOptimizerUrl = (environment?: 'staging' | 'production'): string => {
  return getWorkerUrl('imageOptimizer', environment)
}

export const getApiCacheUrl = (environment?: 'staging' | 'production'): string => {
  return getWorkerUrl('apiCache', environment)
}

// Helper functions for common use cases
export const optimizeImage = (
  imageUrl: string,
  options: { width?: number; height?: number; quality?: number; format?: string } = {}
): string => {
  const workerUrl = getImageOptimizerUrl()
  const params = new URLSearchParams()

  if (options.width) params.set('w', options.width.toString())
  if (options.height) params.set('h', options.height.toString())
  if (options.quality) params.set('q', options.quality.toString())
  if (options.format) params.set('f', options.format)

  const queryString = params.toString()
  return workerUrl + '/' + imageUrl + (queryString ? '?' + queryString : '')
}

export const cacheApiRequest = (apiPath: string): string => {
  const workerUrl = getApiCacheUrl()
  return workerUrl + '/api/' + apiPath.replace(/^//, '')
}