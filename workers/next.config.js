/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // Cloudflare Workers Integration
  env: {
    IMAGE_OPTIMIZER_URL: process.env.IMAGE_OPTIMIZER_URL,
    API_CACHE_URL: process.env.API_CACHE_URL,
  },

  // Image optimization configuration
  images: {
    domains: [
      'syndicaps22.workers.dev',
      'syndicaps-image-optimizer-staging.syndicaps22.workers.dev',
      'syndicaps-image-optimizer.syndicaps22.workers.dev',
      // Add custom domains when available
      'images.syndicaps.com',
      'images-staging.syndicaps.com'
    ],
    loader: 'custom',
    loaderFile: './lib/image-loader.js'
  }
}

module.exports = nextConfig