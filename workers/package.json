{"name": "syndicaps-workers", "version": "1.0.0", "description": "Cloudflare Workers for Syndicaps hybrid deployment", "main": "index.ts", "scripts": {"clean": "rm -rf dist && mkdir -p dist", "prebuild": "npm run clean", "build": "npm run build:all", "build:image-optimizer": "esbuild image-optimizer.ts --bundle --outfile=dist/image-optimizer.js --format=esm --target=es2022 --minify --sourcemap", "build:api-cache": "esbuild api-cache.ts --bundle --outfile=dist/api-cache.js --format=esm --target=es2022 --minify --sourcemap", "build:all": "npm run build:image-optimizer && npm run build:api-cache", "build:watch": "npm run build:all -- --watch", "dev": "wrangler dev", "dev:image-optimizer": "wrangler dev image-optimizer.ts --local --port 8787", "dev:api-cache": "wrangler dev api-cache.ts --local --port 8788", "dev:both": "concurrently \"npm run dev:image-optimizer\" \"npm run dev:api-cache\"", "predeploy": "npm run validate && npm run build", "deploy": "npm run deploy:staging && npm run deploy:production", "deploy:staging": "npm run deploy:staging:image-optimizer && npm run deploy:staging:api-cache", "deploy:production": "npm run deploy:production:image-optimizer && npm run deploy:production:api-cache", "deploy:staging:image-optimizer": "wrangler deploy --config wrangler.toml --env staging", "deploy:staging:api-cache": "wrangler deploy --config wrangler-api-cache.toml --env staging", "deploy:production:image-optimizer": "wrangler deploy --config wrangler.toml --env production", "deploy:production:api-cache": "wrangler deploy --config wrangler-api-cache.toml --env production", "deploy:rollback": "npm run deploy:rollback:image-optimizer && npm run deploy:rollback:api-cache", "deploy:rollback:image-optimizer": "wrangler rollback syndicaps-image-optimizer --env production", "deploy:rollback:api-cache": "wrangler rollback syndicaps-api-cache --env production", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:performance": "vitest run __tests__/performance.test.ts", "test:integration": "vitest run --grep=\"Integration\"", "test:e2e": "npm run test:integration && npm run test:performance", "test:workers": "npm run test:image-optimizer && npm run test:api-cache", "test:image-optimizer": "vitest run __tests__/image-optimizer.test.ts", "test:api-cache": "vitest run __tests__/api-cache.test.ts", "test:comprehensive": "npx tsx scripts/test-api-cache.ts", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "type-check": "tsc --noEmit", "validate": "npm run type-check && npm run lint && npm run test", "validate:staging": "npm run validate && npm run deploy:staging && npm run test:e2e", "validate:production": "npm run validate && npm run test:comprehensive", "health-check": "npx tsx scripts/validate-deployment.ts", "health-check:staging": "npx tsx scripts/validate-deployment.ts --env staging", "health-check:production": "npx tsx scripts/validate-deployment.ts --env production", "monitor": "wrangler tail --env production", "monitor:image-optimizer": "wrangler tail syndicaps-image-optimizer --env production", "monitor:api-cache": "wrangler tail syndicaps-api-cache --env production", "logs": "npm run monitor", "logs:staging": "wrangler tail --env staging", "status": "wrangler deployments list --env production", "status:staging": "wrangler deployments list --env staging", "ci": "npm run validate && npm run build && npm run test:comprehensive", "cd:staging": "npm run ci && npm run deploy:staging && npm run health-check:staging", "cd:production": "npm run ci && npm run deploy:production && npm run health-check:production", "deploy:script": "npx tsx scripts/deploy.ts", "deploy:script:staging": "npx tsx scripts/deploy.ts staging", "deploy:script:production": "npx tsx scripts/deploy.ts production"}, "dependencies": {"@cloudflare/workers-types": "^4.20240925.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitest/coverage-v8": "^1.1.0", "concurrently": "^8.2.2", "esbuild": "^0.23.1", "eslint": "^8.57.0", "jest": "^29.7.0", "jest-environment-miniflare": "^2.14.2", "miniflare": "^3.20240925.0", "tsx": "^4.7.0", "typescript": "^5.6.2", "vitest": "^1.1.0", "wrangler": "^3.78.12"}, "engines": {"node": ">=18.0.0"}, "keywords": ["cloudflare", "workers", "edge-computing", "image-optimization", "api-cache", "syndicaps"], "author": "Syndicaps Team", "license": "MIT"}