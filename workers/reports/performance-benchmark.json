{"summary": {"totalTests": 7, "overallSuccessRate": 35.142857142857146, "averageResponseTime": 1064.5791738380947, "totalDuration": 560}, "results": [{"worker": "API Cache", "endpoint": "Health Check (https://syndicaps-api-cache-staging.syndicaps22.workers.dev/health)", "metrics": {"averageResponseTime": 267.64641799999976, "minResponseTime": 220.82574999999997, "maxResponseTime": 2796.9660000000003, "p95ResponseTime": 247.28737499999988, "throughput": 3.5905512327972526, "errorRate": 0, "successRate": 100}, "samples": 100, "timestamp": "2025-07-27T03:12:31.970Z"}, {"worker": "API Cache", "endpoint": "API Request (https://syndicaps-api-cache-staging.syndicaps22.workers.dev/api/test)", "metrics": {"averageResponseTime": 523.3738075600002, "minResponseTime": 490.700499999999, "maxResponseTime": 1725.3876249999994, "p95ResponseTime": 510.3229999999967, "throughput": 1.8721131021638562, "errorRate": 100, "successRate": 0}, "samples": 50, "timestamp": "2025-07-27T03:12:58.679Z"}, {"worker": "Image Optimizer", "endpoint": "Basic Image (https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev/test.jpg)", "metrics": {"averageResponseTime": 1593.9370541333321, "minResponseTime": 1319.4330419999897, "maxResponseTime": 3154.267541000001, "p95ResponseTime": 2111.9060420000023, "throughput": 0.6231657101094653, "errorRate": 100, "successRate": 0}, "samples": 30, "timestamp": "2025-07-27T03:13:46.818Z"}, {"worker": "Image Optimizer", "endpoint": "Resized Image (https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev/test.jpg?w=300&h=200)", "metrics": {"averageResponseTime": 1578.221238833332, "minResponseTime": 1310.5833749999874, "maxResponseTime": 2984.9349999999977, "p95ResponseTime": 2320.9691249999887, "throughput": 0.6293104493629237, "errorRate": 100, "successRate": 0}, "samples": 30, "timestamp": "2025-07-27T03:14:34.487Z"}, {"worker": "Concurrent Test", "endpoint": "5 users × 10 requests", "metrics": {"averageResponseTime": 1266.3189082599995, "minResponseTime": 0, "maxResponseTime": 0, "p95ResponseTime": 0, "throughput": 3.1137736055079746, "errorRate": 52, "successRate": 48}, "samples": 50, "timestamp": "2025-07-27T03:14:50.546Z"}, {"worker": "Concurrent Test", "endpoint": "10 users × 10 requests", "metrics": {"averageResponseTime": 1139.558500479999, "minResponseTime": 0, "maxResponseTime": 0, "p95ResponseTime": 0, "throughput": 6.438622566802287, "errorRate": 49, "successRate": 51}, "samples": 100, "timestamp": "2025-07-27T03:15:06.076Z"}, {"worker": "Concurrent Test", "endpoint": "20 users × 10 requests", "metrics": {"averageResponseTime": 1082.9982895999995, "minResponseTime": 0, "maxResponseTime": 0, "p95ResponseTime": 0, "throughput": 12.198778144458656, "errorRate": 53, "successRate": 47}, "samples": 200, "timestamp": "2025-07-27T03:15:22.471Z"}], "recommendations": ["Consider optimizing Image Optimizer worker for faster image processing", "Investigate and fix error scenarios to improve success rate", "Consider scaling workers or optimizing for higher throughput"]}