{"summary": {"totalTests": 13, "passedTests": 10, "failedTests": 3, "warningTests": 0, "overallStatus": "FAIL"}, "performance": {"comparisons": [{"metric": "Average Response Time", "baseline": 1000, "current": 456.9454831999998, "improvement": 543.0545168000002, "improvementPercent": 54.30545168000002, "status": "IMPROVED"}], "overallImprovement": 54.30545168000002}, "results": [{"test": "API Cache Health Check", "status": "PASS", "details": {"status": 200, "response": "API Cache Worker OK"}, "duration": 1176.810125, "timestamp": "2025-07-27T03:20:45.807Z"}, {"test": "API Cache Basic Request", "status": "PASS", "details": {"status": 404, "headers": {"access-control-allow-headers": "Content-Type, Authorization, X-Requested-With", "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS", "access-control-allow-origin": "*", "alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-placement": "local-LAX", "cf-ray": "965900c53a8ef7b7-LAX", "connection": "keep-alive", "content-encoding": "br", "content-type": "text/html; charset=UTF-8", "date": "Sun, 27 Jul 2025 03:20:48 GMT", "nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "report-to": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=****************************************%2BPgwx5QcdxvcYJpPc4l9IYC%2BapeVUWwC9vIlX4qo1HHVVvwOQZuVZ0qJfGWsXD8aprKJVEQUEEkJ4PsZO%2Faa3%2F%2FTCQVEz0WCLSkcVB0%3D\"}]}", "server": "cloudflare", "transfer-encoding": "chunked", "vary": "accept-encoding"}}, "duration": 1843.888208, "timestamp": "2025-07-27T03:20:47.653Z"}, {"test": "Image Optimizer Basic Request", "status": "PASS", "details": {"status": 404, "contentType": "application/json", "contentLength": null}, "duration": 2564.9782920000002, "timestamp": "2025-07-27T03:20:50.217Z"}, {"test": "Cross-Environment Consistency", "status": "PASS", "details": {"production": "API Cache Worker OK", "staging": "API Cache Worker OK", "consistent": true}, "duration": 3558.082958, "timestamp": "2025-07-27T03:20:53.775Z"}, {"test": "Response Time Performance", "status": "PASS", "details": {"average": 456.9454831999998, "min": 225.42695900000035, "max": 2339.5542499999992, "samples": 10}, "duration": 4569.642625, "timestamp": "2025-07-27T03:20:58.345Z"}, {"test": "Throughput Performance", "status": "FAIL", "details": {"error": "Throughput too low: 3.37 req/s"}, "duration": 10098.954042, "timestamp": "2025-07-27T03:21:08.443Z"}, {"test": "<PERSON><PERSON>", "status": "PASS", "details": {"firstRequest": 562.3682079999999, "secondRequest": 467.02979100000084, "improvement": 16.953023951880127, "cacheWorking": true}, "duration": 1131.1223329999993, "timestamp": "2025-07-27T03:21:09.575Z"}, {"test": "Concurrent Load Handling", "status": "FAIL", "details": {"error": "Success rate too low under load: 0.0%"}, "duration": 3799.1939999999995, "timestamp": "2025-07-27T03:21:13.374Z"}, {"test": "Sustained <PERSON><PERSON> Handling", "status": "FAIL", "details": {"error": "Sustained load success rate too low: 0.0%"}, "duration": 30114.871042, "timestamp": "2025-07-27T03:21:43.456Z"}, {"test": "HTTPS Security", "status": "PASS", "details": {"protocol": "https:", "secure": true, "headers": {"strictTransportSecurity": null, "contentSecurityPolicy": null}}, "duration": 226.85987500000192, "timestamp": "2025-07-27T03:21:43.683Z"}, {"test": "Rate Limiting", "status": "PASS", "details": {"totalRequests": 50, "rateLimitedRequests": 0, "rateLimitingActive": false}, "duration": 12481.709041000002, "timestamp": "2025-07-27T03:21:56.160Z"}, {"test": "Multi-Region Availability", "status": "PASS", "details": {"regions": [{"region": "us", "available": true, "responseTime": 2421.8195000000123, "status": 200}, {"region": "eu", "available": true, "responseTime": 2492.0437079999974, "status": 200}, {"region": "asia", "available": true, "responseTime": 263.06124999999884, "status": 200}], "availableRegions": 3, "totalRegions": 3, "globalAvailability": 100, "averageResponseTime": 1725.641486000003}, "duration": 5177.279999999999, "timestamp": "2025-07-27T03:22:01.336Z"}, {"test": "Uptime Validation", "status": "PASS", "details": {"totalChecks": 10, "successfulChecks": 10, "uptime": 100}, "duration": 12901.669666999995, "timestamp": "2025-07-27T03:22:14.236Z"}], "recommendations": ["Address failed validation tests before full production rollout", "- Fix: Throughput Performance", "- Fix: Concurrent Load Handling", "- Fix: Sustained Lo<PERSON>ling", "Address critical issues before increasing production traffic", "Implement gradual traffic rollout strategy", "Monitor production metrics for 24-48 hours", "Set up automated health checks and alerting", "Document performance baselines for future reference"], "timestamp": "2025-07-27T03:22:14.236Z"}