#!/usr/bin/env tsx

/**
 * Worker Routes Configuration Script
 * Configures application to use Cloudflare Worker URLs
 */

import { writeFileSync, readFileSync, existsSync } from 'fs'
import { join } from 'path'

interface WorkerConfig {
  imageOptimizer: {
    staging: string
    production: string
  }
  apiCache: {
    staging: string
    production: string
  }
}

interface EnvironmentConfig {
  [key: string]: string
}

class WorkerRoutesConfigurator {
  private workerConfig: WorkerConfig = {
    imageOptimizer: {
      staging: 'https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev',
      production: 'https://syndicaps-image-optimizer.syndicaps22.workers.dev'
    },
    apiCache: {
      staging: 'https://syndicaps-api-cache-staging.syndicaps22.workers.dev',
      production: 'https://syndicaps-api-cache.syndicaps22.workers.dev'
    }
  }

  async configureRoutes(): Promise<void> {
    console.log('🔧 Configuring Worker Routes for Syndicaps Application')
    console.log('=' .repeat(60))

    try {
      // Update environment configuration
      await this.updateEnvironmentConfig()

      // Update Next.js configuration
      await this.updateNextConfig()

      // Update Firebase configuration
      await this.updateFirebaseConfig()

      // Create worker configuration file
      await this.createWorkerConfigFile()

      // Update documentation
      await this.updateDocumentation()

      console.log('\n✅ Worker routes configuration completed successfully!')
      console.log('\n📋 Configuration Summary:')
      console.log(`   Image Optimizer (Staging): ${this.workerConfig.imageOptimizer.staging}`)
      console.log(`   Image Optimizer (Production): ${this.workerConfig.imageOptimizer.production}`)
      console.log(`   API Cache (Staging): ${this.workerConfig.apiCache.staging}`)
      console.log(`   API Cache (Production): ${this.workerConfig.apiCache.production}`)

      console.log('\n📝 Next Steps:')
      console.log('   1. Update your application code to use the worker URLs')
      console.log('   2. Test the integration in staging environment')
      console.log('   3. Deploy workers to production when ready')
      console.log('   4. Update DNS records when custom domains are available')

    } catch (error) {
      console.error('\n❌ Configuration failed:', error)
      throw error
    }
  }

  private async updateEnvironmentConfig(): Promise<void> {
    console.log('\n🔧 Updating environment configuration...')

    const envConfigs = [
      { file: '../.env.local', env: 'development' },
      { file: '../.env.staging', env: 'staging' },
      { file: '../.env.production', env: 'production' }
    ]

    for (const { file, env } of envConfigs) {
      const envPath = join(__dirname, file)
      let envContent = ''

      // Read existing content if file exists
      if (existsSync(envPath)) {
        envContent = readFileSync(envPath, 'utf-8')
      }

      // Remove existing worker configurations
      envContent = envContent.replace(/^NEXT_PUBLIC_IMAGE_OPTIMIZER_URL=.*$/gm, '')
      envContent = envContent.replace(/^NEXT_PUBLIC_API_CACHE_URL=.*$/gm, '')
      envContent = envContent.replace(/^IMAGE_OPTIMIZER_URL=.*$/gm, '')
      envContent = envContent.replace(/^API_CACHE_URL=.*$/gm, '')

      // Add new worker configurations
      const workerEnv = env === 'development' ? 'staging' : env as 'staging' | 'production'
      
      envContent += `\n# Cloudflare Workers Configuration\n`
      envContent += `NEXT_PUBLIC_IMAGE_OPTIMIZER_URL=${this.workerConfig.imageOptimizer[workerEnv]}\n`
      envContent += `NEXT_PUBLIC_API_CACHE_URL=${this.workerConfig.apiCache[workerEnv]}\n`
      envContent += `IMAGE_OPTIMIZER_URL=${this.workerConfig.imageOptimizer[workerEnv]}\n`
      envContent += `API_CACHE_URL=${this.workerConfig.apiCache[workerEnv]}\n`

      // Clean up extra newlines
      envContent = envContent.replace(/\n\n+/g, '\n\n').trim() + '\n'

      writeFileSync(envPath, envContent)
      console.log(`   ✅ Updated ${file}`)
    }
  }

  private async updateNextConfig(): Promise<void> {
    console.log('\n🔧 Updating Next.js configuration...')

    const nextConfigPath = join(__dirname, '../next.config.js')

    if (!existsSync(nextConfigPath)) {
      console.log('   ⚠️  next.config.js not found, creating basic configuration')
    }

    const nextConfigContent = [
      '/** @type {import(\'next\').NextConfig} */',
      'const nextConfig = {',
      '  reactStrictMode: true,',
      '  swcMinify: true,',
      '  ',
      '  // Cloudflare Workers Integration',
      '  env: {',
      '    IMAGE_OPTIMIZER_URL: process.env.IMAGE_OPTIMIZER_URL,',
      '    API_CACHE_URL: process.env.API_CACHE_URL,',
      '  },',
      '',
      '  // Image optimization configuration',
      '  images: {',
      '    domains: [',
      '      \'syndicaps22.workers.dev\',',
      '      \'syndicaps-image-optimizer-staging.syndicaps22.workers.dev\',',
      '      \'syndicaps-image-optimizer.syndicaps22.workers.dev\',',
      '      // Add custom domains when available',
      '      \'images.syndicaps.com\',',
      '      \'images-staging.syndicaps.com\'',
      '    ],',
      '    loader: \'custom\',',
      '    loaderFile: \'./lib/image-loader.js\'',
      '  }',
      '}',
      '',
      'module.exports = nextConfig'
    ].join('\n')

    writeFileSync(nextConfigPath, nextConfigContent)
    console.log('   ✅ Updated next.config.js')
  }

  private async updateFirebaseConfig(): Promise<void> {
    console.log('\n🔧 Updating Firebase configuration...')

    const firebaseConfigPath = join(__dirname, '../lib/firebase-config.ts')
    
    if (existsSync(firebaseConfigPath)) {
      let configContent = readFileSync(firebaseConfigPath, 'utf-8')
      
      // Add worker configuration to Firebase config
      const workerConfigAddition = `
// Cloudflare Workers Configuration
export const WORKER_CONFIG = {
  imageOptimizer: {
    staging: '${this.workerConfig.imageOptimizer.staging}',
    production: '${this.workerConfig.imageOptimizer.production}'
  },
  apiCache: {
    staging: '${this.workerConfig.apiCache.staging}',
    production: '${this.workerConfig.apiCache.production}'
  }
}

export const getWorkerUrl = (service: 'imageOptimizer' | 'apiCache') => {
  const env = process.env.NODE_ENV === 'production' ? 'production' : 'staging'
  return WORKER_CONFIG[service][env]
}
`

      // Remove existing worker config if present
      configContent = configContent.replace(/\/\/ Cloudflare Workers Configuration[\s\S]*?(?=export|$)/g, '')
      
      // Add new worker config
      configContent += workerConfigAddition

      writeFileSync(firebaseConfigPath, configContent)
      console.log('   ✅ Updated Firebase configuration')
    } else {
      console.log('   ⚠️  Firebase config not found, skipping')
    }
  }

  private async createWorkerConfigFile(): Promise<void> {
    console.log('\n🔧 Creating worker configuration file...')

    const configPath = join(__dirname, '../lib/worker-config.ts')
    
    const configContent = `
/**
 * Cloudflare Workers Configuration
 * Generated automatically by worker routes configurator
 */

export interface WorkerConfig {
  imageOptimizer: {
    staging: string
    production: string
  }
  apiCache: {
    staging: string
    production: string
  }
}

export const WORKER_CONFIG: WorkerConfig = ${JSON.stringify(this.workerConfig, null, 2)}

export const getWorkerUrl = (
  service: keyof WorkerConfig,
  environment: 'staging' | 'production' = process.env.NODE_ENV === 'production' ? 'production' : 'staging'
): string => {
  return WORKER_CONFIG[service][environment]
}

export const getImageOptimizerUrl = (environment?: 'staging' | 'production'): string => {
  return getWorkerUrl('imageOptimizer', environment)
}

export const getApiCacheUrl = (environment?: 'staging' | 'production'): string => {
  return getWorkerUrl('apiCache', environment)
}

// Helper functions for common use cases
export const optimizeImage = (
  imageUrl: string,
  options: { width?: number; height?: number; quality?: number; format?: string } = {}
): string => {
  const workerUrl = getImageOptimizerUrl()
  const params = new URLSearchParams()

  if (options.width) params.set('w', options.width.toString())
  if (options.height) params.set('h', options.height.toString())
  if (options.quality) params.set('q', options.quality.toString())
  if (options.format) params.set('f', options.format)

  const queryString = params.toString()
  return workerUrl + '/' + imageUrl + (queryString ? '?' + queryString : '')
}

export const cacheApiRequest = (apiPath: string): string => {
  const workerUrl = getApiCacheUrl()
  return workerUrl + '/api/' + apiPath.replace(/^\//, '')
}
`

    writeFileSync(configPath, configContent.trim())
    console.log('   ✅ Created worker configuration file')
  }

  private async updateDocumentation(): Promise<void> {
    console.log('\n🔧 Updating documentation...')

    const readmePath = join(__dirname, '../README.md')
    
    if (existsSync(readmePath)) {
      let readmeContent = readFileSync(readmePath, 'utf-8')
      
      const workerSection = `
## Cloudflare Workers Integration

This application uses Cloudflare Workers for image optimization and API caching.

### Worker URLs

#### Staging Environment
- Image Optimizer: ${this.workerConfig.imageOptimizer.staging}
- API Cache: ${this.workerConfig.apiCache.staging}

#### Production Environment
- Image Optimizer: ${this.workerConfig.imageOptimizer.production}
- API Cache: ${this.workerConfig.apiCache.production}

### Usage

\`\`\`javascript
import { optimizeImage, cacheApiRequest } from './lib/worker-config'

// Optimize an image
const optimizedUrl = optimizeImage('path/to/image.jpg', { width: 300, height: 200 })

// Cache an API request
const cachedApiUrl = cacheApiRequest('products/list')
\`\`\`

### Environment Variables

Make sure to set the following environment variables:

\`\`\`
NEXT_PUBLIC_IMAGE_OPTIMIZER_URL=https://syndicaps-image-optimizer.syndicaps22.workers.dev
NEXT_PUBLIC_API_CACHE_URL=https://syndicaps-api-cache.syndicaps22.workers.dev
IMAGE_OPTIMIZER_URL=https://syndicaps-image-optimizer.syndicaps22.workers.dev
API_CACHE_URL=https://syndicaps-api-cache.syndicaps22.workers.dev
\`\`\`
`

      // Remove existing worker section if present
      readmeContent = readmeContent.replace(/## Cloudflare Workers Integration[\s\S]*?(?=##|$)/g, '')
      
      // Add new worker section
      readmeContent += workerSection

      writeFileSync(readmePath, readmeContent)
      console.log('   ✅ Updated README.md')
    } else {
      console.log('   ⚠️  README.md not found, skipping')
    }
  }
}

// Main execution
async function main() {
  const configurator = new WorkerRoutesConfigurator()
  await configurator.configureRoutes()
}

// Export for programmatic use
export { WorkerRoutesConfigurator }

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Configuration script failed:', error)
    process.exit(1)
  })
}
