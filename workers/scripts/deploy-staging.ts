#!/usr/bin/env tsx

/**
 * Staging Deployment Script for Syndicaps Workers
 * Deploys workers to staging environment with comprehensive validation
 */

import { execSync } from 'child_process'
import { performance } from 'perf_hooks'

interface StagingDeploymentResult {
  worker: string
  success: boolean
  duration: number
  deploymentUrl?: string
  healthCheckPassed?: boolean
  error?: string
}

class StagingDeploymentManager {
  private results: StagingDeploymentResult[] = []

  async deployToStaging(): Promise<boolean> {
    console.log('🚀 Starting staging deployment for Syndicaps Workers')
    console.log('=' .repeat(60))

    try {
      // Pre-deployment validation
      await this.runPreDeploymentChecks()

      // Deploy Image Optimizer
      const imageOptimizerResult = await this.deployImageOptimizer()
      this.results.push(imageOptimizerResult)

      // Deploy API Cache
      const apiCacheResult = await this.deployApiCache()
      this.results.push(apiCacheResult)

      // Post-deployment validation
      await this.runPostDeploymentValidation()

      // Generate report
      this.generateReport()

      const allSuccessful = this.results.every(r => r.success && r.healthCheckPassed)
      
      if (allSuccessful) {
        console.log('\n✅ Staging deployment completed successfully!')
        console.log('\n🔗 Staging URLs:')
        console.log('   Image Optimizer: https://images-staging.syndicaps.com')
        console.log('   API Cache: https://api-cache-staging.syndicaps.com')
        return true
      } else {
        console.error('\n❌ Staging deployment failed or health checks did not pass')
        return false
      }

    } catch (error) {
      console.error('\n💥 Staging deployment process failed:', error)
      return false
    }
  }

  private async runPreDeploymentChecks(): Promise<void> {
    console.log('\n🔍 Running pre-deployment checks...')

    // Check authentication
    try {
      const whoami = execSync('wrangler whoami', { encoding: 'utf-8' })
      console.log(`  ✅ Authenticated as: ${whoami.trim()}`)
    } catch (error) {
      throw new Error('Wrangler authentication failed. Please run: wrangler auth login')
    }

    // Validate configuration files
    const configs = ['wrangler.toml', 'wrangler-api-cache.toml']
    for (const config of configs) {
      try {
        execSync(`wrangler config validate --config ${config}`, { stdio: 'pipe' })
        console.log(`  ✅ Configuration valid: ${config}`)
      } catch (error) {
        throw new Error(`Invalid configuration: ${config}`)
      }
    }

    // Run tests
    try {
      console.log('  🧪 Running tests...')
      execSync('npm run test', { stdio: 'pipe' })
      console.log('  ✅ All tests passed')
    } catch (error) {
      throw new Error('Tests failed - deployment aborted')
    }

    // Build workers
    try {
      console.log('  🔨 Building workers...')
      execSync('npm run build', { stdio: 'pipe' })
      console.log('  ✅ Build successful')
    } catch (error) {
      throw new Error('Build failed - deployment aborted')
    }

    console.log('✅ Pre-deployment checks completed')
  }

  private async deployImageOptimizer(): Promise<StagingDeploymentResult> {
    console.log('\n📦 Deploying Image Optimizer to staging...')
    const start = performance.now()

    try {
      const output = execSync('npm run deploy:staging:image-optimizer', { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })

      const duration = performance.now() - start
      console.log(`  ✅ Image Optimizer deployed successfully in ${duration.toFixed(2)}ms`)

      return {
        worker: 'image-optimizer',
        success: true,
        duration,
        deploymentUrl: 'https://images-staging.syndicaps.com'
      }

    } catch (error) {
      const duration = performance.now() - start
      console.error(`  ❌ Image Optimizer deployment failed:`, error)

      return {
        worker: 'image-optimizer',
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  private async deployApiCache(): Promise<StagingDeploymentResult> {
    console.log('\n📦 Deploying API Cache to staging...')
    const start = performance.now()

    try {
      const output = execSync('npm run deploy:staging:api-cache', { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })

      const duration = performance.now() - start
      console.log(`  ✅ API Cache deployed successfully in ${duration.toFixed(2)}ms`)

      return {
        worker: 'api-cache',
        success: true,
        duration,
        deploymentUrl: 'https://api-cache-staging.syndicaps.com'
      }

    } catch (error) {
      const duration = performance.now() - start
      console.error(`  ❌ API Cache deployment failed:`, error)

      return {
        worker: 'api-cache',
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  private async runPostDeploymentValidation(): Promise<void> {
    console.log('\n🏥 Running post-deployment validation...')

    // Wait for workers to be ready
    console.log('  ⏳ Waiting for workers to be ready...')
    await new Promise(resolve => setTimeout(resolve, 10000)) // 10 second delay

    // Health check Image Optimizer
    await this.healthCheckWorker('image-optimizer', 'https://images-staging.syndicaps.com/health')

    // Health check API Cache
    await this.healthCheckWorker('api-cache', 'https://api-cache-staging.syndicaps.com/health')

    // Functional tests
    await this.runFunctionalTests()
  }

  private async healthCheckWorker(workerName: string, healthUrl: string): Promise<void> {
    console.log(`  🔍 Health checking ${workerName}...`)
    
    const result = this.results.find(r => r.worker === workerName)
    if (!result || !result.success) {
      console.log(`    ⚠️  Skipping health check for failed deployment`)
      return
    }

    try {
      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: { 'User-Agent': 'Syndicaps-Staging-Health-Check' }
      })

      if (response.ok) {
        result.healthCheckPassed = true
        console.log(`    ✅ ${workerName} health check passed`)
      } else {
        result.healthCheckPassed = false
        console.log(`    ❌ ${workerName} health check failed: ${response.status} ${response.statusText}`)
      }

    } catch (error) {
      result.healthCheckPassed = false
      console.log(`    ❌ ${workerName} health check error:`, error)
    }
  }

  private async runFunctionalTests(): Promise<void> {
    console.log('  🧪 Running functional tests...')

    // Test Image Optimizer
    try {
      const imageResponse = await fetch('https://images-staging.syndicaps.com/test.jpg?w=100&h=100', {
        method: 'HEAD',
        headers: { 'User-Agent': 'Syndicaps-Staging-Test' }
      })

      if (imageResponse.status === 200 || imageResponse.status === 404) {
        console.log('    ✅ Image Optimizer functional test passed')
      } else {
        console.log(`    ⚠️  Image Optimizer functional test warning: ${imageResponse.status}`)
      }
    } catch (error) {
      console.log('    ⚠️  Image Optimizer functional test failed:', error)
    }

    // Test API Cache
    try {
      const apiResponse = await fetch('https://api-cache-staging.syndicaps.com/api/test', {
        method: 'HEAD',
        headers: { 'User-Agent': 'Syndicaps-Staging-Test' }
      })

      if (apiResponse.status === 200 || apiResponse.status === 404) {
        console.log('    ✅ API Cache functional test passed')
      } else {
        console.log(`    ⚠️  API Cache functional test warning: ${apiResponse.status}`)
      }
    } catch (error) {
      console.log('    ⚠️  API Cache functional test failed:', error)
    }
  }

  private generateReport(): void {
    console.log('\n📊 Staging Deployment Report')
    console.log('=' .repeat(40))

    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)
    const successfulDeployments = this.results.filter(r => r.success).length
    const healthyDeployments = this.results.filter(r => r.healthCheckPassed).length

    console.log(`Total Workers: ${this.results.length}`)
    console.log(`Successful Deployments: ${successfulDeployments}/${this.results.length}`)
    console.log(`Healthy Deployments: ${healthyDeployments}/${this.results.length}`)
    console.log(`Total Duration: ${totalDuration.toFixed(2)}ms`)

    console.log('\nWorker Details:')
    for (const result of this.results) {
      const status = result.success ? '✅' : '❌'
      const health = result.healthCheckPassed ? '🟢' : result.healthCheckPassed === false ? '🔴' : '⚪'
      
      console.log(`  ${status} ${health} ${result.worker} (${result.duration.toFixed(2)}ms)`)
      
      if (result.deploymentUrl) {
        console.log(`      URL: ${result.deploymentUrl}`)
      }
      
      if (result.error) {
        console.log(`      Error: ${result.error}`)
      }
    }

    if (successfulDeployments === this.results.length && healthyDeployments === this.results.length) {
      console.log('\n🎉 All workers deployed successfully to staging!')
      console.log('\n📝 Next Steps:')
      console.log('   1. Test the staging environment thoroughly')
      console.log('   2. Validate all functionality works as expected')
      console.log('   3. Run integration tests')
      console.log('   4. Proceed with production deployment when ready')
    } else {
      console.log('\n⚠️  Some issues detected. Please review and fix before proceeding.')
    }
  }
}

// Main execution
async function main() {
  const deployer = new StagingDeploymentManager()
  const success = await deployer.deployToStaging()
  process.exit(success ? 0 : 1)
}

// Export for programmatic use
export { StagingDeploymentManager }

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Staging deployment script failed:', error)
    process.exit(1)
  })
}
