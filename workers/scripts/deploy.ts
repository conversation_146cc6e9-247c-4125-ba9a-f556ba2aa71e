#!/usr/bin/env tsx

/**
 * Comprehensive Deployment Script for Syndicaps Workers
 * Handles staging and production deployments with validation and rollback
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync } from 'fs'
import { performance } from 'perf_hooks'

interface DeploymentConfig {
  environment: 'staging' | 'production'
  workers: Array<{
    name: string
    script: string
    workerName: string
  }>
  healthCheckEndpoints: string[]
  rollbackOnFailure: boolean
  validateBeforeDeploy: boolean
  notificationWebhook?: string
}

interface DeploymentResult {
  worker: string
  success: boolean
  deploymentId?: string
  duration: number
  error?: string
  healthCheckPassed?: boolean
}

class WorkerDeploymentManager {
  private config: DeploymentConfig
  private deploymentResults: DeploymentResult[] = []

  constructor(config: DeploymentConfig) {
    this.config = config
  }

  async deploy(): Promise<boolean> {
    console.log(`🚀 Starting ${this.config.environment} deployment`)
    console.log(`Workers to deploy: ${this.config.workers.map(w => w.name).join(', ')}`)

    try {
      // Pre-deployment validation
      if (this.config.validateBeforeDeploy) {
        await this.runPreDeploymentValidation()
      }

      // Deploy each worker
      for (const worker of this.config.workers) {
        const result = await this.deployWorker(worker)
        this.deploymentResults.push(result)

        if (!result.success && this.config.rollbackOnFailure) {
          console.error(`❌ Deployment failed for ${worker.name}, initiating rollback`)
          await this.rollbackDeployments()
          return false
        }
      }

      // Post-deployment health checks
      await this.runHealthChecks()

      // Generate deployment report
      this.generateDeploymentReport()

      // Send notifications
      if (this.config.notificationWebhook) {
        await this.sendNotification()
      }

      const allSuccessful = this.deploymentResults.every(r => r.success && r.healthCheckPassed)
      
      if (allSuccessful) {
        console.log('✅ All deployments completed successfully!')
        return true
      } else {
        console.error('❌ Some deployments failed or health checks did not pass')
        return false
      }

    } catch (error) {
      console.error('💥 Deployment process failed:', error)
      
      if (this.config.rollbackOnFailure) {
        await this.rollbackDeployments()
      }
      
      return false
    }
  }

  private async runPreDeploymentValidation(): Promise<void> {
    console.log('🔍 Running pre-deployment validation...')

    // Check if all required files exist
    for (const worker of this.config.workers) {
      if (!existsSync(worker.script)) {
        throw new Error(`Worker script not found: ${worker.script}`)
      }
    }

    // Run tests
    try {
      console.log('  Running tests...')
      execSync('npm run test', { stdio: 'pipe' })
      console.log('  ✅ Tests passed')
    } catch (error) {
      throw new Error('Tests failed - deployment aborted')
    }

    // Type checking
    try {
      console.log('  Running type check...')
      execSync('npm run type-check', { stdio: 'pipe' })
      console.log('  ✅ Type check passed')
    } catch (error) {
      throw new Error('Type check failed - deployment aborted')
    }

    // Linting
    try {
      console.log('  Running linter...')
      execSync('npm run lint', { stdio: 'pipe' })
      console.log('  ✅ Linting passed')
    } catch (error) {
      throw new Error('Linting failed - deployment aborted')
    }

    // Build validation
    try {
      console.log('  Building workers...')
      execSync('npm run build', { stdio: 'pipe' })
      console.log('  ✅ Build successful')
    } catch (error) {
      throw new Error('Build failed - deployment aborted')
    }

    console.log('✅ Pre-deployment validation completed')
  }

  private async deployWorker(worker: { name: string; script: string; workerName: string }): Promise<DeploymentResult> {
    console.log(`\n📦 Deploying ${worker.name}...`)
    const start = performance.now()

    try {
      const deployCommand = `wrangler deploy ${worker.script} --name ${worker.workerName} --env ${this.config.environment}`
      console.log(`  Command: ${deployCommand}`)

      const output = execSync(deployCommand, { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })

      const duration = performance.now() - start

      // Extract deployment ID from output
      const deploymentIdMatch = output.match(/deployment-id:\s*([a-f0-9-]+)/i)
      const deploymentId = deploymentIdMatch ? deploymentIdMatch[1] : undefined

      console.log(`  ✅ ${worker.name} deployed successfully in ${duration.toFixed(2)}ms`)
      if (deploymentId) {
        console.log(`  📋 Deployment ID: ${deploymentId}`)
      }

      return {
        worker: worker.name,
        success: true,
        deploymentId,
        duration
      }

    } catch (error) {
      const duration = performance.now() - start
      console.error(`  ❌ ${worker.name} deployment failed:`, error)

      return {
        worker: worker.name,
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  private async runHealthChecks(): Promise<void> {
    console.log('\n🏥 Running health checks...')

    for (const result of this.deploymentResults) {
      if (!result.success) continue

      console.log(`  Checking ${result.worker}...`)
      
      try {
        // Find corresponding health check endpoint
        const healthEndpoint = this.getHealthCheckEndpoint(result.worker)
        
        if (healthEndpoint) {
          const response = await fetch(healthEndpoint, {
            method: 'GET',
            headers: { 'User-Agent': 'Syndicaps-Deployment-Health-Check' }
          })

          if (response.ok) {
            result.healthCheckPassed = true
            console.log(`    ✅ Health check passed`)
          } else {
            result.healthCheckPassed = false
            console.log(`    ❌ Health check failed: ${response.status} ${response.statusText}`)
          }
        } else {
          result.healthCheckPassed = true // No health check endpoint defined
          console.log(`    ⚠️  No health check endpoint defined`)
        }

      } catch (error) {
        result.healthCheckPassed = false
        console.log(`    ❌ Health check error:`, error)
      }
    }
  }

  private getHealthCheckEndpoint(workerName: string): string | undefined {
    const endpointMap: Record<string, string> = {
      'image-optimizer': this.config.environment === 'production' 
        ? 'https://images.syndicaps.com/health'
        : 'https://images-staging.syndicaps.com/health',
      'api-cache': this.config.environment === 'production'
        ? 'https://api-cache.syndicaps.com/health'
        : 'https://api-cache-staging.syndicaps.com/health'
    }

    return endpointMap[workerName]
  }

  private async rollbackDeployments(): Promise<void> {
    console.log('\n🔄 Initiating rollback...')

    for (const result of this.deploymentResults) {
      if (result.success && result.deploymentId) {
        try {
          console.log(`  Rolling back ${result.worker}...`)
          
          const rollbackCommand = `wrangler rollback ${result.worker} --env ${this.config.environment}`
          execSync(rollbackCommand, { stdio: 'pipe' })
          
          console.log(`    ✅ ${result.worker} rolled back successfully`)
        } catch (error) {
          console.error(`    ❌ Failed to rollback ${result.worker}:`, error)
        }
      }
    }
  }

  private generateDeploymentReport(): void {
    console.log('\n📊 Deployment Report')
    console.log('=' .repeat(50))

    const totalDuration = this.deploymentResults.reduce((sum, r) => sum + r.duration, 0)
    const successfulDeployments = this.deploymentResults.filter(r => r.success).length
    const healthyDeployments = this.deploymentResults.filter(r => r.healthCheckPassed).length

    console.log(`Environment: ${this.config.environment}`)
    console.log(`Total Workers: ${this.deploymentResults.length}`)
    console.log(`Successful Deployments: ${successfulDeployments}/${this.deploymentResults.length}`)
    console.log(`Healthy Deployments: ${healthyDeployments}/${this.deploymentResults.length}`)
    console.log(`Total Duration: ${totalDuration.toFixed(2)}ms`)

    console.log('\nWorker Details:')
    for (const result of this.deploymentResults) {
      const status = result.success ? '✅' : '❌'
      const health = result.healthCheckPassed ? '🟢' : result.healthCheckPassed === false ? '🔴' : '⚪'
      
      console.log(`  ${status} ${health} ${result.worker} (${result.duration.toFixed(2)}ms)`)
      
      if (result.deploymentId) {
        console.log(`      Deployment ID: ${result.deploymentId}`)
      }
      
      if (result.error) {
        console.log(`      Error: ${result.error}`)
      }
    }
  }

  private async sendNotification(): Promise<void> {
    if (!this.config.notificationWebhook) return

    try {
      const payload = {
        environment: this.config.environment,
        timestamp: new Date().toISOString(),
        results: this.deploymentResults,
        summary: {
          total: this.deploymentResults.length,
          successful: this.deploymentResults.filter(r => r.success).length,
          healthy: this.deploymentResults.filter(r => r.healthCheckPassed).length
        }
      }

      await fetch(this.config.notificationWebhook, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      console.log('📢 Notification sent successfully')
    } catch (error) {
      console.error('📢 Failed to send notification:', error)
    }
  }
}

// Deployment configurations
const STAGING_CONFIG: DeploymentConfig = {
  environment: 'staging',
  workers: [
    {
      name: 'image-optimizer',
      script: 'image-optimizer.ts',
      workerName: 'syndicaps-image-optimizer-staging'
    },
    {
      name: 'api-cache',
      script: 'api-cache.ts',
      workerName: 'syndicaps-api-cache-staging'
    }
  ],
  healthCheckEndpoints: [
    'https://images-staging.syndicaps.com/health',
    'https://api-cache-staging.syndicaps.com/health'
  ],
  rollbackOnFailure: true,
  validateBeforeDeploy: true
}

const PRODUCTION_CONFIG: DeploymentConfig = {
  environment: 'production',
  workers: [
    {
      name: 'image-optimizer',
      script: 'image-optimizer.ts',
      workerName: 'syndicaps-image-optimizer'
    },
    {
      name: 'api-cache',
      script: 'api-cache.ts',
      workerName: 'syndicaps-api-cache'
    }
  ],
  healthCheckEndpoints: [
    'https://images.syndicaps.com/health',
    'https://api-cache.syndicaps.com/health'
  ],
  rollbackOnFailure: true,
  validateBeforeDeploy: true,
  notificationWebhook: process.env.DEPLOYMENT_WEBHOOK_URL
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)
  const environment = args[0] as 'staging' | 'production'

  if (!environment || !['staging', 'production'].includes(environment)) {
    console.error('Usage: npm run deploy:script <staging|production>')
    process.exit(1)
  }

  const config = environment === 'staging' ? STAGING_CONFIG : PRODUCTION_CONFIG
  const deployer = new WorkerDeploymentManager(config)

  const success = await deployer.deploy()
  process.exit(success ? 0 : 1)
}

// Export for programmatic use
export { WorkerDeploymentManager, type DeploymentConfig, type DeploymentResult }

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Deployment script failed:', error)
    process.exit(1)
  })
}
