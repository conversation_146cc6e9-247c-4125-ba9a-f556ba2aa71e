#!/usr/bin/env tsx

/**
 * Performance Benchmark Script for Cloudflare Workers
 * Measures and compares worker performance against baseline metrics
 */

import { performance } from 'perf_hooks'
import { writeFileSync } from 'fs'
import { join } from 'path'

interface BenchmarkResult {
  worker: string
  endpoint: string
  metrics: {
    averageResponseTime: number
    minResponseTime: number
    maxResponseTime: number
    p95ResponseTime: number
    throughput: number
    errorRate: number
    successRate: number
  }
  samples: number
  timestamp: string
}

interface BenchmarkReport {
  summary: {
    totalTests: number
    overallSuccessRate: number
    averageResponseTime: number
    totalDuration: number
  }
  results: BenchmarkResult[]
  recommendations: string[]
}

class PerformanceBenchmark {
  private imageOptimizerUrl = 'https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev'
  private apiCacheUrl = 'https://syndicaps-api-cache-staging.syndicaps22.workers.dev'
  private results: BenchmarkResult[] = []

  async runBenchmarks(): Promise<void> {
    console.log('🚀 Starting Performance Benchmark Suite')
    console.log('=' .repeat(60))

    try {
      // API Cache Benchmarks
      await this.benchmarkEndpoint(
        'API Cache',
        `${this.apiCacheUrl}/health`,
        100,
        'Health Check'
      )

      await this.benchmarkEndpoint(
        'API Cache',
        `${this.apiCacheUrl}/api/test`,
        50,
        'API Request'
      )

      // Image Optimizer Benchmarks
      await this.benchmarkEndpoint(
        'Image Optimizer',
        `${this.imageOptimizerUrl}/test.jpg`,
        30,
        'Basic Image'
      )

      await this.benchmarkEndpoint(
        'Image Optimizer',
        `${this.imageOptimizerUrl}/test.jpg?w=300&h=200`,
        30,
        'Resized Image'
      )

      // Concurrent Load Tests
      await this.runConcurrentBenchmark()

      // Generate Report
      this.generateBenchmarkReport()

    } catch (error) {
      console.error('\n❌ Benchmark failed:', error)
      throw error
    }
  }

  private async benchmarkEndpoint(
    workerName: string,
    url: string,
    samples: number,
    description: string
  ): Promise<void> {
    console.log(`\n📊 Benchmarking ${workerName} - ${description}`)
    console.log(`   URL: ${url}`)
    console.log(`   Samples: ${samples}`)

    const responseTimes: number[] = []
    let successCount = 0
    let errorCount = 0

    const startTime = performance.now()

    for (let i = 0; i < samples; i++) {
      const requestStart = performance.now()
      
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Syndicaps-Benchmark/1.0',
            'Accept': '*/*'
          }
        })

        const requestEnd = performance.now()
        const responseTime = requestEnd - requestStart

        responseTimes.push(responseTime)

        if (response.ok) {
          successCount++
        } else {
          errorCount++
        }

        // Small delay to avoid overwhelming the worker
        if (i < samples - 1) {
          await new Promise(resolve => setTimeout(resolve, 10))
        }

      } catch (error) {
        const requestEnd = performance.now()
        responseTimes.push(requestEnd - requestStart)
        errorCount++
      }

      // Progress indicator
      if ((i + 1) % 10 === 0) {
        process.stdout.write(`   Progress: ${i + 1}/${samples}\r`)
      }
    }

    const endTime = performance.now()
    const totalDuration = endTime - startTime

    // Calculate metrics
    responseTimes.sort((a, b) => a - b)
    const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
    const minResponseTime = responseTimes[0]
    const maxResponseTime = responseTimes[responseTimes.length - 1]
    const p95Index = Math.floor(responseTimes.length * 0.95)
    const p95ResponseTime = responseTimes[p95Index]
    const throughput = (samples / totalDuration) * 1000 // requests per second
    const errorRate = (errorCount / samples) * 100
    const successRate = (successCount / samples) * 100

    const result: BenchmarkResult = {
      worker: workerName,
      endpoint: `${description} (${url})`,
      metrics: {
        averageResponseTime,
        minResponseTime,
        maxResponseTime,
        p95ResponseTime,
        throughput,
        errorRate,
        successRate
      },
      samples,
      timestamp: new Date().toISOString()
    }

    this.results.push(result)

    // Display results
    console.log(`\n   Results:`)
    console.log(`     Average Response Time: ${averageResponseTime.toFixed(2)}ms`)
    console.log(`     Min Response Time: ${minResponseTime.toFixed(2)}ms`)
    console.log(`     Max Response Time: ${maxResponseTime.toFixed(2)}ms`)
    console.log(`     95th Percentile: ${p95ResponseTime.toFixed(2)}ms`)
    console.log(`     Throughput: ${throughput.toFixed(2)} req/s`)
    console.log(`     Success Rate: ${successRate.toFixed(1)}%`)
    console.log(`     Error Rate: ${errorRate.toFixed(1)}%`)
  }

  private async runConcurrentBenchmark(): Promise<void> {
    console.log(`\n🔄 Running Concurrent Load Benchmark`)
    
    const concurrentUsers = [5, 10, 20]
    const requestsPerUser = 10

    for (const users of concurrentUsers) {
      console.log(`\n   Testing with ${users} concurrent users...`)
      
      const promises: Promise<any>[] = []
      const startTime = performance.now()

      for (let user = 0; user < users; user++) {
        promises.push(this.simulateUser(requestsPerUser))
      }

      const results = await Promise.all(promises)
      const endTime = performance.now()

      // Aggregate results
      const totalRequests = results.reduce((sum, r) => sum + r.totalRequests, 0)
      const totalSuccesses = results.reduce((sum, r) => sum + r.successCount, 0)
      const totalErrors = results.reduce((sum, r) => sum + r.errorCount, 0)
      const averageResponseTime = results.reduce((sum, r) => sum + r.averageResponseTime, 0) / results.length

      const totalDuration = endTime - startTime
      const throughput = (totalRequests / totalDuration) * 1000
      const successRate = (totalSuccesses / totalRequests) * 100

      console.log(`     Total Requests: ${totalRequests}`)
      console.log(`     Success Rate: ${successRate.toFixed(1)}%`)
      console.log(`     Average Response Time: ${averageResponseTime.toFixed(2)}ms`)
      console.log(`     Throughput: ${throughput.toFixed(2)} req/s`)

      // Store concurrent test result
      this.results.push({
        worker: 'Concurrent Test',
        endpoint: `${users} users × ${requestsPerUser} requests`,
        metrics: {
          averageResponseTime,
          minResponseTime: 0,
          maxResponseTime: 0,
          p95ResponseTime: 0,
          throughput,
          errorRate: ((totalErrors / totalRequests) * 100),
          successRate
        },
        samples: totalRequests,
        timestamp: new Date().toISOString()
      })
    }
  }

  private async simulateUser(requests: number): Promise<any> {
    const responseTimes: number[] = []
    let successCount = 0
    let errorCount = 0

    for (let i = 0; i < requests; i++) {
      const url = Math.random() > 0.5 
        ? `${this.apiCacheUrl}/health`
        : `${this.imageOptimizerUrl}/test.jpg`

      const startTime = performance.now()
      
      try {
        const response = await fetch(url)
        const endTime = performance.now()
        
        responseTimes.push(endTime - startTime)
        
        if (response.ok) {
          successCount++
        } else {
          errorCount++
        }
      } catch (error) {
        const endTime = performance.now()
        responseTimes.push(endTime - startTime)
        errorCount++
      }

      // Random delay between requests (50-200ms)
      await new Promise(resolve => 
        setTimeout(resolve, Math.random() * 150 + 50)
      )
    }

    return {
      totalRequests: requests,
      successCount,
      errorCount,
      averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
    }
  }

  private generateBenchmarkReport(): void {
    console.log('\n📊 Performance Benchmark Report')
    console.log('=' .repeat(60))

    const totalTests = this.results.length
    const overallSuccessRate = this.results.reduce((sum, r) => sum + r.metrics.successRate, 0) / totalTests
    const averageResponseTime = this.results.reduce((sum, r) => sum + r.metrics.averageResponseTime, 0) / totalTests
    const totalSamples = this.results.reduce((sum, r) => sum + r.samples, 0)

    const report: BenchmarkReport = {
      summary: {
        totalTests,
        overallSuccessRate,
        averageResponseTime,
        totalDuration: totalSamples
      },
      results: this.results,
      recommendations: this.generateRecommendations()
    }

    // Display summary
    console.log(`\nSummary:`)
    console.log(`  Total Tests: ${totalTests}`)
    console.log(`  Total Samples: ${totalSamples}`)
    console.log(`  Overall Success Rate: ${overallSuccessRate.toFixed(1)}%`)
    console.log(`  Average Response Time: ${averageResponseTime.toFixed(2)}ms`)

    // Performance analysis
    console.log(`\n📈 Performance Analysis:`)
    
    this.results.forEach(result => {
      console.log(`\n  ${result.worker} - ${result.endpoint}:`)
      console.log(`    Response Time: ${result.metrics.averageResponseTime.toFixed(2)}ms (95th: ${result.metrics.p95ResponseTime.toFixed(2)}ms)`)
      console.log(`    Throughput: ${result.metrics.throughput.toFixed(2)} req/s`)
      console.log(`    Success Rate: ${result.metrics.successRate.toFixed(1)}%`)
      
      // Performance rating
      const rating = this.getPerformanceRating(result.metrics)
      console.log(`    Rating: ${rating}`)
    })

    // Recommendations
    console.log(`\n💡 Recommendations:`)
    report.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`)
    })

    // Save detailed report
    const reportPath = join(__dirname, '../reports/performance-benchmark.json')
    writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 Detailed report saved to: ${reportPath}`)
  }

  private getPerformanceRating(metrics: any): string {
    const { averageResponseTime, successRate, throughput } = metrics

    if (successRate >= 99 && averageResponseTime <= 100 && throughput >= 10) {
      return '🟢 EXCELLENT'
    } else if (successRate >= 95 && averageResponseTime <= 500 && throughput >= 5) {
      return '🟡 GOOD'
    } else if (successRate >= 90 && averageResponseTime <= 1000 && throughput >= 2) {
      return '🟠 FAIR'
    } else {
      return '🔴 POOR'
    }
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = []

    // Analyze results and generate recommendations
    const apiCacheResults = this.results.filter(r => r.worker === 'API Cache')
    const imageOptimizerResults = this.results.filter(r => r.worker === 'Image Optimizer')

    // API Cache recommendations
    const avgApiResponseTime = apiCacheResults.reduce((sum, r) => sum + r.metrics.averageResponseTime, 0) / apiCacheResults.length
    if (avgApiResponseTime > 500) {
      recommendations.push('Consider optimizing API Cache worker response times')
    }

    // Image Optimizer recommendations
    const avgImageResponseTime = imageOptimizerResults.reduce((sum, r) => sum + r.metrics.averageResponseTime, 0) / imageOptimizerResults.length
    if (avgImageResponseTime > 1000) {
      recommendations.push('Consider optimizing Image Optimizer worker for faster image processing')
    }

    // Overall success rate
    const overallSuccessRate = this.results.reduce((sum, r) => sum + r.metrics.successRate, 0) / this.results.length
    if (overallSuccessRate < 95) {
      recommendations.push('Investigate and fix error scenarios to improve success rate')
    }

    // Throughput recommendations
    const avgThroughput = this.results.reduce((sum, r) => sum + r.metrics.throughput, 0) / this.results.length
    if (avgThroughput < 5) {
      recommendations.push('Consider scaling workers or optimizing for higher throughput')
    }

    // Default recommendations
    if (recommendations.length === 0) {
      recommendations.push('Workers are performing well - ready for production deployment')
      recommendations.push('Monitor performance metrics in production environment')
      recommendations.push('Set up alerting for response time and error rate thresholds')
    }

    return recommendations
  }
}

// Main execution
async function main() {
  const benchmark = new PerformanceBenchmark()
  await benchmark.runBenchmarks()
}

// Export for programmatic use
export { PerformanceBenchmark }

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Benchmark failed:', error)
    process.exit(1)
  })
}
