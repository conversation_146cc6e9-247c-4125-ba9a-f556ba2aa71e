#!/usr/bin/env tsx

/**
 * Production Monitoring Script for Cloudflare Workers
 * Monitors health, performance, and availability of production workers
 */

import { performance } from 'perf_hooks'
import { writeFileSync } from 'fs'
import { join } from 'path'

interface HealthCheckResult {
  worker: string
  url: string
  status: 'HEALTHY' | 'DEGRADED' | 'DOWN'
  responseTime: number
  statusCode: number
  timestamp: string
  error?: string
}

interface MonitoringReport {
  timestamp: string
  summary: {
    totalWorkers: number
    healthyWorkers: number
    degradedWorkers: number
    downWorkers: number
    averageResponseTime: number
  }
  workers: HealthCheckResult[]
  alerts: string[]
}

class ProductionMonitoring {
  private workers = [
    {
      name: 'Image Optimizer (Production)',
      url: 'https://syndicaps-image-optimizer.syndicaps22.workers.dev/health',
      healthEndpoint: true
    },
    {
      name: 'API Cache (Production)',
      url: 'https://syndicaps-api-cache.syndicaps22.workers.dev/health',
      healthEndpoint: true
    },
    {
      name: 'Image Optimizer (Staging)',
      url: 'https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev/health',
      healthEndpoint: true
    },
    {
      name: 'API Cache (Staging)',
      url: 'https://syndicaps-api-cache-staging.syndicaps22.workers.dev/health',
      healthEndpoint: true
    }
  ]

  async runMonitoring(): Promise<void> {
    console.log('🔍 Starting Production Workers Monitoring')
    console.log('=' .repeat(60))
    console.log(`Timestamp: ${new Date().toISOString()}`)

    const results: HealthCheckResult[] = []

    for (const worker of this.workers) {
      console.log(`\n📊 Checking ${worker.name}...`)
      const result = await this.checkWorkerHealth(worker.name, worker.url)
      results.push(result)
      
      const statusIcon = result.status === 'HEALTHY' ? '✅' : 
                        result.status === 'DEGRADED' ? '⚠️' : '❌'
      
      console.log(`   ${statusIcon} Status: ${result.status}`)
      console.log(`   ⏱️  Response Time: ${result.responseTime.toFixed(2)}ms`)
      console.log(`   🌐 Status Code: ${result.statusCode}`)
      
      if (result.error) {
        console.log(`   ❌ Error: ${result.error}`)
      }
    }

    // Generate monitoring report
    const report = this.generateMonitoringReport(results)
    
    // Display summary
    this.displaySummary(report)
    
    // Save report
    this.saveReport(report)
    
    // Check for alerts
    this.checkAlerts(report)
  }

  private async checkWorkerHealth(name: string, url: string): Promise<HealthCheckResult> {
    const startTime = performance.now()
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Syndicaps-Monitor/1.0'
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // Determine health status
      let status: 'HEALTHY' | 'DEGRADED' | 'DOWN'
      
      if (response.ok && responseTime < 1000) {
        status = 'HEALTHY'
      } else if (response.ok && responseTime < 3000) {
        status = 'DEGRADED'
      } else {
        status = 'DOWN'
      }
      
      return {
        worker: name,
        url,
        status,
        responseTime,
        statusCode: response.status,
        timestamp: new Date().toISOString()
      }
      
    } catch (error) {
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      return {
        worker: name,
        url,
        status: 'DOWN',
        responseTime,
        statusCode: 0,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  private generateMonitoringReport(results: HealthCheckResult[]): MonitoringReport {
    const totalWorkers = results.length
    const healthyWorkers = results.filter(r => r.status === 'HEALTHY').length
    const degradedWorkers = results.filter(r => r.status === 'DEGRADED').length
    const downWorkers = results.filter(r => r.status === 'DOWN').length
    const averageResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / totalWorkers
    
    const alerts: string[] = []
    
    // Generate alerts
    results.forEach(result => {
      if (result.status === 'DOWN') {
        alerts.push(`🚨 CRITICAL: ${result.worker} is DOWN`)
      } else if (result.status === 'DEGRADED') {
        alerts.push(`⚠️ WARNING: ${result.worker} is DEGRADED (${result.responseTime.toFixed(2)}ms)`)
      }
      
      if (result.responseTime > 2000) {
        alerts.push(`⏰ SLOW RESPONSE: ${result.worker} took ${result.responseTime.toFixed(2)}ms`)
      }
    })
    
    // System-wide alerts
    if (downWorkers > 0) {
      alerts.push(`🚨 SYSTEM ALERT: ${downWorkers} worker(s) are DOWN`)
    }
    
    if (degradedWorkers > totalWorkers / 2) {
      alerts.push(`⚠️ SYSTEM WARNING: More than 50% of workers are degraded`)
    }
    
    if (averageResponseTime > 1500) {
      alerts.push(`⏰ PERFORMANCE ALERT: Average response time is ${averageResponseTime.toFixed(2)}ms`)
    }
    
    return {
      timestamp: new Date().toISOString(),
      summary: {
        totalWorkers,
        healthyWorkers,
        degradedWorkers,
        downWorkers,
        averageResponseTime
      },
      workers: results,
      alerts
    }
  }

  private displaySummary(report: MonitoringReport): void {
    console.log('\n📊 Monitoring Summary')
    console.log('=' .repeat(40))
    
    const { summary } = report
    
    console.log(`Total Workers: ${summary.totalWorkers}`)
    console.log(`✅ Healthy: ${summary.healthyWorkers} (${((summary.healthyWorkers / summary.totalWorkers) * 100).toFixed(1)}%)`)
    console.log(`⚠️  Degraded: ${summary.degradedWorkers} (${((summary.degradedWorkers / summary.totalWorkers) * 100).toFixed(1)}%)`)
    console.log(`❌ Down: ${summary.downWorkers} (${((summary.downWorkers / summary.totalWorkers) * 100).toFixed(1)}%)`)
    console.log(`⏱️  Average Response Time: ${summary.averageResponseTime.toFixed(2)}ms`)
    
    // Overall system health
    let systemHealth: string
    if (summary.downWorkers === 0 && summary.degradedWorkers === 0) {
      systemHealth = '🟢 EXCELLENT'
    } else if (summary.downWorkers === 0 && summary.degradedWorkers <= summary.totalWorkers / 4) {
      systemHealth = '🟡 GOOD'
    } else if (summary.downWorkers <= summary.totalWorkers / 4) {
      systemHealth = '🟠 DEGRADED'
    } else {
      systemHealth = '🔴 CRITICAL'
    }
    
    console.log(`\n🎯 System Health: ${systemHealth}`)
  }

  private checkAlerts(report: MonitoringReport): void {
    if (report.alerts.length > 0) {
      console.log('\n🚨 ALERTS')
      console.log('=' .repeat(40))
      
      report.alerts.forEach(alert => {
        console.log(`   ${alert}`)
      })
      
      // In a real implementation, you would send these alerts to:
      // - Slack/Discord webhooks
      // - Email notifications
      // - PagerDuty/OpsGenie
      // - Monitoring dashboards
      
      console.log('\n📧 Alert notifications would be sent to:')
      console.log('   - <EMAIL>')
      console.log('   - #alerts Slack channel')
      console.log('   - PagerDuty on-call rotation')
      
    } else {
      console.log('\n✅ No alerts - all systems operating normally')
    }
  }

  private saveReport(report: MonitoringReport): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const reportPath = join(__dirname, `../reports/monitoring-${timestamp}.json`)
    
    writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 Monitoring report saved to: ${reportPath}`)
    
    // Also save as latest report
    const latestReportPath = join(__dirname, '../reports/monitoring-latest.json')
    writeFileSync(latestReportPath, JSON.stringify(report, null, 2))
  }

  async runContinuousMonitoring(intervalMinutes: number = 5): Promise<void> {
    console.log(`🔄 Starting continuous monitoring (every ${intervalMinutes} minutes)`)
    console.log('Press Ctrl+C to stop')
    
    // Run initial check
    await this.runMonitoring()
    
    // Set up interval
    const intervalMs = intervalMinutes * 60 * 1000
    setInterval(async () => {
      console.log('\n' + '='.repeat(80))
      await this.runMonitoring()
    }, intervalMs)
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)
  const monitoring = new ProductionMonitoring()
  
  if (args.includes('--continuous')) {
    const intervalIndex = args.indexOf('--interval')
    const interval = intervalIndex !== -1 ? parseInt(args[intervalIndex + 1]) : 5
    await monitoring.runContinuousMonitoring(interval)
  } else {
    await monitoring.runMonitoring()
  }
}

// Export for programmatic use
export { ProductionMonitoring }

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Monitoring failed:', error)
    process.exit(1)
  })
}
