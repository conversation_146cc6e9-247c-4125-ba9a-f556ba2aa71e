#!/usr/bin/env tsx

/**
 * Production Workers Performance Validation
 * Validates production deployment and measures performance improvements
 */

import { performance } from 'perf_hooks'
import { writeFileSync } from 'fs'
import { join } from 'path'

interface ValidationResult {
  test: string
  status: 'PASS' | 'FAIL' | 'WARNING'
  details: any
  duration: number
  timestamp: string
}

interface PerformanceComparison {
  metric: string
  baseline: number
  current: number
  improvement: number
  improvementPercent: number
  status: 'IMPROVED' | 'DEGRADED' | 'STABLE'
}

interface ValidationReport {
  summary: {
    totalTests: number
    passedTests: number
    failedTests: number
    warningTests: number
    overallStatus: 'PASS' | 'FAIL' | 'WARNING'
  }
  performance: {
    comparisons: PerformanceComparison[]
    overallImprovement: number
  }
  results: ValidationResult[]
  recommendations: string[]
  timestamp: string
}

class ProductionValidation {
  private productionUrls = {
    imageOptimizer: 'https://syndicaps-image-optimizer.syndicaps22.workers.dev',
    apiCache: 'https://syndicaps-api-cache.syndicaps22.workers.dev'
  }

  private stagingUrls = {
    imageOptimizer: 'https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev',
    apiCache: 'https://syndicaps-api-cache-staging.syndicaps22.workers.dev'
  }

  private results: ValidationResult[] = []

  async runValidation(): Promise<void> {
    console.log('🔍 Starting Production Workers Performance Validation')
    console.log('=' .repeat(60))

    try {
      // Basic functionality validation
      await this.validateBasicFunctionality()

      // Performance validation
      await this.validatePerformance()

      // Load testing validation
      await this.validateLoadHandling()

      // Security validation
      await this.validateSecurity()

      // Availability validation
      await this.validateAvailability()

      // Generate comprehensive report
      const report = this.generateValidationReport()
      this.displayReport(report)
      this.saveReport(report)

    } catch (error) {
      console.error('\n❌ Validation failed:', error)
      throw error
    }
  }

  private async validateBasicFunctionality(): Promise<void> {
    console.log('\n🔧 Validating Basic Functionality...')

    // API Cache Health Check
    await this.runValidationTest('API Cache Health Check', async () => {
      const response = await fetch(`${this.productionUrls.apiCache}/health`)
      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`)
      }
      const text = await response.text()
      if (!text.includes('OK')) {
        throw new Error('Health check response invalid')
      }
      return { status: response.status, response: text }
    })

    // API Cache Basic Request
    await this.runValidationTest('API Cache Basic Request', async () => {
      const response = await fetch(`${this.productionUrls.apiCache}/api/test`)
      return { 
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      }
    })

    // Image Optimizer Basic Request
    await this.runValidationTest('Image Optimizer Basic Request', async () => {
      const response = await fetch(`${this.productionUrls.imageOptimizer}/test.jpg`)
      return { 
        status: response.status,
        contentType: response.headers.get('content-type'),
        contentLength: response.headers.get('content-length')
      }
    })

    // Cross-Environment Consistency
    await this.runValidationTest('Cross-Environment Consistency', async () => {
      const prodResponse = await fetch(`${this.productionUrls.apiCache}/health`)
      const stagingResponse = await fetch(`${this.stagingUrls.apiCache}/health`)
      
      const prodText = await prodResponse.text()
      const stagingText = await stagingResponse.text()
      
      if (prodText !== stagingText) {
        throw new Error('Production and staging responses differ')
      }
      
      return { 
        production: prodText,
        staging: stagingText,
        consistent: true
      }
    })
  }

  private async validatePerformance(): Promise<void> {
    console.log('\n🚀 Validating Performance...')

    // Response Time Validation
    await this.runValidationTest('Response Time Performance', async () => {
      const iterations = 10
      const times: number[] = []
      
      for (let i = 0; i < iterations; i++) {
        const start = performance.now()
        await fetch(`${this.productionUrls.apiCache}/health`)
        const end = performance.now()
        times.push(end - start)
      }
      
      const average = times.reduce((a, b) => a + b, 0) / times.length
      const min = Math.min(...times)
      const max = Math.max(...times)
      
      if (average > 2000) {
        throw new Error(`Average response time too high: ${average.toFixed(2)}ms`)
      }
      
      return { average, min, max, samples: iterations }
    })

    // Throughput Validation
    await this.runValidationTest('Throughput Performance', async () => {
      const duration = 10000 // 10 seconds
      const startTime = performance.now()
      let requestCount = 0
      let successCount = 0
      
      while (performance.now() - startTime < duration) {
        try {
          const response = await fetch(`${this.productionUrls.apiCache}/health`)
          requestCount++
          if (response.ok) successCount++
        } catch {
          requestCount++
        }
        
        // Small delay to avoid overwhelming
        await new Promise(resolve => setTimeout(resolve, 50))
      }
      
      const actualDuration = performance.now() - startTime
      const throughput = (requestCount / actualDuration) * 1000
      const successRate = (successCount / requestCount) * 100
      
      if (throughput < 5) {
        throw new Error(`Throughput too low: ${throughput.toFixed(2)} req/s`)
      }
      
      return { throughput, successRate, totalRequests: requestCount }
    })

    // Cache Performance Validation
    await this.runValidationTest('Cache Performance', async () => {
      const endpoint = `${this.productionUrls.apiCache}/api/cache-test-${Date.now()}`
      
      // First request (cache miss)
      const start1 = performance.now()
      const response1 = await fetch(endpoint)
      const time1 = performance.now() - start1
      
      // Second request (should be faster due to caching)
      await new Promise(resolve => setTimeout(resolve, 100))
      const start2 = performance.now()
      const response2 = await fetch(endpoint)
      const time2 = performance.now() - start2
      
      const improvement = ((time1 - time2) / time1) * 100
      
      return {
        firstRequest: time1,
        secondRequest: time2,
        improvement: improvement,
        cacheWorking: time2 < time1
      }
    })
  }

  private async validateLoadHandling(): Promise<void> {
    console.log('\n📊 Validating Load Handling...')

    // Concurrent Requests Validation
    await this.runValidationTest('Concurrent Load Handling', async () => {
      const concurrentRequests = 20
      const promises: Promise<Response>[] = []
      
      const startTime = performance.now()
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(fetch(`${this.productionUrls.apiCache}/api/load-test-${i}`))
      }
      
      const responses = await Promise.all(promises)
      const endTime = performance.now()
      
      const successCount = responses.filter(r => r.ok).length
      const successRate = (successCount / concurrentRequests) * 100
      const totalTime = endTime - startTime
      const averageTime = totalTime / concurrentRequests
      
      if (successRate < 90) {
        throw new Error(`Success rate too low under load: ${successRate.toFixed(1)}%`)
      }
      
      return {
        concurrentRequests,
        successCount,
        successRate,
        totalTime,
        averageTime
      }
    })

    // Sustained Load Validation
    await this.runValidationTest('Sustained Load Handling', async () => {
      const duration = 30000 // 30 seconds
      const requestInterval = 200 // 200ms between requests
      const startTime = performance.now()
      let requestCount = 0
      let successCount = 0
      let errorCount = 0
      
      while (performance.now() - startTime < duration) {
        try {
          const response = await fetch(`${this.productionUrls.apiCache}/api/sustained-${requestCount}`)
          requestCount++
          if (response.ok) {
            successCount++
          } else {
            errorCount++
          }
        } catch {
          requestCount++
          errorCount++
        }
        
        await new Promise(resolve => setTimeout(resolve, requestInterval))
      }
      
      const actualDuration = performance.now() - startTime
      const successRate = (successCount / requestCount) * 100
      const requestsPerSecond = (requestCount / actualDuration) * 1000
      
      if (successRate < 95) {
        throw new Error(`Sustained load success rate too low: ${successRate.toFixed(1)}%`)
      }
      
      return {
        duration: actualDuration,
        totalRequests: requestCount,
        successCount,
        errorCount,
        successRate,
        requestsPerSecond
      }
    })
  }

  private async validateSecurity(): Promise<void> {
    console.log('\n🔒 Validating Security...')

    // HTTPS Validation
    await this.runValidationTest('HTTPS Security', async () => {
      const response = await fetch(`${this.productionUrls.apiCache}/health`)
      const protocol = new URL(this.productionUrls.apiCache).protocol
      
      if (protocol !== 'https:') {
        throw new Error('Worker not using HTTPS')
      }
      
      return {
        protocol,
        secure: true,
        headers: {
          strictTransportSecurity: response.headers.get('strict-transport-security'),
          contentSecurityPolicy: response.headers.get('content-security-policy')
        }
      }
    })

    // Rate Limiting Validation
    await this.runValidationTest('Rate Limiting', async () => {
      const rapidRequests = 50
      const promises: Promise<Response>[] = []
      
      for (let i = 0; i < rapidRequests; i++) {
        promises.push(fetch(`${this.productionUrls.apiCache}/api/rate-limit-test`))
      }
      
      const responses = await Promise.all(promises)
      const rateLimitedCount = responses.filter(r => r.status === 429).length
      
      return {
        totalRequests: rapidRequests,
        rateLimitedRequests: rateLimitedCount,
        rateLimitingActive: rateLimitedCount > 0
      }
    })
  }

  private async validateAvailability(): Promise<void> {
    console.log('\n🌐 Validating Availability...')

    // Multi-Region Availability
    await this.runValidationTest('Multi-Region Availability', async () => {
      const regions = ['us', 'eu', 'asia'] // Simulated by different request patterns
      const results: any[] = []
      
      for (const region of regions) {
        const start = performance.now()
        const response = await fetch(`${this.productionUrls.apiCache}/health`, {
          headers: { 'CF-IPCountry': region.toUpperCase() }
        })
        const responseTime = performance.now() - start
        
        results.push({
          region,
          available: response.ok,
          responseTime,
          status: response.status
        })
      }
      
      const availableRegions = results.filter(r => r.available).length
      const averageResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length
      
      return {
        regions: results,
        availableRegions,
        totalRegions: regions.length,
        globalAvailability: (availableRegions / regions.length) * 100,
        averageResponseTime
      }
    })

    // Uptime Validation
    await this.runValidationTest('Uptime Validation', async () => {
      const checks = 10
      const interval = 1000 // 1 second
      let successCount = 0
      
      for (let i = 0; i < checks; i++) {
        try {
          const response = await fetch(`${this.productionUrls.apiCache}/health`)
          if (response.ok) successCount++
        } catch {
          // Request failed
        }
        
        if (i < checks - 1) {
          await new Promise(resolve => setTimeout(resolve, interval))
        }
      }
      
      const uptime = (successCount / checks) * 100
      
      if (uptime < 99) {
        throw new Error(`Uptime too low: ${uptime.toFixed(1)}%`)
      }
      
      return {
        totalChecks: checks,
        successfulChecks: successCount,
        uptime
      }
    })
  }

  private async runValidationTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = performance.now()
    
    try {
      const result = await testFn()
      const endTime = performance.now()
      
      this.results.push({
        test: name,
        status: 'PASS',
        details: result,
        duration: endTime - startTime,
        timestamp: new Date().toISOString()
      })
      
      console.log(`   ✅ ${name} (${(endTime - startTime).toFixed(2)}ms)`)
    } catch (error) {
      const endTime = performance.now()
      
      this.results.push({
        test: name,
        status: 'FAIL',
        details: { error: error instanceof Error ? error.message : String(error) },
        duration: endTime - startTime,
        timestamp: new Date().toISOString()
      })
      
      console.log(`   ❌ ${name} (${(endTime - startTime).toFixed(2)}ms): ${error}`)
    }
  }

  private generateValidationReport(): ValidationReport {
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.status === 'PASS').length
    const failedTests = this.results.filter(r => r.status === 'FAIL').length
    const warningTests = this.results.filter(r => r.status === 'WARNING').length
    
    let overallStatus: 'PASS' | 'FAIL' | 'WARNING'
    if (failedTests === 0 && warningTests === 0) {
      overallStatus = 'PASS'
    } else if (failedTests === 0) {
      overallStatus = 'WARNING'
    } else {
      overallStatus = 'FAIL'
    }
    
    // Generate performance comparisons (baseline vs current)
    const comparisons: PerformanceComparison[] = [
      {
        metric: 'Average Response Time',
        baseline: 1000, // Baseline 1000ms
        current: this.getAverageResponseTime(),
        improvement: 0,
        improvementPercent: 0,
        status: 'STABLE'
      }
    ]
    
    // Calculate improvements
    comparisons.forEach(comp => {
      comp.improvement = comp.baseline - comp.current
      comp.improvementPercent = (comp.improvement / comp.baseline) * 100
      comp.status = comp.improvement > 0 ? 'IMPROVED' : 
                   comp.improvement < 0 ? 'DEGRADED' : 'STABLE'
    })
    
    const overallImprovement = comparisons.reduce((sum, comp) => sum + comp.improvementPercent, 0) / comparisons.length
    
    const recommendations = this.generateRecommendations()
    
    return {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        warningTests,
        overallStatus
      },
      performance: {
        comparisons,
        overallImprovement
      },
      results: this.results,
      recommendations,
      timestamp: new Date().toISOString()
    }
  }

  private getAverageResponseTime(): number {
    const performanceTests = this.results.filter(r => 
      r.test.includes('Response Time') && r.status === 'PASS'
    )
    
    if (performanceTests.length === 0) return 0
    
    const totalTime = performanceTests.reduce((sum, test) => {
      return sum + (test.details?.average || test.duration)
    }, 0)
    
    return totalTime / performanceTests.length
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = []
    
    const failedTests = this.results.filter(r => r.status === 'FAIL')
    const passRate = (this.results.filter(r => r.status === 'PASS').length / this.results.length) * 100
    
    if (failedTests.length > 0) {
      recommendations.push('Address failed validation tests before full production rollout')
      failedTests.forEach(test => {
        recommendations.push(`- Fix: ${test.test}`)
      })
    }
    
    if (passRate >= 90) {
      recommendations.push('Workers are ready for full production traffic')
      recommendations.push('Implement continuous monitoring and alerting')
      recommendations.push('Set up performance baselines for future comparisons')
    } else if (passRate >= 75) {
      recommendations.push('Address critical issues before increasing production traffic')
      recommendations.push('Implement gradual traffic rollout strategy')
    } else {
      recommendations.push('Significant issues detected - rollback recommended')
      recommendations.push('Conduct thorough investigation before retry')
    }
    
    recommendations.push('Monitor production metrics for 24-48 hours')
    recommendations.push('Set up automated health checks and alerting')
    recommendations.push('Document performance baselines for future reference')
    
    return recommendations
  }

  private displayReport(report: ValidationReport): void {
    console.log('\n📊 Production Validation Report')
    console.log('=' .repeat(60))
    
    const { summary, performance } = report
    
    console.log(`\nSummary:`)
    console.log(`  Total Tests: ${summary.totalTests}`)
    console.log(`  ✅ Passed: ${summary.passedTests} (${((summary.passedTests / summary.totalTests) * 100).toFixed(1)}%)`)
    console.log(`  ❌ Failed: ${summary.failedTests} (${((summary.failedTests / summary.totalTests) * 100).toFixed(1)}%)`)
    console.log(`  ⚠️  Warnings: ${summary.warningTests} (${((summary.warningTests / summary.totalTests) * 100).toFixed(1)}%)`)
    console.log(`  🎯 Overall Status: ${summary.overallStatus}`)
    
    console.log(`\nPerformance:`)
    performance.comparisons.forEach(comp => {
      const icon = comp.status === 'IMPROVED' ? '📈' : 
                   comp.status === 'DEGRADED' ? '📉' : '➡️'
      console.log(`  ${icon} ${comp.metric}: ${comp.current.toFixed(2)}ms (${comp.improvementPercent.toFixed(1)}% change)`)
    })
    
    console.log(`\nRecommendations:`)
    report.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`)
    })
    
    // Final assessment
    const successRate = (summary.passedTests / summary.totalTests) * 100
    console.log(`\n🎯 Final Assessment:`)
    
    if (successRate >= 95) {
      console.log('   ✅ EXCELLENT - Production deployment validated successfully')
    } else if (successRate >= 85) {
      console.log('   ⚠️  GOOD - Minor issues detected, monitor closely')
    } else if (successRate >= 70) {
      console.log('   ⚠️  FAIR - Significant issues require attention')
    } else {
      console.log('   ❌ POOR - Critical issues detected, consider rollback')
    }
  }

  private saveReport(report: ValidationReport): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const reportPath = join(__dirname, `../reports/production-validation-${timestamp}.json`)
    
    writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 Validation report saved to: ${reportPath}`)
  }
}

// Main execution
async function main() {
  const validation = new ProductionValidation()
  await validation.runValidation()
}

// Export for programmatic use
export { ProductionValidation }

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Validation failed:', error)
    process.exit(1)
  })
}
