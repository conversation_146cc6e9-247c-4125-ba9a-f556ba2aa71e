#!/usr/bin/env tsx

/**
 * API Cache Worker Test Runner
 * Comprehensive testing script for validating API cache worker functionality
 */

import { execSync } from 'child_process'
import { performance } from 'perf_hooks'

interface TestResult {
  name: string
  passed: boolean
  duration: number
  coverage?: number
  errors?: string[]
}

interface TestSuite {
  name: string
  tests: TestResult[]
  totalDuration: number
  passRate: number
}

class ApiCacheTestRunner {
  private results: TestSuite[] = []

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting API Cache Worker Test Suite\n')

    try {
      // Run unit tests
      await this.runUnitTests()
      
      // Run integration tests
      await this.runIntegrationTests()
      
      // Run performance tests
      await this.runPerformanceTests()
      
      // Generate test report
      this.generateReport()

    } catch (error) {
      console.error('❌ Test suite failed:', error)
      process.exit(1)
    }
  }

  private async runUnitTests(): Promise<void> {
    console.log('📋 Running Unit Tests...')
    const start = performance.now()

    try {
      const output = execSync('npx vitest run __tests__/api-cache.test.ts --reporter=json', {
        encoding: 'utf-8',
        cwd: process.cwd()
      })

      const results = JSON.parse(output)
      const duration = performance.now() - start

      const testSuite: TestSuite = {
        name: 'Unit Tests',
        tests: this.parseVitestResults(results),
        totalDuration: duration,
        passRate: this.calculatePassRate(results)
      }

      this.results.push(testSuite)
      console.log(`✅ Unit tests completed in ${duration.toFixed(2)}ms\n`)

    } catch (error) {
      console.error('❌ Unit tests failed:', error)
      throw error
    }
  }

  private async runIntegrationTests(): Promise<void> {
    console.log('🔗 Running Integration Tests...')
    const start = performance.now()

    try {
      // Run integration tests with real KV storage simulation
      const output = execSync('npx vitest run __tests__/api-cache.test.ts --grep="Integration Tests" --reporter=json', {
        encoding: 'utf-8',
        cwd: process.cwd()
      })

      const results = JSON.parse(output)
      const duration = performance.now() - start

      const testSuite: TestSuite = {
        name: 'Integration Tests',
        tests: this.parseVitestResults(results),
        totalDuration: duration,
        passRate: this.calculatePassRate(results)
      }

      this.results.push(testSuite)
      console.log(`✅ Integration tests completed in ${duration.toFixed(2)}ms\n`)

    } catch (error) {
      console.error('❌ Integration tests failed:', error)
      throw error
    }
  }

  private async runPerformanceTests(): Promise<void> {
    console.log('⚡ Running Performance Tests...')
    const start = performance.now()

    try {
      // Run performance benchmarks
      const output = execSync('npx vitest run __tests__/api-cache.test.ts --grep="Performance" --reporter=json', {
        encoding: 'utf-8',
        cwd: process.cwd()
      })

      const results = JSON.parse(output)
      const duration = performance.now() - start

      const testSuite: TestSuite = {
        name: 'Performance Tests',
        tests: this.parseVitestResults(results),
        totalDuration: duration,
        passRate: this.calculatePassRate(results)
      }

      this.results.push(testSuite)
      console.log(`✅ Performance tests completed in ${duration.toFixed(2)}ms\n`)

    } catch (error) {
      console.error('❌ Performance tests failed:', error)
      throw error
    }
  }

  private parseVitestResults(results: any): TestResult[] {
    const tests: TestResult[] = []
    
    if (results.testResults) {
      for (const testFile of results.testResults) {
        for (const testSuite of testFile.assertionResults || []) {
          tests.push({
            name: testSuite.title,
            passed: testSuite.status === 'passed',
            duration: testSuite.duration || 0,
            errors: testSuite.status === 'failed' ? [testSuite.failureMessages?.join('\n')] : undefined
          })
        }
      }
    }

    return tests
  }

  private calculatePassRate(results: any): number {
    if (!results.testResults) return 0
    
    let total = 0
    let passed = 0
    
    for (const testFile of results.testResults) {
      for (const test of testFile.assertionResults || []) {
        total++
        if (test.status === 'passed') passed++
      }
    }
    
    return total > 0 ? (passed / total) * 100 : 0
  }

  private generateReport(): void {
    console.log('📊 Test Report')
    console.log('=' .repeat(50))

    let totalTests = 0
    let totalPassed = 0
    let totalDuration = 0

    for (const suite of this.results) {
      console.log(`\n📁 ${suite.name}`)
      console.log(`   Duration: ${suite.totalDuration.toFixed(2)}ms`)
      console.log(`   Pass Rate: ${suite.passRate.toFixed(1)}%`)
      console.log(`   Tests: ${suite.tests.length}`)

      totalTests += suite.tests.length
      totalPassed += Math.round((suite.passRate / 100) * suite.tests.length)
      totalDuration += suite.totalDuration

      // Show failed tests
      const failedTests = suite.tests.filter(test => !test.passed)
      if (failedTests.length > 0) {
        console.log(`   ❌ Failed Tests:`)
        for (const test of failedTests) {
          console.log(`      - ${test.name}`)
          if (test.errors) {
            test.errors.forEach(error => console.log(`        ${error}`))
          }
        }
      }
    }

    console.log('\n' + '=' .repeat(50))
    console.log('📈 Overall Summary')
    console.log(`   Total Tests: ${totalTests}`)
    console.log(`   Passed: ${totalPassed}`)
    console.log(`   Failed: ${totalTests - totalPassed}`)
    console.log(`   Pass Rate: ${totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0}%`)
    console.log(`   Total Duration: ${totalDuration.toFixed(2)}ms`)

    // Performance benchmarks
    console.log('\n⚡ Performance Benchmarks')
    console.log('   Target: Cache hits < 50ms')
    console.log('   Target: Cache misses < 500ms')
    console.log('   Target: Rate limiting < 100ms')
    console.log('   Target: Overall throughput > 1000 req/s')

    // Coverage report
    this.generateCoverageReport()

    // Exit with appropriate code
    const overallPassRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0
    if (overallPassRate < 90) {
      console.log('\n❌ Test suite failed - pass rate below 90%')
      process.exit(1)
    } else {
      console.log('\n✅ All tests passed successfully!')
      process.exit(0)
    }
  }

  private generateCoverageReport(): void {
    try {
      console.log('\n📊 Coverage Report')
      execSync('npx vitest run --coverage --reporter=text', {
        stdio: 'inherit',
        cwd: process.cwd()
      })
    } catch (error) {
      console.log('   Coverage report generation failed')
    }
  }
}

// Validation functions for specific test scenarios
export class ApiCacheValidator {
  static validateCacheHitRatio(metrics: any): boolean {
    const hitRatio = metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)
    return hitRatio >= 0.8 // 80% hit ratio target
  }

  static validateResponseTime(responseTime: number, cacheStatus: string): boolean {
    if (cacheStatus === 'HIT') {
      return responseTime < 50 // Cache hits should be very fast
    } else if (cacheStatus === 'MISS') {
      return responseTime < 500 // Cache misses should be reasonable
    }
    return responseTime < 1000 // Other operations
  }

  static validateRateLimit(requests: number, timeWindow: number, limit: number): boolean {
    const requestsPerSecond = requests / (timeWindow / 1000)
    const limitPerSecond = limit / 3600 // Convert hourly limit to per second
    return requestsPerSecond <= limitPerSecond
  }

  static validateCacheSize(totalSize: number, maxSize: number = 100 * 1024 * 1024): boolean {
    return totalSize <= maxSize // 100MB default max cache size
  }

  static validateErrorRate(errors: number, total: number): boolean {
    const errorRate = total > 0 ? errors / total : 0
    return errorRate <= 0.01 // 1% error rate threshold
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const runner = new ApiCacheTestRunner()
  runner.runAllTests().catch(error => {
    console.error('Test runner failed:', error)
    process.exit(1)
  })
}
