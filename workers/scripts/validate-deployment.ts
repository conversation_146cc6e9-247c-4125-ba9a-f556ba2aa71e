#!/usr/bin/env node

/**
 * Deployment validation script for Image Optimization Worker
 * Validates worker functionality after deployment
 */

interface ValidationResult {
  test: string
  status: 'PASS' | 'FAIL' | 'WARN'
  message: string
  duration: number
  details?: any
}

interface ValidationReport {
  timestamp: string
  environment: string
  workerUrl: string
  results: ValidationResult[]
  summary: {
    total: number
    passed: number
    failed: number
    warnings: number
    success: boolean
  }
}

class DeploymentValidator {
  private workerUrl: string
  private results: ValidationResult[] = []

  constructor(workerUrl: string) {
    this.workerUrl = workerUrl
  }

  async validateDeployment(): Promise<ValidationReport> {
    console.log('🔍 Validating Image Optimization Worker Deployment')
    console.log(`🌐 Worker URL: ${this.workerUrl}`)
    console.log('=' .repeat(60))

    try {
      // Basic connectivity tests
      await this.testHealthCheck()
      await this.testCorsHeaders()
      await this.testMethodValidation()

      // Functionality tests
      await this.testImageOptimization()
      await this.testParameterValidation()
      await this.testErrorHandling()
      await this.testCaching()

      // Performance tests
      await this.testResponseTimes()
      await this.testConcurrentRequests()

      // Generate report
      const report = this.generateReport()
      this.printSummary(report)

      return report

    } catch (error) {
      console.error('❌ Validation failed:', error)
      throw error
    }
  }

  private async testHealthCheck(): Promise<void> {
    const test = 'Health Check'
    const startTime = Date.now()

    try {
      const response = await fetch(`${this.workerUrl}/health`)
      const duration = Date.now() - startTime

      if (response.ok && await response.text() === 'OK') {
        this.results.push({
          test,
          status: 'PASS',
          message: 'Health check endpoint responding correctly',
          duration
        })
      } else {
        this.results.push({
          test,
          status: 'FAIL',
          message: `Health check failed: ${response.status} ${response.statusText}`,
          duration
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Health check error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testCorsHeaders(): Promise<void> {
    const test = 'CORS Headers'
    const startTime = Date.now()

    try {
      const response = await fetch(`${this.workerUrl}/test.jpg`, {
        method: 'OPTIONS'
      })
      const duration = Date.now() - startTime

      const corsOrigin = response.headers.get('Access-Control-Allow-Origin')
      const corsMethods = response.headers.get('Access-Control-Allow-Methods')

      if (corsOrigin === '*' && corsMethods?.includes('GET')) {
        this.results.push({
          test,
          status: 'PASS',
          message: 'CORS headers configured correctly',
          duration,
          details: { corsOrigin, corsMethods }
        })
      } else {
        this.results.push({
          test,
          status: 'FAIL',
          message: 'CORS headers missing or incorrect',
          duration,
          details: { corsOrigin, corsMethods }
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `CORS test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testMethodValidation(): Promise<void> {
    const test = 'HTTP Method Validation'
    const startTime = Date.now()

    try {
      const response = await fetch(`${this.workerUrl}/test.jpg`, {
        method: 'POST'
      })
      const duration = Date.now() - startTime

      if (response.status === 405) {
        this.results.push({
          test,
          status: 'PASS',
          message: 'Unsupported HTTP methods properly rejected',
          duration
        })
      } else {
        this.results.push({
          test,
          status: 'FAIL',
          message: `Expected 405 for POST method, got ${response.status}`,
          duration
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Method validation test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testImageOptimization(): Promise<void> {
    const test = 'Image Optimization'
    const startTime = Date.now()

    try {
      // Test with transformation parameters
      const response = await fetch(`${this.workerUrl}/w=300,h=200,q=85,f=webp/sample.jpg`)
      const duration = Date.now() - startTime

      if (response.ok) {
        const contentType = response.headers.get('Content-Type')
        const optimized = response.headers.get('X-Image-Optimized')
        
        if (contentType?.includes('image') && optimized === 'true') {
          this.results.push({
            test,
            status: 'PASS',
            message: 'Image optimization working correctly',
            duration,
            details: { contentType, optimized }
          })
        } else {
          this.results.push({
            test,
            status: 'WARN',
            message: 'Image optimization may not be working as expected',
            duration,
            details: { contentType, optimized }
          })
        }
      } else if (response.status === 404) {
        this.results.push({
          test,
          status: 'WARN',
          message: 'Sample image not found (expected for validation)',
          duration
        })
      } else {
        this.results.push({
          test,
          status: 'FAIL',
          message: `Image optimization failed: ${response.status} ${response.statusText}`,
          duration
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Image optimization test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testParameterValidation(): Promise<void> {
    const test = 'Parameter Validation'
    const startTime = Date.now()

    try {
      // Test invalid width parameter
      const response = await fetch(`${this.workerUrl}/w=5000/test.jpg`)
      const duration = Date.now() - startTime

      if (response.status === 400) {
        const errorData = await response.json()
        
        if (errorData.error?.includes('Width')) {
          this.results.push({
            test,
            status: 'PASS',
            message: 'Parameter validation working correctly',
            duration,
            details: errorData
          })
        } else {
          this.results.push({
            test,
            status: 'WARN',
            message: 'Parameter validation response format unexpected',
            duration,
            details: errorData
          })
        }
      } else {
        this.results.push({
          test,
          status: 'FAIL',
          message: `Expected 400 for invalid parameters, got ${response.status}`,
          duration
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Parameter validation test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testErrorHandling(): Promise<void> {
    const test = 'Error Handling'
    const startTime = Date.now()

    try {
      // Test non-existent image
      const response = await fetch(`${this.workerUrl}/nonexistent-image.jpg`)
      const duration = Date.now() - startTime

      if (response.status === 404) {
        this.results.push({
          test,
          status: 'PASS',
          message: 'Error handling working correctly',
          duration
        })
      } else {
        this.results.push({
          test,
          status: 'FAIL',
          message: `Expected 404 for non-existent image, got ${response.status}`,
          duration
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Error handling test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testCaching(): Promise<void> {
    const test = 'Caching Headers'
    const startTime = Date.now()

    try {
      const response = await fetch(`${this.workerUrl}/w=300/sample.jpg`)
      const duration = Date.now() - startTime

      const cacheControl = response.headers.get('Cache-Control')
      const cacheStatus = response.headers.get('X-Cache-Status')

      if (cacheControl && (cacheStatus === 'HIT' || cacheStatus === 'MISS')) {
        this.results.push({
          test,
          status: 'PASS',
          message: 'Caching headers present and correct',
          duration,
          details: { cacheControl, cacheStatus }
        })
      } else {
        this.results.push({
          test,
          status: 'WARN',
          message: 'Caching headers missing or unexpected',
          duration,
          details: { cacheControl, cacheStatus }
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Caching test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testResponseTimes(): Promise<void> {
    const test = 'Response Times'
    const startTime = Date.now()

    try {
      const testRequests = Array.from({ length: 5 }, () => 
        fetch(`${this.workerUrl}/w=200/test-${Math.random()}.jpg`)
      )

      const responses = await Promise.all(testRequests)
      const duration = Date.now() - startTime
      const avgResponseTime = duration / testRequests.length

      if (avgResponseTime < 5000) { // 5 seconds threshold
        this.results.push({
          test,
          status: 'PASS',
          message: `Average response time: ${avgResponseTime.toFixed(0)}ms`,
          duration,
          details: { avgResponseTime, totalRequests: testRequests.length }
        })
      } else {
        this.results.push({
          test,
          status: 'WARN',
          message: `Slow response times: ${avgResponseTime.toFixed(0)}ms average`,
          duration,
          details: { avgResponseTime, totalRequests: testRequests.length }
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Response time test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private async testConcurrentRequests(): Promise<void> {
    const test = 'Concurrent Requests'
    const startTime = Date.now()

    try {
      const concurrentRequests = Array.from({ length: 10 }, (_, i) => 
        fetch(`${this.workerUrl}/w=150/concurrent-${i}.jpg`)
      )

      const responses = await Promise.all(concurrentRequests)
      const duration = Date.now() - startTime

      const successCount = responses.filter(r => r.ok || r.status === 404).length
      const successRate = (successCount / responses.length) * 100

      if (successRate >= 90) {
        this.results.push({
          test,
          status: 'PASS',
          message: `Concurrent requests handled successfully: ${successRate}%`,
          duration,
          details: { successRate, totalRequests: responses.length }
        })
      } else {
        this.results.push({
          test,
          status: 'WARN',
          message: `Low success rate for concurrent requests: ${successRate}%`,
          duration,
          details: { successRate, totalRequests: responses.length }
        })
      }
    } catch (error) {
      this.results.push({
        test,
        status: 'FAIL',
        message: `Concurrent requests test error: ${error.message}`,
        duration: Date.now() - startTime
      })
    }
  }

  private generateReport(): ValidationReport {
    const total = this.results.length
    const passed = this.results.filter(r => r.status === 'PASS').length
    const failed = this.results.filter(r => r.status === 'FAIL').length
    const warnings = this.results.filter(r => r.status === 'WARN').length

    return {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'production',
      workerUrl: this.workerUrl,
      results: this.results,
      summary: {
        total,
        passed,
        failed,
        warnings,
        success: failed === 0
      }
    }
  }

  private printSummary(report: ValidationReport): void {
    console.log('\n' + '='.repeat(60))
    console.log('📊 DEPLOYMENT VALIDATION SUMMARY')
    console.log('='.repeat(60))
    
    console.log(`Total Tests: ${report.summary.total}`)
    console.log(`Passed: ${report.summary.passed} ✅`)
    console.log(`Failed: ${report.summary.failed} ${report.summary.failed > 0 ? '❌' : '✅'}`)
    console.log(`Warnings: ${report.summary.warnings} ${report.summary.warnings > 0 ? '⚠️' : '✅'}`)
    console.log(`Status: ${report.summary.success ? 'SUCCESS ✅' : 'FAILED ❌'}`)

    console.log('\n📋 DETAILED RESULTS:')
    report.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌'
      console.log(`  ${icon} ${result.test}: ${result.message} (${result.duration}ms)`)
    })

    console.log('\n' + '='.repeat(60))
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const workerUrl = process.argv[2] || 'https://images.syndicaps.com'
  
  const validator = new DeploymentValidator(workerUrl)
  
  validator.validateDeployment()
    .then(report => {
      process.exit(report.summary.success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

export { DeploymentValidator }
