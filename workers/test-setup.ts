/**
 * Test setup for Image Optimization Worker tests
 */

import { vi } from 'vitest'

// Mock global objects that are available in Cloudflare Workers runtime
global.Request = class MockRequest {
  url: string
  method: string
  headers: Headers
  body?: ReadableStream

  constructor(input: string | Request, init?: RequestInit) {
    if (typeof input === 'string') {
      this.url = input
    } else {
      this.url = input.url
    }
    
    this.method = init?.method || 'GET'
    this.headers = new Headers(init?.headers)
    
    if (init?.body) {
      this.body = new ReadableStream({
        start(controller) {
          controller.enqueue(init.body)
          controller.close()
        }
      })
    }
  }

  async text() {
    return ''
  }

  async json() {
    return {}
  }

  async arrayBuffer() {
    return new ArrayBuffer(0)
  }

  clone() {
    return new MockRequest(this.url, {
      method: this.method,
      headers: this.headers
    })
  }
} as any

global.Response = class MockResponse {
  status: number
  statusText: string
  headers: Headers
  body?: ReadableStream | ArrayBuffer | string

  constructor(body?: BodyInit | null, init?: ResponseInit) {
    this.status = init?.status || 200
    this.statusText = init?.statusText || 'OK'
    this.headers = new Headers(init?.headers)
    this.body = body
  }

  async text() {
    if (typeof this.body === 'string') {
      return this.body
    }
    return ''
  }

  async json() {
    if (typeof this.body === 'string') {
      return JSON.parse(this.body)
    }
    return {}
  }

  async arrayBuffer() {
    if (this.body instanceof ArrayBuffer) {
      return this.body
    }
    return new ArrayBuffer(0)
  }

  clone() {
    return new MockResponse(this.body, {
      status: this.status,
      statusText: this.statusText,
      headers: this.headers
    })
  }
} as any

global.Headers = class MockHeaders {
  private headers: Map<string, string> = new Map()

  constructor(init?: HeadersInit) {
    if (init) {
      if (Array.isArray(init)) {
        init.forEach(([key, value]) => this.set(key, value))
      } else if (init instanceof Headers) {
        // Copy from another Headers instance
        init.forEach((value, key) => this.set(key, value))
      } else {
        // Object with string keys
        Object.entries(init).forEach(([key, value]) => this.set(key, value))
      }
    }
  }

  get(name: string): string | null {
    return this.headers.get(name.toLowerCase()) || null
  }

  set(name: string, value: string): void {
    this.headers.set(name.toLowerCase(), value)
  }

  has(name: string): boolean {
    return this.headers.has(name.toLowerCase())
  }

  delete(name: string): void {
    this.headers.delete(name.toLowerCase())
  }

  append(name: string, value: string): void {
    const existing = this.get(name)
    if (existing) {
      this.set(name, `${existing}, ${value}`)
    } else {
      this.set(name, value)
    }
  }

  forEach(callback: (value: string, key: string) => void): void {
    this.headers.forEach((value, key) => callback(value, key))
  }

  *[Symbol.iterator](): Iterator<[string, string]> {
    for (const [key, value] of this.headers) {
      yield [key, value]
    }
  }
} as any

// Mock URL constructor
global.URL = class MockURL {
  href: string
  origin: string
  protocol: string
  hostname: string
  port: string
  pathname: string
  search: string
  hash: string
  searchParams: URLSearchParams

  constructor(url: string, base?: string) {
    // Simple URL parsing for testing
    const fullUrl = base ? new URL(url, base).href : url
    
    const urlObj = new URL(fullUrl)
    this.href = urlObj.href
    this.origin = urlObj.origin
    this.protocol = urlObj.protocol
    this.hostname = urlObj.hostname
    this.port = urlObj.port
    this.pathname = urlObj.pathname
    this.search = urlObj.search
    this.hash = urlObj.hash
    this.searchParams = new URLSearchParams(urlObj.search)
  }
} as any

// Mock URLSearchParams
global.URLSearchParams = class MockURLSearchParams {
  private params: Map<string, string> = new Map()

  constructor(init?: string | URLSearchParams | Record<string, string>) {
    if (typeof init === 'string') {
      // Parse query string
      if (init.startsWith('?')) {
        init = init.slice(1)
      }
      init.split('&').forEach(pair => {
        const [key, value] = pair.split('=')
        if (key) {
          this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''))
        }
      })
    } else if (init instanceof URLSearchParams) {
      init.forEach((value, key) => this.params.set(key, value))
    } else if (init && typeof init === 'object') {
      Object.entries(init).forEach(([key, value]) => this.params.set(key, value))
    }
  }

  get(name: string): string | null {
    return this.params.get(name)
  }

  set(name: string, value: string): void {
    this.params.set(name, value)
  }

  has(name: string): boolean {
    return this.params.has(name)
  }

  delete(name: string): void {
    this.params.delete(name)
  }

  forEach(callback: (value: string, key: string) => void): void {
    this.params.forEach((value, key) => callback(value, key))
  }

  toString(): string {
    const pairs: string[] = []
    this.params.forEach((value, key) => {
      pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    })
    return pairs.join('&')
  }
} as any

// Mock FormData
global.FormData = class MockFormData {
  private data: Map<string, any> = new Map()

  append(name: string, value: any): void {
    this.data.set(name, value)
  }

  get(name: string): any {
    return this.data.get(name)
  }

  has(name: string): boolean {
    return this.data.has(name)
  }

  delete(name: string): void {
    this.data.delete(name)
  }
} as any

// Mock Blob
global.Blob = class MockBlob {
  size: number
  type: string
  private data: any

  constructor(blobParts?: BlobPart[], options?: BlobPropertyBag) {
    this.data = blobParts || []
    this.type = options?.type || ''
    this.size = Array.isArray(blobParts) ? blobParts.reduce((size, part) => {
      if (part instanceof ArrayBuffer) {
        return size + part.byteLength
      } else if (typeof part === 'string') {
        return size + part.length
      }
      return size
    }, 0) : 0
  }

  async arrayBuffer(): Promise<ArrayBuffer> {
    if (this.data[0] instanceof ArrayBuffer) {
      return this.data[0]
    }
    return new ArrayBuffer(0)
  }

  async text(): Promise<string> {
    if (typeof this.data[0] === 'string') {
      return this.data[0]
    }
    return ''
  }
} as any

// Mock btoa and atob for base64 encoding/decoding
global.btoa = (str: string): string => {
  return Buffer.from(str, 'binary').toString('base64')
}

global.atob = (str: string): string => {
  return Buffer.from(str, 'base64').toString('binary')
}

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  debug: vi.fn()
}

// Setup test environment
beforeEach(() => {
  vi.clearAllMocks()
})

export {}
