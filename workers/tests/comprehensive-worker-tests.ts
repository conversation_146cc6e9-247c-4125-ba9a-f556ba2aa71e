#!/usr/bin/env tsx

/**
 * Comprehensive Workers Testing Suite
 * Tests both Image Optimizer and API Cache workers with performance validation
 */

import { performance } from 'perf_hooks'

interface TestResult {
  name: string
  status: 'PASS' | 'FAIL' | 'SKIP'
  duration: number
  error?: string
  details?: any
}

interface PerformanceMetrics {
  responseTime: number
  throughput: number
  errorRate: number
  cacheHitRate?: number
}

class WorkerTestSuite {
  private imageOptimizerUrl = 'https://syndicaps-image-optimizer-staging.syndicaps22.workers.dev'
  private apiCacheUrl = 'https://syndicaps-api-cache-staging.syndicaps22.workers.dev'
  private results: TestResult[] = []

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Comprehensive Workers Testing Suite')
    console.log('=' .repeat(60))

    try {
      // Health Check Tests
      await this.runHealthCheckTests()

      // Functional Tests
      await this.runFunctionalTests()

      // Performance Tests
      await this.runPerformanceTests()

      // Error <PERSON>rio Tests
      await this.runErrorScenarioTests()

      // Load Tests
      await this.runLoadTests()

      // Generate Report
      this.generateReport()

    } catch (error) {
      console.error('\n❌ Test suite failed:', error)
      throw error
    }
  }

  private async runHealthCheckTests(): Promise<void> {
    console.log('\n🏥 Running Health Check Tests...')

    // Image Optimizer Health Check
    await this.runTest('Image Optimizer Health Check', async () => {
      const response = await fetch(`${this.imageOptimizerUrl}/health`)
      if (response.status !== 200) {
        throw new Error(`Expected 200, got ${response.status}`)
      }
      return { status: response.status, statusText: response.statusText }
    })

    // API Cache Health Check
    await this.runTest('API Cache Health Check', async () => {
      const response = await fetch(`${this.apiCacheUrl}/health`)
      if (response.status !== 200) {
        throw new Error(`Expected 200, got ${response.status}`)
      }
      const text = await response.text()
      if (!text.includes('OK')) {
        throw new Error('Health check response does not contain OK')
      }
      return { status: response.status, response: text }
    })
  }

  private async runFunctionalTests(): Promise<void> {
    console.log('\n⚙️ Running Functional Tests...')

    // Image Optimizer Basic Functionality
    await this.runTest('Image Optimizer Basic Response', async () => {
      const response = await fetch(`${this.imageOptimizerUrl}/test.jpg`)
      return { 
        status: response.status,
        contentType: response.headers.get('content-type'),
        cacheControl: response.headers.get('cache-control')
      }
    })

    // API Cache Basic Functionality
    await this.runTest('API Cache Basic Response', async () => {
      const response = await fetch(`${this.apiCacheUrl}/api/test`)
      return { 
        status: response.status,
        contentType: response.headers.get('content-type'),
        cacheHeaders: {
          cacheControl: response.headers.get('cache-control'),
          etag: response.headers.get('etag'),
          lastModified: response.headers.get('last-modified')
        }
      }
    })

    // Image Optimizer with Parameters
    await this.runTest('Image Optimizer with Parameters', async () => {
      const response = await fetch(`${this.imageOptimizerUrl}/test.jpg?w=300&h=200&q=80`)
      return { 
        status: response.status,
        contentType: response.headers.get('content-type')
      }
    })

    // API Cache with Different Endpoints
    await this.runTest('API Cache Different Endpoints', async () => {
      const endpoints = ['/api/products', '/api/users', '/api/orders']
      const results = []
      
      for (const endpoint of endpoints) {
        const response = await fetch(`${this.apiCacheUrl}${endpoint}`)
        results.push({
          endpoint,
          status: response.status,
          cacheControl: response.headers.get('cache-control')
        })
      }
      
      return results
    })
  }

  private async runPerformanceTests(): Promise<void> {
    console.log('\n🚀 Running Performance Tests...')

    // Image Optimizer Performance
    await this.runTest('Image Optimizer Performance', async () => {
      const metrics = await this.measurePerformance(
        `${this.imageOptimizerUrl}/test.jpg?w=100&h=100`,
        10
      )
      
      if (metrics.responseTime > 2000) {
        throw new Error(`Response time too high: ${metrics.responseTime}ms`)
      }
      
      return metrics
    })

    // API Cache Performance
    await this.runTest('API Cache Performance', async () => {
      const metrics = await this.measurePerformance(
        `${this.apiCacheUrl}/api/test`,
        10
      )
      
      if (metrics.responseTime > 1000) {
        throw new Error(`Response time too high: ${metrics.responseTime}ms`)
      }
      
      return metrics
    })

    // Cache Hit Rate Test
    await this.runTest('API Cache Hit Rate', async () => {
      const endpoint = `${this.apiCacheUrl}/api/cache-test`
      
      // First request (cache miss)
      const firstResponse = await fetch(endpoint)
      const firstCacheStatus = firstResponse.headers.get('cf-cache-status')
      
      // Second request (should be cache hit)
      await new Promise(resolve => setTimeout(resolve, 100))
      const secondResponse = await fetch(endpoint)
      const secondCacheStatus = secondResponse.headers.get('cf-cache-status')
      
      return {
        firstRequest: { status: firstResponse.status, cacheStatus: firstCacheStatus },
        secondRequest: { status: secondResponse.status, cacheStatus: secondCacheStatus }
      }
    })
  }

  private async runErrorScenarioTests(): Promise<void> {
    console.log('\n🚨 Running Error Scenario Tests...')

    // Invalid Image Request
    await this.runTest('Image Optimizer Invalid Request', async () => {
      const response = await fetch(`${this.imageOptimizerUrl}/nonexistent.jpg`)
      return { 
        status: response.status,
        contentType: response.headers.get('content-type')
      }
    })

    // Invalid API Request
    await this.runTest('API Cache Invalid Request', async () => {
      const response = await fetch(`${this.apiCacheUrl}/api/nonexistent`)
      return { 
        status: response.status,
        contentType: response.headers.get('content-type')
      }
    })

    // Malformed Parameters
    await this.runTest('Image Optimizer Malformed Parameters', async () => {
      const response = await fetch(`${this.imageOptimizerUrl}/test.jpg?w=invalid&h=abc`)
      return { 
        status: response.status,
        contentType: response.headers.get('content-type')
      }
    })

    // Large Request Test
    await this.runTest('API Cache Large Request', async () => {
      const largeData = 'x'.repeat(10000)
      const response = await fetch(`${this.apiCacheUrl}/api/test`, {
        method: 'POST',
        body: largeData,
        headers: { 'Content-Type': 'text/plain' }
      })
      return { 
        status: response.status,
        contentLength: response.headers.get('content-length')
      }
    })
  }

  private async runLoadTests(): Promise<void> {
    console.log('\n📊 Running Load Tests...')

    // Concurrent Requests Test
    await this.runTest('Concurrent Requests Load Test', async () => {
      const concurrentRequests = 20
      const promises = []
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          fetch(`${this.apiCacheUrl}/api/load-test-${i}`)
        )
      }
      
      const startTime = performance.now()
      const responses = await Promise.all(promises)
      const endTime = performance.now()
      
      const successCount = responses.filter(r => r.status === 200).length
      const errorCount = responses.length - successCount
      
      return {
        totalRequests: concurrentRequests,
        successCount,
        errorCount,
        totalTime: endTime - startTime,
        averageTime: (endTime - startTime) / concurrentRequests
      }
    })

    // Sustained Load Test
    await this.runTest('Sustained Load Test', async () => {
      const duration = 30000 // 30 seconds
      const requestInterval = 100 // 100ms between requests
      const startTime = performance.now()
      let requestCount = 0
      let successCount = 0
      let errorCount = 0
      
      while (performance.now() - startTime < duration) {
        try {
          const response = await fetch(`${this.apiCacheUrl}/api/sustained-test`)
          requestCount++
          if (response.status === 200) {
            successCount++
          } else {
            errorCount++
          }
        } catch (error) {
          requestCount++
          errorCount++
        }
        
        await new Promise(resolve => setTimeout(resolve, requestInterval))
      }
      
      const totalTime = performance.now() - startTime
      
      return {
        duration: totalTime,
        totalRequests: requestCount,
        successCount,
        errorCount,
        requestsPerSecond: (requestCount / totalTime) * 1000,
        successRate: (successCount / requestCount) * 100
      }
    })
  }

  private async measurePerformance(url: string, iterations: number): Promise<PerformanceMetrics> {
    const times: number[] = []
    let errorCount = 0
    
    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now()
      try {
        const response = await fetch(url)
        const endTime = performance.now()
        times.push(endTime - startTime)
        
        if (!response.ok) {
          errorCount++
        }
      } catch (error) {
        const endTime = performance.now()
        times.push(endTime - startTime)
        errorCount++
      }
    }
    
    const averageTime = times.reduce((a, b) => a + b, 0) / times.length
    const throughput = (iterations / (times.reduce((a, b) => a + b, 0) / 1000))
    const errorRate = (errorCount / iterations) * 100
    
    return {
      responseTime: averageTime,
      throughput,
      errorRate
    }
  }

  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = performance.now()
    
    try {
      const result = await testFn()
      const endTime = performance.now()
      
      this.results.push({
        name,
        status: 'PASS',
        duration: endTime - startTime,
        details: result
      })
      
      console.log(`   ✅ ${name} (${(endTime - startTime).toFixed(2)}ms)`)
    } catch (error) {
      const endTime = performance.now()
      
      this.results.push({
        name,
        status: 'FAIL',
        duration: endTime - startTime,
        error: error instanceof Error ? error.message : String(error)
      })
      
      console.log(`   ❌ ${name} (${(endTime - startTime).toFixed(2)}ms): ${error}`)
    }
  }

  private generateReport(): void {
    console.log('\n📊 Test Results Summary')
    console.log('=' .repeat(60))
    
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.status === 'PASS').length
    const failedTests = this.results.filter(r => r.status === 'FAIL').length
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)
    
    console.log(`Total Tests: ${totalTests}`)
    console.log(`Passed: ${passedTests} (${((passedTests / totalTests) * 100).toFixed(1)}%)`)
    console.log(`Failed: ${failedTests} (${((failedTests / totalTests) * 100).toFixed(1)}%)`)
    console.log(`Total Duration: ${totalDuration.toFixed(2)}ms`)
    console.log(`Average Test Duration: ${(totalDuration / totalTests).toFixed(2)}ms`)
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:')
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`   - ${r.name}: ${r.error}`)
        })
    }
    
    console.log('\n📈 Performance Summary:')
    const performanceTests = this.results.filter(r => 
      r.name.includes('Performance') && r.status === 'PASS'
    )
    
    performanceTests.forEach(test => {
      if (test.details && typeof test.details === 'object') {
        console.log(`   ${test.name}:`)
        if (test.details.responseTime) {
          console.log(`     Response Time: ${test.details.responseTime.toFixed(2)}ms`)
        }
        if (test.details.throughput) {
          console.log(`     Throughput: ${test.details.throughput.toFixed(2)} req/s`)
        }
        if (test.details.errorRate !== undefined) {
          console.log(`     Error Rate: ${test.details.errorRate.toFixed(2)}%`)
        }
      }
    })
    
    // Overall assessment
    const successRate = (passedTests / totalTests) * 100
    console.log('\n🎯 Overall Assessment:')
    
    if (successRate >= 90) {
      console.log('   ✅ EXCELLENT - Workers are performing optimally')
    } else if (successRate >= 75) {
      console.log('   ⚠️  GOOD - Workers are performing well with minor issues')
    } else if (successRate >= 50) {
      console.log('   ⚠️  FAIR - Workers have significant issues that need attention')
    } else {
      console.log('   ❌ POOR - Workers have critical issues requiring immediate attention')
    }
    
    console.log(`   Success Rate: ${successRate.toFixed(1)}%`)
    console.log(`   Recommendation: ${this.getRecommendation(successRate)}`)
  }

  private getRecommendation(successRate: number): string {
    if (successRate >= 90) {
      return 'Ready for production deployment'
    } else if (successRate >= 75) {
      return 'Address minor issues before production deployment'
    } else if (successRate >= 50) {
      return 'Significant improvements needed before production'
    } else {
      return 'Critical issues must be resolved before deployment'
    }
  }
}

// Main execution
async function main() {
  const testSuite = new WorkerTestSuite()
  await testSuite.runAllTests()
}

// Export for programmatic use
export { WorkerTestSuite }

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test suite failed:', error)
    process.exit(1)
  })
}
