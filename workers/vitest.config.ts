import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./test-setup.ts'],
    testTimeout: 30000, // 30 seconds for performance tests
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        '**/*.test.ts',
        '**/*.spec.ts',
        'test-setup.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    // Environment variables for API cache worker tests
    env: {
      FIREBASE_FUNCTIONS_URL: 'https://us-central1-syndicaps.cloudfunctions.net',
      FIREBASE_PROJECT_ID: 'syndicaps',
      FIREBASE_REGION: 'us-central1',
      ENVIRONMENT: 'test',
      RATE_LIMIT_ENABLED: 'true'
    }
  },
  resolve: {
    alias: {
      '@': new URL('./', import.meta.url).pathname
    }
  }
})
