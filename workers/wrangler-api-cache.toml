# Wrangler configuration for Syndicaps API Cache Worker

name = "syndicaps-api-cache"
main = "api-cache.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Worker settings
workers_dev = true
# route = { pattern = "api-cache.syndicaps.com/*", zone_name = "syndicaps.com" }

# Resource limits (disabled for free plan)
# limits = { cpu_ms = 30000 }

# KV Namespaces
kv_namespaces = [
  { binding = "API_CACHE_KV", id = "62c46dc1f510470b910ad2687eeafff9", preview_id = "62c46dc1f510470b910ad2687eeafff9" },
  { binding = "API_METADATA_KV", id = "3b900ea35e88423e9fdae8e948eb375c", preview_id = "3b900ea35e88423e9fdae8e948eb375c" },
  { binding = "RATE_LIMIT_KV", id = "9ce53fdd5d4b41b185e64389af9e56e7", preview_id = "9ce53fdd5d4b41b185e64389af9e56e7" }
]

# Environment variables
[vars]
ENVIRONMENT = "development"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps-dev.cloudfunctions.net"

# Development environment
[env.development]
name = "syndicaps-api-cache-dev"
[env.development.vars]
ENVIRONMENT = "development"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps-dev.cloudfunctions.net"
kv_namespaces = [
  { binding = "API_CACHE_KV", id = "62c46dc1f510470b910ad2687eeafff9", preview_id = "62c46dc1f510470b910ad2687eeafff9" },
  { binding = "API_METADATA_KV", id = "3b900ea35e88423e9fdae8e948eb375c", preview_id = "3b900ea35e88423e9fdae8e948eb375c" },
  { binding = "RATE_LIMIT_KV", id = "9ce53fdd5d4b41b185e64389af9e56e7", preview_id = "9ce53fdd5d4b41b185e64389af9e56e7" }
]

# Staging environment
[env.staging]
name = "syndicaps-api-cache-staging"
# route = { pattern = "api-cache-staging.syndicaps.com/*", zone_name = "syndicaps.com" }
[env.staging.vars]
ENVIRONMENT = "staging"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps-staging.cloudfunctions.net"
kv_namespaces = [
  { binding = "API_CACHE_KV", id = "62c46dc1f510470b910ad2687eeafff9" },
  { binding = "API_METADATA_KV", id = "3b900ea35e88423e9fdae8e948eb375c" },
  { binding = "RATE_LIMIT_KV", id = "9ce53fdd5d4b41b185e64389af9e56e7" }
]

# Production environment
[env.production]
name = "syndicaps-api-cache"
# route = { pattern = "api-cache.syndicaps.com/*", zone_name = "syndicaps.com" }
[env.production.vars]
ENVIRONMENT = "production"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps.cloudfunctions.net"
kv_namespaces = [
  { binding = "API_CACHE_KV", id = "62c46dc1f510470b910ad2687eeafff9" },
  { binding = "API_METADATA_KV", id = "3b900ea35e88423e9fdae8e948eb375c" },
  { binding = "RATE_LIMIT_KV", id = "9ce53fdd5d4b41b185e64389af9e56e7" }
]

# Build configuration
[build]
command = "npm run build:api-cache"
cwd = "."
watch_dir = "."

# Miniflare configuration for local development
[miniflare]
kv_persist = true
cache_persist = true

# Analytics and observability
[observability]
enabled = true

# Placement configuration
[placement]
mode = "smart"
