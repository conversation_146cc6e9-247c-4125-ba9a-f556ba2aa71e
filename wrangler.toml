# Syndicaps Cloudflare Workers Configuration
# Optimized for Free Plan with upgrade path to paid features

name = "syndicaps"
main = "workers/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Account configuration
account_id = "24a0c424061d2684ac21f5dd6284f906"
zone_id = "${CLOUDFLARE_ZONE_ID}"

# Development environment (Free Plan optimized)
[env.development]
name = "syndicaps-dev"
# Using subdomain routing for free plan
# Format: syndicaps-dev.your-subdomain.workers.dev

[env.staging]
name = "syndicaps-staging"
# Staging subdomain for testing
# Format: syndicaps-staging.your-subdomain.workers.dev

# Production environment
[env.production]
name = "syndicaps-prod"
# Custom domain routing (requires domain verification)
routes = [
  { pattern = "syndicaps.com/api/*", zone_id = "${CLOUDFLARE_ZONE_ID}" },
  { pattern = "syndicaps.com/images/*", zone_id = "${CLOUDFLARE_ZONE_ID}" }
]

# R2 Storage bindings (Available on Free Plan)
[[env.development.r2_buckets]]
binding = "SYNDICAPS_IMAGES"
bucket_name = "syndicaps-images-dev"

[[env.development.r2_buckets]]
binding = "SYNDICAPS_BACKUPS"
bucket_name = "syndicaps-backups-dev"

[[env.staging.r2_buckets]]
binding = "SYNDICAPS_IMAGES"
bucket_name = "syndicaps-images-staging"

[[env.staging.r2_buckets]]
binding = "SYNDICAPS_BACKUPS"
bucket_name = "syndicaps-backups-staging"

[[env.production.r2_buckets]]
binding = "SYNDICAPS_IMAGES"
bucket_name = "syndicaps-images"

[[env.production.r2_buckets]]
binding = "SYNDICAPS_BACKUPS"
bucket_name = "syndicaps-backups"

# KV Storage for caching (Free Plan: 1GB storage, 100k reads/day)
[[env.development.kv_namespaces]]
binding = "CACHE_KV"
id = "71cabead02ef4f448b6b9ebf392fee0c"
preview_id = "71cabead02ef4f448b6b9ebf392fee0c"

[[env.development.kv_namespaces]]
binding = "SESSION_KV"
id = "800ef077f93240b791cd22e4e7d222d2"
preview_id = "800ef077f93240b791cd22e4e7d222d2"

[[env.staging.kv_namespaces]]
binding = "CACHE_KV"
id = "17ddf6927df24c45a47cab108095526a"

[[env.staging.kv_namespaces]]
binding = "SESSION_KV"
id = "8ed4d59d0fbf4a31a5b9f4e78dd1a75f"

[[env.production.kv_namespaces]]
binding = "CACHE_KV"
id = "a0cc4dfdf2ac4655be3369661d79455b"

[[env.production.kv_namespaces]]
binding = "SESSION_KV"
id = "c25f365c2b06419ea1cf1f4cdafe02e9"

# Environment variables
[env.development.vars]
ENVIRONMENT = "development"
FIREBASE_PROJECT_ID = "syndicaps-fullpower"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps-fullpower.cloudfunctions.net"
# Free Plan optimizations
WORKERS_REQUEST_LIMIT = "100000"  # Free tier daily limit
CACHE_TTL_SHORT = "300"           # 5 minutes
CACHE_TTL_MEDIUM = "1800"         # 30 minutes
CACHE_TTL_LONG = "3600"           # 1 hour
DEBUG_MODE = "true"

[env.staging.vars]
ENVIRONMENT = "staging"
FIREBASE_PROJECT_ID = "syndicaps-fullpower"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps-fullpower.cloudfunctions.net"
WORKERS_REQUEST_LIMIT = "100000"
CACHE_TTL_SHORT = "600"
CACHE_TTL_MEDIUM = "3600"
CACHE_TTL_LONG = "7200"
DEBUG_MODE = "false"

[env.production.vars]
ENVIRONMENT = "production"
FIREBASE_PROJECT_ID = "syndicaps-fullpower"
FIREBASE_FUNCTIONS_URL = "https://us-central1-syndicaps-fullpower.cloudfunctions.net"
# Production optimizations (can upgrade to paid plan)
WORKERS_REQUEST_LIMIT = "1000000"  # Paid plan limit
CACHE_TTL_SHORT = "300"
CACHE_TTL_MEDIUM = "1800"
CACHE_TTL_LONG = "86400"           # 24 hours
DEBUG_MODE = "false"

# Build configuration
[build]
command = "npm run build:workers"
cwd = "workers"
watch_dir = "workers"

# Upload configuration
[upload]
format = "modules"
dir = "workers/dist"
main = "./index.js"

# Limits configuration (Free Plan aware)
[limits]
cpu_ms = 10                    # Free plan: 10ms CPU time
memory_mb = 128               # Free plan: 128MB memory
